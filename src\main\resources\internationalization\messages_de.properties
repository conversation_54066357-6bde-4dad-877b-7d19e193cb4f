welcome.message=Greetings {0}
AppName=Labyrinth
LoginViewModel.Copyright=\u00A9 {0} Cargill, Incorporated. Alle Rechte vorbehalten.
LoginViewModel.ErrorDescription=Benutzername und Passwort darf nicht leer sein.
LoginViewModel.ErrorTitle=Login Fehler
LoginViewModel.InvalidMessage=Der Benutzername oder das angegebene Kennwort ist ung\u00FCltig anmelden.
LoginViewModel.InvalidMessageTitle=Ung\u00FCltige Anmeldung
LoginViewModel.LoginPrompt=Login
LoginViewModel.PasswordLabel=Passwort
LoginViewModel.EmailLabel=Email
MenuViewModel.LogoutPrompt=Logout
MainViewModel.EmailLabel=Email
OKLabel=Okay
LoginViewModel.NetworkErrorMessage=Netzwerkfehler
LoginViewModel.NetworkErrorMessageTitle=Derzeit gibt es kein Netz.
UserPreferencesViewModel.MainHeading=Die folgende Option kann sp\u00E4ter in den App-Einstellungen ge\u00E4ndert werden.
UserPreferencesViewModel.UnitOfMeasure=SELECT MASSEINHEIT
UserPreferencesViewModel.Metric=Metrisch
UserPreferencesViewModel.Imperial=Kaiserlich
UserPreferencesViewModel.Title=Benutzereinstellungen
UserPreferencesViewModel.Branding=GO-TO-MARKETING MARKIERUNG
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.CurrencySelection=W\u00C4HRUNGSWAHL
UserPreferencesViewModel.SelectCurrency=W\u00E4hlen Sie eine W\u00E4hrung aus
SelectCurrencyViewModel.Title=W\u00E4hrung
HomeViewModel.Title=Sales-Bereich Werkzeug
HomeViewModel.DashboardTab=Instrumententafel
HomeViewModel.CustomersTab=Kunden
HomeViewModel.ProspectsTab=Perspektiven
HomeViewModel.SyncWithDate=Synchronisieren - Letztes Synchronisierungsdatum: {0:MM/dd/yy}
HomeViewModel.SyncWithTime=Synchronisieren - Letzte Synchronzeit: {0:hh:mm tt}
HomeViewModel.SyncWithDash=Synchronisieren -
ComfortToolsViewModel.ComfortTools=Komfort -Tools
ComfortToolsViewModel.HealthHeading=Bitte w\u00E4hlen Sie ein Werkzeug aus der Liste unten Ihren Besuch zu beginnen.
ComfortToolsViewModel.ComfortHeading=Bitte w\u00E4hlen Sie ein Werkzeug aus der Liste unten Ihren Besuch zu beginnen.
ComfortToolsViewModel.ComfortToolsTitle=Komfort Werkzeuge
ComfortToolsViewModel.ComfortToolsList=WERKZEUGE
ComfortToolsViewModel.HeatstressEvaluationTitle=Heat Stress Bewertung
ComfortToolsViewModel.PenTimeTitle=Stift Zeit
CustomerDetailViewModel.CustomerTitle=Kundenprofil
ProspectProfileViewModel.ProspectTitle=Prospekt Profil
ProspectProfileViewModel.ProspectInfo=Prospektiv Info
NewProspectViewModel.Customer=Kunde
NewVisitViewModel.Title=Besuchen Einzelheiten
SiteDetailViewModel.Reports=MOLKEREI ENTELIGEN BERICHT
SiteDetailViewModel.Summary=Zusammenfassung
SiteDetailViewModel.Detailed=Detailliert
SiteDetailsResourcesViewModel.Title=Ressourcen
SiteDetailViewModel.NewVisit=Beginnen Sie ein neues Besuch
SiteDetailViewModel.SiteSetup=Site-Einrichtung
SiteDetailViewModel.DairyEnteligenReport=Dairy Enteligen Bericht
CustomerDetailViewModel.MainHeading=STANDORTE
VisitViewModel.VisitTitle=BESUCHEN EINZELHEITEN
VisitViewModel.ComfortItem=Komfort
VisitViewModel.HealthItem=Gesundheit
VisitViewModel.NutritionItem=Ern\u00E4hrung
VisitViewModel.ProductivityItem=Produktivit\u00E4t
VisitViewModel.CategorySection=Werkzeugkategorien
VisitViewModel.SiteVisitSummary=Seite besuchen Zusammenfassung
VisitViewModel.WalkthroughReport=L\u00F6sungsweg Bericht
VisitViewModel.PublishVisit=Besuch ver\u00F6ffentlichen
VisitViewModel.PublishPrompt=Sind Sie sicher, dass Sie diesen Besuch ver\u00F6ffentlichen m\u00F6chten? Es kann nicht ungeschehen gemacht werden.
VisitViewModel.Publish=Ver\u00F6ffentlichen
VisitViewModel.Delete=Besuch l\u00F6schen
VisitViewModel.DeletePrompt=Bist du sicher, dass du diesen Besuch l\u00F6schen m\u00F6chtest? Es kann nicht ungeschehen gemacht werden.
CustomerDetailViewModel.NewVisit=Neuer Besuch
CustomerDetailViewModel.NewSite=Hinzuf\u00FCgen einer neuen standort
HeatstressData=Heatstress Daten
HeatstressCalculations=Heatstress Berechnungen
HeatstressChart=Heatstress Diagramm
VisitNotesViewModel.Close=N\u00E4he
VisitNotesViewModel.New=Neu
VisitNotesViewModel.Title=Besuchen Sie Notebook
SiteDetailViewModel.Title=Site-Details
SiteDetailViewModel.MainHeading=F\u00DCHRUNGEN
VisitNotesViewModel.NoteMetadata={0} @ {1} von {2}
VisitNotesViewModel.Observation=\u00DCberwachung
VisitNotesViewModel.Action=Aktion
VisitNotesViewModel.Task=Aufgabe
VisitNotesViewModel.Event=Event
EditNoteViewModel.Cancel=Abbrechen
EditNoteViewModel.Save=Speichern
EditNoteViewModel.Title=Hinweis
EditNoteViewModel.CreatedByMetadata=Erstellt am {0} @ {1} von {2}
EditNoteViewModel.LastUpdatedByMetadata=Letzte \u00C4nderung am {0} @ {1} von {2}
Yes=Ja
No=Nein
Delete=L\u00F6schen
EditNoteViewModel.DeletePrompt=M\u00F6chten Sie diese Notiz l\u00F6schen?
EditNoteViewModel.DeleteImagePrompt=M\u00F6chten Sie dieses Bild l\u00F6schen?
EditNoteViewModel.DeleteVideoPrompt=M\u00F6chten Sie dieses Video l\u00F6schen?
HeatstressTableViewModel.Title=Heat Stress Bewertung
HeatstressTableViewModel.NotebookPrompt=Besuchen Sie Notebook
HeatstressTableViewModel.HeatstressDataTab=Dateneingabe
HeatstressTableViewModel.HeatstressChartTab=Stamm
HeatstressDataEntryViewModel.AnimalInputs=Tiereing\u00E4nge
HeatstressDataEntryViewModel.Milk=Milch ({0})
Milk=Milch ({0})
HeatstressDataEntryViewModel.DMI=DMI ({0})
HeatstressDataEntryViewModel.NEL=NEL ({0})
HeatstressDataEntryViewModel.MilkFat=Milchfett (%)
HeatstressDataEntryViewModel.LactatingAnimals=Laktierende Tiere
HeatstressDataEntryViewModel.CurrentMilkPrice=Aktueller Milchpreis ({0}/{1})
CurrentMilkPrice=Aktueller Milchpreis ({0}/{1})
HeatstressDataEntryViewModel.MilkProtein=Milch eiwei\u00DF (%)
HeatstressDataEntryViewModel.Weather=WETTER
HeatstressDataEntryViewModel.Temperature=Temperatur ({0})
HeatstressDataEntryViewModel.Humidity=Feuchtigkeit (%)
HeatstressDataEntryViewModel.Exposure=BELICHTUNG
HeatstressDataEntryViewModel.HoursExposed=Sonnenstunden
HeatstressChartViewModel.HeatstressEvalLabel=Temperaturen korrigiert f\u00FCr mittlere Temperatur und die relative Luftfeuchtigkeit (ohne Sonneneinstrahlung )
HeatstressChartViewModel.TempHumidIndex=Temperatur-Feuchte-Index
HeatstressChartViewModel.IntakeAdjustment=Aufnahme Einstellung
HeatstressChartViewModel.DMIReduction=DMI Reduktion
HeatstressChartViewModel.EstimateDryMatter=Gesch\u00E4tzte Trockenmasseaufnahme
HeatstressChartViewModel.ReductionDMI=Reduzierung der DMI
HeatstressChartViewModel.LossEnergyConsumed=Der Verlust der Energieverbrauch
HeatstressChartViewModel.EnergyEquivMilkLoss=Energie-\u00C4quivalent Milchverlust
ErrorTitle=Fehler
ErrorDescription=Lesen oder schreiben Ihre Daten ist ein Fehler aufgetreten.
ForageAuditViewModel.Title=Futter Audit
ForageAuditViewModel.ForageHeading=Grundfutter Informationen
ForageAuditViewModel.ForageDetail=Futterqualit\u00E4t ist die Grundlage f\u00FCr jede Molkerei Ern\u00E4hrung Programm und ist ein Schl\u00FCssel f\u00FCr die allgemeine Rentabilit\u00E4t der Farm. Die Forage Audit Scorecard kann verwendet werden, um die derzeitigen Forage-Management-Praktiken auf dem Bauernhof zu bewerten und empfiehlt die wichtigsten M\u00F6glichkeiten f\u00FCr Verbesserungen. Die Scorecard ist in Key Management Bereiche organisiert. Sie k\u00F6nnen alle Bereiche w\u00E4hrend eines Besuchs abdecken oder nur diejenigen ausw\u00E4hlen, die zu diesem Zeitpunkt wichtig sind. Zus\u00E4tzliche Managementressourcen stehen zur Verf\u00FCgung, um die Bereiche zu verbessern, in denen Verbesserungsm\u00F6glichkeiten bestehen.
ForageAuditViewModel.ForageAuditScorecard=Futter Audit Scorecard
ForageAuditScorecard=Futter Audit Scorecard
ForageAuditViewModel.Resources=Ressourcen
HeatstressGreen=Stre\u00DFschwelle
HeatstressYellow=Mild-M\u00E4\u00DFig Stress
HeatstressOrange=M\u00E4\u00DFig-Starken Stress
HeatstressRed=Schwerer Stress
WeightMetric=Kg
WeightImperial=Pfd
WeightImperialCWT=CWT
EnergyMetric=Mcal/kg
EnergyImperial=Mcal/Pfd
Mcal=Mcal
TemperatureMetric=\u00B0C
ForageScorecardViewModel.SurveyOfForages=Umfrage von Forages
ForageScorecardViewModel.Harvest=Ernte
ForageScorecardViewModel.BunkersAndPiles=Bunker und Pf\u00E4hle
ForageScorecardViewModel.TowerSilos=Turm Silos
ForageScorecardViewModel.SilageBags=Silage Taschen
ForageScorecardViewModel.Baleage=Balage
ForageScorecardViewModel.MaintainingForageQuality=Die Aufrechterhaltung Futterqualit\u00E4t
SurveyOfForages_InventoryIsMonitored=Der Bestand wird \u00FCberwacht?
SurveyOfForages_AnnualCowNumAndForageNeeds=J\u00E4hrlich Projekt Kuh Zahlen und Futterbedarf?
SurveyOfForages_SilosSizedForCapacity=Silos sind f\u00FCr die Kapazit\u00E4t bemessen im Vergleich zu Bed\u00FCrfnisse der Milchviehherde ? Nicht zu \u00FCberf\u00FCllen erforderlich.
SurveyOfForages_LooseOrFacedFeedWithin=Lose oder "konfrontiert" Futter wird innerhalb gef\u00FCttert:
SurveyOfForages_NoLooseFeedRemaining=Kein loses Futter nach der F\u00FCtterung verbleibende getan?
SurveyOfForages_InspectedForSpoilageAndMold=Alle forages inspiziert werden f\u00FCr Verderb und Form wird verdorben Futter verworfen?
SurveyOfForages_VisibleSignsOfSoil=Gibt es irgendwelche sichtbaren Zeichen des Bodens in der Silage? (Punkte f\u00FCr das "Nein" Antwort vergeben)
SurveyOfForages_AshLevelsInHaylage=Was sind Asche Ebenen in Heulage?
SurveyOfForages_AshLevelsInCornSilage=Was sind Ascheniveaus in Maissilage?
SurveyOfForages_CornSilageScoreMonitored=Maissilage Kernel Verarbeitung Score wird unter Verwendung \u00FCberwacht Cargill Futter Labor KP-Test
SurveyOfForages_CornSilageProcessingScore=Maissilage Verarbeitung Score?
SurveyOfForages_ButryicAcidLevelsInHaylage=Was sind die Butters\u00E4ure Ebenen in Heulage?
SurveyOfForages_ButyricAcidLevelsInHaylage=Was sind die Butters\u00E4ure Ebenen in Heuhaufen
SurveyOfForages_LacticAcidToAceticAcidLevels=Milchs\u00E4ure zu Essigs\u00E4ure Ebenen?
Harvest_AdequateEquipmentAndLabor=Eine angemessene Ausr\u00FCstung und Arbeit ernten Heulage Ernte
Harvest_WholePlantMoistureDetermined=Ganze Pflanze Feuchtigkeit f\u00FCr jedes Feld bestimmt wird?
Harvest_CornSilageMoistureRangeConsistent=Maissilage Feuchtebereich ist mit mehr als 90% der Proben in diesem Bereich konsistent?
Harvest_ForagesHarvestedAtProper=Forages bei richtigen Reife und Feuchtigkeit f\u00FCr den Pflanzentyp und Lagerhalle geerntet
Harvest_LengthOfCutMonitored=Schnittl\u00E4nge mit der Penn State Shaker Box \u00FCberwacht?
Harvest_KPScoreIsMonitored=KP-Score wird mit Schale 32 Unzen \u00FCberwacht oder die Float-Verfahren verwenden
Harvest_UseSilageAdditive=Verwenden Sie Siliermittel; Impfmittel oder Aerobic-Stabilisator?
Harvest_ForageHarvestingDocumented=Futtererntebedingungen , Feld- und Lagerorte werden dokumentiert?
BunkersAndPiles_CleanlinessOfFeedArea=Sauberkeit des Einzugsbereich ? Bewertungsskala 1 - 10, wobei 10 die beste
BunkersAndPiles_PackingInitialSpreadLayers=Verpackung: Anf\u00E4ngliche Ausbreitung Schichten 6 Zoll oder weniger?
BunkersAndPiles_PorosityScoresConsistently=Por\u00F6sit\u00E4t Partituren konsequent
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Pile Steigung und Bunker Krone sollte nicht weniger als ein 3: 1 Laufquote steigen (18 Grad Neigung)
BunkersAndPiles_TiresSplitsTouching=Reifen / Splits zu ber\u00FChren?
BunkersAndPiles_SideWallsSealedPlastic=Die Seitenw\u00E4nde sind mit Kunststoff versiegelt?
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Bonus: 2 Schichten aus Kunststoff, mit einer Schicht aus nicht-durchl\u00E4ssigen Kunststoff besteht.
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Sealed unmittelbar nach dem mit 6 mil Kunststoff-Verpackung?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Glattes Gesicht, keine Anzeichen f\u00FCr gest\u00F6rte Schichten, die eine Sauerstoffpenetration erm\u00F6glichen
BunkersAndPiles_FaceRemoveRate=Gesicht Abtragsleistung
BunkersAndPiles_LooseOrFacedFeedIsFed=Lose oder "konfrontiert" Futter zugef\u00FChrt wird:
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=Abdeckung Kunststoff nur aus Silage entfernt?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Ist Silo bedeckt nach dem Bef\u00FCllen, wenn nicht innerhalb von 14 Tagen verwendet
TowerSilos_SiloFillingTimeof3DaysOrLess=Silobef\u00FCllung Zeit von 3 Tagen oder weniger / Ernte oder Silo?
TowerSilos_FaceRemovalRateGreaterThan4Inches=Gesichts-Entfernungsrate von mehr als 4 Zoll / Tag?
SilageBags_BagsPlacedOnStableWellManagedSurface=Taschen sind auf stabile gut verwaltet die ganze Saison Oberfl\u00E4che (Asphalt oder Beton) gesetzt
SilageBags_TrashVegRodentControlledAroundBags=Trash, Vegetation und Nagetiere um Taschen kontrolliert
SilageBags_BonusSecureCoverIsUsed=Bonus: Sichere Abdeckung verwendet wird?
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Kontrolliert f\u00FCr Sch\u00E4dlings Loch Sch\u00E4den und in regelm\u00E4\u00DFigen Abst\u00E4nden repariert?
SilageBags_FaceRemovalRate=Gesicht Abtragsleistung:
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Saubere gut gef\u00FChrtes Feed Gesicht, keine Hinweise auf losen Eingabeheizzone und schrumpft
SilageBags_PorosityScoresConsistently=Por\u00F6sit\u00E4t Partituren konsequent:
Baleage_BagsPlacedOnStableWellManagedSurface=Taschen sind auf stabile gut verwaltet die ganze Saison Oberfl\u00E4che (Asphalt oder Beton) gesetzt?
Baleage_TrashVegRodentControlledAroundBags=Trash, Vegetation und Nagetiere um Taschen kontrolliert?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Kontrolliert f\u00FCr Sch\u00E4dlings Loch Sch\u00E4den und in regelm\u00E4\u00DFigen Abst\u00E4nden repariert? (Einmal in der Woche)
Baleage_WaterShedsOffPlasticNotIntoBaleage=Wasser wirft aus Kunststoff und nicht in balage? (Herausforderung mit gro\u00DFen Quaderballen )
Baleage_AreBalesWrappedWith=Sind Ballen gewickelt mit:
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Bonus: Stabilisator / Schimmel-Hemmer zur Behandlung in TMR w\u00E4hrend der hei\u00DFen / feuchten Wetter?
MaintainingForageQuality_TMRMixHasPleasantAroma=TMR-Mix hat ein angenehmes Aroma, wenn Sie es riechen?
MaintainingForageQuality_TMRMixIsCoolToTouch=TMR-Mix ist zu k\u00FChl anf\u00FChlt?
Quarterly=Viertelj\u00E4hrlich
SemiAnnually=Halbj\u00E4hrlich
Annually=J\u00E4hrlich
OneToSixHours=1 bis 6 Stunden
SixToTwelveHours=6 bis 12 Stunden
GreaterThanTwelveHours=Gr\u00F6\u00DFer als 12 Stunden
LessThan4Days=Weniger als 4 Tage
FourToSevenDays=4 bis 7 Tage
GreaterThanSevenDays=Gr\u00F6\u00DFer als 7 Tage
NoWholeKernals=Keine ganze K\u00F6rner
LessThanFiveWholeKernals=Weniger als 5 ganze K\u00F6rner
GreaterThanFive=Mehr als 5
DontKnow=wei\u00DF nicht
LessThanOneHour=Weniger als 1 Stunde
WithinEightHours=Innerhalb von 8 Stunden
GreaterThan8Hours=Mehr als 8 Stunden
TwelveInchesOrGreater=12 Zoll oder l\u00E4nger
SixToTwelveInches=6 bis 12 Zoll
LessThanSixInches=Weniger als 6 Zoll
ForageScorecardViewModel.Yes=Ja
ForageScorecardViewModel.No=Nein
GreaterThanSixHours=Gr\u00F6\u00DFer als 6 Stunden
ThreeTimesPerWeek=3 mal pro Woche
TwicePerWeek=Zweimal pro Woche
OncePerWeek=Einmal pro Woche
GreaterThanThirtySixInchesPerDay=Gr\u00F6\u00DFer als 36 Zoll pro Tag
TwentyFourToThirtySixInchesPerDay=24 bis 36 Zoll pro Tag
LessThanTwentFourInchesPerDay=Weniger als 24 Zoll pro Tag
MoreThan8LayersOfPlastic=Mehr als 8 Schichten aus Kunststoff
SixToEightLayers=6 bis 8 Schichten
LessThanSixLayers=Weniger als 6 Schichten
SurveyCategories=Umfrage Kategorien
SurveyOfForages=Umfrage von Forages
Harvest=Ernte
BunkersPiles=Bunker und H\u00E4morrhoiden
TowerSilos=Turm Silos
SilageBags=Silage Taschen
Balage=Balage
MaintaingForageQuality=Die Aufrechterhaltung Futterqualit\u00E4t
ViewOverallForageScore=Ausblick Generell Futter Score
ForageAuditScorecardScoreViewModel.OverallForageScore=Ausblick Generell Futter Score
OverallForageScoreDetails=Um die gesamte Futter Partitur zu sehen, f\u00FChren zun\u00E4chst mindestens eine der Erhebungen in der obigen Liste.
Next=N\u00E4chster
Finish=Fertig
QuestionViewModel.Close=N\u00E4he
Making_Feed_InventoryFOF=Herstellung eines Futter- Inventar
StorageCalculators=Speicherrechner
DecidingSilageStorage=Die Entscheidung \u00FCber einen Silagelagertyp
ForageAuditScorecardScoreViewModel.ScoreTab=Partitur
ForageAuditScorecardResponsesViewModel.ResponsesTab=Antworten
ForageAuditScorecardResultsViewModel.ImprovementsTab=Verbesserungen
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Futter\u00FCberwachung Verbesserungen
ForageAuditViewModel.ScoreTab=Partitur
ForageAuditViewModel.ResponsesTab=Antworten
ForageAuditViewModel.ImprovementsTab=Verbesserungen
ForageAuditScorecardScoreViewModel.GoodIndicator=Gut
ForageAuditScorecardScoreViewModel.Title=Forage Audit Gesamtergebnis
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=verbesserungen
ForageScorecardResultsViewModel.Title=Balage
BunkersAndPiles=Bunker und Pf\u00E4hle
Baleage=Balage
MaintainingForageQuality=Die Aufrechterhaltung Futterqualit\u00E4t
QuestionViewModel.SurveyOfForages=Umfrage von Forages
QuestionViewModel.Harvest=Ernte
QuestionViewModel.BunkersAndPiles=Bunker und Pf\u00E4hle
QuestionViewModel.TowerSilos=Turm Silos
QuestionViewModel.SilageBags=Silage Taschen
QuestionViewModel.Baleage=Balage
Improvements=Verbesserungen
QuestionViewModel.MaintainingForageQuality=Die Aufrechterhaltung Futterqualit\u00E4t
QuestionTableTitle=Frage {0} von {1}
ResourcesViewModel.Title=Futter Pr\u00FCfressourcen
PreventingStorageLosses=Verhindern von Speicherverluste
FeedoutLossesForageStorageSys=Zuf\u00FChrkopf Verluste aus Futter Storage Systems
CargillForageLabKPTest=Cargill Futter Lab KP Test
FermentationAnalysisSilageQT=Fermentation Analyse &amp; Silage Qualit\u00E4tspr\u00FCfung
GettingtheMostOutofYourForage=Holen Sie das Beste aus Ihrem Futter
SilagePrevention1st=Silage Pr\u00E4vention: 1. Things 1.
CornSilageResources=Maissilage Ressourcen
CropCharacteristicsDecisionGuide=Crop Merkmale Entscheidung F\u00FChrer
RecommendedTLCSettings=Empfohlene Einstellungen TLC
PennStateShakerBoxForageResults=Penn State Shaker Box Futter Ergebnisse
FieldKPTest=Feld KP -Test
AdjustingKPtoAssureSuccess=Einstellen KP Assure Erfolg
HowtoGetBetterKPResults=Wie man eine bessere KP Ergebnisse zu bekommen
InoculantFQAs=Impfmittel FAQs
ChoosingtheCorrectAdditive=CAusw\u00E4hlen der richtigen Additive
PhotoExamples=Foto Beispiele
Porosity=Porosit\u00E4t
ThreePotentialStorageSolutions=Drei M\u00F6gliche Storage-L\u00F6sungen
FeedOutRatesFilmsStorageSysExamined=Feed- out-Raten , Filme &amp; Storage Systems Suchte
ManagingForageinTowerSilos=Verwalten Futter in Hochsilos
BaggedConventionalSilage=Gesackte Konventionelle Silage
ManagingForageinSiloBags=Verwalten von Futter in Silo Taschen
DensityLossesinPressedBagSilos=Dichte &amp; Die Verluste in Pressed Bag Silos
BaleageFQAs=balage FAQs
ScorecardPrompt=Bitte f\u00FCllen Sie das folgende \u00DCbersicht der {0} Scorecard zu sehen
PromptOK=OK
PromptCancel=Stornieren
StatusInProgress=Bearbeitung
StatusCompleted=Fertiggestellt
Resources=Ressourcen
Answers=Antworten
ForageScorecardViewModel.Title=Futter Audit Scorecard
NotebookSectionForageAuditLanding=Futter Audit
NotebookSectionForageAudit=Futter Audit Scorecard
NotebookSectionForageAuditBaleage=Futter Audit-Balage
NotebookSectionForageAuditBunkersPiles=Futter Audit-Bunkers und Piles
NotebookSectionForageAuditHarvest=Futter Audit-Ernte
NotebookSectionForageAuditMaintainingQuality=Futter Audit-Die Aufrechterhaltung Futterqualit\u00E4t
NotebookSectionForageAuditSilageBags=Futter Audit-Silage Taschen
NotebookSectionForageAuditSurveyOfForages=Forage Audit-Survey auf Forages
NotebookSectionForageAuditTowerSilos=Futter Audit-Turm Silos
NotebookSectionComfortTools=Komfort Tools
NotebookSectionHeatstressCalculations=Heatstress-Berechnungen
NotebookSectionHeatstressChart=Heatstress-Chart
NotebookSectionHeatstressData=Heatstress-Data
NotebookSectionNutritionTools=Ern\u00E4hrung Tools
NotebookSectionVisit=Besuch
NotebookWalkthroughReportLanding=L\u00F6sungsweg Bericht
NotebookManureScoreHerdAnalysisInputs=D\u00FCngergebnis Herde-Eing\u00E4nge
NotebookManureScoreHerdAnalysisGoals=D\u00FCngergebnis Herde-Tore
NotebookManureScoreHerdAnalysisResults=D\u00FCngergebnis Herde-Ergebnisse
NotebookMetabolicIncidenceInputs=Metabolische Inzidenz Eing\u00E4nge
NotebookMetabolicIncidenceOutputs=Metabolische Inzidenzausg\u00E4nge
NotebookMetabolicIncidenceCharts=Metabolische Inzidenzdiagramme
NotebookPenTimePenSelection=Pen Time Budget-Pen Auswahl
NotebookPenTimeInputs=Pen Time Budget-Eing\u00E4nge
NotebookPenTimeComparison=Pen Time Budget-Vergleich
NotebookPenTimeResults=Pen Time Budget-Ergebnisse
NotebookWalkthroughReportPenLevel=Komplettierung
NotebookWalkthroughReportPen=Walkthrough Report-Pen-Analyse
VisitViewModel.ToolCategories=Werkzeug Kategorien
VisitViewModel.Instructions=Bitte w\u00E4hlen Sie eine Kategorie oder einen Bericht aus der Liste unten
PileAndBunkerCapacity=Ile und Bunker-Kapazit\u00E4t
PileCapacity=Stapelkapazit\u00E4t
PileFeedOutRate=Stapelzuf\u00FChrung
BunkerCapacity=Bunkerkapazit\u00E4t
BunkerFeedOutRate=Bunker ausf\u00FChren
NotebookRumenHealthTMRParticleScore=TMR Partikelwert
NotebookRumenHealthTMRParticlePercent=TMR Prozent auf dem Bildschirm
NotebookLocomotionLanding=Lokomotion-Main
NotebookLocomotionPenSelection=Lokomotion-Stift-Auswahl
NotebookLocomotionPenInputs=Lokomotion-Pen Eing\u00E4nge
NotebookLocomotionPenResults=Lokomotion-Pen Ergebnisse
NotebookLocomotionHerdInputs=Lokomotion-Pen Eing\u00E4nge
NotebookLocomotionHerdRevenue=Lokomotion-Herd Analyse Einnahmen
NotebookLocomotionHerdResults=Lokomotion-Pen Ergebnisse
NotebookLocomotionEditTable=Lokomotion-Anzahl der K\u00FChe
NotebookCudCalculators=Rumen Gesundheit-Cud Taschenrechner
NotebookCudChewingDataEntry=Rumen Gesundheit-Cud Kauen Eintrag
NotebookCudChewingResults=Rumen Gesundheit-Cud Kauen Ergebnisse
NotebookCudChewing=Rumen Gesundheit-Cud Kauen Stift
NotebookRumenHealthNumberOfChewsInput=Rumen Gesundheit-Anzahl der Kauen Eing\u00E4nge
NotebookRumenHealthNumberOfChewsResults=Rumen Gesundheit-Anzahl der Kauen Ergebnisse
NotebookParticleScoreLanding=TMR Partikelpunkt
NotebookParticleScoreSelectPen=TMR Partikelauswahl-Stift
NotebookParticleScoreSelectScorer=TMR Partikel-Score-Auswahl Scorer
NotebookManureScoreLanding=D\u00FCnger Punktzahl
NotebookManurePenSelection=D\u00FCnger Punktzahl-Stift-Auswahl
NotebookVisitSummary=Besuchen Sie die Zusammenfassung
NotebookTMRParticleHerdAnalysisPenInputs=TMR Partikel-Score Herd Analyse-Eing\u00E4nge
NotebookTMRParticleHerdAnalysisPenResults=TMR Partikel-Score Herd Analyse-Ergebnisse
NotebookParticleScoreHerdAnalysisEdit=TMR Partikel Partitur Herd Analyse-Edit DIM Menge
Overall=Insgesamt
SiteDetailsSetupViewModel.Title=Site Details
SiteDetailsSetupViewModel.Pens=Stifte
SiteDetailsSetupViewModel.Diets=Di\u00E4ten
SiteDetailsSetupViewModel.PenSetup=Stift-Setup
SiteDetailsSetupViewModel.DietSetup=DIET SETUP
SiteDetailsSetupViewModel.MilkYield=Milchleistung ({0})
SiteDetailsSetupViewModel.SiteName=Name der site
SiteDetailsSetupViewModel.Continue=Fortsetzen
Continue=Fortsetzen
SiteDetailsSetupViewModel.CurrentMilkPrice=Aktueller Milchpreis ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Tage in der Milch(TIM) 
SiteDetailsSetupViewModel.MilkingSystem=Melkanlage
SiteDetailsSetupViewModel.DryMatterIntake=Trockenaufnahmemenge (DMI) ({0})
SiteDetailsSetupViewModel.AsFedIntake=As-Fed-Zufuhr ({0})
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=(NEL) Molkerei (Mcal/{0})
SiteDetailsSetupViewModel.LactatingAnimals=Laktierende Tiere
SiteDetailsSetupViewModel.Milk=Milch
SiteDetailsSetupViewModel.MilkFatPercent=Milchfett %
SiteDetailsSetupViewModel.MilkProteinPercent=Milchprotein %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Milch Andere Feststoffe %
SiteDetailsSetupViewModel.SomaticCellCount=Somatische Zellzahl (1000 Zellen/ml)
SiteDetailsSetupViewModel.BacteriaCellCount=Bakterienzellzahl (1.000 cfu/ml)
SiteDetailsSetupViewModel.RationCost=Kosten pro Tier ({0})
SiteDetailsSetupViewModel.NewSite=Neue Seite
SiteDetailsSetupViewModel.NullSiteName=Site Name, Milk Price, Milking System and Pen are mandatory fields. Do you wish to continue or delete the site?
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Allgemeine Kundeneinrichtung
SiteDetailsSetupViewModel.AnimalInputsSite=Tier Eing\u00E4nge Seite
SiteDetailsSetupViewModel.DietInputsSiteLactating=Di\u00E4t-Inputs, Site (laktierende Tiere)
SiteDetailsSetupViewModel.SiteSetup=Site-Einrichtung
SiteDetailsSetupViewModel.InfoSiteSetup=Add a New Site for each farm location owned by a customer. There must be at least 1 Site created to do a Site Visit and access the Dairy Enteligen Tools. 
When creating a new Site the only items required are the Site Name, Current Milk Price, Milking System, and Pens (Click on Pen Setup). Pens are required to use the Pen level tools and the Walkthrough Report. 
Add or update farm Site specific data on this page. You can also add or update data within the tools as you use them. 
Site Setup data will be updated automatically for sites with farm data downloads in Dairy Enteligen. 
SiteDetailsSetupViewModel.WeightImperialCWT=CWT
Parlor=Salon
Robot=Roboter
Other=Andere
SelectMilkingSystemViewModel.Title=Site-Einrichtung
SelectMilkingSystemViewModel.SelectMilkingSystem=W\u00E4hlen Sie ein Melksystem
SelectMilkingSystemViewModel.NoneSelected=Nichts ausgew\u00E4hlt
ResourcesViewModel.VisitNotebook=Besuch Notebook
TemperatureImperial=\u00B0F
ForageScorecardViewModel.VisitNotebook=Besuch Notebook
ForageScorecardViewModel.ForageAuditScorecard=Futter Audit Scorecard
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Stapel und Bunkerkapazit\u00E4en
PileAndBunkerCapacityViewModel.AddPile=Hinzuf\u00FCen Pile
PileAndBunkerCapacityViewModel.AddBunker=Hinzuf\u00FCen Bunker
PileAndBunkerCapacityViewModel.Pile=Pile
Pile=Pile
PileAndBunkerCapacityViewModel.Bunker=Bunker
Bunker=Bunker
PileAndBunkerCapacityViewModel.NameNotUnique=Ein Stapel oder Bunker Namen "{0}" ist bereits vorhanden. Namen m\u00FCssen eindeutig sein.
PileAndBunkerCapacityViewModel.NameTooLong=Ein Stapel oder Bunker Name darf 40 Zeichen lang sein.
PileAndBunkerCapacityViewModel.Piles=H\u00E4morrhoiden
PileAndBunkerCapacityViewModel.Bunkers=Bunkers
PileAndBunkerCapacityViewModel.NewPile=Bitte geben Sie dem neuen Stapel einen Namen.
PileAndBunkerCapacityViewModel.NewBunker=Bitte geben Sie dem neuen Bunker einen Namen.
DietListViewModel.NewDiet=F\u00FCgen Sie eine neue Di\u00E4t
DietListViewModel.MainHeading=Di\u00E4ten
DietListViewModel.InfoNewDiet=Diet names will be updated automatically if MAX is connected to the farm site in Dairy Enteligen. Otherwise, add Diets manually or leave this list blank and select the correct Animal Class / Subclass for each Pen. 
Once the Diet is created, select the Class / Subclass of animal associated with the Diet.
DietListViewModel.New=Neu
NewDietViewModel.Title=Neue Di\u00E4t
ClassSubClass=Tier Klasse / Unterklasse
LinkToPens=Link zu Stifte (Optional)
NewDietViewModel.MainHeading=Di\u00E4t -Name
NewDietViewModel.Save=Sparen
NewDietViewModel.Cancel=Stornieren
DietListViewModel.Title=Di\u00E4ten
NewDietClassViewModel.Title=Tier Klasse / Unterklasse
Lactating=Lactating
Dry=Trocken
Heifer=F\u00E4rse
Calf=Kalb
Male=M\u00E4nnlich
Fresh=Frisch
Milking=Melken
LowForage=Niedrige Futter
FreshHeifer=Frische F\u00E4rse
AAEfficiency=AA Wirksamkeit
CloseUp=Nahansicht
FarOff=Weit weg
CloseUpHeifer=Nahansicht F\u00E4rse
ShortDryPeriod=Kurze Trockenperiode
Bull=Stier
Steer=Steuern
Max=MAX
UserCreated=Benutzer erstellt
PenListViewModel.MainHeading=STIFTE
PenListViewModel.Title=Stifte
PenListViewModel.NewPen=F\u00FCgen Sie einen Neuen Stift
NewPenViewModel.Save=Speichern
NewPenViewModel.Cancel=Abbrechen
NewPenViewModel.Title=Neu Stift
NewPenViewModel.PenDetail=Stift -Detail
Diet=Di\u00E4t
NewPenViewModel.Diet=Di\u00E4t
NewPenViewModel.PenName=Stift Name
NewPenViewModel.Barn=Barn Name
NewPenViewModel.HousingSystem=Geh\u00E4usesystem
NewPenViewModel.NumberOfStalls=Anzahl der St\u00E4nde
NewPenViewModel.FeedingSystem=F\u00FCtterungssystem
NewPenViewModel.MilkingFrequency=Melkfrequenz
NewPenViewModel.Animals=Tiere pro Stift
NewPenViewModel.DaysInMilk=Tage in Milch (DIM)
NewPenViewModel.Milk=Milchleistung ({0})
NewPenViewModel.DryMatterIntake=Trockene Materieaufnahme (DMI) ({0})
NewPenViewModel.AsFedIntake=As-Fed Einnahme ({0})
NewPenViewModel.RationCostPerAnimal=Ration Kosten pro Tier ({0})
NewPenViewModel.General=General
General=General
NewPenViewModel.AnimalsInputsPen=Tiere Eing\u00E4nge, Stift
NewPenViewModel.DietInputsPen=Di\u00E4t Eing\u00E4nge, Stift
SelectHousingSystemViewModel.Title=Stift-Setup
SelectHousingSystemViewModel.SelectHousingSystem=W\u00E4hlen Sie Geh\u00E4use-System
Freestall=Freestall
Tiestall=Tiestall
DryLot=Trockene Lot
BeddedPack=Bettw\u00E4sche
Pasture=Weide
PastureOther=Weide + Sonstiges
SelectFeedingSystemViewModel.SelectFeedingSystem=W\u00E4hlen Sie F\u00FCtterungssystem
SelectFeedingSystemViewModel.Title=Stift-Setup
TMR=TMR
PMRConcentrate=PMR + Konzentrieren
Component=Komponente
SelectPen=Bitte w\u00E4hlen sie eine di\u00E4t f\u00FCr den neuen stift.
NewPenDietViewModel.Title=Di\u00E4t
NewPenDietViewModel.New=Neu
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Ergebnis
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Antworten
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Verbesserungen
FillAllFields=Bitte f\u00FCllen Sie alle Felder aus.
FillAllMandatoryFields=Please fill in all mandatory fields.
NewDietPensViewModel.Title=Link zu Stifte
ComfortToolsViewModel.Title=Komfort Tools
NutritionViewModel.Title=Ern\u00E4hrung Tools
VisitViewModel.Title=Besuchen Einzelheiten
PileAndBunkerResultsFeedOutViewModel.FeedingRate=F\u00FCterungsrate (als gef\u00FCterten / Kuh)
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=K\u00FCe gef\u00FCtert werden
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kilogramm DM In 1 Meter
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Pfund DM In 1 Fu\u00DF
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Feed-Out Fl\u00E4he (m ^ 2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Feed-Out Fl\u00E4he (ft ^ 2)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=Cm. Per Day
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=Zoll pro Tag
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=Bei 3 Zoll pro Tag
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=Bei 7 Zentimeter pro Tag
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=Bei 6 Zoll pro Tag
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=Bei 15 Zentimeter pro Tag
PileAndBunkerResultsFeedOutViewModel.Title=Ausf\u00E4deln
PileAndBunkerResultsCapacityInputViewModel.Title=Kapazit\u00E4t
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Top Breite ({0})
PileAndBunkerResultsCapacityInputViewModel.Height=H\u00F6he ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Bodenbreite ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Untere L\u00E4nge ({0})
PileAndBunkerResultsCapacityInputViewModel.TopLength=Top L\u00E4nge ({0})
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=Trockenmasse %
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Silage DM Dichte ({0})
PileAndBunkerResultsCapacityInputViewModel.TonsDM=TONS DM
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=TONS AF
NewDietPensViewModel.AssociatePens=Sie k\u00F6nnen diese Di\u00E4t mehrere Stifte verbinden.
DietDetailViewModel.Title=Di\u00E4t -Detail
DietDetailViewModel.Created=Erstellt
DietDetailViewModel.UserCreated=Nach Benutzer
DietDetailViewModel.Max=MAX
Pens=Stifte
Created=Erstellt
PenName=Stift Name
PenDetailViewModel.Title=Stift -Detail
CreateDuplicateDiet=Eine Di\u00E4t existiert bereits f\u00FCr diese Tierart . Erstellen Sie \u00FCberhaupt?
DuplicatePenName=Ein Stift ist bereits mit diesem Namen . Bitte w\u00E4hlen Sie einen anderen Namen.
VisitNotebook=Besuch Notebook
LoginViewModel.Title=Login
ForageAuditScorecardResultsViewModel.Title=Turm Silos
VisitNotesViewModel.VisitNotebook=Besuch Notebook
EditNoteViewModel.Close=N\u00E4he
EditNoteViewModel.TitleLabel=Titel
EditNoteViewModel.NoteLabel=Hinweis
EditNoteViewModel.Delete=L\u00F6schen
PileAndBunkerCapacityViewModel.Title=Stapel und Bunker Kapazit\u00E4ten
SettingsViewModel.Milk_Processor_Set_Up=Milch-Prozessor- Set Up
SettingsViewModel.Metric=Metrisch
SettingsViewModel.Imperial=Kaiserlich
SettingsViewModel.Select_Unit_Of_Measure=W\u00E4hlen Sie Ma\u00DFeinheit
SettingsViewModel.More_Settings=Mehr Einstellungen
User_Settings=Anwendungseinstellungen
Privacy_Statement=Datenschutzerkl\u00E4rung
Sync_Data=Synchronisierungsdaten
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelH\u00F6heInMeters=H\u00F6he (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelH\u00F6heInFeet=H\u00F6he (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopBreiteInMeters=Top Breite (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopBreiteInFeet=Top Breite (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomBreiteInMeters=Bodenbreite (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomBreiteInFeet=Bodenbreite (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomL\u00E4ngeInMeters=Untere L\u00E4nge (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomL\u00E4ngeInFeet=Untere L\u00E4nge (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopL\u00E4ngeInMeters=Top L\u00E4nge (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopL\u00E4ngeInFeet=Top L\u00E4nge (f)
UserPreferencesViewModel.UserPreferencesMoreSettings=Mehr Einstellungen
UserPreferencesViewModel.UserPreferencesMilkProcessor=Konfiguration Milchverarbeiter
PricingMatrixViewModel.Title=Bearbeiten Matrix
PricingMatrixViewModel.Amount=Betrag (1000 Zellen / ml)
PricingMatrixViewModel.AmountCFU=Betrag (1000 cfu / ml)
MilkProcessorViewModel.Delete=L\u00F6schen
MilkProcessorViewModel.ConcentrationProcessor=Konzentration Prozessor
MilkProcessorViewModel.ComponentProcessor=Komponenten Prozessor
MilkProcessorViewModel.Name=NAME
MilkProcessorViewModel.BasePrices=DIE BASISPREISE
MilkProcessorViewModel.PricingMatrices=PREIS MATRIZEN
MilkProcessorSettingsMasterViewModel.MilkProcNew=Neu
MilkProcessorSettingsMasterViewModel.Title=Milch-Prozessor-Setup
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Komponente
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Konzentration
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Kapazit\u00E4t
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Ausf\u00E4deln
Amount=Betrag
PricingMatrixEditViewModel.Title=Matrix-Artikeldetail
PricingMatrixEditViewModel.Save=Speichern
PricingMatrixViewModel.New=Neu
DeleteMatrixValue=Sind Sie sicher, dass Sie diese Matrix Wert zu l\u00F6schen?
UnitedStates=United States Of America
US=United States Of America
Euro=Euro Member Countries
UnitedKingdom=United Kingdom
UK=United Kingdom
ProcessorCurrencyPickListViewModel.CurrenciesLabel=W\u00E4hrungen
ProcessorCurrencyPickListViewModel.Title=W\u00E4hrungen
PricingMatrixPickListViewModel.PricingMatrix=Pricing Matrix
PlBacteriaCell=Bakterienzelle
PlSomaticCell=K\u00F6rperzelle
PlMilkFat=Milchfett
PlMilkProtein=Milch eiwei\u00DF
MilkProcessorViewModel.SelectCurrency=W\u00E4hrung ausw\u00E4hlen
SelectMatrix=W\u00E4hlen Sie eine W\u00E4hrung
MilkProcessorViewModel.NewComponentProcessorName=Komponente-Prozessor #{0}
MilkProcessorViewModel.NewConcentrationProcessorName=Konzentration-Prozessor #{0}
MilkProcessorViewModel.BasePriceMilkFat=Milchfett ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Milch ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Milch-Protein ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Andere Feststoffe ({0}/{1})
MilkProcessorViewModel.Amount=Betrag (1000 Zellen / ml)
MilkProcessorViewModel.AmountCFU=Betrag (1000 cfu / ml)
MilkProcessorViewModel.HundredWeight=CWT
MilkProcessorViewModel.WeightMetric=KG
MilkProcessorViewModel.WeightImperialCWT=CWT
MilkProcessorViewModel.WeightImperial=LBS
MilkProcessorViewModel.DeletePrompt=Wenn Sie diesen Prozessor l\u00F6schen, werden die Ergebnisse im Melkprozedur-Einnahmenrechner ung\u00FCltig. M\u00F6chten Sie diesen Prozessor l\u00F6schen?
Edit=Bearbeiten
MenuViewModel.Close=N\u00E4he
RevenueInputViewModel.ComparisonValues=Vergleichswerte
RevenueInputViewModel.SelectProcessor=W\u00E4hlen Prozessor
MilkProcessorInputViewModel.ProcessorDeletedPrompt=Der zuvor ausgew\u00E4hlte Prozessor wurde gel\u00F6scht. W\u00E4hlen Sie einen anderen Prozessor, um fortzufahren.
RevenueEditComparisonValuesViewModel.PotentialSCC=Potenzielle SCC
PotentialSCC=Potenzielle SCC
RevenueEditComparisonValuesViewModel.NumOfCows=Anzahl der K\u00FChe
NumOfCows=Anzahl der K\u00FChe
RevenueEditComparisonValuesViewModel.MilkProduction=Milchproduktion
MilkProduction=Milchproduktion ({0})
RevenueEditComparisonValuesViewModel.MilkPrice=Milchpreis ($/CWT)
MilkPrice=Milchpreis ({0}/{1})
MilkChange=Milch \u00E4ndern ({0})
RevenueEditComparisonValuesViewModel.MilkChange=Milch \u00E4ndern (%)
DownResponse=Let Down Antwort ({0}/Kuh/Tag )
RevenueEditComparisonValuesViewModel.DownResponse=Let Down Antwort
(Lbs/Kuh/Tag )
RevenueEditComparisonValuesViewModel.CurrentSCC=Aktuelle SCC
CurrentSCC=Aktuelle SCC
RevenueInputViewModel.ScenarioOne=Szenario 1
RevenueInputViewModel.ScenarioTwo=Szenario 2
ForageAuditViewModel.VisitNotebook=Besuch Notebook
NutritionViewModel.VisitNotebook=Besuch Notebook
ComfortToolsViewModel.VisitNotebook=Besuch Notebook
HeatstressTableViewModel.VisitNotebook=Besuch Notebook
HeatstressDataEntryViewModel.VisitNotebook=Besuch Notebook
HeatstressChartViewModel.VisitNotebook=Besuch Notebook
HeatstressChartViewModel.TemperatureMetric=\u00B0C
HeatstressChartViewModel.TemperatureImperial=\u00B0F
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.Mcal=Mcal
CudChewingViewModel.CudChewing=Pansen Gesundheit
CudChewingViewModel.CudChewingTitle=Stift name
CudChewingViewModel.CudChewingList=Stifte (laktierenden und trocken nur)
HealthToolsViewModel.Title=Gesundheit Werkzeuge
HealthToolsViewModel.HealthHeading=Bitte w\u00E4hlen Sie ein Werkzeug aus der folgenden Liste.
HealthToolsViewModel.HealthToolsList=Werkzeuge
HealthToolsViewModel.RumenHealthTitle=Pansen Gesundheit wiederk\u00E4uen
HealthToolsViewModel.RumenHealthTMRTitle=Rumen Gesundheit TMR-Partikeln
HealthToolsViewModel.RumenHealthManureTitle=Rumen Gesundheit D\u00FCngergebnis
HealthToolsViewModel.RumenHealthLocomotionTitle=Bewegungswertung
HealthToolsViewModel.RumenHealthBodyConditionTitle=K\u00F6rper-Zustand-Kerbe
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Metabolische Inzidenz
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&#8482;
ProductivityToolsViewModel.ProductivityTools=Werkzeuge
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Milch-Prozess Umsatz Rechner
ProductivityToolsViewModel.MilkRevenueAnalysis=Milchertragsanalyse
ProductivityToolsViewModel.ProductivityTitle=Produktivit\u00E4ts-tools
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Melkvorgang Einnahmen
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Eing\u00E4nge
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Ergebnisse
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Ressourcen
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Bezugs Chart
MilkProcessorResourcesViewModel.LinearScore=Lineare Punktzahl
MilkProcessorResourcesViewModel.ApproxSCC=Ungef\u00E4hre SCC (Zellen / ml)
MilkProcessorResourcesViewModel.Mastitis=Mastitismilch Verlust ({0})
DashboardViewModel.UserPreferences=Nutzerpreferenzen
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Konzentration
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Komponente
MilkProcessRevenueCalculatorMasterViewModel.Title=Melkvorgang Einnahmen
MilkProcessorInputViewModel.SelectProcessor=W\u00E4hlen Prozessor
MilkProcessorInputViewModel.ComparisonValues=Bearbeiten Vergleichswerte
MilkProcessorInputViewModel.Edit=Bearbeiten
MilkProcessorInputViewModel.ScenarioOne=Szenario 1
MilkProcessorInputViewModel.ScenarioTwo=Szenario 2
MilkProcessorEditComparisonValuesViewModel.Title=Bearbeiten Vergleichswerte
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Szenario 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Szenario 2
MilkProcessorResultsViewModel.ScenarioTwo=Szenario 2
MilkProcessorResultsViewModel.Change=Ver\u00E4nderung
MilkProcessorResultsViewModel.ResultsHeader=Jahreswert\u00E4nderungen
MilkProcessorResultsViewModel.HundredWeight=CWT
MilkProductionRevenue=Milchproduktion Umsatz
ClinicalMastitisLosses=Klinische Mastitis Verluste
MilkPricePremiums=Milchpreis Pr\u00E4mien
MilkLetDownResponse=Milch Let Down Antwort
TotalRevenue=Gesamtumsatz
VolumeImperial=Gallone
VolumeMetric=Milliliter
SelectProcessorViewModel.COMPONENT=Komponente
SelectProcessorViewModel.CONCENTRATION=Konzentration
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=CUD RECHNER
RumenHealthPenCudCalculatorViewModel.CudChewing=Wiederk\u00E4uen
CudChewingDataEntryViewModel.CudChewing=Wiederk\u00E4uen
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Anzahl der Kauartikel
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Erfassen Sie die Zahl der K\u00FChe Das Kauen Cud
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Capture The Anzahl der kaut pro Kuh
RumenHealthLandingViewModel.Title=Rumen Gesundheit wiederk\u00E4uen
RumenHealthLandingViewModel.Pens=Stifte Analyse
RumenHealthLandingViewModel.HerdAnalysis=Herd Analyse
HerdAnalysisMasterViewModel.HerdAnalysis=Pansen Gesundheit cud Kauen
MilkProcessorViewModel.NameNotUnique=Ein Component -Prozessor namens "{0} " existiert bereits. Namen m\u00FCssen eindeutig sein.
CudChewingHerdEditScoreViewModel.EditScoreTitle=Bearbeiten Sie wiederk\u00E4uen Noten
CudChewingHerdEditScoreViewModel.PercentChewingItem=Prozent Kauen
CudChewingHerdEditScoreViewModel.AverageChewsItem=Durchschnittliche Kaut Pro Cud
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Tage in Milch
VisitViewModel.VisitNotebook=Besuch Notebook
PileAndBunkerCapacityViewModel.VisitNotebook=Besuch Notebook
PileAndBunkerResultsMasterViewModel.VisitNotebook=Besuch Notebook
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Besuch Notebook
ProductivityToolsViewModel.VisitNotebook=Besuch Notebook
ForageAuditScorecardResultsViewModel.VisitNotebook=Besuch Notebook
PileAndBunkerResultsCapacityInputViewModel.Capacity=Kapazit\u00E4t
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Herd Analyse
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Herd Analyse
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Tore
Cow=Kuh 
ChewsPerCudMasterViewModel.NumOfChews=# Von Kaubonbons
CudChewsCalculatorViewModel.NumOfChews=# Von Kaubonbons
NumberOfChewsViewModel.NumberOfChewsCow=Anzahl der Kaubonbons / Kuh #
NumberOfChewsViewModel.CountHeader=Bitte z\u00E4hlen die Anzahl der kaut f\u00FCr diese Kuh.
NumberOfChewsViewModel.Count=Graf
NumberOfChewsViewModel.NextCow=N\u00E4chste Kuh
CudChewsCalculatorViewModel.CalculatorHeading=Bitte w\u00E4hlen Sie eine Kuh, die Anzahl der kaut zu z\u00E4hlen. Tippen Sie auf "Neu hinzuf\u00FCgen" oben hinzuf\u00FCgen K\u00FChe auf die Z\u00E4hlliste.
CudChewsCalculatorViewModel.CudChewCategorySection=K\u00FChe
ChewsPerCudMasterViewModel.AddNew=Neue hinzuf\u00FCgen
ChewsPerCudMasterViewModel.CudChewingInputs=Eing\u00E4nge
ChewsPerCudMasterViewModel.CudChewingResults=Ergebnisse
CudChewingHerdEditScoreViewModel.Close=Schlie\u00DFen
HerdAnalysisGoalsViewModel.Title=Herd Analyse
HerdAnalysisGoalsViewModel.CudChewingGoals=Wiederk\u00E4uen Tore
HerdAnalysisGoalsViewModel.DIM=DIM
HerdAnalysisGoalsViewModel.PercentChewing=% Kauen
HerdAnalysisGoalsViewModel.CudChews=Cud Kaut
HerdAnalysisGoalsViewModel.FarOffDry=Far-Off Dry
HerdAnalysisGoalsViewModel.CloseUpDry=Close-Up Dry
HerdAnalysisGoalsViewModel.Fresh=Frisch
HerdAnalysisGoalsViewModel.EarlyLactation=Fr\u00FChlaktation
HerdAnalysisGoalsViewModel.PeakMilk=Peak-Milch
HerdAnalysisGoalsViewModel.MidLactation=Mid Laktation
HerdAnalysisGoalsViewModel.LateLactation=Sp\u00E4tlaktation
HerdAnalysisGoalsViewModel.To=to
NotebookSectionRumenHealthLanding=Rumen Gesundheit
NotebookSectionHerdAnalysisGoals=Rumen Gesundheit - Herd Analyse Tore
CudChewingMasterViewModel.CudChewingInputs=Eing\u00E4nge
CudChewingMasterViewModel.CudChewingResults=Ergebnisse
CudChewingDataEntryViewModel.Yes=Ja
CudChewingDataEntryViewModel.No=Nein
CudChewingDataEntryViewModel.HerdCudChewingDescription=F\u00FCr diesen Besuch die Zahl der Tiere zu kauen wiederk\u00E4uen in diesem Stift mit dem Z\u00E4hler unten z\u00E4hlen, bitte. Sie m\u00FCssen mindestens 10 K\u00FChe z\u00E4hlen.
CudChewingMasterViewModel.CudChewing=Wiederk\u00E4uen
HerdAnalysisViewModel.TableTitle=Wiederk\u00E4uen Score-Analyse
HerdAnalysisViewModel.NumberofChewsPerCud=Number of Chews Per Cud
HerdAnalysisViewModel.HerdCudChewing=Herd Cud Chewing %
HerdAnalysisViewModel.EditLabel=Bearbeiten
HerdAnalysisViewModel.PercentChewing=Prozent Kauen
HerdAnalysisViewModel.AverageChews=Durchschnittliche kaut pro Cud
HerdAnalysisViewModel.DaysInMilk=Tage in Milch
HerdAnalysisViewModel.PenNameLabel=Stift name
HerdAnalysisViewModel.NoOfCows=Bitte geben Sie die Tiere pro Stift in die Stift-Einstellung ein.
EditNoteViewModel.DeleteImageButtonText=Bild l\u00F6schen
EditNoteViewModel.DeleteVideoButtonText=Video l\u00F6schen
EditNoteViewModel.NoteOnlyOneVideo=Nur ein Video pro Note erlaubt. Bitte l\u00F6schen Sie das aktuelle Video zuerst.
HerdAnalysisAnalysisTableTitle=Wiederk\u00E4uen Score-Analyse
HerdAnalysisViewModel.HerdAnalysisTableTitle=Wiederk\u00E4uen Score-Analyse
HerdAnalysisViewModel.Edit=Bearbeiten
Camcorder Funktion noch nicht implementiert
Date=Datum
EditNoteViewModel.NoteOnlyOneImage=Nur ein Bild pro Note erlaubt. Bitte l\u00F6schen Sie das aktuelle Bild zuerst.
EditNoteViewModel.NoteGalleryNotImplemented=Gallery Funktion noch nicht implementiert
EditNoteViewModel.NoteCamcorderNotImplemented=Camcorder Funktion noch nicht implementiert
EditNoteViewModel.Category=Kategorie
EditNoteViewModel.Observation=\u00DCberwachung
EditNoteViewModel.Action=Aktion
EditNoteViewModel.Task=Aufgabe
EditNoteViewModel.Event=Event
DatesForComparison=Termine f\u00FCr den Vergleich
Select=W\u00E4hlen
ThisVisit= (Dieser Besuch)
EditDatesForComparison=Bearbeiten Daten f\u00FCr Vergleich
EditDatesForComparisonViewModel.EditDatesTitle=Bearbeiten Daten f\u00FCr Vergleich
EditDatesForComparisonViewModel.EditDatesLabel=Bitte w\u00E4hlen Sie das Datum f\u00FCr den Vergleich f\u00FCr diesen Stift aus der Liste unten.
EditDatesForComparisonViewModel.EditDatesVisits=Besuche
EditDatesForComparisonViewModel.EditDatesClose=Schlie\u00DFen
EditDatesForComparisonViewModel.ManureScoreAverage=Durchschnittliche Dungs Ergebnis
EditDatesForComparisonViewModel.LocomotionScoreAverage=Durchschnittliche Fortbewegung Ergebnis:
EditDatesForComparisonViewModel.TimeRemainingForResting=Verbleibende Zeit zum Ausruhen:
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Bitte w\u00E4hlen Sie ein Datum aus, um es mit dem aktuellen Besuch zu vergleichen.
RumenHealthTMRLandingViewModel.Title=Rumen Gesundheit Particle Score
RumenHealthTMRLandingViewModel.Pens=Stifte Analyse
RumenHealthTMRLandingViewModel.HerdAnalysis=Herd Analyse
RumenHealthManureLandingViewModel.Title=Rumen Gesundheit Manure Score
RumenHealthManureLandingViewModel.Pens=Stifte Analyse
RumenHealthManureLandingViewModel.HerdAnalysis=Herd Analyse
RumenHealthLocomotionLandingViewModel.Title=Locomotion
RumenHealthLocomotionLandingViewModel.Pens=Stifte Analyse
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Herd Analyse
RumenHealthBodyConditionLandingViewModel.Title=K\u00F6rper-Zustand-Kerbe
RumenHealthBodyConditionLandingViewModel.Pens=Stifte Analyse
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Herd Analyse
BodyConditionScoreHerdGoalsViewModel.TableTitle=BCS nach Stadium der Laktation
BodyConditionScoreHerdGoalsViewModel.Edit=Bearbeiten
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Zielsetzung
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=BCS-Ziel Max
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Far-Off Dry (weniger als -21)
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Nahaufnahmen Trocken (-20 bis -1)
BodyConditionScoreHerdGoalsViewModel.Fresh=Frisch (0 bis 15)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Fr\u00FChe Laktation (16 bis 60)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=H\u00F6chstmilch (61 bis 120)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Mittlere Laktation (121 bis 200)
BodyConditionScoreHerdGoalsViewModel.LateLactation=Late Laktation (gr\u00F6\u00DFer als 201)
BodyConditionScoreHerdEditGoalsViewModel.Title=Ziele bearbeiten
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=BCS Min
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=BCS Max
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=FAR-OFF DRY (WENIGER ALS -21)
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=NAHAUFNAHMEN TROCKEN (-20 BIS -1)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=FRISCH (0 BIS 15)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=FR\u00DCHE LAKTATION (16 BIS 60)
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=H\u00D6CHSTMILCH (61 BIS 120)
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=MITTLERE LAKTATION (121 BIS 200)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=LATE LAKTATION (GR\u00D6\u00DFER ALS 201)
PenTimeBudgetComparisonViewModel.TableTitle=Vergleich
PenTimeBudgetComparisonViewModel.Current=Strom
PenTimeBudgetComparisonViewModel.CowsInPen=K\u00FChe in der Feder
PenTimeBudgetComparisonViewModel.Overcrowding=\u00DCberf\u00FCllung (%)
PenTimeBudgetComparisonViewModel.TimePerMilking=Zeit pro Melken (Stunden)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Wohnzimmer dreht sich pro Stunde
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=K\u00FChe gemischt pro Stunde
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Gesamtzeit Melken (Stunden)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Gehen zu finden Stall (Stunden)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Gesamte Ruhezeit (Stunden)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Verbleibende Zeit f\u00FCr Ruhe (Stunden)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Zeit f\u00FCr Ruhe (Stunden)
PenTimeBudgetComparisonViewModel.RestingDifference=Ruheunterschied (Stunden)
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=M\u00F6gliche Milchverluste / Gewinne ({0})
PenTimeBudgetComparisonViewModel.EnergyChange=Energiever\u00E4nderung (Mcals)
PenTimeBudgetComparisonViewModel.BodyWeightChange=K\u00F6rpergewicht \u00E4ndern ({0})
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Body Condition Score \u00C4nderung
RevenueEditComparisonValuesViewModel.EditComparisonValues=Bearbeiten Vergleichswerte
SelectProcessorViewModel.MilkProcessors=Milchverarbeiter
CudChewingViewModel.Title=Stifte
ChewsPerCudMasterViewModel.Title={0} / # von Kaubonbons
EditGoalsCudChewingViewModel.EditGoalsTitle=Bearbeiten Sie wiederk\u00E4uen Tore
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Bearbeiten Sie wiederk\u00E4uen Ergebnis
EditGoalsCudChewingViewModel.Close=N\u00E4he
EditGoalsCudChewingViewModel.PercentChewing=Prozent Kauen
EditGoalsCudChewingViewModel.CudChews=Durchschnittliche kaut Per Cud
EditGoalsCudChewingViewModel.FarOffDry=Far-Off Dry
EditGoalsCudChewingViewModel.CloseUpDry=Close-Up Dry
EditGoalsCudChewingViewModel.Fresh=Frisch
EditGoalsCudChewingViewModel.EarlyLactation=Fr\u00FChlaktation
EditGoalsCudChewingViewModel.PeakMilk=Peak-Milch
EditGoalsCudChewingViewModel.MidLactation=Mid Laktation
EditGoalsCudChewingViewModel.LateLactation=Sp\u00E4tlaktation
NumberOfChewsReportsViewModel.VisitDate=Datum
NumberOfChewsReportsViewModel.AverageNumberChews=Durchschnittliche Anzahl der Kaut
NumberOfChewsReportsViewModel.DatesComparison=Termine f\u00FCr Vergleich
NumberOfChewsReportsViewModel.EditVisits=Bearbeiten
NumberOfChewsReportsViewModel.DateDescription=Datum
NumberOfChewsReportsViewModel.Average=durchschnittliche Anzahl der kaut
MilkingProcessRevenueInputs=Melkvorgang Umsatz - Eing\u00E4nge
MilkingProcessRevenueResults=Melkvorgang Umsatz - Ergebnisse
MilkingProcessRevenueResources=Melkvorgang Umsatz - Ressourcen
NumberOfChewsViewModel.Title=Anzahl der Kaubonbons / Cow # {0}
ManureEditScores=D\u00FCngergebnisse - Ergebnisse bearbeiten
EditDatesForComparisonViewModel.Chews=Kaut
EditDatesForComparisonViewModel.Visits=Besichtigungen
EditDatesForComparisonViewModel.Title=Bearbeiten Sie wiederk\u00E4uen Tore
HerdReporting=Herd Berichterstattung: wiederk\u00E4uen
NumberOfChewsReportsViewModel.AverageChews=Durchschnittliche Kauen
BodyConditionHerdInputs=K\u00F6rperzustand Herd Analysis Eing\u00E4nge
BodyConditionHerdGoals=K\u00F6rper Zustand Herd Analyse Ziele
BodyConditionHerdResults=K\u00F6rper Zustand Herd Ergebnisse
FarOffDry=Far-Off Dry
CloseUpDry=Close-Up Dry
EarlyLactation=Fr\u00FChlaktation
PeakMilk=Peak-Milch
MidLactation=Mid Laktation
LateLactation=Sp\u00E4tlaktation
CudChewingPercentGoal={0}% Tor
NumChewsGoal={0} Tor
RumenHealthTMRSelectPenViewModel.SelectScorer=W\u00C4HLEN SIE EINEN SCORER
RumenHealthTMRSelectPenViewModel.SelectPen=STIFTE (LAKTIERENDEN UND TROCKEN NUR)
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Nichts ausgew\u00E4hlt
RumenHealthTMRSelectPenViewModel.Title=Rumen Gesundheit Particle Score
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Um einen Stift zu sehen, muss zuerst ein Scorer ausgew\u00E4hlt werden.
RumenHealthManureScoresResultsViewModel.Title=D\u00FCngergebnis Ergebnisse
RumenHealthTMRSelectScorerViewModel.SelectScorer=W\u00C4HLEN SIE EINEN SCORER
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Nichts ausgew\u00E4hlt
RumenHealthTMRSelectScorerViewModel.FooterText=NUR eine Punktzahl kann pro Besuch verwendet werden. Das \u00C4ndern der Punktzahl f\u00FChrt zu verlorenen Werten.
RumenHealthTMRSelectScorerViewModel.Title=Rumen Gesundheit Particle Score
NoneSelected=Nichts ausgew\u00E4hlt
FourScreenNew=4 Bildschirm Neu
ThreeScreen=3 Bildschirm
FourScreenOld=4 Bildschirm Alt
RumenHealthManureScoresViewModel.ManureScore=Dung Score
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Durchschnittliche Dungs Score (berechnet))
RumenHealthManureScoresViewModel.PercentOfPen=Prozent der Pen
RumenHealthManureScoresViewModel.AnimalsObserved=Tiere Beobachtet
RumenHealthManureScoresViewModel.Edit=bearbeiten
RumenHealthManureScoresViewModel.ScoreCategory=Score Kategorie
RumenHealthManureScoresResultsViewModel.PercentPen=Prozent der Stift (%)
RumenHealthManureScoresResultsViewModel.SelectedDates=Datum w\u00E4hlen
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Datum
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Durchschnittliche Punktzahl
RumenHealthManureMasterViewModel.VisitNotebook=besuchen notebok
RumenHealthManureMasterViewModel.RumenHealthManureScore=Rumen Gesundheit Dungs Score
ManureScorePenSelectionViewModel.PenSelectionList=STIFTE
ManureScorePenSelectionViewModel.ManureScoreTitle=Rumen Gesundheit D\u00FCnger Punktzahl
LocomotionSelectPenViewModel.Title=Fortbewegung
LocomotionSelectPenViewModel.PenSelectionList=Stifte
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Eing\u00E4nge
RumenHealthTMRPenScorerTableMasterViewModel.Results=Ergebnisse
RumenHealthTMRPenScorerTableMasterViewModel.Title=Rumen Gesundheit Particle Score
RumenHealthTMRParticleScorePenTableInputViewModel.Title=TMR Partikelwert
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Oben (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Mitte 1 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Mitte 2 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Mitte 2 (1.18 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Tablett
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Prozent auf dem Bildschirm (%)
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Scale-Betrag (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Tor - Min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Ziel - Max (%)
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Prozentsatz auf dem Bildschirm (%)
ParticleScorePreviousVisitsViewModel.SelectDates=W\u00E4hlen Sie Daten
ParticleScorePreviousVisitsViewModel.Top=Oben
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.MidOne=Mittel 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Mittel 2
ParticleScorePreviousVisitsViewModel.Tray=Tablett
PercentageOnScreen=Prozentsatz auf dem Bildschirm (%)
SelectDates=W\u00E4hlen Sie Daten
Top=Oben
TopValue=(19mm)
MidOne=Mittel 1
MidOneValue=(8mm)
MidTwo=Mittel 2
Tray=Tablett
FourScreenOldType=(1.18 mm)
FourScreenNewType=(4 mm)
PercentOnScreenTitle=Prozent auf dem Bildschirm (%)
RumenHealthEditManureScoresViewModel.NumberOfCows=Anzahl der K\u00FChe
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Bitte z\u00E4hlen Sie die Anzahl der K\u00FChe
RumenHealthEditManureScoresViewModel.Count=Graf
Category1=Dung Score Kategorie 1.0
Category2=Dung Score Kategorie 2.0
Category3=Dung Score Kategorie 3.0
Category4=Dung Score Kategorie 4.0
Category5=Dung Score Kategorie 5.0
RumenHealthEditManureScoresViewModel.VisitNotebook=besuchen notebok
MenuViewModel.ResetDatabaseTitle=Zur\u00FCcksetzen Testdaten
MenuViewModel.ResetDatabasePrompt="Dies ersetzt alle vorhandenen Daten, einschlie\u00DFlich der Benutzereinstellungen, mit einem Startsatz von Testdaten. Besuchen Sie Tools und neuer Besucher erstellt und Prozessoren verloren. Sie werden auch aus der App eingeloggt sein. Fortsetzen?"
MenuViewModel.ResetDatabaseReset=Zur\u00FCckstellen
MenuViewModel.ResetDatabaseCancel=Stornieren
Reset_Database">
Reset-Datenbank
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=BILDSCHIRM
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Oben (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Mittel 1 (8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Mittel 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Mittel 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Tablett
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Geben Sie den Tara-Betrag ein (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Tor - Min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Ziel - Max (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Geben Sie den Skalierungsbetrag ein (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Geben Sie den Skalierungsbetrag ein
BodyConditionScoresMasterViewModel.Title=K\u00F6rper-Zustand-Kerbe
BodyConditionScoresMasterViewModel.Inputs=Eing\u00E4nge
BodyConditionScoresMasterViewModel.Results=Ergebnisse
BodyConditionScoresMasterViewModel.BodyConditionScore=K\u00F6rper-Zustand-Kerbe
BodyConditionScoresMasterViewModel.VisitNotebook=Besuch Notebook
RumenHealthManureMasterViewModel.Inputs=Eing\u00E4nge
RumenHealthManureMasterViewModel.Results=Ergebnisse
RumenHealthManureMasterViewModel.Title=Rumen Gesundheit Manure Score
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=K\u00F6rper-Zustand-Kerbe (BCS)
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Durchschn. BCS (berechnet)
BodyConditionScoreInputsViewModel.BCSPercentOfPen=Prozent der Pen (%)
BodyConditionScoreInputsViewModel.BCSCategory=BCS-Kategorie
BodyConditionScoreInputsViewModel.AnimalsObserved=Tiere Beobachtet
BodyConditionScoreInputsViewModel.Edit=Bearbeiten
LocomotionPenMasterViewModel.Title=Fortbewegung
LocomotionPenMasterViewModel.Inputs=Eing\u00E4nge
LocomotionPenMasterViewModel.Results=Ergebnisse
LocomotionHerdMasterViewModel.Title=Bewegungswertung
LocomotionHerdMasterViewModel.SubHeading=Herd-Analyse
LocomotionHerdMasterViewModel.Inputs=Eing\u00E4nge
LocomotionHerdMasterViewModel.Revenue=Einnahmen
LocomotionHerdMasterViewModel.Results=Ergebnisse
BodyConditionScoreEditInputsViewModel.Title=Anzahl der K\u00FChe
BodyConditionScoreEditInputsViewModel.NumberOfCows=Anzahl der K\u00FChe
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Bitte z\u00E4hle die Anzahl der K\u00FChe.
BodyConditionScoreEditInputsViewModel.Count=Graf
BodyConditionScoreCategory=K\u00F6rper-Zustand-Kerbe-Kategorie {0}
BCSCategory1=K\u00F6rper-Zustand-Kerbe-Kategorie 1.0
BCSCategory1pt5=K\u00F6rper-Zustand-Kerbe-Kategorie 1.5
BCSCategory2=K\u00F6rper-Zustand-Kerbe-Kategorie 2.0
BCSCategory2pt5=K\u00F6rper-Zustand-Kerbe-Kategorie 2.5
BCSCategory3=K\u00F6rper-Zustand-Kerbe-Kategorie 3.0
BCSCategory3pt5=K\u00F6rper-Zustand-Kerbe-Kategorie 3.5
BCSCategory4=K\u00F6rper-Zustand-Kerbe-Kategorie 4.0
BCSCategory4pt5=K\u00F6rper-Zustand-Kerbe-Kategorie 4.5
BCSCategory5=K\u00F6rper-Zustand-Kerbe-Kategorie 5.0
BodyConditionInputs=K\u00F6rper-Zustand-Eing\u00E4nge
BodyConditionResults=K\u00F6rper Zustand Ergebnisse
NotebookBodyConditionEdit=K\u00F6rperzustand Tabelle bearbeiten
ShowEulaViewModel.Accept=Akzeptieren
ShowEulaViewModel.Decline=Ablehnen
ShowEulaViewModel.ConfirmationTitle=Best\u00E4tigung
ShowEulaViewModel.ConfirmationText=Sind Sie mit den Bedingungen des Mobile Application End User License Agreement einverstanden?
ShowEulaViewModel.ConfirmationYes=Ja
ShowEulaViewModel.ConfirmationNo=Nein
ShowEulaViewModel.EulaScreenTitle=Endbenutzerlizenz
SelectProcessorViewModel.Edit=Bearbeiten
LocomotionScore=Bewegungswertung
AvgLocomotionScore=Durchschn. Fortbewegungspunkt (berechnet)
LocoCategory1=Katze. 1,0
LocoCategory2=Katze. 2,0
LocoCategory3=Katze. 3,0
LocoCategory4=Katze. 4,0
LocoCategory5=Katze. 5,0
AnimalsObserved=Tiere beobachtet
LocomotionScoreHerd=Bewegungsanalyse
HerdGoal=Herd Ziel (%)
HerdAverage=Herd Durchschnitt (%)
TotalAnimalsHerd=Gesamt Tiere
LocomotionScoreAverage=Average Locomotion Score
LocomotionHerdInputsViewModel.Herd=HERD
LocomotionHerdEditGoalViewModel.Title=Zielmenge bearbeiten
LocomotionHerdEditGoalViewModel.HerdGoal=HERD ZIEL
LocomotionHerdEditGoalViewModel.Category1=Kategorie 1.0
LocomotionHerdEditGoalViewModel.Category2=Kategorie 2.0
LocomotionHerdEditGoalViewModel.Category3=Kategorie 3.0
LocomotionHerdEditGoalViewModel.Category4=Category 4.0
LocomotionHerdEditGoalViewModel.Category5=Category 5.0
PercentPen=Prozent der Pen (%)
TotalPenPerScore=Anzahl der Tiere in Pen pro Punktzahl
LocomotionPenInputsViewModel.FromPenSetup=Vom Stiftaufbau
TotalAnimals=Gesamt Tiere in Pen
DaysInMilkItem=Tage in der Milch (DIM)
MilkProductionPounds=Milchproduktion (lbs)
MilkProductionKg=Milchleistung (kg)
LocomotionPenInputsViewModel.Milk=MILCH
MilkLossPounds=Milchverlust (lbs)
MilkLossKg=Milchverlust (kg)
LocomotionPenInputsViewModel.LocomotionScoreReference=Lokalisierung Punktzahl
PercentLossPerCow=% Verlust / Kuh
AvgBCS=Durchschnittlich bcs
LocomotionEditTableViewModel.Title=Anzahl der K\u00FChe
LocomotionEditTableViewModel.EnterNumberOfCows=Bitte z\u00E4hle die Anzahl der K\u00FChe.
LocomotionEditTableViewModel.Category1=Bewegungsbewertung Kategorie 1.0
LocomotionEditTableViewModel.Category2=Bewegungsbewertung Kategorie 2.0
LocomotionEditTableViewModel.Category3=Bewegungsbewertung Kategorie 3.0
LocomotionEditTableViewModel.Category4=Bewegungsbewertung Kategorie 4.0
LocomotionEditTableViewModel.Category5=Bewegungsbewertung Kategorie 5.0
RumenHealthEditManureScoresViewModel.Close=schlie\u00DFen
Count=Graf
BCSPenSelectionViewModel.Title=K\u00F6rper-Zustand-Kerbe
BCSPenSelectionViewModel.Pens=Stifte
BodyConditionScoreResultsViewModel.BCSAverageTitle=Durchschnittliche Punktzahl
BodyConditionScoreResultsViewModel.Title=BCS Ergebnisse
BodyConditionScoreResultsViewModel.PercentPen=Prozent des Pen (%)
BodyConditionScoreResultsViewModel.SelectedDates=W\u00E4hlen Sie Daten
LocomotionPreviousVisitsViewModel.PercentPen=Prozent der Stift (%)
LocomotionPreviousVisitsViewModel.Title=Lokomotion Ergebnis Ergebnisse
LocomotionPreviousVisitsViewModel.SelectedDates=Datum w\u00E4hlen
LocomotionPreviousVisitsViewModel.AverageScore=Durchschnittliche Punktzahl
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Datum
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Durchschnittliche Punktzahl
ProspectsViewModel.NewProspect=Neuen Prospekt hinzuf\u00FCgen
VisitSummaryViewModel.Title=Seite besuchen Zusammenfassung
VisitSummaryViewModel.VisitTitle=Besuchen Sie Name
VisitSummaryViewModel.ComfortItem=Komfort
VisitSummaryViewModel.HealthItem=Gesundheit
VisitSummaryViewModel.NutritionItem=Ern\u00E4hrung
VisitSummaryViewModel.ProductivityItem=Produktivit\u00E4t
VisitSummaryViewModel.CategorySection=Werkzeugkategorien
VisitSummaryViewModel.ComfortHeatStressBanner=Hitze-Druck-Werkzeug-Feder
VisitSummaryViewModel.HeatstressEvaluationTitle=Hitzebest\u00E4ndigkeitsbewertung
VisitSummaryViewModel.RumenHealthTitle=Rumen Gesundheit Cud Kauen
VisitSummaryViewModel.RumenHealthTMRTitle=Rumen Gesundheit TMR Partikel Score
VisitSummaryViewModel.RumenHealthManureTitle=Rumen Gesundheit D\u00FCnger Punktzahl
VisitSummaryViewModel.RumenHealthLocomotionTitle=Bewegungswertung
VisitSummaryViewModel.RumenHealthBodyConditionTitle=K\u00F6rper-Zustand-Kerbe
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Metabolische Inzidenz
VisitSummaryViewModel.InputsOutputsChart=Eing\u00E4nge / Ausg\u00E4nge / Diagramme
VisitSummaryViewModel.NutritionForage=Futter\u00FCberwachung
VisitSummaryViewModel.NutritionPile=Stapel- und Bunkerkapazit\u00E4ten
VisitSummaryViewModel.MilkProcessRevenueCalculator=Milch Prozess Umsatzrechner
VisitSummaryViewModel.NoToolPrompt=Es wurden keine Werkzeuge bearbeitet.
VisitSummaryViewModel.VisitSummaryMilkCalc=Eingabe / Ergebnisse / Ressourcen
VisitSummaryViewModel.HerdAnalysis=Herd-Analyse
VisitSummaryViewModel.EmailReport=E-Mail-Bericht
VisitSummaryViewModel.FreeFormReport=Freier Formularbericht
ProspectProfileViewModel.MainHeading=STANDORTE
ProspectProfileViewModel.NewSite=Hinzuf\u00FCgen einer neuen Standort
ProspectProfileViewModel.DeleteProspect=Prospekt L\u00F6schen
ProspectProfileViewModel.DeleteProspectPrompt=M\u00F6chten Sie diesen Prospekt wirklich l\u00F6schen? Prospekt, Website und in Bearbeitung Besuch Informationen gehen verloren.
NewProspectViewModel.Title=Einzelheiten
NewProspectViewModel.ProspectDetail=PROSPEKTDETAILS
NewProspectViewModel.CustomersTitle=Kundendetails
NewProspectViewModel.BusinessName=FIRMENBEZEICHNUNG
NewProspectViewModel.Image=Tippen, um Bild zu bearbeiten
NewProspectViewModel.PrimaryContactFirstName=PRIMARY KONTAKT FIRST NAME
NewProspectViewModel.PrimaryContactLastName=PRIM\u00C4RKONTAKT LETZTER NAME
NewProspectViewModel.Address1=GESCH\u00C4FTSADRESSE 1
NewProspectViewModel.Address2=GESCH\u00C4FTSADRESSE 2
NewProspectViewModel.City=STADT
NewProspectViewModel.State=BUNDESLAND
NewProspectViewModel.PostalCode=POSTLEITZAHL
NewProspectViewModel.Country=LAND
NewProspectViewModel.PrimaryPhone=KONTAKT TELEFONNUMMER
NewProspectViewModel.EmailAddress=KONTAKT E-MAIL
NewProspectViewModel.Status=STATUS
NewProspectViewModel.Prospect=Aussicht
NewProspectViewModel.NameNotUnique=Ein Prospekt mit dem Namen "{0}" ist bereits vorhanden. Die Namen m\u00FCssen eindeutig sein.
NewProspectViewModel.NullBusinessName=Name des Unternehmens ist erforderlich. Bitte geben Sie einen Firmennamen ein, um fortzufahren
Last_Synced=Letzte Synchronisierung
FreeFormReportViewModel.Title=Freier Formularbericht
FreeFormReportViewModel.VisitTitle=Besuchen Sie Name
FreeFormReportViewModel.ExportSelected=Ausgew\u00E4hlte Werkzeuge exportieren
FreeFormReportViewModel.Inputs=Eing\u00E4nge
FreeFormReportViewModel.Results=Ergebnisse
FreeFormReportViewModel.Notes=Erl\u00E4uterungen 
FreeFormReportViewModel.Outputs=Ausg\u00E4nge
FreeFormReportViewModel.Charts=Charts
FreeFormReportViewModel.ComfortItem=Komfort-Werkzeug
FreeFormReportViewModel.HealthItem=Gesundheit Werkzeug
FreeFormReportViewModel.NutritionItem=Ern\u00E4hrung Werkzeug
FreeFormReportViewModel.ProductivityItem=Produktivit\u00E4tswerkzeug
FreeFormReportViewModel.ComfortHeatStressBanner=Hitze-Druck-Werkzeug-Feder
FreeFormReportViewModel.RumenHealthTitle=Rumen Gesundheit Cud Kauen
FreeFormReportViewModel.RumenHealthTMRTitle=Rumen Gesundheit TMR Partikel Score
FreeFormReportViewModel.RumenHealthManureTitle=Rumen Gesundheit D\u00FCnger Punktzahl
FreeFormReportViewModel.RumenHealthLocomotionTitle=Bewegungswertung
FreeFormReportViewModel.RumenHealthBodyConditionTitle=K\u00F6rper-Zustand-Kerbe
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Metabolische Inzidenz
FreeFormReportViewModel.NutritionForage=Futter\u00FCberwachung
FreeFormReportViewModel.NutritionPile=Stapel- und Bunkerkapazit\u00E4ten
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Stapel - und Bunkerzuf\u00FChrung
FreeFormReportViewModel.MilkProcessRevenueCalculator=Milch Prozess Umsatzrechner
FreeFormReportViewModel.OverallScore=Gesamtpunktzahl
FreeFormReportViewModel.OverallResponses=Gesamtreaktionen
FreeFormReportViewModel.OverallImprovements=Gesamtverbesserungen
FreeFormReportViewModel.MilkProcessCalcInputsTab=Milchprozess Einnahmen - Eing\u00E4nge
FreeFormReportViewModel.MilkProcessCalcResultsTab=Milchproze\u00DFumsatz - Ergebnisse
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Milchprozess Einnahmen - Ressourcen
FreeFormReportViewModel.MarketingBranding=Zum marktmarking gehen
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Provimi=Provimi
RevenueInputViewModel.Title=Milchprozess Einnahmen - Eing\u00E4nge
RevenueInputViewModel.Edit=Bearbeiten
MilkProcessorResultsViewModel.Title=Milchproze\u00DFumsatz - Ergebnisse
MilkProcessorResourcesViewModel.Title=Milchprozess Einnahmen - Ressourcen
FreeFormReportViewModel.GeneralNotes=Allgemeine Besichtigungshinweise
FreeFormReportViewModel.WalkThroughNotes=WalkThrough Notes
MenuViewModel.Sync_PopUp=Daten synchronisieren ..
Bitte halten Sie die App ge\u00F6ffnet, w\u00E4hrend die Synchronisierung l\u00E4uft. Bei langsameren Verbindungen kann die Synchronisierung l\u00E4nger dauern.
MenuViewModel.Menu=Men\u00FC
DashboardViewModel.GoodMorning=Guten Morgen, 
DashboardViewModel.GoodAfternoon=Guten Nachmittag, 
DashboardViewModel.MessageHeader=Nachricht
DashboardViewModel.MessageBody=Gerade eine Anzeige, zum des Notizbuches auf jedem Aufstellungsortbesuch zu verwenden, um Besonderheiten dieses bestimmten Besuchs zu dokumentieren.
DashboardViewModel.RecentSiteVisit=Letzte Besuche besucht
DashboardViewModel.Alert=Aufmerksam!
DashboardViewModel.AlertMessage=Daten wurden in mehr als {0} Tagen nicht synchronisiert.
ConfirmExport=Mit OK wird jede ausgew\u00E4hlte Seite geladen und ein Bild aufgenommen, bevor Sie zu diesem Bildschirm zur\u00FCckkehren.
ManureScoresChart=D\u00FCngerErgebnisse-Chart
ManureScoresResult=D\u00FCngerErgebnisse-Result
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Rumen Gesundheit Partikel
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Herd-Analyse
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Eing\u00E4nge
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Ergebnisse
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Oben
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19 mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Mittel 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8 mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Mitte 2
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18 mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4 mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Tablett
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=DIM
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Bearbeiten
TMRParticleScoreHerdAnalysisInputsViewModel.Title=TMR Partikelanalyse
TMRParticleScoreHerdAnalysisResultsText=TMR Partikelanalyse
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=DIM-Betrag bearbeiten
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Schlie\u00DFen
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Tage in der Milch (DIM)
BCSHerdAnalysisMasterViewModel.Title=K\u00F6rper-Zustand-Kerbe
BCSHerdAnalysisMasterViewModel.BCSTitle=K\u00F6rper-Zustand-Kerbe
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Herd-Analyse
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Eing\u00E4nge
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Ziele
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Ergebnisse
BCSHerdAnalysisInputsViewModel.BCSAnalysis=K\u00F6rper-Zustand-Kerbe-Analyse
BCSHerdAnalysisInputsViewModel.BCSEdit=Bearbeiten
BCSHerdAnalysisInputsViewModel.BCS=BCS
BCS=BCS
BCSHerdAnalysisInputsViewModel.BCSDIM=DIM
BCSHerdAnalysisInputsViewModel.BCSMilk=Milch
BCSEditMilkAndDimViewModel.Title=Bearbeiten DIM und Milch
BCSEditMilkAndDimViewModel.BCSDIMTitle=Tage in der Milch (DIM)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Milch
NotebookBCSHerdAnalysisInputs=BCS Herde Analyse Eing\u00E4nge
NotebookBCSHerdAnalysisGoals=BCS Herde Analyse Ziele
NotebookBCSHerdAnalysisResults=BCS Herde Analyse Ergebnisse
BodyConditionScoreMasterViewModel.Title=K\u00F6rper-Zustand-Kerbe
BodyConditionScoreMasterViewModel.SubHeading=Herd-Analyse
BodyConditionScoreMasterViewModel.Inputs=Eing\u00E4nge
BodyConditionScoreMasterViewModel.Goals=Ziele
BodyConditionScoreMasterViewModel.Results=Ergebnisse
TMRHerdAnalysisTableTitle=Rumen Gesundheit TMR Partikel Herde Analyse
BCSHerdAnalysisResultsViewModel.Title=K\u00F6rper-Zustand-Kerbe
BCSHerdAnalysisResultsViewModel.SubHeading=Herde Analyse
BCSHerdAnalysisResultsViewModel.GraphTitle=K\u00F6rper-Zustand-Score-Analyse
BCSHerdAnalysisResultsViewModel.MinBCS=Mindestens BCS
BCSHerdAnalysisResultsViewModel.MaxBCS=Maximales BCS
BCSHerdAnalysisResultsViewModel.BCSAvg=BCS Durchschnitt
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Milch/Kopf/Tag
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Oben (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Mitte 1 (8mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Mitte 2 (4mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Tablett
MilkProcessorInputViewModel.MilkPrice=Milch Preis ({0}/{1})
MilkProcessorInputViewModel.WeightImperialCWT=CWT
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Milk Price ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorResultsViewModel.MilkPrice=Milk Price ({0}/{1})
MilkProcessorResultsViewModel.WeightImperialCWT=CWT
MilkProcessorResultsViewModel.WeightMetric=kg
SyncFailed=Die Synchronisierung konnte zu diesem Zeitpunkt nicht abgeschlossen werden. Bitte versuchen Sie es erneut.
NoResourcesAvailable=Resource not available.
ConfirmScorerSwitch=Beim \u00C4ndern des Scores werden alle eingegebenen Daten zur\u00FCckgesetzt.
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Silage-AF-Dichte {0} (Tor: > 44)
VisitViewModel.NullVisitName=Der Name "Besuch" darf nicht leer sein. Bitte geben Sie einen Besuchsnamen ein.
SiteDetailViewModel.GeneralCustomerSiteSetup=Allgemeine Kundeneinrichtung
SiteDetailViewModel.AnimalInputsSite=Tier Eing\u00E4nge Seite
SiteDetailViewModel.DietInputsSiteLactating=Di\u00E4t-Inputs, Site (laktierende Tiere)
ManureScoreHerdAnalysisMasterViewModel.Inputs=Eing\u00E4nge
ManureScoreHerdAnalysisMasterViewModel.Goals=Tore
ManureScoreHerdAnalysisMasterViewModel.Results=Ergebnisse
ManureScoreHerdAnalysisMasterViewModel.Title=Pansen GesundheitD\u00FCngergebnis
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Herd-Analyse
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=D\u00FCngergebnisanalyse
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Bearbeiten
ManureScoreHerdAnalysisInputsViewModel.ManureScore=D\u00FCngergebnis
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Tage in der Milch
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Tage in der Milch
ManureScoreHerdAnalysisEditInputsViewModel.Title=Bearbeiten DIM Menge
ManureScoreHerdAnalysisEditInputsViewModel.Close=Schlie\u00DFen
NotebookLocomotionHerdGoals=Fortbewegung Herd Ziele
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=D\u00FCngergebnisanalyse
SCCPremiumDeduction=SCC Premium / Abzug ({0}/{1})
LocomotionHerdResultsViewModel.Title=Bewegungsanalyse
LocomotionHerdRevenueViewModel.Revenue=Einnahmen
AverageMilkLoss=Durchschnittlicher Milchverlust ({0})
MilkLossDay=Milchverlust ({0}/Tag)
MilkLossYear=Milchverlust ({0}/Jahr)
RevenueLossDay=Einnahmenverlust ({0}/Tag)
RevenueLossYear=Einnahmenverlust ({0}/Jahr)
CustomerProspectsSegmentViewModel.Title=Details
CustomerProspectsSegmentViewModel.SelectSegment=W\u00E4hlen Sie ein Segment aus
CustomerProspectsSegmentViewModel.NotSet= - 
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=End User
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Walton=Walton
ManureScoreHerdGoalsViewModel.TableTitle=Ergebnis nach Stadium der Laktation
ManureScoreHerdGoalsViewModel.Edit=Bearbeiten
ManureScoreHerdGoalsViewModel.GoalMinTitle=D\u00FCngen Tor min
ManureScoreHerdGoalsViewModel.GoalMaxTitle=D\u00FCngen Ziel max
ManureScoreHerdGoalsViewModel.FarOffDry=Weit entfernt trocken (Weniger als -21)
ManureScoreHerdGoalsViewModel.CloseUpDry=Nahaufnahmen Trocken (-20 bis -1)
ManureScoreHerdGoalsViewModel.Fresh=Frisch (0 bis 15)
ManureScoreHerdGoalsViewModel.EarlyLactation=Fr\u00FChe Laktation (16 bis 60)
ManureScoreHerdGoalsViewModel.PeakMilk=H\u00F6chstmilch (61 bis 120)
ManureScoreHerdGoalsViewModel.MidLactation=Mittlere Laktation (121 bis 200)
ManureScoreHerdGoalsViewModel.LateLactation=Late Laktation (gr\u00F6\u00DFer als 201)
ManureScoreHerdEditGoalsViewModel.Title=Ziele bearbeiten
ManureScoreHerdEditGoalsViewModel.MinGoal=D\u00FCnger Min
ManureScoreHerdEditGoalsViewModel.MaxGoal=D\u00FCnger Max
ManureScoreHerdEditGoalsViewModel.FarOffDry=FAR AUS TROCKEN (WENIGER ALS -21)
ManureScoreHerdEditGoalsViewModel.CloseUpDry=SCHLIESSEN SIE TROCKNEN (-20 bis -1)
ManureScoreHerdEditGoalsViewModel.Fresh=FRISCH (0 bis 15)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=FR\u00DCHERE LACTATION (16 bis 60)
ManureScoreHerdEditGoalsViewModel.PeakMilk=PEAK MILCH (61 bis 120)
ManureScoreHerdEditGoalsViewModel.MidLactation=MID LACTATION (121 bis 200)
ManureScoreHerdEditGoalsViewModel.LateLactation=LATE LACTATION (GR\u00D6SSER ALS 201)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=schlie\u00DFen
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Mindestpunktzahl
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Maximale Punktzahl
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Durchschnittlich
ManureScoreHerdAnalysisResultsViewModel.ManureScore=D\u00FCngergebnis
LocomotionHerdResultsViewModel.Category1=Kategorie 1.0
LocomotionHerdResultsViewModel.Category2=Kategorie 2.0
LocomotionHerdResultsViewModel.Category3=Kategorie 3.0
LocomotionHerdResultsViewModel.Category4=Kategorie 4.0
LocomotionHerdResultsViewModel.Category5=Kategorie 5.0
LocomotionHerdResultsViewModel.HerdAverage=Herd Durchschnitt
LocomotionHerdResultsViewModel.HerdGoal=Tor
MetabolicIncidenceMasterViewModel.Title=Metabolische Inzidenz
MetabolicIncidenceMasterViewModel.Inputs=Eing\u00E4nge
MetabolicIncidenceMasterViewModel.Outputs=Ausg\u00E4nge
MetabolicIncidenceMasterViewModel.Charts=Charts
EulaScreenTitle=EULA
ShowEulaViewModel.Eula=EULA
ShowEulaViewModel.EulaError=Die Endbenutzer-Lizenzvereinbarung kann nicht angezeigt werden. Bitte verbinde dich mit dem Internet, bevor du es versuchst.
NoResults=Keine Ergebnisse gefunden
Search=Suche
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Metabolische Inzidenz Prozent
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Bearbeiten
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=H\u00E4ufigkeit (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Tor (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Unterschied (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=\u00D6konomische Auswirkungen der metabolischen Inzidenzwerte \u00FCber die definierten Ziele f\u00FCr die Herde.
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=J\u00E4hrliche wirtschaftliche Auswirkungen
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Milchverlustwert
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Erh\u00F6hte Tage offen
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Behandlungskosten
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=J\u00E4hrliche wirtschaftliche Auswirkungen - Gesamt
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Gesamtkosten
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Kosten / Kuh
MetabolicIncidenceOutputsViewModel.Title=Metabolische Inzidenzausg\u00E4nge
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Tor (%)
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Beibehaltene Plazenta
MetabolicIncidenceEditOutputsViewModel.Metritis=Metritis
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Vertriebenes Abomasum
MetabolicIncidenceEditOutputsViewModel.Ketosis=Ketose
MetabolicIncidenceEditOutputsViewModel.MilkFever=Milchfieber
MetabolicIncidenceEditOutputsViewModel.Dystocia=Dystokia
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Todesverlust
MetabolicIncidenceEditOutputsViewModel.Title=Ziele bearbeiten
MetabolicIncidenceEditOutputsViewModel.Close=Schlie\u00DFen
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Beibehaltene Plazenta
MetabolicIncidenceOutputsViewModel.Metritis=Metritis
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Vertriebenes Abomasum
MetabolicIncidenceOutputsViewModel.Ketosis=Ketose
MetabolicIncidenceOutputsViewModel.MilkFever=Milchfieber
MetabolicIncidenceOutputsViewModel.Dystocia=Dystokia
MetabolicIncidenceOutputsViewModel.DeathLoss=Todesverlust
MetabolicIncidenceOutputsViewModel.TotalLosses=Gesamtjahresverluste
MetabolicIncidenceInputsViewModel.Herd=HERD LEVEL INFORMATIONEN
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Summe frische K\u00FChe / Jahr
MetabolicIncidenceInputsViewModel.MilkPrice=Milchpreis
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Ersatzkuh Kosten
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Kosten f\u00FCr zus\u00E4tzliche Tage ge\u00F6ffnet
MetabolicIncidenceInputsViewModel.IncidenceCases=Metabolische Inzidenzf\u00E4lle
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Geben Sie die Anzahl der frischen K\u00FChe und die Anzahl der metabolischen Inzidenzf\u00E4lle w\u00E4hrend des Auswertungszeitraums ein. Dies wird auf der Registerkarte Ausg\u00E4nge zu einem annualisierten Inzidenzkosten umgerechnet.
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Insgesamt frische K\u00FChe f\u00FCr die Bewertung
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Beibehaltene Plazenta
MetabolicIncidenceInputsViewModel.Metritis=Metritis
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Vertriebenes Abomasum
MetabolicIncidenceInputsViewModel.Ketosis=Ketose
MetabolicIncidenceInputsViewModel.MilkFever=Milchfieber
MetabolicIncidenceInputsViewModel.Dystocia=Dystokia
MetabolicIncidenceInputsViewModel.DeathLoss=Todesverlust
MetabolicIncidenceInputsViewModel.PerformanceTreatment=LEISTUNGS- UND BEHANDLUNGSKOSTEN
MetabolicIncidenceInputsViewModel.PerformanceMessage=Referenzdaten zur Berechnung der \u00F6konomischen Auswirkungen jeder metabolischen Inzidenz.
MetabolicIncidenceInputsViewModel.Costs=Kosten
MetabolicIncidenceInputsViewModel.MilkLossKg=Milchverlust pro Laktation ({0})
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Erh\u00F6hte Tage offen
MetabolicIncidenceInputsViewModel.TreatmentCost=Behandlungskosten
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Beibehaltene Plazenta
MetabolicIncidenceInputsEditViewModel.Metritis=Metritis
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Vertriebenes Abomasum
MetabolicIncidenceInputsEditViewModel.Ketosis=Ketose
MetabolicIncidenceInputsEditViewModel.MilkFever=Milchfieber
MetabolicIncidenceInputsEditViewModel.Dystocia=Dystokia
MetabolicIncidenceInputsEditViewModel.DeathLoss=Todesverlust
MetabolicIncidenceInputsEditViewModel.Title=Bearbeiten Sie die Kostenattribute
MetabolicIncidenceInputsEditViewModel.Close=Schlie\u00DFen
MetabolicIncidenceInputsEditViewModel.MilkCow=Milch / Kuh ({0})
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Tage offen
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Behandlungskosten
MetabolicIncidenceInputsViewModel.Title=Metabolische Inzidenz Eing\u00E4nge
PenTimePenSelectionViewModel.PenTimeSection=STIFTE
PenTimePenSelectionViewModel.Title=Pen Zeit Budget
EmailReportViewModel.MarketingBranding=Zum marktmarking gehen
EmailReportViewModel.Title=E-Mail-Bericht
EmailReportViewModel.UserPreferences=Benutzereinstellungen
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.Purina=Purina
EmailReportViewModel.ExportSelected=Email Ausgew\u00E4hlte Werkzeuge
EmailReportViewModel.GotoMarketBranding=Gehen-Sie zum-Markt Branding
EmailReportViewModel.EmailSelectedTools=Email Ausgew\u00E4hlte Werkzeuge
EmailReportViewModel.UserSettings=Benutzereinstellungen
EmailReportViewModel.ComfortHeatStressBanner=Hitze-Druck-Werkzeug-Feder
EmailReportViewModel.ComfortToolsTitle=Komfort Werkzeuge
EmailReportViewModel.RumenHealthTitle=Pansen Gesundheit wiederk\u00E4uen
EmailReportViewModel.HealthItem=Gesundheit Werkzeug
EmailReportViewModel.Herd=Herde
EmailReportViewModel.RumenHealthTMRTitle=Rumen Gesundheit TMR Partikel Herde Ergebnis
EmailReportViewModel.RumenHealthManureTitle=Rumen Gesundheit D\u00FCnger Punktzahl
EmailReportViewModel.RumenHealthLocomotionTitle=Bewegungswertung
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Metabolische Inzidenz
EmailReportViewModel.NutritionForage=Futter\u00FCberwachung
EmailReportViewModel.NutritionItem=Ern\u00E4hrung
EmailReportViewModel.ForageAuditScorecard=Futter Audit Scorecard
EmailReportViewModel.MilkProcessRevenueCalculator=Milch Prozess Umsatzrechner
EmailReportViewModel.ProductivityItem=Produktivit\u00E4tswerkzeug
EmailReportViewModel.MilkProcessCalcInputsTab=Milchprozess Einnahmen: Eing\u00E4nge
EmailReportViewModel.MilkProcessCalcResultsTab=Milchproze\u00DFumsatz: Ergebnisse
EmailReportViewModel.MilkProcessCalcResourcesTab=Milchprozess Einnahmen: Ressourcen
EmailReportViewModel.NutritionPile=Stapel- und Bunkerkapazit\u00E4ten
EmailReportViewModel.EmailSubject={0} Report
EmailReportViewModel.EmailBody={0}-{1} Report
MetabolicIncidenceChartsViewModel.Title=Metabolische Inzidenzdiagramme
MetabolicIncidenceChartsViewModel.GraphTitle=Metabolische Inzidenz %
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Zur\u00FCckgezogene Plazenta
RetainedPlacenta=Zur\u00FCckgezogene Plazenta
MetabolicIncidenceChartsViewModel.Metritis=Metritis
Metritis=Metritis
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Vertriebenes Abomasum
DisplacedAbomasum=Vertriebenes Abomasum
MetabolicIncidenceChartsViewModel.Ketosis=Ketose
Ketosis=Ketose
MetabolicIncidenceChartsViewModel.MilkFever=Milchfieber
MilkFever=Milchfieber
MetabolicIncidenceChartsViewModel.Dystocia=Dystokia
Dystocia=Dystokia
MetabolicIncidenceChartsViewModel.DeathLoss=Todesverlust
DeathLoss=Todesverlust
MetabolicIncidenceChartsViewModel.GoalPercent=Tor (%)
MetabolicIncidenceChartsViewModel.IncidencePercent=H\u00E4ufigkeit (%)
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Stoffwechselst\u00F6rung Kosten / Kuh
MetabolicIncidenceChartsViewModel.Current=Strom
EditDatesForComparisonViewModel.MetabolicIncidence=Bitte w\u00E4hlen Sie bis zu 5 Termine f\u00FCr den Vergleich aus der Liste unten.
PenTimeInputsViewModel.CowsPen=K\u00FChe in der Feder
PenTimeInputsViewModel.StallsPen=St\u00E4nde in der Feder
PenTimeInputsViewModel.WalkingTimeTo=Gehzeit zum Wohnzimmer (Stunden)
PenTimeInputsViewModel.ParlorTime=Zeit im Wohnzimmer (Stunden)
PenTimeInputsViewModel.WalkingTimeFrom=Gehzeit vom Salon (Stunden)
PenTimeInputsViewModel.Frequency=Melkfrequenz (pro Tag)
PenTimeInputsViewModel.TotalStalls=Insgesamt St\u00E4nde im Wohnzimmer
PenTimeInputsViewModel.LockUp=Zeit in Lock-Up (Stunden)
PenTimeInputsViewModel.NonRestTime=Andere Nicht-Ruhezeit (Stunden)
PenTimeInputsViewModel.Resting=Ruheanforderung (Stunden)
PenTimeInputsViewModel.Eating=Essen Zeit (Stunden)
PenTimeInputsViewModel.Drinking=Trinken / pflegen Zeit (Stunden)
ChooseAppPDF=Bitte w\u00E4hlen Sie eine App, um die PDF zu sehen
PenTimeBudgetResultsViewModel.Title=Pen Time Budget Ergebnisse
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Zeit zum Ausruhen
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Potential Milk Loss/Gain
PenTimeBudgetResultsViewModel.TimeRequired=Potentieller Milchverlust/Gewinn
PenTimeBudgetResultsViewModel.TimeRemaining=Verbleibende Zeit
PenTimeBudgetResultsViewModel.Hours=Std
PenTimeBudgetResultsViewModel.MilkDifference=M\u00F6gliche Milchdifferenz
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=lbs
TimeRemaining=Verbleibende Zeit zum Ausruhen
MilkLossGain=Potentieller Milchverlust/Gewinn
EditDatesForComparisonViewModel.PenTimeBudget=Bitte w\u00E4hlen Sie bis zu 7 Termine f\u00FCr den Vergleich aus der Liste unten.
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Datenschutzerkl\u00E4rung
WalkthroughReportViewModel.PenAnalysis=Pen-Analyse
WalkthroughReportViewModel.HerdAnalysis=Herd Analyse
NoteCategoryViewModel.Title=Hinweis
NoteCategoryViewModel.SelectCategory=W\u00C4HLEN SIE EINE KATEGORIE
NoteCategoryViewModel.FooterText=NUR eine Kategorie kann pro Note ausgew\u00E4hlt werden.
Observation=\u00DCberwachung
Action=Aktion
WalkthroughReportLandingViewModel.Title=Exemplarbericht
WalkthroughReportLandingViewModel.PenAnalysis=Pen-Analyse
WalkthroughReportLandingViewModel.HerdAnalysis=Herd Analyse
WalkthroughPenSelectionViewModel.Title=Exemplarbericht
WalkthroughPenSelectionViewModel.Pens=STIFTE
Task=Aufgabe
Event=Event
SiteVisitSummaryReport=Seitenbesuch Zusammenfassung Bericht
Customer=Kunde
VisitDate=Datum des Besuchs
ReportDate=Berichtsdatum
Report=Bericht
PDFPageNumber=Seite {0} von {1}
PDFDisclaimer=Cargill Incorporated, seine Eltern und Tochtergesellschaften garantiert nicht die Genauigkeit dieser Sch\u00E4tzungen, aufgrund vieler Faktoren. Es gibt keine Garantie f\u00FCr Produktions- oder Finanzergebnisse. \u00A9 {0} Cargill, Incorporated Alle Rechte vorbehalten
SiteVisitSummary=Seitenbesuch Zusammenfassung
WalkthroughReportViewModel.Title=Walkthrough-Bericht
WalkthroughReportViewModel.CudChewing=Rumining,% Kauen
WalkthroughReportViewModel.CudChewCategorySection=Cud Counts, Chews pro Cud
WalkthroughReportViewModel.RumenHealthManureTitle=D\u00FCngerleistung
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Fortbewegungspunkt
WalkthroughReportViewModel.HockAbrasion=Hock Abrieb,% der Tiere
WalkthroughReportViewModel.Appearance=Das Auftreten
WalkthroughReportViewModel.RumenFill=Rumen f\u00FCllen
WalkthroughReportViewModel.UterineDischarge=Uterusentladung,% der Tiere
WalkthroughReportViewModel.NasalDischarge=Nasale Entladung,% der Tiere
WalkthroughReportViewModel.ComfortItem=Kuhkomfort,% der Tiere legen
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=K\u00F6rper Kondition Score (BCS)
WalkthroughReportViewModel.WaterQuality=Wasserqualit\u00E4t
WalkthroughReportViewModel.BeddingCleanliness=Bettw\u00E4sche Sauberkeit
WalkthroughReportViewModel.BeddingDepthSoft=Bettw\u00E4sche: Tiefe und Weichheit
WalkthroughReportViewModel.Trends=Positive tendenzen
WalkthroughReportViewModel.Opportunities=Chancen
WalkthroughReportViewModel.Comments=Bemerkungen
WalkthroughReportViewModel.Current=Strom
WalkthroughReportViewModel.Previous=Bisherige
WalkthroughReportViewModel.Goals=Tor
WalkthroughReportViewModel.Clean=Reinigen
WalkthroughReportViewModel.ModeratelyClean=M\u00E4\u00DFig sauber
WalkthroughReportViewModel.Dirty=Dreckig
WalkthroughReportQualityViewModel.Title=Walkthrough-Bericht
WalkthroughReportQualityViewModel.Clean=Reinigen
WalkthroughReportQualityViewModel.ModeratelyClean=M\u00E4\u00DFig sauber
WalkthroughReportQualityViewModel.Dirty=Dreckig
WalkthroughReportQualityViewModel.WaterQuality=W\u00E4hlen Sie Wasserqualit\u00E4t
WalkthroughReportQualityViewModel.BeddingCleanliness=W\u00E4hlen Sie Bettw\u00E4sche Sauberkeit
EmailReportViewModel.HeatstressEvaluationTitle=Heatstress Bewertung
EmailReportViewModel.Inputs=Eing\u00E4nge
EmailReportViewModel.Charts=Charts
EmailReportViewModel.CudChewingTitle=Cud Kauen
EmailReportViewModel.Results=Ergebnisse
EmailReportViewModel.NumOfChews=Anzahl der Kauen
EmailReportViewModel.PenInputs=Pen-Analyse - Inputs
EmailReportViewModel.PenResults=Pen-Analyse - Ergebnisse
EmailReportViewModel.HerdInputs=Herdanalyse - Eing\u00E4nge
EmailReportViewModel.HerdResults=Herdanalyse - Ergebnisse
EmailReportViewModel.TMRParticleScoreTitle=Rumen Gesundheit Partikel Punktzahl
EmailReportViewModel.HerdGoals=Herdanalyse - Tore
EmailReportViewModel.ManureScoreTitle=D\u00FCngerleistung
EmailReportViewModel.LocomotionScoreTitle=Fortbewegungspunkt
EmailReportViewModel.HerdRevenue=Herdanalyse - Einnahmen
EmailReportViewModel.RumenHealthBodyConditionTitle=K\u00F6rperbedingung
EmailReportViewModel.MetabolicIncidenceTitle=Metabolische Inzidenz
EmailReportViewModel.Outputs=Ausg\u00E4nge
EmailReportViewModel.PileAndBunkerTitle=Stapel und Bunker
EmailReportViewModel.Capacity=Kapazit\u00E4t
EmailReportViewModel.FeedOut=Ausf\u00E4deln
LocomotionHerdRevenueViewModel.Title=Fortbewegung Herd Einnahmen
EmailReportViewModel.PenTimeTitle=Pen Zeit Budget
EmailReportViewModel.PenCompare=Pen-Analyse - Vergleichen
EmailReportViewModel.MilkProcessRevenue=Milchprozess Umsatzrechner
EmailReportViewModel.Resources=Ressourcen
EmailReportViewModel.CategoryList=Kategorie-Liste
EmailReportViewModel.Improvements=Verbesserungen
EmailReportViewModel.ScoreScreen=Noten
FinalObservations=Endg\u00FCltige Bemerkungen
Trends=Positive Trends
Opportunities=Chancen
Comments=Bemerkungen
Current=Strom
Previous=Bisherige
Goal=Tor
Clean=Reinigen
ModeratelyClean=M\u00E4\u00DFig sauber
Dirty=Dreckig
WalkthroughReportHerdAnalysisViewModel.CudChewing=Rumining, % Kauen
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Cud Counts, Chews pro Cud
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=D\u00FCngerleistung
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Fortbewegungspunkt
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Hock Abrieb,% der Tiere
WalkthroughReportHerdAnalysisViewModel.Appearance=Das Auftreten
WalkthroughReportHerdAnalysisViewModel.RumenFill=Rumen f\u00FCllen
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Uterusentladung,% der Tiere
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Nasale Entladung,% der Tiere
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Kuhkomfort, % der Tiere legen
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=K\u00F6rperbedingung (BCS)
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Wasserqualit\u00E4t
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Bettw\u00E4sche Sauberkeit
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Bettw\u00E4sche: Tiefe und Weichheit
WalkthroughReport=Walkthrough-Bericht
SiteDetailViewModel.ReportNotAvailable=Bericht nicht zum Download verf\u00FCgbar.
SiteDetailViewModel.ReportNotAvailableTitle=Status
SiteDetailViewModel.GetReportMsg=Bericht herunterladen ...
PublishVisit=Ver\u00F6ffentlicht
ToolNotSelected=Sie m\u00FCssen mindestens ein Werkzeug ausw\u00E4hlen
ReadyToMilkMasterViewModel.Inputs=Eing\u00E4nge
ReadyToMilkMasterViewModel.Outputs=Ausg\u00E4nge
ReadyToMilkMasterViewModel.Charts=Charts
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Ready2Milk&#8482; Index
ReadyToMilkChartViewModel.RetainedPlacenta=Zur\u00FCckgezogene Plazenta
ReadyToMilkChartViewModel.Metritis=Metritis
ReadyToMilkChartViewModel.DisplacedAbomasum=Vertriebenes Abomasum
ReadyToMilkChartViewModel.Ketosis=Ketose
ReadyToMilkChartViewModel.MilkFever=Milchfieber
ReadyToMilkChartViewModel.Dystocia=Dystokia
ReadyToMilkChartViewModel.DeathLoss=Todesverlust
NewProspectViewModel.NotSet= - 
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EndUser=End User
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Noah=Noah
NewProspectViewModel.Spence=Spence
NewProspectViewModel.Walton=Walton
Global=Global
France=Frankreich
CPNFrance=Frankreich
Netherlands=Niederlande
Spain=Spanien
Italy=Italien
Korea=Korea
CPNBrazil=Brasilien
NorthAmerica=Nordamerika
Portugal=Portugal
CFNChina=China
CFNIndia=Indien
CPNPoland=Polen
CPNUS=Vereinigte Staaten
Screen=Screen
ScreenOld=Screen Old
ScreenNew=Screen New
Straw=Straw
Dryhay=Dry Hay
Haylage=Haylage/Grass
Corn=Corn
Other=Other
Report.SheetName=
Report.BCS.EvalDataTitle=
Report.EvalDataTitle=
Report.Bcs.Min=
Report.BCS.Max=
Report.BCS.MilkHeadDay=
Report.BCSAvg=
Report.BCS.LactationStages=
Report.Visit.name=
Report.Visit.Date=
Report.Tool.Name=
Report.Analysis.Type=
Report.Bcs.HerdAnalysis.ChartName=
Report.Bcs=
Report.Bcs.Milk=
Report.CudChewing.EvalDataTitle=
Report.CudChewingPercentage=
Report.No.OfChews=
Report.No.OfChewsPerRegurgitation=
Report.goalChews=
Report.Chews=
Report.GoalCudChewingPercentage=
Report.CudChewingPercentage.Vs.LactStages=
Report.NoOfChews.Vs.LactStages=
Report.Herd.Analysis.CudChewingPercentage=
Report.locomotionScore.Pen.Analysis.ChartName=
Report.Bcs.ChartName=
Report.Animal.Tag.Name=
Report.Calving.Date=
Report.LocomotionScore.chartName=
Report.Locomotion.HerdAnalysis.ChartName=
Report.LocomotionScore.X.Axis=
Report.LocomotionScore.Y.Axis=
Lactation=
FreshCow=
DryCow=
TransitionCow=
Report.AvgRumenFillScore=
Report.ForagePennState=
Report.PercentageOnScreen=
TopUnloadingSilo=
BottomUnloadingSilo=
Bag=
NotSet= - 
AMSUtilization=
CowEfficiency=
ConcentrateDistribution=
AMSUtilizationChart=
CowPerRobot=
MilkingFailure=
SelectVisitComparison=
Lift-Sync-Fail=Sync failed due to unknown reasons; please contact the admin for resolution
No-User-Found= USER not found on LIFT; please contact the admin for resolution
Account-Not-Synced-To-Lift= Account was not synced to LIFT; please contact the admin for resolution
Site-Not-Synced-To-Lift= Site was not synced to LIFT; please contact the admin for resolution
Clean=Clean
Moderate=Moderate
Dirty=Dirty
Holandesa=Holandesa
Jersey=Jersey
Girolando=Girolando
Good=Good
Medium=Medium
Bad=Bad
Freestall=Freestall
Compostbarn=Compostbarn
Semiconfinamento=Semiconfinamento
Pasto=Pasto
Noah=Noah
ProfitablityAnalysis.Date=Date
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
Agridea=Agridea
RaggioDiSole=Raggio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture Corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada


