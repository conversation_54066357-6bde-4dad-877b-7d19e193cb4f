FROM amazoncorretto:19-alpine3.16

RUN apk update && apk upgrade --available && sync

# RUN apk add --update libgcrypt
RUN apk --no-cache add postgresql-client
RUN apk add zlib
RUN apk add --no-cache freetype fontconfig ttf-dejavu  font-noto font-noto-cjk font-noto-extra  font-noto  \
    font-noto-thai font-noto-tibetan font-ipa font-sony-misc font-daewoo-misc font-jis-misc font-isas-misc

#Step 1 : Install the pre-requisite for PlayWright
# Set up glibc
ENV LANG=en_US.UTF-8
ENV LANGUAGE=en_US:en
ENV LC_ALL=en_US.UTF-8
ENV GLIBC_REPO=https://github.com/sgerrand/alpine-pkg-glibc
ENV GLIBC_VERSION=2.34-r0
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

RUN set -ex && \
    apk add --force-overwrite  libstdc++ curl ca-certificates && \
    for pkg in glibc-${GLIBC_VERSION} glibc-bin-${GLIBC_VERSION}; \
        do curl -sSL ${GLIBC_REPO}/releases/download/${GLIBC_VERSION}/${pkg}.apk -o /tmp/${pkg}.apk; done && \
    apk add --force-overwrite  --allow-untrusted /tmp/*.apk && \
    rm -v /tmp/*.apk && \
    /usr/glibc-compat/sbin/ldconfig /lib /usr/glibc-compat/lib

# Install prerequisites and helper packages
RUN apk add --no-cache \
	bash dpkg xeyes

# Install Chrome dependencies
RUN apk add --no-cache \
    udev \
    ttf-freefont \
	alsa-lib \
	atk \
	at-spi2-atk \
	expat \
	glib \
	gtk+3.0 \
	libdrm \
	libx11 \
	libxcomposite \
	libxcursor \
	libxdamage \
	libxext \
	libxi \
	libxrandr \
	libxscrnsaver \
	libxshmfence \
	libxtst \
	mesa-gbm \
	nss \
	pango\
	chromium \
	chromium-chromedriver \
	icu-dev \
	icu-data-full
	
# Create a non-root user
RUN addgroup -S appgroup && adduser -S -G appgroup -u 1001 notroot

ARG SPRING_PROFILES_ACTIVE=default
ARG SERVER_PORT=5000
EXPOSE 8080
VOLUME /tmp
COPY target/de-app-api.jar /app/app.jar

# copy chart templates to be used by playWright to take screenshots
COPY src/main/resources/templates/. /app/generatedTemplateResources/
# Since modules are private by default since java 17
# there is an issue with deserializing UUID.
# The following option would fix the issues but we use other workarounds for now
# and will keep it here just for reference
# --add-opens=java.base/java.util=ALL-UNNAMED
ENV JAVA_OPTS="--add-opens=java.base/java.io=ALL-UNNAMED -Xms1g -Xmx5g -XX:MaxRAMPercentage=60.0"

# Change ownership of the root directory
RUN chown -R notroot:appgroup /app

# Switch to the non-root user
USER 1001

ENTRYPOINT exec java $JAVA_OPTS -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE -jar /app/app.jar