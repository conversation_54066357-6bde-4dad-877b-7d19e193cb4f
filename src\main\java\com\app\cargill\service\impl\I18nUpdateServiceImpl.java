/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.service.I18nUpdateService;
import com.nimbusds.jose.util.StandardCharset;
import java.io.*;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class I18nUpdateServiceImpl implements I18nUpdateService {

  @Value("${i18n.directory}")
  private String i18nDirectory;

  @Override
  public String updateI18nFile(MultipartFile file, String languageColumn, String fileName) {
    if (file == null || file.isEmpty()) {
      return "Please upload a valid Excel file.";
    }

    try (InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile)) {

      Sheet sheet = workbook.getSheetAt(0); // Assuming data is on the first sheet
      Iterator<Row> iterator = sheet.iterator();

      // Read headers
      Row headerRow = iterator.next();
      Map<String, Integer> columnIndexMap = new HashMap<>();
      for (Cell cell : headerRow) {
        columnIndexMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
      }

      // Find index of the language column
      Integer languageColumnIndex = columnIndexMap.get(languageColumn);
      if (languageColumnIndex == null) {
        return "Language column not found in the Excel file.";
      }

      // Process each row
      while (iterator.hasNext()) {
        Row row = iterator.next();
        String key = row.getCell(columnIndexMap.get("Keys")).getStringCellValue().trim();
        String translation = getCellValue(row.getCell(languageColumnIndex));

        updatePropertyFile(fileName, key, translation);
      }

      return "Updates to " + fileName + " completed successfully.";

    } catch (IOException e) {
      log.error("Error processing file: {}", e.getLocalizedMessage());
      return "Failed to process the uploaded file.";
    }
  }

  @Override
  public List<String> findMissingKeys(MultipartFile file, String fileName) {
    if (file == null || file.isEmpty()) {
      return Collections.emptyList();
    }

    try (InputStream excelFile = file.getInputStream();
        Workbook workbook = new XSSFWorkbook(excelFile)) {

      Sheet sheet = workbook.getSheetAt(0);
      Iterator<Row> iterator = sheet.iterator();

      // Read headers
      Row headerRow = iterator.next();
      Map<String, Integer> columnIndexMap = new HashMap<>();
      for (Cell cell : headerRow) {
        columnIndexMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
      }

      // Collect keys from Excel
      Set<String> excelKeys = new HashSet<>();
      while (iterator.hasNext()) {
        Row row = iterator.next();
        excelKeys.add(row.getCell(columnIndexMap.get("Keys")).getStringCellValue().trim());
      }

      return findMissingKeysInFile(fileName, excelKeys);

    } catch (IOException e) {
      log.error("Error processing file: {}", e.getLocalizedMessage());
      return Collections.emptyList();
    }
  }

  private List<String> findMissingKeysInFile(String fileName, Set<String> excelKeys) {
    String filePath = i18nDirectory + File.separator + fileName;
    List<String> missingKeys = new ArrayList<>();

    try (FileInputStream fis = new FileInputStream(filePath);
        InputStreamReader isr = new InputStreamReader(fis, StandardCharset.UTF_8);
        BufferedReader reader = new BufferedReader(isr)) {

      Properties properties = new Properties();
      properties.load(reader);

      for (String key : excelKeys) {
        if (!properties.containsKey(key)) {
          missingKeys.add(key);
        }
      }

    } catch (IOException e) {
      log.error("Error reading properties file: {}", e.getLocalizedMessage());
    }

    return missingKeys;
  }

  private void updatePropertyFile(String fileName, String key, String value) {
    String filePath = i18nDirectory + File.separator + fileName;

    try (FileInputStream fis = new FileInputStream(filePath);
        InputStreamReader isr = new InputStreamReader(fis, StandardCharset.UTF_8);
        BufferedReader reader = new BufferedReader(isr)) {

      Properties properties = new Properties();
      properties.load(reader);
      properties.setProperty(key, value);

      try (FileOutputStream fos = new FileOutputStream(filePath);
          OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharset.UTF_8);
          BufferedWriter writer = new BufferedWriter(osw)) {

        properties.store(writer, "Updated via API");
      }

    } catch (IOException e) {
      log.error("Error updating properties file: {}", e.getLocalizedMessage());
    }
  }

  private String getCellValue(Cell cell) {
    if (cell == null) {
      return "";
    }
    return switch (cell.getCellType()) {
      case STRING -> cell.getStringCellValue().trim();
      case NUMERIC -> String.valueOf(cell.getNumericCellValue()).trim();
      case BOOLEAN -> String.valueOf(cell.getBooleanCellValue()).trim();
      default -> "";
    };
  }
}
