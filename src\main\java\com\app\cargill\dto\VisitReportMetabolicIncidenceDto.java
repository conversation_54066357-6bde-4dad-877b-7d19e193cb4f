/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.LinkedHashMap;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportMetabolicIncidenceDto {
  private LinkedHashMap<String, String> herdLevelInformationLeftTable;
  private LinkedHashMap<String, String> herdLevelInformationRightTable;
  private LinkedHashMap<String, String> metabolicIncidenceCasesLeftTable;
  private LinkedHashMap<String, String> metabolicIncidenceCasesRightTable;
  private List<List<VisitReportColumnValueDto>> performanceAndTreatmentCost;
  private MetabolicIncidenceReportDto graph;
  private List<NotesDto> notes;
}
