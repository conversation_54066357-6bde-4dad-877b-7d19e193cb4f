/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CowUrinePHItem {
  @JsonProperty("CowNumber")
  public Integer cowNumber;

  @JsonProperty("CowName")
  public String cowName;

  @JsonProperty("UrinePH")
  public Double urinePH;
}
