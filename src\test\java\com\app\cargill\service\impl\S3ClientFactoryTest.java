/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.amazonaws.services.s3.AmazonS3;
import org.junit.jupiter.api.Test;

class S3ClientFactoryTest {

  @Test
  void whenNotIAmRoleAndNoKeysAClientIsreturned() {
    S3ClientFactory factory = new S3ClientFactory();

    factory.setAwsRegion("eu-west-1");
    AmazonS3 result = factory.getS3Client();
    assertNotNull(result);
  }

  @Test
  void whenNotIAmRoleAndKeysProvidedAClientIsreturned() {
    S3ClientFactory factory = new S3ClientFactory();

    factory.setAwsRegion("eu-west-1");
    factory.setAmazonAccessKey("x");
    factory.setAmazonSecretKey("x");
    AmazonS3 result = factory.getS3Client();
    assertNotNull(result);
  }
}
