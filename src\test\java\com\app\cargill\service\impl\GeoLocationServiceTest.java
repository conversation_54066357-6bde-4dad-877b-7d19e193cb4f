/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.CityDocument;
import com.app.cargill.document.CountryDocument;
import com.app.cargill.document.StateDocument;
import com.app.cargill.dto.CityDto;
import com.app.cargill.dto.CountryDto;
import com.app.cargill.dto.StateDto;
import com.app.cargill.model.Cities;
import com.app.cargill.model.Countries;
import com.app.cargill.model.States;
import com.app.cargill.repository.CitiesRepository;
import com.app.cargill.repository.CountriesRepository;
import com.app.cargill.repository.StatesRepository;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class GeoLocationServiceTest {

  @InjectMocks private GeoLocationServiceImpl geoLocationService;
  @Mock CountriesRepository countriesRepository;
  @Mock StatesRepository statesRepository;
  @Mock CitiesRepository citiesRepository;
  @Mock private static ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock static Locale locale;

  @BeforeAll
  static void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void fetchAllCountries() {
    when(countriesRepository.findAll())
        .thenReturn(
            List.of(
                Countries.builder()
                    .updatedDate(new Date())
                    .createdDate(new Date())
                    .countryDocument(CountryDocument.builder().countryName("test1").build())
                    .build(),
                Countries.builder()
                    .createdDate(new Date())
                    .updatedDate(new Date())
                    .countryDocument(CountryDocument.builder().countryName("pak").build())
                    .build()));
    List<CountryDto> result =
        geoLocationService.fetchAllCountries(locale, resourceBundleMessageSource);
    assertNotNull(result);
    assertEquals(2, result.size());
  }

  @Test
  void fetchAllCountriesPaginated() {
    when(countriesRepository.findAllByUpdatedDate(any(), any()))
        .thenReturn(
            new PageImpl<>(
                List.of(
                    Countries.builder()
                        .createdDate(new Date())
                        .updatedDate(new Date())
                        .countryDocument(CountryDocument.builder().countryName("test1").build())
                        .build(),
                    Countries.builder()
                        .createdDate(new Date())
                        .updatedDate(new Date())
                        .countryDocument(CountryDocument.builder().countryName("pak").build())
                        .build())));

    Page<CountryDto> result =
        geoLocationService.fetchAllCountriesPaginated(
            1, 10, "sb", Instant.now(), "s", locale, resourceBundleMessageSource);
    assertNotNull(result);

    assertEquals(12, result.getTotalElements());
  }

  @Test
  void fetchStates() {
    when(statesRepository.findByCountryCode(any()))
        .thenReturn(
            List.of(
                States.builder()
                    .createdDate(new Date())
                    .updatedDate(new Date())
                    .stateDocument(StateDocument.builder().stateName("test1").build())
                    .build(),
                States.builder()
                    .createdDate(new Date())
                    .updatedDate(new Date())
                    .stateDocument(StateDocument.builder().stateName("pak").build())
                    .build()));

    List<StateDto> result =
        geoLocationService.fetchStateByCountryCode("cc", locale, resourceBundleMessageSource);

    assertEquals(2, result.size());
  }

  @Test
  void fetchStatesPaginated() {
    when(statesRepository.findByCountryCodeIn(any(), any(), any()))
        .thenReturn(
            new PageImpl<>(
                List.of(
                    States.builder()
                        .createdDate(new Date())
                        .updatedDate(new Date())
                        .stateDocument(StateDocument.builder().stateName("test1").build())
                        .build(),
                    States.builder()
                        .createdDate(new Date())
                        .updatedDate(new Date())
                        .stateDocument(StateDocument.builder().stateName("pak").build())
                        .build())));

    Page<StateDto> result =
        geoLocationService.fetchStatesByCountryCodesPaginated(
            Arrays.asList("cc"),
            1,
            10,
            "sb",
            Instant.now(),
            "s",
            locale,
            resourceBundleMessageSource);

    assertEquals(12, result.getTotalElements());
  }

  @Test
  void fetchCitiesPaginated() {
    when(citiesRepository.findByStateCodeAndCountryCode(any(), any(), any(), any()))
        .thenReturn(
            new PageImpl<>(
                List.of(
                    Cities.builder()
                        .createdDate(new Date())
                        .updatedDate(new Date())
                        .cityDocument(CityDocument.builder().name("test1").build())
                        .build(),
                    Cities.builder()
                        .createdDate(new Date())
                        .updatedDate(new Date())
                        .cityDocument(CityDocument.builder().stateName("pak").build())
                        .build())));

    Page<CityDto> result =
        geoLocationService.fetchCitiesByStateCodeAndCountryCodePaginated(
            "sc", "cc", 1, 10, "sb", Instant.now(), "s");
    assertNotNull(result);

    assertEquals(12, result.getTotalElements());
  }

  private List<CountryDto> generateCountries() {
    return List.of(CountryDto.builder().build(), CountryDto.builder().build());
  }

  private Page<CountryDto> generateCountriesPage() {
    return new PageImpl<>(generateCountries());
  }

  private List<StateDto> generateStates() {
    return List.of(StateDto.builder().build(), StateDto.builder().build());
  }

  private Page<StateDto> generateStatesPage() {
    return new PageImpl<>(generateStates());
  }

  private List<CityDto> generateCities() {
    return List.of(CityDto.builder().build(), CityDto.builder().build());
  }

  private Page<CityDto> generateCitiesPage() {
    return new PageImpl<>(generateCities());
  }
}
