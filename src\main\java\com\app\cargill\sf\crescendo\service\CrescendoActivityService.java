/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import com.app.cargill.model.Activities;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.sf.crescendo.mapper.CrescendoActivityMapper;
import com.app.cargill.sf.crescendo.model.ActivityCrescendo;
import java.time.Instant;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CrescendoActivityService {

  private final ActivitiesRepository activityRepository;

  public List<ActivityCrescendo> getActivity(Instant from) {
    List<Activities> activities;
    activities = activityRepository.getActivitiesByUpdatedDateAfterForCrescendo(from);
    List<Activities> needSyncActivity =
        activities.stream()
            .filter(a -> Boolean.TRUE.equals(a.getActivityDocument().getNeedsSync()))
            .toList();

    return needSyncActivity.stream().map(CrescendoActivityMapper::modelToCrescendo).toList();
  }
}
