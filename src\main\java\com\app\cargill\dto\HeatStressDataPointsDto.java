/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.IndexedColors;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HeatStressDataPointsDto {

  private Double temperatureInCelsius;
  private Double temperatureInFahrenheit;
  private IndexedColors stressColour;
  private String stressName;
  private Double inTakeAdjustment;
  private Double estimatedDryMatterIntake;
  private Double lossOfEnergyConsumed;
  private Double milkValueLossPerDay;
  private Double dmiAdjustmentPercentage;
  private Double reductionInDmi;
  private Double energyEquivalentMilkLoss;
  private Double milkValueLossPerMonth;
  private String weightLabel;
  private String currencyLabel;

  private String temperatureInCelsiusLabel;
  private String temperatureInFahrenheitLabel;
  private String inTakeAdjustmentLabel;
  private String estimatedDryMatterIntakeLabel;
  private String lossOfEnergyConsumedLabel;
  private String milkValueLossPerDayLabel;
  private String dmiAdjustmentPercentageLabel;
  private String reductionInDmiLabel;
  private String energyEquivalentMilkLossLabel;
  private String milkValueLossPerMonthLabel;
}
