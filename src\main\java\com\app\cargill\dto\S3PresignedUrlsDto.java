/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.HttpMethods;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class S3PresignedUrlsDto {

  private String id;

  @Schema(
      name = "contentType",
      allowableValues =
          "image/png image/jpeg image/gif audio/mpeg audio/x-ms-wma audio/vnd.rn-realaudio "
              + " audio/x-wav video/mpeg video/mp4 video/quicktime video/x-ms-wmv "
              + " video/x-msvideo video/x-flv application/octet-stream application/pdf "
              + " application/zip application/x-www-form-urlencoded")
  private String contentType;

  private String generatedUrl;

  @Schema(name = "urlType", allowableValues = "PUT GET")
  private HttpMethods urlType; // PUT or GET

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private Instant lastModified;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private Long fileSize;
}
