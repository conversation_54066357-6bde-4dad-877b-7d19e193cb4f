/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.RumenHealthTMRParticleScoreHerdAnalysisDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreHerdAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl
    implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;
  private List<byte[]> herdAnalysisTmrParticleScoreColors =
      Arrays.asList(
          new byte[] {(byte) 122, (byte) 216, (byte) 220},
          new byte[] {(byte) 131, (byte) 190, (byte) 244},
          new byte[] {(byte) 171, (byte) 161, (byte) 227},
          new byte[] {(byte) 241, (byte) 132, (byte) 148});

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object herdAnalysisTmrParticleData, ResourceBundleMessageSource source, Locale locale)
      throws IOException {
    this.source = source;
    RumenHealthTMRParticleScoreHerdAnalysisReportDto dto =
        modelMapper.map(
            herdAnalysisTmrParticleData, RumenHealthTMRParticleScoreHerdAnalysisReportDto.class);
    try (XSSFWorkbook rumenHealthTmrParticleScoreHerdAnalysisWB = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              rumenHealthTmrParticleScoreHerdAnalysisWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(
                  rumenHealthTmrParticleScoreHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              rumenHealthTmrParticleScoreHerdAnalysisWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  rumenHealthTmrParticleScoreHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              rumenHealthTmrParticleScoreHerdAnalysisWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  rumenHealthTmrParticleScoreHerdAnalysisWB, false, true, IndexedColors.BLACK));

      // rumenHealthTmrParticleScore
      XSSFSheet sheet =
          addRumenHealthTmrParticleScoreHerdAnalysisSheet(
              rumenHealthTmrParticleScoreHerdAnalysisWB,
              source,
              locale,
              dto,
              boldStyle,
              greyCellStyle,
              centerBlack);
      int totalSheetColumns = sheet.getLastRowNum();
      return ExcelUtils.finalizeWorkbook(
          rumenHealthTmrParticleScoreHerdAnalysisWB, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  private XSSFSheet addRumenHealthTmrParticleScoreHerdAnalysisSheet(
      XSSFWorkbook rumenHealthTmrParticleScoreHerdAnalysisWB,
      ResourceBundleMessageSource source,
      Locale locale,
      RumenHealthTMRParticleScoreHerdAnalysisReportDto rumenHealthTmrParticleScoreHerdAnalysisDto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(
            rumenHealthTmrParticleScoreHerdAnalysisWB,
            false,
            IndexedColors.BLACK,
            HorizontalAlignment.CENTER);

    XSSFSheet rumenHealthTmrParticleScoreWBSheet =
        rumenHealthTmrParticleScoreHerdAnalysisWB.createSheet(
            ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, "Report", null, source, locale)
                .replace("/", "⧸"));
    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    prepareHeader(
        rumenHealthTmrParticleScoreHerdAnalysisWB,
        rumenHealthTmrParticleScoreWBSheet,
        rowNumber,
        cellNumber,
        rumenHealthTmrParticleScoreHerdAnalysisDto,
        boldStyle,
        locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = rumenHealthTmrParticleScoreWBSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        greyCellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, "Top", null, source, locale));
    rumenHealthTmrParticleScoreWBSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // setting pen name first
    int penNameStartRowNumber = rowNumber.get();
    cellNumber.set(1);
    row = rumenHealthTmrParticleScoreWBSheet.createRow(rowNumber.getAndIncrement());

    for (RumenHealthTMRParticleScoreHerdAnalysisDto onScreenPercentage :
        rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints()) {
      // set pen name in first cell
      ExcelUtils.highlightEmptyCell(
          row, onScreenPercentage.getPenName(), cellNumber, centerBlack, greyCellStyle);
    }

    // rumen health tmr particle score herd analysis

    int onScreenPercentageStartRowNumber = rowNumber.get();
    // first rest cell and create row
    int goalSize = 4;

    for (int goalIndex = 0; goalIndex < goalSize; goalIndex++) {
      cellNumber.set(0);
      row = rumenHealthTmrParticleScoreWBSheet.createRow(rowNumber.getAndIncrement());

      if (goalIndex == 0) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_TOP, "Top", null, source, locale));
        for (RumenHealthTMRParticleScoreHerdAnalysisDto value :
            rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getTop(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 1) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_MID_1, "Mid 1", null, source, locale));
        for (RumenHealthTMRParticleScoreHerdAnalysisDto value :
            rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getMid1(), cellNumber, decimalStyle, greyCellStyle);
        }

      } else if (goalIndex == 2) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_MID_2, "Mid 2", null, source, locale));
        for (RumenHealthTMRParticleScoreHerdAnalysisDto value :
            rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getMid2(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 3) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_TRAY, "Tray", null, source, locale));
        for (RumenHealthTMRParticleScoreHerdAnalysisDto value :
            rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getTray(), cellNumber, decimalStyle, greyCellStyle);
        }
      }
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    // int columnStart = 1;
    int columnEnd =
        columnStart + rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFBarChartData dataLeft;
    Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first Bar chart======================
    chart =
        ExcelUtils.initChart(
            rumenHealthTmrParticleScoreWBSheet,
            ExcelUtils.getLangValue(LangKeys.REPORT_ON_SCREEN_PERCENTAGE, null, source, locale),
            chartCol0,
            3,
            chartCol0
                + (rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints().size() > 10
                    ? rumenHealthTmrParticleScoreHerdAnalysisDto.getDataPoints().size()
                    : 10),
            23);

    ExcelUtils.initLegends(chart);

    bottomAxis = ExcelUtils.createBottomAxis(chart, "");

    leftAxis = ExcelUtils.createLeftAxis(chart, "");
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
    // create chart data
    dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);
    // create series
    XDDFDataSource<String> penNameDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            rumenHealthTmrParticleScoreWBSheet,
            new CellRangeAddress(
                penNameStartRowNumber, penNameStartRowNumber, columnStart, columnEnd));
    for (int ds = 0; ds < goalSize; ds++) {
      XDDFNumericalDataSource<Double> onScreenPercentDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenHealthTmrParticleScoreWBSheet,
              new CellRangeAddress(
                  onScreenPercentageStartRowNumber,
                  onScreenPercentageStartRowNumber,
                  columnStart,
                  columnEnd));
      series = (Series) dataLeft.addSeries(penNameDataSource, onScreenPercentDataSource);
      series.setTitle(
          rumenHealthTmrParticleScoreWBSheet
              .getRow(onScreenPercentageStartRowNumber)
              .getCell(0)
              .getStringCellValue(),
          new CellReference(
              rumenHealthTmrParticleScoreWBSheet.getSheetName(),
              onScreenPercentageStartRowNumber++,
              0,
              true,
              true));
    }
    chart.plot(dataLeft);
    ExcelUtils.setBarColorInBarChart(chart, herdAnalysisTmrParticleScoreColors, goalSize);

    return rumenHealthTmrParticleScoreWBSheet;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    RumenHealthTMRParticleScoreHerdAnalysisReportDto mappedDto =
        modelMapper.map(data, RumenHealthTMRParticleScoreHerdAnalysisReportDto.class);

    Map<String, byte[]> imageTemplates = new HashMap<>();

    // create sheet 1
    byte[] rumenHealthTmrParticleScore =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.RUMEN_HEALTH_TMR_PARTICLE_SCORE_HERD_ANALYSIS_REPORT
                .getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            LangKeys.REPORT_RUMEN_HEALTH_TMR_PARTICLE_SCORE,
            "Rumen Health TMR Particle Score",
            null,
            source,
            locale),
        rumenHealthTmrParticleScore);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    RumenHealthTMRParticleScoreHerdAnalysisReportDto dto =
        modelMapper.map(data, RumenHealthTMRParticleScoreHerdAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.RUMEN_HEALTH_TMR_PARTICLE_SCORE_HERD_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook rumenHealthTmrParticleScoreHerdAnalysisWB,
      XSSFSheet rumenHealthTmrParticleScoreHerdAnalysisSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      RumenHealthTMRParticleScoreHerdAnalysisReportDto
          rumenHealthTMRParticleScoreHerdAnalysisReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        rumenHealthTmrParticleScoreHerdAnalysisWB,
        rumenHealthTmrParticleScoreHerdAnalysisSheet,
        rowNum.get(),
        cellNum.getAndIncrement());
    // headings
    XSSFRow row = rumenHealthTmrParticleScoreHerdAnalysisSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthTMRParticleScoreHerdAnalysisReportDto.getVisitName());
    rumenHealthTmrParticleScoreHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthTMRParticleScoreHerdAnalysisReportDto.getVisitDate());
    rumenHealthTmrParticleScoreHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = rumenHealthTmrParticleScoreHerdAnalysisSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthTMRParticleScoreHerdAnalysisReportDto.getToolName());
    rumenHealthTmrParticleScoreHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthTMRParticleScoreHerdAnalysisReportDto.getAnalysisType());
    rumenHealthTmrParticleScoreHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));
  }
}
