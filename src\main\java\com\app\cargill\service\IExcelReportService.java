/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;

public interface IExcelReportService {

  String getFileName(Object data);

  ByteArrayResource prepareExportToExcel(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException;

  default Object prepareData(Object data) {
    return data;
  }

  default Object prepareData(Object data, ResourceBundleMessageSource source, Locale locale) {
    return data;
  }

  ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale)
      throws IOException, URISyntaxException;
}
