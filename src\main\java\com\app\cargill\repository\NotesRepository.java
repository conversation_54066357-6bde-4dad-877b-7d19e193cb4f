/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Notes;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface NotesRepository
    extends JpaRepository<Notes, Long>, JpaSpecificationExecutor<Notes> {

  @Query(
      value = "Select cd.* FROM notes cd where cd.notes_document ->> 'id' = :id",
      nativeQuery = true)
  Notes findByDocumentId(@Param("id") String id);

  @Query(
      value =
          "SELECT DISTINCT s.* FROM Notes s WHERE ((s.notes_document ->> 'AccountId' IN"
              + " (:accountIds)) OR ((LOWER(s.notes_document ->>"
              + " 'CreateUser')=LOWER(:currentLoggedInUser)) AND s.notes_document->>'AccountId' is"
              + " null)) AND timezone('UTC',s.updated_date) > :lastSyncTime AND s.deleted=false ",
      nativeQuery = true)
  Page<Notes> findByAccountIdAndUpdatedDate(
      List<String> accountIds, Instant lastSyncTime, String currentLoggedInUser, Pageable pageable);

  @Query(
      value =
          "SELECT DISTINCT s.notes_document->>'id' FROM Notes s WHERE ((s.notes_document ->>"
              + " 'AccountId' IN (:accountIds)) OR ((LOWER(s.notes_document ->>"
              + " 'CreateUser')=LOWER(:currentLoggedInUser)) AND s.notes_document->>'AccountId' is"
              + " null)) AND s.deleted=false ",
      nativeQuery = true)
  List<String> findNoteIdsByAccountId(
      @Param("accountIds") List<String> accountIds,
      @Param("currentLoggedInUser") String currentLoggedInUser);

  @Query(
      value =
          "SELECT n.* FROM Notes n WHERE n.notes_document ->> 'AccountId' IN (:accountIds) AND"
              + " n.deleted=false ",
      nativeQuery = true)
  List<Notes> findAllByAccountId(List<String> accountIds);

  boolean existsByLocalId(String localId);

  @Query(
      value =
          "Select cd.* FROM Notes cd where cd.notes_document ->> 'id' = :noteId AND"
              + " cd.deleted=false",
      nativeQuery = true)
  Notes findByNoteId(@Param("noteId") String noteId);

  @Query(
      value =
          "SELECT DISTINCT cd.notes_document ->> 'Section' FROM Notes  cd WHERE cd.deleted=false"
              + " AND cd.notes_document ->> 'Section' IS NOT null",
      nativeQuery = true)
  List<String> findDistinctSections();

  @Query(
      value = "Select cd.* FROM notes cd where cd.notes_document ->> 'id' IN(:noteIds)",
      nativeQuery = true)
  List<Notes> findByNotesIdsIn(@Param("noteIds") List<String> noteIds);

  @Query(
      value = "Select n.* FROM notes n where n.notes_document ->> 'SiteId' = :siteId",
      nativeQuery = true)
  List<Notes> findByNotesBySiteId(@Param("siteId") String siteId);
}
