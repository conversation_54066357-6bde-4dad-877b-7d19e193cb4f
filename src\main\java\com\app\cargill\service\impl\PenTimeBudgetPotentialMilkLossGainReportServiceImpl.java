/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.PenTimeBudgetPotentialMilkLossGainReportDto;
import com.app.cargill.dto.XAndYAxisValueDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ColorUtils;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.AxisCrosses;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.MarkerStyle;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFLineChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("penTimeBudgetPotentialMilkLossGainReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class PenTimeBudgetPotentialMilkLossGainReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public Object prepareData(Object data) {
    PenTimeBudgetPotentialMilkLossGainReportDto mappedDto =
        modelMapper.map(data, PenTimeBudgetPotentialMilkLossGainReportDto.class);

    if (!Objects.isNull(mappedDto.getDataPoints())) {
      ColorUtils colorUtils = new ColorUtils();
      mappedDto.setLineColorHex(colorUtils.colorNameToHex(mappedDto.getLineColor().name()));
    }
    return mappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    Map<String, byte[]> imageTemplates = new HashMap<>();
    PenTimeBudgetPotentialMilkLossGainReportDto mappedDto =
        modelMapper.map(prepareData(data), PenTimeBudgetPotentialMilkLossGainReportDto.class);
    if (!Objects.isNull(mappedDto.getDataPoints())) {
      byte[] singleAxisGraph =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.PEN_TIME_BUDGET_POTENTIAL_MILK_LOSS_GAIN_REPORT
                  .getImageTemplateName0(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);

      imageTemplates.put(mappedDto.getSheetName(), singleAxisGraph);
    }

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    PenTimeBudgetPotentialMilkLossGainReportDto dto =
        modelMapper.map(data, PenTimeBudgetPotentialMilkLossGainReportDto.class);

    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      int totalSheetColumns = 0;
      if (!Objects.isNull(dto.getDataPoints())) {
        // create sheet 1
        XSSFSheet sheet =
            addSingleAxisGraphSheet(wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        totalSheetColumns = sheet.getLastRowNum();
      }
      return ExcelUtils.finalizeWorkbook(wb, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  void prepareHeader(
      XSSFWorkbook penTimeBudgetPotentialMilkLossGainWB,
      XSSFSheet penTimeBudgetPotentialMilkLossGainSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      PenTimeBudgetPotentialMilkLossGainReportDto penTimeBudgetPotentialMilkLossGainReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        penTimeBudgetPotentialMilkLossGainWB,
        penTimeBudgetPotentialMilkLossGainSheet,
        rowNum.get(),
        cellNum.getAndIncrement());
    // headings
    XSSFRow row = penTimeBudgetPotentialMilkLossGainSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, penTimeBudgetPotentialMilkLossGainReportDto.getVisitName());
    penTimeBudgetPotentialMilkLossGainSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, penTimeBudgetPotentialMilkLossGainReportDto.getVisitDate());
    penTimeBudgetPotentialMilkLossGainSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = penTimeBudgetPotentialMilkLossGainSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, penTimeBudgetPotentialMilkLossGainReportDto.getToolName());
    penTimeBudgetPotentialMilkLossGainSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
  }

  @Override
  public String getFileName(Object data) {
    PenTimeBudgetPotentialMilkLossGainReportDto dto =
        modelMapper.map(data, PenTimeBudgetPotentialMilkLossGainReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.PEN_TIME_BUDGET_POTENTIAL_MILK_LOSS_GAIN_REPORT.getFileName()
        : dto.getFileName();
  }

  private static void writeXAxisHeadingValuesForSingleAxis(
      ResourceBundleMessageSource source,
      Locale locale,
      XSSFCellStyle centerBlack,
      AtomicInteger cellNumber,
      XSSFRow row,
      PenTimeBudgetPotentialMilkLossGainReportDto dto) {
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    for (XAndYAxisValueDto dataPoint : dto.getDataPoints()) {
      ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, dataPoint.getX());
    }
  }

  XSSFSheet addSingleAxisGraphSheet(
      XSSFWorkbook wb,
      ResourceBundleMessageSource source,
      Locale locale,
      PenTimeBudgetPotentialMilkLossGainReportDto dto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFSheet sheet = wb.createSheet(dto.getSheetName().replace("/", "⧸"));

    AtomicInteger rowNum = new AtomicInteger(0);
    AtomicInteger cellNum = new AtomicInteger(0);

    // Preparing header
    prepareHeader(wb, sheet, rowNum, cellNum, dto, boldStyle, locale);

    // create the data

    // calculated table heading
    cellNum.set(0);
    XSSFRow row = sheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(row, cellNum, greyCellStyle, dto.getCategoryLabel());
    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 0, 7));

    // rest cell number
    cellNum.set(0);

    int xAxisStartRowNumber = rowNum.get();

    row = sheet.createRow(rowNum.getAndIncrement());
    writeXAxisHeadingValuesForSingleAxis(source, locale, centerBlack, cellNum, row, dto);

    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    // yAxis
    cellNum.set(0);

    int yAxisStartRowNumber = rowNum.get();
    row = sheet.createRow(rowNum.getAndIncrement());

    ExcelUtils.createAndSetCellValue(row, cellNum, centerBlack, dto.getLabel());

    for (XAndYAxisValueDto dataPoint : dto.getDataPoints()) {
      ExcelUtils.highlightEmptyCell(row, dataPoint.getY(), cellNum, decimalStyle, greyCellStyle);
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    int columnEnd = columnStart + dto.getDataPoints().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XDDFDataSource<String> xAxisDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet,
            new CellRangeAddress(xAxisStartRowNumber, xAxisStartRowNumber, columnStart, columnEnd));
    XDDFNumericalDataSource<Double> yAxisDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(yAxisStartRowNumber, yAxisStartRowNumber, columnStart, columnEnd));

    // needed objects for the charts
    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFLineChartData dataLeft;
    XDDFLineChartData.Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first line chart======================
    chart = ExcelUtils.initChart(sheet, dto.getSheetName(), chartCol0, 3, chartCol0 + 10, 23);

    bottomAxis =
        ExcelUtils.createBottomAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    leftAxis = ExcelUtils.createLeftAxis(chart, dto.getLabel());
    bottomAxis.crossAxis(leftAxis);
    bottomAxis.setCrosses(AxisCrosses.MIN);
    // create lineCharts data
    dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

    // create series
    series = (XDDFLineChartData.Series) dataLeft.addSeries(xAxisDataSource, yAxisDataSource);
    series.setTitle(
        dto.getLabel(),
        new CellReference(sheet.getSheetName(), yAxisStartRowNumber, 0, true, true));
    series.setSmooth(true);
    chart.plot(dataLeft);
    ExcelUtils.setLineMarker(series, MarkerStyle.CIRCLE);
    ExcelUtils.drawLineSeries(dataLeft, 0, dto.getLineColor(), false);

    return sheet;
  }
}
