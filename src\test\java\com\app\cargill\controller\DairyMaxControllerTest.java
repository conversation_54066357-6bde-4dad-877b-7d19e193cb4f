/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;

import com.app.cargill.controller.external.DairyMaxController;
import com.app.cargill.dairymax.response.MaxResponseDto;
import com.app.cargill.dairymax.service.IDairyMaxService;
import com.app.cargill.exceptions.CustomDEExceptions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class DairyMaxControllerTest {

  @Mock private IDairyMaxService dairyMaxService;
  @InjectMocks private DairyMaxController controller;

  @Test
  void updateDataFromMaxControllerSuccess() throws CustomDEExceptions {
    doNothing().when(dairyMaxService).updateSiteFromMax(anyList());
    ResponseEntity<MaxResponseDto> response = controller.save(anyList());
    assertNotNull(response);
  }
}
