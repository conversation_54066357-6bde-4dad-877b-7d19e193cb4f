/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.admin;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.admin.Visit;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class VisitAdminServiceTest {
  @Mock private VisitsRepository visitsRepository;
  @Mock private AccountsRepository accountsRepository;
  @Mock private SitesRepository sitesRepository;
  @InjectMocks private VisitAdminService visitAdminService;

  @Test
  void whenGetLatestVisitsIsCalledResultIsReturned() {
    VisitDocument dbVisitDocument1 = new VisitDocument();
    dbVisitDocument1.setSiteId(UUID.randomUUID());
    dbVisitDocument1.setCustomerId(UUID.randomUUID());
    Visits dbVisit1 = new Visits(dbVisitDocument1);

    VisitDocument dbVisitDocument2 = new VisitDocument();
    dbVisitDocument2.setSiteId(UUID.randomUUID());
    dbVisitDocument2.setCustomerId(UUID.randomUUID());
    Visits dbVisit2 = new Visits(dbVisitDocument2);

    VisitDocument dbVisitDocument3 = new VisitDocument();
    dbVisitDocument3.setSiteId(UUID.randomUUID());
    dbVisitDocument3.setCustomerId(UUID.randomUUID());
    Visits dbVisit3 = new Visits(dbVisitDocument3);

    when(visitsRepository.findLatestVisits()).thenReturn(List.of(dbVisit1, dbVisit2, dbVisit3));

    when(accountsRepository.findByAccountId(any()))
        .thenReturn(null)
        .thenReturn(new Accounts(new AccountDocument()));
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(null)
        .thenReturn(new Sites(new SiteDocument()));

    List<Visit> result = visitAdminService.getLatestVisits();
    assertEquals(3, result.size());
  }

  @Test
  void whenFirstVisitByEmailIsCalledResultIsReturned() {
    VisitDocument dbVisitDocument1 = new VisitDocument();
    dbVisitDocument1.setSiteId(UUID.randomUUID());
    dbVisitDocument1.setCustomerId(UUID.randomUUID());
    Visits dbVisit1 = new Visits(dbVisitDocument1);

    when(visitsRepository.findFirstVisitByEmail(any())).thenReturn(dbVisit1);
    when(accountsRepository.findByAccountId(any())).thenReturn(new Accounts(new AccountDocument()));
    when(sitesRepository.findBySiteId(any())).thenReturn(new Sites(new SiteDocument()));

    assertNotNull(visitAdminService.getUserFirstVisit("something"));
  }

  @Test
  void whenFirstVisitByEmailIsCalledAndNoVisitNullIsReturned() {

    when(visitsRepository.findFirstVisitByEmail(any())).thenReturn(null);

    assertNull(visitAdminService.getUserFirstVisit("something"));
  }
}
