/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.ConfigurationDto;
import com.app.cargill.model.Configurations;
import com.app.cargill.repository.ConfigurationsRepository;
import com.app.cargill.service.IConfigurationService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConfigurationsServiceImpl implements IConfigurationService {

  private final ConfigurationsRepository configurationsRepository;

  @Override
  public ConfigurationDto fetchConfiguration() {
    List<Configurations> configurations = configurationsRepository.findAll();
    if (configurations.isEmpty()) {
      return new ConfigurationDto();
    }

    return mapToDto(configurations.get(0));
  }

  private ConfigurationDto mapToDto(Configurations configurations) {

    return ConfigurationDto.builder()
        .maxPageSize(configurations.getConfigurationDocument().getMaxPageSize())
        .minPageSize(configurations.getConfigurationDocument().getMinPageSize())
        .percentage(configurations.getConfigurationDocument().getPercentage())
        .build();
  }
}
