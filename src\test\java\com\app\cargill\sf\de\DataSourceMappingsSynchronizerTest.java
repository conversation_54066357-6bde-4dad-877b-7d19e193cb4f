/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.SiteMappingsRepository;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DataSourceMappingsSynchronizerTest {

  @Mock private SiteMappingsRepository repository;
  @InjectMocks private SiteMappingsSynchronizer synchronizer;

  @Test
  void whenNewSiteIsAddedEverythingPasses() {
    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(1, syncResult.getNewRecords().get());
  }

  @Test
  void whenSiteIsUpdateEverythingPasses() {
    SiteMappings dbSiteMapping = new SiteMappings(new SiteMappingDocument());
    when(repository.findBySiteId(anyString())).thenReturn(dbSiteMapping);

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void whenThereIsAnExceptionMethodDoesNotFail() {

    when(repository.findBySiteId(anyString())).thenThrow(RuntimeException.class);

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void classReturnsCorrectType() {
    assertEquals(SiteMappingDocument.class, synchronizer.getType());
  }

  private SiteMappingDocument createDocument() {
    SiteMappingDocument result = new SiteMappingDocument();

    result.setId(UUID.randomUUID());
    result.setMaxSiteId(UUID.randomUUID());
    result.setDdwHerdId("123");
    result.setLabyrinthAccountId(UUID.randomUUID());
    result.setLabyrinthSiteId(UUID.randomUUID());

    return result;
  }
}
