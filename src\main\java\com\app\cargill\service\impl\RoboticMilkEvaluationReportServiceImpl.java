/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.RoboticMilkEvaluationReportDto;
import com.app.cargill.dto.XAndYAxisValueDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ColorUtils;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("roboticMilkEvaluationReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class RoboticMilkEvaluationReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    RoboticMilkEvaluationReportDto dto =
        modelMapper.map(data, RoboticMilkEvaluationReportDto.class);

    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      int totalSheetColumns = 0;
      if (!Objects.isNull(dto.getDualYaxisGraph())
          && !Objects.isNull(dto.getDualYaxisGraph().getYleftDataPoints())
          && !Objects.isNull(dto.getDualYaxisGraph().getYrightDataPoints())) {
        // create sheet 0
        XSSFSheet sheet =
            addMultiAxisGraphSheet(wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        totalSheetColumns = sheet.getLastRowNum();
      }
      if (!Objects.isNull(dto.getSingleYaxisGraph())
          && !Objects.isNull(dto.getSingleYaxisGraph().getYleftDataPoints())) {
        // create sheet 1
        XSSFSheet sheet =
            addSingleAxisGraphSheet(wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        totalSheetColumns = sheet.getLastRowNum();
      }
      return ExcelUtils.finalizeWorkbook(wb, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  private XSSFSheet addMultiAxisGraphSheet(
      XSSFWorkbook wb,
      ResourceBundleMessageSource source,
      Locale locale,
      RoboticMilkEvaluationReportDto dto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    XSSFSheet sheet = wb.createSheet(dto.getDualYaxisGraph().getSheetName().replace("/", "⧸"));
    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, dto.getCategoryLabel());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // visit Dates
    cellNumber.set(0);

    int visitDateStartRowNumber = rowNumber.get();

    row = sheet.createRow(rowNumber.getAndIncrement());
    writeXAxisHeadingValuesForDualAxis(source, locale, centerBlack, cellNumber, row, dto);

    // y left
    cellNumber.set(0);

    int yLeftRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());

    ExcelUtils.createAndSetCellValue(
        row, cellNumber, centerBlack, dto.getDualYaxisGraph().getYleftLabel());

    for (XAndYAxisValueDto dataPoint : dto.getDualYaxisGraph().getYleftDataPoints()) {

      ExcelUtils.highlightEmptyCell(row, dataPoint.getY(), cellNumber, decimalStyle, greyCellStyle);
    }

    // Y Right
    cellNumber.set(0);

    int yRightStartRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, centerBlack, dto.getDualYaxisGraph().getYrightLabel());

    for (XAndYAxisValueDto dataPoint : dto.getDualYaxisGraph().getYrightDataPoints()) {
      ExcelUtils.highlightEmptyCell(row, dataPoint.getY(), cellNumber, decimalStyle, greyCellStyle);
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    // int columnStart = 1;
    int columnEnd = columnStart + dto.getDualYaxisGraph().getYleftDataPoints().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XDDFDataSource<String> visitDatesDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet,
            new CellRangeAddress(
                visitDateStartRowNumber, visitDateStartRowNumber, columnStart, columnEnd));
    XDDFNumericalDataSource<Double> yLeftDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet, new CellRangeAddress(yLeftRowNumber, yLeftRowNumber, columnStart, columnEnd));
    XDDFNumericalDataSource<Double> yRightDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(
                yRightStartRowNumber, yRightStartRowNumber, columnStart, columnEnd));

    // needed objects for the charts

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFLineChartData dataLeft;
    XDDFLineChartData.Series series;
    XDDFValueAxis rightAxis;
    XDDFLineChartData dataRight;
    int chartCol0 = columnEnd + 1;
    // ======================first line chart=========================
    chart =
        ExcelUtils.initChart(
            sheet, dto.getDualYaxisGraph().getSheetName(), chartCol0, 3, chartCol0 + 10, 23);

    ExcelUtils.initLegends(chart);

    bottomAxis =
        ExcelUtils.createBottomAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    leftAxis = ExcelUtils.createLeftAxis(chart, dto.getDualYaxisGraph().getYleftLabel());
    bottomAxis.crossAxis(leftAxis);
    bottomAxis.setCrosses(AxisCrosses.MIN);
    // create chart data
    dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

    // create series
    series = (XDDFLineChartData.Series) dataLeft.addSeries(visitDatesDataSource, yLeftDataSource);
    series.setTitle(
        dto.getDualYaxisGraph().getYleftLabel(),
        new CellReference(sheet.getSheetName(), yLeftRowNumber, 0, true, true));
    series.setSmooth(true);
    // ExcelUtils.drawGridLinesInChart(chart, true);

    chart.plot(dataLeft);

    ///////////////////////////// right axis start here //////////////////////////
    bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
    bottomAxis.setVisible(false);
    Double minimumYAxisValue =
        dto.getDualYaxisGraph().getYrightDataPoints().stream()
            .filter(x -> !Objects.isNull(x) && !Objects.isNull(x.getY()))
            .map(XAndYAxisValueDto::getY)
            .sorted()
            .findFirst()
            .orElse(0.0);
    rightAxis =
        ExcelUtils.createRightAxis(
            chart, dto.getDualYaxisGraph().getYrightLabel(), bottomAxis, minimumYAxisValue);

    // create chart data
    dataRight = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);

    // create series
    series = (XDDFLineChartData.Series) dataRight.addSeries(visitDatesDataSource, yRightDataSource);
    series.setTitle(
        dto.getDualYaxisGraph().getYrightLabel(),
        new CellReference(sheet.getSheetName(), yRightStartRowNumber, 0, true, true));

    // ExcelUtils.drawGridLinesInChart(chart, true);

    ExcelUtils.plotYRightAxis(chart, dataRight, series, true);
    ExcelUtils.drawLineSeries(dataRight, 0, dto.getDualYaxisGraph().getYrightLineColor(), false);
    /////////////////////////////////// right axis end here /////////////////////////////////////
    ExcelUtils.drawLineSeries(dataLeft, 0, dto.getDualYaxisGraph().getYleftLineColor(), false);
    return sheet;
  }

  @Override
  public Object prepareData(Object data) {
    RoboticMilkEvaluationReportDto mappedDto =
        modelMapper.map(data, RoboticMilkEvaluationReportDto.class);
    ColorUtils colorUtils = new ColorUtils();
    if (!Objects.isNull(mappedDto.getDualYaxisGraph())
        && !Objects.isNull(mappedDto.getDualYaxisGraph().getYleftDataPoints())
        && !Objects.isNull(mappedDto.getDualYaxisGraph().getYrightDataPoints())) {
      mappedDto
          .getDualYaxisGraph()
          .setYleftLineColorHex(
              colorUtils.colorNameToHex(mappedDto.getDualYaxisGraph().getYleftLineColor().name()));
      mappedDto
          .getDualYaxisGraph()
          .setYrightLineColorHex(
              colorUtils.colorNameToHex(mappedDto.getDualYaxisGraph().getYrightLineColor().name()));
    }
    if (!Objects.isNull(mappedDto.getSingleYaxisGraph())
        && !Objects.isNull(mappedDto.getSingleYaxisGraph().getYleftDataPoints())) {
      mappedDto
          .getSingleYaxisGraph()
          .setYleftLineColorHex(
              colorUtils.colorNameToHex(
                  mappedDto.getSingleYaxisGraph().getYleftLineColor().name()));
    }
    return mappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    Map<String, byte[]> imageTemplates = new HashMap<>();
    RoboticMilkEvaluationReportDto mappedDto =
        modelMapper.map(prepareData(data), RoboticMilkEvaluationReportDto.class);
    if (!Objects.isNull(mappedDto.getDualYaxisGraph())
        && !Objects.isNull(mappedDto.getDualYaxisGraph().getYleftDataPoints())
        && !Objects.isNull(mappedDto.getDualYaxisGraph().getYrightDataPoints())) {
      byte[] dualAxisGraph =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.ROBOTIC_MILK_EVALUATION_REPORT.getImageTemplateName0(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);
      imageTemplates.put(mappedDto.getDualYaxisGraph().getSheetName(), dualAxisGraph);
    }
    if (!Objects.isNull(mappedDto.getSingleYaxisGraph())
        && !Objects.isNull(mappedDto.getSingleYaxisGraph().getYleftDataPoints())) {
      byte[] singleAxisGraph =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.ROBOTIC_MILK_EVALUATION_REPORT.getImageTemplateName1(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);
      imageTemplates.put(mappedDto.getSingleYaxisGraph().getSheetName(), singleAxisGraph);
    }

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    RoboticMilkEvaluationReportDto dto =
        modelMapper.map(data, RoboticMilkEvaluationReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.ROBOTIC_MILK_EVALUATION_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook roboticMilkEvaluationWB,
      XSSFSheet roboticMilkEvaluationSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      RoboticMilkEvaluationReportDto cudChewingHeardAnalysisReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        roboticMilkEvaluationWB,
        roboticMilkEvaluationSheet,
        rowNum.get(),
        cellNum.getAndIncrement());
    // headings
    XSSFRow row = roboticMilkEvaluationSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getVisitName());
    roboticMilkEvaluationSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getVisitDate());
    roboticMilkEvaluationSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = roboticMilkEvaluationSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getToolName());
    roboticMilkEvaluationSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
  }

  XSSFSheet addSingleAxisGraphSheet(
      XSSFWorkbook wb,
      ResourceBundleMessageSource source,
      Locale locale,
      RoboticMilkEvaluationReportDto dto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFSheet sheet = wb.createSheet(dto.getSingleYaxisGraph().getSheetName().replace("/", "⧸"));

    AtomicInteger cellNumber = new AtomicInteger(0);
    AtomicInteger rowNumber = new AtomicInteger(0);

    prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, dto.getCategoryLabel());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // rest cell number
    cellNumber.set(0);

    int xAxisStartRowNumber = rowNumber.get();

    row = sheet.createRow(rowNumber.getAndIncrement());
    writeXAxisHeadingValuesForSingleAxis(source, locale, centerBlack, cellNumber, row, dto);

    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    // yAxis
    cellNumber.set(0);

    int yAxisStartRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());

    ExcelUtils.createAndSetCellValue(
        row, cellNumber, centerBlack, dto.getSingleYaxisGraph().getYleftLabel());

    for (XAndYAxisValueDto dataPoint : dto.getSingleYaxisGraph().getYleftDataPoints()) {
      ExcelUtils.highlightEmptyCell(row, dataPoint.getY(), cellNumber, decimalStyle, greyCellStyle);
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    int columnEnd = columnStart + dto.getSingleYaxisGraph().getYleftDataPoints().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XDDFDataSource<String> xAxisDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet,
            new CellRangeAddress(xAxisStartRowNumber, xAxisStartRowNumber, columnStart, columnEnd));
    XDDFNumericalDataSource<Double> yAxisDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(yAxisStartRowNumber, yAxisStartRowNumber, columnStart, columnEnd));

    // needed objects for the charts
    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFLineChartData dataLeft;
    XDDFLineChartData.Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first line chart======================
    chart =
        ExcelUtils.initChart(
            sheet, dto.getSingleYaxisGraph().getSheetName(), chartCol0, 3, chartCol0 + 10, 23);

    bottomAxis =
        ExcelUtils.createBottomAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    leftAxis = ExcelUtils.createLeftAxis(chart, dto.getSingleYaxisGraph().getYleftLabel());
    bottomAxis.crossAxis(leftAxis);
    bottomAxis.setCrosses(AxisCrosses.MIN);
    // create lineCharts data
    dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

    // create series
    series = (XDDFLineChartData.Series) dataLeft.addSeries(xAxisDataSource, yAxisDataSource);
    series.setTitle(
        dto.getSingleYaxisGraph().getYleftLabel(),
        new CellReference(sheet.getSheetName(), yAxisStartRowNumber, 0, true, true));
    series.setSmooth(true);
    chart.plot(dataLeft);
    ExcelUtils.setLineMarker(series, MarkerStyle.CIRCLE);
    ExcelUtils.drawLineSeries(dataLeft, 0, dto.getSingleYaxisGraph().getYleftLineColor(), false);

    return sheet;
  }

  private static void writeXAxisHeadingValuesForSingleAxis(
      ResourceBundleMessageSource source,
      Locale locale,
      XSSFCellStyle centerBlack,
      AtomicInteger cellNumber,
      XSSFRow row,
      RoboticMilkEvaluationReportDto dto) {
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    for (XAndYAxisValueDto dataPoint : dto.getSingleYaxisGraph().getYleftDataPoints()) {
      ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, dataPoint.getX());
    }
  }

  private static void writeXAxisHeadingValuesForDualAxis(
      ResourceBundleMessageSource source,
      Locale locale,
      XSSFCellStyle centerBlack,
      AtomicInteger cellNumber,
      XSSFRow row,
      RoboticMilkEvaluationReportDto dto) {
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    for (XAndYAxisValueDto dataPoint : dto.getDualYaxisGraph().getYleftDataPoints()) {
      ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, dataPoint.getX());
    }
  }
}
