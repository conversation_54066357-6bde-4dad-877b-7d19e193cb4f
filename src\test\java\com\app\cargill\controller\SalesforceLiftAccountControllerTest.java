/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.sf.cc.service.LiftAccountService;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SalesforceLiftAccountControllerTest {

  @Mock private LiftAccountService liftAccountService;

  @InjectMocks private SalesforceLiftAccountController controller;

  @Test
  void getAccountByMailReturnsCorrectObject() {
    when(liftAccountService.getAllAccounts(any()))
        .thenReturn(Collections.singletonList(new AccountDocument()));
    List<AccountDocument> result = controller.getAccount("<EMAIL>");
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void getDeletedAccountsCorrectObject() {
    when(liftAccountService.getDeletedAccounts())
        .thenReturn(Collections.singletonList(new Accounts()));
    List<Accounts> result = controller.getDeletedAccounts();
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void getMismatchId() {
    when(liftAccountService.getMismatchedId("<EMAIL>"))
        .thenReturn(Collections.singletonList(new AccountDocument()));
    List<AccountDocument> result = controller.getMismatchId("<EMAIL>");
    assertNotNull(result);
    assertEquals(1, result.size());
  }
}
