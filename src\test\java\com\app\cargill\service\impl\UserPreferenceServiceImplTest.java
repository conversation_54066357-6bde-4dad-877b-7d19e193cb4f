/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.Business;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.Tool;
import com.app.cargill.constants.ToolGroup;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.document.CountryToolDocument;
import com.app.cargill.document.DefaultAppSettingDocument;
import com.app.cargill.document.UserFavourites;
import com.app.cargill.document.UserPreferenceDocument;
import com.app.cargill.dto.UserPreferenceDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.CountryTools;
import com.app.cargill.model.DefaultAppSettings;
import com.app.cargill.model.UserPreferences;
import com.app.cargill.repository.CountryToolsRepository;
import com.app.cargill.repository.DefaultAppSettingsRepository;
import com.app.cargill.repository.UserPreferencesRepository;
import com.app.cargill.repository.UserRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

@ExtendWith(MockitoExtension.class)
public class UserPreferenceServiceImplTest {

  @Mock private ApplicationContext ctx;
  @Mock private UserPreferencesRepository userPreferencesRepository;
  @Mock private CountryToolsRepository countryToolRepository;
  @Mock private UserRepository usersRepository;
  @Mock private UserServiceImpl userServiceImpl;
  @InjectMocks private UserPreferenceServiceImpl userPreferenceServiceImpl;

  @Mock private DefaultAppSettingsRepository defaultAppSettingsRepository;

  @Test
  void whenAllDataIsProvidedItIsSavedCorrectly() {
    UserPreferenceDto userPreferenceDto =
        UserPreferenceDto.builder()
            .brandList(new ArrayList<>())
            .eulaContent("Test")
            .favourites(
                UserFavourites.builder()
                    .accounts(new ArrayList<>())
                    .tools(new ArrayList<>())
                    .notes(new ArrayList<>())
                    .build())
            .lastEulaVersionAccepted(Instant.now())
            .lastPrivacyVersionAccepted(Instant.now())
            .lastSyncOperationDateTime(Instant.now())
            .bcsPointScale(BCSPointScale.HalfPointScale)
            .defaultValues(new HashMap<>())
            .selectedCurrency(Currencies.BAM)
            .unitOfMeasure(UnitOfMeasureKeys.Metric)
            .userId("<EMAIL>")
            .showBCSAnimalAnalysisToast(true)
            .build();
    when(userPreferencesRepository.existsByLocalId(any())).thenReturn(false);
    when(userPreferencesRepository.save(any())).thenReturn(loadUserPreferences());
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("Us");
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(countryToolRepository.findByCountryId(any())).thenReturn(List.of(loadCountryTools()));

    UserPreferenceDto result = userPreferenceServiceImpl.save(userPreferenceDto);
    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
  }

  @Test
  void whenAllDataIsProvidedWithDefaultValuesNullItIsSavedCorrectly() {
    UserPreferenceDto userPreferenceDto =
        UserPreferenceDto.builder()
            .brandList(new ArrayList<>())
            .eulaContent("Test")
            .favourites(
                UserFavourites.builder()
                    .accounts(new ArrayList<>())
                    .tools(new ArrayList<>())
                    .notes(new ArrayList<>())
                    .build())
            .lastEulaVersionAccepted(Instant.now())
            .lastPrivacyVersionAccepted(Instant.now())
            .lastSyncOperationDateTime(Instant.now())
            .bcsPointScale(BCSPointScale.HalfPointScale)
            .defaultValues(null)
            .selectedCurrency(Currencies.BAM)
            .unitOfMeasure(UnitOfMeasureKeys.Metric)
            .userId("<EMAIL>")
            .showBCSAnimalAnalysisToast(true)
            .build();
    when(userPreferencesRepository.existsByLocalId(any())).thenReturn(false);
    when(userPreferencesRepository.save(any())).thenReturn(loadUserPreferences());
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("Us");
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(countryToolRepository.findByCountryId(any())).thenReturn(List.of(loadCountryTools()));

    UserPreferenceDto result = userPreferenceServiceImpl.save(userPreferenceDto);
    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
  }

  @Test
  void whenUserPreferenceDoesNotExistItIsCreated() {
    when(userPreferencesRepository.save(any())).thenReturn(loadUserPreferences());
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("Us");
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(countryToolRepository.findByCountryId(any())).thenReturn(List.of(loadCountryTools()));
    when(defaultAppSettingsRepository.findDefaultAppSettingsByCountry(any()))
        .thenReturn(loadDefaultValues());
    UserPreferenceDto result = userPreferenceServiceImpl.getUserPreferences("<EMAIL>");
    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
  }

  @Test
  void whenUserPreferenceDoesNotExistItIsCreatedAndDefaultSettingsNull() {
    when(userPreferencesRepository.save(any())).thenReturn(loadUserPreferences());
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("Us");
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(countryToolRepository.findByCountryId(any())).thenReturn(List.of(loadCountryTools()));
    when(defaultAppSettingsRepository.findDefaultAppSettingsByCountry(any()))
        .thenReturn(loadDefaultValuesNull());
    UserPreferenceDto result = userPreferenceServiceImpl.getUserPreferences("<EMAIL>");
    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
  }

  @Test
  void WhenLocalIdExistsSaveThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    UserPreferenceDto userPreferenceDto = UserPreferenceDto.builder().localId(localId).build();
    when(userPreferencesRepository.existsByLocalId(localId)).thenReturn(true);
    Assertions.assertThrows(
        AlreadyExistsDEException.class, () -> userPreferenceServiceImpl.save(userPreferenceDto));
  }

  @Test
  void WhenUserIdExistsSaveThrowsAnException() {
    UserPreferenceDto userPreferenceDto = UserPreferenceDto.builder().userId("Test").build();
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(userPreferencesRepository.existsByUserId(any())).thenReturn(true);
    Assertions.assertThrows(
        AlreadyExistsDEException.class, () -> userPreferenceServiceImpl.save(userPreferenceDto));
  }

  @Test
  void WhenUserIdDoesNotExistsUpdateThrowsAnException() {
    UserPreferenceDto userPreferenceDto = UserPreferenceDto.builder().userId("Test").build();
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(userPreferencesRepository.findByUserId(any())).thenReturn(null);
    Assertions.assertThrows(
        NotFoundDEException.class, () -> userPreferenceServiceImpl.update(userPreferenceDto));
  }

  @Test
  void whenAllDataIsProvidedItIsUpdatedCorrectly() {
    UserPreferenceDto userPreferenceDto =
        UserPreferenceDto.builder()
            .id(UUID.randomUUID())
            .brandList(new ArrayList<>())
            .eulaContent("Test")
            .favourites(
                UserFavourites.builder()
                    .accounts(new ArrayList<>())
                    .tools(new ArrayList<>())
                    .notes(new ArrayList<>())
                    .build())
            .lastEulaVersionAccepted(Instant.now())
            .lastPrivacyVersionAccepted(Instant.now())
            .bcsPointScale(BCSPointScale.HalfPointScale)
            .lastSyncOperationDateTime(Instant.now())
            .defaultValues(new HashMap<>())
            .selectedCurrency(Currencies.BAM)
            .unitOfMeasure(UnitOfMeasureKeys.Metric)
            .userId("<EMAIL>")
            .build();
    when(userPreferencesRepository.findByUserId(any())).thenReturn(loadUserPreferences());
    when(userPreferencesRepository.save(any())).thenReturn(loadUserPreferences());
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("Us");
    doReturn(userServiceImpl).when(ctx).getBean(UserServiceImpl.class);
    when(countryToolRepository.findByCountryId(any())).thenReturn(List.of(loadCountryTools()));

    UserPreferenceDto result = userPreferenceServiceImpl.update(userPreferenceDto);
    assertNotNull(result);
    assertNotNull(result.getCountryTools());
  }

  private UserPreferences loadUserPreferences() {
    UserPreferenceDocument userPreferenceDocument =
        UserPreferenceDocument.builder()
            .brandList(new ArrayList<>())
            .createTimeUtc(Instant.now())
            .favourites(
                UserFavourites.builder()
                    .accounts(new ArrayList<>())
                    .tools(new ArrayList<>())
                    .notes(new ArrayList<>())
                    .notes(new ArrayList<>())
                    .build())
            .id(UUID.randomUUID())
            .isDeleted(false)
            .showBCSAnimalAnalysisToast(true)
            .lastEulaVersionAccepted(Instant.now())
            .defaultValues(new HashMap<>())
            .unitOfMeasure(UnitOfMeasureKeys.Imperial)
            .build();
    return UserPreferences.builder()
        .userPreferenceDocument(userPreferenceDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(UUID.randomUUID().toString())
        .build();
  }

  private CountryTools loadCountryTools() {
    CountryToolDocument countryToolDocument =
        CountryToolDocument.builder()
            .countryId(Business.US)
            .id(UUID.randomUUID())
            .toolGroupId(ToolGroup.CalfandHeifer)
            .toolId(Tool.LocomotionScore)
            .isDeleted(true)
            .build();

    return CountryTools.builder()
        .countryToolDocument(countryToolDocument)
        .updatedDate(Date.from(Instant.now()))
        .createdDate(Date.from(Instant.now()))
        .build();
  }

  private DefaultAppSettings loadDefaultValues() {
    DefaultAppSettingDocument defaultAppSettingDocument =
        DefaultAppSettingDocument.builder()
            .countryId(Business.US)
            .bcsPointScale(BCSPointScale.HalfPointScale)
            .unitOfMeasure(UnitOfMeasureKeys.Metric)
            .defaultCurrency(Currencies.NotSet)
            .id(UUID.randomUUID())
            .isDeleted(true)
            .build();

    return DefaultAppSettings.builder()
        .defaultAppSettingDocument(defaultAppSettingDocument)
        .build();
  }

  private DefaultAppSettings loadDefaultValuesNull() {

    return DefaultAppSettings.builder().defaultAppSettingDocument(null).build();
  }
}
