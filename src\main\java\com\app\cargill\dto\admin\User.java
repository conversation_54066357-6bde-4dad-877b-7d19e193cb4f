/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.admin;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.MobileDeviceType;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class User {
  private UUID uuid;
  private String email;
  private String name;
  private Boolean deleted;
  private Instant lastLogin;
  private MobileDeviceType deviceType;
  private String deviceModel;
  private String applicationVersion;
  private Business country;
  private Visit firstVisit;
}
