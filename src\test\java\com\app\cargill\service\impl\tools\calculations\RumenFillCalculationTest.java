/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.constants.LactationStage;
import com.app.cargill.document.*;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RumenFillCalculationTest {

  @InjectMocks private RumenFillCalculation rumenFillCalculation;

  @Test
  void calculateFields() {
    RumenFillTool rumenFillTool =
        RumenFillTool.builder()
            .pens(
                List.of(
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(
                            RumenHealthManureScores.builder()
                                .averageValue(10.5)
                                .standardDeviation(0.6)
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(15)
                                                .scoreCategory(0.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(10)
                                                .scoreCategory(1.0)
                                                .build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .build())
                        .build(),
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(
                            RumenHealthManureScores.builder()
                                .averageValue(15.2)
                                .standardDeviation(0.2)
                                .build())
                        .build()))
            .goals(
                List.of(
                    RumenHealthManureScoreToolGoalItem.builder()
                        .goalMin(5.1)
                        .goalMax(10.2)
                        .lactationStage(LactationStage.EarlyLactation)
                        .build(),
                    RumenHealthManureScoreToolGoalItem.builder()
                        .goalMin(4.1)
                        .goalMax(9.1)
                        .lactationStage(LactationStage.DryCow)
                        .build()))
            .build();

    rumenFillTool = rumenFillCalculation.calculateFields(rumenFillTool);

    assertTrue(
        rumenFillTool.getPens().stream()
            .allMatch(
                pen ->
                    pen.getRumenFillScores().getAverageValue() != null
                        && pen.getRumenFillScores().getStandardDeviation() != null));

    assertTrue(
        rumenFillTool.getGoals().stream()
            .allMatch(
                goal ->
                    !goal.getGoalMin().isNaN()
                        && !goal.getGoalMax().isNaN()
                        && goal.getLactationStage() != null));
  }

  @Test
  void calculateFieldsWithNull() {
    RumenFillTool rumenFillTool =
        RumenFillTool.builder()
            .pens(
                List.of(
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder().build(),
                                            RumenHealthManureScoreItem.builder().build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .build())
                        .build(),
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(RumenHealthManureScores.builder().build())
                        .build()))
            .goals(
                List.of(
                    RumenHealthManureScoreToolGoalItem.builder().build(),
                    RumenHealthManureScoreToolGoalItem.builder().build()))
            .build();

    rumenFillTool = rumenFillCalculation.calculateFields(rumenFillTool);

    assertTrue(
        rumenFillTool.getPens().stream()
            .allMatch(
                pen ->
                    pen.getRumenFillScores().getAverageValue() != null
                        && pen.getRumenFillScores().getStandardDeviation() != null));
  }

  @Test
  void verifyCalculations() {
    RumenFillTool rumenFillTool =
        RumenFillTool.builder()
            .pens(
                List.of(
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(3)
                                                .scoreCategory(2.0)
                                                .percentOfPen(37.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(4)
                                                .scoreCategory(3.0)
                                                .percentOfPen(50.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(1)
                                                .scoreCategory(4.0)
                                                .percentOfPen(12.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(0.0)
                                                .build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .averageValue(2.75)
                                .build())
                        .build(),
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(3)
                                                .scoreCategory(2.0)
                                                .percentOfPen(33.33)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(5)
                                                .scoreCategory(3.0)
                                                .percentOfPen(55.56)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(1)
                                                .scoreCategory(4.0)
                                                .percentOfPen(11.11)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(0.0)
                                                .build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .averageValue(2.7777999999999996)
                                .build())
                        .build(),
                    RumenFillScoreToolItem.builder()
                        .rumenFillScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(2.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(2)
                                                .scoreCategory(3.0)
                                                .percentOfPen(100.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(4.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(0.0)
                                                .build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .averageValue(3.0)
                                .build())
                        .build()))
            .build();

    rumenFillTool = rumenFillCalculation.calculateFields(rumenFillTool);

    assertEquals(2.75, rumenFillTool.getPens().get(0).getRumenFillScores().getAverageValue());
    assertEquals(0.66, rumenFillTool.getPens().get(0).getRumenFillScores().getStandardDeviation());

    assertEquals(
        2.7777999999999996, rumenFillTool.getPens().get(1).getRumenFillScores().getAverageValue());
    assertEquals(0.63, rumenFillTool.getPens().get(1).getRumenFillScores().getStandardDeviation());

    assertEquals(3.0, rumenFillTool.getPens().get(2).getRumenFillScores().getAverageValue());
    assertEquals(0.0, rumenFillTool.getPens().get(2).getRumenFillScores().getStandardDeviation());
  }
}
