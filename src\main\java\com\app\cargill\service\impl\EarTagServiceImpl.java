/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.document.EarTagDocument;
import com.app.cargill.dto.EarTagDto;
import com.app.cargill.dto.SaveEarTagDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.model.EarTags;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.EarTagsRepository;
import com.app.cargill.service.IEarTagService;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.mappers.EarTagMapper;
import com.app.cargill.utils.PageableUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class EarTagServiceImpl implements IEarTagService {

  private final IUserService userServiceImpl;
  private final AccountsRepository accountsRepository;
  private final EarTagsRepository earTagsRepository;

  @Override
  public Page<EarTagDto> getAllEarTagsPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    Page<EarTags> earTags =
        earTagsRepository.findByAccountIds(accountIdsByUser, pageable, lastSyncTime);

    if (Objects.isNull(earTags)) {
      return new PageImpl<>(new ArrayList<>());
    }

    return new PageImpl<>(
        earTags.stream().map(EarTagMapper::modelToDto).toList(),
        pageable,
        earTags.getTotalElements());
  }

  @Override
  public List<EarTagDto> save(EarTagDto earTagDto) {
    if (earTagsRepository.existsByLocalId(earTagDto.getLocalId())) {
      throw new AlreadyExistsDEException("LocalId exists. Object already synced.");
    }

    List<EarTags> earTagsList = new ArrayList<>();
    earTagDto.getEarTags().stream()
        .forEach(earTag -> earTagsList.add(mapToModel(earTag, earTagDto)));
    return earTagsRepository.saveAll(earTagsList).stream().map(EarTagMapper::modelToDto).toList();
  }

  private EarTags mapToModel(SaveEarTagDto earTag, EarTagDto earTagDto) {
    EarTagDocument earTagDocument =
        EarTagDocument.builder()
            .accountId(earTagDto.getAccountId())
            .earTagName(earTag.getEarTagName())
            .id(earTag.getId() != null ? earTag.getId() : UUID.randomUUID())
            .siteId(earTagDto.getSiteId())
            .build();

    return EarTags.builder()
        .earTagDocument(earTagDocument)
        .deleted(earTag.getIsDeleted())
        .localId(earTagDto.getLocalId())
        .build();
  }

  @Override
  public List<EarTagDto> update(EarTagDto earTagDto) {

    List<EarTags> earTagsList = new ArrayList<>();
    earTagDto.getEarTags().stream()
        .forEach(
            earTag -> {
              EarTags earTagToPersist = earTagsRepository.findByEarTagId(earTag.getId().toString());
              EarTags updatedEarTag = mapToModel(earTag, earTagDto);
              if (earTagToPersist != null) {
                updatedEarTag.setId(earTagToPersist.getId());
              }
              earTagsList.add(updatedEarTag);
            });

    return earTagsRepository.saveAll(earTagsList).stream().map(EarTagMapper::modelToDto).toList();
  }
}
