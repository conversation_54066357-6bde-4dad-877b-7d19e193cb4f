/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.LinkedHashMap;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportRoboticMilkEvaluationDto {
  private LinkedHashMap<String, String> herdLevelInformationLeftTable;
  private LinkedHashMap<String, String> herdLevelInformationRightTable;
  private LinkedHashMap<String, LinkedHashMap<String, String>> outputTable;

  private List<DialGraphReportDto> analysisDialGraphs;
  private List<RoboticMilkEvaluationReportDto> graphs;
  private List<NotesDto> notes;
}
