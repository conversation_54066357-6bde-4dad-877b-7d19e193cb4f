/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import com.app.cargill.document.DataSource;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.LongRunningTask.TaskName;
import com.app.cargill.model.Sites;
import com.app.cargill.model.TaskStatus;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.service.IAccountService;
import com.app.cargill.service.impl.SiteServiceImpl;
import com.app.cargill.sf.cc.service.LongRunningTasksService;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.Instant;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Slf4j
@Component
public class SalesforceSyncTrigger {

  private final LiftSyncService liftSyncService;

  private final LongRunningTasksService tasksService;

  private final IAccountService accountServiceImpl;
  private final SiteServiceImpl siteService;

  public List<LongRunningTask<SyncResult>> triggerSync(boolean partial) {
    List<LongRunningTask<SyncResult>> tasks = new ArrayList<>();

    // Push Accounts
    LongRunningTask<SyncResult> accountsPushTask =
        tasksService.saveTask(generateTask(TaskName.ACCOUNTS_TO_LIFT_SYNC));
    tasks.add(accountsPushTask);
    try {
      List<Accounts> accounts = accountServiceImpl.getAllUnsyncedAccounts(DataSource.LIFT);
      liftSyncService
          .syncAccountsToSalesforce(accounts)
          .doOnError(e -> failTask(accountsPushTask, "Accounts sync to LIFT failed", e))
          .subscribe(
              syncResult -> {
                accountsPushTask.setMetaData(syncResult);
                accountsPushTask.setStatus(TaskStatus.COMPLETED);
                tasksService.saveTask(accountsPushTask);
                log.info(
                    "Accounts sync to LIFT completed: {}",
                    accountsPushTask.toString().replace("\r", "֎").replace("\n", "֎"));
              });
    } catch (Exception e) {
      failTask(accountsPushTask, "Accounts sync to LIFT failed", e);
    }

    // Push Sites
    LongRunningTask<SyncResult> sitesPushTask =
        tasksService.saveTask(generateTask(TaskName.SITES_TO_LIFT_SYNC));
    tasks.add(sitesPushTask);
    try {
      List<Sites> sites = siteService.getAllUnsyncedSites(DataSource.LIFT);
      liftSyncService
          .syncSitesToSalesforce(sites)
          .doOnError(e -> failTask(sitesPushTask, "Sites sync to LIFT failed", e))
          .subscribe(
              syncResult -> {
                sitesPushTask.setMetaData(syncResult);
                sitesPushTask.setStatus(TaskStatus.COMPLETED);
                tasksService.saveTask(sitesPushTask);
                log.info(
                    "Sites sync to LIFT completed: {}",
                    sitesPushTask.toString().replace("\r", "֎").replace("\n", "֎"));
              });
    } catch (Exception e) {
      failTask(sitesPushTask, "Sites sync to LIFT failed", e);
    }
    // Generate timestamp for partial syncs
    Instant timestamp = partial ? Instant.now().minus(65 * 1000L) : null;

    // Pull Accounts
    tasks.add(startLiftPullAccountsSync(partial));

    // Pull Sites
    LongRunningTask<SyncResult> sitesPullTask =
        tasksService.saveTask(
            generateTask(partial ? TaskName.LIFT_SITES_SYNC : TaskName.LIFT_SITES_FULL_SYNC));
    tasks.add(sitesPullTask);

    liftSyncService
        .executeSitesSync(timestamp)
        .whenComplete(
            (r, e) -> {
              if (e != null) {
                failTask(sitesPullTask, "Failed to pull Sites from LIFT", e);
              } else {
                sitesPullTask.setMetaData(r);
                sitesPullTask.setStatus(TaskStatus.COMPLETED);
                tasksService.saveTask(sitesPullTask);
                log.info(
                    "Sites sync completed: {}",
                    sitesPullTask.toString().replace("\r", "֎").replace("\n", "֎"));
              }
            });
    log.info(
        "Salesforce Sync tasks initiated with timestamp: {}",
        timestamp != null ? timestamp : "null");

    return tasks;
  }

  public LongRunningTask<SyncResult> startLiftPullAccountsSync(boolean partial) {
    LongRunningTask<SyncResult> accountsPullTask =
        tasksService.saveTask(
            generateTask(partial ? TaskName.LIFT_ACCOUNTS_SYNC : TaskName.LIFT_ACCOUNTS_FULL_SYNC));
    Instant timestamp = partial ? Instant.now().minus(65 * 1000L) : null;
    liftSyncService
        .executeAccountsSync(timestamp)
        .whenComplete(
            (r, e) -> {
              if (e != null) {
                failTask(accountsPullTask, "Failed to pull Accounts from LIFT", e);
              } else {
                accountsPullTask.setMetaData(r);
                accountsPullTask.setStatus(TaskStatus.COMPLETED);
                tasksService.saveTask(accountsPullTask);
                log.info(
                    "Accounts sync completed: {}",
                    accountsPullTask.toString().replace("\r", "֎").replace("\n", "֎"));
              }
            });
    return accountsPullTask;
  }

  public LongRunningTask<SyncResult> startLiftPullAccountsMergeSync(boolean partial) {
    LongRunningTask<SyncResult> mergeAccountsPullTask =
        tasksService.saveTask(generateTask(TaskName.LIFT_ACCOUNTS_MERGE_SYNC));
    Instant timestamp = partial ? Instant.now().minus(65 * 1000L) : null;
    liftSyncService
        .executeAccountsMergeSync(timestamp)
        .whenComplete(
            (r, e) -> {
              if (e != null) {
                failTask(mergeAccountsPullTask, "Failed to pull Merged Accounts from LIFT", e);
              } else {
                mergeAccountsPullTask.setMetaData(r);
                mergeAccountsPullTask.setStatus(TaskStatus.COMPLETED);
                tasksService.saveTask(mergeAccountsPullTask);
                log.info(
                    "Accounts sync completed: {}",
                    mergeAccountsPullTask.toString().replace("\r", "֎").replace("\n", "֎"));
              }
            });
    return mergeAccountsPullTask;
  }

  private LongRunningTask<SyncResult> generateTask(TaskName taskName) {
    return new LongRunningTask<>(
        UUID.randomUUID().toString(),
        taskName,
        TaskStatus.RUNNING,
        new SyncResult(taskName.name()));
  }

  private void failTask(LongRunningTask<SyncResult> task, String message, Throwable e) {
    task.setStatus(TaskStatus.FAILED);
    task.getMetaData().setError(e.getMessage());
    tasksService.saveTask(task);
    log.error(message, e);
  }
}
