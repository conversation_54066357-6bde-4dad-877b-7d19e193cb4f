/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.admin;

import com.app.cargill.constants.Business;
import com.app.cargill.document.SalesForceUserDocument;
import com.app.cargill.document.UserDocument;
import com.app.cargill.dto.admin.User;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.SalesForceUser;
import com.app.cargill.model.UserDailyLogin;
import com.app.cargill.repository.SalesForceUsersRepository;
import com.app.cargill.repository.UserDailyLoginRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.sf.cc.service.LiftUserService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserAdminService {

  private final UserRepository userRepository;
  private final SalesForceUsersRepository salesForceUsersRepository;
  private final UserDailyLoginRepository userDailyLoginRepository;
  private final LiftUserService liftUserService;

  public List<User> getAllUsers() {
    return userRepository.findAllWithDeleted().stream().map(this::mapDbUserToDto).toList();
  }

  public User getUser(UUID id) {
    com.app.cargill.model.User dbUser = userRepository.findByUuid(id.toString());
    if (dbUser == null) {
      throw new NotFoundDEException(String.format("User with id %s not found", id));
    }
    return mapDbUserToDto(dbUser);
  }

  public User addUser(String email, String country, String fullName) {
    country = countryFix(country);
    validateAddUser(email, country);
    UUID userId = UUID.randomUUID();
    com.app.cargill.model.User dbUser = new com.app.cargill.model.User();
    dbUser.setEmail(email.toLowerCase());
    dbUser.setFullName(fullName);
    dbUser.setLocalId(userId.toString());
    UserDocument userDocument =
        UserDocument.builder()
            .id(userId)
            .userName(StringEscapeUtils.escapeJava(email.toLowerCase()))
            .countryId(Business.valueOf(country))
            .build();
    dbUser.setUserDocument(userDocument);
    return mapDbUserToDto(userRepository.save(dbUser));
  }

  public User updateUser(UUID id, boolean isDeleted) {
    com.app.cargill.model.User dbUser = userRepository.findByUuid(id.toString());
    if (dbUser == null) {
      throw new NotFoundDEException(String.format("User with id %s not found", id));
    }
    dbUser.setDeleted(isDeleted);
    return mapDbUserToDto(userRepository.save(dbUser));
  }

  public Flux<SalesForceUser> updateLiftUsersData() {
    return Mono.fromCallable(userRepository::findAllUsersforLiftDeactive)
        .subscribeOn(Schedulers.boundedElastic())
        .flatMapIterable(list -> list)
        .parallel()
        .flatMap(
            user ->
                Mono.fromCallable(() -> processLiftUserUpdate(user))
                    .subscribeOn(Schedulers.boundedElastic())
                    .onErrorReturn(
                        throwable -> {
                          log.error("Error with LIFT user data update", throwable);
                          return true;
                        },
                        List.of()))
        .flatMap(Flux::fromIterable)
        .sequential();
  }

  public List<SalesForceUser> processLiftUserUpdate(com.app.cargill.model.User user) {
    try {
      return liftUserService.fetchUserEmailData(user.getUserDocument().getUserName()).stream()
          .map(this::mapSfUserToDbUser)
          .map(
              sfUser -> {
                SalesForceUser dbUser =
                    salesForceUsersRepository.findByEmail(
                        sfUser.getSalesForceUserDocument().getUserName());
                if (dbUser != null) {
                  dbUser.setSalesForceUserDocument(sfUser.getSalesForceUserDocument());
                  return salesForceUsersRepository.save(dbUser);
                } else {
                  return salesForceUsersRepository.save(sfUser);
                }
              })
          .toList();
    } catch (Exception e) {
      log.error("Error with LIFT user update", e);
      return new ArrayList<>();
    }
  }

  private User mapDbUserToDto(com.app.cargill.model.User dbUser) {
    User user = new User();

    user.setUuid(dbUser.getUserDocument().getId());
    user.setEmail(dbUser.getEmail());
    user.setName(dbUser.getFullName());
    user.setCountry(dbUser.getUserDocument().getCountryId());

    user.setLastLogin(dbUser.getUserDocument().getLastLoginDateTime());
    user.setApplicationVersion(dbUser.getUserDocument().getApplicationVersion());
    user.setDeviceType(dbUser.getUserDocument().getDeviceType());
    user.setDeviceModel(dbUser.getUserDocument().getDeviceModel());

    user.setDeleted(dbUser.isDeleted());

    return user;
  }

  private SalesForceUser mapSfUserToDbUser(com.app.cargill.sf.cc.model.simple.User user) {
    SalesForceUser salesForceUser = SalesForceUser.builder().build();
    salesForceUser.setEmail(user.getUsername());
    SalesForceUserDocument salesForceUserDocument = new SalesForceUserDocument();
    salesForceUserDocument.setUserName(user.getUsername());
    salesForceUserDocument.setFirstName(user.getFirstName());
    salesForceUserDocument.setLastName(user.getLastName());
    salesForceUserDocument.setEmail(user.getEmail());
    salesForceUserDocument.setLastLoginDate(user.getLastLoginDate());
    return SalesForceUser.builder().salesForceUserDocument(salesForceUserDocument).build();
  }

  private void validateAddUser(String email, String country) {
    if (email == null || email.isEmpty()) {
      throw new UserAdminException("Email is required");
    }
    if (country == null) {
      throw new UserAdminException("Country is required");
    }

    if (userRepository.existsByUserId(email)) {
      throw new UserAdminException("Email already exists");
    }
    try {
      Business.valueOf(country);
    } catch (Exception e) {
      throw new UserAdminException("Unsupported country: " + country);
    }
  }

  public List<UserDailyLogin> getUsersAllLogin(Instant dateFrom, Instant dateTo) {

    List<com.app.cargill.model.User> userLogIn =
        userRepository.findAllUsersRecentUsed(dateFrom, dateTo);
    return userLogIn.stream()
        .map(this::mapUserToUserLogin)
        .map(
            userLogin -> {
              UserDailyLogin dbUser = userDailyLoginRepository.findByEmail(userLogin.getName());
              if (dbUser != null) {
                return userDailyLoginRepository.save(dbUser);
              } else {
                return userDailyLoginRepository.save(userLogin);
              }
            })
        .toList();
  }

  private String countryFix(String country) {
    if ("United Kingdom".equals(country)) {
      return "UK";
    } else {
      return country;
    }
  }

  private UserDailyLogin mapUserToUserLogin(com.app.cargill.model.User user) {
    UserDailyLogin userDailyLogin = UserDailyLogin.builder().build();
    userDailyLogin.setEmail(user.getEmail());
    userDailyLogin.setName(user.getUserDocument().getUserName());
    userDailyLogin.setCountryId(user.getUserDocument().getCountryId());
    userDailyLogin.setLastLoginDateTime(user.getUserDocument().getLastLoginDateTime());
    return userDailyLogin;
  }
}
