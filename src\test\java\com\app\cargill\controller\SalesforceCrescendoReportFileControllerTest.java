/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.sf.crescendo.model.ReportFileCrescendo;
import java.time.Instant;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoReportFileControllerTest {
  @InjectMocks private SalesforceCrescendoReportFileController controller;

  @Test
  void whenGetReportFilesIsCalledCorrectResponseIsReturned() {
    List<ReportFileCrescendo> result = controller.getReportFiles(Instant.now());
    assertNotNull(result);
  }
}
