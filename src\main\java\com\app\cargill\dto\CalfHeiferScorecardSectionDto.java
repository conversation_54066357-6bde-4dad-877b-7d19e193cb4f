/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CalfHeiferScorecardSectionDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Used to order sections within a larger container.
  /// </summary>
  /// <value>The index.</value>
  private Integer index;
  /// <summary>
  /// The name of the section to display to the user.
  /// </summary>
  /// <value>The name of the section.</value>
  /// <remarks>This will be a key to the String resources file.</remarks>
  private String sectionName;

  /// <summary>
  /// A set of questions that make up this section of a scorecard.
  /// </summary>
  /// <value>The questions.</value>
  private List<CalfHeiferScorecardQuestionDto> questions;
  /// <summary>
  /// Indicates if this sections score should be included in the overall scorecard
  /// score
  /// </summary>
  /// <value><c>true</c> if include in overall scorecard score; otherwise,
  /// <c>false</c>.</value>
  private Boolean includeInOverallScorecardScore;
}
