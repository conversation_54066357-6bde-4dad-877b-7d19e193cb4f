/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.model.CountryCosmos;
import com.app.cargill.cosmos.repo.CountryCosmosRepository;
import com.app.cargill.repository.CountriesRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CountryMigrationTest {
  @Mock private CountryCosmosRepository repository;

  @Mock private CountriesRepository postgresRepository;

  @InjectMocks private CountryMigration service;

  @BeforeEach
  void setUp() {
    lenient().when(postgresRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    CountryCosmos item =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/country.json"), CountryCosmos.class);

    Iterable<CountryCosmos> itemsList = List.of(item, item);
    when(repository.findAll()).thenReturn(itemsList);

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenPostMigrationIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result = service.postMigration(CosmosDataMigration.MigrationType.COUNTRY.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result =
          service.migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.COUNTRIES)).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    CountryCosmos item1 = new CountryCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    CountryCosmos item2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/country.json"), CountryCosmos.class);

    Iterable<CountryCosmos> itemsList = List.of(item1, item2);
    when(repository.findAll()).thenReturn(itemsList);

    MigrationResult result = service.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }
}
