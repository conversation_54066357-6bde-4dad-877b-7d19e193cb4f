/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "analytics")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString
public class AnalyticsDataPoint<T extends Serializable> extends BaseEntity implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @Column(name = "datapoint_name")
  @Enumerated(EnumType.STRING)
  private DataPointName dataPointName;

  @Type(JsonBinaryType.class)
  @Column(name = "meta_data", columnDefinition = "jsonb")
  private T metaData;

  public enum DataPointName {
    DDW_DETAILED_REPORT_DOWNLOAD,
    DDW_SUMMARY_REPORT_DOWNLOAD;
  }
}
