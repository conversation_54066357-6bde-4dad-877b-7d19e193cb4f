/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.constants.UserAccountType;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UserDto {

  private Long id;
  private String fullName;
  private String email;
  private String mobileNumber;
  private String principalName;
  private UserAccountType accountType;
  private String countryId;
  private List<RoleDto> roles;
  private AuthenticationPlatform authenticationPlatform;

  private UserPreferenceDto userPreferences;
  private ConfigurationDto configurations;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String password;
}
