[{"id": 328507, "createdDate": *************, "updatedDate": *************, "deleted": false, "localId": "1", "accountDocument": {"id": "e767de35-fe80-4a59-aa73-f174f79bf643", "Type": "012j00000010CWeAAM", "IsNew": false, "Phone": null, "Users": ["<EMAIL>"], "Active": true, "Assets": null, "BrandId": null, "CourtId": null, "OwnerId": "<EMAIL>", "PhotoId": null, "WonLost": null, "Contacts": [{"LastName": "Contact", "AccountId": "e767de35-fe80-4a59-aa73-f174f79bf643", "ContactId": "8cdb8c7b-526a-45cb-ae53-b656f5f26684", "FirstName": "Tester", "NeedsSync": true, "PhoneNumber": "+*************", "EmailAddress": "<EMAIL>", "CreateTimeUtc": "2023-07-07T08:25:30.566Z", "LastSyncTimeUtc": "2023-07-07T08:25:30.566Z", "LastUpdateDateTime": {"Date": "2023-07-07T08:25:30.566763500Z", "Epoch": **********}, "LastModifiedTimeUtc": {"Date": "2023-07-07T08:25:30.566763500Z", "Epoch": **********}}], "IsDeleted": false, "LegalName": null, "NeedsSync": true, "OtherFlag": null, "PriceFlag": null, "SiteCount": 0, "SubTypeID": null, "UserRoles": null, "WonLostId": null, "BusinessID": 23, "CreateUser": null, "CreditFlag": null, "DataSource": "CRESCENDO", "ERPPayerId": null, "PersonalID": null, "Securities": null, "SubBrandId": null, "lstOtherBU": null, "AccountName": "Crescendo test", "AccountType": 0, "Description": null, "ERPIdLength": null, "ERPShipToId": null, "IsDuplicate": null, "Liabilities": null, "QualityFlag": null, "ServiceFlag": null, "isFavourite": false, "AutoValidate": null, "CompanyEmail": null, "CustomerCode": "10222", "SourceSystem": null, "VeterinaryId": null, "AccountNumber": null, "AccountStatus": null, "BuyingGroupID": null, "CreateTimeUtc": "2023-07-07T08:25:30.566763500Z", "IsMobileFirst": true, "LastOrderDate": null, "PortfolioFlag": null, "AdditionalInfo": null, "ApprovalStatus": null, "ConsumerStatus": null, "CustomerStatus": null, "DateOfLastCall": null, "GoldenRecordId": null, "LastModifiedBy": null, "LastModifyUser": "<EMAIL>", "LastOrdersInfo": null, "MarginEstimate": null, "NewAccountType": null, "PreviousStatus": null, "ProspectStatus": null, "SalesTerritory": null, "VolumeEstimate": null, "WebSiteAddress": null, "AccountCurrency": null, "DateOfLastVisit": null, "LastAdminUpdate": null, "LastInvoiceDate": null, "LastSyncTimeUtc": "2023-07-07T08:25:30.566763500Z", "ParentAccountID": null, "PerformanceFlag": null, "PhysicalAddress": {"City": "Kabul", "Street": "Test street", "Country": "Afghanistan", "PostalCode": "26600", "CountyCommunity": null, "StateOrProvince": null}, "WonLostComments": null, "AccountValidated": null, "ERPPayerIdLength": null, "LastInvoicesInfo": null, "LastUpdateUserId": null, "MarketInfluencer": null, "NineBoxStepTwoID": "0 - Not Defined", "PrimaryContactId": "2e170d73-b0cb-47e8-b8c9-068e83718b91", "ReqProcessingLog": null, "SegmentStepOneId": "<PERSON>", "ChangeAccountType": null, "ERPShiptoIdLength": null, "IsServicedbyCSPro": null, "ReasonDescription": null, "WonLostReasonCode": null, "SocialMediaAddress": null, "LastModifiedTimeUtc": null, "LimitChangeReasonId": null, "PrimaryContactTitle": "Tester Contact", "AvailabilityOnMarket": null, "BusinessSolutionFlag": null, "DefaultCustServiceID": null, "DeliveryInstructions": null, "ExternalLeadSourceID": null, "LastModificationDate": null, "CorrespondenceAddress": null, "DefaultCargillPlantID": null, "ExternalBuyingGroupID": null, "MobileLastUpdatedTime": null, "OwnerProfileNameandId": null, "ExternalParentAccountID": null, "OtherActivityProduction": null, "PrimaryContactPhoneNumber": "+*************", "CurrentUserProfileNameandId": null, "erpIdLengthValidatorIsError": null}}]