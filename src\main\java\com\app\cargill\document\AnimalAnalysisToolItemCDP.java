/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.*;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode
public class AnimalAnalysisToolItemCDP implements Serializable {

  @JsonProperty("PenId")
  public UUID penId;

  @JsonProperty("PenName")
  public String penName;

  @JsonProperty("AnimalDetails")
  private List<AnimalAnalysisDetailsToolItemCDP> animalDetails;
}
