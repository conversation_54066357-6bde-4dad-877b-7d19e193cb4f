/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.*;
import com.app.cargill.cosmos.repo.SitesCosmosRepository;
import com.app.cargill.document.*;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.IAnimalClassService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SitesMigrationTest {
  @Mock private SitesCosmosRepository sitesCosmosRepository;

  @Mock private SitesRepository sitesRepository;
  @Mock private VisitsRepository visitsRepository;
  @Mock private PensRepository pensRepository;

  @Mock private DietRepository dietRepository;

  @Mock private IAnimalClassService animalClassService;

  @InjectMocks private SitesMigration service;

  @BeforeEach
  void setUp() {
    lenient().when(sitesRepository.saveAll(any())).thenReturn(new ArrayList<>());
    lenient().when(pensRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenSitesPostMigrationIsCalledCorrectResultsAreReturned() {
    when(sitesRepository.findAll()).thenReturn(List.of(loadSite()));
    when(visitsRepository.findAll()).thenReturn(List.of(loadVisit()));

    MigrationResult result = null;
    try {
      result = service.postMigration(CosmosDataMigration.MigrationType.SITES.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }

    assertNotNull(result);
  }

  @Test
  void whenSitesPostMigrationIsCalledWithoutMigrationTypeEmptyResultsReturned() {
    MigrationResult result = null;
    try {
      result = service.postMigration(null).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }

    assertNotNull(result);
  }

  @Test
  void whenMigrationFixForPenIsCalledCorrectResponseIsReturned()
      throws ExecutionException, InterruptedException {

    MigrationResult result = null;

    List<UUID> dietIds = List.of(UUID.randomUUID(), UUID.randomUUID());

    lenient()
        .when(pensRepository.findAll())
        .thenReturn(
            List.of(
                Pens.builder()
                    .penDocument(
                        PenDocument.builder()
                            .dietId(dietIds.get(0))
                            .dietSource(DietSource.USER_CREATED)
                            .build())
                    .build(),
                Pens.builder()
                    .penDocument(
                        PenDocument.builder()
                            .dietId(dietIds.get(1))
                            .dietSource(DietSource.SYSTEM_GENERATED)
                            .build())
                    .build()));

    lenient()
        .when(dietRepository.findById(dietIds.get(0).toString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder()
                        .id(dietIds.get(0))
                        .animalType(AnimalClass.builder().subClass("Milking").build())
                        .build())
                .build());
    lenient()
        .when(dietRepository.findById(dietIds.get(1).toString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder()
                        .id(dietIds.get(1))
                        .animalType(
                            AnimalClass.builder()
                                .id(UUID.fromString("00000000-0000-0000-0000-000000000008"))
                                .build())
                        .build())
                .build());

    lenient()
        .when(animalClassService.getAnimalTypeId("Milking"))
        .thenReturn(UUID.fromString("00000000-0000-0000-0000-000000000002"));

    result = service.migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.PENS)).get();

    assertNotNull(result);
  }

  @Test
  void whenSitesAreEmptyFailedResponseIsReturned() {
    when(sitesRepository.findAll()).thenReturn(new ArrayList<>());
    MigrationResult result = null;
    try {
      result = service.postMigration(CosmosDataMigration.MigrationType.SITES.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenVisitsAreEmptyFailedResponseIsReturned() {
    when(sitesRepository.findAll()).thenReturn(List.of(loadSite()));
    when(visitsRepository.findAll()).thenReturn(new ArrayList<>());
    MigrationResult result = null;
    try {
      result = service.postMigration(CosmosDataMigration.MigrationType.SITES.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenMoveAll() {
    service.moveAll();
    assertTrue(true);
  }

  private Sites loadSite() {
    return Sites.builder()
        .siteDocument(
            SiteDocument.builder()
                .id(UUID.fromString("07315216-9ea9-475a-a0fc-39dc8357d44"))
                .visits(
                    List.of(
                        SiteVisit.builder()
                            .visitDate(Instant.now())
                            .status(VisitStatus.Published)
                            .build()))
                .barns(List.of(Barn.builder().build()))
                .dataSourceMappings(List.of())
                .origination(ApplicationMapping.LM_SITE_SYSTEM_NAME)
                .dateOfLastVisit(Instant.now())
                .build())
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Visits loadVisit() {

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .siteId(UUID.fromString("07315216-9ea9-475a-a0fc-39dc8357d44"))
            .status(VisitStatus.InProgress)
            .rumenHealth(
                RumenHealthTool.builder()
                    .pens(
                        List.of(
                            RumenHealthToolItem.builder()
                                .cudChewingCowsCount(
                                    CudChewingCowCount.builder()
                                        .countNo(5)
                                        .countYes(10)
                                        .noPercent(50.5)
                                        .yesPercent(60.5)
                                        .totalCount(0.0)
                                        .build())
                                .cudChewsCount(
                                    List.of(
                                        CudChewingCount.builder()
                                            .chewsCount(10)
                                            .cowNumber(10)
                                            .build()))
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .build()))
                    .visitId(UUID.randomUUID())
                    .goals(
                        List.of(
                            HerdAnalysisGoal.builder()
                                .cudChews(3.6)
                                .percentChewing(50.6)
                                .stage(LactationStage.FarOffDry)
                                .build()))
                    .build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .cudChewing(
                CudChewingTool.builder()
                    .createTimeUtc(Instant.now())
                    .createUser("admin@admin")
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder()
                                .countNo(5)
                                .countYes(5)
                                .noPercent(0.0)
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .yesPercent(0.0)
                                .build()))
                    .build())
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        Arrays.asList(
                            AnimalAnalysisToolItem.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    Arrays.asList(
                                        AnimalAnalysisDetailsToolItem.builder()
                                            .locomotionScore(1.0)
                                            .build()))
                                .build()))
                    .build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .visitDate(Instant.parse("2022-10-05T09:50:03.028Z"))
            .visitName("John Doe 2022-09-29T08:56:34.390Z")
            .build();

    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }
}
