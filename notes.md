#### Remove duplicate accounts after running the migration script
```sql
DELETE FROM accounts where account_document->>'GoldenRecordId' in (select gid from (select account_document->>'GoldenRecordId' as gid, COUNT(*) as co from accounts GROUP BY account_document->>'GoldenRecordId' HAVING COUNT(*) > 1) b)
```

```sql
select account_document->>'GoldenRecordId' as gid, COUNT(*) as co from accounts GROUP BY account_document->>'GoldenRecordId' HAVING COUNT(*) > 1 ORDER BY gid DESC
```

#### Get all contacts
```sql
select jsonb_array_elements((account_document->>'Contacts')::jsonb) as contacts from accounts
```

#### A sample for more complex query on contacts
```sql
select cont.contacts from (select jsonb_array_elements((account_document->>'Contacts')::jsonb) as contacts from accounts where account_document->>'DataSource' IS NULL) as cont where cont.contacts->>'<PERSON><PERSON><PERSON>' is not null;
```
