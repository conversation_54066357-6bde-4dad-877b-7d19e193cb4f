/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.document.AnimalAnalysisToolCDP;
import com.app.cargill.dto.ProfitabilityAnalysisData;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Visits;
import java.time.Instant;
import java.util.List;

public interface ICdpV1Service {
  List<AccountDocumentDTO> getAllAccountsByFromAndToDateV1(Instant dateFrom, Instant dateTo);

  List<SiteDocumentDTO> getAllSitesByFromAndToDateV1(Instant dateFrom, Instant dateTo);

  List<VisitDocumentDTO> getAllVisitsByFromAndToDateV1(Instant dateFrom, Instant dateTo);

  List<Visits> getAllVisitsForOneYear();

  List<ProfitabilityAnalysisData> getAllProftabilityAnalysisDataByFromAndToDateV1(
      Instant dateFrom, Instant dateTo);

  List<AnimalAnalysisToolCDP> getAnimalAnalysisDetails();
}
