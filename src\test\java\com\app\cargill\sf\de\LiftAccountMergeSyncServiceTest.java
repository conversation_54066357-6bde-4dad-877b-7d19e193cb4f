/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.data.MergeAccountsService;
import com.app.cargill.sf.cc.service.LiftAccountMergeService;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.joda.time.Instant;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftAccountMergeSyncServiceTest {

  @InjectMocks private LiftSyncService liftSyncService;

  @Mock private LiftAccountMergeService accountMergeService;

  @Mock private MergeAccountsService mergeAccountsService;
  @Mock private SitesRepository sitesRepository;
  @Mock private AccountsRepository accountsRepository;

  @Test
  void testExecuteAccountsMergeSync_withTimestamp_shouldProcessMergedAccounts() throws Exception {
    // Arrange
    Instant timestamp = Instant.now();
    AccountMergeRecordDto dto = AccountMergeRecordDto.builder().build();
    dto.setSourceId("source123");
    dto.setTargerId("target123");

    when(accountMergeService.getAllMergedAccounts(any())).thenReturn(List.of(dto));
    when(accountsRepository.findAllByGoldenRecordId(any()))
        .thenReturn(
            List.of(
                Accounts.builder()
                    .accountDocument(AccountDocument.builder().id(UUID.randomUUID()).build())
                    .build()));
    when(sitesRepository.countByAccountId(any())).thenReturn(5);

    // Act
    CompletableFuture<SyncResult> future = liftSyncService.executeAccountsMergeSync(timestamp);
    SyncResult result = future.get();

    // Assert
    assertEquals("Merged Accounts", result.getName());
    assertNotNull(result.getDuration());
    verify(mergeAccountsService).transferAccountData("source123", "target123");
  }

  @Test
  void testExecuteAccountsMergeSync_withoutTimestamp_shouldCallServiceWithoutFilters()
      throws Exception {
    when(accountMergeService.getAllMergedAccounts(null)).thenReturn(Collections.emptyList());

    CompletableFuture<SyncResult> future = liftSyncService.executeAccountsMergeSync(null);
    SyncResult result = future.get();

    assertEquals("Merged Accounts", result.getName());
    assertNotNull(result.getDuration());
    verify(mergeAccountsService, never()).transferAccountData(any(), any());
  }

  @Test
  void testExecuteAccountsMergeSync_shouldReturnFailedFutureOnError() {
    when(accountMergeService.getAllMergedAccounts(any()))
        .thenThrow(new RuntimeException("Simulated failure"));

    CompletableFuture<SyncResult> future = liftSyncService.executeAccountsMergeSync(Instant.now());

    assertNotNull(future);
    assertTrue(future.isCompletedExceptionally());
  }
}
