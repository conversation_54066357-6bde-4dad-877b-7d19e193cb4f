/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CalculatedOutputs implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("ForageFeedCostPerDay")
  private Double forageFeedCostPerDay;

  @JsonProperty("GrainsFeedCostPerDay")
  private Double grainsFeedCostPerDay;

  @JsonProperty("PurchasedBulkFeedCostPerDay")
  private Double purchasedBulkFeedCostPerDay;

  @JsonProperty("PurchasedBagsFeedCostPerDay")
  private Double purchasedBagsFeedCostPerDay;

  @JsonProperty("TotalPurchasedCostPerDay")
  private Double totalPurchasedCostPerDay;

  @JsonProperty("TotalConcentrateCostPerDay")
  private Double totalConcentrateCostPerDay;

  @JsonProperty("TotalFeedCostPerDay")
  private Double totalFeedCostPerDay;

  @JsonProperty("ForageFeedCostPerCowPerDay")
  private Double forageFeedCostPerCowPerDay;

  @JsonProperty("GrainsCostPerCowPerDay")
  private Double grainsCostPerCowPerDay;

  @JsonProperty("PurchasedBulkFeedPerCowPerDay")
  private Double purchasedBulkFeedPerCowPerDay;

  @JsonProperty("PurchasedBagsFeedPerCowPerDay")
  private Double purchasedBagsFeedPerCowPerDay;

  @JsonProperty("TotalPurchasedCostPerCowPerDay")
  private Double totalPurchasedCostPerCowPerDay;

  @JsonProperty("TotalConcentrateCostPerCowPerDay")
  private Double totalConcentrateCostPerCowPerDay;

  @JsonProperty("TotalFeedCostPerCowPerDay")
  private Double totalFeedCostPerCowPerDay;

  @JsonProperty("ForageKgDMPerDay")
  private Double forageKgDMPerDay;

  @JsonProperty("GrainsKgDMPerDay")
  private Double grainsKgDMPerDay;

  @JsonProperty("PurchasedBulkKgDMPerDay")
  private Double purchasedBulkKgDMPerDay;

  @JsonProperty("PurchasedBagsKgDMPerDay")
  private Double purchasedBagsKgDMPerDay;

  @JsonProperty("TotalPurchasedCostKgDMPerDay")
  private Double totalPurchasedCostKgDMPerDay;

  @JsonProperty("TotalConcentrateCostKgDMPerDay")
  private Double totalConcentrateCostKgDMPerDay;

  @JsonProperty("TotalFeedCostKgDMPerDay")
  private Double totalFeedCostKgDMPerDay;

  @JsonProperty("ForagePercentage")
  private Double foragePercentage;
}
