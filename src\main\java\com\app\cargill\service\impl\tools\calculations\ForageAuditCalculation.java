/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.ListUtils;

public class ForageAuditCalculation {

  public Scorecard calculateFields(Scorecard tool) {

    if (tool == null || tool.getSections() == null) return null;

    for (ScorecardSection section : tool.getSections()) {
      section.setStatus(sectionStatus(section));
      if (section.getScorecardSilages() == null) continue;
      for (ScorecardSilage silage : section.getScorecardSilages()) {
        silage.setStatus(questionsStatus(ListUtils.emptyIfNull(silage.getQuestions())));
        silage.setAnswersPositivePercent(answersPositivePerSilagePercent(silage));
        silage.setAnswersNegativePercent(answersNegativePerSilagePercent(silage));
      }
    }
    return tool;
  }

  private Double answersPositivePerSilagePercent(ScorecardSilage silage) {
    Integer denominator = getQuestionPerSilageCount(silage);
    if (denominator > 0)
      return roundAvoid(((double) getAnswerPerSilageCount(silage) / denominator) * 100, 0);
    return 0.0;
  }

  /*
   * TODO might have to refactor after confirming the logic of getAnswerPerSilageCount
   *  method.
   */
  private Double answersNegativePerSilagePercent(ScorecardSilage silage) {
    Integer answerPerSilageCount = getAnswerPerSilageCount(silage);
    if (answerPerSilageCount > 0) return 100.0 - silage.getAnswersPositivePercent();

    return 0.0;
  }

  private Integer getQuestionPerSilageCount(ScorecardSilage silage) {
    int count = 0;
    for (ScorecardQuestion question : ListUtils.emptyIfNull(silage.getQuestions())) {
      if (question != null
          && question.getAvailableAnswers() != null
          && (Objects.requireNonNullElse(question.getIsQuestionRequired(), false)
              || (!(Objects.requireNonNullElse(question.getIsQuestionRequired(), false))
                  && question.getSelectedAnswer() != null))) {
        count +=
            question.getAvailableAnswers().stream()
                .filter(a -> a.getPointValue() != null)
                .mapToInt(ScorecardAnswer::getPointValue)
                .max()
                .orElse(0);
      }
    }
    return count;
  }

  /*
   * TODO get confirmation as in the older code, itemsOfConcernOnly is passed as
   *  a param but it was not use in the method
   */
  private Integer getAnswerPerSilageCount(ScorecardSilage silage) {
    int count = 0;
    for (ScorecardQuestion question : ListUtils.emptyIfNull(silage.getQuestions())) {
      if (question != null
          && question.getSelectedAnswer() != null
          && question.getSelectedAnswer().getPointValue() != null)
        count += question.getSelectedAnswer().getPointValue();
    }
    return count;
  }

  private ToolStatuses sectionStatus(ScorecardSection section) {

    List<ScorecardQuestion> questions = new ArrayList<>();

    if (section != null && section.getScorecardSilages() != null) {

      questions =
          ListUtils.emptyIfNull(section.getScorecardSilages()).stream()
              .filter(silage -> silage.getQuestions() != null)
              .map(ScorecardSilage::getQuestions)
              .flatMap(List::stream)
              .toList();
    }
    return questionsStatus(questions);
  }

  private ToolStatuses questionsStatus(List<ScorecardQuestion> questions) {

    int required = 0;
    int requiredAnswered = 0;
    int nonRequiredAnswered = 0;

    for (ScorecardQuestion question : questions) {
      if (Boolean.TRUE.equals(
          Objects.requireNonNullElse(question.getIsQuestionRequired(), false))) {
        required++;
        if (question.getSelectedAnswer() != null) requiredAnswered++;
      } else if (question.getSelectedAnswer() != null) nonRequiredAnswered++;
    }

    if (nonRequiredAnswered == 0 && requiredAnswered == 0) return ToolStatuses.NotStarted;
    if (requiredAnswered >= required) return ToolStatuses.Completed;

    return ToolStatuses.InProgress;
  }
}
