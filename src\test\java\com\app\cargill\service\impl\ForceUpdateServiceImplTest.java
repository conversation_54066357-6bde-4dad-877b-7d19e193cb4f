/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.document.BuildInformationDocument;
import com.app.cargill.dto.ForceUpdateDto;
import com.app.cargill.model.BuildInformation;
import com.app.cargill.repository.BuildInformationRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ForceUpdateServiceImplTest {

  @Mock BuildInformationRepository buildInformationRepository;

  @InjectMocks ForceUpdateServiceImpl forceUpdateServiceImpl;

  @Test
  void whenGetBuildInfoIsCalledCorrectResultIsReturned() {

    BuildInformation buildInformation =
        BuildInformation.builder()
            .buildInfoDocument(BuildInformationDocument.builder().build())
            .build();
    when(buildInformationRepository.getBuildInfo()).thenReturn(buildInformation);

    ForceUpdateDto dto = forceUpdateServiceImpl.getBuildInfo();

    assertNotNull(dto);
  }

  @Test
  void whenGetBuildInfoIsNullCorrectResultIsReturned() {

    when(buildInformationRepository.getBuildInfo()).thenReturn(null);

    ForceUpdateDto dto = forceUpdateServiceImpl.getBuildInfo();

    assertNull(dto);
  }
}
