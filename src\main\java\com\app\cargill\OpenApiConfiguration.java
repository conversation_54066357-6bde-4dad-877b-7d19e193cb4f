/* Cargill Inc.(C) 2022 */
package com.app.cargill;

import com.app.cargill.confproperties.SpringDocProperties;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.servers.Server;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.media.StringSchema;
import io.swagger.v3.oas.models.parameters.Parameter;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.security.SecurityScheme.Type;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerMethod;

@Configuration
@RequiredArgsConstructor
@OpenAPIDefinition(
    servers = {
      @Server(
          url = "${springdoc.swagger-ui.base-url-private-postfix}",
          description = "Default Server URL Postfix For Private Endpoints"),
      @Server(
          url = "${springdoc.swagger-ui.base-url-public-postfix}",
          description = "Default Server URL Postfix For Public Endpoints")
    })
public class OpenApiConfiguration implements OperationCustomizer {
  private final SpringDocProperties springDocProperties;
  private static final String OAUTH_SCHEMA_NAME = "OAuth2";

  @Bean
  public OpenAPI getOpenApi() {
    return new OpenAPI().info(apiInfo()).components(apiComponents()).security(getSecurity());
  }

  private List<SecurityRequirement> getSecurity() {
    List<SecurityRequirement> requirementList = new ArrayList<>();
    SecurityRequirement requirement =
        new SecurityRequirement()
            .addList(OAUTH_SCHEMA_NAME, List.of("all"))
            .addList("api", List.of("all"));
    requirementList.add(requirement);
    return requirementList;
  }

  private Info apiInfo() {
    return new Info()
        .title("Dairy Enteligen Service API")
        .description("API for servicing the Dairy Enteligen Mobile application")
        .version("2.0")
        .license(apiLicence());
  }

  private License apiLicence() {
    return new License().name("Copyright");
  }

  private Components apiComponents() {

    OAuthFlow oAuthFlow = new OAuthFlow().tokenUrl(springDocProperties.getAccessTokenUrl());

    SecurityScheme oathScheme = new SecurityScheme();
    oathScheme.setFlows(new OAuthFlows().password(oAuthFlow));
    oathScheme.setType(Type.OAUTH2);
    oathScheme.setScheme(OAUTH_SCHEMA_NAME);

    SecurityScheme oathSchemeBearer = new SecurityScheme();
    oathSchemeBearer.setBearerFormat("jwt");
    oathSchemeBearer.setType(Type.HTTP);
    oathSchemeBearer.setScheme("bearer");
    oathSchemeBearer.setName("api");

    Components oAuthComponents = new Components();
    oAuthComponents.addSecuritySchemes(OAUTH_SCHEMA_NAME, oathScheme);
    oAuthComponents.addSecuritySchemes("api", oathSchemeBearer);

    return oAuthComponents;
  }

  @Override
  public Operation customize(Operation operation, HandlerMethod handlerMethod) {
    // for customized global headers
    Parameter authIssuer =
        new Parameter()
            .in(ParameterIn.HEADER.toString())
            .schema(new StringSchema()._default("azure"))
            .name("x-auth-issuer")
            .description(
                "set x-auth-issuer to okta/azure on cargill environments when using swagger")
            .required(false);
    operation.addParametersItem(authIssuer);
    return operation;
  }
}
