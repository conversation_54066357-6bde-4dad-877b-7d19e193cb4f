/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RumenHealthManureScoreToolItemDto extends EditableToolPenEntityBaseDto {
  /** */
  @Serial private static final long serialVersionUID = 1L;

  private List<UUID> manureScoreVisitsSelected;

  private RumenHealthManureScoresDto manureScores;

  private Boolean isToolItemNew;

  private Integer daysInMilk;

  private Boolean isFirstTimeWithScore;
}
