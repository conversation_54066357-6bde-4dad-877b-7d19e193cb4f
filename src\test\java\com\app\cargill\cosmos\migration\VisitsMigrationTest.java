/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.cosmos.model.VisitCosmos;
import com.app.cargill.cosmos.repo.VisitsCosmosRepository;
import com.app.cargill.document.AnimalAnalysisDetailsToolItem;
import com.app.cargill.document.AnimalAnalysisTool;
import com.app.cargill.document.AnimalAnalysisToolItem;
import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.CudChewingByPen;
import com.app.cargill.document.CudChewingCount;
import com.app.cargill.document.CudChewingCowCount;
import com.app.cargill.document.CudChewingTool;
import com.app.cargill.document.HerdAnalysisGoal;
import com.app.cargill.document.LocomotionTool;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RumenHealthManureScoreTool;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.RumenHealthToolItem;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.VisitsRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
class VisitsMigrationTest {
  @Mock private VisitsCosmosRepository visitsCosmosRepository;

  @Mock private VisitsRepository visitsRepository;

  @InjectMocks private VisitsMigration service;

  @BeforeEach
  void setUp() {
    lenient().when(visitsRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenPostMigrationIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result = service.postMigration(CosmosDataMigration.MigrationType.VISIT.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenMigrationIsInvokedEverythingPassesAllTools()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    VisitCosmos item =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/visit-with-tools.json"), VisitCosmos.class);

    when(visitsCosmosRepository.findAll()).thenReturn(Flux.just(item, item));
    when(visitsRepository.findByVisitId(anyString())).thenReturn(mock(Visits.class));

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenMigrationIsInvokedEverythingPassesNoTools()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    VisitCosmos item =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/visit-no-tools.json"), VisitCosmos.class);

    when(visitsCosmosRepository.findAll()).thenReturn(Flux.just(item, item));
    when(visitsRepository.findByVisitId(anyString())).thenReturn(mock(Visits.class));

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    VisitCosmos item1 = new VisitCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    VisitCosmos item2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/visit-with-tools.json"), VisitCosmos.class);

    when(visitsCosmosRepository.findAll()).thenReturn(Flux.just(item1, item2));
    when(visitsRepository.findByVisitId(anyString())).thenReturn(mock(Visits.class));

    MigrationResult result = service.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() throws IOException {

    MigrationResult result = null;
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.registerModule(new JavaTimeModule());
      VisitCosmos item =
          objectMapper.readValue(
              new File("src/test/resources/cosmos/samples/visit-with-tools.json"),
              VisitCosmos.class);
      when(visitsCosmosRepository.findByRumenHealthManureScore()).thenReturn(Flux.just(item));
      when(visitsRepository.findAll()).thenReturn(List.of(loadVisit()));
      when(visitsRepository.saveAllAndFlush(any())).thenReturn(List.of(loadVisit()));
      result = service.migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.VISITS)).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  private Visits loadVisit() {

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.fromString("005ce5b0-2b6e-4310-8a4b-a37649bf6c3a"))
            .customerId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .siteId(UUID.fromString("07315216-9ea9-475a-a0fc-39dc8357d44"))
            .status(VisitStatus.InProgress)
            .rumenHealth(
                RumenHealthTool.builder()
                    .pens(
                        List.of(
                            RumenHealthToolItem.builder()
                                .cudChewingCowsCount(
                                    CudChewingCowCount.builder()
                                        .countNo(5)
                                        .countYes(10)
                                        .noPercent(50.5)
                                        .yesPercent(60.5)
                                        .totalCount(0.0)
                                        .build())
                                .cudChewsCount(
                                    List.of(
                                        CudChewingCount.builder()
                                            .chewsCount(10)
                                            .cowNumber(10)
                                            .build()))
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .build()))
                    .visitId(UUID.randomUUID())
                    .goals(
                        List.of(
                            HerdAnalysisGoal.builder()
                                .cudChews(3.6)
                                .percentChewing(50.6)
                                .stage(LactationStage.FarOffDry)
                                .build()))
                    .build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .cudChewing(
                CudChewingTool.builder()
                    .createTimeUtc(Instant.now())
                    .createUser("admin@admin")
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder()
                                .countNo(5)
                                .countYes(5)
                                .noPercent(0.0)
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .yesPercent(0.0)
                                .build()))
                    .build())
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        List.of(
                            AnimalAnalysisToolItem.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    List.of(
                                        AnimalAnalysisDetailsToolItem.builder()
                                            .locomotionScore(1.0)
                                            .build()))
                                .build()))
                    .build())
            .metabolicIncidence(MetabolicIncidenceTool.builder().build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
            .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
            .visitDate(Instant.parse("2022-10-05T09:50:03.028Z"))
            .visitName("John Doe 2022-09-29T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }
}
