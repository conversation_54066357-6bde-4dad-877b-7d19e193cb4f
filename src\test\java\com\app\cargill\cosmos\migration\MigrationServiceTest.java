/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import com.app.cargill.repository.UserMigrationRepository;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class MigrationServiceTest {

  @Mock private UserMigrationRepository userMigrationRepository;
  @Mock private SitesMigration sitesMigration;
  @Mock private VisitsMigration visitsMigration;
  @InjectMocks private MigrationService migrationService;
}
