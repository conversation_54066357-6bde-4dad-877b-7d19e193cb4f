/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DuplicatePenIdDTO implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("PenCount")
  private String penCount;

  @JsonProperty("PenId")
  private String penId;

  @JsonProperty("SiteId")
  private String siteId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("Source")
  private String source;

  @JsonProperty("GroupId")
  private String groupId;

  @JsonProperty("DuplicateFlag")
  private Boolean duplicateFlag;

  @JsonProperty("PenIds")
  private List<String> penIds;

  @JsonProperty("visitPenIds")
  private List<String> visitPenIds;

  @JsonProperty("NoVisitPenIds")
  private List<String> noVisitPenIds;
}
