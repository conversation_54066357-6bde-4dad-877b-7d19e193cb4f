app.debug.web-client=true
app.scheduling.enable=false
cloud.aws.region=${AWS_DEFAULT_REGION:us-east-1}
cloud.aws.s3.bucket-name=${BUCKET_DEDISCOVER_NAME:cargill-customer-portraits}
cloud.aws.s3.access-key=${AWS_ACCESS_KEY_ID:test}
cloud.aws.s3.secret-key=${AWS_SECRET_ACCESS_KEY:test}
cosmos.uri=https://crescendo-preprod.documents.azure.com:443/
cosmos.key=****************************************************************************************
cosmos.database=preprod-prodclone
cosmos.queryMetricsEnabled=true
login.okta.oauth2.issuer=https://cargillcustomer-qa.oktapreview.com/oauth2/aushb5mlqe4IiZu3k0h7
login.okta.oauth2.client-id=0oajmlp89rXYEEGxG0h7
login.azure.oauth2.tenant-id=a348b8b8-a465-4163-859b-9644d697c2b6
login.azure.oauth2.issuer=https://sts.windows.net/${login.azure.oauth2.tenant-id}/
login.azure.oauth2.client-id=45991cfc-031d-4d89-9260-f9d43433ef75
salesforce.lift.token-host=test.salesforce.com
salesforce.lift.token-path=/services/oauth2/token
salesforce.lift.client-secret=57D7EFB7FF2C407CCBA3282E1439D4A7800A1F82747EBE172324A5D5900E9039
salesforce.lift.client-id=3MVG9pHRjzOBdkd.PTG4KZTEYvWRlTi.v1AY6O.BDcCnS_5ZIYHW0H9pivvdFvDmKhUoTrkn4M85HfbOema_9
salesforce.lift.grant-type=password
salesforce.lift.password=DEIntegration@123
salesforce.lift.username=<EMAIL>
salesforce.lift.default-owner-id=<EMAIL>
spring.datasource.url=${DB_CONNECTION:******************************************}
spring.datasource.username=${DB_USERNAME:admin}
spring.datasource.password=${DB_PASSWORD:Passw0rd}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.show_sql=false
spring.sql.init.mode=never
spring.data.rest.default-media-type=application/json
springdoc.swagger-ui.base-url-private-postfix=${SWAGGER_BASE_URL_POSTFIX:/}
springdoc.swagger-ui.base-url-public-postfix=${SWAGGER_BASE_URL_POSTFIX:/}
springdoc.swagger-ui.oauth.client-id=clientId
springdoc.swagger-ui.oauth.client-secret=secret
springdoc.swagger-ui.oauth.use-basic-authentication-with-access-code-grant=true
springdoc.swagger-ui.oauth.scope-separator= 
springdoc.swagger-ui.oauth.access-token-url=${SWAGGER_ACCESS_TOKEN_URL:https://api-dev.dev.dev-cglcloud.com/api/dairyenteligen/dediscover/info/auth/okta-token}
#azure blob
azure.blob.storage.connection=${AZURE_BLOB_STORAGE_CRED:************************************************************************************************************************************************************************************************************************************************************}

#DairyForecast Connection
spring.security.oauth2.client.registration.dairyforecast.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.dairyforecast.client-id=4135ff52-a9fa-4c56-a171-023b0fa51f97
spring.security.oauth2.client.registration.dairyforecast.client-secret=****************************************
spring.security.oauth2.client.registration.dairyforecast.scope=fcce74cd-f098-4844-8fe7-4efffd39041c/.default
spring.security.oauth2.client.provider.dairyforecast.token-uri=https://login.microsoftonline.com/57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/oauth2/v2.0/token
app.dairyforecast.url=http://localhost

api.admin.id=ccf6baea-ce19-4bc8-b8cd-8c990ea358d5


#salesforce Crescendo properties
salesforce.crescendo.token-host=cargill18--preprod.sandbox.my.salesforce.com
salesforce.crescendo.token-path=/services/oauth2/token
salesforce.crescendo.client-secret=${SALESFORCE_CRESCENDO_SECRET:****************************************************************}
salesforce.crescendo.client-id=${SALESFORCE_CRESCENDO_CLIENT_ID:3MVG9iBaHQRfreBmcng3LzB7txKf.Cgl3feXThDZP8oETXSnatzJOUHovst75ApsQDnRRxQvsJ5i4ee0h4J3t}
salesforce.crescendo.default-owner-id=Crescendo_DE_Integration