/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import com.app.cargill.constants.Business;
import com.app.cargill.document.UserRole;
import com.app.cargill.sf.cc.model.UserAccessContainer;
import com.app.cargill.sf.cc.model.simple.Account;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class LiftAccountUsersMapper {
  private LiftAccountUsersMapper() {}

  public static Set<String> transformToUsers(Account account) {
    Set<String> users = new HashSet<>();
    users.add(account.getOwner().getEmail());
    if (account.getUserAccessContainer() != null) {
      users.addAll(
          account.getUserAccessContainer().getRecords().stream()
              .map(
                  r -> {
                    return r.getUserRecord().getEmail();
                  })
              .collect(Collectors.toSet()));
    }

    return users;
  }

  public static List<UserRole> transformToUserRoles(Account account) {
    List<UserRole> userRoles = new ArrayList<>();
    if (account.getUserAccessContainer() == null) {
      return userRoles;
    }
    for (UserAccessContainer uac : account.getUserAccessContainer().getRecords()) {
      UserRole userRole =
          UserRole.builder()
              .roleType(uac.getRoleName() != null ? uac.getRoleName() : "Account Representative")
              .userName(
                  uac.getUserRecord().getEmail() != null ? uac.getUserRecord().getEmail() : null)
              .userBusinessUnit(getBusinessId(uac.getUserRecord().getCountry()))
              .build();
      userRoles.add(userRole);
    }

    return userRoles;
  }

  private static Integer getBusinessId(String country) {
    try {
      return Business.valueOf(country).getBusinessId();
    } catch (Exception e) {
      return Business.Global.getBusinessId();
    }
  }
}
