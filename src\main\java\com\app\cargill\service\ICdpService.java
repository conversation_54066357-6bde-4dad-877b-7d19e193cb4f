/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import java.time.Instant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ICdpService {

  Page<AccountDocumentDTO> getAllAccountsByFromAndToDate(
      Instant dateFrom, Instant dateTo, Pageable pageable);

  Page<SiteDocumentDTO> getAllSitesByFromAndToDate(
      Instant dateFrom, Instant dateTo, Pageable pageable);

  Page<VisitDocumentDTO> getAllVisitsByFromAndToDate(
      Instant dateFrom, Instant dateTo, Pageable pageable);
}
