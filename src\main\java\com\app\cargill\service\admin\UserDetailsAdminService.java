/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.admin;

import com.app.cargill.dto.admin.User;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDetailsAdminService {

  private final UserAdminService userAdminService;
  private final VisitAdminService visitAdminService;

  public User getUserDetails(UUID uuid) {
    User userDetails = userAdminService.getUser(uuid);
    userDetails.setFirstVisit(visitAdminService.getUserFirstVisit(userDetails.getEmail()));
    return userDetails;
  }
}
