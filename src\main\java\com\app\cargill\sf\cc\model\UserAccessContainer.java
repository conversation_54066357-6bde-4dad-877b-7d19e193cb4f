/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserAccessContainer implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("recordId")
  @JsonAlias({"Id", "recordId"})
  private String recordId;

  @JsonProperty("accountRecord")
  @JsonAlias({"accountRecord", "DE_Account__r"})
  private AccountRecord accountRecord;

  @JsonProperty("accountId")
  @JsonAlias({"accountId", "DE_Account__c"})
  private String accountId;

  @JsonProperty("roleName")
  @JsonAlias({"roleName", "DE_Role_Name__c"})
  private String roleName;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("userRecord")
  @JsonAlias({"userRecord", "DE_User__r"})
  private UserRecord userRecord;

  @JsonProperty("userId")
  @JsonAlias({"userId", "DE_User__c"})
  private String userId;

  @JsonProperty("LastModifiedTimeUtc")
  @JsonAlias({"LastModifiedTimeUtc", "LastModifiedDate"})
  private String lastModifiedTimeUtc;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("Owner")
  private OwnerRecord ownerRecord;
}
