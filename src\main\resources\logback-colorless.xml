<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <!-- To enable JMX Management -->
    <jmxConfigurator/>

    <logger name="com.app" level="debug" />
    <logger name="org.springframework.security" level="WARN" />
    <logger name="org.springframework.web.client" level="WARN" />
    <logger name="logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter" level="warn" />
    <logger name="org.apache.http" level="error"/>
    <logger name="org.springframework.integration.aws.sqs.core" level="error"/>
    <logger name="com.amazonaws.services.s3" level="error"/>
    <logger name="org.hibernate.SQL" level="error"/>
    <logger name="org.hibernate.type" level="error"/>
    <logger name="io.netty" level="error"/>

    <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <logger name="jsonLogger" additivity="false" level="DEBUG">
        <appender-ref ref="consoleAppender"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="consoleAppender"/>
    </root>

</configuration>
