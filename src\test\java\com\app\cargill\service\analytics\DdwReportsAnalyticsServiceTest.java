/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.analytics;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Business;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.UserDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.AnalyticsDataPoint;
import com.app.cargill.model.AnalyticsDataPoint.DataPointName;
import com.app.cargill.model.Sites;
import com.app.cargill.model.User;
import com.app.cargill.model.analytics.ReportDownloadMeta;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.AnalyticsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class DdwReportsAnalyticsServiceTest {

  @Mock private IUserService userService;
  @Mock private SitesRepository sitesRepository;
  @Mock private AccountsRepository accountsRepository;
  @Mock private AnalyticsRepository<ReportDownloadMeta> analyticsRepository;

  @InjectMocks private DdwReportsAnalyticsService ddwReportsAnalyticsService;

  @Test
  void loggingDataWorksWithoutException() {
    UserDocument userDocument =
        UserDocument.builder()
            .id(UUID.randomUUID())
            .countryId(Business.US)
            .userName("<EMAIL>")
            .build();
    User user = new User();
    user.setUserDocument(userDocument);
    when(userService.getLoggedUserData()).thenReturn(user);

    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setAccountId(UUID.randomUUID());
    Sites site = new Sites(siteDocument);
    when(sitesRepository.findBySiteId(any())).thenReturn(site);

    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setAccountName("TEST NAME");
    Accounts account = new Accounts(accountDocument);
    when(accountsRepository.findByAccountId(any())).thenReturn(account);

    ddwReportsAnalyticsService.logDdwReport("123", DataPointName.DDW_DETAILED_REPORT_DOWNLOAD);
    verify(analyticsRepository).save(any());
  }

  @Test
  void whenFetchDetailedReportsDataIsReturned() {
    List<AnalyticsDataPoint<HashMap<String, String>>> data = new ArrayList<>();

    AnalyticsDataPoint<HashMap<String, String>> dataPoint1 = new AnalyticsDataPoint<>();
    HashMap<String, String> meta1 = new HashMap<>();
    dataPoint1.setDataPointName(DataPointName.DDW_DETAILED_REPORT_DOWNLOAD);
    dataPoint1.setMetaData(meta1);
    dataPoint1.setCreatedDate(Date.from(Instant.now()));

    AnalyticsDataPoint<HashMap<String, String>> dataPoint2 = new AnalyticsDataPoint<>();
    HashMap<String, String> meta2 = new HashMap<>();
    dataPoint2.setDataPointName(DataPointName.DDW_DETAILED_REPORT_DOWNLOAD);
    dataPoint2.setMetaData(meta2);
    dataPoint2.setCreatedDate(Date.from(Instant.now()));

    data.add(dataPoint1);
    data.add(dataPoint2);

    when(analyticsRepository.findAllByType(any(), any(), any())).thenReturn(data);

    StepVerifier.create(
            ddwReportsAnalyticsService.fetchAllDetailedReports(Instant.now(), Instant.now()))
        .expectNextCount(1)
        .expectComplete()
        .verify();
  }

  @Test
  void whenFetchSummaryReportsDataIsReturned() {
    List<AnalyticsDataPoint<HashMap<String, String>>> data = new ArrayList<>();

    AnalyticsDataPoint<HashMap<String, String>> dataPoint1 = new AnalyticsDataPoint<>();
    HashMap<String, String> meta1 = new HashMap<>();
    dataPoint1.setDataPointName(DataPointName.DDW_SUMMARY_REPORT_DOWNLOAD);
    dataPoint1.setMetaData(meta1);
    dataPoint1.setCreatedDate(Date.from(Instant.now()));

    AnalyticsDataPoint<HashMap<String, String>> dataPoint2 = new AnalyticsDataPoint<>();
    HashMap<String, String> meta2 = new HashMap<>();
    dataPoint2.setDataPointName(DataPointName.DDW_SUMMARY_REPORT_DOWNLOAD);
    dataPoint2.setMetaData(meta2);
    dataPoint2.setCreatedDate(Date.from(Instant.now()));

    data.add(dataPoint1);
    data.add(dataPoint2);

    when(analyticsRepository.findAllByType(any(), any(), any())).thenReturn(data);

    StepVerifier.create(
            ddwReportsAnalyticsService.fetchAllSummaryReports(Instant.now(), Instant.now()))
        .expectNextCount(1)
        .expectComplete()
        .verify();
  }
}
