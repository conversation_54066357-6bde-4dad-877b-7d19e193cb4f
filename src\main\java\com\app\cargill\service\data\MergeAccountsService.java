/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.data;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Notes;
import com.app.cargill.model.Pens;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.simple.ExternalDataSourceUpdateModel;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MergeAccountsService {

  private final AccountsRepository accountsRepository;
  private final SitesRepository sitesRepository;
  private final PensRepository pensRepository;
  private final VisitsRepository visitsRepository;
  private final NotesRepository notesRepository;
  private final LiftAccountService liftAccountService;
  private final SiteMappingsRepository siteMappingsRepository;
  private final LiftSiteMappingsService liftSiteMappingsService;

  public Map<String, List<String>> transferAccountData(String source, String target) {

    Map<String, List<String>> result = initResult(source, target);

    List<Accounts> targetAcccountList = accountsRepository.findAllByGoldenRecordId(target);

    List<Accounts> sourceAcccountList = accountsRepository.findAllByGoldenRecordId(source);

    Accounts targetAccount = null;
    Accounts sourceAccount = null;
    if (!targetAcccountList.isEmpty()) {
      targetAccount = targetAcccountList.get(0);
    }
    if (!sourceAcccountList.isEmpty()) {
      sourceAccount = sourceAcccountList.get(0);
    }

    if (targetAccount != null && sourceAccount != null) {

      List<Sites> sites =
          sitesRepository.findAllByAccountId(sourceAccount.getAccountDocument().getId().toString());
      for (Sites site : sites) {
        site.getSiteDocument().setAccountId(targetAccount.getAccountDocument().getId());
        sitesRepository.save(site);
        result.get("sites").add(site.getSiteDocument().getId().toString());
      }

      List<SiteMappings> siteMappings =
          siteMappingsRepository.findAllByLabyrinthAccountId(
              sourceAccount.getAccountDocument().getId().toString());

      for (SiteMappings siteMapping : siteMappings) {
        siteMapping
            .getSiteMappingDocument()
            .setLabyrinthAccountId(targetAccount.getAccountDocument().getId());
        siteMappingsRepository.save(siteMapping);
      }

      List<Pens> pens =
          pensRepository.findByCustomerAccountIds(
              List.of(sourceAccount.getAccountDocument().getId().toString()));
      for (Pens pen : pens) {
        pen.getPenDocument().setCustomerAccountId(targetAccount.getAccountDocument().getId());
        pensRepository.save(pen);
        result.get("pens").add(pen.getPenDocument().getId().toString());
      }

      Accounts account =
          accountsRepository.findByAccountId(sourceAccount.getAccountDocument().getId().toString());
      if (account != null) {
        account.setDeleted(true);
        account.getAccountDocument().setDeleted(true);
        accountsRepository.save(account);
        result.get("accounts").add(account.getAccountDocument().getId().toString());
      }

      notesAndVisits(
          sourceAccount.getAccountDocument().getId(),
          targetAccount.getAccountDocument().getId(),
          result);
    }

    return result;
  }

  public Map<String, List<String>> transferNotesAndVisits(UUID source, UUID target) {
    Map<String, List<String>> result = initResult(source.toString(), target.toString());
    notesAndVisits(source, target, result);
    return result;
  }

  public List<Map<String, List<String>>> fixMergedAccounts(String goldenRecordId) {
    List<AccountDocument> accountsList =
        liftAccountService.getAllAccounts(List.of(String.format("id='%s'", goldenRecordId)));
    if (accountsList.isEmpty()) {
      log.debug(
          "MERGED_ACCOUNTS_PROCESS No accounts found on LIFT for goldenRecordId {}",
          StringEscapeUtils.escapeJava(goldenRecordId));
      return new ArrayList<>();
    }
    AccountDocument liftAccount = accountsList.get(0);
    // transfer db data
    List<Accounts> dbAccounts = accountsRepository.findAllByGoldenRecordId(goldenRecordId);
    if (dbAccounts.isEmpty()) {
      log.debug(
          "MERGED_ACCOUNTS_PROCESS No accounts found in DB for {}",
          StringEscapeUtils.escapeJava(goldenRecordId));
      return new ArrayList<>();
    }

    Accounts target = getTargetAccount(dbAccounts, liftAccount.getId().toString());

    if (target == null) {
      log.debug(
          "MERGED_ACCOUNTS_PROCESS target account not found in DB for {}",
          StringEscapeUtils.escapeJava(goldenRecordId));
      return new ArrayList<>();
    }

    // We want to fix DB before updating LIFT
    List<Map<String, List<String>>> transfers = transferDbAccounts(target, dbAccounts);

    if (idMismatch(liftAccount)) {
      // update lift objects
      updateLiftObjects(goldenRecordId, liftAccount, target);
    }

    return transfers;
  }

  private boolean idMismatch(AccountDocument accountDocument) {
    List<ExternalDataSource> lmDataSources =
        accountDocument.getApplicationMappings().stream()
            .filter(am -> "LM".equals(am.getSystem()))
            .toList();
    if (lmDataSources.isEmpty()) {
      log.error(
          "MERGED_ACCOUNTS_PROCESS LM Mapping Not Found for {}",
          accountDocument.getGoldenRecordId());
      throw new MergeDataException(
          String.format("LM Mapping Not Found for %s", accountDocument.getGoldenRecordId()));
    } else if (lmDataSources.size() > 1) {
      log.error(
          "MERGED_ACCOUNTS_PROCESS More than one LM Mapping found for {}",
          accountDocument.getGoldenRecordId());
      throw new MergeDataException(
          String.format(
              "More than one LM Mapping found for %s", accountDocument.getGoldenRecordId()));
    }

    return !accountDocument.getGoldenRecordId().equals(lmDataSources.get(0).getUniqueExternalKey());
  }

  private void notesAndVisits(UUID source, UUID target, Map<String, List<String>> result) {
    List<Visits> visits = visitsRepository.findAllByAccountIds(List.of(source.toString()));
    for (Visits visit : visits) {
      visit.getVisitDocument().setCustomerId(target);
      visitsRepository.save(visit);
      result.get("visits").add(visit.getVisitDocument().getId().toString());
    }

    List<Notes> notes = notesRepository.findAllByAccountId(List.of(source.toString()));
    for (Notes note : notes) {
      note.getNotesDocument().setAccountId(target);
      notesRepository.save(note);
      result.get("notes").add(note.getNotesDocument().getId().toString());
    }
  }

  private Map<String, List<String>> initResult(String source, String target) {
    Map<String, List<String>> result = new LinkedHashMap<>();
    result.put("source", List.of(source));
    result.put("target", List.of(target));
    result.put("accounts", new ArrayList<>());
    result.put("sites", new ArrayList<>());
    result.put("pens", new ArrayList<>());
    result.put("visits", new ArrayList<>());
    result.put("notes", new ArrayList<>());
    return result;
  }

  private void updateLiftObjects(
      String goldenRecordId, AccountDocument liftAccount, Accounts dbTargetAccount) {
    ExternalDataSource externalDataSource =
        liftAccount.getApplicationMappings().stream()
            .filter(am -> am.getSystem().equals("LM"))
            .findAny()
            .orElseThrow(
                () ->
                    new MergeDataException(
                        String.format(
                            "MERGED_ACCOUNTS_PROCESS LM Mapping not found %s", goldenRecordId)));
    ExternalDataSourceUpdateModel updateModel = new ExternalDataSourceUpdateModel();
    updateModel.setUniqueExternalKey(dbTargetAccount.getAccountDocument().getId().toString());
    updateModel.setIdCustom(dbTargetAccount.getAccountDocument().getId().toString());
    liftSiteMappingsService.updateMapping(externalDataSource.getId(), updateModel);

    // Dummy account update to enforce sync
    liftAccountService.updateAccountName(
        liftAccount.getGoldenRecordId(), liftAccount.getAccountName());
  }

  private List<Map<String, List<String>>> transferDbAccounts(
      Accounts targetAccount, List<Accounts> dbAccounts) {
    List<Map<String, List<String>>> transfers = new ArrayList<>();
    String targetId = targetAccount.getAccountDocument().getGoldenRecordId();

    List<Accounts> sourceAccounts =
        dbAccounts.stream()
            .filter(a -> !a.getAccountDocument().getId().toString().equals(targetId))
            .toList();

    for (Accounts sourceAccount : sourceAccounts) {
      transfers.add(
          transferAccountData(sourceAccount.getAccountDocument().getGoldenRecordId(), targetId));
    }
    return transfers;
  }

  private Accounts getTargetAccount(List<Accounts> dbAccounts, String targetId) {
    return dbAccounts.stream()
        .filter(a -> a.getAccountDocument().getId().toString().equals(targetId))
        .findAny()
        .orElse(null);
  }
}
