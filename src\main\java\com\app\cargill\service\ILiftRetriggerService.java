/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import java.util.List;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;

public interface ILiftRetriggerService {

  List<String> retriggerAccounts(
      List<String> accountIds, Locale locale, ResourceBundleMessageSource source) throws Exception;

  List<String> retriggerSites(
      List<String> siteIds, Locale locale, ResourceBundleMessageSource source);

  List<String> retriggerEvents(
      List<String> visitIds,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource);
}
