<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="format-detection" content="date=no">
	<meta name="format-detection" content="address=no">
	<meta name="format-detection" content="telephone=no">
	<meta name="x-apple-disable-message-reformatting">
	<link href="../../app/generatedTemplateResources/css/main.css" rel="stylesheet">
	<script src="../../app/generatedTemplateResources/js/chart.js"></script>
	<title>Cargill</title>
</head>
<body>

	<div class="container">
		<div class="template-header">
			<figure>
				<img src="../../app/generatedTemplateResources/images/cargill-logo.svg">
			</figure>
		</div>

		<div class="card mb-5">
			<div class="card-header pt-5">
				<h4 class="mb-2">${model.visitName!}</h4>

				<div class="row">
					<div class="content-set">
						<label>${localization.getMessage("Report.Tool.Name", [], "Tool Name", locale)}:</label>
						<h4>${model.toolName!}</h4>
					</div>
					<div class="content-set">
						<label>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}:</label>
						<h4>${model.visitDate!}</h4>
					</div>
					<div id="region" style="opacity: 0;">${region}</div>
				</div>
			</div>

			<div class="card-body">
				<div class="row">
					<div class="legend-wrap mb-2">
						<label>${model.categoryLabel!} </label>
					</div>
				</div>
				<div class="row">
					<div class="legend-wrap mb-2">
						<p class="dynamic-solid-left">${model.singleYaxisGraph.yleftLabel!}</p>
					</div>
				</div>
				<canvas id="linechart"></canvas>
			</div>

		</div>
	</div>


<script>

var style = document.querySelector('.dynamic-solid-left').style;
style.setProperty('--background', '${model.singleYaxisGraph.yleftLineColorHex!}');

	const colors = {
purple: {
default: "#1baca7",
half: "#1baca778",
quarter: "#1baca73b",
zero: "#1baca71c"
},
	indigo: {
default: "#1baca7",
quarter: "#1baca778"
}
};


const yAxis = [
    <#list model.singleYaxisGraph.yleftDataPoints as datapoint>
        ${(datapoint.y)!'NaN'}<#sep>, </#sep>
    </#list>
];

const xAxis = [
    <#list model.singleYaxisGraph.yleftDataPoints as datapoint>
        "${(datapoint.x)!}"<#sep>, </#sep>
    </#list>
];

const ctx = document.getElementById("linechart").getContext("2d");
ctx.canvas.height = 200;
var rgb = hexToRgb('${model.singleYaxisGraph.yleftLineColorHex!}');
gradient = ctx.createLinearGradient(rgb.r, rgb.g, rgb.b, 300);

gradient.addColorStop(0, 'rgba('+rgb.r+','+rgb.g+','+rgb.b+',0.35)');
gradient.addColorStop(0.35,'rgba('+rgb.r+','+rgb.g+','+rgb.b+',0.20)');
gradient.addColorStop(1, 'rgba('+rgb.r+','+rgb.g+','+rgb.b+',0.05)');

const options = {
type: "line",
data: {
labels: xAxis,
datasets: [
{
fill: true,
backgroundColor: gradient,
borderColor: '${model.singleYaxisGraph.yleftLineColorHex!}',
pointBackgroundColor: '${model.singleYaxisGraph.yleftLineColorHex!}',
pointBorderColor: '#fff',
data: yAxis,
lineTension: 0.2,
borderWidth: 1,
pointRadius: 6,
}
    ]
  },
  options: {
plugins: {
legend: {
display: false,
},
		tooltip: {
callbacks: {
title : () => null // or function () { return null; }
         },
			yAlign: 'bottom',
			backgroundColor: "#fff",
			borderColor: "rgba(0, 0, 0, 0.25)",
			borderWidth: 1,
			displayColors: false,
			bodyColor: "#1BACA7",
			bodyAlign: "center",
        },
  	},

    layout: {
		padding: {
			top:20,
			right: 15
		}
	},

    responsive: true,

    scales: {
y: {
// beginAtZero: true,
title: {
display: true,
color: '#6C7782',
text: '${model.singleYaxisGraph.yleftLabel!}',
				padding: {
bottom: 15,
}
			},

						grid: {
display: false,
},
      	},

		x: {
grid: {
display: false,
},
			color: '#6C7782',
			title: {
display: true,
color: '#6C7782',
text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
			padding: {
bottom: 15,
}
		}
		}
    },
	animation: {
	duration: 0,
	onComplete: function() {
		var chart = this;
		var ctx = chart.ctx;
		ctx.textAlign = 'center';
		ctx.textBaseline = 'bottom';
		ctx.fillStyle =  '#6C7782';
		this.data.datasets.forEach(function(dataset, i) {
						var meta = chart.getDatasetMeta(i);
						meta.data.forEach(function(bar, index) {
						var data = dataset.data[index];
						let regionText = document.getElementById("region").innerHTML;
						data = isNaN(data) ? '': new Intl.NumberFormat(regionText).format(data);
						var yIndex = bar.y - 5;
						if(data && data < 0) {
							yIndex = bar.y + 15;
						}
						ctx.fillText(data, bar.x, yIndex);
						});
				});
			}
		}
  }
};

window.onload = function () {
window.myLine = new Chart(ctx, options);
};

</script>

</body>
</html>