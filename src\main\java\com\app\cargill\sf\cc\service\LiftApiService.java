/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.SitesLiftDeletedWrapperList;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.api.model.SObjectsResponse;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.FieldsMetadata;
import com.app.cargill.sf.cc.model.LiftErrorResponse;
import com.app.cargill.sf.cc.model.LiftMetadata;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.utils.LiftErrorCode;
import com.app.cargill.sf.cc.utils.ReflectionUtils;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings({"java:S3077", "java:S2696"})
public class LiftApiService implements SalesforceApiService {
  private static final int HOUR = 1 * 60 * 60 * 1000;
  @Getter @Setter private static volatile LiftMetadata liftMetaData;
  @Getter private final LiftApiReactiveService liftApiReactiveService;
  private static final String MESSAGE = "message";
  private static final String DUPLICATEENTITYTYPE = "duplicateRuleEntityType";
  private static final String DUPLICATERESULT = "duplicateResult";

  @Override
  public AuthToken getToken() {
    try {
      log.trace("Requesting LIFT token");
      AuthToken authToken = liftApiReactiveService.getToken().block();
      log.info("LIFT token obtained Successfully");
      return authToken;
    } catch (WebClientResponseException e) {
      log.error(e.getResponseBodyAsString());
      throw e;
    }
  }

  public AccessTokenAndApiPathDto getTokenAndApiPath() {
    AuthToken authToken = null;
    String apiPath = null;
    if (getLiftMetaData() != null
        && getLiftMetaData().getAuthToken() != null
        && getLiftMetaData().getApiPath() != null) {
      authToken = getLiftMetaData().getAuthToken();
      apiPath = getLiftMetaData().getApiPath();
    }
    if (getLiftMetaData() == null || getLiftMetaData().getAuthToken().isExpired()) {
      authToken = getToken();
      apiPath = getLatestApiPath(authToken);
      liftMetaData = LiftMetadata.builder().authToken(authToken).apiPath(apiPath).build();
    }

    return AccessTokenAndApiPathDto.builder().apiPath(apiPath).authToken(authToken).build();
  }

  public <T extends Serializable> SalesforceRecordsResponse<T> getRecordsQuery(
      AuthToken authToken,
      String apiPath,
      String query,
      ParameterizedTypeReference<SalesforceRecordsResponse<T>> typeReference) {
    log.trace("Making LIFT query request: {}", query);
    SalesforceRecordsResponse<T> recordsResponse =
        liftApiReactiveService.getRecordsQuery(authToken, apiPath, query, typeReference).block();
    log.trace(
        "Received {} recorde from LIFT",
        recordsResponse != null ? recordsResponse.getTotalSize() : "NULL");
    return recordsResponse;
  }

  public <T extends Serializable> SalesforceRecordsResponse<T> getRecordsPage(
      AuthToken authToken,
      String queryPath,
      ParameterizedTypeReference<SalesforceRecordsResponse<T>> typeReference) {
    log.trace("Getting LIFT records page: {}", queryPath);
    SalesforceRecordsResponse<T> recordsResponse =
        liftApiReactiveService.getRecordsPage(authToken, queryPath, typeReference).block();
    log.trace(
        "Received {} records from LIFT",
        recordsResponse != null ? recordsResponse.getTotalSize() : "NULL");
    return recordsResponse;
  }

  public SitesLiftDeletedWrapperList getRecordsWithQueryParam(
      AuthToken authToken,
      String queryPath,
      Class<SitesLiftDeletedWrapperList> clazz,
      Instant start,
      Instant end) {
    SitesLiftDeletedWrapperList recordsResponse =
        liftApiReactiveService
            .getRecordsWithQueryParam(authToken, queryPath, clazz, start, end)
            .block();
    return recordsResponse;
  }

  public <T extends Serializable> void updateRecord(
      AuthToken authToken, T obj, ParameterizedTypeReference<T> typeReference, String uri) {
    try {
      liftApiReactiveService.updateRecord(authToken, obj, typeReference, uri).block();
    } catch (WebClientResponseException e) {
      ObjectMapper objectMapper = new ObjectMapper();
      try {
        List<LiftErrorResponse> liftErrorResponse =
            objectMapper.readValue(e.getResponseBodyAsString(), new TypeReference<>() {});
        if (liftErrorResponse.get(0).getErrorCode().equals(LiftErrorCode.ENTITY_IS_DELETED)) {
          throw new LiftObjectDeletedException(uri);
        }
        log.error("LIFT_UPDATE_ERROR_RESPONSE {}", e.getResponseBodyAsString());
        log.error(
            "LIFT_UPDATE_ERROR_RESPONSE_DEBUG {} {} {}",
            e.getResponseBodyAsString(),
            typeReference,
            obj);
      } catch (JsonProcessingException ex) {
        log.warn(
            "CANNOT_DESERIALIZE_LIFT_ERROR_RESPONSE {} {}",
            ex.getMessage(),
            e.getResponseBodyAsString());
      }
      throw e;
    }
  }

  public <T extends Serializable> CreateRecordResponse createRecord(
      AuthToken authToken, T obj, ParameterizedTypeReference<T> typeReference, String uri)
      throws JsonProcessingException, CustomDEExceptions {
    try {
      CreateRecordResponse result =
          liftApiReactiveService.createRecord(authToken, obj, typeReference, uri).block();
      log.info("Result: {}", result);
      return result;
    } catch (WebClientResponseException e) {
      log.error("LIFT_CREATE_ERROR_RESPONSE {}", e.getResponseBodyAsString());
      log.error(
          "LIFT_CREATE_ERROR_RESPONSE_DEBUG {} {}", e.getResponseBodyAsString(), typeReference);
      PayloadValidationDto payloadValidationDto = createResponse(e);
      if (payloadValidationDto == null) {
        throw e;
      }
      log.error(e.getResponseBodyAsString());
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()));
    }
  }

  public PayloadValidationDto createResponse(WebClientResponseException e) {
    try {
      JSONArray jsonArray = JsonUtils.convertStringToJsonArray(e.getResponseBodyAsString());
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();

      if (!jsonArray.isEmpty()) {
        JSONObject jsonObj = jsonArray.getJSONObject(0).getJSONObject(DUPLICATERESULT);
        if (jsonObj == null) {
          return null;
        }
        LiftResponseEntityDto liftResponseEntityDto =
            LiftResponseEntityDto.builder()
                .message(
                    jsonArray.getJSONObject(0).get(MESSAGE) != null
                        ? jsonArray.getJSONObject(0).get(MESSAGE).toString()
                        : null)
                .status(ResponseStatus.FAILED)
                .entity(
                    jsonObj.get(DUPLICATEENTITYTYPE) != null
                        ? jsonObj.get(DUPLICATEENTITYTYPE).toString()
                        : null)
                .build();
        payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
        return payloadValidationDto;
      }
      return null;
    } catch (Exception ex) {
      log.error(e.getLocalizedMessage());
      return null;
    }
  }

  public List<VersionObject> getApiVersions() {
    AccessTokenAndApiPathDto tokenAndApiPath = getTokenAndApiPath();
    return getApiVersions(tokenAndApiPath.getAuthToken());
  }

  public VersionObject getLatestApiVersion() {
    AccessTokenAndApiPathDto tokenAndApiPath = getTokenAndApiPath();
    return getLatestApiVersion(tokenAndApiPath.getAuthToken());
  }

  public List<VersionObject> getApiVersions(AuthToken authToken) {
    List<VersionObject> versions =
        liftApiReactiveService.getApiVersions(authToken).collectList().block();
    log.trace("Fetched Salesforce API versions");
    return versions;
  }

  public VersionObject getLatestApiVersion(AuthToken authToken) {
    return liftApiReactiveService.getLatestApiVersion(authToken).block();
  }

  public String getLatestApiPath(AuthToken authToken) {
    return getLatestApiVersion(authToken).getUrl();
  }

  public SObjectsResponse getSObjectsDefinitions(AuthToken authToken) {
    SObjectsResponse sObjectsResponse =
        liftApiReactiveService.getSObjectsDefinitions(authToken).block();
    log.trace("Fetched Salesforce sobject(s) definitions");
    return sObjectsResponse;
  }

  public SObjectsResponse getSObjectsDefinitions() {
    AccessTokenAndApiPathDto tokenAndApiPath = getTokenAndApiPath();
    return getSObjectsDefinitions(tokenAndApiPath.getAuthToken());
  }

  @Scheduled(fixedRate = HOUR)
  @EventListener(ApplicationReadyEvent.class)
  public LiftMetadata init() throws IllegalAccessException {
    try {
      AuthToken authToken = getToken();
      String apiPath = getLatestApiPath(authToken);
      liftMetaData = LiftMetadata.builder().authToken(authToken).apiPath(apiPath).build();

      // 1 - for accounts
      Mono<FieldsMetadata> accountMetadata =
          liftApiReactiveService.getRecords(
              authToken, apiPath.concat("/sobjects/Account/describe"), FieldsMetadata.class);
      accountMetadata.doOnNext(resp -> liftMetaData.setFieldsMetadataForAccount(resp)).block();

      // 2 - for contacts
      Mono<FieldsMetadata> contactMetadata =
          liftApiReactiveService.getRecords(
              authToken, apiPath.concat("/sobjects/contact/describe"), FieldsMetadata.class);
      contactMetadata.doOnNext(resp -> liftMetaData.setFieldsMetadataForContact(resp)).block();

      // 3 - for site
      Mono<FieldsMetadata> siteMetadata =
          liftApiReactiveService.getRecords(
              authToken, apiPath.concat("/sobjects/Species__c/describe"), FieldsMetadata.class);
      siteMetadata.doOnNext(resp -> liftMetaData.setFieldsMetadataForSite(resp)).block();

      // 4 - for event
      Mono<FieldsMetadata> eventMetadata =
          liftApiReactiveService.getRecords(
              authToken, apiPath.concat("/sobjects/Event/describe"), FieldsMetadata.class);
      eventMetadata.doOnNext(resp -> liftMetaData.setFieldsMetadataForEvent(resp)).block();

      // 5 - for siteMapping
      Mono<FieldsMetadata> siteMappingsMetadata =
          liftApiReactiveService.getRecords(
              authToken,
              apiPath.concat("/sobjects/External_Data_Source__c/describe"),
              FieldsMetadata.class);
      siteMappingsMetadata
          .doOnNext(resp -> liftMetaData.setFieldsMetadataForSiteMapping(resp))
          .block();
    } catch (Exception e) {
      log.error(e.getLocalizedMessage());
    }
    return liftMetaData;
  }

  public PayloadValidationDto validate(
      Object obj, LiftEntityName entityName, Locale locale, ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException {
    PayloadValidationDto response = PayloadValidationDto.builder().build();
    return switch (entityName) {
      case ACCOUNT -> {
        ReflectionUtils.validate(
            obj, response, liftMetaData.getFieldsMetadataForAccount(), locale, source);
        yield response;
      }
      case CONTACT -> {
        ReflectionUtils.validate(
            obj, response, liftMetaData.getFieldsMetadataForContact(), locale, source);
        yield response;
      }
      case SITE -> {
        ReflectionUtils.validate(
            obj, response, liftMetaData.getFieldsMetadataForSite(), locale, source);
        yield response;
      }
      case SITE_MAPPING -> {
        ReflectionUtils.validate(
            obj, response, liftMetaData.getFieldsMetadataForSiteMapping(), locale, source);
        yield response;
      }
      case EVENT -> {
        ReflectionUtils.validate(
            obj, response, liftMetaData.getFieldsMetadataForEvent(), locale, source);
        yield response;
      }
    };
  }
}
