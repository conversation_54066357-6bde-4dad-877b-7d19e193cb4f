/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldEvaluationToolOutputToolItemDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Double evaluationDays;

  private Double averageMilkProduction;

  private Double averageMilkProductionAnimalsTank;

  private Double averageMilkFatPer;

  private Double milkFatYield;

  private Double averageMilkProteinPer;

  private Double milkProteinYield;

  private Double milkFatProteinYield;

  private Double componentEfficiency;

  private Double feedEfficiency;

  private Double mun;

  private Double averageSCC;

  private Double averageBacteriaCount;

  @Builder.Default private Boolean isOutputUpdated = false;

  private Double milkSolidNonFat;

  private Double averageMilkSolidNonFat;
}
