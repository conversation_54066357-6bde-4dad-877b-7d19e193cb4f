/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.EmptyJsonBody;
import com.app.cargill.dto.ResponseEntityDto;
import java.util.Locale;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class DiagnosticsControllerTest {

  @Mock private MessageSource messageSource;

  @InjectMocks private DiagnosticsController controller;

  @Test
  void heartbeat() {
    ResponseEntity<ResponseEntityDto<EmptyJsonBody>> result = controller.heartbeat();
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatusCode());
    assertNotNull(Objects.requireNonNull(result.getBody()).getMessage());
  }

  @Test
  void getLocaleMessage() {
    when(messageSource.getMessage(any(), any(), any(Locale.class))).thenReturn("result message");
    ResponseEntity<ResponseEntityDto<EmptyJsonBody>> result = controller.getLocaleMessage("");
    assertNotNull(result);
    assertEquals(HttpStatus.OK, result.getStatusCode());
    assertNotNull(Objects.requireNonNull(result.getBody()).getMessage());
  }
}
