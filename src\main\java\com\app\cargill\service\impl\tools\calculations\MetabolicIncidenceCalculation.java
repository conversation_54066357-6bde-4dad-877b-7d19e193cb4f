/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.MetabolicTypeKeys;
import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.MetabolicIncidenceOutputToolItem;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MetabolicIncidenceToolItem;
import java.util.Objects;

public class MetabolicIncidenceCalculation {

  private static final String ILLEGAL_ARGUMENT_EXCEPTION_STATEMENT =
      "The Key %s was not found for the MetabolicIncidenceCalculation method";

  public MetabolicIncidenceTool calculateFields(MetabolicIncidenceTool tool) {

    if (tool != null
        && tool.getVisitMetabolicIncidenceData() != null
        && tool.getVisitMetabolicIncidenceData().getOutputs() != null) {

      MetabolicIncidenceToolItem item = tool.getVisitMetabolicIncidenceData();
      MetabolicIncidenceOutputToolItem toolOutput = item.getOutputs();
      toolOutput.retainedPlacentaIncidencePercent =
          retainedPlacentaIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.metritisIncidencePercent =
          metritisIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.displacedAbomasumIncidencePercent =
          displacedAbomasumIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.ketosisIncidencePercent =
          ketosisIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.milkFeverIncidencePercent =
          milkFeverIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.dystociaIncidencePercent =
          dystociaIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.deathLossIncidencePercent =
          deathLossIncidencePercent(tool.getVisitMetabolicIncidenceData());
      toolOutput.retainedPlacentaIncidenceDifference =
          retainedPlacentaIncidenceDifference(toolOutput);
      toolOutput.metritisIncidenceDifference = metritisIncidenceDifference(toolOutput);
      toolOutput.displacedAbomasumIncidenceDifference =
          displacedAbomasumIncidenceDifference(toolOutput);
      toolOutput.ketosisIncidenceDifference = ketosisIncidenceDifference(toolOutput);
      toolOutput.milkFeverIncidenceDifference = milkFeverIncidenceDifference(toolOutput);
      toolOutput.dystociaIncidenceDifference = dystociaIncidenceDifference(toolOutput);
      toolOutput.deathLossIncidenceDifference = deathLossIncidenceDifference(toolOutput);
      toolOutput.retainedPlacentaMilkLoss = retainedPlacentaMilkLoss(item, toolOutput);
      toolOutput.metritisMilkLoss = metritisMilkLoss(item, toolOutput);
      toolOutput.displacedAbomasumMilkLoss = displacedAbomasumMilkLoss(item, toolOutput);
      toolOutput.ketosisMilkLoss = ketosisMilkLoss(item, toolOutput);
      toolOutput.milkFeverMilkLoss = milkFeverMilkLoss(item, toolOutput);
      toolOutput.dystociaMilkLoss = dystociaMilkLoss(item, toolOutput);
      toolOutput.metritisIncreasedDaysOpen = metritisIncreasedDaysOpen(item, toolOutput);
      toolOutput.displacedAbomasumIncreasedDaysOpen =
          displacedAbomasumIncreasedDaysOpen(item, toolOutput);
      toolOutput.ketosisIncreasedDaysOpen = ketosisIncreasedDaysOpen(item, toolOutput);
      toolOutput.milkFeverIncreasedDaysOpen = milkFeverIncreasedDaysOpen(item, toolOutput);
      toolOutput.retainedPlacentaIncreasedDaysOpen =
          retainedPlacentaIncreasedDaysOpen(item, toolOutput);
      toolOutput.dystociaIncreasedDaysOpen = dystociaIncreasedDaysOpen(item, toolOutput);
      toolOutput.metritisTreatmentCost = metritisTreatmentCost(item, toolOutput);
      toolOutput.displacedAbomasumTreatmentCost = displacedAbomasumTreatmentCost(item, toolOutput);
      toolOutput.ketosisTreatmentCost = ketosisTreatmentCost(item, toolOutput);
      toolOutput.milkFeverTreatmentCost = milkFeverTreatmentCost(item, toolOutput);
      toolOutput.retainedPlacentaTreatmentCost = retainedPlacentaTreatmentCost(item, toolOutput);
      toolOutput.dystociaTreatmentCost = dystociaTreatmentCost(item, toolOutput);
      toolOutput.milkLossTotalLosses = milkLossTotalLosses(toolOutput);
      toolOutput.increasedDaysOpenTotalLosses = increasedDaysOpenTotalLosses(toolOutput);
      toolOutput.treatmentCostTotalLosses = treatmentCostTotalLosses(toolOutput);
      toolOutput.retainedPlacentaTotalCost = retainedPlacentaTotalCost(toolOutput);
      toolOutput.metritisTotalCost = metritisTotalCost(toolOutput);
      toolOutput.displacedAbomasumTotalCost = displacedAbomasumTotalCost(toolOutput);
      toolOutput.ketosisTotalCost = ketosisTotalCost(toolOutput);
      toolOutput.milkFeverTotalCost = milkFeverTotalCost(toolOutput);
      toolOutput.dystociaTotalCost = dystociaTotalCost(toolOutput);
      toolOutput.deathLossTotalCost = deathLossTotalCost(item, toolOutput);
      toolOutput.totalCost = totalCost(toolOutput);
      toolOutput.retainedPlacentaCostPerCow = retainedPlacentaCostPerCow(item, toolOutput);
      toolOutput.metritisCostPerCow = metritisCostPerCow(item, toolOutput);
      toolOutput.displacedAbomasumCostPerCow = displacedAbomasumCostPerCow(item, toolOutput);
      toolOutput.ketosisCostPerCow = ketosisCostPerCow(item, toolOutput);
      toolOutput.milkFeverCostPerCow = milkFeverCostPerCow(item, toolOutput);
      toolOutput.dystociaCostPerCow = dystociaCostPerCow(item, toolOutput);
      toolOutput.deathLossCostPerCow = deathLossCostPerCow(item, toolOutput);
      toolOutput.totalCostPerCow = totalCostPerCow(toolOutput);
      toolOutput.toolStatus = toolStatus(item);

      tool.getVisitMetabolicIncidenceData().setOutputs(toolOutput);
    }

    return tool;
  }

  private Double retainedPlacentaIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.RetainedPlacenta);
  }

  private Double metritisIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.Metritis);
  }

  private Double displacedAbomasumIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.DisplacedAbomasum);
  }

  private Double ketosisIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.Ketosis);
  }

  private Double milkFeverIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.MilkFever);
  }

  private Double dystociaIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.Dystocia);
  }

  private Double deathLossIncidencePercent(MetabolicIncidenceToolItem item) {
    return metabolicIncidenceCalculationKey(item, MetabolicTypeKeys.DeathLoss);
  }

  private Double retainedPlacentaIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.RetainedPlacenta);
  }

  private Double metritisIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.Metritis);
  }

  private Double displacedAbomasumIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.DisplacedAbomasum);
  }

  private Double ketosisIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.Ketosis);
  }

  private Double milkFeverIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.MilkFever);
  }

  private Double dystociaIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.Dystocia);
  }

  private Double deathLossIncidenceDifference(MetabolicIncidenceOutputToolItem item) {
    return metabolicIncidenceDifferenceCalculation(item, MetabolicTypeKeys.DeathLoss);
  }

  private Integer retainedPlacentaMilkLoss(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return milkLossCalculation(item, outputItem, MetabolicTypeKeys.RetainedPlacenta);
  }

  private Integer metritisMilkLoss(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return milkLossCalculation(item, outputItem, MetabolicTypeKeys.Metritis);
  }

  private Integer displacedAbomasumMilkLoss(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return milkLossCalculation(item, outputItem, MetabolicTypeKeys.DisplacedAbomasum);
  }

  private Integer ketosisMilkLoss(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return milkLossCalculation(item, outputItem, MetabolicTypeKeys.Ketosis);
  }

  private Integer milkFeverMilkLoss(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return milkLossCalculation(item, outputItem, MetabolicTypeKeys.MilkFever);
  }

  private Integer dystociaMilkLoss(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return milkLossCalculation(item, outputItem, MetabolicTypeKeys.Dystocia);
  }

  private Integer metritisIncreasedDaysOpen(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return increasedDaysOpenCalculation(item, outputItem, MetabolicTypeKeys.Metritis);
  }

  private Integer displacedAbomasumIncreasedDaysOpen(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return increasedDaysOpenCalculation(item, outputItem, MetabolicTypeKeys.DisplacedAbomasum);
  }

  private Integer ketosisIncreasedDaysOpen(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return increasedDaysOpenCalculation(item, outputItem, MetabolicTypeKeys.Ketosis);
  }

  private Integer milkFeverIncreasedDaysOpen(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return increasedDaysOpenCalculation(item, outputItem, MetabolicTypeKeys.MilkFever);
  }

  private Integer retainedPlacentaIncreasedDaysOpen(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return increasedDaysOpenCalculation(item, outputItem, MetabolicTypeKeys.RetainedPlacenta);
  }

  private Integer dystociaIncreasedDaysOpen(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return increasedDaysOpenCalculation(item, outputItem, MetabolicTypeKeys.Dystocia);
  }

  private Integer metritisTreatmentCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return treatmentCostCalculation(item, outputItem, MetabolicTypeKeys.Metritis);
  }

  private Integer displacedAbomasumTreatmentCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return treatmentCostCalculation(item, outputItem, MetabolicTypeKeys.DisplacedAbomasum);
  }

  private Integer ketosisTreatmentCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return treatmentCostCalculation(item, outputItem, MetabolicTypeKeys.Ketosis);
  }

  private Integer milkFeverTreatmentCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return treatmentCostCalculation(item, outputItem, MetabolicTypeKeys.MilkFever);
  }

  private Integer retainedPlacentaTreatmentCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return treatmentCostCalculation(item, outputItem, MetabolicTypeKeys.RetainedPlacenta);
  }

  private Integer dystociaTreatmentCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    return treatmentCostCalculation(item, outputItem, MetabolicTypeKeys.Dystocia);
  }

  private Integer milkLossTotalLosses(MetabolicIncidenceOutputToolItem item) {
    int valueToReturn =
        (Objects.requireNonNullElse(item.milkFeverMilkLoss, -1) < 0
                ? 0
                : item.getMilkFeverMilkLoss())
            + (Objects.requireNonNullElse(item.ketosisMilkLoss, -1) < 0 ? 0 : item.ketosisMilkLoss)
            + (Objects.requireNonNullElse(item.metritisMilkLoss, -1) < 0
                ? 0
                : item.metritisMilkLoss)
            + (Objects.requireNonNullElse(item.dystociaMilkLoss, -1) < 0
                ? 0
                : item.dystociaMilkLoss)
            + (Objects.requireNonNullElse(item.retainedPlacentaMilkLoss, -1) < 0
                ? 0
                : item.retainedPlacentaMilkLoss)
            + (Objects.requireNonNullElse(item.displacedAbomasumMilkLoss, -1) < 0
                ? 0
                : item.displacedAbomasumMilkLoss);

    if (valueToReturn == 0) return null;

    return valueToReturn;
  }

  private Integer increasedDaysOpenTotalLosses(MetabolicIncidenceOutputToolItem item) {
    int valueToReturn =
        (Objects.requireNonNullElse(item.milkFeverIncreasedDaysOpen, -1) < 0
                ? 0
                : item.milkFeverIncreasedDaysOpen)
            + (Objects.requireNonNullElse(item.ketosisIncreasedDaysOpen, -1) < 0
                ? 0
                : item.ketosisIncreasedDaysOpen)
            + (Objects.requireNonNullElse(item.metritisIncreasedDaysOpen, -1) < 0
                ? 0
                : item.metritisIncreasedDaysOpen)
            + (Objects.requireNonNullElse(item.dystociaIncreasedDaysOpen, -1) < 0
                ? 0
                : item.dystociaIncreasedDaysOpen)
            + (Objects.requireNonNullElse(item.retainedPlacentaIncreasedDaysOpen, -1) < 0
                ? 0
                : item.retainedPlacentaIncreasedDaysOpen)
            + (Objects.requireNonNullElse(item.displacedAbomasumIncreasedDaysOpen, -1) < 0
                ? 0
                : item.displacedAbomasumIncreasedDaysOpen);

    if (valueToReturn == 0) return null;

    return valueToReturn;
  }

  private Integer treatmentCostTotalLosses(MetabolicIncidenceOutputToolItem item) {
    int valueToReturn =
        (Objects.requireNonNullElse(item.milkFeverTreatmentCost, -1) < 0
                ? 0
                : item.milkFeverTreatmentCost)
            + (Objects.requireNonNullElse(item.ketosisTreatmentCost, -1) < 0
                ? 0
                : item.ketosisTreatmentCost)
            + (Objects.requireNonNullElse(item.metritisTreatmentCost, -1) < 0
                ? 0
                : item.metritisTreatmentCost)
            + (Objects.requireNonNullElse(item.dystociaTreatmentCost, -1) < 0
                ? 0
                : item.dystociaTreatmentCost)
            + (Objects.requireNonNullElse(item.retainedPlacentaTreatmentCost, -1) < 0
                ? 0
                : item.retainedPlacentaTreatmentCost)
            + (Objects.requireNonNullElse(item.displacedAbomasumTreatmentCost, -1) < 0
                ? 0
                : item.displacedAbomasumTreatmentCost);

    if (valueToReturn == 0) return null;

    return valueToReturn;
  }

  private Integer retainedPlacentaTotalCost(MetabolicIncidenceOutputToolItem item) {

    Integer retainedPlacentaMilkLoss = item.retainedPlacentaMilkLoss;
    Integer retainedPlacentaIncreasedDaysOpen = item.retainedPlacentaIncreasedDaysOpen;
    Integer retainedPlacentaTreatmentCost = item.retainedPlacentaTreatmentCost;

    if (retainedPlacentaMilkLoss == null && retainedPlacentaIncreasedDaysOpen == null) return null;

    if (retainedPlacentaMilkLoss == null) retainedPlacentaMilkLoss = 0;
    if (retainedPlacentaIncreasedDaysOpen == null) retainedPlacentaIncreasedDaysOpen = 0;
    if (retainedPlacentaTreatmentCost == null) retainedPlacentaTreatmentCost = 0;

    return retainedPlacentaMilkLoss
        + retainedPlacentaIncreasedDaysOpen
        + retainedPlacentaTreatmentCost;
  }

  private Integer metritisTotalCost(MetabolicIncidenceOutputToolItem item) {
    Integer metritisMilkLoss = item.metritisMilkLoss;
    Integer metritisIncreasedDaysOpen = item.metritisIncreasedDaysOpen;
    Integer metritisTreatmentCost = item.metritisTreatmentCost;

    if (metritisMilkLoss == null && metritisIncreasedDaysOpen == null) return null;

    if (metritisMilkLoss == null) metritisMilkLoss = 0;
    if (metritisIncreasedDaysOpen == null) metritisIncreasedDaysOpen = 0;
    if (metritisTreatmentCost == null) metritisTreatmentCost = 0;

    return metritisMilkLoss + metritisIncreasedDaysOpen + metritisTreatmentCost;
  }

  private Integer displacedAbomasumTotalCost(MetabolicIncidenceOutputToolItem item) {
    Integer displacedAbomasumMilkLoss = item.displacedAbomasumMilkLoss;
    Integer displacedAbomasumIncreasedDaysOpen = item.displacedAbomasumIncreasedDaysOpen;
    Integer displacedAbomasumTreatmentCost = item.displacedAbomasumTreatmentCost;

    if (displacedAbomasumMilkLoss == null && displacedAbomasumIncreasedDaysOpen == null)
      return null;

    if (displacedAbomasumMilkLoss == null) displacedAbomasumMilkLoss = 0;
    if (displacedAbomasumIncreasedDaysOpen == null) displacedAbomasumIncreasedDaysOpen = 0;
    if (displacedAbomasumTreatmentCost == null) displacedAbomasumTreatmentCost = 0;

    return displacedAbomasumMilkLoss
        + displacedAbomasumIncreasedDaysOpen
        + displacedAbomasumTreatmentCost;
  }

  private Integer ketosisTotalCost(MetabolicIncidenceOutputToolItem item) {
    Integer ketosisMilkLoss = item.ketosisMilkLoss;
    Integer ketosisIncreasedDaysOpen = item.ketosisIncreasedDaysOpen;
    Integer ketosisTreatmentCost = item.ketosisTreatmentCost;

    if (ketosisMilkLoss == null && ketosisIncreasedDaysOpen == null) return null;

    if (ketosisMilkLoss == null) ketosisMilkLoss = 0;
    if (ketosisIncreasedDaysOpen == null) ketosisIncreasedDaysOpen = 0;
    if (ketosisTreatmentCost == null) ketosisTreatmentCost = 0;

    return ketosisMilkLoss + ketosisIncreasedDaysOpen + ketosisTreatmentCost;
  }

  private Integer milkFeverTotalCost(MetabolicIncidenceOutputToolItem item) {

    Integer milkFeverMilkLoss = item.milkFeverMilkLoss;
    Integer milkFeverIncreasedDaysOpen = item.milkFeverIncreasedDaysOpen;
    Integer milkFeverTreatmentCost = item.milkFeverTreatmentCost;

    if (milkFeverMilkLoss == null && milkFeverIncreasedDaysOpen == null) return null;

    if (milkFeverMilkLoss == null) milkFeverMilkLoss = 0;
    if (milkFeverIncreasedDaysOpen == null) milkFeverIncreasedDaysOpen = 0;
    if (milkFeverTreatmentCost == null) milkFeverTreatmentCost = 0;

    return milkFeverMilkLoss + milkFeverIncreasedDaysOpen + milkFeverTreatmentCost;
  }

  private Integer dystociaTotalCost(MetabolicIncidenceOutputToolItem item) {
    Integer dystociaMilkLoss = item.dystociaMilkLoss;
    Integer dystociaIncreasedDaysOpen = item.dystociaIncreasedDaysOpen;
    Integer dystociaTreatmentCost = item.dystociaTreatmentCost;

    if (dystociaMilkLoss == null && dystociaIncreasedDaysOpen == null) return null;
    if (dystociaMilkLoss == null) dystociaMilkLoss = 0;
    if (dystociaIncreasedDaysOpen == null) dystociaIncreasedDaysOpen = 0;
    if (dystociaTreatmentCost == null) dystociaTreatmentCost = 0;

    return dystociaMilkLoss + dystociaIncreasedDaysOpen + dystociaTreatmentCost;
  }

  private Integer deathLossTotalCost(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double valueForRounding = null;

    if (!(item.getTotalFreshCowsPerYear() == null || item.getReplacementCowCost() == null))
      valueForRounding =
          ((item.getTotalFreshCowsPerYear()
                      * Objects.requireNonNullElse(outputItem.deathLossIncidenceDifference, 0.0))
                  / 100)
              * item.getReplacementCowCost();

    return valueForRounding != null ? (int) Math.round(valueForRounding) : null;
  }

  private Integer totalCost(MetabolicIncidenceOutputToolItem item) {
    Integer retainedPlacentaTotalCost = item.retainedPlacentaTotalCost;
    Integer metritisTotalCost = item.metritisTotalCost;
    Integer displacedAbomasumTotalCost = item.displacedAbomasumTotalCost;
    Integer ketosisTotalCost = item.ketosisTotalCost;
    Integer milkFeverTotalCost = item.milkFeverTotalCost;
    Integer dystociaTotalCost = item.dystociaTotalCost;
    Integer deathLossTotalCost = item.deathLossTotalCost;

    int totalCost =
        (retainedPlacentaTotalCost == null || retainedPlacentaTotalCost < 0
                ? 0
                : retainedPlacentaTotalCost)
            + (metritisTotalCost == null || metritisTotalCost < 0 ? 0 : metritisTotalCost)
            + (displacedAbomasumTotalCost == null || displacedAbomasumTotalCost < 0
                ? 0
                : displacedAbomasumTotalCost)
            + (ketosisTotalCost == null || ketosisTotalCost < 0 ? 0 : ketosisTotalCost)
            + (milkFeverTotalCost == null || milkFeverTotalCost < 0 ? 0 : milkFeverTotalCost)
            + (dystociaTotalCost == null || dystociaTotalCost < 0 ? 0 : dystociaTotalCost)
            + (deathLossTotalCost == null || deathLossTotalCost < 0 ? 0 : deathLossTotalCost);

    if (totalCost == 0) return null;

    return totalCost;
  }

  private Double retainedPlacentaCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.RetainedPlacenta);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double metritisCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.Metritis);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double displacedAbomasumCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.DisplacedAbomasum);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double ketosisCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.Ketosis);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double milkFeverCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.MilkFever);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double dystociaCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.Dystocia);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double deathLossCostPerCow(
      MetabolicIncidenceToolItem item, MetabolicIncidenceOutputToolItem outputItem) {
    Double costPerCowCalculation =
        costPerCowCalculation(item, outputItem, MetabolicTypeKeys.DeathLoss);
    return costPerCowCalculation != null ? roundAvoid(costPerCowCalculation, 2) : 0.0;
  }

  private Double totalCostPerCow(MetabolicIncidenceOutputToolItem item) {

    Double retainedPlacentaCostPerCow =
        Objects.requireNonNullElse(item.retainedPlacentaCostPerCow, -1.0);
    Double metritisCostPerCow = Objects.requireNonNullElse(item.metritisCostPerCow, -1.0);
    Double displacedAbomasumCostPerCow =
        Objects.requireNonNullElse(item.displacedAbomasumCostPerCow, -1.0);
    Double ketosisCostPerCow = Objects.requireNonNullElse(item.ketosisCostPerCow, -1.0);
    Double milkFeverCostPerCow = Objects.requireNonNullElse(item.milkFeverCostPerCow, -1.0);
    Double dystociaCostPerCow = Objects.requireNonNullElse(item.dystociaCostPerCow, -1.0);
    Double deathLossCostPerCow = Objects.requireNonNullElse(item.deathLossCostPerCow, -1.0);
    double costValue =
        (metritisCostPerCow < 0 ? 0 : metritisCostPerCow)
            + (retainedPlacentaCostPerCow < 0 ? 0 : retainedPlacentaCostPerCow)
            + (displacedAbomasumCostPerCow < 0 ? 0 : displacedAbomasumCostPerCow)
            + (ketosisCostPerCow < 0 ? 0 : ketosisCostPerCow)
            + (milkFeverCostPerCow < 0 ? 0 : milkFeverCostPerCow)
            + (dystociaCostPerCow < 0 ? 0 : dystociaCostPerCow)
            + (deathLossCostPerCow < 0 ? 0 : deathLossCostPerCow);

    if (costValue == 0) return null;

    return costValue;
  }

  public ToolStatuses toolStatus(MetabolicIncidenceToolItem item) {
    if (herdHasValidInputs(item) && incidencesHasValidInputs(item)) return ToolStatuses.Completed;
    return ToolStatuses.InProgress;
  }

  private Double metabolicIncidenceCalculationKey(
      MetabolicIncidenceToolItem item, MetabolicTypeKeys key) {
    if (item.getTotalFreshCowsForEvaluation() == null || item.getTotalFreshCowsForEvaluation() == 0)
      return 0.0;

    Integer incidenceValue =
        switch (key) {
          case DeathLoss -> item.getDeathLossIncidence();
          case DisplacedAbomasum -> item.getDisplacedAbomasumIncidence();
          case Dystocia -> item.getDystociaIncidence();
          case Ketosis -> item.getKetosisIncidence();
          case Metritis -> item.getMetritisIncidence();
          case MilkFever -> item.getMilkFeverIncidence();
          case RetainedPlacenta -> item.getRetainedPlacentaIncidence();
        };

    return roundAvoid(
        ((double) Objects.requireNonNullElse(incidenceValue, 0)
                / item.getTotalFreshCowsForEvaluation())
            * 100,
        1);
  }

  private Double metabolicIncidenceDifferenceCalculation(
      MetabolicIncidenceOutputToolItem item, MetabolicTypeKeys key) {
    double incidence;
    Double goal =
        switch (key) {
          case DeathLoss -> {
            incidence = item.deathLossIncidencePercent;
            yield item.deathLossGoal;
          }
          case DisplacedAbomasum -> {
            incidence = item.displacedAbomasumIncidencePercent;
            yield item.displacedAbomasumGoal;
          }
          case Dystocia -> {
            incidence = item.dystociaIncidencePercent;
            yield item.dystociaGoal;
          }
          case Ketosis -> {
            incidence = item.ketosisIncidencePercent;
            yield item.ketosisGoal;
          }
          case Metritis -> {
            incidence = item.metritisIncidencePercent;
            yield item.metritisGoal;
          }
          case MilkFever -> {
            incidence = item.milkFeverIncidencePercent;
            yield item.milkFeverGoal;
          }
          case RetainedPlacenta -> {
            incidence = item.retainedPlacentaIncidencePercent;
            yield item.retainedPlacentaGoal;
          }
          default -> throw new IllegalArgumentException(
              String.format(ILLEGAL_ARGUMENT_EXCEPTION_STATEMENT, key));
        };

    double goalValue = goal == null ? 0.0 : goal;

    return roundAvoid(incidence - goalValue, 1);
  }

  private Integer milkLossCalculation(
      MetabolicIncidenceToolItem item,
      MetabolicIncidenceOutputToolItem toolItem,
      MetabolicTypeKeys key) {
    Double percentDifference = null;
    Double milkLossWeight = null;

    if (!(item.getMilkPrice() == null || item.getTotalFreshCowsPerYear() == null)) {
      milkLossWeight =
          switch (key) {
            case DeathLoss -> {
              percentDifference = toolItem.deathLossIncidenceDifference;
              yield item.getDeathLossWeight();
            }
            case DisplacedAbomasum -> {
              percentDifference = toolItem.displacedAbomasumIncidenceDifference;
              yield item.getDisplacedAbomasumWeight();
            }
            case Dystocia -> {
              percentDifference = toolItem.dystociaIncidenceDifference;
              yield item.getDystociaWeight();
            }
            case Ketosis -> {
              percentDifference = toolItem.ketosisIncidenceDifference;
              yield item.getKetosisWeight();
            }
            case Metritis -> {
              percentDifference = toolItem.metritisIncidenceDifference;
              yield item.getMetritisWeight();
            }
            case MilkFever -> {
              percentDifference = toolItem.milkFeverIncidenceDifference;
              yield item.getMilkFeverWeight();
            }
            case RetainedPlacenta -> {
              percentDifference = toolItem.retainedPlacentaIncidenceDifference;
              yield item.getRetainedPlacentaWeight();
            }
          };
      if (milkLossWeight == null) return null;
    }

    Double milkPrice = item.getMilkPrice();
    Integer totalFreshCowsPerYear = item.getTotalFreshCowsPerYear();

    double valueForRounding =
        (((totalFreshCowsPerYear != null ? totalFreshCowsPerYear : 0)
                    * (percentDifference != null ? percentDifference : 0))
                / 100)
            * ((milkPrice != null ? milkPrice : 0) * (milkLossWeight != null ? milkLossWeight : 0));

    double roundedValue = Math.round(valueForRounding);
    return (int) roundedValue;
  }

  private Integer increasedDaysOpenCalculation(
      MetabolicIncidenceToolItem item,
      MetabolicIncidenceOutputToolItem outputItem,
      MetabolicTypeKeys key) {

    Integer daysOpen;
    Double percentDifference =
        switch (key) {
          case DeathLoss -> {
            daysOpen = item.getDeathLossOpen();
            yield outputItem.deathLossIncidenceDifference;
          }
          case DisplacedAbomasum -> {
            daysOpen = item.getDisplacedAbomasumDaysOpen();
            yield outputItem.displacedAbomasumIncidenceDifference;
          }
          case Dystocia -> {
            daysOpen = item.getDystociaOpen();
            yield outputItem.dystociaIncidenceDifference;
          }
          case Ketosis -> {
            daysOpen = item.getKetosisDaysOpen();
            yield outputItem.ketosisIncidenceDifference;
          }
          case Metritis -> {
            daysOpen = item.getMetritisDaysOpen();
            yield outputItem.metritisIncidenceDifference;
          }
          case MilkFever -> {
            daysOpen = item.getMilkFeverDaysOpen();
            yield outputItem.milkFeverIncidenceDifference;
          }
          case RetainedPlacenta -> {
            daysOpen = item.getRetainedPlacentaDaysOpen();
            yield outputItem.retainedPlacentaIncidenceDifference;
          }
          default -> throw new IllegalArgumentException(
              String.format(ILLEGAL_ARGUMENT_EXCEPTION_STATEMENT, key));
        };

    if (item.getCostOfExtraDaysOpen() == null
        || daysOpen == null
        || item.getTotalFreshCowsPerYear() == null) return null;

    double valueForRounding =
        ((item.getTotalFreshCowsPerYear() * percentDifference) / 100)
            * (daysOpen * item.getCostOfExtraDaysOpen());
    return (int) Math.round(valueForRounding);
  }

  private Integer treatmentCostCalculation(
      MetabolicIncidenceToolItem item,
      MetabolicIncidenceOutputToolItem outputItem,
      MetabolicTypeKeys key) {

    Double treatmentCost;
    Double percentDifference =
        switch (key) {
          case DeathLoss -> {
            treatmentCost = item.getDeathLossCost();
            yield outputItem.deathLossIncidenceDifference;
          }
          case DisplacedAbomasum -> {
            treatmentCost = item.getDisplacedAbomasumCost();
            yield outputItem.displacedAbomasumIncidenceDifference;
          }
          case Dystocia -> {
            treatmentCost = item.getDystociaCost();
            yield outputItem.dystociaIncidenceDifference;
          }
          case Ketosis -> {
            treatmentCost = item.getKetosisCost();
            yield outputItem.ketosisIncidenceDifference;
          }
          case Metritis -> {
            treatmentCost = item.getMetritisCost();
            yield outputItem.metritisIncidenceDifference;
          }
          case MilkFever -> {
            treatmentCost = item.getMilkFeverCost();
            yield outputItem.milkFeverIncidenceDifference;
          }
          case RetainedPlacenta -> {
            treatmentCost = item.getRetainedPlacentaCost();
            yield outputItem.retainedPlacentaIncidenceDifference;
          }
          default -> throw new IllegalArgumentException(
              String.format(ILLEGAL_ARGUMENT_EXCEPTION_STATEMENT, key));
        };

    if (treatmentCost == null || item.getTotalFreshCowsPerYear() == null) return null;

    double valueForRounding =
        ((item.getTotalFreshCowsPerYear() * percentDifference) / 100) * treatmentCost;
    return (int) Math.round(valueForRounding);
  }

  private Double costPerCowCalculation(
      MetabolicIncidenceToolItem item,
      MetabolicIncidenceOutputToolItem outputItem,
      MetabolicTypeKeys key) {
    if (item.getTotalFreshCowsPerYear() == null || item.getTotalFreshCowsPerYear() == 0)
      return null;

    Integer totalCost =
        switch (key) {
          case DeathLoss -> outputItem.deathLossTotalCost;
          case DisplacedAbomasum -> outputItem.displacedAbomasumTotalCost;
          case Dystocia -> outputItem.dystociaTotalCost;
          case Ketosis -> outputItem.ketosisTotalCost;
          case Metritis -> outputItem.metritisTotalCost;
          case MilkFever -> outputItem.milkFeverTotalCost;
          case RetainedPlacenta -> outputItem.retainedPlacentaTotalCost;
        };

    if (totalCost == null) return null;

    return ((double) totalCost / item.getTotalFreshCowsPerYear());
  }

  private Boolean herdHasValidInputs(MetabolicIncidenceToolItem item) {
    return (item.getTotalFreshCowsPerYear() != null
        && item.getMilkPrice() != null
        && item.getReplacementCowCost() != null
        && item.getCostOfExtraDaysOpen() != null
        && item.getTotalFreshCowsForEvaluation() != null);
  }

  private Boolean incidencesHasValidInputs(MetabolicIncidenceToolItem item) {
    return (item.getRetainedPlacentaIncidence() != null
        || item.getMetritisIncidence() != null
        || item.getDisplacedAbomasumIncidence() != null
        || item.getKetosisIncidence() != null
        || item.getMilkFeverIncidence() != null
        || item.getDystociaIncidence() != null
        || item.getDeathLossIncidence() != null);
  }
}
