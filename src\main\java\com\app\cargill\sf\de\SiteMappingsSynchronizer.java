/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.SiteMappingsRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class SiteMappingsSynchronizer implements DataSynchronizer<SiteMappingDocument> {

  private final SiteMappingsRepository repository;

  @Override
  public SyncResult sync(List<SiteMappingDocument> inputList) {
    List<SiteMappings> modifiedSm = new ArrayList<>();
    List<SiteMappings> newSm = new ArrayList<>();
    for (SiteMappingDocument sfSiteMapping : inputList) {
      try {
        SiteMappings siteMapping = getSiteMappingFromDb(sfSiteMapping);
        if (siteMapping == null) {
          // @TODO for now it is naive implementation. Need to add all required null fields as well
          sfSiteMapping.setId(UUID.randomUUID());
          SiteMappings deSite = new SiteMappings(sfSiteMapping);
          deSite.setLocalId(UUID.randomUUID().toString());
          newSm.add(deSite);
        } else {
          log.info("SiteMappingsSynchronizer SFDDEHerdId: {}", sfSiteMapping.getDdwHerdId());
          SiteMappingDocument deSmDocument = siteMapping.getSiteMappingDocument();
          deSmDocument.setDdwHerdId(sfSiteMapping.getDdwHerdId());
          deSmDocument.setMaxSiteId(sfSiteMapping.getMaxSiteId());
          deSmDocument.setLabyrinthSiteId(sfSiteMapping.getLabyrinthSiteId());
          deSmDocument.setLabyrinthAccountId(sfSiteMapping.getLabyrinthAccountId());
          deSmDocument.setMilkProcessorId(sfSiteMapping.getMilkProcessorId());
          deSmDocument.setDcgoId(sfSiteMapping.getDcgoId());

          siteMapping.setSiteMappingDocument(deSmDocument);
          modifiedSm.add(siteMapping);
          log.info("SiteMappingsSynchronizer modifiedSm: {}", modifiedSm);
        }
      } catch (Exception e) {
        log.error("Error syncing site mapping", e);
      }
    }
    repository.saveAll(newSm);
    repository.saveAll(modifiedSm);
    log.info("SiteMappingsSynchronizer MODIFIED_SITE_MAPPINGS: {}", modifiedSm);
    return new SyncResult(
        "SiteMappings",
        new AtomicInteger(newSm.size()),
        new AtomicInteger(modifiedSm.size()),
        new AtomicInteger(0),
        "",
        "");
  }

  @Override
  public Class<SiteMappingDocument> getType() {
    return SiteMappingDocument.class;
  }

  private SiteMappings getSiteMappingFromDb(SiteMappingDocument sfSiteMapping) {
    return repository.findBySiteId(sfSiteMapping.getLabyrinthSiteId().toString());
  }
}
