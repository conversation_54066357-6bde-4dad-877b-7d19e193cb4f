/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.Pair;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.service.IDDWReportService;
import com.app.cargill.service.analytics.DdwReportsAnalyticsService;
import java.util.UUID;
import org.bouncycastle.util.Strings;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class DDWReportControllerTest {

  @InjectMocks private DDWReportsController controller;
  @Mock IDDWReportService dDWReportServiceImpl;
  @Mock DdwReportsAnalyticsService ddwReportsAnalyticsService;

  @Test
  void whenDDWDetailedReportReturnsValidByteArrayResource() throws CustomDEExceptions {

    when(dDWReportServiceImpl.getLatestDetailedReport(any()))
        .thenReturn(
            new Pair<>("test_detailed.pdf", new ByteArrayResource(Strings.toByteArray(""))));

    ResponseEntity<?> result = controller.getLatestDetailedReport(UUID.randomUUID());

    Object body = result.getBody();
    assertNotNull(body);
  }

  @Test
  void whenDDWSummaryReportReturnsValidByteArrayResource() throws CustomDEExceptions {

    when(dDWReportServiceImpl.getLatestSummaryReport(any()))
        .thenReturn(new Pair<>("test_summary.pdf", new ByteArrayResource(Strings.toByteArray(""))));

    ResponseEntity<?> result = controller.getLatestSummaryReport(UUID.randomUUID());

    Object body = result.getBody();
    assertNotNull(body);
  }
}
