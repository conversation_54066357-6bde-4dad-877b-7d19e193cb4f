/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MilkProductionOutputsDto implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private Double ratioSNFPerButterfat;
  private Double maxAllowed;
  private Double totalFatProtein;
  private Double dairyEfficiency;
  private Double componentEfficiency;
  private Double totalRevenuePerHl;
  private Double feedCostPerHl;
  private Double purchasedFeedCostPerHl;
  private Double concentrateCostPerHl;
  private Double concentrateCostPerKgBF;
  private Double bfRevenue;
  private Double proteinRevenue;
  private Double otherSolidsRevenue;
  private Double deductionsPricePerCowPerDay;
  private Double snfNonPayment;
  private Double totalRevenuePricePerKgFat;
  private Double totalRevenueCowDay;
  private Double underQuotaLostRevenuePerMonth;
  private Double rofPerKgButterFat;
  private Double rof;
}
