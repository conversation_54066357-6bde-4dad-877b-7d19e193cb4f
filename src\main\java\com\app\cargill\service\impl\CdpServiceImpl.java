/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.ISelectKeyValueDto;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.CdpSiteDataWrapper;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.CdpSitesRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.ICdpService;
import com.app.cargill.service.impl.mappers.cdp.CdpAccountDocumentMapper;
import com.app.cargill.service.impl.mappers.cdp.CdpSiteDocumentMapper;
import com.app.cargill.service.impl.mappers.cdp.CdpVisitDocumentMapper;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

@Slf4j
@Service("cdpServiceImpl")
@RequiredArgsConstructor
public class CdpServiceImpl implements ICdpService {

  private final AccountsRepository accountsRepository;
  private final SitesRepository sitesRepository;
  private final VisitsRepository visitsRepository;
  private final CdpSitesRepository cdpSitesRepository;

  @Override
  @Transactional(readOnly = true, isolation = Isolation.SERIALIZABLE)
  public Page<AccountDocumentDTO> getAllAccountsByFromAndToDate(
      @NotNull Instant dateFrom, @NotNull Instant dateTo, Pageable pageable) {
    return accountsRepository
        .findAllAccountsByFromAndToDate(dateFrom, dateTo, pageable)
        .map(Accounts::getAccountDocument)
        .map(CdpAccountDocumentMapper::mapToDto);
  }

  @Override
  @Transactional(readOnly = true, isolation = Isolation.SERIALIZABLE)
  public Page<SiteDocumentDTO> getAllSitesByFromAndToDate(
      Instant dateFrom, Instant dateTo, Pageable pageable) {

    Instant start = Instant.now();
    List<CdpSiteDataWrapper> cdpSites = cdpSitesRepository.getCdpSitesData(dateFrom, dateTo);

    List<SiteDocumentDTO> sites =
        Flux.fromIterable(cdpSites)
            .map(siteWrapper -> CdpSiteDocumentMapper.mapToDto(siteWrapper.getSiteDocument()))
            .collectList()
            .block();

    Instant end = Instant.now();
    log.info("getAllSitesByFromAndToDate duration: {}", ChronoUnit.MILLIS.between(start, end));

    if (sites != null) {
      log.info("getAllSitesByFromAndToDate size: {}", sites.size());
      return new PageImpl<>(sites);
    } else {
      log.warn("getAllSitesByFromAndToDate sites is null");
      return new PageImpl<>(new ArrayList<>());
    }
  }

  @Override
  public Page<VisitDocumentDTO> getAllVisitsByFromAndToDate(
      Instant dateFrom, Instant dateTo, Pageable pageable) {
    Page<Visits> visits = visitsRepository.findAllVisitsByFromAndToDate(dateFrom, dateTo, pageable);
    List<String> siteIds =
        visits.getContent().stream()
            .map(Visits::getVisitDocument)
            .filter(
                visitDocument ->
                    visitDocument != null
                        && visitDocument.getSiteId() != null
                        && visitDocument.getMilkSoldEvaluation() != null)
            .toList()
            .stream()
            .map(VisitDocument::getSiteId)
            .toList()
            .stream()
            .map(UUID::toString)
            .toList();
    List<ISelectKeyValueDto<UUID, Double>> sites =
        ListUtils.emptyIfNull(sitesRepository.findMilkBySiteIds(siteIds));
    sites.forEach(
        s ->
            visits.getContent().stream()
                .filter(v -> v.getVisitDocument().getSiteId().equals(s.getKey()))
                .forEach(sv -> sv.getVisitDocument().setSiteMilk(s.getValue())));

    return visits.map(Visits::getVisitDocument).map(CdpVisitDocumentMapper::mapToDto);
  }
}
