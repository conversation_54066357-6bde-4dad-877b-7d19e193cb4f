/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RoboticMilkEvaluationToolItem;
import java.util.Objects;

public class RoboticMilkEvaluationCalculation {
  RoboticMilkEvaluationTool tool;
  RoboticMilkEvaluationToolItem docDBCurrentItem;

  public RoboticMilkEvaluationTool calculateFields(RoboticMilkEvaluationTool tool) {
    if (tool == null) return null;

    if (tool.getVisitRoboticMilkEvaluationData() != null) {
      this.tool = tool;

      docDBCurrentItem = tool.getVisitRoboticMilkEvaluationData();
      docDBCurrentItem.getOutputs().setCowsPerRobot(roundAvoid(cowsPerRobot(), 1));
      docDBCurrentItem.getOutputs().setMilkingsPerRobot(roundAvoid(milkingPerRobot(), 0));
      docDBCurrentItem.getOutputs().setRobotFreeTime(roundAvoid(robotFreeTime(), 1));
      docDBCurrentItem.getOutputs().setMilkPerRobot(roundAvoid(milkPerRobot(), 0));
      docDBCurrentItem.getOutputs().setMilkings(roundAvoid(milking(), 2));
      docDBCurrentItem.getOutputs().setMilkingRefusals(roundAvoid(milkingRefusals(), 2));
      docDBCurrentItem.getOutputs().setMilkingFailures(roundAvoid(milkingFailures(), 2));
      docDBCurrentItem.getOutputs().setMilkingSpeed(docDBCurrentItem.getMilkingSpeed());
      docDBCurrentItem.getOutputs().setAverageBoxTime(docDBCurrentItem.getAverageBoxTime());
      docDBCurrentItem.getOutputs().setMaximumConcentrate(roundAvoid(maximumConcentrate(), 0));
      docDBCurrentItem.getOutputs().setAverageConcentrate(roundAvoid(averageConcentrate(), 0));
      docDBCurrentItem.getOutputs().setMinimumConcentrate(roundAvoid(minimumConcentrate(), 0));
      docDBCurrentItem
          .getOutputs()
          .setConcentratePer100KGMilk(docDBCurrentItem.getConcentratePer100KGMilk());
      docDBCurrentItem.getOutputs().setRestFeed(docDBCurrentItem.getRestFeed());
    }
    return tool;
  }

  private Double cowsPerRobot() {
    if (tool.getVisitRoboticMilkEvaluationData().getLactatingCows() != null
        && tool.getVisitRoboticMilkEvaluationData().getLactatingCows() > 0
        && tool.getVisitRoboticMilkEvaluationData().getRobotsInHerd() != null
        && tool.getVisitRoboticMilkEvaluationData().getRobotsInHerd() > 0)
      return (tool.getVisitRoboticMilkEvaluationData().getLactatingCows() + 0.0)
          / (tool.getVisitRoboticMilkEvaluationData().getRobotsInHerd() + 0.0);
    return 0.0;
  }

  private Double milkingPerRobot() {
    return Objects.requireNonNullElse(tool.getVisitRoboticMilkEvaluationData().getMilkings(), 0.0)
        * docDBCurrentItem.getOutputs().getCowsPerRobot();
  }

  private Double robotFreeTime() {
    return tool.getVisitRoboticMilkEvaluationData().getRobotFreeTime() != null
            && tool.getVisitRoboticMilkEvaluationData().getRobotFreeTime() > 0
        ? tool.getVisitRoboticMilkEvaluationData().getRobotFreeTime()
        : 0;
  }

  private Double milkPerRobot() {
    return Objects.requireNonNullElse(
            tool.getVisitRoboticMilkEvaluationData().getAverageMilkYield(), 0.0)
        * docDBCurrentItem.getOutputs().getCowsPerRobot();
  }

  private Double milking() {
    return tool.getVisitRoboticMilkEvaluationData().getMilkings() != null
            && tool.getVisitRoboticMilkEvaluationData().getMilkings() > 0
        ? tool.getVisitRoboticMilkEvaluationData().getMilkings()
        : 0;
  }

  private Double milkingRefusals() {
    return tool.getVisitRoboticMilkEvaluationData().getMilkingRefusals() != null
            && tool.getVisitRoboticMilkEvaluationData().getMilkingRefusals() > 0
        ? tool.getVisitRoboticMilkEvaluationData().getMilkingRefusals()
        : 0;
  }

  private Double milkingFailures() {
    return (tool.getVisitRoboticMilkEvaluationData().getTotalMilkingFailures() != null
            && tool.getVisitRoboticMilkEvaluationData().getTotalMilkingFailures() > 0
            && tool.getVisitRoboticMilkEvaluationData().getRobotsInHerd() != null
            && tool.getVisitRoboticMilkEvaluationData().getRobotsInHerd() > 0)
        ? (tool.getVisitRoboticMilkEvaluationData().getTotalMilkingFailures()
            / tool.getVisitRoboticMilkEvaluationData().getRobotsInHerd())
        : 0;
  }

  private Double maximumConcentrate() {
    return tool.getVisitRoboticMilkEvaluationData().getMaximumConcentrate() != null
            && tool.getVisitRoboticMilkEvaluationData().getMaximumConcentrate() > 0
        ? tool.getVisitRoboticMilkEvaluationData().getMaximumConcentrate()
        : 0;
  }

  private Double averageConcentrate() {
    return tool.getVisitRoboticMilkEvaluationData().getAverageConcentrateFed() != null
            && tool.getVisitRoboticMilkEvaluationData().getAverageConcentrateFed() > 0
        ? tool.getVisitRoboticMilkEvaluationData().getAverageConcentrateFed()
        : 0;
  }

  private Double minimumConcentrate() {
    return tool.getVisitRoboticMilkEvaluationData().getMinimumConcentrate() != null
            && tool.getVisitRoboticMilkEvaluationData().getMinimumConcentrate() > 0
        ? tool.getVisitRoboticMilkEvaluationData().getMinimumConcentrate()
        : 0;
  }
}
