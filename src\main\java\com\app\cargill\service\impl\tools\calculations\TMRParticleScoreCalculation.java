/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreToolItem;
import java.util.List;
import java.util.Objects;
import java.util.OptionalDouble;
import java.util.stream.DoubleStream;

public class TMRParticleScoreCalculation {
  RumenHealthTMRParticleScoreTool tool;
  RumenHealthTMRParticleScoreToolItem docDBCurrentTMRScores;

  public RumenHealthTMRParticleScoreTool calculateFields(RumenHealthTMRParticleScoreTool tool) {
    if (tool == null) return null;
    this.tool = tool;

    if (tool.getTmrScores() != null) {
      for (RumenHealthTMRParticleScoreToolItem tmrScore : tool.getTmrScores()) {
        docDBCurrentTMRScores = tmrScore;
        docDBCurrentTMRScores.setTotalScaleAmount(totalScaleAmount());
        docDBCurrentTMRScores.setTopScalePercentage(topScalePercentage());
        docDBCurrentTMRScores.setMid1ScalePercentage(mid1ScalePercentage());
        docDBCurrentTMRScores.setMid2ScalePercentage(mid2ScalePercentage());
        docDBCurrentTMRScores.setTrayScalePercentage(trayScalePercentage());
      }

      tool.setAvgTopScalePercentage(avgTopScalePercentage());
      tool.setAvgMid1ScalePercentage(avgMid1ScalePercentage());
      tool.setAvgMid2ScalePercentage(avgMid2ScalePercentage());
      tool.setAvgTrayScalePercentage(avgTrayScalePercentage());

      for (RumenHealthTMRParticleScoreToolItem tmrScore : tool.getTmrScores()) {
        docDBCurrentTMRScores = tmrScore;
        docDBCurrentTMRScores.setTopStdDevValue(avgStdDevTop());
        docDBCurrentTMRScores.setMid1StdDevValue(avgStdDevMid1());
        docDBCurrentTMRScores.setMid2StdDevValue(avgStdDevMid2());
        docDBCurrentTMRScores.setTrayStdDevValue(avgStdDevTray());
      }
    }
    return tool;
  }

  private Double avgTopScalePercentage() {
    DoubleStream resultValue =
        tool.getTmrScores().stream()
            .filter(item -> item.getTopScalePercentage() != null)
            .mapToDouble(RumenHealthTMRParticleScoreToolItem::getTopScalePercentage);

    OptionalDouble resVal = resultValue.average();
    return resVal.isPresent() ? resVal.getAsDouble() : 0.0;
  }

  private Double avgMid1ScalePercentage() {
    DoubleStream resultValue =
        tool.getTmrScores().stream()
            .filter(item -> item.getMid1ScalePercentage() != null)
            .mapToDouble(RumenHealthTMRParticleScoreToolItem::getMid1ScalePercentage);

    OptionalDouble resVal = resultValue.average();
    return resVal.isPresent() ? resVal.getAsDouble() : 0.0;
  }

  private Double avgMid2ScalePercentage() {
    DoubleStream resultValue =
        tool.getTmrScores().stream()
            .filter(item -> item.getMid2ScalePercentage() != null)
            .mapToDouble(RumenHealthTMRParticleScoreToolItem::getMid2ScalePercentage);

    OptionalDouble resVal = resultValue.average();
    return resVal.isPresent() ? resVal.getAsDouble() : 0.0;
  }

  private Double avgTrayScalePercentage() {
    DoubleStream resultValue =
        tool.getTmrScores().stream()
            .filter(item -> item.getTrayScalePercentage() != null)
            .mapToDouble(RumenHealthTMRParticleScoreToolItem::getTrayScalePercentage);

    OptionalDouble resVal = resultValue.average();
    return resVal.isPresent() ? resVal.getAsDouble() : 0.0;
  }

  private Double totalScaleAmount() {
    return topScaleAmountInGrams()
        + mid1ScaleAmountInGrams()
        + mid2ScaleAmountInGrams()
        + trayScaleAmountInGrams();
  }

  private Double topScalePercentage() {
    double totalScaleAmount = totalScaleAmount();
    return totalScaleAmount > 0
        ? roundAvoid(((topScaleAmountInGrams() / totalScaleAmount) * 100), 1)
        : 0.0;
  }

  private Double mid1ScalePercentage() {
    double totalScaleAmount = totalScaleAmount();
    return totalScaleAmount > 0
        ? roundAvoid(((mid1ScaleAmountInGrams() / totalScaleAmount) * 100), 1)
        : 0.0;
  }

  private Double mid2ScalePercentage() {
    double totalScaleAmount = totalScaleAmount();
    return totalScaleAmount > 0
        ? roundAvoid((mid2ScaleAmountInGrams() / totalScaleAmount) * 100, 1)
        : 0.0;
  }

  private Double trayScalePercentage() {
    double totalScaleAmount = totalScaleAmount();
    return totalScaleAmount > 0
        ? roundAvoid((trayScaleAmountInGrams() / totalScaleAmount) * 100, 1)
        : 0.0;
  }

  private Double topScaleAmountInGrams() {
    double diffVal =
        Objects.requireNonNullElse(docDBCurrentTMRScores.getTopScaleAmountInGrams(), 0.0)
            - Objects.requireNonNullElse(
                docDBCurrentTMRScores.getTopScreenTareAmountInGrams(), 0.0);

    return diffVal > 0 ? diffVal : 0;
  }

  private Double mid1ScaleAmountInGrams() {
    double diffVal =
        Objects.requireNonNullElse(docDBCurrentTMRScores.getMid1ScaleAmountInGrams(), 0.0)
            - Objects.requireNonNullElse(
                docDBCurrentTMRScores.getMid1ScreenTareAmountInGrams(), 0.0);

    return diffVal > 0 ? diffVal : 0;
  }

  private Double mid2ScaleAmountInGrams() {
    double diffVal =
        Objects.requireNonNullElse(docDBCurrentTMRScores.getMid2ScaleAmountInGrams(), 0.0)
            - Objects.requireNonNullElse(
                docDBCurrentTMRScores.getMid2ScreenTareAmountInGrams(), 0.0);

    return diffVal > 0 ? diffVal : 0;
  }

  private Double trayScaleAmountInGrams() {
    double diffVal =
        Objects.requireNonNullElse(docDBCurrentTMRScores.getTrayScaleAmountInGrams(), 0.0)
            - Objects.requireNonNullElse(
                docDBCurrentTMRScores.getTrayScreenTareAmountInGrams(), 0.0);

    return diffVal > 0 ? diffVal : 0;
  }

  private Double avgStdDevTop() {
    return calculateStandardDeviation(
        tool.getTmrScores().stream()
            .filter(x -> x.getToolStatus() == ToolStatuses.Completed)
            .map(RumenHealthTMRParticleScoreToolItem::getTopScalePercentage)
            .toList(),
        tool.getAvgTopScalePercentage());
  }

  private Double avgStdDevMid1() {
    return calculateStandardDeviation(
        tool.getTmrScores().stream()
            .filter(x -> x.getToolStatus() == ToolStatuses.Completed)
            .map(RumenHealthTMRParticleScoreToolItem::getMid1ScalePercentage)
            .toList(),
        tool.getAvgMid1ScalePercentage());
  }

  private Double avgStdDevMid2() {
    return calculateStandardDeviation(
        tool.getTmrScores().stream()
            .filter(x -> x.getToolStatus() == ToolStatuses.Completed)
            .map(RumenHealthTMRParticleScoreToolItem::getMid2ScalePercentage)
            .toList(),
        tool.getAvgMid2ScalePercentage());
  }

  private Double avgStdDevTray() {
    return calculateStandardDeviation(
        tool.getTmrScores().stream()
            .filter(x -> x.getToolStatus() == ToolStatuses.Completed)
            .map(RumenHealthTMRParticleScoreToolItem::getTrayScalePercentage)
            .toList(),
        tool.getAvgTrayScalePercentage());
  }

  private Double calculateStandardDeviation(List<Double> listOfValues, Double avgValue) {
    double avg = avgValue;
    if (listOfValues == null || listOfValues.isEmpty()) return 0.0;
    int n = listOfValues.size();
    double sumOfSquareDelta =
        listOfValues.stream()
            .filter(Objects::nonNull)
            .mapToDouble(score -> Math.pow(Math.abs(score - avg), 2))
            .sum();

    return roundAvoid(Math.sqrt(sumOfSquareDelta / n), 2);
  }
}
