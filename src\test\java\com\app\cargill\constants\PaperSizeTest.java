/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class PaperSizeTest {

  @Test
  void whenA4PaperReturnsCorrectDimensions() {
    assertEquals("210mm", PaperSize.A4.getWidth());
    assertEquals("297mm", PaperSize.A4.getHeight());
    assertEquals("10px", PaperSize.A4.getMarginBottom());
    assertEquals("10px", PaperSize.A4.getMarginLeft());
    assertEquals("10px", PaperSize.A4.getMarginTop());
    assertEquals("10px", PaperSize.A4.getMarginRight());
  }
}
