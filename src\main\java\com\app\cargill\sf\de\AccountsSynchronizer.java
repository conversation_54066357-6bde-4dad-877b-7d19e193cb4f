/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.apache.commons.collections4.CollectionUtils.isEmpty;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.User;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class AccountsSynchronizer implements DataSynchronizer<AccountDocument> {

  private final AccountsRepository accountsRepository;
  private final UserRepository userRepository;

  @Override
  public SyncResult sync(List<AccountDocument> inputList) {
    List<AccountDocument> modifiedAccounts = Collections.synchronizedList(new ArrayList<>());
    List<Accounts> newAccounts = Collections.synchronizedList(new ArrayList<>());
    for (AccountDocument sfAccount : inputList) {
      try {
        Accounts account = getAccountFromDb(sfAccount);
        if (account == null) {
          // @TODO for now it is naive implementation. Need to add all required null fields as well

          // There is possibility that there is already an id but the account is missing in our
          // database
          UUID uuid = UUID.randomUUID();
          if (sfAccount.getId() == null) {
            log.debug("ACCOUNT_LOCAL_ID_GENERATED {} {}", sfAccount.getGoldenRecordId(), uuid);
            sfAccount.setId(uuid);
          }
          sfAccount.setNeedsSync(true);
          Accounts deAccount = new Accounts(sfAccount);
          deAccount.setLocalId(uuid.toString());
          newAccounts.add(deAccount);
        } else if (updateCondition(account, sfAccount)) {
          modifiedAccounts.add(sfAccount);
          account.setAccountDocument(updateAccount(sfAccount, account.getAccountDocument()));
          accountsRepository.saveAndFlush(account);
        }
      } catch (Exception e) {
        log.error("Error syncing account", e);
      }
    }

    accountsRepository.saveAll(newAccounts);
    log.debug("NEW_ACCOUNTS {}", newAccounts);
    log.debug("MODIFIED_ACCOUNTS {}", modifiedAccounts);
    return new SyncResult(
        "Accounts",
        new AtomicInteger(newAccounts.size()),
        new AtomicInteger(modifiedAccounts.size()),
        new AtomicInteger(0),
        "",
        "");
  }

  @Override
  public Class<AccountDocument> getType() {
    return AccountDocument.class;
  }

  @SuppressWarnings("java:S3776") // Complexity is not an issue since code is trivial
  public AccountDocument updateAccount(AccountDocument source, AccountDocument target) {
    if (source.getDataSource() != DataSource.UNKNOWN) {
      target.setDataSource(source.getDataSource());
    }
    if (source.getId() != null && !source.getId().equals(target.getId())) {
      // there are rare cases where we generated the id, but it already is set in SF
      target.setId(source.getId());
    }

    String lmMappingId = getLmMappingId(source);
    // fix mismatching ID
    try {
      if (lmMappingId != null && !target.getId().equals(UUID.fromString(lmMappingId))) {
        target.setId(UUID.fromString(lmMappingId));
      }
    } catch (IllegalArgumentException e) {
      log.error("Invalid UUID for account {} {}", source.getId(), source.getGoldenRecordId());
      throw e;
    }

    // Fix remote mismatch
    if (source.getId() != null && !source.getId().toString().equals(lmMappingId)) {
      target.setNeedsSync(true);
    }

    if (source.getGoldenRecordId() != null
        && !source.getGoldenRecordId().equals(target.getGoldenRecordId())) {
      target.setGoldenRecordId(source.getGoldenRecordId());
    }
    if (source.getAccountName() != null
        && !source.getAccountName().equals(target.getAccountName())) {
      target.setAccountName(source.getAccountName());
    }
    if (source.getLegalName() != null && !source.getLegalName().equals(target.getLegalName())) {
      target.setLegalName(source.getLegalName());
    }
    if (source.getAccountType() != null
        && !source.getAccountType().equals(target.getAccountType())) {
      target.setAccountType(source.getAccountType());
    }
    if (source.getPhysicalAddress() != null
        && !source.getPhysicalAddress().equals(target.getPhysicalAddress())) {
      target.setPhysicalAddress(source.getPhysicalAddress());
    }
    if (source.getSegmentStepOneId() != null
        && !source.getSegmentStepOneId().equals(target.getSegmentStepOneId())) {
      target.setSegmentStepOneId(source.getSegmentStepOneId());
    }
    if (source.getNineBoxStepTwoID() != null
        && !source.getNineBoxStepTwoID().equals(target.getNineBoxStepTwoID())) {
      target.setNineBoxStepTwoID(source.getNineBoxStepTwoID());
    }
    if (source.getSourceSystem() != null
        && !source.getSourceSystem().equals(target.getSourceSystem())) {
      target.setSourceSystem(source.getSourceSystem());
    }
    if (source.getSubTypeID() != null && !source.getSubTypeID().equals(target.getSubTypeID())) {
      target.setSubTypeID(source.getSubTypeID());
    }
    if (source.getBusinessID() != null && !source.getBusinessID().equals(target.getBusinessID())) {
      target.setBusinessID(source.getBusinessID());
    }
    if (source.getParentAccountID() != null
        && !source.getParentAccountID().equals(target.getParentAccountID())) {
      target.setParentAccountID(source.getParentAccountID());
    }
    if (source.getExternalParentAccountID() != null
        && !source.getExternalParentAccountID().equals(target.getExternalParentAccountID())) {
      target.setExternalParentAccountID(source.getExternalParentAccountID());
    }
    if (source.getOwnerId() != null && !source.getOwnerId().equals(target.getOwnerId())) {
      target.setOwnerId(source.getOwnerId());
    }
    if (source.getOwnerProfileNameandId() != null
        && !source.getOwnerProfileNameandId().equals(target.getOwnerProfileNameandId())) {
      target.setOwnerProfileNameandId(source.getOwnerProfileNameandId());
    }
    if (source.getAccountValidated() != null
        && !source.getAccountValidated().equals(target.getAccountValidated())) {
      target.setAccountValidated(source.getAccountValidated());
    }
    if (source.getWebSiteAddress() != null
        && !source.getWebSiteAddress().equals(target.getWebSiteAddress())) {
      target.setWebSiteAddress(source.getWebSiteAddress());
    }
    if (source.getActive() != null && !source.getActive().equals(target.getActive())) {
      target.setActive(source.getActive());
    }
    if (source.getCompanyEmail() != null
        && !source.getCompanyEmail().equals(target.getCompanyEmail())) {
      target.setCompanyEmail(source.getCompanyEmail());
    }
    if (source.getPhone() != null && !source.getPhone().equals(target.getPhone())) {
      target.setPhone(source.getPhone());
    }
    if (source.getAccountCurrency() != null
        && !source.getAccountCurrency().equals(target.getAccountCurrency())) {
      target.setAccountCurrency(source.getAccountCurrency());
    }
    if (source.getAccountNumber() != null
        && !source.getAccountNumber().equals(target.getAccountNumber())) {
      target.setAccountNumber(source.getAccountNumber());
    }
    if (source.getNewAccountType() != null
        && !source.getNewAccountType().equals(target.getNewAccountType())) {
      target.setNewAccountType(source.getNewAccountType());
    }
    if (source.getCustomerStatus() != null
        && !source.getCustomerStatus().equals(target.getCustomerStatus())) {
      target.setCustomerStatus(source.getCustomerStatus());
    }
    if (source.getAccountStatus() != null
        && !source.getAccountStatus().equals(target.getAccountStatus())) {
      target.setAccountStatus(source.getAccountStatus());
    }
    if (source.getCurrentUserProfileNameandId() != null
        && !source
            .getCurrentUserProfileNameandId()
            .equals(target.getCurrentUserProfileNameandId())) {
      target.setCurrentUserProfileNameandId(source.getCurrentUserProfileNameandId());
    }
    if (source.getLastModifiedBy() != null
        && !source.getLastModifiedBy().equals(target.getLastModifiedBy())) {
      target.setLastModifiedBy(source.getLastModifiedBy());
    }
    if (source.getLastModifiedTimeUtc() != null
        && !source.getLastModifiedTimeUtc().equals(target.getLastModifiedTimeUtc())) {
      target.setLastModifiedTimeUtc(source.getLastModifiedTimeUtc());
    }
    if (source.getDescription() != null
        && !source.getDescription().equals(target.getDescription())) {
      target.setDescription(source.getDescription());
    }
    if (source.getCustomerCode() != null
        && !source.getCustomerCode().equals(target.getCustomerCode())) {
      target.setCustomerCode(source.getCustomerCode());
    }
    if (source.getDateOfLastCall() != null
        && !source.getDateOfLastCall().equals(target.getDateOfLastCall())) {
      target.setDateOfLastCall(source.getDateOfLastCall());
    }
    if (source.getAccountStatus() != null
        && !source.getAccountStatus().equals(target.getAccountStatus())) {
      target.setAccountStatus(source.getAccountStatus());
    }

    target.setUsers(source.getUsers());
    target.setUserRoles(source.getUserRoles());

    target.setContacts(updateContacts(source.getContacts(), target.getContacts()));
    target.setApplicationMappings(source.getApplicationMappings());
    return target;
  }

  public boolean accountIsRelatedToUsers(Accounts account) {
    if (account.getAccountDocument().getUsers() == null
        || account.getAccountDocument().getUsers().isEmpty()) {
      return false;
    } else {
      List<String> accountUsers =
          account.getAccountDocument().getUsers().stream().map(String::toLowerCase).toList();
      List<User> existingUsers = userRepository.findMultipleUsersLowerCase(accountUsers);
      return existingUsers != null && !existingUsers.isEmpty();
    }
  }

  private Accounts getAccountFromDb(AccountDocument sfAccount) {
    Accounts account = null;
    if (sfAccount.getId() != null) {
      account = accountsRepository.findByAccountIdInclDeleted(sfAccount.getId().toString());
    }
    if (account == null) {
      // check if it exists but with different or missing id
      account = accountsRepository.findByExternalId(sfAccount.getGoldenRecordId());
      if (account != null) {
        // this can be used to fix wrong UUIDs in Salesforce
        account.getAccountDocument().setNeedsSync(true);
      }
    }
    return account;
  }

  private boolean updateCondition(Accounts account, AccountDocument sfAccount) {
    return account.getUpdatedDate().toInstant().getEpochSecond()
            < sfAccount.getLastModifiedTimeUtc().getEpochSecond()
        || account.getAccountDocument().getDataSource() == DataSource.UNKNOWN
        || !sfAccount.getGoldenRecordId().equals(account.getAccountDocument().getGoldenRecordId())
        || idMismatch(account, sfAccount)
        || usersMismatch(account, sfAccount);
  }

  private boolean idMismatch(Accounts dbAccount, AccountDocument sfAccount) {
    if (isEmpty(sfAccount.getApplicationMappings())) {
      return false;
    }

    return !dbAccount.getAccountDocument().getId().toString().equals(getLmMappingId(sfAccount));
  }

  private boolean usersMismatch(Accounts dbAccount, AccountDocument sfAccount) {
    Set<String> dbUsers = dbAccount.getAccountDocument().getUsers();
    Set<String> sfUsers = sfAccount.getUsers();
    // Merging all records from both sets should result in different size if there are different
    // records
    Set<String> result = new HashSet<>();
    result.addAll(dbUsers);
    result.addAll(sfUsers);
    return result.size() != dbUsers.size() || result.size() != sfUsers.size();
  }

  private String getLmMappingId(AccountDocument sfAccount) {
    Optional<ExternalDataSource> lmMappingOptional =
        sfAccount.getApplicationMappings().stream()
            .filter(am -> am.getSystem() != null && am.getSystem().equals("LM"))
            .findAny();
    return lmMappingOptional.map(ExternalDataSource::getUniqueExternalKey).orElse(null);
  }

  private List<Contact> updateContacts(List<Contact> source, List<Contact> target) {
    List<Contact> result = new ArrayList<>();
    if (source == null || source.isEmpty()) return result;
    if (target == null || target.isEmpty()) {
      return source.stream()
          .map(
              s -> {
                if (s.getContactId() == null) {
                  s.setContactId(UUID.randomUUID());
                  s.setNeedsSync(true);
                }
                return s;
              })
          .toList();
    } else {
      for (Contact sourceContact : source) {
        Optional<Contact> contact =
            target.stream()
                .filter(
                    t ->
                        t.getContactId() != null
                            && t.getContactId().equals(sourceContact.getContactId()))
                .findAny();
        if (contact.isPresent()) {
          result.add(updateSingleContact(sourceContact, contact.get()));
        } else {
          sourceContact.setContactId(UUID.randomUUID());
          sourceContact.setNeedsSync(true);
          result.add(sourceContact);
        }
      }
    }

    return result;
  }

  private Contact updateSingleContact(Contact source, Contact target) {
    target.setAccountId(source.getAccountId());
    target.setGoldenRecordAcountId(source.getGoldenRecordAcountId());
    target.setSFDCContactId(source.getSFDCContactId());
    target.setFirstName(source.getFirstName());
    target.setLastName(source.getLastName());
    target.setMailingAddress(source.getMailingAddress());
    target.setPhoneNumber(source.getPhoneNumber());
    target.setEmailAddress(source.getEmailAddress());
    target.setLastUpdateDateTime(source.getLastUpdateDateTime());
    target.setName(source.getName());
    target.setOtherAddress(source.getOtherAddress());
    target.setPrimaryLangId(source.getPrimaryLangId());
    target.setPreferredMethodId(source.getPreferredMethodId());
    target.setSalutation(source.getSalutation());
    target.setDeleted(source.isDeleted());
    target.setCreateTimeUtc(source.getCreateTimeUtc());
    return target;
  }
}
