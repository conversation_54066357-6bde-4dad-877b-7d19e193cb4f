/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimalClass implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Id")
  public UUID id;

  @JsonProperty("Class")
  public String classs;

  @JsonProperty("SubClass")
  public String subClass;

  @JsonProperty("Selected")
  public Boolean selected;

  public AnimalClass(UUID animalId, String animalClasss, String animalSubClass) {
    id = animalId;
    classs = animalClasss;
    subClass = animalSubClass;
  }
}
