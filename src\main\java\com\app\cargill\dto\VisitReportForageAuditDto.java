/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.LinkedHashMap;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportForageAuditDto {

  private List<VisitReportDoughnutChartDto> forageManagement;
  private List<VisitReportDoughnutChartDto> forageQualityInRation;
  private List<VisitReportDoughnutChartDto> bunkersAndPiles;
  private List<VisitReportDoughnutChartDto> towerSilos;
  private List<VisitReportDoughnutChartDto> silageBags;
  private List<VisitReportDoughnutChartDto> baleage;
  private LinkedHashMap<String, List<VisitReportQuestionAnswerDto>> questionAnswers;
  private List<NotesDto> notes;
}
