/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.UserRole;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.cc.service.LiftAccountService;
import java.sql.Date;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AccountsDataFixServiceTest {

  @Mock private AccountsRepository accountsRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private VisitsRepository visitsRepository;
  @Mock private LiftAccountService liftAccountService;

  @InjectMocks private AccountsDataFixService accountsDataFixService;

  @Test
  void whenAccountsAreUpdatedCorrectResultIsReturned() {
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setVisitDate(Instant.now());
    Visits visit = new Visits(visitDocument);
    Accounts account1 = generateAccount();
    UUID uuid1 = UUID.randomUUID();
    account1.getAccountDocument().setId(uuid1);
    Accounts account2 = generateAccount();
    UUID uuid2 = UUID.randomUUID();
    account2.getAccountDocument().setId(uuid2);
    Accounts account3 = generateAccount();
    UUID uuid3 = UUID.randomUUID();
    account3.getAccountDocument().setId(uuid3);

    when(accountsRepository.findAll()).thenReturn(List.of(account1, account2, account3));
    when(sitesRepository.countByAccountId(uuid1.toString())).thenReturn(0);
    when(sitesRepository.countByAccountId(uuid2.toString())).thenReturn(1);
    when(sitesRepository.countByAccountId(uuid3.toString())).thenReturn(0);
    when(visitsRepository.findLatestVisitByCustomerId(any())).thenReturn(null);
    when(visitsRepository.findLatestVisitByCustomerId(uuid1.toString())).thenReturn(visit);
    when(accountsRepository.saveAllAndFlush(any())).thenAnswer(i -> i.getArgument(0));

    Flux<Accounts> accountsFlux = accountsDataFixService.runAccountsPostMigration();

    StepVerifier.create(accountsFlux).expectNextCount(2).verifyComplete();
  }

  @Test
  void whenUserAccountsAreUpdatedCorrectResultIsReturned() {
    String user = "<EMAIL>";
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setVisitDate(Instant.now());
    Visits visit = new Visits(visitDocument);
    Accounts account1 = generateAccount();
    UUID uuid1 = UUID.randomUUID();
    account1.getAccountDocument().setId(uuid1);
    Accounts account2 = generateAccount();
    UUID uuid2 = UUID.randomUUID();
    account2.getAccountDocument().setId(uuid2);
    Accounts account3 = generateAccount();
    UUID uuid3 = UUID.randomUUID();
    account3.getAccountDocument().setId(uuid3);

    when(accountsRepository.findAccountsByUserEmail(user))
        .thenReturn(List.of(account1, account2, account3));
    when(sitesRepository.countByAccountId(uuid1.toString())).thenReturn(0);
    when(sitesRepository.countByAccountId(uuid2.toString())).thenReturn(1);
    when(sitesRepository.countByAccountId(uuid3.toString())).thenReturn(0);
    when(visitsRepository.findLatestVisitByCustomerId(any())).thenReturn(null);
    when(visitsRepository.findLatestVisitByCustomerId(uuid1.toString())).thenReturn(visit);
    when(accountsRepository.saveAllAndFlush(any())).thenAnswer(i -> i.getArgument(0));

    Flux<Accounts> accountsFlux = accountsDataFixService.runUserAccountsPostMigration(user);

    StepVerifier.create(accountsFlux).expectNextCount(2).verifyComplete();
  }

  @Test
  void testUpdateAccountsWithWrongOwnerId() {

    // Create Accounts with invalid ownerIds
    Accounts accountWithoutEmail1 = generateAccount();
    accountWithoutEmail1.getAccountDocument().setOwnerId("invalid_owner_id");
    accountWithoutEmail1.getAccountDocument().setUsers(Set.of("<EMAIL>"));

    Accounts accountWithoutEmail2 = generateAccount();
    accountWithoutEmail2.getAccountDocument().setOwnerId("another_invalid_owner_id");
    accountWithoutEmail2.getAccountDocument().setUsers(Set.of("<EMAIL>"));

    List<Accounts> accounts = List.of(accountWithoutEmail1, accountWithoutEmail2);

    // Mock the Account Repository response
    when(accountsRepository.findAll()).thenReturn(accounts);
    when(accountsRepository.saveAllAndFlush(anyList())).thenReturn(accounts);

    // Make actual method call
    Flux<Accounts> updatedAccountsFlux = accountsDataFixService.updateAccountsWithWrongOwnerId();

    // Verify
    StepVerifier.create(updatedAccountsFlux)
        .expectNextMatches(account -> account.getAccountDocument().getOwnerId().contains("@"))
        .expectNextMatches(account -> account.getAccountDocument().getOwnerId().contains("@"))
        .expectComplete()
        .verify();
  }

  @Test
  void whenDataSourceAreUpdatedCorrectResultIsReturned() {
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setVisitDate(Instant.now());
    Accounts account1 = generateAccount();
    UUID uuid1 = UUID.randomUUID();
    account1.getAccountDocument().setId(uuid1);
    Accounts account2 = generateAccount();
    UUID uuid2 = UUID.randomUUID();
    account2.getAccountDocument().setId(uuid2);
    Accounts account3 = generateAccount();
    UUID uuid3 = UUID.randomUUID();
    account3.getAccountDocument().setId(uuid3);

    when(accountsRepository.findAll()).thenReturn(List.of(account1, account2, account3));
    when(accountsRepository.saveAllAndFlush(any())).thenAnswer(i -> i.getArgument(0));
    when(liftAccountService.getAllAccounts(anyList()))
        .thenReturn(List.of(mock(AccountDocument.class)));
    when(sitesRepository.findAllByAccountId(anyString()))
        .thenThrow(new IllegalArgumentException("TEST_EXCEPTION"))
        .thenReturn(List.of(new Sites(new SiteDocument())));

    Flux<Accounts> accountsFlux = accountsDataFixService.runAccountsPostMigration();

    StepVerifier.create(accountsFlux).expectNextCount(3).verifyComplete();
  }

  @Test
  void whenOwnerWasNotPartOf() {

    // Create Accounts with invalid ownerIds
    Accounts accountWithoutEmail1 = generateAccount();
    accountWithoutEmail1.getAccountDocument().setOwnerId("<EMAIL>");
    accountWithoutEmail1.getAccountDocument().setUsers(new HashSet<>());

    List<Accounts> accounts = List.of(accountWithoutEmail1);

    // Mock the Account Repository response
    when(accountsRepository.findAll()).thenReturn(accounts);
    when(accountsRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    // Make actual method call
    Flux<Accounts> updatedAccountsFlux = accountsDataFixService.fixOwnerMissingFromUsers();

    // Verify
    StepVerifier.create(updatedAccountsFlux)
        .expectNextMatches(
            account -> account.getAccountDocument().getUsers().contains("<EMAIL>"))
        .expectComplete()
        .verify();
  }

  private Accounts generateAccount() {
    AccountDocument accountDocument = new AccountDocument();
    UUID accountId = UUID.randomUUID();
    accountDocument.setId(accountId);
    accountDocument.setSiteCount(0);
    Accounts account = new Accounts(accountDocument);
    account.setCreatedDate(Date.from(Instant.now()));
    account.setUpdatedDate(Date.from(Instant.now()));

    return account;
  }

  @Test
  void testFixUsersArraySingleAccount() {
    Accounts account =
        createAccount("validOwner", Arrays.asList("validUser", "invalidUser.invalid"));
    Accounts fixedAccount = accountsDataFixService.fixUsersArray(account);

    Set<String> expectedUsers = new HashSet<>(Arrays.asList("validOwner", "validUser"));
    assertEquals(expectedUsers, fixedAccount.getAccountDocument().getUsers());

    Mockito.verify(accountsRepository).save(fixedAccount);
  }

  private Accounts createAccount(String ownerId, List<String> userNames) {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setOwnerId(ownerId);

    List<UserRole> userRoles =
        userNames.stream()
            .map(username -> new UserRole(username, username, null))
            .collect(Collectors.toList());
    accountDocument.setUserRoles(userRoles);

    Accounts account = new Accounts();
    account.setAccountDocument(accountDocument);
    return account;
  }
}
