/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.User;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.data.MergeAccountsService;
import com.app.cargill.service.impl.UserServiceImpl;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.service.LiftAccountMergeService;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftApiReactiveService;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.service.LongRunningTasksService;
import com.app.cargill.sf.cc.service.SalesforceClientFactory;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@SpringBootTest
@ActiveProfiles({"test", "mock-cosmos"})
class LiftSyncServiceTest {
  private LiftConfig config;
  private LiftSyncService service;
  @Autowired private DataSynchronizerFactory synchronizerFactory;

  @MockBean private AccountsRepository accountsRepository;
  @MockBean private SitesRepository sitesRepository;
  @MockBean private SiteMappingsRepository siteMappingsRepository;
  @MockBean private LongRunningTasksService longRunningTasksService;
  @MockBean private PensRepository pensRepository;
  @MockBean private NotesRepository notesRepository;
  @MockBean private VisitsRepository visitsRepository;
  @Mock private ResourceBundleMessageSource bundleMessageSource;
  @MockBean private LiftAccountService accountService;
  @MockBean private LiftSiteMappingsService liftSiteMappingsService;
  @MockBean private UserRepository userRepository;
  @Mock private UserServiceImpl userServiceImpl;
  @Mock private SiteMappingsRepository mappingsRepository;
  @Mock private LiftConfig liftConfig;
  @MockBean private LiftUserService liftUserService;
  @MockBean private LiftApiReactiveService liftApiReactiveService;
  @MockBean private LiftApiService liftApiService;
  @MockBean private LiftAccountMergeService accountMergeService;
  private static MockWebServer mockBackEnd;

  @BeforeAll
  static void setUp() throws IOException {
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();
  }

  @BeforeEach
  void initialize() {
    config = mock(LiftConfig.class);
    when(config.getGrantType()).thenReturn("client_credentials");
    final int size = 32 * 1024 * 1024;
    final ExchangeStrategies strategies =
        ExchangeStrategies.builder()
            .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
            .build();
    LiftApiService sfApi =
        new LiftApiService(
            new LiftApiReactiveService(
                new SalesforceClientFactory(WebClient.builder().exchangeStrategies(strategies)),
                config));
    service =
        new LiftSyncService(
            new LiftAccountService(
                sfApi, liftApiReactiveService, liftUserService, liftConfig, accountsRepository),
            new LiftSitesService(
                sfApi,
                liftApiReactiveService,
                sitesRepository,
                accountsRepository,
                siteMappingsRepository),
            synchronizerFactory,
            sfApi,
            bundleMessageSource,
            new LiftContactService(sfApi),
            accountsRepository,
            sitesRepository,
            new LiftAccountMergeService(
                sfApi, liftApiReactiveService, liftUserService, liftConfig, accountsRepository),
            new MergeAccountsService(
                accountsRepository,
                sitesRepository,
                pensRepository,
                visitsRepository,
                notesRepository,
                accountService,
                mappingsRepository,
                liftSiteMappingsService));
  }

  @Test
  void executeAccountSync() throws IOException, ExecutionException, InterruptedException {
    // because of the mockwebserver response chaining this should be first
    enqueueToken();
    enqueueVersion();

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(
                    Path.of("src/test/resources/salesforce/query-simple-accounts-list-paged.json")))
            .addHeader("Content-Type", "application/json"));

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(
                    Path.of("src/test/resources/salesforce/query-simple-accounts-list.json")))
            .addHeader("Content-Type", "application/json"));
    when(userRepository.findMultipleUsersLowerCase(anyList())).thenReturn(List.of(new User()));
    CompletableFuture<SyncResult> result = service.executeAccountsSync(null);
    assertTrue(result.isDone());
    assertEquals(0, result.get().getNewRecords().get());
  }

  @Test
  void whenFixSiteCountForMergedAccountIsCalledCorrectResultIsReturned() {
    Accounts account =
        Accounts.builder()
            .accountDocument(AccountDocument.builder().id(UUID.randomUUID()).build())
            .build();
    when(accountsRepository.findAllByGoldenRecordId(any())).thenReturn(List.of(account));
    service.fixSiteCountForMergedAccount("test");
    assertNotNull(account);
  }

  @Test
  void whenAccountIsEmptyInSitesCountFix() {
    Accounts account = Accounts.builder().build();
    when(accountsRepository.findAllByGoldenRecordId(any())).thenReturn(new ArrayList<>());
    service.fixSiteCountForMergedAccount("test");
    assertNotNull(account);
  }

  @Test
  void executeAccountsMergeSync() throws IOException, ExecutionException, InterruptedException {
    // because of the mockwebserver response chaining this should be first
    enqueueToken();
    enqueueVersion();

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(
                    Path.of("src/test/resources/salesforce/Accounts_merge_object.json")))
            .addHeader("Content-Type", "application/json"));

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(
                    Path.of("src/test/resources/salesforce/Accounts_merge_object.json")))
            .addHeader("Content-Type", "application/json"));
    when(userRepository.findMultipleUsersLowerCase(anyList())).thenReturn(List.of(new User()));
    List<AccountMergeRecordDto> result = accountMergeService.getAllMergedAccounts(null);
    assertTrue(result.isEmpty());
  }

  @Test
  void executeSitesSync() throws IOException {
    // because of the mockwebserver response chaining this should be first
    enqueueToken();
    enqueueVersion();

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(
                    Path.of("src/test/resources/salesforce/query-simple-sites-list.json")))
            .addHeader("Content-Type", "application/json"));

    CompletableFuture<SyncResult> result = service.executeSitesSync(null);
    assertTrue(result.isDone());
  }

  private void enqueueToken() {
    when(config.getTokenHost()).thenReturn("localhost");
    when(config.getTokenPath()).thenReturn("/");
    when(config.getPort()).thenReturn(String.valueOf(mockBackEnd.getPort()));
    when(config.getScheme()).thenReturn("http");

    String tokenResponse =
        "{"
            + "\"access_token\":\"fake-token\","
            + "\"instance_url\":\"http://localhost:"
            + mockBackEnd.getPort()
            + "\","
            + "\"id\":\"https://some-id.com\","
            + "\"token_type\":\"Bearer\","
            + "\"issued_at\":\""
            + Instant.now().plusSeconds(3600).toEpochMilli()
            + "\","
            + "\"signature\":\"agkg9etMNx9DsS9uXiXy6rcAModj88NmlNF6byfBMd4=\"}";
    mockBackEnd.enqueue(
        new MockResponse().setBody(tokenResponse).addHeader("Content-Type", "application/json"));
  }

  private void enqueueVersion() throws IOException {
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(Path.of("src/test/resources/salesforce/get-versions-resp.json")))
            .addHeader("Content-Type", "application/json"));
  }

  @Test
  void syncAccountsToSalesforce() throws IOException {
    List<Accounts> accounts = generateAccounts();

    enqueueToken();
    enqueueVersion();
    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(500));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(500));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(500));

    Mono<SyncResult> syncResultMono = service.syncAccountsToSalesforce(accounts);
    StepVerifier.create(syncResultMono)
        .assertNext(
            sr -> {
              assertEquals(5, sr.getModifiedRecords().get());
              assertEquals(0, sr.getFailures().get());
            })
        .verifyComplete();
  }

  @Test
  void whenItemIsDeletedItIsMarkedAsFailedAndUpdated() throws IOException {
    enqueueToken();
    enqueueVersion();
    mockBackEnd.enqueue(
        new MockResponse()
            .setResponseCode(404)
            .setBody(
                "["
                    + "    {"
                    + "        \"message\": \"entity is deleted\","
                    + "        \"errorCode\": \"ENTITY_IS_DELETED\","
                    + "        \"fields\": []"
                    + "    }"
                    + "]"));
    Mono<SyncResult> syncResultMono = service.syncAccountsToSalesforce(generateAccounts(1));
    StepVerifier.create(syncResultMono)
        .assertNext(
            sr -> {
              assertEquals(1, sr.getModifiedRecords().get());
              assertEquals(0, sr.getFailures().get());
              verify(accountsRepository).save(any());
            })
        .verifyComplete();
  }

  @Test
  void whenAccountGoldenRecordIdIsMissingItIsSkipped() throws IOException {
    List<Accounts> accounts = generateAccounts(1);
    accounts.get(0).getAccountDocument().setGoldenRecordId(null);
    enqueueToken();
    enqueueVersion();

    Mono<SyncResult> syncResultMono = service.syncAccountsToSalesforce(accounts);
    StepVerifier.create(syncResultMono)
        .assertNext(
            sr -> {
              assertEquals(0, sr.getModifiedRecords().get());
              assertEquals(0, sr.getFailures().get());
            })
        .verifyComplete();
  }

  @Test
  void syncSitesToSalesforce() throws IOException {
    List<Sites> accounts = generateSites();

    enqueueToken();
    enqueueVersion();
    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(500));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(500));
    mockBackEnd.enqueue(new MockResponse().setResponseCode(500));

    Mono<SyncResult> syncResultMono = service.syncSitesToSalesforce(accounts);
    StepVerifier.create(syncResultMono)
        .assertNext(
            sr -> {
              assertEquals(5, sr.getModifiedRecords().get());
              assertEquals(0, sr.getFailures().get());
            })
        .verifyComplete();
  }

  @Test
  void whenAccountsIsEmptyListFlowFinished() throws IOException {
    List<Accounts> accounts = new ArrayList<>();

    enqueueToken();
    enqueueVersion();

    Mono<SyncResult> syncResultMono = service.syncAccountsToSalesforce(accounts);
    StepVerifier.create(syncResultMono)
        .assertNext(
            sr -> {
              assertEquals(0, sr.getNewRecords().get());
              assertEquals(0, sr.getModifiedRecords().get());
              assertEquals(0, sr.getFailures().get());
            })
        .verifyComplete();
  }

  private List<Accounts> generateAccounts() {
    return generateAccounts(5);
  }

  private List<Accounts> generateAccounts(int size) {
    List<Accounts> accounts = new ArrayList<>();
    for (int i = 0; i < size; i++) {
      Address address = new Address();
      address.setCountry("TestCountry");
      accounts.add(
          Accounts.builder()
              .id((long) i)
              .accountDocument(
                  AccountDocument.builder()
                      .id(UUID.randomUUID())
                      .goldenRecordId(String.valueOf(i))
                      .accountType(0)
                      .physicalAddress(address)
                      .build())
              .build());
    }
    return accounts;
  }

  private List<Sites> generateSites() {
    List<Sites> sites = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      sites.add(
          Sites.builder()
              .id((long) i)
              .siteDocument(
                  SiteDocument.builder()
                      .id(UUID.randomUUID())
                      .siteName("site-name")
                      .milkingSystemType(MilkingSystem.Parlor)
                      .externalId("1234")
                      .build())
              .build());
    }
    return sites;
  }
}
