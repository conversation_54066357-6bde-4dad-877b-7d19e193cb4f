/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DataSourceMappingServiceImplTest {

  @Mock private SitesRepository sitesRepository;
  @Mock private SiteMappingsRepository siteMappingsRepository;
  @InjectMocks private SiteMappingServiceImpl siteMappingServiceImpl;

  @Test
  void whenSiteMappingIsNotNull() {
    SiteDocument siteDocument =
        SiteDocument.builder()
            .dataSourceMappings(
                List.of(
                    DataSourceMapping.builder()
                        .systemId(UUID.randomUUID().toString())
                        .systemName("LM_SITE")
                        .build(),
                    DataSourceMapping.builder()
                        .systemId(UUID.randomUUID().toString())
                        .systemName("MAX")
                        .build(),
                    DataSourceMapping.builder()
                        .systemId(UUID.randomUUID().toString())
                        .systemName("DDW")
                        .build()))
            .accountId(UUID.randomUUID())
            .build();
    Sites site =
        Sites.builder()
            .createdDate(Date.from(Instant.now()))
            .localId(UUID.randomUUID().toString())
            .siteDocument(siteDocument)
            .build();
    when(siteMappingsRepository.findAllByLabyrinthAccountId(any())).thenReturn(loadSiteMappings());
    when(siteMappingsRepository.save(any())).thenReturn(loadSiteMappings().get(0));
    siteMappingServiceImpl.updateSiteMapping(site);
    assertFalse(site.getSiteDocument().getDataSourceMappings().isEmpty());
  }

  private List<SiteMappings> loadSiteMappings() {
    SiteMappingDocument siteMappingDocument =
        SiteMappingDocument.builder()
            .ddwHerdId("123")
            .labyrinthAccountId(UUID.randomUUID())
            .labyrinthSiteId(UUID.randomUUID())
            .maxSiteId(UUID.randomUUID())
            .build();
    List<SiteMappings> siteMappingsList = new ArrayList<>();
    siteMappingsList.add(
        SiteMappings.builder()
            .siteMappingDocument(siteMappingDocument)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .build());
    return siteMappingsList;
  }

  @Test
  void whenSiteMappingIsNull() {
    SiteDocument siteDocument =
        SiteDocument.builder().dataSourceMappings(null).accountId(UUID.randomUUID()).build();
    Sites site =
        Sites.builder()
            .createdDate(Date.from(Instant.now()))
            .localId(UUID.randomUUID().toString())
            .siteDocument(siteDocument)
            .build();
    siteMappingServiceImpl.updateSiteMapping(site);
    assertNull(site.getSiteDocument().getDataSourceMappings());
  }

  @Test
  void whenCreateToSiteMappingIsCalledForExistingSiteMapping() {
    when(siteMappingsRepository.findBySiteIdAndIsDeleted(any()))
        .thenReturn(loadSiteMappings().get(0));
    when(siteMappingsRepository.save(any())).thenReturn(loadSiteMappings().get(0));
    SiteDocument siteDocument =
        SiteDocument.builder()
            .id(UUID.randomUUID())
            .dataSourceMappings(null)
            .accountId(UUID.randomUUID())
            .build();
    Sites site =
        Sites.builder()
            .createdDate(Date.from(Instant.now()))
            .localId(UUID.randomUUID().toString())
            .siteDocument(siteDocument)
            .build();
    siteMappingServiceImpl.createLiftSiteMapping(site);
    assertNotNull(loadSiteMappings().get(0));
  }

  @Test
  void whenCreateToSiteMappingIsCalledForNewSiteMapping() {
    when(siteMappingsRepository.findBySiteIdAndIsDeleted(any())).thenReturn(null);
    when(siteMappingsRepository.save(any())).thenReturn(loadSiteMappings().get(0));
    SiteDocument siteDocument =
        SiteDocument.builder()
            .id(UUID.randomUUID())
            .dataSourceMappings(null)
            .accountId(UUID.randomUUID())
            .build();
    Sites site =
        Sites.builder()
            .createdDate(Date.from(Instant.now()))
            .localId(UUID.randomUUID().toString())
            .siteDocument(siteDocument)
            .build();
    siteMappingServiceImpl.createLiftSiteMapping(site);
    assertNotNull(loadSiteMappings().get(0));
  }
}
