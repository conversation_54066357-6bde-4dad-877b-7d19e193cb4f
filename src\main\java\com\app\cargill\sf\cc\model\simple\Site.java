/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.app.cargill.sf.cc.model.AccountRecord;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.PersonRecord;
import com.app.cargill.sf.cc.model.RecordAttributes;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class Site implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Site_Name__c")
  private String siteName;

  @JsonProperty("IsDeleted")
  private boolean deleted;

  @JsonProperty("LM_Site_ID__c")
  private String externalId;

  @JsonProperty("Housing_Type__c")
  private String housingType;

  @JsonProperty("CreatedDate")
  private String createdDate;

  @JsonProperty("LastModifiedDate")
  private String lastModifiedDate;

  @JsonProperty("Number_of_Animals__c")
  private Integer numberOfAnimals;

  @JsonProperty("Current_Milk_Price__c")
  private Double currentMilkPrice;

  @JsonProperty("Sub_Species__c")
  private String subSpecies;

  @JsonProperty("External_Data_Sources__r")
  private SalesforceRecordsResponse<ExternalDataSource> externalDataSources;

  @JsonProperty("Account__r")
  private AccountRecord accountRecord;

  @JsonProperty("CreatedBy")
  private PersonRecord createdBy;

  @JsonProperty("LastModifiedBy")
  private PersonRecord modifiedBy;

  @JsonProperty("DE_Herd_Status_Report_Latest__c")
  private String herdStatusReport;

  @JsonProperty("DE_Herd_Summary_Report_Latest__c")
  private String herdSummaryReport;
}
