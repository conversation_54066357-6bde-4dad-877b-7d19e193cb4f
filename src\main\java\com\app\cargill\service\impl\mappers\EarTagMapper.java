/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.dto.EarTagDto;
import com.app.cargill.model.EarTags;

public class EarTagMapper {

  private EarTagMapper() {}

  public static EarTagDto modelToDto(EarTags earTag) {
    return EarTagDto.builder()
        .accountId(earTag.getEarTagDocument().getAccountId())
        .earTagName(earTag.getEarTagDocument().getEarTagName())
        .id(earTag.getEarTagDocument().getId())
        .localId(earTag.getLocalId())
        .isDeleted(earTag.isDeleted())
        .siteId(earTag.getEarTagDocument().getSiteId())
        .build();
  }
}
