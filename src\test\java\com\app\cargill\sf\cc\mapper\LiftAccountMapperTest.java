/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.sf.cc.model.AccountMergeRecord;
import com.app.cargill.sf.cc.model.UserReferenceRecord;
import com.app.cargill.sf.cc.model.simple.Account;
import org.junit.jupiter.api.Test;

class LiftAccountMapperTest {

  @Test
  void whenOwnerIsIntegrationUserCorrectMailIsReturned() {
    UserReferenceRecord userReferenceRecord = new UserReferenceRecord();
    userReferenceRecord.setEmail("<EMAIL>");
    userReferenceRecord.setUsername("<EMAIL>");

    Account account = new Account();

    account.setOwner(userReferenceRecord);
    account.setLastModifiedBy(userReferenceRecord);
    account.setCreatedBy(userReferenceRecord);

    AccountDocument result = LiftAccountMapper.transform(account);
    assertEquals("<EMAIL>", result.getOwnerId());
    assertEquals("<EMAIL>", result.getLastModifiedBy());
  }

  @Test
  void whenSourceAndTargetIdsAreMappedCorrectResultIsReturned() {

    AccountMergeRecord accountMergeRecord = new AccountMergeRecord();

    accountMergeRecord.setSourceId("test");
    accountMergeRecord.setTargetId("test2");

    AccountMergeRecordDto result = LiftAccountMapper.transformMergedAccounts(accountMergeRecord);
    assertEquals("test", result.getSourceId());
    assertEquals("test2", result.getTargerId());
  }
}
