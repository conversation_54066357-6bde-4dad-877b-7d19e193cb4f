/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import com.app.cargill.constants.AccountType;
import com.app.cargill.constants.Business;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.sf.cc.model.AccountMergeRecord;
import com.app.cargill.sf.cc.model.simple.Account;
import com.app.cargill.sf.cc.model.simple.ContactSalesforce;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LiftAccountMapper {

  private LiftAccountMapper() {}

  public static AccountDocument transform(Account input) {
    try {
      return innerTransform(input);
    } catch (Exception e) {
      log.error("Cannot transform Account", e);
      throw new IllegalArgumentException("Account transformation error");
    }
  }

  private static AccountDocument innerTransform(Account input) {

    AccountDocument account = new AccountDocument();
    try {
      account.setId(
          input.getAccountExternalId() != null
              ? UUID.fromString(input.getAccountExternalId())
              : null);
    } catch (IllegalArgumentException e) {
      log.error("Illegal external UUID for account with golden record id of: {}", input.getId());
    }

    account.setAccountStatus(input.getReviewStatus());
    account.setDataSource(DataSource.LIFT);
    account.setGoldenRecordId(input.getId());
    account.setAccountName(input.getName());
    account.setLegalName(input.getName());
    account.setPhysicalAddress(LiftAddressMapper.transform(input.getBillingAddress()));
    account.setAccountType(
        input.getCustomerAccountType() != null
            ? AccountType.valueOf(input.getCustomerAccountType()).getAccountTypeValue()
            : null);
    account.setSegmentStepOneId(input.getSegmentStepOneId());
    account.setNineBoxStepTwoID(input.getNineBoxStepTwoId());
    account.setSourceSystem(input.getSourceSystem());
    account.setSubTypeID(
        input.getSubTypeId() != null
            ? SubTypeId.fromString(input.getSubTypeId().replaceAll("\\s+", ""))
            : null);
    account.setBusinessID(
        input.getBusinessId() != null
            ? Business.valueOf(input.getBusinessId()).getBusinessId()
            : null);
    account.setParentAccountID(input.getParentId());
    account.setExternalParentAccountID(
        input.getExternalParentAccountId() != null
            ? UUID.fromString(input.getExternalParentAccountId())
            : null);
    account.setOwnerId(input.getOwner().getEmail());
    account.setOwnerProfileNameandId(
        input.getOwner().getProfile() != null
            ? String.format(
                "%s %s",
                input.getOwner().getProfile().getName(), input.getOwner().getProfile().getId())
            : null);
    account.setAccountValidated("Completed".equals(input.getReviewStatus()));
    account.setWebSiteAddress(input.getWebsite());
    account.setActive(input.isVisible());
    account.setCompanyEmail(input.getEmail());
    account.setPhone(input.getPhone());
    account.setAccountCurrency(
        input.getAccountCurrency() != null
            ? Currencies.valueOf(input.getAccountCurrency()).getValue()
            : null);
    account.setAccountNumber(input.getCustomerNumber());
    account.setNewAccountType(
        input.getNewAccountType() != null && input.getNewAccountType().equals("Commercial")
            ? AccountType.Customer
            : AccountType.Prospect);

    if (input.getLastModifiedBy() == null) {
      log.warn("Account Last Modified By Empty {}", input.getId());
    } else {
      account.setLastModifiedBy(input.getLastModifiedBy().getEmail());
    }
    account.setLastModifiedTimeUtc(LiftTimestampParser.parse(input.getLastModifiedDate()));

    account.setDescription(input.getDescription());
    account.setCustomerCode(input.getCustomerNumber());
    account.setCreatedBy(input.getCreatedBy().getEmail());

    if (input.getContacts() != null && input.getContacts().getRecords() != null) {

      account.setContacts(validateContacts(input));
    }
    account.setUsers(LiftAccountUsersMapper.transformToUsers(input));
    account.setUserRoles(LiftAccountUsersMapper.transformToUserRoles(input));
    account.setApplicationMappings(
        input.getExternalDataSources() != null
            ? input.getExternalDataSources().getRecords()
            : new ArrayList<>());

    return account;
  }

  private static List<Contact> validateContacts(Account input) {
    List<ContactSalesforce> safeContacts = new ArrayList<>();
    for (ContactSalesforce contactSalesforce : input.getContacts().getRecords()) {
      if (contactSalesforce.getAccount().getExternalId() != null) {
        safeContacts.add(contactSalesforce);
      }
    }
    return LiftAccountContactsMapper.transform(safeContacts);
  }

  public static AccountMergeRecordDto transformMergedAccounts(AccountMergeRecord input) {
    try {
      return innerTransformMergedAccounts(input);
    } catch (Exception e) {
      log.error("Cannot transform Account", e);
      throw new IllegalArgumentException("Account transformation error");
    }
  }

  private static AccountMergeRecordDto innerTransformMergedAccounts(AccountMergeRecord input) {

    return AccountMergeRecordDto.builder()
        .sourceId(input.getSourceId())
        .targerId(input.getTargetId())
        .build();
  }
}
