/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.sf.cc.model.simple.Site;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftSiteMappingMapperTest {
  private final ObjectMapper objectMapper = new ObjectMapper();

  @Test
  void test() throws IOException {
    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Site liftSite =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_site.json"), Site.class);
    SiteDocument mappedSite = LiftSiteMapper.transform(liftSite);

    Optional<DataSourceMapping> ddwMapping =
        mappedSite.getDataSourceMappings().stream()
            .filter(dataSourceMapping -> dataSourceMapping.getSystemName().equals("DDW"))
            .findAny();

    assertTrue(ddwMapping.isPresent());
    assertEquals("19166", ddwMapping.get().getSystemId());
  }
}
