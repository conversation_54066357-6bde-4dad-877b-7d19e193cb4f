/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RoboticMilkEvaluationReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String categoryLabel;
  private DualYAxisGraphDto dualYaxisGraph;
  private SingleYAxisGraphDto singleYaxisGraph;
}
