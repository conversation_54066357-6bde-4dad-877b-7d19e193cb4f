/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.*;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BCSCalculationTest {

  @InjectMocks private BCSCalculation bcsCalculation;

  @Test
  void calculateBCSFields() {

    BodyConditionTool bcTool =
        BodyConditionTool.builder()
            .selectedPointScale(BCSPointScale.HalfPointScale)
            .pens(
                List.of(
                    BodyConditionToolItem.builder()
                        .bodyConditionScores(
                            List.of(
                                BodyConditionToolItemScoreItem.builder()
                                    .animalsObserved(10)
                                    .bcsCategory(2.0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .animalsObserved(20)
                                    .bcsCategory(4.0)
                                    .build()))
                        .build()))
            .build();

    bcTool = bcsCalculation.calculateFields(bcTool);
    assertTrue(
        bcTool.getPens().get(0).getBodyConditionScores().stream()
            .allMatch(s -> s.getPercentOfPen() != null && s.getSelectedPointScale() != null));
    assertNotNull(bcTool.getPens().get(0).getStandardDeviationBCS());
    assertNotNull(bcTool.getPens().get(0).getAverageBCS());
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyCalculations() {
    BodyConditionTool bcsTool =
        BodyConditionTool.builder()
            .pens(
                List.of(
                    BodyConditionToolItem.builder()
                        .bodyConditionScores(
                            List.of(
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(1.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(1.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(2.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(2.5)
                                    .animalsObserved(2)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(3.0)
                                    .animalsObserved(5)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(3.5)
                                    .animalsObserved(2)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(4.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(4.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(5.0)
                                    .animalsObserved(0)
                                    .build()))
                        .toolStatus(ToolStatuses.Completed)
                        .build(),
                    BodyConditionToolItem.builder()
                        .bodyConditionScores(
                            List.of(
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(1.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(1.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(2.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(2.5)
                                    .animalsObserved(2)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(3.0)
                                    .animalsObserved(3)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(3.5)
                                    .animalsObserved(4)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(4.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(4.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(5.0)
                                    .animalsObserved(0)
                                    .build()))
                        .toolStatus(ToolStatuses.Completed)
                        .build(),
                    BodyConditionToolItem.builder()
                        .bodyConditionScores(
                            List.of(
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(1.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(1.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(2.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(2.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(3.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(3.5)
                                    .animalsObserved(2)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(4.0)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(4.5)
                                    .animalsObserved(0)
                                    .build(),
                                BodyConditionToolItemScoreItem.builder()
                                    .bcsCategory(5.0)
                                    .animalsObserved(0)
                                    .build()))
                        .toolStatus(ToolStatuses.Completed)
                        .build()))
            .build();

    bcsTool = bcsCalculation.calculateFields(bcsTool);

    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(0).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(1).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(2).getPercentOfPen());
    assertEquals(22.2, bcsTool.getPens().get(0).getBodyConditionScores().get(3).getPercentOfPen());
    assertEquals(55.6, bcsTool.getPens().get(0).getBodyConditionScores().get(4).getPercentOfPen());
    assertEquals(22.2, bcsTool.getPens().get(0).getBodyConditionScores().get(5).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(6).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(7).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(8).getPercentOfPen());

    assertEquals(3.0, bcsTool.getPens().get(0).getAverageBCS());
    assertEquals(0.33, bcsTool.getPens().get(0).getStandardDeviationBCS());

    assertEquals(0.0, bcsTool.getPens().get(1).getBodyConditionScores().get(0).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(1).getBodyConditionScores().get(1).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(1).getBodyConditionScores().get(2).getPercentOfPen());
    assertEquals(22.2, bcsTool.getPens().get(1).getBodyConditionScores().get(3).getPercentOfPen());
    assertEquals(33.3, bcsTool.getPens().get(1).getBodyConditionScores().get(4).getPercentOfPen());
    assertEquals(44.4, bcsTool.getPens().get(1).getBodyConditionScores().get(5).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(1).getBodyConditionScores().get(6).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(1).getBodyConditionScores().get(7).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(1).getBodyConditionScores().get(8).getPercentOfPen());

    assertEquals(3.11, bcsTool.getPens().get(1).getAverageBCS());
    assertEquals(0.39, bcsTool.getPens().get(1).getStandardDeviationBCS());

    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(0).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(1).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(2).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(3).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(4).getPercentOfPen());
    assertEquals(100.0, bcsTool.getPens().get(2).getBodyConditionScores().get(5).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(6).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(7).getPercentOfPen());
    assertEquals(0.0, bcsTool.getPens().get(2).getBodyConditionScores().get(8).getPercentOfPen());

    assertEquals(3.5, bcsTool.getPens().get(2).getAverageBCS());
    assertEquals(0.0, bcsTool.getPens().get(2).getStandardDeviationBCS());
  }

  @Test
  void calculateBCSWithNull() {
    BodyConditionTool bcsTool =
        BodyConditionTool.builder()
            .pens(
                List.of(
                    BodyConditionToolItem.builder()
                        .bodyConditionScores(
                            List.of(BodyConditionToolItemScoreItem.builder().build()))
                        .toolStatus(ToolStatuses.Completed)
                        .build()))
            .build();

    bcsTool = bcsCalculation.calculateFields(bcsTool);

    assertEquals(0.0, bcsTool.getPens().get(0).getBodyConditionScores().get(0).getPercentOfPen());
    Assertions.assertNull(
        bcsTool.getPens().get(0).getBodyConditionScores().get(0).getSelectedPointScale());
    Assertions.assertEquals(0.0, bcsTool.getPens().get(0).getAverageBCS());
    Assertions.assertEquals(0.0, bcsTool.getPens().get(0).getStandardDeviationBCS());
  }
}
