/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers.cdp;

import static com.app.cargill.service.impl.mappers.cdp.CdpAccountDocumentMapper.getAccountCurrency;
import static com.app.cargill.service.impl.mappers.cdp.CdpAccountDocumentMapper.getBusinessUnitCountry;

import com.app.cargill.constants.Currencies;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

@SuppressWarnings("java:S5961") // Multiple test cases required
class CdpAccountDocumentMapperTest {

  @Test
  void testMapToDto() {
    // Arrange
    Assertions.assertThrows(
        IllegalArgumentException.class,
        () -> {
          CdpAccountDocumentMapper.mapToDto(null);
        });

    AccountDocument accountDocument = new AccountDocument();

    // Set other properties with sample values
    accountDocument.setGoldenRecordId("GR-123");
    accountDocument.setAccountName("Test Account");
    accountDocument.setBusinessID(15);
    accountDocument.setAccountCurrency(1);
    String businessUnitCountry = getBusinessUnitCountry(accountDocument);

    // Act
    AccountDocumentDTO accountDocumentDTO = CdpAccountDocumentMapper.mapToDto(accountDocument);

    // Assert
    Assertions.assertEquals("GR-123", accountDocumentDTO.getGoldenRecordId());
    Assertions.assertEquals("Test Account", accountDocumentDTO.getAccountName());
    // Assert other properties with their expected values

    // Assert properties with null values
    Assertions.assertNotNull(accountDocumentDTO.getUserRoles());

    // Assert remaining properties with their expected values
    Assertions.assertEquals(accountDocument.getLegalName(), accountDocumentDTO.getLegalName());
    Assertions.assertEquals(accountDocument.getAccountType(), accountDocumentDTO.getAccountType());
    Assertions.assertEquals(accountDocument.getContacts(), accountDocumentDTO.getContacts());
    Assertions.assertEquals(accountDocument.getUsers(), accountDocumentDTO.getUsers());
    Assertions.assertEquals(accountDocument.getOwnerId(), accountDocumentDTO.getOwnerId());
    Assertions.assertEquals(accountDocument.getIsDuplicate(), accountDocumentDTO.getIsDuplicate());
    Assertions.assertEquals(
        accountDocument.getAutoValidate(), accountDocumentDTO.getAutoValidate());
    Assertions.assertEquals(
        accountDocument.getSourceSystem(), accountDocumentDTO.getSourceSystem());
    Assertions.assertEquals(accountDocument.getType(), accountDocumentDTO.getType());
    Assertions.assertEquals(
        accountDocument.getSocialMediaAddress(), accountDocumentDTO.getSocialMediaAddress());
    Assertions.assertEquals(
        accountDocument.getWebSiteAddress(), accountDocumentDTO.getWebSiteAddress());
    Assertions.assertEquals(
        accountDocument.getParentAccountID(), accountDocumentDTO.getParentAccountID());
    Assertions.assertEquals(
        accountDocument.getExternalParentAccountID(),
        accountDocumentDTO.getExternalParentAccountID());
    Assertions.assertEquals(
        accountDocument.getBuyingGroupID(), accountDocumentDTO.getBuyingGroupID());
    Assertions.assertEquals(
        accountDocument.getExternalBuyingGroupID(), accountDocumentDTO.getExternalBuyingGroupID());
    Assertions.assertEquals(accountDocument.getSubTypeID(), accountDocumentDTO.getSubTypeID());
    Assertions.assertEquals(
        accountDocument.getExternalLeadSourceID(), accountDocumentDTO.getExternalLeadSourceID());
    Assertions.assertEquals(
        accountDocument.getAccountStatus(), accountDocumentDTO.getAccountStatus());
    Assertions.assertEquals(
        accountDocument.getDateOfLastVisit(), accountDocumentDTO.getDateOfLastVisit());
    Assertions.assertEquals(
        accountDocument.getDateOfLastCall(), accountDocumentDTO.getDateOfLastCall());
    Assertions.assertEquals(accountDocument.getWonLostId(), accountDocumentDTO.getWonLostId());
    Assertions.assertEquals(
        accountDocument.getWonLostComments(), accountDocumentDTO.getWonLostComments());
    Assertions.assertEquals(accountDocument.getCreditFlag(), accountDocumentDTO.getCreditFlag());
    Assertions.assertEquals(accountDocument.getPriceFlag(), accountDocumentDTO.getPriceFlag());
    Assertions.assertEquals(accountDocument.getServiceFlag(), accountDocumentDTO.getServiceFlag());
    Assertions.assertEquals(
        accountDocument.getPerformanceFlag(), accountDocumentDTO.getPerformanceFlag());
    Assertions.assertEquals(
        accountDocument.getBusinessSolutionFlag(), accountDocumentDTO.getBusinessSolutionFlag());
    Assertions.assertEquals(accountDocument.getQualityFlag(), accountDocumentDTO.getQualityFlag());
    Assertions.assertEquals(accountDocument.getOtherFlag(), accountDocumentDTO.getOtherFlag());
    Assertions.assertEquals("South Africa", businessUnitCountry);
    Assertions.assertEquals(
        accountDocument.getDefaultCargillPlantID(), accountDocumentDTO.getDefaultCargillPlantID());
    Assertions.assertEquals(
        accountDocument.getDefaultCustServiceID(), accountDocumentDTO.getDefaultCustServiceID());
    Assertions.assertEquals(
        accountDocument.getLastModificationDate(), accountDocumentDTO.getLastModificationDate());
    Assertions.assertEquals(accountDocument.getActive(), accountDocumentDTO.getActive());
    Assertions.assertEquals(accountDocument.getBrandId(), accountDocumentDTO.getBrandId());
    Assertions.assertEquals(
        accountDocument.getNineBoxStepTwoID(), accountDocumentDTO.getNineBoxStepTwoID());
    Assertions.assertEquals(
        accountDocument.getSegmentStepOneId(), accountDocumentDTO.getSegmentStepOneId());
    Assertions.assertEquals(
        accountDocument.getCompanyEmail(), accountDocumentDTO.getCompanyEmail());
    Assertions.assertEquals(
        accountDocument.getLastInvoiceDate(), accountDocumentDTO.getLastInvoiceDate());
    Assertions.assertEquals(
        accountDocument.getLastOrderDate(), accountDocumentDTO.getLastOrderDate());
    Assertions.assertEquals(
        accountDocument.getDeliveryInstructions(), accountDocumentDTO.getDeliveryInstructions());
    Assertions.assertEquals(accountDocument.getErpPayerId(), accountDocumentDTO.getErpPayerId());
    Assertions.assertEquals(accountDocument.getErpShipToId(), accountDocumentDTO.getErpShipToId());
    Assertions.assertEquals(
        accountDocument.getIsServicedbyCSPro(), accountDocumentDTO.getIsServicedbyCSPro());
    Assertions.assertEquals(
        accountDocument.getLastAdminUpdate(), accountDocumentDTO.getLastAdminUpdate());
    Assertions.assertEquals(
        accountDocument.getLastInvoicesInfo(), accountDocumentDTO.getLastInvoicesInfo());
    Assertions.assertEquals(
        accountDocument.getLastOrdersInfo(), accountDocumentDTO.getLastOrdersInfo());
    Assertions.assertEquals(accountDocument.getPhone(), accountDocumentDTO.getPhone());
    Assertions.assertEquals(
        accountDocument.getReqProcessingLog(), accountDocumentDTO.getReqProcessingLog());
    Assertions.assertEquals(accountDocument.getLiabilities(), accountDocumentDTO.getLiabilities());
    Assertions.assertEquals(
        accountDocument.getLimitChangeReasonId(), accountDocumentDTO.getLimitChangeReasonId());
    Assertions.assertEquals(
        accountDocument.getMarketInfluencer(), accountDocumentDTO.getMarketInfluencer());
    Assertions.assertEquals(
        accountDocument.getOtherActivityProduction(),
        accountDocumentDTO.getOtherActivityProduction());
    Assertions.assertEquals(accountDocument.getPersonalID(), accountDocumentDTO.getPersonalID());
    Assertions.assertEquals(
        accountDocument.getPreviousStatus(), accountDocumentDTO.getPreviousStatus());
    Assertions.assertEquals(
        accountDocument.getReasonDescription(), accountDocumentDTO.getReasonDescription());
    Assertions.assertEquals(accountDocument.getSecurities(), accountDocumentDTO.getSecurities());
    Assertions.assertEquals(
        accountDocument.getApprovalStatus(), accountDocumentDTO.getApprovalStatus());
    Assertions.assertEquals(accountDocument.getAssets(), accountDocumentDTO.getAssets());
    Assertions.assertEquals(
        accountDocument.getVolumeEstimate(), accountDocumentDTO.getVolumeEstimate());
    Assertions.assertEquals(
        accountDocument.getMarginEstimate(), accountDocumentDTO.getMarginEstimate());
    Assertions.assertEquals(
        accountDocument.getPhysicalAddress(), accountDocumentDTO.getPhysicalAddress());
    Assertions.assertEquals(
        accountDocument.getCorrespondenceAddress(), accountDocumentDTO.getCorrespondenceAddress());
    Assertions.assertEquals(
        accountDocument.getAdditionalInfo(), accountDocumentDTO.getAdditionalInfo());
    Assertions.assertEquals(accountDocument.getLstOtherBU(), accountDocumentDTO.getLstOtherBU());
    Assertions.assertEquals(Currencies.Euro, getAccountCurrency(accountDocument));
    Assertions.assertEquals(
        accountDocument.getAccountNumber(), accountDocumentDTO.getAccountNumber());
    Assertions.assertEquals(
        accountDocument.getAvailabilityOnMarket(), accountDocumentDTO.getAvailabilityOnMarket());
    Assertions.assertEquals(
        accountDocument.getChangeAccountType(), accountDocumentDTO.getChangeAccountType());
    Assertions.assertEquals(
        accountDocument.getNewAccountType(), accountDocumentDTO.getNewAccountType());
    Assertions.assertEquals(
        accountDocument.getConsumerStatus(), accountDocumentDTO.getConsumerStatus());
    Assertions.assertEquals(accountDocument.getCourtId(), accountDocumentDTO.getCourtId());
    Assertions.assertEquals(
        accountDocument.getCustomerStatus(), accountDocumentDTO.getCustomerStatus());
    Assertions.assertEquals(accountDocument.getErpIdLength(), accountDocumentDTO.getErpIdLength());
    Assertions.assertEquals(
        accountDocument.getErpPayerIdLength(), accountDocumentDTO.getErpPayerIdLength());
    Assertions.assertEquals(
        accountDocument.getErpShiptoIdLength(), accountDocumentDTO.getErpShiptoIdLength());
    Assertions.assertEquals(
        accountDocument.getIsMobileFirst(), accountDocumentDTO.getIsMobileFirst());
    Assertions.assertEquals(
        accountDocument.getVeterinaryId(), accountDocumentDTO.getVeterinaryId());
    Assertions.assertEquals(
        accountDocument.getWonLostReasonCode(), accountDocumentDTO.getWonLostReasonCode());
    Assertions.assertEquals(accountDocument.getSubBrandId(), accountDocumentDTO.getSubBrandId());
    Assertions.assertEquals(
        accountDocument.getCurrentUserProfileNameandId(),
        accountDocumentDTO.getCurrentUserProfileNameandId());
    Assertions.assertEquals(
        accountDocument.getLastModifiedBy(), accountDocumentDTO.getLastModifiedBy());
    Assertions.assertEquals(
        accountDocument.getOwnerProfileNameandId(), accountDocumentDTO.getOwnerProfileNameandId());
    Assertions.assertEquals(accountDocument.getDescription(), accountDocumentDTO.getDescription());
    Assertions.assertEquals(
        accountDocument.getSalesTerritory(), accountDocumentDTO.getSalesTerritory());
    Assertions.assertEquals(
        accountDocument.getCustomerCode(), accountDocumentDTO.getCustomerCode());
    Assertions.assertEquals(accountDocument.getUserRoles(), accountDocumentDTO.getUserRoles());
    Assertions.assertEquals(accountDocument.getId(), accountDocumentDTO.getId());
    Assertions.assertEquals(accountDocument.getCreateUser(), accountDocumentDTO.getCreateUser());
    Assertions.assertEquals(accountDocument.isDeleted(), accountDocumentDTO.isDeleted());
    Assertions.assertEquals(
        accountDocument.getLastModifyUser(), accountDocumentDTO.getLastModifyUser());
    Assertions.assertEquals(
        accountDocument.getCreateTimeUtc(), accountDocumentDTO.getCreateTimeUtc());
    Assertions.assertEquals(
        accountDocument.getLastModifiedTimeUtc(), accountDocumentDTO.getLastModifiedTimeUtc());
    Assertions.assertEquals(
        accountDocument.getLastSyncTimeUtc(), accountDocumentDTO.getLastSyncTimeUtc());
    Assertions.assertEquals(accountDocument.isNew(), accountDocumentDTO.isNew());

    accountDocument.setSourceSystem("LM");
    Assertions.assertEquals(Currencies.NotSet, getAccountCurrency(accountDocument));
  }
}
