<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="035" author="Taha">
    <sql>
   INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.023', false, 'ee3c4f87-6ac9-4010-9293-c4b340b7f23c', '2023-01-15 11:39:46.023', '{"id": "d4f7e89c-4349-4c72-9b07-f9fdefc32723", "IsNew": false, "ToolId": "RumenHealth", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.101', false, '2de1b5a3-8f97-460b-815d-8a4c92ddfcfb', '2023-01-15 11:39:46.101', '{"id": "3a71a822-8e10-45de-b0f8-36f22b8c09d6", "IsNew": false, "ToolId": "LocomotionScore", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.953826500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.115', false, 'b6f4225f-c1b4-47b6-b3d1-90ba7dca18f5', '2023-01-15 11:39:46.115', '{"id": "b41f2b6a-1272-455f-8182-d92e8f565689", "IsNew": false, "ToolId": "BodyCondition", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.969489800Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-04-12 12:14:39.563', false, 'b1cb4b3d-36e4-4f7b-9f52-c915de8932af', '2023-04-12 12:14:39.563', '{"id": "5177a98a-1d33-4c0b-9b92-c8fce15b3742", "IsNew": true, "ToolId": "ForagePennState", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:31:45.121003600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:31:45Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.008', false, 'a58f1b95-876d-44a6-a25d-d4af9db90a44', '2023-01-15 11:39:46.008', '{"id": "67c96161-489a-4ab5-b42f-295f02e61df4", "IsNew": false, "ToolId": "HeatStress", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2021-11-11T03:30:29.875713900Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.018', false, '529b5985-ea03-4d66-8674-8a87c25da11f', '2023-01-15 11:39:46.018', '{"id": "61e2e8ad-4478-4e0b-bbdf-0a27abfbc82f", "IsNew": false, "ToolId": "PenTimeBudgetTool", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2021-11-11T03:30:29.891411Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.058', false, 'b3073702-77d0-4980-aeda-f1ec3bb3086b', '2023-01-15 11:39:46.058', '{"id": "21e40f28-09c4-489e-a9d0-94b4bc2cc448", "IsNew": false, "ToolId": "ManureScreener", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.938249600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.036', false, '7ff2d96a-31b5-4c47-a125-2604ffdfda99', '2023-01-15 11:39:46.036', '{"id": "6f742d3a-8b2a-46cc-8537-c27d666fb1d0", "IsNew": false, "ToolId": "TMRParticleScore", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:30:29.922599300Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.090', false, '6c5e94a1-89d2-411f-a0ee-568f64657f55', '2023-01-15 11:39:46.090', '{"id": "bb59e6ac-14e8-45fc-80e2-c7390a1c7957", "IsNew": false, "ToolId": "RumenFill", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.953826500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.048', false, '2c83ed1d-6d66-42f4-87f6-605a8a8b211e', '2023-01-15 11:39:46.048', '{"id": "5748b24e-33f6-4317-914d-7ab9ab7cf009", "IsNew": false, "ToolId": "RumenHealthManureScore", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.922599300Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.144', false, 'a2129443-6bc6-419a-8e12-48ffb1a2d8f5', '2023-01-15 11:39:46.144', '{"id": "00a3fba7-a004-4b60-8e4b-f83d238e825b", "IsNew": false, "ToolId": "MetabolicIncidence", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.985091900Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.220', false, '1ba7e57f-457f-4cf1-88c8-91b5b62283f9', '2023-01-15 11:39:46.220', '{"id": "a3701497-c845-45be-8260-1a1f1ac8eb21", "IsNew": false, "ToolId": "MilkSoldEvaluation", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2021-11-11T03:30:30.031972600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.237', false, '6f8e8c22-ec5c-47ef-bc88-41a3e4054d8c', '2023-01-15 11:39:46.237', '{"id": "aa381b27-74a1-4e57-911e-8886e9c1d091", "IsNew": false, "ToolId": "RoboticMilkEvaluation", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2021-11-11T03:30:30.047580300Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.185', false, '76b4d17d-4a2a-4adf-80bc-b8d4ad8be60d', '2023-01-15 11:39:46.185', '{"id": "b54ae38d-83ff-4fa1-9b50-4e4f7a2a550d", "IsNew": false, "ToolId": "PileAndBunker", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:30:30.016331500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);

INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.171', false, '157f0323-01c4-4113-89a7-e3edb9ed2cd7', '2023-01-15 11:39:46.171', '{"id": "a5cb1b49-0bdf-4391-bd61-109cb010b1a6", "IsNew": false, "ToolId": "ForageAuditScorecard", "CountryId": "Mexico", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:30:30.000718Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
    </sql>
  </changeSet>

</databaseChangeLog>
