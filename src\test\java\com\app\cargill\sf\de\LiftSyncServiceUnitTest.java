/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.SitesAndMappingsWrapper;
import com.app.cargill.model.Sites;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class LiftSyncServiceUnitTest {

  @Mock private LiftAccountService accountService;
  @Mock private LiftSitesService sitesService;
  @Mock private DataSynchronizerFactory synchronizerFactory;
  @Mock private LiftApiService liftApiService;
  @Mock private ResourceBundleMessageSource messageSource;
  @Mock private LiftContactService liftContactService;
  @Mock private AccountsRepository accountsRepository;
  @Mock private SitesRepository sitesRepository;

  @InjectMocks private LiftSyncService liftSyncService;

  @Test
  void missingSiteMappingSitesSyncExecutesCorrect() {

    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("lift-id");
    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
    siteMappingDocument.setId(UUID.randomUUID());
    siteMappingDocument.setLabyrinthSiteId(UUID.randomUUID());

    List<SiteDocument> siteDocuments = List.of(siteDocument);
    List<SiteMappingDocument> siteMappingDocuments = List.of(siteMappingDocument);

    SitesAndMappingsWrapper sitesAndMappingsWrapper =
        new SitesAndMappingsWrapper(siteDocuments, siteMappingDocuments);

    SyncResult syncResult = new SyncResult("TEST");
    syncResult.setNewRecords(new AtomicInteger(1));

    DataSynchronizer<SiteMappingDocument> siteMappingSynchronizer = mock(DataSynchronizer.class);
    doReturn(syncResult).when(siteMappingSynchronizer).sync(any());
    doReturn(siteMappingSynchronizer).when(synchronizerFactory).getSynchronizer(any());
    when(sitesService.getSite(any())).thenReturn(sitesAndMappingsWrapper);

    Sites dbSite = new Sites(new SiteDocument());
    when(sitesRepository.findBySiteIdUnfiltered(any())).thenReturn(dbSite);

    SyncResult result = liftSyncService.executeMissingMappingSiteSync(UUID.randomUUID().toString());
    assertEquals(1, result.getNewRecords().get());
  }

  @Test
  void whenMappingIsEmptyDoNotUpdate() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("lift-id");

    List<SiteDocument> siteDocuments = List.of(siteDocument);
    List<SiteMappingDocument> siteMappingDocuments = List.of();

    SitesAndMappingsWrapper sitesAndMappingsWrapper =
        new SitesAndMappingsWrapper(siteDocuments, siteMappingDocuments);

    when(sitesService.getSite(any())).thenReturn(sitesAndMappingsWrapper);

    SyncResult result = liftSyncService.executeMissingMappingSiteSync(UUID.randomUUID().toString());
    assertEquals(0, result.getNewRecords().get());
    assertEquals(0, result.getModifiedRecords().get());
  }

  @Test
  void whenThereIsAnErrorInPredefinedSyncItIsReturned() {
    when(sitesService.getSite(any())).thenThrow(new RuntimeException("TEST_ERROR"));

    SyncResult result = liftSyncService.executeMissingMappingSiteSync(UUID.randomUUID().toString());
    assertEquals(0, result.getNewRecords().get());
    assertEquals(0, result.getModifiedRecords().get());
    assertFalse(result.getError().isEmpty());
  }

  @Test
  void switchedMappingsSitesSyncExecutesCorrect() {

    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("lift-id");
    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
    siteMappingDocument.setId(UUID.randomUUID());
    siteMappingDocument.setLabyrinthSiteId(UUID.randomUUID());

    List<SiteDocument> siteDocuments = List.of(siteDocument);
    List<SiteMappingDocument> siteMappingDocuments = List.of(siteMappingDocument);

    SitesAndMappingsWrapper sitesAndMappingsWrapper =
        new SitesAndMappingsWrapper(siteDocuments, siteMappingDocuments);

    SyncResult syncResult = new SyncResult("TEST");
    syncResult.setNewRecords(new AtomicInteger(1));

    when(sitesService.getSite(any())).thenReturn(sitesAndMappingsWrapper);

    Sites dbSite = new Sites(new SiteDocument());
    when(sitesRepository.findBySiteIdUnfiltered(any())).thenReturn(dbSite);

    SyncResult result = liftSyncService.fixSwitchedSiteMappings(UUID.randomUUID().toString());
    assertEquals(1, result.getModifiedRecords().get());
  }

  @Test
  void whenThereIsAnErrorInSwitchedMappingsSyncPredefinedSyncIsReturned() {
    when(sitesService.getSite(any())).thenThrow(new RuntimeException("TEST_ERROR"));

    SyncResult result = liftSyncService.fixSwitchedSiteMappings(UUID.randomUUID().toString());
    assertEquals(0, result.getNewRecords().get());
    assertEquals(0, result.getModifiedRecords().get());
    assertFalse(result.getError().isEmpty());
  }

  @Test
  void externalIdSyncExecutesCorrect() {

    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("lift-id");
    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
    siteMappingDocument.setId(UUID.randomUUID());
    siteMappingDocument.setLabyrinthSiteId(UUID.randomUUID());

    List<SiteDocument> siteDocuments = List.of(siteDocument);
    List<SiteMappingDocument> siteMappingDocuments = List.of(siteMappingDocument);

    SitesAndMappingsWrapper sitesAndMappingsWrapper =
        new SitesAndMappingsWrapper(siteDocuments, siteMappingDocuments);

    SyncResult syncResult = new SyncResult("TEST");
    syncResult.setNewRecords(new AtomicInteger(1));

    when(sitesService.getSite(any())).thenReturn(sitesAndMappingsWrapper);

    Sites dbSite = new Sites(new SiteDocument());
    when(sitesRepository.findBySiteIdUnfiltered(any())).thenReturn(dbSite);

    SyncResult result = liftSyncService.executeExternalIdSiteSync(UUID.randomUUID().toString());
    assertEquals(1, result.getModifiedRecords().get());
  }

  @Test
  void whenThereIsAnErrorInExternalIdSyncPredefinedSyncIsReturned() {
    when(sitesService.getSite(any())).thenThrow(new RuntimeException("TEST_ERROR"));

    SyncResult result = liftSyncService.executeExternalIdSiteSync(UUID.randomUUID().toString());
    assertEquals(0, result.getNewRecords().get());
    assertEquals(0, result.getModifiedRecords().get());
    assertFalse(result.getError().isEmpty());
  }
}
