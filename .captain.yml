kind: kubernetes
app: dairyenteligenserviceapi
type: web
team: dairyenteligenfoliothree
docker_image: dairyenteligen-service-api
replicas: 1
container_port: 8080
healthcheck:
  #path: /api/dairyenteligen/dediscover/v1/diagnostics/heartbeat
  path: /diagnostics/heartbeat
dns:
  zone: cglcloud
  name: dairyenteligenserviceapi
database:
  type: postgres
storage:
  s3:
    dediscover:
envvars:
  dev:
    SPRING_PROFILES_ACTIVE: dev
  stage:
    SPRING_PROFILES_ACTIVE: stg
  prod:
    SPRING_PROFILES_ACTIVE: prod
secrets:
  dev:
    dairyenteligenserviceapi:
      version: latest
  stage:
    dairyenteligenserviceapi:
      version: latest
  prod:
    dairyenteligenserviceapi:
      version: latest