/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.config;

import java.util.Base64;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "salesforce.lift")
@Getter
@Setter
@RequiredArgsConstructor
public class LiftConfig implements SalesforceConfig {

  private String tokenHost;
  private String tokenPath;
  private String clientId;
  private String clientSecret;
  private String grantType = "client_credentials";
  private String scheme = "https";
  private String port;
  private String defaultOwnerId;

  @Override
  public String getTokenAuthHeader() {
    String clientIdAndSecret = String.format("%s:%s", getClientId(), getClientSecret());
    return String.format(
        "Basic %s", Base64.getEncoder().encodeToString(clientIdAndSecret.getBytes()));
  }
}
