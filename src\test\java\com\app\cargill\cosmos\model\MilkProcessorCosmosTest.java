/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import org.junit.jupiter.api.Test;

class MilkProcessorCosmosTest {

  @Test
  void jsonIsParsedCorrect() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    MilkProcessorCosmos data =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/milk-processors-sample.json"),
            MilkProcessorCosmos.class);
    assertNotNull(data);
    assertEquals(2, data.getComponentProcessors().size());
    assertEquals(0, data.getConcentrationProcessors().size());
  }
}
