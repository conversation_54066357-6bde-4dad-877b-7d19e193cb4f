/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.LinkedHashMap;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportHeatStressEvaluationDto {
  private List<VisitReportColumnValueDto> animalInputs;
  private List<VisitReportColumnValueDto> weather;
  private List<VisitReportColumnValueDto> exposure;

  private LinkedHashMap<String, String> results;
  private String temperatureAndHumidityIndexValue;
  private String temperatureAndHumidityIndexColorValue;
  private String heatStressColorText;
  private String unitOfMeasure;

  private List<NotesDto> notes;
}
