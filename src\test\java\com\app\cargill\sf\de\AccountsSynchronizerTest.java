/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.description;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.AccountType;
import com.app.cargill.constants.PreferredMethod;
import com.app.cargill.constants.PrimaryLang;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.UserRole;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.sf.cc.mapper.LiftAccountMapper;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.simple.Account;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AccountsSynchronizerTest {

  private final ObjectMapper objectMapper = new ObjectMapper();

  @Mock private AccountsRepository repository;
  @Mock private UserRepository userRepository;

  @InjectMocks private AccountsSynchronizer synchronizer;

  @Test
  void whenNewAccountIsAddedEverythingPasses() {
    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(1, syncResult.getNewRecords().get());
  }

  @Test
  void whenAccountIsUpdateEverythingPasses() {
    Accounts dbAccount = new Accounts(new AccountDocument());
    dbAccount.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(repository.findByExternalId(anyString())).thenReturn(dbAccount);

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void whenAccountIsUpdateWithSameDataEverythingPasses() {
    AccountDocument accountDocument = createDocument();
    Accounts dbAccount = new Accounts(accountDocument);
    dbAccount.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(repository.findByExternalId(anyString())).thenReturn(dbAccount);

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void contactsUpdateWorksAsExpected() {
    AccountDocument sf = createDocument();
    AccountDocument app = createDocument();
    app.setContacts(new ArrayList<>());
    AccountDocument result = synchronizer.updateAccount(sf, app);
    assertEquals(3, result.getContacts().size());
    assertEquals(
        sf.getContacts().get(0).getSFDCContactId(), app.getContacts().get(0).getSFDCContactId());
    assertEquals(
        sf.getContacts().get(1).getSFDCContactId(), app.getContacts().get(1).getSFDCContactId());
  }

  @Test
  void whenAppContactsAreEmptySfContactsAreSet() {
    AccountDocument sf = createDocument();
    AccountDocument app = createDocument();
    app.getContacts().remove(2);
    app.getContacts().get(0).setContactId(sf.getContacts().get(0).getContactId());
    app.getContacts().get(1).setContactId(sf.getContacts().get(1).getContactId());

    AccountDocument result = synchronizer.updateAccount(sf, app);
    assertEquals(3, result.getContacts().size());
    assertEquals(
        sf.getContacts().get(0).getSFDCContactId(), app.getContacts().get(0).getSFDCContactId());
    assertEquals(
        sf.getContacts().get(1).getSFDCContactId(), app.getContacts().get(1).getSFDCContactId());
  }

  @Test
  void whenIdIsMismatchedItIsUpdated() {
    UUID dsmId = UUID.randomUUID();
    AccountDocument sf = createDocument();
    ExternalDataSource dsm = new ExternalDataSource();
    dsm.setSystem("LM");
    dsm.setUniqueExternalKey(dsmId.toString());
    sf.getApplicationMappings().add(dsm);
    sf.setId(UUID.randomUUID());

    AccountDocument app = createDocument();

    AccountDocument result = synchronizer.updateAccount(sf, app);
    assertEquals(dsmId.toString(), result.getId().toString());
  }

  /**
   * <a href="https://cglglobalit.atlassian.net/browse/CDEA-2038">Users update was not reflected on
   * the database</a>
   */
  @Test
  void whenNumberOfUsersIsChangedItIsReflectedInTheUpdate() throws IOException, ParseException {

    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");

    Account liftAccount =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/cdea_2038_lift_account_sample.json"),
            Account.class);
    AccountDocument liftAccountDocument = LiftAccountMapper.transform(liftAccount);
    AccountDocument dbAccountDocument =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/cdea_2038_db_account_sample.json"),
            AccountDocument.class);
    Accounts dbAccount = new Accounts();
    dbAccount.setId(1L);
    dbAccount.setLocalId(UUID.randomUUID().toString());
    dbAccount.setAccountDocument(dbAccountDocument);

    dbAccount.setCreatedDate(formatter.parse("2023-05-19 10:44:48.379"));
    dbAccount.setUpdatedDate(formatter.parse("2023-05-19 10:44:48.379"));
    when(repository.findByAccountIdInclDeleted(anyString())).thenReturn(dbAccount);
    SyncResult syncResult = synchronizer.sync(List.of(liftAccountDocument));
    verify(repository, description("Verify number of users is correct"))
        .saveAndFlush(argThat(accounts -> accounts.getAccountDocument().getUsers().size() == 2));
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertNotNull(liftAccountDocument.getApplicationMappings());
    assertFalse(liftAccountDocument.getApplicationMappings().isEmpty());
  }

  @Test
  void onIdMismatchUpdateConditionIsTriggered() throws IOException, ParseException {
    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Account liftAccount =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/cdea_2038_lift_account_sample.json"),
            Account.class);
    AccountDocument liftAccountDocument = LiftAccountMapper.transform(liftAccount);
    AccountDocument dbAccountDocument =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/cdea_2038_db_account_sample.json"),
            AccountDocument.class);
    Accounts dbAccount = new Accounts();
    dbAccount.setId(1L);
    dbAccount.setLocalId(UUID.randomUUID().toString());
    dbAccount.setAccountDocument(dbAccountDocument);
    dbAccount.setCreatedDate(formatter.parse("2023-06-19 10:44:48.379"));
    dbAccount.setUpdatedDate(formatter.parse("2023-06-19 10:44:48.379"));

    when(repository.findByAccountIdInclDeleted(anyString())).thenReturn(dbAccount);
    SyncResult syncResult = synchronizer.sync(List.of(liftAccountDocument));
    verify(repository, description("Verify number of users is correct"))
        .saveAndFlush(argThat(accounts -> accounts.getAccountDocument().getUsers().size() == 2));
  }

  @Test
  void onUserMismatchUpdateConditionIsTriggered() throws ParseException {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Date date = formatter.parse("2023-06-19 10:44:48.379");
    AccountDocument liftAccountDocument = createDocument();
    liftAccountDocument.setUsers(Set.of("<EMAIL>"));
    liftAccountDocument.setLastModifiedTimeUtc(date.toInstant());
    liftAccountDocument.setGoldenRecordId("123");
    AccountDocument dbAccountDocument = createDocument();
    dbAccountDocument.setId(UUID.randomUUID());
    dbAccountDocument.setGoldenRecordId("123");

    liftAccountDocument.setId(dbAccountDocument.getId());

    Accounts dbAccount = new Accounts();
    dbAccount.setId(1L);
    dbAccount.setLocalId(UUID.randomUUID().toString());
    dbAccount.setAccountDocument(dbAccountDocument);
    dbAccount.setCreatedDate(date);
    dbAccount.setUpdatedDate(date);

    when(repository.findByAccountIdInclDeleted(anyString())).thenReturn(dbAccount);
    SyncResult syncResult = synchronizer.sync(List.of(liftAccountDocument));
    assertEquals(1, syncResult.getModifiedRecords().get());
  }

  private AccountDocument createDocument() {
    AccountDocument result = new AccountDocument();
    Set<String> users = new HashSet<>();
    users.add("<EMAIL>");
    result.setUsers(users);
    result.setGoldenRecordId("123");
    result.setAccountName("AccountName");
    result.setLegalName("LegalName");
    result.setAccountType(1);
    result.setPhysicalAddress(new Address());
    result.setSegmentStepOneId("segmentStepOne");
    result.setNineBoxStepTwoID("nineBox");
    result.setSourceSystem("ss");
    result.setSubTypeID(SubTypeId.FarmProducer);
    result.setBusinessID(1);
    result.setParentAccountID("111");
    result.setExternalParentAccountID(UUID.randomUUID());
    result.setOwnerId("2222");
    result.setOwnerProfileNameandId("nameAndId");
    result.setAccountValidated(true);
    result.setWebSiteAddress("webSite");
    result.setActive(true);
    result.setCompanyEmail("email");
    result.setPhone("phone");
    result.setAccountCurrency(0);
    result.setAccountNumber("123");
    result.setNewAccountType(AccountType.Competitor);
    result.setCustomerStatus(2);
    result.setAccountStatus("active");
    result.setCurrentUserProfileNameandId("nameAndId");
    result.setLastModifiedBy("someId");
    result.setLastModifiedTimeUtc(Instant.now());
    result.setDescription("descr");
    result.setCustomerCode("customer");
    result.setDateOfLastCall(Instant.now());
    result.setDataSource(DataSource.LIFT);
    List<Contact> contacts = new ArrayList<>();
    contacts.add(createContact());
    contacts.add(createContact());
    contacts.add(createContact());

    List<UserRole> userRoles = new ArrayList<>();
    UserRole userRole = new UserRole();
    userRole.setRoleType("Assistant");
    userRole.setUserBusinessUnit(0);
    userRole.setUserName("test");
    userRoles.add(userRole);

    result.setUserRoles(userRoles);

    result.setContacts(contacts);

    return result;
  }

  private Contact createContact() {
    Contact target = new Contact();
    target.setContactId(UUID.randomUUID());
    target.setAccountId(UUID.randomUUID());
    target.setGoldenRecordAcountId(UUID.randomUUID().toString());
    target.setSFDCContactId(UUID.randomUUID().toString());
    target.setFirstName(UUID.randomUUID().toString());
    target.setLastName(UUID.randomUUID().toString());
    Address address = new Address();
    address.setCity(UUID.randomUUID().toString());
    address.setStreet(UUID.randomUUID().toString());
    address.setCountry(UUID.randomUUID().toString());
    target.setMailingAddress(address);
    target.setPhoneNumber(UUID.randomUUID().toString());
    target.setEmailAddress(UUID.randomUUID().toString());
    target.setLastUpdateDateTime(new DateEpoch(Instant.now()));
    target.setName(UUID.randomUUID().toString());
    target.setOtherAddress(null);
    target.setPrimaryLangId(PrimaryLang.Albanian);
    target.setPreferredMethodId(PreferredMethod.Phone);
    target.setSalutation(null);
    target.setDeleted(false);
    target.setCreateTimeUtc(Instant.now());

    return target;
  }
}
