/* Cargill Inc.(C) 2022 */
package com.app.cargill;

import static org.mockito.Mockito.mock;

import com.app.cargill.cosmos.repo.AccountsCosmosRepository;
import com.app.cargill.cosmos.repo.ActivitiesCosmosRepository;
import com.app.cargill.cosmos.repo.ContentDetailsCosmosRepository;
import com.app.cargill.cosmos.repo.CountryCosmosRepository;
import com.app.cargill.cosmos.repo.CountryToolCosmosRepository;
import com.app.cargill.cosmos.repo.DietsCosmosRepository;
import com.app.cargill.cosmos.repo.MilkProcessorsCosmosRepository;
import com.app.cargill.cosmos.repo.NotesCosmosRepository;
import com.app.cargill.cosmos.repo.SiteMappingsCosmosRepository;
import com.app.cargill.cosmos.repo.SitesCosmosRepository;
import com.app.cargill.cosmos.repo.StateCosmosRepository;
import com.app.cargill.cosmos.repo.UserPreferencesCosmosRepository;
import com.app.cargill.cosmos.repo.UsersCosmosRepository;
import com.app.cargill.cosmos.repo.VisitsCosmosRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class CosmosRepositoriesTestBeans {

  @Bean
  @Primary
  public AccountsCosmosRepository getAccountsCosmosRepository() {
    return mock(AccountsCosmosRepository.class);
  }

  @Bean
  @Primary
  public CountryCosmosRepository getCountryCosmosRepository() {
    return mock(CountryCosmosRepository.class);
  }

  @Bean
  @Primary
  public StateCosmosRepository getStateCosmosRepository() {
    return mock(StateCosmosRepository.class);
  }

  @Bean
  @Primary
  public SitesCosmosRepository getSitesCosmosRepository() {
    return mock(SitesCosmosRepository.class);
  }

  @Bean
  @Primary
  public VisitsCosmosRepository getVisitsCosmosRepository() {
    return mock(VisitsCosmosRepository.class);
  }

  @Bean
  @Primary
  public DietsCosmosRepository getDietsCosmosRepository() {
    return mock(DietsCosmosRepository.class);
  }

  @Bean
  @Primary
  public UsersCosmosRepository getUsersCosmosRepository() {
    return mock(UsersCosmosRepository.class);
  }

  @Bean
  @Primary
  public SiteMappingsCosmosRepository getSiteMappingsCosmosRepository() {
    return mock(SiteMappingsCosmosRepository.class);
  }

  @Bean
  @Primary
  public CountryToolCosmosRepository getCountryToolCosmosRepository() {
    return mock(CountryToolCosmosRepository.class);
  }

  @Bean
  @Primary
  public UserPreferencesCosmosRepository getUserPreferencesCosmosRepository() {
    return mock(UserPreferencesCosmosRepository.class);
  }

  @Bean
  @Primary
  public ContentDetailsCosmosRepository getContentDetailsCosmosRepository() {
    return mock(ContentDetailsCosmosRepository.class);
  }

  @Bean
  @Primary
  public NotesCosmosRepository getNotesCosmosRepository() {
    return mock(NotesCosmosRepository.class);
  }

  @Bean
  @Primary
  public MilkProcessorsCosmosRepository getMilkProcessorsCosmosRepository() {
    return mock(MilkProcessorsCosmosRepository.class);
  }

  @Bean
  @Primary
  public ActivitiesCosmosRepository getActivitiesCosmosRepository() {
    return mock(ActivitiesCosmosRepository.class);
  }
}
