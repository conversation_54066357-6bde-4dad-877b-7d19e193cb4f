/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.config;

import static org.junit.jupiter.api.Assertions.*;

import org.apache.commons.codec.EncoderException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftConfigTest {

  @Test
  void getTokenAuthHeader() throws EncoderException {
    LiftConfig liftConfig = new LiftConfig();
    liftConfig.setClientId("test_client");
    liftConfig.setClientSecret("test_secret");
    assertEquals("Basic dGVzdF9jbGllbnQ6dGVzdF9zZWNyZXQ=", liftConfig.getTokenAuthHeader());
  }
}
