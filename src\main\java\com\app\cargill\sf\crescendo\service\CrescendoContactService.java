/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.app.cargill.document.Contact;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.sf.crescendo.exceptions.ContactAccountNotFoundException;
import com.app.cargill.sf.crescendo.mapper.CrescendoContactMapper;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class CrescendoContactService {
  private final AccountsRepository accountsRepository;

  /**
   * @param from Optional (null safe) parameter which changes the semantics from getting needsSync
   *     accounts to all accounts updated since the provided timestamp
   * @return List of Crescendo contacts
   */
  public List<ContactCrescendo> getContactsForSync(Instant from) {
    // The flow is not async with reactive flow because we need to extract contacts first
    // If there are no errors until the result is prepared
    // we can asynchronously update the accounts with contacts just before returning the result
    List<Accounts> accounts;
    List<Accounts> updatedAccounts = new ArrayList<>();
    if (from != null) {
      accounts = accountsRepository.getAccountsByUpdatedDateAfterForCrescendo(from);
    } else {
      accounts = accountsRepository.getAccountsWithNeedsSyncContactsForCrescendo();
    }
    // Filter out accounts with missing GoldenRecordId
    accounts =
        accounts.stream()
            .filter(a -> !isEmpty(a.getAccountDocument().getGoldenRecordId()))
            .toList();

    List<ContactCrescendo> crescendoContacts = new ArrayList<>();
    for (Accounts account : accounts) {
      boolean accountUpdated = false;
      for (Contact contact : account.getAccountDocument().getContacts()) {
        if (Boolean.TRUE.equals(contact.getNeedsSync())) {
          contact.setNeedsSync(false);
          // Make sure this is set
          contact.setGoldenRecordAcountId(account.getAccountDocument().getGoldenRecordId());
          accountUpdated = true;
          crescendoContacts.add(CrescendoContactMapper.documentToCrescendo(contact));
        }
      }
      if (accountUpdated) {
        updatedAccounts.add(account);
      }
    }
    if (!updatedAccounts.isEmpty()) {
      CompletableFuture.runAsync(() -> accountsRepository.saveAll(updatedAccounts));
    }
    return crescendoContacts;
  }

  public CompletableFuture<Integer> updateContacts(List<ContactCrescendo> contactsCrescendo) {
    return updateContactsAsync(contactsCrescendo)
        .whenComplete((i, e) -> log.info("{} contacts synced from Crescendo", i));
  }

  private CompletableFuture<Integer> updateContactsAsync(List<ContactCrescendo> contactsCrescendo) {
    Flux<ContactCrescendo> contactCrescendoFlux = Flux.fromIterable(contactsCrescendo);
    AtomicInteger failedItems = new AtomicInteger(0);
    return contactCrescendoFlux
        .map(CrescendoContactMapper::crescendoToModel)
        .flatMap(
            contact ->
                Mono.fromCallable(() -> mapContactToAccount(contact))
                    .subscribeOn(Schedulers.boundedElastic())
                    .onErrorResume(
                        t -> {
                          failedItems.incrementAndGet();
                          if (t instanceof ContactAccountNotFoundException) {
                            log.warn("{} {}", t.getMessage(), contact);
                          } else {
                            log.error(
                                "CRESCENDO_UPDATE_CONTACT_MAPPING {} {}", t.getMessage(), contact);
                          }
                          return Mono.empty();
                        }))
        .flatMap(
            a ->
                Mono.fromCallable(() -> accountsRepository.save(a))
                    .subscribeOn(Schedulers.boundedElastic())
                    .onErrorResume(
                        t -> {
                          failedItems.incrementAndGet();
                          log.error("CRESCENDO_UPDATE_CONTACT_SAVE {} {}", t.getMessage(), a);
                          return Mono.empty();
                        }))
        .onErrorContinue(
            (t, o) -> {
              failedItems.incrementAndGet();
              log.error("CRESCENDO_UPDATE_CONTACT_PROCESS {}", o, t);
            })
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .toFuture();
  }

  private Accounts mapContactToAccount(Contact contact) {
    Accounts account;
    if (contact.getAccountId() != null) {
      account = accountsRepository.findByAccountId(contact.getAccountId().toString());
    } else {
      account = accountsRepository.findByExternalId(contact.getGoldenRecordAcountId());
    }
    if (account == null) {
      throw new ContactAccountNotFoundException("Could not find the account for Contact");
    }
    List<Contact> existingContacts = account.getAccountDocument().getContacts();
    if (existingContacts == null) {
      populateContactId(contact);
      account.getAccountDocument().setContacts(List.of(contact));
    } else {
      boolean contactFound = false;
      for (Contact c : account.getAccountDocument().getContacts()) {
        if (c.getSFDCContactId() != null
            && c.getSFDCContactId().equals(contact.getSFDCContactId())) {
          contactFound = true;
          CrescendoContactMapper.modelUpdate(c, contact);
        }
      }
      if (!contactFound) {
        populateContactId(contact);
        account.getAccountDocument().getContacts().add(contact);
      }
    }
    return account;
  }

  private void populateContactId(Contact contact) {
    if (contact.getContactId() == null) {
      contact.setContactId(UUID.randomUUID());
    }
  }
}
