/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class BaseDto {

  private UUID id;
  private Instant createdDate;
  private Instant updatedDate;

  private boolean deleted;

  private String localId;

  private Instant currentTimeStamp;

  private Instant mobileLastUpdatedTime;
}
