/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** This refers both "Owner" and "CreatedBy" fields */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class PersonRecord implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("Name")
  private String name;
}
