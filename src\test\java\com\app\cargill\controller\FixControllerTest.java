/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.migration.AccountsDataFixService;
import com.app.cargill.cosmos.migration.SitesDataFixService;
import com.app.cargill.cosmos.migration.VisitsDataFixService;
import com.app.cargill.document.SitesDeletedTransform;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.Visits;
import com.app.cargill.model.tasks.CosmosMigrationMeta;
import com.app.cargill.model.tasks.SyncResult;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class FixControllerTest {

  @Mock private AccountsDataFixService accountsDataFixService;
  @Mock private SitesDataFixService sitesDataFixService;
  @Mock private VisitsDataFixService visitsDataFixService;

  @InjectMocks private FixController fixController;

  @Test
  void whenAccountsFixIsInvokedNoErrors() {

    when(accountsDataFixService.runAccountsPostMigration()).thenReturn(Flux.just(new Accounts()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.runAccountsPostMigration();
    assertNotNull(task);
  }

  @Test
  void whenUpdateAccountWithWrongOwnerFixIsInvokedNoErrors() {
    when(accountsDataFixService.updateAccountsWithWrongOwnerId())
        .thenReturn(Flux.just(new Accounts()));
    Flux<Accounts> task = fixController.updateAccountOwnerId();
    assertNotNull(task);
  }

  @Test
  void whenUserAccountFixIsInvokedNoErrors() {
    when(accountsDataFixService.runUserAccountsPostMigration(any()))
        .thenReturn(Flux.just(new Accounts()));
    LongRunningTask<CosmosMigrationMeta> task =
        fixController.runUserAccountsPostMigration(List.of("<EMAIL>"));
    assertNotNull(task);
  }

  @Test
  void whenSitesFixIsInvokedNoErrors() {
    when(sitesDataFixService.fixSiteMissingSiteMappings())
        .thenReturn(Flux.just(new SyncResult("TEST")));
    LongRunningTask<CosmosMigrationMeta> task = fixController.runSitesFix();
    assertNotNull(task);
  }

  @Test
  void whenSwitchedMappingsFixIsInvokedNoErrors() {
    when(sitesDataFixService.fixSiteSwitchedSiteMappings())
        .thenReturn(Flux.just(new SyncResult("TEST")));
    LongRunningTask<CosmosMigrationMeta> task = fixController.runSwitchedMappingsSitesFix();
    assertNotNull(task);
  }

  @Test
  void whenRunExternalIdFixIsInvokedNoErrors() {
    when(sitesDataFixService.fixSiteMissingExternalId())
        .thenReturn(Flux.just(new SyncResult("TEST")));
    LongRunningTask<CosmosMigrationMeta> task = fixController.runSitesExternalIdFix();
    assertNotNull(task);
  }

  @Test
  void whenMobileLastUpdatedTimeFixIsInvokedNoErrors() {
    when(visitsDataFixService.updateMobileLastUpdatedDate()).thenReturn(Mono.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateMobileLastUpdatedDate();
    assertNotNull(task);
  }

  @Test
  void whenFixOwnerMissingFromUsersNoErrors() {
    when(accountsDataFixService.fixOwnerMissingFromUsers()).thenReturn(Flux.just(new Accounts()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.runAccountsOwnerInUsersFix();
    assertNotNull(task);
  }

  @Test
  void whenFixTmrComparisonFixIsInvokedNoErrors() {
    when(visitsDataFixService.tmrPenStateIndexesFix()).thenReturn(Mono.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.runTmrPenstateIndexesFix();
    assertNotNull(task);
  }

  @Test
  void whenAccountsUserArrayFixIsCalledNoErrors() {
    when(accountsDataFixService.fixUserArrayInAccounts()).thenReturn(Flux.just(new Accounts()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.fixAccountsUserArray();
    assertNotNull(task);
  }

  @Test
  void whenUpdateVisitsNotesDietsForDeletedSiteIds() {
    SitesDeletedTransform sitesDeletedTransform = new SitesDeletedTransform();
    fixController.updateDeletedSiteId(sitesDeletedTransform);
    assertTrue(true);
  }

  @Test
  void whenUpdateBodyConditionPensData() {
    when(visitsDataFixService.updateBodyConditionPensData()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateBodyConditionPensData();
    assertNotNull(task);
  }

  @Test
  void whenUpdateTmrParticleScore() {
    when(visitsDataFixService.updateTmrParticleScore()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateTmrParticleScore();
    assertNotNull(task);
  }

  @Test
  void whenUpdateRumenHealthManureScore() {
    when(visitsDataFixService.updateRumenHealthManureScore()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateRumenHealthManureScore();
    assertNotNull(task);
  }

  @Test
  void whenUpdateRumenFillManureScore() {
    when(visitsDataFixService.updateRumenFillManureScore()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateRumenFillManureScore();
    assertNotNull(task);
  }

  @Test
  void whenUpdateCudChewing() {
    when(visitsDataFixService.updateCudChewing()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateCudChewing();
    assertNotNull(task);
  }

  @Test
  void whenUpdateAnimalAnalysisPensData() {
    when(visitsDataFixService.updateAnimalAnalysisPensData()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateAnimalAnalysisPensData();
    assertNotNull(task);
  }

  @Test
  void whenUpdateLocomotionScorePensData() {
    when(visitsDataFixService.updateLocomotionScorePensData()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateLocomotionScorePensData();
    assertNotNull(task);
  }

  @Test
  void whenUpdateManureScreenerToolData() {
    when(visitsDataFixService.updateManureScreenerTool()).thenReturn(Flux.just(new Visits()));
    LongRunningTask<CosmosMigrationMeta> task = fixController.updateManureScreenerToolScore();
    assertNotNull(task);
  }
}
