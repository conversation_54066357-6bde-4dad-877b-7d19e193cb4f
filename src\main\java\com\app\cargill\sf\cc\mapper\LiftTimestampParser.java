/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LiftTimestampParser {

  private LiftTimestampParser() {}

  public static Instant parse(String timestamp) {
    if (timestamp == null) {
      return null;
    }
    SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSz");
    try {
      return dateFormatter.parse(timestamp).toInstant();
    } catch (ParseException e) {
      log.warn("LIFT_TIME_PARSE_ERROR {} ", timestamp, e);
      return Instant.MIN;
    }
  }
}
