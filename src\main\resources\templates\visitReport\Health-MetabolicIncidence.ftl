<div class="container">
		<div class="legend-head">
			<div class="count">${toolNumber!}</div>
			<div class="main-title">
				<span class="sm-head">${localization.getMessage("VisitSummaryViewModel.HealthItem", [], "Health",
                    locale)}</span>
				<span class="lg-head">${localization.getMessage("MetabolicIncidenceMasterViewModel.Title", [], "Metabolic Incidence",
                    locale)}</span>
			</div>
			<div style="font-size: 1;color: white;">0000777MI</div>
		</div>
	</div>
<#if
            model.metabolicIncidenceTool.herdLevelInformationLeftTable?? || model.metabolicIncidenceTool.herdLevelInformationRightTable??>
	<!-- Herd Level Information -->
	<div class="container">
		<h3 class="title-secondary mb-1">
			<span>${localization.getMessage("MetabolicIncidenceInputsViewModel.Herd", [], "Herd Level Information",
                    locale)}</span>
		</h3>
		<div class="row mx-neg-4">
		<#if model.metabolicIncidenceTool.herdLevelInformationLeftTable??>
			<div class="col-6 mb-1 px-4 table-secondary">
				<table class="w-50">
					<tbody>
						<#list model.metabolicIncidenceTool.herdLevelInformationLeftTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.metabolicIncidenceTool.herdLevelInformationLeftTable[prop]!}</td>
                                    </tr>
                                </#list>
					</tbody>
				</table>
			</div>
			</#if>
                <#if model.metabolicIncidenceTool.herdLevelInformationRightTable??>
			<div class="col-6 mb-1 px-4 table-secondary">
				<table class="w-50">
					<tbody>
						<#list model.metabolicIncidenceTool.herdLevelInformationRightTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.metabolicIncidenceTool.herdLevelInformationRightTable[prop]!}</td>
                                    </tr>
                                </#list>
					</tbody>
				</table>
			</div>
			</#if>
		</div>
	</div>
</#if>
<#if
            model.metabolicIncidenceTool.metabolicIncidenceCasesLeftTable?? || model.metabolicIncidenceTool.metabolicIncidenceCasesRightTable??>
	<!-- Metabolic Incidence Cases -->
	<div class="container">
		<h3 class="title-primary mb-1">
			<span>${localization.getMessage("MetabolicIncidenceInputsViewModel.IncidenceCases", [], "Metabolic Incidence Cases",
                    locale)}</span>
		</h3>
		<div class="row mx-neg-4">
		<#if
            model.metabolicIncidenceTool.metabolicIncidenceCasesLeftTable??>
			<div class="col-6 mb-1 px-4 table-secondary">
				<table class="w-50 mb-1">
					<tbody>
						<#list model.metabolicIncidenceTool.metabolicIncidenceCasesLeftTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.metabolicIncidenceTool.metabolicIncidenceCasesLeftTable[prop]!}</td>
                                    </tr>
                                </#list>
					</tbody>
				</table>
			</div>
			</#if>
			<#if
            model.metabolicIncidenceTool.metabolicIncidenceCasesRightTable??>
			<div class="col-6 mb-1 px-4 table-secondary">
				<table class="w-50 mb-1">
					<tbody>
						<#list model.metabolicIncidenceTool.metabolicIncidenceCasesRightTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.metabolicIncidenceTool.metabolicIncidenceCasesRightTable[prop]!}</td>
                                    </tr>
                                </#list>
					</tbody>
				</table>
			</div>
			</#if>
		</div>
	</div>
</#if>
<#if model.metabolicIncidenceTool.performanceAndTreatmentCost??>
	<!-- Performance & Treatment Costs -->
	<div class="container">
		<h3 class="title-primary mb-1">
			<span>${localization.getMessage("MetabolicIncidenceInputsViewModel.PerformanceTreatment", [], "Performance And Treatment Costs",
                    locale)}</span>
		</h3>

		<div class="row mx-neg-4">
			<div class="col-12 mb-1 px-4 table-primary">
				<table>
					<thead>
						<#if model.metabolicIncidenceTool.performanceAndTreatmentCost?? &&
                            model.metabolicIncidenceTool.performanceAndTreatmentCost[0] ??>
                            <tr>

                                <#list model.metabolicIncidenceTool.performanceAndTreatmentCost[0] as detail>
                                    <th>${detail.column!}</th>
                                </#list>

                            </tr>
                        </#if>
					</thead>
					<tbody>
						<#if model.metabolicIncidenceTool.performanceAndTreatmentCost?? &&
                            model.metabolicIncidenceTool.performanceAndTreatmentCost[0] ??>
							<#list model.metabolicIncidenceTool.performanceAndTreatmentCost as innerlist>
                            <tr>

                                <#list innerlist as detail>
                                    <td>${detail.value!}</td>
                                </#list>

                            </tr>
							</#list>
                        </#if>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</#if>
<#if model.metabolicIncidenceTool.graph??>
<#if model.metabolicIncidenceTool.herdLevelInformationLeftTable?? || model.metabolicIncidenceTool.herdLevelInformationRightTable?? || model.metabolicIncidenceTool.metabolicIncidenceCasesLeftTable?? || model.metabolicIncidenceTool.metabolicIncidenceCasesRightTable?? || model.metabolicIncidenceTool.performanceAndTreatmentCost?? >
	<div class="break-page"></div>
</#if>
<#if model.metabolicIncidenceTool.graph.metabolicDisorderCostPerCow??>
	<!-- Metabolic Disorder Cost/Cow -->
	<div class="container">
		<div class="row mx-neg-4">
			<div class="col-12 px-4">
				<h6>${localization.getMessage("MetabolicIncidenceChartsViewModel.DisorderGraphTitle", [], "Metabolic Disorder Cost/Cow", locale)}</h6>
			</div>
			<div class="col-12 px-4">
				<div class="card mb-1">
					<div class="card-body">
						<#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart2}"></canvas>
					</div>
					<div class="card-footer">
						<div class="row">
					<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.formattedDates as x>
						<#if x?index == 0>
							<div class="legend-wrap mb-2">
								<p class="lagoon-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 1>
							<div class="legend-wrap mb-2">
								<p class="voilet-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 2>
							<div class="legend-wrap mb-2">
								<p class="voilet-2-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 3>
							<div class="legend-wrap mb-2">
								<p class="voilet-3-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 4>
							<div class="legend-wrap mb-2">
								<p class="pink-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 5>
							<div class="legend-wrap mb-2">
								<p class="red-2-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 6>
							<div class="legend-wrap mb-2">
								<p class="gold-solid">${x!}</p>
							</div>
						</#if>
					</#list>
				</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<script>

                            (function () {

const bar1 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate1 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar2 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate2 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar3 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate3 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar4 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate4 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar5 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate5 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar6 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate6 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar7 = [
	<#list model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate7 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const xAxis = [
<#list model.metabolicIncidenceTool.graph.metabolicTypeKeys as typeKeys>
    '${localization.getMessage(typeKeys, [], typeKeys, locale)}'<#sep>, </#sep>
</#list>
];

const ctx = document.getElementById("${linechart2}").getContext("2d");
ctx.canvas.height = 100;
const borderWidth = 2.2;
const categoryPercentage = 0.85;
const barPercentage = 1;
const options = {
    type: "bar",
    data: {
        labels: xAxis,
        datasets: [
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate1?has_content>
		{
			data: bar1,
			grouped:true,
			backgroundColor: "#67B7DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate2?has_content>
		{
			data: bar2,
			backgroundColor: "#6794DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate3?has_content>
		{
			data: bar3,
			backgroundColor: "#8067DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate4?has_content>
		{
			data: bar4,
			backgroundColor: "#A367DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate5?has_content>
		{
			data: bar5,
			backgroundColor: "#DC67CE",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate6?has_content>
		{
			data: bar6,
			backgroundColor: "#DC6967",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceTool.graph.metabolicIncidenceExportImageReportDto.visitDate7?has_content>
		{
			data: bar7,
			backgroundColor: "#DCAF67",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
        ]
    },
    options: {
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                callbacks: {
                    title: () => null // or function () { return null; }
                },
                yAlign: 'bottom',
                backgroundColor: "#fff",
                borderColor: "rgba(0, 0, 0, 0.25)",
                borderWidth: 1,
                displayColors: false,
                bodyColor: "#307698",
                bodyAlign: "center",
            },
        },

        layout: {
            padding: {
                top: 20,
                right: 15
            }
        },

        responsive: true,
        scales: {
            y: {
                // beginAtZero: true,
                title: {
                    display: true,
                    color: '#6C7782',
                    text: '${model.metabolicIncidenceTool.graph.metabolicDisorderCostPerCowLabel!}',
                    padding: {
                        bottom: 15,
                    }
                },

                grid: {
                    display: false,
                },
                suggestedMax: (scale) => {

                    var curr = scale.chart.data.datasets;
                    var arr = [];
                    for (let i = 0; i < curr.length; i++) {
                        arr.push(Math.max.apply(null, curr[i].data.filter(x => !isNaN(x))));
                    }
                    return Math.round(Math.max(...arr) + (Math.max(...arr) * 0.05));
                },
                suggestedMin: (scale) => {
                    var curr = scale.chart.data.datasets;
                    var arr = [];
                    for (let i = 0; i < curr.length; i++) {
                        arr.push(Math.min.apply(null, curr[i].data.filter(x => !isNaN(x))));
                    }
                    return Math.floor(Math.min(...arr) + (Math.min(...arr) * 0.15));
                }
            },

            x: {
                title: {
                    display: true,
                    color: '#6C7782',
                    // text: 'Lactation Stages',
                    padding: {
                        top: 15,
                    }
                },
                grid: {
                    display: false,
                },
            }
        },
        animation: {
            duration: 0,
            onComplete: function() {
                var chart = this;
                var ctx = chart.ctx;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                ctx.fillStyle = '#6C7782';
                this.data.datasets.forEach(function(dataset, i) {
                    var meta = chart.getDatasetMeta(i);
                    meta.data.forEach(function(bar, index) {
                        var data = dataset.data[index];
                        data = isNaN(data) ? '' : data;
                        var yIndex = bar.y - 12;
                        if (data && data < 0) {
                            yIndex = bar.y + 15;
                        }
                        ctx.save();
                        // Translate 0,0 to the point you want the text
                        ctx.translate(bar.x + 5, yIndex);
                        // Rotate context by -90 degrees
                        ctx.rotate(-0.4 * Math.PI);
                        ctx.textAlign = "center";
                        ctx.fillText(data, 0, 0);
                        ctx.restore();
                    });
                });
            }
        }
    }
};

                            window.myLine = new Chart(ctx, options);
}) ();
                        </script>
</#if>
<#if model.metabolicIncidenceTool.graph.incidencePercentage??>
	<!-- Metabolic Incidence % -->
	<div class="container">
		<div class="row mx-neg-4">
			<div class="col-12 px-4">
				<h6>${localization.getMessage("MetabolicIncidenceChartsViewModel.GraphTitle", [], "Metabolic Incidence %", locale)}</h6>
			</div>
			<div class="col-12 px-4">
				<div class="card mb-1">
					<div class="card-body">
						<#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart2}"></canvas>
					</div>
					<div class="card-footer">
						<div class="row">
							<div class="legend-wrap mb-2">
								<p class="green-solid">${localization.getMessage("MetabolicIncidenceChartsViewModel.GoalPercent", [], "Goal (%)", locale)}</p>
							</div>
							<div class="legend-wrap mb-2">
								<p class="copper-solid">${localization.getMessage("MetabolicIncidenceChartsViewModel.IncidencePercent", [], "Incidence (%)", locale)}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
<script>

                            (function () {
	const colors = {
purple: {
default: "#307698",
half: "#30769878",
quarter: "#3076983b",
zero: "#3076981c"
},
	indigo: {
default: "#307698",
quarter: "#30769878"
}
};

const goalPercentage = [
<#list model.metabolicIncidenceTool.graph.metabolicTypeKeys as typeKeys>
    ${(model.metabolicIncidenceTool.graph.goalPercentage[typeKeys])!'NaN'}<#sep>, </#sep>
</#list>
];

const incidencePercentage = [
<#list model.metabolicIncidenceTool.graph.metabolicTypeKeys as typeKeys>
    ${(model.metabolicIncidenceTool.graph.incidencePercentage[typeKeys])!'NaN'}<#sep>, </#sep>
</#list>
];

const xAxis = [
<#list model.metabolicIncidenceTool.graph.metabolicTypeKeys as typeKeys>
    '${localization.getMessage(typeKeys, [], typeKeys, locale)}'<#sep>, </#sep>
</#list>
];

const ctx = document.getElementById("${linechart2}").getContext("2d");
ctx.canvas.height = 100;

gradient = ctx.createLinearGradient(0, 25, 0, 300);
gradient.addColorStop(0, colors.purple.half);
gradient.addColorStop(0.35, colors.purple.quarter);
gradient.addColorStop(1, colors.purple.zero);

const options = {
type: "bar",

data: {
labels: xAxis,
datasets: [
		{

			fill: true,
			backgroundColor: '#55C2BE',
			borderColor: '#55C2BE',
			pointBackgroundColor: '#55C2BE',
			pointBorderColor: '#fff',
			data: goalPercentage,
			lineTension: 0.2,
			borderWidth: 1,
			pointRadius: 6,
		},
      	{
			fill: true,
			backgroundColor: '#D98773',
			borderColor: '#D98773',
			pointBackgroundColor: '#D98773',
			pointBorderColor: '#fff',
			data: incidencePercentage,
			lineTension: 0.2,
			borderWidth: 1,
			pointRadius: 6,
		}
    ]
  },
  options: {
plugins: {
legend: {
display: false,
},
		tooltip: {
callbacks: {
title : () => null // or function () { return null; }
         },
			yAlign: 'bottom',
			backgroundColor: "#fff",
			borderColor: "rgba(0, 0, 0, 0.25)",
			borderWidth: 1,
			displayColors: false,
			bodyColor: "#307698",
			bodyAlign: "center",
        },
  	},

    layout: {
		padding: {
			top:20,
			right: 15
		}
	},

    responsive: true,

    scales: {
y: {
// beginAtZero: true,
title: {
display: true,
color: '#6C7782',
text: '${localization.getMessage("MetabolicIncidenceChartsViewModel.GraphTitle", [], "Metabolic Incidence %", locale)}',
padding: {
bottom: 15,
}
      		},
			
			grid: {
display: false,
},
ticks: {
// Include a % sign in the ticks
callback: function(value, index, ticks) {
return  value + '%';
}
}
      	},

		x: {
title: {
display: true,
color: '#6C7782',
text: '',
padding: {
top: 15,
}
      		},
			grid: {
display: false,
},
		}
    },
	animation: {
	duration: 0,
	onComplete: function() {
		var chart = this;
		var ctx = chart.ctx;
		ctx.textAlign = 'center';
		ctx.textBaseline = 'bottom';
		ctx.fillStyle =  '#6C7782';
		this.data.datasets.forEach(function(dataset, i) {
						var meta = chart.getDatasetMeta(i);
						meta.data.forEach(function(bar, index) {
						var data = dataset.data[index];
						data = isNaN(data) ? '': data + '%';
						var yIndex = bar.y - 5;
						if(data && data < 0) {
							yIndex = bar.y + 15;
						}
						ctx.fillText(data, bar.x, yIndex);
						});
				});
			}
		}
  }
};

                            window.myLine = new Chart(ctx, options);
}) ();
                        </script>
</#if>
</#if>

	<!-- Notes -->
<#if model.metabolicIncidenceTool?? && model.metabolicIncidenceTool.notes??>
        <div class="container mid-body">
            <div class="pt-0">
                <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                    locale)}</h3>
                <#list model.metabolicIncidenceTool.notes as innerlist>
                    <#if innerlist.id??>
                        <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                            <h4 class="followup mb-1">
                                <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                                <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                            </h4>
                            <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                            <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                                <div class="notes-images mb-1">
                                    <#list noteFound.mediaItems as media>
                                        <figure>
                                            <img src="${media.base64EncodedImage!}">
                                        </figure>
                                    </#list>
                                </div>
                            </#if>
                        </#list>
                    </#if>
                </#list>
            </div>
        </div>
    </#if>