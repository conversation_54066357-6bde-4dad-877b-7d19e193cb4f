/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

@Component
@Setter
@Getter
@Slf4j
public class AwsCredentialsFactory {

  @Value("${cloud.aws.s3.access-key}")
  private String amazonAccessKey;

  @Value("${cloud.aws.s3.secret-key}")
  private String amazonSecretKey;

  public AwsCredentialsProvider getCredentialsProvider() {
    log.debug("Creating AwsCredentialsProvider ");
    log.info(amazonAccessKey + ":" + amazonSecretKey);

    if (!StringUtils.isAllBlank(amazonAccessKey, amazonSecretKey)) {
      return StaticCredentialsProvider.create(
          AwsBasicCredentials.create(amazonAccessKey, amazonSecretKey));
    } else {
      return DefaultCredentialsProvider.create();
    }
  }
}
