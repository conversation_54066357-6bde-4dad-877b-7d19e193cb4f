/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.constants.AccountType;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class AccountTypeToEnumDeserializerTest {
  private ObjectMapper mapper;
  private AccountTypeToEnumDeserializer deserializer;

  @BeforeEach
  void setup() {
    mapper = new ObjectMapper();
    deserializer = new AccountTypeToEnumDeserializer();
  }

  @ParameterizedTest
  @ValueSource(strings = {"1", "\"Customer\"", "\"Commercial Customer\"", "\"Commercial\""})
  void whenIntegerIsProvidedCorrectTypeIsReturned(String value) {
    String json = String.format("{\"value\":%s}", value);
    AccountType type = deserialiseString(json);
    assertEquals(AccountType.Customer, type);
  }

  @Test
  void whenUnknownValueIsProvidedCorrectTypeIsReturned() {
    String json = String.format("{\"value\":%s}", "\"Unknown\"");
    AccountType type = deserialiseString(json);
    assertEquals(AccountType.Prospect, type);
  }

  @SneakyThrows({JsonParseException.class, IOException.class})
  private AccountType deserialiseString(String json) {
    InputStream stream = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
    JsonParser parser = mapper.getFactory().createParser(stream);
    DeserializationContext ctxt = mapper.getDeserializationContext();
    parser.nextToken();
    parser.nextToken();
    parser.nextToken();
    return deserializer.deserialize(parser, ctxt);
  }
}
