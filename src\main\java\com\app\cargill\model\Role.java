/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "role")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
public class Role extends BaseEntity {

  @Column(length = 100)
  @NotNull
  private String roleName;

  @Column(length = 100)
  @NotNull
  private String code;
}
