AAEfficiency=Eficiência de AA
AMSUtilization=Utilização do robô
AMSUtilizationChart=Tabela de uso do robô de tratamento
ARA=ARA
ARS=Argentina ($ ARS)
AUD=Austrália ($ AUD)
Account=Conta
Account-Not-Synced-To-Lift=Conta não foi sincronizada com LIFT; contatar o adm
Acre=Acre
Action=Ação
AddBunker=Adicionar Silo Trincheira
AddPile=Adicionar Silo de Superficie
AddTMRScore=Adicionar TMR Escore de Partícula
AdjustingKPtoAssureSuccess=Ajuste de Processamento de Grãos 
Afghanistan=Afeganistão
Agrigento=Agrigento
Aguascalientes=Aguascalientes
Alabama=Alabama
Alagoas=Alagoas
Aland_Islands=Ilhas Aland
Alaska=Abaixo
Albania=Albânia
Alberta=Alberta
Alessandria=Alexandria
Algeria=Argelia
Amapá=Ampass
Amazonas=Amazonas
Amount=Quantidade
Ancona=Ancona
Andaman_and_Nicobar_Islands=Ilhas Andaman e Nicobar
Andhra_Pradesh=Andhra Pradesh
Andorra=Andorra
Angola=Angola
Anguilla=Anguilla
Anhui=Anhui
AnimalInformation=Informação Animal
AnimalListViewModel.Title=Classe Animal / Sub Classe
Animals=Animais
AnimalsInHerd=Animais no Rebanho
AnimalsInPen=Animais no Lote
AnimalsObserved=Animais Observados
Annually=Anualmente
Answers=Respostas
Antarctica=Antártica
Antigua_and_Barbuda=Antígua e Barbuda
Aosta=Aosta
AppName=Dairy Enteligen
Arezzo=Arezzo
Argentina=Argentina
Arizona=Arizona
Arkansas=Arkansa
Armenia=Armênia
Aruba=Aruba
Arunachal_Pradesh=Arunachal Pradesh
Ascoli_Piceno=Ascoli piceno
Assam=Assam
Asti=Até
AtSixLengthPerDayImperial=6 polegadas por dia
AtSixLengthPerDayMetric=15 cm. Por dia
AtThreeLengthPerDayImperial=3 polegadas por dia
AtThreeLengthPerDayMetric=7 cm. Por dia
Australia=Austrália
Australian_Capital_Territory=Território Capital da Australia
Austria=Áustria
Auto_Sync=Auto Sincronização
Avellino=Avellino
Average=Média
AverageMilkLoss=Perda média de Leite ({0})
AverageScoreTitle=Média TMR escore partícula
AvgBCS=Média ECC
AvgLocomotionScore=Média Escore Locomoção (calculada)
Azerbaijan=Azerbaijão
BAM=BAM
BCS=ECC
BCSCategory1=Escore de Condição Corporal 1.0
BCSCategory1pt5=Escore de Condição Corporal 1.5
BCSCategory2=Escore de Condição Corporal 2.0
BCSCategory2pt5=Escore de Condição Corporal 2.5
BCSCategory3=Escore de Condição Corporal 3.0
BCSCategory3pt5=Escore de Condição Corporal 3.5
BCSCategory4=Escore de Condição Corporal 4.0
BCSCategory4pt5=Escore de Condição Corporal 4.5
BCSCategory5=Escore de Condição Corporal 5.0
BCSEditMilkAndDimViewModel.BCSDIMTitle=Dias em Lactação (DEL)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Leite
BCSEditMilkAndDimViewModel.Title=Modifica DEL a leite
BCSHerdAnalysisInputsViewModel.BCS=ECC
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Análise Escore de Condição Corporal
BCSHerdAnalysisInputsViewModel.BCSDIM=DEL
BCSHerdAnalysisInputsViewModel.BCSEdit=Editar
BCSHerdAnalysisInputsViewModel.BCSMilk=Leite
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Análise do Rebanho
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Objetivos
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Inputs
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Resultados
BCSHerdAnalysisMasterViewModel.BCSTitle=Escore de Condição Corporal
BCSHerdAnalysisMasterViewModel.Title=Escore de Condição Corporal
BCSHerdAnalysisResultsViewModel.BCSAvg=Média ECC
BCSHerdAnalysisResultsViewModel.GraphTitle=Análise Escore de condição corporal
BCSHerdAnalysisResultsViewModel.MaxBCS=ECC max.
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Leite/Vaca/dia
BCSHerdAnalysisResultsViewModel.MinBCS=ECC min.
BCSHerdAnalysisResultsViewModel.SubHeading=Análise do Rebanho
BCSHerdAnalysisResultsViewModel.Title=Escore de Condição Corporal
BCSPenSelectionViewModel.PenSelectionList=LOTES
BCSPenSelectionViewModel.Pens=Lotes
BCSPenSelectionViewModel.SelectPointScale=Selecionar escala 
BCSPenSelectionViewModel.Title=Escore de Condição Corporal
BCSSelectPointScaleViewModel.FooterText=Somente uma escala pode ser usado por visita. Mudando a escala, perderá todos os valores
BCSSelectPointScaleViewModel.SelectPointScale=Selecionar escala 
BCSSelectPointScaleViewModel.Title=Escore de Condição Corporal
BGL=BGL
BRL=Brasil (R$ BRL)
BRR=BRR
Bad=Ruim
Bag=Silo em Bag
BaggedConventionalSilage=Bolas de Silagem
Bahamas=Bahamas
Bahia=Bahia
Bahrain=Bahrein
Baja_California=Baja California
Baja_California_Sur=Baja California Sur
Baleage=Silagem em Bolas
BaleageFQAs=FAQs Silagem em Bolsas
Baleage_AreBalesWrappedWith=Bolas estão enroladas com\:
Baleage_BagsPlacedOnStableWellManagedSurface=Bolas estão em local estável e protegido (asfalto ou concreto)?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Monitorado para danos por pragas /animais e reparado regularmente? (semanal)
Baleage_TrashVegRodentControlledAroundBags=Lixo, vegetação e roedores são controlados ao redor das bolsas?
Baleage_WaterShedsOffPlasticNotIntoBaleage=Água não penetra nas bolsas?
Bangladesh=Bangladesh
Barbados=Barbados
Bari=Eles eram
Barletta-Andria-Trani=Barletta-Andria-Trani
Bayern=Bayern
BeddedPack=Cama de Composto
Beijing=Pequim
Belarus=Bielorrússia
Belgium=Bélgica
Belize=Belize
Belluno=Belluno
Benchmarks_Serum_ToolTip=<value />
Benevento=Benevento
Benin=Benin
Bergamo=Bergamo
Bermuda=Bermudas
BetweenFifteenTwenty=Between 15 and 20
Bhutan=Butão
BiWeekly=Bi-Weekly
Biella=Biella
Bihar=Bihar
BodyConditionHerdGoals=Metas - Análise Condição Corporal Rebanho
BodyConditionHerdInputs=Inputs - Análise Condição Corporal Rebanho
BodyConditionHerdResults=Resultados Condição Corporal do Rebanho
BodyConditionInputs=Inputs Condição Corporal 
BodyConditionResults=Resultados Condição Corporal 
BodyConditionScoreCategory=Categoria Escore de Condição Corporal {0}
BodyConditionScoreEditInputsViewModel.Count=Contagem
BodyConditionScoreEditInputsViewModel.NumberOfCows=Número de vacas
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Por favor conte o número de vacas
BodyConditionScoreEditInputsViewModel.Title=Número de vacas
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=Pré Parto (-21 a -1)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=Início de lactação (16 a 60)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=Vaca Seca (mais de 21)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=Pós Parto (0 a 15)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=Fim de lactação (maior que 201)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=Máximo ECC
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=Meio da Lactação (121 a 200)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=Mínimo ECC
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=Pico de lactação (61 a 120)
BodyConditionScoreHerdEditGoalsViewModel.Title=Editar os objetivos
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Pré Parto (-21 a -1)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Início de lactação (16 a 60)
BodyConditionScoreHerdGoalsViewModel.Edit=Editar
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Vaca Seca (mais de 21)
BodyConditionScoreHerdGoalsViewModel.Fresh=Pós Parto (0 a 15)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Objetivo Máx ECC
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=ECC - Objetivo Mín
BodyConditionScoreHerdGoalsViewModel.LateLactation=Fim de lactação (maior que 201)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Meio da Lactação (121 a 200)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Pico de lactação (61 a 120)
BodyConditionScoreHerdGoalsViewModel.TableTitle=ECC por fase de lactação
BodyConditionScoreInputsViewModel.AnimalsObserved=Animais Observados
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Méd. ECC (calculada)
BodyConditionScoreInputsViewModel.BCSCategory=Categoria ECC
BodyConditionScoreInputsViewModel.BCSPercentOfPen=% do lote
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=Escore de Condição Corporal (ECC)
BodyConditionScoreInputsViewModel.Edit=Editar
BodyConditionScoreInputsViewModel.StdDevCalculated=Variação (calculada)
BodyConditionScoreMasterViewModel.Goals=Objetivos
BodyConditionScoreMasterViewModel.Inputs=Inputs
BodyConditionScoreMasterViewModel.Results=Resultados
BodyConditionScoreMasterViewModel.SubHeading=Análise do Rebanho
BodyConditionScoreMasterViewModel.Title=Escore de Condição Corporal
BodyConditionScoreResultsViewModel.BCSAverageTitle=Escore médio
BodyConditionScoreResultsViewModel.PercentPen=% do lote
BodyConditionScoreResultsViewModel.SelectedDates=Selecionar datas
BodyConditionScoreResultsViewModel.Title=Resultados ECC
BodyConditionScoresMasterViewModel.BodyConditionScore=Escore de Condição Corporal
BodyConditionScoresMasterViewModel.Inputs=Inputs
BodyConditionScoresMasterViewModel.Results=Resultados
BodyConditionScoresMasterViewModel.Title=Escore de Condição Corporal
BodyConditionScoresMasterViewModel.VisitNotebook=Anotações da visita
Bolivia,_Plurinational_State_of=Bolívia, estado plurinacional de
Bologna=Bolonha
Bolzano=Bolzano
Bonaire=Bonaire
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius e Saba
Bosnia_and_Herzegovina=Bósnia e Herzegovina
Botswana=Botswana
BottomUnloadingSilo=Silo Descarregado por Baixo
Bouvet_Island=Ilha Bouvet
Brazil=Brasil
Brescia=Brescia
Brindisi=Torradas
British_Columbia=Columbia Britânica
British_Indian_Ocean_Territory=Território do Oceano Índico Britânico
Brunei_Darussalam=Brunei Darussalam
Bulgaria=Bulgária
Bull=Touro
Bunker=Silo de trincheira
BunkerCapacity=Capacidade do Silo
BunkerFeedOutRate=Taxa de remoção do silo
Bunkers=Silo Trincheira
BunkersAndPiles=Silos Trincheira e de Superficie
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Bônus\: 2 camadas de lona plástica com 1 camada de plástico impermeável.
BunkersAndPiles_CleanlinessOfFeedArea=Limpeza da área de alimentação? Classificar de 1 - 10, sendo 10 o melhor
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=Cobertura de plástico removida da silagem?
BunkersAndPiles_FaceRemoveRate=Taxa de remoção da frente
BunkersAndPiles_LooseOrFacedFeedIsFed=Se utiliza face do silo ou silo perdido?
BunkersAndPiles_PackingInitialSpreadLayers=Durante a ensilagem, a camada inicial possui 6 polegadas ou menos?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=A inclinação da rampa do silo não deve ser menor do que 3\:1 (18 graus)
BunkersAndPiles_PorosityScoresConsistently=Porosidade é consistente
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Silo é fechado imediatamente após o enchimento?
BunkersAndPiles_SideWallsSealedPlastic=As paredes laterais são seladas com plástico?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Face lisa, sem indicações de camadas interrompidas permitindo penetração de oxigênio
BunkersAndPiles_TiresSplitsTouching=Pneus / sacos?
Burkina_Faso=Burkina Faso
Burundi=Burundi
CAD=Canadá (CA$ CAD)
CFNChina=China
CFNIndia=Índia
CHF=Suíça (CHF CHF)
CLF=CLF
CLP=Chile ($ CLP)
CNY=China (CNY CNY)
COP=COP
CPNBrazil=Brasil
CPNFrance=França
CPNPoland=Polônia
CPNUS=Estados Unidos da América
CRC=CRC
CZK=República Tcheca (CZK CZK)
Cagliari=Cagliari
Calf=Bezerra
CalfHeiferColostrum=Escore card bezerras e novilhas - colostro
CalfHeiferGrowerPuberty=Escore card bezerras e novilhas - crescimento, puberdade, prenhez e período seco
CalfHeiferKeyBenchmarks=Escore card bezerras e novilhas - Pontos Chave
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=A cor da seção é decidido pelo intervalo\: &lt;75%\: vermelho, &gt;\=75 e &lt;90\: laranja, &gt;\=90\: verde
CalfHeiferPostweaned=Escore card bezerras e novilhas - pós desmama
CalfHeiferPreweaned=Escore card bezerras e novilhas - pré desmama
CalfHeiferQuestionViewModel.Close=Fechar 
CalfHeiferQuestionViewModel.Colostrum=Colostro 
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Crescimento, Puberdade, Prenhez, Pré parto 
CalfHeiferQuestionViewModel.KeyBenchmarks=Pontos chave
CalfHeiferQuestionViewModel.Postweaned=Pós desmame
CalfHeiferQuestionViewModel.Preweaned=Pré desmame
CalfHeiferQuestionViewModel.Resources=Recursos 
CalfHeiferQuestionViewModel.VisitNotebook=Anotações da visita
CalfHeiferResources=Escore card bezerras e novilhas - Recursos
CalfHeiferResults=Escore card bezerras e novilhas - Resultados
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Escore de bezerras e novilhas
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Crescimento, Puberdade, Prenhez, Pré parto 
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Incluir na pontuação total 
CalfHeiferScoreCardScoreViewModel.PhaseOne=Colostro 
CalfHeiferScoreCardScoreViewModel.PhaseThree=Pós desmame
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Pré desmame
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Abaixo as categorias para ver o resultado por fase 
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=A cor da seção é decidido pelo intervalo\: igual a 100%\: verde, &amp;lt;100%\: vermelho
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Dados arquivados?
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Pontos chave
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Fase 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Fase 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Fase 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Fase 7 
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Fase 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Fase 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Mantém dados de crescimento e saúde arquivados 
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Prenhês&amp;\#xA;15 - 23 meses
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Puberdade&amp;\#xA;9 - 15 meses
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Colostragem&amp;\#xA;1 - 3 dias
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Período seco / Produção&amp;\#xA;23 - 26 meses
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Crescimento&amp;\#xA;3 - 9 meses
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Pré/Pós desmama&amp;\#xA;0 - 3 meses
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Anotações da visita
CalfHeiferScorecardLanding=Escore card bezerras e novilhas
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Pontos chave
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Melhorias 
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Respostas
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Escore
CalfHeiferScorecardResultsViewModel.VisitNotebook=Anotações da visita
CalfHeiferScorecardViewModel.Colostrum=Colostro 
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Crescimento, Puberdade, Prenhez, Pré parto 
CalfHeiferScorecardViewModel.KeyBenchmarks=Pontos chave
CalfHeiferScorecardViewModel.Postweaned=Pós desmame
CalfHeiferScorecardViewModel.Preweaned=Pré desmame
CalfHeiferScorecardViewModel.Resources=Recursos 
CalfHeiferScorecardViewModel.Title=Escore card
CalfHeiferScorecardViewModel.VisitNotebook=Anotações da visita
CalfHeiferTools=Ferramentas de bezerras e novilhas
CalfHeiferToolsViewModel.CalfHeiferScorecard=Escore card
CalfHeiferToolsViewModel.CalfHeiferTools=Ferramentas de bezerras e novilhas
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Ferramentas 
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Selecione uma ferramenta da lista abaixo para iniciar sua visita 
CalfHeiferToolsViewModel.CalfHeiferToolsList=Ferramentas 
CalfHeiferToolsViewModel.Title=Ferramentas de bezerras e novilhas
CalfHeiferToolsViewModel.VisitNotebook=Anotações da visita
California=Califórnia
Caltanissetta=Caltanissetta
Cambodia=Camboja
Cameroon=Camarões
Campeche=Campeche
Campobasso=Campobasso
Canada=Canadá
Capacity=Capacidade
Cape_Verde=Cabo verde
Carbonia-Iglesias=Carbonia-iglesias
Cargill=Cargill
CargillForageLabKPTest=Análises de processamento de grãos Laboratório Cargill
Carlow=Carlow
Caserta=Caserta
Catania=Catania
Catanzaro=Catanzaro
Category1=Escore de fezes 1.0
Category2=Escore de fezes 2.0
Category3=Escore de fezes 3.0
Category4=Escore de fezes 4.0
Category5=Escore de fezes 5.0
Cavan=Cavan
Cayman_Islands=Ilhas Cayman
Ceará=Quadrado
Central_African_Republic=República da África Central
Chad=Chade
Chandigarh=Chandigarh
ChewsPerCud=Mastigação por bolo alimentar
ChewsPerCudMasterViewModel.AddNew=Adicionar Novo
ChewsPerCudMasterViewModel.CudChewingInputs=Inputs
ChewsPerCudMasterViewModel.CudChewingResults=Resultados
ChewsPerCudMasterViewModel.NumOfChews=Número de mastigações
ChewsPerCudMasterViewModel.Title={0} / \# de mastigações
Chhattisgarh=Chhattisgarh
Chiapas=Chiapas
Chieti=Chieti
Chihuahua=Chihuahua
Chile=Chile
China=China
Chinese_Taipei=Taipei Chinês
Chongqing=Chongqing
ChooseAppPDF=Escolha um aplicativo para abrir o PDF
ChoosingtheCorrectAdditive=Escolhendo o aditivo correto
Christmas_Island=Ilha do Natal
Clare=Clare
ClassSubClass=Classe Animal / Sub Classe
Clean=Limpa
ClinicalMastitisLosses=Perdas por Mastite Clínica
Clique=no nome de um Lote para atualizar os seus dados.
CloseUp=Pré Parto
CloseUpDry=Pré-parto
CloseUpHeifer=Novilhas Pré Parto
Coahuila=Coahuila
Cocos_(Keeling)_Islands=Ilhas Cocos (Keeling)
Colima=COLIMA
Colombia=Colômbia
Colorado=Colorado
Colostrum=Colostro 
Colostrum_AmountOfColostrumOrFed=Quantidade de colostro administrada
Colostrum_BrixPercentOfColostrumFed=Brix % do colostro fornecido
Colostrum_CleanAndDryCalvingArea=Área do parto limpa e seca
Colostrum_CleanAndDryCalvingArea_ToolTip=Usar o teste dos joelhos para determinar se a área é limpa e seca 
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Os equipamentos utilizados para alimentação são limpos e desinfetados entre alimentações
Colostrum_CleanCalfCartToTransportCalf=Limpeza do carrinhos de transporte das bezerras
Colostrum_HoursTillCalfIsRemovedFromMother=Horas entre o nascimento e a separação do bezerro da mãe
Colostrum_HoursTillCalfReceivesColostrum=Horas até a bezerra receber o colostro
Colostrum_NumberOfCowsInCalvingArea=Número de vacas na área de parto
Colostrum_PasteurizeColostrumBeforeFeeding=Colostro pasteurizado foi administrado
Colostrum_PercentageOfNavelsDippedInSevenPercent=% dos umbigos curados em iodo 7% em 1 hora
Colostrum_RefrigeratedColostrumStoredLess=Colostro refrigerado armazenado em menos de 24 horas
ComfortToolsViewModel.ComfortHeading=Selecione uma ferramenta da lista abaixo para iniciar sua visita
ComfortToolsViewModel.ComfortToolsList=FERRAMENTAS
ComfortToolsViewModel.ComfortToolsTitle=Ferramentas de Conforto
ComfortToolsViewModel.HealthHeading=Selecione uma ferramenta da lista abaixo para iniciar sua visita
ComfortToolsViewModel.HeatstressEvaluationTitle=Avaliação de Estresse Térmico
ComfortToolsViewModel.PenTimeTitle=Tempo de descanso
ComfortToolsViewModel.Title=Ferramentas de Conforto
ComfortToolsViewModel.VisitNotebook=Anotações da visita
Comments=Comentários
CommonSpinnerViewModel.BodyConditionPDF=Guia de Escore de Condição Corporal
CommonSpinnerViewModel.InadequateStimulation=Estímulo inadequado     (preparação inadequada dos tetos\: &lt;10 segundos; Colocação das teteiras\: &lt; 60 ou &gt; 120 segundos)
CommonSpinnerViewModel.LocomotionPDF=Guia de Escore de Locomoção
CommonSpinnerViewModel.ManureScorePDF=Guia de Escore de Fezes
CommonSpinnerViewModel.NoStimulation=Sem estímulo                      (Colocação de teteiras apenas)
CommonSpinnerViewModel.OptimalStimulation=Estímulo ótimo                 (preparação adequada dos tetos\: 10-20 segundos; Colocação das teteiras dentro de  60 a 120 segundos)
Como=Como
Comoros=Comores
Competitor=Concorrente
CompletedTimeKey=Tempo Completado
Component=Componente
Compostbarn=Compostbarn
ConcentrateDistribution=Distribuição da ração
ConfirmExport=Pressionando OK irá carregar todas as páginas selecionadas e tirar uma foto antes de retornar para essa tela
ConfirmScalePointSwitch=Alterando a escala irá resetar todos os dados inseridos
ConfirmScorerSwitch=Alterando o escore irá resetar todo dado inserido
Congo=Congo
Congo,_the_Democratic_Republic_of_the=Congo, a República Democrática do
Connecticut=Connecticut
Consumer=Clientes
ConsumerDetailsViewModel.DeleteProspect=Deletar cliente
ConsumerDetailsViewModel.DeleteProspectPrompt=Você tem certeza que deseja deletar esse cliente? Informações do cliente, local e visita em andamento serão perdidas.
ConsumerDetailsViewModel.ProspectTitle=Detalhes do cliente 
ConsumersViewModel.NewConsumer=Adicionar Novo Cliente
Continue=Continuar
Cook_Islands=Ilhas Cook
Cork=Cortiça
Corn=Milho
CornSilage=Corn Silage
CornSilageKernel=FAQs Silagem em Bolsas
CornSilageResources=Recursos Silagem de Milho
CornSilageStopGo=Check-List Silagem de Milho
Cosenza=Cosenza
Costa_Rica=Costa Rica
Costs=Custos
Cote_d'Ivoire=Costa do Marfim
Count=Contagem
Cow=Vaca
CowEfficiency=Eficiência da vaca
CowPerRobot=Vacas por robô
CowsOutsideTargetRangeToolTip=Objetivo é ter &lt;20% das vacas fora do intervalo alvo
CowsPerDayNeeded=Vacas/dia necessárias
CowsSectionToolTip=Um grupo de 8 vacas ou mais deve ser testado para tirar conclusões. Em rebanhos pequenos deve se testar todos os animais no pré parto
CowsToBeFed=Vacas para serem alimentadas
CreateDuplicateDiet=Uma dieta já existe para essa categoria. Ainda assim deseja criar?
CreateDuplicateNameDiet=Uma dieta já existe com o mesmo nome, por favor inserir um nome diferente
Created=Criado
Cremona=Cremona
Croatia=Croácia
CropCharacteristicsDecisionGuide=Guia de Decisão para características da cultura
Crotone=Crotone
Cuba=Cuba
CudChewingAverageNumber=Número médio
CudChewingDataEntryViewModel.CudChewing=Ruminação
CudChewingDataEntryViewModel.HerdCudChewingDescription=Para essa visita, contar o número de animais ruminando nesse lote usando a contagem abaixo. Necessário contar um mínimo de 10 vacas.
CudChewingDataEntryViewModel.No=Não
CudChewingDataEntryViewModel.Yes=Sim
CudChewingHerdEditScoreViewModel.AverageChewsItem=Média de mastigação por bolo alimentar
CudChewingHerdEditScoreViewModel.Close=Fechar
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Dias em Lactação 
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Editar Escore de ruminação
CudChewingHerdEditScoreViewModel.EditScoreTitle=Editar Escore de ruminação
CudChewingHerdEditScoreViewModel.PercentChewingItem=% Ruminação
CudChewingMasterViewModel.CudChewing=Ruminações
CudChewingMasterViewModel.CudChewingInputs=Inputs
CudChewingMasterViewModel.CudChewingResults=Resultados 
CudChewingPen=Lote 
CudChewingPercentChewing=% Ruminação
CudChewingPercentGoal={0}% Objetivo
CudChewingPercentOfPen=Ruminações (% lote)
CudChewingViewModel.CudChewing=Lote
CudChewingViewModel.CudChewingList=Lotes (lactação e vaca seca somente)
CudChewingViewModel.CudChewingTitle=Nome do Lote
CudChewingViewModel.Title=Lotes
CudChewsCalculatorViewModel.CalculatorHeading=Selecione uma vaca e conte o número de mastigações. Clique em "adicionar novo" acima para adicionar vacas para a contagem
CudChewsCalculatorViewModel.CudChewCategorySection=Vacas 
CudChewsCalculatorViewModel.NumOfChews=\# de Mastigações
CudChewsDatesForComparisonViewModel.CudChewsPercent=% Ruminações
Cundinamarca=Cundinamarca
Cuneo=Cunha
Curaçao=Curaãçao
Current=Atual
CurrentDownResponse=Queda de produção atual ({0}/vaca/dia)
CurrentMilkPrice=Preço do leite atual ({0}/{1})
CurrentSCC=CSS atual (cels/{0})
CurrentVisitSummary=Resumo Visita Atual
Customer=Cliente
CustomerDetailViewModel.CustomerTitle=Perfil do Cliente
CustomerDetailViewModel.MainHeading=Local
CustomerDetailViewModel.NewSite=Adicionar Novo Local
CustomerDetailViewModel.NewVisit=Nova visita
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=Usuário final
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Selecionar segmento
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence 
CustomerProspectsSegmentViewModel.Title=Detalhes
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=Nome do Cliente - Nome do local
Cyprus=Chipre
CzechRepublic=República Tcheca
Czech_Republic=República Checa
DDW=Dados da fazenda
DDWOfflineMessage=Como não há computador, deseja visualizar o relatório offline?
DDWUpdatedTime=Última atualização de dados do rebanho\: {0}
DKK=DKK
DZD=Argelia (DA DZD)
Dadra_and_Nagar_Haveli=Dadra e Nagar Haveli
Daily=Diariamente
DairyEnteligenFarmReportsources=Fonte de informação do Dairy Enteligen
Daman_and_Diu=Daman e Diu
DashboardViewModel.Alert=Alerta\!
DashboardViewModel.AlertMessage=Dados não sincronizados há {0} dias
DashboardViewModel.GoodAfternoon=Boa tarde,
DashboardViewModel.GoodMorning=Bom dia, 
DashboardViewModel.MessageBody=Lembrete para utilizar o computador em cada visita 
DashboardViewModel.MessageHeader=Mensagem
DashboardViewModel.RecentSiteVisit=Visitas recentes
DashboardViewModel.UserPreferences=Preferências do usuário
Date=Data
DateGone=Data final
DatesForComparison=Datas de comparações
Days=Dias 
DaysInMilkItem=Dias em Lactação (DEL)
DeLaval=DeLaval
DeathLoss=Morte
DecidingSilageStorage=Decisão sobre Tipo de Silo
Delaware=Delaware
Delete=Deletar
DeleteMatrixValue=Tem certeza que quer deletar essa matriz de valores?
Delhi=Délhi
Denmark=Dinamarca
DensityLossesinPressedBagSilos=Densidade e Perdas em Silos Bolsa
Diet=Dieta
DietDCAD=Dieta DCAD mEq/100g
DietDetailViewModel.Created=Criado
DietDetailViewModel.DDW=Dados da fazenda
DietDetailViewModel.Max=Máx
DietDetailViewModel.Title=Detalhes da dieta
DietDetailViewModel.UserCreated=pelo usuário 
DietListViewModel.InfoNewDiet=Os nomes das dietas serão atualizados automaticamente se o MAX estiver conectado com o local da fazenda no Dairy Enteligen. Entretanto, pode adicionar dietas manualmente ou deixar essa lista em branco e selecionar a classe animal / subclasse por cada lote. Uma vez a dieta criada, selecione a classe / subclasse dos animais associados com a dieta.
DietListViewModel.MainHeading=Dietas
DietListViewModel.New=Novo
DietListViewModel.NewDiet=Adicionar Nova Dieta
DietListViewModel.Title=Dietas
Digite=o número de vacas pós parto e o número de casos de incidência metabólica durante o período de avaliação. Isso será convertido em um custo de incidência anual na tela de resultados
Dirty=Suja
DisplacedAbomasum=Deslocamento de abomaso
District_of_Columbia=Distrito da Colombia
Distrito_Federal=Distrito Federal
Djibouti=Djibuti
DoNotTest=&amp;lt;20% ou não testar 
Dominica=Dominica
Dominican_Republic=República Dominicana
Donegal=Donegal
DontKnow=Não sei 
DownResponse=Resposta de queda do leite  ({0}/vaca/dia)
Dry=Seco
DryCow=VACAS SECAS
DryLot=Lote Seco
Dryhay=Feno
Dublin=Dublin
DuplicatePenName=Já existe um lote com esse nome. Escolha um nome diferente
Durango=Durango
Dystocia=Distocia
EGP=EGP
EarlyLactation=Início da Lactação
Ecuador=Equador
Edit=Editar
EditDatesForComparison=Editar datas de comparações
EditDatesForComparisonViewModel.Chews=Ruminações
EditDatesForComparisonViewModel.EditDatesClose=Fechar
EditDatesForComparisonViewModel.EditDatesLabel=Selecione as datas para comparações do lote através da lista abaixo
EditDatesForComparisonViewModel.EditDatesTitle=Editar datas de comparações
EditDatesForComparisonViewModel.EditDatesVisits=Visitas 
EditDatesForComparisonViewModel.LocomotionScoreAverage=Média Escore de Locomoção\:
EditDatesForComparisonViewModel.ManureScoreAverage=Média Escore de Fezes\:
EditDatesForComparisonViewModel.MetabolicIncidence=Selecione até 5 visitas para comparações da lista abaixo
EditDatesForComparisonViewModel.PenTimeBudget=Selecione até 7 visitas para comparações da lista abaixo
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Seleciona uma data abaixo para comparar com a visita atual
EditDatesForComparisonViewModel.TimeRemainingForResting=Tempo remanescente para descanso
EditDatesForComparisonViewModel.Title=Editar datas de comparações
EditDatesForComparisonViewModel.Visits=Visitas
EditGoalsCudChewingViewModel.Close=Fechar
EditGoalsCudChewingViewModel.CloseUpDry=Pré-Parto
EditGoalsCudChewingViewModel.CudChews=Média de mastigação por bolo alimentar
EditGoalsCudChewingViewModel.EarlyLactation=Início da Lactação
EditGoalsCudChewingViewModel.EditGoalsTitle=Editar objetivos de ruminação
EditGoalsCudChewingViewModel.FarOffDry=Vaca Seca 
EditGoalsCudChewingViewModel.Fresh=Recém parida
EditGoalsCudChewingViewModel.LateLactation=Final de lactação
EditGoalsCudChewingViewModel.MidLactation=Meio de lactação
EditGoalsCudChewingViewModel.PeakMilk=Pico de lactação 
EditGoalsCudChewingViewModel.PercentChewing=% de Ruminação
EditNoteViewModel.Action=Ação
EditNoteViewModel.Cancel=Cancelar
EditNoteViewModel.Category=Categoria
EditNoteViewModel.Close=Fechar
EditNoteViewModel.CreatedByMetadata=Criado em {0} @ {1} por {2}
EditNoteViewModel.Delete=Deletar
EditNoteViewModel.DeleteImageButtonText=Deletar imagem
EditNoteViewModel.DeleteImagePrompt=Você gostaria de deletar essa imagem
EditNoteViewModel.DeletePrompt=Você gostaria de deletar essa anotação?
EditNoteViewModel.DeleteVideoButtonText=Deletar vídeo
EditNoteViewModel.DeleteVideoPrompt=Você gostaria de deletar esse vídeo?
EditNoteViewModel.Event=Evento
EditNoteViewModel.LastUpdatedByMetadata=Última modificação em {0} @ {1} por {2}
EditNoteViewModel.NoteCamcorderNotImplemented=Recurso de câmera de vídeo ainda não implementado
EditNoteViewModel.NoteGalleryNotImplemented=Recurso de galeria ainda não implementado
EditNoteViewModel.NoteLabel=Anotações 
EditNoteViewModel.NoteOnlyOneImage=Somente uma imagem é permitida por anotação. Por favor delete a imagem atual primeiro
EditNoteViewModel.NoteOnlyOneVideo=Somente um vídeo é permitido por anotação. Por favor delete o vídeo atual primeiro
EditNoteViewModel.Observation=Observação
EditNoteViewModel.Save=Salvar
EditNoteViewModel.Task=Tarefa
EditNoteViewModel.Title=Anotação
EditNoteViewModel.TitleLabel=Título
Egypt=Egito
El_Salvador=O salvador
EmailReportViewModel.AnimalImpact=Impacto animal
EmailReportViewModel.CalfHeiferItem=Bezerras e Novilhas
EmailReportViewModel.CalfHeiferScorecard=Escore card
EmailReportViewModel.Capacity=Capacidade 
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Lista de categorias
EmailReportViewModel.Charts=Gráficos
EmailReportViewModel.CoefficientVariation=C.V. (%)
EmailReportViewModel.ComfortHeatStressBanner=Ferramenta Estresse Térmico lote
EmailReportViewModel.ComfortItem=Ferramenta de Conforto
EmailReportViewModel.ComfortPenTimeBanner=Ferramenta de Calcular Tempo de Descanso
EmailReportViewModel.ComfortToolsTitle=Ferramenta de Conforto
EmailReportViewModel.CowsOutsideTargetRange=Vacas fora do range (%)
EmailReportViewModel.CudChewingTitle=Ruminações
EmailReportViewModel.DietDCADStr=Dieta DCAD 
EmailReportViewModel.EmailBody={0}-{1} Relatório
EmailReportViewModel.EmailSelectedTools=Enviar email com as ferramentas selecionadas
EmailReportViewModel.EmailSubject={0} Relatório
EmailReportViewModel.ExportSelected=Enviar email com as ferramentas selecionadas
EmailReportViewModel.FeedOut=Consumo
EmailReportViewModel.ForageAuditScorecard=Escore de Auditoria de forragem 
EmailReportViewModel.ForageImprovements=Forage Audit Scorecard
EmailReportViewModel.ForageLanding=Página inicial de Auditoria de Forragens
EmailReportViewModel.ForageScorecard=Escore card Auditoria de Forragens
EmailReportViewModel.GeneratingReport=Gerando relatório...
EmailReportViewModel.GotoMarketBranding=Marketing
EmailReportViewModel.HealthItem=Ferramenta de Saúde
EmailReportViewModel.HeatstressEvaluationTitle=Avaliação Estresse Térmico
EmailReportViewModel.Herd=Rebanho
EmailReportViewModel.HerdAnalysis=Análises do Rebanho
EmailReportViewModel.HerdGoals=Objetivos - Análises do Rebanho
EmailReportViewModel.HerdInputs=Análise do Rebanho - Entradas
EmailReportViewModel.HerdResults=Análise do Rebanho - Resultados
EmailReportViewModel.HerdRevenue=Análise do Rebanho - Receita
EmailReportViewModel.Improvements=Melhorias
EmailReportViewModel.Improvements1=Melhorias 
EmailReportViewModel.Inputs=Inputs
EmailReportViewModel.InputsOutputsChart=Inputs / Resultados / Gráficos
EmailReportViewModel.LocomotionScoreTitle=Escore de Locomoção
EmailReportViewModel.ManureScoreTitle=Escore de Fezes
EmailReportViewModel.MarketBranding=Marca Go To Market
EmailReportViewModel.MetabolicIncidenceTitle=Incidência Metabólica
EmailReportViewModel.MilkProcessCalcInputsTab=Receita da ordenha\: inputs
EmailReportViewModel.MilkProcessCalcResourcesTab=Receita da ordenha\: recursos
EmailReportViewModel.MilkProcessCalcResultsTab=Receita da ordenha\: resultados
EmailReportViewModel.MilkProcessRevenue=Receita da ordenha calculadora
EmailReportViewModel.MilkProcessRevenueCalculator=Receita da ordenha calculadora
EmailReportViewModel.MilkingTime=Tempo de ordenha
EmailReportViewModel.Notes=Anotações
EmailReportViewModel.NumOfChews=Número de mastigações
EmailReportViewModel.NutritionForage=Auditoria de Forragens
EmailReportViewModel.NutritionItem=Nutrição
EmailReportViewModel.NutritionPile=Capacidades dos silos de superfície e trincheira
EmailReportViewModel.Outputs=Resultados
EmailReportViewModel.PenCompare=Análise do lote - comparação
EmailReportViewModel.PenDensity=Densidade do lote
EmailReportViewModel.PenInputs=Análise do lote - Entradas
EmailReportViewModel.PenResults=Análise do lote - Resultados
EmailReportViewModel.PenTimeTitle=Orçamento de Tempo de Descanso
EmailReportViewModel.PileAndBunkerTitle=Silo de superfície e trincheira
EmailReportViewModel.PileBunkerCapacities=Capacidade dos silos
EmailReportViewModel.ProductivityItem=Ferramenta de Produtividade
EmailReportViewModel.Provimi=Promivi
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Recursos 
EmailReportViewModel.Results=Resultados
EmailReportViewModel.RumenHealthBodyConditionTitle=Escore de Condição Corporal
EmailReportViewModel.RumenHealthLocomotionTitle=Escore de Locomoção
EmailReportViewModel.RumenHealthManureTitle=Saúde de rúmen - Escore de fezes
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Incidência Metabólica
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
EmailReportViewModel.RumenHealthTMRTitle=Saúde Ruminal - escore de TMR 
EmailReportViewModel.RumenHealthTitle=Saúde Ruminal - número de mastigações
EmailReportViewModel.RumenHealthUrinePHTitle=pH urinário
EmailReportViewModel.ScoreScreen=Escore
EmailReportViewModel.TMRParticleScoreTitle=Escore  Saúde de Rúmen
EmailReportViewModel.TimeBudget=Orçamento de Tempo
EmailReportViewModel.Title=Enviar relatório por email 
EmailReportViewModel.UrinePhSTDDEV=Desvio padrão 
EmailReportViewModel.UserPreferences=Configurações do usuário
EmailReportViewModel.UserSettings=Configurações do usuário
EmailReportViewModel.WalkthroughReportTitle=Relatório durante a visita
EnergyImperial=Mcal/lb
EnergyMetric=Mcal/Kg
Enna=Enna
Equatorial_Guinea=Guiné Equatorial
Eritrea=Eritreia
ErrorDescription=Um erro ocorreu durante a leitura ou escrita da sua informação
ErrorTitle=Erro
Espírito_Santo=Espírito Santo
Estonia=Estônia
Estresse=calórico grave\: babando com a boca aberta, taxa de respiração de 120 a mais de 160 BPM
Ethiopia=Etiópia
Eula=Contrato de licença para usuário final
Euro=Países da Zona do Euro (€ EUR)
Event=Evento
EveryOtherDay=Dias alternados
Excessive=&gt;0.5pt ECC
ExtraDaysOpenCostInfoMessage=Tipicamente varia de $3 a $5 por dia em aberto
FAQDairyEnteligenFarmReportandDDW=FAQ Relatório da fazenda - Dairy  Enteligen 
Falkland_Islands_(Malvinas)=Ilhas Falkland (Malvinas)
FarOff=Vacas secas
FarOffDry=Vaca seca
Faroe_Islands=ilhas Faroe
Federal_District=Distrito Federal
FeedFirst=Alimentado primeiro
FeedOutRateInfo=Informação sobre Taxa de Retirada de Alimentos
FeedOutRatesFilmsStorageSysExamined=Avaliação de Taxas de Retirada, Lonas e Sistemas de Armazenamento
FeedOutSurfaceAreaImperial=Área de superfície de alimentação (ft^2)
FeedOutSurfaceAreaMetric=Área de superfície de alimentação (m^2)
FeedingRate=Taxa de alimentanção (materia natural/vaca)
FeedoutLossesForageStorageSys=Perdas devido ao sistema de armazenamento de forragem
FermentationAnalysisSilageQT=Análise de Fermentação e Teste de Qualidade da Silagem
Fermo=Parou
Ferrara=Ferrara
FieldKPTest=Teste de Campo de Processamento de Grãos
Fiji=Fiji
FillAllFields=Preencher todos os campos
FillAllMandatoryFields=Preencher todos os campos obrigatórios
FinalObservations=Obervações finais
Finish=Finalizado
Finland=Finlândia
Florence=Florence
Florida=Flórida
Foggia=Foggia
ForageAuditScorecard=Escore card Auditoria de Forragens
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Melhorias da auditoria de forragens
ForageAuditScorecardResponsesViewModel.ResponsesTab=Respostas da auditoria de forragens
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Melhorias
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Respostas 
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Escore
ForageAuditScorecardResultsViewModel.ImprovementsTab=Melhorias
ForageAuditScorecardResultsViewModel.ResponsesTab=Respostas
ForageAuditScorecardResultsViewModel.ScoreTab=Escore
ForageAuditScorecardResultsViewModel.Title=Silos verticais
ForageAuditScorecardResultsViewModel.VisitNotebook=Anotações da visita
ForageAuditScorecardScoreViewModel.GoodIndicator=Bom
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Melhorias
ForageAuditScorecardScoreViewModel.OverallForageScore=Incluir no escore geral de forragens
ForageAuditScorecardScoreViewModel.Title=Escore geral da Auditoria de Forragens
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Forage silage types
ForageAuditViewModel.ForageAuditScorecard=Escore card Auditoria de Forragens
ForageAuditViewModel.ForageDetail=Qualidade das forragens é a base de todo programa nutricional e a chava para melhorar a lucratividade da fazenda. O escore da auditoria de forragem pode ser usado para avaliar as práticas de manejo de forragem utilizado na fazenda e recomendar melhorias em áreas que tenham oportunidade. O escore card é organizado entre áreas chave de gerenciamento. Pode-se passar por todas as áreas durante a visita ou selecionar aquelas que serão importantes para aquele momento. Recursos de gerenciamento estão disponíveis para melhorar as áreas onde existem oportunidades.
ForageAuditViewModel.ForageHeading=Informações básicas de forragens
ForageAuditViewModel.Resources=Recursos
ForageAuditViewModel.Title=Auditoria de Forragens
ForageAuditViewModel.VisitNotebook=Anotações da visita
ForageManagement_ForagesHarvestedAtProperMaturity=Are forages harvested at proper maturity for crop type and storage facility?
ForageManagement_ForagesHarvestedAtProperMoisture=Are forages harvested at proper moisture for crop type and storage facility?
ForageScorecardResultsViewModel.Title=Silagem em Bolsas
ForageScorecardViewModel.Baleage=Silagem em Bolsas
ForageScorecardViewModel.BunkersAndPiles=Silos de trincheira e de superfície
ForageScorecardViewModel.ForageAuditCategories=Forage Audit Categories
ForageScorecardViewModel.ForageAuditScore=Forage Audit Score
ForageScorecardViewModel.ForageAuditScorecard=Escore card Auditoria de Forragens
ForageScorecardViewModel.ForageCategoryTooltip=Forage quality is the foundation of any dairy nutrition program and is a key to the overall profitability of the farm. This tools can be used to evaluate the current forage management practices used on the farm and recommend the key opportunity areas for improvement
ForageScorecardViewModel.Harvest=Colheita
ForageScorecardViewModel.MaintainingForageQuality=Qualidade de mantença das forragens
ForageScorecardViewModel.No=Não 
ForageScorecardViewModel.SilageBags=Bolsas de Silagem
ForageScorecardViewModel.SurveyCategories=Escore card Auditoria de Forragens
ForageScorecardViewModel.SurveyOfForages=Levantamento das forragens
ForageScorecardViewModel.Title=Escore card Auditoria de Forragens
ForageScorecardViewModel.TowerSilos=Silos verticais
ForageScorecardViewModel.ViewOverallForageScore=Ver resumo do escore card de forragens
ForageScorecardViewModel.VisitNotebook=Anotações da visita
ForageScorecardViewModel.Yes=Sim
ForlÃ¬-Cesena=Forlã-Cesena
FourScreenNew=4 peneiras nova
FourScreenNewType=(4 mm)
FourScreenOld=4 peneiras antiga
FourScreenOldType=(1.18 mm)
FourToSevenDays=4 a 7 dias
France=França
FreeFlow=Fluxo livre
FreeFormReportViewModel.CalfHeiferItem=Bezerras e Novilhas
FreeFormReportViewModel.CalfHeiferScorecard=Escore card
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Gráficos
FreeFormReportViewModel.ComfortHeatStressBanner=Ferramenta de Estresse Térmico
FreeFormReportViewModel.ComfortItem=Ferramenta de Conforto
FreeFormReportViewModel.ExportSelected=Exportar ferramentas selecionadas
FreeFormReportViewModel.GeneralNotes=Anotações gerais da visita
FreeFormReportViewModel.HealthItem=Ferramentas de Saúde
FreeFormReportViewModel.Inputs=Inputs
FreeFormReportViewModel.KeyBenchmarks=Pontos chave
FreeFormReportViewModel.MarketingBranding=Marca Go To Market
FreeFormReportViewModel.MilkProcessCalcInputsTab=Receita da ordenha - inputs
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Receita da ordenha - recursos
FreeFormReportViewModel.MilkProcessCalcResultsTab=Receita da ordenha - resultados
FreeFormReportViewModel.MilkProcessRevenueCalculator=Receita da ordenha - calculadora
FreeFormReportViewModel.MilkSoldEvaluation=Avaliação dos Sólidos do Leite
FreeFormReportViewModel.Notes=Anotações
FreeFormReportViewModel.NutritionForage=Auditoria de Forragens
FreeFormReportViewModel.NutritionItem=Ferramenta de Nutrição
FreeFormReportViewModel.NutritionPile=Capacidades dos silos de superfície e trincheira
FreeFormReportViewModel.Outputs=Resultados
FreeFormReportViewModel.OverallImprovements=Melhorias gerais
FreeFormReportViewModel.OverallResponses=Respostas gerais
FreeFormReportViewModel.OverallScore=Escore geral
FreeFormReportViewModel.PenTimeTitle=Orçamento de Tempo de Descanso
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Consumos dos silos de superfície e trincheira
FreeFormReportViewModel.ProductivityItem=Ferramenta de Produtividade
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Resultados
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Escore de Condição Corporal
FreeFormReportViewModel.RumenHealthLocomotionTitle=Escore de Locomoção
FreeFormReportViewModel.RumenHealthManureTitle=Escore de fezes - Saúde ruminal
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Incidência Metabólica
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Escore TMR saúde de rúmen do rebanho
FreeFormReportViewModel.RumenHealthTMRTitle=Escore TMR saúde de rúmen
FreeFormReportViewModel.RumenHealthTitle=Saúde de rúmen - mastigação
FreeFormReportViewModel.RumenHealthUrinePHTitle=pH urinário
FreeFormReportViewModel.Title=Versão do relatório livre
FreeFormReportViewModel.VisitTitle=Nome da visita 
FreeFormReportViewModel.WalkThroughNotes=Anotações da visita
FreeHandNoteClearPaletteDialogMessage=Você gostaria de deletar tudo nessa página?
FreeHandNoteEditorPageTitle=Anotação a Mão
FreeHandNoteSaveUserDialogMessage=Você gostaria de salvar essa anotação?
Freestall=Freestall
French_Guiana=Guiana Francesa
French_Polynesia=Polinésia Francesa
French_Southern_Territories=Territórios do Sul da França
Fresh=Pós parto
FreshCow=PÓS PARTO
FreshHeifer=Novilhas Pós Parto
Frosinone=Frosinona
Fujian=Fujian
GBP=Reino unido (GBP GBP)
GEA=GEA
GTQ=Guatemala (Q GTQ)
Gabon=Gabão
Galway=Galway
Gambia=Gâmbia
Gansu=Gansu
General=Geral
Genoa=Génova
Georgia=Geórgia
Germany=Alemanha
GettingtheMostOutofYourForage=Obtendo o máximo proveito da sua forragem
Ghana=Gana
Gibraltar=Gibraltar
Girolando=Girolando
Global=Global
Goa=Goa
Goal=Objetivo
Goiás=Goiás
Good=Bom
Gorizia=Gorizia
GreaterThan8Hours=Mais que 8 horas
GreaterThanFive=Mais que 5 
GreaterThanSevenDays=Mais que 7 dias
GreaterThanSixHours=Mais que 6 horas
GreaterThanThirtySixInchesPerDay=Mais que 90 cm por dia
GreaterThanTwelveHours=Mais que 12 horas
GreaterThanTwenty=&gt;20
Greece=Grécia
Greenland=Groenlândia
Grenada=Granada
Grosseto=GrossEto
GrowerPubertyPregnancyCloseup=Crescimento, Puberdade, Prenhez, Pré parto 
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Lote limpo e seco
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Usar o teste dos joelhos para determinar se a área é limpa e seca 
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=O BCS desejado é alcançado em cada estágio de crescimento
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Evidência de fezes mole
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=Cocho de alimento é limpo diariamente, sendo removido as sobras
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Água limpa e de livre acesso está disponível
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=Sem evidências de contaminação na água
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Grupo com novilhas de tamanho uniforme
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Animais do lote devem ser do mesmo tamanho
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% de Superlotação
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Ração balanceada para meta de crescimento revisada com frequência
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Tamanho de espaço de cocho é adequado por novilha
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg precisa de 30cm, 270-400kg precisa de 38cm, &gt;400kg precisa de 46cm
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Espaço de cama é adequado por novilhas 
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg precisa de 4m2, 270-400kg precisa de 5m2, &gt;400kg precisa de 7m2
Guadeloupe=Guadalupe
Guam=Guam
Guanajuato=Guanajuato
Guangdong=Guangdong
Guangxi=Guangxi
Guatemala=Guatemala
Guernsey=Guernsey
Guerrero=Guerrero
Guinea=Guiné
Guinea-Bissau=Guiné-bissau
Guizhou=Guizhou
Gujarat=Gujarat
Guyana=Guiana
HKD=HKD
HNL=Honduras (HNL HNL)
HRK=HRK
HUF=Hungria (Ft HUF)
Hainan=Hainan
Haiti=Haiti
HalfPointScale=Meio ponto de escala
Harvest=Colheita
Harvest_AdequateEquipmentAndLabor=Equipamento e mão de obra adequados para colheita de safra
Harvest_CornSilageMoistureRangeConsistent=Faixa de umidade de silagem de milho é consistente com mais de 90% das amostras nessa faixa?
Harvest_ForageHarvestingDocumented=Condições de colheita de forragem, locais de armazenamento e estoque são documentados?
Harvest_ForagesHarvestedAtProper=Colheita de forragem ocorre com a correta maturidade e umidade adequadas para o tipo de cultura e instalação de armazenamento?
Harvest_KPScoreIsMonitored=Escore de processamento de grãos é monitorado usando o copo de 900g ou o método de flotação
Harvest_LengthOfCutMonitored=Comprimento de corte é monitorado com a ferramenta Penn State?
Harvest_UseSilageAdditive=Usa aditivos na silagem; inoculante ou estabilizador aeróbico?
Harvest_WholePlantMoistureDetermined=A umidade total de cada planta por plantação é determinada?
Haryana=Haryana
Hawaii=Havaí
Haylage=Pré secado
HealthToolsViewModel.HealthHeading=Selecione uma ferramenta da lista abaixo
HealthToolsViewModel.HealthToolsList=FERRAMENTAS
HealthToolsViewModel.RumenHealthBodyConditionTitle=Escore de Condição Corporal
HealthToolsViewModel.RumenHealthLocomotionTitle=Escore de Locomoção
HealthToolsViewModel.RumenHealthManureScreening=Peneira de escore de fezes
HealthToolsViewModel.RumenHealthManureTitle=Escore de fezes - Saúde ruminal
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Incidência Metabólica
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
HealthToolsViewModel.RumenHealthTMRTitle=Escore TMR Saúde de rúmen
HealthToolsViewModel.RumenHealthTitle=Saúde ruminal - mastigação
HealthToolsViewModel.RumenHealthUrinePHTitle=pH urinário
HealthToolsViewModel.Title=Ferramentas de Saúde
Heard_Island_and_McDonald_Islands=Heard Island e McDonald Islands
HeatstressCalculations=Calculos Estresse Térmico
HeatstressChart=Gráficos Estresse Térmico
HeatstressChartViewModel.DMIReduction=Redução de IMS
HeatstressChartViewModel.EnergyEquivMilkLoss=Perda de leite equivalente por energia
HeatstressChartViewModel.EstimateDryMatter=Ingestão de Matéria Seca estimada
HeatstressChartViewModel.HeatstressEvalLabel=Temperatura corrigida para média de temperatura e umidade relativa (sem sol)
HeatstressChartViewModel.IntakeAdjustment=Ingestão ajustada
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=Perda de energia consumida
HeatstressChartViewModel.Mcal=Mcal
HeatstressChartViewModel.MilkValueLossPerDay=Perda de leite / dia
HeatstressChartViewModel.MilkValueLossPerMonth=Perda de leite / mês
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.ReductionDMI=Redução de IMS
HeatstressChartViewModel.TempHumidIndex=Índice de temperatura e umidade 
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Anotações da visita
HeatstressData=Dados de estresse térmico
HeatstressDataEntryViewModel.AnimalInputs=Inputs animais
HeatstressDataEntryViewModel.CurrentMilkPrice=Preço do leite atual ({0}/{1})
HeatstressDataEntryViewModel.DMI=IMS ({0})
HeatstressDataEntryViewModel.Exposure=Exposição
HeatstressDataEntryViewModel.HoursExposed=Horas de sol
HeatstressDataEntryViewModel.Humidity=Umidade (%)
HeatstressDataEntryViewModel.LactatingAnimals=Animais em Lactação
HeatstressDataEntryViewModel.Milk=Leite ({0})
HeatstressDataEntryViewModel.MilkFat=Gordura do leite (%)
HeatstressDataEntryViewModel.MilkProtein=Proteína do leite (%)
HeatstressDataEntryViewModel.NEL=ELL (Mcal/{0})
HeatstressDataEntryViewModel.Temperature=Temperatura ({0})
HeatstressDataEntryViewModel.VisitNotebook=Anotações da visita
HeatstressDataEntryViewModel.Weather=Clima
HeatstressGreen=Limite de estresse
HeatstressOrange=Estresse moderado-severo
HeatstressRed=Estresse severo
HeatstressTableViewModel.HeatstressChartTab=Gráficos
HeatstressTableViewModel.HeatstressDataTab=Inputs dos dados
HeatstressTableViewModel.Title=Avaliação de Estresse Térmico
HeatstressTableViewModel.VisitNotebook=Anotações da visita
HeatstressYellow=Estresse médio-moderado
Hebei=Hebei
Heifer=Novilha
Heilongjiang=Heilongjiang
Henan=Henan
HerdAnalysisGoalsViewModel.CloseUpDry=Pré parto 
HerdAnalysisGoalsViewModel.CudChewingGoals=Objetivos de mastigação
HerdAnalysisGoalsViewModel.CudChews=Mastigação
HerdAnalysisGoalsViewModel.DIM=DEL
HerdAnalysisGoalsViewModel.EarlyLactation=Início da Lactação
HerdAnalysisGoalsViewModel.FarOffDry=Vaca seca 
HerdAnalysisGoalsViewModel.Fresh=Pós parto
HerdAnalysisGoalsViewModel.LateLactation=Final de lactação
HerdAnalysisGoalsViewModel.MidLactation=Meio de lactação
HerdAnalysisGoalsViewModel.PeakMilk=Pico de lactação 
HerdAnalysisGoalsViewModel.PercentChewing=% ruminações
HerdAnalysisGoalsViewModel.Title=Análise do Rebanho
HerdAnalysisGoalsViewModel.To=a
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Saúde ruminal  - ruminações
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Análise do Rebanho
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Análise do Rebanho
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Objetivos
HerdAnalysisMasterViewModel.Title=Saúde ruminal - ruminações
HerdAnalysisTableTitle=Análise de pontuação de ruminação
HerdAnalysisViewModel.AverageChews=Média de mastigação por bolo alimentar
HerdAnalysisViewModel.DaysInMilk=Dias em lactação
HerdAnalysisViewModel.Edit=Editar
HerdAnalysisViewModel.EditLabel=Editar
HerdAnalysisViewModel.HerdAnalysisTableTitle=Análise de pontuação de ruminação
HerdAnalysisViewModel.HerdCudChewing=% ruminação do rebanho
HerdAnalysisViewModel.NoOfCows=Complete a análise do lote para todos os lotes que você tiver iniciado
HerdAnalysisViewModel.NumberofChewsPerCud=Número de mastigações por bolo alimentar
HerdAnalysisViewModel.PenNameLabel=Nome do Lote
HerdAnalysisViewModel.PercentChewing=Porcentagem de Ruminação
HerdAnalysisViewModel.TableTitle=Análise de pontuação de ruminação
HerdAverage=Média do rebanho (%)
HerdGoal=Objetivo do rebanho (%)
HerdInformation=Informações do rebanho
HerdReporting=Relatório do rebanho\: ruminação
Hidalgo=Hidalgo
Himachal_Pradesh=Himachal Pradesh
Hokkaido=Hokkaido
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=Santa See (Estado da cidade do Vaticano)
HomeViewModel.AutoSync=Auto Sincronização
HomeViewModel.ConsumersTab=Clientes 
HomeViewModel.CustomersTab=Clientes
HomeViewModel.DashboardTab=Painel de controle
HomeViewModel.Eula=Contrato de licença para usuário final
HomeViewModel.Logout=Sair
HomeViewModel.PrivacyStatement=Declaração de privacidade
HomeViewModel.ProspectsTab=Prospectos
HomeViewModel.Settings=Configurações 
HomeViewModel.SyncWithDash=Sincronizar
HomeViewModel.SyncWithDate=Última data de sincronização\: {0\:dd/MM/aa}
HomeViewModel.SyncWithTime=Sincronização\: {0\:hh\:mm\:tt}
HomeViewModel.Title=Ferramenta de vendas no campo
Honduras=Honduras
Hong_Kong=Hong Kong
HowtoGetBetterKPResults=Como obter melhores resultados de KP
Hubei=Hubei
Hunan=Auto
Hungary=Hungria
IDR=Indonésia (Rp IDR)
INR=Índia (INR INR)
Iceland=Islândia
Idaho=Idaho
Illinois=Illinois
Imperia=Imperia
Imperial=Imperial
Improvements=Melhorias 
India=Índia
Indiana=Indiana
Indonesia=Indonésia
InoculantFQAs=Inóculos\: perguntas e respostas
Iowa=Iowa
Iran,_Islamic_Republic_of=Irã (Republic Islâmica do Irã
Iraq=Iraque
Ireland=Irlanda
Isernia=ISERNIA
Isle_of_Man=Ilha de Homem
Israel=Israel
Italy=Itália
JOD=JOD
JPY=JPY
Jalisco=Jalisco
Jamaica=Jamaica
Jammu_and_Kashmir=Jammu e Caxemira
Japan=Japão
Jersey=Jersey
Jharkhand=Jharkhand
Jiangsu=Jiangsu
Jiangxi=Jiangxi
Jilin=Jilin
Jordan=Jordânia
KRW=Coréia do Sul (₩ KRW)
Kansas=Kansas
Karnataka=Karnataka
Kazakhstan=Cazaquistão
Kentucky=Kentucky
Kenya=Quênia
Kerala=Kerala
Kerry=Kerry
Ketosis=Cetose
KeyBenchmarks=Pontos chave
KeyBenchmarks_AgeInMonthAtFirstCalving=Idade em meses ao primeiro parto
KeyBenchmarks_CalvingAndHeiferReocrd=Dados sobre bezerras e novilhas são registrados
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=15% do peso de maturação corpórea aos 90 dias
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=55% do peso de maturação corpórea na gestação
KeyBenchmarks_HeiferPeakProduce=Pico de lactação de novilha como % da média do rebanho
KeyBenchmarks_NintyDaysMorbidityf=Morbidade aos 90 dias
KeyBenchmarks_NintyDaysMortality=Mortalidade aos 90 dias
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=94% do peso maduro corpóreo antes do parto
KeyBenchmarks_PercentOfHeifersPregnant=Porentagem de novilhas prenhes aos 15 meses
KeyBenchmarks_SerumlgG=IgG sérico (g/L) nas 48h
Kildare=Kildare
Kilkenny=Kilkenny
Kiribati=Kiribati
Korea=Coréia
Korea,_Democratic_People's_Republic_of=Coréia, República Popular Democrática da
Korea,_Republic_of=Republica da Coréia
Kuwait=Kuwait
Kyrgyzstan=Quirguistão
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=especiaria
Lactating=Lactação
Lactation=LACTAÇÃO
Lakshadweep=Lakshadweep
Lao_People's_Democratic_Republic=República Democrática Popular do Laos
Laois=Laois
Last_Synced=Sincronização 
LateLactation=Final de lactação
Latina=Latina
Latvia=Letônia
Lebanon=Líbano
Lecce=Lecce
Lecco=LECCO
Leitrim=Leitrim
Lely=Lely
Length-exceed-allowed-limit=Comprimento excedeu o limite
LengthPerDayImperial=In. Por dia
LengthPerDayMetric=Cm. Por dia
Lesotho=Lesoto
LessThan4Days=Mais do que 4 dias
LessThanFifteen=&lt;15 (kernal hardness)
LessThanFiveWholeKernals=Menos de 5 grãos inteiros
LessThanOneHour=Menos que 1 hora
LessThanSixInches=Menos que 15 cm
LessThanSixLayers=Menos que 6 camadas
LessThanTwentFourInchesPerDay=Menos que 60 cm por dia
Liaoning=Liaoning
Liberia=Libéria
Libyan_Arab_Jamahiriya=Jamahiriya árabe da Líbia
Liechtenstein=Liechtenstein
Lift-Sync-Fail=Falha de sincronização com LIFT
Limerick=Limerick
LinkToPens=Link para lotes (opcional)
Lithuania=Lituânia
Livorno=Livorno
LocoCategory1=Cat. 1.0
LocoCategory2=Cat. 2.0
LocoCategory3=Cat. 3.0
LocoCategory4=Cat. 4.0
LocoCategory5=Cat 5.0
LocomotionEditTableViewModel.Category1=Categoria de Escore de Locomoção 1.0
LocomotionEditTableViewModel.Category2=Categoria de Escore de Locomoção 2.0
LocomotionEditTableViewModel.Category3=Categoria de Escore de Locomoção 3.0
LocomotionEditTableViewModel.Category4=Categoria de Escore de Locomoção 4.0
LocomotionEditTableViewModel.Category5=Categoria de Escore de Locomoção 5.0
LocomotionEditTableViewModel.EnterNumberOfCows=Contar o número de vacas
LocomotionEditTableViewModel.Title=Número de vacas
LocomotionHerdEditGoalViewModel.Category1=Categoria 1.0
LocomotionHerdEditGoalViewModel.Category2=Categoria 2.0
LocomotionHerdEditGoalViewModel.Category3=Categoria 3.0
LocomotionHerdEditGoalViewModel.Category4=Categoria 4.0
LocomotionHerdEditGoalViewModel.Category5=Categoria 5.0
LocomotionHerdEditGoalViewModel.HerdGoal=Objetivo do rebanho 
LocomotionHerdEditGoalViewModel.Title=Alterar Objetivos
LocomotionHerdInputsViewModel.Herd=REBANHO
LocomotionHerdMasterViewModel.Inputs=Inputs
LocomotionHerdMasterViewModel.Results=Resultados
LocomotionHerdMasterViewModel.Revenue=Receita
LocomotionHerdMasterViewModel.SubHeading=Análise do Rebanho
LocomotionHerdMasterViewModel.Title=Locomoção
LocomotionHerdResultsViewModel.Category1=Categoria 1.0
LocomotionHerdResultsViewModel.Category2=Categoria 2.0
LocomotionHerdResultsViewModel.Category3=Categoria 3.0
LocomotionHerdResultsViewModel.Category4=Categoria 4.0
LocomotionHerdResultsViewModel.Category5=Categoria 5.0
LocomotionHerdResultsViewModel.HerdAverage=Média do Rebanho
LocomotionHerdResultsViewModel.HerdGoal=Objetivos
LocomotionHerdResultsViewModel.Title=Análise do escore de locomoção
LocomotionHerdRevenueViewModel.Revenue=Receita
LocomotionHerdRevenueViewModel.Title=Receita do rebanho - Locomoção
LocomotionNumberinHerd=Locomoção (número no rebanho)
LocomotionNumberinPen=Locomoção (número no lote)
LocomotionPenInputsViewModel.FromPenSetup=Da configurações do lote
LocomotionPenInputsViewModel.Milk=LEITE 
LocomotionPenMasterViewModel.Inputs=Inputs
LocomotionPenMasterViewModel.Results=Resultados
LocomotionPenMasterViewModel.Title=Locomoção 
LocomotionPercentofHerd=Locomoção (% do rebanho)
LocomotionPercentofPen=Locomoção (% do lote)
LocomotionPreviousVisitsViewModel.AverageScore=Média do escore
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Média do escore
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Data
LocomotionPreviousVisitsViewModel.PercentPen=% do lote
LocomotionPreviousVisitsViewModel.SelectedDates=Selecionar datas
LocomotionPreviousVisitsViewModel.Title=Resultados do escore de locomoção
LocomotionScore=Escore de Locomoção
LocomotionScoreAverage=Média do escore de locomoção
LocomotionScoreHerd=Análise do escore de locomoção
LocomotionScoreReference=Referências para escore de locomoção
LocomotionSelectPenViewModel.MissingDiet=Inserir uma dieta válida para esse lote
LocomotionSelectPenViewModel.PenSelectionList=LOTES
LocomotionSelectPenViewModel.Title=Locomoção 
Lodi=Contrário
LoginViewModel.Copyright=© {0} Cargill, Incorporated. Todos os direitos reservados.
LoginViewModel.EmailLabel=Email
LoginViewModel.ErrorDescription=Usuário e senha não podem ficar em branco
LoginViewModel.ErrorTitle=Erro de acesso
LoginViewModel.InvalidMessage=O usuário ou a senha fornecidos não são válidos para acesso 
LoginViewModel.InvalidMessageTitle=Acesso Inválido
LoginViewModel.LoginPrompt=Acesso
LoginViewModel.LoginPromptConsumer=Other login
LoginViewModel.NetworkErrorMessage=Sem acesso a internet disponível
LoginViewModel.NetworkErrorMessageTitle=Erro de rede
LoginViewModel.PasswordLabel=Senha
LoginViewModel.Title=Acesso
Longford=Longford
Louisiana=Louisiana
Louth=Louth
LowForage=Pouca forragem
Lucca=Lucca
Luxembourg=Luxemburgo
MEQ100G=mEq/100g
MKD=MKD
MUN=NUL (mg/dL)
MXN=México (PESO MXN)
MYR=Malásia (MYR MYR)
Macao=Macau
Macedonia,_the_former_Yugoslav_Republic_of=Macedônia, a antiga República Iugoslava de
Macerata=Macerar
Madagascar=Madagáscar
Madhya_Pradesh=Madhya Pradesh
Maharashtra=Maharashtra
MainViewModel.EmailLabel=Email
Maine=Maine
MaintainingForageQuality=Manutenção da qualidade da forragem
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Bônus\: Utiliza inibidor de bolores na TMR em clima quente e úmido?
MaintainingForageQuality_TMRMixHasPleasantAroma=TMR possui cheiro agradável?
MaintainingForageQuality_TMRMixIsCoolToTouch=TMR está fresco ao toque?
Making_Feed_InventoryFOF=Criando um inventário de alimentos
Malawi=Chama
Malaysia=Malásia 
Maldives=Maldivas
Male=Machos
Mali=Tive
Malta=Malta
ManagingForageinSiloBags=Gerenciamento de forragem em sacos de silo
ManagingForageinTowerSilos=Gerenciamento de forragem em torres de silo
Manipur=Manipur
Manitoba=Manitoba
Mantua=Mantua
ManureEditScores=Alterar escore - Escore de fezes
ManureScoreHerdAnalysisEditInputsViewModel.Close=Fechar
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Dias em Lactação (DEL)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Alterar quantidade de DEL 
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Escore de Fezes 
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Análise de escore de fezes
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Dias em Lactação (DEL)
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Editar
ManureScoreHerdAnalysisMasterViewModel.Goals=Objetivos
ManureScoreHerdAnalysisMasterViewModel.Inputs=Inputs
ManureScoreHerdAnalysisMasterViewModel.Results=Resultados
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Análise do Rebanho
ManureScoreHerdAnalysisMasterViewModel.Title=Avaliação de fezes - Saúde ruminal
ManureScoreHerdAnalysisMasterViewModel.VisitNotebook=Notebook
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Análise de escore de fezes
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Escore de Fezes
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Média
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Escore máx
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Escore mín
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Pré parto (-20 a -1)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Início de lactação (16 a 60)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Fechar
ManureScoreHerdEditGoalsViewModel.FarOffDry=Vaca seca (menos de 21d)
ManureScoreHerdEditGoalsViewModel.Fresh=Pós Parto (0 a 15)
ManureScoreHerdEditGoalsViewModel.LateLactation=Final de lactação (mais que 201)
ManureScoreHerdEditGoalsViewModel.MaxGoal=Avaliação das Fezes - Máx
ManureScoreHerdEditGoalsViewModel.MidLactation=Meio da Lactação (121 a 200)
ManureScoreHerdEditGoalsViewModel.MinGoal=Avaliação das Fezes - Míns
ManureScoreHerdEditGoalsViewModel.PeakMilk=Pico da lactação (61 a 120)
ManureScoreHerdEditGoalsViewModel.Title=Editar objetivos
ManureScoreHerdGoalsViewModel.CloseUpDry=Pré-Parto (D -20 a -1)
ManureScoreHerdGoalsViewModel.EarlyLactation=Início da lactação (16 a 60)
ManureScoreHerdGoalsViewModel.Edit=Editar
ManureScoreHerdGoalsViewModel.FarOffDry=Vacas Secas (Menos de -21)
ManureScoreHerdGoalsViewModel.Fresh=Pós Parto (0 a 15)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Fezes - Objetivo máximo
ManureScoreHerdGoalsViewModel.GoalMinTitle=Fezes - Objetivo mínimo
ManureScoreHerdGoalsViewModel.LateLactation=Final de lactação (mais que 201)
ManureScoreHerdGoalsViewModel.MidLactation=Meio de lactação (121 a 200)
ManureScoreHerdGoalsViewModel.PeakMilk=Pico de lactação (61 a 120)
ManureScoreHerdGoalsViewModel.TableTitle=Escore por estágio da lactação
ManureScorePenSelectionViewModel.ManureScoreTitle=Saúde ruminal - Escore de fezes
ManureScorePenSelectionViewModel.PenSelectionList=LOTES
ManureScorePercentOfPen=Escore de fezes (% lote)
ManureScoresChart=Gráficos - escore de fezes
ManureScoresResult=Resultados - Escore de Fezes
Maranhão=Maranhão
Martinique=Martinica
Maryland=Maryland
Massa_and_Carrara=Massa e Carrara
Massachusetts=Massachusetts
Matera=Matera
Mato_Grosso=Mato Grosso
Mato_Grosso_do_Sul=Mato Grosso do Sul
Mauritania=Mauritânia
Mauritius=Maurício
Max=Máx
Mayo=maionese
Mayotte=Mayotte
Mcal=Mcal
Meath=Meath
Medio_Campidano=Medio Campidano
Medium=Média
Meghalaya=Meghalaya
MenuViewModel.Close=Fechar
MenuViewModel.LogoutPrompt=Sair
MenuViewModel.Menu=Menu
MenuViewModel.ResetDatabaseCancel=Cancelar
MenuViewModel.ResetDatabasePrompt=Isso substituirá todos os dados existentes, incluindo as preferências do usuário, por um conjunto inicial de dados de teste. Ferramentas, visitas criadas e processadores de leite serão excluídos. Você também será desconectado do aplicativo. Continuar?"
MenuViewModel.ResetDatabaseReset=Reiniciar
MenuViewModel.ResetDatabaseTitle=Reiniciar testes
MenuViewModel.Sync_PopUp=Os dados estão sincronizando ...
Messina=Messina
MetabolicIncidenceChartsViewModel.Current=Atual
MetabolicIncidenceChartsViewModel.DeathLoss=Morte
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Custo anual dos distúrbios metabólicos/vaca
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Distorção de abomaso
MetabolicIncidenceChartsViewModel.Dystocia=Distocia 
MetabolicIncidenceChartsViewModel.GoalPercent=Objetivo (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Incidência Metabólica (%)
MetabolicIncidenceChartsViewModel.IncidencePercent=Incidência (%)
MetabolicIncidenceChartsViewModel.Ketosis=Cetose
MetabolicIncidenceChartsViewModel.Metritis=Metrite
MetabolicIncidenceChartsViewModel.MilkFever=Febre do leite
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Retenção de placenta
MetabolicIncidenceChartsViewModel.Title=Gráficos de Incidência metabólica
MetabolicIncidenceEditOutputsViewModel.Close=Fechar
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Morte
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Deslocamento de abomaso
MetabolicIncidenceEditOutputsViewModel.Dystocia=Distocia 
MetabolicIncidenceEditOutputsViewModel.Ketosis=Cetose
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Objetivo (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Metrite
MetabolicIncidenceEditOutputsViewModel.MilkFever=Febre do leite
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Retenção de placenta
MetabolicIncidenceEditOutputsViewModel.Title=Editar objetivos
MetabolicIncidenceInputsEditViewModel.Close=Fechar
MetabolicIncidenceInputsEditViewModel.DeathLoss=Mastite
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Deslocamento de abomaso
MetabolicIncidenceInputsEditViewModel.Dystocia=Distocia 
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Dias em aberto
MetabolicIncidenceInputsEditViewModel.Ketosis=Cetose
MetabolicIncidenceInputsEditViewModel.Metritis=Metrite
MetabolicIncidenceInputsEditViewModel.MilkCow=Leite / vaca ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Febre do leite
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Retenção de placenta
MetabolicIncidenceInputsEditViewModel.Title=Editar atributos de custo
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Tratamento e outros custos (abate, morte) - USD
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Custo extra de dias em aberto
MetabolicIncidenceInputsViewModel.Costs=Custos
MetabolicIncidenceInputsViewModel.DeathLoss=Morte
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Deslocamento de abomaso
MetabolicIncidenceInputsViewModel.Dystocia=Distocia
MetabolicIncidenceInputsViewModel.Herd=INFORMAÇÕES A NÍVEL DE REBANHO
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Digite o número de vacas recém partidas e o histórico de doenças metabólicas durante o período de avaliação. Os valores serão convertidos em custos anuais pela incidência de doenças metabólicas na tabela de resultados.
MetabolicIncidenceInputsViewModel.IncidenceCases=Casos de incidências metabólicas
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Aumento dias em aberto
MetabolicIncidenceInputsViewModel.Ketosis=Cetose
MetabolicIncidenceInputsViewModel.Mastitis=Mastite
MetabolicIncidenceInputsViewModel.Metritis=Metrite
MetabolicIncidenceInputsViewModel.MilkFever=Febre do leite
MetabolicIncidenceInputsViewModel.MilkLossKg=Perda de leite por lactação ({0})
MetabolicIncidenceInputsViewModel.MilkPrice=Preço do leite 
MetabolicIncidenceInputsViewModel.PerformanceMessage=Informações de referência usadas para calcular o impacto econômico de cada doença metabólica.
MetabolicIncidenceInputsViewModel.PerformanceTreatment=CUSTOS DE TRATAMENTO E DESEMPENHO
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Custo de reposição das vacas
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Retenção de placenta
MetabolicIncidenceInputsViewModel.Title=Inputs Incidência metabólica
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Total de vacas no pós parto para avaliação
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Total de vacas em pós parto/ano
MetabolicIncidenceInputsViewModel.TreatmentCost=Tratamento e outros custos (abate, morte) - USD
MetabolicIncidenceMasterViewModel.Charts=Gráficos
MetabolicIncidenceMasterViewModel.Inputs=Inputs
MetabolicIncidenceMasterViewModel.Outputs=Resultados
MetabolicIncidenceMasterViewModel.Title=Incidência Metabólica
MetabolicIncidenceOutputsViewModel.DeathLoss=Morte
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Deslocamento de abomaso
MetabolicIncidenceOutputsViewModel.Dystocia=Distocia 
MetabolicIncidenceOutputsViewModel.Ketosis=Cetose
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Incidência (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Custo / vaca
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Aumento dias em aberto
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Diferença (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Editar
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Objetivo (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Impacto econômico das doenças metabólicas acima das metas estabelecidas para o rebanho
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Impacto econômico anual
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Impacto econômico anual - total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Perda de leite 
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Percentual de incidência metabólica
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Custo total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Tratamento e outros custos (abate, morte) - USD
MetabolicIncidenceOutputsViewModel.Metritis=Metrite
MetabolicIncidenceOutputsViewModel.MilkFever=Febre do leite
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Retenção de placenta
MetabolicIncidenceOutputsViewModel.Title=Resultados - Incidência metabólica
MetabolicIncidenceOutputsViewModel.TotalLosses=Total de perdas anuais
Metric=Métrico
MetricTonsAF=Ton MN
MetricTonsDM=Ton MS
Metritis=Metrite
Mexico=México
Mexico_State=Estado do México
Michigan=MICHIGAN
Michoacán=Michoacãn
MidLactation=Meio da lactação
MidOne=Meio 1
MidOneValue=(8mm)
MidTwo=Meio 2
Milan=Milão
Milk=Leite ({0})
MilkChange=Mudança na produção de leite ({0})
MilkFever=Febre do leite
MilkLetDownResponse=Resposta de perda de leite
MilkLossDay=Perda de leite ({0}/dia)
MilkLossGain=Potencial perda/ganho de leite
MilkLossKg=Perda de leite (kgs)
MilkLossPounds=Perda de leite (lbs)
MilkLossYear=Perda de leite ({0}/ano)
MilkPrice=Preço do leite ({0}/{1})
MilkPricePremiums=Prêmio no preço do leite
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=Gráfico de referência
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Inputs
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Recursos 
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Resultados 
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Receitas no leite
MilkProcessRevenueCalculatorMasterViewModel.Title=Receitas no leite
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Anotações da visita
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Estimulação inadequada
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Preço do leite ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=Sem estimulação
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Estimulação ótima 
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Cenário 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Cenário 2
MilkProcessorEditComparisonValuesViewModel.Title=Editar comparação de valores
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT (45 kg)
MilkProcessorEditComparisonValuesViewModel.WeightMetric=Kg
MilkProcessorInputViewModel.ComparisonValues=Comparação de valores
MilkProcessorInputViewModel.Edit=Editar
MilkProcessorInputViewModel.MilkPrice=Preço do leite ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=O processador anteriormente escolhido foi excluído. Selecione outro processador para continuar.
MilkProcessorInputViewModel.ScenarioOne=Cenário 1
MilkProcessorInputViewModel.ScenarioTwo=Cenário 2
MilkProcessorInputViewModel.SelectProcessor=Selecionar processador
MilkProcessorInputViewModel.Title=Calculadora de entrada de receita - Processador de leite
MilkProcessorInputViewModel.Title1=Inputs - Comparações de procedimento de ordenha
MilkProcessorInputViewModel.WeightImperialCWT=CWT (45 kg)
MilkProcessorInputViewModel.WeightMetric=Kg
MilkProcessorResourcesViewModel.ApproxSCC=CCS aproximado (cels/ml)
MilkProcessorResourcesViewModel.LinearScore=Pontuação linear
MilkProcessorResourcesViewModel.Mastitis=Perda de leite por mastite ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Gráfico de referência
MilkProcessorResourcesViewModel.Title=Recursos - Receita de ordenha
MilkProcessorResultsViewModel.Change=Mudança 
MilkProcessorResultsViewModel.HundredWeight=CWT (45 kg)
MilkProcessorResultsViewModel.MilkPrice=Preço do leite ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Mudança nos valores anuais
MilkProcessorResultsViewModel.ScenarioOne=Cenário 1
MilkProcessorResultsViewModel.ScenarioTwo=Cenário 2
MilkProcessorResultsViewModel.Title=Resultados - Receita de ordenha
MilkProcessorResultsViewModel.WeightImperialCWT=CWT (45 kg)
MilkProcessorResultsViewModel.WeightMetric=Kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Componente
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Concentração 
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Componente
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Concentração 
MilkProcessorSettingsMasterViewModel.MilkProcNew=Novo
MilkProcessorSettingsMasterViewModel.Title=Configuração do processador do leite
MilkProcessorViewModel.Amount=Quantidade (1000 cels/ml)
MilkProcessorViewModel.AmountCFU=Quantidade (1000 cfu/ml)
MilkProcessorViewModel.BasePriceMilkFat=Gordura do leite ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Leite ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Proteína do leite ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Outros sólidos ({0}/{1})
MilkProcessorViewModel.BasePrices=Preço base
MilkProcessorViewModel.ComponentProcessor=Processador de componentes
MilkProcessorViewModel.ConcentrationProcessor=Processador de concentração
MilkProcessorViewModel.Delete=Deletar
MilkProcessorViewModel.DeletePrompt=A exclusão desse processador invalidará os resultados na ferramenta Calculadora de Receita do procedimento de ordenha. Você quer apagar este processador?
MilkProcessorViewModel.HundredWeight=CWT (45 kg)
MilkProcessorViewModel.Name=Nome
MilkProcessorViewModel.NameNotUnique=Este nome já existe. Escolha outro nome "{0}".
MilkProcessorViewModel.NewComponentProcessorName=Processador de componentes \#{0}
MilkProcessorViewModel.NewConcentrationProcessorName=Processador de concentração \#{0}
MilkProcessorViewModel.PricingMatrices=Matrizes de preço
MilkProcessorViewModel.SelectCurrency=Selecione uma moeda
MilkProcessorViewModel.WeightImperial=Lbs
MilkProcessorViewModel.WeightImperialCWT=CWT (45 kg)
MilkProcessorViewModel.WeightMetric=Kg
MilkProduction=Produção de leite ({0})
MilkProductionKg=Produção de leite (kgs)
MilkProductionPounds=Produção de leite (lbs)
MilkProductionRevenue=Receita no leite 
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Rendimento e eficiência dos componentes
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=Ingestão de matéria seca e eficiência alimentar
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=Gordura do leite % e Proteína do leite %
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Produção de leite e dias em lactação
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Contagem de célula somática e uréia no leite
MilkSoldEvaluationChartsListViewModel.VisitComparison=Selecione visitas para comparação
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Eficiência dos componentes
MilkSoldEvaluationChartsViewModel.ComponentYield=Rendimento dos componentes
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Rendimento dos componentes e eficiência
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=Ingestão de matéria seca e eficiência alimentar
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Dias em lactação 
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Ingestão de matéria seca
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Eficiência alimentar
MilkSoldEvaluationChartsViewModel.MilkFat=Gordura do leite (%)
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=Gordura do leite % e Proteína do leite %
MilkSoldEvaluationChartsViewModel.MilkProduction=Produção de leite
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Produção de leite e dias em lactação 
MilkSoldEvaluationChartsViewModel.MilkProtein=Proteína do leite %
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Uréia no leite
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Contagem de célula somática
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Contagem de célula somática e uréia no leite
MilkSoldEvaluationChartsViewModel.Title=Avaliação do leite vendido
MilkSoldEvaluationInputsViewModel.AddPickup=Adicionar Retirada
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Animais em Tanque ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Dias em Lactação (DEL) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Ingestão de matéria seca ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=INFORMAÇÕES A NÍVEL DE REBANHO
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Animais em Lactação ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Coleta de leite  ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Informação do processador de leite
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Nível de Uréia no leite  ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Adicionar Novo
MilkSoldEvaluationMasterViewModel.Charts=Gráficos 
MilkSoldEvaluationMasterViewModel.Inputs=Inputs
MilkSoldEvaluationMasterViewModel.Outputs=Resultados
MilkSoldEvaluationMasterViewModel.Title=Avaliação do leite vendido
MilkSoldEvaluationOutputsViewModel.AvgBCC=Carga bacteriana média (1.000 cfu / mL)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Média de gordura no leite %
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Média de produção de leite, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Média de produção de leite, {0} (Animais em tanque)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Média de proteína no leite %
MilkSoldEvaluationOutputsViewModel.AvgSCC=Média CCS (1,000 cels/mL)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Eficiência dos componentes (% de IMS)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Dias de avaliação
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Eficiência alimentar (relação)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Rendimento Gordura + Proteína do leite ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Rendimento da gordura do leite ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Rendimento da proteína do leite ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Atualizar configurações do local
MilkSoldPickupViewModel.AnimalsInTank=Animais em Tanque ⃰
MilkSoldPickupViewModel.BCC=Carga bacteriana (1.000 cfu/mL)
MilkSoldPickupViewModel.DaysInTank=Dias em tanque ⃰
MilkSoldPickupViewModel.MilkFat=Gordura do leite %
MilkSoldPickupViewModel.MilkProtein=Proteína do leite %
MilkSoldPickupViewModel.MilkSold=Leite vendido, {0} ⃰
MilkSoldPickupViewModel.SCC=Contagem de célula somáticas (1,000 cels/mL)
MilkSoldPickupViewModel.Title=Alterar retirada {0}
MilkSoldSpinnerViewModel.Title=Avaliação do leite vendido
MilkUrea=Uréia no leite (mg/dL)
Milking=Lactação
MilkingFailure=Falhas de ordenha
MilkingFirst=Primeira ordenha
MilkingProcessRevenueInputs=Inputs - Receita de ordenha
MilkingProcessRevenueResources=Recursos - Receita de ordenha
MilkingProcessRevenueResults=Resultados - Receita de ordenha
Minas_Gerais=Minas Gerais
Minnesota=Minnesota
Mississippi=Mississippi
Missouri=Missouri
Mizoram=Mizoram
Modena=Modena
Moderate=Moderado
ModeratelyClean=Moderadamente limpo
Moldova,_Republic_of=Moldávia, República de
Monaco=Mônaco
Monaghan=Monaghan
Mongolia=Mongólia
Montana=Montana
Montenegro=Montenegro
Monthly=Monthly
Montserrat=Montserrat
Monza_and_Brianza=Monza e Brianza
MoreThan8LayersOfPlastic=Mais de 8 camadas de plástico
Morelos=Morelos
Morocco=Marrocos
Mozambique=Moçambique
Myanmar=Mianmar
NGN=NGN
NIO=Nicarágua (NIO NIO)
NOK=NOK
Nagaland=Nagaland
Namibia=Namíbia
Naples=Nápoles
Nauru=Nauru
Nayarit=Nayarit
Nebraska=Nebraska
Nei_Mongol=Nei mongol
Nepal=Nepal
Netherlands=Holanda
Nevada=Nevada
NewBunker=Dar um nome para a nova trincheira
NewDietClassViewModel.Title=Classe Animal / Sub Classe
NewDietPensViewModel.AssociatePens=Essa dieta pode ser associada a múltiplos lotes
NewDietPensViewModel.Title=Link para lotes 
NewDietViewModel.Cancel=Cancelar 
NewDietViewModel.MainHeading=Nome da dieta  ⃰
NewDietViewModel.Save=Salvar
NewDietViewModel.Title=Nova dieta
NewPenDietViewModel.New=Novo
NewPenDietViewModel.Title=Dieta 
NewPenViewModel.Animals=Animais por Lote
NewPenViewModel.AnimalsInputsPen=Inputs animais, lote
NewPenViewModel.AsFedIntake=Ingestão em matéria natural ({0})
NewPenViewModel.Barn=Nome do Galpão
NewPenViewModel.Cancel=Cancelar 
NewPenViewModel.DaysInMilk=Dias em Lactação (DEL)
NewPenViewModel.Diet=Dieta
NewPenViewModel.DietInputsPen=Inputs dieta, lote
NewPenViewModel.DryMatterIntake=Ingestão de matéria seca ({0})
NewPenViewModel.FeedingSystem=Sistema de alimentação
NewPenViewModel.General=Geral 
NewPenViewModel.HousingSystem=Tipo de instalação 
NewPenViewModel.InfoNewPenDetails=Adicione ou atualize dados específicos do lote nesta página. Você também pode adicionar ou atualizar dados nas ferramentas à medida que você as utiliza. Ao criar um novo lote, os únicos itens necessários são o nome do lote, a dieta, o sistema de alojamento e o sistema de alimentação. Os dados das configurações do lote serão atualizados automaticamente para os locais com downloads de dados da fazenda no Dairy Enteligen.
NewPenViewModel.Milk=Produção de leite ({0})
NewPenViewModel.MilkingFrequency=Frequência de ordenha
NewPenViewModel.NumberOfStalls=Número de camas
NewPenViewModel.OnlyOnePen=Você tem somente um lote 
NewPenViewModel.PenDetail=Detalhes do lote
NewPenViewModel.PenMapping=Mapeamento do lote
NewPenViewModel.PenName=Nome do Lote
NewPenViewModel.PenSelection=Selecionar lote 
NewPenViewModel.PublishPenAlert=Por favor publique todas as visitas relacionadas com o lote que você deseja mesclar
NewPenViewModel.RationCostPerAnimal=Custo de ração por animal ({0})
NewPenViewModel.Save=Salvar
NewPenViewModel.Title=Novo Lote
NewPenViewModel.UserCreatedPen=Lote criado pelo usuário
NewPile=Favor inserir um nome para o novo silo
NewProspectViewModel.Address1=Endereço comercial 1
NewProspectViewModel.Address2=Endereço comercial 2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter 
NewProspectViewModel.BusinessName=Nome da empresa
NewProspectViewModel.City=Cidade
NewProspectViewModel.ConsumerDetails=Detalhes do cliente 
NewProspectViewModel.Country=País
NewProspectViewModel.Customer=Cliente
NewProspectViewModel.CustomerDetail=Detalhes do cliente
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=Email de contato
NewProspectViewModel.EndUser=Usuário final 
NewProspectViewModel.FarmProducer=Farm Producer
NewProspectViewModel.Image=Clique para editar a foto
NewProspectViewModel.InvalidEmail=Inserir um email válido
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=Já existe um prospecto com o nome "{0}". Os nomes devem ser exclusivos
NewProspectViewModel.NameNotUniqueForConsumer=O cliente nomeado "{0}" já existe. Os nomes precisam ser únicos
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=-
NewProspectViewModel.NullBusinessName=Nome comercial é obrigatório
NewProspectViewModel.NullFirstName=Primeiro nome é obrigatório
NewProspectViewModel.NullSecondName=Último nome é obrigatório
NewProspectViewModel.PostalCode=Código postal
NewProspectViewModel.PrimaryContactFirstName=Nome de contato
NewProspectViewModel.PrimaryContactLastName=Sobrenome de contato
NewProspectViewModel.PrimaryPhone=Número de telefone do contato
NewProspectViewModel.Prospect=Prospecto
NewProspectViewModel.ProspectDetail=Detalhes do prospecto
NewProspectViewModel.Segment=Segmentação
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Spence=Spence
NewProspectViewModel.State=ESTADO
NewProspectViewModel.Title=Detalhes
NewProspectViewModel.Type=Tipo
NewProspectViewModel.Walton=Walton
NewVisitViewModel.Title=Detalhes da visita
New_Brunswick=New Brunswick
New_Caledonia=Nova Caledônia
New_Hampshire=Nova Hampshire
New_Jersey=Nova Jersey
New_Mexico=Novo México
New_South_Wales=Nova Gales do Sul
New_York=Nova Iorque
New_Zealand=Nova Zelândia
Newfoundland_and_Labrador=Terra Nova e Labrador
Next=Próximo 
Nicaragua=Nicarágua
Niedersachsen=Baixa Saxônia
Niger=Níger
Nigeria=Nigéria
Ningxia=Ningxia
Niue=Niue
No=Não
No-User-Found=Usuário não foi encontrado no LIFT; contatar o adm
NoResourcesAvailable=Recurso não disponível
NoResults=Nenhum resultado encontrado
NoWholeKernals=Sem grãos inteiros
Noah=Noah
None=Nenhum
NoneSelected=Nenhum selecionado
Nordrhein=Reno do Norte
Norfolk_Island=Ilha de Norfolk
Normal=&lt;0.5 unid ECC
NorthAmerica=América do Norte
North_Carolina=Carolina do Norte
North_Dakota=Dakota do Norte
Northern_Territory=Território do Norte
Northwest_Territories=Territórios do Noroeste
Norway=Noruega
Not-matching-with-allowed-values=Valor não permitido
NotMeasured=Not measured
NotRemoved=Not removed
NotSet=-
NoteCamcorderNotImplemented=Recurso de câmera de vídeo ainda não implementado
NoteCategoryViewModel.FooterText=Apenas uma categoria pode ser selecionada por anotação
NoteCategoryViewModel.SelectCategory=Selecione uma categoria
NoteCategoryViewModel.Title=Anotação
NotebookBCSHerdAnalysisGoals=Objetivos - Análise de ECC do Rebanho
NotebookBCSHerdAnalysisInputs=Inputs - Análise de ECC do Rebanho
NotebookBCSHerdAnalysisResults=Resultados - Análise de ECC do Rebanho
NotebookBCSSelectPointScale=Condição Corporal - Selecionar Ponto de Escala
NotebookBodyConditionEdit=Condição Corporal - Editar tabela
NotebookCudCalculators=Saúde de rúmen - Calculadora de mastigação 
NotebookCudChewing=Ruminações - Escolha do lote - saúde de rúmen
NotebookCudChewingDataEntry=Ruminações - Inputs - saúde de rúmen
NotebookCudChewingResults=Ruminação  - Resultados - Saúde de rúmen 
NotebookLocomotionEditTable=Locomoção - Número de vacas
NotebookLocomotionHerdInputs=Locomoção - Análise do rebanho - Inputs
NotebookLocomotionHerdResults=Locomoção - Análise do rebanho - Resultados
NotebookLocomotionHerdRevenue=Locomoção - Análise do rebanho - Receita
NotebookLocomotionLanding=Locomoção - Geral
NotebookLocomotionPenInputs=Locomoção - Inputs do lote
NotebookLocomotionPenResults=Locomoção - Resultados do Lote
NotebookLocomotionPenSelection=Locomoção - Seleção do Lote
NotebookManurePenSelection=Escore de fezes - seleção do lote
NotebookManureScoreHerdAnalysisGoals=Escore de fezes - Objetivos do rebanho
NotebookManureScoreHerdAnalysisInputs=Escore de fezes - Inputs do rebanho
NotebookManureScoreHerdAnalysisResults=Escore de fezes - Resultados rebanho
NotebookManureScoreLanding=Escore de Fezes
NotebookMetabolicIncidenceCharts=Gráficos - Incidência metabólica
NotebookMetabolicIncidenceInputs=Inputs - Incidência metabólica
NotebookMetabolicIncidenceOutputs=Resultados - Incidência metabólica
NotebookParticleScoreHerdAnalysisEdit=Editar IMS - Análise do rebanho - Análise de TMR 
NotebookParticleScoreLanding=Avaliação de TMR
NotebookParticleScoreSelectPen=Selecionar lote - Avaliação TMR
NotebookParticleScoreSelectScorer=Selecionar peneira - Avaliação TMR
NotebookPenTimeComparison=Avaliação do Tempo de Descanso
NotebookPenTimeInputs=Inputs - Avaliação do Tempo de Descanso
NotebookPenTimePenSelection=Seleção de Lote - Avaliação do Tempo de Descanso
NotebookPenTimeResults=Resultados - Avaliação do Tempo de Descanso
NotebookReadyToMilkCharts=Gráficos - Ready to Milk
NotebookReadyToMilkInputs=Inputs - Ready to Milk
NotebookReadyToMilkOutputs=Resultados - Ready to Milk
NotebookRumenHealthNumberOfChewsInput=Inputs número de mastigações - Saúde ruminal
NotebookRumenHealthNumberOfChewsResults=Resultados número de mastigações - Saúde ruminal
NotebookRumenHealthTMRParticlePercent=Percentual TMR por peneira
NotebookRumenHealthTMRParticleScore=Avaliação TMR
NotebookSectionComfortTools=Ferramentas de Conforto
NotebookSectionForageAudit=Auditoria de Forragens - escore card
NotebookSectionForageAuditBaleage=Auditoria de Forragem - Silos Bolsa
NotebookSectionForageAuditBunkersPiles=Auditoria de Forragens - silo de superfície e de trincheira
NotebookSectionForageAuditHarvest=Auditoria de Forragens - colheita
NotebookSectionForageAuditLanding=Auditoria de Forragens 
NotebookSectionForageAuditMaintainingQuality=Auditoria de Forragens - Manutenção da  qualidade da forragem
NotebookSectionForageAuditSilageBags=Auditoria de Forragens - silo de bolsa
NotebookSectionForageAuditSurveyOfForages=Auditoria de Forragens - Pesquisa de forragens
NotebookSectionForageAuditTowerSilos=Auditoria de Forragem - Silos Torre
NotebookSectionHealthTools=Ferramentas de Saúde
NotebookSectionHeatstressCalculations=Estresse calórico - Cálculos
NotebookSectionHeatstressChart=Estresse calórico - Gráfico
NotebookSectionHeatstressData=Etresse calórico - Data
NotebookSectionHerdAnalysisGoals=Metas - Saúde Ruminal - Análise de Rebanho
NotebookSectionMilkSoldEvaluationCharts=Avaliação do leite vendido - Gráfico
NotebookSectionMilkSoldEvaluationEditPickup=Avaliação do leite vendido - Editar coleta
NotebookSectionMilkSoldEvaluationInputs=Avaliação do leite  vendido - Entradas
NotebookSectionMilkSoldEvaluationOutputs=Avaliação do leite vendido - Resultados
NotebookSectionNutritionTools=Ferramentas de Nutrição
NotebookSectionRumenHealthLanding=Saúde de rúmen 
NotebookSectionVisit=Visita
NotebookTMRParticleHerdAnalysisPenInputs=Análise de rebanho TMR - Entradas
NotebookTMRParticleHerdAnalysisPenResults=Análise do Rebanho TMR - Resultados
NotebookUrinePHEditGoals=Editar objetivos pH urinário
NotebookUrinePHInputs=Inputs pH urinário
NotebookUrinePHOutputs=Resultados pH urinário
NotebookVisitSummary=Resumo da visita
NotebookWalkthroughReport=Relatório da visita
NotebookWalkthroughReportLanding=Relatório da visita
NotebookWalkthroughReportPen=Relatório da visita - Análise do lote
Nova_Scotia=Nova Scotia
Novara=Novara
Nuevo_León=Novo Leí
Null-values-not-allowed=Precisa conter um valor
NumChewsGoal={0} Objetivo
NumOfCows=Número de vacas
NumberOfChewsReportsViewModel.Average=Média de \# mastigações
NumberOfChewsReportsViewModel.AverageChews=Média de ruminações
NumberOfChewsReportsViewModel.AverageNumberChews=Média de \# mastigações
NumberOfChewsReportsViewModel.DateDescription=Data
NumberOfChewsReportsViewModel.DatesComparison=Datas de comparações
NumberOfChewsReportsViewModel.EditVisits=Selecionar 
NumberOfChewsReportsViewModel.StdDevCalculated=Desvio padrão (calculado)
NumberOfChewsReportsViewModel.VisitDate=Data
NumberOfChewsViewModel.Count=Contagem 
NumberOfChewsViewModel.CountHeader=Conte o número de mastigações dessa vaca
NumberOfChewsViewModel.NextCow=Próxima vaca
NumberOfChewsViewModel.NumberOfChewsCow=Número de mastigações / vaca \#
NumberOfChewsViewModel.Title=Número de mastigações / vaca \# {0}
NumberOfChewsViewModel.ValidCudInput=Inserir um input válido
NumberOfCows=Número de vacas
Nunavut=Nunavut
Nuoro=Nuoro
NutritionViewModel.NutritionForage=Auditoria de Forragem
NutritionViewModel.NutritionLabel=Selecione uma ferramenta da lista abaixo para iniciar sua visita
NutritionViewModel.NutritionPile=Capacidade dos silos de superfície e trincheira
NutritionViewModel.NutritionTools=Ferramentas de Nutrição
NutritionViewModel.NutritionToolsCaption=Ferramentas 
NutritionViewModel.NutritionToolsInstructions=Selecione uma ferramenta da lista abaixo para iniciar sua visita
NutritionViewModel.NutritionToolsList=Ferramentas
NutritionViewModel.Title=Ferramentas de Nutrição
NutritionViewModel.VisitNotebook=Anotações da visita
OKLabel=OK
Oaxaca=Oaxaca
Observation=Observação
Odisha=Odisha
Offaly=Offaly
Ogliastra=Ogliastra
Ohio=Ohio
Oklahoma=Oklahola
Olbia-Tempio=Olbia-Tempio
Oman=Seu próprio
OncePerWeek=Uma vez por semana
One=Um
OneHourBeforeActionIsDue=Uma hora antes da ação ser vencida
OneToSixHours=1 a 6 horas
Ontario=Ontário
Opportunities=Oportunidades 
Optimal=Resposta ótima
Oregon=Oregon
Oristano=Oristano
Other=Outros
OtherSilage=Other Silage
Overall=Geral 
OverallCalfHeiferDetails=Para ver a pontuação geral de Bezerras e Novilhas, complete pelo menos uma das pesquisa da lista abaixo
OverallForageScoreDetails=Para ver uma pontuação geral de forragem, primeiro você deve completar pelo menos uma das seguintes enquetes da lista.
PDFDisclaimer=A Cargill Incorporated, seus parceiros e afiliadas não garantem a precisão dessas estimativas devido a muitos fatores. Não há garantia de produção ou resultados financeiros. © {0} Cargill, Incorporated. Todos os direitos reservados
PDFDisclaimer_ProvimiUS=A Provimi North America, seus parceiros e afiliadas não garantem a precisão dessas estimativas devido a muitos fatores. Não há garantia de produção ou resultados financeiros. © {0} Provimi North America. Todos os direitos reservados
PDFPageNumber=Página {0} de {1}
PEN=Peru (S/. PEN)
PHP=Filipinas ($ PHP)
PLN=Polônia (zł PLN)
PMRConcentrate=PMR + concentrado
PON=România (lei PON)
Padua=Pádua
Pakistan=Paquistão
Palermo=Palermo
Palestinian_Territory,_Occupied=Território Palestino, Ocupado
Panama=Panamá
Papua_New_Guinea=Papua Nova Guiné
Paraguay=Paraguai
Paraná=Paraná
Paraíba=Paraãba
Parlor=Sala de ordenha
Parma=Parma
ParticleScorePreviousVisitsViewModel.MidOne=Meio 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8 mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Meio 2
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Porcentagem da peneira (%)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Meio 1 (8 mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Meio 2 (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Peneira superior (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Peneira inferior 
ParticleScorePreviousVisitsViewModel.SelectDates=Selecionar datas
ParticleScorePreviousVisitsViewModel.Top=Peneira superior
ParticleScorePreviousVisitsViewModel.TopValue=(19 mm)
ParticleScorePreviousVisitsViewModel.Tray=Peneira inferior
Pará=Paraiatório
PasteurizedWholeMilk=Leite integral pasteurizado 
Pasto=Pasto
Pasture=Pasto
PastureOther=Pasto + outro
Pavia=Pavia
Pays=Paga
PeakMilk=Pico de produção de leite
PenDetailViewModel.Title=Detalhe do lote
PenListViewModel.DietSetup=Ajuste da dieta
PenListViewModel.InfoPenList=É necessário criar Lotes para usar as ferramentas para Lotes e o Relatório durante uma visita. Os nomes e dados dos lotes serão atualizados automaticamente para localidades cujos dados sejam inseridos no Dairy Enteligen. Adicione lotes manualmente se os dados da fazenda não estiverem configurados.                                                                                             Os nomes das dietas serão atualizados automaticamente se o MAX estiver conectado à fazenda no Dairy Enteligen. Atualize a lista de dietas regularmente clicando no nome da dieta para garantir que as informações corretas estejam sendo inseridas no relatório. Selecione manualmente as dietas ou  Classe/Sub Classe Animais se as conexões de dados do MAX ainda não estiverem configuradas.
PenListViewModel.MainHeading=Lotes
PenListViewModel.NewPen=Adicionar Novo Lote
PenListViewModel.Title=Lotes
PenName=Nome do Lote
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Mudança do escore de condição corporal (por 100 dias)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Mudança de escore de condição corporal ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Vacas no lote
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Animais Ordenhados por Hora
PenTimeBudgetComparisonViewModel.Current=Atual 
PenTimeBudgetComparisonViewModel.EnergyChange=Mudança de energia (Mcals)
PenTimeBudgetComparisonViewModel.Overcrowding=% Superlotação
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Turno de ordenha por hora
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Potencial perda/ganho de leite ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Diferença de tempo de descanso (horas)
PenTimeBudgetComparisonViewModel.TableTitle=Comparação 
PenTimeBudgetComparisonViewModel.TimePerMilking=Tempo por ordenha (horas)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Tempo restante para descanso (horas)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Tempo necessário para descanso (horas)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Tempo total sem descanso (horas)
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Tempo total de ordenha (horas)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Caminhada para chegar até as camas (horas)
PenTimeBudgetPenMasterViewModel.Compare=Comparação 
PenTimeBudgetPenMasterViewModel.Inputs=Inputs
PenTimeBudgetPenMasterViewModel.Results=Resultados 
PenTimeBudgetPenMasterViewModel.Title=Orçamento de Tempo de Descanso
PenTimeBudgetResultsViewModel.Hours=Horas
PenTimeBudgetResultsViewModel.MilkDifference=Diferença de leite potencial
PenTimeBudgetResultsViewModel.MilkLossKg=Kg
PenTimeBudgetResultsViewModel.MilkLossPounds=Lbs
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Potencial perda/ganho de leite
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Tempo disponível para descanso
PenTimeBudgetResultsViewModel.TimeRemaining=Tempo restante 
PenTimeBudgetResultsViewModel.TimeRequired=Tempo necessário 
PenTimeBudgetResultsViewModel.Title=Resultados - Avaliação do Tempo de Descanso
PenTimeInputsViewModel.CowsPen=Vacas no lote
PenTimeInputsViewModel.Drinking=Tempo bebendo água e socializando (horas)
PenTimeInputsViewModel.Eating=Tempo de alimentação (horas)
PenTimeInputsViewModel.Frequency=Frequência de ordenha (por dia)
PenTimeInputsViewModel.LockUp=Tempo fechada (horas)
PenTimeInputsViewModel.NonRestTime=Outro tempo sem ser descanso (horas)
PenTimeInputsViewModel.ParlorTime=Tempo em ordenha (horas)
PenTimeInputsViewModel.PenTimeTitle=Orçamento de Tempo de Descanso
PenTimeInputsViewModel.Resting=Requerimento de descanso (horas)
PenTimeInputsViewModel.StallsPen=Camas por lote
PenTimeInputsViewModel.TotalStalls=Total de lugares na ordenha
PenTimeInputsViewModel.WalkingTimeFrom=Tempo de deslocamento de/para a ordenha (horas)
PenTimeInputsViewModel.WalkingTimeTo=Tempo andando para ordenha (horas)
PenTimePenSelectionViewModel.NoLactatingPen=Para acessar essa ferramenta, certifique-se de ter pelo menos um grupo com uma ração de lactação
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Ferramenta de Calcular Tempo de Descanso
PenTimePenSelectionViewModel.PenTimeSection=LOTES
PenTimePenSelectionViewModel.Title=Orçamento de Tempo de Descanso
Pendetails=Detalhes do lote
PennStateShakerBoxForageResults=Resultados de forragem Penn State
Pennsylvania=Pensilvânia
Pens=Lotes
PerceivedHeatStressDietInfoMessage=Estresse calórico moderado\: ofegante, babando ou espumando, mas sem boca aberta, com uma taxa de respiração de 40 a 120 BPM.
PercentLossPerCow=% perdas / vaca
PercentOnScreenTitle=Porcentagem na peneira (%)
PercentPen=Porcentagem do lote (%)
PercentageOnScreen=Porcentagem na peneira (%)
PercentageOnScreenCurrentVisit=Porcentagem da camada (%) - visita atual
PercentageOnScreenTrend=Porcentagem da camada (%) - tendência
Pernambuco=Pernambuco
Peru=Peru
Perugia=Perugia
Pesaro_and_Urbino=Pesaro e Urbino
Pescara=Pescara
Philippines=Filipinas
PhotoExamples=Foto de exemplos
Piacenza=Piacenza
Piauí=Piauã
Pickup=Retirada de leite {0}
Pile=Silo de trincheira
PileAndBunkerCapacitiesDensity=Guia de referência para capacidade e densidade de Silos de superfície e trincheira
PileAndBunkerCapacity=Capacidade dos silos de superfície e trincheira
PileAndBunkerCapacityViewModel.AddBunker=Adicionar Novo Silo de Trincheira
PileAndBunkerCapacityViewModel.AddPile=Adicionar Silo de Superfície
PileAndBunkerCapacityViewModel.Bunker=Silo de trincheira
PileAndBunkerCapacityViewModel.Bunkers=Silos de trincheira
PileAndBunkerCapacityViewModel.NameNotUnique=O silo de superície ou trincheira nomeado como "{0}" já existe. Nomes devem ser únicos
PileAndBunkerCapacityViewModel.NameTooLong=O nome do silo de superície ou trincheira deve conter 40 caracteres ou menos
PileAndBunkerCapacityViewModel.Pile=Silo de trincheira
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Capacidade dos silos de superfície e trincheira
PileAndBunkerCapacityViewModel.Piles=Silo de trincheira
PileAndBunkerCapacityViewModel.Resources=Recursos 
PileAndBunkerCapacityViewModel.Title=Capacidade dos silos de superfície e trincheira
PileAndBunkerCapacityViewModel.VisitNotebook=Anotações da visita
PileAndBunkerName=Nome do silo de superfície e trincheira 
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Comprimento inferior ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Largura inferior ({0})
PileAndBunkerResultsCapacityInputViewModel.Capacity=Capacidade 
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=Matéria seca %
PileAndBunkerResultsCapacityInputViewModel.Height=Altura ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=Tonelada em matéria natural
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Tonelada em matéria seca
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Comprimento inferior (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Comprimento inferior (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Largura inferior (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Largura inferior (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Altura (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Altura (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Comprimento superior (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Comprimento superior (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Largura superior (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Largura superior (m.)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Densidade silagem MS ({0})
PileAndBunkerResultsCapacityInputViewModel.Title=Capacidade 
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Densidade silagem MN {0} (Objetivo\: &gt;44)
PileAndBunkerResultsCapacityInputViewModel.TonsAF=Tons MN
PileAndBunkerResultsCapacityInputViewModel.TonsDM=Tons MS
PileAndBunkerResultsCapacityInputViewModel.TopLength=Comprimento superior ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Largura superior ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=A 6 in. Por dia
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=A 15 cm. Por dia 
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=A 3 in. Por dia 
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=A 7 cm. Por dia
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=vacas / dia necessárias 
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Vacas para serem alimentadas
PileAndBunkerResultsFeedOutViewModel.DateGone=Data final
PileAndBunkerResultsFeedOutViewModel.Days=Dias
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Info de consumo 
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Superfície de consumo (ft^2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Superfície de consumo (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Info de consumo (MN / 
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=In. Por dia 
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=cm. Por dia 
PileAndBunkerResultsFeedOutViewModel.Resources=Recursos 
PileAndBunkerResultsFeedOutViewModel.StartDate=Data de início 
PileAndBunkerResultsFeedOutViewModel.Title=Consumo 
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tons por dia 
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Kbs. MS em 1 foot
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kgs. MS em 1 metro
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Capacidade 
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Consumo 
PileAndBunkerResultsMasterViewModel.VisitNotebook=Anotações da visita
PileAndBunkerTitle=Silo de superfície e trincheira
PileBunkerCapacities=Capacidade dos silos de superfície e trincheira
PileCapacity=Capacidade do Silo
PileFeedOutRate=Taxa de remoção de silagem
Piles=Silos de Superfície
Pisa=Pisa
Pistoia=Pistoia
Pitcairn=Pitcairn
PlBacteriaCell=Contagem bacteriana (1,000 cfu/mL)
PlMilkFat=Gordura do leite
PlMilkProtein=Proteína do leite 
PlSomaticCell=Contagem células somáticas (1,000 cels/mL)
Poland=Polônia
Poor=Pobre 
Por=favor, mantenha o aplicativo aberto enquanto a sincronização está em andamento. Ela pode demorar mais tempo se a conexão estiver lenta
Pordenone=PORDENONA
Porosity=Porosidade 
Portugal=Portugal
Postweaned=Pós desmame
Postweaned_CleanAndDryPen=Local limpo e seco
Postweaned_CleanAndDryPen_ToolTip=Usar o teste dos joelhos para determinar se a área é limpa e seca 
Postweaned_EvidenceOfAcidosisInManure=Evidência de acidose nas fezes
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Existem bolhas nas fezes mole
Postweaned_EvidenceOfScoursOrPneumonia=Evidência de diarréia ou pneumonia
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% das bezerras possuem diarréia ou pneumonia
Postweaned_FeedBunkIsClaanedDaily=Cocho de alimento é limpo diariamente, sendo removido as sobras
Postweaned_ForageAvailability=Disponibilidade de forragens
Postweaned_ForageAvailability_ToolTip=Tamanho de fibra maior que 5cm
Postweaned_FreeChoiceCleanWaterIsAvailable=Água limpa e de livre acesso está disponível
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=Sem evidências de contaminação na água
Postweaned_FreshQualityStarterAvailable=Ração inicial/crescimento de qualidade e fresca disponível
Postweaned_FreshQualityStarterAvailable_ToolTip=Ração inicial/crescimento não possui finos, nem mofo e não está molhada
Postweaned_SizeOfBunkSpace=Tamanho de cocho de alimentação é adequado por bezerra
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45cm por bezerra
Postweaned_SizeOfPenAdequate=Tamanho de cama é adequado por bezerra
Postweaned_SizeOfPenAdequate_ToolTip=Cama individual\: 3m2, Cama em grupo\: 2,5m2
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Cama bem ventilada, sem ventilador nas bezerras
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=Se a roupa fica com cheiro forte de amônia após deixar o bezerreiro
PotentialDownResponse=Potencial resposta negativa ({0}/vaca/dia)
PotentialSCC=Potencial CCS (cels/{0})
Potenza=Poder
Prato=Prato
PreWeaned_CMRisProperlyMixed_ToolTip=Total de leite em pó\: &gt;600g &lt;800g, Temp\: 39-41C, Sólidos 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Usar o teste dos joelhos para determinar se a área é limpa e seca 
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% das bezerras possuem diarréia ou pneumonia
PreWeaned_Forageavailability_ToolTip=Em textura da alimentação, forragem não é necessária
PreWeaned_FreeChoiceCleanWater_ToolTip=Disponível desde o primeiro dia, sem evidência de contaminação na água
PreWeaned_FreeChoiceFreshCalf_ToolTip=Ração inicial não possui finos, ou mofo e não está molhada
PreWeaned_SizeOfPen_ToolTip=Cama individual\: 3m2, Cama em grupo\: 2,5m2
PreWeaned_WellVenilated_ToolTip=Se a roupa fica com cheiro forte de amônia após deixar o bezerreiro
PrematureKelvingsKeyInfoMessage=A entrega de um ou mais bezerros vivos pelo menos 10 dias antes da data de entrega
PreventingStorageLosses=Prevenção de perdas de armazenamento 
Previous=Anterior 
Preweaned=Pré desmame
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=Leite em pó adequadamente misturado e fornecido
Preweaned_CleanAndDryPen=Cama limpa e seca
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Limpeza e higienização adequados do equipamento de alimentação dos bezerros entre as mamadas
Preweaned_ConsistentFeedingTimesAndProtocols=Tempo de alimentação e protocolos consistentes
Preweaned_EvidenceOfScoursOrPneumonia=Evidência de diarréia ou pneumonia
Preweaned_ForageAvailability=Disponibilidade de forragens
Preweaned_FreeChoiceCleanWaterIsAvailable=Água limpa e de livre acesso está disponível
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Disponibilidade de ração de bezerra inicial de livre acesso e fresca
Preweaned_SizeOfPenAadequatePerHeifer=Tamanho de cama é adequado por novilha
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Desmame acontece após ganhar 1kg por dia com ração inicial
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Camas bem ventiladas sem ventilador direto nas bezerras 
PricingMatrixEditViewModel.Cancel=Cancelar 
PricingMatrixEditViewModel.Save=Salvar 
PricingMatrixEditViewModel.Title=Detalhe do item, matriz
PricingMatrixPickListViewModel.PricingMatrix=Matriz de preços 
PricingMatrixViewModel.Amount=Quantidade (1000 cels/ml)
PricingMatrixViewModel.AmountCFU=Quantidade (1000 cfu/ml)
PricingMatrixViewModel.New=Novo
PricingMatrixViewModel.Title=Editar Matriz
Prince_Edward_Island=Ilha Principe Edward
Privacy_Statement=Política de privacidade 
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Moeda
ProcessorCurrencyPickListViewModel.Title=Moeda
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Calculadora de receita do leite
ProductivityToolsViewModel.MilkRevenueAnalysis=Análise de receita do leite
ProductivityToolsViewModel.MilkSoldEvaluation=Avaliação do leite vendido
ProductivityToolsViewModel.ProductivityTitle=Ferramenta de Produtividade
ProductivityToolsViewModel.ProductivityTools=Ferramentas
ProductivityToolsViewModel.VisitNotebook=Anotações da visita
Profitability.Analysis.Milk.Price.Chart.Title=Preço de leite vs Preço Alimentação
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Custo da alimentação por litro de leite
ProfitabilityAnalysis.Iofc=RMCA
ProfitabilityAnalysis.Milk.Price=Preço de leite($)
ProfitabilityAnalysis.Production.In.150.Dim=Produção corrigida 150 DEL (/vaca)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Produção corrigida 150 DEL (/vaca) vs RMCA
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Receita / vaca / dia
ProfitabilityAnalysis.Total.Diet.Cost=Custo total da dieta (R$/Vaca/dia
ProfitabilityAnalysis.TotalProduction=Produção (kg/vaca/dia)
ProfitabilityAnalysis.TotalProduction.Concentrated=Produção total / Total de concentrado consumido
ProfitablityAnalysis.Date=Data
ProftabilityAnalysis.TotalProduction.Chart.Title=Produção total x Concentrado consumido
PromptCancel=Cancelar 
PromptOK=Ok
ProspectProfileViewModel.DeleteProspect=Deletar prospecto
ProspectProfileViewModel.DeleteProspectPrompt=Tem certeza que deseja deletar esse prospecto? Informações de prospecto, local e a visita atual serão perdidas
ProspectProfileViewModel.MainHeading=Local
ProspectProfileViewModel.NewSite=Adicionar Novo Local
ProspectProfileViewModel.ProspectInfo=Informações do prospecto
ProspectProfileViewModel.ProspectTitle=Detalhes do prospecto
ProspectsViewModel.NewProspect=Adicionar Novo Prospecto
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Receita / vaca / dia vs Custo total da dieta (R$/Vaca/dia)
Provimi=Promivi
ProvimiUS=Provimi US
PublishVisit=Publicado
Puducherry=Puducherry
Puebla=Puebla
Puerto_Rico=Porto Rico
Punjab=Punjab
Purina=Purina
Qatar=Catar
Qinghai=Qinghai
QuarterPointScale=Escala de um quarto de ponto
Quarterly=Trimestral
Quebec=Quebec
Queensland=Queensland
Querétaro=Quer é Taro
QuestionTableTitle=Questão {0} de {1}
QuestionViewModel.Baleage=Silagem em Bolsas
QuestionViewModel.BunkersAndPiles=Silo de superfície e trincheira
QuestionViewModel.Close=Fechar
QuestionViewModel.Harvest=Colheita
QuestionViewModel.MaintainingForageQuality=Manutenção da qualidade da forragem
QuestionViewModel.SilageBags=Silo de bolsa
QuestionViewModel.SurveyOfForages=Pesquisa de forragens
QuestionViewModel.TowerSilos=Silos verticais
QuestionViewModel.VisitNotebook=Anotações da visita
Quintana_Roo=Quintana Roo
ROL=ROL
RUB=Rússia (₽‎ RUB)
Ragusa=Ragusa
Rajasthan=Rajasthan
Rationcost=Custo da ração ({0})
Ravenna=Ravenna
ReadyToMilkChartViewModel.Current=Atual 
ReadyToMilkChartViewModel.DeathLoss=Mastite
ReadyToMilkChartViewModel.DisorderGraphTitle=Custo anual desordens metabólicas / vaca
ReadyToMilkChartViewModel.DisplacedAbomasum=Displasia de abomaso
ReadyToMilkChartViewModel.Dystocia=Distocia
ReadyToMilkChartViewModel.Ketosis=Cetose
ReadyToMilkChartViewModel.Metritis=Metrite 
ReadyToMilkChartViewModel.MilkFever=Febre do leite
ReadyToMilkChartViewModel.RetainedPlacenta=Retenção de placenta
ReadyToMilkChartViewModel.Title=Gráficos Ready2Milk™
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Índice Ready2Milk™
ReadyToMilkInputViewModel.Back=Voltar 
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Alteração de ECC do período seco até 21 DEL
ReadyToMilkInputViewModel.CloseUp=Vacas em Pré-Parto
ReadyToMilkInputViewModel.ComfortCloseUp=Conforto no Pré-Parto
ReadyToMilkInputViewModel.ComfortDiet=Conforto de 0 a 21 DEL
ReadyToMilkInputViewModel.CostExtraDaysOpen=Custo por dia extra em aberto
ReadyToMilkInputViewModel.CudChewingDiet=Vacas pós pasto - Ruminações
ReadyToMilkInputViewModel.DeadCowsOrCulled=Descartes/Mortes relacionadas a problemas de saúde
ReadyToMilkInputViewModel.DisplacedAbomasum=Deslocamento de abomaso
ReadyToMilkInputViewModel.Dystocia=Distocia
ReadyToMilkInputViewModel.FreshCows=Vacas pós parto 
ReadyToMilkInputViewModel.Health=SAÚDE
ReadyToMilkInputViewModel.HealthDesc=
ReadyToMilkInputViewModel.HealthRecords=Registros de saúde
ReadyToMilkInputViewModel.Herd=INFORMAÇÕES A NÍVEL DE REBANHO
ReadyToMilkInputViewModel.Ketosis=Cetose
ReadyToMilkInputViewModel.LocomotionScore=Escore de Locomoção
ReadyToMilkInputViewModel.Mastitis=Mastite 0-21 DEL
ReadyToMilkInputViewModel.Metritis=Metrite 
ReadyToMilkInputViewModel.MilkFever=Febre do leite
ReadyToMilkInputViewModel.MilkPrice=Preço do leite
ReadyToMilkInputViewModel.MilkYield=Produção média de 15-60 DEL
ReadyToMilkInputViewModel.Next=Próximo
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Estresse calórico percebido de 0-21 DEL
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Estresse calórico percebido de 0-21 DEL
ReadyToMilkInputViewModel.PrematureCalvings=Nascimentos prematuros
ReadyToMilkInputViewModel.ReplacementCowCost=Custo de reposição das vacas
ReadyToMilkInputViewModel.RetainedPlacenta=Retenção de placenta
ReadyToMilkInputViewModel.RumenFill=Preenchimento ruminal
ReadyToMilkInputViewModel.SccFirstTest=CCS 1° teste (x1000 CCS/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Dieta específica de pré parto
ReadyToMilkInputViewModel.SpecificDiet=Dieta específica de 0-21 DEL
ReadyToMilkInputViewModel.Title=Inputs Ready2Milk™
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Total de vacas pós parto / ano
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Avalição do total de vacas pós parto
ReadyToMilkListViewModel.Title=Índice Ready2Milk™
ReadyToMilkMasterViewModel.Charts=Gráficos 
ReadyToMilkMasterViewModel.Inputs=Inputs
ReadyToMilkMasterViewModel.MastitisNotPresent=Mastite não foi preenchido
ReadyToMilkMasterViewModel.Outputs=Resultados
ReadyToMilkMasterViewModel.Title=Índice Ready2Milk™
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Índice Ready2Milk™
ReadyToMilkOutputViewModel.Title=Resultados Ready2Milk™
RecommendedTLCSettings=Recomendações para tamanho de partículas de forragem
Reggio_Calabria=Reggio Calabria
Reggio_Emilia=Reggio Emilia
RemovedAndMeasured=Removed and measured
RemovedOnly=Removed only
Report=Relatório
Report.Analysis.Type=Tipo de análise
Report.Animal.Analysis=Análise Animal
Report.Animal.Tag.Name=ID do animal
Report.AvgRumenFillScore=Méd. Preenchimento ruminal (calculado)
Report.BCS.EvalDataTitle=Dados calculados de avaliação do ECC
Report.BCS.LactationStages=Estágios de lactação
Report.BCS.Max=ECC max.
Report.BCS.MilkHeadDay=Leite/rebanho/dia
Report.BCSAvg=Média ECC
Report.Bcs=ECC
Report.Bcs.ChartName=Pontuação da condição corporal - Animal {0}
Report.Bcs.HerdAnalysis.ChartName=ECC vs leite
Report.Bcs.Milk=Leite
Report.Bcs.Min=ECC min.
Report.Calving.Date=Parto
Report.Cargill.Report=Cargill relatório
Report.Chewing=Ruminando
Report.Chews=Mastigações
Report.CudChewing.EvalDataTitle=Dados de avaliação de ruminação
Report.CudChewingPercentage=% Ruminações
Report.CudChewingPercentage.Vs.LactStages=% Ruminações
Report.EvalDataTitle=Dados de avaliação calculados
Report.ForagePennState=Penn state de forragem
Report.General.Comments=Geral Comentários
Report.GoalCudChewingPercentage=Objetivo % mastigações
Report.Heatstress.Dmi.Adjustment=Ajuste IMS
Report.Heatstress.Energy.Equivalent.Milk.Loss=Perda de leite equivalente a energia ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Ingestão estimada de matéria seca ({0})
Report.Heatstress.Intake.Adjustment=Ajuste da ingestão
Report.Heatstress.Legend=Legenda
Report.Heatstress.Legends=Legendas
Report.Heatstress.Loss.Of.Energy.Consumed=Perda de energia consumida (MCAL)
Report.Heatstress.Mild.Moderate.Stress=Leve - estresse moderado
Report.Heatstress.Mild.Moderate.Stress.Message=A respiração excede 75 bpm | A temperatura retal excede 39 \\ U2103 (102.2 \\ U2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Perda de leite (por mês) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Perda de leite (por dia) ({0})
Report.Heatstress.Moderate.Severe.Stress=Moderado - estresse grave
Report.Heatstress.Moderate.Severe.Stress.Message=A respiração excede 85 bpm | A temperatura retal excede 40 \\ U2103 (104 \\ U2109)
Report.Heatstress.Reduction.In.Dmi=Redução na IMS ({0})
Report.Heatstress.Relative.Humidity=Umidade relativa (%)
Report.Heatstress.Severe.Stress=Estresse grave
Report.Heatstress.Severe.Stress.Message=A respiração excede 120-140 bpm | A temperatura retal excede 41 \\ U2103 (106 \\ U2109)
Report.Heatstress.Stress.Threshold=Limiar de estresse
Report.Heatstress.Stress.Threshold.Message=A respiração excede 60 bpm | Reprodir perdas detectáveis ​​| A temperatura retal excede 38.5 \\ u2103 (101.3 \\ u2109)
Report.Heatstress.Temperature=Temperatura
Report.Heatstress.Temperature.In.Celcius=Temperatura \\ u2103
Report.Heatstress.Temperature.In.Farenhiet=Temperatura \\ U2109
Report.Heatstress.TemperatureHumidityIndex=Índice de umidade e temperatura
Report.Herd.Analysis.CudChewingPercentage=% Ruminações
Report.Locomotion.HerdAnalysis.ChartName=% Pontuação de locomoção
Report.LocomotionScore.X.Axis=Pontuação de locomoção
Report.LocomotionScore.Y.Axis=%
Report.LocomotionScore.chartName=Pontuação de locomoção - Animal {0}
Report.No.OfChews=Número de mastigações
Report.No.OfChewsPerRegurgitation=No. de mastigações por regurgitação
Report.NoOfChews.Vs.LactStages=Número de mastigações
Report.Not.Chewing=Não ruminando
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Tempo disponível para descanso
Report.PenTimeBudget.TimeAvailableForResting.Label=Horas
Report.PenTimeBudgetTimeRemaining=Tempo restante
Report.PenTimeBudgetTimeRequired=Tempo necessário
Report.Pentime.Budget.Hours=Horas
Report.PercentageOnScreen=Porcentagem da peneira (%)
Report.RumenHealthManureScreening.Bottom=Fundo
Report.RumenHealthManureScreening.BottomGoalMax=Objetivo max (fundo)
Report.RumenHealthManureScreening.BottomGoalMin=Objetivo mín (fundo)
Report.RumenHealthManureScreening.Middle=Meio
Report.RumenHealthManureScreening.MiddleGoalMax=Objetivo max (meio)
Report.RumenHealthManureScreening.MiddleGoalMin=Objetivo mín (meio)
Report.RumenHealthManureScreening.Top=Superior
Report.RumenHealthManureScreening.TopGoalMax=Objetivo max (sup)
Report.RumenHealthManureScreening.TopGoalMin=Objetivo mín (sup)
Report.SheetName=Relatório Master
Report.Tool.Details=Ferramentas Detalhes
Report.Tool.Name=Nome da ferramenta
Report.Visit.Date=Data da visita
Report.Visit.Report=Relatório da visita
Report.Visit.name=Nome da visita
Report.goalChews=Objetivo de mastigações
Report.locomotionScore.Pen.Analysis.ChartName=Categorias vs data da visita
ReportDate=Data do relatório
ReportPDFNote=Nota\:
Reset_Database=Redefinir banco de dados
Resources=Recursos 
ResourcesViewModel.Title=Recurso de auditoria de forragens
ResourcesViewModel.VisitNotebook=Anotações da visita
RetainedPlacenta=Retenção de placenta
Reunion=Reunião
Revenue=Receita
RevenueEditComparisonValuesViewModel.CurrentSCC=Atual CCS (cels/{0})
RevenueEditComparisonValuesViewModel.DownResponse=Resposta negativa ({0}/vaca/dia)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Alterar Valores de Comparação
RevenueEditComparisonValuesViewModel.MilkChange=Mudança na produção de leite (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Preço do leite ($/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Produção de leite 
RevenueEditComparisonValuesViewModel.NumOfCows=Número de vacas 
RevenueEditComparisonValuesViewModel.PotentialSCC=CSS potencial (cels/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Cenário 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Cenário 2
RevenueEditComparisonValuesViewModel.Title=Editar comparações de valores
RevenueInputViewModel.ComparisonValues=Valores de comparação
RevenueInputViewModel.Edit=Editar 
RevenueInputViewModel.ScenarioOne=Cenário 1
RevenueInputViewModel.ScenarioTwo=Cenário 2
RevenueInputViewModel.Title=Inputs - Receita de ordenha
RevenueLossDay=Perda de receita ({0}/dia)
RevenueLossYear=Perda de receita ({0}/ano)
Rheinland=Renânia
Rhode_Island=Rhode Island
Rieti=Rieti
Rimini=Rimini
Rio_Grande_do_Norte=Rio Grande do Norte
Rio_Grande_do_Sul=Rio Grande do Sul
Rio_de_Janeiro=Rio de Janeiro
Robot=Robô
RoboticMilkEvaluationSpinnerViewModel.Title=Avaliação da ordenha robotizada
Romania=România
Rome=Roma
RondÃ´nia=Rondênia
Roraima=Roraima
Roscommon=Roscommon
Rovigo=Rovigo
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Análise do Rebanho 
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Análise do lote 
RumenHealthBodyConditionLandingViewModel.Pens=Lotes
RumenHealthBodyConditionLandingViewModel.Resources=Recursos 
RumenHealthBodyConditionLandingViewModel.Title=Escore de Condição Corporal
RumenHealthEditManureScoresViewModel.Close=Fechar
RumenHealthEditManureScoresViewModel.Count=Contagem
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Contar o número de vacas
RumenHealthEditManureScoresViewModel.NumOfCows=Número de vacas 
RumenHealthEditManureScoresViewModel.NumberOfCows=Número de vacas 
RumenHealthEditManureScoresViewModel.VisitNotebook=Anotações da visita
RumenHealthLandingViewModel.HerdAnalysis=Análise do Rebanho
RumenHealthLandingViewModel.PenAnalysis=Análise do lote 
RumenHealthLandingViewModel.Pens=Lotes
RumenHealthLandingViewModel.Title=Ruminação - Saúde ruminal
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Análise do Rebanho
RumenHealthLocomotionLandingViewModel.PenAnalysis=Análise do lote 
RumenHealthLocomotionLandingViewModel.Pens=Lotes
RumenHealthLocomotionLandingViewModel.Resources=Recursos 
RumenHealthLocomotionLandingViewModel.Title=Locomoção
RumenHealthManureLandingViewModel.HerdAnalysis=Análise do Rebanho
RumenHealthManureLandingViewModel.PenAnalysis=Análise do lote 
RumenHealthManureLandingViewModel.Pens=Lotes
RumenHealthManureLandingViewModel.Resources=Recursos 
RumenHealthManureLandingViewModel.Title=Saúde ruminal - Escore de fezes
RumenHealthManureMasterViewModel.Inputs=Inputs
RumenHealthManureMasterViewModel.Results=Resultados
RumenHealthManureMasterViewModel.RumenHealthManureScore=Saúde ruminal - Escore de fezes
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Saúde ruminal - Escore de fezes
RumenHealthManureMasterViewModel.VisitNotebook=Anotações da visita
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Escore médio
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Data
RumenHealthManureScoresResultsViewModel.PercentPen=Porcentagem do lote (%)
RumenHealthManureScoresResultsViewModel.SelectedDates=Selecionar datas
RumenHealthManureScoresResultsViewModel.Title=Resultados de escore de fezes
RumenHealthManureScoresViewModel.AnimalsObserved=Animais Observados
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Méd. Escore de fezes (Calculado)
RumenHealthManureScoresViewModel.Edit=Editar
RumenHealthManureScoresViewModel.ManureScore=Escore de Fezes
RumenHealthManureScoresViewModel.PercentOfPen=Porcentagem do lote (%)
RumenHealthManureScoresViewModel.ScoreCategory=Categoria escore de fezes
RumenHealthManureScoresViewModel.StdDevCalculated=Desvio padrão (calculado)
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=Calculadora de mastigação
RumenHealthPenCudCalculatorViewModel.CudChewing=Ruminação 
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Registrar o número de vacas ruminando
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Número de mastigação 
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Registrar o número de mastigação por vaca
RumenHealthTMRLandingViewModel.HerdAnalysis=Análise do Rebanho
RumenHealthTMRLandingViewModel.PenAnalysis=Análise do lote 
RumenHealthTMRLandingViewModel.Pens=Lotes
RumenHealthTMRLandingViewModel.Title=Penn State - Saúde Ruminal
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Inserir quantidade (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Inserir peso da tara (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Objetivo - máx (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Objetivo - mín (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Meio 1 (8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Meio 2(1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Meio 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Nome TMR escore de partícula
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Tara
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Inserir a quantidade 
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Peneira superior (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Peneira inferior 
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Adicionar TMR Escore de Partícula
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Escore médio 
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Atual
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Quantidade (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Objetivo - máx (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Objetivo - médio 1 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Objetivo - médio 2 (1.18 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Objetivo médio 2 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Objetivo mín (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Objetivos 
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Objetivo peneira superior (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Objetivo peneira inferior 
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Máx
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Meio 1 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Meio 2
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Meio 2 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Mín
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Escore das partículas (% da peneira)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Porcentagem da peneira (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Desvio padrão 
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Informação TMR escore partículas
RumenHealthTMRParticleScorePenTableInputViewModel.Title=TMR pontuação escore
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Peneira superior (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Peneira inferior
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Inputs
RumenHealthTMRPenScorerTableMasterViewModel.Results=Resultados
RumenHealthTMRPenScorerTableMasterViewModel.Title=Escore de partícula - Saúde ruminal
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Nenhum selecionado
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Um avaliador deve ser escolhido antes de visualizar um lote
RumenHealthTMRSelectPenViewModel.SelectPen=Lotes (lactação e vaca seca somente)
RumenHealthTMRSelectPenViewModel.SelectScorer=Selecionar marcador
RumenHealthTMRSelectPenViewModel.Title=Escore de partícula - Saúde ruminal
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Nenhum selecionado
RumenHealthTMRSelectScorerViewModel.FooterText=Apenas 1 tipo de marcador pode ser escolhido por visita. Mudando o marcador você perderá os resultados
RumenHealthTMRSelectScorerViewModel.SelectScorer=Selecionar marcador
RumenHealthTMRSelectScorerViewModel.Title=Escore de partícula - Saúde ruminal
Russia=Rússia
Russian_Federation=Federação Russa
Rwanda=Ruanda
SAR=Arábia Saudita (﷼ SAR)
SCCPremiumDeduction=CCS prêmio / dedução ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Suriname ($ SRD)
Saint_Barthélemy=São Bartolomeu
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Ascension and Tristan da Cunha
Saint_Kitts_and_Nevis=Saint Kitts e Nevis
Saint_Lucia=Santa Lúcia
Saint_Martin_(French_part)=Saint Martin (parte francesa)
Saint_Pierre_and_Miquelon=São Pierre e Miquelon
Saint_Vincent_and_the_Grenadines=São Vicente e Granadinas
Salerno=Salerno
Samoa=Samoa
San_Luis_Potosí=San Luis Potosã
San_Marino=San Marino
Santa_Catarina=Santa Catarina
Sao_Tome_and_Principe=São Tomé e Príncipe
Saskatchewan=Saskatchewan
Sassari=Sassari
SaudiArabia=Arábia Saudita
Saudi_Arabia=Arábia Saudita
Savona=Savona
Schleswig=Schleswig
ScorecardPrompt=Complete a pesquisa abaixo para ver o {0} escore card
Screen=peneira
ScreenNew=peneiras nova
ScreenOld=peneiras antiga
Search=Pesquisar 
SegmentViewModel.SegmentTitle=Selecionar um segmento 
SegmentViewModel.Title=Detalhes
Select=Selecionar 
SelectCurrencyViewModel.Title=Atual
SelectDates=Selecionar datas
SelectFeedingSystemViewModel.SelectFeedingSystem=Selecionar sistema de alimentação
SelectFeedingSystemViewModel.Title=Configuração do lote
SelectForageImprovement=Please select only 12 improvement from list.
SelectHousingSystemViewModel.SelectHousingSystem=Escolher o tipo de instalação 
SelectHousingSystemViewModel.Title=Configuração do lote
SelectImprovement=Selecionar somente 10 melhorias da lista
SelectMatrix=Selecionar A matriz
SelectMilkingSystemViewModel.NoneSelected=Nenhum selecionado
SelectMilkingSystemViewModel.SelectMilkingSystem=Selecionar o sistema de ordenha
SelectMilkingSystemViewModel.Title=Configuração do local 
SelectOnlyThreeNotes=Selecionar somente 3 anotações por ferramenta
SelectOnlyTwoNotes=Selecionar somente 2 anotações por ferramenta 
SelectPen=Selecionar uma dieta para o novo lote
SelectProcessor=Selecionar o processador
SelectProcessorViewModel.COMPONENT=Componente 
SelectProcessorViewModel.CONCENTRATION=Concentração 
SelectProcessorViewModel.Edit=Editar 
SelectProcessorViewModel.MilkProcessors=Processador do leite 
SelectVisitComparison=Selecione visitas para comparação
SemiAnnually=Semestralmente
Semiconfinamento=Semiconfinamento
Send=Enviar 
Senegal=Senegal
Seoul=Seul
Serbia=Sérvia
Sergipe=Sergipe
SettingsViewModel.Imperial=Imperial
SettingsViewModel.Metric=Métrico 
SettingsViewModel.Milk_Processor_Set_Up=Configurações do processador de leite
SettingsViewModel.More_Settings=Mais configurações
SettingsViewModel.Select_Unit_Of_Measure=Selecionar unidade de medida
Severe=Severo 
Seychelles=Seychelles
Shaanxi=Shaanxi
Shandong=Shandong
Shanghai=Xangai
Shanxi=Shanxi
ShortDryPeriod=Período seco curto
ShowEulaViewModel.Accept=Aceitar 
ShowEulaViewModel.ConfirmationNo=Não
ShowEulaViewModel.ConfirmationText=Você aceita os termos do contrato de licença do usuário final do aplicativo para dispositivos móveis?
ShowEulaViewModel.ConfirmationTitle=Confirmação 
ShowEulaViewModel.ConfirmationYes=Sim 
ShowEulaViewModel.Decline=Declinar 
ShowEulaViewModel.Eula=Contrato de licença para usuário final
ShowEulaViewModel.EulaError=Não é possível visualizar o contrato de licença do usuário final. Por favor, conecte-se à Internet antes de tentar novamente.
ShowEulaViewModel.EulaScreenTitle=Licença do usuário final
ShowPrivacyStatementViewModel.PrivacyStatement=&lt;p&gt;&lt;b&gt;Declaração de privacidade&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Última atualização\: Janeiro 3, 2017&lt;/p&gt;&lt;p&gt;&lt;b&gt;Scope&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Cargill, Incorporated (“Cargill” ou “Nós”) coleta informações sobre você quando usar esse aplicativo de celular (“App”), que é direcionado para consultores oferecendo o seviço Dairy Enteligen™ em nome da  Cargill.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Informações pessoais&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Cargill pode coletar as seguintes informações pessoais diretamente de você, or exemplo, quando você se registra conosco e através do uso do aplicativo.&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Seu nome&lt;/b&gt;&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Sua localização&lt;/b&gt; (via GPS ou tecnologia similar)&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Suas fotos &lt;/b&gt;ou&lt;b&gt;Videos&lt;/b&gt; (se compartilhado com Cargill via App)&lt;/p&gt;&lt;p&gt;Nos podemos usar tecnologias em comum, como cookies e beacons, no App para registar informações pessoais. &lt;/p&gt;&lt;p&gt;&lt;b&gt;Usar e compartilhar – Contexto do negócio&lt;/b&gt;&lt;/p&gt;&lt;p&gt;nosso&lt;a href\="http\://www.cargill.com/privacy/business-notice/index.jsp"&gt;Aviso de informação comercial&lt;/a&gt; explica como usamos informações pessoais coletadas sobre você em um contexto de negócios.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Consentimento de Coleta de Local&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Ao usar o App, você consente expressamente com a coleção de informações de localização em tempo real da Cargill, e renuncia expressamente de liberar a Cargill de toda e qualquer responsabilidade, reclamações, causas de ação ou danos decorrentes do seu uso do Aplicativo, ou de qualquer forma relacionadas ao uso das informações de localização.&lt;/p&gt;
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Política de privacidade 
ShowSyncStatusViewModel.GetAccounts=Dados recebidos na conta\:
ShowSyncStatusViewModel.GetNotes=Anotações recebidas\:
ShowSyncStatusViewModel.GetVisits=Dados recebidos em visita\:
ShowSyncStatusViewModel.Title=Resumo da sincronização de dados
Sichuan=Sichuan
Siena=Siena
Sierra_Leone=Serra Leoa
Sikkim=Sikkim
SilageBags=Silagem em bolsa
SilageBags_BagsPlacedOnStableWellManagedSurface=Os silos de bolsa são colocados em uma estrutura dedicada, bem gerenciados em uma superfície de asfalto ou concreto?
SilageBags_BonusSecureCoverIsUsed=Bônus\: utiliza capa de segurança?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Frente do silo bem manejada, sem indicação de perdas de alimentos, aquecimento ou vazamento
SilageBags_FaceRemovalRate=Taxa de remoção da frente do silo
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Inspecionado por danos e pragas e reparado regularmente?
SilageBags_PorosityScoresConsistently=Porosidade é consistente
SilageBags_TrashVegRodentControlledAroundBags=Controle de lixo, vegetação e roedores ao redor das bolsas
SilagePrevention1st=Prevenção da silagem\: 1as coisas 1°
Sinaloa=Sinaloa
Singapore=Cingapura
Sint_Maarten_(Dutch_part)=Sint Marthes (parte holandesa)
Site-Not-Synced-To-Lift=Local não foi sincronizado no LIFT; contatar o adm
SiteDetailViewModel.AnimalInputsSite=Inputs animal - Local
SiteDetailViewModel.DairyEnteligenReport=Relatório Dairy Enteligen
SiteDetailViewModel.Detailed=Detalhado
SiteDetailViewModel.DietInputsSiteLactating=Inputs dieta, local (animais em lactação)
SiteDetailViewModel.DownloadingVisit=Baixando visita...
SiteDetailViewModel.GeneralCustomerSiteSetup=Configurações gerais do cliente - local
SiteDetailViewModel.GetReportMsg=Baixando relatório...
SiteDetailViewModel.MainHeading=Visitas
SiteDetailViewModel.NetworkErrorMessage=Não há rede disponível
SiteDetailViewModel.NetworkErrorMessageTitle=Erro de rede
SiteDetailViewModel.NewVisit=Iniciar uma nova visita
SiteDetailViewModel.ReportDownloadTimeout=Não foi possível fazer o download do arquivo, tente novamente quando tiver uma melhor conectividade
SiteDetailViewModel.ReportNotAvailable=Relatório não está disponível para download
SiteDetailViewModel.ReportNotAvailableTitle=Status
SiteDetailViewModel.Reports=RELATÓRIO DAIRY ENTELIGEN
SiteDetailViewModel.Resources=Recursos 
SiteDetailViewModel.SiteSetup=Configuração do local 
SiteDetailViewModel.Summary=Resumo
SiteDetailViewModel.Title=Detalhes do local
SiteDetailViewModel.VisitDownloadPrompt=Visita não baixada, gostaria de fazer o download da visita?
SiteDetailViewModel.VisitDownloadPrompt1=Você gostaria de recuperar essa vsista?
SiteDetailViewModel.VisitNotDownloaded=Visita não baixada 
SiteDetailViewModel.VisitUnavailable=Dados da visita estão indisponíveis
SiteDetailsResourcesViewModel.Title=Recursos 
SiteDetailsSetupViewModel.AnimalInputsSite=Local - Inputs animais
SiteDetailsSetupViewModel.AsFedIntake=Consumo em matéria natural ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=Contagem de células bacterianas (1,000 cfu/mL)
SiteDetailsSetupViewModel.Continue=Continuar 
SiteDetailsSetupViewModel.CurrentMilkPrice=Preço do leite ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Dias em Lactação (DEL)
SiteDetailsSetupViewModel.Delete=Deletar
SiteDetailsSetupViewModel.DietInputsSiteLactating=Inputs da dieta, local (animais em lactação)
SiteDetailsSetupViewModel.DietSetup=Configurações das dietas
SiteDetailsSetupViewModel.Diets=Dietas
SiteDetailsSetupViewModel.DryMatterIntake=Ingestão de matéria seca ({0})
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Configurações do cliente - local
SiteDetailsSetupViewModel.InfoSiteSetup=Adicione um novo local para cada fazenda (empresa) de propriedade de um cliente. Deve haver pelo menos um local criado para poder visitar e acessar as ferramentas da Dairy Enteligen. Ao criar um novo local, os únicos elementos necessários são o nome do local, o preço do leite atual, o sistema de ordenha e os lotes (clique em Configuração do lote). Lotes são necessários para usar ferramentas em nível de grupo e relatar o passo a passo. Adicione ou atualize dados específicos do local nesta página. Você também pode adicionar ou atualizar dados nas ferramentas durante o uso. Os dados de configuração do local serão atualizados automaticamente para locais com downloads de dados da empresa em Dairy Enteligen
SiteDetailsSetupViewModel.LactatingAnimals=Animais em Lactação
SiteDetailsSetupViewModel.MilkFatPercent=Gordura do leite %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Sólidos do leite %
SiteDetailsSetupViewModel.MilkProteinPercent=Proteína do leite %
SiteDetailsSetupViewModel.MilkYield=Produção de leite ({0})
SiteDetailsSetupViewModel.MilkingSystem=Sistema de ordenha
SiteDetailsSetupViewModel.NameNotUnique=Um local com nome de "{0}" já existe. Nomes precisam ser únicos
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=Energia líquida de lactação ELL (Mcal/{0})
SiteDetailsSetupViewModel.NewSite=Novo Local
SiteDetailsSetupViewModel.NullSiteName=Nome do local, preço do leite, sistema de ordenha e lote são campos obrigatórios. Gostaria de continuar ou deletar esse local?
SiteDetailsSetupViewModel.NumberOfStalls=Total de lugares na ordenha
SiteDetailsSetupViewModel.PenSetup=Configurações do lote
SiteDetailsSetupViewModel.Pens=Lotes
SiteDetailsSetupViewModel.RationCost=Custo da ração, por animal (R$)
SiteDetailsSetupViewModel.SiteMandatoryFields=Nome do local, preço do leite, sistema de ordenha e lote são campos obrigatórios. Preencha todos os campos obrigatórios para continuar.
SiteDetailsSetupViewModel.SiteName=Nome do Local
SiteDetailsSetupViewModel.SiteSetup=Configurações do local
SiteDetailsSetupViewModel.SomaticCellCount=Contagem de célula somáticas (1,000 cels/mL)
SiteDetailsSetupViewModel.Title=Detalhes do local
SiteDetailsSetupViewModel.WeightImperialCWT=CWT (45 kg)
SiteVisitSummary=Resumo da visita 
SiteVisitSummaryReport=Relatório do resumo da visita
SixToEightLayers=6 a 8 camadas
SixToTwelveHours=6 a 12 horas
SixToTwelveInches=6 a 12 inches 
Sligo=Sligo
Slovakia=Eslováquia
Slovenia=Eslovênia
Solomon_Islands=Ilhas Salomão
Somalia=Somália
SomanticCellCount=Contagem de células somáticas
SomaticCellPerML=Contagem de células somáticas (cels/mL)
Sondrio=Sondrio
Sonora=Sonora
SouthAfrica=África do Sul
SouthKorea=Coréia do Sul
South_Africa=África do Sul
South_Australia=Sul da Austrália
South_Carolina=Carolina do Sul
South_Dakota=Dakota do Sul
South_Georgia_and_the_South_Sandwich_Islands=Geórgia do Sul e as Ilhas Sandwich South
South_Sudan=Sudão do Sul
Spain=Espanha
Sri_Lanka=Sri Lanka
StandardDeviationScoreTitle=Desvio padrão
StartDate=Data de início 
StatusCompleted=Completo
StatusInProgress=Em progresso
StdDevCalculated=Desvio padrão (calculado)
Steer=Boi
StorageCalculators=Calculadora de armazenamento
StrategyToReduceDisplacedAbomasum=Displasia de abomaso
StrategyToReduceDisplacedAbomasumDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Estratégia de redução de incidência de deslocamento de abomaso&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;A estratégia de prevenção se baseia em gerencimaneto dos fatores de risco, pois atualmente não temos uma causa direta clara.&lt;/p&gt;&lt;p&gt;A prevenção inclui apropriada nutrição e gerenciamento de outras doenças concomitantes\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Controle de fatores de risco nutricional\:&lt;ul&gt;&lt;li&gt;Evitar vacas acima do escore de condição recomendado (ideal 3.25 ECC quando secar e ao parto).&lt;/li&gt;&lt;li&gt;Providenciar FDN de forragem suficiente.&lt;/li&gt;&lt;li&gt;Gerenciar  Manage physical form of the maxDiet.&lt;/li&gt;&lt;li&gt;Pay attention to mineral requirements.&lt;/li&gt;&lt;li&gt;Avoid other metabolic disorders such as hypocalcemia, and also infectious disease that could reduce intake.&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Best management practices\:&lt;ul&gt;&lt;li&gt;Assure feed intake in fresh cows especially during the hours/days after calving&lt;/li&gt;&lt;li&gt;Manage feed bunks properly&lt;/li&gt;&lt;li&gt;Increase cow comfort, reduce any stressors&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceDystocia=Distocia
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Estratégia para redução de incidência de distocia&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Ajuda na prevenção da distocia\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Assegurando que novilhas sejam inseminadas na idade e peso corporal corretos.&lt;/li&gt;&lt;li&gt;Selecionar touros com base em facilidade de parto.&lt;/li&gt;&lt;li&gt;Melhoria do treinamento do pessoal em relação ao tempo adequado e métodos de intervenção no parto, métodos apropriados para cuidar de bezerros recém-nascidos comprometidos&lt;/li&gt;&lt;li&gt;Revisar o programa de ração atual utilizando o MAX&lt;sup&gt;TM&lt;/sup&gt; Requerimento para vacas e novilhas no período seco e pré parto focando em\:&lt;ul&gt;&lt;li&gt;Energia para manter o escore de condição corporal e o crescimento fetal&lt;/li&gt;&lt;li&gt;Prevenção de animais acima do escore desejado no período de parição&lt;/li&gt;&lt;li&gt;Controle do risco de hipocalcemia no rebanho&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=Estratégias para reduzir incidênica
StrategyToReduceKetosis=Cetose
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Estratégia para reduzir incidência de cetose&lt;/strong&gt;&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;Assegurar adequado conforto aos animais (cama, controle de estresse calórico e ventilação, evitar estresse e superlotação, etc.)&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="2"&gt;&lt;li&gt;Balancear dietas de acordo com MAX&lt;sup&gt;TM&lt;/sup&gt; Requerimento de nutrientes e características físicas da dieta. Consultar os produtos de Animais em transição i+C1724nventory para produtos específicos desenvolvidos para prevenção de cetose. Assegurar o uso de boa qualidade de forragem que irão aumentar a ingestão e melhorar a capacidade de tamponamento ruminal.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="3"&gt;&lt;li&gt;Monitoramento de alterações no ECC entre o período seco e a parição\: Secar animais com ECC 3.25 e manter o ECC durante o período seco evitando escessiva mobilização de gordura próximo a parição.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="4"&gt;&lt;li&gt;Implementar um protocolo de saúde específico desenvolvido para animais de pós parto para detectar e prever desordens metabólicas e doenças infecciosas. Monitramento de consumo, preenchimento ruminal e ruminação.&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=Mastite
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Estratégia para redução de incidência de mastite&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Microorganismos que mais frequentemente causa mastite podem ser dividos em duas grandes categorias\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Patógenos contagiosos, que passa de vaca pra vaca principalmente durante o processo de ordenha &lt;em&gt;(i.e. Strep. agalactiae&amp;nbsp;and&amp;nbsp;Staph. Aureus)&lt;/em&gt;&lt;/li&gt;&lt;li&gt;Patógenos ambientais, que vem do ambiente dos animais em lactação&lt;em&gt;(i.e. E. coli&amp;nbsp;and&amp;nbsp;Strep. Uberis)&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Dependendo da categoria da bactéria que causa a mastite, o foco da intervenção precisa ser adaptado.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Controle de mastite contagiosa&lt;/strong&gt;\: não existe mágica para a prevenção de infecções de patógenos, porém ao seguir os passos seguinte pode ajudar\:&lt;ul&gt;&lt;li&gt;Focar em higiene no decorrer da ordenha, incluindo imersão do teto&lt;/li&gt;&lt;li&gt;Pelo menos animais infectados&lt;/li&gt;&lt;li&gt;Realizar manutenção regular dos maquinários de ordenha&lt;/li&gt;&lt;li&gt;Revisão o plano nutricional para vacas secas utilizando o MAX&lt;sup&gt;TM&lt;/sup&gt; assegurar o apropriado aporte de nutrientes (energia, anti oxidantes) para suportar o sustema imune&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Controlar mastite ambiental&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;Assegurar a limpeza das camas, correta ventilação nas instalações&lt;/li&gt;&lt;li&gt;Correta densidade animal&lt;/li&gt;&lt;li&gt;Higiene geral do úbere&lt;/li&gt;&lt;li&gt;Rotina do barracão&lt;/li&gt;&lt;li&gt;Revisar o plano de nutrição dos animais secos usando MAX&lt;sup&gt;TM&lt;/sup&gt; assegurar o apropriado aporte de nutrientes (energia, anti oxidantes) para suportar a função do sistema imune&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=Metrite
StrategyToReduceMetritisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Estratégias para redução de incidência de metrite&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Fatores de risco para metrite inclui retenção de placenta, problemas no trato reprodutivo em decorrência de dificuldades no parto, protocolo de parto impróprio, área de parto sem adequada limpeza sanitária, deficiências nutricionais como vitamina E ou selênio, e vacas com escore acima do esperado.&lt;/p&gt;&lt;p&gt;Monitoramento das áreas a seguir, em ordem de importância decrescente&amp;nbsp;\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Praticas de parição&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Os funcionários estão trazendo carga bacteriana para o útero quando auxiliam no parto?&lt;/li&gt;&lt;li&gt;Os animais estão sendo auxiliados muito antes ou muito depois do necessário?&lt;/li&gt;&lt;li&gt;Os bezerros são puxados com frequência?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Práticas de tratamento&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Como as vacas com RP ou metrite são tratadas?&lt;/li&gt;&lt;li&gt;Alguma chance de bactérias de outros ambientes ou vagina entrando no útero no momento do parto?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="3"&gt;&lt;li&gt;Animal estresse&lt;br /&gt;&lt;ul style\="list-style-type\: circle;"&gt;&lt;li&gt;Excesso de estresse antes da parição pode estar deficultando o sistema imune da vaca, diminuindo a resistência para qauqluer infecção que surja após o nascimento.&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="4"&gt;&lt;li&gt;Nutrição&lt;ul&gt;&lt;li&gt;Revisar dieta para vacas secas, focando no controle do ECC, balanço mineral e aporte de nutrientes antioxidantes (vit E, A, selênio, zinco e cobre)&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Check de outras doenças (cetose, LDA)&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMilkFever=Febre do leite
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Estratégia para redução da incidência de febre do leite&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Duas estratégias alternativas para prevenção de hipocalcemia em vacas de leite se baseia em 100% da gestão da dieta.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Use dietas para vacas secas com baixo Ca&lt;/li&gt;&lt;li&gt;Use dieta para vacas secas com baixo DCAD&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Área para considerar manter a hipocalcemia sob controle são\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Testar forragens nos minerais (cálcio, fósforo, magnésio, potássio, sódio, enxofre e cloreto) em um laboratório validado&lt;/li&gt;&lt;li&gt;Revisão o programa atual de ração seguindo MAX&lt;sup&gt;TM&lt;/sup&gt; requerimentos para vacas em pré parto e práticas de alimentação (atenção especial para forragens ad libitum, concentração de K nas forragens, minerais de livre escolha para vacas secas, elas selecionando sua dieta&lt;/li&gt;&lt;li&gt;Consultar o inventário de produtos de vacas em trasição para formulações desenvolvidas especificamente para prevenir hipocalcemia&lt;/li&gt;&lt;li&gt;Quando usar dieta DCAD\:&lt;ul&gt;&lt;li&gt;Monitorar com cuidade a ingestão de sais aniônicos pois possuem baixa palatabilidade e podem reduzir ingestão de materia seca&lt;/li&gt;&lt;li&gt;Monitorar pH urinário para checar eficiência das alterações da dieta&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=Retenção de placenta
StrategyToReduceRetainedPlacentaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;span&gt;&lt;strong&gt;Estratégia para redução de incidência de retenção de placenta&lt;/strong&gt;&lt;/span&gt;&lt;/h4&gt;&lt;p&gt;A incidência de retenção de placenta é positivamente relacionada com imunosupressão, alta de hormônios de estresse, hipocalcemia (incluindo subclínica), assistência em parto e abortos, agentes de infecção endêmicos, e genética.&lt;/p&gt;&lt;p&gt;Monitorar as áreas a seguir, em ordem de importância decrescente\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Nutrição &lt;br/&gt; &lt;p style\="padding-left\: 30px;"&gt;Revisar as dietas de pré parto de acordo com MAX&lt;sup&gt;TM&lt;/sup&gt; orientar-se com foco especial em\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Se e vitamina E&lt;/li&gt;&lt;li&gt;Níveis de minerais (K, Ca, Mg) para reduzir o risco de hipocalcemia clínica e subclínica&lt;/li&gt;&lt;li&gt;Usar um baixo ou negativo DCAD para reduzir a incidência de hipocalcemia&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Gerenciar&lt;/li&gt;&lt;ul&gt;&lt;li&gt;Controlar ECC (3.25 ao nascimento) durante o final da lactação e no período seco&lt;/li&gt;&lt;li&gt;Minimizar fatores de estresse nas áreas durante o período seco e pós parto, reduzir a mobilização de gordura e imunossupressão ao redor do período do parto.&lt;/li&gt;&lt;li&gt;Melhorar o conforto das vacas e providenciar espaço de cama suficiente&lt;/li&gt;&lt;li&gt;Seguir o procedimento de parto determinado pelo veterinário&lt;/li&gt;&lt;li&gt;Não auxiliar vacas no parto sem necessidade&lt;/li&gt;&lt;li&gt;Manter a limpeza do ambiente para reduzir as chances de infecções uterinas.&lt;/li&gt;&lt;/ul&gt;&lt;/ol&gt;&lt;/span&gt;
Straw=Palha
Sudan=Sudão
Surinam=Suriname
Suriname=Suriname
SurveyCategories=Pesquisa de categorias
SurveyOfForages=Pesquisa de forragens
SurveyOfForages_AnnualCowNumAndForageNeeds=Anualmente é projetado o número de vacas e as necessidades de forragens
SurveyOfForages_AshLevelsInCornSilage=Qual o nível de cinzas em selagem de milho
SurveyOfForages_AshLevelsInHaylage=Qual o nível de cinzas no pré secado
SurveyOfForages_ButyricAcidLevelsInHaylage=Qual o nível de ácido butírico no pré secado?
SurveyOfForages_CornSilageProcessingScore=Escore de processamento da silagem de milho?
SurveyOfForages_CornSilageScoreMonitored=O escore de processamento de silagem de milho Kernel é monitorado usando o KP teste do laboratório Cargill?
SurveyOfForages_InspectedForSpoilageAndMold=Todas as forragens são inspecionadas quanto a deteriorização e mofo, e descartadas?
SurveyOfForages_InventoryIsMonitored=O inventário é monitorado?
SurveyOfForages_LacticAcidToAceticAcidLevels=Níveis de ácido lático para ácido acético?
SurveyOfForages_LooseOrFacedFeedWithin=Silo perdido ou boca de silo é utlizado em alimentação dentro de\:
SurveyOfForages_NoLooseFeedRemaining=Não há alimento perdido após a alimentação ser feita?
SurveyOfForages_SilosSizedForCapacity=Silos são dimensionados de acordo com a necessidade do rebanho? Sem necessidade de sobrecarga?
SurveyOfForages_VisibleSignsOfSoil=A silagem é livre visivelmente de qualquer contaminação do solo?
Svalbard_and_Jan_Mayen=Svalbard e Jan Mayen
Swaziland=Suazilândia
Sweden=Suécia
Switzerland=Suíça
Sync-failed-due-to-unknown-reason=알 수없는 이유로 인해 동기화가 실패했습니다. 해결을 위해 관리자에게 문의하십시오.
SyncFailed=A sincronização não pode ser completada nesse momento, tente novamente mais tarde
Sync_Data=Data de sincronização
Syracuse=Siracusa
Syrian_Arab_Republic=República Árabe da Síria
SystemGenerated=Gerado pelo sistema
São_Paulo=São Paulo
THB=Tailândia (THB THB)
TMR=TMR
TMRHerdAnalysisTableTitle=Análise do escore TMR do rebanho
TMRParticleScore=Análise de TMR
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Fechar
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Dias em Lactação (DEL)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Editar quantidade de DEL
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=DEL
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Editar
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Meio 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Meio 2
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Análise escore TMR
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Peneira superior
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Peneira inferior
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Análise do Rebanho
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Inputs
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Resultados
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Saúde ruminal - escore de partículas
TMRParticleScoreHerdAnalysisResultsText=Análise escore TMR
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Meio 1 (8 mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Meio 2 (4 mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Paneira superior (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Peneira inferior 
TRY=TRY
TWD=Taiwan (NT$ TWD)
Tabasco=Tabasco
Taipei_City=Cidade de Taipei
Taiwan=Taiwan
Tajikistan=Tajiquistão
Tamaulipas=Tamaulipas
Tamil_Nadu=Tamil Nadu
Tanzania,_United_Republic_of=Tanzânia, República Unida de
Taranto=Taranto
Task=Tarefa
Tasmania=Tasmânia
TemperatureImperial=ºF
TemperatureMetric=°C
Tennessee=Tennessee
Teramo=Teramo
Terni=Terni
Texas=Texas
TextureFeed=Textura da alimentação
Thailand=Tailândia
Thirdparty=Terceiro
ThisVisit=(essa visita)
ThreePotentialStorageSolutions=Três possíveis soluções para armazenamento
ThreeScreen=3 peneira
ThreeTimesPerWeek=3 vezes por semana 
Tianjin=Tianjin
Tiestall=Tiestall
TimeRemaining=Tempo restante para descanso 
Timor-Leste=Timoria leitura
Tipperary=Tipperary
Tlaxcala=Tlaxcala
Tocantins=Tocantins
Togo=Ir
Tokelau=Tokelau
Tokyo=Tóquio
Tonga=Chegado
TonsAF=TONS MN
TonsDM=TONS MS
TonsPerDay=Tons por dia 
ToolNotSelected=Você deve selecionar pelo menos uma ferramenta
Top=Peneira superior
TopUnloadingSilo=Dimensão do silo de descarregamento superior
TopValue=(19mm)
Total=Total
Total\ Production\ (cow/day)=Produção (kg/vaca/dia)
TotalAnimals=Total de animais no lote
TotalAnimalsHerd=Total de animais 
TotalPenPerScore=Número total de animais no lote para avaliação 
TotalRevenue=Receita total
TowerSilos=Silos verticais
TowerSilos_FaceRemovalRateGreaterThan4Inches=A face do silo é removida mais que 10cm/dia?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=O silo é coberto após o enchimento se não for utilizado no prazo de 14 dias?
TowerSilos_SiloFillingTimeof3DaysOrLess=Enchimento do silo é feito dentro de 3 dias?
TransitionCow=VACAS DE TRANSIÇÃO
Trapani=Trapani
Tray=Peneira inferior
Trends=Tendências positivas
Trento=Trento
Treviso=Treviso
Trieste=Trieste
Trinidad_and_Tobago=Trinidad e Tobago
Tripura=Tripura
Tunisia=Tunísia
Turin=Turim
Turkey=Peru
Turkmenistan=Turquemenistão
Turks_and_Caicos_Islands=Ilhas Turcas e Caicos
Tuvalu=Tuvalu
TwelveInchesOrGreater=30 cm ou mais
TwentyFourHoursAndNoSync=Vinte e quatro horas e sem sincronia
TwentyFourHoursBeforeActionIsDue=Vinte e quatro horas antes do vencimento da ação
TwentyFourToThirtySixInchesPerDay=60 a 90 cm por dia
TwicePerWeek=Duas vezes por semana
UAH=Ucrânia (UAH UAH)
UK=Reino unido
UNITED_STATES=Estados Unidos
US=Estados Unidos da América
USD=Estados Unidos da América ($ USD)
Udine=Udine
Uganda=Uganda
Ukraine=Ucrânia
UnitedKingdom=Reino unido
UnitedStates=Estados Unidos da América
United_Arab_Emirates=Emirados Árabes Unidos
United_Kingdom=Reino Unido
United_States=Estados Unidos
UrinePH=pH urinário
UrinePHAVG=pH urinário médio 
UrinePHAverageNumber=Número médio
UrinePHDensity=Recursos pH urinário 
UrinePHEditCowViewModel.AddNew=Próxima vaca 
UrinePHEditCowViewModel.CowName=Nome da vaca
UrinePHEditCowViewModel.CowValue=Dado da vaca 
UrinePHEditCowViewModel.UrinePHEnterCowValue=Inserir dado da vaca
UrinePHEditCowViewModel.ValidCudInput=Inserir um input válido 
UrinePHEditGoalViewModel.GoalMax=Objetivo - máx
UrinePHEditGoalViewModel.GoalMin=Objetivo - mín
UrinePHEditGoalViewModel.TargetUrinePHRange=Objetivo - range pH urinário
UrinePHEditGoalViewModel.Title=Editar objetivos 
UrinePHInputsViewModel.AddNew=Adicionar novo 
UrinePHInputsViewModel.CalculatorHeading=Selecione um animal para inserir o pH urinário. Cliente "adicionar novo" para adicionar animais a lista. 
UrinePHInputsViewModel.CoefficientVariation=Coeficiente de variação (C.V.) (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Vacas fora do range (%)
UrinePHInputsViewModel.CudChewCategorySection=Vacas
UrinePHInputsViewModel.DietDCAD=Dieta DCAD, mEq/100g
UrinePHInputsViewModel.Resources=Recursos 
UrinePHInputsViewModel.TargetUrinePHRange=Objetivo - range pH urinário
UrinePHInputsViewModel.UrinePHAVG=Média pH urinário (calculado)
UrinePHInputsViewModel.UrinePhSTDDEV=Desvio padrão (calculado)
UrinePHMasterViewModel.Inputs=Inputs
UrinePHMasterViewModel.Results=Resultados 
UrinePHMasterViewModel.Title=pH urinário
UrinePHPenSelectionViewModel.Title=pH urinário
UrinePHPenSelectionViewModel.UrinePHPenList=Lotes (lactação e vaca seca somente)
UrinePHResultsViewModel.DietDCAD=Dieta DCAD, mEq/100g
UrinePHResultsViewModel.MaxpH=pH máx
UrinePHResultsViewModel.MinpH=pH mín
UrinePHResultsViewModel.UrinePHAVG=pH urinário médio 
Uruguay=Uruguai
UserCreated=Usuário criado
UserPreferencesViewModel.Branding=Marca Go To Market
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=Selecionar uma moeda
UserPreferencesViewModel.Imperial=Imperial
UserPreferencesViewModel.MainHeading=Essas opções podem ser modificadas posteriormente nas configurações do aplicativo.
UserPreferencesViewModel.Metric=Métrico 
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Selecionar uma moeda
UserPreferencesViewModel.SelectPointScale=Selecionar escala 
UserPreferencesViewModel.Title=Configuração do usuário
UserPreferencesViewModel.UnitOfMeasure=Selecionar unidade de medida
UserPreferencesViewModel.UserPreferencesMilkProcessor=Configuração do processador de leite
UserPreferencesViewModel.UserPreferencesMoreSettings=Mais configurações
User_Settings=Configuração do usuário
Utah=Utah
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
Uzbekistan=Uzbequistão
VEF=Venezuela (Bs VEF)
VND=Vietnã (₫ VND)
Vanuatu=Vanuatu
Varese=Varese
Venezuela=Venezuela
Venezuela,_Bolivarian_Republic_of=Venezuela, República Bolivariana de
Venice=Veneza
Veracruz=Veracruz
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Vermont=Vermont
Verona=Verona
Vestland=Westland
Vibo_Valentia=Vibo Valentia
Vicenza=Vicenza
Victoria=Victoria
Viet_Nam=Vietnã
Vietnam=Vietnã
ViewOverallCalfHaiferScore=Ver pontuação geral para Bezerras e Novilhas
ViewOverallForageScore=Ver pontuação geral das forragens
Virgin_Islands,_British=Ilhas Virgens, britânico
Virginia=Virgínia
Visit.Report.Footer.Patent=A Cargill Incorporated, suas controladoras e afiliadas não garantem a precisão dessas estimativas, devido a vários fatores. Não há garantia de produção ou resultados financeiros. ©2023 Cargill, Incorporated. Todos os direitos reservados.
VisitAutoPublished=Visite Auto Publicado
VisitDate=Data da visita
VisitDownloadProceed=Prosseguir
VisitNotebook=Anotações da visita 
VisitNotesViewModel.Action=Ação
VisitNotesViewModel.Close=Fechar
VisitNotesViewModel.DownloadingNotes=Baixando anotações...
VisitNotesViewModel.Event=Evento
VisitNotesViewModel.New=Novo 
VisitNotesViewModel.NoteMetadata={0} @ {1} por {2}
VisitNotesViewModel.Observation=Observação 
VisitNotesViewModel.Task=Tarefa
VisitNotesViewModel.Title=Anotações da visita
VisitNotesViewModel.VisitNotebook=Anotações da visita
VisitSummaryViewModel.CalfHeiferItem=Bezerras e Novilhas
VisitSummaryViewModel.CalfHeiferScorecard=Escore Card
VisitSummaryViewModel.CategorySection=Categorias de Ferramentas
VisitSummaryViewModel.ComfortHeatStressBanner=Ferramenta Estresse calórico - lotes
VisitSummaryViewModel.ComfortItem=Conforto 
VisitSummaryViewModel.EmailReport=Relatório por Email
VisitSummaryViewModel.FreeFormReport=Relatório formato livre
VisitSummaryViewModel.HealthItem=Saúde 
VisitSummaryViewModel.HeatstressEvaluationTitle=Avaliação Estresse Térmico
VisitSummaryViewModel.HerdAnalysis=Análise do Rebanho
VisitSummaryViewModel.InputsOutputsChart=Inputs / Resultados / Gráficos
VisitSummaryViewModel.MilkProcessRevenueCalculator=Calculadora de receita do leite
VisitSummaryViewModel.NoToolPrompt=Nenhuma ferramenta foi completada
VisitSummaryViewModel.NutritionForage=Auditoria de Forragem
VisitSummaryViewModel.NutritionItem=Nutrição 
VisitSummaryViewModel.NutritionPile=Silo de superfície e trincheira
VisitSummaryViewModel.PenTimeTitle=Ferramenta de cálculo de tempo de descanso
VisitSummaryViewModel.ProductivityItem=Produtividade 
VisitSummaryViewModel.RumenHealthBodyConditionTitle=Escore de Condição Corporal
VisitSummaryViewModel.RumenHealthLocomotionTitle=Escore de Locomoção
VisitSummaryViewModel.RumenHealthManureTitle=Escore de fezes - Saúde ruminal
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Incidência Metabólica
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
VisitSummaryViewModel.RumenHealthTMRTitle=Ecore TMR - saúde ruminal 
VisitSummaryViewModel.RumenHealthTitle=Ruminação - Saúde ruminal
VisitSummaryViewModel.RumenHealthUrinePHTitle=pH urinário
VisitSummaryViewModel.Title=Resumo da visita 
VisitSummaryViewModel.VisitSummaryMilkCalc=Inputs / Resultados / Gráficos
VisitSummaryViewModel.VisitTitle=Nome da visita 
VisitViewModel.CalfHeiferItem=Bezerras e Novilhas
VisitViewModel.CategorySection=Categorias de Ferramentas
VisitViewModel.ComfortItem=Conforto
VisitViewModel.Delete=Deletar visita
VisitViewModel.DeletePrompt=Tem certeza que deseja deletar essa visita? Esse processo não poderá ser desfeito
VisitViewModel.HealthItem=Saúde
VisitViewModel.Instructions=Selecionar uma categoria ou relatório da lista abaixo
VisitViewModel.NullVisitName=Nome da visita não pode ficar em branco, preencha o nome da visita
VisitViewModel.NutritionItem=Nutrição 
VisitViewModel.ProductivityItem=Produtividade 
VisitViewModel.Publish=Publicar
VisitViewModel.PublishNotes=Publicar anotações
VisitViewModel.PublishNotesPrompt=Você tem certeza que deseja publicar as anotações da visita? O processo não poderá ser desfeito.
VisitViewModel.PublishPrompt=Você tem certeza que deseja publicar essa visita? O processo não poderá ser desfeito.
VisitViewModel.PublishVisit=Publicar visita
VisitViewModel.SiteVisitSummary=Resumo da visita 
VisitViewModel.Title=Detalhes da visita
VisitViewModel.ToolCategories=Categorias de Ferramentas
VisitViewModel.VisitNotebook=Anotações da visita
VisitViewModel.VisitTitle=Nome da visita
VisitViewModel.WalkthroughReport=Relatório da visita
Viterbo=Viterbo
VolumeImperial=gal
VolumeMetric=ml
WalkthroughPenSelectionViewModel.Pens=lotes
WalkthroughPenSelectionViewModel.Title=Relatório da visita
WalkthroughReport=Relatório da visita
WalkthroughReportHerdAnalysisViewModel.Appearance=Aspecto
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Limpeza das camas
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Cama\: profundidade e maciez
WalkthroughReportHerdAnalysisViewModel.Branding=Marca Go To Market
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Conforto das vacas, % animais deitados
WalkthroughReportHerdAnalysisViewModel.Comments=Comentários
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Contagem de ruminações, contagem de mastigações por bolo alimentar 
WalkthroughReportHerdAnalysisViewModel.CudChewing=Ruminação, % mastigação
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} Relatório
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} Relatório 
WalkthroughReportHerdAnalysisViewModel.ExportSelected=Enviar email com as ferramentas selecionadas
WalkthroughReportHerdAnalysisViewModel.FinalObservations=Observações finais 
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Gerando relatório...
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=% de Animais com abrasão de Cascos
WalkthroughReportHerdAnalysisViewModel.MainHeading=Relatório da visita
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Descarga nasal, % dos animais
WalkthroughReportHerdAnalysisViewModel.Notes=Anotações
WalkthroughReportHerdAnalysisViewModel.Opportunities=Oportunidades 
WalkthroughReportHerdAnalysisViewModel.PensForExport=Lotes para exportar
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Preenchimento ruminal
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=Escore de Condição Corporal (ECC)
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Escore locomoção
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Escore de Fezes
WalkthroughReportHerdAnalysisViewModel.SubHeading=Análise do Rebanho
WalkthroughReportHerdAnalysisViewModel.Title=Análise do Rebanho - Anotações da Visita
WalkthroughReportHerdAnalysisViewModel.Trends=Tendências positivas 
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Descarga uterina, % dos animais
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Qualidade da água
WalkthroughReportLandingViewModel.HerdAnalysis=Análise do Rebanho
WalkthroughReportLandingViewModel.PenAnalysis=Análise de lote 
WalkthroughReportLandingViewModel.Title=Relatório da visita 
WalkthroughReportQualityViewModel.BeddingCleanliness=Selecionar limpeza das camas
WalkthroughReportQualityViewModel.Clean=Limpeza
WalkthroughReportQualityViewModel.Dirty=Sujo
WalkthroughReportQualityViewModel.ModeratelyClean=Moderadamente limpo
WalkthroughReportQualityViewModel.Title=Relatório da visita
WalkthroughReportQualityViewModel.WaterQuality=Selecionar qualidade da água 
WalkthroughReportViewModel.Appearance=Aparência
WalkthroughReportViewModel.BeddingCleanliness=Limpeza das camas
WalkthroughReportViewModel.BeddingDepthSoft=Cama\: profundidade e maciez
WalkthroughReportViewModel.Clean=Limpeza
WalkthroughReportViewModel.ComfortItem=Conforto das vacas, % animais deitados
WalkthroughReportViewModel.Comments=Comentários
WalkthroughReportViewModel.CudChewCategorySection=Contagem de ruminações, contagem de mastigações por bolo alimentar 
WalkthroughReportViewModel.CudChewing=Ruminações, % mastigação
WalkthroughReportViewModel.Current=Atual
WalkthroughReportViewModel.Dirty=Sujeira 
WalkthroughReportViewModel.Goals=Objetivo
WalkthroughReportViewModel.HockAbrasion=% de Animais com abrasão de Cascos
WalkthroughReportViewModel.ModeratelyClean=Moderadamente limpo
WalkthroughReportViewModel.NasalDischarge=Descarga nasal, % dos animais
WalkthroughReportViewModel.Opportunities=Oportunidades 
WalkthroughReportViewModel.Previous=Anterior 
WalkthroughReportViewModel.RumenFill=Preenchimento ruminal
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=Escore de condição corporal (ECC)
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Escore de Locomoção
WalkthroughReportViewModel.RumenHealthManureTitle=Escore de Fezes 
WalkthroughReportViewModel.Title=Relatório da visita
WalkthroughReportViewModel.Trends=Tendências positivas
WalkthroughReportViewModel.UterineDischarge=Descarga uterina, % dos animais
WalkthroughReportViewModel.WaterQuality=Qualidade da água
Wallis_and_Futuna=Wallis e Futuna
Washington=Washington
Waterford=Waterford
Weekly=Weekly
WeightDMInLengthImperial=Lbs. MS 1 em 1 foot
WeightDMInLengthMetric=Kgs. MS em 1 metro
WeightImperial=Lbs
WeightImperialCWT=CWT (45 kg)
WeightMetric=Kg
West_Bengal=Bengala Ocidental
West_Virginia=West Virginia
Western_Australia=Austrália Ocidental
Western_Sahara=Saara Ocidental
Westmeath=Westmeath
Wexford=Wexford
Wicklow=Wicklow
Wisconsin=Wisconsin
WithinEightHours=Dentro de 8 horas
Wyoming=Wyoming
Xinjiang=Xinjiang
Xizang=Xizang
Yemen=Iémen
Yes=Sim
Yucatán=Yucatăn
Yukon_Territories=Territórios de Yukon
Yunnan=Yunnan
ZAR=África do Sul (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=Zâmbia
Zhejiang=Zhejiang
Zimbabwe=Zimbábue
welcome.message=Greetings {0}
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry Corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole
Rof.Kg.Per.Fat=Return Over Feed Kg Per Fat



