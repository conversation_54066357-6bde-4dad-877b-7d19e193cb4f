/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint;

import static org.mockito.Mockito.*;

import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.sf.cc.service.*;
import com.app.cargill.sharepoint.service.SharePointWebClientFactory;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClient;

@RunWith(MockitoJUnitRunner.class)
public class SharepointServiceCallsTest {

  @InjectMocks private SharepointServiceCalls underTest;

  @Mock private SharepointProperties sharepointProperties;

  @Mock private WebClient.Builder webClientBuilder;
  @Mock private SharePointWebClientFactory webClientFactory;
  @Captor private ArgumentCaptor<String> urlCapture;

  @Captor private ArgumentCaptor<HttpEntity<String>> entityCapture;

  @Captor private ArgumentCaptor<Class<?>> responseTypeCapture;

  @Mock private Oauth2Dto ccAuthBean;
  private static MockWebServer mockBackEnd;

  @Before
  public void setUp() throws Exception {
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();
    when(sharepointProperties.getInstanceUrl())
        .thenReturn("https://localhost:" + mockBackEnd.getPort() + "/sites/dairyenteligen");
    when(sharepointProperties.getInstanceUrl())
        .thenReturn("http://localhost:" + mockBackEnd.getPort() + "/sites/dairyenteligen");
    when(sharepointProperties.getCheckFolderExistsUrl())
        .thenReturn(
            "/sites/dairyenteligen/_api/web/GetFolderByServerRelativeUrl({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})/Exists");
    when(sharepointProperties.getCreateFolderUrl())
        .thenReturn(
            "/sites/dairyenteligen/_api/Web/Folders/add({0}/sites/dairyenteligen/Shared%20Documents/{1}{2})");

    underTest =
        new SharepointServiceCalls(
            new SharePointWebClientFactory(WebClient.builder()), sharepointProperties);

    webClientFactory = new SharePointWebClientFactory(webClientBuilder);
  }

  @After
  public void tearDown() throws IOException {
    mockBackEnd.shutdown();
  }

  @Test
  public void postFileToSharePoint_Exception_Test() {
    mockBackEnd.enqueue(
        new MockResponse()
            .addHeader("Content-Type", "application/json")
            .setBody("{}")
            .setResponseCode(HttpStatus.OK.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .addHeader("Content-Type", "application/json")
            .setBody("{}")
            .setResponseCode(HttpStatus.CREATED.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .addHeader("Content-Type", "application/json")
            .setBody("{}")
            .setResponseCode(HttpStatus.OK.value()));
    underTest.postFileToSharePoint(
        getAuth(), "bytearray".getBytes(StandardCharsets.UTF_8), "folderName", "fileName");
    underTest.createFolderInSharePoint(getAuth(), "folderName");
    underTest.checkFolderExistsInSharePoint(getAuth(), "folderName");
  }

  @Test
  public void postFileToSharePointThrowExceptions() {
    mockBackEnd.enqueue(
        new MockResponse()
            .addHeader("Content-Type", "application/json")
            .setBody("{}")
            .setResponseCode(HttpStatus.NOT_FOUND.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .addHeader("Content-Type", "application/json")
            .setBody("{}")
            .setResponseCode(HttpStatus.NOT_FOUND.value()));
    mockBackEnd.enqueue(
        new MockResponse()
            .addHeader("Content-Type", "application/json")
            .setBody("{}")
            .setResponseCode(HttpStatus.NOT_FOUND.value()));
    underTest.postFileToSharePoint(null, null, null, null);
    underTest.postFileToSharePoint(
        getAuth(), "bytearray".getBytes(StandardCharsets.UTF_8), "folderName", "fileName");
    underTest.createFolderInSharePoint(getAuth(), "folderName");
    underTest.checkFolderExistsInSharePoint(getAuth(), "folderName");
  }

  private Oauth2Dto getAuth() {
    Oauth2Dto auth = new Oauth2Dto();
    auth.setResource("https://test.sf.com/");
    auth.setAccessToken("XX-YYY");
    return auth;
  }
}
