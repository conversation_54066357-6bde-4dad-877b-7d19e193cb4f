/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.ProfitabilityAnalysisReportDto;
import com.app.cargill.dto.XAndYAxisValueDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.AxisCrossBetween;
import org.apache.poi.xddf.usermodel.chart.AxisCrosses;
import org.apache.poi.xddf.usermodel.chart.AxisPosition;
import org.apache.poi.xddf.usermodel.chart.BarDirection;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.LegendPosition;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFChart;
import org.apache.poi.xddf.usermodel.chart.XDDFChartLegend;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFLineChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("profitabilityAnalysisReportServiceImpl")
@RequiredArgsConstructor
public class ProfitabilityAnalysisReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public String getFileName(Object data) {
    ProfitabilityAnalysisReportDto dto =
        modelMapper.map(data, ProfitabilityAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? dto.getReportType().getFileName()
        : dto.getFileName();
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    ProfitabilityAnalysisReportDto dto =
        modelMapper.map(data, ProfitabilityAnalysisReportDto.class);

    try (XSSFWorkbook workbook = new XSSFWorkbook()) {
      XSSFSheet sheet =
          workbook.createSheet(
              ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              workbook,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(workbook, false, true, IndexedColors.BLACK));

      XSSFCellStyle cellStyle =
          ExcelUtils.applyCellStyle(
              workbook,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(workbook, false, true, IndexedColors.BLACK));

      // Add header before chart
      prepareHeader(workbook, sheet, rowNumber, cellNumber, dto, boldStyle, locale, source);
      rowNumber.getAndIncrement();
      rowNumber.getAndIncrement();
      XSSFRow rowForTitle = sheet.createRow(rowNumber.getAndIncrement());

      cellNumber.set(1);
      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
        ExcelUtils.createAndSetCellValue(
            rowForTitle,
            cellNumber,
            cellStyle,
            ExcelUtils.getLangValue(getTitleForGraph(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()));
      } else {
        ExcelUtils.createAndSetCellValue(
            rowForTitle,
            cellNumber,
            cellStyle,
            ExcelUtils.getLangValue(getTitleForGraph(dto.getReportType()), null, source, locale));
      }

      // Reset cell number after headers
      cellNumber.set(0);

      // Add chart data headers
      XSSFRow rowForTable = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          rowForTable,
          cellNumber,
          cellStyle,
          ExcelUtils.getLangValue(LangKeys.PROFITABILITY_ANALYSIS_DATE, null, source, locale));

      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {
        ExcelUtils.createAndSetCellValue(
            rowForTable,
            cellNumber,
            cellStyle,
            ExcelUtils.getLangValue(
                    getLangKeyForLeftYAxis(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()));
      } else {
        ExcelUtils.createAndSetCellValue(
            rowForTable,
            cellNumber,
            cellStyle,
            ExcelUtils.getLangValue(
                getLangKeyForLeftYAxis(dto.getReportType()), null, source, locale));
      }

      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
        ExcelUtils.createAndSetCellValue(
            rowForTable,
            cellNumber,
            cellStyle,
            ExcelUtils.getLangValue(
                    getLangKeyForRightYAxis(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()));
      } else {
        ExcelUtils.createAndSetCellValue(
            rowForTable,
            cellNumber,
            cellStyle,
            ExcelUtils.getLangValue(
                getLangKeyForRightYAxis(dto.getReportType()), null, source, locale));
      }
      // Populate data
      List<XAndYAxisValueDto> leftYAxis = dto.getLeftYAxis();
      List<XAndYAxisValueDto> rightYAxis = dto.getRightYAxis();

      Integer rowNumberForTable = rowNumber.get();
      for (int i = 0; i < leftYAxis.size(); i++) {
        Row row = sheet.createRow(rowNumber.getAndIncrement());
        row.createCell(0).setCellValue(leftYAxis.get(i).getX()); // Date
        row.createCell(1)
            .setCellValue(
                leftYAxis.get(i).getY() != null ? leftYAxis.get(i).getY() : 0); // Total Production
        row.createCell(2)
            .setCellValue(
                leftYAxis.get(i).getX() != null
                    ? rightYAxis.get(i).getY()
                    : 0); // Total Production / Concentrate Consumed
      }

      // Create drawing and anchor points for the chart below the header and data
      XSSFDrawing drawing = sheet.createDrawingPatriarch();
      XSSFClientAnchor anchor =
          drawing.createAnchor(0, 0, 0, 0, 0, rowNumber.get() + 1, 10, rowNumber.get() + 20);

      // Create chart
      XDDFChart chart = drawing.createChart(anchor);
      XDDFChartLegend legend = chart.getOrAddLegend();
      legend.setPosition(LegendPosition.BOTTOM);

      // Define axis
      XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
      bottomAxis.setTitle(
          ExcelUtils.getLangValue(LangKeys.PROFITABILITY_ANALYSIS_DATE, null, source, locale));

      XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {

        leftAxis.setTitle(
            ExcelUtils.getLangValue(
                    getLangKeyForLeftYAxis(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()));
      } else {
        leftAxis.setTitle(
            ExcelUtils.getLangValue(
                getLangKeyForLeftYAxis(dto.getReportType()), null, source, locale));
      }
      leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);

      XDDFValueAxis rightAxis = chart.createValueAxis(AxisPosition.RIGHT);
      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
        rightAxis.setTitle(
            ExcelUtils.getLangValue(
                    getLangKeyForRightYAxis(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()));
      } else {
        rightAxis.setTitle(
            ExcelUtils.getLangValue(
                getLangKeyForRightYAxis(dto.getReportType()), null, source, locale));
      }
      rightAxis.setCrosses(AxisCrosses.MAX);
      rightAxis.setCrossBetween(AxisCrossBetween.BETWEEN);

      // Data for the series (Bar chart for Total Production)
      XDDFCategoryDataSource dateSource =
          XDDFDataSourcesFactory.fromStringCellRange(
              sheet,
              new CellRangeAddress(rowNumberForTable, rowNumberForTable + leftYAxis.size(), 0, 0));
      XDDFNumericalDataSource<Double> totalProductionSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  rowNumberForTable, rowNumberForTable + leftYAxis.size() - 1, 1, 1));

      // Create bar chart for Total Production
      XDDFBarChartData barChartData =
          (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
      barChartData.setBarDirection(BarDirection.COL);

      XDDFBarChartData.Series barSeries =
          (XDDFBarChartData.Series) barChartData.addSeries(dateSource, totalProductionSource);
      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {
        barSeries.setTitle(
            ExcelUtils.getLangValue(
                    getLangKeyForLeftYAxis(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()),
            null);
      } else {
        barSeries.setTitle(
            ExcelUtils.getLangValue(
                getLangKeyForLeftYAxis(dto.getReportType()), null, source, locale),
            null);
      }

      // Data for the series (Line chart for Total Production / Concentrate Consumed)
      XDDFNumericalDataSource<Double> totalConcentratedSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  rowNumberForTable, rowNumberForTable + rightYAxis.size() - 1, 2, 2));

      XDDFLineChartData lineChartData =
          (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);
      XDDFLineChartData.Series lineSeries =
          (XDDFLineChartData.Series) lineChartData.addSeries(dateSource, totalConcentratedSource);
      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
        lineSeries.setTitle(
            ExcelUtils.getLangValue(
                    getLangKeyForRightYAxis(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()),
            null);
      } else {
        lineSeries.setTitle(
            ExcelUtils.getLangValue(
                getLangKeyForRightYAxis(dto.getReportType()), null, source, locale),
            null);
      }
      lineSeries.setSmooth(false); // Ensure line has points

      if (dto.getReportType()
          .equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
        chart.setTitleText(
            ExcelUtils.getLangValue(getTitleForGraph(dto.getReportType()), null, source, locale)
                .replace("{0}", dto.getCurrency()));
      } else {
        chart.setTitleText(
            ExcelUtils.getLangValue(getTitleForGraph(dto.getReportType()), null, source, locale));
      }
      chart.plot(barChartData);
      chart.plot(lineChartData);

      return ExcelUtils.finalizeWorkbook(workbook, sheet.getRow(0).getLastCellNum());
    }
  }

  public String getTitleForGraph(ReportsToBeanMappings reportType) {
    if (reportType.equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION)) {
      return LangKeys.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION_CHART_TITLE;
    } else if (reportType.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
      return LangKeys.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY_CHART_TITLE;
    } else if (reportType.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM)) {
      return LangKeys.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM_CHART_TITLE;
    } else if (reportType.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {
      return LangKeys.PROFITABILITY_ANALYSIS_MILK_PRICE_CHART_TITLE;
    }
    return null;
  }

  public String getLangKeyForLeftYAxis(ReportsToBeanMappings reportsToBeanMappings) {
    if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION)) {
      return LangKeys.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION;
    } else if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
      return LangKeys.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY;
    } else if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM)) {
      return LangKeys.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM;
    } else if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {
      return LangKeys.PROFITABILITY_ANALYSIS_MILK_PRICE;
    }
    return null;
  }

  public String getLangKeyForRightYAxis(ReportsToBeanMappings reportsToBeanMappings) {
    if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION)) {
      return LangKeys.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION_CONCENTRATED;
    } else if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
      return LangKeys.PROFITABILITY_ANALYSIS_TOTAL_DIET_COST;
    } else if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM)) {
      return LangKeys.PROFITABILITY_ANALYSIS_IOFC;
    } else if (reportsToBeanMappings.equals(
        ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {
      return LangKeys.PROFITABILITY_ANALYSIS_FEEDING_COST_PER_LITRE_OF_MILK;
    }
    return null;
  }

  void prepareHeader(
      XSSFWorkbook xSSFWorkbook,
      XSSFSheet xSSFSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      ProfitabilityAnalysisReportDto dto,
      XSSFCellStyle cellStyle,
      Locale locale,
      ResourceBundleMessageSource source) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(), xSSFWorkbook, xSSFSheet, rowNumber.get(), cellNumber.getAndIncrement());
    // headings
    XSSFRow row = xSSFSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getVisitName());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    cellNumber.getAndIncrement();
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getVisitDate());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    ProfitabilityAnalysisReportDto dto =
        modelMapper.map(data, ProfitabilityAnalysisReportDto.class);
    byte[] report =
        freeMarkerComponent.render(
            data,
            fetchReportType(dto.getReportType()),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  private String fetchReportType(ReportsToBeanMappings reportType) {
    if (reportType.equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION)) {
      return ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION.getImageTemplateName0();
    }
    if (reportType.equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)) {
      return ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY
          .getImageTemplateName0();
    }
    if (reportType.equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM)) {
      return ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM
          .getImageTemplateName0();
    }
    if (reportType.equals(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)) {
      return ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST
          .getImageTemplateName0();
    }
    return null;
  }
}
