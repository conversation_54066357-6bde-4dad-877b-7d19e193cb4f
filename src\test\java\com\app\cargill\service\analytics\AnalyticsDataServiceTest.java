/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.analytics;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.model.AnalyticsDataPoint.DataPointName;
import com.app.cargill.model.analytics.AnalyticsDataResponse.DataPointContainer;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class AnalyticsDataServiceTest {

  @Mock private DdwReportsAnalyticsService ddwReportsAnalyticsService;

  @InjectMocks private AnalyticsDataService analyticsDataService;

  @Test
  void whenGetAllDataPointsIsInvokedDataIsReturned() {
    DataPointContainer detailedReports =
        new DataPointContainer(DataPointName.DDW_DETAILED_REPORT_DOWNLOAD, 0, new ArrayList<>());
    DataPointContainer summaryReports =
        new DataPointContainer(DataPointName.DDW_DETAILED_REPORT_DOWNLOAD, 0, new ArrayList<>());

    Mono<DataPointContainer> detailedReportsMono = Mono.just(detailedReports);
    Mono<DataPointContainer> summaryReportsMono = Mono.just(summaryReports);
    when(ddwReportsAnalyticsService.fetchAllDetailedReports(any(), any()))
        .thenReturn(detailedReportsMono);
    when(ddwReportsAnalyticsService.fetchAllSummaryReports(any(), any()))
        .thenReturn(summaryReportsMono);

    List<DataPointContainer> result =
        analyticsDataService.getAllDataPoints(Instant.now(), Instant.now());
    assertEquals(2, result.size());
  }
}
