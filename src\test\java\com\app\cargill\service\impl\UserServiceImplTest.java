/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.authconfig.CustomGrantedAuthority;
import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.constants.Business;
import com.app.cargill.constants.MobileDeviceType;
import com.app.cargill.constants.UserAccountType;
import com.app.cargill.document.UserDocument;
import com.app.cargill.dto.*;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IUserService;
import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class UserServiceImplTest {
  @Mock private OktaProperties oktaProperties;
  @Mock private AzureADProperties azureADProperties;
  // Do not delete to prevent NPE for UserService
  @Mock private IUserService userServiceImpl;
  @Mock private UserRepository usersRepository;
  @Mock private UserPreferenceServiceImpl userPreferenceServiceImpl;
  @Mock private ConfigurationsServiceImpl configurationsServiceImpl;
  @InjectMocks private UserServiceImpl userService;

  @Test
  void whenIdTokenIsPassed() throws UnsupportedEncodingException {
    ReflectionTestUtils.setField(
        oktaProperties,
        "issuer",
        "https://cargillcustomer-uat.oktapreview.com/oauth2/ausg7rjg8308oyOj70h7");
    when(oktaProperties.getIssuer())
        .thenReturn("https://cargillcustomer-uat.oktapreview.com/oauth2/ausg7rjg8308oyOj70h7");
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("US");
    UserDetails userDetails = UserDetailsDto.builder().username("<EMAIL>").build();
    Authentication auth = new UsernamePasswordAuthenticationToken(userDetails, null);
    SecurityContextHolder.getContext().setAuthentication(auth);
    UserDto userByIdToken =
        userService.getUserByIdToken(
            "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************==");
    assertNotNull(userByIdToken);

    when(azureADProperties.getIssuer())
        .thenReturn("https://login.microsoftonline.com/57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/v2.0");
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("US");
    userByIdToken =
        userService.getUserByIdToken(
            "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
    assertNotNull(userByIdToken);
  }

  @Test
  void whenIdTokenIsPassedToFetchByTokenId() throws UnsupportedEncodingException {
    ReflectionTestUtils.setField(
        oktaProperties,
        "issuer",
        "https://cargillcustomer-uat.oktapreview.com/oauth2/ausg7rjg8308oyOj70h7");
    when(oktaProperties.getIssuer())
        .thenReturn("https://cargillcustomer-uat.oktapreview.com/oauth2/ausg7rjg8308oyOj70h7");
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("US");
    UserDetails userDetails = UserDetailsDto.builder().username("<EMAIL>").build();
    Authentication auth = new UsernamePasswordAuthenticationToken(userDetails, null);
    SecurityContextHolder.getContext().setAuthentication(auth);
    IdTokenDto idTokenDto =
        IdTokenDto.builder()
            .idToken(
                "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D%3D")
            .applicationVersion("Test")
            .deviceType(MobileDeviceType.ANDROID)
            .build();
    UserDto userByIdToken = userService.fetchAndUpdateUserInfo(idTokenDto);
    assertNotNull(userByIdToken);
    idTokenDto.setIdToken(
        "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************");
    when(azureADProperties.getIssuer())
        .thenReturn("https://sts.windows.net/57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/");
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("US");
    userByIdToken = userService.fetchAndUpdateUserInfo(idTokenDto);
    assertNotNull(userByIdToken);
  }

  @Test
  void whenCorrectDataIsSetUserIsCreated() {
    UserSaveDto userSaveDto =
        UserSaveDto.builder()
            .countryId(Business.Brazil)
            .deleted(false)
            .id(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .salesforceCountryId(0)
            .userName("<EMAIL>")
            .build();
    when(usersRepository.existsByUserId(any())).thenReturn(false);
    when(usersRepository.save(any())).thenReturn(loadUser());

    UserSaveDto result = userService.save(userSaveDto);
    assertNotNull(result);
  }

  @Test
  void whenCorrectDataWithoutIDIsSentUserIsCreated() {
    UserSaveDto userSaveDto =
        UserSaveDto.builder()
            .countryId(Business.Brazil)
            .deleted(false)
            .id(null)
            .localId(UUID.randomUUID().toString())
            .salesforceCountryId(0)
            .userName("<EMAIL>")
            .build();
    when(usersRepository.existsByUserId(any())).thenReturn(false);
    when(usersRepository.save(any())).thenReturn(loadUser());

    UserSaveDto result = userService.save(userSaveDto);
    assertNotNull(result);
  }

  @Test
  void whenCorrectDataIsSetUserIsUpdated() {
    UserSaveDto userSaveDto =
        UserSaveDto.builder()
            .countryId(Business.Brazil)
            .deleted(false)
            .id(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .salesforceCountryId(0)
            .userName("<EMAIL>")
            .build();
    when(usersRepository.findByUserName(any())).thenReturn(loadUser());
    when(usersRepository.save(any())).thenReturn(loadUser());
    UserSaveDto result = userService.update(userSaveDto);

    assertNotNull(result);
  }

  @Test
  void whenUserLogsInStatusIsUpdated() {
    IdTokenDto idTokenDto =
        IdTokenDto.builder()
            .idToken(
                "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D%3D")
            .applicationVersion("Test")
            .deviceType(MobileDeviceType.ANDROID)
            .build();
    when(usersRepository.findByUserName(any()))
        .thenReturn(User.builder().userDocument(UserDocument.builder().build()).build());
    when(usersRepository.save(any())).thenReturn(User.builder().build());
    userService.updateUserLoginStatus(
        "<EMAIL>", AuthenticationPlatform.OKTA, "test", idTokenDto);
    assertNotNull(idTokenDto);
  }

  private User loadUser() {
    UserDocument userDocument =
        UserDocument.builder()
            .countryId(Business.Brazil)
            .id(UUID.randomUUID())
            .salesforceCountryId(0)
            .userName("<EMAIL>")
            .build();
    return User.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .accountType(UserAccountType.EMAIL)
        .deleted(false)
        .email("<EMAIL>")
        .fullName("test")
        .principalName("")
        .userDocument(userDocument)
        .build();
  }

  @Test
  void whenBulkInsertUserReturnValidResult() {
    BulkUserInsertDto test =
        BulkUserInsertDto.builder()
            .countryId(Business.US)
            .salesforceCountryId(2)
            .userNames(Arrays.asList("test"))
            .build();
    when(usersRepository.existsByUserId(any())).thenReturn(false);

    BulkUserInsertDto bulkUserInsertDto = userService.bulkInsertUsers(test);
    assertNotNull(bulkUserInsertDto);
  }

  @Test
  void whenGetCurrentLoggedInUserAuthenticatedUserIsReturned() {
    UserDetails userDetails =
        UserDetailsDto.builder()
            .username("<EMAIL>")
            .authorities(List.of(new CustomGrantedAuthority(AuthenticationPlatform.OKTA)))
            .build();
    Authentication auth =
        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
    SecurityContextHolder.getContext().setAuthentication(auth);
    assertEquals(userService.getCurrentLoggedInUserAuthPlatform(), AuthenticationPlatform.OKTA);
  }

  @Test
  void whenCorrectDataIsSetUserNameIsUpdated() {
    User user = User.builder().fullName("User").id(1L).email("<EMAIL>").build();
    List<User> userList = new ArrayList<>();
    userList.add(user);
    when(usersRepository.findByUserNameWithCode(any())).thenReturn(userList);
    when(usersRepository.save(any())).thenReturn(loadUser());
    List<User> result = userService.updateName("user");
    assertNotNull(result);
  }

  @Test
  void whenWrongDataIsSetUserNameIsUpdated() {
    User user = User.builder().fullName(null).id(1L).email(null).build();
    List<User> userList = new ArrayList<>();
    userList.add(user);
    when(usersRepository.findByUserNameWithCode(any())).thenReturn(userList);
    List<User> result = userService.updateName("user");
    assertNotNull(result);
  }
}
