<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <!-- To enable JMX Management -->
    <jmxConfigurator/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %black(%d{ISO8601}) | %highlight(%-5level) | [%blue(%t)] | %yellow(%C{1}): %.-800msg%n%throwable
            </Pattern>
        </layout>
    </appender>

    <logger name="com.app" level="info" />

    <logger name="org.springframework.security" level="trace" />
    <logger name="org.springframework.web.client" level="trace" />
    <logger name="logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter" level="DEBUG" />
    <logger name="org.apache.http" level="info"/>
    <logger name="org.springframework.integration.aws.sqs.core" level="error"/>
    <logger name="org.apache.http.impl.conn.Wire" level="error"/>
    <logger name="org.apache.http.impl.conn.LoggingManagedHttpClientConnection" level="info"/>
    <logger name="org.apache.http.impl.execchain.MainClientExec" level="error"/>
    <logger name="org.apache.http.impl.conn.PoolingHttpClientConnectionManager" level="error"/>
    <logger name="org.apache.http.impl.conn.LoggingManagedHttpClientConnection" level="error"/>
    <logger name="com.amazonaws.services.s3" level="error"/>
    <logger name="org.hibernate.SQL" level="error"/>
    <logger name="org.hibernate.type" level="error"/>
    <root level="warn">
        <appender-ref ref="console" />
    </root>
</configuration>