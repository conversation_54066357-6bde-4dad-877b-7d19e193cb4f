/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.FeedStorageType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PileAndBunkerDto implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private String name;

  private FeedStorageType isPileOrBunker;

  private Instant createTimeUtc;

  private UUID id;

  private Double topWidthInMeters;

  private Double bottomWidthInMeters;

  private Double heightInMeters;

  private Double bottomLengthInMeters;

  private Double topLengthInMeters;

  private Double dryMatterPercentage;

  private Double silageDMDensityInKgPerMetersCubed;

  private Integer dryMatterOfFeedPerCowPerDay;

  private Integer cowsToBeFed;

  private Double feedOutInclusionRate;

  private Double tonnesOfDryMatter;

  private Double tonnesAsFed;

  private Double footPrintArea;

  private Double tonnesAsFedPerMeterSquaredFootPrintArea;

  private Double slope;

  private Integer silageAsFedDensity;

  private Double feedOutSurfaceAreaMetersSquared;

  private Integer cowsPerDayNeededAtLowerFeedRate;

  private Integer cowsPerDayNeededAtHigherFeedRate;

  private Double kilogramsDryMatterInOneMeter;

  private Double metersPerDay;
  // LoadingUnloadingSiloBag Changes
  private Double filledHeightInMeters;

  private Double diameterInMeters;

  private Double silageLeftInMeters;

  private Double dryMatterPercentageSilo;

  private Double lengthInMeters;

  private Double diameterBagInMeters;

  private Double dryMatterPercentageBag;

  private Double silageDMDensityBagKgPerMeter;

  private Double silageAsFedDensityBag;

  private Instant startDate;
}
