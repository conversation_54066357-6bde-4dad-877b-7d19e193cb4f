/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * There are very few fields we send back to Salesforce when we sync an account Might change it in
 * future
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ToString
public class EventUpdateModel implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ActivityDateTime")
  public String activityDateTime;

  @JsonProperty("Subject")
  public String subject;

  @JsonProperty("IsAllDayEvent")
  public Boolean isAllDayEvent;

  @JsonProperty("Type__c")
  public String typeC;

  @JsonProperty("Business__c")
  public String businessC;

  @JsonProperty("IsReminderSet")
  public Boolean isReminderSet;

  @JsonProperty("StartDateTime")
  public String startDateTime;

  @JsonProperty("EndDateTime")
  public String endDateTime;

  @JsonProperty("DE_Activity_External_ID__c")
  public String deActivityExternalIdC;

  @JsonProperty("DE_Site_Visit_ID__c")
  public String deSiteVisitIdC;

  @JsonProperty("Report_Link__c")
  public String reportLinkC;

  @JsonProperty("WhatId")
  public String whatId;

  @JsonProperty("DE_Site__c")
  public String deSiteId;

  @JsonProperty("OwnerId")
  public String ownerId;
}
