/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.poi.xddf.usermodel.PresetColor;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PenTimeBudgetPotentialMilkLossGainReportDto extends BaseDto {

  private String visitName;
  private String visitDate;
  private String sheetName;
  private String label;
  @Builder.Default private PresetColor lineColor = PresetColor.GREEN;
  @JsonIgnore @Builder.Default private String lineColorHex = "#00FF00";
  private List<XAndYAxisValueDto> dataPoints;
  private String fileName;
  private String toolName;
  private String categoryLabel;
}
