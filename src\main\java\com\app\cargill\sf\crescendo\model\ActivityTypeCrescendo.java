/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ActivityTypeCrescendo {
  EVENT("Event"),
  TASK("Task"),
  SITE_VISIT("Site Visit");

  private final String value;

  @JsonCreator
  ActivityTypeCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static ActivityTypeCrescendo getFromInt(Integer index) {
    ActivityTypeCrescendo[] values = ActivityTypeCrescendo.values();
    if (index == null || index >= values.length) {
      return ActivityTypeCrescendo.EVENT;
    } else {
      return values[index];
    }
  }
}
