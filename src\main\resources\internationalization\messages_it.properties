(preparazione=del capezzolo\: 10-20 secondi; Unità di attacco\: 60-90 secondi)
(soltanto=unità di attacco)
0-3=mesi
1-3=giorni
1.0=
15-23=mesi
2.0=
23-26=mesi
3-9=mesi
3.0=
4.0=
5.0=
9-15=mesi
AAEfficiency=Efficienza AA
AMSUtilization=Utilizzo robot
AMSUtilizationChart=Grafico dell'uso del robot
ARA=ARA
ARS=Argentina ($ ARS)
AUD=Australia ($ AUD)
Account=Client
Account-Not-Synced-To-Lift=L'account non è stato sincronizzato con LIFT; si prega di contattare l'amministratore per la risoluzione
Acre=Acro
Action=Azione
AddBag=Silobag
AddBunker=Trincea
AddPile=Platea
AddTMRScore=Nuovo TMR
AdjustingKPtoAssureSuccess=Regolare KP per assicurare il successo
Afghanistan=Afghanistan
Agrigento=Agrigento
Aguascalientes=Aguascalientes
Alabama=Alabama
Alagoas=Alagoas
Aland_Islands=Isole Aland
Alaska=Giù
Albania=Albania
Alberta=Alberta
Alessandria=Alessandria
Algeria=Algeria
Amapá=Ampass
Amazonas=Amazonas
Amount=Quantità
Ancona=Ancona
Andaman_and_Nicobar_Islands=Isole Andamane e Nicobar
Andhra_Pradesh=Andhra Pradesh
Andorra=Andorra
Angola=Angola
Anguilla=Anguilla
Anhui=Anhui
AnimalInformation=Info animale
AnimalListViewModel.Title=Classe/Sottoclasse animale
Animals=Animali
AnimalsInHerd=Animali nella mandria
AnimalsInPen=Animali nel gruppo
AnimalsObserved=Animali osservati
Annually=Annuale
Answers=Risposte
Antarctica=Antartide
Antigua_and_Barbuda=Antigua e Barbuda
Aosta=Aosta
AppName=Dairy Enteligen
Arezzo=Arezzo
Argentina=Argentina
Arizona=Arizona
Arkansas=Arkansa
Armenia=Armenia
Aruba=Aruba
Arunachal_Pradesh=Arunachal Pradesh
Ascoli_Piceno=Ascoli Piceno
Assam=Assam
Asti=Fino a
AtSixLengthPerDayImperial=6 in/giorno
AtSixLengthPerDayMetric=15 cm/giorno
AtThreeLengthPerDayImperial=3 in/giorno
AtThreeLengthPerDayMetric=7 cm/giorno
Australia=Australia
Australian_Capital_Territory=Territorio della capitale australiano
Austria=Austria
Auto_Sync=Auto Sync
Avellino=Avellino
Average=Media
AverageMilkLoss=Media perdita latte ({0})
AverageScoreTitle=Media TMR Particle Score
AvgBCS=Media BCS
AvgLocomotionScore=Media Locomotion Score (calc.)
Azerbaijan=Azerbaigian
BAM=BAM
BCS=BCS
BCSCategory1=BCS 1.0
BCSCategory1pt5=BCS 1.5
BCSCategory2=BCS 2.0
BCSCategory2pt5=BCS 2.5
BCSCategory3=BCS 3.0
BCSCategory3pt5=BCS 3.5
BCSCategory4=BCS 4.0
BCSCategory4pt5=BCS 4.5
BCSCategory5=BCS 5.0
BCSEditMilkAndDimViewModel.BCSDIMTitle=Giorni in latte (DIM)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Latte
BCSEditMilkAndDimViewModel.Title=Modifica DIM e Latte
BCSHerdAnalysisInputsViewModel.BCS=BCS
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Analisi BCS
BCSHerdAnalysisInputsViewModel.BCSDIM=DIM
BCSHerdAnalysisInputsViewModel.BCSEdit=Modifica
BCSHerdAnalysisInputsViewModel.BCSMilk=Latte
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Analisi mandria
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Obiettivi
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Input
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Risultati
BCSHerdAnalysisMasterViewModel.BCSTitle=Body Condition Score
BCSHerdAnalysisMasterViewModel.Title=Body Condition Score
BCSHerdAnalysisResultsViewModel.BCSAvg=BCS medio
BCSHerdAnalysisResultsViewModel.GraphTitle=Analisi BCS
BCSHerdAnalysisResultsViewModel.MaxBCS=BCS max
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Latte/capo/giorno
BCSHerdAnalysisResultsViewModel.MinBCS=BCS min
BCSHerdAnalysisResultsViewModel.SubHeading=Analisi mandria
BCSHerdAnalysisResultsViewModel.Title=Body Condition Score
BCSPenSelectionViewModel.PenSelectionList=Gruppi
BCSPenSelectionViewModel.Pens=Gruppi
BCSPenSelectionViewModel.SelectPointScale=Scegli la scala
BCSPenSelectionViewModel.Title=Punteggio condizione corporea
BCSSelectPointScaleViewModel.FooterText=Solo una scala può essere utilizzata durante la visita. Cambiando la scala i valori andranno persi
BCSSelectPointScaleViewModel.SelectPointScale=Scegliere una scala
BCSSelectPointScaleViewModel.Title=Body Condition Score
BGL=BGL
BRL=Brasile (R$ BRL)
BRR=BRR
Bad=Bad
Bag=Silobag
BaggedConventionalSilage=Insilato convenzionale insaccato
Bags=Silobag
Bahamas=Bahamas
Bahia=Bahia
Bahrain=Bahrein
Baja_California=Baja California
Baja_California_Sur=Baja California Sur
Baleage=Fasciato
BaleageFQAs=FAQ fasciati
Baleage_AreBalesWrappedWith=Le rotoballe sono avvolte con\:
Baleage_BagsPlacedOnStableWellManagedSurface=Le rotoballe sono poste su una superficie apposita e ben gestita?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Le rotoballe sono ispezionate e riparate con regolarità?
Baleage_TrashVegRodentControlledAroundBags=Immondizia, vegetazione e roditori sono controllati intorno alle rotoballe?
Baleage_WaterShedsOffPlasticNotIntoBaleage=L'umidità esce dalla plastica e non dentro il foraggio?
Bangladesh=Bangladesh
Barbados=Barbados
Bari=Li avevamo
Barletta-Andria-Trani=Barletta-Andria-Trani
Bayern=Bayern
BeddedPack=Lettiera permanente
Beijing=Pechino
Belarus=Bielorussia
Belgium=Belgio
Belize=Belize
Belluno=Belluno
Benchmarks_Serum_ToolTip=<value />
Benevento=Benevento
Benin=Benin
Bergamo=Bergamo
Bermuda=Bermuda
BetweenFifteenTwenty=15-20
Bhutan=Bhutan
BiWeekly=Ogni 2 settimane
Biella=Biella
Bihar=Bihar
BodyConditionHerdGoals=Obiettivi - Analisi mandria - BCS   
BodyConditionHerdInputs=Input BCS - Analisi mandria
BodyConditionHerdResults=Risultati BCS mandria
BodyConditionInputs=Input BCS
BodyConditionResults=Risultati BCS
BodyConditionScoreCategory=BCS {0}
BodyConditionScoreEditInputsViewModel.Count=Conta
BodyConditionScoreEditInputsViewModel.NumberOfCows=Numero di vacche
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Conta il numero di vacche
BodyConditionScoreEditInputsViewModel.Title=Numero di vacche
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=Close-up Asciutta (da -20 a -1)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=Fresche (da 16 a 60 DIM)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=Far-off Asciutta (da -60 a -21)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=Post-parto (da 0 a 15 DIM)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=Fine lattazione (&gt;201 DIM)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=BCS Max
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=Metà lattazione (da 121 a 200 DIM)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=BCS Min
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=Picco lattazione (da 61 a 120 DIM)
BodyConditionScoreHerdEditGoalsViewModel.Title=Modifica obiettivi
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Close-up Asciutta (da -20 a -1)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Fresche (da 16 a 60 DIM)
BodyConditionScoreHerdGoalsViewModel.Edit=Modifica
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Far-off Asciutta (da -60 a -21)
BodyConditionScoreHerdGoalsViewModel.Fresh=Post-parto (da 0 a 15 DIM)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Ob. BCS Max
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Ob. BCS Min
BodyConditionScoreHerdGoalsViewModel.LateLactation=Fine lattazione (&gt;201 DIM)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Metà lattazione (da 121 a 200 DIM)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Picco lattazione (da 61 a 120 DIM)
BodyConditionScoreHerdGoalsViewModel.TableTitle=BCS per fase di lattazione
BodyConditionScoreInputsViewModel.AnimalsObserved=Animali osservati
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Media BCS (calc.)
BodyConditionScoreInputsViewModel.BCSCategory=Categoria BCS
BodyConditionScoreInputsViewModel.BCSPercentOfPen=% del gruppo
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=Body Condition Score (BCS)
BodyConditionScoreInputsViewModel.Edit=Modifica
BodyConditionScoreInputsViewModel.StdDevCalculated=Dev. Std (calc.)
BodyConditionScoreMasterViewModel.Goals=Obiettivi
BodyConditionScoreMasterViewModel.Inputs=Input
BodyConditionScoreMasterViewModel.Results=Risultati
BodyConditionScoreMasterViewModel.SubHeading=Analisi mandria
BodyConditionScoreMasterViewModel.Title=Body Condition Score
BodyConditionScoreResultsViewModel.BCSAverageTitle=Punteggio medio
BodyConditionScoreResultsViewModel.PercentPen=% del gruppo
BodyConditionScoreResultsViewModel.SelectedDates=Scegliere date
BodyConditionScoreResultsViewModel.Title=Risultati BCS
BodyConditionScoresMasterViewModel.BodyConditionScore=Body Condition Score
BodyConditionScoresMasterViewModel.Inputs=Input
BodyConditionScoresMasterViewModel.Results=Risultati
BodyConditionScoresMasterViewModel.Title=Body Condition Score
BodyConditionScoresMasterViewModel.VisitNotebook=Notebook
Bolivia,_Plurinational_State_of=Bolivia, stato pluinazionale di
Bologna=Bologna
Bolzano=Bolzano
Bonaire=Bonaire
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius e Saba
Bosnia_and_Herzegovina=Bosnia Erzegovina
Botswana=Botswana
BottomUnloadingSilo=Silo ciclatore
BottomUnloadingSilos=Silo ciclatore
Bouvet_Island=Bouvet Island
Brazil=Brasile
Brescia=Brescia
Brindisi=Brindisi
British_Columbia=British Columbia
British_Indian_Ocean_Territory=Territorio britannico dell'Oceano Indiano
Brunei_Darussalam=Brunei Darussalam
Bulgaria=Bulgaria
Bull=Toro
Bunker=Trincea
BunkerCapacity=Capienza trincea
BunkerFeedOutRate=Consumo trincea
Bunkers=Trincee
BunkersAndPiles=Trincee e Platee
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Trincee/platee sono coperte con 2 strati di plastica e uno strato non permeabile?
BunkersAndPiles_CleanlinessOfFeedArea=Qual è il grado di pulizia della zona di alimentazione?
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=Con che frequenza viene rimosso il rivestimento plastico dall'insilato?
BunkersAndPiles_FaceRemoveRate=Qual è il tasso di rimozione del fronte?
BunkersAndPiles_LooseOrFacedFeedIsFed=Il "cappello" si utilizza nelle razioni?
BunkersAndPiles_PackingInitialSpreadLayers=Gli strati dell'imballaggio sono di 15cm o meno?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Il rapporto pendenza-altezza della trincea/platea è adeguato?
BunkersAndPiles_PorosityScoresConsistently=Qual è il grado di porosità rispetto la s.s.?
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Quanto tempo passa fra compressione e sigillatura della trincea/platea?
BunkersAndPiles_SideWallsSealedPlastic=Le pareti laterali sono sigillate con la plastica?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Il fronte è liscio? (assenza di strati interrotti che permettono la penetrazione dell'ossigeno)
BunkersAndPiles_TiresSplitsTouching=Vengono utilizzati sacchi o pneumatici?
Burkina_Faso=Burkina Faso
Burundi=Burundi
CAD=Canada (CA$ CAD)
CFNChina=Cina
CFNIndia=India
CHF=Svizzera (CHF CHF)
CLF=CLF
CLP=Cile ($ CLP)
CNY=Cina (CNY CNY)
COP=COP
CPNBrazil=Brasile
CPNFrance=Francia
CPNPoland=Polonia
CPNUS=Stati Uniti d'America
CRC=CRC
CZK=Repubblica Ceca (CZK CZK)
Cagliari=Cagliari
Calf=Vitella
CalfHeiferColostrum=Scheda Vitelli &amp; Manze - Colostro
CalfHeiferGrowerPuberty=Scheda Vitelli &amp; Manze - Accrescimento, gravidanza e pre-parto
CalfHeiferKeyBenchmarks=Scheda Vitelli &amp; Manze - Punti chiave
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=Il colore dipende dalla percentuale dei risultati\: &lt;75% rosso, 75-90%\: arancio, &gt;90\: verde
CalfHeiferPostweaned=Scheda Vitelli &amp; Manze - Post-svezzamento
CalfHeiferPreweaned=Scheda Vitelli &amp; Manze - Pre-svezzamento
CalfHeiferQuestionViewModel.Close=Chiudi
CalfHeiferQuestionViewModel.Colostrum=Colostro
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Accrescimento, gravidanza e pre-parto
CalfHeiferQuestionViewModel.KeyBenchmarks=Punti chiave
CalfHeiferQuestionViewModel.Postweaned=Post-svezzamento
CalfHeiferQuestionViewModel.Preweaned=Pre-svezzamento
CalfHeiferQuestionViewModel.Resources=Risorse
CalfHeiferQuestionViewModel.VisitNotebook=Notebook
CalfHeiferResources=Scheda Vitelli &amp; Manze - Risorse
CalfHeiferResults=Scheda Vitelli &amp; Manze - Risultati
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Punteggio vitelli e manze
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Accrescimento, gravidanza e pre-parto
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Includi nel punteggio totale
CalfHeiferScoreCardScoreViewModel.PhaseOne=Colostro
CalfHeiferScoreCardScoreViewModel.PhaseThree=Post-svezzamento
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Pre-svezzamento
CalfHeiferScorecardImprovementViewModel.Colostrum=Colostro
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=Accrescimento, gravidanza e pre-parto
CalfHeiferScorecardImprovementViewModel.Postweaned=Post-svezzamento
CalfHeiferScorecardImprovementViewModel.Preweaned=Pre-svezzamento
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Clicca sulle singole voci per vedere i punteggi
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=Il colore dipende dalla percentuale dei risultati\: 100% verde, &lt;100% rosso
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Archivi
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Punti chiave
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Fase 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Fase 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Fase 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Fase 7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Fase 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseThree=Post svezzamento
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwo=Pre svezzamento
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Fase 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Archivio dati di crescita e salute
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Gravidanza
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Pubertà
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Colostro
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Pre-parto/Produzione
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Crescita
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Pre/Post-svezzamento
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Notebook
CalfHeiferScorecardLanding=Scheda Vitelli &amp; Manze
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Punti chiave
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Da migliorare
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Risposte
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Punteggio
CalfHeiferScorecardResultsViewModel.VisitNotebook=Notebook
CalfHeiferScorecardViewModel.Colostrum=Colostro
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Accrescimento, gravidanza e pre-parto
CalfHeiferScorecardViewModel.KeyBenchmarks=Punti chiave
CalfHeiferScorecardViewModel.Postweaned=Post-svezzamento
CalfHeiferScorecardViewModel.Preweaned=Pre-svezzamento
CalfHeiferScorecardViewModel.Resources=Risorse
CalfHeiferScorecardViewModel.Title=Scheda
CalfHeiferScorecardViewModel.VisitNotebook=Notebook
CalfHeiferTools=Vitelli &amp; Manze - Strumenti
CalfHeiferToolsViewModel.CalfHeiferScorecard=Segnapunti
CalfHeiferToolsViewModel.CalfHeiferTools=Vitelli &amp; Manze
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Strumenti
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Scegli una voce della lista sottostante per iniziare la visita
CalfHeiferToolsViewModel.CalfHeiferToolsList=Strumenti
CalfHeiferToolsViewModel.Title=Vitelli &amp; Manze
CalfHeiferToolsViewModel.VisitNotebook=Notebook
California=California
Caltanissetta=Caltanissetta
Cambodia=Cambogia
Cameroon=Camerun
Campeche=Campeche
Campobasso=Campobasso
Canada=Canada
Capacity=Capienza
Cape_Verde=Cape Verde
Carbonia-Iglesias=Carbonia-Iglesias
Cargill=Cargill
CargillForageLabKPTest=KP Test
Carlow=Carlow
Caserta=Caserta
Catania=Catania
Catanzaro=Catanzaro
Category1=Manure Score 1.0
Category2=Manure Score 2.0
Category3=Manure Score 3.0
Category4=Manure Score 4.0
Category5=Manure Score 5.0
Cavan=Cavan
Cayman_Islands=Isole Cayman
Ceará=Piazza
Central_African_Republic=Repubblica centrale africana
Chad=Chad
Chandigarh=Chandigarh
ChewsPerCud=Masticazioni
ChewsPerCudMasterViewModel.AddNew=Nuova conta
ChewsPerCudMasterViewModel.CudChewingInputs=Input
ChewsPerCudMasterViewModel.CudChewingResults=Risultati
ChewsPerCudMasterViewModel.NumOfChews=Masticazioni
ChewsPerCudMasterViewModel.Title={0} / Masticazioni
Chhattisgarh=Chhattisgarh
Chiapas=Chiapas
Chieti=Chieti
Chihuahua=Chihuahua
Chile=Cile
China=Cina
Chinese_Taipei=Taipei cinese
Chongqing=Chongqing
ChooseAppPDF=Scegliere un'app per visualizzare il PDF
ChoosingtheCorrectAdditive=Scegliere l'additivo corretto
Christmas_Island=Isola di Natale
Clare=Clare
ClassSubClass=Classe/Sottoclasse animale
Clean=Clean
ClinicalMastitisLosses=Perdite per mastite cliniche
CloseUp=Pre-parto
CloseUpDry=Pre-Parto
CloseUpHeifer=Pre-parto Manze
Coahuila=Coahuila
Cocos_(Keeling)_Islands=Isole Cocos (Keeling)
Colima=Colima
Colombia=Colombia
Colorado=Colorado
Colostrum=Colostro
Colostrum_AmountOfColostrumOrFed=Quanto colostro viene somministrato?
Colostrum_BrixPercentOfColostrumFed=Qual è il °Bx del colostro somministrato?
Colostrum_CleanAndDryCalvingArea=La zona è pulita e asciutta?
Colostrum_CleanAndDryCalvingArea_ToolTip=Verifica lo stato dei garretti\: sono puliti e asciutti?
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Gli attrezzi per alimentare i vitelli sono puliti e disinfettati fra i pasti?
Colostrum_CleanCalfCartToTransportCalf=Il carretto per trasporate i vitelli è pulito?
Colostrum_HoursTillCalfIsRemovedFromMother=Quante ore passano dalla nascita alla separazione dalla madre?
Colostrum_HoursTillCalfReceivesColostrum=Quante ore passano fra nascita e somministrazione del colostro?
Colostrum_NumberOfCowsInCalvingArea=Quante vacche ci sono in sala parto?
Colostrum_PasteurizeColostrumBeforeFeeding=Il colostro somministrato è pastorizzato?
Colostrum_PercentageOfNavelsDippedInSevenPercent=Quanti ombelichi sono immersi in soluzione iodata al 7% entro 1h?
Colostrum_RefrigeratedColostrumStoredLess=È disponibile colostro refrigerato stoccato meno di 24h?
ComfortToolsViewModel.ComfortHeading=Scegli una voce della lista sottostante per iniziare la visita
ComfortToolsViewModel.ComfortToolsList=Strumenti
ComfortToolsViewModel.ComfortToolsTitle=Comfort
ComfortToolsViewModel.HealthHeading=Scegli una voce della lista sottostante per iniziare la visita
ComfortToolsViewModel.HeatstressEvaluationTitle=Stress da caldo
ComfortToolsViewModel.PenTimeTitle=Pen Time Budget
ComfortToolsViewModel.Title=Comfort
ComfortToolsViewModel.VisitNotebook=Notebook
Comments=Commenti
CommonSpinnerViewModel.BodyConditionPDF=Guida Body Condition Score
CommonSpinnerViewModel.InadequateStimulation=Stimolazione inadeguata (preparazione del capezzolo\: &lt;10 secondi; unità di attacco\: &lt;60 o &gt;120 secondi)
CommonSpinnerViewModel.LocomotionPDF=Guida Locomotion Score
CommonSpinnerViewModel.ManureScorePDF=Guida Manure Score
CommonSpinnerViewModel.NoStimulation=Nessuna stimolazione 
CommonSpinnerViewModel.OptimalStimulation=Stimolazione ottimale 
Como=COME
Comoros=COMOROS
Competitor=Concorrente
CompletedTimeKey=Tempo completato
Component=Componenti
Compostbarn=Compostbarn
ConcentrateDistribution=Distribuzione concentrato
ConfirmExport=Premendo OK verrà fatto uno screenshot di ogni strumento prima di tornare a questa schermata
ConfirmScalePointSwitch=Cambiando la scala, i dati inseriti andranno persi
ConfirmScorerSwitch=Cambiando il setaccio, i dati inseriti andranno persi
Congo=Congo
Congo,_the_Democratic_Republic_of_the=Congo, la Repubblica democratica del
Connecticut=Connecticut
Consumer=Utenti
ConsumerDetailsViewModel.DeleteProspect=Cancella cliente
ConsumerDetailsViewModel.DeleteProspectPrompt=Sicuro di volere cancellare questo cliente? Il sito e la visita in corso saranno persi
ConsumerDetailsViewModel.MainHeading=Siti produttivi
ConsumerDetailsViewModel.NewSite=Nuovo sito produttivo
ConsumerDetailsViewModel.ProspectTitle=Dettagli cliente
ConsumersViewModel.NewConsumer=Nuovo cliente
Continue=Continua
Cook_Islands=Isole Cook
Cork=sughero
Corn=Mais
CornSilage=Silomais
CornSilageKernel=FAQ fasciati
CornSilageResources=Risorse Silomais
CornSilageStopGo=Regole per la raccolta del silomais
Cosenza=Cosenza
Costa_Rica=Costa Rica
Costs=Costi
Cote_d'Ivoire=Costa d'Avorio
Count=Conta
Cow=Vacca 
CowEfficiency=Efficienza
CowPerRobot=Vacche per robot
CowsOutsideTargetRangeToolTip=Obiettivo\: &lt;20% delle vacche fuori range
CowsPerDayNeeded=Fabbisogno vacche/giorno
CowsSectionToolTip=Analizzare almeno 8 vacche per trarre conclusioni. In gruppi più piccoli, testare tutte le vacche in pre-parto
CowsToBeFed=Animali da alimentare
CreateDuplicateDiet=Esiste già una razione per questi animali. Crearne una nuova comunque?
CreateDuplicateNameDiet=Esiste già una razione con questo nome, prego usare un nome nuovo
Created=Creata
Cremona=Cremona
Croatia=Croazia
CropCharacteristicsDecisionGuide=Guida Caratteristiche delle colture
Crotone=Crotone
Cuba=Cuba
CudChewingAverageNumber=Numero medio
CudChewingDataEntryViewModel.CudChewing=Ruminazioni
CudChewingDataEntryViewModel.HerdCudChewingDescription=Conta il numero di vacche in ruminazione nel gruppo utilizzando il contatore. Inserire almeno 10 vacche
CudChewingDataEntryViewModel.No=No
CudChewingDataEntryViewModel.Yes=Si
CudChewingHerdEditScoreViewModel.AverageChewsItem=Masticazioni medie
CudChewingHerdEditScoreViewModel.Close=Chiudi
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Giorni in latte
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Modifica punteggio ruminazioni
CudChewingHerdEditScoreViewModel.EditScoreTitle=Modifica punteggio ruminazioni
CudChewingHerdEditScoreViewModel.PercentChewingItem=Ruminazioni (%)
CudChewingMasterViewModel.CudChewing=Ruminazioni
CudChewingMasterViewModel.CudChewingInputs=Input
CudChewingMasterViewModel.CudChewingResults=Risultati
CudChewingPen=Gruppo
CudChewingPercentChewing=Masticazioni (%)
CudChewingPercentGoal=Obiettivo {0}%
CudChewingPercentOfPen=Ruminazione (% del gruppo)
CudChewingViewModel.CudChewing=Gruppi
CudChewingViewModel.CudChewingList=Gruppi (solo lattazione e asciutta)
CudChewingViewModel.CudChewingTitle=Nome del gruppo
CudChewingViewModel.Title=Gruppi
CudChewsCalculatorViewModel.CalculatorHeading=Scegli una vacca e conta il numero di masticazioni. Cliccare "Nuova conta" per aggiungerne una nuova
CudChewsCalculatorViewModel.CudChewCategorySection=Vacche
CudChewsCalculatorViewModel.NumOfChews=Masticazioni
CudChewsDatesForComparisonViewModel.CudChewsPercent=% Masticazione/
Cundinamarca=Cundinamarca
Cuneo=Cuneo
Curaçao=Curaãçao
Current=Attuale
CurrentDownResponse=Risposta rilascio del latte ({0}/vacca/giorno)
CurrentMilkPrice=Prezzo attuale del latte ({0}/{1})
CurrentSCC=CCS attuale (cell/{0})
CurrentVisitSummary=Riepilogo visita corrente
Customer=Cliente
CustomerDetailViewModel.CustomerTitle=Profilo cliente
CustomerDetailViewModel.MainHeading=Siti produttivi
CustomerDetailViewModel.NewSite=Nuovo sito produttivo
CustomerDetailViewModel.NewVisit=Nuova visita
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=Allevatore
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Scegli il segmento
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Title=Dettagli
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=Nome cliente - Nome del sito produttivo
Cyprus=Cipro
CzechRepublic=Repubblica Ceca
Czech_Republic=Repubblica Ceca
DDW=Da DDW
DDWOfflineMessage=Rete assente. Si desidera visualizzare il report offline?
DDWUpdatedTime=Ultimi dati aggiornati il\: {0}
DKK=DKK
DZD=Algeria (DA DZD)
Dadra_and_Nagar_Haveli=Dadra e Nagar Haveli
Daily=Giornaliero
DairyEnteligenFarmReportsources=Risorse DDW
Daman_and_Diu=Daman e Diu
DashboardViewModel.Alert=Attenzione\!
DashboardViewModel.AlertMessage=I dati non vengono sincronizzati da {0} giorni
DashboardViewModel.GoodAfternoon=Buon pomeriggio, 
DashboardViewModel.GoodMorning=Buongiorno, 
DashboardViewModel.MessageBody=Utilizza il Notebook per prendere note, fare foto e/o video durante le visite
DashboardViewModel.MessageHeader=Messaggio
DashboardViewModel.RecentSiteVisit=Visite recenti
DashboardViewModel.UserPreferences=Preferenze utente
Date=Data
DateGone=Data fine
DatesForComparison=Date per confronto
Days=Giorni
DaysInMilkItem=Giorni in lattazione (GIL)
DeLaval=DeLaval
DeathLoss=Morte
DecidingSilageStorage=Scegliere la tipologia di stoccaggio
Delaware=Delaware
Delete=Elimina
DeleteMatrixValue=Sei sicuro di voler cancellare questa matrice?
Delhi=Delhi
Denmark=Danimarca
DensityLossesinPressedBagSilos=Densità e perdite nelle silobags
Diet=Razione
DietDCAD=DCAD razione mEq/100g
DietDetailViewModel.Created=Creata
DietDetailViewModel.DDW=Da DDW
DietDetailViewModel.Max=MAX
DietDetailViewModel.SystemGenerated=Generata dal sistema
DietDetailViewModel.Title=Dettagli della razione
DietDetailViewModel.UserCreated=Dall'utente
DietListViewModel.InfoNewDiet=Se il MAX è collegato al sito produttivo, i nomi delle razioni saranno aggiornate automaticamente in Dairy Enteligen. Diversamente, aggiungi le razioni manualmente o lascia questa lista vuota e seleziona la classe/sottoclasse corretta per ogni gruppo. Una volta creata la razione, seleziona la classe/sottoclasse di animali corrispondente
DietListViewModel.MainHeading=Razioni
DietListViewModel.New=Nuovo
DietListViewModel.NewDiet=Nuova razione
DietListViewModel.Title=Razioni
Dirty=Dirty
DisplacedAbomasum=Dislocazione abomaso
District_of_Columbia=Distretto della Colombia
Distrito_Federal=Distretto Federale
Djibouti=Djibouti
DoNotTest=&lt;20
Dominica=Dominica
Dominican_Republic=Repubblica Dominicana
Donegal=Donegal
DontKnow=Non lo so
DownResponse=Risposta rilascio del latte ({0}/vacca/gg)
Dry=Asciutta
DryCow=Vacche Asciutte
DryLot=Box all'aperto
Dryhay=Fieno secco
Dublin=Dublino
DuplicatePenName=Esiste già un gruppo con questo nome. Sceglierne un altro
Durango=Durango
Dystocia=Distocia
EGP=EGP
EarlyLactation=Inizio lattazione
Ecuador=Ecuador
Edit=Modifica
EditDatesForComparison=Modifica date per confronto
EditDatesForComparisonViewModel.Chews=Masticazioni
EditDatesForComparisonViewModel.EditDatesClose=Chiudi
EditDatesForComparisonViewModel.EditDatesLabel=Scegliere dalla lista sottostante le date da confrontare per questo gruppo
EditDatesForComparisonViewModel.EditDatesTitle=Modifica date per confronto
EditDatesForComparisonViewModel.EditDatesVisits=Visite
EditDatesForComparisonViewModel.LocomotionScoreAverage=Media Locomotion Score\:
EditDatesForComparisonViewModel.ManureScoreAverage=Punteggio medio feci
EditDatesForComparisonViewModel.MetabolicIncidence=Scegli fino a 5 date per il confronto
EditDatesForComparisonViewModel.PenTimeBudget=Scegli fino a 7 date per il confronto
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Selezionare una data dalla lista per effettuare il confronto
EditDatesForComparisonViewModel.TimeRemainingForResting=t riposo rimanente\:
EditDatesForComparisonViewModel.Title=Modifica date per confronto
EditDatesForComparisonViewModel.Visits=Visite
EditGoalsCudChewingViewModel.Close=Chiudi
EditGoalsCudChewingViewModel.CloseUpDry=Close-up Asciutta
EditGoalsCudChewingViewModel.CudChews=Masticazioni medie
EditGoalsCudChewingViewModel.EarlyLactation=Fresche
EditGoalsCudChewingViewModel.EditGoalsTitle=Modifica Obiettivi di ruminazioni
EditGoalsCudChewingViewModel.FarOffDry=Far-off Asciutta
EditGoalsCudChewingViewModel.Fresh=Post-parto
EditGoalsCudChewingViewModel.LateLactation=Fine lattazione
EditGoalsCudChewingViewModel.MidLactation=Metà lattazione
EditGoalsCudChewingViewModel.PeakMilk=Picco lattazione
EditGoalsCudChewingViewModel.PercentChewing=Ruminazioni (%)
EditNoteViewModel.Action=Azione
EditNoteViewModel.Cancel=Cancella
EditNoteViewModel.Category=Categoria
EditNoteViewModel.Close=Chiudi
EditNoteViewModel.CreatedByMetadata=CCreata il {0} @ {1} da {2}
EditNoteViewModel.Delete=Cancella
EditNoteViewModel.DeleteImageButtonText=Cancella immagine
EditNoteViewModel.DeleteImagePrompt=Vuoi cancellare questa immagine?
EditNoteViewModel.DeletePrompt=Vuoi cancellare questa nota?
EditNoteViewModel.DeleteVideoButtonText=Cancella video
EditNoteViewModel.DeleteVideoPrompt=Vuoi cancellare questo video?
EditNoteViewModel.Event=Evento
EditNoteViewModel.LastUpdatedByMetadata=Modificata il {0} @ {1} da {2}
EditNoteViewModel.NoteCamcorderNotImplemented=Accesso alla Fotocamera non ancora implementato
EditNoteViewModel.NoteGalleryNotImplemented=Accesso alla Galleria non ancora implementato
EditNoteViewModel.NoteLabel=Note
EditNoteViewModel.NoteOnlyOneImage=È ammessa solo un'immagine per nota. Si prega di cancellare l'immagine esistente
EditNoteViewModel.NoteOnlyOneVideo=È ammesso solo un video per nota. Si prega di cancellare il video esistente
EditNoteViewModel.Observation=Osservazione
EditNoteViewModel.Save=Salva
EditNoteViewModel.Task=Compito
EditNoteViewModel.Title=Note
EditNoteViewModel.TitleLabel=Titolo
Egypt=Egitto
El_Salvador=Il salvatore
EmailReportViewModel.AnimalImpact=Impatto animale
EmailReportViewModel.CalfHeiferItem=Vitelli &amp; Manze
EmailReportViewModel.CalfHeiferScorecard=Segnapunti
EmailReportViewModel.Capacity=Capienza
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Elenco categorie
EmailReportViewModel.Charts=Grafici
EmailReportViewModel.CoefficientVariation=Coeff. di variabilità (%)
EmailReportViewModel.ComfortHeatStressBanner=Stress da caldo - Gruppi
EmailReportViewModel.ComfortItem=Comfort
EmailReportViewModel.ComfortPenTimeBanner=Pen Time Budget
EmailReportViewModel.ComfortToolsTitle=Comfort
EmailReportViewModel.CowsOutsideTargetRange=Vacche fuori range (%)
EmailReportViewModel.CudChewingTitle=Ruminazioni
EmailReportViewModel.DietDCADStr=DCAD razione
EmailReportViewModel.EmailBody={0}-{1} Report
EmailReportViewModel.EmailSelectedTools=Invia mail con gli strumenti selezionati
EmailReportViewModel.EmailSubject={0} Report
EmailReportViewModel.ExportSelected=Invia mail con gli strumenti selezionati
EmailReportViewModel.FeedOut=Consumo
EmailReportViewModel.ForageAuditScorecard=Segnapunti Audit foraggi
EmailReportViewModel.ForageImprovements=Segnapunti Audit foraggi
EmailReportViewModel.ForageLanding=Pagina destinazione Audit foraggi
EmailReportViewModel.ForageScorecard=Segnapunti Audit foraggi
EmailReportViewModel.GeneratingReport=Elaborazione Report in corso …
EmailReportViewModel.GotoMarketBranding=Brand
EmailReportViewModel.HealthItem=Salute
EmailReportViewModel.HeatstressEvaluationTitle=Stress da caldo
EmailReportViewModel.Herd=Mandria
EmailReportViewModel.HerdAnalysis=Analisi mandria
EmailReportViewModel.HerdGoals=Analisi mandria - Obiettivi
EmailReportViewModel.HerdInputs=Analisi mandria - Input
EmailReportViewModel.HerdResults=Analisi mandria - Risultati
EmailReportViewModel.HerdRevenue=Analisi mandria - Ricavi
EmailReportViewModel.Improvements=Da migliorare
EmailReportViewModel.Improvements1=Miglioramenti
EmailReportViewModel.Inputs=Input
EmailReportViewModel.InputsOutputsChart=Input / Risultati / Grafici
EmailReportViewModel.LocomotionScoreTitle=Locomotion Score
EmailReportViewModel.ManureScoreTitle=Manure Score
EmailReportViewModel.MarketBranding=Brand
EmailReportViewModel.MetabolicIncidenceTitle=Malattie metaboliche
EmailReportViewModel.MilkProcessCalcInputsTab=Ricavi mungitura\: Input
EmailReportViewModel.MilkProcessCalcResourcesTab=Ricavi mungitura\: Risorse
EmailReportViewModel.MilkProcessCalcResultsTab=Ricavi mungitura\: Risultati
EmailReportViewModel.MilkProcessRevenue=Ricavi mungitura
EmailReportViewModel.MilkProcessRevenueCalculator=Ricavi mungitura
EmailReportViewModel.MilkingTime=t mungitura
EmailReportViewModel.Notes=Note
EmailReportViewModel.NumOfChews=Numero di masticazioni
EmailReportViewModel.NutritionForage=Audit foraggi
EmailReportViewModel.NutritionItem=Nutrizione
EmailReportViewModel.NutritionPile=Capienza trincee e mucchi
EmailReportViewModel.Outputs=Risultati
EmailReportViewModel.PenCompare=Analisi gruppo - Confronto
EmailReportViewModel.PenDensity=Densità del gruppo
EmailReportViewModel.PenInputs=Analisi gruppo - Input
EmailReportViewModel.PenResults=Analisi gruppo - Risultati
EmailReportViewModel.PenTimeTitle=Pen Time Budget
EmailReportViewModel.PileAndBunkerTitle=Trincee e mucchi
EmailReportViewModel.PileBunkerCapacities=Sitemi di stoccaggio
EmailReportViewModel.ProductivityItem=Produzione
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Risorse
EmailReportViewModel.Results=Risultati
EmailReportViewModel.RumenHealthBodyConditionTitle=Body Condition Score
EmailReportViewModel.RumenHealthLocomotionTitle=Locomotion Score
EmailReportViewModel.RumenHealthManureTitle=Manure Score
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Malattie metaboliche
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
EmailReportViewModel.RumenHealthTMRTitle=TMR Particle Score
EmailReportViewModel.RumenHealthTitle=Ruminazioni
EmailReportViewModel.RumenHealthUrinePHTitle=pH urine
EmailReportViewModel.ScoreScreen=Punteggi
EmailReportViewModel.TMRParticleScoreTitle=Particle Score
EmailReportViewModel.TimeBudget=Pen Time Budget
EmailReportViewModel.Title=Email Report
EmailReportViewModel.UrinePhSTDDEV=Dev. Std
EmailReportViewModel.UserPreferences=Impostazioni
EmailReportViewModel.UserSettings=Impostazioni
EmailReportViewModel.WalkthroughReportTitle=Visita guidata
EnergyImperial=Mcal/lb
EnergyMetric=Mcal/kg
Enna=Enna
Equatorial_Guinea=Guinea Equatoriale
Eritrea=Eritrea
ErrorDescription=Si è verificato un errore durante la lettura o scrittura delle informazioni
ErrorTitle=Errore
Espírito_Santo=Santo
Estonia=Estonia
Ethiopia=Etiopia
Eula=Licenza
Euro=Paesi UE (€ EUR)
Event=Evento
EveryOtherDay=Giorni alterni
Excessive=&gt;0.5 unità BCS
ExtraDaysOpenCostInfoMessage=€2-€5/giorno aperto in più
FAQDairyEnteligenFarmReportandDDW=FAQ
Falkland_Islands_(Malvinas)=Isole Falkland (Malvinas)
FarOff=Asciutta primo periodo
FarOffDry=Asciutta
Faroe_Islands=Isole Faroe
Federal_District=Distretto Federale
FeedFirst=Feed first
FeedOutRateInfo=Tasso di consumo
FeedOutRatesFilmsStorageSysExamined=Tasso di consumo, films e sistemi di stoccaggio esaminati
FeedOutSurfaceAreaImperial=Superficie di consumo (ft^2)
FeedOutSurfaceAreaMetric=Superficie di consumo (m^2)
FeedingRate=Livello d'inclusione (t.q./vacca)
FeedoutLossesForageStorageSys=Eliminare le perdite di stoccaggio
FermentationAnalysisSilageQT=Analisi di fermentazione &amp; Qualità insilati
Fermo=Fermo
Ferrara=Ferrara
FieldKPTest=KP Test
Fiji=Figi
FillAllFields=Prego inserire tutte le informazioni
FillAllMandatoryFields=Prego inserire tutte le informazioni obbligatorie
FinalObservations=Osservazioni finali
Finish=Fine
Finland=Finlandia
Florence=Firenze
Florida=Florida
Foggia=Foggia
ForageAuditScorecard=Segnapunti Audit foraggi
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Audit foraggi - Da migliorare
ForageAuditScorecardResponsesViewModel.ResponsesTab=Audit foraggi - Risposte
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Da Migliorare
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Risposte
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Punteggio
ForageAuditScorecardResultsViewModel.ImprovementsTab=Da Migliorare
ForageAuditScorecardResultsViewModel.ResponsesTab=Risposte
ForageAuditScorecardResultsViewModel.ScoreTab=Punteggio
ForageAuditScorecardResultsViewModel.Title=Silo a torre
ForageAuditScorecardResultsViewModel.VisitNotebook=Notebook
ForageAuditScorecardScoreViewModel.GoodIndicator=Buona
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Da Migliorare
ForageAuditScorecardScoreViewModel.OverallForageScore=Includi nel punteggio totale
ForageAuditScorecardScoreViewModel.Title=Punteggio totale Audit foraggi
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Tipologie di insilato
ForageAuditViewModel.ForageAuditScorecard=Segnapunti Audit foraggi
ForageAuditViewModel.ForageDetail=La qualità del foraggio è l'aspetto più importante in un programma di nutrizione ed è la chiave della redditività aziendale. "Segnapunti Audit foraggi" serve per valutare la gestione dei foraggi usati in azienda ed evidenziare i punti di miglioramento. È possibile utilizzare tutte le voci oppure solo quelle di interesse durante la visita
ForageAuditViewModel.ForageHeading=Informazioni base sui foraggi
ForageAuditViewModel.Resources=Risorse
ForageAuditViewModel.Title=Audit foraggi
ForageAuditViewModel.VisitNotebook=Notebook
ForageAudit_Sample_ToolTip=Testo di esempio per la descrizione. Rimuovere quando disponibile
ForageManagement_ForagesHarvestedAtProperMaturity=I foraggi vengono raccolti alla giusta maturità per tipo di raccolto e struttura di stoccaggio?
ForageManagement_ForagesHarvestedAtProperMoisture=I foraggi vengono raccolti alla giusta umidità per tipo di raccolto e struttura di stoccaggio?
ForageScorecardResultsViewModel.Title=Fasciato
ForageScorecardViewModel.Baleage=Fasciato
ForageScorecardViewModel.BunkersAndPiles=Trincee e Platee
ForageScorecardViewModel.ForageAuditCategories=Categorie Audit foraggi
ForageScorecardViewModel.ForageAuditScore=Punteggio Audit foraggi
ForageScorecardViewModel.ForageAuditScorecard=Segnapunti Audit foraggi
ForageScorecardViewModel.ForageCategoryTooltip=La qualità del foraggio è l'aspetto più importante in un programma di nutrizione ed è la chiave della redditività aziendale. Questo strumento serve per valutare la gestione dei foraggi usati in azienda ed evidenziare i punti di miglioramento
ForageScorecardViewModel.Harvest=Qualità dei foraggi in razione
ForageScorecardViewModel.MaintainingForageQuality=Mantenere la qualità del foraggio
ForageScorecardViewModel.No=No
ForageScorecardViewModel.SilageBags=Silobag
ForageScorecardViewModel.SurveyCategories=Segnapunti Audit foraggi
ForageScorecardViewModel.SurveyOfForages=Gestione del foraggio
ForageScorecardViewModel.Title=Segnapunti Audit foraggi
ForageScorecardViewModel.TowerSilos=Silo a torre
ForageScorecardViewModel.ViewOverallForageScore=Visualizza punteggio generale sui foraggi
ForageScorecardViewModel.VisitNotebook=Notebook
ForageScorecardViewModel.Yes=Si
ForlÃ¬-Cesena=ForlÃ¬-Cesena
FourScreenNew=Setaccio a 4 strati (nuovo)
FourScreenNewType=(4 mm)
FourScreenOld=Setaccio a 4 strati (vecchio)
FourScreenOldType=(1.18 mm)
FourToSevenDays=Da 4 a 7 giorni
France=Francia
FreeFlow=Traffico libero
FreeFormReportViewModel.CalfHeiferItem=Vitelli &amp; Manze
FreeFormReportViewModel.CalfHeiferScorecard=Segnapunti
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Grafici
FreeFormReportViewModel.ComfortHeatStressBanner=Stress da caldo - Gruppi
FreeFormReportViewModel.ComfortItem=Comfort
FreeFormReportViewModel.ExportSelected=Esporta gli strumenti selezionati
FreeFormReportViewModel.GeneralNotes=Note della visita
FreeFormReportViewModel.HealthItem=Salute
FreeFormReportViewModel.Inputs=Input
FreeFormReportViewModel.KeyBenchmarks=Punti chiave
FreeFormReportViewModel.MarketingBranding=Brand
FreeFormReportViewModel.MilkProcessCalcInputsTab=Ricavi mungitura - Input
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Ricavi mungitura - Risorse
FreeFormReportViewModel.MilkProcessCalcResultsTab=Ricavi mungitura - Risultati
FreeFormReportViewModel.MilkProcessRevenueCalculator=Ricavi mungiura
FreeFormReportViewModel.MilkSoldEvaluation=Valutazione latte venduto
FreeFormReportViewModel.Notes=Note
FreeFormReportViewModel.NutritionForage=Audit foraggi
FreeFormReportViewModel.NutritionItem=Nutrizione
FreeFormReportViewModel.NutritionPile=Capienza di trincee e mucchi
FreeFormReportViewModel.Outputs=Risultati
FreeFormReportViewModel.OverallImprovements=Miglioramenti complessivi
FreeFormReportViewModel.OverallResponses=Risposte complessive
FreeFormReportViewModel.OverallScore=Punteggio totale
FreeFormReportViewModel.PenTimeTitle=Pen Time Budget
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Consumo di trincee e mucchi
FreeFormReportViewModel.ProductivityItem=Produzione
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Risultati
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Body Condition Score
FreeFormReportViewModel.RumenHealthLocomotionTitle=Locomotion Score
FreeFormReportViewModel.RumenHealthManureTitle=Manure Score
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Malattie metaboliche
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
FreeFormReportViewModel.RumenHealthTMRHerdTitle=TMR Particle Score mandria
FreeFormReportViewModel.RumenHealthTMRTitle=TMR Particle Score
FreeFormReportViewModel.RumenHealthTitle=Ruminazioni
FreeFormReportViewModel.RumenHealthUrinePHTitle=pH urine
FreeFormReportViewModel.Title=Free Form Report
FreeFormReportViewModel.VisitTitle=Nome visita
FreeFormReportViewModel.WalkThroughNotes=Visita guidata
FreeHandNoteClearPaletteDialogMessage=Vuoi cancellare tutto sulla schermata?
FreeHandNoteEditorPageTitle=Note a mano libera
FreeHandNoteSaveUserDialogMessage=Vuoi salvare questa nota?
FreeHandNotesViewModel.Save=Salva
Freestall=Freestall
French_Guiana=Guiana francese
French_Polynesia=Polinesia francese
French_Southern_Territories=Territori della Francia del sud
Fresh=Fresca
FreshCow=Vacche Fresche
FreshHeifer=Primipara fresca
Frosinone=Frosinone
Fujian=Fujian
GBP=Regno Unito (GBP GBP)
GEA=GEA
GTQ=Guatemala (Q GTQ)
Gabon=Gabon
Galway=Galway
Gambia=Gambia
Gansu=Gansu
General=Info gruppo
Genoa=Genova
Georgia=Georgia
Germany=Germania
GettingtheMostOutofYourForage=Ottenere il massimo dal proprio foraggio
Ghana=Ghana
Gibraltar=Gibilterra
Girolando=Girolando
Global=Globale
Goa=Goa
Goal=Obiettivo
Goiás=Goião
Good=Good
Gorizia=Gorizia
GreaterThan8Hours=&gt;8 ore
GreaterThanFive=&gt;5 grani interi
GreaterThanSevenDays=&gt;7 giorni
GreaterThanSixHours=&gt;6 ore
GreaterThanThirtySixInchesPerDay=&gt;30 cm/giorno
GreaterThanTwelveHours=&gt;12 ore
GreaterThanTwenty=&gt;20
Greece=Grecia
Greenland=Groenlandia
Grenada=Grenada
Grosseto=Grosseto
GrowerPubertyPregnancyCloseup=Accrescimento, gravidanza e pre-parto
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Il box è pulito e asciutto?
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Verifica lo stato dei garretti\: sono puliti e asciutti?
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=Si raggiunge il BCS desiderato in ogni fase di crescita?
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Si notano feci molli?
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=La mangiatoia viene pulita ogni giorno e gli avanzi rimossi?
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=È disponibile acqua fresca e pulita?
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=Nessuna evidenza di contaminazioni
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Le manze nei gruppi sono uniformi?
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Gli animali del gruppo  dovrebbero essere della stessa taglia
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=Qual è la % di sovraffollamento?
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=La razione viene ottimizzata con frequenza per raggiungere gli obiettivi di crescita?
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Lo spazio in mangiatoia è adeguato?
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=30cm per animali di 135-270kg, 38cm per animali di 270-400kg , 46cm per animali di &gt;400kg
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Le dimensioni del box sono adeguate per le manze?
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=4m^2 per animali di 135-270kg, 5m^2 per animali di 270-400kg, 7m^2 per animali di &gt;400kg
Guadeloupe=Guadeloupe
Guam=Guam
Guanajuato=Guanajuato
Guangdong=Guangdong
Guangxi=Guangxi
Guatemala=Guatemala
Guernsey=Guernsey
Guerrero=Guerrero
Guinea=Guinea
Guinea-Bissau=Guinea-Bissau
Guizhou=Guizhou
Gujarat=Gujarat
Guyana=Guyana
HKD=HKD
HNL=Honduras (HNL HNL)
HRK=HRK
HUF=Ungheria (Ft HUF)
Hainan=Hainan
Haiti=Haiti
HalfPointScale=Scala 1/2 punto
Harvest=Qualità dei foraggi in razione
Harvest_AdequateEquipmentAndLabor=Quanto tempo è necessario per ultimare il raccolto?
Harvest_CornSilageMoistureRangeConsistent=Qual è il contenuto medio di umidità del silomais? (costante nel 90% dei campioni)
Harvest_ForageHarvestingDocumented=Sono documentate le condizioni di campo, di raccolta e di stoccaggio dei foraggi?
Harvest_ForagesHarvestedAtProper=I foraggi sono raccolti alla giusta maturità e umidità per il tipo di raccolto e la struttura di stoccaggio?
Harvest_KPScoreIsMonitored=Quante cariossidi sono presenti in 1 litro di campione?
Harvest_LengthOfCutMonitored=La lunghezza del taglio è monitorato con PSPS?
Harvest_UseSilageAdditive=Sono utilizzati additivi, inoculi o stabilizzatori aerobici?
Harvest_WholePlantMoistureDetermined=L'umidità dell'intera pianta è misurata in campo?
Haryana=Haryana
Hawaii=Hawaii
Haylage=Silo fieno
HealthToolsViewModel.HealthHeading=Scegli una voce dalla lista sottostante per iniziare la visita
HealthToolsViewModel.HealthToolsList=Strumenti
HealthToolsViewModel.RumenHealthBodyConditionTitle=Body Condition Score
HealthToolsViewModel.RumenHealthLocomotionTitle=Locomotion Score
HealthToolsViewModel.RumenHealthManureScreening=Salute ruminale valutazioni feci
HealthToolsViewModel.RumenHealthManureTitle=Manure Score
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Malattie metaboliche
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
HealthToolsViewModel.RumenHealthTMRTitle=TMR Particle Score
HealthToolsViewModel.RumenHealthTitle=Ruminazioni
HealthToolsViewModel.RumenHealthUrinePHTitle=pH urine
HealthToolsViewModel.Title=Salute
Heard_Island_and_McDonald_Islands=Heard Island e McDonald Islands
HeatstressCalculations=Calcoli stress da caldo
HeatstressChart=Grafici stress da caldo
HeatstressChartViewModel.DMIReduction=Riduzione s.s.ing.
HeatstressChartViewModel.EnergyEquivMilkLoss=Perdita di latte equivalente per Energia
HeatstressChartViewModel.EstimateDryMatter=Ingestione s.s. stimata
HeatstressChartViewModel.HeatstressEvalLabel=Temperature corrette per temperatura media e umidità relativa (senza ore di sole)
HeatstressChartViewModel.IntakeAdjustment=Ingestione aggiustata
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=Perdita di energia consumata 
HeatstressChartViewModel.Mcal=Mcal
HeatstressChartViewModel.MilkValueLossPerDay=Perdita di latte/giorno
HeatstressChartViewModel.MilkValueLossPerMonth=Perdita di latte/mese
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.ReductionDMI=Riduzione s.s.ing.
HeatstressChartViewModel.TempHumidIndex=Indice THI
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Notebook
HeatstressData=Dati stress da caldo
HeatstressDataEntryViewModel.AnimalInputs=Input animali
HeatstressDataEntryViewModel.CurrentMilkPrice=Prezzo attuale del latte ({0}/{1})
HeatstressDataEntryViewModel.DMI=s.s. ingerita ({0})
HeatstressDataEntryViewModel.Exposure=Esposizione
HeatstressDataEntryViewModel.HoursExposed=Ore di sole
HeatstressDataEntryViewModel.Humidity=Umidità (%)
HeatstressDataEntryViewModel.LactatingAnimals=Animali in mungitura
HeatstressDataEntryViewModel.Milk=Latte ({0})
HeatstressDataEntryViewModel.MilkFat=Grasso (%)
HeatstressDataEntryViewModel.MilkProtein=Proteine (%)
HeatstressDataEntryViewModel.NEL=NEL (Mcal/{0})
HeatstressDataEntryViewModel.Temperature=Temperatura ({0})
HeatstressDataEntryViewModel.VisitNotebook=Notebook
HeatstressDataEntryViewModel.Weather=Clima
HeatstressGreen=Soglia di stress
HeatstressOrange=Stress moderato-severo
HeatstressRed=Stress severo
HeatstressTableViewModel.HeatstressChartTab=Grafici
HeatstressTableViewModel.HeatstressDataTab=Input
HeatstressTableViewModel.Title=Stress da caldo
HeatstressTableViewModel.VisitNotebook=Notebook
HeatstressYellow=Stress medio-moderato
Hebei=Hebei
Heifer=Manza
Heilongjiang=Heilongjiang
Henan=Henan
HerdAnalysisGoalsViewModel.CloseUpDry=Close-up Asciutta
HerdAnalysisGoalsViewModel.CudChewingGoals=Obiettivo ruminazioni
HerdAnalysisGoalsViewModel.CudChews=Masticazioni
HerdAnalysisGoalsViewModel.DIM=DIM
HerdAnalysisGoalsViewModel.EarlyLactation=Inizio lattazione
HerdAnalysisGoalsViewModel.FarOffDry=Far-off Asciutta
HerdAnalysisGoalsViewModel.Fresh=Fresche
HerdAnalysisGoalsViewModel.LateLactation=Fine lattazione
HerdAnalysisGoalsViewModel.MidLactation=Metà lattazione
HerdAnalysisGoalsViewModel.PeakMilk=Picco lattazione
HerdAnalysisGoalsViewModel.PercentChewing=Ruminazioni (%)
HerdAnalysisGoalsViewModel.Title=Analisi mandria
HerdAnalysisGoalsViewModel.To=a
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Ruminazioni
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Analisi mandria
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Analisi mandria
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Obiettivi
HerdAnalysisMasterViewModel.Title=Ruminazioni
HerdAnalysisTableTitle=Analisi punteggio ruminazioni
HerdAnalysisViewModel.AverageChews=Masticazioni medie
HerdAnalysisViewModel.DaysInMilk=Giorni in latte
HerdAnalysisViewModel.Edit=Modifica
HerdAnalysisViewModel.EditLabel=Modifica
HerdAnalysisViewModel.HerdAnalysisTableTitle=Analisi punteggio ruminazioni
HerdAnalysisViewModel.HerdCudChewing=Ruminazioni della mandria (%)
HerdAnalysisViewModel.NoOfCows=Completa la valutazione dei gruppi che hai iniziato
HerdAnalysisViewModel.NumberofChewsPerCud=Numero di masticazioni
HerdAnalysisViewModel.PenNameLabel=Nome del gruppo
HerdAnalysisViewModel.PercentChewing=Ruminazioni (%)
HerdAnalysisViewModel.TableTitle=Analisi punteggio ruminazioni
HerdAverage=Media mandria (%)
HerdGoal=Obiettivo mandria (%)
HerdInformation=Informazioni mandria
HerdReporting=Report mandria\: Ruminazioni
Hidalgo=Hidalgo
Himachal_Pradesh=Himachal Pradesh
Hokkaido=Hokkaido
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=Santa Sede (Stato della città del Vaticano)
HomeViewModel.AutoSync=Auto Sync
HomeViewModel.ConsumersTab=Utenti
HomeViewModel.CustomersTab=Clienti
HomeViewModel.DashboardTab=Home
HomeViewModel.Eula=Licenza
HomeViewModel.Logout=Logout
HomeViewModel.PrivacyStatement=Informativa sulla privacy
HomeViewModel.ProspectsTab=Potenziali
HomeViewModel.Settings=Impostazioni
HomeViewModel.SyncWithDash=Sincronizza -
HomeViewModel.SyncWithDate=Sincronizza - Ultima sincronizzazione\: {0\:MM/gg/aa}
HomeViewModel.SyncWithTime=SSincronizza - Ultima sincronizzazione\: {0\:MM/gg/aa}    
HomeViewModel.Title=Strumento di vendita in campo
Honduras=Honduras
Hong_Kong=Hong Kong
HowtoGetBetterKPResults=Come ottenere migliori risultati KP
Hubei=Hubei
Hunan=Se stesso
Hungary=Ungheria
I=dati verranno aggiornati automaticamente.
IDR=Indonesia (Rp IDR)
INR=India (INR INR)
Iceland=Islanda
Idaho=Idaho
Illinois=Illinois
Imperia=Imperia
Imperial=Imperiale
Improvements=Da migliorare
India=India
Indiana=Indiana
Indonesia=Indonesia
InoculantFQAs=FAQ Inoculi
Iowa=Iowa
Iran,_Islamic_Republic_of=Iran (Repubblica Islamica del
Iraq=Iraq
Ireland=Irlanda
Isernia=Isernia
Isle_of_Man=Isola di Man
Israel=Israele
Italy=Italia
JOD=JOD
JPY=JPY
Jalisco=Jalisco
Jamaica=Giamaica
Jammu_and_Kashmir=Jammu e Kashmir
Japan=Giappone
Jersey=Jersey
Jharkhand=Jharkhand
Jiangsu=Jiangsu
Jiangxi=Jiangxi
Jilin=Jilin
Jordan=Giordania
KBLastPhase=Monitoraggio accurato di crescita e salute
KRW=Corea del Sud (₩ KRW)
Kansas=Kansas
Karnataka=Karnataka
Kazakhstan=Kazakistan
Kentucky=Kentucky
Kenya=Kenya
Kerala=Kerala
Kerry=Kerry
Ketosis=Chetosi
KeyBenchmarks=Punti chiave
KeyBenchmarks_AgeInMonthAtFirstCalving=Qual è l'età al primo parto?
KeyBenchmarks_CalvingAndHeiferReocrd=Il numero di manze e i parti sono registrati?
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=Si raggiunge il 15% del peso adulto a 90 giorni?
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=Si raggiunge il 55% del peso adulto alla gravidanza?
KeyBenchmarks_HeiferPeakProduce=Qual è la % di latte delle manze al picco rispetto la media della mandria?
KeyBenchmarks_NintyDaysMorbidityf=Qual è il tasso di malattia nei primi 90 giorni?
KeyBenchmarks_NintyDaysMortality=Qual è il tasso di mortalità nei primi 90 giorni?
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=Si raggiunge il 94% del peso adulto prima del parto?
KeyBenchmarks_PercentOfHeifersPregnant=Quante sono le manze gravide a 15 mesi?
KeyBenchmarks_SerumlgG=Qual è il contenuto di siero IgG (g/l) a 48h?
Kildare=Kildare
Kilkenny=Kilkenny
Kiribati=Kiribati
Korea=Corea
Korea,_Democratic_People's_Republic_of=Corea, Repubblica popolare democratica
Korea,_Republic_of=Corea, Repubblica
Kuwait=Kuwait
Kyrgyzstan=Kirghizistan
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=La Spezia
Lactating=Lattazione
Lactation=Lattazione
Lakshadweep=Lakshadweep
Lao_People's_Democratic_Republic=Repubblica democratica del popolo di Lao
Laois=Laois
Last_Synced=Sincronizzato il\:
LateLactation=Fine lattazione
Latina=Latina
Latvia=Lettonia
Lebanon=Libano
Lecce=Lecce
Lecco=Lecco
Leitrim=Leitrim
Lely=Lely
Length-exceed-allowed-limit=Lunghezza superiore al limite consentito
LengthPerDayImperial=In/giorno
LengthPerDayMetric=cm/giorno
Lesotho=Lesotho
LessThan4Days=&lt;4 giorni
LessThanFifteen=&lt;15
LessThanFiveWholeKernals=&lt;5 grani interi
LessThanOneHour=&lt;1 ora
LessThanSixInches=&lt;7.5 cm
LessThanSixLayers=&lt;6 strati
LessThanTwentFourInchesPerDay=&lt;15 cm/giorno
Liaoning=Liaoning
Liberia=Liberia
Libyan_Arab_Jamahiriya=Jamahiriya arabo libico
Liechtenstein=Liechtenstein
Lift-Sync-Fail=Questi sono gli ultimi per ora.
Limerick=filastrocca
LinkToPens=Collegare ai gruppi (opzionale)
Lithuania=Lituania
Livorno=Livorno
LocoCategory1=Cat.
LocoCategory2=Cat.
LocoCategory3=Cat.
LocoCategory4=Cat.
LocoCategory5=Cat.
LocomotionEditTableViewModel.Category1=Locomotion Score 1.0
LocomotionEditTableViewModel.Category2=Locomotion Score 2.0
LocomotionEditTableViewModel.Category3=Locomotion Score 3.0
LocomotionEditTableViewModel.Category4=Locomotion Score 4.0
LocomotionEditTableViewModel.Category5=Locomotion Score 5.0
LocomotionEditTableViewModel.EnterNumberOfCows=Conta il numero di vacche
LocomotionEditTableViewModel.Title=Numero di vacche
LocomotionHerdEditGoalViewModel.Category1=Locomotion Score 1.0
LocomotionHerdEditGoalViewModel.Category2=Locomotion Score 2.0
LocomotionHerdEditGoalViewModel.Category3=Locomotion Score 3.0
LocomotionHerdEditGoalViewModel.Category4=Locomotion Score 4.0
LocomotionHerdEditGoalViewModel.Category5=Locomotion Score 5.0
LocomotionHerdEditGoalViewModel.HerdGoal=Obiettivo della mandria
LocomotionHerdEditGoalViewModel.Title=Modifica Obiettivo
LocomotionHerdInputsViewModel.Herd=Mandria
LocomotionHerdMasterViewModel.Inputs=Input
LocomotionHerdMasterViewModel.Results=Risultati
LocomotionHerdMasterViewModel.Revenue=Ricavi
LocomotionHerdMasterViewModel.SubHeading=Analisi mandria
LocomotionHerdMasterViewModel.Title=Locomotion Score
LocomotionHerdResultsViewModel.Category1=LS 1.0
LocomotionHerdResultsViewModel.Category2=LS 2.0
LocomotionHerdResultsViewModel.Category3=LS 3.0
LocomotionHerdResultsViewModel.Category4=LS 4.0
LocomotionHerdResultsViewModel.Category5=LS 5.0
LocomotionHerdResultsViewModel.HerdAverage=Media mandria
LocomotionHerdResultsViewModel.HerdGoal=Obiettivi
LocomotionHerdResultsViewModel.Title=Analisi Locomotion Score
LocomotionHerdRevenueViewModel.Revenue=Ricavi
LocomotionHerdRevenueViewModel.Title=Ricavo della mandria - Locomotion
LocomotionNumberinHerd=Locomotion (\# nella mandria)
LocomotionNumberinPen=Locomotion (\# nel gruppo)
LocomotionPenInputsViewModel.FromPenSetup=Da impostazioni di gruppo
LocomotionPenInputsViewModel.Milk=Latte
LocomotionPenMasterViewModel.Inputs=Input
LocomotionPenMasterViewModel.Results=Risultati
LocomotionPenMasterViewModel.Title=Locomotion
LocomotionPercentofHerd=Locomotion (% mandria)
LocomotionPercentofPen=Locomotion (% gruppo)
LocomotionPreviousVisitsViewModel.AverageScore=Punteggio medio
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Punteggio medio
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Data
LocomotionPreviousVisitsViewModel.PercentPen=% del gruppo
LocomotionPreviousVisitsViewModel.SelectedDates=Scegliere date
LocomotionPreviousVisitsViewModel.Title=Locomotion Score - Risultati
LocomotionScore=Punteggio locomozione
LocomotionScoreAverage=Media Locomotion Score
LocomotionScoreHerd=Analisi Locomotion Score
LocomotionScoreReference=Riferimento Locomotion Score
LocomotionSelectPenViewModel.MissingDiet=Prego inserire una razione valida per questo gruppo
LocomotionSelectPenViewModel.PenSelectionList=Gruppi
LocomotionSelectPenViewModel.Title=Locomotion
Lodi=Contrario
LoginViewModel.Copyright=© {0} Cargill, Incorporated. Tutti i diritti riservati
LoginViewModel.EmailLabel=Email
LoginViewModel.ErrorDescription=Username e password non possono essere vuoti
LoginViewModel.ErrorTitle=Errore di Login
LoginViewModel.InvalidMessage=Username e password non validi per l'accesso
LoginViewModel.InvalidMessageTitle=Accesso non valido
LoginViewModel.LoginPrompt=Cargill
LoginViewModel.LoginPromptConsumer=Esterni
LoginViewModel.NetworkErrorMessage=Nessuna connessione disponibile
LoginViewModel.NetworkErrorMessageTitle=Errore di connessione
LoginViewModel.PasswordLabel=Password
LoginViewModel.Title=Accedi
LoginViewModel.Unauthorized=Accesso non autorizzato
LoginViewModel.UnauthorizedTitle=Non autorizzato
Longford=Longford
Louisiana=Louisiana
Louth=Louth
LowForage=Poco foraggio
Lucca=Lucca
Luxembourg=Lussemburgo
MEQ100G=mEq/100g
MKD=MKD
MUN=MUN (mg/dl)
MXN=Messico (PESO MXN)
MYR=Malesia (MYR MYR)
Macao=Macao
Macedonia,_the_former_Yugoslav_Republic_of=Macedonia, ex repubblica jugoslava di
Macerata=Macerata
Madagascar=Madagascar
Madhya_Pradesh=Madhya Pradesh
Maharashtra=Maharashtra
MainViewModel.EmailLabel=Email
Maine=Maine
MaintainingForageQuality=Mantenere la qualità del foraggio
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Sono utilizzati stabilizzatori o inibitori di muffa nel TMR in condizioni caldo-umide?
MaintainingForageQuality_TMRMixHasPleasantAroma=Il TMR ha un odore gradevole?
MaintainingForageQuality_TMRMixIsCoolToTouch=Il TMR è fresco al tatto?
Making_Feed_InventoryFOF=Come fare un inventario
Malawi=Fiamma
Malaysia=Malesia
Maldives=Maldive
Male=Maschio
Mali=Doveva
Malta=Malta
ManagingForageinSiloBags=Gestione del foraggio in silobag 
ManagingForageinTowerSilos=Gestione del foraggio nei silo a torre
Manipur=Manipur
Manitoba=Manitoba
Mantua=Mantova
ManureEditScores=Manure Score - Modifica punteggi
ManureScoreHerdAnalysisEditInputsViewModel.Close=Chiudi
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Giorni in latte (DIM)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Modifica DIM
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Manure Score
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Analisi Manure Score
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Giorni in latte (DIM)
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Modifica
ManureScoreHerdAnalysisMasterViewModel.Goals=Obiettivi
ManureScoreHerdAnalysisMasterViewModel.Inputs=Input
ManureScoreHerdAnalysisMasterViewModel.Results=Risultati
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Analisi mandria
ManureScoreHerdAnalysisMasterViewModel.Title=Manure Score
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Analisi Manure Score
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Manure Score
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Media
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Punteggio Max
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Punteggio Min
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Close-up Asciutta (da -20 a -1)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Inizio lattazione (da 16 a 60 DIM)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Chiudi
ManureScoreHerdEditGoalsViewModel.FarOffDry=Far-off Asciutta (da -60 a -21)
ManureScoreHerdEditGoalsViewModel.Fresh=Post-parto (da 0 a 15 DIM)
ManureScoreHerdEditGoalsViewModel.LateLactation=Fine lattazione (&gt;201 DIM)
ManureScoreHerdEditGoalsViewModel.MaxGoal=Manure Score - Max
ManureScoreHerdEditGoalsViewModel.MidLactation=Metà lattazione (da 121 a 200 DIM)
ManureScoreHerdEditGoalsViewModel.MinGoal=Manure Score - Min
ManureScoreHerdEditGoalsViewModel.PeakMilk=Picco lattazione (da 61 a 120 DIM)
ManureScoreHerdEditGoalsViewModel.Title=Modifica Obiettivi
ManureScoreHerdGoalsViewModel.CloseUpDry=Close-up Asciutta (da -20 a -1)
ManureScoreHerdGoalsViewModel.EarlyLactation=Inizio lattazione (da 16 a 60 DIM)
ManureScoreHerdGoalsViewModel.Edit=Modifica
ManureScoreHerdGoalsViewModel.FarOffDry=Far-off Asciutta (da -60 a -21)
ManureScoreHerdGoalsViewModel.Fresh=Post-parto (da 0 a 15 DIM)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Obiettivo Max
ManureScoreHerdGoalsViewModel.GoalMinTitle=Obiettivo Min
ManureScoreHerdGoalsViewModel.LateLactation=Fine lattazione (&gt;201 DIM)
ManureScoreHerdGoalsViewModel.MidLactation=Metà lattazione (da 121 a 200 DIM)
ManureScoreHerdGoalsViewModel.PeakMilk=Picco lattazione (da 61 a 120 DIM)
ManureScoreHerdGoalsViewModel.TableTitle=Punteggio per fase di lattazione
ManureScorePenSelectionViewModel.ManureScoreTitle=Manure Score
ManureScorePenSelectionViewModel.PenSelectionList=Gruppi
ManureScorePercentOfPen=Manure Score (% del gruppo)
ManureScoresChart=Manure Score - Grafici
ManureScoresResult=Manure Score - Risultati
Maranhão=Maranhã £ o
Martinique=Martinica
Maryland=Maryland
Massa_and_Carrara=Massa e Carrara
Massachusetts=Massachusetts
Matera=Matera
Mato_Grosso=Mato Grosso
Mato_Grosso_do_Sul=Mato Grosso do Sul
Mauritania=Mauritania
Mauritius=Mauritius
Max=MAX
Mayo=Mayo
Mayotte=Mayotte
Mcal=Mcal
Meath=Meath
Medio_Campidano=Medio Campidano
Medium=Medium
Meghalaya=Meghalaya
MenuViewModel.Close=Chiudi
MenuViewModel.LogoutPrompt=Logout
MenuViewModel.Menu=Menù
MenuViewModel.ResetDatabaseCancel=Cancella
MenuViewModel.ResetDatabasePrompt=Questo sostituirà tutti i dati esistenti (preferenze utente incluse), con dati di defalut. Strumenti, visite e caseificio verranno cancellati. Verrà inoltre eseguito il logout dall'App. Continuare?
MenuViewModel.ResetDatabaseReset=Ripristina
MenuViewModel.ResetDatabaseTitle=Ripristina i dati
MenuViewModel.Sync_PopUp=Sincronizzazione in corso… Non chiudere l'applicazione. In caso di connessione lenta il processo richiederà più tempo
Messina=Messina
MetabolicIncidenceChartsViewModel.Current=Attuale
MetabolicIncidenceChartsViewModel.DeathLoss=Morte
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Costo problemi metabolici/Vacca
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Dislocazione abomaso
MetabolicIncidenceChartsViewModel.Dystocia=Distocia
MetabolicIncidenceChartsViewModel.GoalPercent=Obiettivo (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Incidenza problemi metabolici (%)
MetabolicIncidenceChartsViewModel.IncidencePercent=Incidenza (%)
MetabolicIncidenceChartsViewModel.Ketosis=Chetosi
MetabolicIncidenceChartsViewModel.Metritis=Metrite
MetabolicIncidenceChartsViewModel.MilkFever=Collasso puerperale
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Ritenzione placentare
MetabolicIncidenceChartsViewModel.Title=Grafici Malattie metaboliche
MetabolicIncidenceEditOutputsViewModel.Close=Chiudi
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Morte
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Dislocazione abomaso
MetabolicIncidenceEditOutputsViewModel.Dystocia=Distocia
MetabolicIncidenceEditOutputsViewModel.Ketosis=Chetosi
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Obiettivi (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Metrite
MetabolicIncidenceEditOutputsViewModel.MilkFever=Collasso puerperale
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Ritenzione placentare
MetabolicIncidenceEditOutputsViewModel.Title=Modifica obiettivi
MetabolicIncidenceInputsEditViewModel.Close=Chiudi
MetabolicIncidenceInputsEditViewModel.DeathLoss=Mastiti
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Dislocazione abomaso
MetabolicIncidenceInputsEditViewModel.Dystocia=Distocia
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Giorni aperti
MetabolicIncidenceInputsEditViewModel.Ketosis=Chetosi
MetabolicIncidenceInputsEditViewModel.Metritis=Metrite
MetabolicIncidenceInputsEditViewModel.MilkCow=Latte/vacca ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Collasso puerperale
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Ritenzione placentare
MetabolicIncidenceInputsEditViewModel.Title=Modifica attributi di costo
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Trattamenti e altri costi (€)
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Extra per giorni aperti
MetabolicIncidenceInputsViewModel.Costs=Costi
MetabolicIncidenceInputsViewModel.DeathLoss=Morte
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Dislocazione abomaso
MetabolicIncidenceInputsViewModel.Dystocia=Distocia
MetabolicIncidenceInputsViewModel.Herd=Info mandria
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Inserire il numero di vacche fresche e i casi di malattie metaboliche durante la valutazione. I valori saranno trasformati in costi annui per incidenza di malattie metaboliche nella tabella Risultati
MetabolicIncidenceInputsViewModel.IncidenceCases=Eventi di problemi metabolici
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Aumento giorni aperti
MetabolicIncidenceInputsViewModel.Ketosis=Chetosi
MetabolicIncidenceInputsViewModel.Mastitis=Mastiti
MetabolicIncidenceInputsViewModel.Metritis=Metrite
MetabolicIncidenceInputsViewModel.MilkFever=Collasso puerperale
MetabolicIncidenceInputsViewModel.MilkLossKg=Perdita latte per lattazione ({0})
MetabolicIncidenceInputsViewModel.MilkPrice=Prezzo del latte
MetabolicIncidenceInputsViewModel.PerformanceMessage=Informazione utilizzata per calcolare l'impatto economico di ogni malattia metabolica
MetabolicIncidenceInputsViewModel.PerformanceTreatment=Costi per prestazioni e trattamenti
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Costo riforma
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Ritenzione placentare
MetabolicIncidenceInputsViewModel.Title=Malatte metaboliche - Input
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Totale vacche fresche da valutare
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Totale vacche fresche/anno
MetabolicIncidenceInputsViewModel.TreatmentCost=Trattamenti e altri costi (€)
MetabolicIncidenceMasterViewModel.Charts=Grafici
MetabolicIncidenceMasterViewModel.Inputs=Input
MetabolicIncidenceMasterViewModel.Outputs=Risultati
MetabolicIncidenceMasterViewModel.Title=Malattie metaboliche
MetabolicIncidenceOutputsViewModel.DeathLoss=Morte
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Dislocazione abomaso
MetabolicIncidenceOutputsViewModel.Dystocia=Distocia
MetabolicIncidenceOutputsViewModel.Ketosis=Chetosi
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Incidenza (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Costo/vacca
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Aumento giorni aperti
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Differenza (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Modifica
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Obiettivi (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Impatto economico delle malattie metaboliche oltre gli obiettivi definiti per la mandria
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Impatto economico annuo
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Impatto economico annuo - Totale
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Valore perdita latte
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Malattie metaboliche (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Costi totali
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Trattamenti e altri costi (€)
MetabolicIncidenceOutputsViewModel.Metritis=Metrite
MetabolicIncidenceOutputsViewModel.MilkFever=Collasso puerperale
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Ritenzione placentare
MetabolicIncidenceOutputsViewModel.Title=Malatte metaboliche - Risultati
MetabolicIncidenceOutputsViewModel.TotalLosses=Totale perdite annuali
Metric=Metrico
MetricTonsAF=Tons t.q. metriche
MetricTonsAFSilo=Tons s.s. metriche residue
MetricTonsDM=Tons s.s. metriche
MetricTonsDMSilo=Tons s.s. metriche residue
Metritis=Metrite
Mexico=Messico
Mexico_State=Stato del Messico
Michigan=MICHIGAN
Michoacán=Michoacán
MidLactation=Metà lattazione
MidOne=Strato B
MidOneValue=(8mm)
MidTwo=Strato C
Milan=Milano
Milk=Latte ({0})
MilkChange=Cambiamento nella produzione ({0})
MilkFever=Collasso puerperale
MilkLetDownResponse=Risposta rilascio del latte
MilkLossDay=Perdita latte ({0}/giorno)
MilkLossGain=Potenziale incremento/perdita di latte
MilkLossKg=Perdita latte (kg)
MilkLossPounds=Perdita latte (lbs)
MilkLossYear=Perdita latte ({0}/anno)
MilkPrice=Prezzo attuale del latte ({0}/{1})
MilkPricePremiums=Premi sul prezzo del latte
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=Grafici di riferimento
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Input
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Risorse
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Risultati
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Ricavi di mungitura
MilkProcessRevenueCalculatorMasterViewModel.Title=Ricavi di mungitura
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Notebook
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Stimolazione inadeguata
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Prezzo del latte ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=Nessuna stimolazione
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Stimulazione ottimale
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Scenario 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Scenario 2
MilkProcessorEditComparisonValuesViewModel.Title=Modifica valori di confronto
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=Q.le
MilkProcessorEditComparisonValuesViewModel.WeightMetric=Kg
MilkProcessorInputViewModel.ComparisonValues=Valori di confronto
MilkProcessorInputViewModel.Edit=Modifica
MilkProcessorInputViewModel.MilkPrice=Prezzo del latte ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=Il Caseificio scelto è stato eliminato. Sceglierne un altro per continuare
MilkProcessorInputViewModel.ScenarioOne=Scenario 1
MilkProcessorInputViewModel.ScenarioTwo=Scenario 2
MilkProcessorInputViewModel.SelectProcessor=Scegliere Caseificio
MilkProcessorInputViewModel.Title=Ricavi di mungitura - Input
MilkProcessorInputViewModel.WeightImperialCWT=Q.le
MilkProcessorInputViewModel.WeightMetric=Kg
MilkProcessorResourcesViewModel.ApproxSCC=CCS approx. (cell/ml)
MilkProcessorResourcesViewModel.LinearScore=Punteggio lineare
MilkProcessorResourcesViewModel.Mastitis=Perdita per mastiti ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Grafici di riferimento
MilkProcessorResourcesViewModel.Title=Ricavi di mungitura - Risorse
MilkProcessorResultsViewModel.Change=Cambiare
MilkProcessorResultsViewModel.HundredWeight=Q.le
MilkProcessorResultsViewModel.MilkPrice=Prezzo del latte ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Cambio valori annuali
MilkProcessorResultsViewModel.ScenarioOne=Scenario 1
MilkProcessorResultsViewModel.ScenarioTwo=Scenario 2
MilkProcessorResultsViewModel.Title=Ricavi di mungitura - Risultati
MilkProcessorResultsViewModel.WeightImperialCWT=Q.le
MilkProcessorResultsViewModel.WeightMetric=Kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Componente
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Concentrazione
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Componente
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Concentrazione
MilkProcessorSettingsMasterViewModel.MilkProcNew=Nuovo
MilkProcessorSettingsMasterViewModel.Title=Imposta Caseificio
MilkProcessorViewModel.Amount=Q.tà (1.000 cell/ml)
MilkProcessorViewModel.AmountCFU=Q.tà (1.000 CFU/ml)
MilkProcessorViewModel.BasePriceMilkFat=Grasso ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Latte ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Proteine ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Altri solidi ({0}/{1})
MilkProcessorViewModel.BasePrices=Prezzo base
MilkProcessorViewModel.ComponentProcessor=Caseificio - Componente
MilkProcessorViewModel.ConcentrationProcessor=Caseificio - Concentrazione
MilkProcessorViewModel.Delete=Cancella
MilkProcessorViewModel.DeletePrompt=L'eliminazione di questo Caseificio impedirà il calcolo dei ricavi di mungitura. Desideri procedere?
MilkProcessorViewModel.HundredWeight=Q.le
MilkProcessorViewModel.Name=Nome
MilkProcessorViewModel.NameNotUnique="{0}" già esistente. Scegliere un altro nome
MilkProcessorViewModel.NewComponentProcessorName=Caseificio \#{0} - Componente
MilkProcessorViewModel.NewConcentrationProcessorName=Caseificio \#{0} - Concentrazione
MilkProcessorViewModel.PricingMatrices=Matrici di prezzo
MilkProcessorViewModel.SelectCurrency=Scegli una valuta
MilkProcessorViewModel.WeightImperial=Lbs
MilkProcessorViewModel.WeightImperialCWT=Q.le
MilkProcessorViewModel.WeightMetric=Kg
MilkProduction=Produzione latte ({0})
MilkProductionKg=Produzione latte (kg)
MilkProductionPounds=Produzione latte (lbs)
MilkProductionRevenue=Ricavi sul latte
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Resa titoli ed Efficienza
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=Ingestione sostanza secca ed efficienza alimentare
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=Grasso (%) e Proteine (%)
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Produzione di latte e giorni in lattazione
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=CCS e Urea nel latte
MilkSoldEvaluationChartsListViewModel.VisitComparison=Scegli visite da confrontare
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Efficienza componenti
MilkSoldEvaluationChartsViewModel.ComponentYield=Resa titoli
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Resa ed Efficienza componenti
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=Ingestione s.s. e Feed Efficiency
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Giorni in latte
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Ingestione s.s.
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Efficienza Alimentare
MilkSoldEvaluationChartsViewModel.MilkFat=Grasso (%)
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=Grasso (%) e Proteine (%)
MilkSoldEvaluationChartsViewModel.MilkProduction=Produzione di latte
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Produzione di latte e DIM
MilkSoldEvaluationChartsViewModel.MilkProtein=Proteine (%)
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Urea nel latte
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Conta Cellule Somatiche
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=CCS e Urea
MilkSoldEvaluationChartsViewModel.Title=Valutazione latte venduto
MilkSoldEvaluationInputsViewModel.AddPickup=Aggiungi prelievo
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Animali nel tank ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Giorni in latte (DIM) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Ingestione s.s. ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=Informazioni sulla mandria
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Animali in mungitura ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Prelievo del latte ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Informazioni Caseificio
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Urea ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Aggiungi nuovo
MilkSoldEvaluationMasterViewModel.Charts=Grafici
MilkSoldEvaluationMasterViewModel.Inputs=Input
MilkSoldEvaluationMasterViewModel.Outputs=Risultati
MilkSoldEvaluationMasterViewModel.Title=Valutazione latte venduto
MilkSoldEvaluationOutputsViewModel.AvgBCC=Media CBT (1.000 CFU/ml)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Media grasso (%) 
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Media latte prodotto, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Media latte prodotto, {0} (animali nel tank)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Media proteine (%) 
MilkSoldEvaluationOutputsViewModel.AvgSCC=Media CCS (1.000 cells/ml)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Efficienza componenti (% s.s.i.)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Giorni di valutazione
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Feed Efficiency (rapporto)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Resa grasso + proteine ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Resa grasso ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Resa proteine ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Aggiorna impostazioni del sito
MilkSoldPickupViewModel.AnimalsInTank=Animali nel tank ⃰
MilkSoldPickupViewModel.BCC=CBT (1.000 CFU/ml)
MilkSoldPickupViewModel.DaysInTank=Giorni nel tank ⃰
MilkSoldPickupViewModel.MilkFat=Grasso (%)
MilkSoldPickupViewModel.MilkProtein=Proteine (%)
MilkSoldPickupViewModel.MilkSold=Latte venduto, {0} ⃰
MilkSoldPickupViewModel.SCC=CCS (1.000 cells/ml)
MilkSoldPickupViewModel.Title=Modifica prelievo {0}
MilkSoldSpinnerViewModel.Title=Valutazione latte venduto
MilkUrea=Urea (mg/dl)
Milking=Mungitura
MilkingFailure=Fallimenti di mungitura
MilkingFirst=Milk first
MilkingProcessRevenueInputs=Ricavi di mungitura - Input
MilkingProcessRevenueResources=Ricavi di mungitura - Risorse
MilkingProcessRevenueResults=Ricavi di mungitura - Risultati
Minas_Gerais=Minas Gerais
Minnesota=Minnesota
Mississippi=Mississippi
Missouri=Missouri
Mizoram=Mizoram
Modena=Modena
Moderate=Moderate
ModeratelyClean=Moderatamente pulito
Moldova,_Republic_of=Moldavia, Repubblica
Monaco=Monaco
Monaghan=Monaghan
Mongolia=Mongolia
Montana=Montana
Montenegro=Montenegro
Monthly=Ogni mese
Montserrat=Montserrat
Monza_and_Brianza=Monza and Brianza
MoreThan8LayersOfPlastic=&gt;8 strati di plastica
Morelos=Morelos
Morocco=Marocco
Mozambique=Mozambico
Myanmar=Myanmar
NGN=NGN
NIO=Nicaragua (NIO NIO)
NOK=NOK
Nagaland=Nagaland
Namibia=Namibia
Naples=Napoli
Nauru=Nauru
Nayarit=Nayarit
Nebraska=Nebraska
Nei_Mongol=Nei Mongol
Nepal=Nepal
Netherlands=Olanda
Nevada=Nevada
NewBunker=Prego dare un nome alla nuova trincea
NewDietClassViewModel.Title=Classe/Sottoclasse animale
NewDietPensViewModel.AssociatePens=Puoi associare questa dieta a diversi gruppi
NewDietPensViewModel.Title=Collegare a gruppo
NewDietViewModel.Cancel=Cancella
NewDietViewModel.MainHeading=Nome della razione ⃰
NewDietViewModel.Save=Salva
NewDietViewModel.Title=Nuova razione
NewPenDietViewModel.New=Nuovo
NewPenDietViewModel.Title=Razione
NewPenViewModel.Animals=Animali nel gruppo
NewPenViewModel.AnimalsInputsPen=Animali
NewPenViewModel.AsFedIntake=Ingestione t.q. ({0})
NewPenViewModel.Barn=Nome della struttura
NewPenViewModel.Cancel=Cancella
NewPenViewModel.DaysInMilk=Giorni in latte (DIM)
NewPenViewModel.Diet=Razione
NewPenViewModel.DietInputsPen=Razione
NewPenViewModel.DryMatterIntake=Ingestione s.s. ({0})
NewPenViewModel.FeedingSystem=Sistema di alimentazione
NewPenViewModel.General=Info gruppo
NewPenViewModel.HousingSystem=Tipologia stalla
NewPenViewModel.InfoNewPenDetails=Aggiungi o aggiorna i dati del gruppo in questa pagina. Questa operazione è possibile durante l'utilizzo dei singoli strumenti. 
NewPenViewModel.Milk=Produzione latte ({0})
NewPenViewModel.MilkingFrequency=Numero mungiture
NewPenViewModel.NumberOfStalls=Numero posti
NewPenViewModel.OnlyOnePen=SHai solo un gruppo
NewPenViewModel.PenDetail=Dettagli del gruppo
NewPenViewModel.PenMapping=Mappaggio gruppi
NewPenViewModel.PenName=Nome del gruppo
NewPenViewModel.PenSelection=Scegliere gruppo
NewPenViewModel.PublishPenAlert=Prego pubblicare tutte le visite relative al gruppo che vuoi fondere
NewPenViewModel.RationCostPerAnimal=Costo/capo ({0})
NewPenViewModel.Save=Salva
NewPenViewModel.Title=Nuovo gruppo
NewPenViewModel.UserCreatedPen=Gruppo creato dall'utente
NewPile=Dai un nome alla nuova platea
NewProspectViewModel.Address1=Indirizzo 1
NewProspectViewModel.Address2=Indirizzo 2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.BusinessName=Nome dell'azienda
NewProspectViewModel.City=Città
NewProspectViewModel.ConsumerDetails=Dettagli consumatore
NewProspectViewModel.Country=Nazione
NewProspectViewModel.Customer=Cliente
NewProspectViewModel.CustomerDetail=Dettagli cliente
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=Email
NewProspectViewModel.EndUser=Allevatore
NewProspectViewModel.FarmProducer=Allevatore
NewProspectViewModel.Image=Clicca per modificare l'immagine
NewProspectViewModel.InvalidEmail=Si prega di inserire una e-mail valida
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=Il cliente potenziale "{0}" è già presente. I nomi devono essere unici
NewProspectViewModel.NameNotUniqueForConsumer=Il consumatore "{0}" è già presente. I nomi devono essere unici
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=- 
NewProspectViewModel.NullBusinessName=Il nome commerciale è obbligatorio
NewProspectViewModel.NullFirstName=Il nome è obbligatorio
NewProspectViewModel.NullSecondName=Il cognome è obbligatorio
NewProspectViewModel.PostalCode=CAP
NewProspectViewModel.PrimaryContactFirstName=Nome
NewProspectViewModel.PrimaryContactLastName=Cognome
NewProspectViewModel.PrimaryPhone=Telefono
NewProspectViewModel.Prospect=Cliente potenziale
NewProspectViewModel.ProspectDetail=Dettagli cliente potenziale
NewProspectViewModel.Segment=Segmento
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Spence=Spence
NewProspectViewModel.State=Stato
NewProspectViewModel.Title=Dettagli
NewProspectViewModel.Type=Tipo
NewProspectViewModel.Walton=Walton
NewVisitViewModel.Title=Dettagli della visita
New_Brunswick=New Brunswick
New_Caledonia=Nuova Caledonia
New_Hampshire=New Hampshire
New_Jersey=New Jersey
New_Mexico=Nuovo Messico
New_South_Wales=Nuovo Galles del Sud
New_York=New York
New_Zealand=Nuova Zelanda
Newfoundland_and_Labrador=Terranova e Labrador
Next=Seguente
Nicaragua=Nicaragua
Niedersachsen=Bassa Sassonia
Niger=Niger
Nigeria=Nigeria
Ningxia=Ningxia
Niue=Niue
No=No
No-User-Found=Utente non trovato su LIFT; si prega di contattare l'amministratore per la risoluzione
NoResourcesAvailable=Risorsa non disponibile
NoResults=Nessun risultato trovato
NoWholeKernals=Senza granella intera
Noah=Noah
None=Nessuna
NoneSelected=Nessuna scelta
Nordrhein=Reno nord
Norfolk_Island=Norfolk Island
Normal=&lt;0.5 unità BCS 
NorthAmerica=Nord America
North_Carolina=Carolina del Nord
North_Dakota=Nord Dakota
Northern_Territory=Territori del Nord
Northwest_Territories=Territori del Nordovest
Norway=Norvegia
Not-matching-with-allowed-values=Non corrisponde ai valori consentiti
NotMeasured=Non misurato
NotRemoved=Non rimosso
NotSet=-
NoteCamcorderNotImplemented=Videocamera non ancora implementata
NoteCategoryViewModel.FooterText=Soltanto una categoria può essere scelta per le annotazioni
NoteCategoryViewModel.SelectCategory=Seleziona una categoria
NoteCategoryViewModel.Title=Note
NotebookBCSHerdAnalysisGoals=BCS - Obiettivi analisi mandria
NotebookBCSHerdAnalysisInputs=BCS - Input analisi mandria
NotebookBCSHerdAnalysisResults=BCS - Risultati analisi mandria
NotebookBCSSelectPointScale=BCS - Scegli la scala
NotebookBodyConditionEdit=Modifica tabella BCS
NotebookCudCalculators=Salute ruminale - Contatore
NotebookCudChewing=Salute ruminale - Ruminazione - Seleziona gruppo
NotebookCudChewingDataEntry=Salute ruminale - Input ruminazioni
NotebookCudChewingResults=Salute ruminale - Risultati ruminazioni
NotebookLocomotionEditTable=Locomotion - Numero di vacche
NotebookLocomotionHerdInputs=Locomotion - Input analisi mandria
NotebookLocomotionHerdResults=Locomotion - Risultati analisi mandria
NotebookLocomotionHerdRevenue=Locomotion - Ricavi analisi mandria
NotebookLocomotionLanding=Locomotion - Generale
NotebookLocomotionPenInputs=Locomotion - Input gruppo
NotebookLocomotionPenResults=Locomotion - Risultati gruppo
NotebookLocomotionPenSelection=Locomotion - Scelta gruppo
NotebookManurePenSelection=Manure Score - Scelta gruppo
NotebookManureScoreHerdAnalysisGoals=Manure Score - Obiettivi
NotebookManureScoreHerdAnalysisInputs=Manure Score - Input
NotebookManureScoreHerdAnalysisResults=Manure Score - Risultati
NotebookManureScoreLanding=Punteggio feci
NotebookMetabolicIncidenceCharts=Grafici malattie metaboliche
NotebookMetabolicIncidenceInputs=Malattie metaboliche - Input
NotebookMetabolicIncidenceOutputs=Malattie metaboliche - Risultati
NotebookParticleScoreHerdAnalysisEdit=TMR Particle Score - Analisi mandria - Modifica DIM
NotebookParticleScoreLanding=TMR Particle Score
NotebookParticleScoreSelectPen=TMR Particle Score - Scelta del gruppo
NotebookParticleScoreSelectScorer=TMR Particle Score - Scelta setaccio
NotebookPenTimeComparison=Pen Time Budget - Confronto
NotebookPenTimeInputs=Pen Time Budget - Input
NotebookPenTimePenSelection=Pen Time Budget - Scegliere gruppo
NotebookPenTimeResults=Pen Time Budget - Risultati
NotebookReadyToMilkCharts=Ready2Milk™ - Grafici
NotebookReadyToMilkInputs=Ready2Milk™ - Input
NotebookReadyToMilkOutputs=Ready2Milk™ - Risultati
NotebookRumenHealthNumberOfChewsInput=Salute ruminale - Input masticazioni
NotebookRumenHealthNumberOfChewsResults=Salute ruminale - Risultati masticazioni
NotebookRumenHealthTMRParticlePercent=% TMR per strato
NotebookRumenHealthTMRParticleScore=TMR Particle Score
NotebookSectionComfortTools=Comfort
NotebookSectionForageAudit=Segnapunti Audit foraggi
NotebookSectionForageAuditBaleage=Audit foraggi - Fasciati
NotebookSectionForageAuditBunkersPiles=Audit foraggi - Trincee e Platee
NotebookSectionForageAuditHarvest=Audit foraggi - Raccolta
NotebookSectionForageAuditLanding=Audit foraggi
NotebookSectionForageAuditMaintainingQuality=Audit foraggi - Mantenere qualità foraggi
NotebookSectionForageAuditSilageBags=Audit foraggi - Silobag
NotebookSectionForageAuditSurveyOfForages=Audit foraggi - Sondaggio foraggi
NotebookSectionForageAuditTowerSilos=Audit foraggi - Silo a torre
NotebookSectionHealthTools=Salute
NotebookSectionHeatstressCalculations=Stress da caldo - Calcolo
NotebookSectionHeatstressChart=Stress da caldo - Grafici
NotebookSectionHeatstressData=Stress da caldo - Dati
NotebookSectionHerdAnalysisGoals=Salute ruminale - Analisi obiettivi mandria
NotebookSectionMilkSoldEvaluationCharts=Latte venduto - Grafici
NotebookSectionMilkSoldEvaluationEditPickup=Latte venduto - Modifica prelievo
NotebookSectionMilkSoldEvaluationInputs=Latte venduto - Input
NotebookSectionMilkSoldEvaluationOutputs=Latte venduto - Risultati
NotebookSectionNutritionTools=Nutrizione
NotebookSectionRumenHealthLanding=Salute ruminale
NotebookSectionVisit=Visita
NotebookTMRParticleHerdAnalysisPenInputs=TMR Particle Score - Analisi mandria - Input
NotebookTMRParticleHerdAnalysisPenResults=TMR Particle Score - Analisi mandria - Risultati
NotebookUrinePHEditGoals=pH urine - Modifica obiettivi
NotebookUrinePHInputs=pH urine - Input
NotebookUrinePHOutputs=pH urine - Risultati
NotebookVisitSummary=Riepilogo della visita
NotebookWalkthroughReport=Visita guidata
NotebookWalkthroughReportLanding=Visita guidata
NotebookWalkthroughReportPen=Visita guidata - Analisi di gruppo
Nova_Scotia=Nuova Scozia
Novara=Novara
Nuevo_León=Nuovo Leín
Null-values-not-allowed=Valori nulli non consentiti
NumChewsGoal={0} Obiettivo
NumOfCows=Numero vacche
NumberOfChewsReportsViewModel.Average=Media \# masticazioni
NumberOfChewsReportsViewModel.AverageChews=Media ruminazioni
NumberOfChewsReportsViewModel.AverageNumberChews=Media \# masticazioni
NumberOfChewsReportsViewModel.DateDescription=Data
NumberOfChewsReportsViewModel.DatesComparison=Date per confronto
NumberOfChewsReportsViewModel.EditVisits=Selezionare
NumberOfChewsReportsViewModel.StdDevCalculated=Dev. Std. (calcolata)
NumberOfChewsReportsViewModel.VisitDate=Data
NumberOfChewsViewModel.Count=Conteggio
NumberOfChewsViewModel.CountHeader=Conta il numero di masticazioni di questa vacca
NumberOfChewsViewModel.NextCow=Vacca seguente
NumberOfChewsViewModel.NumberOfChewsCow=Masticazioni/vacca \#
NumberOfChewsViewModel.Title=Masticazioni/vacca \# {0}
NumberOfChewsViewModel.ValidCudInput=Immetere un input valido
NumberOfCows=Numero vacche
Nunavut=Nunavut
Nuoro=Nuoro
NutritionViewModel.NutritionForage=Audit foraggi
NutritionViewModel.NutritionLabel=Scegli una voce della lista sottostante per iniziare la visita
NutritionViewModel.NutritionPile=Capienza trincee e mucchi
NutritionViewModel.NutritionTools=Nutrizione
NutritionViewModel.NutritionToolsCaption=Strumenti
NutritionViewModel.NutritionToolsInstructions=Scegli una voce della lista sottostante per iniziare la visita
NutritionViewModel.NutritionToolsList=Strumenti
NutritionViewModel.Title=Nutrizione
NutritionViewModel.VisitNotebook=Notebook
OKLabel=OK
Oaxaca=Oxaca
Observation=Osservazione
Odisha=Odisha
Offaly=Offaly
Ogliastra=Ogliastra
Ohio=Ohio
Oklahoma=Oklahola
Olbia-Tempio=Olbia-Tempio
Oman=Il proprio
OncePerWeek=1 v/settimana
One=1
OneHourBeforeActionIsDue=Un'ora prima che l'azione sia dovuta
OneToSixHours=1-6 ore
Ontario=Ontario
Opportunities=Opportunità
Optimal=Risposta ottimale
Oregon=Oregon
Oristano=Oristano
Other=Altro
OtherSilage=Altro insilato
Overall=Punteggio totale
OverallCalfHeiferDetails=Completa almeno uno dei sondaggi della lista sopra per vedere il punteggio totale di "Vitelli &amp; Manze"
OverallForageScoreDetails=Completa almeno uno dei sondaggi della lista sopra per vedere il punteggio totale di "Audit foraggi"
PDFDisclaimer=Cargill Incorporated, partners e affiliati non garantiscono l'esattezza dei risultati ottenuti, a causa di molteplici fattori. Non vi è alcuna garanzia di produzione o di risultati finanziari. ©{0} Cargill, Incorporated. Tutti i diritti riservati.
PDFDisclaimer_ProvimiUS=Provimi Nord America,  partners e affiliati non garantiscono l'esattezza dei risultati ottenuti, a causa di molteplici fattori. Non vi è alcuna garanzia di produzione o di risultati finanziari. ©{0} Cargill, Incorporated. Tutti i diritti riservati.
PDFPageNumber=Pagina {0} di {1}
PEN=Perù (S/. PEN)
PHP=Filippine ($ PHP)
PLN=Polonia (zł PLN)
PMRConcentrate=PMR + concentrato
PON=Romania (lei PON)
Padua=Padova
Pakistan=Pakistan
Palermo=Palermo
Palestinian_Territory,_Occupied=Territorio palestinese, occupato
Panama=Panama
Papua_New_Guinea=Papua Nuova Guinea
Paraguay=Paraguay
Paraná=Paranã
Paraíba=Paraãba
Parlor=Sala mungitura
Parma=Parma
ParticleScorePreviousVisitsViewModel.MidOne=Strato B
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Strato C
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=% strato
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Strato B (8mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Strato C (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Strato A (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Fondo
ParticleScorePreviousVisitsViewModel.SelectDates=Scegliere date
ParticleScorePreviousVisitsViewModel.Top=Strato A
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.Tray=Fondo
Pará=Paraiaria
PasteurizedWholeMilk=Fornito latte pastorizzato
Pasto=Pasto
Pasture=Pascolo
PastureOther=Pascolo + Altro
Pavia=Pavia
Pays=Paga
PeakMilk=Picco lattazione
PenDetailViewModel.Title=Dettagli gruppo
PenListViewModel.DietSetup=Imposta razione
PenListViewModel.InfoPenList=I gruppi sono richiesti durante la visita per utilizzare i singoli strumenti e la funzione "Visita guidata". Il gruppi saranno caricati automaticamente se l'app è connessa con DDW. Aggiungi manualmente i gruppi se i dati dell'azienda non sono impostati. Le razioni saranno importate automaticamente se MAX è connesso al sito produttivo. Aggiorna regolarmente i dati sulla razione affinchè le informazioni generate nei report siano corrette. Selezionare manualmente la razione o la classe/sottoclasse di animali se i dati di MAX non sono ancora impostati.
PenListViewModel.MainHeading=Gruppi
PenListViewModel.NewPen=Nuovo gruppo
PenListViewModel.Title=Gruppi
PenName=Nome del gruppo
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Modifica punteggio ruminazioni (per 100 giorni)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Cambio BCS ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Vacche nel gruppo
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Vacche munte/ora
PenTimeBudgetComparisonViewModel.Current=Attuale
PenTimeBudgetComparisonViewModel.EnergyChange=Cambio Energia (Mcal)
PenTimeBudgetComparisonViewModel.Overcrowding=Sovraffollamento (%)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Turni mungitura/ora
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Potenziale incremento/perdita di latte ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Differenza di riposo (h)
PenTimeBudgetComparisonViewModel.TableTitle=Confronto
PenTimeBudgetComparisonViewModel.TimePerMilking=t per mungitura (h)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=t riposo rimanente (h)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=t riposo richiesto (h)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=t totale di non riposo (h)
PenTimeBudgetComparisonViewModel.TotalTimeMilking=t totale di mungitura (h)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=t per arrivare alle cuccette (h)
PenTimeBudgetPenMasterViewModel.Compare=Confronto
PenTimeBudgetPenMasterViewModel.Inputs=Input
PenTimeBudgetPenMasterViewModel.Results=Risultati
PenTimeBudgetPenMasterViewModel.Title=Pen Time Budget
PenTimeBudgetResultsViewModel.Hours=Ore
PenTimeBudgetResultsViewModel.MilkDifference=Differenza in latte potenziale
PenTimeBudgetResultsViewModel.MilkLossKg=Kg
PenTimeBudgetResultsViewModel.MilkLossPounds=Lbs
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Potenziale incremento/perdita di latte
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=t riposo disponibile
PenTimeBudgetResultsViewModel.TimeRemaining=t rimanente
PenTimeBudgetResultsViewModel.TimeRequired=t richiesto
PenTimeBudgetResultsViewModel.Title=Pen Rime Budget - Risultati
PenTimeInputsViewModel.CowsPen=Vacche nel gruppo
PenTimeInputsViewModel.Drinking=t abbeverata (h)
PenTimeInputsViewModel.Eating=t alimentazione (h)
PenTimeInputsViewModel.Frequency=Mungiture/giorno
PenTimeInputsViewModel.LockUp=t bloccate (h)
PenTimeInputsViewModel.NonRestTime=Altro t non riposo (h)
PenTimeInputsViewModel.ParlorTime=t in sala (h)
PenTimeInputsViewModel.PenTimeTitle=Pen Time Budget
PenTimeInputsViewModel.Resting=Fabbisogno riposo (h)
PenTimeInputsViewModel.StallsPen=Posti nel gruppo
PenTimeInputsViewModel.TotalStalls=Posti totali in sala mungitura
PenTimeInputsViewModel.WalkingTimeFrom=t percorrenza sala-posto (h)
PenTimeInputsViewModel.WalkingTimeTo=t percorrenza posto-sala (h)
PenTimePenSelectionViewModel.NoLactatingPen=Per accedere a questo strumento, prego assicurarsi di avere almeno un gruppo con una razione di lattazione
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Pen Time Budget
PenTimePenSelectionViewModel.PenTimeSection=Gruppi
PenTimePenSelectionViewModel.Title=Pen Time Budget
Pendetails=Dettagli gruppo
PennStateShakerBoxForageResults=Risultati PSPS
Pennsylvania=Pennsylvania
Pens=Gruppi
PerceivedHeatStressDietInfoMessage=Stress da caldo moderato\: maggiori atti respiratori, bocca chiusa con bava/schiuma, frequenza cardiaca 40-120bpm.
PercentLossPerCow=% Perdite/vacca
PercentOnScreenTitle=Percentuale nello strato (%)
PercentPen=% del gruppo
PercentageOnScreen=Percentuale nello strato (%)
PercentageOnScreenCurrentVisit=% nello strato - Visita attuale
PercentageOnScreenTrend=% nello strato - Trend
Pernambuco=Pernambuco
Peru=Perù
Perugia=Perugia
Pesaro_and_Urbino=Pesaro and Urbino
Pescara=Pescara
PhaseFive=Pubertà
PhaseFour=Accrescimento
PhaseOne=Colostro
PhaseSeven=Close-up / Produzione
PhaseSix=Gravidanza
PhaseTwoThree=Pre/Post-svezzamento
Philippines=Filippine
PhotoExamples=Foto di esempio
Piacenza=Piacenza
Piauí=Piauã
Pickup=Prelievo latte {0}
Pile=Platea
PileAndBunkerCapacitiesDensity=Guida Densità trincee e mucchi
PileAndBunkerCapacity=Sistemi di stoccaggio
PileAndBunkerCapacityViewModel.AddBag=Silobag
PileAndBunkerCapacityViewModel.AddBunker=Trincea
PileAndBunkerCapacityViewModel.AddPile=Platea
PileAndBunkerCapacityViewModel.Bag=Silobag
PileAndBunkerCapacityViewModel.Bags=Silobag
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=Silo ciclatore
PileAndBunkerCapacityViewModel.Bunker=Trincea
PileAndBunkerCapacityViewModel.Bunkers=Trincee
PileAndBunkerCapacityViewModel.NameNotUnique=Esiste già una trincea/platea con il nome "{0}". Il nome deve essere unico
PileAndBunkerCapacityViewModel.NameTooLong=Il nome delle trincee o platee devono avere 40 caratteri o meno
PileAndBunkerCapacityViewModel.Pile=Platea
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Sitemi di stoccaggio
PileAndBunkerCapacityViewModel.Piles=Platee
PileAndBunkerCapacityViewModel.Resources=Risorse
PileAndBunkerCapacityViewModel.Title=Capienza trincee e mucchi
PileAndBunkerCapacityViewModel.TopUnloadingSilo=Silo a torre
PileAndBunkerCapacityViewModel.VisitNotebook=Notebook
PileAndBunkerName=Nome Inventario foraggi
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=Densità t.q. insilato {0} (Goal\: &gt; {1})
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=s.s. (%)
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=Tons t.q. metriche
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=Tons s.s. metriche
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Lung. inferiore ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Larg. inferiore ({0})
PileAndBunkerResultsCapacityInputViewModel.Capacity=Capienza
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=s.s. (%)
PileAndBunkerResultsCapacityInputViewModel.Height=Altezza ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=Tons t.q.
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Tons s.s.
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Lung. inferiore (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Lung. inferiore (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Larg. inferiore (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Larg. inferiore (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Altezza (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Altezza (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Lung. superiore (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Lung. superiore (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Larg. superiore (f)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Larg. superiore (m)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Densità s.s. insilato ({0})
PileAndBunkerResultsCapacityInputViewModel.Title=Capienza
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Densità t.q. insilato ({0}) (Obiettivo\: &gt; 44)
PileAndBunkerResultsCapacityInputViewModel.TonsAF=Tons t.q.
PileAndBunkerResultsCapacityInputViewModel.TonsDM=Tons s.s.
PileAndBunkerResultsCapacityInputViewModel.TopLength=Lung. superiore ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Larg. superiore ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=A 6 in/giorno
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=A 15 cm/giorno
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=A 3 in/giorno
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=A 7 cm/giorno
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=Vacche/giorno richieste
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Vacche da alimentare
PileAndBunkerResultsFeedOutViewModel.DateGone=Data fine
PileAndBunkerResultsFeedOutViewModel.Days=Giorni
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Info tasso di consumo
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Superficie di consumo (ft^2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Superficie di consumo (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Livello ingestione (t.q./vacca)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=in/giorno
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=cm/giorno
PileAndBunkerResultsFeedOutViewModel.Resources=Risorse
PileAndBunkerResultsFeedOutViewModel.StartDate=Data inizio
PileAndBunkerResultsFeedOutViewModel.Title=Consumo
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tons/giorno
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Lbs di s.s. in 1 piede
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kg s.s. in 1m
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Capienza
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Consumo
PileAndBunkerResultsMasterViewModel.VisitNotebook=Notebook
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=Capienza
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=s.s. (%)
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=Tons t.q. metriche residue
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=Tons s.s. metriche residue
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=Densità s.s. insilato
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=Tons s.s. residue
PileAndBunkerTitle=Trincee e mucchi
PileBunkerCapacities=Sitemi di stoccaggio
PileCapacity=Capienza platea
PileFeedOutRate=Consumo platea
Piles=Platee
Pisa=Pisa
Pistoia=Pistoia
Pitcairn=Pitcairn
PlBacteriaCell=CBT (1.000 CFU/ml)
PlMilkFat=Grasso
PlMilkProtein=Proteine
PlSomaticCell=CCS (1.000 cells/ml)
Poland=Polonia
Poor=Bassa
Pordenone=Pordenone
Porosity=Porosità
Portugal=Portogallo
Postweaned=Post-svezzamento
Postweaned_CleanAndDryPen=Il box è pulito e asciutto?
Postweaned_CleanAndDryPen_ToolTip=Verifica lo stato dei garretti\: sono puliti e asciutti?
Postweaned_EvidenceOfAcidosisInManure=Evidenza di acidosi nelle feci?
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Ci sono bolle nelle feci?
Postweaned_EvidenceOfScoursOrPneumonia=Si evidenziano diarree o problemi respiratori?
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% dei vitelli presenta diarree o problemi respiratori
Postweaned_FeedBunkIsClaanedDaily=La mangiatoia viene pulita ogni giorno e gli avanzi rimossi?
Postweaned_ForageAvailability=Il foraggio è disponbile?
Postweaned_ForageAvailability_ToolTip=Steli con lunghezza &gt;5cm
Postweaned_FreeChoiceCleanWaterIsAvailable=È disponibile acqua fresca e pulita?
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=Nessuna evidenza di acqua contaminata
Postweaned_FreshQualityStarterAvailable=È disponibile mangime fresco?
Postweaned_FreshQualityStarterAvailable_ToolTip=Il mangime è asciutto, senza muffa e polvere?
Postweaned_SizeOfBunkSpace=Le dimensioni della mangiatoia sono adeguate?
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45cm per vitello
Postweaned_SizeOfPenAdequate=Le dimensioni dei box sono adeguate?
Postweaned_SizeOfPenAdequate_ToolTip=3m2 per box singolo, 2.5m2 per box multipli
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Il box è ben ventilato?
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=Se i vestiti odorano di ammoniaca dopo aver lasciato la vitellaia, la concentrazione è elevata
PotentialDownResponse=Risposta rilascio latte potenziale ({0}/vacca/giorno)
PotentialSCC=CCS potenziali (cell/{0})
Potenza=Potenza
Prato=Piatto
PreWeaned_CMRisProperlyMixed_ToolTip=Latte in polvere &gt;600g-&lt;800g, T 39-41°C, latte ricostituito 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Verifica lo stato dei garretti\: sono puliti e asciutti?
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% dei vitelli presenta diarree o problemi respiratori
PreWeaned_Forageavailability_ToolTip=Se è disponibile uno starter strutturato e fibroso, il foraggio non è necessario
PreWeaned_FreeChoiceCleanWater_ToolTip=Disponibile dal primo giorno, nessuna evidenza di contaminazione
PreWeaned_FreeChoiceFreshCalf_ToolTip=Il mangime è asciutto, senza muffa e polvere?
PreWeaned_SizeOfPen_ToolTip=3m2 per box singolo, 2.5m2 per box multipli
PreWeaned_WellVenilated_ToolTip=Se i vestiti odorano di ammoniaca dopo aver lasciato la vitellaia, la concentrazione è elevata
PrematureKelvingsKeyInfoMessage=Partoriti uno o più vitelli vivi almeno 10 giorni prima la data prevista
PreventingStorageLosses=Prevenire le perdite di stoccaggio
Previous=Precedente
Preweaned=Pre-svezzamento
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=Il latte ricostituito è preparato e somministrato correttamente?
Preweaned_CleanAndDryPen=Il box è pulito e asciutto?
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Gli attrezzi per alimentare i vitelli sono puliti e disinfettati fra i pasti?
Preweaned_ConsistentFeedingTimesAndProtocols=L'orario dei pasti è costante?
Preweaned_EvidenceOfScoursOrPneumonia=Evidenza di diarree o problemi respiratori?
Preweaned_ForageAvailability=Il foraggio è disponibile?
Preweaned_FreeChoiceCleanWaterIsAvailable=È disponibile acqua fresca e pulita?
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Lo starter è a disposizione sempre fresco?
Preweaned_SizeOfPenAadequatePerHeifer=Le dimensioni dei box sono adeguate?
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Svezzamento fatto al consumo di 2 kg starter/giorno?
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Il box è ben ventilato?
PricingMatrixEditViewModel.Cancel=Cancella
PricingMatrixEditViewModel.Save=Salva
PricingMatrixEditViewModel.Title=Dettaglio elementi matrice
PricingMatrixPickListViewModel.PricingMatrix=Matrice di prezzo
PricingMatrixViewModel.Amount=Q.tà (1.000 cell/ml)
PricingMatrixViewModel.AmountCFU=Q.tà (1.000 CFU/ml)
PricingMatrixViewModel.New=Nuovo
PricingMatrixViewModel.Title=Modifica matrice
Prince_Edward_Island=Isola del Principe Edoardo
Privacy_Statement=Informativa sulla privacy
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Valute
ProcessorCurrencyPickListViewModel.Title=Valute
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Ricavi mungitura
ProductivityToolsViewModel.MilkRevenueAnalysis=Analisi ricavi del latte
ProductivityToolsViewModel.MilkSoldEvaluation=Valutazione latte venduto
ProductivityToolsViewModel.ProductivityTitle=Produttività
ProductivityToolsViewModel.ProductivityTools=Strumenti
ProductivityToolsViewModel.VisitNotebook=Notebook
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=Annulla
PromptOK=OK
PromptPermissionMsg=Senza questo permesso alcune caratteristiche potrebbero non funzionare. Sicuro di voler rifiutare questo permesso?
PromptPermissionMsgIMSure=Sicuro
PromptPermissionMsgRetry=Riprova
PromptPermissionTitle=Permesso negato
ProspectProfileViewModel.DeleteProspect=Cancella cliente potenziale
ProspectProfileViewModel.DeleteProspectPrompt=Sei sicuro di voler cancellare questo cliente potenziale? Tutte le info associate andranno perse
ProspectProfileViewModel.MainHeading=Siti produttivi
ProspectProfileViewModel.NewSite=Nuovo sito produttivo
ProspectProfileViewModel.ProspectInfo=Informazioni cliente potenziale
ProspectProfileViewModel.ProspectTitle=Dettagli cliente potenziale
ProspectsViewModel.NewProspect=Nuovo cliente potenziale
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=Provimi
ProvimiUS=Provimi US
PublishVisit=Pubblicato
Puducherry=Puducherry
Puebla=Puebla
Puerto_Rico=Puerto Rico
Punjab=Punjab
Purina=Purina
Qatar=Qatar
Qinghai=Qinghai
Quando=viene creato un nuovo gruppo le informazioni richieste sono\: nome, razione, tipo di stalla e sistema di alimentazione. 
QuarterPointScale=Scala 1/4 punto
Quarterly=Trimestrale
Quebec=Quebec
Queensland=Queensland
Querétaro=Quer é Taro
QuestionTableTitle=Domanda {0} di {1}
QuestionViewModel.Baleage=Fasciato
QuestionViewModel.BunkersAndPiles=Trincee e Platee
QuestionViewModel.Close=Chiudi
QuestionViewModel.Harvest=Qualità foraggi in razione
QuestionViewModel.MaintainingForageQuality=Mantenere la qualità del foraggio
QuestionViewModel.SilageBags=Silobag
QuestionViewModel.SurveyOfForages=Gestione del foraggio
QuestionViewModel.TowerSilos=Silo a torre
QuestionViewModel.VisitNotebook=Notebook
Quintana_Roo=Quintana Roo
ROL=ROL
RUB=Russia (₽‎ RUB)
Ragusa=Ragusa
Rajasthan=Rajasthan
Rationcost=Costo della razione ({0})
Ravenna=Ravenna
ReadyToMilkChartViewModel.Current=Attuale
ReadyToMilkChartViewModel.DeathLoss=Mastiti
ReadyToMilkChartViewModel.DisorderGraphTitle=Costi annuali malattie metaboliche/vacca
ReadyToMilkChartViewModel.DisplacedAbomasum=Dislocazione
ReadyToMilkChartViewModel.Dystocia=Distocia
ReadyToMilkChartViewModel.Ketosis=Chetosi
ReadyToMilkChartViewModel.Metritis=Metrite
ReadyToMilkChartViewModel.MilkFever=Collasso
ReadyToMilkChartViewModel.RetainedPlacenta=Ritenzione
ReadyToMilkChartViewModel.Title=Grafici Ready2Milk™
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Indice Ready2Milk™
ReadyToMilkInputViewModel.Back=Indietro
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Cambiamento BCS da -60 a 21 DIM
ReadyToMilkInputViewModel.CloseUp=Close-up
ReadyToMilkInputViewModel.ComfortCloseUp=Comfort nel Close-up
ReadyToMilkInputViewModel.ComfortDiet=Comfort da 0-21 DIM
ReadyToMilkInputViewModel.CostExtraDaysOpen=Costo aggiuntivo per giorni aperti
ReadyToMilkInputViewModel.CudChewingDiet=Vacche in ruminazione
ReadyToMilkInputViewModel.DeadCowsOrCulled=Morti o riforme legate alla salute
ReadyToMilkInputViewModel.DisplacedAbomasum=Dislocazione abomaso
ReadyToMilkInputViewModel.Dystocia=Distocia
ReadyToMilkInputViewModel.FreshCows=Vacche fresche
ReadyToMilkInputViewModel.Health=Salute
ReadyToMilkInputViewModel.HealthDesc=Inserisci il n° di vacche fresche e il n° di incidenze metaboliche del periodo di valutazione. Questi verranno convertiti in costi annui di incidenza nella schermata Risultati
ReadyToMilkInputViewModel.HealthRecords=Registro salute
ReadyToMilkInputViewModel.Herd=Info mandria
ReadyToMilkInputViewModel.Ketosis=Chetosi
ReadyToMilkInputViewModel.LocomotionScore=Locomotion Score
ReadyToMilkInputViewModel.Mastitis=Mastiti 0-21 DIM
ReadyToMilkInputViewModel.Metritis=Metrite
ReadyToMilkInputViewModel.MilkFever=Collasso puerperale
ReadyToMilkInputViewModel.MilkPrice=Prezzo del latte
ReadyToMilkInputViewModel.MilkYield=Produzione 15-60 DIM
ReadyToMilkInputViewModel.Next=Seguente
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Stress da caldo percepito 0-21 DIM
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Stress caldo percepito Close-up
ReadyToMilkInputViewModel.PrematureCalvings=Nati prematuri
ReadyToMilkInputViewModel.ReplacementCowCost=Costo di riforma
ReadyToMilkInputViewModel.RetainedPlacenta=Ritenzione placentare
ReadyToMilkInputViewModel.RumenFill=Riempimento ruminale
ReadyToMilkInputViewModel.SccFirstTest=CCS 1° controllo (x1.000/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Razione specifica Close-up
ReadyToMilkInputViewModel.SpecificDiet=Razione specifica 0-21 DIM
ReadyToMilkInputViewModel.Title=Input Ready2Milk™
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Totale vacche fresche/anno
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Vacche fresche in valutazione
ReadyToMilkListViewModel.Title=Indice Ready2Milk™
ReadyToMilkMasterViewModel.Charts=Grafici
ReadyToMilkMasterViewModel.Inputs=Input
ReadyToMilkMasterViewModel.MastitisNotPresent=Mastiti non inserite
ReadyToMilkMasterViewModel.Outputs=Risultati
ReadyToMilkMasterViewModel.Title=Indice Ready2Milk™
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Indice Ready2Milk™
ReadyToMilkOutputViewModel.Title=Risultati Ready2Milk™
RecommendedTLCSettings=Impostazioni TLC consigliate
RefreshTokenFailed=Sessione scaduta. Effettuare nuovamente il login
Reggio_Calabria=Reggio Calabria
Reggio_Emilia=Reggio Emilia
RemovedAndMeasured=Rimosso e misurato
RemovedOnly=Solo rimosso
Report=Rapporto
Report.Analysis.Type=Tipo di analisi
Report.Animal.Analysis=Analisi degli animali
Report.Animal.Tag.Name=ID animale
Report.AvgRumenFillScore=Punteggio medio riempimento ruminale (calcolato)
Report.BCS.EvalDataTitle=Valutazione dati BCS calcolati
Report.BCS.LactationStages=Fasi di lattazione
Report.BCS.Max=BCS max
Report.BCS.MilkHeadDay=Latte/capo/giorno
Report.BCSAvg=Media BCS
Report.Bcs=BCS
Report.Bcs.ChartName=Punteggio condizione corporea - animale {0}
Report.Bcs.HerdAnalysis.ChartName=BCS vs latte
Report.Bcs.Milk=Latte
Report.Bcs.Min=BCS min
Report.Calving.Date=Parto
Report.Cargill.Report=Cargill report
Report.Chewing=Masticazioni
Report.Chews=Masticazioni
Report.CudChewing.EvalDataTitle=Valutazione dati masticazione calcolati
Report.CudChewingPercentage=% Masticazione/
Report.CudChewingPercentage.Vs.LactStages=% Masticazione/
Report.EvalDataTitle=Valutazione dati calcolati
Report.ForagePennState=Penn State foraggi
Report.General.Comments=Info gruppo Commenti
Report.GoalCudChewingPercentage=Masticazione del bolo obiettivo %
Report.Heatstress.Dmi.Adjustment=SS ingerita aggiustata
Report.Heatstress.Energy.Equivalent.Milk.Loss=Perdita di latte equivalente in energia ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Sostanza secca ingerita stimata ({0})
Report.Heatstress.Intake.Adjustment=Ingestione aggiustata
Report.Heatstress.Legend=Leggenda
Report.Heatstress.Legends=Leggende
Report.Heatstress.Loss.Of.Energy.Consumed=Perdita di energia consumata (mcal)
Report.Heatstress.Mild.Moderate.Stress=Stress lieve - moderato
Report.Heatstress.Mild.Moderate.Stress.Message=La respirazione supera 75 bpm | La temperatura rettale supera 39 \\ U2103 (102.2 \\ U2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Valore latte perso (al mese) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Valore latte perso (al giorno) ({0})
Report.Heatstress.Moderate.Severe.Stress=Stress moderato - grave
Report.Heatstress.Moderate.Severe.Stress.Message=La respirazione supera 85 bpm | La temperatura rettale supera 40 \\ U2103 (104 \\ U2109)
Report.Heatstress.Reduction.In.Dmi=Riduzione SS ingerita ({0})
Report.Heatstress.Relative.Humidity=Umidità relativa (%)
Report.Heatstress.Severe.Stress=Stress grave
Report.Heatstress.Severe.Stress.Message=La respirazione supera 120-140 bpm | La temperatura rettale supera 41 \\ U2103 (106 \\ U2109)
Report.Heatstress.Stress.Threshold=Soglia di stress
Report.Heatstress.Stress.Threshold.Message=La respirazione supera i 60 bpm | Perdite di riproduzione rilevabili | La temperatura rettale supera 38,5 \\ U2103 (101.3 \\ U2109)
Report.Heatstress.Temperature=Temperatura
Report.Heatstress.Temperature.In.Celcius=Temperatura \\ U2103
Report.Heatstress.Temperature.In.Farenhiet=Temperatura \\ U2109
Report.Heatstress.TemperatureHumidityIndex=Indice termoigrometrico
Report.Herd.Analysis.CudChewingPercentage=% Masticazione/
Report.Locomotion.HerdAnalysis.ChartName=Punteggio locomozione %
Report.LocomotionScore.X.Axis=Punteggio locomozione
Report.LocomotionScore.Y.Axis=Percentuale %
Report.LocomotionScore.chartName=Punteggio locomozione - animale {0}
Report.No.OfChews=No. Masticazioni
Report.No.OfChewsPerRegurgitation=N. di masticazioni per rigurgito
Report.NoOfChews.Vs.LactStages=No. Masticazioni
Report.Not.Chewing=Nessuna masticazione
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Tempo di riposo disponibile
Report.PenTimeBudget.TimeAvailableForResting.Label=Ore
Report.PenTimeBudgetTimeRemaining=Tempo rimanente
Report.PenTimeBudgetTimeRequired=Tempo richiesto
Report.Pentime.Budget.Hours=Ore
Report.PercentageOnScreen=Sul setaccio (%)
Report.RumenHealthManureScreening.Bottom=Inferiore
Report.RumenHealthManureScreening.BottomGoalMax=Inferiore obiettivo max
Report.RumenHealthManureScreening.BottomGoalMin=Inferiore obiettivo min
Report.RumenHealthManureScreening.Middle=Centrale
Report.RumenHealthManureScreening.MiddleGoalMax=Centrale obiettivo max
Report.RumenHealthManureScreening.MiddleGoalMin=Centrale obiettivo min
Report.RumenHealthManureScreening.Top=Superiore
Report.RumenHealthManureScreening.TopGoalMax=Superiore obiettivo max
Report.RumenHealthManureScreening.TopGoalMin=Superiore obiettivo min
Report.SheetName=Rapporto Master
Report.Tool.Details=Strumenti Dettagli
Report.Tool.Name=Nome strumento
Report.Visit.Date=Data di visita
Report.Visit.Report=Visita  Report
Report.Visit.name=Visita il nome
Report.goalChews=Obiettivo masticazioni
Report.locomotionScore.Pen.Analysis.ChartName=Categorie vs date di visita
ReportDate=Data di report
ReportPDFNote=Note\:  
Reset_Database=Ripristina Database
Resources=Risorse
ResourcesViewModel.Title=Risorse Audit foraggi
ResourcesViewModel.VisitNotebook=Notebook
RetainedPlacenta=Ritenzione placentare
Reunion=Riunione
Revenue=Reddito
RevenueEditComparisonValuesViewModel.CurrentSCC=CCS attuali (cell/{0})
RevenueEditComparisonValuesViewModel.DownResponse=Risposta di rilascio del latte ({0}/vacca/giorno)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Modifica valori di confronto
RevenueEditComparisonValuesViewModel.MilkChange=Cambiamenti nella produzione (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Prezzo del latte (€/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Produzione di latte
RevenueEditComparisonValuesViewModel.NumOfCows=\# vacche
RevenueEditComparisonValuesViewModel.PotentialSCC=CCS potenziali (cell/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Scenario 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Scenario 2
RevenueEditComparisonValuesViewModel.Title=Modifica valori di confronto
RevenueInputViewModel.ComparisonValues=Valori di confronto
RevenueInputViewModel.Edit=Modifica
RevenueInputViewModel.ScenarioOne=Scenario 1
RevenueInputViewModel.ScenarioTwo=Scenario 2
RevenueInputViewModel.Title=Ricavi mungitura - Input
RevenueLossDay=Perdita di ricavi ({0}/giorno)
RevenueLossYear=Perdita di ricavi ({0}/anno)
Rheinland=Renania
Rhode_Island=Rhode Island
Rieti=Rieti
Rimini=Rimini
Rio_Grande_do_Norte=Grande fiume settentrionale
Rio_Grande_do_Sul=Rio Grande do Sul
Rio_de_Janeiro=Rio de Janeiro
Robot=Robot
RoboticMilkEvaluationSpinnerViewModel.Title=Mungitura robotizzata
Romania=Romania
Rome=Roma
RondÃ´nia=Rondãiania
Roraima=Roraima
Roscommon=Roscommon
Rovigo=Rovigo
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Analisi mandria
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Analisi gruppo
RumenHealthBodyConditionLandingViewModel.Pens=Gruppi
RumenHealthBodyConditionLandingViewModel.Resources=Risorse
RumenHealthBodyConditionLandingViewModel.Title=Body Condition Score
RumenHealthEditManureScoresViewModel.Close=Chiudi
RumenHealthEditManureScoresViewModel.Count=Conteggio
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Conta il numero di vacche
RumenHealthEditManureScoresViewModel.NumOfCows=Vacche
RumenHealthEditManureScoresViewModel.NumberOfCows=Vacche
RumenHealthEditManureScoresViewModel.VisitNotebook=Notebook
RumenHealthLandingViewModel.HerdAnalysis=Analisi mandria
RumenHealthLandingViewModel.PenAnalysis=Analisi gruppo
RumenHealthLandingViewModel.Pens=Gruppi
RumenHealthLandingViewModel.Title=Ruminazione
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Analisi mandria
RumenHealthLocomotionLandingViewModel.PenAnalysis=Analisi gruppo
RumenHealthLocomotionLandingViewModel.Pens=Gruppi
RumenHealthLocomotionLandingViewModel.Resources=Risorse
RumenHealthLocomotionLandingViewModel.Title=Locomotion
RumenHealthManureLandingViewModel.HerdAnalysis=Analisi mandria
RumenHealthManureLandingViewModel.PenAnalysis=Analisi gruppo
RumenHealthManureLandingViewModel.Pens=Gruppi
RumenHealthManureLandingViewModel.Resources=Risorse
RumenHealthManureLandingViewModel.Title=Manure Score
RumenHealthManureMasterViewModel.Inputs=Input
RumenHealthManureMasterViewModel.Results=Risultati
RumenHealthManureMasterViewModel.RumenHealthManureScore=Manure Score
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Manure Score
RumenHealthManureMasterViewModel.VisitNotebook=Notebook
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Punteggio medio
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Data
RumenHealthManureScoresResultsViewModel.PercentPen=% del gruppo
RumenHealthManureScoresResultsViewModel.SelectedDates=Scegliere date
RumenHealthManureScoresResultsViewModel.Title=Risultati Manure Score
RumenHealthManureScoresViewModel.AnimalsObserved=Animali osservati
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Modifica punteggio ruminazioni
RumenHealthManureScoresViewModel.Edit=Modifica
RumenHealthManureScoresViewModel.ManureScore=Manure Score
RumenHealthManureScoresViewModel.PercentOfPen=% del gruppo
RumenHealthManureScoresViewModel.ScoreCategory=Categorie Manure Score
RumenHealthManureScoresViewModel.StdDevCalculated=Dev. Std. (calcolata)
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=Contatore
RumenHealthPenCudCalculatorViewModel.CudChewing=Ruminazioni
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Registrare il numero di vacche in ruminazione
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Masticazioni
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Registrare il numero di masticazioni per vacca
RumenHealthTMRLandingViewModel.HerdAnalysis=Analisi mandria
RumenHealthTMRLandingViewModel.PenAnalysis=Analisi gruppo
RumenHealthTMRLandingViewModel.Pens=Gruppi
RumenHealthTMRLandingViewModel.Title=Particle Score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Inserire quantità (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Tara (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Obiettivo max (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Obiettivo min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Obiettivo
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Strato B (8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Strato C (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Strato C (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Nome TMR
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=TMR Particle Score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Tara
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Inserire quantità
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Strato A (19mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Fondo
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Nuovo TMR Score
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Punteggio medio
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Attuale
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Quantità (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Obiettivo max (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Obiettivo B (8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Obiettivo C (1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Obiettivo C (4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Obiettivo min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Obiettivi
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Obiettivo A (19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Obiettivo Fondo
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Max
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Medio 1 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Strato C (1.18 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Medio 2 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Min
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Particle Score (% nel setaccio)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Percentuale nello strato (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Dev. Std
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Informazioni TMR
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=TMR Particle Score
RumenHealthTMRParticleScorePenTableInputViewModel.Title=TMR Particle Score
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Strato A (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Fondo
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Input
RumenHealthTMRPenScorerTableMasterViewModel.Results=Risultati
RumenHealthTMRPenScorerTableMasterViewModel.Title=Particle Score
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Nessuna scelta
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Si deve scegliere un setaccio per poter vedere un gruppo
RumenHealthTMRSelectPenViewModel.SelectPen=Gruppi (solo lattazione e asciutte)
RumenHealthTMRSelectPenViewModel.SelectScorer=Seleziona un setaccio
RumenHealthTMRSelectPenViewModel.Title=Particle Score
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Nessuna scelta
RumenHealthTMRSelectScorerViewModel.FooterText=È possibile scegliere un setaccio per visita. Cambiando il setaccio i dati andranno persi
RumenHealthTMRSelectScorerViewModel.SelectScorer=Seleziona un setaccio
RumenHealthTMRSelectScorerViewModel.Title=Particle Score
Russia=Russia
Russian_Federation=Federazione Russa
Rwanda=Ruanda
SAR=Arabia Saudita (﷼ SAR)
SCCPremiumDeduction=CCS premi/detrazioni ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Suriname ($ SRD)
Saint_Barthélemy=San Bartolomeo
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Ascension e Tristan Da Cunha
Saint_Kitts_and_Nevis=Saint Kitts e Nevis
Saint_Lucia=Santa Lucia
Saint_Martin_(French_part)=Saint Martin (parte francese)
Saint_Pierre_and_Miquelon=Saint Pierre e Miquelon
Saint_Vincent_and_the_Grenadines=Saint Vincent e Grenadines
Salerno=Salerno
Samoa=Samoa
San_Luis_Potosí=San Luis Potosã
San_Marino=San Marino
Santa_Catarina=Santa Catarina
Sao_Tome_and_Principe=Sao Tome e Principe
Saskatchewan=Saskatchewan
Sassari=Sassari
SaudiArabia=Arabia Saudita
Saudi_Arabia=Arabia Saudita
Savona=Savona
Schleswig=Schleswig
ScorecardPrompt=Completa il sondaggio per vedere il punteggio di\: {0}
Screen=Settaccio a strati
ScreenNew=Setaccio a strati (nuovo)
ScreenOld=Setaccio a strati (vecchio)
Search=Ricerca
SegmentViewModel.SegmentTitle=Scegliere segmento
SegmentViewModel.Title=Dettagli
Select=Selezionare
SelectCurrencyViewModel.Title=Valuta
SelectDates=Scegliere date
SelectFeedingSystemViewModel.SelectFeedingSystem=Scegliere sistema di alimentazione
SelectFeedingSystemViewModel.Title=Impostazioni gruppo
SelectForageImprovement=Prego selezionare solo 12 voci dalla lista
SelectHousingSystemViewModel.SelectHousingSystem=Scegliere tipo di stalla
SelectHousingSystemViewModel.Title=Impostazioni gruppo
SelectImprovement=Prego scegliere solo 10 miglioramenti dalla lista
SelectMatrix=Scegli una matrice
SelectMilkingSystemViewModel.NoneSelected=Nessuna scelta
SelectMilkingSystemViewModel.SelectMilkingSystem=Sistema di mungitura
SelectMilkingSystemViewModel.Title=Impostazioni sito produttivo
SelectOnlyThreeNotes=Si prega di selezionare solo 3 annotazioni per strumento
SelectOnlyTwoNotes=Si prega di selezionare solo 2 annotazioni per strumento
SelectPen=Scegliere una razione per il nuovo gruppo
SelectProcessor=Scegliere Caseificio
SelectProcessorViewModel.COMPONENT=Componenti
SelectProcessorViewModel.CONCENTRATION=Concentrazione
SelectProcessorViewModel.Edit=Modifica
SelectProcessorViewModel.MilkProcessors=Caseificio
SelectVisitComparison=Seleziona le visite per il confronto
SemiAnnually=Semestrale
Semiconfinamento=Semiconfinamento
Send=Invia
Senegal=Senegal
Seoul=Seoul
Serbia=Serbia
Sergipe=Sergipe
SettingsViewModel.Imperial=Imperiale
SettingsViewModel.Metric=Metrico
SettingsViewModel.Milk_Processor_Set_Up=Imposta Caseificio
SettingsViewModel.More_Settings=Altre impostazioni
SettingsViewModel.Select_Unit_Of_Measure=Scegli unità di misura
Severe=Severo
Seychelles=Seychelles
Shaanxi=Shaanxi
Shandong=Shandong
Shanghai=Shanghai
Shanxi=Shanxi
ShortDryPeriod=Asciutta breve
ShowEulaViewModel.Accept=Accetta
ShowEulaViewModel.ConfirmationNo=No
ShowEulaViewModel.ConfirmationText=Accetti i termini del Contratto di Licenza?
ShowEulaViewModel.ConfirmationTitle=Conferma
ShowEulaViewModel.ConfirmationYes=Si
ShowEulaViewModel.Decline=Rifiuta
ShowEulaViewModel.Eula=Licenza
ShowEulaViewModel.EulaError=Impossibile visualizzare il Contratto di Licenza. Si prega di connettersi a Internet e riprovare
ShowEulaViewModel.EulaScreenTitle=Licenza
ShowPrivacyStatementViewModel.PrivacyStatement=&lt;p&gt;&lt;b&gt;Informativa sulla privacy&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Ultimo aggiornamento\: 3 gennaio 2017&lt;/p&gt;&lt;p&gt;&lt;b&gt;Ambito&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Cargill, Incorporated ("Cargill" o "Noi") raccoglie informazioni quando viene utilizzata questa applicazione mobile ("App"), che è destinata ai consulenti che offrono il servizio Dairy Enteligen™ per conto di Cargill.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Informazioni personali&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Cargill può raccogliere le seguenti informazioni personali direttamente da voi, ad esempio quando vi registrate con Noi, e attraverso l'uso dell'App.&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Il vostro nome&lt;/b&gt;&lt;/p&gt;&lt;p&gt;· &lt;b&gt;La vostra posizione&lt;/b&gt; (via GPS o altre tecnologie simili)&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Le foto &lt;/b&gt;o i&lt;b&gt; video&lt;/b&gt; (se condivisi con Cargill tramite l'App)&lt;/p&gt;&lt;p&gt;Possiamo usare le tecnologie comuni, quali i cookies e beacons nell'App per raccogliere tali informazioni personali.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Uso e condivisione – contesto aziendale&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Il nostro &lt;a href\="http\://www.cargill.com/privacy/business-notice/index.jsp"&gt;avviso di informazioni commerciali&lt;/a&gt; spiega come usiamo le informazioni personali raccolte in contesto aziendale.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Consenso alla raccolta localizzazione&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Utilizzando l'App, acconsentite espressamente alla raccolta delle vostre informazioni di localizzazione in tempo reale da parte di Cargill, ed espressamente rinunciate e rilasciate Cargill da ogni responsabilità, reclami, cause di azione o danni derivanti dall'uso dell'App, o in qualsiasi modo in relazione all'uso delle informazioni di posizione.&lt;/p&gt;
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Informativa sulla privacy
ShowSyncStatusViewModel.GetAccounts=Account aggiornati\: 
ShowSyncStatusViewModel.GetNotes=Note aggiornate\: 
ShowSyncStatusViewModel.GetVisits=Visite aggiornate\: 
ShowSyncStatusViewModel.Title=Riepilogo
Sichuan=Sichuan
Siena=Siena
Sierra_Leone=Sierra Leone
Sikkim=Sikkim
SilageBags=Silobag
SilageBags_BagsPlacedOnStableWellManagedSurface=Le silobag sono poste su una struttura dedicata e ben gestita?
SilageBags_BonusSecureCoverIsUsed=È utilizzata una copertura di sicurezza?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Il fronte è ben gestito, pulito e senza indicazione di riscaldamento o perdite?
SilageBags_FaceRemovalRate=Qual è il tasso di rimozione del fronte?
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Le silobag sono ispezionate e riparate con regolarità?
SilageBags_PorosityScoresConsistently=Qual è il grado di porosità rispetto la s.s.?
SilageBags_TrashVegRodentControlledAroundBags=Immondizia, vegetazione e roditori sono controllati intorno alle silobag?
SilagePrevention1st=Conservare l'insilato\: Prima cosa da fare
Sinaloa=Sinaloa
Singapore=Singapore
Sint_Maarten_(Dutch_part)=Sint Marthes (parte olandese)
Site-Not-Synced-To-Lift=Il sito non è stato sincronizzato con LIFT; si prega di contattare l'amministratore per la risoluzione
SiteDetailViewModel.AnimalInputsSite=Animali
SiteDetailViewModel.DairyEnteligenReport=Report DDW
SiteDetailViewModel.Detailed=Dettagliato
SiteDetailViewModel.DietInputsSiteLactating=Razioni, sito produttivo (animali in lattazione)
SiteDetailViewModel.DownloadingVisit=Download visita in corso …
SiteDetailViewModel.GeneralCustomerSiteSetup=Impostazioni sito produttivo
SiteDetailViewModel.GetReportMsg=Download report in corso …
SiteDetailViewModel.MainHeading=Visite
SiteDetailViewModel.NetworkErrorMessage=Nessuna rete disponibile
SiteDetailViewModel.NetworkErrorMessageTitle=Errore di rete
SiteDetailViewModel.NewVisit=Nuova visita
SiteDetailViewModel.ReportDownloadTimeout=Impossibile scaricare il file, si prega di provare quando si dispone di una migliore connettività
SiteDetailViewModel.ReportNotAvailable=Report non disponibile per il download
SiteDetailViewModel.ReportNotAvailableTitle=Stato
SiteDetailViewModel.Reports=Report DDW
SiteDetailViewModel.Resources=Risorse
SiteDetailViewModel.SiteSetup=Impostazioni sito
SiteDetailViewModel.Summary=Riassuntivo
SiteDetailViewModel.Title=Dettagli sito
SiteDetailViewModel.VisitDownloadPrompt=Visita non scaricata, vuoi provare a scaricarla?
SiteDetailViewModel.VisitNotDownloaded=Visita non scaricata
SiteDetailViewModel.VisitUnavailable=Dati della visita non disponibili
SiteDetailsResourcesViewModel.Title=Risorse
SiteDetailsSetupViewModel.AnimalInputsSite=Animali
SiteDetailsSetupViewModel.AsFedIntake=Ingestione t.q. ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=CBT (1.000 CFU/ml)
SiteDetailsSetupViewModel.Continue=Continua
SiteDetailsSetupViewModel.CurrentMilkPrice=Prezzo attuale del latte ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Giorni in latte (DIM)
SiteDetailsSetupViewModel.Delete=Cancella
SiteDetailsSetupViewModel.DietInputsSiteLactating=Razione del sito (animali in lattazione)
SiteDetailsSetupViewModel.DietSetup=Imposta razione
SiteDetailsSetupViewModel.Diets=Razioni
SiteDetailsSetupViewModel.DryMatterIntake=Ingestione s.s. ({0})
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Impostazioni sito
SiteDetailsSetupViewModel.InfoSiteSetup=Aggiungere almeno un sito per ogni cliente per iniziare una nuova visita. Le info obbligatorie per creare un nuovo sito sono\: nome, prezzo del latte, sistema di mungitura e gruppi produttivi (clicca su "Imposta gruppo"). Puoi aggiungere o aggiornare i dati del sito in questa pagina oppure durante la visita all'interno dei singoli strumenti. I gruppi produttivi saranno caricati automaticamente se Dairy Enteligen è connessa con DDW (evidenziati con la dicitura "Dati aziendali").
SiteDetailsSetupViewModel.LactatingAnimals=Animali in mungitura
SiteDetailsSetupViewModel.MilkFatPercent=Grasso (%)
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Altri solidi (%)
SiteDetailsSetupViewModel.MilkProteinPercent=Proteine (%)
SiteDetailsSetupViewModel.MilkYield=Produzione latte ({0})
SiteDetailsSetupViewModel.MilkingSystem=Sistema di mungitura
SiteDetailsSetupViewModel.NameNotUnique=Il sito "{0}" è già esistente. Il nome deve essere unico
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=NEL (Mcal/{0})
SiteDetailsSetupViewModel.NewSite=Nuovo sito
SiteDetailsSetupViewModel.NullSiteName=Nome del sito, prezzo del latte, sistema di mungitura e gruppo sono obbligatori. Desideri continuare o eliminare il sito?
SiteDetailsSetupViewModel.NumberOfStalls=Posti totali in sala mungitura
SiteDetailsSetupViewModel.PenSetup=Imposta gruppo
SiteDetailsSetupViewModel.Pens=Gruppi
SiteDetailsSetupViewModel.RationCost=Costo/capo ({0})
SiteDetailsSetupViewModel.SiteMandatoryFields=Nome del sito, prezzo del latte, sistema di mungitura e gruppo sono obbligatori. Desideri continuare o eliminare il sito?
SiteDetailsSetupViewModel.SiteName=Nome del sito
SiteDetailsSetupViewModel.SiteSetup=Impostazioni sito produttivo
SiteDetailsSetupViewModel.SomaticCellCount=CCS (1.000 cells/ml)
SiteDetailsSetupViewModel.Title=Dettagli sito produttivo
SiteDetailsSetupViewModel.WeightImperialCWT=Q.le
SiteVisitSummary=Riepilogo visita
SiteVisitSummaryReport=Riepilogo report visita
SixToEightLayers=6-8 strati
SixToTwelveHours=6-12 ore
SixToTwelveInches=15-30cm
Sligo=Sligo
Slovakia=Slovacchia
Slovenia=Slovenia
Solomon_Islands=Isole Salomone
Somalia=Somalia
SomanticCellCount=Conta Cellule Somatiche
SomaticCellPerML=CCS (cells/ml)
Sondrio=Sondrio
Sonora=Sonora
SouthAfrica=Sud Africa
SouthKorea=Sud Corea
South_Africa=Sud Africa
South_Australia=sud dell'Australia
South_Carolina=Carolina del Sud
South_Dakota=Sud Dakota
South_Georgia_and_the_South_Sandwich_Islands=South Georgia e South Sandwich Islands
South_Sudan=Sudan del Sud
Spain=Spagna
Sri_Lanka=Sri Lanka
StandardDeviationScoreTitle=Deviazione Standard
StartDate=Data inizio
StatusArchived=Archiviato
StatusCompleted=Completato
StatusInProgress=In corso
StdDevCalculated=Dev. Std. (calc.)
Steer=Vitellone
StorageCalculators=Calcolatrici di stoccaggio
StrategyToReduceDisplacedAbomasum=Dislocazione abomaso
StrategyToReduceDisplacedAbomasumDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategie per ridurre l'incidenza di dislocazione dell'abomaso&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Le strategie di prevenzione dipendono dalla gestione di molteplici fattori di rischio, per i quali non sono ancora chiare le cause.&lt;/p&gt;&lt;p&gt;La prevenzione include un'alimentazione appropriata, una corretta gestione e il controllo delle malattie infettive\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Controllo dei fattori di rischio nutrizionali\:&lt;ul&gt;&lt;li&gt;Evitare il sovrappeso delle bovine (BCS ideale in asciutta e in prossimità del parto\: 3.00).&lt;/li&gt;&lt;li&gt;Apportare una giusta q.tà di NDF da foraggi&lt;/li&gt;&lt;li&gt;Controllare la forma fisica della razione&lt;/li&gt;&lt;li&gt;Prestare attenzione ai requisiti minerali&lt;/li&gt;&lt;li&gt;Evitare malattie metaboliche (es. ipocalcemia) e altre malattie infettive che possono ridurre l'ingestione&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Miglioramento delle pratiche di gestione\:&lt;ul&gt;&lt;li&gt;Assicurare l'ingestione nelle vacche fresche, specialmente nel periodo successivo al parto&lt;/li&gt;&lt;li&gt;Gestire accuratamente le trincee&lt;/li&gt;&lt;li&gt;Aumentare il comfort delle bovine ricudendo ogni forma di stress&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceDystocia=Distocia
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategie per ridurre l'incidenza di distocia&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Aiuto nel prevenire la distocia\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Assicurarsi che le manze vengano inseminate all'età corretta e con un p.v. adeguato&lt;/li&gt;&lt;li&gt;Scegliere il toro sulla base del tasso di fertilità&lt;/li&gt;&lt;li&gt;Migliorare le proprie abilità riguardo tempistiche e metodi di intervento durante il parto, unito alla gestione di parti problematici&lt;/li&gt;&lt;li&gt;Revisionare il programma di razionamento sul MAX&lt;sup&gt;TM&lt;/sup&gt; per controllare i fabbisogni delle vacche in asciutta (far-off e close-up) e delle manze focalizzandosi su\:&lt;ul&gt;&lt;li&gt;Energia, per il mantenimento del BCS ed assicurare un corretto tasso di crescita del feto&lt;/li&gt;&lt;li&gt;Prevenire il sovrappeso delle bovine durante il parto&lt;/li&gt;&lt;li&gt;Controllare il rischio di ipocalcemia nel gruppo&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=Come ridurne l'incidenza?
StrategyToReduceKetosis=Chetosi
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategie per ridurre l'incidenza di chetosi&lt;/strong&gt;&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;Assicurare un adeguato comfort delle bovine (stato delle cuccette, temperatura e ventilazione, evitare stress e sovraffollamento, etc.)&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="2"&gt;&lt;li&gt;Formulare razioni bilanciate attraverso MAX&lt;sup&gt;TM&lt;/sup&gt; rispettando i requisiti nutrizionali e le caratteristiche fisiche della razione. Consultare il catalogo prodotti della transizione per formulare razioni specifiche e prevenire gli stati di chetosi. Assicurare l'utilizzo di foraggi di qualità per migliorare l'ingestione e l'attività tampone&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="3"&gt;&lt;li&gt;Monitorare il cambio di BCS fra far-off e parto\: BCS 3.00 ottimale da mantenere durante tutto il periodo di asciutta per evitare un'eccessiva lipomolizzazione dei grassi durante il parto&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="4"&gt;&lt;li&gt;Sviluppare ed implementare un protocollo sanitario specifico per vacche fresche al fine di identificare e prevenire disordini metabolici e malattie infettive. Monitorare ingestione, riempimento ruminale e stato di ruminazione&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=Mastiti
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategie per ridurre l'incidenza di mastiti&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;I microrganismi principalmente responsabili delle mastiti possono essere suddivisi in due categorie\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Patogeni contagiosi, che si diffondono fra gli animali durante la mungitiura&lt;em&gt;(i.e. S.agalactiae&amp;nbsp;and&amp;nbsp;S.aureus)&lt;/em&gt;&lt;/li&gt;&lt;li&gt;Patogeni ambientale, che derivano dall'ambiente in cui risiedono gli animali&lt;em&gt;(i.e. E.coli&amp;nbsp;and&amp;nbsp;S.uberis)&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;L'intervento per contrastare le mastiti deve essere adattato sulla base del microrganismo che causa l'infezione\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Controllo di mastiti contagiose&lt;/strong&gt;\: non c'è nessuna bacchetta magica per prevenire le infezioni di tutti i patogeni, tuttavia seguire alcune azioni preventive può aiutare\:&lt;ul&gt;&lt;li&gt;Valutare lo stato di igiene ambientale e strumentale durante la mungitura, inclusi i trattamenti pre e post-dipping&lt;/li&gt;&lt;li&gt;Mungere le vacche infette per ultime&lt;/li&gt;&lt;li&gt;Controllare la manutenzione delle macchine di mungitura&lt;/li&gt;&lt;li&gt;Revisionare la razione delle vacche in asciutta tramite MAX&lt;sup&gt;TM&lt;/sup&gt; seguendo le linee guida per assicurare un corretto apporto nutrizionale (energia e antiossidanti) per supportare le funzioni immunitarie&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Controllo di mastiti ambientali\:&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;Verificare che le cuccette siano pulite e asciutte&lt;/li&gt;&lt;li&gt;Controllare la corretta ventilazione del fienile e la densità di stoccaggio&lt;/li&gt;&lt;li&gt;Valutare l'igiene complessiva della mammella&lt;/li&gt;&lt;li&gt;Controllare le operazioni di routine della sala di mungitura&lt;/li&gt;&lt;li&gt;Revisionare la razione delle vacche in asciutta tramite MAX&lt;sup&gt;TM&lt;/sup&gt; seguendo le linee guida per assicurare un corretto apporto nutrizionale (energia e antiossidanti) per supportare le funzioni immunitarie&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=Metrite
StrategyToReduceMetritisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategie per ridurre l'incidenza di metriti&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;I fattori di rischio per le metriti includono\: ritenzioni placentari, ferite del tratto riproduttivo dovuto a parti difficoltosi, inadeguati protocolli di parto, sala parto "sporca", deficit nutrizionali (i.e. vitamina E e selenio) e bovine in sovrappeso.&lt;/p&gt;&lt;p&gt;Monitorare le seguenti aree, per ordine di importanza&amp;nbsp;\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Pratiche di parto\:&lt;br /&gt;&lt;ul&gt;&lt;li&gt;I dipendenti trasmettono patogeni all'utero quando assitono al parto?&lt;/li&gt;&lt;li&gt;Le vacche sono assistite troppo presto o troppo tardi?&lt;/li&gt;&lt;li&gt;I vitelli sono stati "strattonati" troppo spesso?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Pratiche di trattamento\:&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Come vengono trattate le vacche con RP o metriti?&lt;/li&gt;&lt;li&gt;Esiste la possibilità di trasmettere batteri dall'ambiente esterno all'utero al momento del parto?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="3"&gt;&lt;li&gt;Fattori di stres\:s&lt;br /&gt;&lt;ul style\="list-style-type\: circle;"&gt;&lt;li&gt;Stati di stress antecedenti al parto possono compromettere il sistema immunitario della bovina, abbassando la resistenza ad ogni tipo di infezione dopo il parto&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="4"&gt;&lt;li&gt;Nutrizione\:&lt;ul&gt;&lt;li&gt;Revisionare il bilancio della razione delle vacche in asciutta, focalizzando l'attenzione su BCS, bilancio minerale e supporto di sostanze antiossidanti (i.e. vitamine E, A, selenio, zinco and rame)&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Altre patologie (chetosi, LDA)&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMilkFever=Collasso puerperale
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategie per ridurre l'incidenza del collasso puerperale (febbre da latte)&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Due sono le strategie alternative per prevenire l'ipocalcemia nelle vacche da latte, entrambe basate sulla gestione alimentare\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Utilizzo di razioni povere in calcio&lt;/li&gt;&lt;li&gt;Utilizzo di razioni con DCAD basso&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Le aree da monitorare per mantenere l'ipocalcemia sotto controllo sono\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Analizzare la concentrazione minerale dei foraggi (calcio, fosforo, magnesio, potassio, sodio, zolfo e cloro)&lt;/li&gt;&lt;li&gt;Revisionare la razione attraverso MAX&lt;sup&gt;TM&lt;/sup&gt; rispettando i requisiti per vacche in close-up (asciutta) e le pratiche di gestione alimentare (attenzione all'alimentazione con foraggi ad libitum, la concentrazione di potassio, l'impiego di razioni con mangimi non bilanciati dal punto di vista minerale)&lt;/li&gt;&lt;li&gt;Consultare il catalogo dei prodotti per vacche in transizione per formulare razioni che impediscano l'insorgenza dell'ipocalcemia&lt;/li&gt;&lt;li&gt;Quando utilizzare il DCAD nelle razioni\:&lt;ul&gt;&lt;li&gt;Monitorare con attenzione l'ingestione di sali anionici che possono ridurre la palatabilità e possono ridurre l'ingestione di s.s.&lt;/li&gt;&lt;li&gt;Monitorare il pH delle urine per controllare l'efficienza del cambiamento della razione&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=Ritenzione placentare
StrategyToReduceRetainedPlacentaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;span&gt;&lt;strong&gt;Strategie per ridurre la ritenzione placentare&lt;/strong&gt;&lt;/span&gt;&lt;/h4&gt;&lt;p&gt;La ritenzione placentare è correlata positivamente con\: soppressione immunitaria, stress ormonali, ipocalcemia (anche subclinica), assistenza al parto e aborti, agenti infettivi endemici e genetica.&lt;/p&gt;&lt;p&gt;Monitorare le seguenti aree, in ordine di importanza\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Nutrizione\:&lt;br/&gt;&lt;p style\="padding-left\: 30px;"&gt;Revisionare la dieta del close-up attraverso MAX&lt;sup&gt;TM&lt;/sup&gt; seguendo linee guida con particolare attenzione su\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Selenio e vitamina E&lt;/li&gt;&lt;li&gt;Livello minerale (potassio, calcio, magnesio) per ridurre il rischio di ipocalcemia clinica e sub-clinica&lt;/li&gt;&lt;li&gt;Utilizzare DCAD basso o negativo per ridurre l'incidenza dell'ipocalcemia&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Gestione\:&lt;/li&gt;&lt;ul&gt;&lt;li&gt;Controllare il BCS durante l'ultima fase di lattazione e l'asciutta (BCS ideale al parto\: 3.00)&lt;/li&gt;&lt;li&gt;Minimizzare le fonti di stress nel close-up e post-parto per ridurre la mobilizzazione dei grassi e l'immunosoppressione a cavallo del parto&lt;/li&gt;&lt;li&gt;Migliorare il comfort delle bovine&lt;/li&gt;&lt;li&gt;Seguire le procedure del parto proposte dal veterinario&lt;/li&gt;&lt;li&gt;Non aiutare bovine durante il parto senza motivo&lt;/li&gt;&lt;li&gt;Mantenere l'ambiente pulito per ridurre infezioni uterine&lt;/li&gt;&lt;/ul&gt;&lt;/ol&gt;&lt;/span&gt;
Straw=Paglia
Stress=da caldo severo\: bocca aperta con bava, frequenza cardiaca 120-160bpm (anche maggiore)
Sudan=Sudan
Surinam=Suriname
Suriname=Suriname
SurveyCategories=Categorie sondaggi
SurveyOfForages=Gestione del foraggio
SurveyOfForages_AnnualCowNumAndForageNeeds=Il numero di animali e il fabbisogno di foraggi sono pianificati annualmente?
SurveyOfForages_AshLevelsInCornSilage=Qual è il tenore di ceneri?
SurveyOfForages_AshLevelsInHaylage=Qual è il tenore di ceneri?
SurveyOfForages_ButyricAcidLevelsInHaylage=Qual è il tenore di ac.butirrico?
SurveyOfForages_CornSilageProcessingScore=Qual è la lunghezza di taglio?
SurveyOfForages_CornSilageScoreMonitored=Qual è la durezza delle cariossidi nel silomais?
SurveyOfForages_InspectedForSpoilageAndMold=I foraggi vengono ispezionati per deterioramento o muffa ed eventualmente scartati?
SurveyOfForages_InventoryIsMonitored=Con quale frequenza viene monitorato l'inventario del foraggio?
SurveyOfForages_LacticAcidToAceticAcidLevels=Qual è il rapporto lattico\:acetico?
SurveyOfForages_LooseOrFacedFeedWithin=Il "cappello" si utilizza nelle razioni?
SurveyOfForages_NoLooseFeedRemaining=I rifiuti alimentari vengono rimossi e misurati quotidianamente?
SurveyOfForages_SilosSizedForCapacity=Le unità di stoccaggio del foraggio sono divise per capacità rispetto alle esigenze della mandria?
SurveyOfForages_VisibleSignsOfSoil=L'insilato è libero da segni visibili di contaminazione del suolo?
Svalbard_and_Jan_Mayen=Svalbard e Jan Mayen
Swaziland=Swaziland
Sweden=Svezia
Switzerland=Svizzera
Sync-failed-due-to-unknown-reason=Veuillez contacter l'administrateur pour résolution.
SyncFailed=La sincronizzazione non può essere completata in questo momento, si prega di riprovare
Sync_Data=Sincronizza
Syracuse=Siracusa
Syrian_Arab_Republic=Repubblica Araba Siriana
SystemGenerated=Generati dal sistema
São_Paulo=San £ o Paulo
THB=Thailandia (THB THB)
TMR=TMR
TMRHerdAnalysisTableTitle=TMR Particle Score
TMRParticleScore=TMR medio analisi Penn State
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Chiudi
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Giorni in latte (DIM)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Modifica DIM
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=DIM
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Modifica
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Strato B
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Strato C
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Analisi TMR Particle Score
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Strato A
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Fondo
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Analisi mandria
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Input
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Risultati
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Particle Score
TMRParticleScoreHerdAnalysisResultsText=Analisi TMR Particle Score
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Strato B (8mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Strato C (4mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Strato A (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Fondo
TRY=Provare
TWD=Taiwan (NT$ TWD)
Tabasco=Tabasco
Taipei_City=TAIPEI CITY
Taiwan=Taiwan
Tajikistan=Tajikistan
Tamaulipas=Tamaulipas
Tamil_Nadu=Tamil Nadu
Tanzania,_United_Republic_of=Tanzania, Repubblica unita
Taranto=Taranto
Task=Compito
Tasmania=Tasmania
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=Tennessee
Teramo=Teramo
Terni=Terni
Texas=Texas
TextureFeed=Starter strutturato
Thailand=Thailandia
Thirdparty=Thirdparty
ThisVisit=(Questa Visita)
ThreePotentialStorageSolutions=Tre potenziali soluzioni di stoccaggio
ThreeScreen=Settaccio a 3 strati
ThreeTimesPerWeek=3 v/settimana
Tianjin=Tianjin
Tiestall=Stalla legata
TimeRemaining=t rimanenete per riposare
Timor-Leste=Timor ha letto
Tipperary=Tipperary
Tlaxcala=Tlaxcala
Tocantins=Tocantine
Togo=Andare
Tokelau=Tokelau
Tokyo=Tokyo
Tonga=Arrivato
TonsAF=Tons t.q.
TonsDM=Tons s.s.
TonsPerDay=Tons/giorno
ToolNotSelected=Si deve scegliere almeno uno strumento
Top=Superiore
TopUnloadingSilo=Silo a torre
TopUnloadingSilos=Silo a torre
TopValue=(19mm)
Total=Totale
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=Animali totali nel gruppo
TotalAnimalsHerd=Animali totali
TotalPenPerScore=Animali nel gruppo per punteggio
TotalRevenue=Ricavi totali
TowerSilos=Silo a torre
TowerSilos_FaceRemovalRateGreaterThan4Inches=Il tasso di rimozione giornaliera è &gt;10cm/giorno?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Il silo viene coperto dopo il riempimento se non utilizzato immediatamente?
TowerSilos_SiloFillingTimeof3DaysOrLess=Il riempimento del silo viene fatto entro 3 giorni?
TransitionCow=Vacche in Transizione
Trapani=Trapani
Tray=Fondo
Trends=Trends positivi
Trento=Trento
Treviso=Treviso
Trieste=Trieste
Trinidad_and_Tobago=Trinidad e Tobago
Tripura=Tripura
Tunisia=Tunisia
Turin=Torino
Turkey=Tacchino
Turkmenistan=Turkmenistan
Turks_and_Caicos_Islands=Isole Turks e Caicos
Tuvalu=Tuvalu
TwelveInchesOrGreater=≥15 cm
TwentyFourHoursAndNoSync=Ventiquattro ore e nessuna sincronizzazione
TwentyFourHoursBeforeActionIsDue=Ventiquattro ore prima che l'azione sia dovuta
TwentyFourToThirtySixInchesPerDay=15-30 cm/giorno
TwicePerWeek=2 v/settimana
UAH=Ucraina (UAH UAH)
UK=Regno Unito
UNITED_STATES=Stati Uniti
US=Stati Uniti
USD=Stati Uniti d'America ($ USD)
Udine=Udine
Uganda=Uganda
Ukraine=Ucrania
UnitedKingdom=Regno Unito
UnitedStates=Stati Uniti
United_Arab_Emirates=Emirati Arabi Uniti
United_Kingdom=Regno Unito
United_States=stati Uniti
UrinePH=pH urine
UrinePHAVG=Media pH urine
UrinePHAverageNumber=Numero medio
UrinePHDensity=Guida Monitorare il pH delle urine
UrinePHEditCowViewModel.AddNew=Vacca seguente
UrinePHEditCowViewModel.CowName=Nome vacca
UrinePHEditCowViewModel.CowValue=Valore vacca
UrinePHEditCowViewModel.UrinePHEnterCowValue=Inserisci il dato della vacca
UrinePHEditCowViewModel.ValidCudInput=Prego inserire un dato valido
UrinePHEditGoalViewModel.GoalMax=Max
UrinePHEditGoalViewModel.GoalMin=Min
UrinePHEditGoalViewModel.TargetUrinePHRange=Range pH urine
UrinePHEditGoalViewModel.Title=Modifica Obiettivi
UrinePHInputsViewModel.AddNew=Aggiungi nuovo
UrinePHInputsViewModel.CalculatorHeading=Seleziona una vacca e inserisci il pH delle urine. Clicca "Aggiungi nuovo" per aggiungere una nuova vacca
UrinePHInputsViewModel.CoefficientVariation=Coeff. di variabilità (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Vacche fuori range (%)
UrinePHInputsViewModel.CudChewCategorySection=Vacche
UrinePHInputsViewModel.DietDCAD=DCAD razione, mEq/100g
UrinePHInputsViewModel.Resources=Risorse
UrinePHInputsViewModel.TargetUrinePHRange=Range pH urine
UrinePHInputsViewModel.UrinePHAVG=Media pH urine (calc.)
UrinePHInputsViewModel.UrinePhSTDDEV=Dev. Std (calc.)
UrinePHMasterViewModel.Inputs=Input
UrinePHMasterViewModel.Results=Risultati
UrinePHMasterViewModel.Title=pH urine
UrinePHPenSelectionViewModel.Title=pH urine
UrinePHPenSelectionViewModel.UrinePHPenList=Gruppi (solo lattazione e asciutta)
UrinePHResultsViewModel.DietDCAD=DCAD razione, mEq/100g
UrinePHResultsViewModel.MaxpH=pH Max
UrinePHResultsViewModel.MinpH=pH Min
UrinePHResultsViewModel.UrinePHAVG=Media pH urine
Uruguay=Uruguay
UserCreated=Creato
UserPreferencesViewModel.Branding=Brand
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=Valuta
UserPreferencesViewModel.Imperial=Imperiale
UserPreferencesViewModel.MainHeading=Le seguenti informazioni possono essere modificate in seguito nelle "Impostazioni"
UserPreferencesViewModel.Metric=Metrico
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Seleziona una valuta
UserPreferencesViewModel.SelectPointScale=Scegli la scala
UserPreferencesViewModel.Title=Impostazioni
UserPreferencesViewModel.UnitOfMeasure=Unità di Misura
UserPreferencesViewModel.UserPreferencesMilkProcessor=Imposta Caseificio
UserPreferencesViewModel.UserPreferencesMoreSettings=Altre impostazioni
User_Settings=Impostazioni
Utah=Utah
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
Uzbekistan=Uzbekistan
VEF=Venezuela (Bs VEF)
VND=Vietnam (₫ VND)
Vanuatu=Vanuatu
Varese=Varese
Venezuela=Venezuela
Venezuela,_Bolivarian_Republic_of=Venezuela, Repubblica bolivariana di
Venice=Venezia
Veracruz=Veracruz
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Vermont=Vermont
Verona=Verona
Vestland=Westland
Vibo_Valentia=Vibo Valentia
Vicenza=Vicenza
Victoria=Victoria
Viet_Nam=Vietnam
Vietnam=Vietnam
ViewOverallCalfHaiferScore=Visualizza il punteggio totale
ViewOverallForageScore=Visualizza il punteggio totale
Virgin_Islands,_British=Isole Vergini, britanniche
Virginia=Virginia
Visit.Report.Footer.Patent=Cargill Incorporated, le sue società madri e affiliate non garantiscono l'accuratezza di queste stime, a causa di numerosi fattori. Non vi è alcuna garanzia di produzione o di risultati finanziari. ©2023 Cargill, Incorporated. Tutti i diritti riservati.
VisitAutoPublished=Visita Auto pubblicato
VisitDate=Data della visita
VisitDownloadProceed=Procedere
VisitNotebook=Notebook
VisitNotesViewModel.Action=Azione
VisitNotesViewModel.Close=Chiudi
VisitNotesViewModel.DownloadingNotes=Download Note in corso…
VisitNotesViewModel.Event=Evento
VisitNotesViewModel.New=Nuovo
VisitNotesViewModel.NoteMetadata={0} @ {1} di {2}
VisitNotesViewModel.Observation=Osservazione
VisitNotesViewModel.Task=Compito
VisitNotesViewModel.Title=Notebook
VisitNotesViewModel.VisitNotebook=Notebook
VisitSummaryViewModel.CalfHeiferItem=Vitelli &amp; Manze
VisitSummaryViewModel.CalfHeiferScorecard=Segnapunti
VisitSummaryViewModel.CategorySection=Strumenti
VisitSummaryViewModel.ComfortHeatStressBanner=Stress da caldo - Gruppi
VisitSummaryViewModel.ComfortItem=Comfort
VisitSummaryViewModel.EmailReport=Email Report
VisitSummaryViewModel.FreeFormReport=Free Form Report
VisitSummaryViewModel.HealthItem=Salute
VisitSummaryViewModel.HeatstressEvaluationTitle=Stress da caldo
VisitSummaryViewModel.HerdAnalysis=Analisi mandria
VisitSummaryViewModel.InputsOutputsChart=Input / Risultati / Grafici
VisitSummaryViewModel.MilkProcessRevenueCalculator=Ricavi mungitura
VisitSummaryViewModel.NoToolPrompt=Nessuno strumento è stato completato
VisitSummaryViewModel.NutritionForage=Audit foraggi
VisitSummaryViewModel.NutritionItem=Nutrizione
VisitSummaryViewModel.NutritionPile=Capienza di trincee e mucchi
VisitSummaryViewModel.PenTimeTitle=Pen Time Budget
VisitSummaryViewModel.ProductivityItem=Produzione
VisitSummaryViewModel.RumenHealthBodyConditionTitle=Body Condition Score
VisitSummaryViewModel.RumenHealthLocomotionTitle=Locomotion Score
VisitSummaryViewModel.RumenHealthManureTitle=Manure Score
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Malattie metaboliche
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
VisitSummaryViewModel.RumenHealthTMRTitle=TMR Particle Score
VisitSummaryViewModel.RumenHealthTitle=Ruminazioni
VisitSummaryViewModel.RumenHealthUrinePHTitle=pH urine
VisitSummaryViewModel.Title=Riepilogo visita
VisitSummaryViewModel.VisitSummaryMilkCalc=Input / Risultati / Risorse
VisitSummaryViewModel.VisitTitle=Nome della visita
VisitViewModel.CalfHeiferItem=Vitelli &amp; Manze
VisitViewModel.CategorySection=Strumenti
VisitViewModel.ComfortItem=Comfort
VisitViewModel.Delete=Cancella visita
VisitViewModel.DeletePrompt=Sei sicuro di voler cancellare la visita? L'operazione non può essere annullata
VisitViewModel.HealthItem=Salute
VisitViewModel.Instructions=Scegli una voce della lista o un report dalla lista sottostante
VisitViewModel.NullVisitName=Il campo "Nome della visita" non può rimanere vuoto. Si prega di inserire il nome
VisitViewModel.NutritionItem=Nutrizione
VisitViewModel.ProductivityItem=Produzione
VisitViewModel.Publish=Pubblica
VisitViewModel.PublishNotes=Pubblica Note
VisitViewModel.PublishNotesPrompt=Sei sicuro di voler pubblicare le note della visita? L'operazione non può essere annullata
VisitViewModel.PublishPrompt=Sei sicuro di voler pubblicare la visita? L'operazione non può essere annullata
VisitViewModel.PublishVisit=Pubblica visita
VisitViewModel.SiteVisitSummary=Riepilogo visita
VisitViewModel.Title=Dettagli visita
VisitViewModel.ToolCategories=Strumenti
VisitViewModel.VisitNotebook=Notebook
VisitViewModel.VisitTitle=Nome della visita
VisitViewModel.WalkthroughReport=Visita guidata
Viterbo=Viterbo
VolumeImperial=gal
VolumeMetric=ml
WalkthroughPenSelectionViewModel.Pens=Gruppi
WalkthroughPenSelectionViewModel.Title=Visita guidata
WalkthroughReport=Visita guidata
WalkthroughReportHerdAnalysisViewModel.Appearance=Aspetto
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Pulizia cuccette
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Cuccette\: profondità e morbidezza
WalkthroughReportHerdAnalysisViewModel.Branding=Brand
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Comfort animali, % animali sdraiati
WalkthroughReportHerdAnalysisViewModel.Comments=Commenti
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Conta ruminazioni e masticazioni
WalkthroughReportHerdAnalysisViewModel.CudChewing=Ruminazioni (%)
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} Report
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} Report
WalkthroughReportHerdAnalysisViewModel.ExportSelected=Invia mail con le voci selezionate
WalkthroughReportHerdAnalysisViewModel.FinalObservations=Osservazioni finali
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Elaborazione Report ...
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Lesioni al garreto, % di animali
WalkthroughReportHerdAnalysisViewModel.MainHeading=Visita guidata
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Scoli nasali, % di animali
WalkthroughReportHerdAnalysisViewModel.Notes=Note
WalkthroughReportHerdAnalysisViewModel.Opportunities=Opportunità
WalkthroughReportHerdAnalysisViewModel.PensForExport=Gruppi da esportare
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Riempimento ruminale
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=Body Condition Score (BCS)
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Locomotion Score
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Manure Score
WalkthroughReportHerdAnalysisViewModel.SubHeading=Analisi mandria
WalkthroughReportHerdAnalysisViewModel.Title=Visita guidata - Analisi mandria
WalkthroughReportHerdAnalysisViewModel.Trends=Trends positivi
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Scariche uterine, % di animali
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Qualità dell'acqua
WalkthroughReportLandingViewModel.HerdAnalysis=Analisi mandria
WalkthroughReportLandingViewModel.PenAnalysis=Analisi gruppo
WalkthroughReportLandingViewModel.Title=Visita guidata
WalkthroughReportQualityViewModel.BeddingCleanliness=Seleziona il grado di pulizia
WalkthroughReportQualityViewModel.Clean=Pulita
WalkthroughReportQualityViewModel.Dirty=Sporca
WalkthroughReportQualityViewModel.ModeratelyClean=Moderatamente pulita
WalkthroughReportQualityViewModel.Title=Visita guidata
WalkthroughReportQualityViewModel.WaterQuality=Seleziona la qualità
WalkthroughReportViewModel.Appearance=Aspetto
WalkthroughReportViewModel.BeddingCleanliness=Pulizia lettiera/cuccette
WalkthroughReportViewModel.BeddingDepthSoft=Lettiera/cuccette\: Profondità e morbidezza
WalkthroughReportViewModel.Clean=Pulito
WalkthroughReportViewModel.ComfortItem=Comfort, % animali sdraiati
WalkthroughReportViewModel.Comments=Commenti
WalkthroughReportViewModel.CudChewCategorySection=Conta ruminazioni e masticazioni
WalkthroughReportViewModel.CudChewing=Ruminazioni (%)
WalkthroughReportViewModel.Current=Attuale
WalkthroughReportViewModel.Dirty=Sporco
WalkthroughReportViewModel.Goals=Obiettivo
WalkthroughReportViewModel.HockAbrasion=Lesioni al garreto, % animali
WalkthroughReportViewModel.ModeratelyClean=Moderatamente pulita
WalkthroughReportViewModel.NasalDischarge=Scoli nasali, % animali
WalkthroughReportViewModel.Opportunities=Opportunità
WalkthroughReportViewModel.Previous=Precedente
WalkthroughReportViewModel.RumenFill=Riempimento ruminale
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=Body Condition Score (BCS)
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Locomotion Score
WalkthroughReportViewModel.RumenHealthManureTitle=Manure Score
WalkthroughReportViewModel.Title=Visita guidata
WalkthroughReportViewModel.Trends=Trends positivi
WalkthroughReportViewModel.UterineDischarge=Scariche uterine, % animali
WalkthroughReportViewModel.WaterQuality=Qualità dell'acqua
Wallis_and_Futuna=Wallis e Futuna
Washington=Washington
Waterford=Waterford
Weekly=Ogni settimana
WeightDMInLengthImperial=Lbs s.s. in 1ft
WeightDMInLengthMetric=Kg DM in 1m
WeightImperial=Lbs
WeightImperialCWT=Q.le
WeightMetric=Kg
West_Bengal=Bengala occidentale
West_Virginia=Virginia dell'ovest
Western_Australia=Australia Occidentale
Western_Sahara=Sahara occidentale
Westmeath=Westmeath
Wexford=Wexford
Wicklow=Wicklow
Wisconsin=Wisconsin
WithinEightHours=Entro 8 ore
Wyoming=Wyoming
Xinjiang=Xinjiang
Xizang=Xizang
Yemen=Yemen
Yes=Si
Yucatán=Yucatán
Yukon_Territories=Territori Yukon
Yunnan=Yunnan
ZAR=Sudafrica (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=Zambia
Zhejiang=Zhejiang
Zimbabwe=Zimbabwe
abomaso=
placentare=
puerperale=
welcome.message=Hallo {0}
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
Dry_Corn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole




