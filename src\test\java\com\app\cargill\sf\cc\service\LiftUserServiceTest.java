/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.User;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftUserServiceTest {

  @Mock private LiftApiService liftApi;
  @Mock LiftConfig liftConfig;
  @InjectMocks private LiftUserService userService;

  @Test
  void whenUserExistsCorrectResultIsReturned() {
    SalesforceRecordsResponse<Serializable> recordsResponse = new SalesforceRecordsResponse<>();
    User user = new User();
    user.setEmail("<EMAIL>");
    user.setUsername("<EMAIL>");
    recordsResponse.setRecords(List.of(user));
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(recordsResponse);
    when(liftConfig.getDefaultOwnerId()).thenReturn("<EMAIL>");
    User result = userService.findUserByEmail("<EMAIL>");
    assertNotNull(result);
  }

  @Test
  void whenUserExistsWithDifferentDefaultValueCorrectResultIsReturned() {
    SalesforceRecordsResponse<Serializable> recordsResponse = new SalesforceRecordsResponse<>();
    User user = new User();
    user.setEmail("<EMAIL>");
    user.setUsername("<EMAIL>");
    recordsResponse.setRecords(List.of(user));
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(recordsResponse);
    when(liftConfig.getDefaultOwnerId()).thenReturn("");
    User result = userService.findUserByEmail("<EMAIL>");
    assertNotNull(result);
  }

  @Test
  void whenMultipleUsersExistCorrectExceptionIsThrown() {
    SalesforceRecordsResponse<Serializable> recordsResponse1 = new SalesforceRecordsResponse<>();
    User user = new User();
    user.setUsername("something@cargillanh");
    recordsResponse1.setRecords(List.of(user));
    recordsResponse1.setNextRecordsUrl("any-url");
    SalesforceRecordsResponse<Serializable> recordsResponse2 = new SalesforceRecordsResponse<>();
    recordsResponse2.setRecords(List.of(user));
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(recordsResponse1);
    when(liftApi.getRecordsPage(any(), any(), any())).thenReturn(recordsResponse2);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftConfig.getDefaultOwnerId()).thenReturn("<EMAIL>");
    IllegalArgumentException e =
        assertThrows(
            IllegalArgumentException.class, () -> userService.findUserByEmail("<EMAIL>"));
    assertEquals("Something's wrong. Too many users were found.", e.getMessage());
  }

  @Test
  void whenFindOwnerIdIsCalledWithWrongEmailNullIsReturned() {
    assertNull(userService.findOwner("test"));
  }

  @Test
  void whenNoUsersExistNullIsReturned() {
    SalesforceRecordsResponse<Serializable> recordsResponse = new SalesforceRecordsResponse<>();
    recordsResponse.setRecords(List.of());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(recordsResponse);
    assertNull(userService.findUserByEmail("<EMAIL>"));
  }

  @Test
  void whenUserResponseIsReturnedFieldsAreRead() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse usersBatch =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_user_response.json"),
            new TypeReference<SalesforceRecordsResponse<User>>() {});

    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(usersBatch);
    when(liftConfig.getDefaultOwnerId()).thenReturn("<EMAIL>");

    User user = userService.findUserByEmail("something");
    assertNotNull(user);
    assertEquals("Chatter Free", user.getProfile().getUserLicense().getName());
  }

  @Test
  void whenMultipleUsersContainIntegrationOnlyOneIsReturned() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse usersBatch =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_integration_user_response.json"),
            new TypeReference<SalesforceRecordsResponse<User>>() {});

    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(usersBatch);
    when(liftConfig.getDefaultOwnerId()).thenReturn("<EMAIL>");

    User user = userService.findUserByEmail("something");
    assertNotNull(user);
    assertEquals("<EMAIL>", user.getEmail());
  }

  @Test
  void whenMultipleUsersContainInvalidOnlyOneIsReturned() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse usersBatch =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift-invalid-user-response.json"),
            new TypeReference<SalesforceRecordsResponse<User>>() {});

    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(usersBatch);
    when(liftConfig.getDefaultOwnerId()).thenReturn("<EMAIL>");

    User user = userService.findUserByEmail("something");
    assertNotNull(user);
    assertEquals("<EMAIL>", user.getEmail());
  }

  @Test
  void findUserByEmailFromLift() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse usersBatch =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_salesfiorceuser_response.json"),
            new TypeReference<SalesforceRecordsResponse<User>>() {});
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(usersBatch);
    List<User> user = userService.fetchUserEmailData("something");
    assertNotNull(user);
  }
}
