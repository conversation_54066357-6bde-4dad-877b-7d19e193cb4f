/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.MilkProcessors;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MilkProcessorsRepository extends JpaRepository<MilkProcessors, Long> {

  @Query(
      value =
          "Select mp.* FROM milk_processors mp where mp.milk_processors_document ->> 'id' = :id",
      nativeQuery = true)
  MilkProcessors findByDocumentId(@Param("id") String id);
}
