/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.UUID;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ContentDetailsDocument implements Serializable {
  @JsonProperty("id")
  private UUID id;

  @JsonProperty("AccountId")
  private UUID accountId;

  @JsonProperty("LabyrinthVisitId")
  private String labyrinthVisitId;

  @JsonProperty("LabyrinthContentType")
  private Integer labyrinthContentType;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("ReportType")
  private Integer reportType;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("IsNew")
  private boolean isNew;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;
}
