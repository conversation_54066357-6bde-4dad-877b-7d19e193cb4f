/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.amazonaws.services.s3.AmazonS3Client;
import com.app.cargill.constants.HttpMethods;
import com.app.cargill.dto.S3PresignedUrlsDto;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.awscore.AwsRequestOverrideConfiguration;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

@ExtendWith(MockitoExtension.class)
class S3ServiceImplTest {
  @Mock private S3Presigner s3Presigner;
  @Mock private S3ClientFactory s3ClientFactory;
  @Mock private AwsCredentialsFactory awsCredentialsFactory;
  @InjectMocks private S3ServiceImpl s3ServiceImpl;
  private static MockWebServer mockBackEnd;
  @Mock ListObjectsV2Request listObjectsV2Request;
  @Mock S3Client s3Client;
  @Mock AwsRequestOverrideConfiguration awsRequestOverrideConfiguration;

  @BeforeEach
  void setUp() throws IOException {
    s3ServiceImpl.setAwsRegion("us-west-1");
    s3ServiceImpl.setBucketName("test");
    lenient()
        .when(awsCredentialsFactory.getCredentialsProvider())
        .thenReturn(() -> AwsBasicCredentials.create("x", "x"));
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();
  }

  @Test
  void passWhenS3PresignedUrlsAreValid() throws MalformedURLException {
    List<S3PresignedUrlsDto> urlInfos = new ArrayList<>();
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.PUT)
            .id("7531f4b0-48a9-11ed-b878-0242ac120002")
            .contentType("image/jpeg")
            .build());
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.GET)
            .id("7531f4b0-48a9-11ed-b878-0242ac120001")
            .build());
    System.setProperty("aws.region", "awsRegion");
    System.setProperty("aws.accessKeyId", "amazonAccessKey");
    System.setProperty("aws.secretAccessKey", "amazonSecretKey");
    PresignedGetObjectRequest get = mock(PresignedGetObjectRequest.class);
    when(get.url()).thenReturn(new URL("http://www.twst.com"));
    PresignedPutObjectRequest put = mock(PresignedPutObjectRequest.class);
    when(put.url()).thenReturn(new URL("http://www.test.com"));
    when(s3Presigner.presignGetObject(any(GetObjectPresignRequest.class))).thenReturn(get);
    when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class))).thenReturn(put);

    List<S3PresignedUrlsDto> result = s3ServiceImpl.generatePresignedUrls(urlInfos);
    result.stream().map(S3PresignedUrlsDto::getGeneratedUrl).forEach(Assertions::assertNotNull);
  }

  @Test
  void passWhenS3GetPresignedUrlsAreValid() {
    List<S3PresignedUrlsDto> urlInfos = new ArrayList<>();
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.GET)
            .id("7531f4b0-48a9-11ed-b878-0242ac120001")
            .build());
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.GET)
            .id("7531f4b0-48a9-11ed-b878-0242ac120001")
            .build());
    System.setProperty("aws.region", "awsRegion");
    System.setProperty("aws.accessKeyId", "amazonAccessKey");
    System.setProperty("aws.secretAccessKey", "amazonSecretKey");
    when(s3Presigner.presignGetObject(any(GetObjectPresignRequest.class)))
        .thenReturn(mock(PresignedGetObjectRequest.class));

    List<S3PresignedUrlsDto> result = s3ServiceImpl.generatePresignedUrls(urlInfos);
    result.stream().map(S3PresignedUrlsDto::getGeneratedUrl).forEach(Assertions::assertNull);
  }

  @Test
  void passWhenS3PutPresignedUrlsAreValid() {
    List<S3PresignedUrlsDto> urlInfos = new ArrayList<>();
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.PUT)
            .id("7531f4b0-48a9-11ed-b878-0242ac120001")
            .build());
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.PUT)
            .id("7531f4b0-48a9-11ed-b878-0242ac120001")
            .build());
    System.setProperty("aws.region", "awsRegion");
    System.setProperty("aws.accessKeyId", "amazonAccessKey");
    System.setProperty("aws.secretAccessKey", "amazonSecretKey");

    when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class)))
        .thenReturn(mock(PresignedPutObjectRequest.class));

    List<S3PresignedUrlsDto> result = s3ServiceImpl.generatePresignedUrls(urlInfos);
    result.stream().map(S3PresignedUrlsDto::getGeneratedUrl).forEach(Assertions::assertNull);
  }

  @Test
  void passWhenS3PresignedUrlsPutIsProvided() {
    List<S3PresignedUrlsDto> urlInfos = new ArrayList<>();
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.PUT)
            .id("7531f4b0-48a9-11ed-b878-0242ac120002")
            .contentType("image/jpeg")
            .build());
    List<S3PresignedUrlsDto> result = s3ServiceImpl.generatePresignedUrls(urlInfos);
    result.stream().map(S3PresignedUrlsDto::getGeneratedUrl).forEach(Assertions::assertNull);
  }

  @Test
  void passWhenS3PresignedUrlsWhenListIsNull() {
    List<S3PresignedUrlsDto> result = s3ServiceImpl.generatePresignedUrls(null);
    assertNull(result);
  }

  @Test
  void passWhensignBucket() {
    String result = s3ServiceImpl.signBucket(UUID.randomUUID().toString(), "image/png");
    assertNull(result);
  }

  @Test
  void passWhenSignedBucketIsNull() {
    String result = s3ServiceImpl.signBucket(null, "image/png");
    assertNull(result);
  }

  @Test
  void passWhenPresignedUrl() {
    String result = s3ServiceImpl.getPresignedUrl(UUID.randomUUID().toString());
    assertNull(result);
  }

  @Test
  void passWhenPresignedUrlIsNull() {
    String result = s3ServiceImpl.getPresignedUrl(null);
    assertNull(result);
  }

  byte[] getMokedFile() throws IOException {
    File mockedFile = new File("src/test/resources/s3test.png");
    return Files.readAllBytes(mockedFile.toPath());
  }

  @Test
  void passWhenUploadPrivateFile() throws IOException {
    AmazonS3Client client = mock(AmazonS3Client.class);
    when(s3ClientFactory.getS3Client()).thenReturn(client);
    when(client.getResourceUrl(any(), any())).thenReturn("something");
    String result = s3ServiceImpl.uploadFile(getMokedFile(), "s3test.png", false);
    assertNotNull(result);
  }

  @Test
  void passWhenUploadPublicFile() throws IOException {
    AmazonS3Client client = mock(AmazonS3Client.class);
    when(s3ClientFactory.getS3Client()).thenReturn(client);
    when(client.getResourceUrl(any(), any())).thenReturn("something");
    String result = s3ServiceImpl.uploadFile(getMokedFile(), "s3test.png", true);
    assertNotNull(result);
  }
}
