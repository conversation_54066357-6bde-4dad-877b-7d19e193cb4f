/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Business;
import com.app.cargill.document.UserDocument;
import com.app.cargill.dto.EnumSerializerDto;
import com.app.cargill.dto.MultilingualEnumSerializerDto;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IUserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class EnumProcessorServiceImplTest {

  // Do not delete to prevent NPE for UserService
  @Mock private IUserService userServiceImpl;
  @Mock private UserRepository userRepository;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @InjectMocks private EnumProcessorServiceImpl enumProcessorServiceImpl;

  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
  }

  @Test
  void whenFetchEnumsReturnValidResult() {

    EnumSerializerDto animalClasses = enumProcessorServiceImpl.fetchEnums();
    assertNotNull(animalClasses);
    assertNotNull(animalClasses.getHttpMethods());
    assertNotNull(animalClasses.getVisitStatus());
    assertEquals(2, animalClasses.getHttpMethods().length);
    assertEquals(3, animalClasses.getVisitStatus().length);
    assertEquals(7, animalClasses.getMetabolicTypeKeys().length);
    assertEquals(5, animalClasses.getNotesMediaTypes().length);
  }

  @Test
  void whenFetchEnumsMultilingualReturnValidResult() {
    init();
    when(userRepository.findByUserName(any()))
        .thenReturn(
            User.builder()
                .userDocument(
                    UserDocument.builder().userName("test").countryId(Business.Global).build())
                .build());
    MultilingualEnumSerializerDto animalClasses =
        enumProcessorServiceImpl.fetchEnumsMultilingual(resourceBundleMessageSource);
    assertNotNull(animalClasses);
    assertNotNull(animalClasses.getHousingSystem());
    assertNotNull(animalClasses.getCowFlowDesign());
    assertNotNull(animalClasses.getMilkPickup());
    assertNotNull(animalClasses.getMilkPickup());
    assertNotNull(animalClasses.getRobotType());
    assertNotNull(animalClasses.getPenSource());
    assertNotNull(animalClasses.getMilkingSystem());
    assertNotNull(animalClasses.getFeedingSystems());
    assertNotNull(animalClasses.getLactationStages());
    assertNotNull(animalClasses.getMetabolicTypeKeys());
    assertNotNull(animalClasses.getNoteCategoryType());
  }

  @Test
  void whenFetchEnumsMultilingualReturnValidResultWithItalianUser() {
    init();
    when(userRepository.findByUserName(any()))
        .thenReturn(
            User.builder()
                .userDocument(
                    UserDocument.builder().userName("test").countryId(Business.Italy).build())
                .build());
    MultilingualEnumSerializerDto animalClasses =
        enumProcessorServiceImpl.fetchEnumsMultilingual(resourceBundleMessageSource);
    assertNotNull(animalClasses);
    assertNotNull(animalClasses.getHousingSystem());
    assertNotNull(animalClasses.getCowFlowDesign());
    assertNotNull(animalClasses.getMilkPickup());
    assertNotNull(animalClasses.getMilkPickup());
    assertNotNull(animalClasses.getRobotType());
    assertNotNull(animalClasses.getPenSource());
    assertNotNull(animalClasses.getMilkingSystem());
    assertNotNull(animalClasses.getFeedingSystems());
    assertNotNull(animalClasses.getLactationStages());
    assertNotNull(animalClasses.getMetabolicTypeKeys());
    assertNotNull(animalClasses.getNoteCategoryType());
  }
}
