/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ExternalDataSource implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("Species__c")
  private String siteId;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("System__c")
  private String system;

  @JsonProperty("Unique_External_Key__c")
  private String uniqueExternalKey;

  @JsonProperty("ID__c")
  private String idCustom;

  @JsonProperty("Account__c")
  private String accountId;
}
