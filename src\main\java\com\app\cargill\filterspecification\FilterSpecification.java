/* Cargill Inc.(C) 2022 */
package com.app.cargill.filterspecification;

import com.app.cargill.constants.SearchOperation;
import com.app.cargill.model.Visits;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.springframework.data.jpa.domain.Specification;

@SuperBuilder
@Data
@AllArgsConstructor
public class FilterSpecification implements Specification<Visits> {

  /** */
  private static final long serialVersionUID = 1L;

  private static final String JSON_PATH_FUNCTION = "jsonb_extract_path_text";
  private static final String VISIT_DOCUMENT = "visitDocument";

  private List<SearchCriteria> list;

  public FilterSpecification() {
    this.list = new ArrayList<>();
  }

  public void add(SearchCriteria criteria) {
    list.add(criteria);
  }

  @Override
  public Predicate toPredicate(Root<Visits> root, CriteriaQuery<?> query, CriteriaBuilder builder) {

    List<Predicate> predicates = new ArrayList<>();

    if (list.isEmpty()) {
      return null;
    }

    // add criteria to predicate
    for (SearchCriteria criteria : list) {
      if (criteria.getOperation().equals(SearchOperation.IN)) {
        predicates.add(
            builder
                .in(
                    builder.function(
                        JSON_PATH_FUNCTION,
                        Object.class,
                        root.<Instant>get(VISIT_DOCUMENT),
                        builder.literal("" + criteria.getKey() + "")))
                .value(criteria.getValue()));
      } else if (criteria.getOperation().equals(SearchOperation.BETWEEN)) {
        String[] date = criteria.getValue().toString().split(",");
        predicates.add(
            builder.between(
                builder.function(
                    JSON_PATH_FUNCTION,
                    String.class,
                    root.<Instant>get(VISIT_DOCUMENT),
                    builder.literal("" + criteria.getKey() + "")),
                date[0],
                date[1]));
      } else if (criteria.getOperation().equals(SearchOperation.NOT_NULL)) {
        predicates.add(
            builder.isNotNull(
                builder.function(
                    JSON_PATH_FUNCTION,
                    Object.class,
                    root.get(VISIT_DOCUMENT),
                    builder.literal(criteria.getKey()))));
      }
    }

    if (Objects.isNull(predicates.toArray(new Predicate[0]))) {
      return null;
    }
    return builder.and(predicates.toArray(new Predicate[0]));
  }
}
