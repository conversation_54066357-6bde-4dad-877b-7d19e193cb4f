<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="001" author="Aweem">
        <sql>
           CREATE TABLE IF NOT EXISTS role  (
			   id   SERIAL PRIMARY KEY ,
			   code  varchar(100) NOT NULL,
			   role_name  varchar(100) NOT NULL,
			   deleted BOOLEAN NOT NULL,
			   created_date TIMESTAMP NOT NULL DEFAULT CURRENT_DATE,
			   updated_date TIMESTAMP  NULL
			) ;

			CREATE TABLE IF NOT EXISTS users  (
			   id   SERIAL PRIMARY KEY ,
			   account_type  varchar(255) DEFAULT NULL,
			   email  varchar(100) NOT NULL,
			   full_name  varchar(255) NOT NULL,
			   mobile_number  varchar(45) DEFAULT NULL,
			   password  varchar(70) DEFAULT NULL,
			   principal_name  varchar(45) DEFAULT NULL,
			   authentication_platform  varchar(255) DEFAULT NULL,
			   deleted BOOLEAN NOT NULL,
			   created_date TIMESTAMP NOT NULL DEFAULT CURRENT_DATE,
			   updated_date TIMESTAMP  NULL
			) ;

			CREATE TABLE IF NOT EXISTS  users_to_role  (
			   user_id  int NOT NULL,
			   role_id  int NOT NULL
			) ;

        </sql>
        <sql>
        	  CREATE TABLE IF NOT EXISTS oauth_client_details (
			  client_id VARCHAR(150) PRIMARY KEY,
			  resource_ids VARCHAR(256),
			  client_secret VARCHAR(256) NOT NULL,
			  scope VARCHAR(256),
			  authorized_grant_types VARCHAR(256),
			  web_server_redirect_uri VARCHAR(256),
			  authorities VARCHAR(256),
			  access_token_validity INTEGER,
			  refresh_token_validity INTEGER,
			  additional_information VARCHAR(4000),
			  autoapprove VARCHAR(256)
			);
			
			CREATE TABLE IF NOT EXISTS oauth_client_token (
			  token_id VARCHAR(256),
			  token bytea ,
			  authentication_id VARCHAR(150) PRIMARY KEY,
			  user_name VARCHAR(256),
			  client_id VARCHAR(256)
			);
			
			CREATE TABLE IF NOT EXISTS oauth_access_token (
			  token_id VARCHAR(256),
			  token bytea ,
			  authentication_id VARCHAR(256),
			  user_name VARCHAR(256),
			  client_id VARCHAR(256),
			  authentication bytea ,
			  refresh_token VARCHAR(256)
			);
			
			CREATE TABLE IF NOT EXISTS oauth_refresh_token (
			  token_id VARCHAR(256),
			  token bytea ,
			  authentication bytea
				);
			
			CREATE TABLE IF NOT EXISTS oauth_code (
			  code VARCHAR(256), authentication bytea
				);
        </sql>
    </changeSet>

</databaseChangeLog>