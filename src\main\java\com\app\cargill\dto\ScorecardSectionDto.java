/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScorecardSectionDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Used to order sections within a larger container.
  /// </summary>
  /// <value>The index.</value>
  private Integer index;
  /// <summary>
  /// The name of the section to display to the user.
  /// </summary>
  /// <value>The name of the section.</value>
  /// <remarks>This will be a key to the string resources file.</remarks>
  private String sectionName;

  /// <summary>
  /// Category Silage option to select question
  /// </summary>
  private List<ScorecardSilageDto> scorecardSilages;
}
