/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class CountryDocument {

  @JsonProperty("CountryCode")
  private String countryCode;

  @JsonProperty("CountryName")
  private String countryName;

  @JsonProperty("CountryBusinessIDMapping")
  private String countryBusinessIdMapping;

  private UUID id;
}
