/* Cargill Inc.(C) 2022 */
package com.app.cargill.exceptions;

import java.io.Serial;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomDEExceptions extends Exception {

  @Serial private static final long serialVersionUID = 1L;
  private final String message;
  private final Integer statusCode;

  public CustomDEExceptions(String message, Integer statusCode) {
    this.message = message;
    this.statusCode = statusCode;
  }

  public CustomDEExceptions(String message) {
    this.message = message;
    this.statusCode = 403;
  }
}
