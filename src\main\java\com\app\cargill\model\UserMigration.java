/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "user_migration")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
public class UserMigration extends BaseEntity implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @Column(name = "email")
  private String email;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private TaskStatus status;
}
