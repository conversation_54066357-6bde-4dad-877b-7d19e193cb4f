/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.admin;

import com.app.cargill.dto.admin.Visit;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class VisitAdminService {
  private final VisitsRepository visitsRepository;
  private final AccountsRepository accountsRepository;
  private final SitesRepository sitesRepository;

  public List<Visit> getLatestVisits() {
    return visitsRepository.findLatestVisits().stream().map(this::mapDbVisitToDto).toList();
  }

  private Visit mapDbVisitToDto(Visits dbVisit) {
    Visit visit = new Visit();
    visit.setUuid(dbVisit.getVisitDocument().getId());
    visit.setName(dbVisit.getVisitDocument().getVisitName());
    visit.setVisitDate(dbVisit.getVisitDocument().getVisitDate());
    visit.setUserEmail(dbVisit.getVisitDocument().getCreateUser());

    Accounts account =
        accountsRepository.findByAccountId(dbVisit.getVisitDocument().getCustomerId().toString());
    if (account == null) {
      log.warn(
          "ACCOUNT_NOT_FOUND visitId: {} accountId: {}",
          dbVisit.getVisitDocument().getId(),
          dbVisit.getVisitDocument().getCustomerId().toString());
    } else {
      visit.setAccountName(account.getAccountDocument().getAccountName());
    }

    Sites site = sitesRepository.findBySiteId(dbVisit.getVisitDocument().getSiteId().toString());
    if (site == null) {
      log.warn(
          "SITE_NOT_FOUND visitId: {} siteId: {}",
          dbVisit.getVisitDocument().getId(),
          dbVisit.getVisitDocument().getSiteId().toString());
    } else {
      visit.setSiteName(site.getSiteDocument().getSiteName());
    }

    return visit;
  }

  public Visit getUserFirstVisit(String email) {
    Visits dbVisit = visitsRepository.findFirstVisitByEmail(email);
    if (dbVisit == null) {
      return null;
    }
    return mapDbVisitToDto(dbVisit);
  }
}
