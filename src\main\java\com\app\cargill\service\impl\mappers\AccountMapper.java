/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.document.Contact;
import com.app.cargill.dto.AccountDto;
import com.app.cargill.dto.AddressDto;
import com.app.cargill.dto.ContactDto;
import com.app.cargill.model.Accounts;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
public class AccountMapper {

  private AccountMapper() {}

  public static AccountDto mapToDto(Accounts account) {
    if (account.getAccountDocument() == null) {
      String errorMessage =
          String.format("AccountDocument for account id: %1$s should not be null", account.getId());
      log.error(errorMessage);
      throw new IllegalArgumentException(errorMessage);
    }

    AccountDto accountDto =
        AccountDto.builder()
            .id(account.getAccountDocument().getId())
            .localId(account.getLocalId())
            .lastSyncTime(
                account.getUpdatedDate() != null
                    ? account.getUpdatedDate().toInstant()
                    : Instant.now())
            .businessName(account.getAccountDocument().getAccountName())
            .currentTime(Instant.now())
            .accountType(account.getAccountDocument().getAccountType())
            .isFavourite(account.getAccountDocument().isFavourite())
            .type(
                account.getAccountDocument().getSubTypeID() != null
                    ? account.getAccountDocument().getSubTypeID().getTypeId()
                    : null)
            .country(
                account.getAccountDocument().getPhysicalAddress() != null
                    ? account.getAccountDocument().getPhysicalAddress().getCountry()
                    : null)
            .customerCode(account.getAccountDocument().getCustomerCode())
            .segmentId(account.getAccountDocument().getSegmentStepOneId())
            .photoId(account.getAccountDocument().getPhotoId())
            .dateOfLastVisit(account.getAccountDocument().getDateOfLastVisit())
            .siteCount(account.getAccountDocument().getSiteCount())
            .active(account.getAccountDocument().getActive())
            .userRoles(
                account.getAccountDocument().getUserRoles() != null
                    ? account.getAccountDocument().getUserRoles()
                    : new ArrayList<>())
            .build();

    Set<String> users = new HashSet<>();
    if (!CollectionUtils.isEmpty(account.getAccountDocument().getUsers())) {
      users.addAll(account.getAccountDocument().getUsers());
    }

    accountDto.setUsers(users);

    List<ContactDto> contactDtoList = new ArrayList<>();

    if (!CollectionUtils.isEmpty(account.getAccountDocument().getContacts())) {
      for (Contact contact : account.getAccountDocument().getContacts()) {

        ContactDto contactDto =
            ContactDto.builder()
                //                .fullName(
                //                    ((!Strings.isBlank(contact.getFirstName()) ?
                // contact.getFirstName() : "")
                //                            + (!Strings.isBlank(contact.getLastName())
                //                                ? " ".concat(contact.getLastName())
                //                                : ""))
                //                        .trim())
                .firstName(contact.getFirstName())
                .lastName(contact.getLastName())
                .contactId(contact.getContactId())
                .phoneNumber(contact.getPhoneNumber())
                .sfdcId(null)
                .email(contact.getEmailAddress())
                .build();

        contactDtoList.add(contactDto);
      }
    }

    accountDto.setContacts(contactDtoList);

    if (account.getAccountDocument().getPhysicalAddress() != null) {
      AddressDto addressDto =
          AddressDto.builder()
              .city(account.getAccountDocument().getPhysicalAddress().getCity())
              .postalCode(account.getAccountDocument().getPhysicalAddress().getPostalCode())
              .state(account.getAccountDocument().getPhysicalAddress().getStateOrProvince())
              .street(account.getAccountDocument().getPhysicalAddress().getStreet())
              .build();

      accountDto.setPhysicalAddress(addressDto);
    }
    return mapDefaultModelAttributes(account, accountDto);
  }

  private static AccountDto mapDefaultModelAttributes(Accounts account, AccountDto accountDto) {
    accountDto.setCreatedDate(
        account.getCreatedDate() != null ? account.getCreatedDate().toInstant() : Instant.now());
    accountDto.setUpdatedDate(
        account.getUpdatedDate() != null ? account.getUpdatedDate().toInstant() : Instant.now());
    accountDto.setLocalId(account.getLocalId());
    accountDto.setDeleted(account.isDeleted());
    accountDto.setId(account.getAccountDocument().getId());

    return accountDto;
  }
}
