/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static com.app.cargill.utils.PageableUtil.getPageable;

import com.app.cargill.constants.DietSource;
import com.app.cargill.dto.AnalyzeDietOptimizationDto;
import com.app.cargill.dto.DietDto;
import com.app.cargill.dto.FormulateDietOptimizationDto;
import com.app.cargill.model.Diets;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.service.IAnimalClassService;
import com.app.cargill.service.IDietService;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service("dietServiceImpl")
@RequiredArgsConstructor
public class DietServiceImpl implements IDietService {
  private final DietRepository dietRepository;
  private final IUserService userServiceImpl;
  private final AccountsRepository accountsRepository;
  private final IAnimalClassService animalClassService;

  @Override
  public PageImpl<DietDto> getDietsPaginated(
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      DietSource dietSource) {

    Pageable pageable = getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    Page<Diets> diets =
        dietRepository.findByAccountIdAndDietSourceAndUpdatedDate(
            accountIdsByUser, dietSource.name(), lastSyncTime, pageable);
    if (Objects.isNull(diets)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        diets.stream().parallel().map(this::modelToDto).toList(),
        pageable,
        diets.getTotalElements());
  }

  private DietDto modelToDto(Diets diet) {

    return DietDto.builder()
        .id(diet.getDietDocument().getId())
        .labyrinthAccountId(diet.getDietDocument().getLabyrinthAccountId())
        .name(diet.getDietDocument().getName())
        .breedName(diet.getDietDocument().getBreedName())
        .breedId(diet.getDietDocument().getBreedId())
        .startDate(diet.getDietDocument().getStartDate())
        .endDate(diet.getDietDocument().getEndDate())
        .siteId(diet.getDietDocument().getSiteId())
        .animalType(diet.getDietDocument().getAnimalType())
        .barnId(diet.getDietDocument().getBarnId())
        .environmentName(diet.getDietDocument().getEnvironmentName())
        .reportMilkWeight(diet.getDietDocument().getReportMilkWeight())
        .numberOfAnimals(diet.getDietDocument().getNumberOfAnimals())
        .source(diet.getDietDocument().getSource())
        .selected(diet.getDietDocument().getSelected())
        .selectedPenGuids(diet.getDietDocument().getSelectedPenGuids())
        .isSystemGenerated(diet.getDietDocument().getIsSystemGenerated())
        .isDeleted(diet.isDeleted())
        .isActive(diet.getDietDocument().getIsActive())
        .analyzeOptimization(
            AnalyzeDietOptimizationDto.mapAnalyzeDietOptimizationToDto(
                diet.getDietDocument().getAnalyzeOptimization()))
        .formulateOptimization(
            FormulateDietOptimizationDto.mapFormulateDietOptimizationToDto(
                diet.getDietDocument().getFormulateOptimization()))
        .updatedDate(diet.getUpdatedDate().toInstant())
        .animalTypeId(
            diet.getDietDocument().getAnimalType() != null
                ? animalClassService.getAnimalTypeId(
                    diet.getDietDocument().getAnimalType().getSubClass())
                : UUID.fromString("00000000-0000-0000-0000-000000000000"))
        .build();
  }
}
