/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.document.Barn;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.dto.BarnDto;
import com.app.cargill.dto.SiteMappingDto;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
public class SiteMapper {

  private SiteMapper() {}

  public static List<SiteMappingDto> modelToDtoForSiteMappings(
      List<DataSourceMapping> dataSourceMappings) {
    if (CollectionUtils.isEmpty(dataSourceMappings)) {
      return Collections.emptyList();
    }

    List<SiteMappingDto> dto = new ArrayList<>();
    dataSourceMappings.forEach(
        sMapping ->
            dto.add(
                SiteMappingDto.builder()
                    .systemId(sMapping.getSystemId())
                    .systemName(sMapping.getSystemName())
                    .build()));

    return dto;
  }

  public static List<BarnDto> modelToDtoForBarn(List<Barn> barns) {

    if (CollectionUtils.isEmpty(barns)) {
      return Collections.emptyList();
    }

    List<BarnDto> dto = new ArrayList<>();
    barns.forEach(
        brn ->
            dto.add(
                BarnDto.builder()
                    .id(brn.getId())
                    .barnName(brn.getBarnName())
                    .createUser(brn.getCreateUser())
                    .build()));
    return dto;
  }

  public static List<DataSourceMapping> dtoToModelForSiteMappings(
      List<SiteMappingDto> siteMappings) {
    if (CollectionUtils.isEmpty(siteMappings)) {
      return Collections.emptyList();
    }

    List<DataSourceMapping> model = new ArrayList<>();
    siteMappings.forEach(
        sMapping ->
            model.add(
                DataSourceMapping.builder()
                    .systemId(sMapping.getSystemId())
                    .systemName(sMapping.getSystemName())
                    .build()));

    return model;
  }
}
