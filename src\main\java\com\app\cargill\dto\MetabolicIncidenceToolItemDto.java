/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetabolicIncidenceToolItemDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private List<UUID> selectedVisits;

  private MetabolicIncidenceOutputToolItemDto outputs;

  private Integer totalFreshCowsPerYear;

  private Double replacementCowCost;

  private Double costOfExtraDaysOpen;

  private Integer totalFreshCowsForEvaluation;

  private Integer retainedPlacentaIncidence;

  private Integer metritisIncidence;

  private Integer displacedAbomasumIncidence;

  private Integer ketosisIncidence;

  private Integer milkFeverIncidence;

  private Integer dystociaIncidence;

  private Integer deathLossIncidence;

  private Double retainedPlacentaWeight;

  private Integer retainedPlacentaDaysOpen;

  private Double retainedPlacentaCost;

  private Double metritisWeight;

  private Integer metritisDaysOpen;

  private Double metritisCost;

  private Double displacedAbomasumWeight;

  private Integer displacedAbomasumDaysOpen;

  private Double displacedAbomasumCost;

  private Double ketosisWeight;

  private Integer ketosisDaysOpen;

  private Double ketosisCost;

  private Double milkFeverWeight;

  private Integer milkFeverDaysOpen;

  private Double milkFeverCost;

  private Double dystociaWeight;

  @JsonProperty("dystociaDaysOpen")
  private Integer dystociaOpen;

  private Double dystociaCost;

  private Double deathLossWeight;

  @JsonProperty("deathLossDaysOpen")
  private Integer deathLossOpen;

  private Double deathLossCost;

  private Double milkPrice;
}
