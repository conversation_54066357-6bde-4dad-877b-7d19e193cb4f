/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.LinkedHashMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BCSToolHeardAnalysisReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String analysisType;

  @Builder.Default private LinkedHashMap<String, Double> milkHdDay = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> bcsAverage = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> bcsMin = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> bcsMax = new LinkedHashMap<>();
  @JsonIgnore private String[] lactationStages;
}
