/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.mapper.LiftAccountMapper;
import com.app.cargill.sf.cc.model.AccountMergeRecord;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiftAccountMergeService {

  private static final String DELETED_MERGE_ACCOUNTS_QUERY =
      "SELECT+Id,Name,MasterRecordId,MasterRecord.Name+FROM+Account+WHERE+IsDeleted=true+AND+MasterRecordId+!=+null";

  private final LiftApiService liftApi;
  private final LiftApiReactiveService liftApiReactiveService;
  private final LiftUserService liftUserService;
  private final LiftConfig liftConfig;
  private final AccountsRepository accountsRepository;

  /**
   * @param conditions A query conditions list like: "LastModifiedDate>2023-03-06T09:33:58.394993Z"
   *     For some fields you must use single quotes:
   *     "Owner.Email='<EMAIL>'"
   * @return accounts
   */
  public List<AccountMergeRecordDto> getAllMergedAccounts(List<String> conditions) {

    List<AccountMergeRecordDto> mergedAccountsList = new ArrayList<>();
    try {
      AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
      String query =
          conditions == null
              ? DELETED_MERGE_ACCOUNTS_QUERY
              : String.format(
                  "%s+AND+%s", DELETED_MERGE_ACCOUNTS_QUERY, String.join("+AND+", conditions));
      SalesforceRecordsResponse<AccountMergeRecord> accountsBatch =
          liftApi.getRecordsQuery(
              tokenAndApiPath.getAuthToken(),
              tokenAndApiPath.getApiPath(),
              query,
              new ParameterizedTypeReference<>() {});

      mergedAccountsList.addAll(
          accountsBatch.getRecords().stream()
              .map(LiftAccountMapper::transformMergedAccounts)
              .toList());

      log.trace("{} LIFT accounts obtained for merge request", mergedAccountsList.size());
    } catch (Exception e) {
      log.error("Error", e);
    }
    return mergedAccountsList;
  }
}
