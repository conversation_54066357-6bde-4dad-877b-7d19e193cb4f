/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Getter
@Setter
public class S3ClientFactory {
  @Value("${cloud.aws.s3.access-key}")
  private String amazonAccessKey;

  @Value("${cloud.aws.s3.secret-key}")
  private String amazonSecretKey;

  @Value("${cloud.aws.region}")
  private String awsRegion;

  public AmazonS3 getS3Client() {
    log.debug(" - creating s3 client");

    Regions region = Regions.fromName(awsRegion);
    if (StringUtils.isAllBlank(amazonAccessKey, amazonSecretKey)) {
      return AmazonS3ClientBuilder.standard().withRegion(region).build();
    } else {
      AWSCredentials credentials = new BasicAWSCredentials(amazonAccessKey, amazonSecretKey);
      return AmazonS3ClientBuilder.standard()
          .withCredentials(new AWSStaticCredentialsProvider(credentials))
          .withRegion(region)
          .build();
    }
  }
}
