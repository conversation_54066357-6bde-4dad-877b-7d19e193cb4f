<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="format-detection" content="date=no">
	<meta name="format-detection" content="address=no">
	<meta name="format-detection" content="telephone=no">
	<meta name="x-apple-disable-message-reformatting">
	<link href="../../app/generatedTemplateResources/css/main.css" rel="stylesheet">
	<script src="../../app/generatedTemplateResources/js/chart.js"></script>
	<title>Cargill</title>
</head>
<body>

	<div class="container">
		<div class="template-header">
			<figure>
				<img src="../../app/generatedTemplateResources/images/cargill-logo.svg">
			</figure>
		</div>

		<div class="card mb-5">
			<div class="card-header pt-5">
				<h4 class="mb-2">${model.visitName!}</h4>

				<div class="row">
					<div class="content-set">
						<label>${localization.getMessage("Report.Tool.Name", [], "Tool Name", locale)}:</label>
						<h4>${model.toolName!}</h4>
					</div>
					<div class="content-set mx-2">
						<label>${localization.getMessage("Report.Analysis.Type", [], "Analysis Type", locale)}:</label>
						<h4>${model.analysisType!}</h4>
					</div>
					<div class="content-set mx-2">
						<label>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}:</label>
						<h4>${model.visitDate!}</h4>
					</div>
					<div id="region" style="opacity: 0;">${region}</div>
				</div>
			</div>

			<div class="card-body">
				<div class="row">
					<div class="legend-wrap mb-2">
						<p class="middle-blue-solid">${localization.getMessage("Top", [], "Top", locale)}</p>
					</div>
					<div class="legend-wrap mb-2">
						<p class="jordy-blue-solid">${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title", [], "Mid 1", locale)}</p>
					</div>
					<div class="legend-wrap mb-2">
						<p class="blue-purple-solid">${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle", [], "Mid 2", locale)}</p>
					</div>
					<div class="legend-wrap mb-2">
						<p class="tulip-solid">${localization.getMessage("Tray", [], "Tray", locale)}</p>
					</div>

				</div>
                <div>
				    <canvas id="linechart"></canvas>
                </div>
			</div>

		</div>
	</div>


<script>

const bar1 = [
	<#list model.dataPoints as val>
	     ${(val.top)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar2 = [
	<#list model.dataPoints as val>
	     ${(val.mid1)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar3 = [
	<#list model.dataPoints as val>
	     ${(val.mid2)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar4 = [
	<#list model.dataPoints as val>
	     ${(val.tray)!'NaN'}<#sep>, </#sep>
	</#list>
];

const xAxis = [
<#list model.dataPoints as val>
    '${val.penName!}'<#sep>, </#sep>
</#list>
];

const ctx = document.getElementById("linechart").getContext("2d");
ctx.canvas.height = 200;
const borderWidth = 1;
const categoryPercentage = 0.8;
const barPercentage = 1;
const options = {
    type: "bar",
    data: {
        labels: xAxis,
        datasets: [{
                data: bar1,
                grouped: true,
                backgroundColor: "#7AD8DC",
                categoryPercentage: categoryPercentage,
                barPercentage: barPercentage,
                skipNull : true,
                borderColor: "rgba(0,0,0,0)",
                borderWidth: borderWidth,
            },

            {
                data: bar2,
                backgroundColor: "#83BEF4",
                categoryPercentage: categoryPercentage,
                barPercentage: barPercentage,
                skipNull : true,
                borderColor: "rgba(0,0,0,0)",
                borderWidth: borderWidth,
            },

            {
                data: bar3,
                backgroundColor: "#ABA1E3",
                categoryPercentage: categoryPercentage,
                barPercentage: barPercentage,
                skipNull : true,
                borderColor: "rgba(0,0,0,0)",
                borderWidth: borderWidth,
            },

            {
                data: bar4,
                backgroundColor: "#F18494",
                categoryPercentage: categoryPercentage,
                barPercentage: barPercentage,
                skipNull : true,
                borderColor: "rgba(0,0,0,0)",
                borderWidth: borderWidth,
            }
        ]
    },
    options: {
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                callbacks: {
                    title: () => null // or function () { return null; }
                },
                yAlign: 'bottom',
                backgroundColor: "#fff",
                borderColor: "rgba(0, 0, 0, 0.25)",
                borderWidth: 1,
                displayColors: false,
                bodyColor: "#307698",
                bodyAlign: "center",
            },
        },

        layout: {
            padding: {
                top: 20,
                right: 15
            }
        },

        responsive: true,


        scales: {
            y: {
                // beginAtZero: true,
                title: {
                    display: true,
                    color: '#6C7782',
                    text: '${localization.getMessage("", [], "", locale)}',
                    padding: {
                        bottom: 15,
                    }
                },

                grid: {
                    display: false,
                },
                suggestedMax: (scale) => {

                    var curr = scale.chart.data.datasets;
                    var arr = [];
                    for (let i = 0; i < curr.length; i++) {
                        arr.push(Math.max.apply(null, curr[i].data.filter(x => !isNaN(x))));
                    }
                    return Math.round(Math.max(...arr) + (Math.max(...arr) * 0.05));
                },
                suggestedMin: (scale) => {
                    var curr = scale.chart.data.datasets;
                    var arr = [];
                    for (let i = 0; i < curr.length; i++) {
                        arr.push(Math.min.apply(null, curr[i].data.filter(x => !isNaN(x))));
                    }
                    return Math.floor(Math.min(...arr) + (Math.min(...arr) * 0.15));
                },
                ticks: {
                    // Include a % sign in the ticks
                    callback: function(value, index, ticks) {
                        return value;
                    }
                }
            },

            x: {
                title: {
                    display: true,
                    color: '#6C7782',
                    text: '${localization.getMessage("", [], "", locale)}',
                    padding: {
                        top: 15,
                    }
                },
                grid: {
                    display: false,
                },
            }
        },
        animation: {
            duration: 0,
            onComplete: function() {
                var chart = this;
                var ctx = chart.ctx;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                ctx.fillStyle = '#6C7782';
                this.data.datasets.forEach(function(dataset, i) {
                    var meta = chart.getDatasetMeta(i);
                    meta.data.forEach(function(bar, index) {
                        var data = dataset.data[index];
                        let regionText = document.getElementById("region").innerHTML;
						data = isNaN(data) ? '': new Intl.NumberFormat(regionText).format(data);
                        var yIndex = bar.y - 12;
                        if (data && data < 0) {
                            yIndex = bar.y + 15;
                        }

                        var fontSize = Math.round(chart.chartArea.width / dataset.data.length / 3);
                        if (fontSize > 12) {
                            fontSize = 12;
                        } else if (fontSize < 5) {
                            fontSize = 5;
                        }
                        ctx.font = fontSize + 'px "Helvetica Neue", Helvetica, Arial, sans-serif'
                        ctx.save();
                        // Translate 0,0 to the point you want the text
                        ctx.translate(bar.x + 5, yIndex);
                        // Rotate context by -90 degrees
                        ctx.rotate(-0.4 * Math.PI);
                        ctx.textAlign = "center";
                        ctx.fillText(data, 0, 0);
                        ctx.restore();
                        chart.resize();
                    });
                });
            }
        }
    }
};
window.onload = function () {
window.myLine = new Chart(ctx, options);
};

</script>

</body>
</html>