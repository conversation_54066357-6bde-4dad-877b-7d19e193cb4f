/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.ArgumentMatchers.notNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.LongRunningTask.TaskName;
import com.app.cargill.model.TaskStatus;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.service.impl.AccountServiceImpl;
import com.app.cargill.service.impl.SiteServiceImpl;
import com.app.cargill.sf.cc.service.LongRunningTasksService;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class SalesforceSyncTriggerTest {

  @Mock private LiftSyncService liftSyncService;
  @Mock private AccountServiceImpl accountService;
  @Mock private SiteServiceImpl siteService;

  @Mock private LongRunningTasksService tasksService;

  @InjectMocks private SalesforceSyncTrigger syncTrigger;

  @Test
  void whenParameterIsNotProvidedNullIsPassed() {
    when(accountService.getAllUnsyncedAccounts(any())).thenReturn(new ArrayList<>());
    when(liftSyncService.syncAccountsToSalesforce(any()))
        .thenReturn(Mono.just(new SyncResult("any")));
    when(siteService.getAllUnsyncedSites(any())).thenReturn(new ArrayList<>());
    when(liftSyncService.syncSitesToSalesforce(any())).thenReturn(Mono.just(new SyncResult("any")));
    CompletableFuture<SyncResult> mockedFuture =
        CompletableFuture.completedFuture(new SyncResult("any"));
    when(liftSyncService.executeAccountsSync(any())).thenReturn(mockedFuture);
    when(liftSyncService.executeSitesSync(any())).thenReturn(mockedFuture);
    when(tasksService.saveTask(any())).thenReturn(generateTask(TaskName.UNDEFINED));

    syncTrigger.triggerSync(false);
    Mockito.verify(liftSyncService, Mockito.times(1)).executeSitesSync(null);
    Mockito.verify(liftSyncService, Mockito.times(1)).executeSitesSync(null);
  }

  @Test
  void whenParameterIsProvidedTimestampIsPassed() {
    when(accountService.getAllUnsyncedAccounts(any())).thenReturn(new ArrayList<>());
    when(liftSyncService.syncAccountsToSalesforce(any()))
        .thenReturn(Mono.just(new SyncResult("any")));
    when(siteService.getAllUnsyncedSites(any())).thenReturn(new ArrayList<>());
    when(liftSyncService.syncSitesToSalesforce(any())).thenReturn(Mono.just(new SyncResult("any")));
    CompletableFuture<SyncResult> mockedFuture =
        CompletableFuture.completedFuture(new SyncResult("any"));
    when(liftSyncService.executeAccountsSync(any())).thenReturn(mockedFuture);
    when(liftSyncService.executeSitesSync(any())).thenReturn(mockedFuture);
    when(tasksService.saveTask(any())).thenReturn(generateTask(TaskName.UNDEFINED));

    syncTrigger.triggerSync(true);
    Mockito.verify(liftSyncService, Mockito.times(1)).executeSitesSync(notNull());
    Mockito.verify(liftSyncService, Mockito.times(1)).executeSitesSync(notNull());
  }

  @Test
  void whenExceptionOccursInSyncsTheFlowFinishes() {
    when(tasksService.saveTask(any())).thenReturn(generateTask(TaskName.UNDEFINED));
    when(accountService.getAllUnsyncedAccounts(any())).thenReturn(new ArrayList<>());
    when(liftSyncService.syncAccountsToSalesforce(any())).thenThrow(new RuntimeException());
    when(siteService.getAllUnsyncedSites(any())).thenReturn(new ArrayList<>());
    when(liftSyncService.syncSitesToSalesforce(any())).thenThrow(new RuntimeException());
    when(liftSyncService.executeAccountsSync(any()))
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException()));
    when(liftSyncService.executeSitesSync(any()))
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException()));

    assertDoesNotThrow(() -> syncTrigger.triggerSync(true));
  }

  private LongRunningTask<Serializable> generateTask(TaskName taskName) {
    return new LongRunningTask<>(
        UUID.randomUUID().toString(),
        taskName,
        TaskStatus.RUNNING,
        new SyncResult(taskName.name()));
  }

  @Test
  void startLiftPullAccountsMergeSync_partialTrue_shouldSetTimestampAndCompleteSuccessfully() {
    // Arrange
    LongRunningTask<SyncResult> mockTask = new LongRunningTask<>();
    mockTask.setStatus(TaskStatus.RUNNING);

    doReturn(mockTask).when(tasksService).saveTask(any());

    SyncResult result = new SyncResult("test");
    when(liftSyncService.executeAccountsMergeSync(any()))
        .thenReturn(CompletableFuture.completedFuture(result));

    // Act
    LongRunningTask<SyncResult> returnedTask = syncTrigger.startLiftPullAccountsMergeSync(true);

    // Assert
    assertEquals(TaskStatus.COMPLETED, returnedTask.getStatus());
    assertEquals(result, returnedTask.getMetaData());
    verify(tasksService, times(2)).saveTask(any());
  }

  @Test
  void startLiftPullAccountsMergeSync_partialFalse_shouldPassNullTimestamp() {
    // Arrange
    LongRunningTask<SyncResult> mockTask = new LongRunningTask<>();
    mockTask.setStatus(TaskStatus.RUNNING);
    doReturn(mockTask).when(tasksService).saveTask(any());

    SyncResult result = new SyncResult("test");
    when(liftSyncService.executeAccountsMergeSync(isNull()))
        .thenReturn(CompletableFuture.completedFuture(result));

    // Act
    LongRunningTask<SyncResult> returnedTask = syncTrigger.startLiftPullAccountsMergeSync(false);

    // Assert
    assertEquals(TaskStatus.COMPLETED, returnedTask.getStatus());
    assertEquals(result, returnedTask.getMetaData());
    verify(tasksService, times(2)).saveTask(any());
  }

  @Test
  void startLiftPullAccountsMergeSync_shouldFailWhenExceptionOccurs() {
    // Arrange
    LongRunningTask<SyncResult> mockTask = new LongRunningTask<>();
    mockTask.setStatus(TaskStatus.RUNNING);
    doReturn(mockTask).when(tasksService).saveTask(any());

    RuntimeException ex = new RuntimeException("Simulated failure");
    when(liftSyncService.executeAccountsMergeSync(any()))
        .thenReturn(CompletableFuture.failedFuture(ex));

    // Act
    LongRunningTask<SyncResult> returnedTask = syncTrigger.startLiftPullAccountsMergeSync(true);

    // Assert
    assertEquals(
        TaskStatus.FAILED,
        returnedTask.getStatus()); // update to FAILED if failTask modifies status
    verify(tasksService, times(1))
        .saveTask(any()); // Second save may be skipped depending on your failTask logic
  }
}
