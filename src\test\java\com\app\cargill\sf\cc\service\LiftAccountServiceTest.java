/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.model.*;
import com.app.cargill.sf.cc.model.simple.Account;
import com.app.cargill.sf.cc.model.simple.User;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
class LiftAccountServiceTest {

  @Mock private LiftApiService liftApi;
  @Mock private LiftUserService liftUserService;
  @Mock ResourceBundleMessageSource bundleMessageSource;
  @InjectMocks private LiftAccountService accountService;
  @Mock LiftConfig liftConfig;
  @Mock private AccountsRepository accountsRepository;
  @Mock private LiftApiReactiveService liftApiReactiveService;
  @Mock private UserRepository userRepository;

  @Test
  void fetchingMultiplePagesOfSimpleAccountsReturnsAccounts() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse response1 =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/query-simple-accounts-list.json"),
            new TypeReference<SalesforceRecordsResponse<Account>>() {});
    response1.setNextRecordsUrl("next-url");
    SalesforceRecordsResponse response2 =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/query-simple-accounts-list.json"),
            new TypeReference<SalesforceRecordsResponse<Account>>() {});

    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(response1);
    when(liftApi.getRecordsPage(any(), any(), any())).thenReturn(response2);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    List<AccountDocument> accounts = accountService.getAllAccounts();
    assertNotNull(accounts);
    assertEquals(10, accounts.size());
  }

  @Test
  void whenUpdateSucceedsTrueIsReturned() {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setGoldenRecordId("random-id");
    accountDocument.setAccountType(0);
    Address physicalAddress = new Address();
    physicalAddress.setCountry("Test");
    accountDocument.setPhysicalAddress(physicalAddress);
    assertDoesNotThrow(
        () -> accountService.updateAccount(mock(AuthToken.class), "fake-url", accountDocument));
  }

  @Test
  void whenCreateSucceedsCorrectObjectIsReturned() throws Exception {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setAccountName("account-name");
    accountDocument.setOwnerId("owner-id");
    accountDocument.setAccountType(0);
    Address physicalAddress = new Address();
    physicalAddress.setCountry("Test");
    accountDocument.setPhysicalAddress(physicalAddress);
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    User user = new User();
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    AccountDocument result = accountService.createAccount(accountDocument, null, null);
    assertEquals("new-golden-record-id", result.getGoldenRecordId());
  }

  @Test
  void whenCreateSucceedsCorrectObjectIsReturnedWithAzure() throws Exception {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setAccountName("account-name");
    accountDocument.setOwnerId("owner-id");
    accountDocument.setAccountType(0);
    Address physicalAddress = new Address();
    physicalAddress.setCountry("Test");
    User user = new User();
    user.setUserType("test");
    user.setId("test");
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);

    when(liftUserService.findUserByEmail(any())).thenReturn(user);
    accountDocument.setPhysicalAddress(physicalAddress);
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    AccountDocument result = accountService.createAccount(accountDocument, null, null);
    assertEquals("new-golden-record-id", result.getGoldenRecordId());
  }

  @Test
  void whenIdIsNotPresentExceptionIsThrown() {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setAccountName("account-name");
    accountDocument.setOwnerId("owner-id");
    accountDocument.setPhysicalAddress(new Address());
    assertThrows(
        IllegalArgumentException.class,
        () -> accountService.createAccount(accountDocument, null, null));
  }

  @Test
  void whenNameIsNotPresentExceptionIsThrown() {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setOwnerId("owner-id");
    accountDocument.setPhysicalAddress(new Address());
    assertThrows(
        IllegalArgumentException.class,
        () -> accountService.createAccount(accountDocument, null, null));
  }

  @Test
  void whenOwnerIsNotPresentExceptionIsThrown() {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setAccountName("account-name");
    accountDocument.setPhysicalAddress(new Address());
    assertThrows(
        IllegalArgumentException.class,
        () -> accountService.createAccount(accountDocument, null, null));
  }

  @Test
  void whenCreateAccountHasAnExceptionItIsThrown() {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setAccountName("account-name");
    accountDocument.setOwnerId("owner-id");
    accountDocument.setAccountType(0);
    Address physicalAddress = new Address();
    physicalAddress.setCountry("Test");
    accountDocument.setPhysicalAddress(physicalAddress);
    User user = new User();
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    when(liftUserService.findUserByEmail(any())).thenReturn(user);

    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());

    assertThrows(
        CustomDEExceptions.class,
        () -> accountService.createAccount(accountDocument, null, bundleMessageSource));
  }

  @Test
  void whenChatterFreeUserCorrectOwnerIsReturned() {
    User user1 = new User();
    user1.setEmail("<EMAIL>");
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Chatter Free");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user1.setProfile(profileRecord);

    User defaultUser = new User();
    defaultUser.setEmail("<EMAIL>");
    defaultUser.setId("123");

    defaultUser.setProfile(profileRecord);
    when(liftUserService.findUserByEmail(any())).thenReturn(user1).thenReturn(defaultUser);

    String result = accountService.getSfOwnerId("<EMAIL>");

    assertEquals("123", result);
  }

  @Test
  void whenStandardUserCorrectOwnerIsReturned() {
    User user1 = new User();
    user1.setEmail("<EMAIL>");
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("Standard");
    ProfileRecord profileRecord = new ProfileRecord();
    profileRecord.setUserLicense(userLicenseRecord);
    user1.setProfile(profileRecord);

    User defaultUser = new User();
    defaultUser.setEmail("<EMAIL>");
    defaultUser.setId("123");

    defaultUser.setProfile(profileRecord);
    when(liftUserService.findUserByEmail(any())).thenReturn(defaultUser);

    String result = accountService.getSfOwnerId("<EMAIL>");

    assertEquals("123", result);
  }

  @Test
  void whenUserNotFoundNullIsReturned() {
    String result = accountService.getSfOwnerId("<EMAIL>");

    assertNull(result);
  }

  @Test
  void getDeletedAccountsSuccess() {
    List<Accounts> accountsresp = new ArrayList<>();
    Accounts acc = new Accounts();
    AccountDocument accDoc = new AccountDocument();
    accDoc.setGoldenRecordId("**********");
    acc.setAccountDocument(accDoc);
    acc.setLocalId("12_122_222");
    Account acc1 = new Account();
    acc1.setId("12121");

    accountsresp.add(acc);
    when(accountsRepository.getAllLiftAccounts()).thenReturn(accountsresp);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApiReactiveService.getRecords(any(), any(), any())).thenReturn(Mono.empty());
    List<Accounts> resp = accountService.getDeletedAccounts();
    assertNotNull(resp);
  }

  @Test
  void updateAccountNameSuccess() {

    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertDoesNotThrow(
        () -> {
          accountService.updateAccountName("record-id", "name");
        });
  }

  @Test
  void findMismatchIdTest() {

    List<AccountDocument> mismatchList = new ArrayList<>();
    List<AccountDocument> accDoc = new ArrayList<>();
    ObjectMapper objectMapper = new ObjectMapper();
    UUID targetUuid = UUID.randomUUID();
    UUID mismatchedId = UUID.randomUUID();

    ExternalDataSource liftExternalDataSource = new ExternalDataSource();
    liftExternalDataSource.setUniqueExternalKey(mismatchedId.toString());
    liftExternalDataSource.setSystem("LM");
    liftExternalDataSource.setIdCustom("idCustom");
    AccountDocument liftAccount = new AccountDocument();
    liftAccount.setGoldenRecordId("record-id");
    liftAccount.setId(targetUuid);
    liftAccount.setApplicationMappings(List.of(liftExternalDataSource));
    accDoc.add(liftAccount);
    List<AccountDocument> findList = accountService.findMismatchedId(liftAccount, mismatchList);
    assertNotNull(findList);
  }
}
