/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.forecast;

import com.app.cargill.constants.Business;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.UserRole;
import com.app.cargill.model.AccountRoles;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.utils.BusinessToCountryMapper;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class AccountRolesService {

  private final AccountsRepository accountsRepository;

  public List<AccountRoles> getAllAccountsRoles() {
    List<AccountRoles> accountsRolesResult = new ArrayList<>();
    List<Accounts> accountsList =
        accountsRepository.findAll().stream().filter(a -> !a.isDeleted()).toList();
    for (Accounts account : accountsList) {
      AccountDocument accountDocument = account.getAccountDocument();
      AccountRoles accountRoles = new AccountRoles();
      accountRoles.setAccountId(accountDocument.getId());
      accountRoles.setGoldenRecordId(accountDocument.getGoldenRecordId());
      accountRoles.setCountry(
          BusinessToCountryMapper.businessToCountry(
              Business.fromId(accountDocument.getBusinessID())));
      if (accountDocument.getUserRoles() == null || accountDocument.getUserRoles().isEmpty()) {
        if (accountDocument.getUsers() != null) {
          accountRoles
              .getUsers()
              .addAll(
                  accountDocument.getUsers().stream()
                      .map(u -> new AccountRoles.UserRole(u, "UNDEFINED"))
                      .toList());
        }
      } else {
        for (UserRole userRole : accountDocument.getUserRoles()) {
          accountRoles
              .getUsers()
              .add(new AccountRoles.UserRole(userRole.getUserName(), userRole.getRoleType()));
          accountRoles
              .getUsers()
              .add(new AccountRoles.UserRole(accountDocument.getOwnerId(), "OWNER"));
        }
      }
      accountsRolesResult.add(accountRoles);
    }
    return accountsRolesResult;
  }
}
