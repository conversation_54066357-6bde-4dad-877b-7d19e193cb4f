/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.HeatStressTool;

public class HeatStressCalculation {
  public HeatStressTool calculateFields(HeatStressTool tool) {
    if (tool == null) {
      return null;
    }

    tool.milkValueLossPerDay = milkValueLossPerDay(tool);
    tool.energyEquivalentMilkLossWeightInkg = energyEquivalentMilkLossWeightInKG(tool);
    tool.temperatureHumidityInCelsius = temperatureHumidityInCelsius(tool);
    tool.intakeAdjustmentPercent = intakeAdjustmentPercent(tool);
    tool.dmiReductionPercent = dmiReductionPercent(tool);
    tool.estimatedDryMatterIntakeWeightInkg = estimatedDryMatterIntakeWeightInKG(tool);
    tool.reductionInDMIWeightInkg = reductionInDMIWeightInKG(tool);
    tool.lossOfEnergyConsumedInMcal = lossOfEnergyConsumedInMcal(tool);

    return tool;
  }

  private Double energyEquivalentMilkLossWeightInKG(HeatStressTool tool) {
    return tool.energyEquivalentMilkLossWeightInkg != null
        ? roundAvoid(tool.energyEquivalentMilkLossWeightInkg, 1)
        : null;
  }

  public Double milkValueLossPerDay(HeatStressTool tool) {
    return (tool.avgLactatingAnimals != null
            && tool.avgCurrentMilkPrice != null
            && tool.energyEquivalentMilkLossWeightInkg != null)
        ? roundAvoid(
            tool.avgLactatingAnimals
                * tool.avgCurrentMilkPrice
                * tool.energyEquivalentMilkLossWeightInkg,
            1)
        : null;
  }

  public Double temperatureHumidityInCelsius(HeatStressTool tool) {
    return (tool.temperatureInCelsius != null
            && tool.humidityPercent != null
            && tool.hoursExposedToSun != null)
        ? roundAvoid(
            (((((1.8 * tool.temperatureInCelsius + 32)
                            - ((0.55 - 0.0055 * tool.humidityPercent)
                                * (1.8 * tool.temperatureInCelsius - 26.8)))
                        - 32)
                    / 1.8)
                + (0.4167 * tool.hoursExposedToSun)),
            1)
        : null;
  }

  private Double intakeAdjustmentPercent(HeatStressTool tool) {
    return tool.intakeAdjustmentPercent != null
        ? roundAvoid(tool.intakeAdjustmentPercent, 1)
        : null;
  }

  private Double dmiReductionPercent(HeatStressTool tool) {
    return tool.dmiReductionPercent != null ? roundAvoid(tool.dmiReductionPercent, 1) : null;
  }

  private Double estimatedDryMatterIntakeWeightInKG(HeatStressTool tool) {
    return tool.estimatedDryMatterIntakeWeightInkg != null
        ? roundAvoid(tool.estimatedDryMatterIntakeWeightInkg, 1)
        : null;
  }

  private Double reductionInDMIWeightInKG(HeatStressTool tool) {
    return tool.reductionInDMIWeightInkg != null
        ? roundAvoid(tool.reductionInDMIWeightInkg, 1)
        : null;
  }

  private Double lossOfEnergyConsumedInMcal(HeatStressTool tool) {
    return tool.lossOfEnergyConsumedInMcal != null
        ? roundAvoid(tool.lossOfEnergyConsumedInMcal, 2)
        : null;
  }
}
