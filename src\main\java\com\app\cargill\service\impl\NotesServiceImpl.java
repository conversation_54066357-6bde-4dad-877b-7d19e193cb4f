/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static com.app.cargill.utils.PageableUtil.getPageable;

import com.app.cargill.constants.*;
import com.app.cargill.document.NoteMediaItem;
import com.app.cargill.document.NotesDocument;
import com.app.cargill.dto.NoteMediaItemDto;
import com.app.cargill.dto.NotesDto;
import com.app.cargill.dto.NotesSearchDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.filterspecification.NotesFilterSpecification;
import com.app.cargill.filterspecification.SearchCriteria;
import com.app.cargill.model.Notes;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.INotesService;
import com.app.cargill.service.IUserService;
import com.app.cargill.utils.PageableUtil;
import com.azure.cosmos.implementation.Strings;
import java.time.Instant;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service("notesServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class NotesServiceImpl implements INotesService {
  private final IUserService userServiceImpl;
  private final NotesRepository notesRepository;
  private final VisitsRepository visitsRepository;
  private final SitesRepository sitesRepository;
  private final AccountsRepository accountsRepository;

  private Notes updateNotesModel(Notes notes, NotesDto notesDto, String currentLoggedInUser) {

    notes.getNotesDocument().setLastSyncTimeUtc(Instant.now());
    notes.getNotesDocument().setAccountId(notesDto.getAccountId());
    notes.getNotesDocument().setIsComment(notesDto.getIsComment());
    notes.getNotesDocument().setSiteId(notesDto.getSiteId());
    notes.getNotesDocument().setNote(notesDto.getNote());
    notes.getNotesDocument().setNew(notesDto.isNew());
    notes.getNotesDocument().setTitle(notesDto.getTitle());
    notes.getNotesDocument().setSection(notesDto.getSection());
    notes.getNotesDocument().setVisitId(notesDto.getVisitId());
    notes.getNotesDocument().setCategory(notesDto.getCategory());
    notes.getNotesDocument().setDeleted(notesDto.isDeleted());
    notes.getNotesDocument().setSectionId(notesDto.getSectionId());
    notes.getNotesDocument().setMobileLastUpdatedTime(notesDto.getMobileLastUpdatedTime());
    notes.getNotesDocument().setLastModifyUser(notesDto.getLastModifyUser());
    notes.getNotesDocument().setLastModifiedTimeUtc(notesDto.getLastModifiedTimeUtc());
    notes.getNotesDocument().setSectionTitle(notesDto.getSectionTitle());
    notes
        .getNotesDocument()
        .setActionNotificationDateTimeUtc(notesDto.getActionNotificationDateTimeUtc());
    notes.getNotesDocument().setNew(false);
    notes
        .getNotesDocument()
        .setMediaItems(
            notesDto.getMediaItems().stream()
                .map(mi -> mapToModelMediaItems(mi, currentLoggedInUser))
                .toList());
    notes.setDeleted(notesDto.isDeleted());
    return notes;
  }

  private NotesDto modelToDto(Notes note) {
    NotesDto build =
        NotesDto.builder()
            .localId(note.getLocalId())
            .updatedDate(note.getUpdatedDate().toInstant())
            .createdDate(note.getCreatedDate().toInstant())
            .deleted(note.isDeleted())
            .currentTimeStamp(Instant.now())
            .build();
    if (note.getNotesDocument() == null) {
      return build;
    }
    build.setId(note.getNotesDocument().getId());
    build.setAccountId(note.getNotesDocument().getAccountId());
    build.setSiteId(note.getNotesDocument().getSiteId());
    build.setNote(note.getNotesDocument().getNote());
    build.setNew(note.getNotesDocument().isNew());
    build.setTitle(note.getNotesDocument().getTitle());
    build.setSection(note.getNotesDocument().getSection());
    build.setSectionTitle(note.getNotesDocument().getSectionTitle());
    build.setVisitId(note.getNotesDocument().getVisitId());
    build.setCategory(note.getNotesDocument().getCategory());
    build.setSectionId(note.getNotesDocument().getSectionId());
    build.setCreateUser(note.getNotesDocument().getCreateUser());
    build.setCreateTimeUtc(note.getNotesDocument().getCreateTimeUtc());
    build.setLastModifyUser(note.getNotesDocument().getLastModifyUser());
    build.setLastSyncTimeUtc(note.getNotesDocument().getLastSyncTimeUtc());
    build.setLastModifiedTimeUtc(note.getNotesDocument().getLastModifiedTimeUtc());
    build.setMobileLastUpdatedTime(note.getNotesDocument().getMobileLastUpdatedTime());
    build.setIsComment(note.getNotesDocument().getIsComment());
    build.setActionNotificationDateTimeUtc(
        note.getNotesDocument().getActionNotificationDateTimeUtc());
    build.setMediaItems(
        note.getNotesDocument().getMediaItems().stream().map(this::mapToDtoMediaItems).toList());
    return build;
  }

  private NoteMediaItemDto mapToDtoMediaItems(NoteMediaItem mediaItem) {
    return NoteMediaItemDto.builder()
        .noteId(mediaItem.getNoteId())
        .mediaId(mediaItem.getMediaId())
        .latitude(mediaItem.getLatitude())
        .longitude(mediaItem.getLongitude())
        .createUtc(mediaItem.getCreateUtc())
        .mediaType(mediaItem.getMediaType())
        .createUserId(mediaItem.getCreateUserId())
        .labyrinthContentType(mediaItem.getLabyrinthContentType())
        .mediaName(mediaItem.getMediaName())
        .reportType(mediaItem.getReportType())
        .isNew(mediaItem.getIsNew())
        .attachmentMediaSequence(mediaItem.getAttachmentSequence())
        .build();
  }

  private Notes dtoToModel(NotesDto notesDto, String currentLoggedInUser) {

    if (notesDto == null) {
      return null;
    }
    NotesDocument build =
        NotesDocument.builder()
            .id(notesDto.getId())
            .accountId(notesDto.getAccountId())
            .siteId(notesDto.getSiteId())
            .note(notesDto.getNote())
            .isNew(notesDto.isNew())
            .title(notesDto.getTitle())
            .section(notesDto.getSection())
            .sectionTitle(notesDto.getSectionTitle())
            .visitId(notesDto.getVisitId())
            .category(notesDto.getCategory())
            .isDeleted(notesDto.isDeleted())
            .sectionId(notesDto.getSectionId())
            .createUser(
                Strings.isNullOrEmpty(notesDto.getCreateUser())
                    ? currentLoggedInUser
                    : notesDto.getCreateUser())
            .createTimeUtc(notesDto.getCreateTimeUtc())
            .lastModifyUser(notesDto.getLastModifyUser())
            .lastSyncTimeUtc(notesDto.getLastSyncTimeUtc())
            .lastModifiedTimeUtc(notesDto.getLastModifiedTimeUtc())
            .mobileLastUpdatedTime(notesDto.getMobileLastUpdatedTime())
            .actionNotificationDateTimeUtc(notesDto.getActionNotificationDateTimeUtc())
            .isComment(notesDto.getIsComment())
            .mediaItems(
                notesDto.getMediaItems().stream()
                    .map(mi -> mapToModelMediaItems(mi, currentLoggedInUser))
                    .toList())
            .build();

    return Notes.builder()
        .deleted(notesDto.isDeleted())
        .localId(notesDto.getLocalId())
        .notesDocument(build)
        .build();
  }

  private NoteMediaItem mapToModelMediaItems(
      NoteMediaItemDto mediaItemDto, String currentLoggedInUser) {
    return NoteMediaItem.builder()
        .noteId(mediaItemDto.getNoteId())
        .mediaId(mediaItemDto.getMediaId())
        .latitude(mediaItemDto.getLatitude())
        .longitude(mediaItemDto.getLongitude())
        .createUtc(mediaItemDto.getCreateUtc())
        .mediaType(mediaItemDto.getMediaType())
        .attachmentSequence(mediaItemDto.getAttachmentMediaSequence())
        .labyrinthContentType(mediaItemDto.getLabyrinthContentType())
        .mediaName(mediaItemDto.getMediaName())
        .reportType(mediaItemDto.getReportType())
        .isNew(mediaItemDto.getIsNew())
        .createUserId(
            Strings.isNullOrEmpty(mediaItemDto.getCreateUserId())
                ? currentLoggedInUser
                : mediaItemDto.getCreateUserId())
        .attachmentSequence(mediaItemDto.getAttachmentMediaSequence())
        .build();
  }

  public NotesFilterSpecification setSpecifications(NotesSearchDto notesSearchDto) {
    NotesFilterSpecification spec = new NotesFilterSpecification();

    if (!Strings.isNullOrEmpty(notesSearchDto.getTitle())) {
      spec.add(
          new SearchCriteria(SearchKey.TITLE, SearchOperation.LIKE, notesSearchDto.getTitle()));
    }
    if (!Objects.isNull(notesSearchDto.getCreationDateFrom())
        && !Objects.isNull(notesSearchDto.getCreationDateTo())) {
      spec.add(
          new SearchCriteria(
              SearchKey.CREATE_DATE,
              SearchOperation.BETWEEN,
              notesSearchDto.getCreationDateFrom() + "," + notesSearchDto.getCreationDateTo()));
    }
    if (!Objects.isNull(notesSearchDto.getUpdatedDateFrom())
        && !Objects.isNull(notesSearchDto.getUpdatedDateTo())) {
      spec.add(
          new SearchCriteria(
              SearchKey.UPDATED_DATE,
              SearchOperation.BETWEEN,
              notesSearchDto.getUpdatedDateFrom() + "," + notesSearchDto.getUpdatedDateTo()));
    }
    spec.add(filterNoteCategoryTypes(notesSearchDto));
    spec.add(filterTools(notesSearchDto));
    spec.add(filterAccountIds(notesSearchDto));
    spec.add(filterSiteIds(notesSearchDto));
    spec.add(filterVisitIds(notesSearchDto));
    return spec;
  }

  private SearchCriteria filterTools(NotesSearchDto notesSearchDto) {
    if (!Objects.isNull(notesSearchDto.getTools())) {
      return new SearchCriteria(SearchKey.SECTION, SearchOperation.IN, notesSearchDto.getTools());
    } else {
      List<String> sections = notesRepository.findDistinctSections();
      if (Objects.isNull(sections) || sections.isEmpty()) {
        return new SearchCriteria(SearchKey.SECTION, SearchOperation.IN, "");
      } else {
        return new SearchCriteria(SearchKey.SECTION, SearchOperation.IN, sections);
      }
    }
  }

  private SearchCriteria filterNoteCategoryTypes(NotesSearchDto notesSearchDto) {
    if (!Objects.isNull(notesSearchDto.getNoteCategoryTypes())) {
      return new SearchCriteria(
          SearchKey.CATEGORY, SearchOperation.IN, notesSearchDto.getNoteCategoryTypes());
    } else {
      return new SearchCriteria(
          SearchKey.CATEGORY,
          SearchOperation.IN,
          Arrays.stream(NoteCategoryType.values()).map(Enum::name).toList());
    }
  }

  private SearchCriteria filterSiteIds(NotesSearchDto notesSearchDto) {
    if (!Objects.isNull(notesSearchDto.getSiteIds())) {
      return new SearchCriteria(SearchKey.SITE_ID, SearchOperation.IN, notesSearchDto.getSiteIds());
    } else {
      String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
      List<String> accountIdsByUser =
          accountsRepository.findAccountIdsByUserWithAllFlags(
              currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
      List<String> siteIds = sitesRepository.findSiteIdsByAccountIds(accountIdsByUser);
      siteIds = siteIds.stream().filter(x -> !Strings.isNullOrEmpty(x)).toList();
      if (Objects.isNull(siteIds) || siteIds.isEmpty()) {
        return new SearchCriteria(SearchKey.SITE_ID, SearchOperation.IN, "");
      } else {
        return new SearchCriteria(SearchKey.SITE_ID, SearchOperation.IN, siteIds);
      }
    }
  }

  private SearchCriteria filterVisitIds(NotesSearchDto notesSearchDto) {
    if (!Objects.isNull(notesSearchDto.getVisitIds())) {
      return new SearchCriteria(
          SearchKey.VISIT_ID, SearchOperation.IN, notesSearchDto.getVisitIds());
    } else {
      String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
      List<String> accountIdsByUser =
          accountsRepository.findAccountIdsByUserWithAllFlags(
              currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
      List<String> siteIds = sitesRepository.findSiteIdsByAccountIds(accountIdsByUser);
      List<String> visitIds = visitsRepository.findVisitIdsBySiteIds(siteIds);
      visitIds = visitIds.stream().filter(x -> !Strings.isNullOrEmpty(x)).toList();
      if (Objects.isNull(visitIds) || visitIds.isEmpty()) {
        return new SearchCriteria(SearchKey.VISIT_ID, SearchOperation.IN, "");
      } else {
        return new SearchCriteria(SearchKey.VISIT_ID, SearchOperation.IN, visitIds);
      }
    }
  }

  private SearchCriteria filterAccountIds(NotesSearchDto notesSearchDto) {

    if (!Objects.isNull(notesSearchDto.getAccountIds())
        && !notesSearchDto.getAccountIds().isEmpty()) {
      return new SearchCriteria(
          SearchKey.ACCOUNT_ID, SearchOperation.IN, notesSearchDto.getAccountIds());
    } else {
      String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
      List<String> accountsIds =
          accountsRepository.findAccountIdsByUserWithAllFlags(
              currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
      accountsIds = accountsIds.stream().filter(x -> !Strings.isNullOrEmpty(x)).toList();
      if (Objects.isNull(accountsIds) || accountsIds.isEmpty()) {
        return new SearchCriteria(SearchKey.ACCOUNT_ID, SearchOperation.IN, "");
      } else {
        return new SearchCriteria(SearchKey.ACCOUNT_ID, SearchOperation.IN, accountsIds);
      }
    }
  }

  @Override
  public Page<NotesDto> getAllNotesPaginated(
      int page,
      int size,
      String sortBy,
      String sorting,
      Instant lastSyncTime,
      String currentLoggedInUserJsonObj,
      String currentLoggedInUser) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);

    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedInUserJsonObj, currentLoggedInUser);
    Page<Notes> notes =
        notesRepository.findByAccountIdAndUpdatedDate(
            accountIdsByUser, lastSyncTime, currentLoggedInUser, pageable);
    if (Objects.isNull(notes)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        notes.stream().parallel().map(this::modelToDto).toList(),
        pageable,
        notes.getTotalElements());
  }

  @Override
  public Page<NotesDto> getAllNotesOnlinePaginated(
      int page, int size, String sortBy, String sorting, NotesSearchDto notesSearchDto) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    NotesFilterSpecification spec = setSpecifications(notesSearchDto);
    Page<Notes> notes = notesRepository.findAll(spec, pageable);

    if (notes.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        notes.stream().parallel().map(this::modelToDto).toList(),
        pageable,
        notes.getTotalElements());
  }

  @Override
  public NotesDto save(NotesDto notesDto, String currentLoggedInUser) {
    if (Objects.isNull(notesDto.getLocalId())) {
      throw new NotFoundDEException("LocalId is null");
    }
    if (notesRepository.existsByLocalId(notesDto.getLocalId())) {
      throw new AlreadyExistsDEException("Duplicate Record, Local Id Already Exists.");
    }
    notesDto.setLastSyncTimeUtc(Instant.now());
    notesDto.setId(UUID.randomUUID());
    notesDto.setNew(true);
    return modelToDto(notesRepository.save(dtoToModel(notesDto, currentLoggedInUser)));
  }

  @Override
  public NotesDto update(NotesDto notesDto, String currentLoggedInUser) {

    Notes notes = notesRepository.findByNoteId(notesDto.getId().toString());
    if (notes == null) {
      throw new NotFoundDEException("No Notes found against ID: " + notesDto.getId());
    }
    notesDto.setLastModifyUser(currentLoggedInUser);
    notesDto.setLastModifiedTimeUtc(
        notesDto.getLastModifiedTimeUtc() != null
            ? notesDto.getLastModifiedTimeUtc()
            : Instant.now());
    return modelToDto(notesRepository.save(updateNotesModel(notes, notesDto, currentLoggedInUser)));
  }

  @Override
  public List<NotesDto> updateOrInsert(List<NotesDto> notesDto, String currentLoggedInUser) {
    List<NotesDto> result = Collections.synchronizedList(new ArrayList<>());

    notesDto.parallelStream()
        .forEachOrdered(
            note -> {
              try {
                if (note.getId() == null) {
                  result.add(save(note, currentLoggedInUser));
                } else {
                  result.add(update(note, currentLoggedInUser));
                }
              } catch (Exception e) {
                note.setResponseStatus(ResponseStatus.FAILED);
                note.setResponseMessage(e.getLocalizedMessage());
                result.add(note);
                log.error(e.getLocalizedMessage());
              }
            });
    return result;
  }
}
