/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.dto.UserPreferenceDto;
import com.app.cargill.model.UserPreferences;
import java.time.Instant;
import java.util.HashMap;

public class UserPreferenceMapper {

  private UserPreferenceMapper() {}

  public static UserPreferenceDto mapToDto(UserPreferences userPreference) {
    return UserPreferenceDto.builder()
        .updatedDate(
            userPreference.getUpdatedDate() != null
                ? userPreference.getUpdatedDate().toInstant()
                : null)
        .id(userPreference.getUserPreferenceDocument().getId())
        .createUser(userPreference.getUserPreferenceDocument().getCreateUser())
        .localId(userPreference.getLocalId())
        .createdDate(
            userPreference.getCreatedDate() != null
                ? userPreference.getCreatedDate().toInstant()
                : null)
        .currentTimeStamp(Instant.now())
        .brandList(userPreference.getUserPreferenceDocument().getBrandList())
        .eulaContent(userPreference.getUserPreferenceDocument().getEulaContent())
        .favourites(userPreference.getUserPreferenceDocument().getFavourites())
        .bcsPointScale(userPreference.getUserPreferenceDocument().getBcsPointScale())
        .defaultMilkUreaMeasure(
            userPreference.getUserPreferenceDocument().getDefaultMilkUreaMeasure())
        .defaultMilkPickup(userPreference.getUserPreferenceDocument().getDefaultMilkPickup())
        .lastEulaVersionAccepted(
            userPreference.getUserPreferenceDocument().getLastEulaVersionAccepted())
        .lastPrivacyVersionAccepted(
            userPreference.getUserPreferenceDocument().getLastPrivacyVersionAccepted())
        .lastSyncOperationDateTime(
            userPreference.getUserPreferenceDocument().getLastSyncOperationDateTime())
        .selectedCurrency(userPreference.getUserPreferenceDocument().getSelectedCurrency())
        .unitOfMeasure(userPreference.getUserPreferenceDocument().getUnitOfMeasure())
        .userId(userPreference.getUserPreferenceDocument().getUserId())
        .defaultValues(
            userPreference.getUserPreferenceDocument().getDefaultValues() != null
                ? userPreference.getUserPreferenceDocument().getDefaultValues()
                : new HashMap<>())
        .showBCSAnimalAnalysisToast(
            userPreference.getUserPreferenceDocument().isShowBCSAnimalAnalysisToast())
        .build();
  }
}
