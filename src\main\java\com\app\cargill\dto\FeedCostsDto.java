/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeedCostsDto implements Serializable {
  private static final long serialVersionUID = 1L;

  private Double forageFeedCostPerCowPerDay;
  private Double grainsCostPerCowPerDay;
  private Double totalOnFarmFeedCostPerCowPerDay;
  private Double purchasedBulkFeedPerCowPerDay;
  private Double purchasedBagsFeedPerCowPerDay;
  private Double totalPurchasedCostPerCowPerDay;
  private Double totalFeedCostPerCowPerDay;
  private Double feedCostPerKgOfBF;
  private Double feedCostPerHlOfMilk;
  private Double foragePercentage;
}
