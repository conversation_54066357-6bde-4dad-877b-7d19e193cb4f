/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Tool;
import com.app.cargill.constants.ToolGroup;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class CountryToolDocument extends EditableDocumentBase implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("CountryId")
  private Business countryId;

  @JsonProperty("ToolId")
  private Tool toolId;

  @JsonProperty("ToolGroupId")
  private ToolGroup toolGroupId;
}
