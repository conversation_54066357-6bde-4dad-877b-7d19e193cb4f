<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="022" author="Taha">
    <sql splitStatements="false">
    
    DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'Global'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'Global',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">
   
  DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'Canada'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'Canada',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">

DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'Italy'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'Italy',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">

DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'Brazil'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'Brazil',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">

DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'SouthAfrica'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'SouthAfrica',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">
 
DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'Poland'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'Poland',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">

DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ForagePennState' 
      AND country_tool_document->>'CountryId' = 'Hungary'
  ) THEN 
    INSERT INTO country_tools
    (created_date, deleted, local_id, updated_date, country_tool_document)
    VALUES
    ('2023-04-12 12:14:39.563', false, newLocalId, '2023-04-12 12:14:39.563', 
     jsonb_build_object(
       'id', newDocumentId,
       'IsNew', true,
       'ToolId', 'ForagePennState',
       'CountryId', 'Hungary',
       'IsDeleted', false,
       'CreateUser', null,
       'ToolGroupId', 'Nutrition',
       'CreateTimeUtc', '2021-11-11T03:31:45.121003600Z',
       'LastModifyUser', null,
       'LastSyncTimeUtc', null,
       'LastModifiedTimeUtc', '2021-11-11T03:31:45Z'
     )
    );
  END IF; 
END $$;

  
    </sql>
  </changeSet>
  
    
</databaseChangeLog>
