/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.VisitStatus;
import com.app.cargill.cosmos.model.SiteCosmos;
import com.app.cargill.cosmos.repo.SitesCosmosRepository;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.de.LiftSyncService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SitesDataFixServiceTest {

  @Mock private SitesRepository sitesRepository;
  @Mock private LiftSyncService liftSyncService;
  @Mock private VisitsRepository visitsRepository;
  @Mock private SiteMappingsRepository siteMappingsRepository;

  @Mock private SitesCosmosRepository sitesCosmosRepository;

  @InjectMocks private SitesDataFixService sitesDataFixService;

  @Test
  void fixServiceExecutesWithoutErrors() {

    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(UUID.randomUUID());
    Sites site1 = new Sites(siteDocument1);

    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(UUID.randomUUID());
    Sites site2 = new Sites(siteDocument2);

    List<Sites> sites = List.of(site1, site2);
    when(sitesRepository.findSitesWithMissingSiteMappings()).thenReturn(sites);
    when(liftSyncService.executeMissingMappingSiteSync(anyString()))
        .thenReturn(mock(SyncResult.class));
    StepVerifier.create(sitesDataFixService.fixSiteMissingSiteMappings())
        .expectNextCount(2)
        .verifyComplete();
    verify(liftSyncService, times(2)).executeMissingMappingSiteSync(anyString());
  }

  @Test
  void fixVisitsReferenceInSites() {

    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(UUID.randomUUID());
    Sites site1 = new Sites(siteDocument1);

    SiteDocument siteDocument2 = new SiteDocument();
    Visits visit =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder()
                    .visitDate(Instant.now())
                    .visitName("Test")
                    .id(UUID.randomUUID())
                    .status(VisitStatus.Published)
                    .build())
            .build();
    siteDocument2.setId(UUID.randomUUID());
    Sites site2 = new Sites(siteDocument2);

    List<Sites> sites = List.of(site1, site2);
    when(visitsRepository.findVisitsBySiteId(any())).thenReturn(List.of(visit));
    StepVerifier.create(sitesDataFixService.fixVisitsReference(sites))
        .expectNextCount(1)
        .verifyComplete();
  }

  @Test
  void fixVisitsReferenceInSitesWithEmptySitesList() {

    List<Sites> sites = new ArrayList<>();
    StepVerifier.create(sitesDataFixService.fixVisitsReference(sites))
        .expectNextCount(1)
        .verifyComplete();
  }

  @Test
  void fixSwitchedMappingsExecutesWithoutErrors() {

    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(UUID.randomUUID());
    Sites site1 = new Sites(siteDocument1);

    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(UUID.randomUUID());
    Sites site2 = new Sites(siteDocument2);

    List<Sites> sites = List.of(site1, site2);
    when(sitesRepository.findAll()).thenReturn(sites);
    when(liftSyncService.fixSwitchedSiteMappings(anyString())).thenReturn(mock(SyncResult.class));
    StepVerifier.create(sitesDataFixService.fixSiteSwitchedSiteMappings())
        .expectNextCount(2)
        .verifyComplete();
    verify(liftSyncService, times(2)).fixSwitchedSiteMappings(anyString());
  }

  @Test
  void fixExternalIdExecutesWithoutErrors() {

    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(UUID.randomUUID());
    Sites site1 = new Sites(siteDocument1);

    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(UUID.randomUUID());
    Sites site2 = new Sites(siteDocument2);

    List<Sites> sites = List.of(site1, site2);
    when(sitesRepository.findSitesWithMissingExternalId()).thenReturn(sites);
    when(liftSyncService.executeExternalIdSiteSync(anyString())).thenReturn(mock(SyncResult.class));
    StepVerifier.create(sitesDataFixService.fixSiteMissingExternalId())
        .expectNextCount(2)
        .verifyComplete();
    verify(liftSyncService, times(2)).executeExternalIdSiteSync(anyString());
  }

  @Test
  void fixSitesWithMissingDdwExecutesWithoutErrors() {

    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(UUID.randomUUID());
    siteDocument1.setDataSourceMappings(new ArrayList<>());
    Sites site1 = new Sites(siteDocument1);

    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(UUID.randomUUID());
    siteDocument2.setDataSourceMappings(new ArrayList<>());
    Sites site2 = new Sites(siteDocument2);

    SiteDocument siteDocument3 = new SiteDocument();
    siteDocument3.setId(UUID.randomUUID());
    siteDocument3.setDataSourceMappings(new ArrayList<>());
    Sites site3 = new Sites(siteDocument3);

    List<Sites> sites = List.of(site1, site2, site3);

    SiteCosmos siteCosmos1 = mock(SiteCosmos.class);
    DataSourceMapping dsm1 = new DataSourceMapping();
    dsm1.setSystemName("DDW");
    dsm1.setSystemId("12345");
    when(siteCosmos1.getDataSourceMappings()).thenReturn(List.of(dsm1));

    SiteCosmos siteCosmos2 = mock(SiteCosmos.class);
    when(siteCosmos2.getDataSourceMappings()).thenReturn(null);

    when(sitesRepository.findCrescendoSitesWithMissingDdw()).thenReturn(sites);
    when(sitesCosmosRepository.findById(anyString()))
        .thenReturn(Optional.of(siteCosmos1))
        .thenReturn(Optional.empty())
        .thenReturn(Optional.of(siteCosmos2));
    StepVerifier.create(sitesDataFixService.fixSitesWithMissingDdwMapping())
        .assertNext(s -> assertFalse(site1.getSiteDocument().getDataSourceMappings().isEmpty()))
        .verifyComplete();
  }

  @Test
  void fixHasReportInSites_ShouldUpdateHasReport_WhenSiteExists() {

    SiteMappings siteMapping1 =
        SiteMappings.builder()
            .siteMappingDocument(
                SiteMappingDocument.builder()
                    .labyrinthSiteId(UUID.randomUUID())
                    .ddwHerdId("33333")
                    .build())
            .build();
    when(siteMappingsRepository.findAllWithDDWHerdId()).thenReturn(List.of(siteMapping1));
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(SiteDocument.builder().id(UUID.randomUUID()).build())
                .build());

    StepVerifier.create(sitesDataFixService.fixHasReportInSites())
        .expectNext(siteMapping1)
        .verifyComplete();

    // Ensure `save` was called
    verify(sitesRepository, times(1)).save(any(Sites.class));
  }

  @Test
  void fixHasReportInSites_ShouldNotUpdate_WhenSiteDoesNotExist() {
    SiteMappings siteMapping1 =
        SiteMappings.builder()
            .siteMappingDocument(
                SiteMappingDocument.builder()
                    .labyrinthSiteId(UUID.randomUUID())
                    .ddwHerdId("33333")
                    .build())
            .build();

    when(siteMappingsRepository.findAllWithDDWHerdId()).thenReturn(List.of(siteMapping1));
    when(sitesRepository.findBySiteId(any())).thenReturn(null); // No site found

    StepVerifier.create(sitesDataFixService.fixHasReportInSites())
        .expectNext(siteMapping1)
        .verifyComplete();

    // Ensure `save` was never called
    verify(sitesRepository, never()).save(any(Sites.class));
  }

  @Test
  void fixHasReportInSites_ShouldHandleEmptyDatabase() {
    when(siteMappingsRepository.findAllWithDDWHerdId()).thenReturn(List.of());

    StepVerifier.create(sitesDataFixService.fixHasReportInSites()).verifyComplete();

    // Ensure no interactions with `sitesRepository`
    verify(sitesRepository, never()).findBySiteId(anyString());
    verify(sitesRepository, never()).save(any(Sites.class));
  }
}
