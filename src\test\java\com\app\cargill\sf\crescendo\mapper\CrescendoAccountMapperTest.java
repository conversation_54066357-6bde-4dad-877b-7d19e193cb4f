/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.model.Accounts;
import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import com.app.cargill.sf.crescendo.model.AccountStatusCrescendo;
import com.app.cargill.sf.crescendo.model.AccountTypeCrescendo;
import com.app.cargill.sf.crescendo.model.AdditionalInformationCrescendo;
import com.app.cargill.sf.crescendo.model.AddressCrescendo;
import com.app.cargill.sf.crescendo.model.ApplicationMappingCrescendo;
import com.app.cargill.sf.crescendo.model.BusinessUnitCrescendo;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import com.app.cargill.sf.crescendo.model.CurrencyCrescendo;
import com.app.cargill.sf.crescendo.model.NineBoxStepTwoIDCrescendo;
import com.app.cargill.sf.crescendo.model.SegmentStepOneIdCrescendo;
import com.app.cargill.sf.crescendo.model.SubTypeIdCrescendo;
import com.app.cargill.sf.crescendo.model.UserRoleCrescendo;
import java.time.Instant;
import java.util.List;
import org.junit.jupiter.api.Test;

class CrescendoAccountMapperTest {

  @Test
  void whenNewAccountIsCreatedRequiredFieldsArePresent() {
    AccountCrescendo accountCrescendo = new AccountCrescendo();
    accountCrescendo.setAccountType(AccountTypeCrescendo.valueOf("STANDARD"));
    accountCrescendo.setGoldenRecordId("0014C00000z4oALQAY");
    accountCrescendo.setAccountName("crescendo account 1");

    ContactCrescendo contactCrescendo = new ContactCrescendo();
    contactCrescendo.setGoldenRecordAccountId("0014C00000z4oALQAY");
    accountCrescendo.getContacts().add(contactCrescendo);

    accountCrescendo.setProspectValidated(true);
    accountCrescendo.setOwnerId("<EMAIL>");

    ApplicationMappingCrescendo applicationMappingCrescendo = new ApplicationMappingCrescendo();
    applicationMappingCrescendo.setSfdcAccountId("0014C00000z4oALQAY");
    accountCrescendo.setApplicationMapping(List.of(applicationMappingCrescendo));

    accountCrescendo.setIsDuplicate(false);
    accountCrescendo.setAutoValidate(true);
    accountCrescendo.setSourceSystem("LM");
    accountCrescendo.setType("012f1000000LJIrAAO");
    accountCrescendo.setSubTypeId(SubTypeIdCrescendo.valueOf("FARM_PRODUCER"));
    accountCrescendo.setAccountStatus(AccountStatusCrescendo.valueOf("APPROVED"));
    accountCrescendo.setBusinessId(BusinessUnitCrescendo.CFN_INDIA);
    accountCrescendo.setNineBoxStepTwoID(NineBoxStepTwoIDCrescendo.NOT_DEFINED);
    accountCrescendo.setSegmentStepOneId(SegmentStepOneIdCrescendo.NOAH);

    AddressCrescendo addressCrescendo = new AddressCrescendo();
    addressCrescendo.setCountry("India");
    accountCrescendo.setPhysicalAddress(addressCrescendo);

    AdditionalInformationCrescendo additionalInformationCrescendo =
        new AdditionalInformationCrescendo();
    additionalInformationCrescendo.setVatFarmer(false);
    accountCrescendo.setAdditionalInfo(additionalInformationCrescendo);

    accountCrescendo.setAccountCurrency(CurrencyCrescendo.INR);
    accountCrescendo.setUserRoles(List.of());
    accountCrescendo.setId("f0075257-b20e-4373-8358-557f307036eb");
    accountCrescendo.setCreateUser("<EMAIL>");
    accountCrescendo.setDeleted(false);
    accountCrescendo.setLastModifyUser("<EMAIL>");
    accountCrescendo.setCreateTimeUtc(Instant.now());
    accountCrescendo.setLastModifiedTimeUtc(Instant.now());
    accountCrescendo.setNew(false);
    accountCrescendo.setIsMobileFirst(true);

    UserRoleCrescendo userRoleCrescendo1 = new UserRoleCrescendo();
    userRoleCrescendo1.setUserName("user1");
    userRoleCrescendo1.setUserRole("Someone");
    userRoleCrescendo1.setUserBusinessUnit(BusinessUnitCrescendo.INDIA);
    UserRoleCrescendo userRoleCrescendo2 = new UserRoleCrescendo();
    userRoleCrescendo2.setUserName("user2");
    userRoleCrescendo2.setUserRole("Technical Specialist");
    userRoleCrescendo2.setUserBusinessUnit(BusinessUnitCrescendo.INDIA);
    accountCrescendo.setUserRoles(List.of(userRoleCrescendo1, userRoleCrescendo2));
    UserRoleCrescendo userRoleCrescendo3 = new UserRoleCrescendo();
    userRoleCrescendo3.setUserName("user3");
    userRoleCrescendo3.setUserRole("Technical Specialist");
    userRoleCrescendo3.setUserBusinessUnit(BusinessUnitCrescendo.INDIA);
    accountCrescendo.setUserRoles(
        List.of(userRoleCrescendo1, userRoleCrescendo2, userRoleCrescendo3));

    Accounts account = CrescendoAccountMapper.crescendoToModel(accountCrescendo);
    // Owner + 1 user
    assertEquals(2, account.getAccountDocument().getUsers().size());
  }

  @Test
  void whenContactsDoNotExistEmptyListIsReturned() {
    AccountDocument existingAccountDocument = new AccountDocument();
    Accounts existingAccount = new Accounts(existingAccountDocument);

    AccountDocument crescendoAccountDocument = new AccountDocument();
    Contact crescendoContact = new Contact();
    crescendoContact.setGoldenRecordAcountId("someValue");
    crescendoAccountDocument.setContacts(List.of(crescendoContact));
    Accounts crescendoAccount = new Accounts(crescendoAccountDocument);

    Accounts result = CrescendoAccountMapper.modelUpdate(existingAccount, crescendoAccount);
    assertTrue(result.getAccountDocument().getContacts().isEmpty());
  }

  @Test
  void whenContactsAreNotIncludedExistingAreUnchanged() {
    AccountDocument existingAccountDocument = new AccountDocument();
    Contact existingContact = new Contact();
    existingContact.setGoldenRecordAcountId("fake");
    List<Contact> existingContacts = List.of(existingContact);
    existingAccountDocument.setContacts(existingContacts);
    Accounts existingAccount = new Accounts(existingAccountDocument);

    AccountDocument crescendoAccountDocument = new AccountDocument();
    Accounts crescendoAccount = new Accounts(crescendoAccountDocument);

    Accounts result = CrescendoAccountMapper.modelUpdate(existingAccount, crescendoAccount);
    assertEquals(existingContacts, result.getAccountDocument().getContacts());
    assertEquals(
        "fake", result.getAccountDocument().getContacts().get(0).getGoldenRecordAcountId());
  }

  @Test
  void whenContactsExistIsIsUpdated() {
    AccountDocument existingAccountDocument = new AccountDocument();
    Contact existingContact = new Contact();
    existingContact.setGoldenRecordAcountId("fake");
    List<Contact> existingContacts = List.of(existingContact);
    existingAccountDocument.setContacts(existingContacts);
    Accounts existingAccount = new Accounts(existingAccountDocument);

    AccountDocument crescendoAccountDocument = new AccountDocument();
    Contact crescendoContact = new Contact();
    crescendoContact.setGoldenRecordAcountId("someValue");
    crescendoAccountDocument.setContacts(List.of(crescendoContact));
    Accounts crescendoAccount = new Accounts(crescendoAccountDocument);

    Accounts result = CrescendoAccountMapper.modelUpdate(existingAccount, crescendoAccount);
    assertEquals(existingContacts, result.getAccountDocument().getContacts());
    assertEquals(
        "someValue", result.getAccountDocument().getContacts().get(0).getGoldenRecordAcountId());
  }

  @Test
  void whenDataIsCorrectCorrectResponseIsReturned() {}
}
