/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.model.DietCosmos;
import com.app.cargill.cosmos.repo.DietsCosmosRepository;
import com.app.cargill.document.DietDocument;
import com.app.cargill.model.Diets;
import com.app.cargill.repository.DietRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class DietsMigrationTest {

  @Mock private DietsCosmosRepository cosmosRepository;
  @Mock private DietRepository dietRepository;
  @InjectMocks private DietsMigration dietsMigration;

  @Test
  void whenDietsMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    DietCosmos diet =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/diets.json"), DietCosmos.class);

    when(cosmosRepository.findAll()).thenReturn(Flux.just(diet, diet));

    MigrationResult result = dietsMigration.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenPostMigrationIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result = dietsMigration.postMigration(CosmosDataMigration.MigrationType.DIETS.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      List<DietCosmos> cosmosList = loadCosmosDiets();
      when(cosmosRepository.findAllDiets()).thenReturn(Flux.fromIterable(cosmosList));
      when(dietRepository.findAll())
          .thenReturn(
              List.of(
                  Diets.builder()
                      .dietDocument(
                          DietDocument.builder()
                              .id(UUID.fromString(cosmosList.iterator().next().getId()))
                              .build())
                      .build()));
      result =
          dietsMigration.migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.DIETS)).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenDietsMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    DietCosmos diet1 = new DietCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    DietCosmos diet2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/diets.json"), DietCosmos.class);

    Iterable<DietCosmos> dietsList = List.of(diet1, diet2);
    when(cosmosRepository.findAll()).thenReturn(Flux.fromIterable(dietsList));

    MigrationResult result = dietsMigration.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }

  @Test
  void whenDietsDataIsOkNoErrors() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());

    DietCosmos dietCosmos =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/diets.json"), DietCosmos.class);
    when(cosmosRepository.findAllBySiteId(anyString())).thenReturn(Flux.just(dietCosmos));
    when(dietRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    Flux<Diets> result = dietsMigration.moveRecords("test");

    StepVerifier.create(result).expectNextCount(1).verifyComplete();
  }

  private List<DietCosmos> loadCosmosDiets() {
    DietCosmos cosmosDiet = new DietCosmos();
    cosmosDiet.setId(UUID.randomUUID().toString());
    cosmosDiet.setSiteId(UUID.randomUUID().toString());
    cosmosDiet.setOptimizationId(1);
    cosmosDiet.setOptimizationType("type");
    cosmosDiet.setOptimizationStatus("status");
    cosmosDiet.setCreateUser("<EMAIL>");

    return List.of(cosmosDiet);
  }
}
