/* Cargill Inc.(C) 2022 */
package com.app.cargill.schedulers;

import com.app.cargill.model.Visits;
import com.app.cargill.service.ICdpV1Service;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class CDPForceVisitUpdateScheduler {

  private final ICdpV1Service cdpV1Service;

  /*
   * runs at 1 am
   */
  @Scheduled(cron = "0 0 1 * * *")
  public void forceUpdateVisits() {
    log.info("forceUpdateVisits invoked");
    List<Visits> page = cdpV1Service.getAllVisitsForOneYear();
    log.info("forceUpdateVisits {} {} {}", page.size());
  }
}
