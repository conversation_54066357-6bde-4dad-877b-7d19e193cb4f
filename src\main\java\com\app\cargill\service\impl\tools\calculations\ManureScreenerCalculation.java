/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.ManureScreenerScoreGoalToolItem;
import com.app.cargill.document.ManureScreenerTool;
import com.app.cargill.document.ManureScreenerToolItem;

public class ManureScreenerCalculation {
  ManureScreenerTool manureScreenerTool;
  ManureScreenerToolItem manureScreenerToolItem;
  ManureScreenerScoreGoalToolItem manureScreenerScoreGoalToolItem;

  public ManureScreenerTool calculateFields(ManureScreenerTool tool) {
    if (tool == null) return null;

    manureScreenerTool = tool;

    if (tool.getMstScores() != null) {
      for (ManureScreenerToolItem pen : tool.getMstScores()) {
        manureScreenerToolItem = pen;
        pen.totalScaleAmount = totalScaleAmount();
        pen.topScalePercentage = topScalePercentage();
        pen.midScalePercentage = midScalePercentage();
        pen.bottomScalePercentage = bottomScalePercentage();
      }
    }

    manureScreenerScoreGoalToolItem = tool.getMstGoal();

    return tool;
  }

  private Double totalScaleAmount() {
    if (manureScreenerToolItem.getTopScaleAmountInGrams() != null
        && manureScreenerToolItem.getMidScaleAmountInGrams() != null
        && manureScreenerToolItem.getBottomScaleAmountInGrams() != null) {
      return (manureScreenerToolItem.getTopScaleAmountInGrams()
          + manureScreenerToolItem.getMidScaleAmountInGrams()
          + manureScreenerToolItem.getBottomScaleAmountInGrams());
    }
    return 0.0;
  }

  private Double topScalePercentage() {
    return manureScreenerToolItem.totalScaleAmount > 0
            && manureScreenerToolItem.getTopScaleAmountInGrams() != null
        ? roundAvoid(
            (manureScreenerToolItem.getTopScaleAmountInGrams()
                / manureScreenerToolItem.totalScaleAmount
                * 100),
            1)
        : 0.0;
  }

  private Double midScalePercentage() {
    return (manureScreenerToolItem.totalScaleAmount > 0
            && manureScreenerToolItem.getMidScaleAmountInGrams() != null)
        ? roundAvoid(
            (manureScreenerToolItem.getMidScaleAmountInGrams()
                / manureScreenerToolItem.totalScaleAmount
                * 100),
            1)
        : 0.0;
  }

  private Double bottomScalePercentage() {
    return (manureScreenerToolItem.totalScaleAmount > 0
            && manureScreenerToolItem.getBottomScaleAmountInGrams() != null)
        ? roundAvoid(
            (manureScreenerToolItem.getBottomScaleAmountInGrams()
                / manureScreenerToolItem.totalScaleAmount
                * 100),
            1)
        : 0.0;
  }
}
