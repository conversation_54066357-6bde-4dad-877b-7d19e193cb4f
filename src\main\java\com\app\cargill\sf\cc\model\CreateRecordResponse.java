/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/** { "id": "0017400000FyKEbAAN", "success": true, "errors": [] } */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CreateRecordResponse {
  private String id;
  private boolean success;

  // Unspecified type
  private List<Object> errors;
}
