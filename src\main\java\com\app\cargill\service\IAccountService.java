/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.document.DataSource;
import com.app.cargill.dto.AccountDto;
import com.app.cargill.dto.AccountFavouriteDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;

@SuppressWarnings("java:S107")
public interface IAccountService {

  List<AccountDto> getAllAccountsNonPaginated();

  List<AccountDto> getAllAccounts(String accountType, Instant syncTime, String email);

  Page<AccountDto> getPaginatedAccounts(
      String search,
      int page,
      int size,
      String sortBy,
      String accountType,
      Instant syncTime,
      String email,
      String sorting);

  AccountDto save(AccountDto accountRequest, Locale locale, ResourceBundleMessageSource source)
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions;

  AccountDto update(AccountDto accountDto, Locale locale, ResourceBundleMessageSource source)
      throws NotFoundDEException, JsonProcessingException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions;

  AccountDto getAccountById(String id);

  AccountDto favourite(AccountFavouriteDto accountFavouriteDto);

  List<Accounts> getAllUnsyncedAccounts(DataSource dataSource);

  List<String> getFilteredAccountIds();
}
