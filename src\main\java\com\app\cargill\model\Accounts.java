/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.document.AccountDocument;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Builder.Default;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "accounts")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
// @Convert(attributeName = "jsonb", converter = JsonBinaryType.class)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class Accounts extends BaseEntity {

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb") // or, json
  private AccountDocument accountDocument;

  @Transient @Default private boolean recordModified = false;
  @Transient @Default private boolean recordExists = true;

  public Accounts(AccountDocument accountDocument) {
    this.accountDocument = accountDocument;
  }
}
