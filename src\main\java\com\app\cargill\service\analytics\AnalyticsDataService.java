/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.analytics;

import com.app.cargill.model.analytics.AnalyticsDataResponse.DataPointContainer;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AnalyticsDataService {

  private final DdwReportsAnalyticsService ddwReportsAnalyticsService;

  public List<DataPointContainer> getAllDataPoints(Instant from, Instant to) {
    List<DataPointContainer> result = new ArrayList<>();

    Mono<DataPointContainer> detailedReports =
        ddwReportsAnalyticsService.fetchAllDetailedReports(from, to);
    detailedReports.doOnNext(result::add).block();

    Mono<DataPointContainer> summaryReports =
        ddwReportsAnalyticsService.fetchAllSummaryReports(from, to);
    summaryReports.doOnNext(result::add).block();

    return result;
  }
}
