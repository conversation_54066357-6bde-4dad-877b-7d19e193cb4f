/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.constants.AccountType;
import com.app.cargill.constants.Business;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.mapper.CountryToCurrencyConverter;
import com.app.cargill.sf.cc.mapper.LiftAccountMapper;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.Account;
import com.app.cargill.sf.cc.model.simple.AccountUpdateModel;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.utils.BusinessToCountryMapper;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiftAccountService {

  private static final String SIMPLE_ACCOUNTS_QUERY =
      "SELECT+Id,CreatedDate,IsDeleted,Name,BillingAddress,Customer_Account_Type__c,Segment__c,X9_Box__c,Source_System__c,Business_Type__c,"
          + "Cargill_Business__c,ParentId,Review_Status__c,Website,Visible_on_DE__c,Email__c,Phone,CurrencyIsoCode,"
          + "Customer_Number__c,New_Account_Type__c,Account_Status__c,DE_Account_External_ID__c,LastModifiedById,"
          + "LastModifiedBy.Email,CreatedBy.Email,Description,"
          + "Owner.Id,Owner.Name,Owner.Email,Owner.Username,LastModifiedDate,"
          + "(SELECT+Id,Account.DE_Account_External_ID__c,Account.Id,LastName,FirstName,Salutation,OtherAddress,MailingAddress,Phone,Email,Title,CreatedDate,"
          + "LastModifiedDate,Preferred_Contact_Method__c,Description,Primary_Language__c,Primary_Role__c,"
          + "DE_Contact_External_ID__c+FROM+Contacts),"
          + "(SELECT+DE_Role_Name__c,DE_User__r.Name,DE_User__r.Email,DE_User__r.Country,LastModifiedDate+FROM+DE_User_Access__r),"
          + "(SELECT+Id,ID__c,Species__c,System__c,Unique_External_Key__c+FROM+External_Data_Sources__r)"
          + "+FROM+Account+WHERE+DE_Account__c=TRUE+AND+isDeleted=false";

  private static final String CHATTER_FREE = "Chatter Free";

  private final LiftApiService liftApi;
  private final LiftApiReactiveService liftApiReactiveService;
  private final LiftUserService liftUserService;
  private final LiftConfig liftConfig;
  private final AccountsRepository accountsRepository;

  public List<AccountDocument> getAllAccounts() {
    return getAllAccounts(null);
  }

  /**
   * @param conditions A query conditions list like: "LastModifiedDate>2023-03-06T09:33:58.394993Z"
   *     For some fields you must use single quotes:
   *     "Owner.Email='<EMAIL>'"
   * @return accounts
   */
  public List<AccountDocument> getAllAccounts(List<String> conditions) {
    List<AccountDocument> accountsList = new ArrayList<>();
    try {
      AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
      String query =
          conditions == null
              ? SIMPLE_ACCOUNTS_QUERY
              : String.format("%s+AND+%s", SIMPLE_ACCOUNTS_QUERY, String.join("+AND+", conditions));
      SalesforceRecordsResponse<Account> accountsBatch =
          liftApi.getRecordsQuery(
              tokenAndApiPath.getAuthToken(),
              tokenAndApiPath.getApiPath(),
              query,
              new ParameterizedTypeReference<>() {});
      accountsList.addAll(
          accountsBatch.getRecords().stream().map(LiftAccountMapper::transform).toList());
      while (accountsBatch.getNextRecordsUrl() != null) {
        accountsBatch =
            liftApi.getRecordsPage(
                tokenAndApiPath.getAuthToken(),
                accountsBatch.getNextRecordsUrl(),
                new ParameterizedTypeReference<>() {});
        accountsList.addAll(
            accountsBatch.getRecords().stream().map(LiftAccountMapper::transform).toList());
      }
      log.trace("{} LIFT accounts obtained", accountsList.size());
    } catch (Exception e) {
      log.error("Error", e);
    }
    return accountsList;
  }

  public void updateAccount(AuthToken authToken, String apiPath, AccountDocument accountDocument) {
    String accountUrl =
        String.format("%s/sobjects/Account/%s", apiPath, accountDocument.getGoldenRecordId());
    String userId = getSfOwnerId(accountDocument.getOwnerId());
    AccountUpdateModel accountUpdateModel = documentToModel(accountDocument, userId);
    accountUpdateModel.setReviewStatus(null);
    accountUpdateModel.setCustomerAccountType(null);
    log.info("UPDATE_LIFT_ACCOUNT {}", accountUpdateModel);
    liftApi.updateRecord(
        authToken, accountUpdateModel, new ParameterizedTypeReference<>() {}, accountUrl);
  }

  public void updateAccountName(String recordId, String name) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    String accountUrl =
        String.format("%s/sobjects/Account/%s", tokenAndApiPath.getApiPath(), recordId);
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    accountUpdateModel.setName(name);
    log.info("UPDATE_LIFT_ACCOUNT_NAME {}", accountUpdateModel);
    liftApi.updateRecord(
        tokenAndApiPath.getAuthToken(),
        accountUpdateModel,
        new ParameterizedTypeReference<>() {},
        accountUrl);
  }

  public AccountDocument createAccount(
      AccountDocument accountDocument, Locale locale, ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {
    if (!inputModelIsValid(accountDocument)) {
      throw new IllegalArgumentException(
          "AccountName, OwnerId, DE_Id, AccountType and BillingAddress are required fields.");
    }
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return createAccount(
        tokenAndApiPath.getAuthToken(),
        tokenAndApiPath.getApiPath(),
        accountDocument,
        locale,
        source);
  }

  public AccountDocument createAccount(
      AuthToken authToken,
      String apiPath,
      AccountDocument accountDocument,
      Locale locale,
      ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {
    try {
      String accountUrl = String.format("%s/sobjects/Account", apiPath);
      String userId = getSfOwnerId(accountDocument.getOwnerId());
      log.info("CREATE_ACCOUNT_OWNER email: {} id: {}", accountDocument.getOwnerId(), userId);

      AccountUpdateModel accountModel = documentToModel(accountDocument, userId);
      log.info("CREATE_LIFT_ACCOUNT {}", accountModel);
      CreateRecordResponse recordResponse =
          liftApi.createRecord(
              authToken, accountModel, new ParameterizedTypeReference<>() {}, accountUrl);
      log.info("LIFT_ACCOUNT_CREATED {}", recordResponse.getId());
      accountDocument.setGoldenRecordId(recordResponse.getId());
      return accountDocument;
    } catch (Exception e) {
      log.error(String.format("Error on account creation: %s", accountDocument.getId()), e);
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  source.getMessage(
                      LangKeys.LIFT_SYNC_FAILED,
                      new Object[] {},
                      LiftErrorResponseConstants.LIFT_ERROR_MESSAGE,
                      locale))
              .entity("Account")
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()),
          HttpStatus.SC_FORBIDDEN);
    }
  }

  public String getSfOwnerId(String userEmail) {
    if (userEmail != null) {
      User user = liftUserService.findUserByEmail(userEmail);
      if (!Objects.isNull(user)
          && user.getProfile().getUserLicense().getName().equals(CHATTER_FREE)) {
        user = liftUserService.findUserByEmail(liftConfig.getDefaultOwnerId());
      }
      return user != null ? user.getId() : null;
    } else {
      log.warn("LIFT_MISSING_ACCOUNT_OWNER_EMAIL");
    }
    return null;
  }

  public AuthToken getAuthToken() {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return tokenAndApiPath.getAuthToken();
  }

  public String getApiPath() {
    return liftApi.getTokenAndApiPath().getApiPath();
  }

  private boolean inputModelIsValid(AccountDocument accountDocument) {
    return accountDocument.getAccountName() != null
        && accountDocument.getOwnerId() != null
        && accountDocument.getId() != null
        && accountDocument.getAccountType() != null
        && accountDocument.getPhysicalAddress() != null
        && accountDocument.getPhysicalAddress().getCountry() != null;
  }

  public AccountUpdateModel documentToModel(AccountDocument accountDocument, String ownerId) {
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    accountUpdateModel.setName(accountDocument.getAccountName());
    accountUpdateModel.setAccountExternalId(accountDocument.getId().toString());
    accountUpdateModel.setSegment(accountDocument.getSegmentStepOneId());
    accountUpdateModel.setEmail(accountDocument.getCompanyEmail());
    accountUpdateModel.setPhone(accountDocument.getPhone());
    accountUpdateModel.setCustomerNumber(accountDocument.getCustomerCode());
    accountUpdateModel.setOwnerId(ownerId);
    accountUpdateModel.setReviewStatus("New");
    accountUpdateModel.setCustomerAccountType(
        Objects.requireNonNull(AccountType.fromId(accountDocument.getAccountType())).name());
    if (accountDocument.getPhysicalAddress() != null) {
      accountUpdateModel.setBillingStreet(accountDocument.getPhysicalAddress().getStreet());
      accountUpdateModel.setBillingCity(accountDocument.getPhysicalAddress().getCity());
      accountUpdateModel.setBillingState(accountDocument.getPhysicalAddress().getStateOrProvince());
      accountUpdateModel.setBillingCountry(accountDocument.getPhysicalAddress().getCountry());
      accountUpdateModel.setBillingPostalCode(accountDocument.getPhysicalAddress().getPostalCode());
      accountUpdateModel.setCurrencyIsoCode(
          CountryToCurrencyConverter.countryToCurrency(
              accountDocument.getPhysicalAddress().getCountry()));
      accountUpdateModel.setBusinessId(calculateLiftBusinessId(accountDocument));
    }
    return accountUpdateModel;
  }

  private String calculateLiftBusinessId(AccountDocument accountDocument) {
    if (accountDocument.getPhysicalAddress().getCountry() != null) {
      if (accountDocument.getPhysicalAddress().getCountry().equals("Paraguay")) {
        return "Brazil";
      } else {
        return accountDocument.getPhysicalAddress().getCountry();
      }
    } else {
      return BusinessToCountryMapper.businessToCountry(
              Business.fromId(accountDocument.getBusinessID()))
          .getValue();
    }
  }

  public List<Accounts> getDeletedAccounts() {
    List<Accounts> allAccountsDB = accountsRepository.getAllLiftAccounts();
    List<Accounts> deletedAccounts = new ArrayList<>();

    List<Accounts> missingAccounts =
        Flux.fromIterable(allAccountsDB)
            .publishOn(Schedulers.parallel())
            .flatMap(
                account ->
                    Mono.fromCallable(
                            () -> {
                              String goldenRecordId =
                                  account.getAccountDocument().getGoldenRecordId();
                              if (goldenRecordId != null && !goldenRecordId.isEmpty()) {
                                Account sfAccount = checkAccountExistsLift(goldenRecordId).block();
                                if (sfAccount == null) {
                                  account.setRecordExists(false);
                                }
                              }
                              return account;
                            })
                        .subscribeOn(Schedulers.boundedElastic()))
            .filter(account -> !account.isRecordExists())
            .collectList()
            .block();

    if (missingAccounts != null) {
      deletedAccounts.addAll(missingAccounts);
    }

    log.info("{} FOUND_DELETED_ACCOUNTS {}", deletedAccounts.size(), deletedAccounts);
    return deletedAccounts;
  }

  public Mono<Account> checkAccountExistsLift(String goldenRecordId) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    String accountUrl = tokenAndApiPath.getApiPath() + "/sobjects/Account/" + goldenRecordId;
    log.info("CHECK_ACCOUNT_EXISTS {}", goldenRecordId);
    return liftApiReactiveService.getRecords(
        tokenAndApiPath.getAuthToken(), accountUrl, Account.class);
  }

  public List<AccountDocument> getMismatchedId(String email) {
    log.info("LiftAccountService_GetMismatchedId ");
    List<AccountDocument> allAccountsDB = null;
    if (email != null) {
      allAccountsDB =
          getAllAccounts(Collections.singletonList(String.format("Owner.Email='%s'", email)));
    } else {
      allAccountsDB = getAllAccounts();
    }

    log.info("LiftAccountService_GetMismatchedId_TotalAccounts_From_LIFT {}", allAccountsDB.size());
    List<AccountDocument> mismatchList = new ArrayList<>();
    List<AccountDocument> mismatchList1 =
        Flux.fromIterable(allAccountsDB)
            .publishOn(Schedulers.parallel())
            .flatMap(
                accDoc ->
                    Mono.fromCallable(
                            () -> {
                              return findMismatchedId(accDoc, mismatchList);
                            })
                        .subscribeOn(Schedulers.boundedElastic()))
            .blockFirst();
    return mismatchList;
  }

  public List<AccountDocument> findMismatchedId(
      AccountDocument accDoc, List<AccountDocument> mismatchList) {
    List<ExternalDataSource> externalDataSourceList = accDoc.getApplicationMappings();
    for (ExternalDataSource externalDataSource : externalDataSourceList) {

      if (externalDataSource.getSystem() != null) {
        if ((externalDataSource.getSystem().equals("LM")
                && (!externalDataSource
                    .getIdCustom()
                    .equals(externalDataSource.getUniqueExternalKey())))
            || (externalDataSource.getSystem().equals("LM")
                && externalDataSource.getUniqueExternalKey() == null)) {

          mismatchList.add(accDoc);
        }
      }
    }
    return mismatchList;
  }
}
