/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkProductionDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Double averageMilkProductionKg;

  private Double milkProductionKg;
  private Double kgOfQuotaPerDay;
  private Double incentiveDaysKgPerDay;
  private Double totalQuotaKgPerDay;
  private Double currentQuotaUtilizationKgPerDay;

  private MilkComponentDto butterfat;
  private MilkComponentDto protein;
  private MilkComponentDto lactoseAndOtherSolids;
  private SimpleComponentDto class2Protein;
  private SimpleComponentDto class2LactoseAndOtherSolids;
  private SimpleComponentDto deductions;
}
