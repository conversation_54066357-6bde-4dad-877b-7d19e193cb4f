/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.States;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface StatesRepository extends JpaRepository<States, Long> {

  @Query(
      value =
          "Select s.* FROM states s where s.state_document->> 'CountryCode' = ?1 AND"
              + " s.deleted=false",
      nativeQuery = true)
  List<States> findByCountryCode(String countryId);

  @Query(
      value =
          "Select s.* FROM states s WHERE s.state_document->> 'CountryCode' = :countryCode AND"
              + " s.updated_date > :lastSyncTime AND s.deleted=false",
      nativeQuery = true)
  Page<States> findByCountryCode(
      @Param("countryCode") String countryCode,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value = "Select s.* FROM states s WHERE s.updated_date > :lastSyncTime AND s.deleted=false",
      nativeQuery = true)
  Page<States> findAllByUpdatedDate(@Param("lastSyncTime") Instant lastSyncTime, Pageable pageable);

  @Query(
      value =
          "Select s.* FROM states s WHERE s.state_document->> 'CountryCode' IN (:countryCodes) AND"
              + " s.updated_date > :lastSyncTime AND s.deleted=false",
      nativeQuery = true)
  Page<States> findByCountryCodeIn(
      @Param("countryCodes") List<String> countryCodes,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);
}
