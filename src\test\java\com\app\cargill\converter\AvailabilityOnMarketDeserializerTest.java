/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AvailabilityOnMarketDeserializerTest {

  private ObjectMapper mapper;
  private AvailabilityOnMarketDeserializer deserializer;

  @BeforeEach
  void setup() {
    mapper = new ObjectMapper();
    deserializer = new AvailabilityOnMarketDeserializer();
  }

  @Test
  void whenCorrectDataIsPassedExpectedResultIsReturned() {
    String json = String.format("{\"value\":%s}", "1");
    Integer type = deserialiseString(json);
    assertEquals(1, type);
  }

  @Test
  void whenUnknownValueIsPassedNullIsReturned() {
    String json = String.format("{\"value\":%s}", "\"Available\"");
    Integer type = deserialiseString(json);
    assertEquals(null, type);
  }

  @SneakyThrows({JsonParseException.class, IOException.class})
  private Integer deserialiseString(String json) {
    InputStream stream = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
    JsonParser parser = mapper.getFactory().createParser(stream);
    DeserializationContext ctxt = mapper.getDeserializationContext();
    parser.nextToken();
    parser.nextToken();
    parser.nextToken();
    return deserializer.deserialize(parser, ctxt);
  }
}
