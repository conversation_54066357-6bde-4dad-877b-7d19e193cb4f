/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.app.cargill.confproperties.OktaProperties;
import java.io.IOException;
import java.util.List;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClientResponseException.BadRequest;

@ExtendWith(MockitoExtension.class)
class LoginServiceImplTest {

  @Mock private OktaProperties oktaProperties;
  @InjectMocks private LoginServiceImpl loginServiceImpl;

  private static MockWebServer mockBackEnd;

  @BeforeAll
  static void setUp() throws IOException {
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();
  }

  @Test
  void whenLoginSuccess() {

    assertTrue(loginServiceImpl.logout());
  }

  @Test
  void whenFetchAccessTokenUsingOktaReturnsSuccess() {
    ReflectionTestUtils.setField(
        oktaProperties, "issuer", String.format("http://localhost:%s/", mockBackEnd.getPort()));
    when(oktaProperties.getIssuer())
        .thenReturn(String.format("http://localhost:%s/", mockBackEnd.getPort()));
    when(oktaProperties.getClientId()).thenReturn("0oajmlp89rXYEEGxG0h7");
    mockBackEnd.enqueue(new MockResponse().setResponseCode(400));
    MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
    body.put("username", List.of("<EMAIL>"));
    body.put("password", List.of("<EMAIL>"));
    BadRequest badRequest =
        assertThrows(BadRequest.class, () -> loginServiceImpl.fetchAccessTokenUsingOkta(body));

    assertEquals(400, badRequest.getStatusCode().value());
  }
}
