/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.document.DietDocument;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sites")
public class CdpSiteDataWrapper {

  @Id private String siteId;

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb")
  private SiteDocument siteDocument;

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb")
  private List<PenDocument> pensData = new ArrayList<>();

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb")
  private List<DietDocument> dietsData = new ArrayList<>();
}
