/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.TempDDW;
import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TempDDWRepository extends JpaRepository<TempDDW, Long> {

  @Query(
      nativeQuery = true,
      value =
          "Select t.* FROM tempDDW t where t.temp_ddw_document -> 'Herd' -> "
              + " 'Inventory' ->> 'HerdProfileId' = :herdProfileId and "
              + " t.deleted=false ")
  List<TempDDW> getByHerdProfileId(@Param("herdProfileId") String herdProfileId);

  @Query(
      nativeQuery = true,
      value =
          "Select t.* FROM tempDDW t where t.temp_ddw_document ->> 'id' "
              + " = :documentId and t.deleted=false ")
  List<TempDDW> get(@Param("documentId") UUID documentId);
}
