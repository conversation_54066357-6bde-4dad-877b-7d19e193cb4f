/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Tool;
import com.app.cargill.constants.ToolGroup;
import com.app.cargill.cosmos.model.CountryToolCosmos;
import com.app.cargill.cosmos.repo.CountryToolCosmosRepository;
import com.app.cargill.document.CountryToolDocument;
import com.app.cargill.model.CountryTools;
import com.app.cargill.repository.CountryToolsRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CountryToolsMigrationTest {

  @Mock private CountryToolCosmosRepository repository;

  @Mock private CountryToolsRepository postgresCountryToolsRepository;

  @InjectMocks private CountryToolsMigration countryToolsMigration;

  @BeforeEach
  void setUp() {
    lenient().when(postgresCountryToolsRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenCountryToolsMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    CountryToolCosmos countryTools =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/countryTools.json"),
            CountryToolCosmos.class);

    Iterable<CountryToolCosmos> countryToolsList = List.of(countryTools, countryTools);
    when(repository.findAll()).thenReturn(countryToolsList);

    MigrationResult result = countryToolsMigration.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {

    MigrationResult result;
    try {
      result =
          countryToolsMigration
              .migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.COUNTRY_TOOLS))
              .get();
    } catch (InterruptedException | ExecutionException e) {
      throw new RuntimeException(e);
    }
    assertNotNull(result);
  }

  @Test
  void whenPostMigrationIsCalledCorrectResponseIsReturned() {

    MigrationResult result;
    try {
      result =
          countryToolsMigration
              .postMigration(CosmosDataMigration.MigrationType.COUNTRY_TOOLS.name())
              .get();
    } catch (InterruptedException | ExecutionException e) {
      throw new RuntimeException(e);
    }
    assertNotNull(result);
  }

  @Test
  void whenPostMigrationIsCalledWithAllCorrectResponseIsReturned() {

    MigrationResult result;
    try {
      result =
          countryToolsMigration.postMigration(CosmosDataMigration.MigrationType.ALL.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      throw new RuntimeException(e);
    }
    assertNotNull(result);
  }

  @Test
  void whenPostMigrationIsCalledWithNullCorrectResponseIsReturned() {

    MigrationResult result;
    try {
      result = countryToolsMigration.postMigration(null).get();
    } catch (InterruptedException | ExecutionException e) {
      throw new RuntimeException(e);
    }
    assertNotNull(result);
  }

  @Test
  void whenUpdateToolFunctionIsCalledCorrectResponseIsReturned() {

    CountryTools countryTool =
        CountryTools.builder()
            .countryToolDocument(
                CountryToolDocument.builder()
                    .toolId(Tool.TMRParticleScore)
                    .toolGroupId(ToolGroup.Health)
                    .build())
            .build();
    when(postgresCountryToolsRepository.findByToolId(any())).thenReturn(List.of(countryTool));
    when(postgresCountryToolsRepository.saveAll(any())).thenReturn(List.of(countryTool));
    countryToolsMigration.updatRumenHealthTmrParticleScore();

    assertNotNull(countryTool.getCountryToolDocument());
  }

  @Test
  void whenCountryToolsMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    CountryToolCosmos countryTool1 = new CountryToolCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    CountryToolCosmos countryTool2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/countryTools.json"),
            CountryToolCosmos.class);

    Iterable<CountryToolCosmos> countryToolsList = List.of(countryTool1, countryTool2);
    when(repository.findAll()).thenReturn(countryToolsList);

    MigrationResult result = countryToolsMigration.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }
}
