/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.CudChewingHerdAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.AxisCrossBetween;
import org.apache.poi.xddf.usermodel.chart.AxisCrosses;
import org.apache.poi.xddf.usermodel.chart.BarDirection;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFLineChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("cudChewingHerdAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class CudChewingHerdAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    CudChewingHerdAnalysisReportDto dto =
        modelMapper.map(data, CudChewingHerdAnalysisReportDto.class);
    dto.setLactationStages(
        dto.getChewsPerRegurgitation().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));

    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      // create sheet 0
      addCudChewingPercentageSheet(wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
      // create sheet 1
      XSSFSheet sheet =
          wb.createSheet(
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_1, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;

      prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // Lact Stages
      cellNumber.set(0);

      int lactationStageStartRowNumber = rowNumber.get();

      row = sheet.createRow(rowNumber.getAndIncrement());
      writeLactStageHeadingValues(source, locale, centerBlack, cellNumber, row, dto);

      // CHEWS PER REGURGITATION
      cellNumber.set(0);

      int chewsPerRegurgitationRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_CHEWS_PER_REGURGITATION, null, source, locale));

      for (String lStage : dto.getLactationStages()) {

        ExcelUtils.highlightEmptyCell(
            row,
            dto.getChewsPerRegurgitation().get(lStage),
            cellNumber,
            decimalStyle,
            greyCellStyle);
      }

      // goal chews
      cellNumber.set(0);

      int goalChewsRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_GOAL_CHEWS, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getGoalChews().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      // y0 axis
      int columnStart = 1;
      // int columnStart = 1;
      int columnEnd = columnStart + dto.getLactationStages().length - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

      XDDFDataSource<String> lactationStage =
          XDDFDataSourcesFactory.fromStringCellRange(
              sheet,
              new CellRangeAddress(
                  lactationStageStartRowNumber,
                  lactationStageStartRowNumber,
                  columnStart,
                  columnEnd));
      XDDFNumericalDataSource<Double> chewsPerRegurgitationDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  chewsPerRegurgitationRowNumber,
                  chewsPerRegurgitationRowNumber,
                  columnStart,
                  columnEnd));
      XDDFNumericalDataSource<Double> goalChewsDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(goalChewsRowNumber, goalChewsRowNumber, columnStart, columnEnd));

      // needed objects for the charts

      XSSFChart chart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 1;
      // ======first line chart=========================
      chart =
          ExcelUtils.initChart(
              sheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_CHART_NAME_1, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart,
              ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_1, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series =
          (XDDFLineChartData.Series)
              dataLeft.addSeries(lactationStage, chewsPerRegurgitationDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_CHEWS_PER_REGURGITATION, null, source, locale),
          new CellReference(sheet.getSheetName(), chewsPerRegurgitationRowNumber, 0, true, true));
      series.setSmooth(true);

      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, goalChewsDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_GOAL_CHEWS, null, source, locale),
          new CellReference(sheet.getSheetName(), goalChewsRowNumber, 0, true, true));
      // to smooth lines rather then edges
      series.setSmooth(true);
      chart.plot(dataLeft);
      // ExcelUtils.drawGridLinesInChart(chart, true);
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.CORNFLOWER_BLUE, false);
      ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.RED, true);
      return ExcelUtils.finalizeWorkbook(
          wb, sheet.getRow(lactationStageStartRowNumber).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public Object prepareData(Object data) {
    CudChewingHerdAnalysisReportDto mappedDto =
        modelMapper.map(data, CudChewingHerdAnalysisReportDto.class);
    mappedDto.setLactationStages(
        mappedDto.getChewsPerRegurgitation().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));
    return mappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    Map<String, byte[]> imageTemplates = new HashMap<>();
    CudChewingHerdAnalysisReportDto mappedDto = (CudChewingHerdAnalysisReportDto) prepareData(data);
    byte[] cudChewingPercentage =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.CUD_CHEWING_HERD_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            "Report.Herd.Analysis.CudChewingPercentage", "Cud Chewing %", null, source, locale),
        cudChewingPercentage);

    byte[] noOfChews =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.CUD_CHEWING_HERD_ANALYSIS_REPORT.getImageTemplateName1(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue("Report.No.OfChews", "No Of Chews", null, source, locale),
        noOfChews);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    CudChewingHerdAnalysisReportDto dto =
        modelMapper.map(data, CudChewingHerdAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.CUD_CHEWING_HERD_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook workBk,
      XSSFSheet xssfSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      CudChewingHerdAnalysisReportDto cudChewingHeardAnalysisReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(), workBk, xssfSheet, rowNum.get(), cellNum.getAndIncrement());
    // headings
    XSSFRow row = xssfSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getVisitName());
    xssfSheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getVisitDate());
    xssfSheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = xssfSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getToolName());
    xssfSheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, cudChewingHeardAnalysisReportDto.getAnalysisType());
    xssfSheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));
  }

  void addCudChewingPercentageSheet(
      XSSFWorkbook wb,
      ResourceBundleMessageSource source,
      Locale locale,
      CudChewingHerdAnalysisReportDto dto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFSheet sheet =
        wb.createSheet(
            ExcelUtils.getLangValue(
                LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_0, null, source, locale));

    AtomicInteger cellNumber = new AtomicInteger(0);
    AtomicInteger rowNumber = new AtomicInteger(0);

    prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        greyCellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // Lact Stages
    cellNumber.set(0);

    int lactationStageStartRowNumber = rowNumber.get();

    row = sheet.createRow(rowNumber.getAndIncrement());
    writeLactStageHeadingValues(source, locale, centerBlack, cellNumber, row, dto);

    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    // Cud Chewing %
    cellNumber.set(0);

    int cudChewingPercentageRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(
            LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_CUD_CHEWING_PERCENTAGE,
            null,
            source,
            locale));

    for (String lStage : dto.getLactationStages()) {
      ExcelUtils.highlightEmptyCell(
          row, dto.getCudChewingPercentage().get(lStage), cellNumber, decimalStyle, greyCellStyle);
    }

    // goal cud chewing percentage
    cellNumber.set(0);

    int goalCudChewingPercentageRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_GOAL_CUD_CHEWING_PERCENTAGE, null, source, locale));

    for (String lStage : dto.getLactationStages()) {
      ExcelUtils.highlightEmptyCell(
          row,
          dto.getGoalCudChewingPercentage().get(lStage),
          cellNumber,
          decimalStyle,
          greyCellStyle);
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    int columnEnd = columnStart + dto.getLactationStages().length - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XDDFDataSource<String> lactationStage =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet,
            new CellRangeAddress(
                lactationStageStartRowNumber,
                lactationStageStartRowNumber,
                columnStart,
                columnEnd));
    XDDFNumericalDataSource<Double> cudChewingPercentageDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(
                cudChewingPercentageRowNumber,
                cudChewingPercentageRowNumber,
                columnStart,
                columnEnd));
    XDDFNumericalDataSource<Double> goalCudChewingPercentageDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(
                goalCudChewingPercentageRowNumber,
                goalCudChewingPercentageRowNumber,
                columnStart,
                columnEnd));

    // needed objects for the charts

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFBarChartData dataLeft;
    Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first line chart======================
    chart =
        ExcelUtils.initChart(
            sheet,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_CHART_NAME_0, null, source, locale),
            chartCol0,
            3,
            chartCol0 + 10,
            23);

    ExcelUtils.initLegends(chart);

    bottomAxis =
        ExcelUtils.createBottomAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

    leftAxis =
        ExcelUtils.createLeftAxis(
            chart,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_0, null, source, locale));
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
    // create chart data
    dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);
    // create series
    series =
        (XDDFBarChartData.Series)
            dataLeft.addSeries(lactationStage, cudChewingPercentageDataSource);
    series.setTitle(
        ExcelUtils.getLangValue(
            LangKeys.REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_0, null, source, locale),
        new CellReference(sheet.getSheetName(), cudChewingPercentageRowNumber, 0, true, true));

    series =
        (XDDFBarChartData.Series)
            dataLeft.addSeries(lactationStage, goalCudChewingPercentageDataSource);
    series.setTitle(
        ExcelUtils.getLangValue(LangKeys.REPORT_GOAL_CUD_CHEWING_PERCENTAGE, null, source, locale),
        new CellReference(sheet.getSheetName(), goalCudChewingPercentageRowNumber, 0, true, true));
    chart.plot(dataLeft);
    ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.CORNFLOWER_BLUE, false);
    ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.SKY_BLUE, false);
  }

  private static void writeLactStageHeadingValues(
      ResourceBundleMessageSource source,
      Locale locale,
      XSSFCellStyle centerBlack,
      AtomicInteger cellNumber,
      XSSFRow row,
      CudChewingHerdAnalysisReportDto dto) {
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

    for (String lStage : dto.getLactationStages()) {
      ExcelUtils.createAndSetCellValue(
          row, cellNumber, centerBlack, ExcelUtils.getLangValue(lStage, null, source, locale));
    }
  }
}
