/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint.model;

import com.app.cargill.constants.VisitReportType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@SuppressWarnings("java:S116")
public class VisitReport {
  @JsonProperty("ContentId")
  private String contentId;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("GoldenRecordId")
  private String goldenRecordId;

  @JsonProperty("ReportDate")
  private String reportDate;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("FileSize")
  private double fileSize;

  @JsonProperty("LastModifiedDateUtc")
  private String lastModifiedDateUtc;

  @JsonProperty("ReportType")
  private VisitReportType reportType;

  @JsonProperty("Url")
  private String url;

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("VisitReportFilePathDateTime")
  private String visitReportFilePathDateTime;

  @JsonProperty("ReportFile")
  byte[] reportFile;
}
