/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import reactor.core.publisher.Mono;

@Slf4j
public class WebClientLogFilter {

  private WebClientLogFilter() {}

  public static ExchangeFilterFunction logRequest() {
    return ExchangeFilterFunction.ofRequestProcessor(
        request -> {
          logMethodAndUrl(request);
          return Mono.just(request);
        });
  }

  private static void logHeaders(ClientRequest request) {
    request
        .headers()
        .forEach((name, values) -> values.forEach(value -> logNameAndValuePair(name, value)));
  }

  private static void logNameAndValuePair(String name, String value) {
    log.debug("{}={}", name, value);
  }

  private static void logMethodAndUrl(ClientRequest request) {
    log.trace("{} to {}", request.method().name(), request.url());
  }
}
