/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.*;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PhysicalAddressSalesforce implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("City")
  @JsonAlias({"City", "city", "MailingCity"})
  private String city;

  @JsonProperty("Country")
  @JsonAlias({"Country", "country", "MailingCountry"})
  private String country;

  @JsonProperty("countryCode")
  @JsonAlias({"countryCode", "CountryCode"})
  private String countryCode;

  @JsonProperty("geocodeAccuracy")
  @JsonAlias({"geocodeAccuracy", "GeocodeAccuracy"})
  private String geocodeAccuracy;

  @JsonProperty("latitude")
  @JsonAlias({"latitude", "Latitude"})
  private String latitude;

  @JsonProperty("longitude")
  @JsonAlias({"longitude", "Longitude"})
  private String longitude;

  @JsonProperty("PostalCode")
  @JsonAlias({"PostalCode", "postalCode", "MailingPostalCode"})
  private String postalCode;

  @JsonProperty("state")
  @JsonAlias({"StateOrProvince", "state", "MailingState"})
  private String state;

  @JsonProperty("stateCode")
  @JsonAlias({"stateCode", "StateCode"})
  private String stateCode;

  @JsonProperty("Street")
  @JsonAlias({"Street", "street", "MailingStreet"})
  private String street;
}
