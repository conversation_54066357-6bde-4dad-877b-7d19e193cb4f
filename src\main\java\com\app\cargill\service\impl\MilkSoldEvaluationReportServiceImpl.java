/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.MilkSoldEvaluationExportChartDto;
import com.app.cargill.dto.MilkSoldEvaluationReportDto;
import com.app.cargill.dto.Pair;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("milkSoldEvaluationReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class MilkSoldEvaluationReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  private final FreeMarkerComponent freeMarkerComponent;

  enum LabelIdentifiers {
    Y_LEFT,
    Y_RIGHT,
    CHART_TITLE
  }

  public Map<LabelIdentifiers, String> getLabels(
      MilkSoldEvaluationReportDto dto, Locale locale, ResourceBundleMessageSource source) {
    Map<LabelIdentifiers, String> labelIdentifiersStringMap = new EnumMap<>(LabelIdentifiers.class);
    return switch (dto.getChartType()) {
      case MILK_PRODUCTION_AND_DIM -> {
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_LEFT, dto.getYaxisLeftLabel());

        labelIdentifiersStringMap.put(LabelIdentifiers.Y_RIGHT, dto.getYaxisRightLabel());

        labelIdentifiersStringMap.put(
            LabelIdentifiers.CHART_TITLE,
            ExcelUtils.getLangValue(LangKeys.REPORT_MILK_PRODUCTION_AND_DIM, null, source, locale));
        yield labelIdentifiersStringMap;
      }
      case COMPONENT_YIELD_AND_EFFICIENCY -> {
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_LEFT, dto.getYaxisLeftLabel());
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_RIGHT, dto.getYaxisRightLabel());
        labelIdentifiersStringMap.put(
            LabelIdentifiers.CHART_TITLE,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_COMPONENT_YIELD_AND_EFFICIENCY, null, source, locale));
        yield labelIdentifiersStringMap;
      }
      case MILK_FAT_PERCENT_AND_MILK_PROTEIN_PERCENT -> {
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_LEFT, dto.getYaxisLeftLabel());
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_RIGHT, dto.getYaxisRightLabel());
        labelIdentifiersStringMap.put(
            LabelIdentifiers.CHART_TITLE,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_MILK_FAT_PERCENT_AND_MILK_PROTEIN_PERCENT, null, source, locale));
        yield labelIdentifiersStringMap;
      }
      case SOMATIC_CELL_COUNT_AND_MILK_UREA -> {
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_LEFT, dto.getYaxisLeftLabel());
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_RIGHT, dto.getYaxisRightLabel());
        labelIdentifiersStringMap.put(
            LabelIdentifiers.CHART_TITLE,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_SOMATIC_CELL_COUNT_AND_MILK_UREA, null, source, locale));
        yield labelIdentifiersStringMap;
      }
      case DRY_MATTER_INTAKE_AND_FEED_EFFICIENCY -> {
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_LEFT, dto.getYaxisLeftLabel());
        labelIdentifiersStringMap.put(LabelIdentifiers.Y_RIGHT, dto.getYaxisRightLabel());
        labelIdentifiersStringMap.put(
            LabelIdentifiers.CHART_TITLE,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_DRY_MATTER_INTAKE_AND_FEED_EFFICIENCY, null, source, locale));
        yield labelIdentifiersStringMap;
      }
    };
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    // this.source = source;
    MilkSoldEvaluationReportDto dto = modelMapper.map(data, MilkSoldEvaluationReportDto.class);
    Map<LabelIdentifiers, String> chartLabelMap = getLabels(dto, locale, source);
    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      // create sheet
      XSSFSheet sheet =
          wb.createSheet(ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, new Pair<>(locale, source));

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // visit dates x axis
      cellNumber.set(0);

      int visitDateStartRowNumber = rowNumber.get();

      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      for (MilkSoldEvaluationExportChartDto dataPoint : dto.getDataPoints()) {
        ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, dataPoint.getVisitDate());
      }

      // y-left
      cellNumber.set(0);

      int yLeftRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row, cellNumber, centerBlack, chartLabelMap.get(LabelIdentifiers.Y_LEFT));

      for (MilkSoldEvaluationExportChartDto dataPoint : dto.getDataPoints()) {
        ExcelUtils.highlightEmptyCell(
            row, dataPoint.getYaxisLeft(), cellNumber, decimalStyle, greyCellStyle);
      }

      // y-right
      cellNumber.set(0);

      int yRightRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row, cellNumber, centerBlack, chartLabelMap.get(LabelIdentifiers.Y_RIGHT));

      for (MilkSoldEvaluationExportChartDto dataPoint : dto.getDataPoints()) {
        ExcelUtils.highlightEmptyCell(
            row, dataPoint.getYaxisRight(), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      int columnStart = 1;
      int columnEnd = columnStart + dto.getDataPoints().size() - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

      XDDFDataSource<String> visitDates =
          XDDFDataSourcesFactory.fromStringCellRange(
              sheet,
              new CellRangeAddress(
                  visitDateStartRowNumber, visitDateStartRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> yLeft =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet, new CellRangeAddress(yLeftRowNumber, yLeftRowNumber, columnStart, columnEnd));
      // y1 axis
      XDDFNumericalDataSource<Double> yRight =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(yRightRowNumber, yRightRowNumber, columnStart, columnEnd));

      // needed objects for the charts
      XSSFChart chart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFValueAxis rightAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData dataRight;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 3;
      // ======first line chart==========
      chart =
          ExcelUtils.initChart(
              sheet,
              chartLabelMap.get(LabelIdentifiers.CHART_TITLE),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart, ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      leftAxis = ExcelUtils.createLeftAxis(chart, chartLabelMap.get(LabelIdentifiers.Y_LEFT));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series = (XDDFLineChartData.Series) dataLeft.addSeries(visitDates, yLeft);
      series.setTitle(
          chartLabelMap.get(LabelIdentifiers.Y_LEFT),
          new CellReference(sheet.getSheetName(), yLeftRowNumber, 0, true, true));
      // to smooth lines rather than edges
      series.setSmooth(true);

      chart.plot(dataLeft);

      ///////////////////////////// right axis start here //////////////////////////
      bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
      bottomAxis.setVisible(false);
      Double minimumYAxisValue =
          dto.getDataPoints().stream()
              .map(MilkSoldEvaluationExportChartDto::getYaxisRight)
              .filter(x -> !Objects.isNull(x))
              .sorted()
              .findFirst()
              .orElse(0.0);
      rightAxis =
          ExcelUtils.createRightAxis(
              chart, chartLabelMap.get(LabelIdentifiers.Y_RIGHT), bottomAxis, minimumYAxisValue);

      // create chart data
      dataRight = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);

      // create series
      series = (XDDFLineChartData.Series) dataRight.addSeries(visitDates, yRight);
      series.setTitle(
          chartLabelMap.get(LabelIdentifiers.Y_RIGHT),
          new CellReference(sheet.getSheetName(), yRightRowNumber, 0, true, true));
      // ExcelUtils.drawGridLinesInChart(chart, true);
      ExcelUtils.plotYRightAxis(chart, dataRight, series, true);
      ExcelUtils.drawLineSeries(dataRight, 0, PresetColor.PURPLE, true);
      /////////////////////////////////// right axis end here /////////////////////////////////////
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.GREEN, false);
      return ExcelUtils.finalizeWorkbook(wb, sheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public Object prepareData(Object dto, ResourceBundleMessageSource source, Locale locale) {
    MilkSoldEvaluationReportDto mappedDto = modelMapper.map(dto, MilkSoldEvaluationReportDto.class);
    mappedDto.setChartLabels(
        getLabels(mappedDto, locale, source).entrySet().stream()
            .collect(
                Collectors.toMap(
                    e -> e.getKey().name(), e -> e.getValue() == null ? "" : e.getValue())));
    return mappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {

    byte[] report =
        freeMarkerComponent.render(
            prepareData(dto, source, locale),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    MilkSoldEvaluationReportDto dto = modelMapper.map(data, MilkSoldEvaluationReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook wb,
      XSSFSheet sheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      MilkSoldEvaluationReportDto milkSoldEvaluationReportDto,
      XSSFCellStyle boldStyle,
      Pair<Locale, ResourceBundleMessageSource> langSource) {
    // add logo in chart
    ExcelUtils.addCargillLogo(getClass(), wb, sheet, rowNumber.get(), cellNumber.getAndIncrement());
    // headings
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(
            LangKeys.REPORT_VISIT_NAME, null, langSource.right(), langSource.left()));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, milkSoldEvaluationReportDto.getVisitName());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(
            LangKeys.REPORT_VISIT_DATE, null, langSource.right(), langSource.left()));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, milkSoldEvaluationReportDto.getVisitDate());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(
            LangKeys.REPORT_TOOL_NAME, null, langSource.right(), langSource.left()));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, milkSoldEvaluationReportDto.getToolName());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
  }
}
