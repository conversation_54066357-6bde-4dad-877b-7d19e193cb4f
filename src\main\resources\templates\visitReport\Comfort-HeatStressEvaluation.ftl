<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitViewModel.ComfortItem", [],"Comfort",locale)}</span>
            <span class="lg-head">${localization.getMessage("HeatstressTableViewModel.Title", [],"Heat Stress
                Evaluation",locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000333HS</div>
    </div>
</div>
<#if model.heatStressEvaluationTool.animalInputs ?? || model.heatStressEvaluationTool.weather ?? ||
    model.heatStressEvaluationTool.exposure ??>
    <div class="container">
        <div class="row mx-neg-4">
            <#if model.heatStressEvaluationTool.animalInputs ?? && model.heatStressEvaluationTool.animalInputs[0] ??>
                <div class="col-6 px-4 table-secondary">
                    <h3 class="title-primary">
                        <span>${localization.getMessage("HeatstressDataEntryViewModel.AnimalInputs", [],"Animal
                            Inputs",locale)}</span>
                    </h3>
                    <table>
                        <tbody>
                            <#list model.heatStressEvaluationTool.animalInputs as animalInput>
                                <tr>
                                    
                                        <td width="50%">${animalInput.column!}</td>
                                        <td width="50%">${animalInput.value!}</td>
                                    
                                </tr>
                            </#list>
                        </tbody>
                    </table>
                </div>
            </#if>
            <#if (model.heatStressEvaluationTool.weather ?? && model.heatStressEvaluationTool.weather[0] ??) ||
                (model.heatStressEvaluationTool.exposure ?? && model.heatStressEvaluationTool.exposure[0] ??)>
                <div class="col-6 px-4 table-secondary">
                    <#if (model.heatStressEvaluationTool.weather ?? && model.heatStressEvaluationTool.weather[0] ??)>
                        <h3 class="title-primary">
                            <span>${localization.getMessage("HeatstressDataEntryViewModel.Weather",
                                [],"Weather",locale)}</span>
                        </h3>
                        <table>
                            <tbody>
                                <#list model.heatStressEvaluationTool.weather as weather>
                                    <tr>
                                            <td width="50%">${weather.column!}</td>
                                            <td width="50%">${weather.value!}</td>
                                        
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </#if>
                    <#if (model.heatStressEvaluationTool.exposure ?? && model.heatStressEvaluationTool.exposure[0] ??)>
                        <h3 class="title-primary mt-1">
                            <span>${localization.getMessage("HeatstressDataEntryViewModel.Exposure",
                                [],"Exposure",locale)}</span>
                        </h3>
                        <table>
                            <tbody>
                                <#list model.heatStressEvaluationTool.exposure as exposure>
                                    <tr>
                                      
                                            <td width="50%">${exposure.column!}</td>
                                            <td width="50%">${exposure.value!}</td>
                                        
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </#if>
                </div>
            </#if>
        </div>
    </div>
</#if>
<#if model.heatStressEvaluationTool.results ?? && model.heatStressEvaluationTool.temperatureAndHumidityIndexValue ??>
    <div class="container">
        <div class="row mx">
            <div class="col-12">
                <h3 class="title-secondary my-1">
                    <span>${localization.getMessage("ChewsPerCudMasterViewModel.CudChewingResults", [],"Results",locale)}</span>
                </h3>
            </div>

            <div class="col-12 cards-row">
                <#if model.heatStressEvaluationTool.temperatureAndHumidityIndexValue?? && model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue??>
                    <div class="fullwidth-card-row">
                        <div class="fullwidth-card-img">
                            <#if model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_GREEN'>
									<svg viewBox="212.841 157.162 56.5 56.5" width="56.5" height="56.5">
									  <circle cx="241.091" cy="185.412" r="28.25" fill="#4EBA7D" stroke="#4EBA7D" stroke-width="1.5" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></circle>
									  <path d="M 246.493 189.451 L 246.493 170.394 C 246.493 167.415 244.07 164.992 241.092 164.992 C 238.113 164.992 235.69 167.415 235.69 170.394 L 235.69 189.451 C 233.376 191.163 232.012 193.848 232.012 196.747 C 232.012 201.753 236.085 205.826 241.091 205.826 C 246.097 205.826 250.17 201.753 250.17 196.747 C 250.17 193.848 248.806 191.163 246.493 189.451 Z M 241.092 203.689 C 237.263 203.689 234.149 200.575 234.149 196.746 C 234.149 194.378 235.34 192.195 237.336 190.908 L 237.826 190.593 L 237.826 170.394 C 237.826 168.593 239.29 167.128 241.091 167.128 C 242.892 167.128 244.357 168.593 244.357 170.394 L 244.357 190.593 L 244.846 190.908 C 246.842 192.195 248.034 194.378 248.034 196.746 C 248.034 200.574 244.919 203.689 241.091 203.689 L 241.092 203.689 Z" fill="white" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
									  <path d="M 242.577 188.629 L 239.606 188.629 L 239.606 193.515 L 242.577 193.515 L 242.577 188.629 Z" fill="#2C7321" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
									  <path d="M 241.091 190.123 C 241.911 190.123 242.576 189.458 242.576 188.638 C 242.576 187.817 241.911 187.152 241.091 187.152 C 240.271 187.152 239.606 187.817 239.606 188.638 C 239.606 189.458 240.271 190.123 241.091 190.123 Z" fill="#2C7321" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
									  <path d="M 241.091 202.157 C 244.055 202.157 246.458 199.756 246.458 196.795 C 246.458 193.833 244.055 191.432 241.091 191.432 C 238.127 191.432 235.724 193.833 235.724 196.795 C 235.724 199.756 238.127 202.157 241.091 202.157 Z" fill="#2C7321" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
									</svg>
							<#elseif model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_YELLOW'>
								<svg viewBox="247.135 282.441 56.5 56.5" width="56.5" height="56.5">
								  <circle cx="275.385" cy="310.691" r="28.25" fill="#FFCB29" stroke="#FFCB29" stroke-width="1.5" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></circle>
								  <path d="M 278.619 314.184 L 278.619 298.414 C 278.619 295.949 276.613 293.944 274.148 293.944 C 271.683 293.944 269.678 295.949 269.678 298.414 L 269.678 314.184 C 267.764 315.6 266.635 317.822 266.635 320.221 C 266.635 324.364 270.006 327.734 274.148 327.734 C 278.291 327.734 281.661 324.364 281.661 320.221 C 281.661 317.822 280.532 315.6 278.618 314.184 L 278.619 314.184 Z M 274.149 325.966 C 270.981 325.966 268.403 323.389 268.403 320.221 C 268.403 318.261 269.389 316.455 271.041 315.39 L 271.446 315.129 L 271.446 298.414 C 271.446 296.923 272.658 295.711 274.148 295.711 C 275.639 295.711 276.851 296.923 276.851 298.414 L 276.851 315.129 L 277.256 315.39 C 278.907 316.455 279.893 318.261 279.893 320.221 C 279.893 323.389 277.316 325.966 274.148 325.966 L 274.149 325.966 Z" fill="white" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								  <path d="M 275.378 308.442 L 272.919 308.442 L 272.919 317.067 L 275.378 317.067 L 275.378 308.442 Z" fill="#CD9C1F" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								  <path d="M 274.148 309.64 C 274.827 309.64 275.378 309.09 275.378 308.411 C 275.378 307.732 274.827 307.182 274.148 307.182 C 273.47 307.182 272.919 307.732 272.919 308.411 C 272.919 309.09 273.47 309.64 274.148 309.64 Z" fill="#CD9C1F" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								  <path d="M 274.148 324.7 C 276.601 324.7 278.59 322.714 278.59 320.263 C 278.59 317.812 276.601 315.826 274.148 315.826 C 271.695 315.826 269.707 317.812 269.707 320.263 C 269.707 322.714 271.695 324.7 274.148 324.7 Z" fill="#CD9C1F" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								</svg>
							<#elseif model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_ORANGE'>
							   
								<svg viewBox="64.632 294.144 56.5 56.5" width="56.5" height="56.5">
								  <circle cx="92.882" cy="322.394" r="28.25" fill="#F58837" stroke="#F58837" stroke-width="1.5" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></circle>
								  <path d="M 96.307 325.867 L 96.307 310.097 C 96.307 307.632 94.302 305.627 91.837 305.627 C 89.372 305.627 87.367 307.632 87.367 310.097 L 87.367 325.867 C 85.453 327.283 84.324 329.505 84.324 331.904 C 84.324 336.047 87.694 339.417 91.837 339.417 C 95.98 339.417 99.35 336.047 99.35 331.904 C 99.35 329.505 98.221 327.283 96.307 325.867 Z M 91.837 337.649 C 88.669 337.649 86.092 335.072 86.092 331.904 C 86.092 329.944 87.078 328.138 88.729 327.073 L 89.134 326.812 L 89.134 310.097 C 89.134 308.606 90.346 307.394 91.837 307.394 C 93.327 307.394 94.539 308.606 94.539 310.097 L 94.539 326.812 L 94.944 327.073 C 96.596 328.138 97.582 329.944 97.582 331.904 C 97.582 335.071 95.004 337.649 91.836 337.649 L 91.837 337.649 Z" fill="white" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								  <path d="M 93.066 314.8 L 90.608 314.8 L 90.608 329.227 L 93.066 329.227 L 93.066 314.8 Z" fill="#96511D" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								  <path d="M 91.837 315.885 C 92.516 315.885 93.066 315.334 93.066 314.656 C 93.066 313.977 92.516 313.426 91.837 313.426 C 91.158 313.426 90.608 313.977 90.608 314.656 C 90.608 315.334 91.158 315.885 91.837 315.885 Z" fill="#96511D" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								  <path d="M 91.837 336.383 C 94.29 336.383 96.278 334.397 96.278 331.946 C 96.278 329.495 94.29 327.509 91.837 327.509 C 89.384 327.509 87.396 329.495 87.396 331.946 C 87.396 334.397 89.384 336.383 91.837 336.383 Z" fill="#96511D" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
								</svg>
						<#elseif model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_RED'>
						   
							<svg viewBox="272.1 404.946 56.5 56.5" width="56.5" height="56.5">
							  <circle cx="300.35" cy="433.196" r="28.25" fill="#F75D5E" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></circle>
							  <path d="M 304.114 436.669 L 304.114 420.898 C 304.114 418.434 302.109 416.428 299.644 416.428 C 297.179 416.428 295.174 418.434 295.174 420.898 L 295.174 436.669 C 293.26 438.085 292.131 440.307 292.131 442.706 C 292.131 446.848 295.501 450.219 299.644 450.219 C 303.787 450.219 307.157 446.848 307.157 442.706 C 307.157 440.307 306.028 438.085 304.114 436.669 Z M 299.644 448.451 C 296.476 448.451 293.898 445.873 293.898 442.705 C 293.898 440.745 294.885 438.939 296.536 437.874 L 296.941 437.613 L 296.941 420.898 C 296.941 419.408 298.153 418.196 299.644 418.196 C 301.134 418.196 302.346 419.408 302.346 420.898 L 302.346 437.613 L 302.751 437.874 C 304.402 438.939 305.389 440.745 305.389 442.705 C 305.389 445.873 302.811 448.451 299.643 448.451 L 299.644 448.451 Z" fill="white" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
							  <path d="M 300.873 420.701 L 298.414 420.701 L 298.414 440.028 L 300.873 440.028 L 300.873 420.701 Z" fill="#912727" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
							  <path d="M 299.644 421.928 C 300.322 421.928 300.873 421.377 300.873 420.699 C 300.873 420.02 300.322 419.469 299.644 419.469 C 298.965 419.469 298.414 420.02 298.414 420.699 C 298.414 421.377 298.965 421.928 299.644 421.928 Z" fill="#912727" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
							  <path d="M 299.643 447.185 C 302.096 447.185 304.085 445.198 304.085 442.748 C 304.085 440.297 302.096 438.31 299.643 438.31 C 297.191 438.31 295.202 440.297 295.202 442.748 C 295.202 445.198 297.191 447.185 299.643 447.185 Z" fill="#912727" transform="matrix(0.9999999999999999, 0, 0, 0.9999999999999999, 0, 1.4210854715202004e-14)"></path>
							</svg>
                         </#if>
                        </div>
                        <div class="fullwidth-card-content">
                            <p>${localization.getMessage("HeatstressChartViewModel.TempHumidIndex", [],"Temperature
                                Humidity Index",locale)}</p>
                                <#if model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_GREEN'>
                            <h5 style="color: #45AE7E;">${model.heatStressEvaluationTool.temperatureAndHumidityIndexValue!0.0}</h5>
                            <span style="color: #45AE7E;">${model.heatStressEvaluationTool.heatStressColorText!localization.getMessage("HeatstressGreen", [],"Stress threshold",locale)}</span>
                            </#if>
                            <#if model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_YELLOW'>
                            <h5 style="color: #FFCB29;">${model.heatStressEvaluationTool.temperatureAndHumidityIndexValue!0.0}</h5>
                            <span style="color: #FFCB29;">${model.heatStressEvaluationTool.heatStressColorText!localization.getMessage("HeatstressGreen", [],"Stress threshold",locale)}</span>
                            </#if>
                            <#if model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_ORANGE'>
                            <h5 style="color: #F58837;">${model.heatStressEvaluationTool.temperatureAndHumidityIndexValue!0.0}</h5>
                            <span style="color: #F58837;">${model.heatStressEvaluationTool.heatStressColorText!localization.getMessage("HeatstressGreen", [],"Stress threshold",locale)}</span>
                            </#if>
                            <#if model.heatStressEvaluationTool.temperatureAndHumidityIndexColorValue=='STRESS_THRESHOLD_RED'>
                            <h5 style="color: #F75D5E;">${model.heatStressEvaluationTool.temperatureAndHumidityIndexValue!0.0}</h5>
                            <span style="color: #F75D5E;">${model.heatStressEvaluationTool.heatStressColorText!localization.getMessage("HeatstressGreen", [],"Stress threshold",locale)}</span>
                            </#if>
                        </div>
                    </div>
                </#if>
				<#assign counter=0>
                <#list model.heatStressEvaluationTool.results?keys as key>
                    <div class="card-col">
                        <p>${key!}</p>
                        <strong>${model.heatStressEvaluationTool.results[key]!}</strong>
                    </div>
					<#assign counter=counter+1>
					<#if counter gte 3>
						<#break>
					</#if>
                </#list>
            </div>
        </div>
		<#assign counter=0>
		<div class="row mx mt-1">
            

            <div class="col-12 cards-row-2 ">
	             <#list model.heatStressEvaluationTool.results?keys as key>
				 <#assign counter=counter+1>
				 <#if counter gt 3>
					<div class="card-col">
						<p>${key!}</p>
						<strong>${model.heatStressEvaluationTool.results[key]!}</strong>
					</div>						
				</#if>
				
                </#list>
                 </div>
    </div>
    </div>
	
</#if>

<div class="container mt-1">
    <div class="row mx-neg-4">
        <div class="col-12 px-4">
            <h3 class="title-secondary mb-1">
                <span>${localization.getMessage("HeatstressChartViewModel.TempHumidIndex", [],"Temperature humidity
                    index",locale)}</span>
            </h3>
        </div>

		 <#if model.heatStressEvaluationTool.unitOfMeasure == "Metric">
        <div class="col-7 px-4">
            <table class="custom-table">
                <thead>
                    <tr>
                        <th class="navy-blue-bg" colspan="2">${localization.getMessage("Report.Heatstress.Temperature",
                            [],"Temperature",locale)}</th>
                        <th class="navy-blue-bg" colspan="6">
                            ${localization.getMessage("Report.Heatstress.Relative.Humidity", [],"Relative Humidity
                            (%)",locale)}</th>
                    </tr>
                    <tr>
                        <th class="blue-bg">(°F)</th>
                        <th class="blue-bg">(°C)</th>
                        <th class="blue-bg">40</th>
                        <th class="blue-bg">50</th>
                        <th class="blue-bg">60</th>
                        <th class="blue-bg">70</th>
                        <th class="blue-bg">80</th>
                        <th class="blue-bg">90</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="sky-blue-bg">64</td>
                        <td class="blue-bg">18</td>
                        <td class="green-bg">17</td>
                        <td class="green-bg">17</td>
                        <td class="green-bg">17</td>
                        <td class="green-bg">17</td>
                        <td class="green-bg">18</td>
                        <td class="green-bg">18</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">68</td>
                        <td class="blue-bg">20</td>
                        <td class="green-bg">18</td>
                        <td class="green-bg">19</td>
                        <td class="green-bg">19</td>
                        <td class="green-bg">19</td>
                        <td class="green-bg">19</td>
                        <td class="green-bg">20</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">72</td>
                        <td class="blue-bg">22</td>
                        <td class="green-bg">20</td>
                        <td class="green-bg">20</td>
                        <td class="green-bg">20</td>
                        <td class="green-bg">21</td>
                        <td class="green-bg">21</td>
                        <td class="green-bg">22</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">75</td>
                        <td class="blue-bg">24</td>
                        <td class="green-bg">21</td>
                        <td class="green-bg">21</td>
                        <td class="green-bg">22</td>
                        <td class="green-bg">22</td>
                        <td class="yellow-bg">23</td>
                        <td class="yellow-bg">23</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">79</td>
                        <td class="blue-bg">26</td>
                        <td class="green-bg">22</td>
                        <td class="yellow-bg">23</td>
                        <td class="yellow-bg">24</td>
                        <td class="yellow-bg">24</td>
                        <td class="yellow-bg">25</td>
                        <td class="yellow-bg">25</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">82</td>
                        <td class="blue-bg">28</td>
                        <td class="yellow-bg">24</td>
                        <td class="yellow-bg">24</td>
                        <td class="yellow-bg">25</td>
                        <td class="yellow-bg">26</td>
                        <td class="yellow-bg">27</td>
                        <td class="yellow-bg">27</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">86</td>
                        <td class="blue-bg">30</td>
                        <td class="yellow-bg">25</td>
                        <td class="yellow-bg">26</td>
                        <td class="yellow-bg">27</td>
                        <td class="orange-bg">28</td>
                        <td class="orange-bg">28</td>
                        <td class="orange-bg">29</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">90</td>
                        <td class="blue-bg">32</td>
                        <td class="yellow-bg">26</td>
                        <td class="yellow-bg">27</td>
                        <td class="orange-bg">28</td>
                        <td class="orange-bg">29</td>
                        <td class="orange-bg">30</td>
                        <td class="orange-bg">31</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">93</td>
                        <td class="blue-bg">34</td>
                        <td class="orange-bg">28</td>
                        <td class="orange-bg">29</td>
                        <td class="orange-bg">30</td>
                        <td class="orange-bg">31</td>
                        <td class="orange-bg">32</td>
                        <td class="red-bg">33</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">97</td>
                        <td class="blue-bg">36</td>
                        <td class="orange-bg">29</td>
                        <td class="orange-bg">30</td>
                        <td class="orange-bg">31</td>
                        <td class="red-bg">33</td>
                        <td class="red-bg">34</td>
                        <td class="red-bg">35</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">100</td>
                        <td class="blue-bg">38</td>
                        <td class="orange-bg">30</td>
                        <td class="orange-bg">32</td>
                        <td class="red-bg">33</td>
                        <td class="red-bg">34</td>
                        <td class="red-bg">35</td>
                        <td class="red-bg">37</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">104</td>
                        <td class="blue-bg">40</td>
                        <td class="orange-bg">32</td>
                        <td class="red-bg">33</td>
                        <td class="red-bg">34</td>
                        <td class="red-bg">36</td>
                        <td class="red-bg">37</td>
                        <td class="red-bg">39</td>
                    </tr>
                </tbody>
            </table>
        </div>
        </#if>
        
        <#if model.heatStressEvaluationTool.unitOfMeasure == "Imperial">
        <div class="col-7 px-4">
                    <table class="custom-table">
                <thead>
                    <tr>
                        <th class="navy-blue-bg" colspan="2">${localization.getMessage("Report.Heatstress.Temperature",
                            [],"Temperature",locale)}</th>
                        <th class="navy-blue-bg" colspan="6">
                            ${localization.getMessage("Report.Heatstress.Relative.Humidity", [],"Relative Humidity
                            (%)",locale)}</th>
                    </tr>
                    <tr>
                        <th class="blue-bg">(°F)</th>
                        <th class="blue-bg">(°C)</th>
                        <th class="blue-bg">40</th>
                        <th class="blue-bg">50</th>
                        <th class="blue-bg">60</th>
                        <th class="blue-bg">70</th>
                        <th class="blue-bg">80</th>
                        <th class="blue-bg">90</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="sky-blue-bg">64</td>
                        <td class="blue-bg">18</td>
                        <td class="green-bg">63</td>
                        <td class="green-bg">63</td>
                        <td class="green-bg">63</td>
                        <td class="green-bg">63</td>
                        <td class="green-bg">64</td>
                        <td class="green-bg">64</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">68</td>
                        <td class="blue-bg">20</td>
                        <td class="green-bg">65</td>
                        <td class="green-bg">65</td>
                        <td class="green-bg">66</td>
                        <td class="green-bg">66</td>
                        <td class="green-bg">67</td>
                        <td class="green-bg">67</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">72</td>
                        <td class="blue-bg">22</td>
                        <td class="green-bg">67</td>
                        <td class="green-bg">68</td>
                        <td class="green-bg">69</td>
                        <td class="green-bg">69</td>
                        <td class="green-bg">70</td>
                        <td class="green-bg">71</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">75</td>
                        <td class="blue-bg">24</td>
                        <td class="green-bg">70</td>
                        <td class="green-bg">71</td>
                        <td class="green-bg">72</td>
                        <td class="green-bg">72</td>
                        <td class="yellow-bg">73</td>
                        <td class="yellow-bg">74</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">79</td>
                        <td class="blue-bg">26</td>
                        <td class="green-bg">72</td>
                        <td class="yellow-bg">73</td>
                        <td class="yellow-bg">74</td>
                        <td class="yellow-bg">76</td>
                        <td class="yellow-bg">77</td>
                        <td class="yellow-bg">78</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">82</td>
                        <td class="blue-bg">28</td>
                        <td class="yellow-bg">75</td>
                        <td class="yellow-bg">76</td>
                        <td class="yellow-bg">77</td>
                        <td class="yellow-bg">79</td>
                        <td class="yellow-bg">80</td>
                        <td class="orange-bg">81</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">86</td>
                        <td class="blue-bg">30</td>
                        <td class="yellow-bg">77</td>
                        <td class="yellow-bg">79</td>
                        <td class="yellow-bg">80</td>
                        <td class="orange-bg">82</td>
                        <td class="orange-bg">83</td>
                        <td class="orange-bg">85</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">90</td>
                        <td class="blue-bg">32</td>
                        <td class="yellow-bg">79</td>
                        <td class="orange-bg">81</td>
                        <td class="orange-bg">83</td>
                        <td class="orange-bg">85</td>
                        <td class="orange-bg">86</td>
                        <td class="orange-bg">88</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">93</td>
                        <td class="blue-bg">34</td>
                        <td class="orange-bg">82</td>
                        <td class="orange-bg">84</td>
                        <td class="orange-bg">86</td>
                        <td class="orange-bg">88</td>
                        <td class="orange-bg">89</td>
                        <td class="red-bg">91</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">97</td>
                        <td class="blue-bg">36</td>
                        <td class="orange-bg">84</td>
                        <td class="orange-bg">86</td>
                        <td class="orange-bg">88</td>
                        <td class="red-bg">91</td>
                        <td class="red-bg">93</td>
                        <td class="red-bg">95</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">100</td>
                        <td class="blue-bg">38</td>
                        <td class="orange-bg">87</td>
                        <td class="orange-bg">89</td>
                        <td class="red-bg">91</td>
                        <td class="red-bg">94</td>
                        <td class="red-bg">96</td>
                        <td class="red-bg">98</td>
                    </tr>
                    <tr>
                        <td class="sky-blue-bg">104</td>
                        <td class="blue-bg">40</td>
                        <td class="orange-bg">89</td>
                        <td class="red-bg">92</td>
                        <td class="red-bg">94</td>
                        <td class="red-bg">97</td>
                        <td class="red-bg">99</td>
                        <td class="red-bg">102</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        </#if>

        <div class="col-5 px-4">
            <h3 class="title-primary mb-1">
                <span>${localization.getMessage("Report.Heatstress.Legends", [],"Legends",locale)}</span>
            </h3>

            <div class="fullwidth-card-row mb-1">
                <div class="fullwidth-card-img">
                    <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="24" cy="24.4697" r="23.25" fill="#4EBA7D" stroke="#4EBA7D" stroke-width="1.5">
                        </circle>
                        <path
                            d="M28.4709 27.8126V12.0407C28.4709 9.57571 26.4655 7.57031 24.0005 7.57031C21.5355 7.57031 19.5297 9.57571 19.5297 12.0407V27.8126C17.6154 29.2288 16.4865 31.4511 16.4865 33.8502C16.4865 37.9932 19.8571 41.3638 24.0001 41.3638C28.1432 41.3638 31.5138 37.9932 31.5138 33.8502C31.5138 31.4511 30.3848 29.2288 28.4705 27.8126H28.4709ZM24.0005 39.5957C20.8321 39.5957 18.2546 37.0182 18.2546 33.8498C18.2546 31.8897 19.2408 30.0835 20.8926 29.0184L21.2975 28.7573V12.0407C21.2975 10.5504 22.5098 9.33808 24.0001 9.33808C25.4905 9.33808 26.7028 10.5504 26.7028 12.0407V28.7573L27.1077 29.0184C28.7595 30.0835 29.7457 31.8897 29.7457 33.8498C29.7457 37.0179 27.1682 39.5957 23.9998 39.5957H24.0005Z"
                            fill="white"></path>
                        <path d="M25.2295 27.1318H22.7706V31.1753H25.2295V27.1318Z" fill="#2C7321"></path>
                        <path
                            d="M23.9999 28.3687C24.6788 28.3687 25.2292 27.8183 25.2292 27.1394C25.2292 26.4605 24.6788 25.9102 23.9999 25.9102C23.321 25.9102 22.7706 26.4605 22.7706 27.1394C22.7706 27.8183 23.321 28.3687 23.9999 28.3687Z"
                            fill="#2C7321"></path>
                        <path
                            d="M24.0001 38.3277C26.4532 38.3277 28.4419 36.3408 28.4419 33.8899C28.4419 31.439 26.4532 29.4521 24.0001 29.4521C21.5469 29.4521 19.5582 31.439 19.5582 33.8899C19.5582 36.3408 21.5469 38.3277 24.0001 38.3277Z"
                            fill="#2C7321"></path>
                    </svg>
                </div>
                <div class="fullwidth-card-content">
                    <h4>${localization.getMessage("HeatstressGreen", [],"Stress threshold",locale)}</h4>
                    <p>${localization.getMessage("Report.Heatstress.Stress.Threshold.Message", [],"Respiration exceeds
                        60 BPM | Repro losses detectable | Rectal temperature exceeds 38.5°C (101.3°F)",locale)}</p>
                </div>
            </div>

            <div class="fullwidth-card-row mb-1">
                <div class="fullwidth-card-img">
                    <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="24" cy="24.4697" r="23.25" fill="#FFCB29" stroke="#FFCB29" stroke-width="1.5">
                        </circle>
                        <path
                            d="M28.6654 27.4825V11.7122C28.6654 9.24739 26.6602 7.24219 24.1951 7.24219C21.73 7.24219 19.7251 9.24739 19.7251 11.7122V27.4825C17.811 28.8987 16.6821 31.1207 16.6821 33.5196C16.6821 37.6622 20.0525 41.0325 24.1951 41.0325C28.3377 41.0325 31.7081 37.6622 31.7081 33.5196C31.7081 31.1207 30.5792 28.8987 28.6651 27.4825H28.6654ZM24.1954 39.2646C21.0274 39.2646 18.4501 36.6873 18.4501 33.5192C18.4501 31.5592 19.4362 29.7532 21.0879 28.6882L21.4927 28.4272V11.7122C21.4927 10.2219 22.7049 9.00979 24.1951 9.00979C25.6853 9.00979 26.8975 10.2219 26.8975 11.7122V28.4272L27.3023 28.6882C28.954 29.7532 29.9401 31.5592 29.9401 33.5192C29.9401 36.687 27.3628 39.2646 24.1948 39.2646H24.1954Z"
                            fill="white"></path>
                        <path d="M25.4246 21.7402H22.9659V30.3658H25.4246V21.7402Z" fill="#CD9C1F"></path>
                        <path
                            d="M24.1951 22.9388C24.8739 22.9388 25.4243 22.3885 25.4243 21.7096C25.4243 21.0308 24.8739 20.4805 24.1951 20.4805C23.5163 20.4805 22.9659 21.0308 22.9659 21.7096C22.9659 22.3885 23.5163 22.9388 24.1951 22.9388Z"
                            fill="#CD9C1F"></path>
                        <path
                            d="M24.1951 37.9987C26.648 37.9987 28.6365 36.0121 28.6365 33.5614C28.6365 31.1107 26.648 29.124 24.1951 29.124C21.7422 29.124 19.7537 31.1107 19.7537 33.5614C19.7537 36.0121 21.7422 37.9987 24.1951 37.9987Z"
                            fill="#CD9C1F"></path>
                    </svg>
                </div>
                <div class="fullwidth-card-content">
                    <h4>${localization.getMessage("Report.Heatstress.Mild.Moderate.Stress", [],"Mild - moderate
                        stress",locale)}</h4>
                    <p>${localization.getMessage("Report.Heatstress.Mild.Moderate.Stress.Message", [],"Respiration
                        exceeds 75 BPM | Rectal temperature exceeds 39°C (102.2°F)",locale)}</p>
                </div>
            </div>

            <div class="fullwidth-card-row mb-1">
                <div class="fullwidth-card-img">
                    <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="24" cy="24.4697" r="23.25" fill="#F58837" stroke="#F58837" stroke-width="1.5">
                        </circle>
                        <path
                            d="M28.4251 27.4728V11.7024C28.4251 9.23763 26.4199 7.23242 23.9551 7.23242C21.4903 7.23242 19.4851 9.23763 19.4851 11.7024V27.4728C17.571 28.8889 16.4421 31.111 16.4421 33.5098C16.4421 37.6524 19.8125 41.0228 23.9551 41.0228C28.0977 41.0228 31.4681 37.6524 31.4681 33.5098C31.4681 31.111 30.3392 28.8889 28.4251 27.4728ZM23.9551 39.2548C20.787 39.2548 18.2097 36.6775 18.2097 33.5095C18.2097 31.5495 19.1959 29.7435 20.8475 28.6785L21.2524 28.4174V11.7024C21.2524 10.2122 22.4645 9.00002 23.9548 9.00002C25.445 9.00002 26.6572 10.2122 26.6572 11.7024V28.4174L27.062 28.6785C28.7137 29.7435 29.6998 31.5495 29.6998 33.5095C29.6998 36.6772 27.1225 39.2548 23.9544 39.2548H23.9551Z"
                            fill="white"></path>
                        <path d="M25.1842 16.4053H22.7256V30.8326H25.1842V16.4053Z" fill="#96511D"></path>
                        <path
                            d="M23.9551 17.4905C24.634 17.4905 25.1843 16.9402 25.1843 16.2614C25.1843 15.5825 24.634 15.0322 23.9551 15.0322C23.2763 15.0322 22.726 15.5825 22.726 16.2614C22.726 16.9402 23.2763 17.4905 23.9551 17.4905Z"
                            fill="#96511D"></path>
                        <path
                            d="M23.9551 37.989C26.408 37.989 28.3965 36.0023 28.3965 33.5516C28.3965 31.1009 26.408 29.1143 23.9551 29.1143C21.5022 29.1143 19.5137 31.1009 19.5137 33.5516C19.5137 36.0023 21.5022 37.989 23.9551 37.989Z"
                            fill="#96511D"></path>
                    </svg>
                </div>
                <div class="fullwidth-card-content">
                    <h4>${localization.getMessage("Report.Heatstress.Moderate.Severe.Stress", [],"Moderate - severe
                        stress",locale)}</h4>
                    <p>${localization.getMessage("Report.Heatstress.Moderate.Severe.Stress.Message", [],"Respiration
                        exceeds 85 BPM | Rectal temperature exceeds 40°C (104°F)",locale)}</p>
                </div>
            </div>

            <div class="fullwidth-card-row mb-1">
                <div class="fullwidth-card-img">
                    <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="24" cy="24.4697" r="24" fill="#F75D5E"></circle>
                        <path
                            d="M28.7642 27.4728V11.7024C28.7642 9.23763 26.759 7.23242 24.2942 7.23242C21.8294 7.23242 19.8242 9.23763 19.8242 11.7024V27.4728C17.9101 28.8889 16.7812 31.111 16.7812 33.5098C16.7812 37.6524 20.1516 41.0228 24.2942 41.0228C28.4369 41.0228 31.8072 37.6524 31.8072 33.5098C31.8072 31.111 30.6783 28.8889 28.7642 27.4728ZM24.2942 39.2548C21.1261 39.2548 18.5488 36.6775 18.5488 33.5095C18.5488 31.5495 19.535 29.7435 21.1866 28.6785L21.5915 28.4174V11.7024C21.5915 10.2122 22.8037 9.00002 24.2939 9.00002C25.7841 9.00002 26.9963 10.2122 26.9963 11.7024V28.4174L27.4011 28.6785C29.0528 29.7435 30.0389 31.5495 30.0389 33.5095C30.0389 36.6772 27.4616 39.2548 24.2935 39.2548H24.2942Z"
                            fill="white"></path>
                        <path d="M25.5234 11.5049H23.0647V30.8319H25.5234V11.5049Z" fill="#912727"></path>
                        <path
                            d="M24.2939 12.7318C24.9727 12.7318 25.523 12.1814 25.523 11.5026C25.523 10.8238 24.9727 10.2734 24.2939 10.2734C23.615 10.2734 23.0647 10.8238 23.0647 11.5026C23.0647 12.1814 23.615 12.7318 24.2939 12.7318Z"
                            fill="#912727"></path>
                        <path
                            d="M24.2938 37.989C26.7468 37.989 28.7353 36.0023 28.7353 33.5516C28.7353 31.1009 26.7468 29.1143 24.2938 29.1143C21.8409 29.1143 19.8524 31.1009 19.8524 33.5516C19.8524 36.0023 21.8409 37.989 24.2938 37.989Z"
                            fill="#912727"></path>
                    </svg>
                </div>
                <div class="fullwidth-card-content">
                    <h4>${localization.getMessage("HeatstressRed", [],"Severe stress",locale)}</h4>
                    <p>${localization.getMessage("Report.Heatstress.Severe.Stress.Message", [],"Respiration exceeds
                        120-140 BPM | Rectal temperature exceeds 41°C (106°F)",locale)}</p>
                </div>
            </div>
        </div>

    </div>
</div>

<#if model.heatStressEvaluationTool?? && model.heatStressEvaluationTool.notes??>
    <#if model.heatStressEvaluationTool.animalInputs ?? || model.heatStressEvaluationTool.weather ?? ||
        model.heatStressEvaluationTool.exposure ?? || model.heatStressEvaluationTool.results ?? ||
        model.heatStressEvaluationTool.temperatureAndHumidityIndexValue ??>
        <div class="break-page"></div>
    </#if>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.heatStressEvaluationTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1"  style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>