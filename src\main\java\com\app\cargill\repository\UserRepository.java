/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.model.User;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

  Optional<User> findByPrincipalName(String name);

  Optional<User> findByPrincipalNameAndAuthenticationPlatform(
      String username, AuthenticationPlatform none);

  @Query(
      value =
          "Select u.user_document->>'CountryId' from Users u where"
              + " LOWER(u.user_document->>'UserName')=LOWER(:currentLoggedInUser) AND u.deleted ="
              + " false",
      nativeQuery = true)
  String findCountryIdByUserName(@Param("currentLoggedInUser") String currentLoggedInUser);

  @Query(
      value =
          "Select count(u.user_document->>'id') >0 from Users u where"
              + " LOWER(u.user_document->>'UserName')=LOWER(:email) and u.deleted=false",
      nativeQuery = true)
  boolean existsByUserId(@Param("email") String email);

  @Query(
      value =
          "SELECT u.* from Users u where LOWER(u.user_document->>'UserName')=LOWER(:userName) and"
              + " u.deleted= false",
      nativeQuery = true)
  User findByUserName(@Param("userName") String userName);

  @Query(
      value =
          "SELECT u.* from Users u where LOWER(u.user_document->>'UserName') in (:userNames) and"
              + " u.deleted= false",
      nativeQuery = true)
  List<User> findMultipleUsersLowerCase(List<String> userNames);

  @Query(value = "SELECT u.* from Users u", nativeQuery = true)
  List<User> findAllWithDeleted();

  @Query(value = "SELECT u.* from Users u where u.user_document->>'id' = :id", nativeQuery = true)
  User findByUuid(@Param("id") String id);

  @Query(
      value =
          "SELECT u.* from Users u where u.user_document->>'CountryId' in"
              + " ('US','Global','Canada','Brazil') and u.deleted= false",
      nativeQuery = true)
  List<User> findAllUsersforLiftDeactive();

  @Query(
      value =
          "SELECT u.* from Users u where "
              + "u.user_document->>'LastLoginDateTime' > :dateFrom "
              + " and u.user_document->>'LastLoginDateTime' <= :dateTo"
              + " and u.deleted= false",
      nativeQuery = true)
  List<User> findAllUsersRecentUsed(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo);

  @Query(
      value =
          "SELECT u.* from Users u where LOWER(full_name)=LOWER(:code) and" + " u.deleted= false",
      nativeQuery = true)
  List<User> findByUserNameWithCode(@Param("code") String userName);
}
