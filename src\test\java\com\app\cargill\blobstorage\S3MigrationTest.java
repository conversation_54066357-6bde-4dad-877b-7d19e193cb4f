/* Cargill Inc.(C) 2022 */
package com.app.cargill.blobstorage;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.amazonaws.services.s3.model.PutObjectResult;
import com.app.cargill.cosmos.migration.MigrationResult;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.UserMigrationRepository;
import com.app.cargill.service.impl.AwsCredentialsFactory;
import com.app.cargill.service.impl.BlobServiceClientFactory;
import com.app.cargill.service.impl.S3ServiceImpl;
import com.azure.core.http.HttpHeaderName;
import com.azure.core.http.HttpHeaders;
import com.azure.core.http.HttpMethod;
import com.azure.core.http.HttpRequest;
import com.azure.core.http.rest.*;
import com.azure.core.util.IterableStream;
import com.azure.storage.blob.BlobClient;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.models.BlobContainerItem;
import com.azure.storage.blob.models.BlobItem;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.IntStream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.transfer.s3.S3TransferManager;
import software.amazon.awssdk.transfer.s3.model.Upload;
import software.amazon.awssdk.transfer.s3.model.UploadRequest;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
class S3MigrationTest {
  private final String containerName = "container";
  private final String itemNameWithDuplicateUUID =
      "Image/ba9db075-8275-4034-ab25-5ec189ca19a3-ba9db075-8275-4034-ab25-5ec189ca19a3.jpg";
  private final String itemNameWithoutDuplicateUUID =
      "Image/ba9db075-8275-4034-ab25-5ec189ca19a4.jpg";
  @Mock private S3AsyncClient s3AsyncClient;
  @Mock private S3TransferManager s3TransferManager;
  @Mock private BlobServiceClientFactory blobServiceClientFactory;
  @InjectMocks private S3Migration s3Migration;
  private List<? extends PagedResponse<BlobContainerItem>> pagedResponses;
  @Mock BlobContainerClient blobContainerClient;
  @Mock private S3ServiceImpl s3Service;
  @Mock UserMigrationRepository userMigrationRepository;
  @Mock AccountsRepository accountsRepository;
  @Mock private AwsCredentialsFactory awsCredentialsFactory;

  void setUpForValidCredentials() throws CustomDEExceptions {
    s3Service.setAwsRegion("us-west-1");
    s3Service.setBucketName("test");
    lenient()
        .when(awsCredentialsFactory.getCredentialsProvider())
        .thenReturn(() -> AwsBasicCredentials.create("x", "x"));
    blobServiceClientFactory.setBlobStorageConnectionString("c29tZUNvbm5TdHJpbmc=");
    s3Migration.setAwsRegion("us-east-1");
    s3Migration.setBucketName("test-bucket");
    BlobServiceClient blobServiceClient = mock(BlobServiceClient.class);
    BlobContainerClient blobContainerClient = mock(BlobContainerClient.class);
    when(blobServiceClientFactory.getBlobClient()).thenReturn(blobServiceClient);
    when(blobServiceClientFactory.getBlobClient().listBlobContainers(any(), any()))
        .thenReturn(getContainerItems());
    when(blobServiceClient.getBlobContainerClient(anyString())).thenReturn(blobContainerClient);
    when(blobContainerClient.listBlobs()).thenReturn(getPagedBlobItem());
    when(blobContainerClient.getBlobClient(anyString())).thenReturn(mock(BlobClient.class));
  }

  @Test
  void whenUploadBlobToS3ReturnSuccess() throws IOException {

    when(blobContainerClient.getBlobContainerName()).thenReturn("test");
    when(s3TransferManager.upload(any(UploadRequest.class))).thenReturn(mock(Upload.class));
    try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
      stream.write("test".getBytes(StandardCharsets.UTF_8));
      assertDoesNotThrow(
          () -> {
            s3Migration.uploadBlobItemToS3(
                "Image/" + UUID.randomUUID() + "-" + UUID.randomUUID(),
                stream,
                s3TransferManager,
                blobContainerClient);
          });
    }
  }

  void setUpForInvalidCredentials() throws CustomDEExceptions {
    s3Service.setAwsRegion("us-west-1");
    s3Service.setBucketName("test");
    lenient()
        .when(awsCredentialsFactory.getCredentialsProvider())
        .thenReturn(() -> AwsBasicCredentials.create("x", "x"));
    blobServiceClientFactory.setBlobStorageConnectionString("c29tZUNvbm5TdHJpbmc=");
    s3Migration.setAwsRegion("us-east-1");
    s3Migration.setBucketName("test-bucket");
    BlobServiceClient blobServiceClient = mock(BlobServiceClient.class);
    BlobContainerClient blobContainerClient = mock(BlobContainerClient.class);
    when(blobServiceClientFactory.getBlobClient()).thenReturn(blobServiceClient);
  }

  @Test
  void whenS3MigrationTestIsSuccessful() throws IOException, CustomDEExceptions {
    setUpForValidCredentials();
    PutObjectResult mockedResponse = new PutObjectResult();
    mockedResponse.setVersionId("some-version-id");
    assertDoesNotThrow(() -> s3Migration.migrate("<EMAIL>"));
    assertDoesNotThrow(() -> s3Migration.migrate(1, true));
  }

  @Test
  void whenS3MigrationFailCorrectFutureIsReturned() {
    when(accountsRepository.findAccountIdsByEmailIn(any()))
        .thenThrow(new IllegalArgumentException("Test failing s3 migration"));
    CompletableFuture<MigrationResult> result = s3Migration.migrate("any");
    assertTrue(result.isCompletedExceptionally());
  }

  @Test
  void whenS3MigrationTestFailsOnInvalidCredentials() throws CustomDEExceptions {
    setUpForInvalidCredentials();
    when(blobServiceClientFactory.getBlobClient()).thenReturn(null);
    assertThrows(
        CustomDEExceptions.class,
        () -> {
          s3Migration.migrate(1, false);
        });
  }

  @Test
  void whenS3MigrationTestFailsOnDownload() throws CustomDEExceptions {
    setUpForValidCredentials();
    when(blobServiceClientFactory
            .getBlobClient()
            .getBlobContainerClient(anyString())
            .getBlobClient(anyString()))
        .thenReturn(null);
    assertDoesNotThrow(
        () -> {
          s3Migration.migrate(1, true);
        });
  }

  @Test
  void whenS3MigrationTestFailsOnUpload() throws CustomDEExceptions {
    setUpForValidCredentials();
    when(blobServiceClientFactory
            .getBlobClient()
            .getBlobContainerClient("test")
            .getBlobContainerName())
        .thenReturn(null);
    assertDoesNotThrow(
        () -> {
          s3Migration.migrate(1, true);
        });
  }

  @Test
  void whenGetModifiedFileNameReturnCorrectName() {

    assertNotNull(
        s3Migration.getModifiedFileName("Image/" + UUID.randomUUID() + "-" + UUID.randomUUID()));
  }

  @Test
  void whenGetModifiedFileNameReturnNotCorrectName() {

    assertNotNull(s3Migration.getModifiedFileName("Image/test"));
  }

  private PagedIterable<BlobContainerItem> getContainerItems() {
    final HttpHeaders httpHeaders =
        new HttpHeaders()
            .set(HttpHeaderName.fromString("header1"), "value1")
            .set(HttpHeaderName.fromString("header2"), "value2");
    final HttpRequest httpRequest = new HttpRequest(HttpMethod.GET, "http://localhost");
    final String deserializedHeaders = "header1,value1,header2,value2";

    pagedResponses =
        IntStream.range(0, 3)
            .mapToObj(
                i -> {
                  var item = new BlobContainerItem();
                  item.setName(containerName + i);
                  return new PagedResponseBase<String, BlobContainerItem>(
                      httpRequest,
                      200,
                      httpHeaders,
                      Collections.singletonList(item),
                      Integer.toString(i + 1),
                      deserializedHeaders);
                })
            .toList();

    PagedFlux<BlobContainerItem> flux =
        new PagedFlux<>(
            () -> pagedResponses.isEmpty() ? Mono.empty() : Mono.just(pagedResponses.get(0)),
            this::getNextPage);

    return new PagedIterable<>(flux);
  }

  private Mono<PagedResponse<BlobContainerItem>> getNextPage(String continuationToken) {
    if (continuationToken == null || continuationToken.isEmpty()) {
      return Mono.empty();
    }

    int parsedToken = Integer.parseInt(continuationToken);
    if (parsedToken >= 1) {
      return Mono.empty();
    }

    return Mono.just(pagedResponses.get(parsedToken));
  }

  private PagedIterable<BlobItem> getPagedBlobItem() {
    PagedIterable<BlobItem> listBlobItem =
        new PagedIterable<>(
            () ->
                new PagedResponseBase<Void, BlobItem>(
                    null,
                    200,
                    null,
                    new Page<BlobItem>() {
                      @Override
                      public IterableStream<BlobItem> getElements() {
                        return new IterableStream<>(
                            Arrays.asList(
                                new BlobItem().setName(itemNameWithDuplicateUUID),
                                new BlobItem().setName(itemNameWithoutDuplicateUUID)));
                      }

                      @Override
                      public String getContinuationToken() {
                        return null;
                      }
                    },
                    null) {});
    return listBlobItem;
  }
}
