(Неправильная=подготовка сосков\: &lt;10 секунд; время до подключения\:  &lt; 60 или &gt; 120 секунд)
(Соответствующая=подготовка сосков\: 10-20 секунд; время до подключения 60-90 секунд)
(только=подключение аппарата)
AAEfficiency=АК эффективность
AMSUtilization=Использование AMS
AMSUtilizationChart=Таблица использования робота для лечения
ARA=ARA
ARS=Аргентина ($ ARS)
AUD=Австралия ($ AUD)
Account=счет
Account-Not-Synced-To-Lift=Аккаунт не синхронизирован с LIFT, пожалуйста свяжитесь с вашим администратором  для решения вопроса
Acre=Акра
Action=Действие
AddBag=Добавить рукав
AddBunker=Добавить траншею
AddPile=Добавить курган
AddTMRScore=Добавить оценку ПСР
AdjustingKPtoAssureSuccess=Регулировка степени обработки зерен для достижения результатов
Afghanistan=Афганистан
Agrigento=Агригенто
Aguascalientes=Aguascalientes
Alabama=Алабама
Alagoas=Алагоас
Aland_Islands=Аландские острова
Alaska=Вниз
Albania=Албания
Alberta=Альберта
Alessandria=Александрия
Algeria=Алжир
Amapá=Ампан
Amazonas=Амазонас
Amount=Количество
Ancona=Анкона
Andaman_and_Nicobar_Islands=Андаманские и Никобарские острова
Andhra_Pradesh=Андхра -Прадеш
Andorra=Андорра
Angola=Ангола
Anguilla=Ангилья
Anhui=Анхуй
AnimalInformation=Информация о животных
AnimalListViewModel.Title=Класс/подкласс животных
Animals=Животных
AnimalsInHerd=Животных в стаде
AnimalsInPen=Животных в секции
AnimalsObserved=Наблюдаемых животных
Annually=Ежегодно
Answers=Ответы
Antarctica=Антарктида
Antigua_and_Barbuda=Антигуа и Барбуда
Aosta=Аоста
AppName=Dairy Enteligen 
Arezzo=Ареццо
Argentina=Аргентина
Arizona=Аризона
Arkansas=Арканза
Armenia=Армения
Aruba=Аруба
Arunachal_Pradesh=Аруначал -Прадеш
Ascoli_Piceno=Асколи Пикено
Assam=Ассам
Asti=До
AtSixLengthPerDayImperial=15 см/день
AtSixLengthPerDayMetric=15 см /день
AtThreeLengthPerDayImperial=7 см/день
AtThreeLengthPerDayMetric=7 см/день
Australia=Австралия
Australian_Capital_Territory=территория столицы Австралии
Austria=Австрия
Auto_Sync=Автоматическая синхронизация
Avellino=Авеллино
Average=Нормально
AverageMilkLoss=Потери молока в среднем ({0})
AverageScoreTitle=Средняя оценка ПСР
AvgBCS=Ср. балл упитанности
AvgLocomotionScore=Ср.балл походки
Azerbaijan=Азербайджан
BAM=BAM
BCS=Упитанность
BCSCategory1=Балл упитанности 1.0
BCSCategory1pt5=Балл упитанности 1.5
BCSCategory2=Балл упитанности 2.0
BCSCategory2pt5=Балл упитанности 2.5
BCSCategory3=Балл упитанности 3.0
BCSCategory3pt5=Балл упитанности 3.5
BCSCategory4=Балл упитанности 4.0
BCSCategory4pt5=Балл упитанности 4.5
BCSCategory5=Балл упитанности 5.0
BCSEditMilkAndDimViewModel.BCSDIMTitle=Дней лактации(ДЛ)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Молоко
BCSEditMilkAndDimViewModel.Title=Редактировать ДЛ и молоко
BCSHerdAnalysisInputsViewModel.BCS=Упитанность
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Упитанность - анализ
BCSHerdAnalysisInputsViewModel.BCSDIM=ДЛ
BCSHerdAnalysisInputsViewModel.BCSEdit=Редактировать
BCSHerdAnalysisInputsViewModel.BCSMilk=Молоко
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Анализ стада
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Цели
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Вводные данные
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Результаты
BCSHerdAnalysisMasterViewModel.BCSTitle=Упитанность 
BCSHerdAnalysisMasterViewModel.Title=Упитанность 
BCSHerdAnalysisResultsViewModel.BCSAvg=Ср. упитанность 
BCSHerdAnalysisResultsViewModel.GraphTitle=Упитанность - Анализ
BCSHerdAnalysisResultsViewModel.MaxBCS=Упитанность (макс.)
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Молоко/гол./день
BCSHerdAnalysisResultsViewModel.MinBCS=Упитанность (мин.)
BCSHerdAnalysisResultsViewModel.SubHeading=Анализ стада
BCSHerdAnalysisResultsViewModel.Title=Оценка упитанности
BCSPenSelectionViewModel.PenSelectionList=Секции (Группы)
BCSPenSelectionViewModel.Pens=Секции (Группы)
BCSPenSelectionViewModel.SelectPointScale=Выбрать шкалу оценки
BCSPenSelectionViewModel.Title=Оценка упитанности
BCSSelectPointScaleViewModel.FooterText=При посещении может быть использована ТОЛЬКО одна балльная шкала оценки. Замена шкалы оценки приведет к потере данных
BCSSelectPointScaleViewModel.SelectPointScale=Выбрать шкалу оценки
BCSSelectPointScaleViewModel.Title=Оценка упитанности
BGL=BGL
BRL=Бразилия (R$ BRL)
BRR=BRR
Bad=Bad
Bag=Силосный рукав
BaggedConventionalSilage=Стандартный силос в рукавах
Bags=Силосный рукав
Bahamas=Багамские острова
Bahia=Бахия
Bahrain=Бахрейн
Baja_California=Баджа Калифорния
Baja_California_Sur=Baja California Sur
Baleage=Тюки
BaleageFQAs=Тюки ЧАВО
Baleage_AreBalesWrappedWith=Тюки обмотаны\:
Baleage_BagsPlacedOnStableWellManagedSurface=Тюки размещены на ровной и твердой поверхности (асфальт или бетон)?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Осматриваются ли тюки на наличие повреждений вредителей и ставятся ли заплатки? (Еженедельно)
Baleage_TrashVegRodentControlledAroundBags=Ведется ли контроль за наличием грызунов, растительности и мусора вокруг тюков?
Baleage_WaterShedsOffPlasticNotIntoBaleage=Не затекает ли вода с пленки в тюки? (сложность возникает с большими квадратными тюками)
Bangladesh=Бангладеш
Barbados=Барбадос
Bari=Они были
Barletta-Andria-Trani=Barletta-andria-trani
Bayern=Бавария
BeddedPack=Глубокая подстилка
Beijing=Пекин
Belarus=Беларусь
Belgium=Бельгия
Belize=Белиз
Belluno=Беллуно
Benchmarks_Serum_ToolTip=Всплывающая подсказка по контролю сыворотки
Benevento=Беневенто
Benin=Бенин
Bergamo=Бергамо
Bermuda=Бермудские острова
BetweenFifteenTwenty=15-20
Bhutan=Бутан
BiWeekly=Раз в две недели
Biella=Билла
Bihar=Бихар
BodyConditionHerdGoals=Упитанность стада - цели
BodyConditionHerdInputs=Упитанность стада - ввод данных
BodyConditionHerdResults=Упитанность стада - результаты
BodyConditionInputs=Упитанность - ввод данных
BodyConditionResults=Упитанность - результаты
BodyConditionScoreCategory=Кондиция упитанности, балл {0}
BodyConditionScoreEditInputsViewModel.Count=Счет
BodyConditionScoreEditInputsViewModel.NumberOfCows=Кол-во коров
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Пожалуйста, подсчитайте количество коров
BodyConditionScoreEditInputsViewModel.Title=Кол-во коров
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=Поздний сухостой (менее -21 дня)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=Начало лактации (16-60 дней)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=Ранний сухостой (-60-21 день)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=Новотельные (0-15 дней)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=Поздняя лактация (более 201 дня)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=Упитанность, макс.
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=Середина лактации (121-200 дней)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=Упитанность, мин.
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=Пик лактации (61-120 дней)
BodyConditionScoreHerdEditGoalsViewModel.Title=Редактировать цели
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Поздний сухостой (-21 день - отел)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Начало лактации (16-60 дней)
BodyConditionScoreHerdGoalsViewModel.Edit=Редактировать
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Ранний сухостой (-60-21 день)
BodyConditionScoreHerdGoalsViewModel.Fresh=Новотельные (0-15 дней)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Максимальная цель по упитанности
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Минимальная цель по упитанности
BodyConditionScoreHerdGoalsViewModel.LateLactation=Поздняя лактация (более 201 дня)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Середина лактации (121-200 дней)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Пик лактации (61-120 дней)
BodyConditionScoreHerdGoalsViewModel.TableTitle=Упитанность по стадиям лактации
BodyConditionScoreInputsViewModel.AnimalsObserved=Наблюдаемых животных
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Ср. упитанность
BodyConditionScoreInputsViewModel.BCSCategory=Упитанность, балл
BodyConditionScoreInputsViewModel.BCSPercentOfPen=Процент от секции (%)
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=Оценка упитанности
BodyConditionScoreInputsViewModel.Edit=Редактировать
BodyConditionScoreInputsViewModel.StdDevCalculated=Станд. отклонение
BodyConditionScoreMasterViewModel.Goals=Цели
BodyConditionScoreMasterViewModel.Inputs=Вводные данные
BodyConditionScoreMasterViewModel.Results=Результаты
BodyConditionScoreMasterViewModel.SubHeading=Анализ стада
BodyConditionScoreMasterViewModel.Title=Оценка упитанности
BodyConditionScoreResultsViewModel.BCSAverageTitle=Ср.балл
BodyConditionScoreResultsViewModel.PercentPen=Процент от секции (%)
BodyConditionScoreResultsViewModel.SelectedDates=Выбрать даты
BodyConditionScoreResultsViewModel.Title=Упитаннось - результаты
BodyConditionScoresMasterViewModel.BodyConditionScore=Оценка упитанности
BodyConditionScoresMasterViewModel.Inputs=Вводные данные
BodyConditionScoresMasterViewModel.Results=Результаты
BodyConditionScoresMasterViewModel.Title=Оценка упитанности
BodyConditionScoresMasterViewModel.VisitNotebook=Блокнот посещения
Bolivia,_Plurinational_State_of=Боливия, множество
Bologna=Болонья
Bolzano=Болцано
Bonaire=Бонейр
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius и Saba
Bosnia_and_Herzegovina=Босния и Герцеговина
Botswana=Ботсвана
BottomUnloadingSilo=Башня (нижняя выгрузка)
BottomUnloadingSilos=Башня (нижняя выгрузка)
Bouvet_Island=Остров Буве
Brazil=Бразилия
Brescia=Брешия
Brindisi=Тосты
British_Columbia=британская Колумбия
British_Indian_Ocean_Territory=Британская территория Индийского океана
Brunei_Darussalam=Бруней-Даруссалам
Bulgaria=Болгария
Bull=Бык
Bunker=Траншея
BunkerCapacity=Вместительность траншеи
BunkerFeedOutRate=Скармливание траншеи
Bunkers=Траншеи
BunkersAndPiles=Курганы и траншеи
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Бонус\: 2 слоя пленки, один из которых непроницаемый
BunkersAndPiles_CleanlinessOfFeedArea=Чистота зоны хранения кормов.Оценить от 1 до 10.
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=С какой частотой убирают укрывную пленку? 
BunkersAndPiles_FaceRemoveRate=Глубина среза (выемка) грубых кормов в сутки
BunkersAndPiles_LooseOrFacedFeedIsFed=Опавший и "лицевой" корм скармливается в течение
BunkersAndPiles_PackingInitialSpreadLayers=Закладка\: начальная толщина трамбуемого слоя 15 см или менее?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Соотношение сторон кургана или траншеи менее 3\:1 (уклон склона 18°)
BunkersAndPiles_PorosityScoresConsistently=Плотность трамбовки корма (%СВ/м²)
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Скорость укрывания пленкой по завершении закладки основных кормов
BunkersAndPiles_SideWallsSealedPlastic=Боковые стены укрыты пленкой?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Гладкий срез, нет признаков нарушения слоев, что способствовало бы попаданию кислорода
BunkersAndPiles_TiresSplitsTouching=Укрыты ли корма покрышками?
Burkina_Faso=Буркина-Фасо
Burundi=Бурунди
CAD=Канада (CA$ CAD)
CFNChina=Китай
CFNIndia=Индия
CHF=Швейцария (CHF CHF)
CLF=CLF
CLP=Чили ($ CLP)
CNY=Китай (CNY CNY)
COP=COP
CPNBrazil=Бразилия
CPNFrance=Франция
CPNPoland=Польша
CPNUS=Соединенные Штаты Америки
CRC=CRC
CZK=Чехия (CZK CZK)
Cagliari=Кальяри
Calf=Телята
CalfHeiferColostrum=Телята &amp; телки - молозиво
CalfHeiferGrowerPuberty=Телята &amp; телки - периоды роста и развития, случной, стельности, предотельный
CalfHeiferKeyBenchmarks=Телята &amp; телки - ключевые показатели
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=Цвет раздела определяется следующим образом\: &lt;75% - красный; ≥75% и &lt;90% - оранжевый; ≥90% - зеленый
CalfHeiferPostweaned=Телята &amp; телки - послеотъемный период
CalfHeiferPreweaned=Телята &amp; телки - предотъемный период
CalfHeiferQuestionViewModel.Close=Закрыть
CalfHeiferQuestionViewModel.Colostrum=Молозивный период
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Рост и развитие - предотел
CalfHeiferQuestionViewModel.KeyBenchmarks=Ключевые показатели
CalfHeiferQuestionViewModel.Postweaned=Послеотъемный период
CalfHeiferQuestionViewModel.Preweaned=Предотъемный период
CalfHeiferQuestionViewModel.Resources=Ресурсы
CalfHeiferQuestionViewModel.VisitNotebook=Блокнот посещения
CalfHeiferResources=Телята &amp; телки - ресурсы
CalfHeiferResults=Телята &amp; телки - результаты
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Оценка выращивания телят и телок
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Рост и развитие - предотел
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Включить в общую оценку
CalfHeiferScoreCardScoreViewModel.PhaseOne=Молозивный период
CalfHeiferScoreCardScoreViewModel.PhaseThree=Послеотъемный период
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Предотъемный период
CalfHeiferScorecardImprovementViewModel.Colostrum=Молозивный период
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=Периоды роста и развития, случной, стельности, предотельный
CalfHeiferScorecardImprovementViewModel.Postweaned=Послеотъемный период
CalfHeiferScorecardImprovementViewModel.Preweaned=Предотъемный период
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Выберите категорию из списка ниже, чтобы просмотреть пошаговые результаты
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=Цвет раздела определяется следующим образом\: 100% - зеленый, &lt;100% - красный
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Записи
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Ключевые показатели
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Период 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Период 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Период 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Период 7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Период 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Периоды 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Проводите точную оценку роста и здоровья
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Стельность (15-23 месяца)
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Случной (9-15 месяцев)
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Молозивный (1-3 дни)
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Предотельный/продуктивный (23-26 месяцев)
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Роста и развития (3-9 месяцев)
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Пред-/Послеотъемный (0-3 месяца)
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Блокнот посещения
CalfHeiferScorecardLanding=Телята &amp; телки - оценка
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Ключевые показатели
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Улучшения
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Ответы
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Баллы
CalfHeiferScorecardResultsViewModel.VisitNotebook=Блокнот посещения
CalfHeiferScorecardViewModel.Colostrum=Молозивный период
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Периоды роста и развития, случной, стельности, предотельный
CalfHeiferScorecardViewModel.KeyBenchmarks=Ключевые показатели
CalfHeiferScorecardViewModel.Postweaned=Послеотъемный период
CalfHeiferScorecardViewModel.Preweaned=Предотъемный период
CalfHeiferScorecardViewModel.Resources=Ресурсы
CalfHeiferScorecardViewModel.Title=Оценка
CalfHeiferScorecardViewModel.VisitNotebook=Блокнот посещения
CalfHeiferTools=Телята &amp; телки - инструменты
CalfHeiferToolsViewModel.CalfHeiferScorecard=Оценка
CalfHeiferToolsViewModel.CalfHeiferTools=Телята &amp; телки - инструменты
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Инструменты
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Выберите инструмент из списка ниже, чтобы начать посещение
CalfHeiferToolsViewModel.CalfHeiferToolsList=Инструменты
CalfHeiferToolsViewModel.Title=Телята &amp; телки - инструменты
CalfHeiferToolsViewModel.VisitNotebook=Блокнот посещения
California=Калифорния
Caltanissetta=Кальтаниссетта
Cambodia=Камбоджа
Cameroon=Камерун
Campeche=Кампече
Campobasso=Кампобассо
Canada=Канада
Capacity=Вместительность
Cape_Verde=Зеленый накид
Carbonia-Iglesias=Карбоньи-Иглесии
Cargill=Cargill
CargillForageLabKPTest=Лабораторный тест Cargill на степень обработки зерен
Carlow=Карлоу
Caserta=Казерта
Catania=Катания
Catanzaro=Катанзаро
Category1=Оценка навоза - 1.0 балл
Category2=Оценка навоза - 2.0 балла
Category3=Оценка навоза - 3.0 балла
Category4=Оценка навоза - 4.0 балла
Category5=Оценка навоза - 5.0 баллов
Cavan=Каван
Cayman_Islands=Каймановы острова
Ceará=Квадрат
Central_African_Republic=Центрально-Африканская Республика
Chad=Чад
Chandigarh=Чандигарх
ChewsPerCud=Жев.движ./жвачку
ChewsPerCudMasterViewModel.AddNew=Добавить
ChewsPerCudMasterViewModel.CudChewingInputs=Вводные данные
ChewsPerCudMasterViewModel.CudChewingResults=Результаты
ChewsPerCudMasterViewModel.NumOfChews=Кол-во жев. движ.
ChewsPerCudMasterViewModel.Title={0} / \# жев.движ.
Chhattisgarh=Чхаттисгарх
Chiapas=Чиапас
Chieti=Чиети
Chihuahua=Чихуахуа
Chile=Чили
China=Китай
Chinese_Taipei=Китайский Тайбэй
Chongqing=Чунцин
ChooseAppPDF=Выберите приложение для просмотра PDF
ChoosingtheCorrectAdditive=Выбор правильной добавки
Christmas_Island=Остров Рождества
Clare=Клэр
ClassSubClass=Класс/подкласс животных
Clean=Clean
ClinicalMastitisLosses=Потери - клинич. мастит
CloseUp=Предотел
CloseUpDry=Поздний сухостой
CloseUpHeifer=Нетель перед отелом
Coahuila=Коахула
Cocos_(Keeling)_Islands=Кокосовые (Килинг) Острова
Colima=Колима
Colombia=Колумбия
Colorado=Колорадо
Colostrum=Молозиво
Colostrum_AmountOfColostrumOrFed=Объем выпойки молозива (л)
Colostrum_BrixPercentOfColostrumFed=Качество молозива, Brix % 
Colostrum_CleanAndDryCalvingArea=В помещении для отела чисто и сухо
Colostrum_CleanAndDryCalvingArea_ToolTip=Используйте тест "мокрое колено" для оценки подстилки
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Оборудование для выпойки моется и дезинфицируется 
Colostrum_CleanCalfCartToTransportCalf=Проводится дезинфекция оборудования для транспортировки телят
Colostrum_HoursTillCalfIsRemovedFromMother=Теленка забирают от коровы в течение … ч
Colostrum_HoursTillCalfReceivesColostrum=Первая выпойка в течение … ч после отела 
Colostrum_NumberOfCowsInCalvingArea=Количество животных в помещении для отела 
Colostrum_PasteurizeColostrumBeforeFeeding=Проводится выпойка пастеризованного молозива
Colostrum_PercentageOfNavelsDippedInSevenPercent=% телят с обработанной пуповиной (погружение в 7% р-р йода)
Colostrum_RefrigeratedColostrumStoredLess=Охлажденное молозиво хранится менее 24 ч
ComfortToolsViewModel.ComfortHeading=Выберите инструмент из списка, чтобы начать посещение
ComfortToolsViewModel.ComfortToolsList=Инструменты
ComfortToolsViewModel.ComfortToolsTitle=Оценка комфорта
ComfortToolsViewModel.HealthHeading=Выберите инструмент из списка, чтобы начать посещение
ComfortToolsViewModel.HeatstressEvaluationTitle=Оценка теплового стресса
ComfortToolsViewModel.PenTimeTitle=Время секции
ComfortToolsViewModel.Title=Оценка комфорта
ComfortToolsViewModel.VisitNotebook=Блокнот посещения
Comments=Комментарии
CommonSpinnerViewModel.BodyConditionPDF=Руководство по оценке упитанности 
CommonSpinnerViewModel.InadequateStimulation=Неправильная стимуляция
CommonSpinnerViewModel.LocomotionPDF=Руководство по оценке походки
CommonSpinnerViewModel.ManureScorePDF=Руководство по оценке навоза
CommonSpinnerViewModel.NoStimulation=Без стимуляции 
CommonSpinnerViewModel.OptimalStimulation=Оптимальная Стимуляция
Como=Как
Comoros=Коморос
Competitor=Конкурент
CompletedTimeKey=Время завершения
Component=Компонент
Compostbarn=Compostbarn
ConcentrateDistribution=Распределение концентрата
ConfirmExport=После нажатия "ОК" каждая выбранная страница будет загружена и сфотографирована, после Вы будете перенаправлены на исходную страницу
ConfirmScalePointSwitch=Изменение шкалы оценки приведет к потере всех введенных данных
ConfirmScorerSwitch=Изменение счетчика приведет к потере всех введенных данных
Congo=Конго
Congo,_the_Democratic_Republic_of_the=Конго, Демократическая Республика
Connecticut=Коннектикут
Consumer=Потребители
ConsumerDetailsViewModel.DeleteProspect=Удалить потребителя
ConsumerDetailsViewModel.DeleteProspectPrompt=Вы уверены, что хотите удалить этого потребителя? Потребитель, сайт и информация по текущему визиту будут утеряны
ConsumerDetailsViewModel.MainHeading=Сайты
ConsumerDetailsViewModel.NewSite=Добавить новый сайт
ConsumerDetailsViewModel.ProspectTitle=Детали потребителя
ConsumersViewModel.NewConsumer=Добавить нового потребителя
Continue=Продолжение
Cook_Islands=Острова Кука
Cork=Пробег
Corn=Кукуруза
CornSilage=Кукурузный силос  
CornSilageKernel=Тюки ЧАВО
CornSilageResources=Ресурсы для кукурузного силоса
CornSilageStopGo=Кукурузный силос Стоп энд Гоу
Cosenza=Косенца
Costa_Rica=Коста -Рика
Costs=Затраты
Cote_d'Ivoire=Кот-д'Ивуар
Count=Счет
Cow=Корова
CowEfficiency=Эффективность коров
CowPerRobot=Коровы на робота
CowsOutsideTargetRangeToolTip=Цель - не более 20% коров за пределами желаемого диапазона
CowsPerDayNeeded=Необходимое кол-во коров/дней
CowsSectionToolTip=Чтобы сделать точные выводы, необходимо проверить 8 или более коров. В малочисленных стадах необходимо проверять всех коров перед отелом
CowsToBeFed=Для кормления…коров, гол.
CreateDuplicateDiet=Рацион для этой группы животных уже существует. Все равно создать?
CreateDuplicateNameDiet=Рацион с таким названием уже существует, введите другое название
Created=Созданный
Cremona=Кремона
Croatia=Хорватия
CropCharacteristicsDecisionGuide=Руководство принятия решений по характеристикам с/х культуры
Crotone=Кротон
Cuba=Куба
CudChewingAverageNumber=Среднее кол-во
CudChewingDataEntryViewModel.CudChewing=Наличие жвачки
CudChewingDataEntryViewModel.HerdCudChewingDescription=Для этого визита, посчитайте количество животных, жующих жвачку, в этой секции, используя счетчик. Минимальное количество животных - 10. 
CudChewingDataEntryViewModel.No=Нет
CudChewingDataEntryViewModel.Yes=Да
CudChewingHerdEditScoreViewModel.AverageChewsItem=Среднее кол-во жев.движ./болюс
CudChewingHerdEditScoreViewModel.Close=Закрыть
CudChewingHerdEditScoreViewModel.DaysInMilkItem=ДЛ 
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Редактировать жвачку
CudChewingHerdEditScoreViewModel.EditScoreTitle=Редактировать жвачку 
CudChewingHerdEditScoreViewModel.PercentChewingItem=Процент жующих
CudChewingMasterViewModel.CudChewing=Жвачка
CudChewingMasterViewModel.CudChewingInputs=Вводные данные
CudChewingMasterViewModel.CudChewingResults=Результаты
CudChewingPen=Секция
CudChewingPercentChewing=% Жующих
CudChewingPercentGoal={0}% Цель
CudChewingPercentOfPen=Жуют (% секции)
CudChewingViewModel.CudChewing=Секции
CudChewingViewModel.CudChewingList=Секции (только лактирующие и сухостойные)
CudChewingViewModel.CudChewingTitle=Название секции
CudChewingViewModel.Title=Секции
CudChewsCalculatorViewModel.CalculatorHeading=Выберите животное для подсчета количества жевательных движений. Нажмите "Добавить" внизу, чтобы добавить коров в список для оценки
CudChewsCalculatorViewModel.CudChewCategorySection=Коров
CudChewsCalculatorViewModel.NumOfChews=Кол-во жев. движений
CudChewsDatesForComparisonViewModel.CudChewsPercent=Жуют, % /
Cundinamarca=Cundinamarca
Cuneo=Клин
Curaçao=Curaãao
Current=Текущее
CurrentDownResponse=Текущий эффект ({0}/гол./день)
CurrentMilkPrice=Цена молока ({0}/{1})
CurrentSCC=Текущая соматика (клеток/{0})
CurrentVisitSummary=Краткий отчет по текущему посещению
Customer=Клиент
CustomerDetailViewModel.CustomerTitle=Профиль клиента
CustomerDetailViewModel.MainHeading=Сайты
CustomerDetailViewModel.NewSite=Добавить новый сайт
CustomerDetailViewModel.NewVisit=Новое посещение
CustomerProspectsSegmentViewModel.Aiden=Эйден
CustomerProspectsSegmentViewModel.Baxter=Бакстер
CustomerProspectsSegmentViewModel.Dennis=Деннис
CustomerProspectsSegmentViewModel.EndUser=Конечный пользователь
CustomerProspectsSegmentViewModel.Kobe=Коуб
CustomerProspectsSegmentViewModel.Mila=Мила
CustomerProspectsSegmentViewModel.Noah=Ноа
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Выбрать сегмент
CustomerProspectsSegmentViewModel.Sonya=Соня
CustomerProspectsSegmentViewModel.Spence=Спенс
CustomerProspectsSegmentViewModel.Title=Детали
CustomerProspectsSegmentViewModel.Walton=Уолтон
CustomerWithSiteName=Название клиента - название сайта
Cyprus=Кипр
CzechRepublic=Чехия
Czech_Republic=Чешская Республика
DDW=Данные фермы
DDWOfflineMessage=Отсутствует подключение к сети. Вы хотите просмотреть отчет офлайн?
DDWUpdatedTime=Последнее обновление данных по стаду\: {0}
DKK=DKK
DZD=Алжир (DA DZD)
Dadra_and_Nagar_Haveli=Дадра и Нагар Хавели
Daily=Ежедневно
DairyEnteligenFarmReportsources=Информационные ресурсы Dairy Enteligen
Daman_and_Diu=Даман и Диу
DashboardViewModel.Alert=Предупреждение\!
DashboardViewModel.AlertMessage=Данные не были синхронизированы в течение {0} дней
DashboardViewModel.GoodAfternoon=Добрый день,
DashboardViewModel.GoodMorning=Доброе утро,
DashboardViewModel.MessageBody=Напоминание\: при посещении сайта используйте блокнот, чтобы зафиксировать дополнительные наблюдения и особенности 
DashboardViewModel.MessageHeader=Сообщение
DashboardViewModel.RecentSiteVisit=Недавние посещения сайтов
DashboardViewModel.UserPreferences=Предпочтения пользователя
Date=Дата
DateGone=Дата завершения
DatesForComparison=Даты для сравнения
Days=Дней
DaysInMilkItem=ДЛ
DeLaval=DeLaval
DeathLoss=Падеж
DecidingSilageStorage=Решение по типу хранения силоса
Delaware=Делавэр
Delete=Удалить
DeleteMatrixValue=Вы уверены, что хотите удалить значение этой матрицы?
Delhi=Дели
Denmark=Дания
DensityLossesinPressedBagSilos=Плотность и потери в прессованных силосных рукавах
Diet=Рацион
DietDCAD=DCAD рациона мЭкв/100г
DietDetailViewModel.Created=Созданный
DietDetailViewModel.DDW=Данные фермы
DietDetailViewModel.Max=MAX
DietDetailViewModel.SystemGenerated=Генерирование системы
DietDetailViewModel.Title=Детали рациона
DietDetailViewModel.UserCreated=Пользователем
DietListViewModel.InfoNewDiet=Если MAX подключен к сайту фермы в Dairy Enteligen, названия рационов будут обновлены автоматически. В противном случае, добавьте рацион вручную или оставьте поле пустым и выберите для этой Секции класс/подкласс животных.
DietListViewModel.MainHeading=Рационы
DietListViewModel.New=Новый
DietListViewModel.NewDiet=Добавить новый рацион
DietListViewModel.Title=Рационы
Dirty=Dirty
DisplacedAbomasum=Смещение сычуга
District_of_Columbia=округ Колумбия
Distrito_Federal=Федеральный округ
Djibouti=Джибути
DoNotTest=&lt;20% или не проверено 
Dominica=Доминика
Dominican_Republic=Доминиканская Республика
Donegal=Донегол
DontKnow=Не известно 
DownResponse=Эффект ({0}/гол/день)
Dry=Сухостой
DryCow=Сухостойные коровы
DryLot=Открытая площадка
Dryhay=Сухое сено
Dublin=Дублин
DuplicatePenName=Секция с таким названием уже существует, выберите другое название
Durango=Дуранго
Dystocia=Трудный отел
EGP=EGP
EarlyLactation=Начало лактации
Ecuador=Эквадор
Edit=Редактировать
EditDatesForComparison=Радактировать даты для сравнения
EditDatesForComparisonViewModel.Chews=Жев. движений
EditDatesForComparisonViewModel.EditDatesClose=Закрыть
EditDatesForComparisonViewModel.EditDatesLabel=Выберите даты для сравнения по этой секции из списка ниже
EditDatesForComparisonViewModel.EditDatesTitle=Радактировать даты для сравнения
EditDatesForComparisonViewModel.EditDatesVisits=Посещения
EditDatesForComparisonViewModel.LocomotionScoreAverage=Ср.балл оценки походки\:
EditDatesForComparisonViewModel.ManureScoreAverage=Ср.балл оценки навоза\:
EditDatesForComparisonViewModel.MetabolicIncidence=Выберите из списка ниже до 5 дат посещений для сравнения
EditDatesForComparisonViewModel.PenTimeBudget=Выберите из списка ниже до 7 дат посещений для сравнения
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Выберите дату предыдущего посещения для сравнения с текущим визитом
EditDatesForComparisonViewModel.TimeRemainingForResting=Остаток врем. на отдых, ч\:
EditDatesForComparisonViewModel.Title=Редактировать данные для сравнения
EditDatesForComparisonViewModel.Visits=Посещения
EditGoalsCudChewingViewModel.Close=Закрыть
EditGoalsCudChewingViewModel.CloseUpDry=Поздний сухостой
EditGoalsCudChewingViewModel.CudChews=Среднее кол-во жев.движ. /болюс
EditGoalsCudChewingViewModel.EarlyLactation=Начало лактации 
EditGoalsCudChewingViewModel.EditGoalsTitle=Редактировать цели по жвачке
EditGoalsCudChewingViewModel.FarOffDry=Ранний сухостой
EditGoalsCudChewingViewModel.Fresh=Новотел
EditGoalsCudChewingViewModel.LateLactation=Конец лактации
EditGoalsCudChewingViewModel.MidLactation=Середина лактации
EditGoalsCudChewingViewModel.PeakMilk=Пик лактации
EditGoalsCudChewingViewModel.PercentChewing=% жующих
EditNoteViewModel.Action=Действие
EditNoteViewModel.Cancel=Отменить
EditNoteViewModel.Category=Категория
EditNoteViewModel.Close=Закрыть
EditNoteViewModel.CreatedByMetadata=Скорректировано {0} @ {1} {2}
EditNoteViewModel.Delete=Удалить
EditNoteViewModel.DeleteImageButtonText=Удалить фотографию
EditNoteViewModel.DeleteImagePrompt=Вы хотите удалить это изображение?
EditNoteViewModel.DeletePrompt=Вы хотите удалить эту заметку?
EditNoteViewModel.DeleteVideoButtonText=Удалить видео
EditNoteViewModel.DeleteVideoPrompt=Вы хотите удалить это видео?
EditNoteViewModel.Event=Событие
EditNoteViewModel.LastUpdatedByMetadata=Последние изменения {0} @ {1}  {2}
EditNoteViewModel.NoteCamcorderNotImplemented=Функции камеры не доступны
EditNoteViewModel.NoteGalleryNotImplemented=Функции галереи не доступны
EditNoteViewModel.NoteLabel=Заметка
EditNoteViewModel.NoteOnlyOneImage=Не более одной фотографии для заметки. Пожалуйста, сначала удалите текущую фотографию
EditNoteViewModel.NoteOnlyOneVideo=Не более одного видео для заметки. Пожалуйста, сначала удалите текущее видео
EditNoteViewModel.Observation=Наблюдение
EditNoteViewModel.Save=Сохранить
EditNoteViewModel.Task=Задача
EditNoteViewModel.Title=Заметка
EditNoteViewModel.TitleLabel=Заголовок
Egypt=Египет
El_Salvador=Спаситель
EmailReportViewModel.AnimalImpact=Влияние на животных
EmailReportViewModel.CalfHeiferItem=Телята &amp; Телки
EmailReportViewModel.CalfHeiferScorecard=Оценка
EmailReportViewModel.Capacity=Вместительность
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Список категорий
EmailReportViewModel.Charts=Графики
EmailReportViewModel.CoefficientVariation=Коэф. вариации (КВ,%)
EmailReportViewModel.ComfortHeatStressBanner=Оценка теплового стресса
EmailReportViewModel.ComfortItem=Оценка комфорта
EmailReportViewModel.ComfortPenTimeBanner=Бюджет времени секции
EmailReportViewModel.ComfortToolsTitle=Оценка комфорта
EmailReportViewModel.CowsOutsideTargetRange=Коровы вне диапазона (%)
EmailReportViewModel.CudChewingTitle=Жвачка
EmailReportViewModel.DietDCADStr=DCAD рациона
EmailReportViewModel.EmailBody={0}-{1} Отчет
EmailReportViewModel.EmailSelectedTools=Отправить выбранные инструменты по e-mail 
EmailReportViewModel.EmailSubject={0} Отчет
EmailReportViewModel.ExportSelected=Отправить выбранные инструменты по e-mail 
EmailReportViewModel.FeedOut=Потребление
EmailReportViewModel.ForageAuditScorecard=Аудит структурных кормов
EmailReportViewModel.ForageImprovements=Аудит структурных кормов
EmailReportViewModel.ForageLanding=Аудит структурных кормов
EmailReportViewModel.ForageScorecard=Аудит структурных кормов
EmailReportViewModel.GeneratingReport=Формирование отчета…
EmailReportViewModel.GotoMarketBranding=Бренд
EmailReportViewModel.HealthItem=Оценка здоровья
EmailReportViewModel.HeatstressEvaluationTitle=Оценка теплового стресса
EmailReportViewModel.Herd=Стадо
EmailReportViewModel.HerdAnalysis=Анализ стада
EmailReportViewModel.HerdGoals=Анализ стада - цели
EmailReportViewModel.HerdInputs=Анализ стада - вводные данные
EmailReportViewModel.HerdResults=Анализ стада - результаты
EmailReportViewModel.HerdRevenue=Анализ стада - доход
EmailReportViewModel.Improvements=Улучшения
EmailReportViewModel.Inputs=Вводные данные
EmailReportViewModel.InputsOutputsChart=Вводные данные/выходные данные/графики
EmailReportViewModel.LocomotionScoreTitle=Оценка походки
EmailReportViewModel.ManureScoreTitle=Оценка навоза
EmailReportViewModel.MarketBranding=Бренд
EmailReportViewModel.MetabolicIncidenceTitle=Нарушения обмена веществ
EmailReportViewModel.MilkProcessCalcInputsTab=Сравнение процедуры доения – вводные данные
EmailReportViewModel.MilkProcessCalcResourcesTab=Сравнение процедуры доения – источники
EmailReportViewModel.MilkProcessCalcResultsTab=Сравнение процедуры доения – результаты
EmailReportViewModel.MilkProcessRevenue=Сравнение процедуры доения
EmailReportViewModel.MilkProcessRevenueCalculator=Сравнение процедуры доения
EmailReportViewModel.MilkingTime=Время доения
EmailReportViewModel.Notes=Заметки
EmailReportViewModel.NumOfChews=Кол-во жев. движений
EmailReportViewModel.NutritionForage=Аудит структурных кормов
EmailReportViewModel.NutritionItem=Кормление
EmailReportViewModel.NutritionPile=Инвентаризация кормов
EmailReportViewModel.Outputs=Выходные данные
EmailReportViewModel.PenCompare=Анализ секции - сравнение
EmailReportViewModel.PenDensity=Плотность секции
EmailReportViewModel.PenInputs=Анализ секции - ввод данных
EmailReportViewModel.PenResults=Анализ секции - результаты
EmailReportViewModel.PenTimeTitle=Бюджет времени секции
EmailReportViewModel.PileAndBunkerTitle=Инвентаризация кормов
EmailReportViewModel.ProductivityItem=Продуктивность
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Ресурсы
EmailReportViewModel.Results=Результаты
EmailReportViewModel.RumenHealthBodyConditionTitle=Упитанность 
EmailReportViewModel.RumenHealthLocomotionTitle=Оценка походки
EmailReportViewModel.RumenHealthManureTitle=Здоровье рубца. Оценка навоза
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Нарушения обмена веществ
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
EmailReportViewModel.RumenHealthTMRTitle=Здоровье рубца. Оценка ПСР
EmailReportViewModel.RumenHealthTitle=Здоровье рубца. Жвачка
EmailReportViewModel.RumenHealthUrinePHTitle=pH Мочи
EmailReportViewModel.ScoreScreen=Балл
EmailReportViewModel.TMRParticleScoreTitle=Здоровье рубца - оценка размера частиц
EmailReportViewModel.TimeBudget=Бюджет времени
EmailReportViewModel.Title=E-mail отчет
EmailReportViewModel.UrinePhSTDDEV=Стандартное отклонение
EmailReportViewModel.UserPreferences=Настройки пользователя
EmailReportViewModel.UserSettings=Настройки пользователя
EmailReportViewModel.WalkthroughReportTitle=Отчет обхода
EnergyImperial=Мкал/фунт
EnergyMetric=Мкал/кг
Enna=Энна
Equatorial_Guinea=Экваториальная Гвинея
Eritrea=Эритрея
ErrorDescription=При чтении или вводе информации возникла ошибка
ErrorTitle=Ошибка
Espírito_Santo=Эспириту-Санту
Estonia=Эстония
Ethiopia=Эфиопия
Eula=ЛСКП
Euro=Страны Евросоюза (€ EUR)
Event=Событие
EveryOtherDay=Через день
Excessive=&gt;0.5 балла 
ExtraDaysOpenCostInfoMessage=Обычно варьируется от 3 до 5$ за дополнительный день сервис-периода
FAQDairyEnteligenFarmReportandDDW=ЧаВо Dairy Enteligen отчет по ферме
Falkland_Islands_(Malvinas)=Фолклендские острова (Мальвинс)
FarOff=Ранний сухостой
FarOffDry=Ранний сухостой
Faroe_Islands=Фарерские острова
Federal_District=Федеральный округ
FeedFirst=Сначала кормление
FeedOut=Потребление
FeedOutRateInfo=Уровень потребления
FeedOutRatesFilmsStorageSysExamined=Уровень потребления, состояние пленки и система хранения оцениваются регулярно
FeedOutSurfaceAreaImperial=Площадь поверхности среза кормов  (фут²)
FeedOutSurfaceAreaMetric=Площадь поверхности среза кормов  (м²)
FeedingRate=Кол-во кормов (НВ кг/гол)
FeedoutLossesForageStorageSys=Потери кормов при хранении
FermentationAnalysisSilageQT=Анализ ферментации и проверка качества силоса
Fermo=Остановился
Ferrara=Феррара
FieldKPTest=Полевой тест на предмет степени обработки зерен
Fiji=Фиджи
FillAllFields=Пожалуйста, заполните все обязательные поля
FillAllMandatoryFields=Пожалуйста, заполните все обязательные поля
FinalObservations=Финальные наблюдения
Finish=Завершить
Finland=Финляндия
Florence=Флоренция
Florida=Флорида
Foggia=Фоггия
ForageAuditScorecard=Аудит структурных кормов
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Аудит структурных кормов - улучшения
ForageAuditScorecardResponsesViewModel.ResponsesTab=Выполнение рекомендаций по итогам аудита кормов
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Улучшения
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Ответы
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Оценка
ForageAuditScorecardResultsViewModel.ImprovementsTab=Улучшения
ForageAuditScorecardResultsViewModel.ResponsesTab=Ответы
ForageAuditScorecardResultsViewModel.ScoreTab=Оценка
ForageAuditScorecardResultsViewModel.Title=Силосная башня
ForageAuditScorecardResultsViewModel.VisitNotebook=Блокнот посещения
ForageAuditScorecardScoreViewModel.GoodIndicator=Хорошо
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Улучшения
ForageAuditScorecardScoreViewModel.OverallForageScore=Включить в общую оценку структурных кормов
ForageAuditScorecardScoreViewModel.Title=Аудит структурных кормов - общая оценка
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Типы структурных кормов
ForageAuditViewModel.ForageAuditScorecard=Аудит структурных кормов
ForageAuditViewModel.ForageDetail=Качество структурных кормов - это основа любой программы кормления молочного скота и ключ к прибыльности молочного бизнеса. Список показателей аудита структурных кормов может быть использован для оценки текущей организации заготовки и использования кормов, а также выявления возможностей для улучшения. Список показателей распределен по ключевым областям. Во время посещения вы можете уделить внимание всем областям или только тем, которые являются наиболее актуальными на момент визита. Доступны дополнительные организационные ресурсы, направленные на улучшение ситуации в этих областях
ForageAuditViewModel.ForageHeading=Базовая информация по структурным кормам
ForageAuditViewModel.Resources=Ресурсы
ForageAuditViewModel.Title=Аудит структурных кормов
ForageAuditViewModel.VisitNotebook=Блокнот посещения
ForageAudit_Sample_ToolTip=Образец текста для всплывающих подсказок. Замените, если доступен другой тип
ForageManagement_ForagesHarvestedAtProperMaturity=Структурные корма заготовлены при соответствующей зрелости для данного типа культур и хранилища? 
ForageManagement_ForagesHarvestedAtProperMoisture=Структурные корма заготовлены при соответствующей влажности для данного типа культур и хранилища
ForageScorecardResultsViewModel.Title=Тюки
ForageScorecardViewModel.Baleage=Тюки
ForageScorecardViewModel.BunkersAndPiles=Курганы и траншеи
ForageScorecardViewModel.ForageAuditCategories=Категории аудита структурных кормов
ForageScorecardViewModel.ForageAuditScore=Аудит структурных кормов
ForageScorecardViewModel.ForageAuditScorecard=Аудит структурных кормов
ForageScorecardViewModel.ForageCategoryTooltip=Качество структурных кормов представляет собой основу любой программы кормления молочного скота и является ключом к общей прибыльности предприятия. Данный инструмент может быть использован для оценки текущего менеджмента структурных кормов на предприятии и для рекомендации ключевых областей, в которых возможны улучшения 
ForageScorecardViewModel.Harvest=Качество ПСР
ForageScorecardViewModel.MaintainingForageQuality=Сохранение качества структурных кормов
ForageScorecardViewModel.No=Нет
ForageScorecardViewModel.SilageBags=Силосные рукава
ForageScorecardViewModel.SurveyCategories=Аудит структурных кормов
ForageScorecardViewModel.SurveyOfForages=Осмотр структурных кормов
ForageScorecardViewModel.Title=Аудит структурных кормов
ForageScorecardViewModel.TowerSilos=Силосная башня
ForageScorecardViewModel.ViewOverallForageScore=Посмотреть общую оценку структурных кормов
ForageScorecardViewModel.VisitNotebook=Блокнот посещения
ForageScorecardViewModel.Yes=Да
ForlÃ¬-Cesena=Форла-Чесена
FourScreenNew=4-секционный новый
FourScreenNewType=(4 мм)
FourScreenOld=4-секционный старый
FourScreenOldType=(1.18 мм)
FourToSevenDays=4 - 7 дней
France=Франция
FreeFlow=Свободное движение
FreeFormReportViewModel.CalfHeiferItem=Телята &amp; телки
FreeFormReportViewModel.CalfHeiferScorecard=Оценка
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Графики
FreeFormReportViewModel.ComfortHeatStressBanner=Тепловой стресс секции
FreeFormReportViewModel.ComfortItem=Оценка комфорта
FreeFormReportViewModel.ExportSelected=Экспорт выбранных инструментов
FreeFormReportViewModel.GeneralNotes=Общие заметки посещения
FreeFormReportViewModel.HealthItem=Оценка здоровья
FreeFormReportViewModel.Inputs=Вводные данные
FreeFormReportViewModel.KeyBenchmarks=Ключевые показатели
FreeFormReportViewModel.MarketingBranding=Бренд
FreeFormReportViewModel.MilkProcessCalcInputsTab=Сравнение процедуры доения – вводные данные
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Сравнение процедуры доения – ресурсы
FreeFormReportViewModel.MilkProcessCalcResultsTab=Сравнение процедуры доения – результаты
FreeFormReportViewModel.MilkProcessRevenueCalculator=Сравнение процедуры доения
FreeFormReportViewModel.MilkSoldEvaluation=Оценка проданного молока
FreeFormReportViewModel.Notes=Заметки
FreeFormReportViewModel.NutritionForage=Аудит структурных кормов
FreeFormReportViewModel.NutritionItem=Оценка кормления
FreeFormReportViewModel.NutritionPile=Инвентаризация структурных кормов
FreeFormReportViewModel.Outputs=Выходные данные
FreeFormReportViewModel.OverallImprovements=Общие улучшения
FreeFormReportViewModel.OverallResponses=Общие ответы
FreeFormReportViewModel.OverallScore=Общая оценка
FreeFormReportViewModel.PenTimeTitle=Бюджет времени секции
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Инвентаризация кормов - скрамливание
FreeFormReportViewModel.ProductivityItem=Оценка продуктивности
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Результаты
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Оценка упитанности
FreeFormReportViewModel.RumenHealthLocomotionTitle=Оценка походки
FreeFormReportViewModel.RumenHealthManureTitle=Здоровье рубца - оценка навоза
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Нарушения обмена веществ
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Здоровье рубца - оценка ПСР (стадо)
FreeFormReportViewModel.RumenHealthTMRTitle=Здоровье рубца - оценка ПСР
FreeFormReportViewModel.RumenHealthTitle=Здоровье рубца - жвачка
FreeFormReportViewModel.RumenHealthUrinePHTitle=pH мочи
FreeFormReportViewModel.Title=Отчет в свободной форме
FreeFormReportViewModel.VisitTitle=Название посещения
FreeFormReportViewModel.WalkThroughNotes=Заметки при посещении
FreeHandNoteClearPaletteDialogMessage=Вы хотите удалить все на экране?
FreeHandNoteEditorPageTitle=Заметка "хэнд фри"
FreeHandNoteSaveUserDialogMessage=Вы хотите сохранить эту заметку?
FreeHandNotesViewModel.Save=Сохранить
Freestall=Freestall
French_Guiana=Французская Гвиана
French_Polynesia=Французская Полинезия
French_Southern_Territories=Южные Французские Территории
Fresh=Новотельная
FreshCow=Новотельные коровы
FreshHeifer=Первотелка
Frosinone=Фрозинон
Fujian=Fujian
GBP=Соединенное Королевство (GBP GBP)
GEA=GEA
GTQ=Гватемала (Q GTQ)
Gabon=Габон
Galway=Голуэй
Gambia=Гамбия
Gansu=Гансу
General=Общее
Genoa=Генуя
Georgia=Грузия
Germany=Германия
GettingtheMostOutofYourForage=Получая все из вашего силоса
Ghana=Гана
Gibraltar=Гибралтар
Girolando=Girolando
Global=Глобальный
Goa=Гоа
Goal=Цель
Goiás=Гояо
Good=Good
Gorizia=Гориция
GreaterThan8Hours=Более 8 ч
GreaterThanFive=Более 5
GreaterThanSevenDays=Более 7 дней
GreaterThanSixHours=Более 6 ч
GreaterThanThirtySixInchesPerDay=Более 90 см в день
GreaterThanTwelveHours=Более 12 ч
GreaterThanTwenty=&gt;20
Greece=Греция
Greenland=Гренландия
Grenada=Гренада
Grosseto=Гроссетто
GrowerPubertyPregnancyCloseup=Рост и развитие - предотел
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Чистое и сухое помещение
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Используйте тест "мокрое колено" для оценки подстилки
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=Целевая кондиция упитанности достигнута
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Диарея
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=Чистка кормушек и удаление остатков проводится ежедневно
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Свободный доступ к чистой воде
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=Нет видимых загрязений воды
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Животные в группе однородны
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Группа животных должна быть максимально однородной 
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% переполнения группы
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Рационы сбалансированы для достижения целевых показателей, регулярно пересматриваются
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Оптимальный фронт кормления на голову
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=ЖМ 135-270 кг - 30 см, ЖМ 270-400 кг - 38 см, ЖМ &gt;400 кг - 46 см
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Оптимальная площадь на голову
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=ЖМ 135-270 кг - 4м², ЖМ 270-400 кг - 5м², ЖМ &gt;400 кг - 7м²
Guadeloupe=Гваделупа
Guam=Гуам
Guanajuato=Гуанахуато
Guangdong=Гуандун
Guangxi=Гуанси
Guatemala=Гватемала
Guernsey=Гернси
Guerrero=Герреро
Guinea=Гвинея
Guinea-Bissau=Гвинея-Бисау
Guizhou=Гуйчжоу
Gujarat=Гуджарат
Guyana=Гайана
HKD=HKD
HNL=Гондурас (HNL HNL)
HRK=HRK
HUF=Венгрия (Ft HUF)
Hainan=Хейнан
Haiti=Гаити
HalfPointScale=Полубалльная шкала оценки
Harvest=Качество ПСР
Harvest_AdequateEquipmentAndLabor=Время от начала до заверешения заготовки (при соответствующем оборудовании и ресурсах для уборки сенажа)
Harvest_CornSilageMoistureRangeConsistent=Среднее содержание влаги в силосе? (Должно быть постоянным в 90% образцов)
Harvest_ForageHarvestingDocumented=Задокументированы ли условия уборки, поля и места закладки?
Harvest_ForagesHarvestedAtProper=Корма убраны при оптимальных для данной культуры влажности и зрелости с учетом типа сооружения для хранения?
Harvest_KPScoreIsMonitored=Сколько цельных зерен представлено в образце объемом 1л (32 унции)?
Harvest_LengthOfCutMonitored=Оценена ли длина резки при заготовке с помощью пенсильванских сит?
Harvest_UseSilageAdditive=Используются ли добавки, инокулянты или анаэробные стабилизаторы?
Harvest_WholePlantMoistureDetermined=Определена ли влажность травостоя на каждом поле?
Haryana=Харьяна
Hawaii=Гавайи
Haylage=Сенаж
HealthToolsViewModel.HealthHeading=Выберите инструмент из списка ниже
HealthToolsViewModel.HealthToolsList=Инструменты
HealthToolsViewModel.RumenHealthBodyConditionTitle=Упитанность
HealthToolsViewModel.RumenHealthLocomotionTitle=Оценка походки
HealthToolsViewModel.RumenHealthManureScreening=Здоовье рубца Оценка навоза
HealthToolsViewModel.RumenHealthManureTitle=Здоровье рубца - оценка навоза
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Нарушения обмена веществ
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
HealthToolsViewModel.RumenHealthTMRTitle=Здоровье рубца - оценка ПСР
HealthToolsViewModel.RumenHealthTitle=Здоровье рубца - жвачка
HealthToolsViewModel.RumenHealthUrinePHTitle=pH мочи
HealthToolsViewModel.Title=Здоровье
Heard_Island_and_McDonald_Islands=Слышал острова и острова Макдональд
HeatstressCalculations=Расчет теплового стресса
HeatstressChart=График теплового стресса
HeatstressChartViewModel.DMIReduction=Снижение ПСВ
HeatstressChartViewModel.EnergyEquivMilkLoss=Энергетический эквивалент потери молока
HeatstressChartViewModel.EstimateDryMatter=Расчетное ПСВ
HeatstressChartViewModel.HeatstressEvalLabel=Температура с поправкой на среднюю температуру и относительную влажность (не солнечно)
HeatstressChartViewModel.IntakeAdjustment=Корректировка потребления
HeatstressChartViewModel.Kilograms=кг
HeatstressChartViewModel.LossEnergyConsumed=Потеря потребленной энергии
HeatstressChartViewModel.Mcal=Мкал
HeatstressChartViewModel.MilkValueLossPerDay=Стоимость потери молока/день
HeatstressChartViewModel.MilkValueLossPerMonth=Стоимость потери молока/месяц
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Фунт
HeatstressChartViewModel.ReductionDMI=Снижение ПСВ
HeatstressChartViewModel.TempHumidIndex=Температурно-влажностный индекс
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Блокнот посещения
HeatstressData=Данные по тепловому стрессу
HeatstressDataEntryViewModel.AnimalInputs=Ввод данных по животным
HeatstressDataEntryViewModel.CurrentMilkPrice=Цена молока ({0}/{1})
HeatstressDataEntryViewModel.DMI=ПСВ
HeatstressDataEntryViewModel.Exposure=Воздействие
HeatstressDataEntryViewModel.HoursExposed=Солнечный свет, ч
HeatstressDataEntryViewModel.Humidity=Влажность (%)
HeatstressDataEntryViewModel.LactatingAnimals=Лактирующие коровы
HeatstressDataEntryViewModel.Milk=Молоко ({0})
HeatstressDataEntryViewModel.MilkFat=Жир молока (%)
HeatstressDataEntryViewModel.MilkProtein=Белок молока (%)
HeatstressDataEntryViewModel.NEL=NEL ({0})
HeatstressDataEntryViewModel.Temperature=Температура ({0})
HeatstressDataEntryViewModel.VisitNotebook=Блокнот посещения
HeatstressDataEntryViewModel.Weather=Погода
HeatstressGreen=Порог стресса
HeatstressOrange=Умеренно-сильный стресс
HeatstressRed=Сильный стресс
HeatstressTableViewModel.HeatstressChartTab=Графики
HeatstressTableViewModel.HeatstressDataTab=Ввод данных
HeatstressTableViewModel.Title=Оценка теплового стресса
HeatstressTableViewModel.VisitNotebook=Блокнот посещения
HeatstressYellow=Легкий-умеренный стресс
Hebei=Хэбэй
Heifer=Телки/нетели
Heilongjiang=Хейлонгцзян
Henan=Хенань
HerdAnalysisGoalsViewModel.CloseUpDry=Поздний сухостой
HerdAnalysisGoalsViewModel.CudChewingGoals=Цели по жеванию жвачки
HerdAnalysisGoalsViewModel.CudChews=Жев.дв.
HerdAnalysisGoalsViewModel.DIM=ДЛ 
HerdAnalysisGoalsViewModel.EarlyLactation=Начало лактации
HerdAnalysisGoalsViewModel.FarOffDry=Ранний сухостой
HerdAnalysisGoalsViewModel.Fresh=Новотел
HerdAnalysisGoalsViewModel.LateLactation=Конец лактации
HerdAnalysisGoalsViewModel.MidLactation=Середина лактации
HerdAnalysisGoalsViewModel.PeakMilk=Пик лактации
HerdAnalysisGoalsViewModel.PercentChewing=% жующих
HerdAnalysisGoalsViewModel.Title=Анализ стада
HerdAnalysisGoalsViewModel.To=к
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Здоровье рубца - жвачка
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Анализ стада
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Анализ стада
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Цели
HerdAnalysisMasterViewModel.Title=Здоровье рубца - жвачка
HerdAnalysisTableTitle=Анализ жвачки
HerdAnalysisViewModel.AverageChews=Ср. кол-во жев. дв. /жвачку
HerdAnalysisViewModel.DaysInMilk=ДЛ 
HerdAnalysisViewModel.Edit=Редактировать
HerdAnalysisViewModel.EditLabel=Редактировать
HerdAnalysisViewModel.HerdAnalysisTableTitle=Анализ жвачки
HerdAnalysisViewModel.HerdCudChewing=Жуют в стаде, %
HerdAnalysisViewModel.NoOfCows=Завершите анализ всех секций, которые вы начали
HerdAnalysisViewModel.NumberofChewsPerCud=Жев. дв. /жвачку
HerdAnalysisViewModel.PenNameLabel=Название секции
HerdAnalysisViewModel.PercentChewing=Жуют, %
HerdAnalysisViewModel.TableTitle=Анализ жвачки
HerdAverage=Среднее по стаду (%)
HerdGoal=Цель по стаду (%)
HerdInformation=Информация по стаду
HerdReporting=Отчет по стаду - жвачка
Hidalgo=Идальго
Himachal_Pradesh=Химачал -Прадеш
Hokkaido=Хоккайдо
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=Святое Престол (штат Ватикан Сити)
HomeViewModel.AutoSync=Автосинхронизация
HomeViewModel.ConsumersTab=Потребители
HomeViewModel.CustomersTab=Клиенты
HomeViewModel.DashboardTab=Приборная панель
HomeViewModel.Eula=ЛСКП
HomeViewModel.Logout=Выйти
HomeViewModel.PrivacyStatement=Заявление о конфиденциальности
HomeViewModel.ProspectsTab=Потенциальные клиенты
HomeViewModel.Settings=Параметры
HomeViewModel.SyncWithDash=Синхронизация -
HomeViewModel.SyncWithDate=Синхронизация - дата последней синхронизации\: {0\:MM/dd/yy}
HomeViewModel.SyncWithTime=Синхронизация - дата последней синхронизации\: {0\:hh\:mm tt}
HomeViewModel.Title=Инструмент продажи
Honduras=Гондурас
Hong_Kong=Гонконг
HowtoGetBetterKPResults=Как получить лучшие результаты обработки зерна
Hubei=Хубей
Hunan=Себя
Hungary=Венгрия
IDR=Индонезия (Rp IDR)
INR=Индия (INR INR)
Iceland=Исландия
Idaho=Айдахо
Illinois=Иллинойс
Imperia=Империя
Imperial=Имперская
Improvements=Улучшения
India=Индия
Indiana=Индиана
Indonesia=Индонезия
InoculantFQAs=Инокулянт ЧАВО
Iowa=Айова
Iran,_Islamic_Republic_of=Иран, Исламская Республика
Iraq=Ирак
Ireland=Ирландия
Isernia=Исиния
Isle_of_Man=Остров Мэн
Israel=Израиль
Italy=Италия
JOD=JOD
JPY=JPY
Jalisco=Джалиско
Jamaica=Ямайка
Jammu_and_Kashmir=Джамму и Кашмир
Japan=Япония
Jersey=Jersey
Jharkhand=Джаркханд
Jiangsu=Цзянсу
Jiangxi=Цзянси
Jilin=Джилин
Jordan=Иордания
KBLastPhase=Вести точный учет показателей роста и здоровья
KRW=Корея (Южная) (₩ KRW)
Kansas=Канзас
Karnataka=Карнатака
Kazakhstan=Казахстан
Kentucky=Кентукки
Kenya=Кения
Kerala=Керала
Kerry=Керри
Ketosis=Кетоз
KeyBenchmarks=Ключевые показатели
KeyBenchmarks_AgeInMonthAtFirstCalving=Возраст первого отела, мес.
KeyBenchmarks_CalvingAndHeiferReocrd=Регистрируется ли информация об отелах?
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=15% от массы взрослой коровы в возрасте 90 дней
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=55% от массы взрослой коровы при осеменении
KeyBenchmarks_HeiferPeakProduce=Удой первотелок на пике лактации, % от среднего по стаду
KeyBenchmarks_NintyDaysMorbidityf=Заболеваемость до 90 дней
KeyBenchmarks_NintyDaysMortality=Смертность до 90 дней
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=94% от массы взрослой коровы в предотельный период
KeyBenchmarks_PercentOfHeifersPregnant=% стельных животных в 15 месяцев
KeyBenchmarks_SerumlgG=Количество IgG в сыворотке крови (г/л) через 48 ч
Kildare=Kildare
Kilkenny=Килкенни
Kiribati=Кирибати
Korea=Корея
Korea,_Democratic_People's_Republic_of=Корея, Демократическая народная Республика
Korea,_Republic_of=Корея, Республика
Kuwait=Кувейт
Kyrgyzstan=Кыргизстан
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=Специя
Lactating=Лактирующие
Lactation=Лактация
Lakshadweep=Лакшадвип
Lao_People's_Democratic_Republic=Лаосская Народно-Демократическая Республика
Laois=Лаоа
Last_Synced=Последняя синхронизация
LateLactation=Конец лактации
Latina=Латина
Latvia=Латвия
Lebanon=Ливан
Lecce=Lecce
Lecco=Lecco
Leitrim=Лейтрим
Lely=Lely
Length-exceed-allowed-limit=Длинна превысила допустимый предел
LengthPerDayImperial=Дюйм/день
LengthPerDayMetric=См/день
Lesotho=Лесото
LessThan4Days=Менее 4 дней
LessThanFifteen=&lt;15 (твердость зерна)
LessThanFiveWholeKernals=Менее 5 цельных зерен
LessThanOneHour=Менее 1 часа
LessThanSixInches=Менее 15 см
LessThanSixLayers=Менее 6 слоев
LessThanTwentFourInchesPerDay=Менее 60 см в день
Liaoning=Liaoning
Liberia=Либерия
Libyan_Arab_Jamahiriya=Ливийская арабская джамахирия
Liechtenstein=Лихтенштейн
Lift-Sync-Fail=Синхронизация с LIFT прервалась
Limerick=Лимерик
LinkToPens=Ссылка на секции (опция)
Lithuania=Литва
Livorno=Ливорно
LocoCategory1=1 балл
LocoCategory2=2 балла
LocoCategory3=3 балла
LocoCategory4=4 балла
LocoCategory5=5 балл.
LocomotionEditTableViewModel.Category1=Оценка походки - 1 балл
LocomotionEditTableViewModel.Category2=Оценка походки - 2 балла
LocomotionEditTableViewModel.Category3=Оценка походки - 3 балла
LocomotionEditTableViewModel.Category4=Оценка походки - 4 балла
LocomotionEditTableViewModel.Category5=Оценка походки - 5 баллов
LocomotionEditTableViewModel.EnterNumberOfCows=Посчитайте количество коров
LocomotionEditTableViewModel.Title=Количество коров
LocomotionHerdEditGoalViewModel.Category1=1 балл
LocomotionHerdEditGoalViewModel.Category2=2 балла
LocomotionHerdEditGoalViewModel.Category3=3 балла
LocomotionHerdEditGoalViewModel.Category4=4 балла
LocomotionHerdEditGoalViewModel.Category5=5 баллов
LocomotionHerdEditGoalViewModel.HerdGoal=Цель по стаду
LocomotionHerdEditGoalViewModel.Title=Редактировать цель
LocomotionHerdInputsViewModel.Herd=Стадо
LocomotionHerdMasterViewModel.Inputs=Вводные данные
LocomotionHerdMasterViewModel.Results=Результаты
LocomotionHerdMasterViewModel.Revenue=Доход
LocomotionHerdMasterViewModel.SubHeading=Анализ стада
LocomotionHerdMasterViewModel.Title=Походка
LocomotionHerdResultsViewModel.Category1=1 балл
LocomotionHerdResultsViewModel.Category2=2 балла
LocomotionHerdResultsViewModel.Category3=3 балла
LocomotionHerdResultsViewModel.Category4=4 балла
LocomotionHerdResultsViewModel.Category5=5 баллов
LocomotionHerdResultsViewModel.HerdAverage=Среднее по стаду
LocomotionHerdResultsViewModel.HerdGoal=Цель
LocomotionHerdResultsViewModel.Title=Оценка походки - анализ
LocomotionHerdRevenueViewModel.Revenue=Доход
LocomotionHerdRevenueViewModel.Title=Походка - доход по стаду
LocomotionNumberinHerd=Походка (кол-во в стаде)
LocomotionNumberinPen=Походка (кол-во в секции)
LocomotionPenInputsViewModel.FromPenSetup=Из настроек секции
LocomotionPenInputsViewModel.Milk=Молоко
LocomotionPenMasterViewModel.Inputs=Вводные данные
LocomotionPenMasterViewModel.Results=Результаты
LocomotionPenMasterViewModel.Title=Походка
LocomotionPercentofHerd=Походка (% в стаде)
LocomotionPercentofPen=Походка (% в секции)
LocomotionPreviousVisitsViewModel.AverageScore=Ср.балл
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Ср.балл
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Дата
LocomotionPreviousVisitsViewModel.PercentPen=Процент от секции(%)
LocomotionPreviousVisitsViewModel.SelectedDates=Выбрать даты
LocomotionPreviousVisitsViewModel.Title=Результаты оценки походки
LocomotionScore=Оценка походки
LocomotionScoreAverage=Ср. балл оценки походки
LocomotionScoreHerd=Анализ походки
LocomotionScoreReference=Справочник по оценке походки
LocomotionSelectPenViewModel.MissingDiet=Введите правильный рацион для этой секции
LocomotionSelectPenViewModel.PenSelectionList=Секции
LocomotionSelectPenViewModel.Title=Походка
Lodi=Наоборот
LoginViewModel.Copyright=© {0} Cargill, Инкорпорейтед. Все права защищены.
LoginViewModel.EmailLabel=E-mail
LoginViewModel.ErrorDescription=Необходимо ввести имя пользователя и пароль
LoginViewModel.ErrorTitle=Ошибка авторизации
LoginViewModel.InvalidMessage=Неправильное имя пользователя или пароль
LoginViewModel.InvalidMessageTitle=Неверный логин
LoginViewModel.LoginPrompt=Логин Cargill
LoginViewModel.LoginPromptConsumer=Другой логин
LoginViewModel.NetworkErrorMessage=Нет доступных сетей
LoginViewModel.NetworkErrorMessageTitle=Ошибка сети
LoginViewModel.PasswordLabel=Пароль
LoginViewModel.Title=Логин
LoginViewModel.Unauthorized=Неавторизованный доступ
LoginViewModel.UnauthorizedTitle=Нет авторизации 
Longford=Лонгфорд
Louisiana=Луизиана
Louth=Лаут
LowForage=Низкий уровень структурных кормов
Lucca=Лучка
Luxembourg=Люксембург
MEQ100G=мЭкв/100г
MKD=MKD
MUN=Аммиак (мг/дл)
MXN=Мексика (PESO MXN)
MYR=Малайзия (MYR MYR)
Macao=Макао
Macedonia,_the_former_Yugoslav_Republic_of=Македония, бывшая Югославская Республика
Macerata=Макроуратор
Madagascar=Мадагаскар
Madhya_Pradesh=Мадхья -Прадеш
Maharashtra=Махараштра
MainViewModel.EmailLabel=E-mail
Maine=Штат Мэн
MaintainingForageQuality=Поддержание качества структурных кормов
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Бонус\: используется ли стабилизатор/ингибитор плесени в ПСР в жаркую/влажную погоду?
MaintainingForageQuality_TMRMixHasPleasantAroma=Смесь ПСР имеет приятный аромат
MaintainingForageQuality_TMRMixIsCoolToTouch=Смесь ПСР прохладная на ощупь
Making_Feed_InventoryFOF=Проведение инвентаризации кормов
Malawi=Пламя
Malaysia=Малайзия
Maldives=Мальдивы
Male=Бычок
Mali=Должен был
Malta=Мальта
ManagingForageinSiloBags=Организация закладки и хранения в рукавах
ManagingForageinTowerSilos=Организация закладки и хранения в силосных башнях
Manipur=Манипур
Manitoba=Манитоба
Mantua=Мантуа
ManureEditScores=Оценка навоза - редактировать
ManureScoreHerdAnalysisEditInputsViewModel.Close=Закрыть
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Дней лактации (ДЛ)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Редактировать кол-во ДЛ
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Оценка навоза
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Анализ оценки навоза
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=День лактации (ДЛ)
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Редактировать
ManureScoreHerdAnalysisMasterViewModel.Goals=Цели
ManureScoreHerdAnalysisMasterViewModel.Inputs=Вводные данные
ManureScoreHerdAnalysisMasterViewModel.Results=Результаты
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Анализ стада
ManureScoreHerdAnalysisMasterViewModel.Title=Здоровье рубца - оценка навоза
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Анализ оценки навоза
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Оценка навоза
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Среднее
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Макс. балл
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Мин. балл
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Поздний сухостой (менее -21 дня)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Начало лактации (16-60 дней)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Закрыть
ManureScoreHerdEditGoalsViewModel.FarOffDry=Ранний сухостой (-60-21 день)
ManureScoreHerdEditGoalsViewModel.Fresh=Новотел (0-15 дней)
ManureScoreHerdEditGoalsViewModel.LateLactation=Конец лактации  (более 200 дней )
ManureScoreHerdEditGoalsViewModel.MaxGoal=Навоз макс.
ManureScoreHerdEditGoalsViewModel.MidLactation=Середина лактации (121-200 дней)
ManureScoreHerdEditGoalsViewModel.MinGoal=Навоз мин.
ManureScoreHerdEditGoalsViewModel.PeakMilk=Пик лактации (от 61 до 120 дней)
ManureScoreHerdEditGoalsViewModel.Title=Редактировать цели
ManureScoreHerdGoalsViewModel.CloseUpDry=Поздний сухостой (менее -21 дня)
ManureScoreHerdGoalsViewModel.EarlyLactation=Начало лактации (16-60 дней)
ManureScoreHerdGoalsViewModel.Edit=Редактировать
ManureScoreHerdGoalsViewModel.FarOffDry=Ранний сухостой (-60-21 день)
ManureScoreHerdGoalsViewModel.Fresh=Новотельные (0-15 дней)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Макс. цель по навозу
ManureScoreHerdGoalsViewModel.GoalMinTitle=Мин. цель по навозу
ManureScoreHerdGoalsViewModel.LateLactation=Конец лактации  (более 200 дней )
ManureScoreHerdGoalsViewModel.MidLactation=Середина лактации (121-200 дней)
ManureScoreHerdGoalsViewModel.PeakMilk=Пик лактации (61 - 120 дней)
ManureScoreHerdGoalsViewModel.TableTitle=Оценка по стадии лактации
ManureScorePenSelectionViewModel.ManureScoreTitle=Здоровье рубца. Оценка навоза
ManureScorePenSelectionViewModel.PenSelectionList=Секции
ManureScorePercentOfPen=Оценка навоза (% в секции)
ManureScoresChart=Оценка навоза -график
ManureScoresResult=Оценка навоза - результат
Maranhão=Maranhã o
Martinique=Мартиника
Maryland=Мэриленд
Massa_and_Carrara=Масса и Каррара
Massachusetts=Массачусетс
Matera=Матера
Mato_Grosso=Мато Гроссо
Mato_Grosso_do_Sul=Mato Grosso do Sul
Mauritania=Мавритания
Mauritius=Маврикий
Max=MAX
Mayo=Майонез
Mayotte=Mayotte
Mcal=Мкал
Meath=Мит
Medio_Campidano=Medio Campidano
Medium=Medium
Meghalaya=Мегхалая
MenuViewModel.Close=Закрыть
MenuViewModel.LogoutPrompt=Выйти
MenuViewModel.Menu=Меню
MenuViewModel.ResetDatabaseCancel=Отменить
MenuViewModel.ResetDatabasePrompt=Cуществующие данные, включая настройки пользователя, будут замены на начальные данные по умолчанию. Инструменты посещения, созданные новые посещения, а также переработчики будут утеряны. Также вы выйдете из приложения. Продолжить?
MenuViewModel.ResetDatabaseReset=Сброс
MenuViewModel.ResetDatabaseTitle=Сбросить данные теста
MenuViewModel.Sync_PopUp=Синхронизация ваших данных… 
Messina=Мессина
MetabolicIncidenceChartsViewModel.Current=Текущий
MetabolicIncidenceChartsViewModel.DeathLoss=Падеж
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Нарушения обмена веществ, стоимость на гол.
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Смещение сычуга
MetabolicIncidenceChartsViewModel.Dystocia=Трудный отел
MetabolicIncidenceChartsViewModel.GoalPercent=Цель (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Нарушения обмена веществ, %
MetabolicIncidenceChartsViewModel.IncidencePercent=Случаи (%)
MetabolicIncidenceChartsViewModel.Ketosis=Кетоз
MetabolicIncidenceChartsViewModel.Metritis=Метрит
MetabolicIncidenceChartsViewModel.MilkFever=Родильный парез
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Задержание последа
MetabolicIncidenceChartsViewModel.Title=Нарушения обмена веществ - график
MetabolicIncidenceEditOutputsViewModel.Close=Закрыть
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Падеж
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Смещение сычуга
MetabolicIncidenceEditOutputsViewModel.Dystocia=Трудный отел
MetabolicIncidenceEditOutputsViewModel.Ketosis=Кетоз
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Цель (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Метрит
MetabolicIncidenceEditOutputsViewModel.MilkFever=Родильный парез
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Задержание последа
MetabolicIncidenceEditOutputsViewModel.Title=Редактировать цели
MetabolicIncidenceInputsEditViewModel.Close=Закрыть
MetabolicIncidenceInputsEditViewModel.DeathLoss=Потери
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Смещение сычуга
MetabolicIncidenceInputsEditViewModel.Dystocia=Трудный отел
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Рост сервис-периода
MetabolicIncidenceInputsEditViewModel.Ketosis=Кетоз
MetabolicIncidenceInputsEditViewModel.Metritis=Метрит
MetabolicIncidenceInputsEditViewModel.MilkCow=Молоко/гол({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Родильный парез
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Задержание последа
MetabolicIncidenceInputsEditViewModel.Title=Редактировать атрибуты стоимости
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Стоимость лечения
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Стоимость дней прохолоста
MetabolicIncidenceInputsViewModel.Costs=ЗАТРАТЫ
MetabolicIncidenceInputsViewModel.DeathLoss=Падеж
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Смещение сычуга
MetabolicIncidenceInputsViewModel.Dystocia=Трудный отел
MetabolicIncidenceInputsViewModel.Herd=Информация по стаду
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Введите количество новотельных коров и количество нарушений обмена веществ за рассматриваемый период. Эти данные будут пересчитаны в годовые потери от заболеваний и появятся во вкладке "результаты"
MetabolicIncidenceInputsViewModel.IncidenceCases=Случаи нарушений обмена веществ
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Увеличение сервис-периода, дни
MetabolicIncidenceInputsViewModel.Ketosis=Кетоз
MetabolicIncidenceInputsViewModel.Mastitis=Падеж
MetabolicIncidenceInputsViewModel.Metritis=Метрит
MetabolicIncidenceInputsViewModel.MilkFever=Родильный парез
MetabolicIncidenceInputsViewModel.MilkLossKg=Потеря молока за лактацию ({0})
MetabolicIncidenceInputsViewModel.MilkPrice=Цена молока
MetabolicIncidenceInputsViewModel.PerformanceMessage=Справочная информация для подсчета экономического эффекта от каждого случая нарушения обмена веществ
MetabolicIncidenceInputsViewModel.PerformanceTreatment=Стоимость лечения и потеря продуктивности
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Стоимость ремонта стада
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Задержание последа
MetabolicIncidenceInputsViewModel.Title=Нарушения обмена веществ - ввод данных
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Кол-во новот. коров для оценки
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Кол-во новотельн. коров/год
MetabolicIncidenceInputsViewModel.TreatmentCost=Затраты на лечение
MetabolicIncidenceMasterViewModel.Charts=Графики
MetabolicIncidenceMasterViewModel.Inputs=Вводные данные
MetabolicIncidenceMasterViewModel.Outputs=Выходные данные
MetabolicIncidenceMasterViewModel.Title=Нарушения обмена веществ
MetabolicIncidenceOutputsViewModel.DeathLoss=Падеж
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Смещение сычуга
MetabolicIncidenceOutputsViewModel.Dystocia=Трудный отел
MetabolicIncidenceOutputsViewModel.Ketosis=Кетоз
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Случаи  (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Затраты/гол
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Увеличение сервис-периода, дни
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Разница (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Редактировать
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Цель (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Экономический эффект от нарушений обмена веществ сверх установленных целей по стаду
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Годовой экономический эффект 
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Годовой экономический эффект - общий
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Стоимость потерянного молока
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Процент нарушения обмена веществ
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Общие затраты
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Затраты на лечение
MetabolicIncidenceOutputsViewModel.Metritis=Метрит
MetabolicIncidenceOutputsViewModel.MilkFever=Родильный парез
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Задержание последа
MetabolicIncidenceOutputsViewModel.Title=Нарушения обмена веществ - выходные данные
MetabolicIncidenceOutputsViewModel.TotalLosses=Общегодовые потери
Metric=Метрическая
MetricTonsAF=тонн НВ
MetricTonsAFSilo=тонн НВ (осталось в хранилище)
MetricTonsDM=тонн СВ
MetricTonsDMSilo=тонн СВ (осталось в хранилище)
Metritis=Метрит
Mexico=Мексика
Mexico_State=Мексика штат
Michigan=МИЧИГАН
Michoacán=Мичокаканн
MidLactation=Середина лактации
MidOne=Среднее 1
MidOneValue=(8 мм)
MidTwo=Среднее 2
Milan=Милан
Milk=Молоко ({0})
MilkChange=Изменения по молоку ({0})
MilkFever=Родильный парез
MilkLetDownResponse=Эффект по молоку ({0}/гол/день)
MilkLossDay=Потери молока ({0}/Day)
MilkLossGain=Потенц.эффект по молоку
MilkLossKg=Потери молока (кг)
MilkLossPounds=Потери молока (фунты)
MilkLossYear=Потери молока ({0}/год)
MilkPrice=Цена молока ({0}/{1})
MilkPricePremiums=Надбавки к цене молока
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=Справочная таблица
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Вводные данные
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Ресурсы
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Результаты
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Сравнение процедуры доения
MilkProcessRevenueCalculatorMasterViewModel.Title=Сравнение процедуры доения
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Блокнот посещения
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Неоптимальная стимуляция
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Цена молока ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=Без стимуляции
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Оптимальная стимуляция
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Сценарий 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Сценарий 2
MilkProcessorEditComparisonValuesViewModel.Title=Редактировать значения для сравнения
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=хандредвейт
MilkProcessorEditComparisonValuesViewModel.WeightMetric=кг
MilkProcessorInputViewModel.ComparisonValues=Сравнение значений
MilkProcessorInputViewModel.Edit=Редактировать
MilkProcessorInputViewModel.MilkPrice=Цена молока ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=Выбранный ранее переработчик был удален. Выберите  другого переработчика, чтобы продолжить
MilkProcessorInputViewModel.ScenarioOne=Сценарий 1
MilkProcessorInputViewModel.ScenarioTwo=Сценарий 2
MilkProcessorInputViewModel.SelectProcessor=Выбрать переработчика
MilkProcessorInputViewModel.Title=Сравнение процедуры доения – вводные данные
MilkProcessorInputViewModel.WeightImperialCWT=хандредвейт
MilkProcessorInputViewModel.WeightMetric=кг
MilkProcessorResourcesViewModel.ApproxSCC=Возм. уровень сом. клеток (кл/мл)
MilkProcessorResourcesViewModel.LinearScore=Линейная оценка
MilkProcessorResourcesViewModel.Mastitis=Мастит - потери молока ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Справочная таблица
MilkProcessorResourcesViewModel.Title=Сравнение процедуры доения – ресурсы
MilkProcessorResultsViewModel.Change=Изменить
MilkProcessorResultsViewModel.HundredWeight=хандредвейт
MilkProcessorResultsViewModel.MilkPrice=Цена молока ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Изменения годовых значений
MilkProcessorResultsViewModel.ScenarioOne=Сценарий 1
MilkProcessorResultsViewModel.ScenarioTwo=Сценарий 2
MilkProcessorResultsViewModel.Title=Сравнение процедуры доения – результаты
MilkProcessorResultsViewModel.WeightImperialCWT=хандредвейт
MilkProcessorResultsViewModel.WeightMetric=кг
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Компонент
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Концентрация
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Компонент
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Концентрация
MilkProcessorSettingsMasterViewModel.MilkProcNew=Новый
MilkProcessorSettingsMasterViewModel.Title=Выбрать переработчика молока
MilkProcessorViewModel.Amount=Количество (1000 кл/мл)
MilkProcessorViewModel.AmountCFU=Количество (1000 КОЕ/мл)
MilkProcessorViewModel.BasePriceMilkFat=Жир молока ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Молоко ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Белок молока ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Другие компоненты ({0}/{1})
MilkProcessorViewModel.BasePrices=Базовые цены
MilkProcessorViewModel.ComponentProcessor=Переработчик (компоненты)
MilkProcessorViewModel.ConcentrationProcessor=Переработчик (концентрация)
MilkProcessorViewModel.Delete=Удалить
MilkProcessorViewModel.DeletePrompt=Удаление переработчика аннулирует результат во вкладке "сравнение процедуры доения". Вы хотите удалить этого переработчика?
MilkProcessorViewModel.HundredWeight=хандредвейт
MilkProcessorViewModel.Name=Название
MilkProcessorViewModel.NameNotUnique=Переработчик компонентов под названием "{0}" уже существует. Введите другое название
MilkProcessorViewModel.NewComponentProcessorName=Переработчик (компоненты) \#{0}
MilkProcessorViewModel.NewConcentrationProcessorName=Переработчик (концентрация)  \#{0}
MilkProcessorViewModel.PricingMatrices=Матрицы цен
MilkProcessorViewModel.SelectCurrency=Выбрать валюту
MilkProcessorViewModel.WeightImperial=фунт
MilkProcessorViewModel.WeightImperialCWT=хандредвейт
MilkProcessorViewModel.WeightMetric=кг
MilkProduction=Мол. продуктивность ({0})
MilkProductionKg=Произ-во молока (кг)
MilkProductionPounds=Произ-во молока (фунт)
MilkProductionRevenue=Доход от переработки молока
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Кол-во и эффективность компонентов
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=ПСВ и эффективность использования кормов
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=% жира и белка молока
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Мол. продуктивность и ДЛ
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Соматика и мочевина молока
MilkSoldEvaluationChartsListViewModel.VisitComparison=Выберите посещения для сравнения
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Эффективность компонентов
MilkSoldEvaluationChartsViewModel.ComponentYield=Выход компонентов
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Кол-во и эффективность компонентов
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=ПСВ и эффективность использования кормов
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Дней лактации
MilkSoldEvaluationChartsViewModel.DryMatterIntake=ПСВ
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Эффективность кормов
MilkSoldEvaluationChartsViewModel.MilkFat=Жир молока, %
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=% жира и белка молока
MilkSoldEvaluationChartsViewModel.MilkProduction=Мол. продуктивность
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Мол. продуктивность и ДЛ
MilkSoldEvaluationChartsViewModel.MilkProtein=Белок молока%
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Измерение мочевины в молоке
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Кол-во сом. клеток
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Соматика и мочевина молока
MilkSoldEvaluationChartsViewModel.Title=Оценка проданного молока
MilkSoldEvaluationInputsViewModel.AddPickup=Добавить отгрузку
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Животных на танк ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Дней лактации (ДЛ) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=ПСВ ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=Информация по стаду
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Лактирующих животных ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Отгрузка молока ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Информация о переработчике молока
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Измерение мочевины в молоке ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Добавить
MilkSoldEvaluationMasterViewModel.Charts=Графики
MilkSoldEvaluationMasterViewModel.Inputs=Вводные данные
MilkSoldEvaluationMasterViewModel.Outputs=Результаты
MilkSoldEvaluationMasterViewModel.Title=Оценка проданного молока
MilkSoldEvaluationOutputsViewModel.AvgBCC=Ср.бак.обсемененность (1000 КОЕ/мл)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Средний % жира молока 
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Ср. мол. продуктивность, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Ср. мол. продуктивность, {0} (животные/танк)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Средний % белка молока 
MilkSoldEvaluationOutputsViewModel.AvgSCC=Ср. соматика (1000 кл/мл)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Эффективность компонентов (% от ПСВ)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Оценка за… дней
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Эффективность использования кормов (соотношение)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Кол-во жир+белок молока ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Кол-во жира молока ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Кол-во белка молока ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Настройка обновления сайта 
MilkSoldPickupViewModel.AnimalsInTank=Животных/танк ⃰
MilkSoldPickupViewModel.BCC=Бак. обсемененность (1000 КОЕ/мл)
MilkSoldPickupViewModel.DaysInTank=Дней в танке ⃰
MilkSoldPickupViewModel.MilkFat=Жир молока %
MilkSoldPickupViewModel.MilkProtein=Белок молока%
MilkSoldPickupViewModel.MilkSold=Проданное молоко, {0} ⃰
MilkSoldPickupViewModel.SCC=Кол-во сом. клеток (1000 кл/мл)
MilkSoldPickupViewModel.Title=Редактировать отгрузку {0}
MilkSoldSpinnerViewModel.Title=Оценка проданного молока
MilkUrea=Мочевина молока (мг/дл)
Milking=Дойные
MilkingFailure=Доильные неудачи
MilkingFirst=Сначала доение
MilkingProcessRevenueInputs=Сравнение процедуры доения – вводные данные
MilkingProcessRevenueResources=Сравнение процедуры доения – ресурсы
MilkingProcessRevenueResults=Сравнение процедуры доения – результаты
Minas_Gerais=Минас Герайс
Minnesota=Миннесота
Mississippi=Миссисипи
Missouri=Миссури
Mizoram=Мизорам
Modena=Моден
Moderate=Moderate
ModeratelyClean=Умеренно чисто
Moldova,_Republic_of=Молдова, Республика
Monaco=Монако
Monaghan=Монаган
Mongolia=Монголия
Montana=Монтана
Montenegro=Черногория
Monthly=Ежемесячно
Montserrat=Монтсеррат
Monza_and_Brianza=Монца и Брианза
MoreThan8LayersOfPlastic=Более 8 слоев пленки
Morelos=Морелос
Morocco=Марокко
Mozambique=Мозамбик
Myanmar=Мьянма
NGN=NGN
NIO=Никарагуа (NIO NIO)
NOK=NOK
Nagaland=Нагаленд
Namibia=Намибия
Naples=Неаполь
Nauru=Науру
Nayarit=Наярит
Nebraska=Небраска
Nei_Mongol=Ней Монгол
Nepal=Непал
Netherlands=Нидерланды
Nevada=Невада
NewBunker=Введите название новой траншеи
NewDietClassViewModel.Title=Класс/подкласс животных
NewDietPensViewModel.AssociatePens=Вы можете назначить этот рацион нескольким секциям
NewDietPensViewModel.Title=Ссылка на секции
NewDietViewModel.Cancel=Отменить
NewDietViewModel.MainHeading=Название рациона ⃰
NewDietViewModel.Save=Сохранить
NewDietViewModel.Title=Новый рацион
NewPenDietViewModel.New=Новый
NewPenDietViewModel.Title=Рацион
NewPenViewModel.Animals=Животных в секции, гол.
NewPenViewModel.AnimalsInputsPen=Ввод данных по животным в секции
NewPenViewModel.AsFedIntake=Потребление корма на НВ  ({0})
NewPenViewModel.Barn=Название отделения
NewPenViewModel.Cancel=Отменить
NewPenViewModel.DaysInMilk=ДЛ
NewPenViewModel.Diet=Рацион
NewPenViewModel.DietInputsPen=Ввод данных по рациону в секции
NewPenViewModel.DryMatterIntake=ПСВ  ({0})
NewPenViewModel.FeedingSystem=Система кормления
NewPenViewModel.General=Общее
NewPenViewModel.HousingSystem=Система содержания
NewPenViewModel.Milk=Продуктивность ({0})
NewPenViewModel.MilkingFrequency=Кратность доений
NewPenViewModel.NumberOfStalls=Количество стойл
NewPenViewModel.OnlyOnePen=Имеется только одна секция
NewPenViewModel.PenDetail=Детали секции
NewPenViewModel.PenMapping=Отображение секции
NewPenViewModel.PenName=Название секции
NewPenViewModel.PenSelection=Выбрать секцию
NewPenViewModel.PublishPenAlert=Опубликуйте все визиты по секциям, которые вы хотите объединить
NewPenViewModel.RationCostPerAnimal=Стоимость рациона на гол. ({0})
NewPenViewModel.Save=Сохранить
NewPenViewModel.Title=Новая секция
NewPenViewModel.UserCreatedPen=Секция создана пользователем
NewPile=Дайте новое название новому кургану
NewProspectViewModel.Address1=Адрес 1
NewProspectViewModel.Address2=Адрес 2
NewProspectViewModel.Aiden=Эйден
NewProspectViewModel.Baxter=Бакстер 
NewProspectViewModel.BusinessName=Название предприятия
NewProspectViewModel.City=Город
NewProspectViewModel.ConsumerDetails=Детали потребителя
NewProspectViewModel.Country=Страна
NewProspectViewModel.Customer=Клиент
NewProspectViewModel.CustomerDetail=Детали клиента
NewProspectViewModel.Dennis=Деннис
NewProspectViewModel.EmailAddress=Контактный e-mail
NewProspectViewModel.EndUser=Конечный пользователь
NewProspectViewModel.FarmProducer=Производитель
NewProspectViewModel.Image=Нажмите, чтобы отредактировать изображение
NewProspectViewModel.InvalidEmail=Введите e-mail
NewProspectViewModel.Kobe=Коуб
NewProspectViewModel.Mila=Мила
NewProspectViewModel.NameNotUnique=Потенциальный клиент с именем "{0}" уже существует. Введите уникальное имя
NewProspectViewModel.NameNotUniqueForConsumer=Потребитель с названием "{0}" уже существует. Введите уникальное имя
NewProspectViewModel.Noah=Ноа
NewProspectViewModel.NotSet=- 
NewProspectViewModel.NullBusinessName=Необходимо ввести название предприятия
NewProspectViewModel.NullFirstName=Введите имя
NewProspectViewModel.NullSecondName=Введите фамилию
NewProspectViewModel.PostalCode=Индекс
NewProspectViewModel.PrimaryContactFirstName=Имя основного контактного лица
NewProspectViewModel.PrimaryContactLastName=Фамилия основного контактного лица
NewProspectViewModel.PrimaryPhone=Контактный телефонный номер
NewProspectViewModel.Prospect=Потенциальный клиент
NewProspectViewModel.ProspectDetail=Детали потенциального клиента
NewProspectViewModel.Segment=Сегмент
NewProspectViewModel.Sonya=Соня
NewProspectViewModel.Spence=Спенс
NewProspectViewModel.State=Штат
NewProspectViewModel.Title=Детали
NewProspectViewModel.Type=Тип
NewProspectViewModel.Walton=Уолтон
NewVisitViewModel.Title=Детали посещения
New_Brunswick=Новый Брансвик
New_Caledonia=Новая Каледония
New_Hampshire=Нью-Гемпшир
New_Jersey=Нью-Джерси
New_Mexico=Нью-Мексико
New_South_Wales=Новый Южный Уэльс
New_York=Нью-Йорк
New_Zealand=Новая Зеландия
Newfoundland_and_Labrador=Ньюфаундленд и Лабрадор
Next=Следующий
Nicaragua=Никарагуа
Niedersachsen=Нижняя Саксония
Niger=Нигер
Nigeria=Нигерия
Ningxia=Ningxia
Niue=Niue
No=Нет
No-User-Found=Контакт не найден в LIFT, пожайлуйста свяжитесь с вашим администратором
NoResourcesAvailable=Ресурс недоступен
NoResults=Результаты не найдены
NoWholeKernals=Нет цельных зерен
Noah=Noah
None=Нет
NoneSelected=Нет выбранных
Nordrhein=Северный Рейн
Norfolk_Island=Остров Норфолк
Normal=&lt;0.5 балла 
NorthAmerica=Северная Америка
North_Carolina=Северная Каролина
North_Dakota=Северная Дакота
Northern_Territory=Северная территория
Northwest_Territories=Северо-западные территории
Norway=Норвегия
Not-matching-with-allowed-values=Не соответствует допустимым значениям
NotMeasured=Оценку не проводили
NotRemoved=Не удаляются
NotSet=-
NoteCamcorderNotImplemented=Функция камеры недоступна 
NoteCategoryViewModel.FooterText=Можно выбрать только одну категорию для заметки
NoteCategoryViewModel.SelectCategory=Выберите категорию
NoteCategoryViewModel.Title=Заметка
NotebookBCSHerdAnalysisGoals=Анализ упитанности стада - цель
NotebookBCSHerdAnalysisInputs=Анализ упитанности стада - ввод данных
NotebookBCSHerdAnalysisResults=Анализ упитанности стада - результат
NotebookBCSSelectPointScale=Кондиция упитанности – выбрать шкалу оценки
NotebookBodyConditionEdit=Кондиция упитанности - редактировать таблицу
NotebookCudCalculators=Здоровье рубца - оценка жвачки
NotebookCudChewing=Здоровье рубца - жвачка в выбранной секции
NotebookCudChewingDataEntry=Здоровье рубца - ввод жевательных движений
NotebookCudChewingResults=Здоровье рубца - результаты по жвачке
NotebookLocomotionEditTable=Походка - количество коров
NotebookLocomotionHerdInputs=Анализ походки стада - ввод данных
NotebookLocomotionHerdResults=Анализ походки стада - результаты
NotebookLocomotionHerdRevenue=Анализ походки стада - доход
NotebookLocomotionLanding=Походка - основное
NotebookLocomotionPenInputs=Походка - ввод данных по секции
NotebookLocomotionPenResults=Походка - результат по секции
NotebookLocomotionPenSelection=Походка - выбор секции
NotebookManurePenSelection=Оценка навоза - выбор секции
NotebookManureScoreHerdAnalysisGoals=Оценка навоза - цели
NotebookManureScoreHerdAnalysisInputs=Оценка навоза - ввод данных
NotebookManureScoreHerdAnalysisResults=Оценка навоза - результаты
NotebookManureScoreLanding=Оценка навоза
NotebookMetabolicIncidenceCharts=Нарушения обмена веществ - график
NotebookMetabolicIncidenceInputs=Нарушения обмена веществ - ввод данных
NotebookMetabolicIncidenceOutputs=Нарушения обмена веществ - выходные данные
NotebookParticleScoreHerdAnalysisEdit=Анализ оценки ПСР по стаду - редактировать кол-во ПСВ
NotebookParticleScoreLanding=Оценка размера частиц ПСР
NotebookParticleScoreSelectPen=Оценка размера частиц ПСР - выбор секции
NotebookParticleScoreSelectScorer=Оценка размера частиц ПСР - выбор инструмента
NotebookPenTimeComparison=Бюджет времени секции - сравнение
NotebookPenTimeInputs=Бюджет времени секции - ввод данных
NotebookPenTimePenSelection=Бюджет времени секции - выбор секции
NotebookPenTimeResults=Бюджет времени секции - результаты
NotebookReadyToMilkCharts=ReadyToMilk графики
NotebookReadyToMilkInputs=ReadyToMilk ввод данных
NotebookReadyToMilkOutputs=ReadyToMilk выходные данные
NotebookRumenHealthNumberOfChewsInput=Здоровье рубца - ввод кол-ва жев. движений
NotebookRumenHealthNumberOfChewsResults=Здоровье рубца - результаты по кол-ву жев. движений
NotebookRumenHealthTMRParticlePercent=Процент ПСР на сите
NotebookRumenHealthTMRParticleScore=Оценка размера частиц ПСР
NotebookSectionComfortTools=Инструменты оценки комфорта
NotebookSectionForageAudit=Аудит структурных кормов - оценка
NotebookSectionForageAuditBaleage=Аудит структурных кормов - тюки
NotebookSectionForageAuditBunkersPiles=Аудит структурных кормов - траншеи и курганы
NotebookSectionForageAuditHarvest=Аудит структурных кормов - заготовка
NotebookSectionForageAuditLanding=Аудит структурных кормов
NotebookSectionForageAuditMaintainingQuality=Аудит структурных кормов - поддержание качества
NotebookSectionForageAuditSilageBags=Аудит структурных кормов - силосные рукава
NotebookSectionForageAuditSurveyOfForages=Аудит структурных кормов - осмотр кормов
NotebookSectionForageAuditTowerSilos=Аудит структурных кормов - силосные башни
NotebookSectionHealthTools=Здоровье
NotebookSectionHeatstressCalculations=Тепловой стресс - калькуляция
NotebookSectionHeatstressChart=Тепловой стресс - график
NotebookSectionHeatstressData=Тепловой стресс - данные
NotebookSectionHerdAnalysisGoals=Здоровье рубца - анализ целей по стаду
NotebookSectionMilkSoldEvaluationCharts=Оценка проданного молока - графики
NotebookSectionMilkSoldEvaluationEditPickup=Оценка проданного молока - редактировать отгрузку
NotebookSectionMilkSoldEvaluationInputs=Оценка проданного молока - вводные данные
NotebookSectionMilkSoldEvaluationOutputs=Оценка проданного молока - результат
NotebookSectionNutritionTools=Инструменты кормления
NotebookSectionRumenHealthLanding=Здоровье рубца
NotebookSectionVisit=Посещение
NotebookTMRParticleHerdAnalysisPenInputs=Анализ оценки ПСР по стаду - ввод данных
NotebookTMRParticleHerdAnalysisPenResults=Анализ оценки ПСР по стаду - результаты
NotebookUrinePHEditGoals=рН мочи - редактировать цели
NotebookUrinePHInputs=рН Мочи - ввод данных
NotebookUrinePHOutputs=рН мочи - результаты
NotebookVisitSummary=Резюме посещения
NotebookWalkthroughReport=Отчет о посещении
NotebookWalkthroughReportLanding=Отчет о посещении
NotebookWalkthroughReportPen=Отчет о посещении - анализ секции
Nova_Scotia=Новая Шотландия
Novara=Новара
Nuevo_León=Новый Лейн
Null-values-not-allowed=Значение 0 не допустимо
NumChewsGoal={0} Цель
NumOfCows=Количество коров
NumberOfChewsReportsViewModel.Average=Среднее кол-во жев. движений
NumberOfChewsReportsViewModel.AverageChews=Среднее кол-во жев. движений
NumberOfChewsReportsViewModel.AverageNumberChews=Среднее кол-во жев. движений
NumberOfChewsReportsViewModel.DateDescription=Дата
NumberOfChewsReportsViewModel.DatesComparison=Даты для сравнения
NumberOfChewsReportsViewModel.EditVisits=Выбрать
NumberOfChewsReportsViewModel.StdDevCalculated=Стандартное отклонение
NumberOfChewsReportsViewModel.VisitDate=Дата
NumberOfChewsViewModel.Count=Количество
NumberOfChewsViewModel.CountHeader=Посчитайте количество жевательных движений у этой коровы
NumberOfChewsViewModel.NextCow=Следующая корова
NumberOfChewsViewModel.NumberOfChewsCow=Кол-во жев. движений/корову
NumberOfChewsViewModel.Title=Кол-во жев. движений/корова \# {0}
NumberOfChewsViewModel.ValidCudInput=Введите полученные данные
NumberOfCows=Количество коров
Nunavut=Нунавут
Nuoro=Нуро
NutritionViewModel.NutritionForage=Аудит структурных кормов
NutritionViewModel.NutritionLabel=Выберите инструмент из списка ниже, чтобы начать посещение
NutritionViewModel.NutritionPile=Инвентаризация структурных кормов
NutritionViewModel.NutritionTools=Инструменты оценки кормления
NutritionViewModel.NutritionToolsCaption=Инструменты
NutritionViewModel.NutritionToolsInstructions=Выберите инструмент из списка ниже, чтобы начать посещение
NutritionViewModel.NutritionToolsList=Инструменты
NutritionViewModel.Title=Инструменты оценки кормления
NutritionViewModel.VisitNotebook=Блокнот посещения
OKLabel=Ок
Oaxaca=Оксака
Observation=Наблюдения
Odisha=Одиша
Offaly=Оффали
Ogliastra=Оглиастра
Ohio=Огайо
Oklahoma=Оклахола
Olbia-Tempio=Ольбиа-Темпио
Oman=Свой
OncePerWeek=Раз в неделю
One=1
OneHourBeforeActionIsDue=За час до действия
OneToSixHours=От 1 до 6 ч
Ontario=Онтарио
Opportunities=Возможности
Optimal=Оптимальный ответ
Oregon=Орегон
Oristano=Ористано
Other=Другое
OtherSilage=Другой вид силоса 
Overall=Всеобщий
OverallCalfHeiferDetails=Для просмотра общей оценки по телятам&amp;телкам завершите хотя бы один из опросов из списка
OverallForageScoreDetails=Чтобы просмотреть общую оценку, завершите хотя бы один опрос из списка
PDFDisclaimer=Cargill инкорпорейтед, основные и дочерние организации не гарантируют точность оценки ввиду многих факторов. Нет гарантии на получение продуктивности или финансовых результатов. ©{0} Cargill, Инкорпорейтед. Все права защищены
PDFDisclaimer_ProvimiUS=Provimi North America, основные и дочерние организации не гарантируют точность оценки ввиду многих факторов. Нет гарантии на получение продуктивности или финансовых результатов.  ©{0} Provimi North America. Все права защищены
PDFPageNumber=Страница {0} из {1}
PEN=Перу (S/. PEN)
PHP=Филиппины ($ PHP)
PLN=Польша (zł PLN)
PMRConcentrate=ЧСР + концентрат
PON=Румыния (lei PON)
Padua=Падуя
Pakistan=Пакистан
Palermo=Палермо
Palestinian_Territory,_Occupied=Палестинская территория, занятая
Panama=Панама
Papua_New_Guinea=Папуа - Новая Гвинея
Paraguay=Парагвай
Paraná=Парана
Paraíba=Параба
Parlor=Доильный зал
Parma=Парма
ParticleScorePreviousVisitsViewModel.MidOne=Среднее 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8 мм)
ParticleScorePreviousVisitsViewModel.MidTwo=Среднее 2
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Процент на сите (%)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Среднее 1 (8 мм)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Среднее 2 (4 мм)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Верхее (19 мм)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Поддон
ParticleScorePreviousVisitsViewModel.SelectDates=Выбрать даты
ParticleScorePreviousVisitsViewModel.Top=Верхнее
ParticleScorePreviousVisitsViewModel.TopValue=(19 мм)
ParticleScorePreviousVisitsViewModel.Tray=Поддон
Pará=Парааторий
PasteurizedWholeMilk=Выпаивают пастеризованное цельное молоко
Pasto=Pasto
Pasture=Пастбище
PastureOther=Пастбище + другое
Pavia=Павия
Pays=Оплачивается
PeakMilk=Пик лактации
PenDetailViewModel.Title=Детали секции
PenListViewModel.DietSetup=Установка рациона
PenListViewModel.InfoPenList=При посещении сайта в разделе секций необходимо использовать инструменты секции и отчет обхода. Названия секций и данные будут обновлены автоматически для сайтов при загрузке данных фермы в Dairy Enteligen. Если автоматическая загрузка данных не установлена, добавьте секции вручную. 
PenListViewModel.MainHeading=Секции
PenListViewModel.NewPen=Добавить новую секцию
PenListViewModel.Title=Секции
PenName=Название секции
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Изменения кондиций упитанности (за 100 дней)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Изменения массы тела ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Коров в секции
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Выдаивается, коров/ч
PenTimeBudgetComparisonViewModel.Current=Текущее
PenTimeBudgetComparisonViewModel.EnergyChange=Изменения по энергии (Мкал)
PenTimeBudgetComparisonViewModel.Overcrowding=Переполнение (%)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Оборотов доильн. зала/ч
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Потенц.эффект по мол. ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Разница в отдыхе (ч)
PenTimeBudgetComparisonViewModel.TableTitle=Сравнение
PenTimeBudgetComparisonViewModel.TimePerMilking=Время доения,ч
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Время, доступн.для отдыха,ч
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Требуется на отдых,ч
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Общ. время не на отдых,ч
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Общее время доения,ч
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Время на поиск стойла,ч
PenTimeBudgetPenMasterViewModel.Compare=Сравнить
PenTimeBudgetPenMasterViewModel.Inputs=Вводные данные
PenTimeBudgetPenMasterViewModel.Results=Результаты
PenTimeBudgetPenMasterViewModel.Title=Бюджет времени секции
PenTimeBudgetResultsViewModel.Hours=Часов
PenTimeBudgetResultsViewModel.MilkDifference=Потенциальная разница по молоку
PenTimeBudgetResultsViewModel.MilkLossKg=кг
PenTimeBudgetResultsViewModel.MilkLossPounds=фунт
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Потенц.эффект по молоку
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Время, доступн.для отдыха
PenTimeBudgetResultsViewModel.TimeRemaining=Остаток времени
PenTimeBudgetResultsViewModel.TimeRequired=Требующееся время
PenTimeBudgetResultsViewModel.Title=Бюджет времени секции - результаты
PenTimeInputsViewModel.CowsPen=Кол-во коров в секции
PenTimeInputsViewModel.Drinking=Время - питье/груминг,ч
PenTimeInputsViewModel.Eating=Время поедания корма,ч
PenTimeInputsViewModel.Frequency=Кратность доения (в день)
PenTimeInputsViewModel.LockUp=Время в хедлоках,ч
PenTimeInputsViewModel.NonRestTime=Др. время не на отдых,ч
PenTimeInputsViewModel.ParlorTime=Пребыв. в доильн. зале,ч
PenTimeInputsViewModel.PenTimeTitle=Бюджет времени секции
PenTimeInputsViewModel.Resting=Потребность в отдыхе (ч)
PenTimeInputsViewModel.StallsPen=Кол-во стойл в секции
PenTimeInputsViewModel.TotalStalls=Кол-во мест в доильн. зале
PenTimeInputsViewModel.WalkingTimeFrom=Время пути из доильн. зала,ч
PenTimeInputsViewModel.WalkingTimeTo=Время пути в доильн. зал,ч
PenTimePenSelectionViewModel.NoLactatingPen=Чтобы этот инструмент стал доступен, убедитесь, что есть хотя бы одна секция с рационом для лактирующих
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Бюджет времени секции
PenTimePenSelectionViewModel.PenTimeSection=Секции
PenTimePenSelectionViewModel.Title=Бюджет времени секции
Pendetails=Детали секции
PennStateShakerBoxForageResults=Результаты оценки кормов Пенсильванскими ситами
Pennsylvania=Пенсильвания
Pens=Секции
PerceivedHeatStressDietInfoMessage=Умеренный тепловой стресс\: тяжелое дыхание, обильное выделение слюны или пены, но рот закрыт, частота дыхания 40-120 в мин. Сильный тепловой стресс\: открыт рот + обильное слюноотделение, частота дыхания от 120 до более чем 160 в минуту.
PercentLossPerCow=% потерь/гол.
PercentOnScreenTitle=Процент на сите (%)
PercentPen=Процент от секции (%)
PercentageOnScreen=Процент на сите (%)
PercentageOnScreenCurrentVisit=Процент на сите (%) - текущее посещение
PercentageOnScreenTrend=Процент на сите (%) - тренд
Pernambuco=Pernambuco
Peru=Перу
Perugia=Перуджа
Pesaro_and_Urbino=Песаро и Урбино
Pescara=Пескара
PhaseFive=Случной
PhaseFour=Роста и развития
PhaseOne=Молозивный
PhaseSeven=Предотел/продуктивный
PhaseSix=Стельность
PhaseTwoThree=Пред/послеотъемный
Philippines=Филиппины
PhotoExamples=Образцы фотографий
Piacenza=Piacenza
Piauí=Пиау
Pickup=Отгрузка {0}
Pile=Курган
PileAndBunkerCapacitiesDensity=Руководство по инвентаризации структурных кормов и оценке плотности запасов
PileAndBunkerCapacity=Вместительность курганов и траншей
PileAndBunkerCapacityViewModel.AddBag=Добавить силосный рукав
PileAndBunkerCapacityViewModel.AddBunker=Добавить траншею
PileAndBunkerCapacityViewModel.AddPile=Добавить курган
PileAndBunkerCapacityViewModel.Bag=Силосный рукав
PileAndBunkerCapacityViewModel.Bags=Силосные рукава
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=Башня (нижняя выгрузка)
PileAndBunkerCapacityViewModel.Bunker=Траншея
PileAndBunkerCapacityViewModel.Bunkers=Траншеи
PileAndBunkerCapacityViewModel.NameNotUnique=Курган или траншея с названием "{0}" уже существует. Введите уникальное название
PileAndBunkerCapacityViewModel.NameTooLong=В названии кургана или траншеи не должно быть более 40 знаков
PileAndBunkerCapacityViewModel.Pile=Курган
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Инвентаризация структурных кормов
PileAndBunkerCapacityViewModel.Piles=Курганы
PileAndBunkerCapacityViewModel.Resources=Ресурсы
PileAndBunkerCapacityViewModel.Title=Инвентаризация структурных кормов
PileAndBunkerCapacityViewModel.TopUnloadingSilo=Башня (верхняя выгрузка)
PileAndBunkerCapacityViewModel.VisitNotebook=Блокнот посещения
PileAndBunkerName=Введите название(дату или номер) инвентаризации  
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=Плотность силоса НВ {0} (Цель\: &gt; {1})
PileAndBunkerResultsBagCapacityInputViewModel.CapacityBag=Вместительность
PileAndBunkerResultsBagCapacityInputViewModel.DiameterBag=Диаметр ({0})
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=Сухое вещество, %
PileAndBunkerResultsBagCapacityInputViewModel.LenghtBag=Длина ({0})
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=тонн НВ
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=тонн СВ
PileAndBunkerResultsBagCapacityInputViewModel.SilageDMDensityBag=Плотность силоса СВ ({0})
PileAndBunkerResultsBagCapacityInputViewModel.TonsAFBag=тонн НВ
PileAndBunkerResultsBagCapacityInputViewModel.TonsDMBag=тонн СВ
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Длина основания ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Ширина основания ({0})
PileAndBunkerResultsCapacityInputViewModel.Capacity=Вместительность
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=Сухое вещество, %
PileAndBunkerResultsCapacityInputViewModel.Height=Высота ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=тонн НВ
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=тонн СВ
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Длина основания (фут)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Длина основания (м)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Ширина основания (фут)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Ширина основания (м)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Высота (фут)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Высота (м)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Длина верхней части (фут)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Длина верхней части (м)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Высота верхней части (фут)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Ширина верхней части (м)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Плотность силоса СВ ({0})
PileAndBunkerResultsCapacityInputViewModel.SlopeMessage=Уклон {0} to 1.0 
PileAndBunkerResultsCapacityInputViewModel.SlopeMessagePile=Уклон {0} к 1.0 (Цель > 3.5 to 1.0)
PileAndBunkerResultsCapacityInputViewModel.Title=Вместительность
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Плотность силоса НВ {0} (цель\: &gt; {1})
PileAndBunkerResultsCapacityInputViewModel.TonsAF=тонн НВ
PileAndBunkerResultsCapacityInputViewModel.TonsDM=тонн СВ
PileAndBunkerResultsCapacityInputViewModel.TopLength=Длина верхней части ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Ширина верхней части ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=6 дюймов в день
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=15 см в день
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=При 3 дюйм/день
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=7 см в день
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=Требуется для коров/дней
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Для кормления…коров, гол.
PileAndBunkerResultsFeedOutViewModel.DateGone=Дата завершения
PileAndBunkerResultsFeedOutViewModel.Days=Дней
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Информация по потреблению
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Площадь поверхности среза кормов (фут²)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Площадь поверхности среза кормов (м²)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Скармливание (кг НВ/гол)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=дюйм/день
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=см/день
PileAndBunkerResultsFeedOutViewModel.StartDate=Дата начала
PileAndBunkerResultsFeedOutViewModel.Title=Потребление
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=тонн/день
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=фунтов СВ в 1 футе
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=кг СВ в 1 м
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Вместительность
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Потребление
PileAndBunkerResultsMasterViewModel.VisitNotebook=Блокнот посещения
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=Вместительность
PileAndBunkerResultsSiloCapacityInputViewModel.Diameter=Диаметр ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=Сухое вещество %
PileAndBunkerResultsSiloCapacityInputViewModel.FilledHeight=Заполнен на высоту ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=тонн СВ (осталось в хранилище)
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=тонн СВ (осталось в хранилище)
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=Плотность силоса СВ ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.SilageLeft=Высота силоса (осталось) ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.TonsAFSilo=тонн НВ (осталось в хранилище)
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=тонн СВ (осталось в хранилище)
PileAndBunkerTitle=Инвентаризация структурных кормов
PileBunkerCapacities=Инвентаризация структурных кормов
PileCapacity=Вместительность кургана
PileFeedOutRate=Потребление кургана
Piles=Курганы
Pisa=Пиза
Pistoia=Пистоя
Pitcairn=Питкэрн
PlBacteriaCell=Бак. обсемененность (1,000 кл/мл)
PlMilkFat=Жир молока
PlMilkProtein=Белок молока
PlSomaticCell=Сом. клетки молока (1,000 кл/мл)
Poland=Польша
Poor=Плохо
Pordenone=Порденон
Porosity=Плотность трамбовки (пористость)
Portugal=Португалия
Postweaned=Послеотъемный
Postweaned_CleanAndDryPen=Чистое и сухое помещение
Postweaned_CleanAndDryPen_ToolTip=Используйте тест "мокрое колено", чтобы определить, насколько подстилка сухая и чистая
Postweaned_EvidenceOfAcidosisInManure=Признаки ацидоза в навозе
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Пузырьки газа в навозе
Postweaned_EvidenceOfScoursOrPneumonia=Признаки диареи или пневмонии
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% телят с диареей или пневмонией
Postweaned_FeedBunkIsClaanedDaily=Чистку кормушек и удаление остатков проводят регулярно
Postweaned_ForageAvailability=Доступность кормов
Postweaned_ForageAvailability_ToolTip=Резка более 5 см
Postweaned_FreeChoiceCleanWaterIsAvailable=Свободный доступ к чистой воде
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=Нет видимых загрязнений воды
Postweaned_FreshQualityStarterAvailable=Свежий, качественный стартер/гроуэр в свободном доступе
Postweaned_FreshQualityStarterAvailable_ToolTip=В стартере/гроуэре нет посторонних включений, плесени; корм не размокший
Postweaned_SizeOfBunkSpace=Адекватный фронт кормления для теленка
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45 см на теленка
Postweaned_SizeOfPenAdequate=Адекватная площадь пространства на телку
Postweaned_SizeOfPenAdequate_ToolTip=Индивидуальные боксы\: 3 м², групповое содержание\: 2.75 м²
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Помещение хорошо проветривается, сквозняков нет
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=Если по окончании визита одежда пахнет аммиаком, концентрация аммиака слишком высокая
PotentialDownResponse=Потенциальный эффект ({0}/гол./день)
PotentialSCC=Потенц.уровень сом. клеток (кл/{0})
Potenza=Власть
Prato=Блюдо
PreWeaned_CMRisProperlyMixed_ToolTip=Количество ЗЦМ\: &gt;600 г - &lt;800 г, температура\: 39-41°C, СВ 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Используйте тест "мокрое колено", чтобы определить, насколько подстилка сухая и чистая
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% телят с диареей или пневмонией 
PreWeaned_Forageavailability_ToolTip=При скармливании текстурированного стартера нет потребности в грубых кормах
PreWeaned_FreeChoiceCleanWater_ToolTip=Чистая и свежая вода без видимых загрязнений в свободном доступе с первого дня
PreWeaned_FreeChoiceFreshCalf_ToolTip=В стартере нет посторонних включений, плесени; корм не размокший
PreWeaned_SizeOfPen_ToolTip=Индивидуальные клетки\: 3 м², групповые клетки\: 2.5 м²
PreWeaned_WellVenilated_ToolTip=Если по окончании визита одежда пахнет аммиаком, концентрация аммиака слишком высокая
PrematureKelvingsKeyInfoMessage=Рождение одного или более живых телят как минимум за 10 дней до планируемой даты отела
PreventingStorageLosses=Профилактика потерь при хранении
Previous=Предыдущее
Preweaned=Предотъемный
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=ЗЦМ качественно приготовлен и выпаивается правильно
Preweaned_CleanAndDryPen=Чистое и сухое помещение
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Оборудование для кормления и выпойки дезинфицируется перед каждым кормлением
Preweaned_ConsistentFeedingTimesAndProtocols=Стабильное время выпойки и протоколы
Preweaned_EvidenceOfScoursOrPneumonia=Признаки диареи или пневмонии
Preweaned_ForageAvailability=Доступность структурных кормов
Preweaned_FreeChoiceCleanWaterIsAvailable=Свободный доступ к чистой воде
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Свежий, качественный стартер в свободном доступе
Preweaned_SizeOfPenAadequatePerHeifer=Адекватная площадь пространства на голову
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Отъем при потреблении не менее 1 кг стартера в сутки
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Помещение хорошо проветривается, сквозняков нет
PricingMatrixEditViewModel.Cancel=Отменить
PricingMatrixEditViewModel.Save=Сохранить
PricingMatrixEditViewModel.Title=Детали матрицы
PricingMatrixPickListViewModel.PricingMatrix=Матрица ценообразования
PricingMatrixViewModel.Amount=Кол-во (1000 кл/мл)
PricingMatrixViewModel.AmountCFU=Кол-во (1000 КОЕ/мл)
PricingMatrixViewModel.New=Новый
PricingMatrixViewModel.Title=Редактировать матрицу
Prince_Edward_Island=Остров Принца Эдуарда
Privacy_Statement=Заявление о конфиденциальности
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Валюта
ProcessorCurrencyPickListViewModel.Title=Валюта
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Сравнение процедуры доения
ProductivityToolsViewModel.MilkRevenueAnalysis=Анализ доходов от молока
ProductivityToolsViewModel.MilkSoldEvaluation=Оценка проданного молока
ProductivityToolsViewModel.ProductivityTitle=Инструменты продуктивности
ProductivityToolsViewModel.ProductivityTools=Инструменты
ProductivityToolsViewModel.VisitNotebook=Блокнот посещения
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=Отмена
PromptOK=Ок
PromptPermissionMsg=Без этого соглашения некоторые функции могут быть недоступны. Вы уверены, что хотите отклонить это соглашение?
PromptPermissionMsgIMSure=Я уверен
PromptPermissionMsgRetry=Попытаться еще раз
PromptPermissionTitle=Соглашение отклонено
ProspectProfileViewModel.DeleteProspect=Удалить потенциального клиента
ProspectProfileViewModel.DeleteProspectPrompt=Вы уверены, что хотите удалить потенциального клиента? Потенциальный клиент, сайт и незавершенный визит будут удалены
ProspectProfileViewModel.MainHeading=Сайты
ProspectProfileViewModel.NewSite=Добавить новый сайт
ProspectProfileViewModel.ProspectInfo=Информация о потенциальном клиенте
ProspectProfileViewModel.ProspectTitle=Детали потенциального клиента
ProspectsViewModel.NewProspect=Добавить нового потенциального клиента
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=Provimi
ProvimiUS=Provimi US
PublishVisit=Опубликовано
Puducherry=Пудучерри
Puebla=Пуэбла
Puerto_Rico=Пуэрто-Рико
Punjab=Пенджаб
Purina=Purina
Qatar=Катар
Qinghai=Цинхай
QuarterPointScale=Четвертьбалльная шкала оценки
Quarterly=Квартально
Quebec=Квебек
Queensland=Квинсленд
Querétaro=Quer é Taro
QuestionTableTitle=Вопрос {0} из {1}
QuestionViewModel.Baleage=Тюки
QuestionViewModel.BunkersAndPiles=Курганы и траншеи
QuestionViewModel.Close=Закрыть
QuestionViewModel.Harvest=Качество корма ПСР
QuestionViewModel.MaintainingForageQuality=Поддержание качества структурных кормов
QuestionViewModel.SilageBags=Силосные рукава
QuestionViewModel.SurveyOfForages=Осмотр структурных кормов
QuestionViewModel.TowerSilos=Силосная башня
QuestionViewModel.VisitNotebook=Блокнот посещения
Quintana_Roo=Кинтана Ру
ROL=ROL
RUB=Россия (₽‎ RUB)
Ragusa=Рагуза
Rajasthan=Раджастхан
Rationcost=Стоимость рациона ({0})
Ravenna=Равенна
ReadyToMilkChartViewModel.Current=Текущее
ReadyToMilkChartViewModel.DeathLoss=Мастит
ReadyToMilkChartViewModel.DisorderGraphTitle=Стоимость нарушений обмена веществ/гол./год
ReadyToMilkChartViewModel.DisplacedAbomasum=Смещение сычуга
ReadyToMilkChartViewModel.Dystocia=Трудный отел
ReadyToMilkChartViewModel.Ketosis=Кетоз
ReadyToMilkChartViewModel.Metritis=Метрит
ReadyToMilkChartViewModel.MilkFever=Родильный
ReadyToMilkChartViewModel.RetainedPlacenta=Задержание последа
ReadyToMilkChartViewModel.Title=Ready2Milk™ графики
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Индекс Ready2Milk™
ReadyToMilkInputViewModel.Back=Назад
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Изменение упитанности - запуск-21 ДЛ
ReadyToMilkInputViewModel.CloseUp=Предотельные коровы
ReadyToMilkInputViewModel.ComfortCloseUp=Комфорт предотела
ReadyToMilkInputViewModel.ComfortDiet=Комфорт  0-21 ДЛ
ReadyToMilkInputViewModel.CostExtraDaysOpen=Стоимость дней прохолоста
ReadyToMilkInputViewModel.CudChewingDiet=Жвачка - новотел
ReadyToMilkInputViewModel.DeadCowsOrCulled=Выбытие по здоровью
ReadyToMilkInputViewModel.DisplacedAbomasum=Смещение сычуга
ReadyToMilkInputViewModel.Dystocia=Трудный отел
ReadyToMilkInputViewModel.FreshCows=Новотел
ReadyToMilkInputViewModel.Health=Здоровье
ReadyToMilkInputViewModel.HealthDesc=Введите количество новотельных коров и количество нарушений обмена веществ за рассматриваемый период. На основе этих данных будут рассчитаны годовые потери от заболеваний, они появятся во вкладке "результаты"
ReadyToMilkInputViewModel.HealthRecords=Записи о здоровье
ReadyToMilkInputViewModel.Herd=Информация по стаду
ReadyToMilkInputViewModel.Ketosis=Кетоз
ReadyToMilkInputViewModel.LocomotionScore=Оценка походки
ReadyToMilkInputViewModel.Mastitis=Мастит 0-21 ДЛ
ReadyToMilkInputViewModel.Metritis=Метрит
ReadyToMilkInputViewModel.MilkFever=Родильный парез
ReadyToMilkInputViewModel.MilkPrice=Цена молока
ReadyToMilkInputViewModel.MilkYield=Мол. продуктивность 15-60 ДЛ
ReadyToMilkInputViewModel.Next=Далее
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Тепловой стресс  0-21 ДЛ
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Тепловой стресс в предотел
ReadyToMilkInputViewModel.PrematureCalvings=Преждевременный отел
ReadyToMilkInputViewModel.ReplacementCowCost=Стоимость ремонта стада
ReadyToMilkInputViewModel.RetainedPlacenta=Задержание последа
ReadyToMilkInputViewModel.RumenFill=Наполненность рубца
ReadyToMilkInputViewModel.SccFirstTest=Соматика, 1-й тест (x1000 кл/мл)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Спец. рацион в предотел
ReadyToMilkInputViewModel.SpecificDiet=Спец. рацион 0-21 ДЛ
ReadyToMilkInputViewModel.Title=Ready2Milk™ вводные данные
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Кол-во новотельн. коров/год
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Кол-во новотельн. коров для оценки
ReadyToMilkListViewModel.Title=Индекс Ready2Milk™
ReadyToMilkMasterViewModel.Charts=Графики
ReadyToMilkMasterViewModel.Inputs=Вводные данные
ReadyToMilkMasterViewModel.MastitisNotPresent=Информация по маститу не введена
ReadyToMilkMasterViewModel.Outputs=Выходные данные
ReadyToMilkMasterViewModel.Title=Индекс Ready2Milk™
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Индекс Ready2Milk™
ReadyToMilkOutputViewModel.Title=Ready2Milk™ выходные данные
RecommendedTLCSettings=Рекомендуемые настройки TLC
RefreshTokenFailed=Сессия завершена, вам необходимо повторно войти в систему
Reggio_Calabria=Реггио Калабрия
Reggio_Emilia=Реггио Эмилия
RemovedAndMeasured=Удаляются и взвешиваются
RemovedOnly=Только удаляются
Report=Отчет
Report.Analysis.Type=Анализ тип
Report.Animal.Analysis=Анализ животных
Report.Animal.Tag.Name=Номер животного
Report.AvgRumenFillScore=Средний балл заполненности рубца
Report.BCS.EvalDataTitle=Расчетные данные оценки BCS
Report.BCS.LactationStages=Этапы лактации
Report.BCS.Max=Упитанность (макс.)
Report.BCS.MilkHeadDay=Молоко/HD/день
Report.BCSAvg=Ср. балл упитанности
Report.Bcs=Упитанность
Report.Bcs.ChartName=Оценка состояния тела - животное {0}
Report.Bcs.HerdAnalysis.ChartName=Упитанность vs молоко
Report.Bcs.Milk=Молоко
Report.Bcs.Min=Упитанность (мин.)
Report.Calving.Date=Отел
Report.Cargill.Report=Cargill Отчет
Report.Chewing=Жующих
Report.Chews=Жвачка
Report.CudChewing.EvalDataTitle=Расчетные данные о оценке CUD
Report.CudChewingPercentage=Жуют, % /
Report.CudChewingPercentage.Vs.LactStages=Жуют, % /
Report.EvalDataTitle=Расчетные данные оценки
Report.ForagePennState=Структурные корма Пенс. сито
Report.General.Comments=Общее Комментарии
Report.GoalCudChewingPercentage=Цель по жвачке, % /
Report.Heatstress.Dmi.Adjustment=Регулировка DMI
Report.Heatstress.Energy.Equivalent.Milk.Loss=Энергетический эквивалент потери молока ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Расчетное потребление сухого вещества ({0})
Report.Heatstress.Intake.Adjustment=Корректировка потребления
Report.Heatstress.Legend=Легенда
Report.Heatstress.Legends=Легенды
Report.Heatstress.Loss.Of.Energy.Consumed=Потеря потребляемой энергии (MCAL)
Report.Heatstress.Mild.Moderate.Stress=Слабый - умеренный стресс
Report.Heatstress.Mild.Moderate.Stress.Message=Дыхание превышает 75 ударов в минуту | Температура прямой кишки превышает 39 \\ U2103 (102,2 \\ U2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Потеря стоимости молока (в месяц) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Потеря стоимости молока (в день) ({0})
Report.Heatstress.Moderate.Severe.Stress=Умеренный - тяжелый стресс
Report.Heatstress.Moderate.Severe.Stress.Message=Дыхание превышает 85 ударов в минуту | Температура прямой кишки превышает 40 \\ U2103 (104 \\ U2109)
Report.Heatstress.Reduction.In.Dmi=Сокращение ПСВ ({0})
Report.Heatstress.Relative.Humidity=Относительная влажность (%)
Report.Heatstress.Severe.Stress=Тяжелый стресс
Report.Heatstress.Severe.Stress.Message=Дыхание превышает 120-140 ударов в минуту | Температура прямой кишки превышает 41 \\ U2103 (106 \\ U2109)
Report.Heatstress.Stress.Threshold=Порог стресса
Report.Heatstress.Stress.Threshold.Message=Дыхание превышает 60 ударов в минуту | Потери резо обнаруживаются | Температура прямой кишки превышает 38,5 \\ U2103 (101,3 \\ U2109)
Report.Heatstress.Temperature=Температура
Report.Heatstress.Temperature.In.Celcius=Температура \\ U2103
Report.Heatstress.Temperature.In.Farenhiet=Температура \\ U2109
Report.Heatstress.TemperatureHumidityIndex=Индекс влажности температуры
Report.Herd.Analysis.CudChewingPercentage=Жуют, % /
Report.Locomotion.HerdAnalysis.ChartName=Оценка походки в процентах
Report.LocomotionScore.X.Axis=Оценка хромоты
Report.LocomotionScore.Y.Axis=Процент %
Report.LocomotionScore.chartName=Оценка походки- животное {0}
Report.No.OfChews=Кол-во жев. движ.
Report.No.OfChewsPerRegurgitation=Количество жеваний за цикл
Report.NoOfChews.Vs.LactStages=Кол-во жев. движ.
Report.Not.Chewing=Не жую
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Время, доступн.для отдыха
Report.PenTimeBudget.TimeAvailableForResting.Label=Часов
Report.PenTimeBudgetTimeRemaining=Остаток времени
Report.PenTimeBudgetTimeRequired=Требующееся время
Report.Pentime.Budget.Hours=Часы
Report.PercentageOnScreen=Процент на сите (%)
Report.RumenHealthManureScreening.Bottom=Нижнее
Report.RumenHealthManureScreening.BottomGoalMax=Нижнее цель макс
Report.RumenHealthManureScreening.BottomGoalMin=Нижнее цель мин
Report.RumenHealthManureScreening.Middle=Середина
Report.RumenHealthManureScreening.MiddleGoalMax=Среднее цель Макс
Report.RumenHealthManureScreening.MiddleGoalMin=Среднее цель мин
Report.RumenHealthManureScreening.Top=Верхнее
Report.RumenHealthManureScreening.TopGoalMax=Верхнее цель Макс
Report.RumenHealthManureScreening.TopGoalMin=Верхнее цель мин
Report.SheetName=Отчет Мастер
Report.Tool.Details=Инструменты Детали
Report.Tool.Name=Название инструмента
Report.Visit.Date=Дата визита
Report.Visit.Report=Отчет о посещении
Report.Visit.name=Имя визита
Report.goalChews=Цель по жвачке
Report.locomotionScore.Pen.Analysis.ChartName=Категория походки vs дата посещения
ReportDate=Дата отчета
ReportPDFNote=Заметка\:  
Reset_Database=Сбросить базу данных
Resources=Ресурсы
ResourcesViewModel.Title=Аудит структурных кормов - ресурсы
ResourcesViewModel.VisitNotebook=Блокнот посещения
RetainedPlacenta=Задержание последа
Reunion=Воссоединение
Revenue=Доход
RevenueEditComparisonValuesViewModel.CurrentSCC=Текущий уровень сом. клеток (кл/{0})
RevenueEditComparisonValuesViewModel.DownResponse=Эффект ({0}/гол/день)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Редактировать значения для сравнения
RevenueEditComparisonValuesViewModel.MilkChange=Изменения по молоку (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Цена молока ($/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Мол. продуктивность
RevenueEditComparisonValuesViewModel.NumOfCows=Количество коров
RevenueEditComparisonValuesViewModel.PotentialSCC=Потенц.уровень сом. клеток (кл/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Сценарий 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Сценарий 2
RevenueEditComparisonValuesViewModel.Title=Редактировать значения для сравнения
RevenueInputViewModel.ComparisonValues=Сравнение значений
RevenueInputViewModel.Edit=Редактировать
RevenueInputViewModel.ScenarioOne=Сценарий 1
RevenueInputViewModel.ScenarioTwo=Сценарий 2
RevenueInputViewModel.Title=Сравнение процедуры доения – вводные данные
RevenueLossDay=Потеря дохода ({0}/день)
RevenueLossYear=Потеря дохода ({0}/год)
Rheinland=Рейнланд
Rhode_Island=Род-Айленд
Rieti=Rieti
Rimini=Римини
Rio_Grande_do_Norte=Большая северная река
Rio_Grande_do_Sul=Rio Grande do Sul
Rio_de_Janeiro=Рио де Жанейро
Robot=Робот
RoboticMilkEvaluationSpinnerViewModel.Title=Оценка роботизированного доения
Romania=Румыния
Rome=Рим
RondÃ´nia=Ронда
Roraima=Рорайма
Roscommon=Роскоммон
Rovigo=Ровиго
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Анализ стада
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Анализ секции
RumenHealthBodyConditionLandingViewModel.Pens=Секции
RumenHealthBodyConditionLandingViewModel.Resources=Ресурсы
RumenHealthBodyConditionLandingViewModel.Title=Оценка упитанности
RumenHealthEditManureScoresViewModel.Close=Закрыть
RumenHealthEditManureScoresViewModel.Count=Подсчет
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Подсчитайте количество коров
RumenHealthEditManureScoresViewModel.NumOfCows=Количество коров
RumenHealthEditManureScoresViewModel.NumberOfCows=Количество коров
RumenHealthEditManureScoresViewModel.VisitNotebook=Блокнот посещения
RumenHealthLandingViewModel.HerdAnalysis=Анализ стада
RumenHealthLandingViewModel.PenAnalysis=Анализ секции
RumenHealthLandingViewModel.Pens=Секции
RumenHealthLandingViewModel.Title=Здоровье рубца - жвачка
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Анализ стада
RumenHealthLocomotionLandingViewModel.PenAnalysis=Анализ секции
RumenHealthLocomotionLandingViewModel.Pens=Секции
RumenHealthLocomotionLandingViewModel.Resources=Ресурсы
RumenHealthLocomotionLandingViewModel.Title=Походка
RumenHealthManureLandingViewModel.HerdAnalysis=Анализ стада
RumenHealthManureLandingViewModel.PenAnalysis=Анализ секции
RumenHealthManureLandingViewModel.Pens=Секции
RumenHealthManureLandingViewModel.Resources=Ресурсы
RumenHealthManureLandingViewModel.Title=Здоровье рубца - оценка навоза
RumenHealthManureMasterViewModel.Inputs=Вводные данные
RumenHealthManureMasterViewModel.Results=Результаты
RumenHealthManureMasterViewModel.RumenHealthManureScore=Здоровье рубца - оценка навоза
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Здоровье рубца - оценка навоза
RumenHealthManureMasterViewModel.VisitNotebook=Блокнот посещения
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Ср.балл
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Дата
RumenHealthManureScoresResultsViewModel.PercentPen=Процент от секции (%)
RumenHealthManureScoresResultsViewModel.SelectedDates=Выбрать даты
RumenHealthManureScoresResultsViewModel.Title=Рузультаты оценки навоза
RumenHealthManureScoresViewModel.AnimalsObserved=Наблюдаемых животных
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Ср.балл оценки навоза
RumenHealthManureScoresViewModel.Edit=Редактировать
RumenHealthManureScoresViewModel.ManureScore=Оценка навоза
RumenHealthManureScoresViewModel.PercentOfPen=Процент от секции (%)
RumenHealthManureScoresViewModel.ScoreCategory=Категория оценки навоза
RumenHealthManureScoresViewModel.StdDevCalculated=Стандартное отклонение 
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=Калькуляторы жвачки
RumenHealthPenCudCalculatorViewModel.CudChewing=Жевание жвачки
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Зафиксировать количество коров, жующих жвачку
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Количество жевательных движений
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Зафиксировать количество жевательных движений на голову
RumenHealthTMRLandingViewModel.HerdAnalysis=Анализ стада
RumenHealthTMRLandingViewModel.PenAnalysis=Анализ секции
RumenHealthTMRLandingViewModel.Pens=Секции
RumenHealthTMRLandingViewModel.Title=Здоровье рубца - оценка частиц ПСР
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Введите данные с весов (г)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Введите вес тары (г)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Цель - макс. (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Цель - мин. (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Цели
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Среднее 1 (8 мм)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Среднее 2 (1.18 мм)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Среднее 2 (4 мм)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Название оцениваемого ПСР
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=Оценка размера частиц ПСР
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Тара сита
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Введите данные с весов
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Верхнее (19 мм)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Поддон
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Добавить результаты оценки ПСР
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Средняя оценка
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Текущее
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Кол-во на весах (г)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Цель - макс. (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Цель (среднее 1 - 8мм)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Цель (среднее 2 - 1.18мм)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Цель (среднее 2 - 4мм)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Цель - мин. (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Цели
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Цель (верхнее 19мм)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Цель (поддон)
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Макс.
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Среднее 1 (8 мм)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Среднее 2
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Среднее 2 (4 мм)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Мин.
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Оценка частиц (% на сите)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Процент на сите (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Стандартное отклонение 
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Информация по оценке размера частиц ПСР
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=Оценка размера частиц ПСР
RumenHealthTMRParticleScorePenTableInputViewModel.Title=Оценка частиц ПСР
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Верхнее (19 мм)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Поддон
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Вводные данные
RumenHealthTMRPenScorerTableMasterViewModel.Results=Результаты
RumenHealthTMRPenScorerTableMasterViewModel.Title=Оценка размера частиц
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Нет выбранных
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Для просмотра секции сначала нужно выбрать тип сита
RumenHealthTMRSelectPenViewModel.SelectPen=Секции (только лактирующие и сухостойные)
RumenHealthTMRSelectPenViewModel.SelectScorer=Выбрать счетчик
RumenHealthTMRSelectPenViewModel.Title=Оценка размера частиц
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Нет выбранных
RumenHealthTMRSelectScorerViewModel.FooterText=ТОЛЬКО один тип сита может быть использован при посещении. Смена сита приведет к потере значений
RumenHealthTMRSelectScorerViewModel.SelectScorer=Выбрать сито
RumenHealthTMRSelectScorerViewModel.Title=Здоровье рубца - оценка размера частиц
Russia=Россия
Russian_Federation=Российская Федерация
Rwanda=Руанда
SAR=Саудовская Аравия (﷼ SAR)
SCCPremiumDeduction=Надбавка/вычет за соматику ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Суринам ($ SRD)
Saint_Barthélemy=Сен-Бартелеми
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Святая Елена, Вознесение и Тристан да Кунья
Saint_Kitts_and_Nevis=Сент-Китс и Невис
Saint_Lucia=Санкт-Люсия
Saint_Martin_(French_part)=Святой Мартин (французская часть)
Saint_Pierre_and_Miquelon=Сент -Пьер и Микелон
Saint_Vincent_and_the_Grenadines=Святой Винсент и Гренадины
Salerno=Салерно
Samoa=Самоа
San_Luis_Potosí=Сан Луис Потос
San_Marino=Сан -Марино
Santa_Catarina=Санта -Катарина
Sao_Tome_and_Principe=Sao Tome и Principe
Saskatchewan=Саскачеван
Sassari=Сассари
SaudiArabia=Саудовская Аравия
Saudi_Arabia=Саудовская Аравия
Savona=Савона
Schleswig=Шлезвиг
ScorecardPrompt=Пожалуйста завершите следующий осмотр, чтобы увидеть оценку {0}
Screen=Трехсекционный
ScreenNew=секционный новый
ScreenOld=-секционный старый
Search=Поиск
SegmentViewModel.SegmentTitle=Выбрать сегмент
SegmentViewModel.Title=Детали
Select=Выбрать
SelectCurrencyViewModel.Title=Валюта
SelectDates=Выбрать даты
SelectFeedingSystemViewModel.SelectFeedingSystem=Выбрать систему кормления
SelectFeedingSystemViewModel.Title=Установки секции
SelectForageImprovement=Выберите не более 12 направлений для улучшений из списка
SelectHousingSystemViewModel.SelectHousingSystem=Выбор системы содержания
SelectHousingSystemViewModel.Title=Установки секции
SelectImprovement=Выберите не более 10 направлений для улучшения из списка
SelectMatrix=Выбрать матрицу
SelectMilkingSystemViewModel.NoneSelected=Нет выбранных
SelectMilkingSystemViewModel.SelectMilkingSystem=Выбрать систему доения
SelectMilkingSystemViewModel.Title=Настройки сайта
SelectOnlyThreeNotes=Выберите не более 3 заметок в разделе
SelectOnlyTwoNotes=Выберите не более 2 заметок для каждого раздела
SelectPen=Выберите рацион для новой секции
SelectProcessor=Выбор переработчика
SelectProcessorViewModel.COMPONENT=Компонент
SelectProcessorViewModel.CONCENTRATION=Концентрация
SelectProcessorViewModel.Edit=Редактировать
SelectProcessorViewModel.MilkProcessors=Переработчики молока
SelectVisitComparison=Выберите посещения для сравнения
SemiAnnually=Раз в полгода
Semiconfinamento=Semiconfinamento
Send=Отправить
Senegal=Сенегал
Seoul=Сеул
Serbia=Сербия
Sergipe=Сергипе
SettingsViewModel.Imperial=Имперская
SettingsViewModel.Metric=Метрическая
SettingsViewModel.Milk_Processor_Set_Up=Установить переработчика молока
SettingsViewModel.More_Settings=Дополнительные настройки
SettingsViewModel.Select_Unit_Of_Measure=Выбрать единицы измерения
Severe=Сильный
Seychelles=Сейшельские острова
Shaanxi=Shaanxi
Shandong=Шаньдун
Shanghai=Шанхай
Shanxi=Шаньси
ShortDryPeriod=Короткий сухостойный период
ShowEulaViewModel.Accept=Принять
ShowEulaViewModel.ConfirmationNo=Нет
ShowEulaViewModel.ConfirmationText=Вы принимаете Лицензионное соглашение конечного пользвателя мобильного приложения?
ShowEulaViewModel.ConfirmationTitle=Подтверждение
ShowEulaViewModel.ConfirmationYes=Да
ShowEulaViewModel.Decline=Отклонить
ShowEulaViewModel.Eula=ЛСКП
ShowEulaViewModel.EulaError=Лицензионное соглашение конечного пользователя не может быть отображено. Проверьте подключение к сети и попробуйте снова
ShowEulaViewModel.EulaScreenTitle=Лицензия конечного пользователя
ShowPrivacyStatementViewModel.PrivacyStatement=&lt;p&gt;&lt;b&gt;Заявление о конфиденциальности&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Последнее обновление\: 3 января 2017&lt;/p&gt;&lt;p&gt;&lt;b&gt;Scope&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Cargill, Incorporated (“Cargill” или “Мы”) осуществляет сбор вашей информации во время использования вами мобильного приложения  (“App”), предназначенного для консультантов, предлагающих сервис Dairy Enteligen™ от имени Cargill.&lt;/p&gt;&lt;p&gt;&lt;b&gt;Персональная информация&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Cargill может осуществлять сбор персональной информации непосредственно от вас, когда вы регистрируетесь у нас и когда вы используете приложение&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Ваше имя&lt;/b&gt;&lt;/p&gt;&lt;p&gt;· &lt;b&gt;ваше местоположение&lt;/b&gt; (с помощью GPS или других аналогичных технологий)&lt;/p&gt;&lt;p&gt;· &lt;b&gt;Ваши фотографии &lt;/b&gt;or&lt;b&gt; Видео&lt;/b&gt; (если вы делитесь ими с Cargill через приложение)&lt;/p&gt;&lt;p&gt; для сбора персональной информации Cargill может ипользовать в приложении общедоступные технологии, такие как cookies и beacons,&lt;/p&gt;&lt;p&gt;&lt;b&gt;Использование и распространение – бизнес-контекст&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Our&lt;a href\="http\://www.cargill.com/privacy/business-notice/index.jsp"&gt;бизнес-информация&lt;/a&gt; описание того, как мы используем вашу персональную информацию в бизнес-контексте&lt;/p&gt;&lt;p&gt;&lt;b&gt;соглашении о данных местоположения&lt;/b&gt;&lt;/p&gt;&lt;p&gt;используя приложение, вы даете согласие Cargill осуществлять сбор информации о вашем местоположении в режиме реального времени и освобождаете Cargill от  ответственности по претензиям, повлекшим за собой действия или убытки, возникшие по причине использования вами приложения или в любом другом случае, связанном с использованием данных о местоположении.&lt;/p&gt;
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Заявление о конфиденциальности
ShowSyncStatusViewModel.GetAccounts=Получено аккаунтов\: 
ShowSyncStatusViewModel.GetNotes=Получено заметок\: 
ShowSyncStatusViewModel.GetVisits=Получено посещений\: 
ShowSyncStatusViewModel.Title=Сводка по синхронизации данных
Sichuan=СИЧУАН
Siena=Сиена
Sierra_Leone=Сьерра-Леоне
Sikkim=Сикким
SilageBags=Силосные рукава
SilageBags_BagsPlacedOnStableWellManagedSurface=Рукава размещены на ровной и твердой поверхности (асфальт или бетон)
SilageBags_BonusSecureCoverIsUsed=Используется ли защитное покрытие?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Срез корма чистый и ровный, нет опавшего корма и признаков согревания
SilageBags_FaceRemovalRate=Объем срезаемого корма (потребление)
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Регулярно ли силосные рукава осматриваются на наличие повреждений от вредителей и ставятся ли заплатки?
SilageBags_PorosityScoresConsistently=Плотность трамбовки корма (%СВ/м²))
SilageBags_TrashVegRodentControlledAroundBags=Ведется ли контроль за наличием грызунов, растительности и мусора вокруг?
SilagePrevention1st=Профилактика порчи силоса\: в первую очередь то, что нужно в первую очередь
Sinaloa=Синалоа
Singapore=Сингапур
Sint_Maarten_(Dutch_part)=Синт Март (голландская часть)
Site-Not-Synced-To-Lift=Сайт не синхрозирован с LIFT; пожалуйста обратитесь к своему администратору
SiteDetailViewModel.AnimalInputsSite=Ввод данных по животным сайта
SiteDetailViewModel.DairyEnteligenReport=Dairy Enteligen отчет
SiteDetailViewModel.Detailed=Детальный
SiteDetailViewModel.DietInputsSiteLactating=Ввод данных по рациону (лактирующие коровы)
SiteDetailViewModel.DownloadingVisit=Загрузка посещения…
SiteDetailViewModel.GeneralCustomerSiteSetup=Общие настройки сайта клиента
SiteDetailViewModel.GetReportMsg=Загрузка отчета
SiteDetailViewModel.MainHeading=Посещения
SiteDetailViewModel.NetworkErrorMessage=Нет доступной сети
SiteDetailViewModel.NetworkErrorMessageTitle=Ошибка сети
SiteDetailViewModel.NewVisit=Начать новое посещение
SiteDetailViewModel.ReportDownloadTimeout=Невозможно загрузить файл, проверьте подключение к сети и попробуйте снова
SiteDetailViewModel.ReportNotAvailable=Отчет не доступен для загрузки
SiteDetailViewModel.ReportNotAvailableTitle=Статус
SiteDetailViewModel.Reports=Отчет Dairy Enteligen
SiteDetailViewModel.Resources=Ресурсы
SiteDetailViewModel.SiteSetup=Настройка сайта
SiteDetailViewModel.Summary=Резюме
SiteDetailViewModel.Title=Детали сайта
SiteDetailViewModel.VisitDownloadPrompt=Посещение не загружено. Хотите попробовать загрузить это посещение?
SiteDetailViewModel.VisitNotDownloaded=Посещение не загружено
SiteDetailViewModel.VisitUnavailable=Дата посещения не доступна
SiteDetailsResourcesViewModel.Title=Ресурсы
SiteDetailsSetupViewModel.AnimalInputsSite=Сайт с данными по животным
SiteDetailsSetupViewModel.AsFedIntake=Потребление корма на НВ  ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=Бактериальная обсемененность (1,000 кл/мл)
SiteDetailsSetupViewModel.Continue=Продолжение
SiteDetailsSetupViewModel.CurrentMilkPrice=Цена молока ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Дней лактации (ДЛ)
SiteDetailsSetupViewModel.Delete=Удалить
SiteDetailsSetupViewModel.DietInputsSiteLactating=Вводные данные по рациону (лактирующие)
SiteDetailsSetupViewModel.DietSetup=Установки рациона
SiteDetailsSetupViewModel.Diets=Рационы
SiteDetailsSetupViewModel.DryMatterIntake=ПСВ  ({0})
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Общие установки сайта клиента
SiteDetailsSetupViewModel.InfoSiteSetup=Добавьте новый сайт для каждого отделения фермы клиента. Чтобы начать посещение фермы и получить доступ к инструментам Dairy Enteligen,  создайте хотя бы 1 сайт
SiteDetailsSetupViewModel.LactatingAnimals=Лактирующие коровы
SiteDetailsSetupViewModel.MilkFatPercent=Жир молока, %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Другие компоненты молока, %
SiteDetailsSetupViewModel.MilkProteinPercent=Протеин молока, %
SiteDetailsSetupViewModel.MilkYield=Удой  ({0})
SiteDetailsSetupViewModel.MilkingSystem=Система доения
SiteDetailsSetupViewModel.NameNotUnique=Сайт с названием "{0}" уже существует. Введите уникальное название
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=NEL Dairy (Мкал/{0})
SiteDetailsSetupViewModel.NewSite=Новый сайт
SiteDetailsSetupViewModel.NullSiteName=Название сайта, цена молока, система доения и секция обязательны к заполнению. Вы хотите продолжить работу или удалить сайт?
SiteDetailsSetupViewModel.NumberOfStalls=Кол-во мест в доильн. зале
SiteDetailsSetupViewModel.PenSetup=Установки секции
SiteDetailsSetupViewModel.Pens=Клетки
SiteDetailsSetupViewModel.RationCost=Стоимость рациона на гол. ({0})
SiteDetailsSetupViewModel.SiteMandatoryFields=Название сайта, цена молока, система доения и секция - необходимые поля для заполения. Заполните все необходимые поля, чтобы продолжить работу
SiteDetailsSetupViewModel.SiteName=Название сайта
SiteDetailsSetupViewModel.SiteSetup=Настройки сайта
SiteDetailsSetupViewModel.SomaticCellCount=Сом. клетки молока (1,000 кл/мл)
SiteDetailsSetupViewModel.Title=Детали сайта
SiteDetailsSetupViewModel.WeightImperialCWT=хандредвейт
SiteVisitSummary=Резюме посещения сайта
SiteVisitSummaryReport=Краткий отчет по посещению сайта
SixToEightLayers=От 6 до 8 слоев
SixToTwelveHours=От 6 до 12 ч
SixToTwelveInches=От 15 до 30 см
Sligo=Слайго
Slovakia=Словакия
Slovenia=Словения
Solomon_Islands=Соломоновы острова
Somalia=Сомали
SomanticCellCount=Кол-во сом. клеток
SomaticCellPerML=Сом. клетки молока (кл/мл)
Sondrio=Сондрио
Sonora=Сонора
SouthAfrica=Южная Африка
SouthKorea=Корея (Южная)
South_Africa=Южная Африка
South_Australia=Южная Австралия
South_Carolina=Южная Каролина
South_Dakota=Северная Дакота
South_Georgia_and_the_South_Sandwich_Islands=Южная Грузия и Южные Сэндвич Острова
South_Sudan=южный Судан
Spain=Испания
Sri_Lanka=Шри -Ланка
StandardDeviationScoreTitle=Стандартное отклонение
StartDate=Дата начала
StatusArchived=Архивировано
StatusCompleted=Завершенный
StatusInProgress=В работе
StdDevCalculated=Стандартное отклонение
Steer=Кастрат
StorageCalculators=Подсчет кормов на хранении
StrategyToReduceDisplacedAbomasum=Смещение сычуга
StrategyToReduceDisplacedAbomasumDetails=<span style\="font-family\:Calibri,Calibrib;"><p><strong>Стратегия снижения риска возникновения</strong><strong> левостороннего смещения сычуга</strong></p><p>Профилактика основана на управлении факторами риска, т.к. не существует единственной причины.</p><p>Профилактика включает в себя соответствующее кормление и менеджмент, профилактику сопутствующих заболеваний\:</p><ul><li>Контролируйте факторы риска, связанные с кормлением\:<ul><li>избегайте высокой кондиции упитанности (идеальная упитанность 3.0 балла от запуска до отела);</li><li>обеспечение необходимого количества НДК из структурных кормов;</li><li>контроль за физическими параметрами рациона;</li><li>внимание к потребностям в минеральных веществах;</li><li>избегайте возникновения таких нарушений обмена веществ, как гипокальциемия, а также инфекционных заболеваний, которые снижают потребление кормов.</li></ul></li><li>Повысьте уровень менеджмента\:<ul><li> гарантированное потребление кормов новотельными животными, особенно в течение первых ч/дней после отела</li><li>соответствующий менеджмент кормового стола</li><li>повышение комфорта, снижение стресса</li></ul></li></ul></span>
StrategyToReduceDystocia=Трудный отел
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;p&gt;&lt;strong&gt;Стратегия снижения рисков возникновения трудного отела&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;Предотвратить трудный отел помогут\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;-осеменение телок в соответствующем возрасте и при правильной живой массе&lt;/li&gt;&lt;li&gt;-подбор производителя на основании показателя легкости отела&lt;/li&gt;&lt;li&gt;-обучение персонала правильному и своевременному родовспоможению, а также обращению со слабыми новорожденными телятами&lt;/li&gt;&lt;li&gt;-анализ текущих программ кормления согласно потребностям, указанным в системе MAX&lt;sup&gt;TM&lt;/sup&gt; для коров в период раннего и позднего сухостоя, а также нетелей с фокусом на\:&lt;ul&gt;&lt;li&gt;*уровень энергии для поддержания кондиции упитанности и роста плода&lt;/li&gt;&lt;li&gt;*предотвращение появления коров с высокой кондицией упитанности в период отела&lt;/li&gt;&lt;li&gt;*контроль за риском возникновения гипокальциемии в стаде&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=Стратегии на снижение количества случаев
StrategyToReduceKetosis=Кетоз
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;p&gt;&lt;strong&gt;Стратегия снижения риска возникновения кетоза&lt;/strong&gt;&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Убедитесь, что животные содержатся в комфортных условиях (чистая, сухая подстилка, нет теплового стресса, хорошая вентиляция, низкий уровень стресса, секция не переполнена и т.п.)&lt;/li&gt;&lt;li&gt; Сбалансируйте рационы согласно потребностям, установленным в системе MAX&lt;sup&gt;TM&lt;/sup&gt; по нутриентам и физическим характеристикам рациона. Порекомендуйте специальные продукты для транзитных коров для профилактики кетоза. Убедитесь, что используемые структурные корма хорошего качества, что способстует увеличению потребления рациона и обеспечивает рубец буфером.&lt;/li&gt;&lt;li&gt;Контролируйте изменения кондиций упитанности между запуском и отелом\: в период запуска оптимальная кондиция упитанности 3.0 балла, такую упитанность необходимо поддерживать на протяжении всего сухостойного периода, чтобы избежать избыточной мобилизации жира в период после отела.&lt;/li&gt;&lt;li&gt;Используйте специальный протокол для диагностики и предотвращения развития инфекционных заболеваний и нарушений обмена веществ. Контролируйте потребление кормов, наполненность рубца и жвачку.&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=Мастит
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;p&gt;&lt;strong&gt;Стратегия снижения риска возникновения маститов&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;Микроорганизмы, которые являются наиболее частой причиной возникновения мастита, делятся на две основные категории\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;контагиозные патогены, которые передаются от коровы к корове в основном во время доения &lt;em&gt;(&lt;/em&gt;&lt;em&gt;Strep. Agalactiae&amp;nbsp; и &amp;nbsp;Staph. Aureus).&lt;/em&gt;&lt;/li&gt;&lt;li&gt;патогены, которые происходят из окружающей корову среды &lt;em&gt;(&lt;/em&gt;&lt;em&gt;E. coli&amp;nbsp; и &amp;nbsp;Strep. Uberis).&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;В зависимости от того, какие бактерии стали причиной мастита, проводятся те или иные мероприятия.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Контроль за контагиозными патогенами&lt;/strong&gt;. В данном случае не существует волшебной серебряной пули против всех патогенов, но следующие шаги будут полезны\:&lt;ul&gt;&lt;li&gt;фокус на гигиене до, после и во время доения, включая обработку сосков.&lt;/li&gt;&lt;li&gt;доите инфицированных животных в последнюю очередь&lt;/li&gt;&lt;li&gt;регулярно проводите обслуживание доильного оборудования&lt;/li&gt;&lt;li&gt;анализируйте рационы кормления сухостойных коров согласно потребностям, указанным в системе MAX&lt;sup&gt;TM&lt;/sup&gt; , чтобы убедиться в обеспеченности нутриентами (энергия, антиоксиданты) для поддержания функции иммунитета.&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Контроль за патогенами из окружающей среды&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;обеспечьте животных чистой и сухой подстилкой, хорошей вентиляцией коровника&lt;/li&gt;&lt;li&gt;следите за плотностью размещения животных в секции&lt;/li&gt;&lt;li&gt;контролируйте общую гигиену вымени&lt;/li&gt;&lt;li&gt;соблюдайте рутину доения&lt;/li&gt;&lt;li&gt;анализируйте рационы кормления сухостойных коров согласно потребностям, указанным в системе MAX&lt;sup&gt;TM&lt;/sup&gt;, чтобы убедиться в обеспеченности нутриентами (энергия, антиоксиданты) для поддержания функции иммунитета.&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=Метрит
StrategyToReduceMetritisDetails=<span style\="font-family\:Calibri,Calibrib;"><p><strong>Стратегия снижения риска возникновения метритов</strong></p><p>К факторам риска метрита относятся\: задержание последа, повреждение родовых путей при трудном отеле, неподходящий протокол отела, плохая гигиена в секции, дефицит витамина Е и селена, а также высокая упитанность коров.</p><p>Контролируйте следующие моменты, начиная с наиболее важных\:</p><ol><li>Отел.<br /><ul><li>Не заносят ли сотрудники инфекцию в матку при родовспоможении?</li><li>Не  осуществляется ли родовспоможение слишком рано или слишком поздно?</li><li>Не слишком ли часто вытаскивают телят?</li></ul></li></ol><ol start\="2"><li>Лечение.<br /><ul><li>Как происходит лечение коров с задержанием последа или метритом?</li><li>Есть ли риск занесения инфекции из окружающей среды или влагалища в матку в это время?</li></ul></li></ol><ol start\="3"><li>Стресс.<br /><ul><li>Избыточный стресс перед отелом может понизить иммунитет, снижая сопротивление к любой инфекции после отела.</li></ul></li></ol><ol start\="4"><li>Кормление.<br /><ul><li>Проанализируйте рацион сухостойных коров. Сфокусируйтесь на контроле за упитанностью, балансе минеральных веществ и обеспеченностью антиоксидантами (витамины Е, А, селен, цинк и медь).</li></ul></li><li>Другие факторы риска (кетоз, смещение сычуга).</li></ol></span>
StrategyToReduceMilkFever=Родильный парез
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;p&gt;&lt;strong&gt;Стратегия снижения риска возникновения гипокальциемии&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;Существуют две альтернативные стратегии предотвращения возникновения гипокальциемии у коров, полностью основанные на менеджменте кормления.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Кормите сухостойных коров рационами с низким содержанием Са.&lt;/li&gt;&lt;li&gt;Кормите сухостойных коров рационами с низким показателем DCAD.&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;Что необходимо учитывать, чтобы держать гипокальциемию под контролем\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;проводите анализ структурных кормов на содержание минеральных веществ (кальций, фосфор, магний, калий, натрий, сера, хлор) в проверенных лабораториях&lt;/li&gt;&lt;li&gt;анализируйте текущие программы кормления по указанным  в системе MAX&lt;sup&gt;TM&lt;/sup&gt; потребностям коров в период позднего сухостоя, а также практику менеджмента кормления (особое внимание к неограниченному скармливанию структурных кормов, уровню калия в рационе, свободному доступу к минеральным подкормкам, сортировке кормов).&lt;/li&gt;&lt;li&gt;Используйте специальные продукты для транзитных коров, направленные на профилактику гипокальциемии.&lt;/li&gt;&lt;li&gt;При использовании рационов с DCAD\:&lt;ul&gt;&lt;li&gt;Отслеживайте потребление кормов, т.к. анионовые соли имеют низкие вкусовые качества и могут снизить потребление сухого вещества&lt;/li&gt;&lt;li&gt;контролируйте рН мочи для проверки эффективности изменений в рационе&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=Задержание последа
StrategyToReduceRetainedPlacentaDetails=<span style\="font-family\:Calibri,Calibrib;"><p><strong>Стратегия снижения риска возникновения метритов</strong></p><p>К факторам риска метрита относятся\: задержание последа, повреждение родовых путей при трудном отеле, неподходящий протокол отела, плохая гигиена в секции, дефицит витамина Е и селена, а также высокая упитанность коров.</p><p>Контролируйте следующие моменты, начиная с наиболее важных\:</p><ol><li>Отел.<br /><ul><li>Не заносят ли сотрудники инфекцию в матку при родовспоможении?</li><li>Не  осуществляется ли родовспоможение слишком рано или слишком поздно?</li><li>Не слишком ли часто вытаскивают телят?</li></ul></li></ol><ol start\="2"><li>Лечение.<br /><ul><li>Как происходит лечение коров с задержанием последа или метритом?</li><li>Есть ли риск занесения инфекции из окружающей среды или влагалища в матку в это время?</li></ul></li></ol><ol start\="3"><li>Стресс.<br /><ul><li>Избыточный стресс перед отелом может понизить иммунитет, снижая сопротивление к любой инфекции после отела.</li></ul></li></ol><ol start\="4"><li>Кормление.<br /><ul><li>Проанализируйте рацион сухостойных коров. Сфокусируйтесь на контроле за упитанностью, балансе минеральных веществ и обеспеченностью антиоксидантами (витамины Е, А, селен, цинк и медь).</li></ul></li><li>Другие факторы риска (кетоз, смещение сычуга).</li></ol></span>
Straw=Солома
Sudan=Судан
Surinam=Суринам
Suriname=Суринам
SurveyCategories=Категории осмотра
SurveyOfForages=Осмотр структурных кормов
SurveyOfForages_AnnualCowNumAndForageNeeds=Проводится ли планирование поголовья и потребностей в структурных кормах ежегодно?
SurveyOfForages_AshLevelsInCornSilage=Содержание золы в кукурузном силосе
SurveyOfForages_AshLevelsInHaylage=Содержание золы в сенаже
SurveyOfForages_AshLevelsInOtherSilage=Какой уровень золы в кукурузном силосе?
SurveyOfForages_ButyricAcidLevelsInHaylage=Содержание масляной кислоты в сенаже
SurveyOfForages_CornSilageProcessingScore=Длина резки стеблей в образце кукурузного силоса
SurveyOfForages_CornSilageScoreMonitored=Твердость зерна в кукурузном силосе
SurveyOfForages_InspectedForSpoilageAndMold=Все ли корма проверены на плесень и порчу, отброшены ли плохие корма?
SurveyOfForages_InventoryIsMonitored=Как часто проводят инвентаризацию кормов? 
SurveyOfForages_LacticAcidToAceticAcidLevels=Уровень молочной и уксусной кислоты
SurveyOfForages_LooseOrFacedFeedWithin=Осыпавшийся сверху или силос с лицевой стороны скармливается в течение\:
SurveyOfForages_NoLooseFeedRemaining=Остатки с кормового стола удаляются и взвешиваются ежедневно?
SurveyOfForages_SilosSizedForCapacity=Соответсвует ли вместимость хранилищ структурных кормов  потребностям молочного стада? (Не переполнены)
SurveyOfForages_VisibleSignsOfSoil=Есть ли видимые следы загрязнения силоса почвой?
Svalbard_and_Jan_Mayen=Svalbard и Jan Mayen
Swaziland=Свазиленд
Sweden=Швеция
Switzerland=Швейцария
Sync-failed-due-to-unknown-reason=Veuillez contacter l'administrateur pour résolution.
SyncFailed=Синхронизация не может быть завершена в данный момент, попробуйте снова
Sync_Data=Синхронизация данных
Syracuse=Сиракузы
Syrian_Arab_Republic=Сирийская Арабская Республика
SystemGenerated=Генерирование системы
São_Paulo=Сан -Паулу
THB=Таиланд (THB THB)
TMR=ПСР
TMRHerdAnalysisTableTitle=Оценка частиц ПСР - анализ стада
TMRParticleScore=Анализ оценки частиц ПСР
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Закрыть
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Дней лактации (ДЛ)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Редактировать кол-во ДЛ
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=ДЛ
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Редактировать
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4 мм)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18 мм)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Среднее 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8 мм)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Среднее 2
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Анализ размера частиц ПСР
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Верхнее
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19 мм)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Поддон
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Анализ стада
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Вводные данные
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Результаты
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Оценка размера частиц
TMRParticleScoreHerdAnalysisResultsText=Анализ размера частиц ПСР
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Среднее 1 (8 мм)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Среднее 2 (4 мм)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Верхнее (19 мм)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Поддон
TRY=TRY
TWD=Тайвань (NT$ TWD)
Tabasco=Табаско
Taipei_City=Город Тайбэй
Taiwan=Тайвань
Tajikistan=Таджикистан
Tamaulipas=Тамаулипас
Tamil_Nadu=Тамил Наду
Tanzania,_United_Republic_of=Танзания, Объединенная Республика
Taranto=Таранто
Task=Задача
Tasmania=Тасмания
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=Теннесси
Teramo=Терамо
Terni=Терни
Texas=Техас
TextureFeed=Текстурированный корм
Thailand=Таиланд
Thirdparty=Третья сторона
ThisVisit=(Это посещение)
ThreePotentialStorageSolutions=Три потенциальных решения для хранения
ThreeScreen=Трехсекционный
ThreeTimesPerWeek=3 раза в неделю
Tianjin=Тяньцзинь
Tiestall=Привязное содержание
TimeRemaining=Время, доступн.для отдыха
Timor-Leste=Тимор читал
Tipperary=Типперэри
Tlaxcala=Tlaxcala
Tocantins=Токантины
Togo=Идти
Tokelau=Токелау
Tokyo=Токио
Tonga=Приехал
TonsAF=тонн НВ
TonsAFSilo=тонн НВ (осталось в хранилище)
TonsDM=тонн СВ
TonsDMSilo=тонн СВ (осталось в хранилище)
TonsPerDay=тонн/день
ToolNotSelected=Требуется выбрать хотя бы один иструмент
Top=Верхнее
TopUnloadingSilo=Башня (верхняя выгрузка)
TopUnloadingSilos=Башня (верхняя выгрузка)
TopValue=(19 мм)
Total=Общее
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=Общее кол-во животных в секции
TotalAnimalsHerd=Общее количество животных
TotalPenPerScore=Общее кол-во животных в секции для оценки
TotalRevenue=Общий доход
TowerSilos=Силосная башня
TowerSilos_FaceRemovalRateGreaterThan4Inches=Ежедневно срезают корм на 10 см вглубь и больше?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Укрыт ли силос после завершения закладки, если не используется сразу же? 
TowerSilos_SiloFillingTimeof3DaysOrLess=Башня, траншея или курган заполняется за 3 дня или меньше
TransitionCow=Транзитные коровы
Trapani=Трапани
Tray=Поддон
Trends=Позитивные тенденции
Trento=Тренто
Treviso=Тревизо
Trieste=Триест
Trinidad_and_Tobago=Тринидад и Тобаго
Tripura=Трипура
Tunisia=Тунис
Turin=Турин
Turkey=Турция
Turkmenistan=Туркменистан
Turks_and_Caicos_Islands=Турки и острова Кайкос
Tuvalu=Тувалу
TwelveInchesOrGreater=30 см и более
TwentyFourHoursAndNoSync=Двадцать четыре часа и не синхронизировать
TwentyFourHoursBeforeActionIsDue=За двадцать за четыре часа до действия
TwentyFourToThirtySixInchesPerDay=От 60 до 90 см в день
TwicePerWeek=Дважды в неделю
UAH=Украина (UAH UAH)
UK=Соединенное Королевство
UNITED_STATES=Соединенные ШтатыS
US=Соединенные Штаты Америки
USD=Соединенные Штаты Америки ($ USD)
Udine=Удин
Uganda=Уганда
Ukraine=Украина
UnitedKingdom=Соединенное Королевство
UnitedStates=Соединенные Штаты Америки
United_Arab_Emirates=Объединенные Арабские Эмираты
United_Kingdom=Великобритания
United_States=Соединенные Штаты
UrinePH=pH мочи
UrinePHAVG=Ср. pН мочи
UrinePHAverageNumber=Средний показатель
UrinePHDensity=рН мочи - ресурсы
UrinePHEditCowViewModel.AddNew=Следующая корова
UrinePHEditCowViewModel.CowName=Номер коровы
UrinePHEditCowViewModel.CowValue=рН
UrinePHEditCowViewModel.UrinePHEnterCowValue=Введите рН 
UrinePHEditCowViewModel.ValidCudInput=Введите полученные данные
UrinePHEditGoalViewModel.GoalMax=Цель - макс.
UrinePHEditGoalViewModel.GoalMin=Цель - мин.
UrinePHEditGoalViewModel.TargetUrinePHRange=Целевой диапазон рН мочи
UrinePHEditGoalViewModel.Title=Редактировать цель
UrinePHInputsViewModel.AddNew=Добавить 
UrinePHInputsViewModel.CalculatorHeading=Выберите корову для ввода рН мочи. Нажмите "Добавить", чтобы добавить корову
UrinePHInputsViewModel.CoefficientVariation=Коэф. вариации (КВ,%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Коровы вне диапазона (%)
UrinePHInputsViewModel.CudChewCategorySection=Коровы
UrinePHInputsViewModel.DietDCAD=DCAD рациона, мЭкв/100 г
UrinePHInputsViewModel.Resources=Ресурсы
UrinePHInputsViewModel.TargetUrinePHRange=Целевой диапазон рН мочи
UrinePHInputsViewModel.UrinePHAVG=Ср. рН мочи
UrinePHInputsViewModel.UrinePhSTDDEV=Станд. отклонение
UrinePHMasterViewModel.Inputs=Вводные данные
UrinePHMasterViewModel.Results=Результаты
UrinePHMasterViewModel.Title=рН мочи
UrinePHPenSelectionViewModel.Title=pH мочи
UrinePHPenSelectionViewModel.UrinePHPenList=Секции (только дойные и сухостой)
UrinePHResultsViewModel.DietDCAD=DCAD Рациона, мЭкв/ 100 г
UrinePHResultsViewModel.MaxpH=Maкс. pH
UrinePHResultsViewModel.MinpH=Мин. pH
UrinePHResultsViewModel.UrinePHAVG=Ср. pН мочи
Uruguay=Уругвай
UserCreated=Пользователь создан
UserPreferencesViewModel.Branding=Бренд
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=Выбор валюты
UserPreferencesViewModel.Imperial=Имперская
UserPreferencesViewModel.MainHeading=Следующие опции могут быть изменены позже в настройках приложения
UserPreferencesViewModel.Metric=Метрическая
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Выбрать валюту
UserPreferencesViewModel.SelectPointScale=Выбрать шкалу оценки упитанности
UserPreferencesViewModel.Title=Настройки пользователя
UserPreferencesViewModel.UnitOfMeasure=Выбрать единицы измерения
UserPreferencesViewModel.UserPreferencesMilkProcessor=Установки переработчика молока
UserPreferencesViewModel.UserPreferencesMoreSettings=Дополнительные настройки
User_Settings=Настройки пользователя
Utah=Юта
Uttar_Pradesh=Уттар -Прадеш
Uttarakhand=Уттаракханд
Uzbekistan=Узбекистан
VEF=Венесуэла (Bs VEF)
VND=Вьетнам (₫ VND)
Vanuatu=Вануату
Varese=Варизе
Venezuela=Венесуэла
Venezuela,_Bolivarian_Republic_of=Венесуэла, Боливарианская Республика
Venice=Венеция
Veracruz=Веракрус
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Верцелли
Vermont=Вермонт
Verona=Верона
Vestland=Вестленд
Vibo_Valentia=Vibo Valentia
Vicenza=Викенца
Victoria=Виктория
Viet_Nam=Вьетнам
Vietnam=Вьетнам
ViewOverallCalfHaiferScore=Посмотреть общую оценку по телятам&amp;телкам
ViewOverallForageScore=Посмотреть общую оценку структурных кормов
Virgin_Islands,_British=Виргинские острова, британские
Virginia=Вирджиния
Visit.Report.Footer.Patent=Компания Cargill Incorporated, ее материнские компании и дочерние компании не гарантируют точность этих оценок по причине многих факторов. Нет никаких гарантий производственных и финансовых результатов. © Cargill, Incorporated, 2023. Все права защищены.
VisitAutoPublished=Посетите Auto опубликовано
VisitDate=Дата посещения
VisitDownloadProceed=Продолжить
VisitNotebook=Блокнот посещения
VisitNotesViewModel.Action=Действие
VisitNotesViewModel.Close=Закрыть
VisitNotesViewModel.DownloadingNotes=Загрузка заметок...
VisitNotesViewModel.Event=Событие
VisitNotesViewModel.New=Новый
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=Наблюдение
VisitNotesViewModel.Task=Задача
VisitNotesViewModel.Title=Блокнот посещения
VisitNotesViewModel.VisitNotebook=Блокнот посещения
VisitSummaryViewModel.CalfHeiferItem=Телята&amp;телки
VisitSummaryViewModel.CalfHeiferScorecard=Оценка
VisitSummaryViewModel.CategorySection=Категории инструментов
VisitSummaryViewModel.ComfortHeatStressBanner=Оценка теплового стресса секции
VisitSummaryViewModel.ComfortItem=Комфорт
VisitSummaryViewModel.EmailReport=E-mail отчет
VisitSummaryViewModel.FreeFormReport=Свободная форма отчета
VisitSummaryViewModel.HealthItem=Здоровье
VisitSummaryViewModel.HeatstressEvaluationTitle=Оценка теплового стресса
VisitSummaryViewModel.HerdAnalysis=Анализ стада
VisitSummaryViewModel.InputsOutputsChart=Вводные/выходные данные/графики
VisitSummaryViewModel.MilkProcessRevenueCalculator=Сравнение процедуры доения
VisitSummaryViewModel.NoToolPrompt=Нет ни одного заполненного раздела
VisitSummaryViewModel.NutritionForage=Аудит структурных кормов
VisitSummaryViewModel.NutritionItem=Кормление
VisitSummaryViewModel.NutritionPile=Инвентаризация структурных кормов
VisitSummaryViewModel.PenTimeTitle=Бюджет времени секции
VisitSummaryViewModel.ProductivityItem=Продуктивность
VisitSummaryViewModel.RumenHealthBodyConditionTitle=Оценка упитанности
VisitSummaryViewModel.RumenHealthLocomotionTitle=Оценка походки
VisitSummaryViewModel.RumenHealthManureTitle=Здоровье рубца - оценка навоза
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Нарушения обмена веществ
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
VisitSummaryViewModel.RumenHealthTMRTitle=Здоровье рубца - оценка ПСР
VisitSummaryViewModel.RumenHealthTitle=Здоровье рубца - жвачка
VisitSummaryViewModel.RumenHealthUrinePHTitle=pH мочи
VisitSummaryViewModel.Title=Резюме посещения сайта
VisitSummaryViewModel.VisitSummaryMilkCalc=Вводные данные/результаты/ресурсы
VisitSummaryViewModel.VisitTitle=Название посещения
VisitViewModel.CalfHeiferItem=Телята&amp;телки
VisitViewModel.CategorySection=Категории инструментов
VisitViewModel.ComfortItem=Комфорт
VisitViewModel.Delete=Удалить посещение
VisitViewModel.DeletePrompt=Вы уверены, что хотите удалить посещение? Это действие нельзя будет отменить
VisitViewModel.HealthItem=Здоровье
VisitViewModel.Instructions=Выберите категорию или отчет из списка ниже
VisitViewModel.NullVisitName=Название посещения не может быть пустым. Введите название посещения
VisitViewModel.NutritionItem=Кормление
VisitViewModel.ProductivityItem=Продуктивность
VisitViewModel.Publish=Опубликовать
VisitViewModel.PublishNotes=Опубликовать заметки
VisitViewModel.PublishNotesPrompt=Вы уверены, что хотите опубликовать заметки по посещению? Публикацию нельзя будет отменить.
VisitViewModel.PublishPrompt=Вы уверены, что хотите опубликовать посещение? Это действие нельзя будет отменить
VisitViewModel.PublishVisit=Опубликовать посещение
VisitViewModel.SiteVisitSummary=Резюме посещения сайта
VisitViewModel.Title=Детали посещения
VisitViewModel.ToolCategories=Категории инструментов
VisitViewModel.VisitNotebook=Блокнот посещения
VisitViewModel.VisitTitle=Название посещения
VisitViewModel.WalkthroughReport=Отчет обхода
Viterbo=Витербо
VolumeImperial=гал
VolumeMetric=мл
WalkthroughPenSelectionViewModel.Pens=Секции
WalkthroughPenSelectionViewModel.Title=Отчет обхода
WalkthroughReport=Отчет обхода
WalkthroughReportHerdAnalysisViewModel.Appearance=Внешний вид
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Чистота подстилки
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Подстилка\: глубина и мягкость
WalkthroughReportHerdAnalysisViewModel.Branding=Бренд
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Комфорт коров, % лежащих животных
WalkthroughReportHerdAnalysisViewModel.Comments=Комментарии
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Оценка жвачки, кол-во жев. движ./ жвачку
WalkthroughReportHerdAnalysisViewModel.CudChewing=Руминация, % жующих
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} Отчет
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} Отчет
WalkthroughReportHerdAnalysisViewModel.ExportSelected=Отправка по e-mail выбранных нструментов
WalkthroughReportHerdAnalysisViewModel.FinalObservations=Финальные наблюдения
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Формирование отчета...
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Повреждения скакательных суставов, % животных
WalkthroughReportHerdAnalysisViewModel.MainHeading=Отчет обхода
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Выделения из носа, % животных
WalkthroughReportHerdAnalysisViewModel.Notes=Заметки
WalkthroughReportHerdAnalysisViewModel.Opportunities=Возможности
WalkthroughReportHerdAnalysisViewModel.PensForExport=Секции для экспорта
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Заполненность рубца
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=Оценка упитанности
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Оценка походки
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Оценка навоза
WalkthroughReportHerdAnalysisViewModel.SubHeading=Анализ стада
WalkthroughReportHerdAnalysisViewModel.Title=Отчет обхода - анализ стада
WalkthroughReportHerdAnalysisViewModel.Trends=Позитивные тенденции
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Выделения из матки, % животных
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Качество воды
WalkthroughReportLandingViewModel.HerdAnalysis=Анализ стада
WalkthroughReportLandingViewModel.PenAnalysis=Анализ секции
WalkthroughReportLandingViewModel.Title=Отчет обхода
WalkthroughReportQualityViewModel.BeddingCleanliness=Оценить чистоту подстилки
WalkthroughReportQualityViewModel.Clean=Чисто
WalkthroughReportQualityViewModel.Dirty=Грязно
WalkthroughReportQualityViewModel.ModeratelyClean=Умеренно чисто
WalkthroughReportQualityViewModel.Title=Отчет обхода
WalkthroughReportQualityViewModel.WaterQuality=Оценить качество воды
WalkthroughReportViewModel.Appearance=Внешний вид
WalkthroughReportViewModel.BeddingCleanliness=Чистота подстилки
WalkthroughReportViewModel.BeddingDepthSoft=Подстилка\: глубина и мягкость
WalkthroughReportViewModel.Clean=Чисто
WalkthroughReportViewModel.ComfortItem=Комфорт коров, % лежащих животных
WalkthroughReportViewModel.Comments=Комментарии
WalkthroughReportViewModel.CudChewCategorySection=Оценка жвачки, кол-во жев. движ./жвачка
WalkthroughReportViewModel.CudChewing=Руминация, % жующих
WalkthroughReportViewModel.Current=Текущее
WalkthroughReportViewModel.Dirty=Грязно
WalkthroughReportViewModel.Goals=Цель
WalkthroughReportViewModel.HockAbrasion=Повреждения скакательных суставов, % животных
WalkthroughReportViewModel.ModeratelyClean=Умеренно чисто
WalkthroughReportViewModel.NasalDischarge=Выделения из носа, % животных
WalkthroughReportViewModel.Opportunities=Возможности
WalkthroughReportViewModel.Previous=Предыдущее
WalkthroughReportViewModel.RumenFill=Заполненность рубца
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=Оценка упитанности
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Оценка походки
WalkthroughReportViewModel.RumenHealthManureTitle=Оценка навоза
WalkthroughReportViewModel.Title=Отчет обхода
WalkthroughReportViewModel.Trends=Позитивные тенденции
WalkthroughReportViewModel.UterineDischarge=Выделения из матки, % животных
WalkthroughReportViewModel.WaterQuality=Качество воды
Wallis_and_Futuna=Уоллис и Футуна
Washington=Вашингтон
Waterford=Уотерфорд
Weekly=Еженедельно
WeightDMInLengthImperial=Фунтов СВ в 1 футе
WeightDMInLengthMetric=Кг СВ в 1 метре
WeightImperial=фунтов
WeightImperialCWT=хандредвейт
WeightMetric=кг
West_Bengal=Западная Бенгалия
West_Virginia=Западная Виргиния
Western_Australia=Западная Австралия
Western_Sahara=Западная Сахара
Westmeath=Вестмит
Wexford=Уэксфорд
Wicklow=Уиклоу
Wisconsin=Висконсин
WithinEightHours=В течение 8 ч
Wyoming=Вайоминг
Xinjiang=Синьцзян
Xizang=СИЗАНГ
Yemen=Йемен
Yes=Да
Yucatán=Юкат
Yukon_Territories=Юкон территории
Yunnan=Юньнан
ZAR=Южная Африка (ZAR ZAR)
Zacatecas=Закатекас
Zambia=Замбия
Zhejiang=Чжэцзян
Zimbabwe=Зимбабве
welcome.message=Hallo {0}
Данные=по установкам сайта будут обновлены автоматически при загрузке данных фермы в Dairy Enteligen
Добавьте=или обновите данные сайта на этой странице. Вы также можете добавить или обновить данные по каждому разделу по мере их использования 
Нажмите=на название секции, чтобы обновить данные по секциям для данной фермы
Название=рационов будут обновлены автоматически, если MAX подключен к сайту фермы в Dairy Enteligen. Регулярно обновляйте актуальность рационов, нажимая на название рациона, чтобы гарантировать корректную информацию в отчете.  Если связь с данными MAX еще не установлена, выберите рацион или класс/подкласс животных вручную. 
Не=выходите из приложения, пока идет синхронизация
После=того, как рацион будет создан, выберите класс/подкласс животных, относящихся к данному рациону.
При=создании нового сайта необходимо ввести название сайта, текущая стоимость молока, систему доения и секции (установки секции). Заполнение разделов секций необходимо для просмотра результатов по секции и отчета обхода фермы
Скорость=синхронизации зависит от соединения
парез=
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole


