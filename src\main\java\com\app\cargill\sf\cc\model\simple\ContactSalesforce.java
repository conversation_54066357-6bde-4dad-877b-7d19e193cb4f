/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.app.cargill.sf.cc.model.ContactAccountRecord;
import com.app.cargill.sf.cc.model.PhysicalAddressSalesforce;
import com.app.cargill.sf.cc.model.RecordAttributes;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class ContactSalesforce implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Salutation")
  private String salutation;

  @JsonProperty("OtherAddress")
  private PhysicalAddressSalesforce otherAddress;

  @JsonProperty("MailingAddress")
  private PhysicalAddressSalesforce mailingAddress;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("Title")
  private String title;

  @JsonProperty("CreatedDate")
  private String createdDate;

  @JsonProperty("LastModifiedDate")
  private String lastModifiedDate;

  @JsonProperty("Preferred_Contact_Method__c")
  private String preferredContactMethod;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("Primary_Language__c")
  private String primaryLanguage;

  @JsonProperty("DE_Contact_External_ID__c")
  private String contactExternalId;

  @JsonProperty("Account")
  private ContactAccountRecord account;

  @JsonProperty("Primary_Role__c")
  private String primaryRole;
}
