/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.cdp.account;

import com.app.cargill.constants.Currencies;
import com.app.cargill.document.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class AccountDocumentDTO extends AccountDocument {

  private static final long serialVersionUID = 1L;

  @JsonProperty("BusinessID")
  private String businessCountry;

  @JsonProperty("AccountCurrency")
  private Currencies currency;
}
