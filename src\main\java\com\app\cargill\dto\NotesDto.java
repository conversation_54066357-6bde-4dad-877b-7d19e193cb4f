/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.NoteCategoryType;
import com.app.cargill.constants.ResponseStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class NotesDto extends BaseDto implements Serializable {
  private static final long serialVersionUID = 1L;
  private String localId;
  private String title;
  private String note;
  private UUID accountId;
  private UUID visitId;
  private UUID siteId;
  /* mapping to string from NoteVisitSection enum to make it dynamic, it contains unique name of
  each screen*/
  private String section;
  private String sectionTitle;
  // it contains UUID of selected section entity
  private UUID sectionId;

  private List<NoteMediaItemDto> mediaItems;

  private NoteCategoryType category;
  private Instant actionNotificationDateTimeUtc;
  private boolean isNew;
  private String createUser;
  private Instant createTimeUtc;
  private String lastModifyUser;
  private Instant lastSyncTimeUtc;
  private Instant lastModifiedTimeUtc;
  @Builder.Default private ResponseStatus responseStatus = ResponseStatus.SUCCESS;
  private String responseMessage;
  private String cratedDateTimeFormatted;
  private Boolean isComment;
}
