{"id": "********-c9b0-4e95-863f-281d2eaa1e70", "Type": "0124x000000QgwpAAC", "IsNew": false, "Phone": "**********", "Users": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "Active": false, "Assets": null, "BrandId": null, "CourtId": null, "OwnerId": "<EMAIL>", "PhotoId": "********-0000-0000-0000-********0000", "WonLost": null, "Contacts": [{"Fax": null, "IsNew": false, "Level": null, "OptIn": null, "Phone": null, "Title": null, "Mobile": null, "NameId": null, "EmailId": null, "Primary": null, "Website": null, "Assitant": null, "Comments": null, "FlowName": null, "Function": null, "LastName": "<PERSON>", "AccountId": "********-c9b0-4e95-863f-281d2eaa1e70", "ContactId": "c2ad66b0-577d-4ceb-b84b-6c3d2cea0a3c", "CreatedBy": null, "DoNotCall": null, "FaxOptOut": null, "FirstName": "Kerry", "HomePhone": null, "IsDeleted": false, "Languages": null, "NeedsSync": true, "BusinessId": null, "CreateUser": null, "Department": null, "ExternalId": null, "FunctionId": null, "LeadSource": null, "Salutation": null, "Description": null, "EmailOptOut": null, "MarketingId": null, "MobileFirst": null, "PhoneNumber": "**********", "ReportsToID": null, "BusinessUnit": null, "ContactOwner": null, "EmailAddress": "<EMAIL>", "OtherAddress": null, "PrimaryOwner": null, "AssitantPhone": null, "CreateTimeUtc": "2018-08-17T20:34:57.113014400Z", "DataDotComKey": null, "PrimaryLangId": null, "SFDCContactId": "003f100001zWj3TAAS", "LastModifiedBy": null, "LastModifiedID": null, "LastModifyUser": "<EMAIL>", "MailingAddress": {"City": "Rockwood", "Street": "5956 4th Line", "Country": "Canada", "AddressID": "********-0000-0000-0000-********0000", "PostalCode": "N0B 2K0", "CountyCommunity": null, "StateOrProvince": "ON"}, "SecondaryEmail": null, "LastSyncTimeUtc": "2018-08-17T20:34:57.113014400Z", "OtherPhoneInUse": null, "SecondaryLangId": null, "MobilePhoneNumber": null, "PreferredMethodId": null, "LastUpdateDateTime": {"Date": "2018-08-17T20:34:57.113014400Z", "Epoch": **********}, "ExternalReportsToID": null, "LastModifiedTimeUtc": {"Date": "2018-08-17T20:34:57.113014400Z", "Epoch": **********}, "GoldenRecordAcountId": "0014x00000cmGmHAAU", "LastStayInTouchSaveDate": null, "LastStayInTouchRequestDate": null, "TestNationalIdentificationNumber": null}, {"Fax": null, "IsNew": false, "Level": null, "OptIn": null, "Phone": null, "Title": null, "Mobile": null, "NameId": null, "EmailId": null, "Primary": null, "Website": null, "Assitant": null, "Comments": null, "FlowName": null, "Function": null, "LastName": "<PERSON>", "AccountId": "********-c9b0-4e95-863f-281d2eaa1e70", "ContactId": "884fe0a4-1d0f-4d90-8177-c14a11019ad3", "CreatedBy": null, "DoNotCall": null, "FaxOptOut": null, "FirstName": "<PERSON>", "HomePhone": null, "IsDeleted": false, "Languages": null, "NeedsSync": true, "BusinessId": null, "CreateUser": null, "Department": null, "ExternalId": null, "FunctionId": null, "LeadSource": null, "Salutation": null, "Description": null, "EmailOptOut": null, "MarketingId": null, "MobileFirst": null, "PhoneNumber": "**********", "ReportsToID": null, "BusinessUnit": null, "ContactOwner": null, "EmailAddress": null, "OtherAddress": null, "PrimaryOwner": null, "AssitantPhone": null, "CreateTimeUtc": "2018-08-17T20:34:57.113014400Z", "DataDotComKey": null, "PrimaryLangId": null, "SFDCContactId": "003f100001zWj3WAAS", "LastModifiedBy": null, "LastModifiedID": null, "LastModifyUser": "<EMAIL>", "MailingAddress": {"City": "Rockwood", "Street": "5956 4th Line", "Country": "Canada", "AddressID": "********-0000-0000-0000-********0000", "PostalCode": "N0B 2K0", "CountyCommunity": null, "StateOrProvince": "ON"}, "SecondaryEmail": null, "LastSyncTimeUtc": "2018-08-17T20:34:57.113014400Z", "OtherPhoneInUse": null, "SecondaryLangId": null, "MobilePhoneNumber": null, "PreferredMethodId": null, "LastUpdateDateTime": {"Date": "2018-08-17T20:34:57.113014400Z", "Epoch": **********}, "ExternalReportsToID": null, "LastModifiedTimeUtc": {"Date": "2018-08-17T20:34:57.113014400Z", "Epoch": **********}, "GoldenRecordAcountId": "0014x00000cmGmHAAU", "LastStayInTouchSaveDate": null, "LastStayInTouchRequestDate": null, "TestNationalIdentificationNumber": null}, {"Fax": null, "IsNew": false, "Level": null, "OptIn": null, "Phone": null, "Title": null, "Mobile": null, "NameId": null, "EmailId": null, "Primary": null, "Website": null, "Assitant": null, "Comments": null, "FlowName": null, "Function": null, "LastName": "<PERSON>", "AccountId": "********-c9b0-4e95-863f-281d2eaa1e70", "ContactId": "569368a8-3783-4758-b98b-49cb4b1e02ab", "CreatedBy": null, "DoNotCall": null, "FaxOptOut": null, "FirstName": "<PERSON>", "HomePhone": null, "IsDeleted": false, "Languages": null, "NeedsSync": true, "BusinessId": null, "CreateUser": null, "Department": null, "ExternalId": null, "FunctionId": null, "LeadSource": null, "Salutation": null, "Description": null, "EmailOptOut": null, "MarketingId": null, "MobileFirst": null, "PhoneNumber": null, "ReportsToID": null, "BusinessUnit": null, "ContactOwner": null, "EmailAddress": null, "OtherAddress": null, "PrimaryOwner": null, "AssitantPhone": null, "CreateTimeUtc": "2018-08-17T20:34:57.113014400Z", "DataDotComKey": null, "PrimaryLangId": null, "SFDCContactId": "003f1000022hM1QAAU", "LastModifiedBy": null, "LastModifiedID": null, "LastModifyUser": "<EMAIL>", "MailingAddress": null, "SecondaryEmail": null, "LastSyncTimeUtc": "2018-08-17T20:34:57.113014400Z", "OtherPhoneInUse": null, "SecondaryLangId": null, "MobilePhoneNumber": null, "PreferredMethodId": null, "LastUpdateDateTime": {"Date": "2018-08-17T20:34:57.113014400Z", "Epoch": **********}, "ExternalReportsToID": null, "LastModifiedTimeUtc": {"Date": "2018-08-17T20:34:57.113014400Z", "Epoch": **********}, "GoldenRecordAcountId": "0014x00000cmGmHAAU", "LastStayInTouchSaveDate": null, "LastStayInTouchRequestDate": null, "TestNationalIdentificationNumber": null}, {"Fax": null, "IsNew": false, "Level": null, "OptIn": null, "Phone": null, "Title": null, "Mobile": null, "NameId": null, "EmailId": null, "Primary": null, "Website": null, "Assitant": null, "Comments": null, "FlowName": null, "Function": null, "LastName": "<PERSON>", "AccountId": "********-c9b0-4e95-863f-281d2eaa1e70", "ContactId": "3004f8d2-8e88-42f7-8b88-e081d1b611f6", "CreatedBy": "CH Ashok Naidu", "DoNotCall": null, "FaxOptOut": null, "FirstName": "Kerry", "HomePhone": null, "IsDeleted": false, "Languages": null, "NeedsSync": true, "BusinessId": null, "CreateUser": "CH Ashok Naidu", "Department": null, "ExternalId": null, "FunctionId": null, "LeadSource": null, "Salutation": null, "Description": null, "EmailOptOut": null, "MarketingId": [], "MobileFirst": null, "PhoneNumber": "**********", "ReportsToID": null, "BusinessUnit": null, "ContactOwner": null, "EmailAddress": "<EMAIL>", "OtherAddress": null, "PrimaryOwner": null, "AssitantPhone": null, "CreateTimeUtc": "2022-03-07T07:48:36Z", "DataDotComKey": null, "PrimaryLangId": null, "SFDCContactId": "0038D000005L5byQAC", "LastModifiedBy": "CH Ashok Naidu", "LastModifiedID": "0054x0000054D3uAAE", "LastModifyUser": "CH Ashok Naidu", "MailingAddress": null, "SecondaryEmail": null, "LastSyncTimeUtc": "2022-03-29T09:33:51.195769200Z", "OtherPhoneInUse": null, "SecondaryLangId": null, "MobilePhoneNumber": null, "PreferredMethodId": null, "LastUpdateDateTime": {"Date": "2022-03-29T09:33:51.195769200Z", "Epoch": 1648546431}, "ExternalReportsToID": null, "LastModifiedTimeUtc": {"Date": "2022-03-29T00:00:00Z", "Epoch": **********}, "GoldenRecordAcountId": "0014x00000cmGmHAAU", "LastStayInTouchSaveDate": null, "LastStayInTouchRequestDate": null, "TestNationalIdentificationNumber": null}, {"Fax": null, "IsNew": false, "Level": null, "OptIn": null, "Phone": null, "Title": null, "Mobile": null, "NameId": null, "EmailId": null, "Primary": null, "Website": null, "Assitant": null, "Comments": null, "FlowName": null, "Function": null, "LastName": "<PERSON>", "AccountId": "********-c9b0-4e95-863f-281d2eaa1e70", "ContactId": "7ef06ab5-09aa-4bf2-afb0-8250277e1e28", "CreatedBy": "CH Ashok Naidu", "DoNotCall": null, "FaxOptOut": null, "FirstName": "<PERSON>", "HomePhone": null, "IsDeleted": false, "Languages": null, "NeedsSync": true, "BusinessId": null, "CreateUser": "CH Ashok Naidu", "Department": null, "ExternalId": null, "FunctionId": null, "LeadSource": null, "Salutation": null, "Description": null, "EmailOptOut": null, "MarketingId": [], "MobileFirst": null, "PhoneNumber": "**********", "ReportsToID": null, "BusinessUnit": null, "ContactOwner": null, "EmailAddress": null, "OtherAddress": null, "PrimaryOwner": null, "AssitantPhone": null, "CreateTimeUtc": "2022-03-07T07:48:36Z", "DataDotComKey": null, "PrimaryLangId": null, "SFDCContactId": "0038D000005L5bzQAC", "LastModifiedBy": "CH Ashok Naidu", "LastModifiedID": "0054x0000054D3uAAE", "LastModifyUser": "CH Ashok Naidu", "MailingAddress": null, "SecondaryEmail": null, "LastSyncTimeUtc": "2022-03-29T09:33:51.195769200Z", "OtherPhoneInUse": null, "SecondaryLangId": null, "MobilePhoneNumber": null, "PreferredMethodId": null, "LastUpdateDateTime": {"Date": "2022-03-29T09:33:51.195769200Z", "Epoch": 1648546431}, "ExternalReportsToID": null, "LastModifiedTimeUtc": {"Date": "2022-03-29T00:00:00Z", "Epoch": **********}, "GoldenRecordAcountId": "0014x00000cmGmHAAU", "LastStayInTouchSaveDate": null, "LastStayInTouchRequestDate": null, "TestNationalIdentificationNumber": null}], "IsDeleted": false, "LegalName": null, "NeedsSync": false, "OtherFlag": false, "PriceFlag": false, "SiteCount": 0, "SubTypeID": 8, "UserRoles": [{"roleType": null, "userName": null, "userBusinessUnit": 0}, {"roleType": null, "userName": null, "userBusinessUnit": 0}, {"roleType": null, "userName": null, "userBusinessUnit": 0}, {"roleType": null, "userName": null, "userBusinessUnit": 0}, {"roleType": null, "userName": null, "userBusinessUnit": 0}, {"roleType": null, "userName": null, "userBusinessUnit": 0}], "WonLostId": null, "BusinessID": 2, "CreateUser": null, "CreditFlag": false, "ERPPayerId": null, "ExternalId": null, "PersonalID": null, "Securities": null, "SubBrandId": null, "lstOtherBU": [], "AccountName": "Maker Farms Inc. % Matt <PERSON>", "AccountType": 0, "Description": null, "ERPIdLength": null, "ERPShipToId": null, "IsDuplicate": false, "Liabilities": null, "QualityFlag": false, "ServiceFlag": false, "isFavourite": false, "AutoValidate": false, "CompanyEmail": null, "CustomerCode": null, "SourceSystem": null, "VeterinaryId": null, "AccountNumber": null, "AccountStatus": null, "BuyingGroupID": null, "IsMobileFirst": true, "LastOrderDate": null, "PortfolioFlag": false, "createTimeUtc": "2012-12-12T21:28:32Z", "AdditionalInfo": null, "ApprovalStatus": null, "ConsumerStatus": null, "CustomerStatus": null, "DateOfLastCall": null, "GoldenRecordId": "0014x00000cmGmHAAU", "LastModifiedBy": "<PERSON><PERSON><PERSON>", "LastModifyUser": null, "LastOrdersInfo": null, "MarginEstimate": null, "NewAccountType": "Prospect", "PreviousStatus": null, "ProspectStatus": null, "SalesTerritory": null, "VolumeEstimate": null, "WebSiteAddress": null, "AccountCurrency": null, "DateOfLastVisit": null, "LastAdminUpdate": null, "LastInvoiceDate": null, "LastSyncTimeUtc": "2022-03-29T09:33:51.195769200Z", "ParentAccountID": null, "PerformanceFlag": false, "PhysicalAddress": {"City": "Rockwood", "Street": "5956 4th Line", "Country": "Canada", "AddressID": "96ff4935-a967-4a0c-bcfa-5e15eca62916", "PostalCode": "N0B 2K0", "CountyCommunity": null, "StateOrProvince": "Ontario"}, "WonLostComments": null, "AccountValidated": false, "ERPPayerIdLength": null, "LastInvoicesInfo": null, "LastUpdateUserId": null, "MarketInfluencer": null, "NineBoxStepTwoID": 8, "PrimaryContactId": "c2ad66b0-577d-4ceb-b84b-6c3d2cea0a3c", "ReqProcessingLog": null, "SegmentStepOneId": null, "ChangeAccountType": true, "ERPShiptoIdLength": null, "IsServicedbyCSPro": null, "ReasonDescription": null, "WonLostReasonCode": null, "SocialMediaAddress": null, "LimitChangeReasonId": null, "PrimaryContactTitle": "<PERSON>", "lastModifiedTimeUtc": "2022-03-29T09:33:51Z", "AvailabilityOnMarket": null, "BusinessSolutionFlag": false, "DefaultCustServiceID": null, "DeliveryInstructions": null, "ExternalLeadSourceID": null, "LastModificationDate": null, "CorrespondenceAddress": null, "DefaultCargillPlantID": null, "ExternalBuyingGroupID": null, "OwnerProfileNameandId": "ANH Standard Profile00e4x000000ZNTlAAO", "ExternalParentAccountID": null, "OtherActivityProduction": null, "PrimaryContactPhoneNumber": "**********", "CurrentUserProfileNameandId": null, "erpIdLengthValidatorIsError": null}