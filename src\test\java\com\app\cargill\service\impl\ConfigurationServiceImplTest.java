/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.document.ConfigurationDocument;
import com.app.cargill.dto.ConfigurationDto;
import com.app.cargill.model.Configurations;
import com.app.cargill.repository.ConfigurationsRepository;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ConfigurationServiceImplTest {

  @Mock private ConfigurationsRepository configurationsRepository;

  @InjectMocks private ConfigurationsServiceImpl configurationsServiceImpl;

  @Test
  void whenFetchConfigurationIsCalledCorrectResultIsReturned() {
    when(configurationsRepository.findAll()).thenReturn(List.of(loadConfiguration()));

    ConfigurationDto result = configurationsServiceImpl.fetchConfiguration();
    assertNotNull(result);
  }

  @Test
  void whenNoConfigurationIsPresentEmptyResultIsReturned() {
    when(configurationsRepository.findAll()).thenReturn(new ArrayList<>());

    ConfigurationDto result = configurationsServiceImpl.fetchConfiguration();
    assertNotNull(result);
  }

  private Configurations loadConfiguration() {
    return Configurations.builder()
        .configurationDocument(
            ConfigurationDocument.builder()
                .maxPageSize(500)
                .minPageSize(100)
                .percentage(15.0)
                .build())
        .build();
  }
}
