/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ActivityCrescendo implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ActivityDateTime")
  private Instant activityDateTime;

  @JsonProperty("ActivityType")
  private ActivityTypeCrescendo activityType;

  @JsonProperty("Subject")
  private String subject;

  @JsonProperty("AccountLevelAccess")
  private String accountLevelAccess;

  @JsonProperty("IsPrivate")
  private Boolean isPrivate;

  @JsonProperty("RecordTypeId")
  private String recordTypeId;

  @JsonProperty("IsGroupEvent")
  private Boolean isGroupEvent;

  @JsonProperty("AllDayEvent")
  private Boolean allDayEvent;

  @JsonProperty("VisitReportFilePathDateTime")
  private String visitReportFilePathDateTime;

  @JsonProperty("SFDCAccountId")
  private String sfdcAccountId;

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("SFDCId")
  private String sfdcId;

  @JsonProperty("SFDCVisitId")
  private String sfdcVisitId;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("AssignedToID")
  private String assignedToID;

  @JsonProperty("EndDateTime")
  private Instant endDateTime;

  @JsonProperty("Location")
  private String location;

  @JsonProperty("DescriptionInfo")
  private String descriptionInfo;

  @JsonProperty("BusinessID")
  private BusinessUnitCrescendo businessId;

  @JsonProperty("SegmentID")
  private SegmentStepOneIdCrescendo segmentId;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("ActivityCurrency")
  private CurrencyCrescendo activityCurrency;

  @JsonProperty("CreatedBy")
  private String createdBy;

  @JsonProperty("ExternalAccountId")
  private String externalAccountId;

  @JsonProperty("ExternalId")
  private String externalId;

  @JsonProperty("MobileFirst")
  private Boolean mobileFirst;

  @JsonProperty("SourceSystem")
  private String sourceSystem;

  @JsonProperty("Type")
  private EventTypeCrescendo type;

  @JsonProperty("VisitReport")
  private String visitReport;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted;

  @JsonProperty("id")
  private String id;

  @JsonProperty("LastModifiedTimeUtc")
  private Instant lastModifiedTimeUtc;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("LastSyncTimeUtc")
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private Boolean isNew;

  @JsonProperty("EventStartDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant eventStartDate;

  @JsonProperty("IsAllDayEvent")
  private Boolean isAllDayEvent;

  @JsonProperty("CreateTimeUtc")
  private Instant createTimeUtc;

  @JsonProperty("ComplaintRaised")
  private Boolean complaintRaised;

  @JsonProperty("EventRecordTypeID")
  private Integer eventRecordTypeID;

  @JsonProperty("TrainingScheduled")
  private Boolean trainingScheduled;

  @JsonProperty("SubTypeID")
  private Integer subTypeID;

  @JsonProperty("RelatedToListId")
  private String relatedToListId;

  @JsonProperty("RelatedToTypeID")
  private Integer relatedToTypeID;

  @JsonProperty("ExternalRelatedToListId")
  private UUID externalRelatedToListId;

  @JsonProperty("ExternalNameListId")
  private UUID externalNameListId;

  @JsonProperty("NameListId")
  private String nameListId;

  @JsonProperty("ServiceFlag")
  private Boolean serviceFlag;

  @JsonProperty("NameTypeID")
  private Integer nameTypeID;

  @JsonProperty("ShowTimeAsID")
  private Integer showTimeAsID;

  @JsonProperty("GroupID")
  private Integer groupID;

  @JsonProperty("ProductLineID")
  private Integer productLineID;

  @JsonProperty("ReminderFollowupDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant reminderFollowupDate;

  @JsonProperty("ClassID")
  private Integer classID;

  @JsonProperty("ProductFunctionID")
  private List<Integer> productFunctionID;

  @JsonProperty("ReminderDateTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant reminderDateTime;

  @JsonProperty("ApprovalRequestNo")
  private Integer approvalRequestNo;

  @JsonProperty("ReminderFlag")
  private Boolean reminderFlag;

  @JsonProperty("ReadyforApprovalFlag")
  private Boolean readyforApprovalFlag;

  @JsonProperty("Comments")
  private String comments;

  @JsonProperty("DueDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant dueDate;

  @JsonProperty("StatusId")
  private Integer statusId;

  @JsonProperty("Recurrence")
  private Integer recurrence;

  @JsonProperty("PriorityId")
  private Integer priorityId;

  @JsonProperty("ApprovalStatus")
  private String approvalStatus;

  @JsonProperty("CallDuration")
  private String callDuration;

  @JsonProperty("Approved")
  private String approved;

  @JsonProperty("CallNotesUpdated")
  private String callNotesUpdated;

  @JsonProperty("CallResult")
  private String callResult;

  @JsonProperty("CallObjectIdentifier")
  private String callObjectIdentifier;

  @JsonProperty("CompletedDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant completedDate;

  @JsonProperty("CallType")
  private String callType;

  @JsonProperty("ClaimComplaint")
  private Boolean claimComplaint;

  @JsonProperty("DBActivityType")
  private String dbActivityType;

  @JsonProperty("OwnerSManagerRoleName")
  private String ownerSManagerRoleName;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("OwnerRole")
  private String ownerRole;

  @JsonProperty("ReadyforApproval")
  private String readyforApproval;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("ReportLink")
  private String reportLink;

  @JsonProperty("Frequency")
  private Integer frequency;

  @JsonProperty("Service")
  private Boolean service;

  @JsonProperty("Time")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant time;

  @JsonProperty("YearMonth")
  private String yearMonth;

  @JsonProperty("Weekno")
  private String weekNo;

  @JsonProperty("ExtendedAttributes")
  private Map<String, String> extendedAttributes;

  @JsonProperty("TimeTakenHours")
  private String timeTakenHours;

  @JsonProperty("Sales")
  private Boolean sales;

  @JsonProperty("Cancelled")
  private Boolean cancelled;
}
