/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.LeadSource;
import com.app.cargill.constants.Level;
import com.app.cargill.constants.MarketingClassification;
import com.app.cargill.constants.PreferredMethod;
import com.app.cargill.constants.PrimaryLang;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@JsonInclude(Include.NON_NULL)
public class Contact implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("AccountId")
  private UUID accountId;

  @JsonProperty("ContactId")
  private UUID contactId;

  @JsonProperty("GoldenRecordAcountId")
  private String goldenRecordAcountId;

  @JsonProperty("SFDCContactId")
  private String sFDCContactId;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("BusinessId")
  private Integer businessId;

  @JsonProperty("FunctionId") // CPN - France,CPN - Brazil,US,Canada,Netherlands,Philippines,Poland
  private Integer functionId;

  @JsonProperty("BusinessUnit")
  private String businessUnit;

  @JsonProperty("Function")
  private String function;

  @JsonProperty("MailingAddress")
  private Address mailingAddress;

  @JsonProperty("PhoneNumber")
  private String phoneNumber;

  /**
   * In the existing data there is not other populated phone field other than PhoneNumber and
   * HomePhone For this reason we'll use HomePhone as MobilePhone value in Lift Salesforce
   */
  @JsonProperty("HomePhone")
  private String homePhone;

  @JsonProperty("EmailAddress")
  private String emailAddress;

  @JsonProperty("SecondaryEmail")
  private String secondaryEmail;

  @JsonProperty("Comments")
  private String comments;

  @JsonProperty("LastUpdateDateTime")
  private DateEpoch lastUpdateDateTime;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("Title")
  private String title;

  @JsonProperty("OtherAddress")
  private Address otherAddress;

  @JsonProperty("EmailId")
  private String emailId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("PrimaryOwner")
  private Boolean primaryOwner;

  @JsonProperty("PrimaryLangId")
  private PrimaryLang primaryLangId;

  @JsonProperty("ReportsToID")
  private String reportsToID;

  @JsonProperty("ExternalReportsToID")
  private UUID externalReportsToID;

  @JsonProperty("PreferredMethodId")
  private PreferredMethod preferredMethodId;

  @JsonProperty("OptIn")
  private Boolean optIn;

  @JsonProperty("Assitant")
  private String assitant;

  @JsonProperty("AssitantPhone")
  private String assitantPhone;

  @JsonProperty("Website")
  private String website;

  @JsonProperty("Department")
  private String department;

  @JsonProperty("DataDotComKey")
  private String dataDotComKey;

  @JsonProperty("DoNotCall")
  private Boolean doNotCall;

  @JsonProperty("EmailOptOut")
  private Boolean emailOptOut;

  @JsonProperty("FlowName")
  private String flowName;

  @JsonProperty("Languages")
  private String languages;

  @JsonProperty("LastStayInTouchRequestDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastStayInTouchRequestDate;

  @JsonProperty("LastStayInTouchSaveDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastStayInTouchSaveDate;

  @JsonProperty("LeadSource")
  private LeadSource leadSource;

  @JsonProperty("Level")
  private Level level;

  @JsonProperty("MobileFirst")
  private Boolean mobileFirst;

  @JsonProperty("Primary")
  private Boolean primary;

  @JsonProperty("MarketingId")
  private List<MarketingClassification> marketingId;

  @JsonProperty("Salutation")
  private String salutation;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("CreateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  private DateEpoch lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private Boolean isNew;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;
}
