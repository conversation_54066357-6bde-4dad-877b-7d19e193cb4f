/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ToString
public class SiteUpdateModel implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Site_Name__c")
  private String siteName;

  @JsonProperty("Number_of_Animals__c")
  private String numberOfAnimals;

  @JsonProperty("LM_Site_ID__c")
  private String externalId;

  @JsonProperty("Current_Milk_Price__c")
  private Double currentMilkPrice;

  @JsonProperty("Housing_Type__c")
  private String housingType;

  @JsonProperty("Account__c")
  private String accountId;

  @JsonProperty("DE_Site__c")
  private boolean deSite = true;

  @JsonProperty("DE_Herd_Status_Report_Latest__c")
  private String herdStatusReport;

  @JsonProperty("DE_Herd_Summary_Report_Latest__c")
  private String herdSummaryReport;
}
