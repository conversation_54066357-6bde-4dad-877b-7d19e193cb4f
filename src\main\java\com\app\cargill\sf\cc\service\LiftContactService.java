/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.Contact;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.simple.ContactSalesforce;
import com.app.cargill.sf.cc.model.simple.MobileToLiftContactSalesforce;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiftContactService {
  private final LiftApiService liftApi;

  public String createContact(
      Contact contactDocument, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return createContact(
        tokenAndApiPath.getAuthToken(),
        tokenAndApiPath.getApiPath(),
        contactDocument,
        locale,
        source);
  }

  /**
   * Create a contact on Lift
   *
   * @param authToken the token object
   * @param apiPath url to salesforce instance
   * @param contactDocument the document model
   * @param source
   * @param locale
   * @return Created contact ID
   * @throws CustomDEExceptions
   * @throws Exception
   */
  public String createContact(
      AuthToken authToken,
      String apiPath,
      Contact contactDocument,
      Locale locale,
      ResourceBundleMessageSource source)
      throws CustomDEExceptions, JsonProcessingException {
    if (contactDocument.getContactId() == null) {
      throw new IllegalArgumentException("ContactId is a required field");
    }
    try {
      String contactUrl = String.format("%s/sobjects/Contact", apiPath);
      ContactSalesforce contactModel = documentToModel(contactDocument);
      log.debug("creating contact");
      CreateRecordResponse recordResponse =
          liftApi.createRecord(
              authToken, contactModel, new ParameterizedTypeReference<>() {}, contactUrl);
      log.debug("Contact created: ", recordResponse.getId());
      return recordResponse.getId();
    } catch (CustomDEExceptions ex) {
      log.error(String.format("Error on contact creation: %s", contactDocument.getContactId()), ex);
      throw ex;
    } catch (Exception e) {
      log.error(String.format("Error on contact creation: %s", contactDocument.getContactId()), e);
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  source.getMessage(
                      LangKeys.LIFT_SYNC_FAILED,
                      new Object[] {},
                      LiftErrorResponseConstants.LIFT_ERROR_MESSAGE,
                      locale))
              .entity("Contact")
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()),
          HttpStatus.SC_FORBIDDEN);
    }
  }

  public boolean updateContact(
      Contact contactDocument, ResourceBundleMessageSource source, Locale locale)
      throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return updateContact(
        tokenAndApiPath.getAuthToken(),
        tokenAndApiPath.getApiPath(),
        contactDocument,
        source,
        locale);
  }

  public boolean updateContact(
      AuthToken authToken,
      String apiPath,
      Contact contactDocument,
      ResourceBundleMessageSource source,
      Locale locale)
      throws CustomDEExceptions, JsonProcessingException {
    try {
      if (contactDocument.getSFDCContactId() == null) {
        throw new IllegalArgumentException("Missing contact external id.");
      }
      String contactUrl =
          String.format("%s/sobjects/Contact/%s", apiPath, contactDocument.getSFDCContactId());
      ContactSalesforce contactUpdateModel = documentToModel(contactDocument);
      liftApi.updateRecord(
          authToken, contactUpdateModel, new ParameterizedTypeReference<>() {}, contactUrl);
      return true;
    } catch (Exception e) {
      log.error(String.format("Error on contact Updation: %s", contactDocument.getContactId()), e);
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  source.getMessage(
                      LangKeys.LIFT_SYNC_FAILED,
                      new Object[] {},
                      LiftErrorResponseConstants.LIFT_ERROR_MESSAGE,
                      locale))
              .entity("Contact")
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()),
          HttpStatus.SC_FORBIDDEN);
    }
  }

  public ContactSalesforce documentToModel(Contact contactDocument) {
    ContactSalesforce contactSalesforce = new ContactSalesforce();
    contactSalesforce.setAccountId(contactDocument.getGoldenRecordAcountId());
    contactSalesforce.setContactExternalId(contactDocument.getContactId().toString());
    contactSalesforce.setFirstName(contactDocument.getFirstName());
    contactSalesforce.setLastName(contactDocument.getLastName());
    contactSalesforce.setPhone(contactDocument.getPhoneNumber());
    contactSalesforce.setEmail(contactDocument.getEmailAddress());
    return contactSalesforce;
  }

  public MobileToLiftContactSalesforce validateDocument(Contact contactDocument) {
    if (contactDocument == null) {
      return null;
    }
    MobileToLiftContactSalesforce mobileToLiftContactSalesforce =
        new MobileToLiftContactSalesforce();
    mobileToLiftContactSalesforce.setAccountId(contactDocument.getGoldenRecordAcountId());
    mobileToLiftContactSalesforce.setContactExternalId(contactDocument.getContactId().toString());
    mobileToLiftContactSalesforce.setFirstName(contactDocument.getFirstName());
    mobileToLiftContactSalesforce.setLastName(contactDocument.getLastName());
    mobileToLiftContactSalesforce.setPhone(contactDocument.getPhoneNumber());
    mobileToLiftContactSalesforce.setEmail(contactDocument.getEmailAddress());
    return mobileToLiftContactSalesforce;
  }

  public List<Contact> saveToLift(
      List<Contact> contacts, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions, JsonProcessingException {
    List<Contact> modifiedContacts = new ArrayList<>();

    if (contacts != null && !contacts.isEmpty()) {
      contacts.get(0).setSFDCContactId(createContact(contacts.get(0), locale, source));
      modifiedContacts.add(contacts.get(0));
    }
    return modifiedContacts;
  }
}
