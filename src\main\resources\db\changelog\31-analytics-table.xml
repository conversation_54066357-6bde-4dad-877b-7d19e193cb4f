<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="31" author="anenkov">
    <sql>
      CREATE TABLE IF NOT EXISTS analytics
      (
        id SERIAL PRIMARY KEY ,
        datapoint_name VARCHAR(256),
        created_date timestamp without time zone,
        updated_date timestamp without time zone,
        meta_data jsonb,
        deleted BOOLEAN default false,
        local_id varchar(255));
    </sql>
  </changeSet>

</databaseChangeLog>
