<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="format-detection" content="date=no">
	<meta name="format-detection" content="address=no">
	<meta name="format-detection" content="telephone=no">
	<meta name="x-apple-disable-message-reformatting">
	<link href="../../app/generatedTemplateResources/css/main.css" rel="stylesheet">
	<script src="../../app/generatedTemplateResources/js/chart.js"></script>
	<title>Cargill</title>
</head>
<body>

	<div class="container">
		<div class="template-header">
			<figure>
				<img src="../../generatedTemplateResources/images/cargill-logo.svg">
			</figure>
		</div>

		<div class="card mb-5">
			<div class="card-header pt-5">
				<h4 class="mb-2">${model.visitName!}</h4>

				<div class="row">
					<div class="content-set">
						<label>${localization.getMessage("Report.Tool.Name", [], "Tool Name", locale)}:</label>
						<h4>${model.toolName!}</h4>
					</div>
					<div class="content-set mx-2">
						<label>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}:</label>
						<h4>${model.visitDate!}</h4>
					</div>
					<div id="region" style="opacity: 0;">${region}</div>
				</div>
			</div>

			<div class="card-body">
				<div class="row">
					<div class="content-set">
						<h4 class="single-line-data"><span>${localization.getMessage("MetabolicIncidenceChartsViewModel.DisorderGraphTitle", [], "Metabolic Disorder Cost/Cow", locale)}</span></h4>
					</div>
				</div>
				<div class="row">
					<#list model.metabolicIncidenceExportImageReportDto.formattedDates as x>
						<#if x?index == 0>
							<div class="legend-wrap mb-2">
								<p class="lagoon-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 1>
							<div class="legend-wrap mb-2">
								<p class="voilet-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 2>
							<div class="legend-wrap mb-2">
								<p class="voilet-2-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 3>
							<div class="legend-wrap mb-2">
								<p class="voilet-3-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 4>
							<div class="legend-wrap mb-2">
								<p class="pink-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 5>
							<div class="legend-wrap mb-2">
								<p class="red-2-solid">${x!}</p>
							</div>
						</#if>
						<#if x?index == 6>
							<div class="legend-wrap mb-2">
								<p class="gold-solid">${x!}</p>
							</div>
						</#if>
					</#list>
				</div>

				<canvas id="linechart"></canvas>
			</div>

		</div>
	</div>


<script>

const bar1 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate1 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar2 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate2 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar3 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate3 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar4 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate4 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar5 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate5 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar6 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate6 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar7 = [
	<#list model.metabolicIncidenceExportImageReportDto.visitDate7 as val>
	     ${(val)!'NaN'}<#sep>, </#sep>
	</#list>
];

const xAxis = [
<#list model.metabolicTypeKeys as typeKeys>
    '${localization.getMessage(typeKeys, [], typeKeys, locale)}'<#sep>, </#sep>
</#list>
];

const ctx = document.getElementById("linechart").getContext("2d");
ctx.canvas.height = 200;
const borderWidth = 2.2;
const categoryPercentage = 0.85;
const barPercentage = 1;
const options = {
    type: "bar",
    data: {
        labels: xAxis,
        datasets: [
	<#if model.metabolicIncidenceExportImageReportDto.visitDate1?has_content>
		{
			data: bar1,
			grouped:true,
			backgroundColor: "#67B7DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceExportImageReportDto.visitDate2?has_content>
		{
			data: bar2,
			backgroundColor: "#6794DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceExportImageReportDto.visitDate3?has_content>
		{
			data: bar3,
			backgroundColor: "#8067DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceExportImageReportDto.visitDate4?has_content>
		{
			data: bar4,
			backgroundColor: "#A367DC",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceExportImageReportDto.visitDate5?has_content>
		{
			data: bar5,
			backgroundColor: "#DC67CE",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceExportImageReportDto.visitDate6?has_content>
		{
			data: bar6,
			backgroundColor: "#DC6967",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
	<#if model.metabolicIncidenceExportImageReportDto.visitDate7?has_content>
		{
			data: bar7,
			backgroundColor: "#DCAF67",
			borderColor: "rgba(0,0,0,0)",
			borderWidth: borderWidth,
			categoryPercentage: categoryPercentage,
			barPercentage: barPercentage,
		},
	</#if>
        ]
    },
    options: {
        plugins: {
            legend: {
                display: false,
            },
            tooltip: {
                callbacks: {
                    title: () => null // or function () { return null; }
                },
                yAlign: 'bottom',
                backgroundColor: "#fff",
                borderColor: "rgba(0, 0, 0, 0.25)",
                borderWidth: 1,
                displayColors: false,
                bodyColor: "#307698",
                bodyAlign: "center",
            },
        },

        layout: {
            padding: {
                top: 20,
                right: 15
            }
        },

        responsive: true,
        scales: {
            y: {
                // beginAtZero: true,
                title: {
                    display: true,
                    color: '#6C7782',
                    text: "${model.metabolicDisorderCostPerCowLabel!}",
                    padding: {
                        bottom: 15,
                    }
                },

                grid: {
                    display: false,
                },
                suggestedMax: (scale) => {

                    var curr = scale.chart.data.datasets;
                    var arr = [];
                    for (let i = 0; i < curr.length; i++) {
                        arr.push(Math.max.apply(null, curr[i].data.filter(x => !isNaN(x))));
                    }
                    return Math.round(Math.max(...arr) + (Math.max(...arr) * 0.05));
                },
                suggestedMin: (scale) => {
                    var curr = scale.chart.data.datasets;
                    var arr = [];
                    for (let i = 0; i < curr.length; i++) {
                        arr.push(Math.min.apply(null, curr[i].data.filter(x => !isNaN(x))));
                    }
                    return Math.floor(Math.min(...arr) + (Math.min(...arr) * 0.15));
                }
            },

            x: {
                title: {
                    display: true,
                    color: '#6C7782',
                    // text: 'Lactation Stages',
                    padding: {
                        top: 15,
                    }
                },
                grid: {
                    display: false,
                },
            }
        },
        animation: {
            duration: 0,
            onComplete: function() {
                var chart = this;
                var ctx = chart.ctx;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'bottom';
                ctx.fillStyle = '#6C7782';
                this.data.datasets.forEach(function(dataset, i) {
                    var meta = chart.getDatasetMeta(i);
                    meta.data.forEach(function(bar, index) {
                        var data = dataset.data[index];
                        let regionText = document.getElementById("region").innerHTML;
						data = isNaN(data) ? '': new Intl.NumberFormat(regionText).format(data);
                        var yIndex = bar.y - 12;
                        if (data && data < 0) {
                            yIndex = bar.y + 15;
                        }
                        ctx.save();
                        // Translate 0,0 to the point you want the text
                        ctx.translate(bar.x + 5, yIndex);
                        // Rotate context by -90 degrees
                        ctx.rotate(-0.4 * Math.PI);
                        ctx.textAlign = "center";
                        ctx.fillText(data, 0, 0);
                        ctx.restore();
                    });
                });
            }
        }
    }
};

window.onload = function() {
    window.myLine = new Chart(ctx, options);
};
</script>

</body>
</html>