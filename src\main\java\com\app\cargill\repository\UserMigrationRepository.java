/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.TaskStatus;
import com.app.cargill.model.UserMigration;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface UserMigrationRepository extends JpaRepository<UserMigration, Long> {
  List<UserMigration> findAllByStatusNotIn(List<TaskStatus> taskStatus);

  @Query(value = "SELECT email FROM user_migration WHERE deleted = false", nativeQuery = true)
  List<String> findAllEmailByDeletedFalse();
}
