/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

public class LangKeys {

  private LangKeys() {}

  public static final String WELCOME_MESSAGE = "welcome.message";
  public static final String LACTATING = "Lactating";
  public static final String DRY = "Dry";
  public static final String HEIFER = "Heifer";
  public static final String CALF = "Calf";
  public static final String MALE = "Male";
  public static final String FRESH = "Fresh";
  public static final String MILKING = "Milking";
  public static final String LOWFORGE = "LowForage";
  public static final String PASTURE = "Pasture";
  public static final String FRESH_HEIFER = "FreshHeifer";
  public static final String AAEFFICIENCY = "AAEfficiency";
  public static final String CLOSE_UP = "CloseUp";
  public static final String FAR_OFF = "FarOff";
  public static final String CLOSE_UP_HEIFER = "CloseUpHeifer";
  public static final String SHORT_DRY_PERIOD = "ShortDryPeriod";
  public static final String BULL = "Bull";
  public static final String STEER = "Steer";
  public static final String CUD_CHEWING_TITLE = "VisitSummaryViewModel.RumenHealthTitle";

  public static final String REPORT_SHEET_NAME = "Report";
  public static final String REPORT_BCS_EVAL_DATA_TITLE = "Report.BCS.EvalDataTitle";
  public static final String REPORT_BCS_MIN = "Report.Bcs.Min";
  public static final String REPORT_BCS_MAX = "Report.BCS.Max";
  public static final String REPORT_BCS_MILK_HEAD_DAY = "Report.BCS.MilkHeadDay";
  public static final String REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_GOALS = "Goal";
  public static final String REPORT_BCS_AVG = "Report.BCSAvg";
  public static final String REPORT_LOCOMOTION_AVG = "LocomotionHerdResultsViewModel.HerdAverage";
  public static final String REPORT_LACTATION_STAGES = "Report.BCS.LactationStages";
  public static final String REPORT_LOCOMOTION_SCORE_X_AXIS = "Report.LocomotionScore.X.Axis";
  public static final String REPORT_LOCOMOTION_SCORE = "LocomotionScore";
  public static final String REPORT_VISIT_NAME = "Report.Visit.name";
  public static final String REPORT_VISIT_DATE = "Report.Visit.Date";
  public static final String REPORT_TOOL_NAME = "Report.Tool.Name";
  public static final String REPORT_ANALYSIS_TYPE = "Report.Analysis.Type";

  public static final String REPORT_BCS_HERD_ANALYSIS_CHART_NAME =
      "Report.Bcs.HerdAnalysis.ChartName";
  public static final String REPORT_RUMEN_HEALTH_HERD_ANALYSIS_CHART_NAME =
      "NotebookManureScoreLanding";
  public static final String REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_CHART_NAME =
      "Report.Locomotion.HerdAnalysis.ChartName";

  public static final String REPORT_BCS = "Report.Bcs";
  public static final String REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_Y_AXIS =
      "Report.LocomotionScore.Y.Axis";

  public static final String REPORT_BCS_MILK = "Report.Bcs.Milk";
  public static final String REPORT_PEN_NAME = "PenName";
  public static final String REPORT_CUD_CHEWING_EVAL_DATA_TITLE = "Report.CudChewing.EvalDataTitle";
  public static final String REPORT_EVAL_DATA_TITLE = "Report.EvalDataTitle";
  public static final String REPORT_CUD_CHEWING_CATEGORY = "EditNoteViewModel.Category";

  public static final String REPORT_CUD_CHEWING_PEN_ANALYSIS_PERCENT_CHEWING =
      "CudChewsDatesForComparisonViewModel.CudChewsPercent";
  public static final String REPORT_LOCOMOTION_SCORE_PEN_ANALYSIS_CHART_NAME =
      "Report.locomotionScore.Pen.Analysis.ChartName";

  public static final String REPORT_CHEWS_PER_REGURGITATION = "Report.No.OfChewsPerRegurgitation";
  public static final String REPORT_GOAL_CHEWS = "Report.goalChews";
  public static final String REPORT_CHEWS = "Report.Chews";
  public static final String REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_0 =
      "Report.CudChewingPercentage";
  public static final String REPORT_CUD_CHEWING_HERD_ANALYSIS_SHEET_1 = "Report.No.OfChews";
  public static final String REPORT_GOAL_CUD_CHEWING_PERCENTAGE = "Report.GoalCudChewingPercentage";
  public static final String REPORT_CUD_CHEWING_HERD_ANALYSIS_CHART_NAME_0 =
      "Report.CudChewingPercentage.Vs.LactStages";
  public static final String REPORT_CUD_CHEWING_HERD_ANALYSIS_CHART_NAME_1 =
      "Report.NoOfChews.Vs.LactStages";
  public static final String REPORT_CUD_CHEWING_HERD_ANALYSIS_CUD_CHEWING_PERCENTAGE =
      "Report.Herd.Analysis.CudChewingPercentage";

  public static final String REPORT_BCS_PEN_ANALYSIS_CATEGORIES = "EditNoteViewModel.Category";
  public static final String REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CATEGORIES =
      "EditNoteViewModel.Category";
  public static final String REPORT_BCS_PEN_ANALYSIS_CHART_NAME = "EditNoteViewModel.Category";
  public static final String REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CHART_NAME =
      "EditNoteViewModel.Category";
  public static final String REPORT_ANIMAL_TAG_NAME = "Report.Animal.Tag.Name";
  public static final String REPORT_CALVING_DATE = "Report.Calving.Date";
  public static final String REPORT_DIM = "DaysInMilkItem";
  public static final String REPORT_BODY_CONDITION_SCORE = "BCSPenSelectionViewModel.Title";
  public static final String REPORT_BODY_CONDITION_SCORE_CHART_NAME = "Report.Bcs.ChartName";
  public static final String REPORT_LOCOMOTION_SCORE_CHART_NAME =
      "Report.LocomotionScore.chartName";
  public static final String REPORT_AVERAGE = "Average";
  public static final String REPORT_STANDARD_DEVIATION = "StandardDeviationScoreTitle";
  public static final String REPORT_AVG_MANURE_SCORE =
      "EditDatesForComparisonViewModel.ManureScoreAverage";
  public static final String REPORT_GOAL = "WalkthroughReportViewModel.Goals";
  public static final String REPORT_MIN = "RumenHealthTMRParticleScorePenTableInputViewModel.Min";
  public static final String REPORT_MAX = "RumenHealthTMRParticleScorePenTableInputViewModel.Max";
  public static final String REPORT_MANURE_SCORE = "NotebookManureScoreLanding";
  public static final String REPORT_MILK_PRODUCTION =
      "MilkSoldEvaluationChartsViewModel.MilkProduction";
  public static final String REPORT_COMPONENT_YIELD =
      "MilkSoldEvaluationChartsViewModel.ComponentYield";
  public static final String REPORT_COMPONENT_EFFICIENCY =
      "MilkSoldEvaluationChartsViewModel.ComponentEfficiency";
  public static final String REPORT_MILK_FAT_PERCENT = "SiteDetailsSetupViewModel.MilkFatPercent";
  public static final String REPORT_MILK_PROTEIN_PERCENT = "MilkSoldPickupViewModel.MilkProtein";
  public static final String REPORT_SOMATIC_CELL_COUNT =
      "MilkSoldEvaluationChartsViewModel.SomanticCellCount";
  public static final String REPORT_MILK_UREA = "MilkSoldEvaluationChartsViewModel.MilkUreaMeasure";
  public static final String REPORT_DRY_MATTER_INTAKE =
      "MilkSoldEvaluationChartsViewModel.DryMatterIntake";
  public static final String REPORT_FEED_EFFICIENCY =
      "MilkSoldEvaluationChartsViewModel.FeedEfficiency";
  public static final String REPORT_MILK_PRODUCTION_AND_DIM =
      "MilkSoldEvaluationChartsListViewModel.MilkProductionDIM";
  public static final String REPORT_COMPONENT_YIELD_AND_EFFICIENCY =
      "MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency";
  public static final String REPORT_MILK_FAT_PERCENT_AND_MILK_PROTEIN_PERCENT =
      "MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent";
  public static final String REPORT_SOMATIC_CELL_COUNT_AND_MILK_UREA =
      "MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea";
  public static final String REPORT_DRY_MATTER_INTAKE_AND_FEED_EFFICIENCY =
      "MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency";

  public static final String REPORT_METABOLIC_INCIDENCE_SHEET_0 =
      "MetabolicIncidenceChartsViewModel.GraphTitle";
  public static final String REPORT_METABOLIC_INCIDENCE_CASES =
      "MetabolicIncidenceInputsViewModel.IncidenceCases";
  public static final String REPORT_GOAL_PERCENTAGE =
      "MetabolicIncidenceChartsViewModel.GoalPercent";
  public static final String REPORT_INCIDENCE_PERCENTAGE =
      "MetabolicIncidenceChartsViewModel.IncidencePercent";
  public static final String REPORT_METABOLIC_INCIDENCE_SHEET_1 =
      "MetabolicIncidenceChartsViewModel.DisorderGraphTitle";
  public static final String REPORT_CURRENT = "MetabolicIncidenceChartsViewModel.Current";
  public static final String REPORT_METABOLIC_INCIDENCE_CHART_NAME_0 =
      "MetabolicIncidenceChartsViewModel.GraphTitle";
  public static final String REPORT_METABOLIC_INCIDENCE_CHART_NAME_1 =
      "MetabolicIncidenceChartsViewModel.DisorderGraphTitle";
  public static final String REPORT_AVG_RUMEN_FILL_SCORE = "Report.AvgRumenFillScore";
  public static final String REPORT_PEN_TIME_BUDGET_TIME_REMAINING =
      "Report.PenTimeBudgetTimeRemaining";
  public static final String REPORT_PEN_TIME_BUDGET_TIME_REQUIRED =
      "Report.PenTimeBudgetTimeRequired";
  public static final String REPORT_FILL_HERD_ANALYSIS_CHART_NAME =
      "WalkthroughReportViewModel.RumenFill";
  public static final String REPORT_TOP = "Top";
  public static final String REPORT_MID_1 =
      "RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title";
  public static final String REPORT_MID_2 =
      "RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle";
  public static final String REPORT_TRAY = "Tray";
  public static final String REPORT_ON_SCREEN_PERCENTAGE = "Report.PercentageOnScreen";
  public static final String REPORT_FORAGE_PEN_STATE = "Report.ForagePennState";
  public static final String PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_LABEL =
      "Report.PenTimeBudget.TimeAvailableForResting.Label";
  public static final String PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_CATEGORY_LABEL =
      "Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel";
  public static final String REPORT_RUMEN_HEALTH_TMR_PARTICLE_SCORE = "TMRParticleScore";
  public static final String REPORT_RETURN_OVER_FEED_KG_PER_FAT = "Rof.Kg.Per.Fat";
  public static final String REPORT_RETURN_OVER_FEED = "ReturnOverFeed";


  public static final String RETURN_OVER_FEED = "ReturnOverFeed";
  public static final String REPORT_HEAT_STRESS_TEMPERATURE_IN_CENTIGRADE =
      "Report.Heatstress.Temperature.In.Celcius";
  public static final String REPORT_HEAT_STRESS_TEMPERATURE_IN_FARENHIET =
      "Report.Heatstress.Temperature.In.Farenhiet";
  public static final String REPORT_HEAT_STRESS_IN_TAKE_ADJUSTMENT =
      "Report.Heatstress.Intake.Adjustment";
  public static final String REPORT_HEAT_STRESS_ESTIMATED_DRY_MATTER_INTAKE =
      "Report.Heatstress.Estimated.Dry.Matter.Intake";
  public static final String REPORT_HEAT_STRESS_LOSS_OF_ENERGY_CONSUMED =
      "Report.Heatstress.Loss.Of.Energy.Consumed";
  public static final String REPORT_HEAT_STRESS_MILK_VALUE_LOSS_PER_DAY =
      "Report.Heatstress.Milk.Value.Loss.Perday";
  public static final String REPORT_HEAT_STRESS_DMI_ADJUSTMENT = "Report.Heatstress.Dmi.Adjustment";
  public static final String REPORT_HEAT_STRESS_REDUCTION_IN_DMI =
      "Report.Heatstress.Reduction.In.Dmi";
  public static final String REPORT_HEAT_STRESS_ENERGY_EQUIVALENT_MILK_LOSS =
      "Report.Heatstress.Energy.Equivalent.Milk.Loss";
  public static final String REPORT_HEAT_STRESS_MILK_VALUE_LOSS_PER_MONTH =
      "Report.Heatstress.Milk.Value.Loss.PerMonth";

  public static final String REPORT_PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_HOURS =
      "Report.Pentime.Budget.Hours";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING =
      "HealthToolsViewModel.RumenHealthManureScreening";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_TOP =
      "Report.RumenHealthManureScreening.Top";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_MIDDLE =
      "Report.RumenHealthManureScreening.Middle";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_BOTTOM =
      "Report.RumenHealthManureScreening.Bottom";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_TOP_GOAL_MIN =
      "Report.RumenHealthManureScreening.TopGoalMin";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_TOP_GOAL_MAX =
      "Report.RumenHealthManureScreening.TopGoalMax";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_MIDDLE_GOAL_MIN =
      "Report.RumenHealthManureScreening.MiddleGoalMin";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_MIDDLE_GOAL_MAX =
      "Report.RumenHealthManureScreening.MiddleGoalMax";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_BOTTOM_GOAL_MIN =
      "Report.RumenHealthManureScreening.BottomGoalMin";
  public static final String REPORT_RUMEN_HEALTH_MANURE_SCREENING_BOTTOM_GOAL_MAX =
      "Report.RumenHealthManureScreening.BottomGoalMax";
  public static final String VISIT_REPORT = "Report.Visit.Report";
  public static final String VISIT_REPORT_FOOTER_PATENT = "Visit.Report.Footer.Patent";
  public static final String VISIT_REPORT_FOOTER_DETAILS = "NewVisitViewModel.Title";
  public static final String NULL_VALUES_NOT_ALLOWED = "Null-values-not-allowed";
  public static final String LENGTH_EXCEED_ALLOWED_LIMIT = "Length-exceed-allowed-limit";
  public static final String NOT_MATCHING_WITH_ALLOWED_VALUES = "Not-matching-with-allowed-values";
  public static final String LIFT_SYNC_FAILED = "Lift-Sync-Fail";
  public static final String NO_USER_FOUND = "No-User-Found";
  public static final String ACCOUNT_NOT_SYNCED_TO_LIFT = "Account-Not-Synced-To-Lift";
  public static final String NO_SITE_NOT_SYNCED_TO_LIFT = "Site-Not-Synced-To-Lift";
  public static final String CALF_HEIFER_TITLE = "VisitViewModel.CalfHeiferItem";
  public static final String HEAT_STRESS_TITLE = "HeatstressTableViewModel.Title";
  public static final String PENTIME_BUDGET_TITLE = "VisitSummaryViewModel.PenTimeTitle";
  public static final String BODY_CONDITION_TITLE = "BCSPenSelectionViewModel.Title";
  public static final String METABOLIC_INCIDENCE_TITLE = "MetabolicIncidenceMasterViewModel.Title";
  public static final String RUMEN_HEALTH_MANURE_SCORE_TITLE =
      "RumenHealthManureLandingViewModel.Title";
  public static final String FORAGE_AUDIT_TITLE = "NutritionViewModel.NutritionForage";
  public static final String FORAGE_INVENTORIES_TITLE = "PileBunkerCapacities";
  public static final String MILK_SOLD_EVALUATION_TITLE = "MilkSoldSpinnerViewModel.Title";
  public static final String ROBOTIC_MILK_EVALUATION_TITLE =
      "RoboticMilkEvaluationSpinnerViewModel.Title";
  public static final String PROFITABILITY_ANALYSIS_DATE = "ProfitablityAnalysis.Date";
  public static final String PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION =
      "ProfitabilityAnalysis.TotalProduction";
  public static final String PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION_CONCENTRATED =
      "ProfitabilityAnalysis.TotalProduction.Concentrated";
  public static final String PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION_CHART_TITLE =
      "ProftabilityAnalysis.TotalProduction.Chart.Title";
  public static final String PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY =
      "ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day";
  public static final String PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM =
      "ProfitabilityAnalysis.Production.In.150.Dim";
  public static final String PROFITABILITY_ANALYSIS_MILK_PRICE = "ProfitabilityAnalysis.Milk.Price";
  public static final String PROFITABILITY_ANALYSIS_TOTAL_DIET_COST =
      "ProfitabilityAnalysis.Total.Diet.Cost";
  public static final String PROFITABILITY_ANALYSIS_IOFC = "ProfitabilityAnalysis.Iofc";
  public static final String PROFITABILITY_ANALYSIS_FEEDING_COST_PER_LITRE_OF_MILK =
      "ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk";
  public static final String PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY_CHART_TITLE =
      "ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title";
  public static final String PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM_CHART_TITLE =
      "ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title";
  public static final String PROFITABILITY_ANALYSIS_MILK_PRICE_CHART_TITLE =
      "Profitability.Analysis.Milk.Price.Chart.Title";
  public static final String REPORT_RETURN_OVER_FEED_YAXIS = "Report.Return.Over.Feed.YAxis";
}
