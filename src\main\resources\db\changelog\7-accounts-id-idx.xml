<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="007" author="AlexNenkov">
        <sql>
            CREATE UNIQUE INDEX IF NOT EXISTS account_document_id_idx ON public.accounts ( (account_document->>'id') ) ;
        </sql>
    </changeSet>

</databaseChangeLog>