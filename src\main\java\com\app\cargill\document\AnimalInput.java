/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.Breed;
import com.app.cargill.constants.ProductionSystem;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimalInput implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("AnimalsInHerd")
  private Double animalsInHerd;

  @JsonProperty("TotalNumberOfCows")
  private Double totalNumberOfCows;

  @JsonProperty("TotalNumberOfLactatingAnimals")
  private Double totalNumberOfLactatingAnimals;

  @JsonProperty("Breed")
  private Breed breed;

  @JsonProperty("ProductionSystem")
  private ProductionSystem productionSystem;
}
