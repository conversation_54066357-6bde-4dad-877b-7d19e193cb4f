/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class HeatStressToolDto extends EditableDocumentBaseDto {

  /** */
  private static final long serialVersionUID = 1L;

  private UUID visitId;

  private Double avgMilkWeightInkg;

  private Double avgDMIWeightInkg;

  private Double avgNELWeightInkg;

  private Double avgMilkFatPercent;

  private Double avgMilkProteinPercent;

  private Double temperatureInCelsius;

  private Double humidityPercent;

  private Integer hoursExposedToSun;

  private Double temperatureHumidityInCelsius;

  private Double intakeAdjustmentPercent;

  private Double dmiReductionPercent;

  private Double estimatedDryMatterIntakeWeightInkg;

  private Double reductionInDMIWeightInkg;

  private Double lossOfEnergyConsumedInMcal;

  private Double energyEquivalentMilkLossWeightInkg;

  // Added the below fields for HeatStressTool Changes
  private Double avgLactatingAnimals;

  private Double avgCurrentMilkPrice;
}
