/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.model.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
class LiftAccountMergeServiceTest {

  @Mock private LiftApiService liftApi;
  @Mock private LiftUserService liftUserService;
  @Mock ResourceBundleMessageSource bundleMessageSource;
  @InjectMocks private LiftAccountMergeService accountMergeService;
  @Mock LiftConfig liftConfig;
  @Mock private AccountsRepository accountsRepository;
  @Mock private LiftApiReactiveService liftApiReactiveService;
  @Mock private UserRepository userRepository;

  @Test
  void fetchingMultiplePagesOfSimpleAccountsReturnsAccounts() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse response1 =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/Accounts_merge_object.json"),
            new TypeReference<SalesforceRecordsResponse<AccountMergeRecord>>() {});
    response1.setNextRecordsUrl("next-url");
    when(liftApi.getRecordsQuery(any(), any(), any(), any())).thenReturn(response1);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    List<AccountMergeRecordDto> accounts = accountMergeService.getAllMergedAccounts(null);
    assertNotNull(accounts);
    assertEquals(16, accounts.size());
  }
}
