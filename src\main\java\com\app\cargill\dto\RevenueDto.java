/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RevenueDto implements Serializable {
  private static final long serialVersionUID = 1L;

  private Double bfRevenue;
  private Double proteinRevenue;
  private Double otherSolidsRevenue;
  private Double subtotal;
  private Double deductionsPricePerCowPerDay;
  private Double snfNonPayment;
  private Double totalRevenueCowDay;
  private Double totalRevenuePricePerKgButterFat;
  private Double totalRevenuePerHl;
}
