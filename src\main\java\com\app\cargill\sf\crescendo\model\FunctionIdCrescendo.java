/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum FunctionIdCrescendo {
  OWNER("Owner"),
  MANAGER("Manager"),
  WAREHOUSE("Warehouse"),
  FINANCE("Finance"),
  TRUCK_SALESMAN("Truck Salesman"),
  SALES_REPRESENTATIVE("Sales Representative"),
  VET("Vet"),
  CUSTOMER_SERVICE("Customer Service"),
  NUTRITIONIST("Nutritionist"),
  OTHER("Other"),
  INACTIVE("Inactive");

  private final String value;

  @JsonCreator
  FunctionIdCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
