/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

class AwsCredentialsFactoryTest {

  @Test
  void whenNotIAmRoleAndAccessAndSecretKeysAreProvidedCredentialsAreReturned() {
    AwsCredentialsFactory awsCredentialsFactory = new AwsCredentialsFactory();

    awsCredentialsFactory.setAmazonSecretKey("x");
    awsCredentialsFactory.setAmazonAccessKey("x");

    AwsCredentialsProvider result = awsCredentialsFactory.getCredentialsProvider();
    assertNotNull(result);
    assertTrue(result instanceof StaticCredentialsProvider);
  }

  @Test
  void whenNotIAmRoleAndAccessAndSecretKeysAreNotProvidedCredentialsAreReturned() {
    AwsCredentialsFactory awsCredentialsFactory = new AwsCredentialsFactory();

    AwsCredentialsProvider result = awsCredentialsFactory.getCredentialsProvider();
    assertNotNull(result);
    assertTrue(result instanceof DefaultCredentialsProvider);
  }

  @Test
  void whenIAmRoleAndAccessAndSecretKeysAreProvidedCredentialsAreReturned() {
    AwsCredentialsFactory awsCredentialsFactory = new AwsCredentialsFactory();

    awsCredentialsFactory.setAmazonSecretKey("x");
    awsCredentialsFactory.setAmazonAccessKey("x");

    AwsCredentialsProvider result = awsCredentialsFactory.getCredentialsProvider();
    assertNotNull(result);
    assertTrue(result instanceof StaticCredentialsProvider);
  }
}
