/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ToString
public class UserAccessUpdateModel implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("DE_Account__c")
  private String accountId;

  @JsonProperty("Account_Access_Level__c")
  private String accessLevel = "Read/Write";

  @JsonProperty("DE_Role_Name__c")
  private String roleName;

  @JsonProperty("DE_User__c")
  private String userId;
}
