/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.cosmos.model.AccountCosmos;
import com.app.cargill.cosmos.repo.AccountsCosmosRepository;
import com.app.cargill.cosmos.repo.SitesCosmosRepository;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.AnimalAnalysisDetailsToolItem;
import com.app.cargill.document.AnimalAnalysisTool;
import com.app.cargill.document.AnimalAnalysisToolItem;
import com.app.cargill.document.Barn;
import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.Contact;
import com.app.cargill.document.CudChewingByPen;
import com.app.cargill.document.CudChewingCount;
import com.app.cargill.document.CudChewingCowCount;
import com.app.cargill.document.CudChewingTool;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.HerdAnalysisGoal;
import com.app.cargill.document.LocomotionTool;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.RumenHealthToolItem;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SegmentsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class AccountsMigrationTest {
  @Mock private AccountsCosmosRepository cosmosRepository;

  @Mock private AccountsRepository postgresAccountsRepository;

  @Mock private SitesCosmosRepository sitesCosmosRepository;

  @Mock private VisitsRepository postgresVisitsRepository;

  @Mock private SitesRepository postgresSitesRepository;

  @Mock private SegmentsRepository segmentsRepository;

  @InjectMocks private AccountsMigration accountsMigration;

  @BeforeEach
  void setUp() {
    lenient().when(postgresAccountsRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenAccountsMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    AccountCosmos acc =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/accounts.json"), AccountCosmos.class);

    Iterable<AccountCosmos> accountsList = List.of(acc, acc);
    when(cosmosRepository.findAll()).thenReturn(accountsList);
    MigrationResult result = accountsMigration.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenAccountsPostMigrationIsCalledWithoutMigrationTypeEmptyResultsReturned() {

    MigrationResult result = null;
    try {
      result = accountsMigration.postMigration(null).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }

    assertNotNull(result);
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result =
          accountsMigration
              .migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.ACCOUNTS))
              .get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  private Sites loadSite() {
    return Sites.builder()
        .siteDocument(
            SiteDocument.builder()
                .id(UUID.fromString("********-9ea9-475a-a0fc-39dc8357d44"))
                .accountId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
                .visits(
                    List.of(
                        SiteVisit.builder()
                            .visitDate(Instant.now())
                            .status(VisitStatus.Published)
                            .build()))
                .barns(List.of(Barn.builder().build()))
                .dataSourceMappings(List.of())
                .origination(ApplicationMapping.LM_SITE_SYSTEM_NAME)
                .dateOfLastVisit(Instant.now())
                .build())
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Accounts loadAccount() {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .id(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .users(Set.of("user1", "user2"))
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .sFDCContactId("sf-1")
                        .firstName("John")
                        .lastName("Doe")
                        .build(),
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("Jane")
                        .lastName("Doe")
                        .build()))
            .subTypeID(SubTypeId.FarmProducer)
            .physicalAddress(Address.builder().build())
            .dateOfLastVisit(Instant.now())
            .build();
    return Accounts.builder()
        .accountDocument(accountDocument)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }

  private Visits loadVisit() {

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .siteId(UUID.fromString("********-9ea9-475a-a0fc-39dc8357d44"))
            .status(VisitStatus.InProgress)
            .rumenHealth(
                RumenHealthTool.builder()
                    .pens(
                        List.of(
                            RumenHealthToolItem.builder()
                                .cudChewingCowsCount(
                                    CudChewingCowCount.builder()
                                        .countNo(5)
                                        .countYes(10)
                                        .noPercent(50.5)
                                        .yesPercent(60.5)
                                        .totalCount(0.0)
                                        .build())
                                .cudChewsCount(
                                    List.of(
                                        CudChewingCount.builder()
                                            .chewsCount(10)
                                            .cowNumber(10)
                                            .build()))
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .build()))
                    .visitId(UUID.randomUUID())
                    .goals(
                        List.of(
                            HerdAnalysisGoal.builder()
                                .cudChews(3.6)
                                .percentChewing(50.6)
                                .stage(LactationStage.FarOffDry)
                                .build()))
                    .build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .cudChewing(
                CudChewingTool.builder()
                    .createTimeUtc(Instant.now())
                    .createUser("admin@admin")
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder()
                                .countNo(5)
                                .countYes(5)
                                .noPercent(0.0)
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .yesPercent(0.0)
                                .build()))
                    .build())
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        Arrays.asList(
                            AnimalAnalysisToolItem.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    Arrays.asList(
                                        AnimalAnalysisDetailsToolItem.builder()
                                            .locomotionScore(1.0)
                                            .build()))
                                .build()))
                    .build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .visitDate(Instant.parse("2022-10-05T09:50:03.028Z"))
            .visitName("John Doe 2022-09-29T08:56:34.390Z")
            .build();

    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }

  @Test
  void whenAccountsMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    AccountCosmos acc1 = new AccountCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    AccountCosmos acc2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/accounts.json"), AccountCosmos.class);

    Iterable<AccountCosmos> accountsList = List.of(acc1, acc2);
    when(cosmosRepository.findAll()).thenReturn(accountsList);

    MigrationResult result = accountsMigration.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }

  @Test
  void whenAccountIsMigratedAllIsOk() {
    String email = "<EMAIL>";
    AccountCosmos accountCosmos = getAccountCosmos();
    when(cosmosRepository.getAccountsByUserEmail(email)).thenReturn(List.of(accountCosmos));
    when(postgresAccountsRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    StepVerifier.create(accountsMigration.moveUserAccounts(email))
        .assertNext(Assertions::assertNotNull)
        .verifyComplete();
  }

  @Test
  void whenThereIsDbErrorExceptionIsReturned() {
    String email = "<EMAIL>";
    AccountCosmos accountCosmos = getAccountCosmos();

    when(cosmosRepository.getAccountsByUserEmail(email))
        .thenReturn(List.of(accountCosmos, accountCosmos, accountCosmos, accountCosmos));

    when(postgresAccountsRepository.findByAccountId(any()))
        .thenReturn(null)
        .thenThrow(new IllegalArgumentException("Error saving in db"))
        .thenThrow(new IllegalArgumentException("Error saving in db"))
        .thenReturn(null);
    when(postgresAccountsRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    StepVerifier.create(accountsMigration.moveUserAccounts(email))
        .expectNextCount(1)
        .expectError()
        .verify();
  }

  private AccountCosmos getAccountCosmos() {
    AccountCosmos accountCosmos = mock(AccountCosmos.class);
    when(accountCosmos.getId()).thenReturn(UUID.randomUUID().toString());
    when(accountCosmos.getPrimaryContactId()).thenReturn(UUID.randomUUID().toString());
    when(accountCosmos.getLastModifiedTimeUtc()).thenReturn(new DateEpoch(Instant.now()));
    when(accountCosmos.getSegmentStepOneId()).thenReturn("0");
    when(accountCosmos.getGoldenRecordId()).thenReturn("some_random_id");
    return accountCosmos;
  }
  //
  //  @Test
  //  void whenThereIsNoSuchRecordInCosmosReturnException() {
  //    String email = "<EMAIL>";
  //    when(postgresUsersRepository.findByUserName(email)).thenReturn(null);
  //    when(usersCosmosRepository.getUsersByEmail(email)).thenReturn(List.of());
  //    StepVerifier.create(usersMigration.moveRecord(email))
  //        .expectErrorMatches(
  //            p -> p instanceof MigrationException && p.getMessage().contains("User not found"))
  //        .verify();
  //  }
  //
  //  @Test
  //  void whenThereAreMultipleRecordsInCosmosReturnException() {
  //    String email = "<EMAIL>";
  //    when(postgresUsersRepository.findByUserName(email)).thenReturn(null);
  //    when(usersCosmosRepository.getUsersByEmail(email))
  //        .thenReturn(List.of(mock(UserCosmos.class), mock(UserCosmos.class)));
  //    StepVerifier.create(usersMigration.moveRecord(email))
  //        .expectErrorMatches(
  //            p ->
  //                p instanceof MigrationException
  //                    && p.getMessage().contains("More than one record found"))
  //        .verify();
  //  }
  //
  //  @Test
  //  void whenUserAlreadyExistsReturnTheResult() {
  //    String email = "<EMAIL>";
  //    User mockedUser = mock(User.class);
  //    when(postgresUsersRepository.findByUserName(email)).thenReturn(mockedUser);
  //    StepVerifier.create(usersMigration.moveRecord(email))
  //        .assertNext(user -> assertEquals(mockedUser, user))
  //        .verifyComplete();
  //  }
}
