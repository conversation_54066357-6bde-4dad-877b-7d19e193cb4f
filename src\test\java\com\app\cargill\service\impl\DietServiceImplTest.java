/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.DietSource;
import com.app.cargill.document.AnalyzeDietOptimization;
import com.app.cargill.document.AnimalClass;
import com.app.cargill.document.DietDocument;
import com.app.cargill.document.FormulateDietOptimization;
import com.app.cargill.dto.DietDto;
import com.app.cargill.model.Diets;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class DietServiceImplTest {

  // Do not delete to prevent NPE for UserService
  @Mock private IUserService userServiceImpl;
  @Mock private DietRepository dietRepository;
  @Mock private AccountsRepository accountsRepository;
  @InjectMocks private DietServiceImpl dietServiceImpl;

  @Mock private AnimalClassServiceImpl animalClassService;

  @Test
  void whenValidDietsAreReturned() {

    Page<Diets> diets =
        new PageImpl<>(
            List.of(
                Diets.builder()
                    .dietDocument(
                        DietDocument.builder()
                            .animalType(AnimalClass.builder().subClass("Fresh").build())
                            .build())
                    .updatedDate(Date.from(Instant.now()))
                    .build()));
    // when(animalClassService.getAnimalTypeId("Fresh")).thenReturn(UUID.randomUUID());
    when(dietRepository.findByAccountIdAndDietSourceAndUpdatedDate(any(), any(), any(), any()))
        .thenReturn(diets);
    PageImpl<DietDto> result =
        dietServiceImpl.getDietsPaginated(0, 10, "id", Instant.now(), "desc", DietSource.MAX);
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenValidDietsAreReturnedWithAnalyzedAndFormulateDiet() {

    Page<Diets> diets =
        new PageImpl<>(
            List.of(
                Diets.builder()
                    .dietDocument(
                        DietDocument.builder()
                            .animalType(AnimalClass.builder().subClass("Fresh").build())
                            .analyzeOptimization(AnalyzeDietOptimization.builder().build())
                            .formulateOptimization(FormulateDietOptimization.builder().build())
                            .build())
                    .updatedDate(Date.from(Instant.now()))
                    .build()));

    when(dietRepository.findByAccountIdAndDietSourceAndUpdatedDate(any(), any(), any(), any()))
        .thenReturn(diets);
    PageImpl<DietDto> result =
        dietServiceImpl.getDietsPaginated(0, 10, "id", Instant.now(), "desc", DietSource.MAX);
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }
}
