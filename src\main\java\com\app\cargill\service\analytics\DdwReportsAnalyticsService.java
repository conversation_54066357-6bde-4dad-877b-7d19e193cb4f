/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.analytics;

import com.app.cargill.model.Accounts;
import com.app.cargill.model.AnalyticsDataPoint;
import com.app.cargill.model.AnalyticsDataPoint.DataPointName;
import com.app.cargill.model.Sites;
import com.app.cargill.model.User;
import com.app.cargill.model.analytics.AnalyticsDataResponse.DataPointContainer;
import com.app.cargill.model.analytics.ReportDownloadMeta;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.AnalyticsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.HashMap;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
@Slf4j
public class DdwReportsAnalyticsService {
  private final IUserService userService;
  private final SitesRepository sitesRepository;
  private final AccountsRepository accountsRepository;
  private final AnalyticsRepository<ReportDownloadMeta> analyticsRepository;

  public void logDdwReport(String siteId, DataPointName dataPointName) {
    try {

      ReportDownloadMeta reportDownloadMeta = new ReportDownloadMeta();
      reportDownloadMeta.setSiteId(siteId);
      User user = userService.getLoggedUserData();
      if (user != null) {
        reportDownloadMeta.setUserId(user.getUserDocument().getId().toString());
        reportDownloadMeta.setUserEmail(user.getUserDocument().getUserName());
        reportDownloadMeta.setUserCountry(user.getUserDocument().getCountryId().name());
      }

      Sites site = sitesRepository.findBySiteId(siteId);
      if (site != null) {
        reportDownloadMeta.setAccountId(site.getSiteDocument().getAccountId().toString());
        Accounts account =
            accountsRepository.findByAccountId(site.getSiteDocument().getAccountId().toString());
        if (account != null) {
          reportDownloadMeta.setAccountName(account.getAccountDocument().getAccountName());
        }
      }

      AnalyticsDataPoint<ReportDownloadMeta> dataPoint = new AnalyticsDataPoint<>();
      dataPoint.setLocalId(UUID.randomUUID().toString());
      dataPoint.setDataPointName(dataPointName);
      dataPoint.setMetaData(reportDownloadMeta);

      analyticsRepository.save(dataPoint);
    } catch (Exception e) {
      log.error("DDW_REPORT_DOWNLOAD_ERROR", e);
    }
  }

  public Mono<DataPointContainer> fetchAllDetailedReports(Instant from, Instant to) {
    return fetchAllData(DataPointName.DDW_DETAILED_REPORT_DOWNLOAD, from, to);
  }

  public Mono<DataPointContainer> fetchAllSummaryReports(Instant from, Instant to) {
    return fetchAllData(DataPointName.DDW_SUMMARY_REPORT_DOWNLOAD, from, to);
  }

  private Mono<DataPointContainer> fetchAllData(
      DataPointName dataPointName, Instant from, Instant to) {
    DataPointContainer container = new DataPointContainer();
    container.setDataPointName(dataPointName);
    return Mono.from(
        Flux.fromIterable(analyticsRepository.findAllByType(dataPointName.name(), from, to))
            .map(this::mapToDto)
            .collectList()
            .map(
                rdList -> {
                  container.setDataPoints(rdList);
                  container.setTotalRecords(rdList.size());
                  return container;
                }));
  }

  private ReportDownloadDto mapToDto(AnalyticsDataPoint<HashMap<String, String>> dbItem) {
    ReportDownloadDto reportDownloadDto = new ReportDownloadDto();
    reportDownloadDto.setCreatedTime(dbItem.getCreatedDate().toInstant());
    reportDownloadDto.setAccountId(dbItem.getMetaData().get("accountId"));
    reportDownloadDto.setAccountName(dbItem.getMetaData().get("accountName"));
    reportDownloadDto.setUserId(dbItem.getMetaData().get("userId"));
    reportDownloadDto.setUserEmail(dbItem.getMetaData().get("userEmail"));
    reportDownloadDto.setUserCountry(dbItem.getMetaData().get("userCountry"));
    reportDownloadDto.setSiteId(dbItem.getMetaData().get("siteId"));
    return reportDownloadDto;
  }
}
