/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ManureScreenerScoreGoalToolItemDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private List<UUID> visitsSelected;

  private UUID penId;

  private String penName;

  private Boolean isToolItemNew;

  private String goalTitle;

  private Double topGoalMinimumPercent;

  private Double topGoalMaximumPercent;

  private Double midGoalMinimumPercent;

  private Double midGoalMaximumPercent;

  private Double bottomGoalMinimumPercent;

  private Double bottomGoalMaximumPercent;

  private ToolStatuses toolStatus;
}
