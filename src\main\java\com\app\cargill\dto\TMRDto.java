/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class TMRDto implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private HerdProfileDto herdProfile;
  private FeedingReturnOverFeedDto feeding;
  private MilkProductionDto milkProduction;
  private MilkProductionOutputsDto milkProductionOutputs;
  private CalculatedOutputsDto calculatedOutputs;
  private SummaryDto summary;
}
