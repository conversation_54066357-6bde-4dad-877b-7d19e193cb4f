/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.model;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.cosmos.model.tools.RumenHealthTMRParticleScoreToolItemCosmos;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.List;
import org.junit.jupiter.api.Test;

class ActivitiesCosmosTest {
  @Test
  void jsonIsParsedCorrect() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    ActivityCosmos data =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/activity-sample.json"),
            ActivityCosmos.class);
    assertNotNull(data);
    assertNotNull(data.getLastModifiedTimeUtc());
  }

  @Test
  void tmrExistsTest() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    VisitCosmos data =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/cosmos-tmr-visit.json"), VisitCosmos.class);
    List<RumenHealthTMRParticleScoreToolItemCosmos> tmrScores =
        data.getTmrParticleScore().getTmrScores();
    assertNotNull(tmrScores);
    assertFalse(tmrScores.isEmpty());
  }
}
