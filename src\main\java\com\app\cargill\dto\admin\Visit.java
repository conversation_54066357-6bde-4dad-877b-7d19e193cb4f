/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.admin;

import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class Visit {
  private UUID uuid;
  private String name;
  private String accountName;
  private String siteName;
  private Instant visitDate;
  private String userEmail;
}
