/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.authconfig.AccessTokenVerifierFactory;
import com.app.cargill.authconfig.JwtAuthenticationFilter;
import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.controller.GlobalExceptionHandler;
import com.app.cargill.dto.DiscoveryKeyDto;
import com.app.cargill.dto.UserDetailsDto;
import com.app.cargill.repository.UserRepository;
import com.okta.jwt.JwtVerificationException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.filter.OncePerRequestFilter;

@ExtendWith(MockitoExtension.class)
class JwtValidatorRequestFilterTest {

  private MockHttpServletRequest request;
  private MockHttpServletResponse response;
  private MockFilterChain chain;

  @Mock private HttpServlet servlet;
  @Mock private UserRepository userRepository;
  @Mock private OktaProperties oktaProperties;
  @Mock private AzureADProperties azureADProperties;
  @Mock private AccessTokenVerifierFactory verifierFactory;
  @Mock AuthenticationException authenticationException;
  private List<OncePerRequestFilter> invocations;
  @InjectMocks @Spy private JwtAuthenticationFilter filter;

  @BeforeEach
  public void setup() {
    servlet =
        new HttpServlet() {

          /** */
          private static final long serialVersionUID = 1L;
        };
    request = new MockHttpServletRequest();
    response = new MockHttpServletResponse();
    chain = new MockFilterChain();
    invocations = new ArrayList<>();
  }

  @Test
  void doFilterOnce() throws ServletException, IOException {
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void whenPrepareUserContextReturnsValidResult() {
    UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken =
        JwtAuthenticationFilter.prepareUserContext(
            Objects.requireNonNull(
                JwtAuthenticationFilter.parseJWT(
                    "eyJraWQiOiI4TTg0RmVPZlB2QU90VUloU09zdDk2ZVAzaUFsUkVLaTd6QU53WkNsbVZnIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rt07CSnCsiFv5sW_INdOOM8PEisLBmo62qJU5MjHaVqiAkT9chcpovdWkxV7y3WBCpQoI0BOrNB9egqW1_XLTE2BgwVEpIQ6IpYW2P45qtZdwHOLqNrKyLcz_AWzCo_mkIk0mD1xTJNCqR44bi2ooLSuCzhocFGbgaC_gvJcBpXizEWDln0REFc6hecaDcwa8BEMyubbygCDuclUm10qlWb5GLfCz4MK7wklBAxqyP7FSaB4QmZb3jmFOG2d06W3SBGCvy6edTB70X1VMznw8hkC7wQ8xaLlAwmJOltDOxVSR-99haEM3wHC-HCJSF9n0nuQ9zhssDk0m4ltMtwAJg")),
            request,
            AuthenticationPlatform.OKTA);
    Assertions.assertNotNull(usernamePasswordAuthenticationToken);
  }

  @Test
  void whenHeaderKeyFoundReturnsValidResult() {
    Map<String, Object> parsedHeaders =
        Objects.requireNonNull(
                JwtAuthenticationFilter.parseJWT(
                    "eyJraWQiOiI4TTg0RmVPZlB2QU90VUloU09zdDk2ZVAzaUFsUkVLaTd6QU53WkNsbVZnIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rt07CSnCsiFv5sW_INdOOM8PEisLBmo62qJU5MjHaVqiAkT9chcpovdWkxV7y3WBCpQoI0BOrNB9egqW1_XLTE2BgwVEpIQ6IpYW2P45qtZdwHOLqNrKyLcz_AWzCo_mkIk0mD1xTJNCqR44bi2ooLSuCzhocFGbgaC_gvJcBpXizEWDln0REFc6hecaDcwa8BEMyubbygCDuclUm10qlWb5GLfCz4MK7wklBAxqyP7FSaB4QmZb3jmFOG2d06W3SBGCvy6edTB70X1VMznw8hkC7wQ8xaLlAwmJOltDOxVSR-99haEM3wHC-HCJSF9n0nuQ9zhssDk0m4ltMtwAJg"))
            .right()
            .toJSONObject();
    parsedHeaders.put("x5t", UUID.randomUUID().toString());
    parsedHeaders.put("aud", "api://aud");
    boolean found = filter.headerKeyFound(prepareDiscoveryKeyDto(), parsedHeaders, "api://aud");
    Assertions.assertFalse(found);
  }

  @Test
  void whenHeaderKeyFoundWithSameKeysReturnsValidResult() {
    Map<String, Object> parsedHeaders = new HashMap<>();
    parsedHeaders.put("x5t", "x5t");
    parsedHeaders.put("kid", "kid");
    parsedHeaders.put("aud", "api://aud");
    DiscoveryKeyDto discoveryKeyDto = prepareDiscoveryKeyDto();
    discoveryKeyDto.getKeys().get(0).put("x5t", "x5t");
    discoveryKeyDto.getKeys().get(0).put("kid", "kid");
    boolean found = filter.headerKeyFound(discoveryKeyDto, parsedHeaders, "api://aud");
    Assertions.assertTrue(found);
  }

  @Test
  void doFilterWithOktaAuthToken() throws ServletException, IOException, JwtVerificationException {
    when(azureADProperties.getIssuer())
        .thenReturn("https://sts.windows.net/ea8bccbf-17d4-447c-8e61-53e08ae27625/");
    when(oktaProperties.getIssuer())
        .thenReturn("https://cargillcustomer-qa.oktapreview.com/oauth2/aushb5mlqe4IiZu3k0h7");
    verifierFactory.getVerifier();
    request.addHeader(
        "Authorization",
        "Bearer"
            + " eyJraWQiOiItaVhDNnpfRExQTVpmNnl2T0xFN2pUMko3blp0Vy1CVmlHdmFTeHg1eDBjIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NFl07pwVLw1et1VCYQ78UBNkHhfCGVWpBWzYuE6Nn3niVQX1JS_eQwTusr_WGwiF_TN6nJSSo7YIQsGf-DUyNbDRBuAHoYN1e-piLmjif6B04f4zSjpdg3A9hciWfVV85Hwi0FQHc_-gUCLgRhhCiTWI8iD89JXO-F9XO3R9V-rIavpKRp2_B7ojKKntIFJJyb8a0nLrWfMqCodY4YoCIG7v7s28II46OXQYjWAh8UyRYtb6cKWqcQWlFnMrhk2UYYRpNR6o1EKWIlTcpkQMxPrqoSgIGgVfGOYX7kiXIpvTdZ9sLUjpyskRliJYocveQum9bNl-ZtgD-fga1PYx9g");
    request.addHeader("Accept-Language", "EN");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterWithNotValidOktaAuthToken()
      throws ServletException, IOException, JwtVerificationException {
    when(azureADProperties.getIssuer())
        .thenReturn("https://sts.windows.net/ea8bccbf-17d4-447c-8e61-53e08ae27625/");
    when(oktaProperties.getIssuer())
        .thenReturn("https://cargillcustomer-qa.oktapreview.com/oauth2/aushb5mlqe4IiZu3k0h7");
    verifierFactory.getVerifier();
    request.addHeader(
        "Authorization",
        "Bearer"
            + " eyJraWQiOiI4TTg0RmVPZlB2QU90VUloU09zdDk2ZVAzaUFsUkVLaTd6QU53WkNsbVZnIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rt07CSnCsiFv5sW_INdOOM8PEisLBmo62qJU5MjHaVqiAkT9chcpovdWkxV7y3WBCpQoI0BOrNB9egqW1_XLTE2BgwVEpIQ6IpYW2P45qtZdwHOLqNrKyLcz_AWzCo_mkIk0mD1xTJNCqR44bi2ooLSuCzhocFGbgaC_gvJcBpXizEWDln0REFc6hecaDcwa8BEMyubbygCDuclUm10qlWb5GLfCz4MK7wklBAxqyP7FSaB4QmZb3jmFOG2d06W3SBGCvy6edTB70X1VMznw8hkC7wQ8xaLlAwmJOltDOxVSR-99haEM3wHC-HCJSF9n0nuQ9zhssDk0m4ltMtwA100g");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  DiscoveryKeyDto prepareDiscoveryKeyDto() {
    Map<String, Object> x5t0 = new HashMap<>();
    x5t0.put("x5t", UUID.randomUUID().toString());
    x5t0.put("kid", UUID.randomUUID().toString());
    DiscoveryKeyDto discoveryKeyDto = DiscoveryKeyDto.builder().keys(new ArrayList<>()).build();
    discoveryKeyDto.getKeys().add(x5t0);
    Map<String, Object> x5t1 = new HashMap<>();
    x5t1.put("x5t", UUID.randomUUID().toString());
    x5t1.put("kid", UUID.randomUUID().toString());
    discoveryKeyDto.getKeys().add(x5t1);
    return discoveryKeyDto;
  }

  @Test
  void doFilterWithAzureAuthToken() throws ServletException, IOException {
    when(azureADProperties.getIssuer())
        .thenReturn("https://sts.windows.net/ea8bccbf-17d4-447c-8e61-53e08ae27625/");
    when(oktaProperties.getIssuer())
        .thenReturn("https://cargillcustomer-qa.oktapreview.com/oauth2/aushb5mlqe4IiZu3k0h7");
    when(oktaProperties.getClientId()).thenReturn(UUID.randomUUID().toString());
    verifierFactory.getAzureDiscoveredKeys();
    request.addHeader(
        "Authorization",
        "Bearer"
            + " eyJraWQiOiJPNGw0czhPWU1GMlJiczhmR0JBYzBWS1ZfcFBqblNmbm9HSmhKY2RMRkJjIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SGXAPAD253dZ-6zE0DXLaJiZyo8uDcR4RFsnmqQUP85otHRQ9ZocK0By5YorIlHB2UCBVK9M0il3DFaiaskReKf1jKQ-SbXZ9JemFg71JZ01x9kaojqN6100hfLunU8X5TAS81hJm4iX6WtTFNM2KstMx1425nC3q3dYdPuns0erQr3CT7PyBBTSouS-ZfjU-C1LzQgRbQs0UPrZ2tm7ZyDyY36nI4aQmcC2oGVYS43H4FWcC-TjphIO58mSPMaIsK0kYEzXcPVU2tev6JzE967pAvrGuMU0PUmmNo7xSX7aA1kuvVTLYN9RVgDjKCjvt2rsu21FSRN0T2zDn4NHOw");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterWithAzureValidAuthToken() throws ServletException, IOException {
    when(azureADProperties.getIssuer())
        .thenReturn("https://sts.windows.net/ea8bccbf-17d4-447c-8e61-53e08ae27625/");
    when(oktaProperties.getIssuer())
        .thenReturn("https://cargillcustomer-qa.oktapreview.com/oauth2/aushb5mlqe4IiZu3k0h7");
    when(oktaProperties.getClientId()).thenReturn(UUID.randomUUID().toString());
    verifierFactory.getAzureDiscoveryKeys();

    request.addHeader(
        "Authorization",
        "Bearer"
            + " eyJraWQiOiJPNGw0czhPWU1GMlJiczhmR0JBYzBWS1ZfcFBqblNmbm9HSmhKY2RMRkJjIiwiYWxnIjoiUlMyNTYifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SGXAPAD253dZ-6zE0DXLaJiZyo8uDcR4RFsnmqQUP85otHRQ9ZocK0By5YorIlHB2UCBVK9M0il3DFaiaskReKf1jKQ-SbXZ9JemFg71JZ01x9kaojqN6100hfLunU8X5TAS81hJm4iX6WtTFNM2KstMx1425nC3q3dYdPuns0erQr3CT7PyBBTSouS-ZfjU-C1LzQgRbQs0UPrZ2tm7ZyDyY36nI4aQmcC2oGVYS43H4FWcC-TjphIO58mSPMaIsK0kYEzXcPVU2tev6JzE967pAvrGuMU0PUmmNo7xSX7aA1kuvVTLYN9RVgDjKCjvt2rsu21FSRN0T2zDn4NHOw");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterWithInvalidToken() throws ServletException, IOException {

    request.addHeader(
        "Authorization",
        "Bearer"
            + " e1yJraWQiOiI4TTg0RmVPZlB2QU90VUloU09zdDk2ZVAzaUFsUkVLaTd6QU53WkNsbVZnIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rt07CSnCsiFv5sW_INdOOM8PEisLBmo62qJU5MjHaVqiAkT9chcpovdWkxV7y3WBCpQoI0BOrNB9egqW1_XLTE2BgwVEpIQ6IpYW2P45qtZdwHOLqNrKyLcz_AWzCo_mkIk0mD1xTJNCqR44bi2ooLSuCzhocFGbgaC_gvJcBpXizEWDln0REFc6hecaDcwa8BEMyubbygCDuclUm10qlWb5GLfCz4MK7wklBAxqyP7FSaB4QmZb3jmFOG2d06W3SBGCvy6edTB70X1VMznw8hkC7wQ8xaLlAwmJOltDOxVSR-99haEM3wHC-HCJSF9n0nuQ9zhssDk0m4ltMtwAJg");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterWithValidToken() throws ServletException, IOException {
    request.addHeader(
        "Authorization",
        "Bearer"
            + "eyJraWQiOiI4TTg0RmVPZlB2QU90VUloU09zdDk2ZVAzaUFsUkVLaTd6QU53WkNsbVZnIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Rt07CSnCsiFv5sW_INdOOM8PEisLBmo62qJU5MjHaVqiAkT9chcpovdWkxV7y3WBCpQoI0BOrNB9egqW1_XLTE2BgwVEpIQ6IpYW2P45qtZdwHOLqNrKyLcz_AWzCo_mkIk0mD1xTJNCqR44bi2ooLSuCzhocFGbgaC_gvJcBpXizEWDln0REFc6hecaDcwa8BEMyubbygCDuclUm10qlWb5GLfCz4MK7wklBAxqyP7FSaB4QmZb3jmFOG2d06W3SBGCvy6edTB70X1VMznw8hkC7wQ8xaLlAwmJOltDOxVSR-99haEM3wHC-HCJSF9n0nuQ9zhssDk0m4ltMtwAJg");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterWithNoAuthToken() throws ServletException, IOException {

    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterMultiOnlyInvokesOnce() throws ServletException, IOException {
    filter.doFilter(request, response, new MockFilterChain(servlet, filter));

    assertEquals(0, invocations.size());
  }

  @Test
  void doFilterOtherSubclassInvoked() throws ServletException, IOException {
    OncePerRequestFilter filter2 =
        new JwtAuthenticationFilter(
            oktaProperties, azureADProperties, verifierFactory, userRepository) {
          @Override
          protected void doFilterInternal(
              HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
              throws ServletException, IOException {
            invocations.add(this);
            filterChain.doFilter(request, response);
          }
        };
    filter.doFilter(request, response, new MockFilterChain(servlet, filter2));

    assertEquals(1, invocations.size());
  }

  @Test
  void whenUnauthorized() throws IOException {
    GlobalExceptionHandler handler = new GlobalExceptionHandler();
    handler.commence(request, response, authenticationException);
    assertEquals(0, invocations.size());
  }

  @Test
  void whenUserDetailServiceReturnsValidValues() {
    UserDetailsDto dto =
        UserDetailsDto.builder()
            .username("<EMAIL>")
            .accountExpired(false)
            .accountNonLocked(true)
            .additionalProperties(new HashMap<>())
            .password("mypass")
            .accountExpired(false)
            .isCredentialsNonExpired(true)
            .authorities(new ArrayList<>())
            .build();
    assertNotNull(dto.getAuthorities());
    assertNotNull(dto.getUsername());
    assertNotNull(dto.getPassword());
    assertNotNull(dto.getAdditionalProperties());
  }

  @Test
  void whenUserDetailDtoConstructorIsCalled() {
    UserDetailsDto dto = new UserDetailsDto("dummy", "dummy");
    dto.setAuthorities(new ArrayList<>());
    dto.setAccountNonLocked(true);
    assertNotNull(dto.getUsername());
    assertNotNull(dto.getPassword());
    dto.isAccountNonExpired();
  }

  @Test
  void whenAuthorizationHeaderIsEmpty() throws ServletException, IOException {

    request.addHeader("Authorization", "");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }

  @Test
  void whenAuthorizationHeaderIsNotEmptyAndInValid() throws ServletException, IOException {

    request.addHeader("Authorization", "BBearer token");
    filter.doFilter(request, response, chain);

    assertEquals(0, invocations.size());
  }
}
