/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.CountryTools;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CountryToolsRepository extends JpaRepository<CountryTools, Long> {

  @Query(
      value =
          "Select c.* from Country_Tools c WHERE c.country_tool_document->>'CountryId' = :countryId"
              + " AND c.deleted= false AND timezone('UTC',c.updated_date) > :syncTime",
      nativeQuery = true)
  Page<CountryTools> findByCountryId(
      @Param("countryId") String countryId, Pageable pageable, @Param("syncTime") Instant syncTime);

  @Query(
      value =
          "select c.* from Country_tools c where c.country_tool_document->>'id' = :id and"
              + " c.deleted=false",
      nativeQuery = true)
  CountryTools findById(@Param("id") String id);

  @Query(
      value =
          "select c.* from Country_Tools c where c.country_tool_document->>'CountryId' = :countryId"
              + " and c.deleted= false",
      nativeQuery = true)
  List<CountryTools> findByCountryId(@Param("countryId") String countryId);

  @Query(
      value =
          "select c.* from Country_Tools c where c.country_tool_document->>'ToolId' = :toolId"
              + " and c.deleted= false",
      nativeQuery = true)
  List<CountryTools> findByToolId(@Param("toolId") String toolId);
}
