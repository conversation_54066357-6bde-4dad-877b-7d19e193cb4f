/* Cargill Inc.(C) 2022 */
package com.app.cargill;

import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.confproperties.SpringDocProperties;
import com.azure.cosmos.implementation.Strings;
import com.mchange.v2.c3p0.ComboPooledDataSource;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.modelmapper.ModelMapper;
import org.modelmapper.config.Configuration;
import org.modelmapper.convention.MatchingStrategies;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@SpringBootApplication
@EnableAsync(proxyTargetClass = true)
@Slf4j
@EnableConfigurationProperties({
  OktaProperties.class,
  AzureADProperties.class,
  SpringDocProperties.class,
  SharepointProperties.class
})
public class CargillApplication {

  public static void main(String[] args) {
    SpringApplication.run(CargillApplication.class, args);
  }

  @Value("${spring.datasource.url}")
  private String jdbcUrl;

  @Value("${spring.datasource.username}")
  private String jdbcUsername;

  @Value("${spring.datasource.password}")
  private String jdbcPassword;

  private PoolingHttpClientConnectionManager cm;

  @Bean
  public PasswordEncoder passwordEncoder() {
    return PasswordEncoderFactories.createDelegatingPasswordEncoder();
  }

  @Bean
  public ModelMapper modelMapper() {
    ModelMapper modelMapper = new ModelMapper();
    modelMapper
        .getConfiguration()
        .setMatchingStrategy(MatchingStrategies.STANDARD)
        .setFieldAccessLevel(Configuration.AccessLevel.PRIVATE);
    return modelMapper;
  }

  @Bean
  protected DataSource getDataSource() {
    log.debug(" ~~> getDatasource() called");
    ComboPooledDataSource cpds = new ComboPooledDataSource();

    cpds.setJdbcUrl(jdbcUrl);
    cpds.setUser(jdbcUsername);
    cpds.setPassword(jdbcPassword);

    cpds.setMaxPoolSize(50); // 200
    cpds.setAcquireIncrement(5); // 5
    cpds.setInitialPoolSize(50); // 5
    cpds.setMinPoolSize(30); // 0
    cpds.setMaxIdleTime(3000); // 3000
    cpds.setTestConnectionOnCheckin(true);
    cpds.setAutomaticTestTable("auto_test_table");
    cpds.setIdleConnectionTestPeriod(300);

    return cpds;
  }

  @Bean("poolingHttpClientConnectionManager")
  PoolingHttpClientConnectionManager poolingHttpClientConnectionManager() {
    cm = new PoolingHttpClientConnectionManager();
    // Increase max total connection to 100 initially
    cm.setMaxTotal(100);
    cm.setDefaultMaxPerRoute(10);
    return cm;
  }

  @Bean
  @DependsOn("poolingHttpClientConnectionManager")
  HttpClientBuilder configureHttpClient() {

    return HttpClients.custom()
        .setConnectionManager(cm) // shared connection manager
        .setConnectionManagerShared(true);
  }

  @Bean
  S3Presigner getS3Presigner(
      @Value("${cloud.aws.region}") String awsRegion,
      @Value("${cloud.aws.s3.access-key}") String amazonAccessKey,
      @Value("${cloud.aws.s3.secret-key}") String amazonSecretKey) {
    if (!Strings.isNullOrEmpty(awsRegion)) {
      System.setProperty("aws.region", awsRegion);
    }
    if (!Strings.isNullOrEmpty(amazonAccessKey)) {
      System.setProperty("aws.accessKeyId", amazonAccessKey);
    }
    if (!Strings.isNullOrEmpty(amazonSecretKey)) {
      System.setProperty("aws.secretAccessKey", amazonSecretKey);
    }
    return S3Presigner.create();
  }
}
