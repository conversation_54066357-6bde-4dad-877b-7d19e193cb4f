/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.app.cargill.sf.cc.model.ProfileRecord;
import com.app.cargill.sf.cc.model.RecordAttributes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * { "attributes": { "type": "User", "url": "/services/data/v56.0/sobjects/User/0000x000000AAAaAAA"
 * }, "Id": "0000x000000AAAaAAA", "Name": "System", "Email": "<EMAIL>",
 * "IsActive": true }
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class User implements Serializable {
  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Username")
  private String username;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("UserType")
  private String userType;

  @JsonProperty("IsActive")
  private String isActive;

  @JsonProperty("LastLoginDate")
  private Instant lastLoginDate;

  @JsonProperty("Profile")
  private ProfileRecord profile;
}
