/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.sf.crescendo.model.ActivityCrescendo;
import com.app.cargill.sf.crescendo.service.CrescendoActivityService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoActivityControllerTest {
  @InjectMocks private SalesforceCrescendoActivityController controller;
  @Mock private CrescendoActivityService activityService;

  @Test
  void whenGetActivitiesIsCalledCorrectResponseIsReturned() {
    when(activityService.getActivity(any())).thenReturn(new ArrayList<>());
    ResponseEntity<List<ActivityCrescendo>> result = controller.getActivities(Instant.now());
    assertNotNull(result);
  }
}
