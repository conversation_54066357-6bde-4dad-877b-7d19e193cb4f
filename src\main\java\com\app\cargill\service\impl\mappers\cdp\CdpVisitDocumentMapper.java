/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers.cdp;

import static com.app.cargill.utils.AttributesUtil.getAttributes;

import com.app.cargill.document.ProfitabilityAnalysisTool;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.CalculatedOutputs;
import com.app.cargill.dto.Inputs;
import com.app.cargill.dto.Outputs;
import com.app.cargill.dto.ProfitabilityAnalysisData;
import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.service.impl.tools.calculations.BCSCalculation;
import com.app.cargill.service.impl.tools.calculations.ForageAuditCalculation;
import com.app.cargill.service.impl.tools.calculations.ForageInventoriesCalculation;
import com.app.cargill.service.impl.tools.calculations.ForagePenStateCalculation;
import com.app.cargill.service.impl.tools.calculations.HeatStressCalculation;
import com.app.cargill.service.impl.tools.calculations.LocomotionScoreCalculation;
import com.app.cargill.service.impl.tools.calculations.ManureScoreCalculation;
import com.app.cargill.service.impl.tools.calculations.ManureScreenerCalculation;
import com.app.cargill.service.impl.tools.calculations.MetabolicIncidenceCalculation;
import com.app.cargill.service.impl.tools.calculations.MilkSoldEvaluationCalculation;
import com.app.cargill.service.impl.tools.calculations.PenTimeBudgetCalculation;
import com.app.cargill.service.impl.tools.calculations.RoboticMilkEvaluationCalculation;
import com.app.cargill.service.impl.tools.calculations.RumenFillCalculation;
import com.app.cargill.service.impl.tools.calculations.RumenHealthCudChewingCalculation;
import com.app.cargill.service.impl.tools.calculations.TMRParticleScoreCalculation;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CdpVisitDocumentMapper {

  private CdpVisitDocumentMapper() {}

  public static VisitDocumentDTO mapToDto(VisitDocument visitDocument) {

    if (visitDocument == null) {
      String errorMessage = "VisitDocument should not be null";
      log.error(errorMessage);
      throw new IllegalArgumentException(errorMessage);
    }

    List<AttributesDTO> attributes = setAttributes(visitDocument);

    return VisitDocumentDTO.builder()
        .id(visitDocument.getId())
        .accountId(visitDocument.getCustomerId())
        .siteId(visitDocument.getSiteId())
        .visitDate(visitDocument.getVisitDate())
        .status(visitDocument.getStatus())
        .visitName(visitDocument.getVisitName())
        .formattedCreationDate(visitDocument.getFormattedCreationDate())
        .selectedCurrency(visitDocument.getSelectedCurrency())
        .createUser(visitDocument.getCreateUser())
        .isDeleted(visitDocument.isDeleted())
        .lastModifiedTimeUtc(visitDocument.getLastModifiedTimeUtc())
        .lastSyncTimeUtc(visitDocument.getLastSyncTimeUtc())
        .isNew(visitDocument.isNew())
        .lastModifyUser(visitDocument.getLastModifyUser())
        .createTimeUtc(visitDocument.getCreateTimeUtc())
        .mobileLastUpdatedTime(visitDocument.getMobileLastUpdatedTime())
        .attributes(attributes)
        .build();
  }

  public static ProfitabilityAnalysisData mapToDtoForProfitabilityAnalysis(
      VisitDocument visitDocument) {

    if (visitDocument == null) {
      String errorMessage = "VisitDocument should not be null";
      log.error(errorMessage);
      throw new IllegalArgumentException(errorMessage);
    }
    return mapToDtoForProfitabilityAnalysis(visitDocument.getProfitabilityAnalysis());
  }

  private static ProfitabilityAnalysisData mapToDtoForProfitabilityAnalysis(
      ProfitabilityAnalysisTool profitabilityAnalysisToolDto) {
    return ProfitabilityAnalysisData.builder()
        .visitId(profitabilityAnalysisToolDto.getVisitId())
        .inputs(
            Inputs.builder()
                .animalsInHerd(profitabilityAnalysisToolDto.getAnimalInput().getAnimalsInHerd())
                .totalNumberOfCows(
                    profitabilityAnalysisToolDto.getAnimalInput().getTotalNumberOfCows())
                .totalNumberOfLactatingAnimals(
                    profitabilityAnalysisToolDto
                        .getAnimalInput()
                        .getTotalNumberOfLactatingAnimals())
                .breed(profitabilityAnalysisToolDto.getAnimalInput().getBreed())
                .productionSystem(
                    profitabilityAnalysisToolDto.getAnimalInput().getProductionSystem())
                .numberOfMilkings(
                    profitabilityAnalysisToolDto.getMilkInformation().getNumberOfMilkings())
                .totalProductionHerd(
                    profitabilityAnalysisToolDto.getMilkInformation().getTotalProductionHerd())
                .milkPrice(profitabilityAnalysisToolDto.getMilkInformation().getMilkPrice())
                .dim(profitabilityAnalysisToolDto.getMilkInformation().getDIM())
                .milkFatPercentage(
                    profitabilityAnalysisToolDto.getMilkInformation().getMilkFatPercentage())
                .milkProteinPercentage(
                    profitabilityAnalysisToolDto.getMilkInformation().getMilkProteinPercentage())
                .somanticCellCount(
                    profitabilityAnalysisToolDto.getMilkInformation().getSomanticCellCount())
                .bacteriaCellCount(
                    profitabilityAnalysisToolDto.getMilkInformation().getBacteriaCellCount())
                .mun(profitabilityAnalysisToolDto.getMilkInformation().getMUN())
                .commercialConcentrate(
                    profitabilityAnalysisToolDto.getFeedingInformation().getCommercialConcentrate())
                .commercialConcentrateToggle(
                    profitabilityAnalysisToolDto
                        .getFeedingInformation()
                        .getCommercialConcentrateToggle())
                .mineralBaseMix(
                    profitabilityAnalysisToolDto.getFeedingInformation().getMineralBaseMix())
                .mineralBaseMixValue(
                    profitabilityAnalysisToolDto.getFeedingInformation().getMineralBaseMixValue())
                .nutritek(profitabilityAnalysisToolDto.getFeedingInformation().getNutritek())
                .xpcUltra(profitabilityAnalysisToolDto.getFeedingInformation().getXpcUltra())
                .actiforBoost(
                    profitabilityAnalysisToolDto.getFeedingInformation().getActiforBoost())
                .buffer(profitabilityAnalysisToolDto.getFeedingInformation().getBuffer())
                .nutrigorduraLac(
                    profitabilityAnalysisToolDto.getFeedingInformation().getNutrigorduraLac())
                .ice(profitabilityAnalysisToolDto.getFeedingInformation().getIce())
                .energyIce(profitabilityAnalysisToolDto.getFeedingInformation().getEnergyIce())
                .monensin(profitabilityAnalysisToolDto.getFeedingInformation().getMonensin())
                .soyPassBr(profitabilityAnalysisToolDto.getFeedingInformation().getSoyPassBr())
                .concentrateTotalConsumed(
                    profitabilityAnalysisToolDto
                        .getFeedingInformation()
                        .getConcentrateTotalConsumed())
                .silage(profitabilityAnalysisToolDto.getFeedingInformation().getSilage())
                .haylage(profitabilityAnalysisToolDto.getFeedingInformation().getHaylage())
                .hay(profitabilityAnalysisToolDto.getFeedingInformation().getHay())
                .pasture(profitabilityAnalysisToolDto.getFeedingInformation().getPasture())
                .waterQuality(
                    profitabilityAnalysisToolDto.getFeedingInformation().getWaterQuality())
                .beddingQuality(
                    profitabilityAnalysisToolDto.getFeedingInformation().getBeddingQuality())
                .ventilation(profitabilityAnalysisToolDto.getFeedingInformation().getVentilation())
                .sprinkler(profitabilityAnalysisToolDto.getFeedingInformation().getSprinkler())
                .temperatureInC(
                    profitabilityAnalysisToolDto.getFeedingInformation().getTemperatureInC())
                .airRuPercentage(
                    profitabilityAnalysisToolDto.getFeedingInformation().getAirRuPercentage())
                .thi(profitabilityAnalysisToolDto.getFeedingInformation().getTHI())
                .respiratoryMovement(
                    profitabilityAnalysisToolDto.getFeedingInformation().getRespiratoryMovement())
                .cowLayingDownPercentage(
                    profitabilityAnalysisToolDto
                        .getFeedingInformation()
                        .getCowLayingDownPercentage())
                .totalDietCost(
                    profitabilityAnalysisToolDto.getFeedingInformation().getTotalDietCost())
                .build())
        .calculatedOutputs(
            CalculatedOutputs.builder()
                .totalProduction(
                    profitabilityAnalysisToolDto.getMilkInformation().getTotalProduction())
                .productionIn150DIM(
                    profitabilityAnalysisToolDto.getMilkInformation().getProductionIn150DIM())
                .revenuePerCowPerDay(
                    profitabilityAnalysisToolDto.getFeedingInformation().getRevenuePerCowPerDay())
                .build())
        .outputs(
            Outputs.builder()
                .animalsInHerd(profitabilityAnalysisToolDto.getAnimalInput().getAnimalsInHerd())
                .milkPrice(profitabilityAnalysisToolDto.getMilkInformation().getMilkPrice())
                .totalProductionHerd(
                    profitabilityAnalysisToolDto.getMilkInformation().getTotalProductionHerd())
                .totalDietCost(
                    profitabilityAnalysisToolDto.getFeedingInformation().getTotalDietCost())
                .feedConcentrate(
                    profitabilityAnalysisToolDto
                        .getFeedingInformation()
                        .getConcentrateTotalConsumed())
                .milkLitresKgConcentrate(
                    calclateMilkLitresKgConcentrate(profitabilityAnalysisToolDto))
                .totalProduction(
                    profitabilityAnalysisToolDto.getMilkInformation().getTotalProduction())
                .revenuePerCowPerDay(
                    profitabilityAnalysisToolDto.getFeedingInformation().getRevenuePerCowPerDay())
                .iofc(calculateIOFC(profitabilityAnalysisToolDto))
                .totalDietCostPerRevenuePercentage(
                    calculateTotalDietCostPerRevenuePerPercentage(profitabilityAnalysisToolDto))
                .build())
        .build();
  }

  private static Double calculateTotalDietCostPerRevenuePerPercentage(
      ProfitabilityAnalysisTool profitabilityAnalysisToolDto) {
    if (profitabilityAnalysisToolDto.getFeedingInformation().getTotalDietCost() != null
        && profitabilityAnalysisToolDto.getFeedingInformation().getRevenuePerCowPerDay() != null) {

      BigDecimal totalDietCost =
          BigDecimal.valueOf(
              profitabilityAnalysisToolDto.getFeedingInformation().getTotalDietCost());
      BigDecimal revenuePerCowPerDay =
          BigDecimal.valueOf(
              profitabilityAnalysisToolDto.getFeedingInformation().getRevenuePerCowPerDay());

      BigDecimal result =
          totalDietCost
              .divide(revenuePerCowPerDay, 10, RoundingMode.HALF_UP) // Intermediate precision
              .multiply(BigDecimal.valueOf(100));

      return result.setScale(3, RoundingMode.HALF_UP).doubleValue();
    }
    return null;
  }

  private static Double calculateIOFC(ProfitabilityAnalysisTool profitabilityAnalysisToolDto) {
    if (profitabilityAnalysisToolDto.getFeedingInformation().getRevenuePerCowPerDay() != null
        && profitabilityAnalysisToolDto.getFeedingInformation().getTotalDietCost() != null) {

      BigDecimal revenuePerCowPerDay =
          BigDecimal.valueOf(
              profitabilityAnalysisToolDto.getFeedingInformation().getRevenuePerCowPerDay());
      BigDecimal totalDietCost =
          BigDecimal.valueOf(
              profitabilityAnalysisToolDto.getFeedingInformation().getTotalDietCost());

      BigDecimal result = revenuePerCowPerDay.subtract(totalDietCost);

      return result.setScale(3, RoundingMode.HALF_UP).doubleValue();
    }
    return null;
  }

  private static Double calclateMilkLitresKgConcentrate(
      ProfitabilityAnalysisTool profitabilityAnalysisToolDto) {
    if (profitabilityAnalysisToolDto.getMilkInformation().getTotalProduction() != null
        && profitabilityAnalysisToolDto.getFeedingInformation().getConcentrateTotalConsumed()
            != null) {

      BigDecimal totalProduction =
          BigDecimal.valueOf(
              profitabilityAnalysisToolDto.getMilkInformation().getTotalProduction());
      BigDecimal concentrateTotalConsumed =
          BigDecimal.valueOf(
              profitabilityAnalysisToolDto.getFeedingInformation().getConcentrateTotalConsumed());

      BigDecimal result =
          totalProduction.divide(
              concentrateTotalConsumed, 10, RoundingMode.HALF_UP); // Intermediate precision

      return result.setScale(3, RoundingMode.HALF_UP).doubleValue();
    }
    return null;
  }
  // Reducing complexity will only make the code harder to read. This code piece does 1 thing.
  // Preparation of the attributes for all tools
  @SuppressWarnings("java:S3776")
  static List<AttributesDTO> setAttributes(VisitDocument visitDocument) {

    List<AttributesDTO> attributes = new ArrayList<>();

    if (visitDocument.heatStress != null) {
      new HeatStressCalculation().calculateFields(visitDocument.heatStress);
      attributes.addAll(getAttributes(visitDocument.heatStress, null));
    }

    if (visitDocument.getForageAuditScorecard() != null) {
      new ForageAuditCalculation().calculateFields(visitDocument.forageAuditScorecard);
      attributes.addAll(getAttributes(visitDocument.forageAuditScorecard, null));
    }

    if (visitDocument.getPileAndBunker() != null) {
      new ForageInventoriesCalculation()
          .calculateFields(visitDocument.pileAndBunker, visitDocument.getCreateTimeUtc());
      attributes.addAll(getAttributes(visitDocument.pileAndBunker, null));
    }

    if (visitDocument.getRumenHealth() != null) {
      new RumenHealthCudChewingCalculation().calculateFields(visitDocument.rumenHealth);
      attributes.addAll(getAttributes(visitDocument.rumenHealth, null));
    }

    if (visitDocument.getTmrParticleScore() != null) {
      new TMRParticleScoreCalculation().calculateFields(visitDocument.tmrParticleScore);
      attributes.addAll(getAttributes(visitDocument.tmrParticleScore, null));
    }

    if (visitDocument.getRumenHealthManureScore() != null) {
      visitDocument.rumenHealthManureScore =
          new ManureScoreCalculation().calculateFields(visitDocument.rumenHealthManureScore);
      attributes.addAll(getAttributes(visitDocument.rumenHealthManureScore, null));
    }

    if (visitDocument.getLocomotionScore() != null) {
      new LocomotionScoreCalculation().calculateFields(visitDocument.locomotionScore);
      attributes.addAll(getAttributes(visitDocument.locomotionScore, null));
    }

    if (visitDocument.getPenTimeBudgetTool() != null) {
      new PenTimeBudgetCalculation().calculateFields(visitDocument.penTimeBudgetTool);
      attributes.addAll(getAttributes(visitDocument.penTimeBudgetTool, null));
    }

    if (visitDocument.getBodyCondition() != null) {
      new BCSCalculation().calculateFields(visitDocument.bodyCondition);
      attributes.addAll(getAttributes(visitDocument.bodyCondition, null));
    }

    if (visitDocument.getMetabolicIncidence() != null) {
      new MetabolicIncidenceCalculation().calculateFields(visitDocument.metabolicIncidence);
      attributes.addAll(getAttributes(visitDocument.metabolicIncidence, null));
    }

    if (visitDocument.getRevenue() != null) {
      attributes.addAll(getAttributes(visitDocument.revenue, null));
    }

    if (visitDocument.getMilkSoldEvaluation() != null) {
      new MilkSoldEvaluationCalculation()
          .calculateFields(visitDocument.milkSoldEvaluation, visitDocument.getSiteMilk());
      attributes.addAll(getAttributes(visitDocument.milkSoldEvaluation, null));
    }

    if (visitDocument.getReadyToMilk() != null) {
      attributes.addAll(getAttributes(visitDocument.readyToMilk, null));
    }

    if (visitDocument.getUrinePHTool() != null) {
      attributes.addAll(getAttributes(visitDocument.urinePHTool, null));
    }

    if (visitDocument.getCalfHeiferScorecard() != null) {
      attributes.addAll(getAttributes(visitDocument.calfHeiferScorecard, null));
    }

    if (visitDocument.getRoboticMilkEvaluation() != null) {
      new RoboticMilkEvaluationCalculation().calculateFields(visitDocument.roboticMilkEvaluation);
      attributes.addAll(getAttributes(visitDocument.roboticMilkEvaluation, null));
    }

    if (visitDocument.getManureScreenerTool() != null) {
      new ManureScreenerCalculation().calculateFields(visitDocument.manureScreenerTool);
      attributes.addAll(getAttributes(visitDocument.manureScreenerTool, null));
    }

    if (visitDocument.getRumenFillManureScore() != null) {
      new RumenFillCalculation().calculateFields(visitDocument.rumenFillManureScore);
      attributes.addAll(getAttributes(visitDocument.rumenFillManureScore, null));
    }

    if (visitDocument.getForagePennState() != null) {
      new ForagePenStateCalculation().calculateFields(visitDocument.foragePennState);
      attributes.addAll(getAttributes(visitDocument.foragePennState, null));
    }

    return attributes;
  }
}
