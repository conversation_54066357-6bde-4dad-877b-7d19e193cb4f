/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.model.SiteMappingCosmos;
import com.app.cargill.cosmos.repo.SiteMappingsCosmosRepository;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.repository.SiteMappingsRepository;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
@Slf4j
class SiteMappingsMigrationTest {

  @Mock private SiteMappingsCosmosRepository cosmosRepository;
  @Mock private SiteMappingsRepository siteMappingsRepository;

  @InjectMocks private SiteMappingsMigration siteMappingsMigration;

  @Test
  void whenSiteMappingsMigrationIsCalledExpectedResultsAreReturned() {

    SiteMappingCosmos sm1 = new SiteMappingCosmos();
    sm1.setId(UUID.randomUUID().toString());
    sm1.setLabyrinthSiteId(UUID.randomUUID().toString());
    sm1.setDdwHerdId("sm1");

    SiteMappingCosmos sm2 = new SiteMappingCosmos();
    sm2.setId(UUID.randomUUID().toString());
    sm2.setLabyrinthSiteId(UUID.randomUUID().toString());
    sm2.setDdwHerdId("sm2");
    sm2.setMaxSiteId("invalid-uuid");

    SiteMappingCosmos sm3 = new SiteMappingCosmos();
    sm3.setId(UUID.randomUUID().toString());
    sm3.setLabyrinthSiteId(null);
    sm3.setDdwHerdId("sm3");

    when(cosmosRepository.findAllBySiteId(any())).thenReturn(Flux.just(sm1, sm2, sm3));
    when(siteMappingsRepository.save(any()))
        .thenThrow(new IllegalArgumentException("Test Exception"))
        .thenAnswer(i -> i.getArgument(0));

    Flux<SiteMappings> result = siteMappingsMigration.moveRecords("siteId");

    StepVerifier.create(result).expectNextCount(1).verifyComplete();
  }
}
