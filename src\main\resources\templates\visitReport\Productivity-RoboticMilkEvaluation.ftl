<div class="container">
    <div class="pt-0">
        <div class="legend-head">
            <div class="count">${toolNumber!}</div>
            <div class="main-title">
                <span class="sm-head">${localization.getMessage("VisitViewModel.ProductivityItem", [], "Productivity",
                    locale)}</span>
                <span class="lg-head">${localization.getMessage("RoboticMilkEvaluationSpinnerViewModel.Title", [],
                    "Robotic Milking Evaluation", locale)}</span>
            </div>
            <div style="font-size: 1;color: white;">0000161616RME</div>
        </div>
        <#if
            model.roboticMilkEvaluationTool.herdLevelInformationLeftTable??||model.roboticMilkEvaluationTool.herdLevelInformationRightTable??>
            <h3 class="title-secondary mb-1 d-flex justify-space-between">
                <span>${localization.getMessage("MetabolicIncidenceInputsViewModel.Herd", [], "Herd Level Information",
                    locale)}</span>
            </h3>
            <div class="row mx-neg-4">
                <#if model.roboticMilkEvaluationTool.herdLevelInformationLeftTable??>
                    <div class="col-6 mb-1 px-4 table-secondary">
                        <table class="w-50">
                            <tbody>
                                <#list model.roboticMilkEvaluationTool.herdLevelInformationLeftTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.roboticMilkEvaluationTool.herdLevelInformationLeftTable[prop]!}</td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </#if>
                <#if model.roboticMilkEvaluationTool.herdLevelInformationRightTable??>
                    <div class="col-6 mb-1 px-4 table-secondary">
                        <table class="w-50">
                            <tbody>
                                <#list model.roboticMilkEvaluationTool.herdLevelInformationRightTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.roboticMilkEvaluationTool.herdLevelInformationRightTable[prop]!}
                                        </td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </#if>
            </div>
        </#if>
        <#if model.roboticMilkEvaluationTool.outputTable??>
            <div class="row mx-neg-4">
                <div class="col-6 mb-1 px-4 table-secondary">
                    <h3 class="title-secondary mb-1 d-flex justify-space-between">
                        <span>${localization.getMessage("Outputs", [], "Outputs", locale)}</span>
                    </h3>
                    <#list model.roboticMilkEvaluationTool.outputTable?keys as prop>
                        <h3 class="title">${prop!}</h3>
                        <table class="w-50 mb-1">
                            <tbody>
                                <#list model.roboticMilkEvaluationTool.outputTable[prop]?keys as innerProp>
                                    <tr>
                                        <td>${innerProp!}</td>
                                        <td>${model.roboticMilkEvaluationTool.outputTable[prop][innerProp]!}</td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </#list>
                </div>
            </div>
        </#if>
    </div>
</div>
<#assign counter=0>
    <#if model.roboticMilkEvaluationTool.analysisDialGraphs?? &&
        model.roboticMilkEvaluationTool.analysisDialGraphs[0]??>
        <div class="break-page"></div>

        <div class="container">
            <div class="pt-0">
                <div class="row mx-neg-4">
                    <div class="col-12">
                        <h6>${localization.getMessage("FreeFormReportViewModel.Analysis", [], "Analysis", locale)}</h6>
                    </div>
                </div>
                <#list model.roboticMilkEvaluationTool.analysisDialGraphs?chunk(2) as row>
                    <div class="row mx-neg-4">
                        <#list row as graph>
                            <div class="col-6 px-4">
                                <h3 class="title-primary justify-space-between mb-1"><span>${graph.label!}</span>
                                    <span>${graph.value!'0.0'}</span>
                                </h3>
                                <div class="card mb-1">
                                    <div class="card-body v-center ">

                                        <div class="w-left">
                                            <#assign dialGraph=statics["java.util.UUID"].randomUUID()>
                                                <div id="${dialGraph!}"></div>
                                        </div>
                                        <div class="w-right ">
                                            <ul>
                                                <li class="red">${graph.redRange!}</li>
                                                <li class="yellow">${graph.yellowRange!'0.0'}</li>
                                                <li class="green">${graph.greenRange!'0.0'}</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <script>
                                (function () {
                                    var dom = document.getElementById('${dialGraph!}');
                                    var myChart = echarts.init(dom, null, {
                                        renderer: 'canvas',
                                        width: 170,
                                        height: 170
                                    });
                                    var app = {};

                                    var option;

                                    option = {

                                        series: [
                                            {
                                                responsive: true,
                                                maintainAspectRatio: false,
                                                type: 'gauge',
                                                min: ${ graph.minValue! },
                                            max: ${ graph.maxValue! },
                                            splitNumber: ${ graph.totalGaugeLabels - 1! },
                                            radius: '100%', // this
                                            center: ['50%', '50%'],
                                            animation: false,
                                            grid: {
                                                left: 0,
                                                right: 0,
                                                top: 0,
                                                bottom: 0,
                                                width: '140px',
                                                height: '140px'
                                            },
                                            axisLine: {
                                                lineStyle: {
                                                    width: 12,
                                                    color: [
                                                        <#list graph.gaugeColors as col >
                                                        [${ col.calculatedValue! }, '${col.hexColor!}'] <#sep>, </#sep>
                        </#list >
                        ]
                                                }
                                            },
                                            pointer: {
                                                itemStyle: {
                                                    color: '#0B5E86',
                                                    borderJoin: 'round',
                                                    borderCap: 'round'
                                                },
                                                length: '65%',
                                                animation: false
                                            },
                                            axisTick: {
                                                distance: 0,
                                                length: 20,
                                                lineStyle: {
                                                    color: '#fff',
                                                    width: 0
                                                }
                                            },
                                            splitLine: {
                                                distance: 0,
                                                length: 0,
                                                lineStyle: {
                                                    color: '#fff',
                                                    width: 0
                                                }
                                            },
                                            axisLabel: {
                                                color: '#333333',
                                                distance: 18,
                                                formatter: val => Math.round(val),
                                                fontSize: 10
                                            },
                                            detail: {
                                                valueAnimation: true,
                                                formatter: '{value} km/h',
                                                color: 'transparent'
                                            },
                                            data: [
                                                {
                                                    value: ${ graph.value!'0.0'}
                    }
                ],


                                }
        ]
    };


                                if (option && typeof option === 'object') {
                                    myChart.setOption(option);
                                }

                                window.addEventListener('resize', myChart.resize);
}) ();

                            </script>

                            <#assign counter=counter+1>
                        </#list>
                    </div>
                    <#if counter%6==0>
                        <div class="break-page"></div>
                    </#if>
                </#list>
            </div>
        </div>
    </#if>
    <#if model.roboticMilkEvaluationTool.graphs?? && model.roboticMilkEvaluationTool.graphs[0]??>
        <div class="container">
            <div class="pt-0">
                <div class="row mx-neg-4">
                    <#list model.roboticMilkEvaluationTool.graphs as graph>
                        <div class="col-12">
                            <h6>${graph.categoryLabel!}</h6>
                        </div>
                        <#if graph.dualYaxisGraph??>
                            <div class="col-6 px-4">
                                <div class="card mb-1">
                                    <div class="card-header">
                                        <h4 class="text-center">${graph.dualYaxisGraph.sheetName!}</h4>
                                    </div>
                                    <div class="card-body">
                                        <#assign linechart=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart!}"></canvas>
                                    </div>
                                    <div class="card-footer">
                                        <div class="row">
                                            <div class="legend-wrap mb-2">
                                                <p class="cls${linechart!} dynamic-solid-left">
                                                    ${graph.dualYaxisGraph.yleftLabel!}</p>
                                            </div>
                                            <div class="legend-wrap mb-2">
                                                <p class="cls${linechart!} dynamic-solid-right">
                                                    ${graph.dualYaxisGraph.yrightLabel!}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <script>

                                (function () {

                                    var style = document.querySelector('.cls${linechart!}.dynamic-solid-left').style;
                                    style.setProperty('--background', '${graph.dualYaxisGraph.yleftLineColorHex!}');
                                    style = document.querySelector('.cls${linechart!}.dynamic-solid-right').style;
                                    style.setProperty('--background', '${graph.dualYaxisGraph.yrightLineColorHex!}');
                                    const colors = {
                                        purple: {
                                            default: "#1baca7",
                                            half: "#1baca778",
                                            quarter: "#1baca73b",
                                            zero: "#1baca71c"
                                        },
                                        indigo: {
                                            default: "#1baca7",
                                            quarter: "#1baca778"
                                        }
                                    };

                                    const yLeftAxis = [
                                        <#list graph.dualYaxisGraph.yleftDataPoints as dataPoint >
                                        ${(dataPoint.y)!'NaN'} <#sep>, </#sep>
</#list >
];

                                const yRightAxis = [
                                    <#list graph.dualYaxisGraph.yrightDataPoints as dataPoint >
                                    ${(dataPoint.y)!'NaN'}<#sep>, </#sep>
</#list >
];

                                const labels = [
                                    <#list graph.dualYaxisGraph.yleftDataPoints as dataPoint >
                                    '${dataPoint.x!}' <#sep>, </#sep>
</#list >
];


                                const ctx = document.getElementById("${linechart!}").getContext("2d");
                                ctx.canvas.height = 100;

                                var rgb = hexToRgb('${graph.dualYaxisGraph.yleftLineColorHex!}');
                                gradient = ctx.createLinearGradient(rgb.r, rgb.g, rgb.b, 300);

                                gradient.addColorStop(0, 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.35)');
                                gradient.addColorStop(0.35, 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.20)');
                                gradient.addColorStop(1, 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.05)');

                                const options = {
                                    type: "line",

                                    data: {
                                        labels: labels,
                                        datasets: [
                                            {
                                                borderColor: '${graph.dualYaxisGraph.yrightLineColorHex!}',
                                                pointBackgroundColor: '${graph.dualYaxisGraph.yrightLineColorHex!}',
                                                pointBorderColor: '#fff',
                                                data: yRightAxis,
                                                lineTension: 0.2,
                                                borderWidth: 1,
                                                pointRadius: 6,
                                                yAxisID: 'y1',
                                            },
                                            {
                                                fill: true,
                                                backgroundColor: gradient,
                                                borderColor: '${graph.dualYaxisGraph.yleftLineColorHex!}',
                                                pointBackgroundColor: '${graph.dualYaxisGraph.yleftLineColorHex!}',
                                                pointBorderColor: '#fff',
                                                data: yLeftAxis,
                                                lineTension: 0.2,
                                                borderWidth: 1,
                                                pointRadius: 6,
                                                yAxisID: 'y',
                                            }
                                        ]
                                    },
                                    options: {
                                        plugins: {
                                            legend: {
                                                display: false,
                                            },
                                            tooltip: {
                                                callbacks: {
                                                    title: () => null // or function () { return null; }
                                                },
                                                yAlign: 'bottom',
                                                backgroundColor: "#fff",
                                                borderColor: "rgba(0, 0, 0, 0.25)",
                                                borderWidth: 1,
                                                displayColors: false,
                                                bodyColor: "#1BACA7",
                                                bodyAlign: "center",
                                            },
                                        },

                                        layout: {
                                            padding: {
                                                top: 20,
                                                right: 15
                                            }
                                        },

                                        responsive: true,

                                        scales: {

                                            y1: {
                                                type: 'linear',
                                                beginAtZero: true,
                                                position: 'right',
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${graph.dualYaxisGraph.yrightLabel!}',
                                                    padding: {
                                                        bottom: 0,
                                                    },font: {
size: 10,
},
                                                },
                                                grid: { display: false }
                                            },
                                            y: {
                                                type: 'linear',
                                                position: 'left',
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${graph.dualYaxisGraph.yleftLabel!}',
                                                    padding: {
                                                        bottom: 0,
                                                    },
                                                    font: {
                                                        size: 10,
                                                    },
                                                },
                                                grid: { display: false }
                                            },

                                            x: {
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                    font: {
                                                        size: 10,
                                                    },
                                                },
                                                grid: {
                                                    display: false,
                                                },
                                            }
                                        },
                                        animation: {
                                            duration: 0,
                                            onComplete: function () {
                                                var chart = this;
                                                var ctx = chart.ctx;
                                                ctx.textAlign = 'center';
                                                ctx.textBaseline = 'bottom';
                                                ctx.fillStyle = '#6C7782';
                                                this.data.datasets.forEach(function (dataset, i) {
                                                    var meta = chart.getDatasetMeta(i);
                                                    meta.data.forEach(function (bar, index) {
                                                        var data = dataset.data[index];
                                                        data = isNaN(data) ? '' : data;
                                                        var yIndex = bar.y - 5;
                                                        if (data && data < 0) {
                                                            yIndex = bar.y + 15;
                                                        }
                                                        ctx.fillText(data, bar.x, yIndex);
                                                    });
                                                });
                                            }
                                        }
                                    }
                                };

                                window.myLine = new Chart(ctx, options);
}) ();
                            </script>
                        </#if>
                        <#if graph.singleYaxisGraph??>
                            <div class="col-6 px-4">
                                <div class="card mb-1">
                                    <div class="card-header">
                                        <h4 class="text-center">${graph.singleYaxisGraph.sheetName!}</h4>
                                    </div>
                                    <div class="card-body">
                                        <#assign linechart=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart!}"></canvas>
                                    </div>
                                    <div class="card-footer">
                                        <div class="row">
                                            <div class="legend-wrap mb-2">
                                                <p class="cls${linechart!} dynamic-solid-left">
                                                    ${graph.singleYaxisGraph.yleftLabel!}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <script>

                                (function () {

                                    var style = document.querySelector('.cls${linechart!}.dynamic-solid-left').style;
                                    style.setProperty('--background', '${graph.singleYaxisGraph.yleftLineColorHex!}');

                                    const colors = {
                                        purple: {
                                            default: "#1baca7",
                                            half: "#1baca778",
                                            quarter: "#1baca73b",
                                            zero: "#1baca71c"
                                        },
                                        indigo: {
                                            default: "#1baca7",
                                            quarter: "#1baca778"
                                        }
                                    };


                                    const yAxis = [
                                        <#list graph.singleYaxisGraph.yleftDataPoints as datapoint >
                                        ${(datapoint.y)!'NaN'} <#sep>, </#sep>
</#list >
];

                                const xAxis = [
                                    <#list graph.singleYaxisGraph.yleftDataPoints as datapoint >
                                    "${(datapoint.x)!}" <#sep>, </#sep>
</#list >
];

                                const ctx = document.getElementById("${linechart!}").getContext("2d");
                                ctx.canvas.height = 100;
                                var rgb = hexToRgb('${graph.singleYaxisGraph.yleftLineColorHex!}');
                                gradient = ctx.createLinearGradient(rgb.r, rgb.g, rgb.b, 300);

                                gradient.addColorStop(0, 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.35)');
                                gradient.addColorStop(0.35, 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.20)');
                                gradient.addColorStop(1, 'rgba(' + rgb.r + ',' + rgb.g + ',' + rgb.b + ',0.05)');

                                const options = {
                                    type: "line",
                                    data: {
                                        labels: xAxis,
                                        datasets: [
                                            {
                                                fill: true,
                                                backgroundColor: gradient,
                                                borderColor: '${graph.singleYaxisGraph.yleftLineColorHex!}',
                                                pointBackgroundColor: '${graph.singleYaxisGraph.yleftLineColorHex!}',
                                                pointBorderColor: '#fff',
                                                data: yAxis,
                                                lineTension: 0.2,
                                                borderWidth: 1,
                                                pointRadius: 6,
                                            }
                                        ]
                                    },
                                    options: {
                                        plugins: {
                                            legend: {
                                                display: false,
                                            },
                                            tooltip: {
                                                callbacks: {
                                                    title: () => null // or function () { return null; }
                                                },
                                                yAlign: 'bottom',
                                                backgroundColor: "#fff",
                                                borderColor: "rgba(0, 0, 0, 0.25)",
                                                borderWidth: 1,
                                                displayColors: false,
                                                bodyColor: "#1BACA7",
                                                bodyAlign: "center",
                                            },
                                        },

                                        layout: {
                                            padding: {
                                                top: 20,
                                                right: 15
                                            }
                                        },

                                        responsive: true,

                                        scales: {
                                            y: {
                                                // beginAtZero: true,
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${graph.singleYaxisGraph.yleftLabel!}',
                                                    padding: {
                                                        bottom: 0,
                                                    },
                                                    font: {
                                                        size: 10,
                                                    },
                                                },

                                                grid: {
                                                    display: false,
                                                },
                                            },

                                            x: {
                                                grid: {
                                                    display: false,
                                                },
                                                color: '#6C7782',
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                    padding: {
                                                        bottom: 0,
                                                    },
                                                    font: {
                                                    size: 10,
                                                    },
                                                }
                                            }
                                        },
                                        animation: {
                                            duration: 0,
                                            onComplete: function () {
                                                var chart = this;
                                                var ctx = chart.ctx;
                                                ctx.textAlign = 'center';
                                                ctx.textBaseline = 'bottom';
                                                ctx.fillStyle = '#6C7782';
                                                this.data.datasets.forEach(function (dataset, i) {
                                                    var meta = chart.getDatasetMeta(i);
                                                    meta.data.forEach(function (bar, index) {
                                                        var data = dataset.data[index];
                                                        data = isNaN(data) ? '' : data;
                                                        var yIndex = bar.y - 5;
                                                        if (data && data < 0) {
                                                            yIndex = bar.y + 15;
                                                        }
                                                        ctx.fillText(data, bar.x, yIndex);
                                                    });
                                                });
                                            }
                                        }
                                    }
                                };

                                window.myLine = new Chart(ctx, options);
}) ();
                            </script>
                        </#if>
                    </#list>
                </div>
            </div>
        </div>
    </#if>

    <#if model.roboticMilkEvaluationTool?? && model.roboticMilkEvaluationTool.notes??>
        <div class="container mid-body">
            <div class="pt-0">
                <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                    locale)}</h3>
                <#list model.roboticMilkEvaluationTool.notes as innerlist>
                    <#if innerlist.id??>
                        <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                            <h4 class="followup mb-1">
                                <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                                <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                            </h4>
                            <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                            <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                                <div class="notes-images mb-1">
                                    <#list noteFound.mediaItems as media>
                                        <figure>
                                            <img src="${media.base64EncodedImage!}">
                                        </figure>
                                    </#list>
                                </div>
                            </#if>
                        </#list>
                    </#if>
                </#list>
            </div>
        </div>
    </#if>