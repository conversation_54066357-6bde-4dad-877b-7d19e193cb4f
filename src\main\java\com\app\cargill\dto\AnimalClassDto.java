/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;
import lombok.Data;

@Data
public class AnimalClassDto {
  private Map<String, AnimalClassTriplet<UUID, String, String>> lactatingFresh =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> lactatingFreshHeifer =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> lactatingMilking =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> lactatingPasture =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> lactatingLowForage =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> lactatingAAEfficiency =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> dryFarOff = new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> dryShortDryPeriod =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> dryCloseUp = new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> dryCloseUpHeifer =
      new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> heifer = new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> calf = new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> maleBull = new LinkedHashMap<>();
  private Map<String, AnimalClassTriplet<UUID, String, String>> maleSteer = new LinkedHashMap<>();
}
