/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Accounts;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AccountsRepository extends JpaRepository<Accounts, Long> {

  @Query(
      value =
          "Select a.* FROM Accounts a where a.account_document ->> 'id' = :id and a.deleted=false",
      nativeQuery = true)
  Accounts findByAccountId(@Param("id") String id);

  @Query(
      value = "Select a.* FROM Accounts a where a.account_document ->> 'id' = :id",
      nativeQuery = true)
  Accounts findByAccountIdInclDeleted(@Param("id") String id);

  @Query(
      value =
          "Select a.* FROM Accounts a where a.account_document ->> 'GoldenRecordId' = :id and"
              + " a.deleted=false",
      nativeQuery = true)
  Accounts findByExternalId(@Param("id") String id);

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'SubTypeID'= :subType AND"
              + " timezone('UTC',a.updated_date) > :syncTime AND CAST(account_document->>'Users' as"
              + " jsonb) @> CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND"
              + " a.deleted=false",
      nativeQuery = true)
  List<Accounts> findAllBySubTypeSyncTimeAndAccountStatus(
      @Param("subType") String subType,
      @Param("syncTime") Instant syncTime,
      @Param("email") String email);

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'SubTypeID' = :subType AND"
              + " a.account_document->> 'AccountType' =:accountType AND"
              + " timezone('UTC',a.updated_date) > :syncTime AND CAST(account_document->>'Users' as"
              + " jsonb) @> CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND"
              + " a.deleted=false",
      nativeQuery = true)
  List<Accounts> findAllBySubtypeAccountTypeSyncTimeAndAccountStatus(
      @Param("subType") String subType,
      @Param("accountType") String accountType,
      @Param("syncTime") Instant syncTime,
      @Param("email") String email);

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'SubTypeID' = :subType AND"
              + " a.account_document->> 'AccountType' = :accountType AND"
              + " timezone('UTC',a.updated_date) > :syncTime AND a.account_document ->>"
              + " 'PrimaryContactTitle' like %:search% AND CAST(account_document->>'Users' as"
              + " jsonb) @> CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND"
              + " (a.account_document->'UserRoles' @> CAST(:userRole as jsonb) OR"
              + " a.account_document->>'OwnerId' ilike :email) AND a.deleted=false",
      nativeQuery = true)
  Page<Accounts> findAllAccountsBySearchItemSubTypeIdAccountIdSyncTimeAndAccountStatusPaginated(
      @Param("search") String search,
      @Param("subType") String subType,
      @Param("accountType") String accountType,
      @Param("syncTime") Instant syncTime,
      @Param("email") String email,
      @Param("userRole") String userRole,
      Pageable pageable);

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'SubTypeID' = :subType AND"
              + " timezone('UTC',a.updated_date) > :syncTime AND a.account_document ->>"
              + " 'PrimaryContactTitle' like %:search% AND CAST(account_document->>'Users' as"
              + " jsonb) @> CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND"
              + " (a.account_document->'UserRoles' @> CAST(:userRole as jsonb) OR"
              + " a.account_document->>'OwnerId' ilike :email) AND a.deleted=false",
      nativeQuery = true)
  Page<Accounts> findAllAccountsBySearchItemSubTypeSyncTimeAndAccountStatusPaginated(
      @Param("search") String search,
      @Param("subType") String subType,
      @Param("syncTime") Instant syncTime,
      @Param("email") String email,
      @Param("userRole") String userRole,
      Pageable pageable);

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'SubTypeID' = :subType AND"
              + " a.account_document->> 'AccountType' =:accountType AND"
              + " timezone('UTC',a.updated_date) > :syncTime AND CAST(account_document->>'Users' as"
              + " jsonb) @> CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND ("
              + " EXISTS (     SELECT 1 FROM jsonb_array_elements(CASE         WHEN"
              + " jsonb_typeof(a.account_document->'UserRoles') = 'array' THEN"
              + " a.account_document->'UserRoles'         ELSE '[]'     END) AS userRole     WHERE"
              + " userRole->>'UserName' = :userEmail AND userRole->>'UserRole' <> 'Technical"
              + " Specialist' ) OR a.account_document->>'OwnerId' ilike :userEmail) AND"
              + " a.deleted=false",
      nativeQuery = true)
  Page<Accounts> findAllBySubTypeIdAccountTypeSyncTimeAndAccountStatusPaginated(
      @Param("subType") String subType,
      @Param("syncTime") Instant syncTime,
      @Param("accountType") String accountType,
      @Param("email") String email,
      @Param("userEmail") String userEmail,
      Pageable pageable);

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'SubTypeID'= :subType AND"
              + " timezone('UTC',a.updated_date) > :syncTime AND CAST(account_document->>'Users' as"
              + " jsonb) @> CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND ("
              + " EXISTS (     SELECT 1 FROM jsonb_array_elements(CASE         WHEN"
              + " jsonb_typeof(a.account_document->'UserRoles') = 'array' THEN"
              + " a.account_document->'UserRoles'         ELSE '[]'     END) AS userRole     WHERE"
              + " userRole->>'UserName' = :userEmail AND userRole->>'UserRole' <> 'Technical"
              + " Specialist' ) OR a.account_document->>'OwnerId' ilike :userEmail) AND"
              + " a.deleted=false",
      nativeQuery = true)
  Page<Accounts> findAllBySubTypeIdSyncTimeAndAccountStatusPaginated(
      @Param("subType") String subType,
      @Param("syncTime") Instant syncTime,
      @Param("email") String email,
      @Param("userEmail") String userEmail,
      Pageable pageable);

  @Query(
      value =
          "SELECT a.account_document->>'id' FROM Accounts a where a.account_document->>"
              + " 'SubTypeID'= :subType AND CAST(account_document->>'Users' as jsonb) @>"
              + " CAST(:email as jsonb) AND a.account_document->>'Active' = 'true' AND ( EXISTS (  "
              + "   SELECT 1 FROM jsonb_array_elements(CASE         WHEN"
              + " jsonb_typeof(a.account_document->'UserRoles') = 'array' THEN"
              + " a.account_document->'UserRoles'         ELSE '[]'     END) AS userRole     WHERE"
              + " userRole->>'UserName' = :userEmail AND userRole->>'UserRole' <> 'Technical"
              + " Specialist' ) OR a.account_document->>'OwnerId' ilike :userEmail) AND"
              + " a.deleted=false",
      nativeQuery = true)
  List<String> findAllFilteredAccountIds(
      @Param("subType") String subType,
      @Param("email") String email,
      @Param("userEmail") String userEmail);

  boolean existsByLocalId(String localId);

  @Query(
      value =
          "SELECT a.account_document->>'id' FROM accounts a WHERE CAST(a.account_document ->"
              + " 'Users' as jsonb)  @> CAST(:currentLoggedUser as jsonb) AND a.deleted=false",
      nativeQuery = true)
  List<String> findAccountIdsByUser(@Param("currentLoggedUser") String currentLoggedUser);

  @Query(
      value =
          "SELECT a.* FROM accounts a WHERE CAST(a.account_document ->"
              + " 'Users' as jsonb)  @> CAST(:currentLoggedInUser as jsonb) AND a.deleted=false",
      nativeQuery = true)
  List<Accounts> findAccountsByUser(@Param("currentLoggedInUser") String currentLoggedInUser);

  @Query(
      value =
          "SELECT * FROM accounts WHERE account_document->>'id' IN (:customerId) and deleted ="
              + " false",
      nativeQuery = true)
  List<Accounts> findAccountsByAccountIds(@Param("customerId") List<String> customerId);

  @Query(
      value =
          "SELECT * FROM accounts WHERE account_document->>'NeedsSync' = :needsSync and"
              + " account_document->>'DataSource' = :dataSource and deleted = false",
      nativeQuery = true)
  List<Accounts> findAllByAccountDocumentNeedsSyncAndAccountDocumentDataSource(
      @Param("needsSync") String needsSync, @Param("dataSource") String dataSource);

  /**
   * This is required for the Crescendo integration @TODO Once we rollout all users we should check
   * and mark all unmarked records to Crescendo
   *
   * @param needsSync true/false
   * @return account
   */
  @Query(
      value =
          "SELECT * FROM accounts WHERE account_document->>'NeedsSync' = :needsSync and"
              + " (account_document->>'DataSource' IS NULL OR account_document->>'DataSource' NOT"
              + " LIKE 'LIFT') and deleted = false",
      nativeQuery = true)
  List<Accounts> findAllByAccountDocumentNeedsSyncForCrescendo(
      @Param("needsSync") String needsSync);

  /**
   * @TODO Not the most effective query. We can optimize it in the future
   *
   * @return List of accounts that include contacts for sync
   */
  @Query(
      value =
          "select accounts.* from accounts, jsonb_array_elements(CAST(account_document->>'Contacts'"
              + " as jsonb)) as cont where (accounts.account_document->>'DataSource' IS NULL OR"
              + " accounts.account_document->>'DataSource' NOT LIKE 'LIFT') AND"
              + " CAST(cont->>'NeedsSync' as jsonb) @> 'true' group by accounts.id",
      nativeQuery = true)
  List<Accounts> getAccountsWithNeedsSyncContactsForCrescendo();

  @Query(
      value =
          "select * from accounts where timezone('UTC',updated_date) > :from and"
              + " (account_document->>'DataSource' IS NULL OR account_document->>'DataSource' NOT"
              + " LIKE 'LIFT') and deleted = false",
      nativeQuery = true)
  List<Accounts> getAccountsByUpdatedDateAfterForCrescendo(Instant from);

  @Query(
      value =
          "SELECT * FROM accounts where (timezone('UTC',updated_date) > :dateFrom AND"
              + " timezone('UTC',updated_date) <= :dateTo)"
              + " ORDER BY account_document ->> 'id'",
      nativeQuery = true)
  Page<Accounts> findAllAccountsByFromAndToDate(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo, Pageable pageable);

  @Query(
      value =
          "SELECT DISTINCT a.account_document->>'id' FROM accounts a CROSS JOIN LATERAL"
              + " jsonb_array_elements_text(a.account_document->'Users') AS u(user_email) WHERE"
              + " u.user_email IN (:emails) and deleted = false",
      nativeQuery = true)
  List<String> findAccountIdsByEmailIn(@Param("emails") List<String> emails);

  @Query(
      value =
          "SELECT a.account_document->>'id' FROM accounts a WHERE a.account_document->> 'SubTypeID'"
              + " = 'FarmProducer' AND CAST(a.account_document -> 'Users' as jsonb)  @>"
              + " CAST(:currentLoggedInUser as jsonb) AND a.account_document->>'Active' = 'true'"
              + " AND ( EXISTS (     SELECT 1 FROM jsonb_array_elements(CASE         WHEN"
              + " jsonb_typeof(a.account_document->'UserRoles') = 'array' THEN"
              + " a.account_document->'UserRoles'         ELSE '[]'     END) AS userRole     WHERE"
              + " userRole->>'UserName' = :userEmail AND userRole->>'UserRole' <> 'Technical"
              + " Specialist' ) OR a.account_document->>'OwnerId' ilike :userEmail) AND"
              + " a.deleted=false",
      nativeQuery = true)
  List<String> findAccountIdsByUserWithAllFlags(
      @Param("currentLoggedInUser") String currentLoggedUser, @Param("userEmail") String userEmail);

  @Query(
      value =
          "select * from accounts where id in (select id from (select id, account_document->>'id',"
              + " account_document->>'GoldenRecordId', account_document->>'AccountName',"
              + " cast(jsonb_array_elements(CAST(account_document->>'UserRoles' as jsonb)) as"
              + " jsonb) as user_roles from accounts) ur where user_roles->>'UserName' like"
              + " :email)",
      nativeQuery = true)
  List<Accounts> findAccountsByUserEmail(String email);

  @Query(
      value =
          "SELECT a.* FROM accounts a WHERE a.account_document->> 'SubTypeID' = 'FarmProducer' AND"
              + " CAST(a.account_document -> 'Users' as jsonb)  @> CAST(:currentLoggedInUser as"
              + " jsonb) AND a.account_document->>'Active' = 'true' AND ( EXISTS (     SELECT 1"
              + " FROM jsonb_array_elements(CASE         WHEN"
              + " jsonb_typeof(a.account_document->'UserRoles') = 'array' THEN"
              + " a.account_document->'UserRoles'         ELSE '[]'     END) AS userRole     WHERE"
              + " userRole->>'UserName' = :userEmail AND userRole->>'UserRole' <> 'Technical"
              + " Specialist' ) OR a.account_document->>'OwnerId' ilike :userEmail) AND"
              + " a.deleted=false",
      nativeQuery = true)
  List<Accounts> findAccountsByUserWithAllFlags(
      @Param("currentLoggedInUser") String currentLoggedInUser,
      @Param("userEmail") String userEmail);

  @Query(
      value =
          "select * from accounts where "
              + " ( account_document->>'DataSource' "
              + " LIKE 'LIFT') and deleted = false  ",
      nativeQuery = true)
  List<Accounts> getAllLiftAccounts();

  @Query(
      value =
          "SELECT a.* FROM Accounts a where a.account_document->> 'GoldenRecordId'= :goldenRecordId"
              + " AND a.deleted=false",
      nativeQuery = true)
  List<Accounts> findAllByGoldenRecordId(@Param("goldenRecordId") String goldenRecordId);

  @Query(
      value =
          "SELECT * FROM accounts where (timezone('UTC',updated_date) > :dateFrom AND"
              + " timezone('UTC',updated_date) <= :dateTo)"
              + " ORDER BY account_document ->> 'id'",
      nativeQuery = true)
  List<Accounts> findAllAccountsByFromAndToDateV1(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo);

  @Query(
      value =
          "SELECT * FROM accounts where account_document->'Users' is not null and deleted = false",
      nativeQuery = true)
  List<Accounts> findAccountsWhereUserArrayIsNotNull();
}
