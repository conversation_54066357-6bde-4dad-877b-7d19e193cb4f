<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitViewModel.NutritionItem", [],
                "Nutrition",locale)}</span>
            <span class="lg-head">${localization.getMessage("Report.ForagePennState", [], "Forage Penn
                State",locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000141414FPS</div>
    </div>
</div>

<#if model.foragePennStateTool.scorerLabel??>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-secondary mb-1">
                    <span>${model.foragePennStateTool.scorerLabel!}</span>
                </h3>
            </div>
        </div>
    </div>
</#if>

<!-- 3 - Screen -->
<#if model.foragePennStateTool.details?? && model.foragePennStateTool.details[0]??>
    <div class="container">

        <div class="row">
            <div class="col-12 table-secondary">
                <table>
                    <thead>
                        <tr>
                            <#list model.foragePennStateTool.details[0]?keys as prop>
                                <#list model.foragePennStateTool.details[0][prop] as key>
                                    <th>${key.column!}</th>
                                </#list>
                            </#list>
                        </tr>
                    </thead>

                    <tbody>

                        <#list model.foragePennStateTool.details as item>
                            <#list item?keys as prop>
                                <tr>
                                    <td colspan="10">
                                        <h3 class="title mb-0">${prop!}</h3>
                                    </td>
                                </tr>
                                <tr>
                                    <#list item[prop] as key>
                                        <td>${key.value!}</td>
                                    </#list>
                                </tr>
                            </#list>
                        </#list>
                    </tbody>

                </table>
            </div>
        </div>
    </div>

</#if>
<#assign counter=0>
    <#if model.foragePennStateTool.graphs?? && model.foragePennStateTool.graphs[0]??>
        <#if model.foragePennStateTool.details?? && model.foragePennStateTool.details[0]??>
            <div class="break-page"></div>
        </#if>
        <!-- Graphs -->
        <div class="container">
            <#list model.foragePennStateTool.graphs?chunk(2) as row>
                <div class="row mx-neg-4">
                    <#list row as graph>
                        <div class="col-6 px-4">
                            <h5 class="title-sub">${graph.pspsLabel!}</h5>
                            <div class="card mb-1">
                                <div class="card-body">
                                    <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                        <canvas id="${linechart2}"></canvas>
                                </div>
                                <div class="card-footer">
                                    <div class="row">
                                        <div class="legend-wrap mb-2">
                                            <p class="middle-blue-solid">${localization.getMessage("Top", [], "Top",
                                                locale)}</p>
                                        </div>
                                        <div class="legend-wrap mb-2">
                                            <p class="jordy-blue-solid">
                                                ${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title",
                                                [], "Mid 1 (8 mm)", locale)}</p>
                                        </div>
                                        <div class="legend-wrap mb-2">
                                            <p class="blue-purple-solid">
                                                ${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle",
                                                [], "Mid 2 (4 mm)", locale)}</p>
                                        </div>
                                        <div class="legend-wrap mb-2">
                                            <p class="tulip-solid">${localization.getMessage("Tray", [], "Tray",
                                                locale)}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <script>
                            (function () {

                                const bar1 = [
                                    <#list graph.onScreenPercentage as val >
                                    ${(val.top)!'NaN'} <#sep>, </#sep>
</#list >
];

                            const bar2 = [
                                <#list graph.onScreenPercentage as val >
                                ${(val.mid1)!'NaN'} <#sep>, </#sep>
</#list >
];

                            const bar3 = [
                                <#list graph.onScreenPercentage as val >
                                ${(val.mid2)!'NaN'} <#sep>, </#sep>
</#list >
];

                            const bar4 = [
                                <#list graph.onScreenPercentage as val >
                                ${(val.tray)!'NaN'} <#sep>, </#sep>
</#list >
];

                            const xAxis = [
                                <#list graph.onScreenPercentage as val >
                                '${val.visitDate!}' <#sep>, </#sep>
</#list >
];

                            const ctx = document.getElementById("${linechart2}").getContext("2d");
                            ctx.canvas.height = 100;
                            const categoryPercentage = 0.8;
                            const barPercentage = 1;
                            const options = {
                                type: "bar",
                                data: {
                                    labels: xAxis,
                                    datasets: [
                                        {
                                            data: bar1,
                                            grouped: true,
                                            backgroundColor: "#7AD8DC",
                                            categoryPercentage: categoryPercentage,
                                            barPercentage: barPercentage,
                                            barThickness: 20,
                                            borderColor: "rgba(0,0,0,0)",
                                            borderWidth: 4,
                                        },

                                        {
                                            data: bar2,
                                            backgroundColor: "#83BEF4",
                                            categoryPercentage: categoryPercentage,
                                            barPercentage: barPercentage,
                                            barThickness: 20,
                                            borderColor: "rgba(0,0,0,0)",
                                            borderWidth: 4,
                                        },

                                        {
                                            data: bar3,
                                            backgroundColor: "#ABA1E3",
                                            categoryPercentage: categoryPercentage,
                                            barPercentage: barPercentage,
                                            barThickness: 20,
                                            borderColor: "rgba(0,0,0,0)",
                                            borderWidth: 4,
                                        },

                                        {
                                            data: bar4,
                                            backgroundColor: "#F18494",
                                            categoryPercentage: categoryPercentage,
                                            barPercentage: barPercentage,
                                            barThickness: 20,
                                            borderColor: "rgba(0,0,0,0)",
                                            borderWidth: 4,
                                        }
                                    ]
                                },
                                options: {
                                    plugins: {
                                        legend: {
                                            display: false,
                                        },
                                        tooltip: {
                                            callbacks: {
                                                title: () => null // or function () { return null; }
                                            },
                                            yAlign: 'bottom',
                                            backgroundColor: "#fff",
                                            borderColor: "rgba(0, 0, 0, 0.25)",
                                            borderWidth: 1,
                                            displayColors: false,
                                            bodyColor: "#307698",
                                            bodyAlign: "center",
                                        },
                                    },

                                    layout: {
                                        padding: {
                                            top: 20,
                                            right: 15
                                        }
                                    },

                                    responsive: true,

                                    scales: {
                                        y: {
                                            // beginAtZero: true,
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("Report.PercentageOnScreen", [], "On Screen %", locale)}',
                                                padding: {
                                                    bottom: 15,
                                                }
                                            },

                                            grid: {
                                                display: false,
                                            },
                                            ticks: {
                                                // Include a % sign in the ticks
                                                callback: function (value, index, ticks) {
                                                    return value + '%';
                                                }
                                            }
                                        },

                                        x: {
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                padding: {
                                                    top: 15,
                                                }
                                            },
                                            grid: {
                                                display: false,
                                            },
                                        }
                                    },
                                    animation: {
                                        duration: 0,
                                        onComplete: function () {
                                            var chart = this;
                                            var ctx = chart.ctx;
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'bottom';
                                            ctx.fillStyle = '#6C7782';
                                            this.data.datasets.forEach(function (dataset, i) {
                                                var meta = chart.getDatasetMeta(i);
                                                meta.data.forEach(function (bar, index) {
                                                    var data = dataset.data[index];
                                                    data = isNaN(data) ? '' : data + '%';
                                                    var yIndex = bar.y - 5;
                                                    if (data && data < 0) {
                                                        yIndex = bar.y + 15;
                                                    }
                                                    var fontSize = Math.round(chart.chartArea.width / dataset.data.length / 3);
                                                    if (fontSize > 12) {
                                                        fontSize = 12;
                                                    } else if (fontSize < 5) {
                                                        fontSize = 5;
                                                    }
                                                    ctx.font = fontSize + 'px "Helvetica Neue", Helvetica, Arial, sans-serif'
                                                    ctx.save();
                                                    // Translate 0,0 to the point you want the text
                                                    ctx.translate(bar.x + 5, yIndex);
                                                    // Rotate context by -90 degrees
                                                    ctx.rotate(-0.4 * Math.PI);
                                                    ctx.textAlign = "center";
                                                    ctx.fillText(data, 5, 0);
                                                    ctx.restore();
                                                    chart.resize();
                                                });
                                            });
                                        }
                                    }
                                }
                            };

                            window.myLine = new Chart(ctx, options);
                                }) ();

                        </script>
                        <#assign counter=counter+1>
                    </#list>
                </div>
                <#if counter%10==0>
                    <div class="break-page"></div>
                </#if>
            </#list>
        </div>

    </#if>

    <#if model.foragePennStateTool?? && model.foragePennStateTool.notes??>
        <div class="container mid-body">
            <div class="pt-0">
                <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                    locale)}</h3>
                <#list model.foragePennStateTool.notes as innerlist>
                    <#if innerlist.id??>
                        <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                            <h4 class="followup mb-1">
                                <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                                <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                            </h4>
                            <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                            <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                                <div class="notes-images mb-1">
                                    <#list noteFound.mediaItems as media>
                                        <figure>
                                            <img src="${media.base64EncodedImage!}">
                                        </figure>
                                    </#list>
                                </div>
                            </#if>
                        </#list>
                    </#if>
                </#list>
            </div>
        </div>
    </#if>