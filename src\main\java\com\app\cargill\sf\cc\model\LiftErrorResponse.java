/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.app.cargill.sf.cc.utils.LiftErrorCode;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/** [{"message":"entity is deleted","errorCode":"ENTITY_IS_DELETED","fields":[]}] */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class LiftErrorResponse {
  private String message;
  private LiftErrorCode errorCode;
  private List<String> fields;
}
