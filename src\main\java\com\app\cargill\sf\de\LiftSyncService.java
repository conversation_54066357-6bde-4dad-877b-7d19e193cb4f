/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.SitesAndMappingsWrapper;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.AccountMergeRecordDto;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.LongRunningTask.TaskName;
import com.app.cargill.model.Sites;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.data.MergeAccountsService;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.service.LiftAccountMergeService;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftObjectDeletedException;
import com.app.cargill.sf.cc.service.LiftSitesService;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.joda.time.Instant;
import org.joda.time.Period;
import org.joda.time.PeriodType;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@RequiredArgsConstructor
@Component
@Slf4j
public class LiftSyncService {

  private final LiftAccountService accountService;
  private final LiftSitesService sitesService;
  private final DataSynchronizerFactory synchronizerFactory;
  private final LiftApiService liftApiService;
  private final ResourceBundleMessageSource messageSource;
  private final LiftContactService liftContactService;
  private final AccountsRepository accountsRepository;
  private final SitesRepository sitesRepository;
  private final LiftAccountMergeService accountMergeService;
  private final MergeAccountsService mergeAccountsService;

  @Async
  public CompletableFuture<SyncResult> executeAccountsSync(Instant timestamp) {
    try {
      DateTime start = DateTime.now();
      List<AccountDocument> accountDocuments;
      if (timestamp != null) {
        accountDocuments =
            accountService.getAllAccounts(
                Collections.singletonList(String.format("LastModifiedDate>%s", timestamp)));
      } else {
        accountDocuments = accountService.getAllAccounts();
      }

      DataSynchronizer<AccountDocument> synchronizer =
          synchronizerFactory.getSynchronizer(AccountDocument.class);
      SyncResult syncResult = synchronizer.sync(accountDocuments);
      DateTime end = DateTime.now();
      Period duration =
          new Duration(start, end).toPeriod().normalizedStandard(PeriodType.standard());

      syncResult.setDuration(duration.toString());
      return CompletableFuture.completedFuture(syncResult);
    } catch (Exception e) {
      return CompletableFuture.failedFuture(e);
    }
  }

  public CompletableFuture<SyncResult> executeAccountsMergeSync(Instant timestamp) {
    try {
      DateTime start = DateTime.now();
      List<AccountMergeRecordDto> mergedAccounts = null;
      if (timestamp != null) {
        mergedAccounts =
            accountMergeService.getAllMergedAccounts(
                Collections.singletonList(String.format("LastModifiedDate>%s", timestamp)));
      } else {
        mergedAccounts = accountMergeService.getAllMergedAccounts(null);
      }
      if (!mergedAccounts.isEmpty()) {
        mergedAccounts.stream()
            .forEach(
                mergedAccount -> {
                  mergeAccountsService.transferAccountData(
                      mergedAccount.getSourceId(), mergedAccount.getTargerId());
                  // fix site count
                  fixSiteCountForMergedAccount(mergedAccount.getTargerId());
                });
      }

      SyncResult syncResult = new SyncResult("Merged Accounts");
      DateTime end = DateTime.now();
      Period duration =
          new Duration(start, end).toPeriod().normalizedStandard(PeriodType.standard());

      syncResult.setDuration(duration.toString());
      return CompletableFuture.completedFuture(syncResult);
    } catch (Exception e) {
      return CompletableFuture.failedFuture(e);
    }
  }

  public void fixSiteCountForMergedAccount(String targerId) {
    List<Accounts> accounts = accountsRepository.findAllByGoldenRecordId(targerId);

    if (accounts.isEmpty()) {
      return;
    }
    Accounts account = accounts.get(0);
    account
        .getAccountDocument()
        .setSiteCount(
            sitesRepository.countByAccountId(account.getAccountDocument().getId().toString()));
    accountsRepository.save(account);
  }

  @Async
  public CompletableFuture<SyncResult> executeSitesSync(Instant timestamp) {
    try {
      // @TODO Currently getAllSites is a blocking operation.
      //  Should be changed to async leveraging WebClient reactive calls
      DateTime start = DateTime.now();

      SitesAndMappingsWrapper liftSitesResult = sitesService.getAllSites(timestamp);
      List<SiteDocument> siteDocuments = liftSitesResult.getSiteDocuments();
      List<SiteMappingDocument> siteMappingDocuments = liftSitesResult.getSiteMappingDocuments();
      SyncResult sitesSyncResult = sitesSync(siteDocuments, start);
      SyncResult smSyncResult = siteMappingsSync(siteMappingDocuments, start);
      log.info("SiteMappings LIFT sync completed: {}", smSyncResult);

      sitesSyncResult.setDuration(smSyncResult.getDuration());

      return CompletableFuture.completedFuture(sitesSyncResult);
    } catch (Exception e) {
      return CompletableFuture.failedFuture(e);
    }
  }

  public SyncResult executeMissingMappingSiteSync(String siteId) {
    try {
      DateTime start = DateTime.now();

      SitesAndMappingsWrapper liftSitesResult = sitesService.getSite(siteId);
      List<SiteDocument> siteDocuments = liftSitesResult.getSiteDocuments();
      List<SiteMappingDocument> siteMappingDocuments = liftSitesResult.getSiteMappingDocuments();

      if (siteMappingDocuments.isEmpty()) {
        return new SyncResult(
            "SiteMappingsFIx",
            new AtomicInteger(0),
            new AtomicInteger(0),
            new AtomicInteger(0),
            "0",
            "");
      }

      SyncResult smSyncResult = siteMappingsSync(siteMappingDocuments, start);
      for (SiteDocument siteDocument : siteDocuments) {
        Sites existingSite =
            sitesRepository.findBySiteIdUnfiltered(siteDocument.getId().toString());
        if (existingSite != null
            && !siteDocument
                .getExternalId()
                .equals(existingSite.getSiteDocument().getExternalId())) {
          existingSite.getSiteDocument().setExternalId(siteDocument.getExternalId());
          sitesRepository.save(existingSite);
        }
      }
      log.info("SITE_MAPPINGS_FIX_COMPLETE: {}", smSyncResult);

      smSyncResult.setDuration(smSyncResult.getDuration());

      return smSyncResult;
    } catch (Exception e) {
      log.error("SITE_MAPPINGS_FIX_COMPLETE_WITH_ERROR", e);
      return new SyncResult(
          "SiteMappingsFIx",
          new AtomicInteger(0),
          new AtomicInteger(0),
          new AtomicInteger(0),
          "0",
          e.getMessage());
    }
  }

  public Mono<SyncResult> syncAccountsToSalesforce(List<Accounts> accounts) {
    AuthToken authToken = accountService.getAuthToken();
    String apiPath = accountService.getApiPath();
    return Flux.fromIterable(accounts)
        .publishOn(Schedulers.boundedElastic())
        .filter(
            account -> {
              if (account.getAccountDocument().getGoldenRecordId() == null) {
                log.error("MISSING_GOLDEN_RECORD_ID {}", account.getId());
                return false;
              }
              return true;
            })
        .map(
            account -> {
              try {
                accountService.updateAccount(authToken, apiPath, account.getAccountDocument());
                if (account.getAccountDocument().getContacts() != null
                    && !account.getAccountDocument().getContacts().isEmpty()) {
                  liftContactService.updateContact(
                      account.getAccountDocument().getContacts().get(0),
                      messageSource,
                      Locale.ENGLISH);
                }
                account.getAccountDocument().setNeedsSync(false);
                accountsRepository.save(account);
                return true;
              } catch (LiftObjectDeletedException e) {
                log.warn(
                    "LIFT_OBJECT_DELETED {} {} {}",
                    account.getId(),
                    account.getAccountDocument().getId(),
                    account.getAccountDocument().getGoldenRecordId());
                account.setDeleted(true);
                account.getAccountDocument().setDeleted(true);
                accountsRepository.save(account);
                return false;
              } catch (Exception e) {
                log.error("Error occurred while syncing account to LIFT: {}", account.getId(), e);
                return false;
              }
            })
        .reduce(
            new SyncResult(TaskName.ACCOUNTS_TO_LIFT_SYNC.name()),
            (current, apiResult) -> {
              if (Boolean.TRUE.equals(apiResult)) {
                current.incrementModified();
              } else {
                current.incrementFailures();
              }
              return current;
            });
  }

  public Mono<SyncResult> syncSitesToSalesforce(List<Sites> sites) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApiService.getTokenAndApiPath();
    return Flux.fromIterable(sites)
        .publishOn(Schedulers.boundedElastic())
        .map(
            site -> {
              if (sitesService.updateSite(
                  tokenAndApiPath.getAuthToken(),
                  tokenAndApiPath.getApiPath(),
                  site.getSiteDocument())) {
                site.getSiteDocument().setNeedsSync(false);
                sitesRepository.save(site);
                return true;
              } else {
                log.error("Error occurred while syncing site to LIFT: {}", site.getId());
                return false;
              }
            })
        .reduce(
            new SyncResult(TaskName.SITES_TO_LIFT_SYNC.name()),
            (current, apiResult) -> {
              if (Boolean.TRUE.equals(apiResult)) {
                current.incrementModified();
              } else {
                current.incrementFailures();
              }
              return current;
            });
  }

  public SyncResult fixSwitchedSiteMappings(String siteId) {
    String jobName = "SwitchedSiteMappingsFix";
    try {
      SitesAndMappingsWrapper liftSitesResult = sitesService.getSite(siteId);
      List<SiteDocument> siteDocuments = liftSitesResult.getSiteDocuments();

      if (siteDocuments.isEmpty()) {
        return new SyncResult(
            jobName, new AtomicInteger(0), new AtomicInteger(0), new AtomicInteger(0), "0", "");
      }

      for (SiteDocument siteDocument : siteDocuments) {
        processSiteDocument(siteDocument);
      }
      log.info("SWITCHED_SITE_MAPPINGS_FIX_COMPLETE: {}", liftSitesResult);

      return new SyncResult(
          jobName,
          new AtomicInteger(0),
          new AtomicInteger(siteDocuments.size()),
          new AtomicInteger(0),
          "0",
          "");
    } catch (Exception e) {
      log.error("SWITCHED_SITE_MAPPINGS_FIX_COMPLETE_WITH_ERROR", e);
      return new SyncResult(
          jobName,
          new AtomicInteger(0),
          new AtomicInteger(0),
          new AtomicInteger(0),
          "0",
          e.getMessage());
    }
  }

  public SyncResult executeExternalIdSiteSync(String siteId) {
    try {
      DateTime start = DateTime.now();

      SitesAndMappingsWrapper liftSitesResult = sitesService.getSite(siteId);
      List<SiteDocument> siteDocuments = liftSitesResult.getSiteDocuments();

      for (SiteDocument siteDocument : siteDocuments) {
        Sites existingSite =
            sitesRepository.findBySiteIdUnfiltered(siteDocument.getId().toString());
        if (existingSite != null) {
          existingSite.getSiteDocument().setExternalId(siteDocument.getExternalId());
          existingSite.getSiteDocument().setDataSource(DataSource.LIFT);
          sitesRepository.save(existingSite);
        }
      }
      Period duration =
          new Duration(start, Instant.now()).toPeriod().normalizedStandard(PeriodType.standard());
      log.info(
          "SITE_EXTERNAL_ID_FIX_COMPLETE Modified: {} SiteId: {}", siteDocuments.size(), siteId);

      return new SyncResult(
          "SITE_EXTERNAL_ID_FIX",
          new AtomicInteger(0),
          new AtomicInteger(siteDocuments.size()),
          new AtomicInteger(0),
          duration.toString(),
          "");

    } catch (Exception e) {
      log.error("SITE_EXTERNAL_ID_FIX_COMPLETE_WITH_ERROR", e);
      return new SyncResult(
          "SITE_EXTERNAL_ID_FIX",
          new AtomicInteger(0),
          new AtomicInteger(0),
          new AtomicInteger(0),
          "0",
          e.getMessage());
    }
  }

  private void processSiteDocument(SiteDocument siteDocument) {
    try {
      Sites existingSite = sitesRepository.findBySiteIdUnfiltered(siteDocument.getId().toString());
      if (existingSite != null) {
        existingSite.getSiteDocument().setDataSourceMappings(siteDocument.getDataSourceMappings());
        sitesRepository.save(existingSite);
      }
    } catch (Exception e) {
      log.error("SWITCHED_SITE_MAPPINGS_FIX_ERROR for site {}", siteDocument.getId().toString(), e);
    }
  }

  private SyncResult sitesSync(List<SiteDocument> siteDocuments, DateTime start) {
    DataSynchronizer<SiteDocument> synchronizer =
        synchronizerFactory.getSynchronizer(SiteDocument.class);
    SyncResult syncResult = synchronizer.sync(siteDocuments);
    DateTime end = DateTime.now();
    Period duration = new Duration(start, end).toPeriod().normalizedStandard(PeriodType.standard());
    syncResult.setDuration(duration.toString());
    return syncResult;
  }

  private SyncResult siteMappingsSync(List<SiteMappingDocument> smDocuments, DateTime start) {
    DataSynchronizer<SiteMappingDocument> synchronizer =
        synchronizerFactory.getSynchronizer(SiteMappingDocument.class);
    SyncResult syncResult = synchronizer.sync(smDocuments);
    DateTime end = DateTime.now();
    Period duration = new Duration(start, end).toPeriod().normalizedStandard(PeriodType.standard());
    syncResult.setDuration(duration.toString());
    return syncResult;
  }
}
