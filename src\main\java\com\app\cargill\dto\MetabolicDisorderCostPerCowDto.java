/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Barns are contained within a site. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetabolicDisorderCostPerCowDto {

  private String visitDate;
  private boolean currentVisit;
  @JsonIgnore private Instant visitDateObject;
  @JsonIgnore private String formattedVisitDate;
  @JsonIgnore private String metabolicIncidenceCase;
  private Double cost;
}
