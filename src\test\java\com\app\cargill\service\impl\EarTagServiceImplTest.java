/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.EarTagDocument;
import com.app.cargill.dto.EarTagDto;
import com.app.cargill.dto.SaveEarTagDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.model.EarTags;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.EarTagsRepository;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class EarTagServiceImplTest {

  @Mock AccountsRepository accountsRepository;
  @Mock IUserService userServiceImpl;
  @Mock EarTagsRepository earTagsRepository;
  @InjectMocks EarTagServiceImpl earTagService;

  @Test
  void whenAllDataIsProvidedExpectedResultIsReturned() {
    Page<EarTags> earTags = new PageImpl<>(List.of(loadEarTags()));
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(earTagsRepository.findByAccountIds(any(), any(), any())).thenReturn(earTags);

    Page<EarTagDto> result =
        earTagService.getAllEarTagsPaginated(0, 10, "id", "desc", Instant.now());
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenDataIsNullNoExceptionIsThrown() {
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(earTagsRepository.findByAccountIds(any(), any(), any())).thenReturn(null);

    Page<EarTagDto> result =
        earTagService.getAllEarTagsPaginated(0, 10, "id", "desc", Instant.now());
    assertNotNull(result);
    assertEquals(0, result.getSize());
  }

  @Test
  void whenCorrectDataIsSentProperResultIsSaved() {
    EarTagDto earTagDto =
        EarTagDto.builder()
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .earTags(List.of(SaveEarTagDto.builder().earTagName("Test").isDeleted(false).build()))
            .build();
    when(earTagsRepository.existsByLocalId(any())).thenReturn(false);
    when(earTagsRepository.saveAll(any())).thenReturn(List.of(loadEarTags()));

    List<EarTagDto> result = earTagService.save(earTagDto);
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void whenLocalIdExistsSaveThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    EarTagDto earTagDto =
        EarTagDto.builder()
            .localId(localId)
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .earTags(List.of(SaveEarTagDto.builder().earTagName("Test").isDeleted(false).build()))
            .build();
    when(earTagsRepository.existsByLocalId(localId)).thenReturn(true);
    Assertions.assertThrows(AlreadyExistsDEException.class, () -> earTagService.save(earTagDto));
  }

  @Test
  void whenAllDataIsFilledProperlyItIsUpdated() {
    EarTagDto earTagDto =
        EarTagDto.builder()
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .earTags(
                List.of(
                    SaveEarTagDto.builder()
                        .id(UUID.randomUUID())
                        .earTagName("Test")
                        .isDeleted(false)
                        .build()))
            .build();
    when(earTagsRepository.findByEarTagId(any())).thenReturn(loadEarTags());
    when(earTagsRepository.saveAll(any())).thenReturn(List.of(loadEarTags()));
    List<EarTagDto> result = earTagService.update(earTagDto);
    assertEquals(1, result.size());
    assertNotNull(result);
  }

  @Test
  void whenEarTagIsNotFoundItIsCreated() {
    EarTagDto earTagDto =
        EarTagDto.builder()
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .earTags(
                List.of(
                    SaveEarTagDto.builder()
                        .id(UUID.randomUUID())
                        .earTagName("Test")
                        .isDeleted(false)
                        .build()))
            .build();
    when(earTagsRepository.findByEarTagId(any())).thenReturn(null);
    when(earTagsRepository.saveAll(any())).thenReturn(List.of(loadEarTags()));
    List<EarTagDto> result = earTagService.update(earTagDto);
    assertEquals(1, result.size());
    assertNotNull(result);
  }

  private EarTags loadEarTags() {

    return EarTags.builder()
        .earTagDocument(
            EarTagDocument.builder()
                .accountId(UUID.randomUUID())
                .id(UUID.randomUUID())
                .siteId(UUID.randomUUID())
                .earTagName("Test")
                .build())
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }
}
