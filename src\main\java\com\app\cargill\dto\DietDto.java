/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.DietSource;
import com.app.cargill.document.AnimalClass;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DietDto {

  private UUID id;

  private UUID siteId;

  private UUID labyrinthAccountId;

  private String name;

  private String breedName;

  private UUID breedId;

  private Instant startDate;

  private Instant endDate;

  private AnimalClass animalType;
  private UUID animalTypeId;
  private UUID barnId;

  private String environmentName;

  private Double reportMilkWeight;

  private Integer numberOfAnimals;

  private DietSource source;

  @Builder.Default private Boolean selected = false;

  private List<UUID> selectedPenGuids;

  @Builder.Default private Boolean isSystemGenerated = false;

  @Builder.Default private Boolean isDeleted = false;

  private Boolean isActive;

  private AnalyzeDietOptimizationDto analyzeOptimization;

  private FormulateDietOptimizationDto formulateOptimization;
  private Instant updatedDate;
}
