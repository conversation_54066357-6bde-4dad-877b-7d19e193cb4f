/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.dto.ReflectionTestDto;
import com.app.cargill.sf.cc.model.MetaDataFields;
import com.app.cargill.sf.cc.model.MetaDataPicklistValues;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Locale;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class ReflectionUtilsTest {
  @Mock ResourceBundleMessageSource resourceBundleMessageSource;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
  }

  // write test for getFieldNamesFromJsonProperty method
  @Test
  void getFieldNamesFromJsonProperty() throws IOException {
    assertNotNull(
        ReflectionUtils.getFieldNamesFromJsonProperty(
            ReflectionTestDto.class.getDeclaredFields()[0]));
  }

  // write test for ReflectionUtilsTest.applyRules method when all conditions are true
  @Test
  void applyRules() throws IOException {

    PayloadValidationDto build = PayloadValidationDto.builder().build();
    build.setErrorDetails(new ArrayList<>());
    Field field = ReflectionTestDto.class.getDeclaredFields()[0];
    field.setAccessible(true);
    ReflectionUtils.applyRules(
        new ReflectionTestDto(),
        field,
        getMetadataFields(),
        null,
        build,
        Locale.ENGLISH,
        resourceBundleMessageSource);
    assertNotNull(build.getErrorDetails());
    ReflectionUtils.applyRules(
        new ReflectionTestDto(),
        field,
        getMetadataFields(),
        "test",
        build,
        Locale.ENGLISH,
        resourceBundleMessageSource);
    assertNotNull(build.getErrorDetails());
  }

  MetaDataFields getMetadataFields() {
    return MetaDataFields.builder()
        .name("Salutation")
        .nillable(true)
        .type("string")
        .picklistValues(
            Collections.singletonList(
                MetaDataPicklistValues.builder().active(true).value("Mr.").label("Mr.").build()))
        .aggregatable(true)
        .build();
  }
}
