/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.cosmos.model.PenCosmos;
import com.app.cargill.document.PenDocument;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class PenMapperTest {

  @Test
  void map() {
    PenCosmos penCosmos = PenCosmos.builder().id(UUID.randomUUID().toString()).build();

    PenDocument result = PenMapper.map(penCosmos);
    assertNotNull(result);
  }
}
