/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.sf.crescendo.model.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CrescendoActivityServiceTest {

  @Mock private ActivitiesRepository activityRepository;

  @InjectMocks private CrescendoActivityService activityService;

  @Test
  void whenNewActivityIsFetchedAllRequiredFieldsExist() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<Activities> dbItems =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_db_activity.json"),
            new TypeReference<>() {});
    when(activityRepository.getActivitiesByUpdatedDateAfterForCrescendo(any())).thenReturn(dbItems);
    List<ActivityCrescendo> items = activityService.getActivity(Instant.now());
    assertEquals(1, items.size());

    ActivityCrescendo activityCrescendo = items.get(0);
    assertNotNull(activityCrescendo.getActivityType());
    assertNotNull(activityCrescendo.getAccountId());
    //  assertAccountValues(accountCrescendo);

  }

  private void assertAccountValues(AccountCrescendo accountCrescendo) {
    assertEquals("LM", accountCrescendo.getSourceSystem());
    assertEquals("Farm Producer", accountCrescendo.getSubTypeId().getValue());
    assertEquals(1, accountCrescendo.getUsers().size());
    assertEquals("Standard", accountCrescendo.getAccountType().getValue());
  }

  private void assertContact(ContactCrescendo contactCrescendo) {
    assertNotNull(contactCrescendo.getAccountId());
    assertNotNull(contactCrescendo.getFirstName());
    assertNotNull(contactCrescendo.getLastName());
    assertNotNull(contactCrescendo.getMarketingId());
    assertNotNull(contactCrescendo.getId());
    assertNotNull(contactCrescendo.getCreateTimeUtc());
    assertNotNull(contactCrescendo.getLastModifiedTimeUtc());
    assertNotNull(contactCrescendo.getLastSyncTimeUtc());
    assertFalse(contactCrescendo.getIsNew());
    // @TODO assert address structure
    // "MailingAddress": {
    //        "Street": null,
    //            "City": null,
    //            "StateOrProvince": "Dadra and Nagar Haveli",
    //            "PostalCode": null,
    //            "Country": "India",
    //            "AddressID": "********-0000-0000-0000-************",
    //            "CountyCommunity": null
    //      }
    //    assertNotNull(contactCrescendo.getMailingAddress());
  }

  private Accounts generateRandomAccount() throws ParseException {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setNeedsSync(true);
    return setAccountFields(accountDocument);
  }

  private Accounts generateRandomAccountFullData() throws ParseException {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setSegmentStepOneId("Kobe");
    accountDocument.setNeedsSync(true);
    Address address = new Address();
    accountDocument.setPhysicalAddress(address);
    Contact contact = new Contact();
    contact.setContactId(UUID.randomUUID());
    accountDocument.setContacts(List.of(contact));
    return setAccountFields(accountDocument);
  }

  private AccountCrescendo generateRandomAccountCrescendo() {
    return new AccountCrescendo();
  }

  private Accounts setAccountFields(AccountDocument accountDocument) throws ParseException {
    Accounts account = new Accounts(accountDocument);

    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");

    account.setId(8066L);
    account.setLocalId(UUID.randomUUID().toString());
    account.setDeleted(false);

    account.setCreatedDate(formatter.parse("2023-06-07 08:39:56.983"));
    account.setUpdatedDate(formatter.parse("2023-06-07 08:42:57.287"));
    return account;
  }

  private AccountCrescendo generateRandomAccountCrescendoFullData() {
    AccountCrescendo accountCrescendo = new AccountCrescendo();
    accountCrescendo.setId(UUID.randomUUID().toString());
    accountCrescendo.setAccountType(AccountTypeCrescendo.CONSUMER);
    accountCrescendo.setSegmentStepOneId(SegmentStepOneIdCrescendo.KOBE);
    accountCrescendo.setNineBoxStepTwoID(NineBoxStepTwoIDCrescendo.LARGE_SELECTED);
    accountCrescendo.setSubTypeId(SubTypeIdCrescendo.BARN);
    accountCrescendo.setBusinessId(BusinessUnitCrescendo.BRAZIL);
    accountCrescendo.setExternalParentAccountId(UUID.randomUUID().toString());
    accountCrescendo.setAccountCurrency(CurrencyCrescendo.BGL);
    accountCrescendo.setAccountStatus(AccountStatusCrescendo.COMPLETE);
    accountCrescendo.setUsers(Set.of("User-X"));
    accountCrescendo.setPhysicalAddress(new AddressCrescendo());

    ContactCrescendo contactCrescendo = new ContactCrescendo();
    contactCrescendo.setMailingAddress(new AddressCrescendo());
    contactCrescendo.setOtherAddress(new AddressCrescendo());
    contactCrescendo.setBusinessId(BusinessUnitCrescendo.BRAZIL);
    contactCrescendo.setReportsToID(UUID.randomUUID().toString());
    contactCrescendo.setExternalId(UUID.randomUUID().toString());
    contactCrescendo.setLeadSource(LeadSourceCrescendo.PARTNER);
    contactCrescendo.setLevel(ContactLevelCrescendo.PRIMARY);
    contactCrescendo.setLastSyncTimeUtc(Instant.now());

    accountCrescendo.setContacts(List.of(contactCrescendo));

    return accountCrescendo;
  }
}
