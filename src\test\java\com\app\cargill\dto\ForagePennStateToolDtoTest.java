/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.constants.RumenHealthTmrScores;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

class ForagePennStateToolDtoTest {

  @Test
  void whenScorerNonSelectedIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "NoneSelected"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.NoneSelected, fpst.getScorer());
  }

  @Test
  void whenScorer0StringIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "0"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.NoneSelected, fpst.getScorer());
  }

  @Test
  void whenScorer0IntIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": 0
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.NoneSelected, fpst.getScorer());
  }

  @Test
  void whenScorerFourScreenNewIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "FourScreenNew"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenNew, fpst.getScorer());
  }

  @Test
  void whenScorer1StringIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "1"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenNew, fpst.getScorer());
  }

  @Test
  void whenScorer1IntIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": 1
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenNew, fpst.getScorer());
  }

  @Test
  void whenScorerThreeScreenIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "ThreeScreen"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.ThreeScreen, fpst.getScorer());
  }

  @Test
  void whenScorer2StringIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "2"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.ThreeScreen, fpst.getScorer());
  }

  @Test
  void whenScorer2IntIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": 2
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.ThreeScreen, fpst.getScorer());
  }

  @Test
  void whenScorerFourScreenOldIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "FourScreenOld"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenOld, fpst.getScorer());
  }

  @Test
  void whenScorer3StringIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "3"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenOld, fpst.getScorer());
  }

  @Test
  void whenScorerEmptyIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": ""
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenNew, fpst.getScorer());
  }

  @Test
  void whenScorerRandomStringIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": "ababa"
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenNew, fpst.getScorer());
  }

  @Test
  void whenScorerRandomIntIsOk() throws JsonProcessingException {
    String input =
        """
        {
          "visitid": "30cf1a8a-374b-42cb-900c-6838e9413768",
          "scorer": 123
        }

        """;
    ObjectMapper objectMapper = new ObjectMapper();
    ForagePennStateToolDto fpst = objectMapper.readValue(input, ForagePennStateToolDto.class);
    assertEquals(RumenHealthTmrScores.FourScreenNew, fpst.getScorer());
  }
}
