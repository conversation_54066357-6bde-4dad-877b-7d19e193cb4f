/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.app.cargill.sf.cc.model.BusinessIdDeserializer;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.PhysicalAddressSalesforce;
import com.app.cargill.sf.cc.model.RecordAttributes;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.UserAccessContainer;
import com.app.cargill.sf.cc.model.UserReferenceRecord;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** This is the simplified Salesforce LIFT Account model containing only the fields of interest */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class Account implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("BillingAddress")
  private PhysicalAddressSalesforce billingAddress;

  @JsonProperty("CustomerAccountType")
  @JsonAlias({"CustomerAccountType", "Customer_Account_Type__c"})
  private String customerAccountType;

  @JsonProperty("SegmentStepOneId")
  @JsonAlias({"SegmentStepOneId", "Segment__c"})
  private String segmentStepOneId;

  @JsonProperty("NineBoxStepTwoId")
  @JsonAlias({"NineBoxStepTwoId", "X9_Box__c"})
  private String nineBoxStepTwoId;

  @JsonProperty("SourceSystem")
  @JsonAlias({"SourceSystem", "Source_System__c"})
  private String sourceSystem;

  @JsonProperty("SubTypeID")
  @JsonAlias({"SubTypeID", "Business_Type__c"})
  private String subTypeId;

  @JsonProperty("BusinessID")
  @JsonAlias({"BusinessID", "Cargill_Business__c"})
  @JsonDeserialize(using = BusinessIdDeserializer.class)
  private String businessId;

  @JsonProperty("ParentId")
  private String parentId;

  // This field does not exist in LIFT
  @JsonProperty("ExternalParentAccountID")
  @JsonAlias({"ExternalParentAccountID", "Parent_Account_Customer_Number__c"})
  private String externalParentAccountId;

  @JsonProperty("ReviewStatus")
  @JsonAlias({"ReviewStatus", "Review_Status__c"})
  private String reviewStatus;

  @JsonProperty("Website")
  private String website;

  // There are only 28 records with value of false on production
  @JsonProperty("Visible")
  @JsonAlias({"Visible", "Visible_on_DE__c"})
  private boolean isVisible;

  @JsonProperty("Email")
  @JsonAlias({"Email", "Email__c"})
  private String email;

  @JsonProperty("Phone")
  @JsonAlias("Phone")
  private String phone;

  @JsonProperty("AccountCurrency")
  @JsonAlias({"AccountCurrency", "CurrencyIsoCode"})
  private String accountCurrency;

  @JsonProperty("CustomerNumber")
  @JsonAlias({"CustomerNumber", "Customer_Number__c"})
  private String customerNumber;

  @JsonProperty("NewAccountType")
  @JsonAlias({"NewAccountType", "New_Account_Type__c"})
  private String newAccountType;

  @JsonProperty("AccountStatus")
  @JsonAlias({"AccountStatus", "Account_Status__c"})
  private String accountStatus;

  @JsonProperty("AccountExternalId")
  @JsonAlias({"AccountExternalId", "DE_Account_External_ID__c"})
  private String accountExternalId;

  @JsonProperty("LastModifiedById")
  private String lastModifiedById;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("DateOfLastCall")
  @JsonAlias({"DateOfLastCall", "Date_Of_Last_Call__c"})
  private String dateOfLastCall;

  @JsonProperty("LastModifiedDate")
  private String lastModifiedDate;

  @JsonProperty("Owner")
  private UserReferenceRecord owner;

  @JsonProperty("LastModifiedBy")
  private UserReferenceRecord lastModifiedBy;

  @JsonProperty("CreatedBy")
  private UserReferenceRecord createdBy;

  @JsonProperty("Contacts")
  private SalesforceRecordsResponse<ContactSalesforce> contacts;

  @JsonProperty("DE_User_Access__r")
  private SalesforceRecordsResponse<UserAccessContainer> userAccessContainer;

  @JsonProperty("External_Data_Sources__r")
  private SalesforceRecordsResponse<ExternalDataSource> externalDataSources;
}
