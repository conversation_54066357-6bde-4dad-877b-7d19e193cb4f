/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.SilageDocument;
import com.app.cargill.dto.SaveSilageDto;
import com.app.cargill.dto.SilageDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.model.Silages;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SilagesRepository;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
public class SilageServiceImplTest {

  @Mock AccountsRepository accountsRepository;
  @Mock IUserService userServiceImpl;
  @Mock SilagesRepository silagesRepository;
  @InjectMocks SilageServiceImpl silageServiceImpl;

  @Test
  void whenAllDataIsProvidedExpectedResultIsReturned() {
    Page<Silages> silages = new PageImpl<>(List.of(loadSilages()));
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(silagesRepository.findByAccountIdsPaginated(any(), any(), any())).thenReturn(silages);

    Page<SilageDto> result =
        silageServiceImpl.getAllSilagesPaginated(0, 10, "id", "desc", Instant.now());
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenDataIsNullNoExceptionIsThrown() {
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(silagesRepository.findByAccountIdsPaginated(any(), any(), any())).thenReturn(null);

    Page<SilageDto> result =
        silageServiceImpl.getAllSilagesPaginated(0, 10, "id", "desc", Instant.now());
    assertNotNull(result);
    assertEquals(0, result.getSize());
  }

  @Test
  void whenCorrectDataIsSentProperResultIsSaved() {
    SilageDto silageDto =
        SilageDto.builder()
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .silages(List.of(SaveSilageDto.builder().silageName("Test").deleted(false).build()))
            .build();
    when(silagesRepository.existsByLocalId(any())).thenReturn(false);
    when(silagesRepository.saveAll(any())).thenReturn(List.of(loadSilages()));

    List<SilageDto> result = silageServiceImpl.save(silageDto);
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  @Test
  void whenLocalIdExistsSaveThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    SilageDto silageDto =
        SilageDto.builder()
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .localId(localId)
            .silages(List.of(SaveSilageDto.builder().silageName("Test").deleted(false).build()))
            .build();
    when(silagesRepository.existsByLocalId(localId)).thenReturn(true);
    Assertions.assertThrows(
        AlreadyExistsDEException.class, () -> silageServiceImpl.save(silageDto));
  }

  @Test
  void whenAllDataIsFilledProperlyItIsUpdated() {
    String localId = UUID.randomUUID().toString();
    SilageDto silageDto =
        SilageDto.builder()
            .id(UUID.randomUUID())
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .localId(localId)
            .silages(
                List.of(
                    SaveSilageDto.builder()
                        .silageName("Test")
                        .id(UUID.randomUUID())
                        .deleted(false)
                        .build()))
            .build();
    when(silagesRepository.findBySilageId(any())).thenReturn(loadSilages());
    when(silagesRepository.saveAll(any())).thenReturn(List.of(loadSilages()));
    List<SilageDto> result = silageServiceImpl.update(silageDto);
    assertEquals(1, result.size());
    assertNotNull(result);
  }

  @Test
  void whenSilageIsNotFoundItIsCreated() {
    SilageDto silageDto =
        SilageDto.builder()
            .id(UUID.randomUUID())
            .accountId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .silages(
                List.of(
                    SaveSilageDto.builder()
                        .silageName("Test")
                        .id(UUID.randomUUID())
                        .deleted(false)
                        .build()))
            .build();
    when(silagesRepository.findBySilageId(any())).thenReturn(null);
    when(silagesRepository.saveAll(any())).thenReturn(List.of(loadSilages()));
    List<SilageDto> result = silageServiceImpl.update(silageDto);
    assertEquals(1, result.size());
    assertNotNull(result);
  }

  private Silages loadSilages() {
    SilageDocument silageDocument =
        SilageDocument.builder()
            .accountId(UUID.randomUUID())
            .silageName("Test silage")
            .siteId(UUID.randomUUID())
            .build();

    return Silages.builder()
        .silageDocument(silageDocument)
        .localId(UUID.randomUUID().toString())
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }
}
