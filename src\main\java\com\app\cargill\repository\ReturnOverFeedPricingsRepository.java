/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.ReturnOverFeedPricings;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ReturnOverFeedPricingsRepository
    extends JpaRepository<ReturnOverFeedPricings, Long> {

  @Query(
      value = "Select * FROM return_over_feed_pricings where deleted = false",
      nativeQuery = true)
  List<ReturnOverFeedPricings> findAll();

  @Query(
      value =
          "Select * FROM return_over_feed_pricings where"
              + " pricing_return_over_feed_document->>'ReturnOverFeedType' = :type",
      nativeQuery = true)
  List<ReturnOverFeedPricings> findAllByType(@Param("type") String returnOverFeedType);

  @Query(
      value =
          "Select * FROM return_over_feed_pricings where pricing_return_over_feed_document->>'id' ="
              + " :id",
      nativeQuery = true)
  ReturnOverFeedPricings findByUUID(@Param("id") String id);
}
