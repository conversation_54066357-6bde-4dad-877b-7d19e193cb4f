/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.NotificationDto;
import com.app.cargill.dto.ReadNotificationDto;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;

public interface INotificationService {

  Page<NotificationDto> getAllNotificationsPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime);

  NotificationDto save(NotificationDto notificationDto);

  NotificationDto update(NotificationDto notificationDto);

  ReadNotificationDto markAsRead(ReadNotificationDto notifications);

  List<String> getFilteredNotificationIds();
}
