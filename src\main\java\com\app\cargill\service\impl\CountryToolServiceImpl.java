/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static com.app.cargill.utils.PageableUtil.getPageable;

import com.app.cargill.constants.Business;
import com.app.cargill.document.CountryToolDocument;
import com.app.cargill.dto.CountryToolDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.CountryTools;
import com.app.cargill.repository.CountryToolsRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.ICountryToolService;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CountryToolServiceImpl implements ICountryToolService {

  private final IUserService userServiceImpl;
  private final CountryToolsRepository countryToolRepository;
  private final UserRepository usersRepository;

  @Override
  public Page<CountryToolDto> getAllCountryToolsByCurrentLoggedInUserPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);
    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();
    String countryId = usersRepository.findCountryIdByUserName(currentLoggedInUser);
    if (Objects.isNull(countryId)) {
      throw new NotFoundDEException("User Not found");
    }
    Page<CountryTools> countryTools =
        countryToolRepository.findByCountryId(countryId, pageable, lastSyncTime);
    if (countryTools.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        countryTools.stream().parallel().map(this::modelToDto).toList(),
        pageable,
        countryTools.getTotalElements());
  }

  private CountryToolDto modelToDto(CountryTools countryTool) {

    return CountryToolDto.builder()
        .createdDate(countryTool.getCreatedDate().toInstant())
        .id(countryTool.getCountryToolDocument().getId())
        .countryId(countryTool.getCountryToolDocument().getCountryId())
        .deleted(countryTool.getCountryToolDocument().isDeleted())
        .toolGroupId(countryTool.getCountryToolDocument().getToolGroupId())
        .createUser(countryTool.getCountryToolDocument().getCreateUser())
        .toolId(countryTool.getCountryToolDocument().getToolId())
        .updatedDate(countryTool.getUpdatedDate().toInstant())
        .currentTimeStamp(Instant.now())
        .build();
  }

  @Override
  public List<CountryToolDto> save(List<CountryToolDto> countryToolDtoList) {
    List<CountryTools> countryToolsList = new ArrayList<>();
    for (CountryToolDto countryToolDto : countryToolDtoList) {
      countryToolsList.add(dtoToModel(countryToolDto));
    }

    return countryToolRepository.saveAll(countryToolsList).stream()
        .parallel()
        .map(this::modelToDto)
        .toList();
  }

  private CountryTools dtoToModel(CountryToolDto countryToolDto) {
    CountryToolDocument countryToolDocument =
        CountryToolDocument.builder()
            .countryId(countryToolDto.getCountryId())
            .createTimeUtc(
                countryToolDto.getCreatedDate() != null
                    ? countryToolDto.getCreatedDate()
                    : Instant.now())
            .createUser(
                countryToolDto.getCreateUser() != null
                    ? countryToolDto.getCreateUser()
                    : userServiceImpl.getCurrentLoggedInUser())
            .id(countryToolDto.getId() != null ? countryToolDto.getId() : UUID.randomUUID())
            .isDeleted(countryToolDto.isDeleted())
            .isNew(false)
            .lastModifiedTimeUtc(Instant.now())
            .lastModifyUser(userServiceImpl.getCurrentLoggedInUser())
            .lastSyncTimeUtc(Instant.now())
            .toolGroupId(countryToolDto.getToolGroupId())
            .toolId(countryToolDto.getToolId())
            .build();
    return CountryTools.builder()
        .countryToolDocument(countryToolDocument)
        .deleted(countryToolDto.isDeleted())
        .build();
  }

  @Override
  public CountryToolDto deleteById(UUID id) {
    CountryTools countryTool = countryToolRepository.findById(id.toString());
    countryTool.setDeleted(true);
    countryTool.getCountryToolDocument().setDeleted(true);
    return modelToDto(countryToolRepository.save(countryTool));
  }

  @Override
  public List<CountryToolDto> deleteByCountryId(Business countryId) {
    List<CountryTools> countryToolsList =
        countryToolRepository.findByCountryId(countryId.toString());
    for (CountryTools countryTool : countryToolsList) {
      countryTool.setDeleted(true);
      countryTool.getCountryToolDocument().setDeleted(true);
    }
    return countryToolRepository.saveAll(countryToolsList).stream().map(this::modelToDto).toList();
  }
}
