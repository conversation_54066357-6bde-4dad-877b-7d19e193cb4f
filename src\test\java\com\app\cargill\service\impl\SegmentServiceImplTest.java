/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.document.SegmentDocument;
import com.app.cargill.dto.SegmentsDto;
import com.app.cargill.model.Segments;
import com.app.cargill.repository.SegmentsRepository;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SegmentServiceImplTest {

  @Mock private SegmentsRepository segmentsRepository;
  @InjectMocks private SegmentServiceImpl segmentService;

  @Test
  void whenSegmentsAreReceivedCorrectResultIsReturned() {

    when(segmentsRepository.findAll()).thenReturn(List.of(loadSegments()));

    List<SegmentsDto<Integer, String, Boolean>> result = segmentService.getAllSegments();

    assertNotNull(result);
    assertEquals(1, result.size());
  }

  private Segments loadSegments() {

    SegmentDocument segmentDocument =
        SegmentDocument.builder().defaultValue(false).key(1).value("Sonya").build();

    return Segments.builder().segmentDocument(segmentDocument).build();
  }
}
