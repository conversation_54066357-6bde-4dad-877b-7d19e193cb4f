/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.admin;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.admin.User;
import com.app.cargill.dto.admin.Visit;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UserDetailsAdminServiceTest {

  @Mock private UserAdminService userAdminService;
  @Mock private VisitAdminService visitAdminService;

  @InjectMocks private UserDetailsAdminService userDetailsAdminService;

  @Test
  void getUserDetailsPasses() {
    when(userAdminService.getUser(any())).thenReturn(new User());
    when(visitAdminService.getUserFirstVisit(any())).thenReturn(new Visit());

    assertNotNull(userDetailsAdminService.getUserDetails(UUID.randomUUID()));
  }
}
