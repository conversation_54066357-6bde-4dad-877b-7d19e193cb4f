/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.poi.xddf.usermodel.PresetColor;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SingleYAxisGraphDto extends BaseDto {
  private String sheetName;
  private String yleftLabel;
  @Builder.Default private PresetColor yleftLineColor = PresetColor.GREEN;
  @JsonIgnore @Builder.Default private String yleftLineColorHex = "#00FF00";
  @Builder.Default private List<XAndYAxisValueDto> yleftDataPoints = new ArrayList<>();
}
