/* Cargill Inc.(C) 2022 */
package com.app.cargill.filterspecification;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.SearchKey;
import com.app.cargill.constants.SearchOperation;
import com.app.cargill.model.Notes;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.impl.NotesServiceImpl;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaBuilder.In;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class NotesFilterSpecificationsTest {

  @Mock private Root<Notes> root;
  @Mock private CriteriaQuery<?> criteriaQuery;
  @Mock private CriteriaBuilder criteriaBuilder;
  @Mock private In<Object> expression;
  @Mock private Predicate predicate;
  @Mock VisitsRepository visitsRepository;
  @Mock NotesServiceImpl notesServiceImpl;
  @InjectMocks private NotesFilterSpecification filterSpecification;
  @Mock NotesRepository notesRepository;

  @Test
  void WhenSpecificationsListIsNull() {

    List<SearchCriteria> search =
        List.of(
            SearchCriteria.builder()
                .key(SearchKey.ACCOUNT_ID)
                .operation(SearchOperation.IN)
                .value(UUID.randomUUID().toString())
                .build(),
            SearchCriteria.builder()
                .key(SearchKey.UPDATED_DATE)
                .operation(SearchOperation.BETWEEN)
                .value(Instant.now().toString().concat(",").concat(Instant.now().toString()))
                .build(),
            SearchCriteria.builder()
                .key(SearchKey.TITLE)
                .operation(SearchOperation.LIKE)
                .value("test")
                .build(),
            SearchCriteria.builder()
                .key(SearchKey.SECTION)
                .operation(SearchOperation.NOT_NULL)
                .value(null)
                .build());
    filterSpecification = new NotesFilterSpecification(search);
    when(criteriaBuilder.in(
            criteriaBuilder.function(
                "jsonb_extract_path_text",
                Object.class,
                root.<Instant>get("notesDocument"),
                criteriaBuilder.literal("title"))))
        .thenReturn(expression);
    lenient().when(criteriaBuilder.and(any(Predicate.class))).thenReturn(predicate);
    Predicate actualPredicate =
        filterSpecification.toPredicate(root, criteriaQuery, criteriaBuilder);
    assertEquals(null, actualPredicate);
  }
}
