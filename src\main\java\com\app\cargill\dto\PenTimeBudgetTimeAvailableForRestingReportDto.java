/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PenTimeBudgetTimeAvailableForRestingReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String label;
  private String categoryLabel;

  @Builder.Default private List<XAndYAxisValueDto> timeRequired = new ArrayList<>();

  @Builder.Default private List<XAndYAxisValueDto> timeRemaining = new ArrayList<>();

  @JsonIgnore private String[] visitDates;
}
