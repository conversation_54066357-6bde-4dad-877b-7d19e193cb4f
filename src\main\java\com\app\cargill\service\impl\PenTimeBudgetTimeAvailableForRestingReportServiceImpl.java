/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.PenTimeBudgetTimeAvailableForRestingReportDto;
import com.app.cargill.dto.XAndYAxisValueDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.XDDFColor;
import org.apache.poi.xddf.usermodel.XDDFShapeProperties;
import org.apache.poi.xddf.usermodel.XDDFSolidFillProperties;
import org.apache.poi.xddf.usermodel.chart.AxisCrosses;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFLineChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTLineChart;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTLineSer;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTMarker;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTMarkerStyle;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("penTimeBudgetTimeAvailableForRestingReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"})
public class PenTimeBudgetTimeAvailableForRestingReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public PenTimeBudgetTimeAvailableForRestingReportDto prepareData(Object data) {

    PenTimeBudgetTimeAvailableForRestingReportDto penTimeBudgetTimeAvailableForRestingMappedDto =
        modelMapper.map(data, PenTimeBudgetTimeAvailableForRestingReportDto.class);
    penTimeBudgetTimeAvailableForRestingMappedDto.setVisitDates(
        penTimeBudgetTimeAvailableForRestingMappedDto.getTimeRequired().stream()
            .map(XAndYAxisValueDto::getX)
            .toArray(String[]::new));
    return penTimeBudgetTimeAvailableForRestingMappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    PenTimeBudgetTimeAvailableForRestingReportDto penTimeBudgetTimeAvailableForRestingMappedDto =
        modelMapper.map(dto, PenTimeBudgetTimeAvailableForRestingReportDto.class);

    penTimeBudgetTimeAvailableForRestingMappedDto =
        prepareData(penTimeBudgetTimeAvailableForRestingMappedDto);
    byte[] report =
        freeMarkerComponent.render(
            penTimeBudgetTimeAvailableForRestingMappedDto,
            ReportsToBeanMappings.PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_REPORT
                .getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    PenTimeBudgetTimeAvailableForRestingReportDto penTimeBudgetTimeAvailableForRestingMappedDto =
        modelMapper.map(data, PenTimeBudgetTimeAvailableForRestingReportDto.class);
    return StringUtils.isBlank(penTimeBudgetTimeAvailableForRestingMappedDto.getFileName())
        ? ReportsToBeanMappings.PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_REPORT.getFileName()
        : penTimeBudgetTimeAvailableForRestingMappedDto.getFileName();
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    PenTimeBudgetTimeAvailableForRestingReportDto penTimeBudgetTimeAvailableForRestingMappedDto =
        modelMapper.map(data, PenTimeBudgetTimeAvailableForRestingReportDto.class);
    penTimeBudgetTimeAvailableForRestingMappedDto =
        prepareData(penTimeBudgetTimeAvailableForRestingMappedDto);
    try (XSSFWorkbook penTimeBudgetTimeAvailableForRestingWB = new XSSFWorkbook()) {
      // create rumenFillHerdAnalysisWBSheet
      XSSFSheet penTimeBudgetTimeAvailableForRestingWBSheet =
          penTimeBudgetTimeAvailableForRestingWB.createSheet(
              ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              penTimeBudgetTimeAvailableForRestingWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(
                  penTimeBudgetTimeAvailableForRestingWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              penTimeBudgetTimeAvailableForRestingWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  penTimeBudgetTimeAvailableForRestingWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              penTimeBudgetTimeAvailableForRestingWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  penTimeBudgetTimeAvailableForRestingWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(
              penTimeBudgetTimeAvailableForRestingWB,
              false,
              IndexedColors.BLACK,
              HorizontalAlignment.CENTER);

      prepareHeader(
          penTimeBudgetTimeAvailableForRestingWB,
          penTimeBudgetTimeAvailableForRestingWBSheet,
          rowNumber,
          cellNumber,
          penTimeBudgetTimeAvailableForRestingMappedDto,
          boldStyle,
          locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = penTimeBudgetTimeAvailableForRestingWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      penTimeBudgetTimeAvailableForRestingWBSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // Lactation Stages
      cellNumber.set(0);

      int visitDateStartRowNumber = rowNumber.get();

      row = penTimeBudgetTimeAvailableForRestingWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      for (String visitDate : penTimeBudgetTimeAvailableForRestingMappedDto.getVisitDates()) {
        ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, visitDate);
      }

      cellNumber.set(0);

      int timeRequiredRowNumber = rowNumber.get();
      row = penTimeBudgetTimeAvailableForRestingWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_PEN_TIME_BUDGET_TIME_REQUIRED, null, source, locale));

      for (XAndYAxisValueDto timeRequired :
          penTimeBudgetTimeAvailableForRestingMappedDto.getTimeRequired()) {
        ExcelUtils.highlightEmptyCell(
            row, timeRequired.getY(), cellNumber, decimalStyle, greyCellStyle);
      }

      //  Min
      cellNumber.set(0);
      int timeRemainingNumber = rowNumber.get();
      row = penTimeBudgetTimeAvailableForRestingWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_PEN_TIME_BUDGET_TIME_REMAINING, null, source, locale));
      for (XAndYAxisValueDto timeRemaining :
          penTimeBudgetTimeAvailableForRestingMappedDto.getTimeRemaining()) {
        ExcelUtils.highlightEmptyCell(
            row, timeRemaining.getY(), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      int columnStart = 1;
      int columnEnd =
          columnStart + penTimeBudgetTimeAvailableForRestingMappedDto.getVisitDates().length - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

      XDDFDataSource<String> visitDate =
          XDDFDataSourcesFactory.fromStringCellRange(
              penTimeBudgetTimeAvailableForRestingWBSheet,
              new CellRangeAddress(
                  visitDateStartRowNumber, visitDateStartRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> timeRequiredDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              penTimeBudgetTimeAvailableForRestingWBSheet,
              new CellRangeAddress(
                  timeRequiredRowNumber, timeRequiredRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> timeRemainingDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              penTimeBudgetTimeAvailableForRestingWBSheet,
              new CellRangeAddress(
                  timeRemainingNumber, timeRemainingNumber, columnStart, columnEnd));

      // needed objects for the charts
      XSSFChart chart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 1;
      // ======first line chart==========
      chart =
          ExcelUtils.initChart(
              penTimeBudgetTimeAvailableForRestingWBSheet,
              ExcelUtils.getLangValue(
                  LangKeys.PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_CATEGORY_LABEL,
                  null,
                  source,
                  locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart, ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart,
              ExcelUtils.getLangValue(
                  LangKeys.PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_LABEL, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series = (XDDFLineChartData.Series) dataLeft.addSeries(visitDate, timeRemainingDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_PEN_TIME_BUDGET_TIME_REMAINING, null, source, locale),
          new CellReference(
              penTimeBudgetTimeAvailableForRestingWBSheet.getSheetName(),
              timeRemainingNumber,
              0,
              true,
              true));
      series.setSmooth(true);
      series = (XDDFLineChartData.Series) dataLeft.addSeries(visitDate, timeRequiredDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_PEN_TIME_BUDGET_TIME_REQUIRED, null, source, locale),
          new CellReference(
              penTimeBudgetTimeAvailableForRestingWBSheet.getSheetName(),
              timeRequiredRowNumber,
              0,
              true,
              true));
      series.setSmooth(true);
      // Create first marker style with red color
      XDDFShapeProperties propertiesMarkerRed = new XDDFShapeProperties();
      propertiesMarkerRed.setFillProperties(
          new XDDFSolidFillProperties(XDDFColor.from(new byte[] {(byte) 0xFF, 0x00, 0x00})));
      CTMarker ctMarkerRed = CTMarker.Factory.newInstance();
      ctMarkerRed.setSymbol(CTMarkerStyle.Factory.newInstance());
      ctMarkerRed.addNewSpPr().set(propertiesMarkerRed.getXmlObject());

      // Create the second marker style with dark blue color
      XDDFShapeProperties propertiesMarkerBlue = new XDDFShapeProperties();
      propertiesMarkerBlue.setFillProperties(
          new XDDFSolidFillProperties(XDDFColor.from(new byte[] {0x00, 0x00, (byte) 0xFF})));
      CTMarker ctMarkerBlue = CTMarker.Factory.newInstance();
      ctMarkerBlue.setSymbol(CTMarkerStyle.Factory.newInstance());
      ctMarkerBlue.addNewSpPr().set(propertiesMarkerBlue.getXmlObject());

      CTPlotArea plotArea = chart.getCTChart().getPlotArea();

      int seriesIndex = 0;
      for (CTLineChart ser : plotArea.getLineChartArray()) {
        for (CTLineSer ct : ser.getSerArray()) {
          // Assign different marker styles to different series
          CTMarker ctMarker = (seriesIndex == 0) ? ctMarkerRed : ctMarkerBlue;
          ct.setMarker(ctMarker);
          seriesIndex++;
        }
      }

      chart.plot(dataLeft);
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.RED, false);
      ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.BLUE, false);

      return ExcelUtils.finalizeWorkbook(
          penTimeBudgetTimeAvailableForRestingWB,
          penTimeBudgetTimeAvailableForRestingWBSheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  void prepareHeader(
      XSSFWorkbook penTimeBudgetTimeAvailableForRestingWB,
      XSSFSheet penTimeBudgetTimeAvailableForRestingSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      PenTimeBudgetTimeAvailableForRestingReportDto penTimeBudgetTimeAvailableForRestingDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        penTimeBudgetTimeAvailableForRestingWB,
        penTimeBudgetTimeAvailableForRestingSheet,
        rowNumber.get(),
        cellNumber.getAndIncrement());
    // headings
    XSSFRow row = penTimeBudgetTimeAvailableForRestingSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, penTimeBudgetTimeAvailableForRestingDto.getVisitName());
    penTimeBudgetTimeAvailableForRestingSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, penTimeBudgetTimeAvailableForRestingDto.getVisitDate());
    penTimeBudgetTimeAvailableForRestingSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = penTimeBudgetTimeAvailableForRestingSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, penTimeBudgetTimeAvailableForRestingDto.getToolName());
    penTimeBudgetTimeAvailableForRestingSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
  }
}
