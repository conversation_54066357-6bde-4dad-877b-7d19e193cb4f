/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Cities;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CitiesRepository extends JpaRepository<Cities, Long> {

  @Query(
      value =
          "Select c.* FROM cities c where c.city_document->> 'state_code' = ?1 AND"
              + " c.city_document->>'country_code' = ?2 AND c.deleted=false",
      nativeQuery = true)
  List<Cities> findByStateCodeAndCountryCode(String stateCode, String countryCode);

  @Query(
      value = "Select c.* FROM cities c where c.updated_date > :lastSyncTime AND c.deleted=false",
      nativeQuery = true)
  Page<Cities> findAllByUpdatedDate(@Param("lastSyncTime") Instant lastSyncTime, Pageable pageable);

  @Query(
      value =
          "Select c.* FROM cities c where c.city_document->> 'state_code' = :stateCode AND"
              + " c.city_document->>'country_code' = :countryCode AND c.updated_date >"
              + " :lastSyncTime AND c.deleted=false",
      nativeQuery = true)
  Page<Cities> findByStateCodeAndCountryCode(
      @Param("stateCode") String stateCode,
      @Param("countryCode") String countryCode,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);
}
