/* Cargill Inc.(C) 2022 */
package com.app.cargill.model.tasks;

import java.io.Serial;
import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@ToString
public class SyncResult implements Serializable {

  public SyncResult(String name) {
    this.name = name;
  }

  @Serial private static final long serialVersionUID = 1L;
  private String name;
  private AtomicInteger newRecords = new AtomicInteger(0);

  private AtomicInteger modifiedRecords = new AtomicInteger(0);

  private AtomicInteger failures = new AtomicInteger(0);

  private String duration;

  private String error;

  public int incrementModified() {
    return modifiedRecords.incrementAndGet();
  }

  public int incrementFailures() {
    return failures.incrementAndGet();
  }
}
