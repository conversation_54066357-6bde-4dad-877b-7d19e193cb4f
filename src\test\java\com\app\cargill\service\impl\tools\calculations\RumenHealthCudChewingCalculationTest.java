/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.*;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RumenHealthCudChewingCalculationTest {

  @InjectMocks private RumenHealthCudChewingCalculation rumenHealthCudChewingCalculation;

  @Test
  void calculateFields() {
    RumenHealthTool rumenHealthTool =
        RumenHealthTool.builder()
            .pens(
                List.of(
                    RumenHealthToolItem.builder()
                        .cudChewsCount(
                            List.of(CudChewingCount.builder().cowNumber(5).chewsCount(1).build()))
                        .cudChewingCowsCount(
                            CudChewingCowCount.builder()
                                .countYes(10)
                                .countNo(4)
                                .totalCount(14.0)
                                .build())
                        .daysInMilk(201)
                        .stage(LactationStage.Fresh)
                        .build()))
            .goals(List.of(HerdAnalysisGoal.builder().cudChews(11.0).build()))
            .build();

    rumenHealthTool = rumenHealthCudChewingCalculation.calculateFields(rumenHealthTool);
    assertTrue(
        rumenHealthTool.getPens().stream()
            .allMatch(
                rumenHealthToolItem ->
                    rumenHealthToolItem.getPercentChewing() != null
                        && rumenHealthToolItem.getAverageChewsPerCud() != null
                        && rumenHealthToolItem.getStandardDeviationOfChewsPerCud() != null));

    assertTrue(
        rumenHealthTool.getGoals().stream()
            .allMatch(herdAnalysisGoal -> herdAnalysisGoal.getToolStatus() != null));
  }

  @Test
  void calculateFieldsWithNullValues() {
    RumenHealthTool rumenHealthTool =
        RumenHealthTool.builder()
            .pens(
                List.of(
                    RumenHealthToolItem.builder()
                        .cudChewsCount(List.of(CudChewingCount.builder().build()))
                        .cudChewingCowsCount(CudChewingCowCount.builder().build())
                        .build()))
            // .goals(List.of(HerdAnalysisGoal.builder().cudChews(11.0).build()))
            .build();

    rumenHealthTool = rumenHealthCudChewingCalculation.calculateFields(rumenHealthTool);
    assertTrue(
        rumenHealthTool.getPens().stream()
            .allMatch(
                rumenHealthToolItem ->
                    rumenHealthToolItem.getPercentChewing() == 0.0
                        && rumenHealthToolItem.getAverageChewsPerCud() == 0.0
                        && rumenHealthToolItem.getStandardDeviationOfChewsPerCud() == 0.0));
  }

  @Test
  void verifyCalculations() {
    RumenHealthTool rumenHealthTool =
        RumenHealthTool.builder()
            .pens(
                List.of(
                    RumenHealthToolItem.builder()
                        .cudChewsCount(
                            List.of(
                                CudChewingCount.builder().cowNumber(1).chewsCount(62).build(),
                                CudChewingCount.builder().cowNumber(2).chewsCount(64).build(),
                                CudChewingCount.builder().cowNumber(3).chewsCount(58).build(),
                                CudChewingCount.builder().cowNumber(4).chewsCount(56).build(),
                                CudChewingCount.builder().cowNumber(5).chewsCount(63).build(),
                                CudChewingCount.builder().cowNumber(6).chewsCount(65).build()))
                        .cudChewingCowsCount(
                            CudChewingCowCount.builder()
                                .countYes(6)
                                .countNo(6)
                                .totalCount(12.0)
                                .yesPercent(50.0)
                                .noPercent(50.0)
                                .build())
                        .daysInMilk(0)
                        .build(),
                    RumenHealthToolItem.builder()
                        .cudChewsCount(
                            List.of(
                                CudChewingCount.builder().cowNumber(1).chewsCount(66).build(),
                                CudChewingCount.builder().cowNumber(2).chewsCount(66).build(),
                                CudChewingCount.builder().cowNumber(3).chewsCount(60).build(),
                                CudChewingCount.builder().cowNumber(4).chewsCount(56).build(),
                                CudChewingCount.builder().cowNumber(5).chewsCount(59).build()))
                        .cudChewingCowsCount(
                            CudChewingCowCount.builder()
                                .countYes(4)
                                .countNo(8)
                                .totalCount(12.0)
                                .yesPercent(33.3)
                                .noPercent(66.7)
                                .build())
                        .daysInMilk(0)
                        .build(),
                    RumenHealthToolItem.builder()
                        .cudChewsCount(
                            List.of(
                                CudChewingCount.builder().cowNumber(1).chewsCount(68).build(),
                                CudChewingCount.builder().cowNumber(2).chewsCount(67).build(),
                                CudChewingCount.builder().cowNumber(3).chewsCount(60).build()))
                        .cudChewingCowsCount(
                            CudChewingCowCount.builder()
                                .countYes(2)
                                .countNo(3)
                                .totalCount(5.0)
                                .yesPercent(0.0)
                                .noPercent(0.0)
                                .build())
                        .daysInMilk(0)
                        .build()))
            .goals(
                List.of(
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.FarOffDry)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build(),
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.CloseUpDry)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build(),
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.Fresh)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build(),
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.EarlyLactation)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build(),
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.PeakMilk)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build(),
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.MidLactation)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build(),
                    HerdAnalysisGoal.builder()
                        .stage(LactationStage.LateLactation)
                        .percentChewing(60.0)
                        .cudChews(65.0)
                        .build()))
            .build();

    rumenHealthTool = rumenHealthCudChewingCalculation.calculateFields(rumenHealthTool);

    assertEquals(50.0, rumenHealthTool.getPens().get(0).getPercentChewing());
    assertEquals(61.0, rumenHealthTool.getPens().get(0).getAverageChewsPerCud());
    assertEquals(
        3.265986323710904, rumenHealthTool.getPens().get(0).getStandardDeviationOfChewsPerCud());
    assertEquals(
        LactationStage.Fresh.name(), rumenHealthTool.getPens().get(0).getStage().toString());

    assertEquals(33.3, rumenHealthTool.getPens().get(1).getPercentChewing());
    assertEquals(61.0, rumenHealthTool.getPens().get(1).getAverageChewsPerCud());
    assertEquals(4.0, rumenHealthTool.getPens().get(1).getStandardDeviationOfChewsPerCud());
    assertEquals(
        LactationStage.Fresh.name(), rumenHealthTool.getPens().get(1).getStage().toString());

    assertEquals(0.0, rumenHealthTool.getPens().get(2).getPercentChewing());
    assertEquals(65.0, rumenHealthTool.getPens().get(2).getAverageChewsPerCud());
    assertEquals(
        3.559026084010437, rumenHealthTool.getPens().get(2).getStandardDeviationOfChewsPerCud());
    assertEquals(
        LactationStage.Fresh.name(), rumenHealthTool.getPens().get(2).getStage().toString());

    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(0).getToolStatus());
    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(1).getToolStatus());
    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(2).getToolStatus());
    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(3).getToolStatus());
    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(4).getToolStatus());
    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(5).getToolStatus());
    assertEquals(ToolStatuses.Completed, rumenHealthTool.getGoals().get(6).getToolStatus());
  }
}
