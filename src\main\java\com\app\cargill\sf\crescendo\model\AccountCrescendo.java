/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.app.cargill.document.Address;
import com.app.cargill.document.DateEpoch;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountCrescendo implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("GoldenRecordId")
  private String goldenRecordId;

  @JsonProperty("AccountName")
  private String accountName;

  @JsonProperty("LegalName")
  private String legalName;

  @JsonProperty("AccountType")
  private AccountTypeCrescendo accountType;

  @JsonProperty("Contacts")
  private List<ContactCrescendo> contacts = new ArrayList<>();

  @JsonProperty("Users")
  private Set<String> users;

  @JsonProperty("ProspectValidated")
  private Boolean prospectValidated = false;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("ApplicationMapping")
  private List<ApplicationMappingCrescendo> applicationMapping = new ArrayList<>();

  @JsonProperty("IsDuplicate")
  private Boolean isDuplicate = false;

  @JsonProperty("AutoValidate")
  private Boolean autoValidate = false;

  @JsonProperty("SourceSystem")
  private String sourceSystem;

  @JsonProperty("Type")
  private String type;

  @JsonProperty("ParentAccountID")
  private String parentAccountID;

  @JsonProperty("ExternalParentAccountID")
  private String externalParentAccountId;

  @JsonProperty("BuyingGroupID")
  private String buyingGroupId;

  @JsonProperty("ExternalBuyingGroupID")
  private String externalBuyingGroupId;

  @JsonProperty("SubTypeID")
  private SubTypeIdCrescendo subTypeId;

  @JsonProperty("ExternalLeadSourceID")
  private LeadSourceCrescendo externalLeadSourceId;

  @JsonProperty("AccountStatus")
  private AccountStatusCrescendo accountStatus;

  @JsonProperty("DateOfLastVisit")
  private String dateOfLastVisit;

  @JsonProperty("DateOfLastCall")
  private String dateOfLastCall;

  @JsonProperty("BusinessID")
  private BusinessUnitCrescendo businessId;

  @JsonProperty("LastModificationDate")
  private DateEpoch lastModificationDate;

  @JsonProperty("Active")
  private Boolean active = true;

  @JsonProperty("NineBoxStepTwoID")
  private NineBoxStepTwoIDCrescendo nineBoxStepTwoID;

  @JsonProperty("SegmentStepOneId")
  private SegmentStepOneIdCrescendo segmentStepOneId;

  @JsonProperty("CompanyEmail")
  private String companyEmail;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("ApprovalStatus")
  private AccountStatusCrescendo approvalStatus;

  @JsonProperty("ApplicationMappingLastModifiedDate")
  private DateEpoch applicationMappingLastModifiedDate;

  @JsonProperty("PhysicalAddress")
  private AddressCrescendo physicalAddress;

  @JsonProperty("CorrespondenceAddress")
  private Address correspondenceAddress;

  @JsonProperty("AdditionalInfo")
  private AdditionalInformationCrescendo additionalInfo;

  @JsonProperty("AccountCurrency")
  private CurrencyCrescendo accountCurrency;

  @JsonProperty("CANBusiness")
  private CANBusinessIDCrescendo canBusiness;

  @JsonProperty("CurrentUserProfileNameandId")
  private String currentUserProfileNameAndId;

  @JsonProperty("ExternalId")
  private String externalId;

  @JsonProperty("LastModifiedBy")
  private String lastModifiedBy;

  @JsonProperty("OwnerProfileNameandId")
  private String ownerProfileNameAndId;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("CustomerCode")
  private String customerCode;

  @JsonProperty("UserRoles")
  private List<UserRoleCrescendo> userRoles = new ArrayList<>();

  @JsonProperty("id")
  private String id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private boolean isNew = false;

  @JsonProperty("IsMobileFirst")
  private Boolean isMobileFirst;

  @JsonProperty("SocialMediaAddress")
  private String socialMediaAddress;

  @JsonProperty("WebSiteAddress")
  private String webSiteAddress;

  @JsonProperty("WonLostId")
  private String wonLostId;

  @JsonProperty("WonLostComments")
  private String wonLostComments;

  @JsonProperty("CreditFlag")
  private String creditFlag;

  @JsonProperty("PriceFlag")
  private String priceFlag;

  @JsonProperty("ServiceFlag")
  private String serviceFlag;

  @JsonProperty("PerformanceFlag")
  private String performanceFlag;

  @JsonProperty("PortfolioFlag")
  private String portfolioFlag;

  @JsonProperty("BusinessSolutionFlag")
  private String businessSolutionFlag;

  @JsonProperty("QualityFlag")
  private String qualityFlag;

  @JsonProperty("OtherFlag")
  private String otherFlag;

  @JsonProperty("DefaultCargillPlantID")
  private String defaultCargillPlantID;

  @JsonProperty("DefaultCustServiceID")
  private String defaultCustServiceID;

  @JsonProperty("BrandId")
  private String brandId;

  @JsonProperty("LastInvoiceDate")
  private String lastInvoiceDate;

  @JsonProperty("LastOrderDate")
  private String lastOrderDate;

  @JsonProperty("DeliveryInstructions")
  private String deliveryInstructions;

  @JsonProperty("ERPPayerId")
  private String erpPayerId;

  @JsonProperty("ERPShipToId")
  private String erpShipToId;

  @JsonProperty("IsServicedbyCSPro")
  private String isServicedByCSPro;

  @JsonProperty("LastAdminUpdate")
  private String lastAdminUpdate;

  @JsonProperty("LastInvoicesInfo")
  private String lastInvoicesInfo;

  @JsonProperty("LastOrdersInfo")
  private String lastOrdersInfo;

  @JsonProperty("Liabilities")
  private String liabilities;

  @JsonProperty("LimitChangeReasonId")
  private String limitChangeReasonId;

  @JsonProperty("MarketInfluencer")
  private String marketInfluencer;

  @JsonProperty("OtherActivityProduction")
  private String otherActivityProduction;

  @JsonProperty("PersonalID")
  private String personalID;

  @JsonProperty("PreviousStatus")
  private String previousStatus;

  @JsonProperty("ReasonDescription")
  private String reasonDescription;

  @JsonProperty("Securities")
  private String securities;

  @JsonProperty("Assets")
  private String assets;

  @JsonProperty("AccountTeamLastModifiedDate")
  private DateEpoch accountTeamLastModifiedDate;

  @JsonProperty("VolumeEstimate")
  private String volumeEstimate;

  @JsonProperty("MarginEstimate")
  private String marginEstimate;

  @JsonProperty("lstOtherBU")
  private List<String> lstOtherBU = new ArrayList<>();

  @JsonProperty("TemplstOtherBU")
  private String templstOtherBU;

  @JsonProperty("AccountNumber")
  private String accountNumber;

  @JsonProperty("AvailabilityOnMarket")
  private String availabilityOnMarket;

  @JsonProperty("ChangeAccountType")
  private boolean changeAccountType = false;

  @JsonProperty("NewAccountType")
  private String newAccountType;

  @JsonProperty("ConsumerStatus")
  private String consumerStatus;

  @JsonProperty("CourtId")
  private String courtId;

  @JsonProperty("CustomerStatus")
  private String customerStatus;

  @JsonProperty("ERPIdLength")
  private String erpIdLength;

  @JsonProperty("ERPPayerIdLength")
  private String erpPayerIdLength;

  @JsonProperty("ERPShiptoIdLength")
  private String erpShipToIdLength;

  @JsonProperty("VeterinaryId")
  private String veterinaryId;

  @JsonProperty("WonLostReasonCode")
  private String wonLostReasonCode;

  @JsonProperty("SubBrandId")
  private String subBrandId;

  @JsonProperty("SalesTerritory")
  private String salesTerritory;

  @JsonProperty("ReqProcessingLog")
  private String reqProcessingLog;
}
