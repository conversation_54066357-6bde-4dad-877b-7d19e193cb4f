/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreToolItem;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TMRParticleScoreCalculationTest {

  @InjectMocks private TMRParticleScoreCalculation tmrParticleScoreCalculation;

  @Test
  void calculateFields() {
    RumenHealthTMRParticleScoreTool rumenHealthTMRParticleScoreTool =
        RumenHealthTMRParticleScoreTool.builder()
            .tmrScores(
                List.of(
                    RumenHealthTMRParticleScoreToolItem.builder()
                        .topScaleAmountInGrams(22.1)
                        .mid1ScaleAmountInGrams(22.1)
                        .mid2ScaleAmountInGrams(22.1)
                        .trayScaleAmountInGrams(22.1)
                        .topScreenTareAmountInGrams(22.1)
                        .mid1ScreenTareAmountInGrams(22.1)
                        .mid2ScreenTareAmountInGrams(22.1)
                        .trayScreenTareAmountInGrams(22.1)
                        .build()))
            .build();

    rumenHealthTMRParticleScoreTool =
        tmrParticleScoreCalculation.calculateFields(rumenHealthTMRParticleScoreTool);

    assertTrue(
        rumenHealthTMRParticleScoreTool.getTmrScores().stream()
            .allMatch(
                rumenHealthTMRParticleScoreToolItem ->
                    rumenHealthTMRParticleScoreToolItem.getTotalScaleAmount() != null
                        && rumenHealthTMRParticleScoreToolItem.getTopScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid1ScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid2ScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getTrayScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getTopStdDevValue() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid1StdDevValue() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid2StdDevValue() != null
                        && rumenHealthTMRParticleScoreToolItem.getTrayStdDevValue() != null));

    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgTopScalePercentage());
    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgMid1ScalePercentage());
    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgMid2ScalePercentage());
    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgTrayScalePercentage());
  }

  @Test
  void calculateFieldsWithNull() {
    RumenHealthTMRParticleScoreTool rumenHealthTMRParticleScoreTool =
        RumenHealthTMRParticleScoreTool.builder()
            .tmrScores(List.of(RumenHealthTMRParticleScoreToolItem.builder().build()))
            .build();

    rumenHealthTMRParticleScoreTool =
        tmrParticleScoreCalculation.calculateFields(rumenHealthTMRParticleScoreTool);

    assertTrue(
        rumenHealthTMRParticleScoreTool.getTmrScores().stream()
            .allMatch(
                rumenHealthTMRParticleScoreToolItem ->
                    rumenHealthTMRParticleScoreToolItem.getTotalScaleAmount() != null
                        && rumenHealthTMRParticleScoreToolItem.getTopScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid1ScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid2ScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getTrayScalePercentage() != null
                        && rumenHealthTMRParticleScoreToolItem.getTopStdDevValue() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid1StdDevValue() != null
                        && rumenHealthTMRParticleScoreToolItem.getMid2StdDevValue() != null
                        && rumenHealthTMRParticleScoreToolItem.getTrayStdDevValue() != null));

    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgTopScalePercentage());
    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgMid1ScalePercentage());
    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgMid2ScalePercentage());
    assertNotNull(rumenHealthTMRParticleScoreTool.getAvgTrayScalePercentage());
  }

  @Test
  void verifyCalculations() {

    RumenHealthTMRParticleScoreTool rumenHealthTMRParticleScoreTool =
        RumenHealthTMRParticleScoreTool.builder()
            .tmrScores(
                List.of(
                    RumenHealthTMRParticleScoreToolItem.builder()
                        .topScaleAmountInGrams(90.0)
                        .mid1ScaleAmountInGrams(181.0)
                        .mid2ScaleAmountInGrams(65.0)
                        .trayScaleAmountInGrams(127.0)
                        .toolStatus(ToolStatuses.Completed)
                        .visitsSelected(
                            List.of(
                                (UUID.fromString("643b7e98-a0a4-415c-88d2-fa86c9f0ad47")),
                                (UUID.fromString("0fbcb712-5555-42f6-a672-4e260a310438")),
                                (UUID.fromString("4b8a5bc4-0de6-4050-9716-12bb40b1758b"))))
                        .build()))
            .build();

    rumenHealthTMRParticleScoreTool =
        tmrParticleScoreCalculation.calculateFields(rumenHealthTMRParticleScoreTool);

    assertEquals(
        463.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getTotalScaleAmount());

    assertEquals(
        19.4, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getTopScalePercentage());

    assertEquals(
        39.1, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getMid1ScalePercentage());

    assertEquals(
        14.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getMid2ScalePercentage());

    assertEquals(
        14.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getMid2ScalePercentage());

    assertEquals(
        27.4, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getTrayScalePercentage());

    assertEquals(19.4, rumenHealthTMRParticleScoreTool.getAvgTopScalePercentage());

    assertEquals(39.1, rumenHealthTMRParticleScoreTool.getAvgMid1ScalePercentage());

    assertEquals(14.0, rumenHealthTMRParticleScoreTool.getAvgMid2ScalePercentage());

    assertEquals(27.4, rumenHealthTMRParticleScoreTool.getAvgTrayScalePercentage());

    assertEquals(0.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getTopStdDevValue());

    assertEquals(0.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getMid1StdDevValue());

    assertEquals(0.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getMid2StdDevValue());

    assertEquals(0.0, rumenHealthTMRParticleScoreTool.getTmrScores().get(0).getTrayStdDevValue());
  }
}
