/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class ProfitabilityAnalysisToolDto extends EditableDocumentBaseDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private AnimalInputDto animalInput;
  private MilkInformationDto milkInformation;
  private FeedingInformationDto feedingInformation;
  private UUID visitId;
}
