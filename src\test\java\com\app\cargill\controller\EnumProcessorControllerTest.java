/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.dto.EnumSerializerDto;
import com.app.cargill.dto.MultilingualEnumSerializerDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.IEnumProcessorService;
import com.app.cargill.service.IUserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class EnumProcessorControllerTest {

  @Mock private IEnumProcessorService enumProcessorServiceImpl;

  @Mock private IUserService userService;
  @InjectMocks private EnumProcessorController controller;

  @Test
  void fetchEnums() {
    ResponseEntity<ResponseEntityDto<EnumSerializerDto>> result = controller.fetchEnums();
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void fetchEnumsMultilingual() {
    ResponseEntity<ResponseEntityDto<MultilingualEnumSerializerDto>> result =
        controller.fetchEnumsMultilingual();
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }
}
