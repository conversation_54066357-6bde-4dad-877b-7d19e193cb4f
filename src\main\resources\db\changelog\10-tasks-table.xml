<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="010" author="anenkov">
        <sql>
            CREATE TABLE IF NOT EXISTS tasks
            (
                id           SERIAL PRIMARY KEY,
                task_name    varchar(100) NOT NULL,
                status       varchar(20)  NOT NULL,
                created_date TIMESTAMP    NOT NULL DEFAULT CURRENT_DATE,
                updated_date TIMESTAMP NULL
            );
        </sql>
    </changeSet>

</databaseChangeLog>