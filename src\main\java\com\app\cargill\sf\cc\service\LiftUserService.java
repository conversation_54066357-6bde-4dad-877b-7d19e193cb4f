/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.User;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiftUserService {

  private static final String USERS_QUERY =
      "SELECT+Id,Name,Email,Username,UserType,FirstName,LastName,LastLoginDate,Profile.UserLicense.Id,IsActive,Profile.UserLicense.Name+FROM+User";
  private static final String CHATTER_FREE = "Chatter Free";
  private final LiftApiService liftApi;

  private final LiftConfig liftConfig;

  public User findUserByEmail(String email) {
    String condition;
    User output;
    if (email != null && email.equals(liftConfig.getDefaultOwnerId())) {
      condition = String.format("Username='%s'", email);
    } else {
      condition = String.format("Email='%s'", email);
    }
    List<User> result = findUsers(Collections.singletonList(condition));
    if (result.isEmpty()) {
      return null;
    } else if (result.size() > 1) {
      output =
          result.stream()
              .filter(u -> liftConfig.getDefaultOwnerId().equals(u.getUsername()))
              .findAny()
              .orElseThrow(
                  () ->
                      new IllegalArgumentException(
                          "Something's wrong. Too many users were found."));

    } else {
      output = result.iterator().next();
    }
    if (output != null) {
      if (liftConfig.getDefaultOwnerId().equals(output.getUsername())) {
        output.setEmail(output.getUsername());
      }
      log.debug("FIND_USER {}", output.getEmail());
    }

    return output;
  }

  public User findOwner(String currentLoggedInUser) {
    // Fetching owner user based on the User License i.e Standard or Chatter Free
    User user = findUserByEmail(currentLoggedInUser);
    if (!Objects.isNull(user)
        && CHATTER_FREE.equalsIgnoreCase(user.getProfile().getUserLicense().getName())) {
      user = findUserByEmail(liftConfig.getDefaultOwnerId());
    }
    return user;
  }

  /**
   * This can be changed to public if there is need but from a security standpoint we better leave
   * it as private
   *
   * @param conditions A query conditions list like: "LastModifiedDate>2023-03-06T09:33:58.394993Z"
   *     "Email='<EMAIL>'"
   * @return List of users
   */
  private List<User> findUsers(List<String> conditions) {
    try {
      List<User> usersList = fetchUsersData(conditions);
      if (usersList.size() > 1) {

        usersList =
            usersList.stream()
                .filter(
                    user ->
                        (user.getUsername().contains("cargillanh")
                                || user.getUsername().contains("integration"))
                            && !user.getUsername().contains("invalid"))
                .toList();
      }
      return usersList;
    } catch (Exception e) {
      log.error("Error", e);
      return new ArrayList<>();
    }
  }

  public List<User> fetchUserEmailData(String email) {
    return fetchUsersData(Collections.singletonList(String.format("email='%s'", email)));
  }

  private List<User> fetchUsersData(List<String> conditions) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    String query =
        conditions == null
            ? USERS_QUERY
            : String.format("%s+WHERE+%s", USERS_QUERY, String.join("+AND+", conditions));
    SalesforceRecordsResponse<User> usersBatch =
        liftApi.getRecordsQuery(
            tokenAndApiPath.getAuthToken(),
            tokenAndApiPath.getApiPath(),
            query,
            new ParameterizedTypeReference<>() {});
    List<User> usersList = new ArrayList<>(usersBatch.getRecords());
    while (usersBatch.getNextRecordsUrl() != null) {
      usersBatch =
          liftApi.getRecordsPage(
              tokenAndApiPath.getAuthToken(),
              usersBatch.getNextRecordsUrl(),
              new ParameterizedTypeReference<>() {});
      usersList.addAll(usersBatch.getRecords());
    }
    return usersList;
  }
}
