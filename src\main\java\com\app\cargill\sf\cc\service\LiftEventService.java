/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.EventDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.simple.EventUpdateModel;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiftEventService {

  private final LiftApiService liftApi;

  public EventDocument createEvent(
      EventDocument eventDocument,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, CustomDEExceptions {
    if (!inputModelIsValid(eventDocument)) {
      throw new IllegalArgumentException("Some of the required fields are null.");
    }
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return createEvent(
        tokenAndApiPath.getAuthToken(),
        tokenAndApiPath.getApiPath(),
        eventDocument,
        locale,
        resourceBundleMessageSource);
  }

  public EventDocument createEvent(
      AuthToken authToken,
      String apiPath,
      EventDocument eventDocument,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, CustomDEExceptions {
    try {
      String eventUrl = String.format("%s/sobjects/Event", apiPath);
      EventUpdateModel eventUpdateModel = documentToModel(eventDocument);

      log.debug("CREATING EVENT");
      CreateRecordResponse recordResponse =
          liftApi.createRecord(
              authToken, eventUpdateModel, new ParameterizedTypeReference<>() {}, eventUrl);
      log.debug(
          "EVENT CREATED EventId: " + recordResponse.getId() + " Id : " + eventDocument.getId());
      eventDocument.setEventId(recordResponse.getId());
      return eventDocument;
    } catch (Exception e) {
      log.error(String.format("Error on event creation: %s", eventDocument.getId()), e);
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  resourceBundleMessageSource.getMessage(
                      LangKeys.LIFT_SYNC_FAILED,
                      new Object[] {},
                      LiftErrorResponseConstants.LIFT_ERROR_MESSAGE,
                      locale))
              .entity("Event")
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()));
    }
  }

  public EventDocument updateEvent(EventDocument eventDocument) {
    if (!inputModelIsValidForUpdate(eventDocument)) {
      throw new IllegalArgumentException("Some of the required fields are null.");
    }
    AuthToken authToken = liftApi.getToken();
    String apiPath = liftApi.getLatestApiPath(authToken);
    String eventUpdateUrl =
        String.format("%s/sobjects/Event/%s", apiPath, eventDocument.getEventId());
    EventUpdateModel eventUpdateModel = documentToModel(eventDocument);
    liftApi.updateRecord(
        authToken, eventUpdateModel, new ParameterizedTypeReference<>() {}, eventUpdateUrl);
    log.info("EVENT_UPDATED {}", eventDocument);
    return eventDocument;
  }

  private boolean inputModelIsValid(EventDocument eventDocument) {
    return eventDocument.getDeSiteVisitIdC() != null
        && eventDocument.getSubject() != null
        && eventDocument.getTypeC() != null
        && eventDocument.getDeActivityExternalIdC() != null
        && eventDocument.getStartDateTime() != null
        && eventDocument.getActivityDateTime() != null;
  }

  private boolean inputModelIsValidForUpdate(EventDocument eventDocument) {
    return eventDocument.getEventId() != null;
  }

  public EventUpdateModel documentToModel(EventDocument eventDocument) {
    EventUpdateModel eventUpdateModel = new EventUpdateModel();
    eventUpdateModel.setIsAllDayEvent(eventDocument.getIsAllDayEvent());
    eventUpdateModel.setBusinessC(eventDocument.getBusinessC());
    eventUpdateModel.setSubject(eventDocument.getSubject());
    eventUpdateModel.setActivityDateTime(eventDocument.getActivityDateTime());
    eventUpdateModel.setTypeC(eventDocument.getTypeC());
    eventUpdateModel.setDeActivityExternalIdC(eventDocument.getDeActivityExternalIdC());
    eventUpdateModel.setDeSiteVisitIdC(eventDocument.getDeSiteVisitIdC());
    eventUpdateModel.setEndDateTime(eventDocument.getEndDateTime());
    eventUpdateModel.setIsReminderSet(eventDocument.getIsReminderSet());
    eventUpdateModel.setReportLinkC(eventDocument.getReportLinkC());
    eventUpdateModel.setStartDateTime(eventDocument.getStartDateTime());
    eventUpdateModel.setWhatId(eventDocument.getWhatId());
    eventUpdateModel.setOwnerId(eventDocument.getOwnerId());
    eventUpdateModel.setDeSiteId(eventDocument.getDeSiteId());
    return eventUpdateModel;
  }
}
