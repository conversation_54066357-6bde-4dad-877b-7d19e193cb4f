<div class="container mid-body">
    <div class="pt-0">
        <div class="legend-head">
            <div class="count">${toolNumber!}</div>
            <div class="main-title">
                <span class="sm-head">${localization.getMessage("VisitSummaryViewModel.HealthItem", [], "Health",
                    locale)}</span>
                <span class="lg-head">${localization.getMessage("LocomotionScore", [], "Locomotion Score",
                    locale)}</span>
            </div>
            <div style="font-size: 1;color: white;">0000666LS</div>
        </div>
        <#if model.locomotionScoreTool?? && model.locomotionScoreTool.animalAnalysis??>
            <h3 class="title-secondary mb-1 d-flex justify-space-between">
                <span>${localization.getMessage("Report.Animal.Analysis", [], "Animal Analysis", locale)}</span>
                <span>${model.locomotionScoreTool.bcsPointScale!}</span>
            </h3>
            <div class="row">
                <div class="col-12 table-secondary">
                    <table class="table-center">

                        <#if model.locomotionScoreTool?? && model.locomotionScoreTool.animalAnalysis?? &&
                            model.locomotionScoreTool.animalAnalysis[0] ?? &&
                            model.locomotionScoreTool.animalAnalysis[0].animalDetails[0] ??>
                            <tr>

                                <#list model.locomotionScoreTool.animalAnalysis[0].animalDetails as animalAnalysis>
                                    <th>${animalAnalysis.column!}</th>
                                </#list>

                            </tr>
                        </#if>

                        <#if model.locomotionScoreTool?? && model.locomotionScoreTool.animalAnalysis?? &&
                            model.locomotionScoreTool.animalAnalysis[0] ??>
                            <#list model.locomotionScoreTool.animalAnalysis as innerlist>

                                <tr>
                                    <#list innerlist.animalDetails as innerlistObj>
                                        <td>${innerlistObj.value!}</td>
                                    </#list>
                                </tr>
                            </#list>

                        </#if>
                    </table>
                </div>
            </div>
        </#if>
    </div>
</div>
<#if model.locomotionScoreTool?? && model.locomotionScoreTool.animalAnalysis?? &&
    model.locomotionScoreTool.animalAnalysis[0] ??>
    <div class="break-page"></div>
    <#assign counter=0>
        <div class="container mid-body">
            <div class="pt-0">

                <#list model.locomotionScoreTool.animalAnalysis as innerlist>
                    <#if innerlist.animalAnalysisChart ??>
                        <div class="row">
                            <div class="col-12">
                                <h5 class="title-sub">${innerlist.earTagName!}</h5>
                            </div>
                        </div>
                        <div class="row mx-neg-4">
                            <div class="col-6 px-4">
                                <div class="card mb-1">
                                    <div class="card-body">
                                        <#assign linechart=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart}"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 px-4">

                                <div class="card mb-1">
                                    <div class="card-body">
                                        <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart2}"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <script>

                            (function () {
                                const colors = {
                                    purple: {
                                        default: "#1baca7",
                                        half: "#1baca778",
                                        quarter: "#1baca73b",
                                        zero: "#1baca71c"
                                    },
                                    indigo: {
                                        default: "#1baca7",
                                        quarter: "#1baca778"
                                    }
                                };


                                const data = [
                                    <#list innerlist.animalAnalysisChart as animal >
                                    <#if animal.bcs ?? && animal.dim ??>
                                    {
                                        x: ${ animal.dim },
                                    y: ${ animal.bcs }
				} <#sep>, </#sep>
		</#if >
	</#list >
];
                            const ctx = document.getElementById("${linechart}").getContext("2d");
                            ctx.canvas.height = 100;

                            gradient = ctx.createLinearGradient(0, 25, 0, 300);
                            gradient.addColorStop(0, colors.purple.half);
                            gradient.addColorStop(0.35, colors.purple.quarter);
                            gradient.addColorStop(1, colors.purple.zero);

                            const options = {
                                type: "scatter",
                                data: {
                                    datasets: [
                                        {
                                            labels: '${localization.getMessage("BCSPenSelectionViewModel.Title", [], "Body Condition Score", locale)}',
                                            fill: true,
                                            backgroundColor: gradient,
                                            borderColor: '#1BACA7',
                                            pointBackgroundColor: '#1BACA7',
                                            pointBorderColor: '#ffAA00',
                                            data: data,
                                            lineTension: 0.2,
                                            borderWidth: 1,
                                            pointRadius: 6,
                                        }
                                    ]
                                },
                                options: {
                                    plugins: {
                                        legend: {
                                            display: false,
                                        },
                                        tooltip: {
                                            enabled: false,
                                            titleColor: "#1BACA7",
                                            titleFont: {
                                                font: {
                                                    size: 14,
                                                    weight: 'normal',
                                                },
                                            },
                                            yAlign: 'bottom',
                                            backgroundColor: "#fff",
                                            borderColor: "rgba(0, 0, 0, 0.25)",
                                            borderWidth: 1,
                                            displayColors: false,
                                            bodyColor: "#323F4B",
                                            bodyAlign: "center",
                                        },
                                    },

                                    layout: {
                                        padding: {
                                            top: 20,
                                            right: 15
                                        }
                                    },

                                    responsive: true,

                                    scales: {
                                        y: {
                                            // beginAtZero: true,
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("BCSPenSelectionViewModel.Title", [], "Body Condition Score", locale)}',
                                                padding: {
                                                    bottom: 15,
                                                }
                                            },

                                            grid: {
                                                display: false,
                                            },
                                        },

                                        x: {
                                            type: 'linear',
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("DaysInMilkItem", [], "DIM", locale)}',
                                                padding: {
                                                    top: 15,
                                                }
                                            },
                                            grid: {
                                                display: false,
                                            },
                                        }
                                    },
                                    animation: {
                                        duration: 0,
                                        onComplete: function () {
                                            var chart = this;
                                            var ctx = chart.ctx;
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'bottom';
                                            ctx.fillStyle = '#6C7782';
                                            this.data.datasets.forEach(function (dataset, i) {
                                                var meta = chart.getDatasetMeta(i);
                                                meta.data.forEach(function (bar, index) {
                                                    var data = dataset.data[index];
                                                    var yIndex = bar.y - 8;
                                                    if (data && data < 0) {
                                                        yIndex = bar.y + 20;
                                                    }
                                                    ctx.fillText(data.x + ', ' + data.y, bar.x, yIndex);
                                                });
                                            });
                                        }
                                    }
                                }
                            };

                            window.myLine = new Chart(ctx, options);
}) ();
                        </script>
                        <script>

                            (function () {
                                const colors = {
                                    purple: {
                                        default: "#1baca7",
                                        half: "#1baca778",
                                        quarter: "#1baca73b",
                                        zero: "#1baca71c"
                                    },
                                    indigo: {
                                        default: "#1baca7",
                                        quarter: "#1baca778"
                                    }
                                };


                                const data = [
                                    <#list innerlist.animalAnalysisChart as animal >
                                    <#if animal.locomotionScore ?? && animal.dim ??>
                                    {
                                        x: ${ animal.dim },
                                    y: ${ animal.locomotionScore }
				} <#sep>, </#sep>
		</#if >
	</#list >
];
                            const ctx = document.getElementById("${linechart2}").getContext("2d");
                            ctx.canvas.height = 100;

                            gradient = ctx.createLinearGradient(0, 25, 0, 300);
                            gradient.addColorStop(0, colors.purple.half);
                            gradient.addColorStop(0.35, colors.purple.quarter);
                            gradient.addColorStop(1, colors.purple.zero);

                            const options = {
                                type: "scatter",
                                data: {
                                    datasets: [
                                        {
                                            labels: '${localization.getMessage("LocomotionScore", [], "Locomotion Score", locale)}',
                                            fill: true,
                                            backgroundColor: gradient,
                                            borderColor: '#1BACA7',
                                            pointBackgroundColor: '#1BACA7',
                                            pointBorderColor: '#ffAA00',
                                            data: data,
                                            lineTension: 0.2,
                                            borderWidth: 1,
                                            pointRadius: 6,
                                        }
                                    ]
                                },
                                options: {
                                    plugins: {
                                        legend: {
                                            display: false,
                                        },
                                        tooltip: {
                                            enabled: false,
                                            titleColor: "#1BACA7",
                                            titleFont: {
                                                font: {
                                                    size: 14,
                                                    weight: 'normal',
                                                },
                                            },
                                            yAlign: 'bottom',
                                            backgroundColor: "#fff",
                                            borderColor: "rgba(0, 0, 0, 0.25)",
                                            borderWidth: 1,
                                            displayColors: false,
                                            bodyColor: "#323F4B",
                                            bodyAlign: "center",
                                        },
                                    },

                                    layout: {
                                        padding: {
                                            top: 20,
                                            right: 15
                                        }
                                    },

                                    responsive: true,

                                    scales: {
                                        y: {
                                            // beginAtZero: true,
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("LocomotionScore", [], "Locomotion Score", locale)}',
                                                padding: {
                                                    bottom: 15,
                                                }
                                            },

                                            grid: {
                                                display: false,
                                            },
                                        },

                                        x: {
                                            type: 'linear',
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("DaysInMilkItem", [], "DIM", locale)}',
                                                padding: {
                                                    top: 15,
                                                }
                                            },
                                            grid: {
                                                display: false,
                                            },
                                        }
                                    },
                                    animation: {
                                        duration: 0,
                                        onComplete: function () {
                                            var chart = this;
                                            var ctx = chart.ctx;
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'bottom';
                                            ctx.fillStyle = '#6C7782';
                                            this.data.datasets.forEach(function (dataset, i) {
                                                var meta = chart.getDatasetMeta(i);
                                                meta.data.forEach(function (bar, index) {
                                                    var data = dataset.data[index];
                                                    var yIndex = bar.y - 8;
                                                    if (data && data < 0) {
                                                        yIndex = bar.y + 20;
                                                    }
                                                    ctx.fillText(data.x + ', ' + data.y, bar.x, yIndex);
                                                });
                                            });
                                        }
                                    }
                                }
                            };

                            window.myLine = new Chart(ctx, options);
}) ();
                        </script>
                    </#if>
                    <#assign counter=counter+1>
                        <#if counter%6==0>
                            <div class="break-page"></div>
                        </#if>
                </#list>
            </div>
        </div>
</#if>

<div class="container mid-body">
    <div class="pt-0">
        <#if model.locomotionScoreTool?? && model.locomotionScoreTool.penAnalysis??>
            <h3 class="title-secondary mb-1">${localization.getMessage("RumenHealthLandingViewModel.PenAnalysis", [],
                "Pen Analysis", locale)}</h3>

            <div class="row mx-neg-4">


                <#if model.locomotionScoreTool?? && model.locomotionScoreTool.penAnalysis?? &&
                    model.locomotionScoreTool.penAnalysis[0] ?? &&
                    model.locomotionScoreTool.penAnalysis[0].penDetails[0] ??>
                    <#list model.locomotionScoreTool.penAnalysis as penAnalysis>
                        <div class="col-6 mb-1 px-4 table-secondary">
                            <h3 class="title">${penAnalysis.penName!}</h3>
                            <table class="table-center">
                                <tbody>
                                    <tr>
                                        <#list penAnalysis.penDetails[0] as penDetailsObj>
                                            <th>${penDetailsObj.column!}</th>
                                        </#list>
                                    </tr>

                                    <#list penAnalysis.penDetails as penDetailsObj>
                                        <tr>
                                            <#list penDetailsObj as innerlistObj>
                                                <td>${innerlistObj.value!}</td>
                                            </#list>
                                        </tr>
                                    </#list>
                                </tbody>
                            </table>
                        </div>
                    </#list>
                </#if>
            </div>
        </#if>
    </div>
</div>

<#assign counter=0>
    <#if model.locomotionScoreTool?? && model.locomotionScoreTool.penAnalysis ??>
        <div class="break-page"></div>
        <#if model.locomotionScoreTool?? && model.locomotionScoreTool.penAnalysis ?? &&
            model.locomotionScoreTool.penAnalysis[0] ?? && model.locomotionScoreTool.penAnalysis[0].categoriesChart ??>

            <div class="container mid-body">
                <div class="pt-0">
                    <#list model.locomotionScoreTool.penAnalysis?chunk(2) as row>
                        <div class="row mx-neg-4">
                            <#list row as penAnalysis>

                                <div class="col-6 px-4">
                                    <h5 class="title-sub">${penAnalysis.penName!}</h5>
                                    <div class="card mb-1">
                                        <div class="card-body">
                                            <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                                <canvas id="${linechart2}"></canvas>
                                        </div>
                                        <div class="card-footer pt-0">
                                            <div class="row">
                                                <div class="legend-wrap mb-1">
                                                    <p>${localization.getMessage("Average", [], "Average", locale)}:
                                                        <b>${penAnalysis.average!0.0} </b>&nbsp; &nbsp;
                                                        ${localization.getMessage("StandardDeviationScoreTitle", [],
                                                        "Standard Deviation", locale)}:
                                                        <b>${penAnalysis.standardDeviation!0.0} </b></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <script>

                                    (function () {
                                        const colors = {
                                            purple: {
                                                default: "#1baca7",
                                                half: "#1baca778",
                                                quarter: "#1baca73b",
                                                zero: "#1baca71c"
                                            },
                                            indigo: {
                                                default: "#1baca7",
                                                quarter: "#1baca778"
                                            }
                                        };


                                        const yAxis = [
                                            <#list penAnalysis.categoriesChart as category >
                                            ${(category.locomotionCategoryScoreAverage)!'NaN'} <#sep>, </#sep>
    </#list >
];

                                    const xAxis = [
                                        <#list penAnalysis.categoriesChart as category >
                                        "${(category.visitDate)!}" <#sep>, </#sep>
    </#list >
];

                                    const ctx = document.getElementById("${linechart2}").getContext("2d");
                                    ctx.canvas.height = 100;

                                    gradient = ctx.createLinearGradient(0, 25, 0, 300);
                                    gradient.addColorStop(0, colors.purple.half);
                                    gradient.addColorStop(0.35, colors.purple.quarter);
                                    gradient.addColorStop(1, colors.purple.zero);

                                    const options = {
                                        type: "line",
                                        data: {
                                            labels: xAxis,
                                            datasets: [
                                                {
                                                    fill: true,
                                                    backgroundColor: gradient,
                                                    borderColor: '#1BACA7',
                                                    pointBackgroundColor: '#1BACA7',
                                                    pointBorderColor: '#fff',
                                                    data: yAxis,
                                                    lineTension: 0.2,
                                                    borderWidth: 1,
                                                    pointRadius: 6,
                                                }
                                            ]
                                        },
                                        options: {
                                            plugins: {
                                                legend: {
                                                    display: false,
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        title: () => null // or function () { return null; }
                                                    },
                                                    yAlign: 'bottom',
                                                    backgroundColor: "#fff",
                                                    borderColor: "rgba(0, 0, 0, 0.25)",
                                                    borderWidth: 1,
                                                    displayColors: false,
                                                    bodyColor: "#1BACA7",
                                                    bodyAlign: "center",
                                                },
                                            },

                                            layout: {
                                                padding: {
                                                    top: 20,
                                                    right: 15
                                                }
                                            },

                                            responsive: true,

                                            scales: {
                                                y: {
                                                    // beginAtZero: true,
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("EditNoteViewModel.Category", [], "Category", locale)}',
                                                        padding: {
                                                            bottom: 15,
                                                        }
                                                    },

                                                    grid: {
                                                        display: false,
                                                    },
                                                },

                                                x: {
                                                    grid: {
                                                        display: false,
                                                    },
                                                    color: '#6C7782',
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                        padding: {
                                                            bottom: 15,
                                                        }
                                                    }
                                                }
                                            },
                                            animation: {
                                                duration: 0,
                                                onComplete: function () {
                                                    var chart = this;
                                                    var ctx = chart.ctx;
                                                    ctx.textAlign = 'center';
                                                    ctx.textBaseline = 'bottom';
                                                    ctx.fillStyle = '#6C7782';
                                                    this.data.datasets.forEach(function (dataset, i) {
                                                        var meta = chart.getDatasetMeta(i);
                                                        meta.data.forEach(function (bar, index) {
                                                            var data = dataset.data[index];
                                                            data = isNaN(data) ? '' : data;
                                                            var yIndex = bar.y - 5;
                                                            if (data && data < 0) {
                                                                yIndex = bar.y + 15;
                                                            }
                                                            ctx.fillText(data, bar.x, yIndex);
                                                        });
                                                    });
                                                }
                                            }
                                        }
                                    };

                                    window.myLine = new Chart(ctx, options);
}) ();
                                </script>
                                <#assign counter=counter+1>
                            </#list>
                        </div>
                        <#if counter%10==0>
                            <div class="break-page"></div>
                        </#if>
                    </#list>
                </div>
            </div>
        </#if>
    </#if>
    <#if model.locomotionScoreTool?? && model.locomotionScoreTool.herdAnalysis??>
		<#if model.locomotionScoreTool.animalAnalysis??>
			<div class="break-page"></div>
		</#if>
        <div class="container mid-body pt-0">
            <h3 class="title-secondary mb-1">${localization.getMessage("RumenHealthLandingViewModel.HerdAnalysis", [],
                "Herd Analysis", locale)}</h3>
            <div class="row">
                <div class="col-12 table-secondary">
                    <table class="table-center">
                        <tbody>

                            <#if model.locomotionScoreTool?? && model.locomotionScoreTool.herdAnalysis?? &&
                                model.locomotionScoreTool.herdAnalysis.herdAnalysisDetails[0] ??>
                                <tr>
                                    <#list model.locomotionScoreTool.herdAnalysis.herdAnalysisDetails[0] as headings>
                                        <th>${headings.column!}</th>
                                    </#list>
                                </tr>
                            </#if>

                            <#if model.locomotionScoreTool?? && model.locomotionScoreTool.herdAnalysis?? &&
                                model.locomotionScoreTool.herdAnalysis.herdAnalysisDetails[0] ??>
                                <#list model.locomotionScoreTool.herdAnalysis.herdAnalysisDetails as innerlist>
                                    <tr>
                                        <#list innerlist as obj>
                                            <td>${obj.value!}</td>
                                        </#list>
                                    </tr>
                                </#list>
                            </#if>
                        </tbody>
                    </table>
                </div>
            </div>
            <#if model.locomotionScoreTool?? && model.locomotionScoreTool.herdAnalysis?? &&
                model.locomotionScoreTool.herdAnalysis.graph??>
                <div class="row">
                    <div class="col-12">
                        <div class="card mt-2">
                            <div class="card-body">
                                <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                    <canvas id="${linechart2}"></canvas>
                            </div>
                            <div class="card-footer  pt-0">
                                <div class="row">
                                    <div class="legend-wrap mb-1">
                                        <p class="green-solid">
                                            ${localization.getMessage("LocomotionHerdResultsViewModel.HerdAverage", [],
                                            "Herd Avg", locale)}</p>
                                    </div>
                                    <div class="legend-wrap mb-1">
                                        <p class="purple-strip">${localization.getMessage("Goal", [], "Goal", locale)}
                                        </p>
                                    </div>
                                    <div class="legend-wrap mb-1">
                                        <p>${localization.getMessage("Average", [], "Average", locale)}:
                                            <span>${model.locomotionScoreTool.herdAnalysis.graph.average!}</span></p>
                                    </div>
                                    <div class="legend-wrap mb-1">
                                        <p>${localization.getMessage("StandardDeviationScoreTitle", [], "Standard
                                            Deviation", locale)}:
                                            <span>${model.locomotionScoreTool.herdAnalysis.graph.standardDeviation!}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <script>

                        (function () {
                            const colors = {
                                purple: {
                                    default: "#1baca7",
                                    half: "#1baca778",
                                    quarter: "#1baca73b",
                                    zero: "#1baca71c"
                                },
                                indigo: {
                                    default: "#1baca7",
                                    quarter: "#1baca778"
                                }
                            };

                            const herdAverage = [
                                <#list model.locomotionScoreTool.herdAnalysis.graph.herdAvg ? keys as prop >
                                    "${model.locomotionScoreTool.herdAnalysis.graph.herdAvg[prop]!'NaN'}" <#sep>, </#sep>
    </#list >
];

                            const goal = [
                                <#list model.locomotionScoreTool.herdAnalysis.graph.goals ? keys as prop >
                                    "${model.locomotionScoreTool.herdAnalysis.graph.goals[prop]!'NaN'}" <#sep>, </#sep>
    </#list >
];

                            const xAxis = [
                                <#list model.locomotionScoreTool.herdAnalysis.graph.herdAvg ? keys as prop >
                                    "${prop}" <#sep>, </#sep>
    </#list >
];

                            const ctx = document.getElementById("${linechart2}").getContext("2d");
                            ctx.canvas.height = 100;

                            gradient = ctx.createLinearGradient(0, 25, 0, 300);
                            gradient.addColorStop(0, colors.purple.half);
                            gradient.addColorStop(0.35, colors.purple.quarter);
                            gradient.addColorStop(1, colors.purple.zero);

                            const options = {
                                type: "line",

                                data: {
                                    labels: xAxis,
                                    datasets: [
                                        {
                                            borderColor: '#A160E6',
                                            pointBackgroundColor: '#A160E6',
                                            pointBorderColor: '#fff',
                                            data: goal,
                                            lineTension: 0.2,
                                            borderWidth: 1,
                                            pointRadius: 6,
                                            borderDash: [5, 4],
                                        },
                                        {
                                            fill: true,
                                            backgroundColor: gradient,
                                            borderColor: '#1BACA7',
                                            pointBackgroundColor: '#1BACA7',
                                            pointBorderColor: '#fff',
                                            data: herdAverage,
                                            lineTension: 0.2,
                                            borderWidth: 1,
                                            pointRadius: 6,
                                        }
                                    ]
                                },
                                options: {
                                    plugins: {
                                        legend: {
                                            display: false,
                                        },
                                        tooltip: {
                                            callbacks: {
                                                title: () => null // or function () { return null; }
                                            },
                                            yAlign: 'bottom',
                                            backgroundColor: "#fff",
                                            borderColor: "rgba(0, 0, 0, 0.25)",
                                            borderWidth: 1,
                                            displayColors: false,
                                            bodyColor: "#1BACA7",
                                            bodyAlign: "center",
                                        },
                                    },

                                    layout: {
                                        padding: {
                                            top: 20,
                                            right: 15
                                        }
                                    },

                                    responsive: true,

                                    scales: {
                                        y: {
                                            // beginAtZero: true,
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("Report.LocomotionScore.Y.Axis", [], "Percent %", locale)}',
                                                padding: {
                                                    bottom: 15,
                                                }
                                            },

                                            grid: {
                                                display: false,
                                            },
                                        },

                                        x: {
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("LocomotionScore", [], "Locomotion Score", locale)}',
                                                padding: {
                                                    top: 15,
                                                }
                                            },
                                            grid: {
                                                display: false,
                                            },
                                        }
                                    },
                                    animation: {
                                        duration: 0,
                                        onComplete: function () {
                                            var chart = this;
                                            var ctx = chart.ctx;
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'bottom';
                                            ctx.fillStyle = '#6C7782';
                                            this.data.datasets.forEach(function (dataset, i) {
                                                var meta = chart.getDatasetMeta(i);
                                                meta.data.forEach(function (bar, index) {
                                                    var data = dataset.data[index];
                                                    data = isNaN(data) ? '' : data;
                                                    var yIndex = bar.y - 5;
                                                    if (data && data < 0) {
                                                        yIndex = bar.y + 15;
                                                    }
                                                    ctx.fillText(data, bar.x, yIndex);
                                                });
                                            });
                                        }
                                    }
                                }
                            };

                            window.myLine = new Chart(ctx, options);
                        })();
                    </script>

            </#if>
        </div>
        </div>
    </#if>
    <#if model.locomotionScoreTool?? && model.locomotionScoreTool.notes??>
        <div class="container mid-body">
            <div class="pt-0">
                <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                    locale)}</h3>
                <#list model.locomotionScoreTool.notes as innerlist>
                    <#if innerlist.id??>
                        <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                            <h4 class="followup mb-1">
                                <span  style="white-space: pre-wrap;" >${noteFound.title!}</span>
                                <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                            </h4>
                            <p class="mb-1"  style="white-space: pre-wrap;" >${noteFound.note!}</p>
                            <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                                <div class="notes-images mb-1">
                                    <#list noteFound.mediaItems as media>
                                        <figure>
                                            <img src="${media.base64EncodedImage!}">
                                        </figure>
                                    </#list>
                                </div>
                            </#if>
                        </#list>
                    </#if>
                </#list>
            </div>
        </div>
    </#if>