/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftSyncSchedulerTest {

  @Mock private SalesforceSyncTrigger syncTrigger;

  @InjectMocks private LiftSyncScheduler liftSyncScheduler;

  @Test
  void whenStartLiftSyncItDoesNotRunTheFirstTime() {
    //    liftSyncScheduler.startLiftSync();
    //    liftSyncScheduler.startLiftSync();
    //    Mockito.verify(syncTrigger, Mockito.times(1)).triggerSync(false);
    //    Mockito.verify(syncTrigger, Mockito.times(1)).triggerSync(false);
  }

  @Test
  void whenStartLiftPartialSyncItDoesNotRunTheFirstTime() {
    liftSyncScheduler.startLiftPartialSync();
    liftSyncScheduler.startLiftPartialSync();
    Mockito.verify(syncTrigger, Mockito.times(1)).triggerSync(true);
    Mockito.verify(syncTrigger, Mockito.times(1)).triggerSync(true);
  }
}
