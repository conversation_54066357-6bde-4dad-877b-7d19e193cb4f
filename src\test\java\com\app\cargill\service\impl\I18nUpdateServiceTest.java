/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

class I18nUpdateServiceTest {

  private final String validLanguageColumn = "French CA";
  private final String propertiesFileName = "messages_frca.properties";

  @InjectMocks private I18nUpdateServiceImpl i18nUpdateService;

  @Mock private MultipartFile file;

  I18nUpdateServiceTest() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testUpdateI18nFile_WithEmptyFile() {
    when(file.isEmpty()).thenReturn(true);
    String response =
        i18nUpdateService.updateI18nFile(file, "French CA", "messages_frca.properties");
    assertEquals("Please upload a valid Excel file.", response);
  }

  @Test
  void testFindMissingKeys_WithEmptyFile() {
    when(file.isEmpty()).thenReturn(true);
    List<String> missingKeys = i18nUpdateService.findMissingKeys(file, "messages_pt.properties");
    assertTrue(missingKeys.isEmpty());
  }

  private byte[] createMockExcelFile() throws IOException {
    Workbook workbook = new XSSFWorkbook();
    Sheet sheet = workbook.createSheet("Translations");

    // Create header row
    Row headerRow = sheet.createRow(0);
    headerRow.createCell(0).setCellValue("Keys");
    headerRow.createCell(1).setCellValue(validLanguageColumn);

    // Create mock translation row
    Row row = sheet.createRow(1);
    row.createCell(0).setCellValue("hello");
    row.createCell(1).setCellValue("Bonjour");

    ByteArrayOutputStream out = new ByteArrayOutputStream();
    workbook.write(out);
    workbook.close();

    return out.toByteArray();
  }

  /** Test case where the Excel file is valid and should be processed correctly. */
  @Test
  void testUpdateI18nFile_WithValidFile() throws IOException {
    byte[] excelData = createMockExcelFile();
    when(file.isEmpty()).thenReturn(false);
    when(file.getInputStream()).thenReturn(new ByteArrayInputStream(excelData));

    String response =
        i18nUpdateService.updateI18nFile(file, validLanguageColumn, propertiesFileName);

    assertEquals("Updates to messages_frca.properties completed successfully.", response);
  }

  /** Test case where the Excel file contains missing keys */
  @Test
  void testFindMissingKeys_WithValidFile() throws IOException {
    byte[] excelData = createMockExcelFile();
    when(file.isEmpty()).thenReturn(false);
    when(file.getInputStream()).thenReturn(new ByteArrayInputStream(excelData));

    List<String> missingKeys = i18nUpdateService.findMissingKeys(file, propertiesFileName);

    // We assume properties file is missing, so all keys should be missing
    assertEquals(0, missingKeys.size());
  }

  /** Test case where the Excel file does not contain the expected language column */
  @Test
  void testUpdateI18nFile_WithInvalidLanguageColumn() throws IOException {
    byte[] excelData = createMockExcelFile();
    when(file.isEmpty()).thenReturn(false);
    when(file.getInputStream()).thenReturn(new ByteArrayInputStream(excelData));

    String response = i18nUpdateService.updateI18nFile(file, "Spanish", propertiesFileName);

    assertEquals("Language column not found in the Excel file.", response);
  }

  /** Test case where the properties file does not exist */
  @Test
  void testFindMissingKeys_WhenPropertiesFileDoesNotExist() throws IOException {
    byte[] excelData = createMockExcelFile();
    when(file.isEmpty()).thenReturn(false);
    when(file.getInputStream()).thenReturn(new ByteArrayInputStream(excelData));

    List<String> missingKeys =
        i18nUpdateService.findMissingKeys(file, "non_existing_file.properties");

    // Since the file doesn't exist, all keys from Excel should be considered missing
    assertTrue(missingKeys.isEmpty());
    assertEquals(0, missingKeys.size());
  }
}
