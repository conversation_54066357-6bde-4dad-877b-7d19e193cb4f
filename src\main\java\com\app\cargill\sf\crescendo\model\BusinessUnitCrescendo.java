/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum BusinessUnitCrescendo {
  BRAZIL("Brazil"),
  CANADA("Canada"),
  FRANCE("France"),
  NETHERLANDS("Netherlands"),
  PHILIPPINES("Philippines"),
  POLAND("Poland"),
  US("US"),
  VIETNAM("Vietnam"),
  SPAIN("Spain"),
  ITALY("Italy"),
  KOREA("Korea"),
  INDIA("India"),
  MEXICO("Mexico"),
  RUSSIA("Russia"),
  SOUTH_AFRICA("South Africa"),
  CPN_BRAZIL("CPN - Brazil"),
  CPN_FRANCE("CPN - France"),
  NORTH_AMERICA("North America"),
  CHINA("China"),
  PORTUGAL("Portugal"),
  UKRAINE("Ukraine"),
  CFN_CHINA("CFN - China"),
  CFN_INDIA("CFN - India"),
  CPN_POLAND("CPN - Poland"),
  CPN_US("CPN US"),
  HUNGARY("Hungary"),
  UNITED_KINGDOM("United Kingdom"),
  PAKISTAN("Pakistan"),
  UNITED_STATES("United States"),
  ARGENTINA("Argentina"),
  NEOLAIT_FRANCE("Neolait - France"),
  PROVIMI_FRANCE("Provimi - France");

  private final String value;

  @JsonCreator
  BusinessUnitCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static BusinessUnitCrescendo getFromInt(Integer index) {
    BusinessUnitCrescendo[] values = BusinessUnitCrescendo.values();
    if (index == null || index >= values.length) {
      return BusinessUnitCrescendo.US;
    } else {
      return values[index];
    }
  }
}
