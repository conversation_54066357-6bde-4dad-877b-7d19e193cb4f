/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.sf.cc.model.simple.ExternalDataSourceUpdateModel;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftSiteMappingsServiceTest {

  @Mock private LiftApiService liftApi;

  @InjectMocks private LiftSiteMappingsService siteMappingsService;

  @Test
  void updateMappingSuccess() {

    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());

    assertDoesNotThrow(
        () -> {
          siteMappingsService.updateMapping("record-id", new ExternalDataSourceUpdateModel());
        });
  }
}
