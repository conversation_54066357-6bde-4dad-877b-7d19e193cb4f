/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import java.time.Instant;
import java.time.ZoneOffset;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DatetimeUtilsTest {
  @Test
  void whenDateTimeReturnsSuccess() {
    Assertions.assertNotNull(
        DateTimeUtils.instantToLocalDateTime(Instant.ofEpochSecond(365241780837L), ZoneOffset.UTC));
    Assertions.assertNotNull(
        DateTimeUtils.instantToFormattedDate(Instant.ofEpochSecond(365241780837L)));
  }

  @Test
  void whenDateTimeReturnsNull() {
    Assertions.assertNull(DateTimeUtils.instantToFormattedDate(null));
  }
}
