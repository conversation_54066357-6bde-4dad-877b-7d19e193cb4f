/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.constants.AccountType;
import com.app.cargill.constants.SalesforceAccountType;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AccountTypeMapper {

  private AccountTypeMapper() {}

  public static SalesforceAccountType map(Integer accountType) {
    if (Objects.equals(accountType, AccountType.Competitor.getAccountTypeValue())) {
      return SalesforceAccountType.COMPETITOR;
    } else if (Objects.equals(accountType, AccountType.Consumer.getAccountTypeValue())) {
      return SalesforceAccountType.CONSUMER;
    } else if (Objects.equals(accountType, AccountType.Customer.getAccountTypeValue())) {
      return SalesforceAccountType.CUSTOMER;
    } else if (Objects.equals(accountType, AccountType.Prospect.getAccountTypeValue())) {
      return SalesforceAccountType.PROSPECT;
    } else if (Objects.equals(accountType, AccountType.Thirdparty.getAccountTypeValue())) {
      return SalesforceAccountType.THIRD_PARTY;
    } else {
      String errorMessage = String.format("Unknown accountType value: %1$s", accountType);
      log.error(errorMessage);
      throw new IllegalArgumentException(errorMessage);
    }
  }
}
