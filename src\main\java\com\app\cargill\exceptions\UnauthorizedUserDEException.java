/* Cargill Inc.(C) 2022 */
package com.app.cargill.exceptions;

import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class UnauthorizedUserDEException extends RuntimeException {

  private final String message;
  @Serial private static final long serialVersionUID = 1L;

  public UnauthorizedUserDEException(String message) {
    this.message = message;
  }
}
