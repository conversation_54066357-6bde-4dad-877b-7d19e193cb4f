/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.app.cargill.constants.Types;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum EventTypeCrescendo {
  EMAIL("Email"),
  MEETING("Meeting"),
  OTHER("Other"),
  CALL("Call");
  private final String value;

  @JsonCreator
  EventTypeCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static EventTypeCrescendo getFromTypes(Types types) {
    EventTypeCrescendo[] values = EventTypeCrescendo.values();
    if (types == null) {
      return EventTypeCrescendo.EMAIL;
    } else {
      return values[types.getValue()];
    }
  }
}
