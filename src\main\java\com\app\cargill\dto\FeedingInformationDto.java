/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.BeddingQuality;
import com.app.cargill.constants.ProfitabilityAnalysisQuality;
import com.app.cargill.constants.WaterQuality;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeedingInformationDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Double commercialConcentrate;
  private Boolean commercialConcentrateToggle;
  private Boolean mineralBaseMix;
  private Double mineralBaseMixValue;
  private Boolean nutritek;
  private Boolean xpcUltra;
  private Boolean actiforBoost;
  private Boolean buffer;
  private Boolean nutrigorduraLac;
  private Boolean ice;
  private Boolean energyIce;
  private Boolean monensin;
  private Boolean soyPassBr;
  private Double concentrateTotalConsumed;
  private ProfitabilityAnalysisQuality silage;
  private ProfitabilityAnalysisQuality haylage;
  private ProfitabilityAnalysisQuality hay;
  private ProfitabilityAnalysisQuality pasture;
  private WaterQuality waterQuality;
  private BeddingQuality beddingQuality;
  private Boolean ventilation;
  private Boolean sprinkler;
  private Double temperatureInC;
  private Double airRuPercentage;
  private Double THI;
  private Double respiratoryMovement;
  private Double cowLayingDownPercentage;
  private Double totalDietCost;
  private Double revenuePerCowPerDay;
}
