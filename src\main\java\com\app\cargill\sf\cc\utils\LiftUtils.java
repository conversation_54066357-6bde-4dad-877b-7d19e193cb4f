/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class LiftUtils {

  public String getLangKey(User user, Accounts account, Sites site) {
    String langKey = null;
    if (user == null) {
      langKey = LangKeys.NO_USER_FOUND;
    } else if (account.getAccountDocument().getGoldenRecordId() == null) {
      langKey = LangKeys.ACCOUNT_NOT_SYNCED_TO_LIFT;
    } else if (site != null && site.getSiteDocument().getExternalId() == null) {
      langKey = LangKeys.NO_SITE_NOT_SYNCED_TO_LIFT;
    } else {
      langKey = LangKeys.LIFT_SYNC_FAILED;
    }
    return langKey;
  }

  public String getDefaultValue(User user, Accounts account, Sites site) {
    String defaultValue = null;
    if (user == null) {
      defaultValue = LiftErrorResponseConstants.USER_NOT_FOUND;
    } else if (account.getAccountDocument().getGoldenRecordId() == null) {
      defaultValue = LiftErrorResponseConstants.ACCOUNT_NOT_SYNCED_TO_LIFT;
    } else if (site != null && site.getSiteDocument().getExternalId() == null) {
      defaultValue = LiftErrorResponseConstants.SITE_NOT_SYNCED_TO_LIFT;
    } else {
      defaultValue = LiftErrorResponseConstants.LIFT_ERROR_MESSAGE;
    }
    return defaultValue;
  }

  public void siteValidationError(
      User user,
      Accounts salesforceAccount,
      Sites site,
      Locale locale,
      ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {
    String langKey = getLangKey(user, salesforceAccount, site);
    String defaultValue = getDefaultValue(user, salesforceAccount, site);

    PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
    LiftResponseEntityDto liftResponseEntityDto =
        LiftResponseEntityDto.builder()
            .message(source.getMessage(langKey, new Object[] {}, defaultValue, locale))
            .entity("Site")
            .status(ResponseStatus.FAILED)
            .build();
    payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
    throw new CustomDEExceptions(
        JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()),
        HttpStatus.SC_FORBIDDEN);
  }
}
