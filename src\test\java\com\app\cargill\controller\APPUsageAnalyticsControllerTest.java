/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.model.UserDailyLogin;
import com.app.cargill.service.admin.UserAdminService;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class APPUsageAnalyticsControllerTest {

  @Mock private UserAdminService userAdminService;
  @InjectMocks private APPUsageAnalyticsController appUsageAnalyticsController;

  @Test
  void getUsersAllLogin() {
    when(userAdminService.getUsersAllLogin(any(), any())).thenReturn(new ArrayList<>());
    ResponseEntity<ResponseEntityDto<List<UserDailyLogin>>> result =
        appUsageAnalyticsController.getUsersAllLogin(
            Instant.now().minus(1, ChronoUnit.DAYS), Instant.now());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }
}
