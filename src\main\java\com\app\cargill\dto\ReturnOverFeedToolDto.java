/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("java:S125")
public class ReturnOverFeedToolDto extends EditableDocumentBaseDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private UUID visitId;

  private TMRDto tmr;
  private TMRDto
      individualCow; // Using same DTO as all the fields are same, but only the object name is
  // different.
}
