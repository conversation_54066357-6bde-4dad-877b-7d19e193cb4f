/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.time.Instant;
import java.util.*;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class StateDto {

  private UUID id;
  private String stateName;
  private String countryCode;
  private String stateCode;

  private Instant createdDate;
  private Instant updatedDate;

  private boolean deleted;

  private String localId;

  private Instant currentTimeStamp;
  private String stateKey;

  public Instant getCurrentTimeStamp() {
    return currentTimeStamp = Instant.now();
  }
}
