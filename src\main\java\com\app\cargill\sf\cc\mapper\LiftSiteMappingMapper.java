/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.simple.Site;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LiftSiteMappingMapper {
  private LiftSiteMappingMapper() {}

  public static SiteMappingDocument transform(Site input) {
    try {
      return innerTransform(input);
    } catch (Exception e) {
      log.error("Cannot transform SiteMapping {}", e.getMessage());
      throw new IllegalArgumentException("SiteMapping transformation error");
    }
  }

  private static SiteMappingDocument innerTransform(Site input) {

    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
    for (ExternalDataSource dataSource : input.getExternalDataSources().getRecords()) {
      if ("LM_SITE".equals(dataSource.getSystem())) {
        siteMappingDocument.setLabyrinthSiteId(
            extractUUID(dataSource.getUniqueExternalKey(), input.getId()));
        siteMappingDocument.setLabyrinthAccountId(
            extractUUID(input.getAccountRecord().getExternalId(), input.getId()));
      }
      if ("MAX".equals(dataSource.getSystem())) {
        siteMappingDocument.setMaxSiteId(
            extractUUID(dataSource.getUniqueExternalKey(), input.getId()));
      }
      if ("DDW".equals(dataSource.getSystem())) {
        siteMappingDocument.setDdwHerdId(dataSource.getUniqueExternalKey());
      }
      if ("MILK PROCESSOR".equals(dataSource.getSystem())) {
        siteMappingDocument.setMilkProcessorId(dataSource.getUniqueExternalKey());
      }
    }
    return siteMappingDocument;
  }

  private static UUID extractUUID(String input, String siteId) {
    try {
      return UUID.fromString(input);
    } catch (Exception e) {
      log.error("UUID_ERROR input: {} siteId: {} {}", input, siteId, e.getMessage(), e);
      return null;
    }
  }
}
