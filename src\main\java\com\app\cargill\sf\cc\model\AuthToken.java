/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Getter;
import lombok.Setter;

/**
 * This is the authorization token received by the Salesforce instance after calling
 * {cc.loginURL}?grant_type=password&client_id={cc.auth.can.CC_CLIENT_ID}&client_secret={cc.auth.can.CC_CLIENT_SECRET}&username={cc.auth.can.CC_USER}&password={cc.auth.can.CC_PASS}
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
public class AuthToken implements Serializable {

  /** The Constant TOKEN_EXPIRATION_TIME - 2 hours */
  public static final long TOKEN_EXPIRATION_TIME = 2 * 60 * 60 * 1000L;

  /** The Constant serialVersionUID. */
  @Serial private static final long serialVersionUID = 1L;

  /** The access token. */
  @JsonProperty("access_token")
  private String accessToken;

  /** The instance url. */
  @JsonProperty("instance_url")
  private String instanceUrl;

  /** The issued at. */
  @JsonProperty("issued_at")
  private long issuedAt;

  /** The token type. */
  @JsonProperty("token_type")
  private String tokenType;

  /** The signature */
  @JsonProperty("signature")
  private String signature;

  /** The id */
  @JsonProperty("id")
  private String id;

  /**
   * Checks if a token has expired.
   *
   * @return true, if is token expired
   */
  public boolean isExpired() {
    Timestamp currentTime = new Timestamp(System.currentTimeMillis());
    return currentTime.getTime() - issuedAt > TOKEN_EXPIRATION_TIME;
  }
}
