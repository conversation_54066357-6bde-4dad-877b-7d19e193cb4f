/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.AccountDto;
import com.app.cargill.dto.AccountFavouriteDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IAccountService;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class AccountControllerTest {

  @Mock private IAccountService accountService;

  @InjectMocks private AccountController controller;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void getAllAccountsNonPaginated() {
    when(accountService.getAllAccountsNonPaginated()).thenReturn(generateAccounts());
    ResponseEntity<ResponseEntityDto<List<AccountDto>>> result =
        controller.getAllAccountsNonPaginated();
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void getAllAccounts() {
    when(accountService.getAllAccounts(any(), any(), any())).thenReturn(generateAccounts());
    ResponseEntity<ResponseEntityDto<List<AccountDto>>> result =
        controller.getAllAccounts("x", Instant.now(), "<EMAIL>");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void getAllPaginatedAccounts() {
    when(accountService.getPaginatedAccounts(
            any(), anyInt(), anyInt(), any(), any(), any(), any(), any()))
        .thenReturn(generatePage());
    ResponseEntity<ResponseEntityDto<Page<AccountDto>>> result =
        controller.getPaginatedAccounts("s", 1, 10, "sb", "at", Instant.now(), "<EMAIL>", "asc");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  //  @Test
  //  void getPaginatedAccounts() {
  //    when(accountService.getPaginatedAccounts(
  //            any(), anyInt(), anyInt(), any(), any(), any(), any(), any()))
  //        .thenReturn(generatePage());
  //
  //    ResponseEntity<ResponseEntityDto<Page<AccountDto>>> result =
  //        controller.getPaginatedAccounts("s", 1, 10, "sb", "at",
  // Instant.parse("1970-01-01T00:00:00Z"), "<EMAIL>", "asc");
  //    assertNotNull(result);
  //    assertNotNull(Objects.requireNonNull(result.getBody()));
  //    assertNotNull(result.getBody().getData());
  //    assertEquals(2, result.getBody().getData().getTotalElements());
  //  }

  @Test
  void favourite() {
    when(accountService.favourite(any())).thenReturn(AccountDto.builder().build());
    ResponseEntity<ResponseEntityDto<AccountDto>> result =
        controller.favourite(AccountFavouriteDto.builder().build());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void save() throws Exception {
    when(accountService.save(any(), any(), any())).thenReturn(AccountDto.builder().build());
    ResponseEntity<ResponseEntityDto<Object>> result =
        controller.save(AccountDto.builder().build(), locale.getLanguage());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void update() throws NotFoundDEException, Exception {
    when(accountService.update(any(), any(), any())).thenReturn(AccountDto.builder().build());
    ResponseEntity<ResponseEntityDto<Object>> result =
        controller.update(AccountDto.builder().build(), locale.getLanguage());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void testSaveAccountFailure()
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {
    AccountDto accountDto = new AccountDto();

    when(accountService.save(eq(accountDto), any(), any()))
        .thenThrow(new CustomDEExceptions("Test exception message"));

    ResponseEntity<ResponseEntityDto<Object>> response =
        controller.save(accountDto, Locale.ENGLISH.getLanguage());

    verify(accountService, times(1)).save(any(), any(), any());

    assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
  }

  @Test
  void testUpdateAccountFailure()
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {
    AccountDto accountDto = new AccountDto();

    when(accountService.update(any(), any(), any()))
        .thenThrow(new CustomDEExceptions("Test exception message"));

    ResponseEntity<ResponseEntityDto<Object>> response =
        controller.update(accountDto, locale.getLanguage());

    verify(accountService, times(1)).update(any(), any(), any());

    assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
  }

  @Test
  void getAccountById() {
    when(accountService.getAccountById(any())).thenReturn(AccountDto.builder().build());
    ResponseEntity<ResponseEntityDto<AccountDto>> result =
        controller.getAccountById(UUID.randomUUID().toString());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  private List<AccountDto> generateAccounts() {
    return List.of(AccountDto.builder().build(), AccountDto.builder().build());
  }

  private Page<AccountDto> generatePage() {
    return new PageImpl<>(generateAccounts());
  }
}
