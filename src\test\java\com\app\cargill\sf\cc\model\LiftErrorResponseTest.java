/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.sf.cc.utils.LiftErrorCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import org.junit.jupiter.api.Test;

class LiftErrorResponseTest {

  @Test
  void testDeserializer() throws JsonProcessingException {
    ObjectMapper objectMapper = new ObjectMapper();
    List<LiftErrorResponse> liftErrorResponse =
        objectMapper.readValue(
            "[{\"message\": \"entity is deleted\",\"errorCode\": \"ENTITY_IS_DELETED\", \"fields\":"
                + " []}]",
            new TypeReference<>() {});
    assertEquals(LiftErrorCode.ENTITY_IS_DELETED, liftErrorResponse.get(0).getErrorCode());
  }
}
