(Attachement=de trayeuse seulement)
(Préparation=des trayons inadéquate\: &lt; 10 secondes; attachement de trayeuse\:  &lt; 60 ou &gt; 120 secondes)
(Péparation=adéquate\: 10-20 secondes; pose de la trayeuse de 60 à 90 secondes après)
({0})=
AAEfficiency=Efficacité des AA
AMSUtilization=Utilisation de l'AMS
AMSUtilizationChart=Utilisation du robot de traite
ARA=ARA
ARS=Argentine ($ ARS)
AUD=Australie ($ AUD)
Account=Compte
Account-Not-Synced-To-Lift=Le compte n'a pas été synchronisé avec LIFT ; veuillez contacter l'administrateur pour une résolution
Acre=Acre
Action=Action
AddBag=Ajouter un ag-bag
AddBunker=Ajouter un bunker
AddPile=Ajouter un amas
AddTMRScore=Ajouter un score de RTM
AdjustingKPtoAssureSuccess=Contrôler l'éclatement du grain pour assurer le succès
Afghanistan=Afghanistan
Agrigento=Agrigento
Aguascalientes=Aguascalientes
Alabama=Alabama
Alagoas=Alagoas
Aland_Islands=Iles Aland
Alaska=Vers le bas
Albania=Albanie
Alberta=Alberta
Alessandria=Alexandrie
Algeria=Algérie
AmapÃ¡=Ampasse
Amazonas=Amazonas
Amount=Quantité
Ancona=AncÃ´ne
Andaman_and_Nicobar_Islands=Ãles Andaman et Nicobar
Andhra_Pradesh=Andhra Pradesh
Andorra=Andorre
Angola=Angola
Anguilla=Anguille
Anhui=Anhui
AnimalInformation=Informations de l'animal
AnimalListViewModel.Title=Classe / sous-classe animale
Animals=Animaux
AnimalsInHerd=Animaux dans le troupeau
AnimalsInPen=Animaux dans le groupe
AnimalsObserved=Animaux observés
Annually=1 fois par année
Answers=Réponses
Antarctica=Antarctique
Antigua_and_Barbuda=Antigua-et-Barbuda
Aosta=Aosta
AppName=Dairy Enteligen
Arezzo=Arezzo
Argentina=Argentine
Arizona=Arizona
Arkansas=Arkansa
Armenia=ArmÃ©nie
Aruba=Aruba
Arunachal_Pradesh=Arunachal Pradesh
Ascoli_Piceno=Ascoli Piceno
Assam=Assam
Asti=Jusqu'Ã 
AtSixLengthPerDayImperial=à 6 pouces par jour
AtSixLengthPerDayMetric=à 15 cm par jour
AtThreeLengthPerDayImperial=À 3 pouces par jour
AtThreeLengthPerDayMetric=à 7 cm par jour
Australia=Australie
Australian_Capital_Territory=Territoire de la capitale australienne
Austria=L'Autriche
Auto_Sync=Synchronisation automatique
Avellino=Avellino
Average=Moyen
AverageConcentrate=Moyenne de concentrés
AverageMilkLoss=Perte moyenne de lait ({0})
AverageScoreTitle=Moyennne des résultats du séparateur de particules de la RTM
AvgBCS=État corporel moyen
AvgLocomotionScore=Score de locomotion moyenne (calculé)
Azerbaijan=AzerbaÃ¯djan
BAM=BAM
BCS=État corporel
BCSCategory1=État corporel Catégorie 1.0
BCSCategory1pt5=État corporel Catégorie 1.5
BCSCategory2=État corporel Catégorie 2.0
BCSCategory2pt5=État corporel Catégorie 2.5
BCSCategory3=État corporel Catégorie 3.0
BCSCategory3pt5=État corporel Categorie 3.5
BCSCategory4=État corporel Categorie 4.0
BCSCategory4pt5=État corporel Catégorie 4.5
BCSCategory5=État corporel Catégorie 5.0
BCSEditMilkAndDimViewModel.BCSDIMTitle=Jours en lait (JEL)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Production laitière
BCSEditMilkAndDimViewModel.Title=Modifier jour de lactation et production laitière
BCSHerdAnalysisInputsViewModel.BCS=État corporel
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Analyser l'état corporel
BCSHerdAnalysisInputsViewModel.BCSDIM=Jours en lait
BCSHerdAnalysisInputsViewModel.BCSEdit=Modifier
BCSHerdAnalysisInputsViewModel.BCSMilk=Production laitière
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Analyse du troupeau
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Objectifs
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Données
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Résultats
BCSHerdAnalysisMasterViewModel.BCSTitle=État corporel
BCSHerdAnalysisMasterViewModel.Title=État corporel
BCSHerdAnalysisResultsViewModel.BCSAvg=État corporel moyen
BCSHerdAnalysisResultsViewModel.GraphTitle=Analyse de l'état corporel
BCSHerdAnalysisResultsViewModel.MaxBCS=État corporel maximum
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Lait/vache/jour
BCSHerdAnalysisResultsViewModel.MinBCS=État corporel minimum
BCSHerdAnalysisResultsViewModel.SubHeading=Analyse du troupeau
BCSHerdAnalysisResultsViewModel.Title=État corporel
BCSPenSelectionViewModel.PenSelectionList=GROUPES
BCSPenSelectionViewModel.Pens=Groupes
BCSPenSelectionViewModel.SelectPointScale=Sélectionnez une échelle de point
BCSPenSelectionViewModel.Title=État corporel
BCSSelectPointScaleViewModel.FooterText=Une seule échelle de points ne peut être utilisée par visite. Changer l'échelle de points entraînera la perte de valeurs.
BCSSelectPointScaleViewModel.SelectPointScale=Sélectionnez une échelle de point
BCSSelectPointScaleViewModel.Title=État corporel
BGL=BGL
BRL=Brésil (R$ BRL)
BRR=BRR
Bad=Bad
Bag=Ag-bag
BaggedConventionalSilage=Ag-bag
Bags=Ag-bag
Bahamas=Bahamas
Bahia=Bahia
Bahrain=BahreÃ¯n
Baja_California=Baja Californie
Baja_California_Sur=Baja California Sur
Baleage=Balle enrobée
BaleageFQAs=Enrubannage FAQs
Baleage_AreBalesWrappedWith=Est-ce que les balles sont enrubannées avec\:
Baleage_BagsPlacedOnStableWellManagedSurface=Les balles sont placées sur une surface stable et bien gérée toute la saison?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Les balles sont-elles inspectées et les trous réparés hebdomadairement?
Baleage_TrashVegRodentControlledAroundBags=Les déchets, la végétation et les rongeurs sont-ils maîtrisés autour des balles?
Baleage_WaterShedsOffPlasticNotIntoBaleage=L'eau ne pénètre pas dans les balles ou le silo
Bangladesh=Bangladesh
Barbados=Barbade
Bari=Ils Ã©taient
Barletta-Andria-Trani=Barletta-andria-Trani
Bayern=Bayern
BeddedPack=Aire paillée
Beijing=PÃ©kin
Belarus=BiÃ©lorussie
Belgium=Belgique
Belize=Belize
Belluno=Belluno
Benchmarks_Serum_ToolTip=<value />
Benevento=Bienfaisance
Benin=BÃ©nin
Bergamo=Bergame
Bermuda=Bermudes
BetweenFifteenTwenty=Entre 15 et 20
Bhutan=Bhoutan
BiWeekly=Toutes les 2 semaines
Biella=Birella
Bihar=Bihar
BodyConditionHerdGoals=État corporel -Évaluation de troupeau - Objectifs
BodyConditionHerdInputs=État corporel - Évaluation de troupeau - Données
BodyConditionHerdResults=État corporel - Évaluation de troupeau - Résultats
BodyConditionInputs=État corporel Données
BodyConditionResults=État corporel Résultats
BodyConditionScoreCategory=État corporel Categorie {0}
BodyConditionScoreEditInputsViewModel.Count=Nombre
BodyConditionScoreEditInputsViewModel.NumberOfCows=Nombre de vaches
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Veuillez compter le nombre de vaches
BodyConditionScoreEditInputsViewModel.Title=Nombre de vaches
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=VACHES EN PRÉPARATION (-20 à -1)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=DÉBUT DE LACTATION (16 à 60)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=VACHES TARIES (moins de -21)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=FRAÎCHES VÊLÉES (0 à 15)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=FIN DE LACTATION (PLUS DE 201)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=Note d'état corporel minimum
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=MI-LACTATION (121 à 200)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=Note d'état corporel minimum
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=PIC DE LACTATION (61 à 120)
BodyConditionScoreHerdEditGoalsViewModel.Title=Modifier les objectifs
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Vaches en préparation (-20 à -1)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Début de lactation (16 à 60)
BodyConditionScoreHerdGoalsViewModel.Edit=Modifier
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Vaches taries (moins de -21)
BodyConditionScoreHerdGoalsViewModel.Fresh=Fraîches Vêlées  (0 à 15)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Objectif maximal de l'état corporel
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Objectif minimal de l'état corporel
BodyConditionScoreHerdGoalsViewModel.LateLactation=Fin de lactation (plus de 201)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Mi-lactation (121 à 200)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Pic de lactation (61 à 120)
BodyConditionScoreHerdGoalsViewModel.TableTitle=État corporel par stade de lactation
BodyConditionScoreInputsViewModel.AnimalsObserved=Nombre d'animaux évalués
BodyConditionScoreInputsViewModel.AvgBCSCalculated=État corporel moyen (calculé)
BodyConditionScoreInputsViewModel.BCSCategory=Catégorie État corporel
BodyConditionScoreInputsViewModel.BCSPercentOfPen=%
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=État corporel
BodyConditionScoreInputsViewModel.Edit=Modifier
BodyConditionScoreInputsViewModel.StdDevCalculated=Écart standard (calculé)
BodyConditionScoreMasterViewModel.Goals=Objectifs
BodyConditionScoreMasterViewModel.Inputs=Données
BodyConditionScoreMasterViewModel.Results=Résultats
BodyConditionScoreMasterViewModel.SubHeading=Analyse du troupeau
BodyConditionScoreMasterViewModel.Title=État corporel
BodyConditionScoreResultsViewModel.BCSAverageTitle=Score moyen
BodyConditionScoreResultsViewModel.PercentPen=Pourcentage du groupe (%)
BodyConditionScoreResultsViewModel.SelectedDates=Sélectionnez les dates
BodyConditionScoreResultsViewModel.Title=État corporel - résultats
BodyConditionScoresMasterViewModel.BodyConditionScore=État corporel
BodyConditionScoresMasterViewModel.Inputs=Données
BodyConditionScoresMasterViewModel.Results=Résultats
BodyConditionScoresMasterViewModel.Title=État corporel
BodyConditionScoresMasterViewModel.VisitNotebook=Cahier de notes
Bolivia,_Plurinational_State_of=Bolivie, Ã©tat plurinal de
Bologna=Bologne
Bolzano=Bolzano
Bonaire=Bonaire
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius et Saba
Bosnia_and_Herzegovina=Bosnie HerzÃ©govine
Botswana=Botwana
BottomUnloadingSilo=Silo à déchargement par le bas
BottomUnloadingSilos=Silo à déchargement par le bas
Bouvet_Island=Ãle Bouvet
Brazil=Brésil
Brescia=Brescia
Brindisi=Toasts
British_Columbia=Colombie britannique
British_Indian_Ocean_Territory=Territoire britannique de l'ocÃ©an Indien
Brunei_Darussalam=Brunei Darussalam
Bulgaria=Bulgarie
Bull=Taureau
Bunker=Bunker
BunkerCapacity=Capacité du bunker
BunkerFeedOutRate=Reprise du bunker
Bunkers=Bunkers
BunkersAndPiles=Bunkers et Amas
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Les murs sont-ils couverts de 2 couches de plastique et d'une couche imperméable?
BunkersAndPiles_CleanlinessOfFeedArea=Comment évaluez-vous la propreté de la zone d'alimentation?
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=À quelle fréquence la bâche est-elle reculée lors de la reprise?
BunkersAndPiles_FaceRemoveRate=Vitesse d'avancement quotidien de la façade?
BunkersAndPiles_LooseOrFacedFeedIsFed=En combien de temps l'ensilage détassé est distribué?
BunkersAndPiles_PackingInitialSpreadLayers=L'ensilage est-il tassé par couches inférieurs à 15 cm (6 pouces) à la fois?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Le ratio surface/élévation de la pente de l'amas ou du bunker est-il supérieur à 3\:1.
BunkersAndPiles_PorosityScoresConsistently=Quelle est la compaction, en respect à la MS?
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Après combien de temps l'ensilage est-il couvert après la fin du tassage?
BunkersAndPiles_SideWallsSealedPlastic=Les murs sont-ils couverts de plastique?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Le front d'attaque est-il lisse? (aucun signe de couche permettant à l'oxygène de passer)
BunkersAndPiles_TiresSplitsTouching=Les pneus/boudins se touchent-ils?
Burkina_Faso=Burkina Faso
Burundi=Burundi
CAD=Canada (CA$ CAD)
CFNChina=Chine
CFNIndia=Inde
CHF=Suisse (CHF CHF)
CLF=CLF
CLP=Chili ($ CLP)
CNY=Chine (CNY CNY)
COP=COP
CPNBrazil=Bresil
CPNFrance=France
CPNPoland=Pologne
CPNUS=Les États-unis d'Amérique
CRC=CRC
CZK=République Tchèque (CZK CZK)
Cagliari=Cagliari
Calf=Veau
CalfHeiferColostrum=Évaluation Veaux et Génisses - Colostrum 
CalfHeiferGrowerPuberty=Évaluation - Croissance, Puberté, Gestation et Préparation
CalfHeiferKeyBenchmarks=Évaluation comparative 
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=La couleur de la section est determiniée par\: &lt; 75% rouge, entre 75 et 90% orange, &gt;\=90% vert
CalfHeiferPostweaned=Évaluation Veaux et Génisses - Post-sevrage 
CalfHeiferPreweaned=Évaluation Veaux et Génisses - Pré sevrage
CalfHeiferQuestionViewModel.Close=Fermer
CalfHeiferQuestionViewModel.Colostrum=Colostrum
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Préparation
CalfHeiferQuestionViewModel.KeyBenchmarks=Comparaison des données clées 
CalfHeiferQuestionViewModel.Postweaned=Post-Sevrage
CalfHeiferQuestionViewModel.Preweaned=Pre-Sevrage
CalfHeiferQuestionViewModel.Resources=Ressources
CalfHeiferQuestionViewModel.VisitNotebook=Cahier de notes
CalfHeiferResources=Évaluation Veaux et Génisses - Ressources
CalfHeiferResults=Évaluation Veaux et Génisses - Résultats
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Évaluation Veaux et Génisses 
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Préparation
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Inclu dans la fiche d'évaluation globale  
CalfHeiferScoreCardScoreViewModel.PhaseOne=Colostrum
CalfHeiferScoreCardScoreViewModel.PhaseThree=Post-Sevrage
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Pré-Sevrage
CalfHeiferScorecardImprovementViewModel.Colostrum=Colostrum
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Préparation
CalfHeiferScorecardImprovementViewModel.Postweaned=Post-Sevrage
CalfHeiferScorecardImprovementViewModel.Preweaned=Pré-Sevrage
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Cliquer plus bas pour voir les catégories des résutlats par stade de croissance
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=La couleur de la section est determiniée par\: égale  100%\: vert, &lt; 100%\: rouge
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Registres
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Comparaison des données clées 
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Stade 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Stade 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Stade 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Stade 7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Stade 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseThree=Stade 3
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwo=Stade 2
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Stade 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Maintenir des registres de croissance et de santé précis
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Gestation 15 - 23 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Puberté 9 - 15 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Colostrum 1 - 3 jours
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Préparation / Production 23 - 26 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Croissance 3 - 9 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Pré/Post sevrage 0 - 3 mois
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Cahier de notes
CalfHeiferScorecardLanding=Évaluation Veaux et Génisses
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Comparaison des données clées 
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Amélioration
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Réponses
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Évaluation 
CalfHeiferScorecardResultsViewModel.VisitNotebook=Cahier de notes
CalfHeiferScorecardViewModel.Colostrum=Colostrum
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Préparation
CalfHeiferScorecardViewModel.KeyBenchmarks=Comparaison des données clées 
CalfHeiferScorecardViewModel.Postweaned=Post-Sevrage
CalfHeiferScorecardViewModel.Preweaned=Pré-Sevrage
CalfHeiferScorecardViewModel.Resources=Ressources
CalfHeiferScorecardViewModel.Title=Fiche d'évaluation
CalfHeiferScorecardViewModel.VisitNotebook=Cahier de notes
CalfHeiferTools=Outils pour Veaux et Génisses
CalfHeiferToolsViewModel.CalfHeiferScorecard=Fiche d'évaluation
CalfHeiferToolsViewModel.CalfHeiferTools=Outils pour Veaux et Génisses
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Outils  
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=SVP selectioner l'outil ci-bas pour débuter la visite
CalfHeiferToolsViewModel.CalfHeiferToolsList=Outils
CalfHeiferToolsViewModel.Title=Outils pour Veaux et Génisses
CalfHeiferToolsViewModel.VisitNotebook=Cahier de notes
California=Californie
Caltanissetta=Caltanissetta
Cambodia=Cambodge
Cameroon=Cameroun
Campeche=Campche
Campobasso=Campobasso
Canada=Canada
Capacity=CAPACITÉ
Cape_Verde=Cape verte
Carbonia-Iglesias=Carbonia-iglesias
Cargill=Cargill
CargillForageLabKPTest=Test KP du Laboratoire Cargill pour fourrages 
Carlow=Carlow
Caserta=Caserta
Catania=Catane
Catanzaro=Catanzaro
Category1=Évaluation des fumiers - 1.0
Category2=Évaluation des fumiers - 2.0
Category3=Évaluation des fumiers - 3.0
Category4=Évaluation des fumiers - 4.0
Category5=Évaluation des fumiers - 5.0
Cavan=Cavan
Cayman_Islands=Ãles CaÃ¯mans
CearÃ¡=CarrÃ©
Central_African_Republic=RÃ©publique centrafricaine
Chad=Tchad
Chandigarh=Chandigarh
ChewsPerCud=Mastications par bolus
ChewsPerCudMasterViewModel.AddNew=Ajouter nouveau
ChewsPerCudMasterViewModel.CudChewingInputs=Données
ChewsPerCudMasterViewModel.CudChewingResults=Résultats
ChewsPerCudMasterViewModel.NumOfChews=\# de coups de machoires
ChewsPerCudMasterViewModel.Title={0} / \# Mastication
Chhattisgarh=Chhattisgarh
Chiapas=Chiapas
Chieti=Chieti
Chihuahua=Chihuahua
Chile=Chili
China=Chine
Chinese_Taipei=Taipei chinois
Chongqing=Chongqing
ChooseAppPDF=Choisissez une application pour voir le PDF
ChoosingtheCorrectAdditive=Choix du bon conservateur
Christmas_Island=L'Ã®le de noÃ«l
Clare=Clare
ClassSubClass=Type animal / sous-classe
Clean=Clean
ClinicalMastitisLosses=Pertes pour mammite clinique
CloseUp=Vaches en préparation
CloseUpDry=Prével.
CloseUpHeifer=Génisse en préparation
Coahuila=Coahuila
Cocos_(Keeling)_Islands=Ãles Cocos (Keeling)
Colima=Colima
Colombia=Colombie
Colorado=Colorado
Colostrum=Colostrum
Colostrum_AmountOfColostrumOrFed=Quantité de colostrum servi 
Colostrum_BrixPercentOfColostrumFed=Brix (%) du colostrum servi 
Colostrum_CleanAndDryCalvingArea=Zone de parturition propre et sèche
Colostrum_CleanAndDryCalvingArea_ToolTip=Utilisez un test de genou humide pour déterminer si la zone est propre et sèche
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Nettoyer et désinfecter le matériel d'alimentation des veaux entre les repas
Colostrum_CleanCalfCartToTransportCalf=Remorque à veau propre pour le transport
Colostrum_HoursTillCalfIsRemovedFromMother=Heure(s) avant que le veau soit séparer de la mère 
Colostrum_HoursTillCalfReceivesColostrum=Heure(s) avant que le veau reçoive le colostrum 
Colostrum_NumberOfCowsInCalvingArea=Nombre de vaches dans la zone de parturition
Colostrum_PasteurizeColostrumBeforeFeeding=Du colostrum pasterisé est servi 
Colostrum_PercentageOfNavelsDippedInSevenPercent=% de nombrils trempés dans 7% d'iode dans la première heure
Colostrum_RefrigeratedColostrumStoredLess=Colostrum réfrigéré et est entreposé en moins de 24 heures 
ComfortToolsViewModel.ComfortHeading=Sélectionnez un outil dans la liste ci-dessous pour débuter votre visite.
ComfortToolsViewModel.ComfortToolsList=OUTILS
ComfortToolsViewModel.ComfortToolsTitle=Outils Confort
ComfortToolsViewModel.HealthHeading=Sélectionnez un outil dans la liste ci-dessous pour débuter votre visite.
ComfortToolsViewModel.HeatstressEvaluationTitle=Evaluation du stress thermique
ComfortToolsViewModel.PenTimeTitle=Evaluation du temps de repos
ComfortToolsViewModel.Title=Outils de confort
ComfortToolsViewModel.VisitNotebook=Cahier de notes
Comments=Commentaires
CommonSpinnerViewModel.BodyConditionPDF=Guide d'évaluation de l'état corporel
CommonSpinnerViewModel.InadequateStimulation=Stimulation inadéquate
CommonSpinnerViewModel.LocomotionPDF=Guide d'évaluation de la locomotion
CommonSpinnerViewModel.ManureScorePDF=Guide d'évaluation du fumier
CommonSpinnerViewModel.NoStimulation=Aucune stimulation
CommonSpinnerViewModel.OptimalStimulation=Stimulation optimale
Como=Comme
Comoros=Comores
Competitor=Concurrent
CompletedTimeKey=Temps écoulé
Component=Composantes
Compostbarn=Compostbarn
ConcentrateDistribution=Taux de distribution
ConfirmExport=En appuyant sur OK, l'application va charger toutes les pages sélectionnées et va prendre une photo avant de revenir à cet écran.
ConfirmScalePointSwitch=La modification de l’échelle de points réinitialisera toutes les données entrées
ConfirmScorerSwitch=La modification de méthode d'évaluation va réinitialiser toutes les données entrées.
Congo=Congo
Congo,_the_Democratic_Republic_of_the=Congo, la RÃ©publique dÃ©mocratique du
Connecticut=Connecticut
Consumer=Consommateurs
ConsumerDetailsViewModel.DeleteProspect=Effacer un consommateur
ConsumerDetailsViewModel.DeleteProspectPrompt=Êtes-vous certain de vouloir effacer ce consommateur? Les informations sur le consommateur, le site et les visites seront perdues
ConsumerDetailsViewModel.MainHeading=SITES
ConsumerDetailsViewModel.NewSite=Ajouter un nouveau site
ConsumerDetailsViewModel.ProspectTitle=Détails du consommateur
ConsumersViewModel.NewConsumer=Ajouter un nouveau consommateur
Continue=Continuer
Cook_Islands=les Ãles Cook
Cork=LiÃ¨ge
Corn=ensilage de maïs
CornSilage=Ensilage de maïs
CornSilageKernel=FAQs Balles enrobées
CornSilageResources=Ressources sur l'ensilage de maïs
CornSilageStopGo=Liste de vérifications d'un bon ensilage de maïs
Cosenza=Cosenza
Costa_Rica=Costa Rica
Costs=COÛTS
Cote_d'Ivoire=Cote d'Ivoire
Count=Compte
Cow=Vache 
CowEfficiency=Efficacité des vaches
CowPerRobot=Nombre de vaches par robot
CowsOutsideTargetRangeToolTip=Le but est d'avoir &lt; 20% des vaches qui n'atteignent pas l'objectif
CowsPerDayNeeded=VACHES/JOUR NÉCESSAIRES
CowsSectionToolTip=Un groupe de 8 vaches ou plus devrait être testé pour tirer des conclusions. Dans les petits troupeaux, il convient de tester toutes les vaches fraîches
CowsToBeFed=Nombre d'animaux alimentés
CreateDuplicateDiet=Une ration existe déjà pour ce type d'animal. Créer un autre?
CreateDuplicateNameDiet=Une ration sous le même nom existe déjà. Veuillez entrer un nouveau nom
Created=Créé
Cremona=Cremona
Croatia=Croatie
CropCharacteristicsDecisionGuide=Guide de décisions des cultures (stade de récolte, type de conservateur et  de stockage)
Crotone=Crotone
Cuba=Cuba
CudChewingAverageNumber=Nombre moyen
CudChewingDataEntryViewModel.CudChewing=Rumination
CudChewingDataEntryViewModel.HerdCudChewingDescription=Pour cette visite, comptez le nombre d'animaux qui ruminent  dans ce groupe en utilisant le compteur ci-dessous. Vous devez compter au moins 10 vaches.
CudChewingDataEntryViewModel.No=Non
CudChewingDataEntryViewModel.Yes=Oui
CudChewingHerdEditScoreViewModel.AverageChewsItem=Nombre moyen de mastications par cycle de rumination
CudChewingHerdEditScoreViewModel.Close=Fermer
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Jours de lactation
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Modifier le score pour la rumination
CudChewingHerdEditScoreViewModel.EditScoreTitle=Modifier le score de comptage de coups de machoire
CudChewingHerdEditScoreViewModel.PercentChewingItem=Pourcentage des vaches qui ruminent
CudChewingMasterViewModel.CudChewing=Rumination
CudChewingMasterViewModel.CudChewingInputs=Données
CudChewingMasterViewModel.CudChewingResults=Résultats
CudChewingPen=Groupe
CudChewingPercentChewing=% Animaux qui ruminent - % Rumination
CudChewingPercentGoal={0}% des objectifs
CudChewingPercentOfPen=Rumination (% du groupe)
CudChewingViewModel.CudChewing=Groupes
CudChewingViewModel.CudChewingList=Groupes (lactation et taries)
CudChewingViewModel.CudChewingTitle=Nom du groupe
CudChewingViewModel.Title=Groupes
CudChewsCalculatorViewModel.CalculatorHeading=Sélectionner une vache pour compter le nombre de mastications par cycle. Sélectionnez 'Ajouter nouveau' pour ajouter une vache.
CudChewsCalculatorViewModel.CudChewCategorySection=Vaches
CudChewsCalculatorViewModel.NumOfChews=\# Mastication
CudChewsDatesForComparisonViewModel.CudChewsPercent=Rumination,%
Cundinamarca=Cundinamarca
Cuneo=Coin
CuraÃ§ao=CuraÃ£Ã§ao
Current=Actuel
CurrentDownResponse=Relâchement du lait actuel ({0}/vache/jour)
CurrentMilkPrice=Prix du lait actuel ({0}/{1})
CurrentSCC=CCS actuel (cellules/ml)
CurrentVisitSummary=Sommaire de visite actuelle
Customer=Client
CustomerDetailViewModel.CustomerTitle=Profil du client
CustomerDetailViewModel.MainHeading=SITES
CustomerDetailViewModel.NewSite=Ajouter un nouveau site
CustomerDetailViewModel.NewVisit=Nouvelle Visite
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=Utilisateur final
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Sélectionnez un segment
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Title=Détails
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=Nom du Client - Nom du Site
Cyprus=Chypre
CzechRepublic=République Tchèque
Czech_Republic=RÃ©publique tchÃ¨que
DDW=Données de la ferme
DDWOfflineMessage=Comme il n'y a pas de réseau, aimeriez-vous voir le rapport hors ligne?
DDWUpdatedTime=Dernière mise à jour de données\:
DKK=DKK
DZD=Algérie (DA DZD)
Dadra_and_Nagar_Haveli=Dadra et Nagar Haveli
Daily=Journalier
DairyEnteligenFarmReportsources=Source d’information - Rapport Dairy Enteligen
Daman_and_Diu=Daman et Diu
DashboardViewModel.Alert=Alerte\!
DashboardViewModel.AlertMessage=Les données n'ont pas été synchronisées depuis plus de {0} jours.
DashboardViewModel.GoodAfternoon=Bon après-midi,
DashboardViewModel.GoodMorning=Bonjour,
DashboardViewModel.MessageBody=Rappel\: Utilisez les notes libres à chaque visite du site pour reporter les observations particulières.
DashboardViewModel.MessageHeader=Message
DashboardViewModel.RecentSiteVisit=Visites récentes du site
DashboardViewModel.UserPreferences=Préférences de l'utilisateur
Date=Date
DateGone=Date de fin
DatesForComparison=Dates de comparaison
Days=Jours
DaysInMilkItem=Jours de lactation
DeLaval=DeLaval
DeathLoss=Mortalité
DecidingSilageStorage=choix du type d'entreposage de l'ensilage
Delaware=Delaware
Delete=Supprimer
DeleteMatrixValue=Êtes-vous sûr de vouloir supprimer cette valeur de la grille de paiement?
Delhi=Delhi
Denmark=Danemark
DensityLossesinPressedBagSilos=Densité et pertes dans les ag-bags
Diet=Ration
DietDCAD=Ration BACA mEq/100g
DietDetailViewModel.Created=Créé
DietDetailViewModel.DDW=Données de la ferme
DietDetailViewModel.Max=MAX/OptiLac
DietDetailViewModel.SystemGenerated=Système généré
DietDetailViewModel.Title=Détail de la ration
DietDetailViewModel.UserCreated=Par utilisateur
DietListViewModel.InfoNewDiet=Les noms des rations seront mis à jour automatiquement si MAX est connecté au site de la ferme dans Dairy Enteligen. Sinon, ajoutez manuellement les rations ou laissez
DietListViewModel.MainHeading=Rations
DietListViewModel.New=Nouveau
DietListViewModel.NewDiet=Ajouter une ration
DietListViewModel.Title=Rations
Dirty=Dirty
DisplacedAbomasum=Caillette
District_of_Columbia=District de Colombie
Distrito_Federal=District fÃ©dÃ©ral
Djibouti=Djibouti
DoNotTest=&lt; 20% ou ne teste pas
Dominica=Dominique
Dominican_Republic=RÃ©publique dominicaine
Donegal=Donegal
DontKnow=Ne sais pas
DownResponse=Relâchement du lait ({0} / vache / jour)
Dry=Tarie
DryCow=Vache tarie
DryLot=Groupe Tarie
Dryhay=Foin sec
Dublin=Dublin
DuplicatePenName=Un groupe existe déjà avec ce nom. Choisissez un autre nom.
Durango=Durango
Dystocia=Dystocie
EGP=EGP
EarlyLactation=Début lact.
Ecuador=Equateur
Edit=Modifier
EditDatesForComparison=Modifier les dates de comparaison
EditDatesForComparisonViewModel.Chews=Mastication
EditDatesForComparisonViewModel.EditDatesClose=Fermer
EditDatesForComparisonViewModel.EditDatesLabel=Sélectionnez les dates pour la comparaison de ce groupe dans la liste ci-dessous.
EditDatesForComparisonViewModel.EditDatesTitle=Modifier les dates de comparaison
EditDatesForComparisonViewModel.EditDatesVisits=Visites
EditDatesForComparisonViewModel.LocomotionScoreAverage=Score moyen de la locomotion\:
EditDatesForComparisonViewModel.ManureScoreAverage=Score de fumier moyen
EditDatesForComparisonViewModel.MetabolicIncidence=Veuillez sélectionner jusqu'à 5 dates de visite dans la liste ci-dessous à fin de comparaison
EditDatesForComparisonViewModel.PenTimeBudget=Veuillez sélectionner jusqu'à 7 dates de visite dans la liste ci-dessous à fin de comparaison
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Veuillez sélectionner une date ci-dessous pour comparer avec la visite en cours.
EditDatesForComparisonViewModel.TimeRemainingForResting=Temps restant pour le repos\:
EditDatesForComparisonViewModel.Title=Modifier les dates de comparaison
EditDatesForComparisonViewModel.Visits=Visites
EditGoalsCudChewingViewModel.Close=Fermer
EditGoalsCudChewingViewModel.CloseUpDry=VACHES EN PRÉPARATION
EditGoalsCudChewingViewModel.CudChews=Nombre moyen de mastications par cycle de rumination
EditGoalsCudChewingViewModel.EarlyLactation=DÉBUT DE LACTATION
EditGoalsCudChewingViewModel.EditGoalsTitle=Modifier les objectifs pour la rumination
EditGoalsCudChewingViewModel.FarOffDry=VACHES TARIES
EditGoalsCudChewingViewModel.Fresh=FRAÎCHES VÊLÉES
EditGoalsCudChewingViewModel.LateLactation=FIN DE LACTATION 
EditGoalsCudChewingViewModel.MidLactation=MI-LACTATION
EditGoalsCudChewingViewModel.PeakMilk=PIC DE LACTATION
EditGoalsCudChewingViewModel.PercentChewing=Pourcentage des vaches qui ruminent
EditNoteViewModel.Action=Action
EditNoteViewModel.Cancel=Annuler
EditNoteViewModel.Category=Catégorie
EditNoteViewModel.Close=Fermer
EditNoteViewModel.CreatedByMetadata=Créé le (0)
EditNoteViewModel.Delete=Supprimer
EditNoteViewModel.DeleteImageButtonText=Supprimer l'image
EditNoteViewModel.DeleteImagePrompt=Voulez-vous supprimer cette image?
EditNoteViewModel.DeletePrompt=Voulez-vous supprimer cette note?
EditNoteViewModel.DeleteVideoButtonText=Supprimer la vidéo
EditNoteViewModel.DeleteVideoPrompt=Voulez-vous supprimer cette vidéo?
EditNoteViewModel.Event=Événement
EditNoteViewModel.LastUpdatedByMetadata=Modifier le (0)
EditNoteViewModel.NoteCamcorderNotImplemented=Fonctionnalité de la caméra n'est pas encore configuré.
EditNoteViewModel.NoteGalleryNotImplemented=La fonctionnalité de la galerie n'est pas encore configuré.
EditNoteViewModel.NoteLabel=Note
EditNoteViewModel.NoteOnlyOneImage=Une seule image est autorisée par note. Supprimer l'image en cours en premier.
EditNoteViewModel.NoteOnlyOneVideo=Une seule vidéo est autorisée par note. Supprimer la vidéo actuelle en premier.
EditNoteViewModel.Observation=Observation
EditNoteViewModel.Save=Sauvegarder
EditNoteViewModel.Task=Tâche
EditNoteViewModel.Title=Note
EditNoteViewModel.TitleLabel=Titre
Egypt=Egypte
El_Salvador=Le sauveur
EmailReportViewModel.AnimalImpact=Impact animal
EmailReportViewModel.CalfHeiferItem=Veaux et Génisses
EmailReportViewModel.CalfHeiferScorecard=Fiche d'évaluation
EmailReportViewModel.Capacity=Capacité
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Liste des catégories
EmailReportViewModel.Charts=Graphiques
EmailReportViewModel.CoefficientVariation=Coefficient de variation (C.V.) (%)
EmailReportViewModel.ComfortHeatStressBanner=Outil pour évaluer le stress termique
EmailReportViewModel.ComfortItem=Outil de confort
EmailReportViewModel.ComfortPenTimeBanner=Outil pour évaluer le temps de repos
EmailReportViewModel.ComfortToolsTitle=Outils de confort
EmailReportViewModel.CowsOutsideTargetRange=Vaches on dehors de l'intervalle (%)
EmailReportViewModel.CudChewingTitle=Rumination du bolus
EmailReportViewModel.DietDCADStr=Ration BACA
EmailReportViewModel.EmailBody={0} - {1} Rapport
EmailReportViewModel.EmailSelectedTools=Sélection de l'outil e-mail
EmailReportViewModel.EmailSubject={0}Rapport
EmailReportViewModel.ExportSelected=Envoyer les outils sélectionnés par courriel
EmailReportViewModel.FeedOut=Reprise
EmailReportViewModel.ForageAuditScorecard=Pointage des audits des fourrages
EmailReportViewModel.ForageImprovements=Fiche d'évaluation de l'audit fourrages
EmailReportViewModel.ForageLanding=Page d'Acceuil  Audits des fourrages
EmailReportViewModel.ForageScorecard=Pointage des audits des fourrages
EmailReportViewModel.GeneratingReport=Création du rapport…
EmailReportViewModel.GotoMarketBranding=NOM pour MARKETING
EmailReportViewModel.HealthItem=Outil de santé
EmailReportViewModel.HeatstressEvaluationTitle=Évaluation de stress thermique
EmailReportViewModel.Herd=Troupeau
EmailReportViewModel.HerdAnalysis=Analyse du troupeau
EmailReportViewModel.HerdGoals=Analyse du troupeau - Objectifs
EmailReportViewModel.HerdInputs=Analyse du troupeau - Données
EmailReportViewModel.HerdResults=Analyse du troupeau - Résultats
EmailReportViewModel.HerdRevenue=Analyse du troupeau - Revenu
EmailReportViewModel.Improvements=Améliorations
EmailReportViewModel.Improvements1=Amélioration 
EmailReportViewModel.Inputs=Données
EmailReportViewModel.InputsOutputsChart=Données / Résultats / Graphiques
EmailReportViewModel.LocomotionScoreTitle=Score de locomotion
EmailReportViewModel.ManureScoreTitle=Score de fumier
EmailReportViewModel.MarketBranding=NOM pour MARKETING
EmailReportViewModel.MetabolicIncidenceTitle=Maladies métaboliques
EmailReportViewModel.MilkProcessCalcInputsTab=Comparaison de procédure de traite - Donnnées
EmailReportViewModel.MilkProcessCalcResourcesTab=Comparaison de procédure de traite- Information
EmailReportViewModel.MilkProcessCalcResultsTab=Comparaison de procédure de traite- Résultats
EmailReportViewModel.MilkProcessRevenue=Comparaison de procédure de traite
EmailReportViewModel.MilkProcessRevenueCalculator=Comparaison de procédure de traite
EmailReportViewModel.MilkingTime=Temps de traite
EmailReportViewModel.Notes=Notes
EmailReportViewModel.NumOfChews=Nombre de mastications
EmailReportViewModel.NutritionForage=Audit des fourrages
EmailReportViewModel.NutritionItem=Nutrition
EmailReportViewModel.NutritionPile=Inventaires des fourrages
EmailReportViewModel.Outputs=Résultat
EmailReportViewModel.PenCompare=Comparaison d'analyse par groupe
EmailReportViewModel.PenDensity=Densité du groupe
EmailReportViewModel.PenInputs=Analyse de groupe - Données
EmailReportViewModel.PenResults=Analyse de groupe - Résultats
EmailReportViewModel.PenTimeTitle=Allocation du temps
EmailReportViewModel.PileAndBunkerTitle=Inventaire des fourrages
EmailReportViewModel.PileBunkerCapacities=Capacité des silos
EmailReportViewModel.ProductivityItem=Outil de productivité
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Ressources
EmailReportViewModel.Results=Résultats
EmailReportViewModel.RumenHealthBodyConditionTitle=État corporel
EmailReportViewModel.RumenHealthLocomotionTitle=Score de Locomotion
EmailReportViewModel.RumenHealthManureTitle=Santé du rumen -Score de fumier
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
EmailReportViewModel.RumenHealthTMRTitle=Santé du rumen -Grosseur de particules dans la RTM
EmailReportViewModel.RumenHealthTitle=Santé du rumen - Mastication
EmailReportViewModel.RumenHealthUrinePHTitle=pH urinaire
EmailReportViewModel.ScoreScreen=Scores
EmailReportViewModel.TMRParticleScoreTitle=Grosseur des particules
EmailReportViewModel.TimeBudget=Évaluation du temps de repos
EmailReportViewModel.Title=Rapport par courriel
EmailReportViewModel.UrinePhSTDDEV=Écart type
EmailReportViewModel.UserPreferences=Configuration de l'utilisateur
EmailReportViewModel.UserSettings=Configuration de l'utilisateur
EmailReportViewModel.WalkthroughReportTitle=Rapport de visite
EnergyImperial=Mcal/lb
EnergyMetric=Mcal/kg
Enna=Enna
Equatorial_Guinea=GuinÃ©e Ãquatoriale
Eritrea=ÃrythrÃ©e
ErrorDescription=Une erreur est survenue lors de la lecture ou de l'écriture de vos informations
ErrorTitle=Erreur
EspÃ­rito_Santo=Saint
Estonia=Estonie
Ethiopia=Ethiopie
Eula=Contrat de licence utilisateur final
Euro=Pays membres de l'UE (€ EUR)
Event=Événement
EveryOtherDay=Tous les deux jours
Excessive=&gt;0.5NEC 
ExtraDaysOpenCostInfoMessage=Il varie généralement de 3 $ à 5 $ par jour ouvert supplémentaire.
FAQDairyEnteligenFarmReportandDDW=FAQ (question fréquente) Rapport Dairy Enteligen et Herdnet
Falkland_Islands_(Malvinas)=Ãles Falkland (Malvinas)
FarOff=Taries
FarOffDry=Taries
Faroe_Islands=Ãles FÃ©roÃ©
Federal_District=District fÃ©dÃ©ral
FeedFirst=Alimentation en premier
FeedOut=Reprise
FeedOutRateInfo=TAUX D'ALIMENTAION
FeedOutRatesFilmsStorageSysExamined=Évaluation des taux de reprise, couches de plastique et système d'entreposage
FeedOutSurfaceAreaImperial=Surface du front d'attaque (ft ^ 2)
FeedOutSurfaceAreaMetric=Surface alimenté (m ^ 2)
FeedingRate=Consommation (TQS/Vache)
FeedoutLossesForageStorageSys=Pertes à la reprises selon le système d'entreposage des fourrages
FermentationAnalysisSilageQT=Analyse de la conservation et test qualité fourrage
Fermo=ArrÃªtÃ©
Ferrara=Ferrare
FieldKPTest=Évaluation de l'éclatement du grain au champs
Fiji=Fidji
FillAllFields=SVP remplir tous les champs.
FillAllMandatoryFields=Veuillez remplir tous les champs obligatoires.
FinalObservations=Observations finales
Finish=Terminer
Finland=Finlande
Florence=Florence
Florida=Floride
Foggia=Foggia
ForageAuditScorecard=Fiches repères  pour la vérification des fourrages
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Améliorations de l'audit des fourrages
ForageAuditScorecardResponsesViewModel.ResponsesTab=Réponses des audits des fourrages
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Améliorations
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Réponses
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Résultats
ForageAuditScorecardResultsViewModel.ImprovementsTab=Améliorations
ForageAuditScorecardResultsViewModel.ResponsesTab=Réponses
ForageAuditScorecardResultsViewModel.ScoreTab=Résultats
ForageAuditScorecardResultsViewModel.Title=Silos Tours
ForageAuditScorecardResultsViewModel.VisitNotebook=Voir le Cahier de Notes
ForageAuditScorecardScoreViewModel.GoodIndicator=Bonne
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Améliorations
ForageAuditScorecardScoreViewModel.OverallForageScore=Inclus dans le résultat global des ensilages
ForageAuditScorecardScoreViewModel.Title=Résultat global des audits des fourrages
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Types d'ensilage
ForageAuditViewModel.ForageAuditScorecard=Résultats des audits de fourrages
ForageAuditViewModel.ForageDetail=La qualité des fourrages est le fondement même de tout programme de nutrition laitière est la clé du rendement global de la ferme. Le Tableau de Bord de l'audit des Fourrrages peut être utilisé pour évaluer les pratiques de gestion actuel des fourrages sur la ferme et conseiller des secteurs clés de gestion. Vous pouvez traiter  tous les secteurs lors d'une visite ou choisir seulement ceux qui sont important pour vous lors de votre visite. Des ressources additionnelles de gestion sont disponibles pour vous aider à perfectionner les secteurs où des opportunitées d'amélioration sont présents
ForageAuditViewModel.ForageHeading=Information de base sur le fourrage
ForageAuditViewModel.Resources=Ressources
ForageAuditViewModel.Title=Audit des fourrages
ForageAuditViewModel.VisitNotebook=Voir le carnet de notes
ForageAudit_Sample_ToolTip=Sample text for tool tip. Remove when actual available
ForageManagement_ForagesHarvestedAtProperMaturity=Le stade de récolte de l'ensilage est-il optimal pour le type de culture et d'entreposage?
ForageManagement_ForagesHarvestedAtProperMoisture=Le taux d'humidité de l'ensilage est-il optimal pour son type d'entreposage?
ForageScorecardResultsViewModel.Title=Balle enrobée
ForageScorecardViewModel.Baleage=Balle enrobée
ForageScorecardViewModel.BunkersAndPiles=Silos couloirs et Amas
ForageScorecardViewModel.ForageAuditCategories=Catégories de l'audit fourrages
ForageScorecardViewModel.ForageAuditScore=Score de l'audit fourrages
ForageScorecardViewModel.ForageAuditScorecard=Résultats des audits de fourrages
ForageScorecardViewModel.ForageCategoryTooltip=La qualité des fourrages constitue la base de tous programmes alimentaires des ruminants et est un élément clé de la rentabilité globale de la ferme. Cet outil permet d'évaluer les pratiques actuelles de gestion des fourrages à la ferme et de cibler les points à améliorer.
ForageScorecardViewModel.Harvest=Qualité des fourrages dans la ration
ForageScorecardViewModel.MaintainingForageQuality=Maintenir la qualité des fourrages
ForageScorecardViewModel.No=Non
ForageScorecardViewModel.SilageBags=Ag-bags
ForageScorecardViewModel.SurveyCategories=Résultats des audits des fourrages
ForageScorecardViewModel.SurveyOfForages=Gestion des fourrages
ForageScorecardViewModel.Title=Résultats des audits des fourrages
ForageScorecardViewModel.TowerSilos=Silos tours
ForageScorecardViewModel.ViewOverallForageScore=Voir le résultat global des fourrages
ForageScorecardViewModel.VisitNotebook=Visiter le cahier de notes
ForageScorecardViewModel.Yes=Oui
ForlÃÂ¬-Cesena=ForlÃ£Â¬-cesena
FourScreenNew=4 tamis - nouveau
FourScreenNewType=(4 mm)
FourScreenOld=4 tamis - ancien
FourScreenOldType=(1.18 mm)
FourToSevenDays=4 à 7 jours
France=France
FreeFlow=Libre
FreeFormReportViewModel.Analysis=Analyse
FreeFormReportViewModel.CalfHeiferItem=Veaux et Génisses
FreeFormReportViewModel.CalfHeiferScorecard=Fiche d'évaluation
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Graphiques
FreeFormReportViewModel.ComfortHeatStressBanner=Evaluation stress thermique groupe
FreeFormReportViewModel.ComfortItem=Outil de confort
FreeFormReportViewModel.ExportSelected=Exporter les outils sélectionnés
FreeFormReportViewModel.GeneralNotes=Annotations générales de la visite
FreeFormReportViewModel.HealthItem=Outil de santé
FreeFormReportViewModel.Inputs=Données
FreeFormReportViewModel.KeyBenchmarks=Comparaison des données clées 
FreeFormReportViewModel.MarketingBranding=NOM DE MARKETING
FreeFormReportViewModel.MilkProcessCalcInputsTab=Comparaison de procédure de traite- Données
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Comparaison de procédure de traite- Information
FreeFormReportViewModel.MilkProcessCalcResultsTab=Comparaison de procédure de traite- Résultats
FreeFormReportViewModel.MilkProcessRevenueCalculator=Comparaison de procédure de traite
FreeFormReportViewModel.MilkSoldEvaluation=Évaluation du lait vendu
FreeFormReportViewModel.Notes=Annotations
FreeFormReportViewModel.NutritionForage=Audit des fourrages
FreeFormReportViewModel.NutritionItem=Outil de nutrition
FreeFormReportViewModel.NutritionPile=Inventaires des fourrages
FreeFormReportViewModel.Outputs=Résultats
FreeFormReportViewModel.OverallImprovements=Améliorations globales
FreeFormReportViewModel.OverallResponses=Réponses globales
FreeFormReportViewModel.OverallScore=Résultat global
FreeFormReportViewModel.PenTimeTitle=Temps de repos par groupe
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Taux de reprise des fourrages
FreeFormReportViewModel.ProductivityItem=Outil de productivité
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Résultats
FreeFormReportViewModel.RoboticMilkingEvaluation=Évaluation du robot de traite
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Score de l'état corporel
FreeFormReportViewModel.RumenHealthLocomotionTitle=Score de Locomotion
FreeFormReportViewModel.RumenHealthManureTitle=Santé du rumen - Score de fumier
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Santé du rumen - Grosseurs de particules de la RTM pour le troupeau
FreeFormReportViewModel.RumenHealthTMRTitle=Santé du rumen - Grosseurs de particules de la RTM
FreeFormReportViewModel.RumenHealthTitle=Santé du rumen - mastication
FreeFormReportViewModel.RumenHealthUrinePHTitle=pH urinaire
FreeFormReportViewModel.Title=Rapport version imprimable
FreeFormReportViewModel.Trends=Tendances
FreeFormReportViewModel.VisitTitle=Nom de la visite
FreeFormReportViewModel.WalkThroughNotes=Notes de la visite
FreeHandNoteClearPaletteDialogMessage=Voulez-vous suprimer tout sur l'écran?
FreeHandNoteEditorPageTitle=Notes libres
FreeHandNoteSaveUserDialogMessage=Voulez-vous enregistrer cette note?
FreeHandNotesViewModel.Save=Enregistrer
Freestall=Freestall
French_Guiana=Guyane FranÃ§aise
French_Polynesia=PolynÃ©sie franÃ§aise
French_Southern_Territories=Territoires du Sud franÃ§ais
Fresh=Fraîches
FreshCow=Vache fraiche
FreshHeifer=Génisse fraîche vêlée
Frosinone=Frosinone
Fujian=Fujian
GBP=Royaume-Uni (GBP GBP)
GEA=GEA
GTQ=Guatémala (Q GTQ)
Gabon=Gabon
Galway=Galway
Gambia=Gambie
Gansu=Gansu
Gardez=l'application ouverte pendant que la synchronisation est en cours. La synchronisation peut prendre plus de temps avec des connexions plus lentes. "
General=Général
Genoa=GÃªnes
Georgia=GÃ©orgie
Germany=Allemagne
GettingtheMostOutofYourForage=Tirer le maximum de vos fourrages
Ghana=Ghana
Gibraltar=Gibraltar
Girolando=Girolando
Global=Mondial
Goa=Goa
Goal=Objectif
GoiÃ¡s=GoiÃ£o
Good=Good
Gorizia=Gorizia
GreaterThan8Hours=Plus de 8 heures
GreaterThanFive=Plus de 5 grains entiers
GreaterThanSevenDays=Plus de 7 jours
GreaterThanSixHours=Plus que 6 heures
GreaterThanThirtySixInchesPerDay=Plus de 30 cm (12 pouces) par jour
GreaterThanTwelveHours=Plus de 12 heures
GreaterThanTwenty=&gt;20
Greece=GrÃ¨ce
Greenland=Groenland
Grenada=Grenade
Grosseto=Grosseto
GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Préparation
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Enclos propre et sèche 
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Utilisez le test du genou humide pour déterminer si la zone est propre et sèche
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=État corporel désiré pour atteindre le stade de maturité
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Signe de fumier lousse 
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=Mangeoire est nettoyée à chaque jour et les refus sont enlevés
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Eau propre et  à volonté est acessible 
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=Pas de signe de contaminant dans l'eau
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Groupe de génisses avec une taille uniforme 
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Les animaux dans un groupe devraient être de même taille 
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% de surpeuplement
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Ration balancée pour atteindre les objectifs de croissance et revue fréquemment
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Espace à la mangeoire par génisses est adéquat
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=Besoin de 30 cm (12 po) pour 135-270kg , besoin de 38cm (15 po) pour 270-400kg et besoin de 46 cm (18 po) pour > 400 kg 
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Espace dans l'enclos par génisse est adéquat
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=Besoin de 4m2 (40 pi2) pour 135-270kg, besoin de 5m2 (50 pi2) pour 270-400kg et besoin de 7m2 (70 pi2) pour >400 kg
Guadeloupe=Guadeloupe
Guam=Guam
Guanajuato=Guanajuato
Guangdong=Guangdong
Guangxi=Guangxi
Guatemala=Guatémala
Guernsey=Guernesey
Guerrero=Guerrero
Guinea=GuinÃ©e
Guinea-Bissau=GuinÃ©e-Bissau
Guizhou=Guizhou
Gujarat=Gujarat
Guyana=Guyane
HKD=HKD
HNL=Honduras (HNL HNL)
HRK=HRK
HUF=Hongrie (Ft HUF)
Hainan=Hainan
Haiti=HaÃ¯ti
HalfPointScale=Échelle de demi-point
Harvest=Qualité des fourrages dans la ration
Harvest_AdequateEquipmentAndLabor=Combien de temps prend la récolte? (Travail et équipements adéquats)
Harvest_CornSilageMoistureRangeConsistent=Le taux d'humidité de l'ensilage est-il optimal pour son type d'entreposage?
Harvest_ForageHarvestingDocumented=Les conditions de récoltes d'ensilage, de champs et d'emplacements sont-elles documentées?
Harvest_ForagesHarvestedAtProper=Le stade de récolte de l'ensilage est-il optimal pour le type de culture et d'entreposage?
Harvest_KPScoreIsMonitored=Combien de grains de maïs sont éclatés dans un échantillon d'ensilage de 1L (32 oz)?
Harvest_LengthOfCutMonitored=La longueur de coupe a-t-elle été controlée avec un Penn State à la récolte?
Harvest_UseSilageAdditive=Est-ce qu'un inoculant est utilisé pour la conservation de l'ensilage?
Harvest_WholePlantMoistureDetermined=L'humidité des plants est-elle déterminée pour chaque parcelle?
Haryana=Haryana
Hawaii=Hawaii
Haylage=Ensilage de foin
HealthToolsViewModel.HealthHeading=Sélectionner un outil dans la liste ci-dessous.
HealthToolsViewModel.HealthToolsList=OUTILS
HealthToolsViewModel.RumenHealthBodyConditionTitle=État corporel
HealthToolsViewModel.RumenHealthLocomotionTitle=Score de locomotion
HealthToolsViewModel.RumenHealthManureScreening=Tamis à fumier
HealthToolsViewModel.RumenHealthManureTitle=Santé Ruminale- score de fumier
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
HealthToolsViewModel.RumenHealthTMRTitle=Evaluation - Grosseur de particules dans la ration mélangée
HealthToolsViewModel.RumenHealthTitle=Santé Ruminale- rumination
HealthToolsViewModel.RumenHealthUrinePHTitle=pH urinaire
HealthToolsViewModel.Title=Outils de santé
Heard_Island_and_McDonald_Islands=Entendus Ã®les et Ã®les McDonald
HeatstressCalculations=Calculs\: stress thermique
HeatstressChart=Graphique Stress thermique
HeatstressChartViewModel.DMIReduction=Baisse de la MSI
HeatstressChartViewModel.EnergyEquivMilkLoss=Perte de lait potentielle (lait corrigé de l'énergie)
HeatstressChartViewModel.EstimateDryMatter=Estimation de la MSI
HeatstressChartViewModel.HeatstressEvalLabel=Température ajustée pour la température et l'humidité moyenne (non ensoleillé)
HeatstressChartViewModel.IntakeAdjustment=Ajustement de la consommation
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=Perte d'énergie consommée
HeatstressChartViewModel.Mcal=Mcal
HeatstressChartViewModel.MilkValueLossPerDay=Valeur de perte de lait/jour
HeatstressChartViewModel.MilkValueLossPerMonth=Valeur de perte de lait/mois
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.ReductionDMI=Réduction de la MSI
HeatstressChartViewModel.TempHumidIndex=Index température humidité
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Cahier de notes
HeatstressData=Données\: stress thermique
HeatstressDataEntryViewModel.AnimalInputs=Données des animaux
HeatstressDataEntryViewModel.CurrentMilkPrice=Prix du lait actuel ({0}/{1})
HeatstressDataEntryViewModel.DMI=Matière sèche ingérée en KG
HeatstressDataEntryViewModel.Exposure=Exposition
HeatstressDataEntryViewModel.HoursExposed=Heures d'ensoleillement
HeatstressDataEntryViewModel.Humidity=Humidité (%)
HeatstressDataEntryViewModel.LactatingAnimals=Vaches en lactation
HeatstressDataEntryViewModel.Milk=Lait({0})
HeatstressDataEntryViewModel.MilkFat=Matière Grasse %
HeatstressDataEntryViewModel.MilkProtein=Protéine %
HeatstressDataEntryViewModel.NEL=Densité énergétique de la ration (en UFL)
HeatstressDataEntryViewModel.Temperature=Température ({0})
HeatstressDataEntryViewModel.VisitNotebook=Cahier de notes
HeatstressDataEntryViewModel.Weather=Météo
HeatstressGreen=Seuil de stress
HeatstressOrange=stress modéré-sévère
HeatstressRed=Stress sévère
HeatstressTableViewModel.HeatstressChartTab=Graphiques
HeatstressTableViewModel.HeatstressDataTab=Entrée de données
HeatstressTableViewModel.Title=Evaluation du stress thermique
HeatstressTableViewModel.VisitNotebook=Cahier de notes
HeatstressYellow=stress léger-modéré
Hebei=Hebei
Heifer=Génisse
Heilongjiang=Heilongjiang
Henan=Henan
HerdAnalysisGoalsViewModel.CloseUpDry=Vaches en préparation
HerdAnalysisGoalsViewModel.CudChewingGoals=Objectifs pour mastication du bolus
HerdAnalysisGoalsViewModel.CudChews=Coups de machoire
HerdAnalysisGoalsViewModel.DIM=Jours en lait (JEL)
HerdAnalysisGoalsViewModel.EarlyLactation=début de lactation
HerdAnalysisGoalsViewModel.FarOffDry=Vaches taries
HerdAnalysisGoalsViewModel.Fresh=fraîches vêlées
HerdAnalysisGoalsViewModel.LateLactation=fin de lactation
HerdAnalysisGoalsViewModel.MidLactation=mi-lactation
HerdAnalysisGoalsViewModel.PeakMilk=pic de lactation
HerdAnalysisGoalsViewModel.PercentChewing=%vache qui ruminent
HerdAnalysisGoalsViewModel.Title=Analyse du troupeau
HerdAnalysisGoalsViewModel.To=à
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Santé Ruminale - Rumination
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Analyse du troupeau
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Analyse du troupeau
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Objectifs
HerdAnalysisMasterViewModel.Title=Santé Ruminale - Rumination
HerdAnalysisTableTitle=Analyse du résultat des ruminations
HerdAnalysisViewModel.AverageChews=Nombre moyen de mastication par cycle de rumination
HerdAnalysisViewModel.DaysInMilk=Jours en lait (JEL)
HerdAnalysisViewModel.Edit=Modifier
HerdAnalysisViewModel.EditLabel=Modifier
HerdAnalysisViewModel.HerdAnalysisTableTitle=Analyse du résultat des ruminations
HerdAnalysisViewModel.HerdCudChewing=Pourcentage du troupeau qui rumine
HerdAnalysisViewModel.NoOfCows=Compléter l'analyse des groupes pour tous les groupes que vous avez commencé.
HerdAnalysisViewModel.NumberofChewsPerCud=Nombre de mastication par cycle de rumination
HerdAnalysisViewModel.PenNameLabel=Nom du groupe
HerdAnalysisViewModel.PercentChewing=Pourcentage de vaches qui ruminent
HerdAnalysisViewModel.TableTitle=Analyse du résultat des ruminations
HerdAverage=Moyenne pour le troupeau (%)
HerdGoal=Objetif pour le troupeau (%)
HerdInformation=Données du troupeau
HerdReporting=Rapport sur le troupeau\: rumination
Hidalgo=Hidalgo
Himachal_Pradesh=Himachal Pradesh
Hokkaido=Hokkaido
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=Saint-SiÃ¨ge (Ãtat de la ville du Vatican)
HomeViewModel.AutoSync=Synchronisation automatique
HomeViewModel.ConsumersTab=Consommateurs
HomeViewModel.CustomersTab=Clients
HomeViewModel.DashboardTab=Tableau de bord
HomeViewModel.Eula=Contrat de licence utilisateur final
HomeViewModel.Logout=Déconnexion
HomeViewModel.PrivacyStatement=Déclaration de confidentialité
HomeViewModel.ProspectsTab=Prospects
HomeViewModel.Settings=Paramètres
HomeViewModel.SyncWithDash=Synchroniser
HomeViewModel.SyncWithDate=Synchroniser- Dernière date de sync\: JJ MM AA
HomeViewModel.SyncWithTime=Synchroniser- Dernière date de sync\: JJ MM AA
HomeViewModel.Title=Outils de vente
Honduras=Honduras
Hong_Kong=Hong Kong
HowtoGetBetterKPResults=comment améliorer l'éclatement du grain?
Hubei=Hubei
Hunan=Soi
Hungary=Hongrie
IDR=Indonésie (Rp IDR)
INR=Inde (INR INR)
Iceland=Islande
Idaho=Idaho
Illinois=Illinois
Imperia=ImpÃ©ria
Imperial=Impérial
Improvements=Améliorations
India=Inde
Indiana=Indiana
Indonesia=Indonésie
InoculantFQAs=Inoculant de conservation FAQs
Iowa=Iowa
Iran,_Islamic_Republic_of=Iran (RÃ©publique islamique d
Iraq=Irak
Ireland=Irlande
Isernia=Isernia
Isle_of_Man=Ã®le de Man
Israel=IsraÃ«l
Italy=Italie
JOD=JOD
JPY=JPY
Jalisco=Jalisco
Jamaica=JamaÃ¯que
Jammu_and_Kashmir=Jammu et Cachemire
Japan=Japon
Jersey=Jersey
Jharkhand=Jharkhand
Jiangsu=Jiangsu
Jiangxi=Jiangxi
Jilin=Jilin
Jordan=Jordan
KBLastPhase=Tenir des registres précis de croissance et de santé
KRW=Corée du Sud (₩ KRW)
Kansas=Kansas
Karnataka=Karnataka
Kazakhstan=Kazakhstan
Kentucky=Kentucky
Kenya=Kenya
Kerala=Kerala
Kerry=Kerry
Ketosis=Acétonémie
KeyBenchmarks=Comparaison des données clées 
KeyBenchmarks_AgeInMonthAtFirstCalving=Âge (en mois) au premier vêlage
KeyBenchmarks_CalvingAndHeiferReocrd=Registre des vêlages et des génisses utilisé
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=Atteint 15% du poids mature à 90 jours
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=Atteint 55% du poids mature à la gestation
KeyBenchmarks_HeiferPeakProduce=Pics de lait des génisses en % de la moyenne du troupeau 
KeyBenchmarks_NintyDaysMorbidityf=Morbidité à 90 jours
KeyBenchmarks_NintyDaysMortality=Mortalité à 90 jours 
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=Atteint 94% du poids mature au vêlage
KeyBenchmarks_PercentOfHeifersPregnant=% des génisses pleines à 15 mois
KeyBenchmarks_SerumlgG=Serum IgG (g/L) à 48 heures
Kildare=Kildare
Kilkenny=Kilkenny
Kiribati=Kiribati
Korea=Corée
Korea,_Democratic_People's_Republic_of=RÃ©publique populaire dÃ©mocratique de CorÃ©e
Korea,_Republic_of=CorÃ©e, RÃ©publique de
Kuwait=Koweit
Kyrgyzstan=Kirghizistan
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=Pimenter
Lactating=Lactation
Lactation=Lactation
Lakshadweep=Lakshadweep
Lao_People's_Democratic_Republic=RÃ©publique dÃ©mocratique populaire lao
Laois=Laois
Last_Synced=Synchroniser
LateLactation=Fin Lact.
Latina=Latina
Latvia=Lettonie
Lebanon=Liban
Lecce=Licce
Lecco=LECCO
Leitrim=Leitrim
Lely=Lely
Length-exceed-allowed-limit={0} longueur dépasse du total des caractères autorisés {0}
LengthPerDayImperial=pouces par jour
LengthPerDayMetric=cm par jour
Les=données de configuration de l'enclos seront mises à jour automatiquement pour les sites avec des téléchargements Dairy Enteligen.
Lesotho=Lesotho
LessThan4Days=Moins de 4 jours
LessThanFifteen=&lt;15 (dureté du grain)
LessThanFiveWholeKernals=Moins de 5 grains entiers
LessThanOneHour=Moins de 1 heure
LessThanSixInches=Moins de 7.5 cm (3 pouces)
LessThanSixLayers=Moins de 6 couches
LessThanTwentFourInchesPerDay=moins de 60 cm par jour
Liaoning=Liaon
Liberia=LibÃ©ria
Libyan_Arab_Jamahiriya=Jamahiriya arabe libyen
Liechtenstein=Liechtenstein
Lift-Sync-Fail=La synchronisation a  chou  pour des raisons inconnues, veuillez contacter l'administrateur pour r solution dans les coordonn es
Limerick=Limerick
LinkToPens=Liaison avec le lot  (optionnel)
Lithuania=Lituanie
Livorno=Viveno
LocoCategory1=1.0
LocoCategory2=2.0
LocoCategory3=3.0
LocoCategory4=4.0
LocoCategory5=5.0
LocomotionEditTableViewModel.Category1=Score de Locomotion Catégorie 1.0
LocomotionEditTableViewModel.Category2=Scode de Locomotion Catégorie 2.0
LocomotionEditTableViewModel.Category3=Scode de Locomotion Catégorie 3.0
LocomotionEditTableViewModel.Category4=Scode de Locomotion Catégorie 4.0
LocomotionEditTableViewModel.Category5=Scode de Locomotion Catégorie 5.0
LocomotionEditTableViewModel.EnterNumberOfCows=Veuillez compter le nombre de vaches.
LocomotionEditTableViewModel.Title=Nombre de vaches
LocomotionHerdEditGoalViewModel.Category1=Catégorie 1.0
LocomotionHerdEditGoalViewModel.Category2=Catégorie 2.0
LocomotionHerdEditGoalViewModel.Category3=Catégorie 3.0
LocomotionHerdEditGoalViewModel.Category4=Catégorie 4.0
LocomotionHerdEditGoalViewModel.Category5=Catégorie 5.0
LocomotionHerdEditGoalViewModel.HerdGoal=OBJECTIF DE TROUPEAU
LocomotionHerdEditGoalViewModel.Title=Modifier les objectifs
LocomotionHerdInputsViewModel.Herd=TROUPEAU
LocomotionHerdMasterViewModel.Inputs=Données
LocomotionHerdMasterViewModel.Results=Résultats
LocomotionHerdMasterViewModel.Revenue=Revenu
LocomotionHerdMasterViewModel.SubHeading=Évaluation de troupeau
LocomotionHerdMasterViewModel.Title=Locomotion
LocomotionHerdResultsViewModel.Category1=Catégorie 1.0
LocomotionHerdResultsViewModel.Category2=Catégorie 2.0
LocomotionHerdResultsViewModel.Category3=Catégorie 3.0
LocomotionHerdResultsViewModel.Category4=Catégorie 4.0
LocomotionHerdResultsViewModel.Category5=Catégorie 5.0
LocomotionHerdResultsViewModel.HerdAverage=Moyenne pour le troupeau
LocomotionHerdResultsViewModel.HerdGoal=Objectif
LocomotionHerdResultsViewModel.Title=Analyse de score de locomotion
LocomotionHerdRevenueViewModel.Revenue=Revenu
LocomotionHerdRevenueViewModel.Title=Locomotion de troupeau - Revenu
LocomotionNumberinHerd=Locomotion (\# dans le troupeau)
LocomotionNumberinPen=Locomotion (\# dans le groupe)
LocomotionPenInputsViewModel.FromPenSetup=Organisation par groupe
LocomotionPenInputsViewModel.Milk=LAIT
LocomotionPenMasterViewModel.Inputs=Données
LocomotionPenMasterViewModel.Results=Résultats
LocomotionPenMasterViewModel.Title=Locomotion
LocomotionPercentofHerd=Locomotion (% du troupeau)
LocomotionPercentofPen=Locomotion (% de l’enclos)
LocomotionPreviousVisitsViewModel.AverageScore=Score moyen
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Score moyen
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Date
LocomotionPreviousVisitsViewModel.PercentPen=Pourcentage du groupe (%)
LocomotionPreviousVisitsViewModel.SelectedDates=Sélectionnez les dates
LocomotionPreviousVisitsViewModel.Title=Résultats de score de locomotion
LocomotionScore=Évaluation de la locomotion
LocomotionScoreAverage=Score de locomotion moyen
LocomotionScoreHerd=Analyse de score de locomotion
LocomotionScoreReference=Score de Locomotion Référence
LocomotionSelectPenViewModel.MissingDiet=S'il vous plaît entrer une ration valide pour ce groupe.
LocomotionSelectPenViewModel.PenSelectionList=GROUPES
LocomotionSelectPenViewModel.Title=Locomotion
Lodi=Contraire
LoginViewModel.Copyright=© {0} Cargill, Incorporé. Tous droits réservés.
LoginViewModel.EmailLabel=Courriel
LoginViewModel.ErrorDescription=Nom d'utilisateur et mot de passe ne peuvent pas être vide
LoginViewModel.ErrorTitle=Erreur d'identifiant
LoginViewModel.InvalidMessage=Le nom d'utilisateur ou le mot de passe fourni n'était pas valide pour se connecter.
LoginViewModel.InvalidMessageTitle=Identifiant invalide
LoginViewModel.LoginPrompt=S'identifier
LoginViewModel.LoginPromptConsumer=Autre connection
LoginViewModel.NetworkErrorMessage=Pas de réseau disponible
LoginViewModel.NetworkErrorMessageTitle=Erreur de réseau
LoginViewModel.PasswordLabel=Mot de Passe
LoginViewModel.Title=Se connecter
LoginViewModel.TitleMsg=Se connecter... Veuillez patienter... 
LoginViewModel.Unauthorized=Accès non-authorisé
LoginViewModel.UnauthorizedTitle=Non-authorisé
Longford=Longford
Lors=de la création d'un nouveau lot, les seuls éléments requis sont le nom du lot, la ration, le système de logement et le système d'alimentation.
Louisiana=Louisiane
Louth=Louth
LowForage=Faible en fourrage
Lucca=Lucca
Luxembourg=Luxembourg
MEQ100G=mEq/100g
MKD=MKD
MUN=Urée du lait (mg/dL)
MXN=Mexique (PESO MXN)
MYR=Malaisie (MYR MYR)
Macao=Macao
Macedonia,_the_former_Yugoslav_Republic_of=MacÃ©doine, ancienne RÃ©publique yougoslave
Macerata=MacÃ©rer
Madagascar=Madagascar
Madhya_Pradesh=Madhya Pradesh
Maharashtra=Maharashtra
MainViewModel.EmailLabel=Courriel
Maine=Maine
MaintainingForageQuality=Maintien de la qualité des fourrages
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Un stabilisateur/inhibiteur est-il utilisé dans la ration durant les saisons chaudes/humides?
MaintainingForageQuality_TMRMixHasPleasantAroma=Le mélange de la RTM a-t-il une odeur agréable?
MaintainingForageQuality_TMRMixIsCoolToTouch=La ration mélangée est-elle fraîche au toucher?
Making_Feed_InventoryFOF=Faire un inventaire des fourrages
Malawi=Flamme
Malaysia=Malaisie
Maldives=Maldives
Male=Mâle
Mali=Devait
Malta=Malte
ManagingForageinSiloBags=Gérer les fourrages dans les ag-bags
ManagingForageinTowerSilos=Gérer les fourrages dans les silos tours
Manipur=Manipur
Manitoba=Manitoba
Mantua=Mantua
ManureEditScores=Évaluation des fumiers  - Modifier le score
ManureScoreHerdAnalysisEditInputsViewModel.Close=Fermer
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Jours en lait (JEL)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Modifier les jours en lait (JEL)
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Score de fumier
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Analyse de Score de fumier
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Jours en lait
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Modifier
ManureScoreHerdAnalysisMasterViewModel.Goals=Objectifs
ManureScoreHerdAnalysisMasterViewModel.Inputs=Données
ManureScoreHerdAnalysisMasterViewModel.Results=Résultats
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Analyse du troupeau
ManureScoreHerdAnalysisMasterViewModel.Title=Score de fumier pour la santé du rumen
ManureScoreHerdAnalysisMasterViewModel.VisitNotebook=Cahier de notes
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Analyse de score de fumier
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Score de fumier
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Moyenne
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Score maximum
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Score minimum
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Préparation vêlage (- 21 et -1)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Début de lactation  (16 -60)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Fermer
ManureScoreHerdEditGoalsViewModel.FarOffDry=Taries (moins que - 21)
ManureScoreHerdEditGoalsViewModel.Fresh=Début de lactation (0 - 15)
ManureScoreHerdEditGoalsViewModel.LateLactation=Fin de lactation (plus que 201)
ManureScoreHerdEditGoalsViewModel.MaxGoal=Consistance de fumier  Objectif maximum
ManureScoreHerdEditGoalsViewModel.MidLactation=Mi-lactation (121 to 200)
ManureScoreHerdEditGoalsViewModel.MinGoal=Consistance de fumier Objetif minimum
ManureScoreHerdEditGoalsViewModel.PeakMilk=Pic de lactation (60 - 120)
ManureScoreHerdEditGoalsViewModel.Title=Modifier les objectifs
ManureScoreHerdGoalsViewModel.CloseUpDry=Préparation vêlage (- 21 et -1)
ManureScoreHerdGoalsViewModel.EarlyLactation=Début de lactation  (16 -60)
ManureScoreHerdGoalsViewModel.Edit=Modifier
ManureScoreHerdGoalsViewModel.FarOffDry=Taries  (moins que - 21)
ManureScoreHerdGoalsViewModel.Fresh=Début de lactation (0 - 15)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Fumier Objectif maximum
ManureScoreHerdGoalsViewModel.GoalMinTitle=Fumier Objetif minimum
ManureScoreHerdGoalsViewModel.LateLactation=Fin de lactation (plus que 201)
ManureScoreHerdGoalsViewModel.MidLactation=Mi-lactation (121 to 200)
ManureScoreHerdGoalsViewModel.PeakMilk=Pic de lactation (60 - 120)
ManureScoreHerdGoalsViewModel.TableTitle=Score par stade de lactation
ManureScorePenSelectionViewModel.ManureScoreTitle=Evaluation du fumier
ManureScorePenSelectionViewModel.PenSelectionList=GROUPES
ManureScorePercentOfPen=Évaluation du Fumier (% du groupe)
ManureScoresChart=Score de fumier - Graphique
ManureScoresResult=Score de fumier - Résultats
MaranhÃ£o=MaranhÃ£ Â£ o
Martinique=Martinique
Maryland=Maryland
Massa_and_Carrara=Massa et Carrara
Massachusetts=Massachusetts
Matera=Matera
Mato_Grosso=Mato Grosso
Mato_Grosso_do_Sul=Mato grosse do sul
Mauritania=Mauritanie
Mauritius=Maurice
Max=MAX
Mayo=Mayo
Mayotte=Mayotte
Mcal=Mcal
Meath=Mine
Medio_Campidano=Medio Campidano
Medium=Medium
Meghalaya=Meghalaya
MenuViewModel.Close=Fermer
MenuViewModel.LogoutPrompt=Déconnexion
MenuViewModel.Menu=Menu
MenuViewModel.ResetDatabaseCancel=Annuler
MenuViewModel.ResetDatabasePrompt=Cette option remplacera toutes les données existantes et les préférences de l'utilisateur, avec un ensemble de données prédéterminées pour le départ. Tous les outils de la visite en cours, les nouvelles visites créées et les laiteries seront perdus. Vous serez également déconnecté de l'application. Continuer? "
MenuViewModel.ResetDatabaseReset=Réinitialiser
MenuViewModel.ResetDatabaseTitle=Réinitialiser le test
MenuViewModel.Sync_PopUp="Synchronisation de données ...
Messina=Messina
MetabolicIncidenceChartsViewModel.Current=Actuel
MetabolicIncidenceChartsViewModel.DeathLoss=Mortalité
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Problèmes métaboliques, coût / vache
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceChartsViewModel.Dystocia=Dystocie
MetabolicIncidenceChartsViewModel.GoalPercent=Objectif (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Maladies métaboliques%
MetabolicIncidenceChartsViewModel.IncidencePercent=Incidence (%)
MetabolicIncidenceChartsViewModel.Ketosis=Acétonémie
MetabolicIncidenceChartsViewModel.Metritis=Métrite
MetabolicIncidenceChartsViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceChartsViewModel.Title=Graphiques d'incidences métaboliques
MetabolicIncidenceEditOutputsViewModel.Close=Fermer
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Perte pour mortalité
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceEditOutputsViewModel.Dystocia=Dystocie
MetabolicIncidenceEditOutputsViewModel.Ketosis=Acétonémie
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Objectif (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Métrite
MetabolicIncidenceEditOutputsViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceEditOutputsViewModel.Title=Modifier les objectifs
MetabolicIncidenceInputsEditViewModel.Close=Fermer
MetabolicIncidenceInputsEditViewModel.DeathLoss=Mortalité
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceInputsEditViewModel.Dystocia=Dystocie
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Augmentation des jours ouverts
MetabolicIncidenceInputsEditViewModel.Ketosis=Acétonémie
MetabolicIncidenceInputsEditViewModel.Metritis=Métrite
MetabolicIncidenceInputsEditViewModel.MilkCow=Lait / Vache ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceInputsEditViewModel.Title=Modifier les attributs de coût
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Coûts de traitement
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Coût par jour ouvert additionnel
MetabolicIncidenceInputsViewModel.Costs=Coûts
MetabolicIncidenceInputsViewModel.DeathLoss=Perte pour mortalite
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceInputsViewModel.Dystocia=Dystocie
MetabolicIncidenceInputsViewModel.Herd=RENSEIGNEMENTS SUR LE TROUPEAU
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Entrez le nombre de vaches fraîches et le nombre de maladies métaboliques pendant la période d'évaluation. Cela sera converti en coût d'incidence annuel sur l'onglet résultats
MetabolicIncidenceInputsViewModel.IncidenceCases=Cas de maladies métaboliques
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Augmentation des jours ouverts
MetabolicIncidenceInputsViewModel.Ketosis=Acétonémie
MetabolicIncidenceInputsViewModel.Mastitis=Mortalité
MetabolicIncidenceInputsViewModel.Metritis=Métrite
MetabolicIncidenceInputsViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceInputsViewModel.MilkLossKg=Perte de lait par Lactation ({0}) 
MetabolicIncidenceInputsViewModel.MilkPrice=Prix du lait
MetabolicIncidenceInputsViewModel.PerformanceMessage=Données de référence utilisées pour calculer l'impact économique de chaque incidence métabolique.
MetabolicIncidenceInputsViewModel.PerformanceTreatment=COÛTS DE PERFORMANCE ET DE TRAITEMENT
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Coût de remplacement
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceInputsViewModel.Title=Données pour incidence métabolique
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Nombre total de vaches
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Nombre de vêlage / Année
MetabolicIncidenceInputsViewModel.TreatmentCost=Coût du traitement
MetabolicIncidenceMasterViewModel.Charts=Graphiques
MetabolicIncidenceMasterViewModel.Inputs=Données
MetabolicIncidenceMasterViewModel.Outputs=Résultats
MetabolicIncidenceMasterViewModel.Title=Maladies métaboliques
MetabolicIncidenceOutputsViewModel.DeathLoss=Perte pour mortalite
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceOutputsViewModel.Dystocia=Dystocie
MetabolicIncidenceOutputsViewModel.Ketosis=Acétonémie
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Incidence (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Coût / Vache
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Augmentation des jours ouverts
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Différence (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Modifier
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Objectif (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Impact économique des maladies métaboliques au-dessus des objectifs du troupeau
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Impact économique annuel
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Impact économique annuel - Total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Valeur de la perte de lait
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Pourcentage de troubles métaboliques
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Coût total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Coût du traitement
MetabolicIncidenceOutputsViewModel.Metritis=Métrite
MetabolicIncidenceOutputsViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceOutputsViewModel.Title=Résultats d'incidence métabolique
MetabolicIncidenceOutputsViewModel.TotalLosses=Pertes annuelles totales
Metric=MÃ©trique
MetricTonsAF=Tonnes métriques TQS
MetricTonsAFSilo=Tonnes métriques TQS (restantes dans le silo)
MetricTonsDM=Tonnes métriques MS
MetricTonsDMSilo=Tonnes métriques MS (restantes dans le silo)
Metritis=Métrite
Mexico=Mexique
Mexico_State=Ãtat du Mexique
Michigan=MICHIGAN
MichoacÃ¡n=MichoacÃ¡n
MidLactation=Mi-lact.
MidOne=Milieu 2
MidOneValue=(8mm)
MidTwo=Milieu 3
Milan=Milan
Milk=Lait ({0})
MilkChange=Changement en production ({0})
MilkFever=Fièvre du lait
MilkLetDownResponse=Réflex du relâchement du lait
MilkLossDay=Perte de lait ({0} / Jour)
MilkLossGain=Perte / gain potentiel, en lait
MilkLossKg=Perte de lait (kg)
MilkLossPounds=Perte de lait (lbs)
MilkLossYear=Perte de lait ({0} / Année)
MilkPrice=Prix du lait ({0}/{1})
MilkPricePremiums=Primes de prix du lait
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=TABLEAU DE RÉFÉRENCE
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Données
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Ressources
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Résultats
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Comparaison de procédure de traite
MilkProcessRevenueCalculatorMasterViewModel.Title=Comparaison de procédure de traite
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Cahier de notes
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Stimulation inadéquate
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Prix du lait  ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=Aucune stimulation
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Stimulation optimale
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Scénario 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Scénario 2
MilkProcessorEditComparisonValuesViewModel.Title=Modifier les valeurs de comparaison
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorInputViewModel.ComparisonValues=Valeurs de comparaison
MilkProcessorInputViewModel.Edit=modifier
MilkProcessorInputViewModel.MilkPrice=Prix du lait  ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=La laiterie sélectionnée avant a été supprimé. Sélectionner une autre laiterie  pour continuer.
MilkProcessorInputViewModel.ScenarioOne=Scénario 1
MilkProcessorInputViewModel.ScenarioTwo=Scénario 2
MilkProcessorInputViewModel.SelectProcessor=Sélectionner la laiterie
MilkProcessorInputViewModel.Title=Comparaison de procédure de traite-Données
MilkProcessorInputViewModel.WeightImperialCWT=CWT
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorResourcesViewModel.ApproxSCC=CCS approximatif (cellules / ml)
MilkProcessorResourcesViewModel.LinearScore=Score linéaire
MilkProcessorResourcesViewModel.Mastitis=Pertes de production pour la Mammite ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Tableau de référence
MilkProcessorResourcesViewModel.Title=Comparaison de procédure de traite- Information
MilkProcessorResultsViewModel.Change=Changement
MilkProcessorResultsViewModel.HundredWeight=CWT
MilkProcessorResultsViewModel.MilkPrice=Prix du lait  ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Changements annuels de la valeur
MilkProcessorResultsViewModel.ScenarioOne=Scénario 1
MilkProcessorResultsViewModel.ScenarioTwo=Scénario 2
MilkProcessorResultsViewModel.Title=Comparaison de procédure de traite- Résultats
MilkProcessorResultsViewModel.WeightImperialCWT=CWT
MilkProcessorResultsViewModel.WeightMetric=kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Composantes
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Composantes
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsMasterViewModel.MilkProcNew=Nouveau
MilkProcessorSettingsMasterViewModel.Title=Configuration de la laiterie
MilkProcessorViewModel.Amount=Quantité (1000 cellules / ml)
MilkProcessorViewModel.AmountCFU=Quantité (1000 ufc / ml)
MilkProcessorViewModel.BasePriceMilkFat=Matière Grasse (euro/kg)({0} / {1})
MilkProcessorViewModel.BasePriceMilkPrice=Lait ({0} / {1})
MilkProcessorViewModel.BasePriceMilkProtein=Proteine du lait ({0} / {1})
MilkProcessorViewModel.BasePriceOtherSolids=Autres solides ({0} / {1})
MilkProcessorViewModel.BasePrices=PRIX DE BASE
MilkProcessorViewModel.ComponentProcessor=Laiterie; composantes
MilkProcessorViewModel.ConcentrationProcessor=Laiterie paiement au litre de lait et qualité (g/L)
MilkProcessorViewModel.Delete=Effacer
MilkProcessorViewModel.DeletePrompt=La suppression de cette laiterie annulera les résultats dans l'outil Calculatrice du produit lait. Voulez-vous supprimer cette laiterie?
MilkProcessorViewModel.HundredWeight=CWT
MilkProcessorViewModel.Name=NOM
MilkProcessorViewModel.NameNotUnique=Une laiterie nommée "{0}" existe déjà. Les noms doivent être uniques.
MilkProcessorViewModel.NewComponentProcessorName=Laiterie - composantes  \# {0}
MilkProcessorViewModel.NewConcentrationProcessorName=Laiterie - primes \# {0}
MilkProcessorViewModel.PricingMatrices=PRIX DES MATRICES
MilkProcessorViewModel.SelectCurrency=Sélectionnez la devise
MilkProcessorViewModel.WeightImperial=LBS
MilkProcessorViewModel.WeightImperialCWT=CWT
MilkProcessorViewModel.WeightMetric=KG
MilkProduction=Production de lait({0})
MilkProductionKg=Production kg
MilkProductionPounds=Production de lait (lbs)
MilkProductionRevenue=Revenus de production de lait
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Production de composantes et efficacité
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=CMS et efficacité alimentaire
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=% gras et % protéines du lait
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Production de lait et jours en lait
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Cellules somatiques et urée du lait
MilkSoldEvaluationChartsListViewModel.VisitComparison=Veuillez sélectionner des visites pour la comparaison
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Efficacité des composantes
MilkSoldEvaluationChartsViewModel.ComponentYield=Production des composantes
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Production et efficacité des composantes
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=CMS et efficacité alimentaire
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Jours en lait
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Consommation de matière sèche
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Efficacité alimentaire
MilkSoldEvaluationChartsViewModel.MilkFat=Matière grasse (%)
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=% Gras et % protéine 
MilkSoldEvaluationChartsViewModel.MilkProduction=Production de lait
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Production de lait et jours en lait
MilkSoldEvaluationChartsViewModel.MilkProtein=Protéine du lait %
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Urée du lait
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Comptage de cellules somatiques
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Cellules somatiques et urée du lait
MilkSoldEvaluationChartsViewModel.Title=Évaluation du lait vendu
MilkSoldEvaluationInputsViewModel.AddPickup=Ajouter une collecte
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Animaux dans le réservoir ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Jours en lait (JEL) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Consommation de matière sèche ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=DONNÉES DU TROUPEAU
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Vaches en lactation ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Livraison de lait ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Information sur la laiterie 
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Unité de l'urée du lait ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Ajouter nouveau
MilkSoldEvaluationMasterViewModel.Charts=Graphiques
MilkSoldEvaluationMasterViewModel.Inputs=Données
MilkSoldEvaluationMasterViewModel.Outputs=Résultats
MilkSoldEvaluationMasterViewModel.Title=Évaluation du lait vendu
MilkSoldEvaluationOutputsViewModel.AvgBCC=Comptage bactérien moyen (1,000 cfu/mL)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Matière grasse moyenne (%)
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Production de lait moyenne, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Production de lait moyenne, {0} (animaux dans le réservoir)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Protéine du lait moyenne (%)
MilkSoldEvaluationOutputsViewModel.AvgSCC=CCS moyen (1,000 cellules/mL)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Efficacité des composantes (% de CMS)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Journées d'évaluation
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Efficacité alimentaire (ratio)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Production de gras + protéine  ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Production de gras ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Production de protéine du lait ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Mise à jour du site
MilkSoldPickupViewModel.AnimalsInTank=Animaux dans le réservoir ⃰
MilkSoldPickupViewModel.BCC=Comptage bactérien (1,000 cfu/mL)
MilkSoldPickupViewModel.DaysInTank=Jours dans le réservoir ⃰
MilkSoldPickupViewModel.MilkFat=Matière grasse (%)
MilkSoldPickupViewModel.MilkProtein=Protéine du lait %
MilkSoldPickupViewModel.MilkSold=Lait vendu, {0} ⃰
MilkSoldPickupViewModel.SCC=Cellules somatiques (1,000 cells/mL)
MilkSoldPickupViewModel.Title=Modifier la livraison {0}
MilkSoldSpinnerViewModel.Title=Évaluation du lait vendu
MilkUrea=Urée du lait (mg/dL)
Milking=Lactation
MilkingFailure=Échecs de traites
MilkingFirst=Traite en premier
MilkingProcessRevenueInputs=Comparaison de procédure de traite- Données
MilkingProcessRevenueResources=Comparaison de procédure de traite- Information
MilkingProcessRevenueResults=Comparaison de procédure de traite- Résultats
Minas_Gerais=minas Gerais
Minnesota=Minnesota
Mississippi=Mississippi
Missouri=Missouri
Mizoram=Mizoram
Modena=Modena
Moderate=Moderate
ModeratelyClean=Modérément propre
Moldova,_Republic_of=Moldavie, RÃ©publique de
Monaco=Monaco
Monaghan=Monaghan
Mongolia=Mongolie
Montana=Montana
Montenegro=MontÃ©nÃ©gro
Monthly=Mensuel
Montserrat=Montserrat
Monza_and_Brianza=Monza et Brianza
MoreThan8LayersOfPlastic=Plus de 8 couches
Morelos=Morelos
Morocco=Maroc
Mozambique=Mozambique
Myanmar=Myanmar
NGN=NGN
NIO=Nicaragua (NIO NIO)
NOK=NOK
Nagaland=Nagaland
Namibia=Namibie
Naples=Naples
Nauru=Nauru
Nayarit=Nayarit
Nebraska=Nebraska
Nei_Mongol=Nei Mongol
Nepal=NÃ©pal
Netherlands=Pays-Bas
Nevada=Nevada
NewBunker=Donnez un nom au silo couloir
NewDietClassViewModel.Title=Type animal / sous-classe
NewDietPensViewModel.AssociatePens=Vous pouvez associer cette ration à plusieurs groupes
NewDietPensViewModel.Title=Associer à des groupes
NewDietViewModel.Cancel=Annuler
NewDietViewModel.MainHeading=Nom de la ration ⃰
NewDietViewModel.Save=Sauvegarder
NewDietViewModel.Title=Nouvelle ration
NewPenDietViewModel.New=Nouveau
NewPenDietViewModel.Title=ration
NewPenViewModel.Animals=Animaux par groupe
NewPenViewModel.AnimalsInputsPen=Données d'animaux, par groupe
NewPenViewModel.AsFedIntake=Consommation TQS
NewPenViewModel.Barn=Nom de l'étable
NewPenViewModel.Cancel=Annuler
NewPenViewModel.DaysInMilk=Jours en lait (JEL)
NewPenViewModel.Diet=ration
NewPenViewModel.DietInputsPen=Données de la ration, par groupe
NewPenViewModel.DryMatterIntake=Matière sèche ingérée en KG
NewPenViewModel.FeedingSystem=Système d'alimentation
NewPenViewModel.General=Général
NewPenViewModel.HousingSystem=Type de logement
NewPenViewModel.InfoNewPenDetails=Ajoutez ou mettez à jour les données spécifiques au lot  sur cette page. Vous pouvez également ajouter ou mettre à jour des données dans les outils au fur et à mesure que vous les utilisez.
NewPenViewModel.Milk=Production de lait({0})
NewPenViewModel.MilkingFrequency=Fréquence de traite
NewPenViewModel.NumberOfStalls=Nombre de logettes
NewPenViewModel.OnlyOnePen=Vous avez seulement un groupe.
NewPenViewModel.PenDetail=Caractéristiques du groupe
NewPenViewModel.PenMapping=Schéma des groupes
NewPenViewModel.PenName=Nom du groupe
NewPenViewModel.PenSelection=Sélectionnez un groupe
NewPenViewModel.PublishPenAlert=Veuillez publier tous les visites associés au groupe que vous voulez fusionner.
NewPenViewModel.RationCostPerAnimal=Coûts $/vache/jour ({0})
NewPenViewModel.Save=Sauvegarder
NewPenViewModel.Title=Nouveau groupe
NewPenViewModel.UserCreatedPen=L'utilisateur a créé un groupe
NewPile=Donnez un nom à l'amas
NewProspectViewModel.Address1=ADRESSE COMMERCIALE 1
NewProspectViewModel.Address2=ADRESSE COMMERCIALE 2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.BusinessName=NOM DE L'ENTREPRISE
NewProspectViewModel.City=VILLE
NewProspectViewModel.ConsumerDetails=Détails sur le consommateur
NewProspectViewModel.Country=PAYS
NewProspectViewModel.Customer=Clients
NewProspectViewModel.CustomerDetail=DÉTAILS DU CLIENT
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=COURRIEL DU CONTACT
NewProspectViewModel.EndUser=Utilisateur final
NewProspectViewModel.FarmProducer=Producteur
NewProspectViewModel.Image=Appuyez pour modifier l'image
NewProspectViewModel.InvalidEmail=Veuillez entrer un courriel valide
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=Un client potentiel nommé "{0}" existe déjà. Les noms doivent être uniques.
NewProspectViewModel.NameNotUniqueForConsumer=Ce nom "{0}" existe déjà. Les noms doivent être uniques.
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=- 
NewProspectViewModel.NullBusinessName=Le nom commercial est requis.
NewProspectViewModel.NullFirstName=Prénom requis
NewProspectViewModel.NullSecondName=Nom requis
NewProspectViewModel.PostalCode=CODE POSTAL
NewProspectViewModel.PrimaryContactFirstName=PRENOM CONTACT
NewProspectViewModel.PrimaryContactLastName=NOM DE FAMILLE CONTACT
NewProspectViewModel.PrimaryPhone=NUMÉRO DE TÉLÉPHONE DE CONTACT
NewProspectViewModel.Prospect=Prospect
NewProspectViewModel.ProspectDetail=DÉTAILS DU CLIENT POTENTIEL
NewProspectViewModel.Segment=SEGMENT
NewProspectViewModel.State=ÉTAT
NewProspectViewModel.Title=Détails
NewProspectViewModel.Type=TYPE
NewVisitViewModel.Title=Détails de Visite
New_Brunswick=Nouveau-Brunswick
New_Caledonia=Nouvelle CalÃ©donie
New_Hampshire=New Hampshire
New_Jersey=New Jersey
New_Mexico=Nouveau Mexique
New_South_Wales=Nouvelle Galles du Sud
New_York=New York
New_Zealand=Nouvelle-ZÃ©lande
Newfoundland_and_Labrador=Terre-Neuve-et-Labrador
Next=Suivant
Nicaragua=Nicaragua
Niedersachsen=SAXONE DE BAIS
Niger=Niger
Nigeria=Nigeria
Ningxia=Ningxia
Niue=Niue
No=Non
No-User-Found=Utalisateur introuvable sur LIFT ; veuillez contacter l'administrateur pour une résolution
NoResourcesAvailable=Ressource non disponible.
NoResults=Aucun résultat trouvé
NoWholeKernals=Aucun grain entier
Noah=Noah
None=Aucun
NoneSelected=Aucun sélectionné
Nordrhein=Rhin du Nord
Norfolk_Island=l'ile de Norfolk
Normal=&lt; 0.5 état corporel 
NorthAmerica=Amerique du Nord
North_Carolina=Caroline du Nord
North_Dakota=Dakota du nord
Northern_Territory=Territoire du Nord
Northwest_Territories=Territoires du nord-ouest
Norway=NorvÃ¨ge
Not-matching-with-allowed-values={0} ne correspondant pas aux valeurs autorisées
NotMeasured=Non mesuré
NotRemoved=Non enlevés
NotSet=-
NoteCamcorderNotImplemented=Fonctionnalité de la  caméra n'est pas encore configurée
NoteCategoryViewModel.FooterText=SEULEMENT une catégorie peut être sélectionnée par note
NoteCategoryViewModel.SelectCategory=CHOISIR UNE CATÉGORIE
NoteCategoryViewModel.Title=Note
NotebookBCSHerdAnalysisGoals=Analyse d'état corporel de troupeau - Objectifs
NotebookBCSHerdAnalysisInputs=Analyse d'état corporel de troupeau - Données
NotebookBCSHerdAnalysisResults=Analyse d'état corporel de troupeau - Résultats
NotebookBCSSelectPointScale=État corporel- Sélectionnez une échelle de point
NotebookBodyConditionEdit=État corporel Modifier le tableau
NotebookCudCalculators=Santé du rumen-compteur proportion vaches qui ruminent
NotebookCudChewing=Santé du rumen- compteur coup de machoire-sélectionner le lot
NotebookCudChewingDataEntry=Santé du rumen-compteur nombre de coups de machoire par cycle de rumination
NotebookCudChewingResults=Santé du rumen-Résultats nombre de coups de machoire par cycle de rumination
NotebookLocomotionEditTable=Locomotion - Nombre de Vaches
NotebookLocomotionHerdInputs=Locomotion-Analyse donnée troupeau
NotebookLocomotionHerdResults=Locomotion-Résultat d'analyse du troupeau
NotebookLocomotionHerdRevenue=Locomotion-Incidence performance troupeau
NotebookLocomotionLanding=Locomotion-selection du groupe
NotebookLocomotionPenInputs=Locomotion- Données du groupe
NotebookLocomotionPenResults=Locomotion-Resultats du groupe
NotebookLocomotionPenSelection=Locomotion-sélection du groupe
NotebookManurePenSelection=Score de fumier- Sélection du lot
NotebookManureScoreHerdAnalysisGoals=Score de fumier du troupeau-objectifs
NotebookManureScoreHerdAnalysisInputs=Score de fumier du troupeau-données
NotebookManureScoreHerdAnalysisResults=Score de fumier du troupeau-résultat
NotebookManureScoreLanding=Evaluation des fumiers
NotebookMetabolicIncidenceCharts=Graphiques maladies métaboliques
NotebookMetabolicIncidenceInputs=Données pour maladies métaboliques
NotebookMetabolicIncidenceOutputs=Résultat maladie métabolique
NotebookParticleScoreHerdAnalysisEdit=Score des particules de la RTM du troupeau- Modifier les JEL
NotebookParticleScoreLanding=Grosseurs des particules de la RTM
NotebookParticleScoreSelectPen=Grosseurs de particules RTM - sélectionner un groupe
NotebookParticleScoreSelectScorer=Grosseurs de particules RTM -sélectionner le tamis
NotebookPenTimeComparison=evaluation du temps de repos- Comparaison
NotebookPenTimeInputs=evaluation du temps de repos- Données
NotebookPenTimePenSelection=evaluation du temps de repos- Selection
NotebookPenTimeResults=evaluation du temps de repos- Résultats
NotebookReadyToMilkCharts=ReadyToMilk Graphiques
NotebookReadyToMilkInputs=ReadyToMilk Données
NotebookReadyToMilkOutputs=ReadyToMilk Résultats
NotebookRumenHealthNumberOfChewsInput=Santé du rumen- Nombre de mastication
NotebookRumenHealthNumberOfChewsResults=Santé du rumen- Nombre de coups de machoire-Résultat
NotebookRumenHealthTMRParticlePercent=Répartition par étage du tamis
NotebookRumenHealthTMRParticleScore=Scores des particules RTM
NotebookSectionComfortTools=Evaluation du confort
NotebookSectionForageAudit=Pointage des audits des fourrages
NotebookSectionForageAuditBaleage=Audit des fourrages-Balle enrobée
NotebookSectionForageAuditBunkersPiles=Audit des fourrages-Silo fosse et amas
NotebookSectionForageAuditHarvest=Audit des fourrages-Qualité des fourrages dans la ration
NotebookSectionForageAuditLanding=Audit des fourrages
NotebookSectionForageAuditMaintainingQuality=Audit des fourrages-Maintien de la qualité des fourrages
NotebookSectionForageAuditSilageBags=Audit des fourrages-Ag-bags
NotebookSectionForageAuditSurveyOfForages=Audit des fourrages-Gestion des fourrages
NotebookSectionForageAuditTowerSilos=Audit des fourrages-Silos tours
NotebookSectionHealthTools=Outils de santé
NotebookSectionHeatstressCalculations=calcul\: stress thermique
NotebookSectionHeatstressChart=Graphique Stress thermique
NotebookSectionHeatstressData=Données\: stress thermique
NotebookSectionHerdAnalysisGoals=Santé ruminale - Analyse des objectifs du troupeau
NotebookSectionMilkSoldEvaluationCharts=Graphiques - Évaluation du lait vendu
NotebookSectionMilkSoldEvaluationEditPickup=Évaluation du lait vendu - modifier la livraison
NotebookSectionMilkSoldEvaluationInputs=Évaluation du lait vendu - données
NotebookSectionMilkSoldEvaluationOutputs=Évaluation du lait vendu - résultat
NotebookSectionNutritionTools=Outils nutrition
NotebookSectionRoboticMilkEvaluationAnalysis=Évaluation du robot de traite - Analyse
NotebookSectionRoboticMilkEvaluationCharts=Évaluation du robot de traite - Graphiques
NotebookSectionRoboticMilkEvaluationInputs=Évaluation du robot de traite - entrée de données
NotebookSectionRoboticMilkEvaluationOutputs=Évaluation du robot de traite - résultats
NotebookSectionRoboticMilkEvaluationTrends=Évaluation du robot de traite - Tendances
NotebookSectionRumenHealthLanding=Santé ruminale
NotebookSectionVisit=Visite
NotebookTMRParticleHerdAnalysisPenInputs=Score particules RTM-Résultats troupeau-Données
NotebookTMRParticleHerdAnalysisPenResults=Score particules RTM-Résultats troupeau-Résultats
NotebookUrinePHEditGoals=Objectif pH urinaire
NotebookUrinePHInputs=Donnée sur le pH urinaire
NotebookUrinePHOutputs=Résultats du pH urinaire
NotebookVisitSummary=Sommaire de Visite
NotebookWalkthroughReport=Rapport pas-à-pas
NotebookWalkthroughReportLanding=Rapport pas-à-pas
NotebookWalkthroughReportPen=Rapport de visite- Analyse par groupe
Nova_Scotia=Nouvelle-Ãcosse
Novara=Novara
Nuevo_LeÃ³n=Nouveau LeÃ­n
Null-values-not-allowed=Valeur nulle en {0} non autorisée
NumChewsGoal={0} objetif
NumOfCows=Nombre de vaches
NumberOfChewsReportsViewModel.Average=Nombre moyen de mastications
NumberOfChewsReportsViewModel.AverageChews=Mastication moyenne
NumberOfChewsReportsViewModel.AverageNumberChews=Nombre moyen de mastications
NumberOfChewsReportsViewModel.DateDescription=Date
NumberOfChewsReportsViewModel.DatesComparison=Dates de comparaison
NumberOfChewsReportsViewModel.EditVisits=Sélectionner
NumberOfChewsReportsViewModel.StdDevCalculated=Deviation std (Calculée)
NumberOfChewsReportsViewModel.VisitDate=Date
NumberOfChewsViewModel.Count=Nombre
NumberOfChewsViewModel.CountHeader=Compte le nombre de mastication par vache.
NumberOfChewsViewModel.NextCow=Vache suivante
NumberOfChewsViewModel.NumberOfChewsCow=Mastication / cycle de rumination / vache
NumberOfChewsViewModel.Title=Mastication /vache \# {0}
NumberOfChewsViewModel.ValidCudInput=Merci d'entrer une donnée valide
NumberOfCows=nombre de vaches
Nunavut=Nunavut
Nuoro=Nuoro
NutritionViewModel.NutritionForage=Audit de Fourrage
NutritionViewModel.NutritionLabel=Choisir parmis les outils suivants pour débuter votre visite
NutritionViewModel.NutritionPile=Inventaires des fourrages
NutritionViewModel.NutritionTools=Outils nutrition
NutritionViewModel.NutritionToolsCaption=Outils
NutritionViewModel.NutritionToolsInstructions=Choissisez un outils de la liste ici bas pour débuter votre visite
NutritionViewModel.NutritionToolsList=Outils
NutritionViewModel.Title=Outils nutrition
NutritionViewModel.VisitNotebook=Cahier de notes
OKLabel=OK
Oaxaca=Oxaca
Observation=Observation
Odisha=Odisha
Offaly=Offnaly
Ogliastra=Oglistra
Ohio=Ohio
Oklahoma=Oklahola
Olbia-Tempio=Olbia-Tempio
Oman=Le sien
OncePerWeek=1 fois par semaine
One=Un
OneHourBeforeActionIsDue=Une heure avant que l'action ne soit due
OneToSixHours=1 à 6 heures
Ontario=Ontario
Opportunities=Opportunités
Optimal=Réponses optimales
Oregon=Oregon
Oristano=Oristano
Other=Autres
OtherSilage=Autre ensilage
Overall=Globalement
OverallCalfHeiferDetails=Pour voir le score global de l'outil Veaux et Génisses, SVP compléter au moins un questionnaire dans la liste ci-haut
OverallForageScoreDetails=Pour voir le résultat global des fourrages, vous devez compléter au moins un questionnaire de la liste précédente
PDFDisclaimer=Cargill Incorporated, ses parents et ses sociétés affiliées ne justifie pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. © {0} Cargill, Incorporated. Tous les droits sont réservés
PDFDisclaimer_ProvimiUS=Provimi North America, ses parents et ses sociétés affiliées ne justifie pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. © {0} Provimi North America. Tous les droits sont réservés
PDFPageNumber=Page {0} de {1}
PEN=Pérou (S/. PEN)
PHP=Philippines ($ PHP)
PLN=Pologne (zł PLN)
PMRConcentrate=RPM + concentrés
PON=Roumanie (lei PON)
Padua=Padoue
Pakistan=Pakistan
Palermo=Palerme
Palestinian_Territory,_Occupied=Territoire palestinien, occupÃ©
Panama=Panama
Papua_New_Guinea=Papouasie Nouvelle GuinÃ©e
Paraguay=Paraguay
ParanÃ¡=ParansÃ©
ParaÃ­ba=ParaÃ£ba
Parlor=Salle de traite
Parma=Parme
ParticleScorePreviousVisitsViewModel.MidOne=Milieu 2
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Milieu 3
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Quantité sur le tamis (%)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Milieu 1 (8 mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Milieu 2 (4 mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Haut (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Tamis
ParticleScorePreviousVisitsViewModel.SelectDates=Sélectionnez les dates
ParticleScorePreviousVisitsViewModel.Top=Haut
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.Tray=Tamis
ParÃ¡=Paraictoire
PasteurizedWholeMilk=Lait pasteurisé servi
Pasto=Pasto
Pasture=Pâturage
PastureOther=Pâturage + Autre
Pavia=Pavie
Pays=Paie
PeakMilk=Pic lact.
PenDetailViewModel.Title=Caractéristiques du groupe
PenListViewModel.DietSetup=Configuration – Ration
PenListViewModel.MainHeading=Enclos
PenListViewModel.NewPen=Ajouter un nouveau groupe/parc
PenListViewModel.Title=Groupes
PenName=Nom du groupe
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Changement de l'état corporel (sur 100 jours)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Changement de poids corporel ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Vaches dans le groupe
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Vaches traites par heure
PenTimeBudgetComparisonViewModel.Current=Actuel
PenTimeBudgetComparisonViewModel.EnergyChange=Changement d'énergie (Mcals)
PenTimeBudgetComparisonViewModel.Overcrowding=Surpopulation (%)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Nombre de vaches dans la salle de traite par heure
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Perte / gain potentiel de lait ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Temps restant (heures)
PenTimeBudgetComparisonViewModel.TableTitle=Comparaison
PenTimeBudgetComparisonViewModel.TimePerMilking=Temps par traite (heures)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Temps restant pour le repos (heures)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Temps requis pour le repos (heures)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Temps total sans repos (heures)
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Temps total de la traite (heures)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Temps de marche vers la traite (Heures)
PenTimeBudgetPenMasterViewModel.Compare=Comparer
PenTimeBudgetPenMasterViewModel.Inputs=Données
PenTimeBudgetPenMasterViewModel.Results=Résultats
PenTimeBudgetPenMasterViewModel.Title=Temps de repos par groupe
PenTimeBudgetResultsViewModel.Hours=Heures
PenTimeBudgetResultsViewModel.MilkDifference=Différence potentielle en lait
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=lbs
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Perte / gain potentiel en lait
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Temps disponible pour le repos
PenTimeBudgetResultsViewModel.TimeRemaining=Temps restant
PenTimeBudgetResultsViewModel.TimeRequired=Temps requis
PenTimeBudgetResultsViewModel.Title=Temps de repos par groupe - Résultats
PenTimeInputsViewModel.CowsPen=Nombre de vaches dans le groupe
PenTimeInputsViewModel.Drinking=Temps pour boire et socialiser (heures)
PenTimeInputsViewModel.Eating=Temps pour manger (heures)
PenTimeInputsViewModel.Frequency=Fréquence de traite (par jour)
PenTimeInputsViewModel.LockUp=Temps barré dans les carcans (heures)
PenTimeInputsViewModel.NonRestTime=Autres temps d'activité (heures)
PenTimeInputsViewModel.ParlorTime=Temps en salle de traite (heures)
PenTimeInputsViewModel.PenTimeTitle=Temps de repos par groupe
PenTimeInputsViewModel.Resting=Exigence de repos (heures)
PenTimeInputsViewModel.StallsPen=Nombre de logettes dans le groupe
PenTimeInputsViewModel.TotalStalls=Nombre de places dans la salle de traite
PenTimeInputsViewModel.WalkingTimeFrom=Temps de retour de la traite (heures)
PenTimeInputsViewModel.WalkingTimeTo=Temps de marche vers la traite (heures)
PenTimePenSelectionViewModel.NoLactatingPen=Pour accéder à cet outil, assurez-vous d'avoir au moins un groupe associé à la ration lactation.
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Temps de repos par groupe
PenTimePenSelectionViewModel.PenTimeSection=GROUPES
PenTimePenSelectionViewModel.Title=Temps de repos par groupe
PennStateShakerBoxForageResults=Résultat du séparateur de (l'Université) Penn State des fourrages
Pennsylvania=Pennsylvanie
Pens=Goupes
PerceivedHeatStressDietInfoMessage=Stress thermique modéré\: haletant, bave ou mousse mais pas de bouche ouverte, taux de respiration de 40 à 120 bpm.
PercentLossPerCow=% de perte / vache
PercentOnScreenTitle=Quantité sur le tamis (%)
PercentPen=% du groupe
PercentageOnScreen=Quantité sur le tamis (%)
PercentageOnScreenCurrentVisit=Quantité sur le tamis (%) - Visite actuelle
PercentageOnScreenTrend=Quantité sur le tamis (%) - Tendance
Pernambuco=Pernambuco
Peru=Pérou
Perugia=PÃ©rugie
Pesaro_and_Urbino=Pesaro et Urbino
Pescara=Pescara
PhaseFive=Puberté
PhaseFour=Croissance
PhaseOne=Colostrum
PhaseSeven=Préparation / Production
PhaseSix=Gestation
PhaseTwoThree=Pré/post sevrage
Philippines=Philippines
PhotoExamples=Exemple photo
Piacenza=Piacenza
PiauÃ­=PiauÃ£
Pickup=Livraison {0}
Pile=Amas
PileAndBunkerCapacitiesDensity=Guide de référence de la capacité des structures d'entreposage des fourrages
PileAndBunkerCapacity=Capacité des silos
PileAndBunkerCapacityViewModel.AddBag=Ajouter un ag-bag
PileAndBunkerCapacityViewModel.AddBunker=Ajouter un bunker
PileAndBunkerCapacityViewModel.AddPile=Ajouter un amas
PileAndBunkerCapacityViewModel.Bag=Ag-bag
PileAndBunkerCapacityViewModel.Bags=Ag-bags
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=Silo à déchargement par le bas
PileAndBunkerCapacityViewModel.Bunker=Bunker
PileAndBunkerCapacityViewModel.Bunkers=Bunkers
PileAndBunkerCapacityViewModel.NameNotUnique=Un bunker ou un amas nommé "{0}" existe déjà. Les noms doivent être uniques.
PileAndBunkerCapacityViewModel.NameTooLong=Le nom d'un bunker ou d'un amas  doit contenir moins de 40 caractères
PileAndBunkerCapacityViewModel.Pile=Amas
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Inventaires des fourrages
PileAndBunkerCapacityViewModel.Piles=Amas
PileAndBunkerCapacityViewModel.Title=Inventaires des fourrages
PileAndBunkerCapacityViewModel.TopUnloadingSilo=Silo à déchargement par le haut
PileAndBunkerCapacityViewModel.VisitNotebook=Cahier de notes
PileAndBunkerName=Nom de l'inventaire de fourrages
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=Densité de l'ensilage TQS {0} (objectif\: &gt; {1})
PileAndBunkerResultsBagCapacityInputViewModel.CapacityBag=Capacité
PileAndBunkerResultsBagCapacityInputViewModel.DiameterBag=Diamètre ({0})
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=% Matière sèche
PileAndBunkerResultsBagCapacityInputViewModel.LenghtBag=Longueur ({0})
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=Tonnes métriques TQS
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=Tonnes métriques MS
PileAndBunkerResultsBagCapacityInputViewModel.SilageDMDensityBag=Densité de l'ensilage MS ({0})
PileAndBunkerResultsBagCapacityInputViewModel.TonsAFBag=Tonnes TQS
PileAndBunkerResultsBagCapacityInputViewModel.TonsDMBag=Tonnes MS
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Longueur en bas ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Largeur en bas ({0}))
PileAndBunkerResultsCapacityInputViewModel.Capacity=Capacité
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=MS %
PileAndBunkerResultsCapacityInputViewModel.Height=Hauteur ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=Tonnes métriques TQS
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Tonnes métriques MS
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Longueur en bas (pi)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Longueur em bas (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Largeur en bas (pi)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Largeur en bas (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Hauteur (pi)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Hauteur (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Longueur en haut (pi)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Longueur en haut (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Largeur en haut (pi)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Largeur en haut (m)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Densité MS d'ensilage ({0})
PileAndBunkerResultsCapacityInputViewModel.Title=Capacité
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Densité TQS d'ensilage {0} (Objetif\:> {1})
PileAndBunkerResultsCapacityInputViewModel.TonsAF=TONNES TQS
PileAndBunkerResultsCapacityInputViewModel.TonsDM=Tonnes MS
PileAndBunkerResultsCapacityInputViewModel.TopLength=Longueur en haut ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Largeur en haut ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=à 6 pouces par jour
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=à 15 cm par jour
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=à 3 pouces par jour
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=à 7 cm par jour
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=Nombre de vaches nécessaires pour assurer avancement silo
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Nombre d'animaux alimentés
PileAndBunkerResultsFeedOutViewModel.DateGone=Date vide
PileAndBunkerResultsFeedOutViewModel.Days=Jours
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Consommation de fourrage par le troupeau par jour
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Surface du front d'attaque (ft ^ 2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Surface d'alimentation (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Consommation (TQS/vache/jour)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=pouces par jour
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=cm Par jour
PileAndBunkerResultsFeedOutViewModel.Resources=Ressources
PileAndBunkerResultsFeedOutViewModel.StartDate=Date de début
PileAndBunkerResultsFeedOutViewModel.Title=Reprise
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tonnes par jour
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Lbs de matière sèche dans 1 pied
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kgs matière sèche dans 1 mètre
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0.0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Capacité
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Reprise
PileAndBunkerResultsMasterViewModel.VisitNotebook=Cahier de notes
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=Capacité
PileAndBunkerResultsSiloCapacityInputViewModel.Diameter=Diamètre ({0}))
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=% Matière sèche
PileAndBunkerResultsSiloCapacityInputViewModel.FilledHeight=Hauteur de remplissage({0})
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=Tonnes métriques TQS (restantes dans le silo)
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=Tonnes métriques MS (restantes dans le silo)
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=Densité MS ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.SilageLeft=Hauteur de l'ensilage restant dans le silo ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.TonsAFSilo=Tonnes TQS (restantes dans le silo)
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=Tonnes MS (restantes dans le silo)
PileAndBunkerTitle=Inventaire des fourrages
PileBunkerCapacities=Inventaires des fourrages
PileCapacity=Amas -Capacité
PileFeedOutRate=Amas -Reprise
Piles=Amas
Pisa=Pise
Pistoia=Pistoia
Pitcairn=Pitcairn
PlBacteriaCell=Comptage Bactérien (1,000cfu/mL)
PlMilkFat=Matière Grasse (euro/kg)
PlMilkProtein=Protéine du lait
PlSomaticCell=Cellules Somatiques (1,000 cellules/mL)
Placentaire=
Poland=Pologne
Poor=Faible
Pordenone=PordÃ©none
Porosity=Densité de compaction
Portugal=le Portugal
Postweaned=Post-Sevrage
Postweaned_CleanAndDryPen=Enclos propre et sèche 
Postweaned_CleanAndDryPen_ToolTip=Utilisez le test du genou humide pour déterminer si la zone est propre et sèche
Postweaned_EvidenceOfAcidosisInManure=Signe d'acidose dans le fumier
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Est-ce qu'il y a des bulles dans le fumier?
Postweaned_EvidenceOfScoursOrPneumonia=Signe de diarrhée ou de pneumonie 
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% des veaux ont des signes de diarrhée ou de pneumonie 
Postweaned_FeedBunkIsClaanedDaily=Mangeoire est netoyée à chaque jour et les refus sont enlevés
Postweaned_ForageAvailability=Disponibilité des fourrages
Postweaned_ForageAvailability_ToolTip=Les tiges ont plus de 5 cm 
Postweaned_FreeChoiceCleanWaterIsAvailable=Eau propre à volonté est acessible 
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=Pas de signe de contaminant dans l'eau
Postweaned_FreshQualityStarterAvailable=Moulée de départ/croissance est disponible 
Postweaned_FreshQualityStarterAvailable_ToolTip=La moulée départ / croissance n'a pas de poussière, de moisissure et n'est pas mouillée
Postweaned_SizeOfBunkSpace=Espace à la mangeoire par génisses est adéquate 
Postweaned_SizeOfBunkSpace_ToolTip=> 45cm par veau
Postweaned_SizeOfPenAdequate=Espace dans l'enclos par génisses est adéquat
Postweaned_SizeOfPenAdequate_ToolTip=Enclos individuel\: 3m2 (32 pi2), Enclos de groupe\: 2.75m2 (28 pi2)
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Enclos bien ventilé et sans courant d'air directement sur les veaux 
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=Si les vêtement sentent l'ammoniac après avoir quitté l'étable, C'est trop élevé 
PotentialDownResponse=Vitesse de traite potentielle ({0}/cow/day)
PotentialSCC=CCS Potentiel (Cellules/{0})
Potenza=Pouvoir
Prato=Plat
PreWeaned_CMRisProperlyMixed_ToolTip=Quantité de  LR\: &gt;600g &lt;800g, Température\: 39-41C, Solides\: 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Utilisez le test du genou humide pour déterminer si la zone est propre et sèche
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% des veaux ont des signes de diarrhée ou de pneumonie 
PreWeaned_Forageavailability_ToolTip=Pas de fourrages requis si la moulée  est texturée 
PreWeaned_FreeChoiceCleanWater_ToolTip=Disponible depuis le premier jour, pas de signe de contaminant dans l'eau 
PreWeaned_FreeChoiceFreshCalf_ToolTip=La moulée départ / croissance n'a pas de poussière, de moisissure et n'est pas mouillée
PreWeaned_SizeOfPen_ToolTip=Enclos individuel\: 3m2 (32 pi2), Enclos de groupe\: 2.5m2 (25 pi2)
PreWeaned_WellVenilated_ToolTip=Si les vêtement sentent l'ammoniac après avoir quitté l'étable, C'est trop élevé 
PrematureKelvingsKeyInfoMessage=Naissance d'un veau vivant ou plus, au moins 10 jours avant la date prévue
PreventingStorageLosses=Prévenir des Pertes dû au stockage
Previous=Précédent
Preweaned=Pré-Sevrage
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=LR est bien mélangé et servi adéquatement
Preweaned_CleanAndDryPen=Enclos propre et sèche 
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Correctement nettoyer et désinfecter l'équipement et la mangeoire des veaux entre les repas
Preweaned_ConsistentFeedingTimesAndProtocols=Les protocoles et la température d'alimentation sont constents
Preweaned_EvidenceOfScoursOrPneumonia=Signe de diarrhée ou de pneumonie 
Preweaned_ForageAvailability=Disponibilité des fourrages
Preweaned_FreeChoiceCleanWaterIsAvailable=Eau propre à volonté est acessible 
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Moulée de départ/croissance est disponible 
Preweaned_SizeOfPenAadequatePerHeifer=Grandeur de l'enclos des génisses est adéquate 
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Sevrage fait lorsque la consommation de la  moulée départ atteint 1 kg par jour 
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Enclos bien ventilé et sans courant d'air directement sur les veaux 
PricingMatrixEditViewModel.Cancel=Annuler
PricingMatrixEditViewModel.Save=Sauvegarder
PricingMatrixEditViewModel.Title=Détail grille de paiement par critère
PricingMatrixPickListViewModel.PricingMatrix=Matrice de tarification
PricingMatrixViewModel.Amount=Quantité (1000 cellules / ml)
PricingMatrixViewModel.AmountCFU=Quantité (1000 cfu / ml)
PricingMatrixViewModel.New=Nouveau
PricingMatrixViewModel.Title=Modifier la matrice
Prince_Edward_Island=Ãle-du-Prince-Ãdouard
Privacy_Statement=Déclaration de confidentialité
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Devises
ProcessorCurrencyPickListViewModel.Title=Devises
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Comparaison de procédure de traite
ProductivityToolsViewModel.MilkRevenueAnalysis=Analyse des revenus de production laitière
ProductivityToolsViewModel.MilkSoldEvaluation=Évaluation du lait vendu
ProductivityToolsViewModel.ProductivityTitle=Outils de productivité
ProductivityToolsViewModel.ProductivityTools=OUTILS
ProductivityToolsViewModel.RoboticMilkingEvaluation=Évaluation Robot de traite
ProductivityToolsViewModel.VisitNotebook=Cahier de notes
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=Annuler
PromptOK=Ok
PromptPermissionMsg=Sans cette permission, certaines fonctionnalités peuvent ne pas fonctionner. Êtes-vous certain de vouloir refuser cette permission?
PromptPermissionMsgIMSure=Je suis certain
PromptPermissionMsgRetry=réessayer
PromptPermissionTitle=Permission refusée
ProspectProfileViewModel.DeleteProspect=Supprimer le client potentiel
ProspectProfileViewModel.DeleteProspectPrompt=Êtes-vous sûr de vouloir supprimer ce client potentiel? Le client potentiel, le site et l'information de la visite en cours seront perdues.
ProspectProfileViewModel.MainHeading=SITES
ProspectProfileViewModel.NewSite=Ajouter un nouveau site
ProspectProfileViewModel.ProspectInfo=Info Prospect
ProspectProfileViewModel.ProspectTitle=Détails de Prospect
ProspectsViewModel.NewProspect=Ajouter un prospect
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=Provimi
ProvimiUS=Provimi US
PublishVisit=Publié
Puducherry=PudichÃ©ry
Puebla=Puebla
Puerto_Rico=Porto Rico
Punjab=Punjab
Purina=Purina
Qatar=Qatar
Qinghai=Qinghai
QuarterPointScale=Échelle quart de point (0.25)
Quarterly=3 fois par année
Quebec=QuÃ©bec
Queensland=Queensland
QuerÃ©taro=Quer Ã© Taro
QuestionTableTitle=Question {0} de {1}
QuestionViewModel.Baleage=Balle enrobée
QuestionViewModel.BunkersAndPiles=Bunkers et amas
QuestionViewModel.Close=fermer
QuestionViewModel.Harvest=Qualité des fourrages dans la ration
QuestionViewModel.MaintainingForageQuality=Maintien de la qualité des fourrages
QuestionViewModel.SilageBags=Ag-bags
QuestionViewModel.SurveyOfForages=Gestion des fourrages
QuestionViewModel.TowerSilos=Silos tours
QuestionViewModel.VisitNotebook=Visiter le cahier de notes
Quintana_Roo=Quintana Roo
ROL=ROL
RUB=Russie (₽‎ RUB)
Ragusa=Ragusa
Rajasthan=Rajasthan
Rationcost=Coût de la ration
Ravenna=Corbeau
ReadyToMilkChartViewModel.Current=Actuel
ReadyToMilkChartViewModel.DeathLoss=Mammite
ReadyToMilkChartViewModel.DisorderGraphTitle=Coût annuel désordres métaboliques
ReadyToMilkChartViewModel.DisplacedAbomasum=Caillette
ReadyToMilkChartViewModel.Dystocia=Dystocie
ReadyToMilkChartViewModel.Ketosis=Acétonémie
ReadyToMilkChartViewModel.Metritis=Métrite
ReadyToMilkChartViewModel.MilkFever=Fièvre du lait
ReadyToMilkChartViewModel.RetainedPlacenta=Rétention
ReadyToMilkChartViewModel.Title=Ready2Milk™ Graphiques
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Indice Ready2Milk™
ReadyToMilkInputViewModel.Back=Précédent
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Changement de l'état corporel du tarissement à 21 JEL
ReadyToMilkInputViewModel.CloseUp=Préparation vêlage
ReadyToMilkInputViewModel.ComfortCloseUp=Confort pré-vêlage
ReadyToMilkInputViewModel.ComfortDiet=Confort 0-21 JEL
ReadyToMilkInputViewModel.CostExtraDaysOpen=Coût par jour ouvert additionnel
ReadyToMilkInputViewModel.CudChewingDiet=Rumination vaches fraîches
ReadyToMilkInputViewModel.DeadCowsOrCulled=Réforme liée à la santé
ReadyToMilkInputViewModel.DisplacedAbomasum=Caillette
ReadyToMilkInputViewModel.Dystocia=Dystocie
ReadyToMilkInputViewModel.FreshCows=Vaches fraîches
ReadyToMilkInputViewModel.Health=Santé
ReadyToMilkInputViewModel.HealthDesc=Entrez le nombre de vaches fraîches et le nombre de maladies métaboliques pendant la période d'évaluation. Cela sera converti en coût d'incidence annuel sur l'onglet
ReadyToMilkInputViewModel.HealthRecords=Dossiers de santé
ReadyToMilkInputViewModel.Herd=RENSEIGNEMENTS SUR LE TROUPEAU
ReadyToMilkInputViewModel.Ketosis=Acétonémie
ReadyToMilkInputViewModel.LocomotionScore=Score de locomotion
ReadyToMilkInputViewModel.Mastitis=Mammite 0-21 JEL
ReadyToMilkInputViewModel.Metritis=Métrite
ReadyToMilkInputViewModel.MilkFever=Fièvre du lait
ReadyToMilkInputViewModel.MilkPrice=Prix du lait
ReadyToMilkInputViewModel.MilkYield=Production de lait 15-60 JEL
ReadyToMilkInputViewModel.Next=Retour
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Stress de chaleur perçu de 0-21 JEL
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Stress de chaleur perçu en pré-vêlage
ReadyToMilkInputViewModel.PrematureCalvings=Vêlage prématuré
ReadyToMilkInputViewModel.ReplacementCowCost=Coût de remplacement
ReadyToMilkInputViewModel.RetainedPlacenta=Rétention Placentaire
ReadyToMilkInputViewModel.RumenFill=Remplissage du rumen
ReadyToMilkInputViewModel.SccFirstTest=CCS au premier test (x1000 SCC/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Ration spécifique pré-vêlage
ReadyToMilkInputViewModel.SpecificDiet=Ration spécifique 0-21 JEL
ReadyToMilkInputViewModel.Title=Ready2Milk™ Données
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Nombre de vêlage / Année
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Nombre total de vaches
ReadyToMilkListViewModel.Title=Indice Ready2Milk™
ReadyToMilkMasterViewModel.Charts=Graphiques
ReadyToMilkMasterViewModel.Inputs=Données
ReadyToMilkMasterViewModel.MastitisNotPresent=La mammite n'est pas remplie
ReadyToMilkMasterViewModel.Outputs=Résultats
ReadyToMilkMasterViewModel.Title=Indice Ready2Milk™
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Indice Ready2Milk™
ReadyToMilkOutputViewModel.Title=Ready2Milk™ Résultats
RecommendedTLCSettings=Objectif de longueur de coupe théorique (LCT)
RefreshTokenFailed=Erreur d'authentification, vous incrire a nouveau
Reggio_Calabria=Reggio Calabre
Reggio_Emilia=Reggio Emilia
RemovedAndMeasured=Enlevés et mesurés
RemovedOnly=Seulement enlevés
Report=Rapport
Report.Analysis.Type=Type d'analyse
Report.Animal.Analysis=Analyse animale
Report.Animal.Tag.Name=Pièce d'identité animale
Report.AvgRumenFillScore=Moyenne des r sultats du score rumen (calcul e)
Report.BCS.EvalDataTitle=Données d'évaluation BCS calculées
Report.BCS.LactationStages=Étapes de lactation
Report.BCS.Max=État corporel maximum
Report.BCS.MilkHeadDay=Lait / hd / jour
Report.BCSAvg=État corporel moyen
Report.Bcs=État corporel
Report.Bcs.ChartName=Score de condition corporelle - Animal {0}
Report.Bcs.HerdAnalysis.ChartName=BCS vs lait
Report.Bcs.Milk=Lait
Report.Bcs.Min=État corporel minimum
Report.Calving.Date=Vêlage
Report.Cargill.Report=Cargill rapport
Report.Chewing=Animaux qui ruminent - Rumination
Report.Chews=Mâcher
Report.CudChewing.EvalDataTitle=Données d'évaluation CUD CUD CUD
Report.CudChewingPercentage=Rumination,%
Report.CudChewingPercentage.Vs.LactStages=Rumination,%
Report.EvalDataTitle=Données d'évaluation calculées
Report.ForagePennState=Pennstate Fourrages
Report.General.Comments=Commentaires généraux
Report.GoalCudChewingPercentage=Objectif Rumination,%
Report.Heatstress.Dmi.Adjustment=Ajustement DMI
Report.Heatstress.Energy.Equivalent.Milk.Loss=Perte de lait équivalente énergétique ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Admission estimée à la matière sèche ({0})
Report.Heatstress.Intake.Adjustment=Réglage de l'admission
Report.Heatstress.Legend=Légende
Report.Heatstress.Legends=LÃ©gendes
Report.Heatstress.Loss.Of.Energy.Consumed=Perte d'énergie consommée (MCAL)
Report.Heatstress.Mild.Moderate.Stress=Stress doux - modéré
Report.Heatstress.Mild.Moderate.Stress.Message=La respiration dépasse 75 bpm | La température rectale dépasse 39  U2103 (102.2  U2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Perte de valeur de lait (par mois) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Perte de valeur de lait (par jour) ({0})
Report.Heatstress.Moderate.Severe.Stress=Stress modéré - grave
Report.Heatstress.Moderate.Severe.Stress.Message=La respiration dépasse 85 bpm | La température rectale dépasse 40  U2103 (104  U2109)
Report.Heatstress.Reduction.In.Dmi=Réduction de DMI ({0})
Report.Heatstress.Relative.Humidity=Humidité relative (%)
Report.Heatstress.Severe.Stress=Stress sévère
Report.Heatstress.Severe.Stress.Message=La respiration dépasse 120-140 bpm | La température rectale dépasse 41  U2103 (106  U2109)
Report.Heatstress.Stress.Threshold=Seuil de stress
Report.Heatstress.Stress.Threshold.Message=La respiration dépasse 60 bpm | Pertes de repro détectables | La température rectale dépasse 38,5  U2103 (101.3  U2109)
Report.Heatstress.Temperature=Température
Report.Heatstress.Temperature.In.Celcius=Température  U2103
Report.Heatstress.Temperature.In.Farenhiet=Température  U2109
Report.Heatstress.TemperatureHumidityIndex=Indice d'humidité de la température
Report.Herd.Analysis.CudChewingPercentage=Rumination,%
Report.Locomotion.HerdAnalysis.ChartName=Pourcentages de score de locomotion
Report.LocomotionScore.X.Axis=Score de locomotion
Report.LocomotionScore.Y.Axis=Pour cent%
Report.LocomotionScore.chartName=Score de locomotion - Animal {0}
Report.No.OfChews=\# de coups de machoires
Report.No.OfChewsPerRegurgitation=Nombre de mâtes par régurgitation
Report.NoOfChews.Vs.LactStages=\# de coups de machoires
Report.Not.Chewing=Pas mÃ¢cher
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Temps disponible pour le repos
Report.PenTimeBudget.TimeAvailableForResting.Label=Heures
Report.PenTimeBudgetTimeRemaining=Temps restant
Report.PenTimeBudgetTimeRequired=Temps requis
Report.Pentime.Budget.Hours=Heures
Report.PercentageOnScreen=Quantité sur le tamis (%)
Report.RumenHealthManureScreening.Bottom=Bas
Report.RumenHealthManureScreening.BottomGoalMax=But inférieur max
Report.RumenHealthManureScreening.BottomGoalMin=Bott-but Min
Report.RumenHealthManureScreening.Middle=Milieu
Report.RumenHealthManureScreening.MiddleGoalMax=But intermédiaire max
Report.RumenHealthManureScreening.MiddleGoalMin=Objectif moyen min
Report.RumenHealthManureScreening.Top=Haut
Report.RumenHealthManureScreening.TopGoalMax=Top but max
Report.RumenHealthManureScreening.TopGoalMin=Top objectif Min
Report.SheetName=MaÃ®tre de rapport
Report.Tool.Details=Outils DÃ©tails
Report.Tool.Name=Nom d'outil
Report.Visit.Date=Date de visite
Report.Visit.Report=Visite rapport
Report.Visit.name=Nom de visite
Report.goalChews=Mâcher des objectifs
Report.locomotionScore.Pen.Analysis.ChartName=Catégories vs dates de visite
ReportDate=Date du Rapport
Reset_Database=Réinitialiser la base de données
Resources=Ressources
ResourcesViewModel.Title=Ressources fourragères
ResourcesViewModel.VisitNotebook=Cahier de notes
RetainedPlacenta=Rétention Placentaire
Reunion=RÃ©union
Revenue=Revenu
RevenueEditComparisonValuesViewModel.CurrentSCC=CCS actuel (cells / {0})
RevenueEditComparisonValuesViewModel.DownResponse=Relâchement du lait ({0} / vache / jour)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Modifier les valeurs de comparaison
RevenueEditComparisonValuesViewModel.MilkChange=Changement en production (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Prix du lait (euro/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Production de lait
RevenueEditComparisonValuesViewModel.NumOfCows=Nombre de vaches
RevenueEditComparisonValuesViewModel.PotentialSCC=CCS potentiel (Cellules/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Scénario 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Scénario 2
RevenueEditComparisonValuesViewModel.Title=Modifier les valeurs de comparaison
RevenueInputViewModel.ComparisonValues=Valeurs de comparaison
RevenueInputViewModel.Edit=Modifier
RevenueInputViewModel.ScenarioOne=Scénario 1
RevenueInputViewModel.ScenarioTwo=Scénario 2
RevenueInputViewModel.Title=Comparaison de procédure de traite- Données
RevenueLossDay=Perte de revenus ({0} / Jour)
RevenueLossYear=Perte des revenus ({0} / Année)
Rheinland=RhÃ©nanie
Rhode_Island=Rhode Island
Rieti=Rieti
Rimini=Rimini
Rio_Grande_do_Norte=Grande riviÃ¨re nord
Rio_Grande_do_Sul=Rio Grande do sul
Rio_de_Janeiro=Rio de Janeiro
Robot=Robot
RoboticMilkEvaluationAnalysisViewModel.AverageBoxTime=Temps de box moyen (min/vache)
RoboticMilkEvaluationAnalysisViewModel.AverageConcentrate=Moyenne de concentrés
RoboticMilkEvaluationAnalysisViewModel.CowsPerRobot=Nombre de vaches par robot
RoboticMilkEvaluationAnalysisViewModel.MilkingFailures=Échecs de traite (\#/troupeau)
RoboticMilkEvaluationAnalysisViewModel.MilkingRefusals=Refus (\#/vache)
RoboticMilkEvaluationAnalysisViewModel.Milkings=Traites (\#/vache)
RoboticMilkEvaluationAnalysisViewModel.RobotFreeTime=Temps libre (%)
RoboticMilkEvaluationAnalysisViewModel.RoboticMilkingEvaluation=Évaluation Robot de traite
RoboticMilkEvaluationChartsViewModel.AMSUtilization=Temps libre du robot et moyenne de temps de box par vache
RoboticMilkEvaluationChartsViewModel.AverageBoxTime=Moyenne temps de box (min/vache)
RoboticMilkEvaluationChartsViewModel.AverageConcentrate=Moyenne de concentrés
RoboticMilkEvaluationChartsViewModel.AverageConcentrateFed=Moyenne de concentrés ({0}/vache)
RoboticMilkEvaluationChartsViewModel.ConcentrateDistribution=Moyenne de concentrés servus et concentrés par 100kg de lait
RoboticMilkEvaluationChartsViewModel.ConcentratePer100KGMilk=Concentrés par 100 {0} de lait ({0}/100{0} de lait)
RoboticMilkEvaluationChartsViewModel.CowEfficiency=Traites par vache et refus par vache
RoboticMilkEvaluationChartsViewModel.CowsPerRobot=Nombre de vaches par robot
RoboticMilkEvaluationChartsViewModel.DLAverageBoxTime=Moy. durée de production par traite (min/vache)
RoboticMilkEvaluationChartsViewModel.DLAverageConcentrateFed=Moy. de concentrés servis ({0}/vache)
RoboticMilkEvaluationChartsViewModel.DLConcentratePer100KGMilk=Concentrés/lait ratio - ({0}/100 {0} de lait)
RoboticMilkEvaluationChartsViewModel.DLMilkings=Moy. de traites par animal (\#traite/animal)
RoboticMilkEvaluationChartsViewModel.DLRobotFreeTime=Temps en mode veille (%) 
RoboticMilkEvaluationChartsViewModel.DLTotalMilkingFailures=Nb incomplètes (total)
RoboticMilkEvaluationChartsViewModel.GAverageBoxTime=Temps de box moyen (min/vache)
RoboticMilkEvaluationChartsViewModel.GAverageConcentrateFed=Concentrés servis ({0}/vache)
RoboticMilkEvaluationChartsViewModel.GConcentratePer100KGMilk=Conc./100 {0} de lait
RoboticMilkEvaluationChartsViewModel.GMilkings=Nb moyen de traites/vache
RoboticMilkEvaluationChartsViewModel.GTotalMilkingFailures=Nombre de traites incomplètes (total)
RoboticMilkEvaluationChartsViewModel.LelyAverageBoxTime=Temps de box (min/vache) 
RoboticMilkEvaluationChartsViewModel.LelyAverageConcentrateFed=Moyenne concentrés servis ({0}/vache)
RoboticMilkEvaluationChartsViewModel.LelyConcentratePer100KGMilk=Conc./{0} de lait - ({0}/100 {0} de lait)
RoboticMilkEvaluationChartsViewModel.LelyMilkingRefusals=Refus (\#/vache)
RoboticMilkEvaluationChartsViewModel.LelyMilkings=Traites/vache/jour
RoboticMilkEvaluationChartsViewModel.LelyRobotFreeTime=Temps libre (%) 
RoboticMilkEvaluationChartsViewModel.MilkingFailures=Échecs de traites
RoboticMilkEvaluationChartsViewModel.MilkingRefusals=Refus (\#/vache)
RoboticMilkEvaluationChartsViewModel.Milkings=Traites (\#/vache)
RoboticMilkEvaluationChartsViewModel.RobotFreeTime=Temps libre (%)
RoboticMilkEvaluationChartsViewModel.Title=Évaluation Robot de traiten
RoboticMilkEvaluationInputsViewModel.AverageConcentrateFed=Moyenne de concentrés servis ({0}/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.AverageMilkYield=Moyenne de lait (kg/vache)
RoboticMilkEvaluationInputsViewModel.ConcentratePer100KGMilk=Concentrés par 100 {0} de lait ({0}/100{0} de lait) ⃰
RoboticMilkEvaluationInputsViewModel.ConcentratePer100KGMilkMsg=System shows 1 kg/ milk so x 100
RoboticMilkEvaluationInputsViewModel.CowFlowDesign=Type de stabulation  ⃰
RoboticMilkEvaluationInputsViewModel.DLAverageBoxTime=Moy. durée de production par traite (min/vache) ⃰
RoboticMilkEvaluationInputsViewModel.DLAverageConcentrateFed=Moy. de concentrés servis ({0}/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.DLAverageMilkYield=Moyenne de lait ({0}/vache) ⃰
RoboticMilkEvaluationInputsViewModel.DLConcentratePer100KGMilk=Concentrés/lait ratio- ({0}/100 {0} de lait) ⃰
RoboticMilkEvaluationInputsViewModel.DLMilkingSpeed=Moy. débit de lait quotidien par animal ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.DLMilkings=Moy. de traites par animal (\#traite/animal) ⃰
RoboticMilkEvaluationInputsViewModel.DLRestFeed=% Concentrés NON consommés hier  ⃰
RoboticMilkEvaluationInputsViewModel.DLRobotFreeTime=Temps en mode veille (%) ⃰
RoboticMilkEvaluationInputsViewModel.DLTotalMilkingFailures=Nb incomplètes (total) ⃰
RoboticMilkEvaluationInputsViewModel.GAverageBoxTime=Temps de box moyen (min/vache) ⃰
RoboticMilkEvaluationInputsViewModel.GAverageConcentrateFed=Concentrés servis ({0}/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.GAverageMilkYield=Moyenne de lait ({0})/vache ⃰
RoboticMilkEvaluationInputsViewModel.GConcentratePer100KGMilk=Conc./100 {0} de lait ⃰
RoboticMilkEvaluationInputsViewModel.GMilkingSpeed=Vitesse de traite ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.GMilkings=Nb moyen de traites/vache ⃰
RoboticMilkEvaluationInputsViewModel.GRestFeed=Aliments permis non consommés (%)  ⃰
RoboticMilkEvaluationInputsViewModel.GTotalMilkingFailures=Nombre de traites incomplètes (total) ⃰
RoboticMilkEvaluationInputsViewModel.Herd=INFORMATION TROUPEAU
RoboticMilkEvaluationInputsViewModel.LactatingCows=Vaches en lactation  ⃰
RoboticMilkEvaluationInputsViewModel.LelyAverageBoxTime=Temps de box (min/vache) ⃰
RoboticMilkEvaluationInputsViewModel.LelyAverageConcentrateFed=Moyenne concentrés servis ({0}/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.LelyAverageMilkYield=Lait/vache/jour ⃰
RoboticMilkEvaluationInputsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} de lait ({0}/100 {0} de lait) ⃰
RoboticMilkEvaluationInputsViewModel.LelyMilkingRefusals=Refus (\#/vache) ⃰
RoboticMilkEvaluationInputsViewModel.LelyMilkingSpeed=Vitesse de traite ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.LelyMilkings=Traites/vache/jour ⃰
RoboticMilkEvaluationInputsViewModel.LelyRobotFreeTime=Temps libre (%) ⃰
RoboticMilkEvaluationInputsViewModel.LelyTotalMilkingFailures=Échec de traite (total)
RoboticMilkEvaluationInputsViewModel.MaximumConcentrate=Maximum de concentrés ({0}/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.MilkingRefusals=Refus (\#/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.MilkingRefusalsDLMsg=Système statistiques\: refus totaux / nombre de vaches 
RoboticMilkEvaluationInputsViewModel.MilkingRefusalsGEMsg=Pour calculer les refus\: moyenne de visite/vache - moyenne de traite/vache?
RoboticMilkEvaluationInputsViewModel.Milkings=Traites (\#/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.MinimumConcentrate=Minimum de concentrés servis ({0}/vache)  ⃰
RoboticMilkEvaluationInputsViewModel.RestFeed=Reste concentrés (%)  ⃰
RoboticMilkEvaluationInputsViewModel.RestFeedDLMsg=% Concentrés NON cons. hier \= 100% - % cons. hier
RoboticMilkEvaluationInputsViewModel.RestFeedMsg=Pour l'onglet analyser\: calculer les aliments permis non consommés (kg) / total programmé
RoboticMilkEvaluationInputsViewModel.RobotFreeTime=Temps libre (%)  ⃰
RoboticMilkEvaluationInputsViewModel.RobotFreeTimeMsg=temps d'utilisation \= 100% - utilisation système \= temps libre
RoboticMilkEvaluationInputsViewModel.RobotType=Type de robot  ⃰
RoboticMilkEvaluationInputsViewModel.RobotsInHerd=Nombre de robots dans le troupeau  ⃰
RoboticMilkEvaluationInputsViewModel.TotalMilkingFailureMsg=Nombre de traites X % incomplètes
RoboticMilkEvaluationInputsViewModel.TotalMilkingFailures=Échecs de traites totaux (\#/troupeau)  ⃰
RoboticMilkEvaluationMasterViewModel.Analysis=Analyse
RoboticMilkEvaluationMasterViewModel.Inputs=Données
RoboticMilkEvaluationMasterViewModel.Outputs=Résultats
RoboticMilkEvaluationMasterViewModel.Title=Évaluation Robot de traite
RoboticMilkEvaluationMasterViewModel.Trends=Tendances
RoboticMilkEvaluationOutputsViewModel.AMSUtilization=Utilisation du robot de traite
RoboticMilkEvaluationOutputsViewModel.AverageBoxTime=Temps de box moyen (min/vache)
RoboticMilkEvaluationOutputsViewModel.AverageConcentrate=Moyenne de concentrés ({0}/vache)
RoboticMilkEvaluationOutputsViewModel.ConcentrateDistribution=Taux de distribution
RoboticMilkEvaluationOutputsViewModel.ConcentratePer100KGMilk=Concentrés par 100 {0} de lait ({0}/100{0} de lait)
RoboticMilkEvaluationOutputsViewModel.CowEfficiency=Efficacité des vaches
RoboticMilkEvaluationOutputsViewModel.CowsPerRobot=Nombre de vaches par robot
RoboticMilkEvaluationOutputsViewModel.DLAverageBoxTime=Moy. durée de production par traite (min/vache)
RoboticMilkEvaluationOutputsViewModel.DLConcentratePer100KGMilk=Concentrés/lait ratio - ({0}/100 {0} de lait)
RoboticMilkEvaluationOutputsViewModel.DLMilkingSpeed=Moy. débit de lait quotidien par animal ({0}/min) 
RoboticMilkEvaluationOutputsViewModel.DLRestFeed=Refus (\#/vache)  
RoboticMilkEvaluationOutputsViewModel.GAverageBoxTime=Temps de box moyen (min/vache)
RoboticMilkEvaluationOutputsViewModel.GConcentratePer100KGMilk=Conc./100{0} de lait
RoboticMilkEvaluationOutputsViewModel.GMilkingSpeed=Vitesse de traite ({0}/min))
RoboticMilkEvaluationOutputsViewModel.LelyAverageBoxTime=Temps de box (min/vache)
RoboticMilkEvaluationOutputsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} de lait - ({0}/100 {0} de lait)
RoboticMilkEvaluationOutputsViewModel.LelyMilkingSpeed=Vitesse de traite ({0}/min) 
RoboticMilkEvaluationOutputsViewModel.MaximumConcentrate=Maximum de concentrés ({0}/vache)
RoboticMilkEvaluationOutputsViewModel.MilkPerRobot=Lait par robot ({0})
RoboticMilkEvaluationOutputsViewModel.MilkingFailures=Échecs de traite (\#/robot)
RoboticMilkEvaluationOutputsViewModel.MilkingRefusals=Refus (\#/vache)
RoboticMilkEvaluationOutputsViewModel.MilkingSpeed=Vitesse de traite ({0}/min)
RoboticMilkEvaluationOutputsViewModel.Milkings=Traites (\#/vache)
RoboticMilkEvaluationOutputsViewModel.MilkingsPerRobot=Nombre de traites par robot
RoboticMilkEvaluationOutputsViewModel.MinimumConcentrate=Minimum de concentrés servis ({0}/vache)
RoboticMilkEvaluationOutputsViewModel.RestFeed=Refus (\#/vache) 
RoboticMilkEvaluationOutputsViewModel.RobotFreeTime=Temps libre (%)
RoboticMilkEvaluationSpinnerViewModel.Title=Évaluation Robot de traite
RoboticMilkEvaluationTrendsListViewModel.AMSUtilization=Utilisation du robot de traite
RoboticMilkEvaluationTrendsListViewModel.ConcentrateDistribution=Distribution des concentrés
RoboticMilkEvaluationTrendsListViewModel.CowEfficiency=Efficacité des vaches
RoboticMilkEvaluationTrendsListViewModel.VisitComparison=Veuillez sélectionner des visites pour la comparaison
Romania=Roumanie
Rome=Rome
RondÃÂ´nia=RondÃ¢mes
Roraima=Roraima
Roscommon=Roscommon
Rovigo=Rovigo
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Analyse du groupe
RumenHealthBodyConditionLandingViewModel.Pens=Groupes
RumenHealthBodyConditionLandingViewModel.Resources=Ressources
RumenHealthBodyConditionLandingViewModel.Title=État corporel
RumenHealthEditManureScoresViewModel.Close=Fermer
RumenHealthEditManureScoresViewModel.Count=Nombre
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Svp compter le nombre de vaches
RumenHealthEditManureScoresViewModel.NumOfCows=Nombre de vaches
RumenHealthEditManureScoresViewModel.NumberOfCows=nombre de vaches
RumenHealthEditManureScoresViewModel.VisitNotebook=Cahier de notes
RumenHealthLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthLandingViewModel.PenAnalysis=Analyse de groupes
RumenHealthLandingViewModel.Pens=Groupes
RumenHealthLandingViewModel.Title=Santé Ruminale - Rumination
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthLocomotionLandingViewModel.PenAnalysis=Analyse du groupe
RumenHealthLocomotionLandingViewModel.Pens=Groupes
RumenHealthLocomotionLandingViewModel.Resources=Ressources
RumenHealthLocomotionLandingViewModel.Title=Locomotion
RumenHealthManureLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthManureLandingViewModel.PenAnalysis=Analyse du groupe
RumenHealthManureLandingViewModel.Pens=Groupes
RumenHealthManureLandingViewModel.Resources=Ressources
RumenHealthManureLandingViewModel.Title=Santé ruminale - score de fumier
RumenHealthManureMasterViewModel.Inputs=Données
RumenHealthManureMasterViewModel.Results=Résultats
RumenHealthManureMasterViewModel.RumenHealthManureScore=Santé ruminale - score de fumier
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Santé ruminale - score de fumier
RumenHealthManureMasterViewModel.VisitNotebook=Cahier de notes
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Score moyen
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Date
RumenHealthManureScoresResultsViewModel.PercentPen=% du groupe
RumenHealthManureScoresResultsViewModel.SelectedDates=Sélectionnez les dates
RumenHealthManureScoresResultsViewModel.Title=Score de fumier - Résultats
RumenHealthManureScoresViewModel.AnimalsObserved=nombre d'animaux évalués
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Score moyen pour les fumiers (calculé)
RumenHealthManureScoresViewModel.Edit=modifier
RumenHealthManureScoresViewModel.ManureScore=Score de fumier
RumenHealthManureScoresViewModel.PercentOfPen=% du groupe
RumenHealthManureScoresViewModel.ScoreCategory=Score de fumier Catégorie
RumenHealthManureScoresViewModel.StdDevCalculated=Écart type (calculé)
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=CALCULATEURS
RumenHealthPenCudCalculatorViewModel.CudChewing=Rumination
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Nombres de vaches qui ruminent
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Mastication
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Nombre de mastication par cycle de rumination
RumenHealthTMRLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthTMRLandingViewModel.PenAnalysis=Analyse du groupe
RumenHealthTMRLandingViewModel.Pens=Groupes
RumenHealthTMRLandingViewModel.Title=Santé ruminale - penn state
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Poids en gramme (balance)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Poids du tamis (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Objetif max  (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Objetif min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Objectifs
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Milieu 1(8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Milieu 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Milieu 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Nom du séparateur de particules de la RTM
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=TMR Particle Score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Poids du tamis
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Poids en grammes (balance)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Haut (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Tamis
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Ajouter score du RTM
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Score Moyen
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Actuel
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Poids en grammes (balance)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Objetif max  (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Objectif du milieu 1 (8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Objectif du milieu 2 (1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Objectif du milieu 2 (4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Objetif min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Objectifs
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Objectif du haut (19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Objectif du fond
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Max
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Milieu 2 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Milieu 3 (1.18 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Milieu 3 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Min
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Séparateur de particules (% sur les tamis)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Quantité sur le tamis (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Écart-type
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Informations sur le score des particules RTM
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=TMR Particle Score
RumenHealthTMRParticleScorePenTableInputViewModel.Title=Scores des particules RTM
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Haut (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Tamis
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Données
RumenHealthTMRPenScorerTableMasterViewModel.Results=Résultats
RumenHealthTMRPenScorerTableMasterViewModel.Title=Santé ruminale - Grosseur des particules
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Aucun sélectionné
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Un évaluateur doit  être sélectionné avant d'évaluer le group.
RumenHealthTMRSelectPenViewModel.SelectPen=nombre des groupes (vaches en lactation et taries, seulement)
RumenHealthTMRSelectPenViewModel.SelectScorer=Sélectionner votre penn state
RumenHealthTMRSelectPenViewModel.Title=Grosseur des particules
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Aucun sélectionné
RumenHealthTMRSelectScorerViewModel.FooterText=Seulement un modèle d'évaluation peut être utilisé par visite.
RumenHealthTMRSelectScorerViewModel.SelectScorer=Sélectionner votre penn state
RumenHealthTMRSelectScorerViewModel.Title=Grosseur des particules
Russia=Russie
Russian_Federation=FÃ©dÃ©ration Russe
Rwanda=Rwanda
SAR=Arabie Saoudite (﷼ SAR)
SCCPremiumDeduction=Prime / pénalité pour CCS ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Suriname ($ SRD)
Saint_BarthÃ©lemy=Saint BarthÃ©lemy
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Ascension et Tristan da Cunha
Saint_Kitts_and_Nevis=Saint-Christophe-et-NiÃ©vÃ¨s
Saint_Lucia=Sainte-Lucie
Saint_Martin_(French_part)=Saint Martin (partie franÃ§aise)
Saint_Pierre_and_Miquelon=Saint Pierre and Miquelon
Saint_Vincent_and_the_Grenadines=Saint-Vincent-et-les-Grenadines
Salerno=Salerne
Samoa=Samoa
San_Luis_PotosÃ­=San Luis PotosÃ£
San_Marino=Saint Marin
Santa_Catarina=Santa Catarina
Sao_Tome_and_Principe=Sao tome et principe
Saskatchewan=Saskatchewan
Sassari=Sassari
SaudiArabia=Arabie Saoudite
Saudi_Arabia=Arabie Saoudite
Savona=Savona
Schleswig=Schleswig
ScorecardPrompt=Veuillez compléter les questions suivantes pour voir la fiche d'évaluation
Screen=tamis
ScreenNew=tamis - nouveau
ScreenOld=tamis - ancien
Search=Chercher
SegmentViewModel.SegmentTitle=SÉLECTIONNEZ UN SEGMENT
SegmentViewModel.Title=Détails
Select=Sélectionner
SelectCurrencyViewModel.Title=Choisir une devise
SelectDates=Sélectionnez les dates
SelectFeedingSystemViewModel.SelectFeedingSystem=Sélectionner le système d'alimentation
SelectFeedingSystemViewModel.Title=Configuration du groupe
SelectForageImprovement=SVP sélectionnez seulement 12 améliorations parmi la liste
SelectHousingSystemViewModel.SelectHousingSystem=Sélectionner le système de logement
SelectHousingSystemViewModel.Title=Configuration du groupe
SelectImprovement=SVP sélectionner seulement 10 améliorations dans la liste 
SelectMatrix=Sélectionner système de prime
SelectMilkingSystemViewModel.NoneSelected=Aucun sélectionné
SelectMilkingSystemViewModel.SelectMilkingSystem=Selectionnez un système de traite
SelectMilkingSystemViewModel.Title=Configurer le site
SelectOnlyThreeNotes=Veuillez sélectionner seulement 3 notes dans l'outil.
SelectOnlyTwoNotes=SVP ne sélectionnez que 2 notes par outil
SelectPen=Sélectionner une ration pour le nouveau groupe
SelectProcessor=Sélectionner une laiterie
SelectProcessorViewModel.COMPONENT=Composantes
SelectProcessorViewModel.CONCENTRATION=Concentration
SelectProcessorViewModel.Edit=Modifier
SelectProcessorViewModel.MilkProcessors=Laiterie
SelectVisitComparison=Sélectionnez les visites à comparer
SemiAnnually=2 fois par année
Semiconfinamento=Semiconfinamento
Send=Envoyer
Senegal=SÃ©nÃ©gal
Seoul=SÃ©oul
Serbia=Serbie
Sergipe=Sergipe
SettingsViewModel.Imperial=Impérial
SettingsViewModel.Metric=Métrique
SettingsViewModel.Milk_Processor_Set_Up=Configuration de la laiterie
SettingsViewModel.More_Settings=Plus de paramètres
SettingsViewModel.Select_Unit_Of_Measure=Sélectionner l'unité de mesure
Severe=Sévère
Seychelles=les Seychelles
Shaanxi=Shaanxi
Shandong=Shandong
Shanghai=Shanghai
Shanxi=Shanxi
ShortDryPeriod=Tarissement court
ShowEulaViewModel.Accept=Accepter
ShowEulaViewModel.ConfirmationNo=non
ShowEulaViewModel.ConfirmationText=Êtes-vous d'accord avec les termes du Contrat de Licence d'utilisateur dans l'Application Mobile?
ShowEulaViewModel.ConfirmationTitle=Confirmation
ShowEulaViewModel.ConfirmationYes=oui
ShowEulaViewModel.Decline=Décliner
ShowEulaViewModel.Eula=Contrat de licence utilisateur final
ShowEulaViewModel.EulaError=Le contrat de licence de l'utilisateur final (CLUF) ne peut pas être affiché. Connectez-vous à Internet avant d'essayer à nouveau.
ShowEulaViewModel.EulaScreenTitle=Utilisateur
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Déclaration de confidentialité
ShowSyncStatusViewModel.GetAccounts=Données sur le compte reçues
ShowSyncStatusViewModel.GetNotes=Données sur les notes reçues
ShowSyncStatusViewModel.GetVisits=Données sur la visite reçues
ShowSyncStatusViewModel.Title=Résumé de la synchronisation des données
Sichuan=Sichuan
Siena=Sienne
Sierra_Leone=Sierra Leone
Sikkim=Sikkim
SilageBags=Ag-bags
SilageBags_BagsPlacedOnStableWellManagedSurface=Les ag-bags sont placés sur une surface stable et bien gérée toute la saison?
SilageBags_BonusSecureCoverIsUsed=Une couche de sécurité est-elle utilisée?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Le front d'attaque est-il lisse? (aucun signe de couches permettant à l'oxygène de passer)
SilageBags_FaceRemovalRate=Quel est le taux de reprise quotidien?
SilageBags_InspectedForPestHoleDamageRepairOnBasis=les ag-bags sont contrôlés régulièrement et les trous sont réparés?
SilageBags_PorosityScoresConsistently=Quelle est la cote de porosité de l'ensilage selon la matière sèche?
SilageBags_TrashVegRodentControlledAroundBags=les déchets, la végétation et les rongeurs sont maitrisés autour des ag-bags?
SilagePrevention1st=Incontournables d'un ensilage réussi
Sinaloa=Sinaloa
Singapore=Singapour
Sint_Maarten_(Dutch_part)=Sint Marthes (partie nÃ©erlandaise)
Site-Not-Synced-To-Lift=Le site n'a pas été synchronisé avec LIFT ; veuillez contacter l'administrateur pour une résolution
SiteDetailViewModel.AnimalInputsSite=Données des animaux
SiteDetailViewModel.DairyEnteligenReport=Rapport Dairy Enteligen
SiteDetailViewModel.Detailed=Détaillé
SiteDetailViewModel.DietInputsSiteLactating=Données de la ration, Site (Vaches en lactation)
SiteDetailViewModel.DownloadingVisit=Téléchargement de visite en cours…
SiteDetailViewModel.GeneralCustomerSiteSetup=Configuration générale du site
SiteDetailViewModel.GetReportMsg=Téléchargement du rapport en cours ...
SiteDetailViewModel.MainHeading=Visites
SiteDetailViewModel.NetworkErrorMessage=Il n'y a actuellement aucun réseau disponible.
SiteDetailViewModel.NetworkErrorMessageTitle=Erreur du réseau
SiteDetailViewModel.NewVisit=Nouvelle visite
SiteDetailViewModel.ReportDownloadTimeout=Impossible de télécharger le fichier, essayez quand vous avez une meilleure connectivité
SiteDetailViewModel.ReportNotAvailable=Le rapport n'est pas disponible pour téléchargement.
SiteDetailViewModel.ReportNotAvailableTitle=Statut
SiteDetailViewModel.Reports=Rapport Dairy Enteligen
SiteDetailViewModel.Resources=Ressources
SiteDetailViewModel.SiteSetup=Configuration du site
SiteDetailViewModel.Summary=Sommaire
SiteDetailViewModel.Title=Détails du site
SiteDetailViewModel.VisitDownloadPrompt=Voulez-vous récupérer cette visite?
SiteDetailViewModel.VisitNotDownloaded=Visite non téléchargée
SiteDetailViewModel.VisitUnavailable=Les données de visite ne sont pas disponibles.
SiteDetailsResourcesViewModel.Title=Ressources
SiteDetailsSetupViewModel.AnimalInputsSite=Données des animaux
SiteDetailsSetupViewModel.AsFedIntake=Consommation TQS ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=Comptage Bactérien (1,000cfu/mL)
SiteDetailsSetupViewModel.Continue=Continuer
SiteDetailsSetupViewModel.CurrentMilkPrice=Prix du lait actuel ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Jours en lait (JEL)
SiteDetailsSetupViewModel.Delete=Supprimer
SiteDetailsSetupViewModel.DietInputsSiteLactating=Données de la ration, Site (Vaches en lactation)
SiteDetailsSetupViewModel.DietSetup=Configuration – Ration
SiteDetailsSetupViewModel.Diets=ration
SiteDetailsSetupViewModel.DryMatterIntake=Matière sèche ingérée
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Configuration générale du site
SiteDetailsSetupViewModel.LactatingAnimals=Vaches en lactation
SiteDetailsSetupViewModel.MilkFatPercent=Gras %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Autres Solides du Lait %
SiteDetailsSetupViewModel.MilkProteinPercent=Protéine %
SiteDetailsSetupViewModel.MilkYield=Production de lait({0})
SiteDetailsSetupViewModel.MilkingSystem=Système de Traite
SiteDetailsSetupViewModel.NameNotUnique=Un site nommé "{0}" existe déjà. Les noms doivent être uniques.
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=Densité énergétique de la ration (en UFL)
SiteDetailsSetupViewModel.NewSite=Nouveau Site
SiteDetailsSetupViewModel.NullSiteName=Le nom du site, le prix du lait, le système de traite et le lot sont des champs obligatoires. Souhaitez-vous continuer ou supprimer le site?
SiteDetailsSetupViewModel.NumberOfStalls=Nombre de place dans la salle de traite
SiteDetailsSetupViewModel.PenSetup=Configuration du groupe
SiteDetailsSetupViewModel.Pens=Groupes
SiteDetailsSetupViewModel.RationCost=Coût d'alimentation / vache (euro)
SiteDetailsSetupViewModel.SiteMandatoryFields=Le nom du site, le prix du lait, le système de traite et le groupe sont des champs obligatoires. Remplissez tous les champs obligatoires pour continuer.
SiteDetailsSetupViewModel.SiteName=Nom du Site
SiteDetailsSetupViewModel.SiteSetup=Configurer le site
SiteDetailsSetupViewModel.SomaticCellCount=Cellules Somatiques (1,000 cellules/mL)
SiteDetailsSetupViewModel.Title=Détails de site
SiteDetailsSetupViewModel.WeightImperialCWT=CWT
SiteVisitSummary=Rapport sommaire du site
SiteVisitSummaryReport=Rapport sommaire du site
SixToEightLayers=6 à 8 couches
SixToTwelveHours=6 à 12 heures
SixToTwelveInches=7.5 à 15 cm (3 à 6 pouces)
Sligo=Sligo
Slovakia=Slovaquie
Slovenia=SlovÃ¨ne
Solomon_Islands=Les Ã®les Salomon
Somalia=Somalie
SomanticCellCount=Comptage de cellules somatiques
SomaticCellPerML=Cellules Somatiques (cellules/mL)
Sondrio=Sondrio
Sonora=Sonora
SouthAfrica=Afrique du Sud
SouthKorea=Coree du Sud
South_Africa=Afrique du Sud
South_Australia=Australie du Sud
South_Carolina=Caroline du Sud
South_Dakota=Dakota du Sud
South_Georgia_and_the_South_Sandwich_Islands=GÃ©orgie du Sud et Ã®les Sandwich du Sud
South_Sudan=Soudan du sud
Spain=Espagne
Sri_Lanka=Sri Lanka
StandardDeviationScoreTitle=Écart-type
StartDate=Date de début
StatusArchived=Archivé
StatusCompleted=Complété
StatusInProgress=En cours
StdDevCalculated=Écart type (calculé)
Steer=Boeuf
StorageCalculators=Calculateurs d'entreposage 
StrategyToReduceDisplacedAbomasum=Caillette
StrategyToReduceDisplacedAbomasumDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence du déplacement de la caillette (DGC)&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;La stratégie de prévention repose sur la gestion des facteurs de risque car il n'existe actuellement aucune cause directe claire à cette condition.&lt;/p&gt;&lt;p&gt;La prévention comprend la nutrition et la gestion ainsi que la prise en charge des maladies concomitantes \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Contrôle des facteurs de risque nutritionnels \:&lt;ul&gt;&lt;li&gt;Éviter des vaches grasses (idéalement 3,00 ENC au tarissement et au vêlage)&lt;/li&gt;&lt;li&gt;Fournir assez de fourrage à fibre détergente neutre (FDN)&lt;/li&gt;&lt;li&gt;Gérer les caractéristiques physiques de la ration&lt;/li&gt;&lt;li&gt;Faire attention aux exigences minérales&lt;/li&gt;&lt;li&gt;Éviter les autres troubles métaboliques tels que l'hypocalcémie ainsi que les maladies infectieuses qui pourraient diminuer la consommation de matière sèche&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Meilleures pratiques de gestion \:&lt;ul&gt;&lt;li&gt;Assurer une bonne CMS chez les vaches fraîches, en particulier pendant les heures et les jours qui suivent le vêlage&lt;/li&gt;&lt;li&gt;Bien gérer la mangeoire&lt;/li&gt;&lt;li&gt;Augmenter le confort des vaches, réduire les facteurs de stress&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceDystocia=Dystocie
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de la dystocie&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Pour aider à prévenir la dystocie \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;S'assurer que les génisses sont inséminées à l'âge et au poids corporel appropriés&lt;/li&gt;&lt;li&gt;Sélection des taureaux sur la base de la facilité de vêlage&lt;/li&gt;&lt;li&gt;Améliorer la formation du personnel concernant le bon moment et les méthodes d'intervention pendant le vêlage ainsi que des méthodes appropriées pour prendre soin des veaux nouveau-nés en détresse&lt;/li&gt;&lt;li&gt;Réviser la ration en suivant les exigences OptiLac pour les vaches et les génisses à tous les stades en se concentrant sur \:&lt;ul&gt;&lt;li&gt;L'énergie pour maintenir l'évaluation de la condition corporelle et la croissance du fœtus&lt;/li&gt;&lt;li&gt;La prévention du sur-conditionnement des vaches au vêlage&lt;/li&gt;&lt;li&gt;La gestion du risque d'hypocalcémie au sein du troupeau&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=Stratégies pour réduire l'incidence
StrategyToReduceKetosis=Acétonémie
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de la cétose&lt;/strong&gt;&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;Assurer un confort adéquat aux vaches (litière, contrôle du stress thermique et ventilation, limiter les facteurs de stress et le surpeuplement, etc.)&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="2"&gt;&lt;li&gt;Équilibrer les apports alimentaires en fonction des nutriments et des caractéristiques physiques selon les exigences OptiLac. Consulter l'inventaire des produits s'adressant aux vaches en période de transition pour des formulations spécifiques de produits développés pour la prévention de la cétose. Utiliser des fourrages de bonne qualité qui augmenteront l'apport alimentaire et la capacité tampon du rumen.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="3"&gt;&lt;li&gt;Surveiller le changement du NEC (évaluation de la condition corporelle) entre la période de tarissement et le vêlage\: Début de la période tarie à 3,00 NEC et maintenir le NEC pendant la période sèche pour éviter une mobilisation excessive des lipides autour de la période du vêlage.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="4"&gt;&lt;li&gt;Mettre en œuvre un protocole de santé spécifique développé pour les vaches ayant vêlé récemment afin de détecter et de prévenir les troubles métaboliques et les maladies infectieuses. Surveiller l'apport alimentaire, le remplissage du rumen et la rumination.&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=Mammite
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de la mammite&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Les micro-organismes qui causent le plus souvent la mammite peuvent être divisés en deux catégories principales\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Agents pathogènes contagieux qui se propagent principalement de vache à vache pendant la traite &lt;em&gt;(c'est-à-dire streptococcus aureus et staphylocoque doré)&lt;/em&gt;&lt;/li&gt;&lt;li&gt;Pathogènes environnementaux provenant de l'environnement des vaches laitières  &lt;em&gt;(i.e. E. coli&amp;nbsp;et&amp;nbsp;Strep. Uberis)&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;L'intervention doit être adaptée selon la catégorie de bactéries responsable de la mammite.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Contrôle de la mammite contagieuse\:&lt;/strong&gt;\: il n'y a pas de solution simple contre les infections pour tous les pathogènes. Les étapes suivantes aideront\:&lt;ul&gt;&lt;li&gt;Mettre l'accent sur l'hygiène autour et pendant la traite y compris le bain de trayons&lt;/li&gt;&lt;li&gt;Traire les vaches infectées en dernier&lt;/li&gt;&lt;li&gt;Effectuer l'entretien régulier de la machine à traire&lt;/li&gt;&lt;li&gt;Réviser la ration des vaches taries à l'aide des directives OptiLac pour assurer un apport adéquat en nutriments (énergie, antioxydant) et soutenir ainsi la fonction immunitaire&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Contrôler la mammite environnementale&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;Assurer une litière propre et sèche, une étable bien ventilée&lt;/li&gt;&lt;li&gt;Optimiser la densité de stockage&lt;/li&gt;&lt;li&gt;Hygiène générale de la mamelle&lt;/li&gt;&lt;li&gt;La routine de la traite&lt;/li&gt;&lt;li&gt;Réviser la ration des vaches taries à l'aide des directives OptiLac pour assurer un apport adéquat en nutriments (énergie, antioxydant) pour soutenir la fonction immunitaire&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=Mérite
StrategyToReduceMetritisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de la métrite&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Les facteurs de risque de métrite comprennent la rétention placentaire, les lésions de l'appareil génital dues à un vêlage difficile, un protocole de vêlage inadéquat, une zone de vêlage non hygiénique, une carence nutritionnelle comme la vitamine E ou des carences en sélénium et des vaches sur-conditionnées.&lt;/p&gt;&lt;p&gt;Surveiller les points suivants, par ordre décroissant d'importance\:&amp;nbsp;\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Pratiques de vêlage&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Les employés transportent-ils des bactéries dans l'utérus lorsqu'ils aident au vêlage ?&lt;/li&gt;&lt;li&gt;Les vaches sont-elles aidées trop tôt ou trop tard ?&lt;/li&gt;&lt;li&gt;Les veaux sont-ils tirés trop souvent ?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Les pratiques de traitement&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Comment sont traitées les vaches atteintes de rétention placentaire (RP) ou de métrite ?&lt;/li&gt;&lt;li&gt;Existe-t-il un risque de transporter des bactéries de l'environnement extérieur ou du vagin dans l'utérus à ce moment-là ?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="3"&gt;&lt;li&gt;Stress animal&lt;br /&gt;&lt;ul style\="list-style-type\: circle;"&gt;&lt;li&gt;Un stress excessif avant le vêlage peut épuiser le système immunitaire des vaches réduisant la résistance aux infections après le vêlage&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="4"&gt;&lt;li&gt;Nutrition&lt;ul&gt;&lt;li&gt;Revoir l'équilibre de la ration chez les vaches taries en se concentrant sur le contrôle du NEC, l'équilibre minéral et l'apport de nutriments antioxydants (vit E, A, sélénium, zinc et cuivre)&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Autre contrôle de maladie (cétose, DGC)&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMilkFever=Fièvre du lait
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de l'hypocalcémie&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Deux stratégies alternatives pour la prévention de l'hypocalcémie chez les vaches laitières dépendent à 100% de la gestion de la ration.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Utiliser les régimes à faible teneur en Ca pour vaches taries&lt;/li&gt;&lt;li&gt;Utiliser des régimes à faible Différence alimentaire Cation-Anion (DACA) formulés pour vaches en pré-vêlage&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Points à considérer pour contrôler l'hypocalcémie \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Test minéral des fourrages (calcium, phosphore, magnésium, potassium, sodium, soufre et chlorure) dans un laboratoire homologué&lt;/li&gt;&lt;li&gt;Réviser la ration suivant les exigences OptiLac pour les vaches en pré-vêlage et réviser les pratiques de gestion de l'alimentation (porter une attention spéciale aux fourrages ad libitum, à la concentration en K des fourrages, aux minéraux libre choix donnés aux vaches taries et au tri)&lt;/li&gt;&lt;li&gt;Consulter l'inventaire des produits spécifiques développés pour prévenir l'hypocalcémie chez les vaches en transition&lt;/li&gt;&lt;li&gt;Lors de l'utilisation de régimes alimentaires DACA\:&lt;ul&gt;&lt;li&gt;Surveiller attentivement la consommation d'aliments car les sels anioniques peuvent être peu appétant et peuvent réduire la consommation en matière sèche&lt;/li&gt;&lt;li&gt;Surveiller le pH de l'urine pour vérifier l'efficacité des changements de régime alimentaire&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=Rétention Placentaire
StrategyToReduceRetainedPlacentaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;span&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de rétention placentaire&lt;/strong&gt;&lt;/span&gt;&lt;/h4&gt;&lt;p&gt;L'incidence de rétention placentaire est positivement corrélée à l'immunosuppression, aux hormones de stress élevé, à l'hypocalcémie (y compris subclinique), au vêlage assisté et à l'avortement, aux agents infectieux endémiques et à la génétique.&lt;/p&gt;&lt;p&gt;Surveiller les points suivants, par ordre décroissant d'importance \:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Nutrition &lt;br/&gt; &lt;p style\="padding-left\: 30px;"&gt;Réviser les rations pré-vêlage suivant les directives OptiLac en mettant l'accent sur \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Se et vitamine E&lt;/li&gt;&lt;li&gt;Niveaux minéraux (K, Ca, Mg) pour diminuer le risque d'hypocalcémie subclinique et clinique&lt;/li&gt;&lt;li&gt;Utiliser un DCAD faible ou négatif pour diminuer l'incidence de l'hypocalcémie&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Gestion&lt;/li&gt;&lt;ul&gt;&lt;li&gt;Gérer NEC (3,00 au vêlage) en fin de lactation et en période tarie&lt;/li&gt;&lt;li&gt;Réduire au minimum les facteurs de stress dans la zone de pré-vêlage et la zone de vaches fraîches pour diminuer la mobilisation des graisses et l'immunosuppression autour du vêlage&lt;/li&gt;&lt;li&gt;Améliorer le confort des vaches et fournir suffisamment d'espace à la mangeoire (~ 30 cm par vache)&lt;/li&gt;&lt;li&gt;Suivre une procédure de vêlage définie par le vétérinaire&lt;/li&gt;&lt;li&gt;Ne pas aider les vaches à vêler inutilement&lt;/li&gt;&lt;li&gt;Maintenir un environnement propre pour diminuer les risques d'infection utérine&lt;/li&gt;&lt;/ul&gt;&lt;/ol&gt;&lt;/span&gt;
Straw=Paille
Sudan=Soudan
Surinam=Suriname
Suriname=Suriname
SurveyCategories=Choix de l'audit
SurveyOfForages=Gestion des fourrages
SurveyOfForages_AnnualCowNumAndForageNeeds=L'inventaire des fourrages est-il planifié selon la taille du troupeau projetée annuellement?
SurveyOfForages_AshLevelsInCornSilage=Quel est le niveau de cendres?
SurveyOfForages_AshLevelsInHaylage=Quel est le niveau de cendres?
SurveyOfForages_ButyricAcidLevelsInHaylage=Quel est le niveau d'acide butyrique?
SurveyOfForages_CornSilageProcessingScore=Quelle est la longueur de coupe de l'ensilage de maïs?
SurveyOfForages_CornSilageScoreMonitored=Quelle est la dureté des grains dans l'ensilage de maïs?
SurveyOfForages_InspectedForSpoilageAndMold=Tous les ensilages sont-ils vérifiés pour la détérioration et les moisissures? L'ensilage détérioré est-il éliminé?
SurveyOfForages_InventoryIsMonitored=À quelle fréquence l'inventaire est-il évalué?
SurveyOfForages_LacticAcidToAceticAcidLevels=Quel est le niveau d'acide lactique sur acide acétique?
SurveyOfForages_LooseOrFacedFeedWithin=Le surplus d'ensilage prélevé de la façade est alimenté à l'intérieur de \:
SurveyOfForages_NoLooseFeedRemaining=Les refus sont-ils mesurés et enlevés à tous les jours?
SurveyOfForages_SilosSizedForCapacity=La capacité du type d'entreposage est-elle suffisante pour les besoins du troupeau? (Pas de surcharge)
SurveyOfForages_VisibleSignsOfSoil=Est-ce que l'ensilage est exempt de tout signe visible de contamination par la terre? 
Svalbard_and_Jan_Mayen=Svalbard et Jan Mayen
Swaziland=Swaziland
Sweden=SuÃ¨de
Switzerland=Suisse
Sync-failed-due-to-unknown-reason=please contact the admin for resolution.
SyncFailed=La synchronisation n'a pas pu être complétée en ce moment, réessayez.
Sync_Data=synchroniser l'information
Syracuse=Syracuse
Syrian_Arab_Republic=RÃ©publique arabe syrienne
SystemGenerated=Système généré
SÃ£o_Paulo=SÃ£ Â£ o Paulo
THB=Thaïlande (THB THB)
TMR=RTM
TMRHerdAnalysisTableTitle=Analyse du troupeau - Grosseur de particules dans la RTM
TMRParticleScore=Pennstate RTM
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Fermer
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Jours en lait (JEL)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Modifier les jours en lait 
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=Jours en lait
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Modifier
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Milieu 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Milieu 2
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Analyse de la taille des particules
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Haut
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Tamis
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Analyse du troupeau
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Données
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Résultats
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Santé Ruminale - Grosseur des particules
TMRParticleScoreHerdAnalysisResultsText=Grosseur des particules de la RTM
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Milieu 1 (8 mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Milieu 2 (4 mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Haut (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Tamis
TRY=TRY
TWD=Taïwan (NT$ TWD)
Tabasco=Tabasco
Taipei_City=Ville de Taipei
Taiwan=Taïwan
Tajikistan=Tadjikistan
Tamaulipas=Tamoulipas
Tamil_Nadu=Tamil Nadu
Tanzania,_United_Republic_of=Tanzanie, RÃ©publique unie de
Taranto=Tarente
Task=Tâche
Tasmania=Tasmanie
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=Tennessee
Teramo=TÃ©ramo
Terni=Terni
Texas=Texas
TextureFeed=Moulée texturée
Thailand=Thaïlande
Thirdparty=Tiers
ThisVisit=(Cette visite)
ThreePotentialStorageSolutions=Trois solutions potentielles d'entreposage des fourrages
ThreeScreen=3 tamis -
ThreeTimesPerWeek=3 fois par semaine
Tianjin=Tianjin
Tiestall=Stabulation entravée
TimeRemaining=Temps restant pour le repos
Timor-Leste=Timor Read
Tipperary=Tipperary
Tlaxcala=Tlaxcala
Tocantins=Tocantins
Togo=Aller
Tokelau=Tokelau
Tokyo=Tokyo
Tonga=ArrivÃ©
TonsAF=TONNES TQS
TonsAFSilo=Tonnes TQS (restantes dans le silo)
TonsDM=Tonnes MS
TonsDMSilo=Tonnes MS (restantes dans le silo))
TonsPerDay=Tons Per Day
ToolNotSelected=Vous devez sélectionner au moins un outil
Top=Haut
TopUnloadingSilo=Silo à déchargement par le haut
TopUnloadingSilos=Silo à déchargement par le haut
TopValue=(19mm)
Total=Total
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=Nombre d'animaux total
TotalAnimalsHerd=Total des animaux
TotalPenPerScore=Nombre total d'animaux par score
TotalRevenue=Revenu total
TowerSilos=Silo tour
TowerSilos_FaceRemovalRateGreaterThan4Inches=Le taux de reprise quotidien est-il supérieur à 10 cm (4 pouces)?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Après le remplissage, le silo est-il fermé pendant 1 mois s'il n'est pas immédiatement alimenté?
TowerSilos_SiloFillingTimeof3DaysOrLess=Le remplissage du silo prend-il moins de 3 jours?
TransitionCow=Vache en transition
Trapani=Trapani
Tray=Tamis
Trends=Tendances positives
Trento=Trento
Treviso=Treviso
Trieste=Trieste
Trinidad_and_Tobago=TrinitÃ©-et-Tobago
Tripura=Tripura
Tunisia=Tunisie
Turin=Turin
Turkey=Turquie
Turkmenistan=TurkmÃ©nistan
Turks_and_Caicos_Islands=Ã®les Turques-et-CaÃ¯ques
Tuvalu=Tuvalu
TwelveInchesOrGreater=Plus de 15 cm (6 pouces)
TwentyFourHoursAndNoSync=Vingt-quatre heures et aucune synchronisation
TwentyFourHoursBeforeActionIsDue=Vingt-quatre heures avant que l'action ne soit due
TwentyFourToThirtySixInchesPerDay=15 à 30 cm (6 à 12 pouces) par jour
TwicePerWeek=2 fois par semaine
UAH=Ukraine (UAH UAH)
UK=Royaume-Uni
UNITED_STATES=États-Unis
US=États-Unis d'Amérique
USD=États-Unis d'Amérique ($ USD)
Udine=Udine
Uganda=Ouganda
Ukraine=Ukraine
UnitedKingdom=Royaume-Uni
UnitedStates=États-Unis d'Amérique
United_Arab_Emirates=Emirats Arabes Unis
United_Kingdom=Royaume-Uni
United_States=Ãtats-Unis
UrinePH=pH urinaire
UrinePHAVG=pH urinaire moyen
UrinePHAverageNumber=Nombre moyen
UrinePHDensity=Ressource du pH urinaire
UrinePHEditCowViewModel.AddNew=Prochaine vache
UrinePHEditCowViewModel.CowName=Nom de la vache
UrinePHEditCowViewModel.CowValue=Valeur de la vache
UrinePHEditCowViewModel.UrinePHEnterCowValue=Entrer la valeur de la vache
UrinePHEditCowViewModel.ValidCudInput=SVP,entrer des données valides
UrinePHEditGoalViewModel.GoalMax=Objectif - Max
UrinePHEditGoalViewModel.GoalMin=Objectif - Min
UrinePHEditGoalViewModel.TargetUrinePHRange=Valeurs cibles pour pH urinaire
UrinePHEditGoalViewModel.Title=Modifier objectifs
UrinePHInputsViewModel.AddNew=Ajouter un nouveau
UrinePHInputsViewModel.CalculatorHeading=Veuillez sélectionner une vache pour saisir la valeur du pH urinaire. Appuyez sur "Ajouter Nouveau" pour ajouter des vaches à la liste.
UrinePHInputsViewModel.CoefficientVariation=Coefficient de Variation (CV) (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Vaches en dehors des valeurs types (%)
UrinePHInputsViewModel.CudChewCategorySection=Vaches
UrinePHInputsViewModel.DietDCAD=ration BACA,  mEq/100g
UrinePHInputsViewModel.Resources=Ressources
UrinePHInputsViewModel.TargetUrinePHRange=Plage cible pour pH urinaire
UrinePHInputsViewModel.UrinePHAVG=pH urinaire moyen (Calculé)
UrinePHInputsViewModel.UrinePhSTDDEV=Ecart type (Calculé)
UrinePHMasterViewModel.Inputs=Données
UrinePHMasterViewModel.Results=Résultats
UrinePHMasterViewModel.Title=pH urinaire
UrinePHPenSelectionViewModel.Title=pH urinaire
UrinePHPenSelectionViewModel.UrinePHPenList=Groupe (Vaches en lactation et fraichent seulement)
UrinePHResultsViewModel.DietDCAD=ration BACA,  mEq/100g
UrinePHResultsViewModel.MaxpH=pH max
UrinePHResultsViewModel.MinpH=pH min
UrinePHResultsViewModel.UrinePHAVG=pH urinaire moyen
Uruguay=Uruguay
UserCreated=Créé par l'utilisateur
UserPreferencesViewModel.Branding=Choisir votre logo corporatif
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=Choisir une devise
UserPreferencesViewModel.Imperial=Impérial
UserPreferencesViewModel.MainHeading=Les options suivantes peuvent être modifiées plus tard dans les paramètres de l'application
UserPreferencesViewModel.Metric=Métrique
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Choisir une devise
UserPreferencesViewModel.SelectPointScale=Sélectionnez une échelle de point
UserPreferencesViewModel.Title=Paramètres de l'utilisateur
UserPreferencesViewModel.UnitOfMeasure=Selectionner votre unité de mesure
UserPreferencesViewModel.UserPreferencesMilkProcessor=Configurer la laiterie
UserPreferencesViewModel.UserPreferencesMoreSettings=PLUS DE CONFIGURATIONS
User_Settings=Paramètres de l'utilisateur
Utah=Utah
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
Uzbekistan=OuzbÃ©kistan
VEF=Vénézuela (Bs VEF)
VND=Vietnam (₫ VND)
Vanuatu=Vanuatu
Varese=Varese
Venezuela=Vénézuela
Venezuela,_Bolivarian_Republic_of=Venezuela, RÃ©publique bolivarienne de
Venice=Venise
Veracruz=Veracruz
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Vermont=Vermont
Verona=VÃ©rone
Vestland=Westland
Vibo_Valentia=Vibo Valentia
Vicenza=Vicenza
Victoria=Victoria
Viet_Nam=Vietnam
Vietnam=Vietnam
ViewOverallCalfHaiferScore=Voir le score Global de l'outil Veaux et Génisses
ViewOverallForageScore=Voir le résultat global de la gestion des fourrages
Virgin_Islands,_British=Ãles vierges, britanniques
Virginia=Virginie
Visit.Report.Footer.Patent=Cargill Incorporated, ses sociétés mères ou affliliées ne jusitifie pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. ©2023 Cargill, Incorporated. Tous les droit sont réservés
VisitAutoPublished=Visite auto Publiée
VisitDate=Date de visite
VisitDownloadProceed=Poursuivre
VisitNotebook=Voir le carnet de notes
VisitNotesViewModel.Action=Action
VisitNotesViewModel.Close=Fermer
VisitNotesViewModel.DownloadingNotes=Téléchargement des notes
VisitNotesViewModel.DownloadingNotes1=Téléchargement du rapport…
VisitNotesViewModel.Event=Évenement
VisitNotesViewModel.New=Nouveau
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=Observation
VisitNotesViewModel.Task=Tâche
VisitNotesViewModel.Title=Cahier de notes
VisitNotesViewModel.VisitNotebook=Voir le cahier de notes
VisitSummaryViewModel.CalfHeiferItem=Veaux et Génisses
VisitSummaryViewModel.CalfHeiferScorecard=Fiche d'évaluation
VisitSummaryViewModel.CategorySection=Catégories d'outils
VisitSummaryViewModel.ComfortHeatStressBanner=Outil pour l'evaluation du stress thermique
VisitSummaryViewModel.ComfortItem=Confort
VisitSummaryViewModel.EmailReport=Rapport imprimé
VisitSummaryViewModel.FreeFormReport=Rapport libre
VisitSummaryViewModel.HealthItem=Santé
VisitSummaryViewModel.HeatstressEvaluationTitle=Évaluation du stress thermique
VisitSummaryViewModel.HerdAnalysis=Analyse du troupeau
VisitSummaryViewModel.InputsOutputsChart=Données / Résultats / Graphiques
VisitSummaryViewModel.MilkProcessRevenueCalculator=Comparaison de procédure de traite
VisitSummaryViewModel.NoToolPrompt=Aucun outil n'a été complété.
VisitSummaryViewModel.NutritionForage=Evaluation des fourrages
VisitSummaryViewModel.NutritionItem=Nutrition
VisitSummaryViewModel.NutritionPile=Inventaires des fourrages
VisitSummaryViewModel.PenTimeTitle=Allocation du temps
VisitSummaryViewModel.ProductivityItem=Productivité
VisitSummaryViewModel.RumenHealthBodyConditionTitle=État corporel
VisitSummaryViewModel.RumenHealthLocomotionTitle=Score de Locomotion
VisitSummaryViewModel.RumenHealthManureTitle=Santé du rumen - Score de fumier
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
VisitSummaryViewModel.RumenHealthTMRTitle=Santé du rumen - Grosseur de particules de la ration mélangée
VisitSummaryViewModel.RumenHealthTitle=Santé du rumen - mastication
VisitSummaryViewModel.RumenHealthUrinePHTitle=pH urinaire
VisitSummaryViewModel.Title=Sommaire de la visite
VisitSummaryViewModel.VisitSummaryMilkCalc=Données / Résultats / Ressources
VisitSummaryViewModel.VisitTitle=NOM DE VISITE
VisitViewModel.CalfHeiferItem=Veaux et Génisses
VisitViewModel.CategorySection=Catégorie d'outils
VisitViewModel.ComfortItem=Confort
VisitViewModel.Delete=Supprimer la visite
VisitViewModel.DeletePrompt=Êtes-vous sûr de vouloir publier cette visite? Cela ne peut pas être annulé.
VisitViewModel.HealthItem=Santé
VisitViewModel.Instructions=Choissisez un outils catégorie ou rapport de la liste ci-dessous
VisitViewModel.NullVisitName=Le nom de la visite ne peut pas être vide. Entrez un nom de visite.
VisitViewModel.NutritionItem=Nutrition
VisitViewModel.ProductivityItem=Productivité
VisitViewModel.Publish=Publier
VisitViewModel.PublishNotes=Publier les notes
VisitViewModel.PublishNotesPrompt=Êtes-vous sûr de vouloir publier les notes de cette visite? Cela ne peut pas être annulé.
VisitViewModel.PublishPrompt=Êtes-vous sûr de vouloir publier cette visite? Cela ne peut pas être annulé.
VisitViewModel.PublishVisit=Publier la visite
VisitViewModel.SiteVisitSummary=Sommaire de la vite
VisitViewModel.Title=Détails de la visite
VisitViewModel.ToolCategories=Catégories d'outils
VisitViewModel.VisitNotebook=Cahier de notes
VisitViewModel.VisitTitle=Nom de la visite
VisitViewModel.WalkthroughReport=Rapport de visite
Viterbo=Viterbo
VolumeImperial=gal
VolumeMetric=ml
WalkthroughPenSelectionViewModel.Pens=GROUPES
WalkthroughPenSelectionViewModel.Title=Rapport pas-à-pas
WalkthroughReport=Rapport pas-à-pas
WalkthroughReportHerdAnalysisViewModel.Appearance=Observation d'état
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Propreté de la litière
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Litière\: profondeur et douceur
WalkthroughReportHerdAnalysisViewModel.Branding=NOM DE MARKETING
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Confort de la vache,% de animaux couchés
WalkthroughReportHerdAnalysisViewModel.Comments=COMMENTAIRES
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Compte des bolus, mastication par bolus
WalkthroughReportHerdAnalysisViewModel.CudChewing=Rumination,% mastication
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} Rapport
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} Rapport
WalkthroughReportHerdAnalysisViewModel.ExportSelected=selection de l'outil de courriel
WalkthroughReportHerdAnalysisViewModel.FinalObservations=OBSERVATIONS FINALES
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Créer le rapport
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Abrasion des jarrets,% des animaux
WalkthroughReportHerdAnalysisViewModel.MainHeading=Rapport pas-à-pas
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Décharge nasale,% d'animaux
WalkthroughReportHerdAnalysisViewModel.Notes=Notes
WalkthroughReportHerdAnalysisViewModel.Opportunities=OPPORTUNITÉS
WalkthroughReportHerdAnalysisViewModel.PensForExport=GROUPES POUR EXPORTER
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Remplissage du rumen
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=État corporel
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Score de locomotion
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Score de fumier
WalkthroughReportHerdAnalysisViewModel.SubHeading=Analyse du troupeau
WalkthroughReportHerdAnalysisViewModel.Title=Rapport pas-à-pas-  Analyse du troupeau
WalkthroughReportHerdAnalysisViewModel.Trends=TENDANCES POSITIVES
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Décharge utérine,% d'animaux
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Qualité d'eau
WalkthroughReportLandingViewModel.HerdAnalysis=Analyse du troupeau
WalkthroughReportLandingViewModel.PenAnalysis=Analyse de groupes
WalkthroughReportLandingViewModel.Title=Rapport pas-à-pas
WalkthroughReportQualityViewModel.BeddingCleanliness=Sélectionnez la propreté de la litière
WalkthroughReportQualityViewModel.Clean=Propre
WalkthroughReportQualityViewModel.Dirty=Sale
WalkthroughReportQualityViewModel.ModeratelyClean=Modérément propre
WalkthroughReportQualityViewModel.Title=Rapport pas-à-pas
WalkthroughReportQualityViewModel.WaterQuality=Sélectionnez la qualité de l'eau
WalkthroughReportViewModel.Appearance=Apparence
WalkthroughReportViewModel.BeddingCleanliness=Propeté de la litière
WalkthroughReportViewModel.BeddingDepthSoft=Litière\: profondeur et douceur
WalkthroughReportViewModel.Clean=Propre
WalkthroughReportViewModel.ComfortItem=Confort de la vache,% de vaches couchées
WalkthroughReportViewModel.Comments=Commentaires
WalkthroughReportViewModel.CudChewCategorySection=Nombre de bolus et mastication par bolus
WalkthroughReportViewModel.CudChewing=Rumination,% mastication
WalkthroughReportViewModel.Current=Actuel
WalkthroughReportViewModel.Dirty=Sale
WalkthroughReportViewModel.Goals=Objectif
WalkthroughReportViewModel.HockAbrasion=Abrasion de jarrets,% des animaux
WalkthroughReportViewModel.ModeratelyClean=Modérément propre
WalkthroughReportViewModel.NasalDischarge=Décharge nasale,% d'animaux
WalkthroughReportViewModel.Opportunities=Opportunités
WalkthroughReportViewModel.Previous=Précédent
WalkthroughReportViewModel.RumenFill=Ramplissage du rumen
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=État corporel
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Score de locomotion
WalkthroughReportViewModel.RumenHealthManureTitle=Score de fumier
WalkthroughReportViewModel.Title=Rapport pas-à-pas
WalkthroughReportViewModel.Trends=Tendances positives
WalkthroughReportViewModel.UterineDischarge=Décharge utérine,% d'animaux
WalkthroughReportViewModel.WaterQuality=Qualité d'eau
Wallis_and_Futuna=Wallis et Futuna
Washington=Washington
Waterford=Waterford
Weekly=Hebdomadaire
WeightDMInLengthImperial=Lbs matière sèche dans 1 pied
WeightDMInLengthMetric=Kgs matière sèche dans 1 mètre
WeightImperial=lbs
WeightImperialCWT=cwt
WeightMetric=kg
West_Bengal=Bengale-Occidental
West_Virginia=Virginie-Occidentale
Western_Australia=Australie occidentale
Western_Sahara=Sahara occidental
Westmeath=Ã l'ouest
Wexford=Wexford
Wicklow=Moustiquaire
Wisconsin=Wisconsin
WithinEightHours=à l'intérieur de 8 heures
Wyoming=Wyoming
Xinjiang=Xinjiang
Xizang=Xizang
Yemen=YÃ©men
Yes=Oui
YucatÃ¡n=YucatÄn
Yukon_Territories=Territoires du Yukon
Yunnan=Yunnan
ZAR=Afrique du Sud (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=Zambie
Zhejiang=Zhejiang
Zimbabwe=Zimbabwe
welcome.message=Hallo {0}
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conv.
PMR=RPM
CompleteFeed=Moulée Complète
Supplement=Supplément
Ingredients=Ingrédients
RoundBales=Ensilage ou balle ronde
Silage=Ensilage
SmallGrainSilage=Ensilage de céréales
DryCorn=Sec Maïs
HighMoistureCorn=Humide Maïs
Barley=Orge
MixedGrain=Avoine
Wheat=Blé
DryHay=Foin sec
Oats=Avoine
Cobmeal=Maïs-épi
Soybeans=Fève soya
butterfat=Matière Grasse
protein=Protéine
lactoseAndOtherSolids=Lactose et autres solides
deductions=Déductions
class2Protein=Classe 2 Protéine
class2LactoseAndOtherSolids=Lactose et autres solides
Report.Return.Over.Feed.YAxis=Retour sur l alimentation ($/vache/jour)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole





