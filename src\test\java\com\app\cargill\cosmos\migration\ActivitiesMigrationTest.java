/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.migration.CosmosDataMigration.MigrationType;
import com.app.cargill.cosmos.model.ActivityCosmos;
import com.app.cargill.cosmos.repo.ActivitiesCosmosRepository;
import com.app.cargill.model.Activities;
import com.app.cargill.repository.ActivitiesRepository;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
class ActivitiesMigrationTest {
  @Mock private ActivitiesCosmosRepository cosmosRepository;

  @Mock private ActivitiesRepository dbRepository;

  @InjectMocks private ActivitiesMigration migration;

  @Test
  void whenMigrationPassesCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    ActivityCosmos activityCosmos1 = new ActivityCosmos();
    activityCosmos1.setId(UUID.randomUUID().toString());

    ActivityCosmos activityCosmos2 = new ActivityCosmos();
    activityCosmos2.setId(UUID.randomUUID().toString());

    Flux<ActivityCosmos> cosmosFlux = Flux.just(activityCosmos1, activityCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(Activities.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(2, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenMigrationPassesButSomeRecordsFailCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    ActivityCosmos activityCosmos1 = new ActivityCosmos();
    activityCosmos1.setId(UUID.randomUUID().toString());

    ActivityCosmos activityCosmos2 = new ActivityCosmos();
    activityCosmos2.setId(UUID.randomUUID().toString());

    ActivityCosmos activityCosmos3 = new ActivityCosmos();
    activityCosmos3.setId(UUID.randomUUID().toString());

    Flux<ActivityCosmos> cosmosFlux = Flux.just(activityCosmos1, activityCosmos2, activityCosmos3);
    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.findByDocumentId(any()))
        .thenThrow(new IllegalStateException())
        .thenReturn(null);
    when(dbRepository.save(any()))
        .thenThrow(new RuntimeException())
        .thenReturn(mock(Activities.class));

    MigrationResult migrationResult = migration.moveAll().get();

    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(2, migrationResult.getFailed());
  }

  @Test
  void whenDataIsWrongThePipelinePassesButFailsAreMarked()
      throws ExecutionException, InterruptedException {
    ActivityCosmos activityCosmos1 = new ActivityCosmos();
    activityCosmos1.setId(UUID.randomUUID().toString());

    ActivityCosmos activityCosmos2 = new ActivityCosmos();
    activityCosmos2.setId("random-string");

    Flux<ActivityCosmos> cosmosFlux = Flux.just(activityCosmos1, activityCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(Activities.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(1, migrationResult.getFailed());
  }

  @Test
  void correctTypeIsReturned() {
    assertEquals(MigrationType.ACTIVITIES, migration.migrationType());
  }
}
