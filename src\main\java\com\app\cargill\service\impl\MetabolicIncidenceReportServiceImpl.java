/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.*;
import com.app.cargill.dto.MetabolicDisorderCostPerCowDto;
import com.app.cargill.dto.MetabolicIncidenceExportImageReportDto;
import com.app.cargill.dto.MetabolicIncidenceReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.AxisCrossBetween;
import org.apache.poi.xddf.usermodel.chart.AxisCrosses;
import org.apache.poi.xddf.usermodel.chart.BarDirection;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("metabolicIncidenceReportServiceImpl")
@RequiredArgsConstructor
public class MetabolicIncidenceReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;
  private final List<byte[]> metabolicDisorderCostPerCowColors =
      Arrays.asList(
          new byte[] {(byte) 103, (byte) 183, (byte) 220},
          new byte[] {(byte) 103, (byte) 148, (byte) 220},
          new byte[] {(byte) 128, (byte) 103, (byte) 220},
          new byte[] {(byte) 163, (byte) 103, (byte) 220},
          new byte[] {(byte) 220, (byte) 103, (byte) 206},
          new byte[] {(byte) 220, (byte) 105, (byte) 103},
          new byte[] {(byte) 220, (byte) 175, (byte) 103});
  private final List<byte[]> metabolicIncidencePercentageColors =
      Arrays.asList(
          new byte[] {(byte) 85, (byte) 194, (byte) 190},
          new byte[] {(byte) 217, (byte) 135, (byte) 115});

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    MetabolicIncidenceReportDto dto = modelMapper.map(data, MetabolicIncidenceReportDto.class);
    dto.setMetabolicTypeKeys(
        Arrays.stream(MetabolicTypeKeys.values()).map(Enum::name).toArray(String[]::new));

    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));

      int totalSheetColumns = 0;
      if (dto.getGraphType() == MetabolicIncidenceGraphExportType.METABOLIC_INCIDENCE_PERCENT) {
        // create sheet 0
        XSSFSheet sheet =
            addMetabolicIncidencePercentageSheet(
                wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        totalSheetColumns = sheet.getLastRowNum();

      } else if (dto.getGraphType()
          == MetabolicIncidenceGraphExportType.METABOLIC_DISORDER_COST_PER_COW) {
        // create sheet 1
        XSSFSheet sheet =
            addMetabolicIncidenceCostPerCowSheet(
                wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        totalSheetColumns = sheet.getLastRowNum();

      } else if (dto.getGraphType() == MetabolicIncidenceGraphExportType.BOTH) {
        // create sheet 1 and sheet 2
        addMetabolicIncidencePercentageSheet(
            wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        XSSFSheet sheet =
            addMetabolicIncidenceCostPerCowSheet(
                wb, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
        totalSheetColumns = sheet.getLastRowNum();
      }
      return ExcelUtils.finalizeWorkbook(wb, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  private XSSFSheet addMetabolicIncidenceCostPerCowSheet(
      XSSFWorkbook wb,
      ResourceBundleMessageSource source,
      Locale locale,
      MetabolicIncidenceReportDto dto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    XSSFSheet sheet =
        wb.createSheet(
            ExcelUtils.getLangValue(
                    LangKeys.REPORT_METABOLIC_INCIDENCE_SHEET_1,
                    "Metabolic Disorder Cost/Cow",
                    null,
                    source,
                    locale)
                .replace("/", "⧸"));
    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        greyCellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_METABOLIC_INCIDENCE_SHEET_1, null, source, locale));
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // setting visit dates first
    cellNumber.set(1);

    int metabolicTypeHeadingStartRowNumber = rowNumber.get();

    row = sheet.createRow(rowNumber.getAndIncrement());
    writeMetabolicTypeHeadingValues(source, locale, centerBlack, cellNumber, row, dto);

    // Metabolic Disorder Cost Per Cow
    List<MetabolicDisorderCostPerCowDto> sortedVisits = getSortedVisitObject(dto, locale);
    List<MetabolicDisorderCostPerCowDto> metabolicDisorderCostPerCowDtoList =
        getListOfCostPerCowDataPoints(dto);
    int costPerCowStartRowNumber = rowNumber.get();
    for (MetabolicDisorderCostPerCowDto formattedVisitDateDto : sortedVisits) {
      // first rest cell and create row
      cellNumber.set(0);
      XSSFRow tempRow = sheet.createRow(rowNumber.getAndIncrement());
      // set date in first cell

      ExcelUtils.highlightEmptyCell(
          tempRow,
          formattedVisitDateDto.getFormattedVisitDate(),
          cellNumber,
          boldStyle,
          greyCellStyle);

      dto.getMetabolicDisorderCostPerCow().entrySet().stream()
          .forEachOrdered(
              entry -> {
                Optional<MetabolicDisorderCostPerCowDto> first =
                    metabolicDisorderCostPerCowDtoList.parallelStream()
                        .filter(
                            obj ->
                                obj.getMetabolicIncidenceCase().contentEquals(entry.getKey())
                                    && obj.getVisitDate()
                                        .contentEquals(formattedVisitDateDto.getVisitDate()))
                        .findFirst();

                ExcelUtils.highlightEmptyCell(
                    tempRow,
                    first.orElse(MetabolicDisorderCostPerCowDto.builder().build()).getCost(),
                    cellNumber,
                    decimalStyle,
                    greyCellStyle);
              });
    }
    // create data sources
    // y0 axis
    int columnStart = 1;
    int columnEnd = columnStart + dto.getMetabolicDisorderCostPerCow().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFBarChartData dataLeft;
    Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first Bar chart======================
    chart =
        ExcelUtils.initChart(
            sheet,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_1, null, source, locale),
            chartCol0,
            3,
            chartCol0 + 12,
            23);

    ExcelUtils.initLegends(chart);

    bottomAxis = ExcelUtils.createBottomAxis(chart, "");

    leftAxis = ExcelUtils.createLeftAxis(chart, dto.getMetabolicDisorderCostPerCowLabel());
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
    // create chart data
    dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);
    // create series
    XDDFDataSource<String> metabolicTypeHeadingDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet,
            new CellRangeAddress(
                metabolicTypeHeadingStartRowNumber,
                metabolicTypeHeadingStartRowNumber,
                columnStart,
                columnEnd));
    for (int sv = 0; sv < sortedVisits.size(); sv++) {

      XDDFNumericalDataSource<Double> costPerCow0DataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  costPerCowStartRowNumber, costPerCowStartRowNumber, columnStart, columnEnd));
      series = (Series) dataLeft.addSeries(metabolicTypeHeadingDataSource, costPerCow0DataSource);
      series.setTitle(
          sheet.getRow(costPerCowStartRowNumber).getCell(0).getStringCellValue(),
          new CellReference(sheet.getSheetName(), costPerCowStartRowNumber++, 0, true, true));
    }
    chart.plot(dataLeft);
    ExcelUtils.setBarColorInBarChart(chart, metabolicDisorderCostPerCowColors, sortedVisits.size());

    return sheet;
  }

  private List<MetabolicDisorderCostPerCowDto> getListOfCostPerCowDataPoints(
      MetabolicIncidenceReportDto dto) {
    List<MetabolicDisorderCostPerCowDto> metabolicDisorderCostPerCowDtoList = new ArrayList<>();
    dto.getMetabolicDisorderCostPerCow().entrySet().stream()
        .forEachOrdered(
            entry -> {
              for (int index = 0; index < entry.getValue().size(); index++) {
                MetabolicDisorderCostPerCowDto tempObj =
                    modelMapper.map(
                        entry.getValue().get(index), MetabolicDisorderCostPerCowDto.class);
                tempObj.setMetabolicIncidenceCase(entry.getKey());
                metabolicDisorderCostPerCowDtoList.add(tempObj);
              }
            });
    return metabolicDisorderCostPerCowDtoList;
  }

  private List<MetabolicDisorderCostPerCowDto> getSortedVisitObject(
      MetabolicIncidenceReportDto dto, Locale locale) {
    // init object with formatted date and Instant object
    DateTimeFormatter monthDayFormatter =
        DateTimeFormatter.ofPattern("MM/dd").withZone(ZoneId.of("UTC"));
    DateTimeFormatter monthDayYearFormatter =
        DateTimeFormatter.ofPattern("MM/dd/yy").withZone(ZoneId.of("UTC"));
    List<MetabolicDisorderCostPerCowDto> visits = new ArrayList<>();
    dto.getMetabolicDisorderCostPerCow().entrySet().stream()
        .forEachOrdered(
            index -> {
              for (int costIndex = 0; costIndex < index.getValue().size(); costIndex++) {
                MetabolicDisorderCostPerCowDto cost =
                    modelMapper.map(
                        index.getValue().get(costIndex), MetabolicDisorderCostPerCowDto.class);
                cost.setVisitDateObject(Instant.parse(cost.getVisitDate()));
                if (LocalDateTime.ofInstant(cost.getVisitDateObject(), ZoneOffset.UTC).getYear()
                    == LocalDateTime.ofInstant(Instant.now(), ZoneOffset.UTC).getYear()) {
                  cost.setFormattedVisitDate(monthDayFormatter.format(cost.getVisitDateObject()));
                } else {
                  cost.setFormattedVisitDate(
                      monthDayYearFormatter.format(cost.getVisitDateObject()));
                }

                if (cost.isCurrentVisit()) {
                  cost.setFormattedVisitDate(
                      ExcelUtils.getLangValue(LangKeys.REPORT_CURRENT, null, source, locale));
                }
                visits.add(cost);
              }
            });

    // remove duplicates
    Set<String> duplicateVisitDateSet = new HashSet<>();

    return visits.stream()
        .filter(e -> duplicateVisitDateSet.add(e.getVisitDate()))
        .sorted(Comparator.comparing(MetabolicDisorderCostPerCowDto::getVisitDateObject))
        .toList();
  }

  @Override
  public Object prepareData(Object data, ResourceBundleMessageSource source, Locale locale) {
    this.source = source;
    MetabolicIncidenceReportDto mappedDto =
        modelMapper.map(data, MetabolicIncidenceReportDto.class);
    mappedDto.setMetabolicTypeKeys(
        Arrays.stream(MetabolicTypeKeys.values()).map(Enum::name).toArray(String[]::new));
    return prepareDataForCostPerCowExportImage(mappedDto, locale);
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    MetabolicIncidenceReportDto mappedDto =
        (MetabolicIncidenceReportDto) prepareData(data, source, locale);

    Map<String, byte[]> imageTemplates = new HashMap<>();
    if (mappedDto.getGraphType() == MetabolicIncidenceGraphExportType.METABOLIC_INCIDENCE_PERCENT) {
      // create sheet 0
      byte[] metabolicIncidencePercentage =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT.getImageTemplateName0(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);

      imageTemplates.put(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_0,
              "Metabolic Incidence %",
              null,
              source,
              locale),
          metabolicIncidencePercentage);

    } else if (mappedDto.getGraphType()
        == MetabolicIncidenceGraphExportType.METABOLIC_DISORDER_COST_PER_COW) {
      // create sheet 1
      byte[] costPerCow =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT.getImageTemplateName1(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);

      imageTemplates.put(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_1,
              "Metabolic Disorder Cost / Cow",
              null,
              source,
              locale),
          costPerCow);

    } else if (mappedDto.getGraphType() == MetabolicIncidenceGraphExportType.BOTH) {

      // create sheet 0
      byte[] metabolicIncidencePercentage =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT.getImageTemplateName0(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);

      imageTemplates.put(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_0,
              "Metabolic Incidence %",
              null,
              source,
              locale),
          metabolicIncidencePercentage);
      // create sheet 1
      byte[] costPerCow =
          freeMarkerComponent.render(
              mappedDto,
              ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT.getImageTemplateName1(),
              source,
              locale,
              TemplateExportType.EXPORT_IMAGE);

      imageTemplates.put(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_1,
              "Metabolic Disorder Cost / Cow",
              null,
              source,
              locale),
          costPerCow);
    }

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  private MetabolicIncidenceReportDto prepareDataForCostPerCowExportImage(
      MetabolicIncidenceReportDto mappedDto, Locale locale) {
    List<MetabolicDisorderCostPerCowDto> sortedVisits = getSortedVisitObject(mappedDto, locale);
    MetabolicIncidenceExportImageReportDto dataPoints =
        new MetabolicIncidenceExportImageReportDto();
    dataPoints.setFormattedDates(
        sortedVisits.stream().map(MetabolicDisorderCostPerCowDto::getFormattedVisitDate).toList());
    List<MetabolicDisorderCostPerCowDto> metabolicDisorderCostPerCowDtoList =
        getListOfCostPerCowDataPoints(mappedDto);

    mappedDto.getMetabolicDisorderCostPerCow().entrySet().stream()
        .forEachOrdered(
            entry -> {
              for (int vDateIndex = 0; vDateIndex < sortedVisits.size(); vDateIndex++) {
                MetabolicDisorderCostPerCowDto formattedVisitDateDto = sortedVisits.get(vDateIndex);
                Optional<MetabolicDisorderCostPerCowDto> first =
                    metabolicDisorderCostPerCowDtoList.parallelStream()
                        .filter(
                            obj ->
                                obj.getMetabolicIncidenceCase().contentEquals(entry.getKey())
                                    && obj.getVisitDate()
                                        .contentEquals(formattedVisitDateDto.getVisitDate()))
                        .findFirst();

                Double cost =
                    first.orElse(MetabolicDisorderCostPerCowDto.builder().build()).getCost();

                if (vDateIndex == 0) {
                  dataPoints.getVisitDate1().add(cost);
                } else if (vDateIndex == 1) {
                  dataPoints.getVisitDate2().add(cost);
                } else if (vDateIndex == 2) {
                  dataPoints.getVisitDate3().add(cost);
                } else if (vDateIndex == 3) {
                  dataPoints.getVisitDate4().add(cost);
                } else if (vDateIndex == 4) {
                  dataPoints.getVisitDate5().add(cost);
                } else if (vDateIndex == 5) {
                  dataPoints.getVisitDate6().add(cost);
                } else if (vDateIndex == 6) {
                  dataPoints.getVisitDate7().add(cost);
                }
              }
            });

    mappedDto.setMetabolicIncidenceExportImageReportDto(dataPoints);
    return mappedDto;
  }

  @Override
  public String getFileName(Object data) {
    MetabolicIncidenceReportDto dto = modelMapper.map(data, MetabolicIncidenceReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook metabolicIncidenceWorkBook,
      XSSFSheet metabolicIncidenceSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      MetabolicIncidenceReportDto metabolicIncidenceReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        metabolicIncidenceWorkBook,
        metabolicIncidenceSheet,
        rowNum.get(),
        cellNum.getAndIncrement());
    // headings
    XSSFRow row = metabolicIncidenceSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, metabolicIncidenceReportDto.getVisitName());
    metabolicIncidenceSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, metabolicIncidenceReportDto.getVisitDate());
    metabolicIncidenceSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = metabolicIncidenceSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, metabolicIncidenceReportDto.getToolName());
    metabolicIncidenceSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
  }

  XSSFSheet addMetabolicIncidencePercentageSheet(
      XSSFWorkbook metabolicIncidenceWorkBook,
      ResourceBundleMessageSource source,
      Locale locale,
      MetabolicIncidenceReportDto metabolicIncidenceDto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFSheet sheet =
        metabolicIncidenceWorkBook.createSheet(
            ExcelUtils.getLangValue(
                    LangKeys.REPORT_METABOLIC_INCIDENCE_SHEET_0, null, source, locale)
                .replace("/", "⧸"));

    AtomicInteger cellNumber = new AtomicInteger(0);
    AtomicInteger rowNumber = new AtomicInteger(0);

    prepareHeader(
        metabolicIncidenceWorkBook,
        sheet,
        rowNumber,
        cellNumber,
        metabolicIncidenceDto,
        boldStyle,
        locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        greyCellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_METABOLIC_INCIDENCE_SHEET_0, null, source, locale));
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // Metabolic Type keys
    cellNumber.set(1);

    int metabolicTypeKeysStartRowNumber = rowNumber.get();

    row = sheet.createRow(rowNumber.getAndIncrement());
    writeMetabolicTypeHeadingValues(
        source, locale, centerBlack, cellNumber, row, metabolicIncidenceDto);

    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(
            metabolicIncidenceWorkBook, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    DataFormat format = metabolicIncidenceWorkBook.createDataFormat();
    decimalStyle.setDataFormat(format.getFormat("0.00%")); // percentage number format
    // goal  %
    cellNumber.set(0);

    int goalPercentageStartRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_GOAL_PERCENTAGE, null, source, locale));

    for (String typeKeys : metabolicIncidenceDto.getMetabolicTypeKeys()) {
      Double value = metabolicIncidenceDto.getGoalPercentage().get(typeKeys);
      value = value == null ? null : value / 100;
      ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
    }

    // Incidence percentage
    cellNumber.set(0);

    int incidencePercentageStartRowNumber = rowNumber.get();
    row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_INCIDENCE_PERCENTAGE, null, source, locale));

    for (String typeKeys : metabolicIncidenceDto.getMetabolicTypeKeys()) {
      Double value = metabolicIncidenceDto.getIncidencePercentage().get(typeKeys);
      value = value == null ? null : value / 100;
      ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    int columnEnd = columnStart + metabolicIncidenceDto.getMetabolicTypeKeys().length - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XDDFDataSource<String> metabolicTypeKeysDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet,
            new CellRangeAddress(
                metabolicTypeKeysStartRowNumber,
                metabolicTypeKeysStartRowNumber,
                columnStart,
                columnEnd));
    XDDFNumericalDataSource<Double> goalPercentageDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(
                goalPercentageStartRowNumber,
                goalPercentageStartRowNumber,
                columnStart,
                columnEnd));
    XDDFNumericalDataSource<Double> incidencePercentageDataSource =
        XDDFDataSourcesFactory.fromNumericCellRange(
            sheet,
            new CellRangeAddress(
                incidencePercentageStartRowNumber,
                incidencePercentageStartRowNumber,
                columnStart,
                columnEnd));

    // needed objects for the charts

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFBarChartData dataLeft;
    Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first Bar chart======================
    chart =
        ExcelUtils.initChart(
            sheet,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_0, null, source, locale),
            chartCol0,
            3,
            chartCol0 + 10,
            23);

    ExcelUtils.initLegends(chart);

    bottomAxis = ExcelUtils.createBottomAxis(chart, "");

    leftAxis =
        ExcelUtils.createLeftAxis(
            chart,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_METABOLIC_INCIDENCE_CHART_NAME_0, null, source, locale));
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
    // create chart data
    dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);
    // create series
    series = (Series) dataLeft.addSeries(metabolicTypeKeysDataSource, goalPercentageDataSource);
    series.setTitle(
        ExcelUtils.getLangValue(LangKeys.REPORT_GOAL_PERCENTAGE, null, source, locale),
        new CellReference(sheet.getSheetName(), goalPercentageStartRowNumber, 0, true, true));

    series =
        (Series) dataLeft.addSeries(metabolicTypeKeysDataSource, incidencePercentageDataSource);
    series.setTitle(
        ExcelUtils.getLangValue(LangKeys.REPORT_INCIDENCE_PERCENTAGE, null, source, locale),
        new CellReference(sheet.getSheetName(), incidencePercentageStartRowNumber, 0, true, true));
    chart.plot(dataLeft);
    ExcelUtils.setBarColorInBarChart(chart, metabolicIncidencePercentageColors, 2);

    return sheet;
  }

  private static void writeMetabolicTypeHeadingValues(
      ResourceBundleMessageSource source,
      Locale locale,
      XSSFCellStyle centerBlack,
      AtomicInteger cellNumber,
      XSSFRow row,
      MetabolicIncidenceReportDto metabolicIncidenceReportDto) {

    for (String typeKeys : metabolicIncidenceReportDto.getMetabolicTypeKeys()) {
      ExcelUtils.createAndSetCellValue(
          row, cellNumber, centerBlack, ExcelUtils.getLangValue(typeKeys, null, source, locale));
    }
  }
}
