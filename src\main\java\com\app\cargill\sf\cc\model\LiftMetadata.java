/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Builder
public class LiftMetadata implements Serializable {
  private AuthToken authToken;
  private String apiPath;
  private FieldsMetadata fieldsMetadataForAccount;
  private FieldsMetadata fieldsMetadataForContact;
  private FieldsMetadata fieldsMetadataForSite;
  private FieldsMetadata fieldsMetadataForSiteMapping;
  private FieldsMetadata fieldsMetadataForEvent;
}
