/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public enum LiftErrorCode {
  ENTITY_IS_DELETED,
  UNKNOWN;

  @JsonCreator
  public static LiftErrorCode fromString(String message) {
    try {
      return LiftErrorCode.valueOf(message);
    } catch (IllegalArgumentException exception) {
      log.warn("UNKNOWN_LIFT_ERROR_MESSAGE {}", message);
      return UNKNOWN;
    }
  }
}
