/* Cargill Inc.(C) 2022 */
package com.app.cargill.filterspecification;

import com.app.cargill.constants.SearchOperation;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchCriteria implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private String key;
  private SearchOperation operation;
  private Object value;
}
