/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.ForagePennStateTool;
import com.app.cargill.document.ForagePennStateToolItem;
import java.util.List;
import java.util.Objects;
import java.util.function.ToDoubleFunction;

public class ForagePenStateCalculation {

  public ForagePennStateTool calculateFields(ForagePennStateTool tool) {

    if (tool != null && tool.getInputs() != null) {

      List<ForagePennStateToolItem> inputs = tool.getInputs();

      for (ForagePennStateToolItem item : inputs) {
        item.setTotalScreenAmount(getTotal(item));
        item.setTopOnScreenPercentage(calculateTopOnScreenPercentage(item));
        item.setMid1OnScreenPercentage(calculateMid1OnScreenPercentage(item));
        item.setMid2OnScreenPercentage(calculateMid2OnScreenPercentage(item));
        item.setTrayOnScreenPercentage(calculateTrayOnScreenPercentage(item));
      }

      tool.setAvgTopOnScreenPercentage(
          calculateAverage(inputs, ForagePennStateToolItem::getTopOnScreenPercentage));
      tool.setAvgMid1OnScreenPercentage(
          calculateAverage(inputs, ForagePennStateToolItem::getMid1OnScreenPercentage));
      tool.setAvgMid2OnScreenPercentage(
          calculateAverage(inputs, ForagePennStateToolItem::getMid2OnScreenPercentage));
      tool.setAvgTrayOnScreenPercentage(
          calculateAverage(inputs, ForagePennStateToolItem::getTrayOnScreenPercentage));

      Double avgStdDevTop =
          calculateStandardDeviation(
              inputs.stream().map(ForagePennStateToolItem::getTopOnScreenPercentage).toList(),
              tool.getAvgTopOnScreenPercentage());
      Double avgStdDevMid1 =
          calculateStandardDeviation(
              inputs.stream().map(ForagePennStateToolItem::getMid1OnScreenPercentage).toList(),
              tool.getAvgMid1OnScreenPercentage());
      Double avgStdDevMid2 =
          calculateStandardDeviation(
              inputs.stream().map(ForagePennStateToolItem::getMid2OnScreenPercentage).toList(),
              tool.getAvgMid2OnScreenPercentage());
      Double avgStdDevTray =
          calculateStandardDeviation(
              inputs.stream().map(ForagePennStateToolItem::getTrayOnScreenPercentage).toList(),
              tool.getAvgTrayOnScreenPercentage());

      for (ForagePennStateToolItem input : inputs) {
        input.setTopStdDevValue(avgStdDevTop);
        input.setMid1StdDevValue(avgStdDevMid1);
        input.setMid2StdDevValue(avgStdDevMid2);
        input.setTrayStdDevValue(avgStdDevTray);
      }
    }
    return tool;
  }

  private Double calculateTopOnScreenPercentage(ForagePennStateToolItem item) {
    Double totalSum = getTotal(item);
    return roundAvoid((item.getTop() / totalSum) * 100, 1);
  }

  private Double calculateMid1OnScreenPercentage(ForagePennStateToolItem item) {
    Double totalSum = getTotal(item);
    return roundAvoid((item.getMid1() / totalSum) * 100, 1);
  }

  private Double calculateMid2OnScreenPercentage(ForagePennStateToolItem item) {
    Double totalSum = getTotal(item);
    return roundAvoid((item.getMid2() / totalSum) * 100, 1);
  }

  private Double calculateTrayOnScreenPercentage(ForagePennStateToolItem item) {
    Double totalSum = getTotal(item);
    return roundAvoid((item.getTray() / totalSum) * 100, 1);
  }

  private Double getTotal(ForagePennStateToolItem item) {
    if (item.getTop() == null) item.setTop(0.0);
    if (item.getMid1() == null) item.setMid1(0.0);
    if (item.getMid2() == null) item.setMid2(0.0);
    if (item.getTray() == null) item.setTray(0.0);
    return item.getTop() + item.getMid1() + item.getMid2() + item.getTray();
  }

  private Double calculateAverage(
      List<ForagePennStateToolItem> items, ToDoubleFunction<ForagePennStateToolItem> getter) {
    return items.stream().mapToDouble(getter).average().orElse(0.0);
  }

  private Double calculateStandardDeviation(List<Double> listOfValues, Double avgValue) {
    if (listOfValues == null || listOfValues.isEmpty()) return 0.0;
    int n = listOfValues.size();
    double sumOfSquareDelta =
        listOfValues.stream()
            .filter(Objects::nonNull)
            .mapToDouble(score -> Math.pow(Math.abs(score - avgValue), 2))
            .sum();

    return roundAvoid(Math.sqrt(sumOfSquareDelta / n), 2);
  }
}
