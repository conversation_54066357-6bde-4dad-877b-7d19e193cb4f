/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldEvaluationToolItemDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private MilkSoldEvaluationToolOutputToolItemDto outputs;

  private List<MilkSoldMilkProcessorToolItemDto> pickups;

  private List<UUID> selectedVisits;

  private Integer lactatingAnimals;

  private Integer animalsinTank;

  private Double asFedIntake;

  private Double netEnergyOfLactationDairy;

  private Double rationCost;

  private Double currentMilkPrice;

  private MilkPickup milkPickup;

  private Double dryMatterIntake;

  private Integer daysInMilk;

  private MilkUreaMeasure milkUreaMeasure;
}
