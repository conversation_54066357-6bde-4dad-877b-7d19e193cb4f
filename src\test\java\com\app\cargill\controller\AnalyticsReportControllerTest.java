/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.model.analytics.AnalyticsDataResponse;
import com.app.cargill.service.analytics.AnalyticsDataService;
import java.time.Instant;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AnalyticsReportControllerTest {

  @Mock private AnalyticsDataService analyticsDataService;

  @InjectMocks private AnalyticsReportController analyticsReportController;

  @Test
  void getAllDataReturnsCorrectResponse() {
    when(analyticsDataService.getAllDataPoints(any(), any())).thenReturn(new ArrayList<>());
    AnalyticsDataResponse result =
        analyticsReportController.getAllData(Instant.now(), Instant.now());

    assertEquals(0, result.getTotalRecords());
  }
}
