package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ReturnOverFeedKgFatReportDto extends BaseDto {
	
	
	private String fileName;
	  private String visitName;
	  private String visitDate;
	  private String toolName;
	  private String returnOverFeedKgPerFatLabel;
	  private String rofPerKgButterFatLabel;
	  private String concentrateCostPerKgButterFatLabel;
	  private String xAxisLabel;
	  private String totalRevenueDollarKgPerFatLabel;
	  private String totalCostsPerKgPerFatLabel;

	  @Builder.Default private List<ReturnOverFeedPerKgFatDataPoints> dataPoints = new ArrayList<>();


}
