/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.SilageType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ForagePennStateToolGoalItemDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private SilageType silage;
  private String silageRange;
  private UUID silageId;
  private Double goalMax;
  private Double goalMin;
}
