/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.app.cargill.document.UserRole;
import com.app.cargill.sf.cc.config.IgnoreValidation;
import com.app.cargill.sf.cc.config.PickListValueLookUp;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * There are very few fields we send back to Salesforce when we sync an account Might change it in
 * future
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ToString
public class AccountUpdateModel implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("DE_Account_External_ID__c")
  private String accountExternalId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("BillingStreet")
  private String billingStreet;

  @JsonProperty("BillingCity")
  private String billingCity;

  @JsonProperty("BillingState")
  @PickListValueLookUp(key = "BillingStateCode", value = "label")
  private String billingState;

  @JsonProperty("BillingCountry")
  @PickListValueLookUp(key = "BillingCountryCode", value = "label")
  private String billingCountry;

  @IgnoreValidation
  @JsonProperty("Segment__c")
  private String segment;

  @JsonProperty("Email__c")
  private String email;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("Customer_Number__c")
  private String customerNumber;

  @JsonProperty("Business_Type__c")
  private String businessType = "Farm Producer";

  @JsonProperty("DE_Account__c")
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private boolean deAccount = true;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("Customer_Account_Type__c")
  private String customerAccountType;

  @JsonProperty("BillingPostalCode")
  private String billingPostalCode;

  @JsonProperty("UserRoles")
  private List<UserRole> userRoles;

  @JsonProperty("Review_Status__c")
  private String reviewStatus;

  @JsonProperty("CurrencyIsoCode")
  private String currencyIsoCode;

  @JsonProperty("Cargill_Business__c")
  private String businessId;
}
