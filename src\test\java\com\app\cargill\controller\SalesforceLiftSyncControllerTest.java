/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.when;

import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.TaskStatus;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.sf.de.SalesforceSyncTrigger;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SalesforceLiftSyncControllerTest {
  @Mock private SalesforceSyncTrigger syncTrigger;

  @InjectMocks SalesforceLiftSyncController controller;

  @Test
  void verifyNoError() {
    when(syncTrigger.triggerSync(anyBoolean())).thenReturn(new ArrayList<>());

    List<LongRunningTask<SyncResult>> result = controller.startLiftSync(false);

    assertNotNull(result);
  }

  @Test
  void testStartLiftPullAccountsMergeSync_withPartialFalse() {
    // Arrange
    LongRunningTask<SyncResult> task = new LongRunningTask<>();
    task.setLocalId("1");
    task.setStatus(TaskStatus.RUNNING);

    when(syncTrigger.startLiftPullAccountsMergeSync(false)).thenReturn(task);

    // Act
    LongRunningTask<SyncResult> result = controller.startLiftPullAccountsMergeSync(false);

    // Assert
    assertEquals("1", result.getLocalId());
    assertEquals(TaskStatus.RUNNING, result.getStatus());
  }
}
