/* Cargill Inc.(C) 2022 */
package com.app.cargill.ddw.service.impl;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.PenSource;
import com.app.cargill.ddw.calculation.Calculation;
import com.app.cargill.ddw.constants.HerdConstant;
import com.app.cargill.ddw.document.TempDDWDocument;
import com.app.cargill.ddw.model.*;
import com.app.cargill.document.*;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.*;
import com.app.cargill.repository.*;
import com.app.cargill.service.IPensService;
import com.app.cargill.service.ISiteService;
import java.util.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class HerdServiceImplTest {

  @InjectMocks private HerdServiceImpl herdService;
  @Mock private AccountsRepository accountsRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private SiteMappingsRepository siteMappingsRepository;
  @Mock private DietRepository dietRepository;
  @Mock private TempDDWRepository tempDDWRepository;
  @Mock private PensRepository pensRepository;
  @Mock private ISiteService siteService;
  private SiteDocument siteDoc;

  @Mock private IPensService pensService;

  @Test
  void testUpdateHerds_NoInventoryOrPeriod() {
    List<HerdData> herds = new ArrayList<>();
    herds.add(new HerdData());

    List<String> messages = herdService.updateHerds(herds);

    Assertions.assertTrue(messages.isEmpty());
  }

  @Test
  void testUpdateHerds_WithInventoryWithNewData() {
    HerdData herdData = getData().get(0);
    herdData.setPeriod(null);

    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);

    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());

    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());

    processHerdData();
    List<String> messages = herdService.updateHerds(herds);

    Assertions.assertNotNull(messages);
  }

  @Test
  void testUpdateHerds_WithInventoryWithNewDataAndTempDDList() {
    HerdData herdData = getData().get(0);
    herdData.setPeriod(null);

    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);

    when(tempDDWRepository.getByHerdProfileId(anyString()))
        .thenReturn(
            List.of(
                TempDDW.builder()
                    .localId(UUID.randomUUID().toString())
                    .id(1L)
                    .tempDdwDocument(new TempDDWDocument(new HerdData()))
                    .build()));

    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());

    processHerdData();
    List<String> messages = herdService.updateHerds(herds);

    Assertions.assertNotNull(messages);
  }

  @Test
  void testUpdateHerds_WithProfileExistingData() {

    HerdPeriod period = new HerdPeriod();
    period.setHerdProfileId("inventory_id");

    HerdData herdData = new HerdData();
    herdData.setPeriod(period);

    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);

    List<TempDDW> existingList = new ArrayList<>();

    for (int temp = 0; temp < 1; temp++) {

      TempDDW tempDDW1 = new TempDDW();
      tempDDW1.setLocalId(UUID.randomUUID().toString());
      tempDDW1.setId(1L);
      tempDDW1.setTempDdwDocument(new TempDDWDocument(new HerdData()));
      existingList.add(tempDDW1);
    }

    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(existingList);

    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());

    processHerdData();
    List<String> messages = herdService.updateHerds(herds);

    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  List<HerdData> getData() {
    List<HerdData> herds = new ArrayList<>();

    HerdData herdData = new HerdData();

    // Set Cullings List
    List<HerdCulling> cullings = new ArrayList<>();
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    cullings.add(herdCulling);
    herdData.setCullings(cullings);

    // Set Herd Health
    HerdHealth health =
        HerdHealth.builder()
            .herdProfileId("10003")
            .endDate(new Date())
            .externalHerdId("IT-049")
            .build();

    herdData.setHealth(health);

    // Set Herd Inventory
    HerdInventory inventory =
        HerdInventory.builder()
            .endDate(new Date())
            .externalHerdId("IT-049")
            .herdProfileId("10003")
            .avgAgeDaysExclDry("1423")
            .avgAgeDaysInclDry("1454")
            .avgDimDaysExclDry("168")
            .avgDimDaysInclDry("194")
            .nrMilkingCows("169")
            .nrDry("22")
            .build();

    herdData.setInventory(inventory);

    // Herd Period
    HerdPeriod period =
        HerdPeriod.builder()
            .endDate(new Date())
            .externalHerdId("IT-049")
            .herdProfileId("10003")
            .startDate(new Date())
            .build();

    herdData.setPeriod(period);

    // Set Milk Productions
    List<HerdMilkProduction> milkProductions = new ArrayList<>();
    HerdMilkProduction production =
        HerdMilkProduction.builder()
            .endDate(new Date())
            .herdProfileId("10003")
            .externalHerdId("IT-049")
            .groupCategory("LactatingCows")
            .groupId("8")
            .groupName("HerdLactatingCows")
            .averageDIMLact1("357.67")
            .averageDIMLact2("343")
            .averageDIMLact3plus("319.75")
            .averageDIMOverall("337.56")
            .averageYieldLact1("17.6")
            .averageYieldLact2("17.6")
            .averageYieldLact3plus("15.55")
            .nrCowsLact1("3")
            .nrCowsLact2("2")
            .nrCowsLact3plus("4")
            .nrCowsOverall("9")
            .build();
    milkProductions.add(production);

    herdData.setMilkProductions(milkProductions);

    // Set Milk Recordings
    herdData.setMilkRecordings(null);
    herdData.setNutritions(null);
    herdData.setPenDatas(null);
    herdData.setUdderHealths(null);

    // Set Milk Reproductions
    HerdReproduction reproduction =
        HerdReproduction.builder()
            .endDate(new Date())
            .externalHerdId("IT-049")
            .herdProfileId("10003")
            .nrCalvings("26")
            .nrCalvingsCows("15")
            .percInHeat60DIM("0.73")
            .percNotIns100DIM("0.545")
            .build();

    herdData.setReproduction(reproduction);
    herds.add(herdData);
    return herds;
  }

  void processHerdData() {

    List<HerdData> herds = getData();
    Accounts account = getAccountsData();
    SiteMappings siteMappings = getSiteMappingsData();
    when(siteMappingsRepository.findByDDWHerdIdAndDeleted(anyString())).thenReturn(siteMappings);
    Assertions.assertNotNull(siteMappings);

    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);

    Assertions.assertNotNull(account);

    Sites sites = getSitesData();
    when(sitesRepository.findBySiteId(anyString())).thenReturn(sites);
    Assertions.assertNotNull(sites);

    List<Diets> diets = getDietData();
    when(dietRepository.findBySiteId(anyString())).thenReturn(diets);
    Assertions.assertNotNull(diets);
    Assertions.assertNotNull(herds.get(0).getInventory().getNrMilkingCows());
    Assertions.assertNotNull(herds.get(0).getInventory().getAvgDimDaysExclDry());
  }

  void processHerdData1() {

    List<HerdData> herds = getData1();
    Accounts account = getAccountsData();
    SiteMappings siteMappings = getSiteMappingsData();
    when(siteMappingsRepository.findByDDWHerdIdAndDeleted(anyString())).thenReturn(siteMappings);
    Assertions.assertNotNull(siteMappings);

    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);

    Assertions.assertNotNull(account);

    Sites sites = getSitesData();
    when(sitesRepository.findBySiteId(anyString())).thenReturn(sites);
    Assertions.assertNotNull(sites);

    List<Diets> diets = getDietData();
    when(dietRepository.findBySiteId(anyString())).thenReturn(diets);
    Assertions.assertNotNull(diets);
    Assertions.assertNotNull(herds.get(0).getInventory().getNrMilkingCows());
    Assertions.assertNotNull(herds.get(0).getInventory().getAvgDimDaysExclDry());
  }

  List<HerdData> getData1() {
    List<HerdData> herds = new ArrayList<>();

    HerdData herdData = new HerdData();

    // Set Cullings List
    List<HerdCulling> cullings = new ArrayList<>();
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("1222")
            .groupName("Buiutte")
            .groupCategory("Unknown1")
            .herdProfileId("19878")
            .lactatingTotal("32")
            .penId(UUID.randomUUID())
            .build();

    cullings.add(herdCulling);
    herdData.setCullings(cullings);

    // Set Herd Health
    HerdHealth health =
        HerdHealth.builder()
            .herdProfileId("10003")
            .endDate(new Date())
            .externalHerdId("IT-049")
            .build();

    herdData.setHealth(health);

    // Set Herd Inventory
    HerdInventory inventory =
        HerdInventory.builder()
            .endDate(new Date())
            .externalHerdId("IT-049")
            .herdProfileId("10003")
            .avgAgeDaysExclDry("1423")
            .avgAgeDaysInclDry("1454")
            .avgDimDaysExclDry("168")
            .avgDimDaysInclDry("194")
            .nrMilkingCows("169")
            .nrDry("22")
            .build();

    herdData.setInventory(inventory);

    // Herd Period
    HerdPeriod period =
        HerdPeriod.builder()
            .endDate(new Date())
            .externalHerdId("IT-049")
            .herdProfileId("10003")
            .startDate(new Date())
            .build();

    herdData.setPeriod(period);

    // Set Milk Productions
    List<HerdMilkProduction> milkProductions = new ArrayList<>();
    HerdMilkProduction production =
        HerdMilkProduction.builder()
            .endDate(new Date())
            .herdProfileId("10003")
            .externalHerdId("IT-049")
            .groupCategory("LactatingCows")
            .groupId("8")
            .groupName("HerdLactatingCows")
            .averageDIMLact1("357.67")
            .averageDIMLact2("343")
            .averageDIMLact3plus("319.75")
            .averageDIMOverall("337.56")
            .averageYieldLact1("17.6")
            .averageYieldLact2("17.6")
            .averageYieldLact3plus("15.55")
            .nrCowsLact1("3")
            .nrCowsLact2("2")
            .nrCowsLact3plus("4")
            .nrCowsOverall("9")
            .build();
    milkProductions.add(production);

    herdData.setMilkProductions(milkProductions);

    // Set Milk Recordings
    herdData.setMilkRecordings(null);
    herdData.setNutritions(null);
    herdData.setPenDatas(null);
    herdData.setUdderHealths(null);

    // Set Milk Reproductions
    HerdReproduction reproduction =
        HerdReproduction.builder()
            .endDate(new Date())
            .externalHerdId("IT-049")
            .herdProfileId("10003")
            .nrCalvings("26")
            .nrCalvingsCows("15")
            .percInHeat60DIM("0.73")
            .percNotIns100DIM("0.545")
            .build();

    herdData.setReproduction(reproduction);
    herds.add(herdData);
    return herds;
  }

  List<Diets> getDietData() {

    List<Diets> list = new ArrayList<>();

    for (int diet = 0; diet < 2; diet++) {
      DietDocument dietDocument =
          DietDocument.builder()
              .id(UUID.randomUUID())
              .siteId(UUID.randomUUID())
              .source(DietSource.DDW)
              .labyrinthAccountId(UUID.randomUUID())
              .build();

      Diets diets =
          Diets.builder()
              .id(new Random().nextLong())
              .localId(UUID.randomUUID().toString())
              .dietDocument(dietDocument)
              .build();

      list.add(diets);
    }

    return list;
  }

  Sites getSitesData() {

    DataSourceMapping siteMapping =
        DataSourceMapping.builder().systemId("1212").systemName("DDW").build();

    List<DataSourceMapping> mappings = new ArrayList<>();
    mappings.add(siteMapping);

    siteDoc =
        SiteDocument.builder()
            .id(UUID.randomUUID())
            .dataSourceMappings(mappings)
            .netEnergyOfLactationDairy(10.0)
            .build();

    return Sites.builder()
        .id(1L)
        .localId(UUID.randomUUID().toString())
        .siteDocument(siteDoc)
        .build();
  }

  SiteMappings getSiteMappingsData() {

    SiteMappingDocument siteDocument =
        SiteMappingDocument.builder()
            .id(UUID.randomUUID())
            .labyrinthSiteId(UUID.randomUUID())
            .labyrinthAccountId(UUID.randomUUID())
            .ddwHerdId("10003")
            .build();

    return SiteMappings.builder()
        .id(1L)
        .localId(UUID.randomUUID().toString())
        .siteMappingDocument(siteDocument)
        .build();
  }

  Accounts getAccountsData() {

    AccountDocument accountDocument = AccountDocument.builder().id(UUID.randomUUID()).build();

    return Accounts.builder()
        .id(1L)
        .localId(UUID.randomUUID().toString())
        .accountDocument(accountDocument)
        .build();
  }

  @Test
  void processHerdData_WithNullSite() {
    List<String> messages = new ArrayList<>();
    when(siteMappingsRepository.findByDDWHerdIdAndDeleted(anyString())).thenReturn(null);
    herdService.processHerdData(getData().get(0), messages);
    Assertions.assertNotNull(messages);
  }

  @Test
  void processHerdData_cullingpenid() {
    List<String> messages = new ArrayList<>();
    when(siteMappingsRepository.findByDDWHerdIdAndDeleted(anyString())).thenReturn(null);
    processHerdData1();
    herdService.processHerdData(getData1().get(0), messages);
    Assertions.assertNotNull(messages);
  }

  @Test
  void processHerdData_WithSiteAndNullAccount() {
    List<String> messages = new ArrayList<>();
    when(siteMappingsRepository.findByDDWHerdIdAndDeleted(anyString()))
        .thenReturn(getSiteMappingsData());
    when(accountsRepository.findByAccountId(anyString())).thenReturn(null);
    herdService.processHerdData(getData().get(0), messages);
    Assertions.assertNotNull(messages);
  }

  @Test
  void testCullingHeifersCategory() {
    HerdCulling culling = new HerdCulling();
    culling.setGroupCategory("HEIFERS");

    UUID result = herdService.determineAnimalClassId(culling);

    Assertions.assertEquals(HerdConstant.HEIFER_ID, result);
  }

  @Test
  void testCullingDryCowsCategory() {
    HerdCulling culling = new HerdCulling();
    culling.setGroupCategory("DRYCOWS");

    UUID result = herdService.determineAnimalClassId(culling);

    Assertions.assertEquals(HerdConstant.DRY_FAR_OFF_ID, result);
  }

  @Test
  void testCullingLactatingCowsCategory() {
    HerdCulling culling = new HerdCulling();
    culling.setGroupCategory("LACTATINGCOWS");

    UUID result = herdService.determineAnimalClassId(culling);

    Assertions.assertEquals(HerdConstant.LACTATING_MILKING_ID, result);
  }

  @Test
  void testUnknownCategory() {
    HerdCulling culling = new HerdCulling();
    culling.setGroupCategory("UNKNOWN");

    UUID result = herdService.determineAnimalClassId(culling);

    Assertions.assertEquals(HerdConstant.LACTATING_MILKING_ID, result);
  }

  @Test
  void testCullingEmptyCategory() {
    HerdCulling culling = new HerdCulling();
    culling.setGroupCategory("");

    UUID result = herdService.determineAnimalClassId(culling);

    Assertions.assertNull(result);
  }

  @Test
  void testNullCategory() {
    HerdCulling culling = new HerdCulling();
    culling.setGroupCategory(null);

    UUID result = herdService.determineAnimalClassId(culling);

    Assertions.assertNull(result);
  }

  @Test
  void testUpdateHerds_withPens() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .groupName("HerdLactatingCows")
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .avgTMRAsFed("2")
            .build();

    HerdMilkProduction herdMilkProduction =
        HerdMilkProduction.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .nrCowsOverall("1")
            .averageDIMOverall("1")
            .averageYieldOverall("1")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .build();

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");

    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setMilkProductions(List.of(herdMilkProduction));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(pens);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testUpdateHerds_withPensAndHerdLactatingCows() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .groupName("HerdLactatingCows")
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .avgTMRAsFed("2")
            .build();

    HerdMilkProduction herdMilkProduction =
        HerdMilkProduction.builder()
            .groupId("abc")
            .groupName("HerdLactatingCows")
            .groupCategory("Unknown")
            .nrCowsOverall("1")
            .averageDIMOverall("1")
            .averageYieldOverall("1")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .build();

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");

    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setMilkProductions(List.of(herdMilkProduction));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(pens);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testUpdateHerds_withoutPens() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .avgTMRAsFed("2")
            .build();

    HerdMilkProduction herdMilkProduction =
        HerdMilkProduction.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .nrCowsOverall("1")
            .averageDIMOverall("1")
            .averageYieldOverall("1")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .build();

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");
    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setMilkProductions(List.of(herdMilkProduction));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(null);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testUpdateHerds_withoutPensAndMilkProduction() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .avgTMRAsFed("2")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("HerdLactatingCows")
            .groupCategory("Unknown")
            .numberCowsInMilk("1")
            .averageDIM("1")
            .averageDailyYieldKg("1")
            .averageFatPerc("1")
            .averageProtienPerc("1")
            .averageSCC("1")
            .build();

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");
    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(null);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testUpdateHerds_withoutPensAndMilkProductionTMRAAsNull() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .numberCowsInMilk("1")
            .averageDIM("1")
            .averageDailyYieldKg("1")
            .averageFatPerc("1")
            .averageProtienPerc("1")
            .averageSCC("1")
            .dryMatterIntake("2")
            .build();

    HerdMilkProduction herdMilkProduction =
        HerdMilkProduction.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .nrCowsOverall("1")
            .averageDIMOverall("1")
            .averageYieldOverall("1")
            .build();

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");
    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setMilkProductions(List.of(herdMilkProduction));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(null);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testUpdateHerds_witPensAndMilkRecording() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .avgTMRAsFed("2")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("HerdLactatingCows")
            .groupCategory("Unknown")
            .numberCowsInMilk("1")
            .averageDIM("1")
            .averageDailyYieldKg("1")
            .averageFatPerc("1")
            .averageProtienPerc("1")
            .averageSCC("1")
            .build();

    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");
    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(pens);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testUpdateHerds_witPensAndMilkRecordingHEIFERSGroupCategory() {
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("abc")
            .groupName("Asciutte")
            .groupCategory("HEIFERS")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition herdNutrition =
        HerdNutrition.builder()
            .penName("Asciutte")
            .avgDryMatterPerc("1")
            .avgNrCowsFS("1")
            .avgPMRAsCalled("1")
            .avgPMRAsFed("1")
            .avgNrCowsMS("1")
            .avgPMRasFedCosts("1")
            .avgPMRDMAsCalled("1")
            .avgPMRDMWeighBack("1")
            .avgTMRasFedCosts("1")
            .avgTMRWBCosts("1")
            .avgTMRWeighBack("1")
            .avgTMRDMAsFed("2")
            .avgTMRDMWeighBack("1")
            .avgPMRDMAsFed("1")
            .avgTMRAsFed("2")
            .build();

    MilkRecording milkRecording =
        MilkRecording.builder()
            .groupId("abc")
            .groupName("HerdLactatingCows")
            .groupCategory("HEIFERS")
            .numberCowsInMilk("1")
            .averageDIM("1")
            .averageDailyYieldKg("1")
            .averageFatPerc("1")
            .averageProtienPerc("1")
            .averageSCC("1")
            .build();

    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());

    HerdInventory inventory = new HerdInventory();
    inventory.setHerdProfileId("inventory_id");
    HerdData herdData = new HerdData();
    herdData.setMilkRecordings(List.of(milkRecording));
    herdData.setCullings(List.of(herdCulling));
    herdData.setInventory(inventory);
    herdData.setNutritions(List.of(herdNutrition));
    List<HerdData> herds = new ArrayList<>();
    herds.add(herdData);
    when(tempDDWRepository.getByHerdProfileId(anyString())).thenReturn(new ArrayList<>());
    when(tempDDWRepository.save(any())).thenReturn(new TempDDW());
    when(pensRepository.findBySiteId(anyString())).thenReturn(pens);
    processHerdData();
    siteDoc.setBarns(List.of(Barn.builder().id(UUID.randomUUID()).build()));
    List<String> messages = herdService.updateHerds(herds);
    Assertions.assertTrue(messages.contains("Staging DDW Herd Profile inventory_id"));
  }

  @Test
  void testGetDietId_MilkProduction_LactatingCows() {
    // Mock data
    // Set Milk Productions
    HerdMilkProduction production =
        HerdMilkProduction.builder()
            .endDate(new Date())
            .herdProfileId("10003")
            .externalHerdId("IT-049")
            .groupCategory("LactatingCows")
            .groupId("8")
            .groupName("HerdLactatingCows")
            .averageDIMLact1("357.67")
            .averageDIMLact2("343")
            .averageDIMLact3plus("319.75")
            .averageDIMOverall("337.56")
            .averageYieldLact1("17.6")
            .averageYieldLact2("17.6")
            .averageYieldLact3plus("15.55")
            .nrCowsLact1("3")
            .nrCowsLact2("2")
            .nrCowsLact3plus("4")
            .nrCowsOverall("9")
            .build();

    List<DietDocument> diets =
        Arrays.asList(
            new DietDocument(UUID.randomUUID(), "LACTATING/MILKING", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "DRY/FAROFF", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "HEIFERS/HEIFERS", DietSource.SYSTEM_GENERATED));

    // Call the method
    UUID result = herdService.getDietId(production, diets);

    // Assert the result
    Assertions.assertNotNull(result);
    Assertions.assertNotNull(result);
  }

  @Test
  void testGetDietId_MilkProduction_DryCows() {
    // Mock data
    // Set Milk Productions
    HerdMilkProduction production =
        HerdMilkProduction.builder()
            .endDate(new Date())
            .herdProfileId("10003")
            .externalHerdId("IT-049")
            .groupCategory("DRYCOWS")
            .groupId("8")
            .groupName("DRYCOWS")
            .averageDIMLact1("357.67")
            .averageDIMLact2("343")
            .averageDIMLact3plus("319.75")
            .averageDIMOverall("337.56")
            .averageYieldLact1("17.6")
            .averageYieldLact2("17.6")
            .averageYieldLact3plus("15.55")
            .nrCowsLact1("3")
            .nrCowsLact2("2")
            .nrCowsLact3plus("4")
            .nrCowsOverall("9")
            .build();

    List<DietDocument> diets =
        Arrays.asList(
            new DietDocument(UUID.randomUUID(), "LACTATING/MILKING", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "DRY/FAROFF", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "HEIFERS/HEIFERS", DietSource.SYSTEM_GENERATED));

    // Call the method
    UUID result = herdService.getDietId(production, diets);

    // Assert the result
    Assertions.assertNotNull(result);
  }

  @Test
  void testGetDietId_MilkProduction_HEIFERS() {
    // Mock data
    // Set Milk Productions
    HerdMilkProduction production =
        HerdMilkProduction.builder()
            .endDate(new Date())
            .herdProfileId("10003")
            .externalHerdId("IT-049")
            .groupCategory("HEIFERS")
            .groupId("8")
            .groupName("HEIFERS")
            .averageDIMLact1("357.67")
            .averageDIMLact2("343")
            .averageDIMLact3plus("319.75")
            .averageDIMOverall("337.56")
            .averageYieldLact1("17.6")
            .averageYieldLact2("17.6")
            .averageYieldLact3plus("15.55")
            .nrCowsLact1("3")
            .nrCowsLact2("2")
            .nrCowsLact3plus("4")
            .nrCowsOverall("9")
            .build();

    List<DietDocument> diets =
        Arrays.asList(
            new DietDocument(UUID.randomUUID(), "LACTATING/MILKING", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "DRY/FAROFF", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "HEIFERS/HEIFERS", DietSource.SYSTEM_GENERATED));

    // Call the method
    UUID result = herdService.getDietId(production, diets);

    // Assert the result
    Assertions.assertNotNull(result);
  }

  @Test
  void testGetDietId_MilkProduction_UNKNOWNNAME() {
    // Mock data
    // Set Milk Productions
    HerdMilkProduction production =
        HerdMilkProduction.builder()
            .endDate(new Date())
            .herdProfileId("10003")
            .externalHerdId("IT-049")
            .groupCategory("not")
            .groupId("8")
            .groupName("HEIFERS")
            .averageDIMLact1("357.67")
            .averageDIMLact2("343")
            .averageDIMLact3plus("319.75")
            .averageDIMOverall("337.56")
            .averageYieldLact1("17.6")
            .averageYieldLact2("17.6")
            .averageYieldLact3plus("15.55")
            .nrCowsLact1("3")
            .nrCowsLact2("2")
            .nrCowsLact3plus("4")
            .nrCowsOverall("9")
            .build();

    List<DietDocument> diets =
        Arrays.asList(
            new DietDocument(UUID.randomUUID(), "LACTATING/MILKING", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "DRY/FAROFF", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "HEIFERS/HEIFERS", DietSource.SYSTEM_GENERATED));

    // Call the method
    UUID result = herdService.getDietId(production, diets);

    // Assert the result
    Assertions.assertNull(result);
  }

  @Test
  void testGetDietId_Culling_UNKNOWNNAME() {
    // Mock data
    // Set Culling
    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("")
            .groupName("Asciutte")
            .groupCategory("no")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();
    List<DietDocument> diets =
        Arrays.asList(
            new DietDocument(UUID.randomUUID(), "LACTATING/MILKING", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "DRY/FAROFF", DietSource.SYSTEM_GENERATED),
            new DietDocument(UUID.randomUUID(), "HEIFERS/HEIFERS", DietSource.SYSTEM_GENERATED));

    // Call the method
    UUID result = herdService.getDietId(herdCulling, diets);

    // Assert the result
    Assertions.assertNull(result);
  }

  @Test
  void testRationCostCalculationForPen_WithNonNull() {
    // Create a mock HerdNutrition object
    HerdNutrition nutrition = Mockito.mock(HerdNutrition.class);

    // Set up behavior for the mock object
    when(nutrition.getAvgTMRasFedCosts()).thenReturn("10.0"); // Replace with your desired values
    when(nutrition.getAvgTMRWBCosts()).thenReturn("5.0"); // Replace with your desired values
    when(nutrition.getAvgPMRasFedCosts()).thenReturn("8.0"); // Replace with your desired values

    // Call the method you want to test
    double result = Calculation.rationCostCalculationForPen(nutrition);

    // Assert the expected result
    Assertions.assertEquals(10.0 - 5.0 + 8.0, result, 0.01); // Adjust the delta value as needed
  }

  @Test
  void testRationCostCalculationForPen_Null() {
    // Create a mock HerdNutrition object
    HerdNutrition nutrition = Mockito.mock(HerdNutrition.class);

    // Set up behavior for the mock object
    when(nutrition.getAvgTMRasFedCosts()).thenReturn("10.0"); // Replace with your desired values
    when(nutrition.getAvgTMRWBCosts()).thenReturn("5.0"); // Replace with your desired values
    when(nutrition.getAvgPMRasFedCosts()).thenReturn(""); // Replace with your desired values

    // Call the method you want to test
    double result = Calculation.rationCostCalculationForPen(nutrition);

    // Assert the expected result
    Assertions.assertEquals(5.0, result); // Adjust the delta value as needed
  }

  @Test
  void testMatchNutritionList_NonNull() {
    List<HerdNutrition> nutritionList =
        List.of(
            HerdNutrition.builder()
                .groupName("HerdLactatingCows")
                .penName("Asciutte")
                .avgDryMatterPerc("1")
                .avgNrCowsFS("1")
                .avgPMRAsCalled("1")
                .avgPMRAsFed("1")
                .avgNrCowsMS("1")
                .avgPMRasFedCosts("1")
                .avgPMRDMAsCalled("1")
                .avgPMRDMWeighBack("1")
                .avgTMRasFedCosts("1")
                .avgTMRWBCosts("1")
                .avgTMRWeighBack("1")
                .avgTMRDMAsFed("2")
                .avgTMRDMWeighBack("1")
                .avgPMRDMAsFed("1")
                .avgTMRAsFed("2")
                .build());

    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition nutrition = herdService.getMatchingNutritionList(nutritionList, herdCulling);
    Assertions.assertNotNull(nutrition);
  }

  @Test
  void testMatchNutritionList_Null() {
    List<HerdNutrition> nutritionList = null;

    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId("")
            .groupName("Asciutte")
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition nutrition = herdService.getMatchingNutritionList(nutritionList, herdCulling);
    Assertions.assertNull(nutrition);
  }

  @Test
  void testMatchNutritionList_NullGroupName() {
    List<HerdNutrition> nutritionList =
        List.of(
            HerdNutrition.builder()
                .groupName("HerdLactatingCows")
                .penName("Asciutte")
                .avgDryMatterPerc("1")
                .avgNrCowsFS("1")
                .avgPMRAsCalled("1")
                .avgPMRAsFed("1")
                .avgNrCowsMS("1")
                .avgPMRasFedCosts("1")
                .avgPMRDMAsCalled("1")
                .avgPMRDMWeighBack("1")
                .avgTMRasFedCosts("1")
                .avgTMRWBCosts("1")
                .avgTMRWeighBack("1")
                .avgTMRDMAsFed("2")
                .avgTMRDMWeighBack("1")
                .avgPMRDMAsFed("1")
                .avgTMRAsFed("2")
                .build());

    HerdCulling herdCulling =
        HerdCulling.builder()
            .groupId(null)
            .groupName(null)
            .groupCategory("Unknown")
            .herdProfileId("10003")
            .lactatingTotal("33")
            .build();

    HerdNutrition nutrition = herdService.getMatchingNutritionList(nutritionList, herdCulling);
    Assertions.assertNull(nutrition);
  }

  @Test
  void testGetUniquePens() {
    // Create a list of Pens with some duplicate names
    List<Pens> pens =
        List.of(
            Pens.builder().penDocument(PenDocument.builder().name("Pen1").build()).build(),
            Pens.builder().penDocument(PenDocument.builder().name("Pen2").build()).build(),
            Pens.builder().penDocument(PenDocument.builder().name("Pen1").build()).build(),
            Pens.builder().penDocument(PenDocument.builder().name("Pen3").build()).build(),
            Pens.builder().penDocument(PenDocument.builder().name("Pen2").build()).build());

    // Call the method to get unique pens
    List<Pens> uniquePens = herdService.getUniquePens(pens);

    // Check the size of the uniquePens list
    Assertions.assertEquals(3, uniquePens.size());

    // Check if the unique pens are in the correct order
    Assertions.assertEquals("Pen1", uniquePens.get(0).getPenDocument().getName());
    Assertions.assertEquals("Pen2", uniquePens.get(1).getPenDocument().getName());
    Assertions.assertEquals("Pen3", uniquePens.get(2).getPenDocument().getName());
  }

  @Test
  void testSetSiteNetEnergyLactationMilkWithEmptyNutritionsList() {
    HerdData herdData = new HerdData(); // Empty nutritions list
    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());
    Sites sites = getSitesData();

    herdService.setSiteNetEnergyLactationMilk(herdData, pens, sites);

    // Actual value won't be changed because if value is Zero then it retained
    Assertions.assertEquals(10.0, sites.getSiteDocument().getNetEnergyOfLactationDairy());
  }

  @Test
  void testSetSiteNetEnergyLactationMilkWithEmptyPensList() {
    HerdData herdData = new HerdData();
    List<HerdNutrition> nutritionList =
        List.of(
            HerdNutrition.builder()
                .groupName("HerdLactatingCows")
                .build()); // Any non-empty nutrition list
    List<Pens> pens = Collections.emptyList(); // Empty pens list
    Sites sites = getSitesData();

    herdService.setSiteNetEnergyLactationMilk(herdData, pens, sites);
    // Add assertions to verify the behavior when pens list is empty

    // Actual value won't be changed because if value is Zero then it retained
    Assertions.assertEquals(10.0, sites.getSiteDocument().getNetEnergyOfLactationDairy());
  }

  @Test
  void testSetSiteNetEnergyLactationMilkWithZeroNetEnergy() {
    HerdData herdData = new HerdData();
    List<HerdNutrition> nutritionList =
        List.of(
            HerdNutrition.builder()
                .groupName("HerdLactatingCows")
                .build()); // Any non-empty nutrition list
    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());
    Sites sites = getSitesData();

    // No need to mock pensService in this test case

    herdService.setSiteNetEnergyLactationMilk(herdData, pens, sites);
    // Actual value won't be changed because if value is Zero then it retained
    Assertions.assertEquals(10.0, sites.getSiteDocument().getNetEnergyOfLactationDairy());

    // Add assertions to verify the behavior when net energy is zero
  }

  @Test
  void testSetSiteNetEnergyLactationMilkWithNonZeroNetEnergy() throws CustomDEExceptions {
    HerdData herdData = new HerdData();
    List<HerdNutrition> nutritionList =
        List.of(
            HerdNutrition.builder()
                .groupName("HerdLactatingCows")
                .build()); // Any non-empty nutrition list
    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .siteId(UUID.randomUUID())
                        .groupId("abc")
                        .source(PenSource.DDW)
                        .build())
                .build());
    Sites sites = getSitesData();

    herdData.setNutritions(nutritionList);
    // Mock pensService to return non zero net energy
    when(pensService.getNetEnergyOfLactationDairy(any())).thenReturn(5.0);

    herdService.setSiteNetEnergyLactationMilk(herdData, pens, sites);

    Assertions.assertEquals(5.0, sites.getSiteDocument().getNetEnergyOfLactationDairy());
  }
}
