# dairyenteligen_serviceapis
# Getting Started
Build Steps :
* Install openJDK 18
* Install PostgreSQL and update credentials in application.yml
* To access host db in docker container go to `C:\Program Files\PostgreSQL\14\data\pg_hba.conf` and append `# TYPE DATABASE USER CIDR-ADDRESS  METHOD
  local all all md5
  host  all  all 0.0.0.0/0 md5`
* Restart PostgreSQL service
* Install Docker
* Run `mvn clean install` in the terminal
* Run `docker build -t cargill .`
* Run `docker run -p 8080:8080 -e SPRING_PROFILES_ACTIVE=dev cargill:latest`
* swagger link : http://localhost:8080/swagger-ui.html#/diagnostics-controller
* Oauth2 login using swagger : <EMAIL>/secret
* fetch token from Ok<PERSON> using localhost: https://dev-16033766.okta.com/oauth2/default/v1/authorize?client_id=0oa5zk00buFZxKlDG5d7&scope=openid+profile+email&nonce=1234&redirect_uri=http://localhost:8080/auth/fetchAccessTokenByOktaCode&state=myState&response_type=code

### Guides
The following guides illustrate how to use some features concretely:

* [Securing a Web Application](https://spring.io/guides/gs/securing-web/)
* [Spring Boot and OAuth2](https://spring.io/guides/tutorials/spring-boot-oauth2/)
* [Authenticating a User with LDAP](https://spring.io/guides/gs/authenticating-ldap/)
* [Building a RESTful Web Service](https://spring.io/guides/gs/rest-service/)
* [Serving Web Content with Spring MVC](https://spring.io/guides/gs/serving-web-content/)
* [Building REST services with Spring](https://spring.io/guides/tutorials/bookmarks/)
* [Accessing Data with JPA](https://spring.io/guides/gs/accessing-data-jpa/)

### Reference Documentation
For further reference, please consider the following sections:

* [Official Apache Maven documentation](https://maven.apache.org/guides/index.html)
* [Spring Boot Maven Plugin Reference Guide](https://docs.spring.io/spring-boot/docs/2.6.3-SNAPSHOT/maven-plugin/reference/html/)
* [Create an OCI image](https://docs.spring.io/spring-boot/docs/2.6.3-SNAPSHOT/maven-plugin/reference/html/#build-image)
* [Spring Security](https://docs.spring.io/spring-boot/docs/2.6.2/reference/htmlsingle/#boot-features-security)
* [Spring Web](https://docs.spring.io/spring-boot/docs/2.6.2/reference/htmlsingle/#boot-features-developing-web-applications)
* [Spring Data JPA](https://docs.spring.io/spring-boot/docs/2.6.2/reference/htmlsingle/#boot-features-jpa-and-spring-data)


### Before committing the code Run below commands to fix issues in project
* mvn spotless:check
* mvn spotless:apply