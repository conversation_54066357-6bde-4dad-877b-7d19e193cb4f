/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.AccountType;
import com.app.cargill.constants.ProspectStatus;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/*
@EqualsAndHashCode.Exclude
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountDocument extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("GoldenRecordId")
  private String goldenRecordId;

  @JsonProperty("AccountName")
  private String accountName;

  @JsonProperty("LegalName")
  private String legalName;

  @JsonProperty("Type")
  private String type;

  @JsonProperty("AccountType")
  private Integer accountType;

  @JsonProperty("Contacts")
  private List<Contact> contacts;

  @JsonProperty("PrimaryContactId")
  private UUID primaryContactId;

  @JsonProperty("PrimaryContactTitle")
  private String primaryContactTitle;

  @JsonProperty("PrimaryContactPhoneNumber")
  private String primaryContactPhoneNumber;

  @JsonProperty("LastUpdateUserId")
  private String lastUpdateUserId;

  @JsonProperty("AccountValidated")
  private Boolean accountValidated;

  @JsonProperty("PhotoId")
  private String photoId;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("Users")
  private Set<String> users = new HashSet<>();

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("ProspectStatus")
  private ProspectStatus prospectStatus;

  @JsonProperty("IsDuplicate")
  private Boolean isDuplicate;

  @JsonProperty("AutoValidate")
  private Boolean autoValidate;

  @JsonProperty("SourceSystem")
  private String sourceSystem;

  @JsonProperty("ParentAccountID")

  // Crescendo Fields

  // region Crescendo Fields

  private String parentAccountID;

  @JsonProperty("SubTypeID")
  private SubTypeId subTypeID;

  @JsonProperty("ExternalLeadSourceID")
  private Integer externalLeadSourceID;

  @JsonProperty("BuyingGroupID")
  private String buyingGroupID;

  @JsonProperty("ExternalBuyingGroupID")
  private UUID externalBuyingGroupID;

  @JsonProperty("AccountStatus")
  private String accountStatus;

  @JsonProperty("DateOfLastVisit")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant dateOfLastVisit;

  @JsonProperty("DateOfLastCall")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant dateOfLastCall;

  @JsonProperty("WonLostId")
  private Integer wonLostId;

  @JsonProperty("WonLostComments")
  private String wonLostComments;

  @JsonProperty("WonLost")
  private String wonLost;

  @JsonProperty("WonLostReasonCode")
  private String wonLostReasonCode;

  @JsonProperty("CreditFlag")
  private Boolean creditFlag;

  @JsonProperty("PriceFlag")
  private Boolean priceFlag;

  @JsonProperty("ServiceFlag")
  private Boolean serviceFlag;

  @JsonProperty("PerformanceFlag")
  private Boolean performanceFlag;

  @JsonProperty("PortfolioFlag")
  private Boolean portfolioFlag;

  @JsonProperty("BusinessSolutionFlag")
  private Boolean businessSolutionFlag;

  @JsonProperty("QualityFlag")
  private Boolean qualityFlag;

  @JsonProperty("OtherFlag")
  private Boolean otherFlag;

  @JsonProperty("BusinessID")
  private Integer businessID;

  @JsonProperty("DefaultCargillPlantID")
  private Integer defaultCargillPlantID;

  @JsonProperty("DefaultCustServiceID")
  private Integer defaultCustServiceID;

  @JsonProperty("LastModificationDate")
  private DateEpoch lastModificationDate;

  @JsonProperty("Active") // Epoch datetime is commented in base model
  private Boolean active;

  @JsonProperty("BrandId")
  private Integer brandId;

  @JsonProperty("NineBoxStepTwoID")
  private String nineBoxStepTwoID;

  @JsonProperty("SegmentStepOneId")
  private String segmentStepOneId;

  @JsonProperty("CompanyEmail")
  private String companyEmail;

  @JsonProperty("LastInvoiceDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastInvoiceDate;

  @JsonProperty("LastInvoicesInfo")
  private String lastInvoicesInfo;

  @JsonProperty("LastOrderDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastOrderDate;

  @JsonProperty("LastOrdersInfo")
  private String lastOrdersInfo;

  @JsonProperty("DeliveryInstructions")
  private String deliveryInstructions;

  @JsonProperty("ERPPayerId")
  private String erpPayerId;

  @JsonProperty("ERPShipToId")
  private String erpShipToId;

  @JsonProperty("ERPIdLength")
  private String erpIdLength;

  @JsonProperty("ERPPayerIdLength")
  private String erpPayerIdLength;

  @JsonProperty("ERPShiptoIdLength")
  private String erpShiptoIdLength;

  @JsonProperty("erpIdLengthValidatorIsError")
  private String erpIdLengthValidatorIsError;

  @JsonProperty("IsServicedbyCSPro")
  private Boolean isServicedbyCSPro;

  @JsonProperty("LastAdminUpdate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant lastAdminUpdate;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("ReqProcessingLog")
  private String reqProcessingLog;

  @JsonProperty("Liabilities")
  private String liabilities;

  @JsonProperty("LimitChangeReasonId")
  private Integer limitChangeReasonId;

  @JsonProperty("MarketInfluencer")
  private Boolean marketInfluencer;

  @JsonProperty("OtherActivityProduction")
  private String otherActivityProduction;

  @JsonProperty("PersonalID")
  private String personalID;

  @JsonProperty("PreviousStatus")
  private String previousStatus;

  @JsonProperty("ReasonDescription")
  private String reasonDescription;

  @JsonProperty("Securities")
  private String securities;

  @JsonProperty("Assets")
  private String assets;

  @JsonProperty("VolumeEstimate")
  private BigInteger volumeEstimate;

  @JsonProperty("MarginEstimate")
  private BigInteger marginEstimate;

  @JsonProperty("PhysicalAddress")
  private Address physicalAddress;

  @JsonProperty("CorrespondenceAddress")
  private Address correspondenceAddress;

  @JsonProperty("SocialMediaAddress")
  private String socialMediaAddress;

  @JsonProperty("WebSiteAddress")
  private String webSiteAddress;

  @JsonProperty("lstOtherBU")
  private List<Integer> lstOtherBU;

  @JsonProperty("ExternalParentAccountID")
  private UUID externalParentAccountID;

  @JsonProperty("AccountCurrency")
  private Integer accountCurrency;

  @JsonProperty("ApprovalStatus")
  private String approvalStatus;

  @JsonProperty("AccountNumber")
  private String accountNumber;

  @JsonProperty("AvailabilityOnMarket")
  private Integer availabilityOnMarket;

  @JsonProperty("ChangeAccountType")
  private Boolean changeAccountType;

  @JsonProperty("NewAccountType")
  private AccountType newAccountType;

  @JsonProperty("ConsumerStatus")
  private Integer consumerStatus;

  @JsonProperty("CustomerStatus")
  private Integer customerStatus;

  @JsonProperty("CourtId")
  private String courtId;

  @JsonProperty("IsMobileFirst")
  @Builder.Default
  // This is always true but still needs to be handled if account is created from the API
  private Boolean isMobileFirst = true;

  @JsonProperty("VeterinaryId")
  private String veterinaryId;

  @JsonProperty("CurrentUserProfileNameandId")
  private String currentUserProfileNameandId;

  @JsonProperty("LastModifiedBy")
  private String lastModifiedBy;

  @JsonProperty("CreatedBy")
  private String createdBy;

  @JsonProperty("OwnerProfileNameandId")
  private String ownerProfileNameandId;

  @JsonProperty("CustomerCode")
  private String customerCode;

  @JsonProperty("isFavourite")
  private boolean isFavourite;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("AdditionalInfo")
  private AdditionalInformation additionalInfo;

  @JsonProperty("SalesTerritory")
  private String salesTerritory;

  @JsonProperty("SubBrandId")
  private Integer subBrandId;

  @JsonProperty("SiteCount")
  @Builder.Default
  private Integer siteCount = 0;

  @JsonProperty("DataSource")
  @Builder.Default
  private DataSource dataSource = DataSource.UNKNOWN;

  @JsonProperty("UserRoles")
  private List<UserRole> userRoles = new ArrayList<>();

  @JsonProperty("ApplicationMappings")
  @Builder.Default
  private List<ExternalDataSource> applicationMappings = new ArrayList<>();
}
