/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@Builder
@SuppressWarnings("java:S1948")
public class MetaDataFields implements Serializable {
  private boolean aggregatable;
  private String compoundFieldName;
  private Object defaultValue;
  private String type; // type depends on defaultValue
  private String name; // actual field
  private String label; // just for display purpose
  private boolean nillable;
  private List<MetaDataPicklistValues> picklistValues;
  private List<MetaDataPicklistValues> additionalPicklistValues;
  private int length;
  private String soapType;
  private boolean unique;
}
