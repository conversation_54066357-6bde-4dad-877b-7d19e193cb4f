/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ReportsToBeanMappings;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProfitabilityAnalysisReportDto extends BaseDto {

  private String visitName;
  private String visitDate;
  private List<XAndYAxisValueDto> leftYAxis;
  private List<XAndYAxisValueDto> rightYAxis;
  private String fileName;
  private String toolName;
  private ReportsToBeanMappings reportType;
  private String currency;
}
