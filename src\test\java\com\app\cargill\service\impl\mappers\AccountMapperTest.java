/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.constants.SubTypeId;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.dto.AccountDto;
import com.app.cargill.model.Accounts;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class AccountMapperTest {

  @Test
  void whenAccountDocumentIsMissingExceptionIsThrown() {

    Accounts account = Accounts.builder().build();

    Assertions.assertThrows(
        IllegalArgumentException.class,
        () -> {
          AccountMapper.mapToDto(account);
        });
  }

  @Test
  void whenAccountIsCorrectTheCorrectAccountDtoIsReturned() {
    AccountDocument accountDocument =
        AccountDocument.builder()
            .users(Set.of("user1", "user2"))
            .contacts(
                List.of(
                    Contact.builder()
                        .contactId(UUID.randomUUID())
                        .firstName("John")
                        .lastName("Doe")
                        .build()))
            .subTypeID(SubTypeId.FarmProducer)
            .physicalAddress(Address.builder().build())
            .build();
    Accounts account =
        Accounts.builder()
            .accountDocument(accountDocument)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .build();
    AccountDto result = AccountMapper.mapToDto(account);
    assertNotNull(result);
  }
}
