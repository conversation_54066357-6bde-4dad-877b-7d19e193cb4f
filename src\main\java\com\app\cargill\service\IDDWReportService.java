/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.dto.Pair;
import com.app.cargill.exceptions.CustomDEExceptions;
import java.util.UUID;
import org.springframework.core.io.ByteArrayResource;

public interface IDDWReportService {

  Pair<String, ByteArrayResource> getLatestDetailedReport(UUID siteId) throws CustomDEExceptions;

  Pair<String, ByteArrayResource> getLatestSummaryReport(UUID siteId) throws CustomDEExceptions;

  Oauth2Dto fetchAccessToken();
}
