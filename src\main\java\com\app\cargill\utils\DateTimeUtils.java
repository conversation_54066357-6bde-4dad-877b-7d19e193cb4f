/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

public class DateTimeUtils {

  private DateTimeUtils() {}

  public static LocalDateTime instantToLocalDateTime(Instant instant, ZoneOffset zoneOffset) {
    if (zoneOffset == null) {
      zoneOffset = ZoneOffset.UTC;
    }
    return LocalDateTime.ofInstant(instant, zoneOffset);
  }

  public static String instantToFormattedDate(Instant instant) {
    if (instant != null) {
      DateTimeFormatter monthDayYearFormatter =
          DateTimeFormatter.ofPattern("HH:mm:ss dd/MM/yyyy").withZone(ZoneId.of("UTC"));
      return monthDayYearFormatter.format(instant);
    }
    return null;
  }
}
