/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.BCSToolHeardAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("bcsHerdAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class BCSHerdAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    BCSToolHeardAnalysisReportDto dto = modelMapper.map(data, BCSToolHeardAnalysisReportDto.class);
    dto.setLactationStages(
        dto.getBcsAverage().entrySet().stream().map(Map.Entry::getKey).toArray(String[]::new));

    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      // create sheet
      XSSFSheet sheet =
          wb.createSheet(ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // Lact Stages
      cellNumber.set(0);

      int lactationStageStartRowNumber = rowNumber.get();

      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.createAndSetCellValue(
            row, cellNumber, centerBlack, ExcelUtils.getLangValue(lStage, null, source, locale));
      }

      // Bcs Avg
      cellNumber.set(0);

      int bcsAvgRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_AVG, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getBcsAverage().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // Milk/hd/day
      cellNumber.set(0);

      int milkHdDayRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MILK_HEAD_DAY, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getMilkHdDay().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // BCS Min
      cellNumber.set(0);
      int bcsMinRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MIN, null, source, locale));
      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getBcsMin().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // Bcs Max
      cellNumber.set(0);
      int bcsMaxRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MAX, null, source, locale));
      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getBcsMax().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      int columnStart = 1;
      int columnEnd = columnStart + dto.getLactationStages().length - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

      XDDFDataSource<String> lactationStage =
          XDDFDataSourcesFactory.fromStringCellRange(
              sheet,
              new CellRangeAddress(
                  lactationStageStartRowNumber,
                  lactationStageStartRowNumber,
                  columnStart,
                  columnEnd));
      XDDFNumericalDataSource<Double> bcsAverage =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(bcsAvgRowNumber, bcsAvgRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> milkHdDays =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(milkHdDayRowNumber, milkHdDayRowNumber, columnStart, columnEnd));

      // y1 axis
      XDDFNumericalDataSource<Double> min =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(bcsMinRowNumber, bcsMinRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> max =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(bcsMaxRowNumber, bcsMaxRowNumber, columnStart, columnEnd));

      // needed objects for the charts
      XSSFChart chart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFValueAxis rightAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData dataRight;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 3;
      // ======first line chart==========
      chart =
          ExcelUtils.initChart(
              sheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_BCS_HERD_ANALYSIS_CHART_NAME, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart,
              ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart, ExcelUtils.getLangValue(LangKeys.REPORT_BCS, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, min);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MIN, null, source, locale),
          new CellReference(sheet.getSheetName(), bcsMinRowNumber, 0, true, true));
      series.setSmooth(true);
      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, max);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MAX, null, source, locale),
          new CellReference(sheet.getSheetName(), bcsMaxRowNumber, 0, true, true));
      series.setSmooth(true);
      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, bcsAverage);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_AVG, null, source, locale),
          new CellReference(sheet.getSheetName(), bcsAvgRowNumber, 0, true, true));
      // to smooth lines rather than edges
      series.setSmooth(true);
      chart.plot(dataLeft);

      ///////////////////////////// right axis start here //////////////////////////
      bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
      bottomAxis.setVisible(false);
      Double minimumYAxisValue =
          dto.getMilkHdDay().entrySet().stream()
              .map(Map.Entry::getValue)
              .filter(x -> !Objects.isNull(x))
              .sorted()
              .findFirst()
              .orElse(0.0);
      rightAxis =
          ExcelUtils.createRightAxis(
              chart,
              ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MILK, null, source, locale),
              bottomAxis,
              minimumYAxisValue);

      // create chart data
      dataRight = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, rightAxis);

      // create series
      series = (XDDFLineChartData.Series) dataRight.addSeries(lactationStage, milkHdDays);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_BCS_MILK_HEAD_DAY, null, source, locale),
          new CellReference(sheet.getSheetName(), milkHdDayRowNumber, 0, true, true));

      ExcelUtils.plotYRightAxis(chart, dataRight, series, true);
      ExcelUtils.drawLineSeries(dataRight, 0, PresetColor.PURPLE, false);

      // ExcelUtils.drawGridLinesInChart(chart, true);
      /////////////////////////////////// right axis end here /////////////////////////////////////
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.ORANGE, true);
      ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.RED, true);
      ExcelUtils.drawLineSeries(dataLeft, 2, PresetColor.GREEN, false);
      return ExcelUtils.finalizeWorkbook(wb, sheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public BCSToolHeardAnalysisReportDto prepareData(Object dto) {
    BCSToolHeardAnalysisReportDto mappedDto =
        modelMapper.map(dto, BCSToolHeardAnalysisReportDto.class);
    mappedDto.setLactationStages(
        mappedDto.getBcsAverage().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));
    return mappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    BCSToolHeardAnalysisReportDto mappedDto = prepareData(dto);
    byte[] report =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.BCS_HERD_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    BCSToolHeardAnalysisReportDto dto = modelMapper.map(data, BCSToolHeardAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.BCS_HERD_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook wb,
      XSSFSheet sheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      BCSToolHeardAnalysisReportDto dto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(getClass(), wb, sheet, rowNumber.get(), cellNumber.getAndIncrement());
    // headings
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getVisitName());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getVisitDate());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = sheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getToolName());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getAnalysisType());
    sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
  }
}
