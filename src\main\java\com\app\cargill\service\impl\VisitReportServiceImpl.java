/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.MediaTypes;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.constants.UserSettingsBrands;
import com.app.cargill.dto.BCSToolHeardAnalysisReportDto;
import com.app.cargill.dto.CudChewingHerdAnalysisReportDto;
import com.app.cargill.dto.MetabolicIncidenceReportDto;
import com.app.cargill.dto.NoteMediaItemDto;
import com.app.cargill.dto.NotesDto;
import com.app.cargill.dto.PenTimeBudgetPotentialMilkLossGainReportDto;
import com.app.cargill.dto.PenTimeBudgetTimeAvailableForRestingReportDto;
import com.app.cargill.dto.RumenFillHealthHerdAnalysisReportDto;
import com.app.cargill.dto.ToolHeaderDto;
import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Notes;
import com.app.cargill.model.UserPreferences;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.UserPreferencesRepository;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.service.IS3Service;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.IVisitReportService;
import com.app.cargill.sharepoint.service.VisitReportUploadToSharePoint;
import com.app.cargill.utils.DateTimeUtils;
import com.app.cargill.utils.ExcelUtils;
import io.netty.util.internal.StringUtil;
import java.awt.Color;
import java.awt.image.RenderedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.awscore.exception.AwsServiceException;

@Slf4j
@Service("visitReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S3655", "java:S125", "java:S3776", "java:S3358"})
public class VisitReportServiceImpl implements IVisitReportService {
  public static final String CLASSPATH_TEMPLATES_IMAGES_CARGILL_LOGO_PNG =
      "templates/images/cargill-logo.png";
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;
  private final UserPreferencesRepository userPreferencesRepository;
  private final IUserService userServiceImpl;
  private final NotesRepository notesRepository;
  private final IExcelReportService cudChewingHerdAnalysisReportServiceImpl;
  private final IExcelReportService bcsHerdAnalysisReportServiceImpl;
  private final IExcelReportService rumenHealthMSHerdAnalysisReportServiceImpl;
  private final IExcelReportService milkSoldEvaluationReportServiceImpl;
  private final IExcelReportService roboticMilkEvaluationReportServiceImpl;
  private final IExcelReportService metabolicIncidenceReportServiceImpl;
  private final IExcelReportService rumenFillHealthHerdAnalysisReportServiceImpl;
  private final IExcelReportService penTimeBudgetTimeAvailableForRestingReportServiceImpl;
  private final IExcelReportService penTimeBudgetPotentialMilkLossGainReportServiceImpl;
  private static final String CUD_CHEWING_CODE = "0000111CC";
  private static final String CALF_HEIFER_CODE = "0000222CH";
  private static final String HEAT_STRESS_CODE = "0000333HS";
  private static final String PENTIME_BUDGET_CODE = "0000444PB";
  private static final String BCS_CODE = "0000555BCS";
  private static final String LOCOMOTION_CODE = "0000666LS";
  private static final String METABOLIC_CODE = "0000777MI";
  private static final String RUMEN_FILL_CODE = "0000888RF";
  private static final String MANURE_SCORE_CODE = "0000999MS";
  private static final String MANURE_SCREENER_CODE = "0000101010MS";
  private static final String TMR_PARTICLE_CODE = "0000111111TMR";
  private static final String FORAGE_AUDIT_CODE = "0000121212FA";
  private static final String FORAGE_INVENTORIES_CODE = "0000131313FI";
  private static final String FORAGE_PENNSTATE_CODE = "0000141414FPS";
  private static final String MILKSOLD_EVALUATION_CODE = "0000151515MSE";
  private static final String ROBOTICMILK_EVALUATION_CODE = "0000161616RME";

  private final IS3Service s3ServiceImpl;
  private final VisitReportUploadToSharePoint visitReportUploadToSharePoint;

  @Override
  public ByteArrayResource downloadVisitReport(
      VisitReportDto dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    // Start prepare data here from user preferences
    getUserPreferences(dto);
    // download notes media
    downloadNotes(dto);
    // 1- prepare data for cudChewing herd analysis
    prepareDataForRumenHealthCudChewing(dto);
    // 2- prepare data for BCS
    prepareDataForBCS(dto);
    // 3- prepare data for locomotion score
    // prepareDataForLocomotionScore(dto);
    // 4- prepare data for Rumen Health Manure Score
    prepareDataForRumenHealthManureScore(dto);
    // 5- prepare data for milk sold Evaluation
    prepareDataForMilkSoldEvaluation(dto, source, locale);
    // 6- prepare data for robotic milking evaluation
    prepareDataForRoboticMilkingEvaluation(dto);
    // 7- prepare data for Metabolic Incidence
    prepareDataForMetabolicIncidence(dto, source, locale);
    // 8- prepare data for Metabolic Incidence
    prepareDataForRumenFill(dto);
    // 9- prepare data for Forage Inventories
    // prepareDataForageInventories(dto);
    // 11- prepare data for rumen health tmr particle score
    // prepareDataForRumenHealthTMRParticleScore(dto);
    // 12- prepare data for rumen health manure screening
    // prepareDataForRumenHealthManureScreening(dto);
    // 15- prepare data for rumen health manure screening
    prepareDataForPenTimeBudget(dto);
    // End prepare data
    byte[] bytes =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.VISIT_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_PDF);
    bytes = addHeaderFooterInPdf(bytes, source, locale, dto.getBrandName(), dto.getAccountName());
    // upload visit report to sharepoint
    visitReportUploadToSharePoint.uploadVisitReportToSharePoint(dto, bytes);
    return new ByteArrayResource(bytes);
  }

  @Override
  public void shareVisitReportToSharePoint(String visitId, MultipartFile visitReportFile)
      throws CustomDEExceptions, IOException {
    if (visitReportFile == null) {
      throw new CustomDEExceptions("PDF is NULL");
    }
    visitReportUploadToSharePoint.uploadOfflineVisitReportToSharePoint(
        visitId, visitReportFile.getBytes());
  }

  private void prepareDataForPenTimeBudget(VisitReportDto dto) {
    if (dto.getPenTimeBudgetTool() != null && dto.getPenTimeBudgetTool().getPenAnalysis() != null) {
      dto.getPenTimeBudgetTool().getPenAnalysis().parallelStream()
          .forEach(
              pen -> {
                pen.setTimeAvailableForRestingGraph(
                    (PenTimeBudgetTimeAvailableForRestingReportDto)
                        penTimeBudgetTimeAvailableForRestingReportServiceImpl.prepareData(
                            pen.getTimeAvailableForRestingGraph()));
                pen.setPotentialMilkLossGainGraph(
                    (PenTimeBudgetPotentialMilkLossGainReportDto)
                        penTimeBudgetPotentialMilkLossGainReportServiceImpl.prepareData(
                            pen.getPotentialMilkLossGainGraph()));
              });
    }
  }

  private void prepareDataForRumenFill(VisitReportDto dto) {

    if (dto.getRumenFillTool() != null
        && dto.getRumenFillTool().getHerdAnalysis() != null
        && dto.getRumenFillTool().getHerdAnalysis().getGraph() != null) {
      dto.getRumenFillTool()
          .getHerdAnalysis()
          .setGraph(
              (RumenFillHealthHerdAnalysisReportDto)
                  rumenFillHealthHerdAnalysisReportServiceImpl.prepareData(
                      dto.getRumenFillTool().getHerdAnalysis().getGraph()));
    }
  }

  private void prepareDataForMetabolicIncidence(
      VisitReportDto dto, ResourceBundleMessageSource source, Locale locale) {
    if (dto.getMetabolicIncidenceTool() != null
        && dto.getMetabolicIncidenceTool().getGraph() != null) {
      dto.getMetabolicIncidenceTool()
          .setGraph(
              (MetabolicIncidenceReportDto)
                  metabolicIncidenceReportServiceImpl.prepareData(
                      dto.getMetabolicIncidenceTool().getGraph(), source, locale));
    }
  }

  private void prepareDataForRoboticMilkingEvaluation(VisitReportDto dto) {
    if (dto.getRoboticMilkEvaluationTool() != null) {
      if (dto.getRoboticMilkEvaluationTool().getGraphs() != null) {
        dto.getRoboticMilkEvaluationTool().getGraphs().stream()
            .forEach(roboticMilkEvaluationReportServiceImpl::prepareData);
      }

      if (dto.getRoboticMilkEvaluationTool().getAnalysisDialGraphs() != null) {
        DecimalFormat twoDForm = new DecimalFormat("#.##");
        dto.getRoboticMilkEvaluationTool().getAnalysisDialGraphs().parallelStream()
            .forEach(
                graph -> {
                  if (graph.getGaugeColors() != null) {
                    AtomicReference<Double> calculatedValueSum = new AtomicReference<>(0.0);
                    AtomicInteger index = new AtomicInteger(0);
                    graph.getGaugeColors().stream()
                        .forEach(
                            gc -> {
                              calculatedValueSum.set(
                                  calculatedValueSum.get()
                                      + (gc.getEndAngle()
                                              - (index.get() == 0 ? 0 : gc.getStartAngle()))
                                          / graph.getGaugeTotal());
                              if (index.get() >= graph.getGaugeColors().size() - 1) {
                                calculatedValueSum.set(1.0);
                              }
                              gc.setCalculatedValue(
                                  Double.parseDouble(twoDForm.format(calculatedValueSum.get())));
                              index.incrementAndGet();
                            });
                  }
                });
      }
    }
  }

  private void downloadNotes(VisitReportDto dto) {
    List<NotesDto> notes = new ArrayList<>();
    // 1
    if (dto.getRumenHealthCudChewingTool() != null
        && dto.getRumenHealthCudChewingTool().getNotes() != null) {
      dto.getRumenHealthCudChewingTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getRumenHealthCudChewingTool().getNotes());
    }
    // 2
    if (dto.getBodyConditionScoreTool() != null
        && dto.getBodyConditionScoreTool().getNotes() != null) {
      dto.getBodyConditionScoreTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getBodyConditionScoreTool().getNotes());
    }
    // 3
    if (dto.getLocomotionScoreTool() != null && dto.getLocomotionScoreTool().getNotes() != null) {
      dto.getLocomotionScoreTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getLocomotionScoreTool().getNotes());
    }
    // 4
    if (dto.getRumenHealthManureScoreTool() != null
        && dto.getRumenHealthManureScoreTool().getNotes() != null) {
      dto.getRumenHealthManureScoreTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getRumenHealthManureScoreTool().getNotes());
    }
    // 5
    if (dto.getMilkSoldEvaluationTool() != null
        && dto.getMilkSoldEvaluationTool().getNotes() != null) {
      dto.getMilkSoldEvaluationTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getMilkSoldEvaluationTool().getNotes());
    }
    // 6
    if (dto.getRoboticMilkEvaluationTool() != null
        && dto.getRoboticMilkEvaluationTool().getNotes() != null) {
      dto.getRoboticMilkEvaluationTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getRoboticMilkEvaluationTool().getNotes());
    }

    // 7
    if (dto.getMetabolicIncidenceTool() != null
        && dto.getMetabolicIncidenceTool().getNotes() != null) {
      dto.getMetabolicIncidenceTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getMetabolicIncidenceTool().getNotes());
    }

    // 8
    if (dto.getRumenFillTool() != null && dto.getRumenFillTool().getNotes() != null) {
      dto.getRumenFillTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getRumenFillTool().getNotes());
    }
    // 9
    if (dto.getForageInventoriesTool() != null
        && dto.getForageInventoriesTool().getNotes() != null) {
      dto.getForageInventoriesTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getForageInventoriesTool().getNotes());
    }
    // 10
    if (dto.getForagePennStateTool() != null && dto.getForagePennStateTool().getNotes() != null) {
      dto.getForagePennStateTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getForagePennStateTool().getNotes());
    }
    // 11
    if (dto.getRumenHealthTMRParticleScoreTool() != null
        && dto.getRumenHealthTMRParticleScoreTool().getNotes() != null) {
      dto.getRumenHealthTMRParticleScoreTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getRumenHealthTMRParticleScoreTool().getNotes());
    }
    // 12
    if (dto.getRumenHealthManureScreeningTool() != null
        && dto.getRumenHealthManureScreeningTool().getNotes() != null) {
      dto.getRumenHealthManureScreeningTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getRumenHealthManureScreeningTool().getNotes());
    }

    // 13
    if (dto.getHeatStressEvaluationTool() != null
        && dto.getHeatStressEvaluationTool().getNotes() != null) {
      dto.getHeatStressEvaluationTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getHeatStressEvaluationTool().getNotes());
    }

    // 14
    if (dto.getForageAuditTool() != null && dto.getForageAuditTool().getNotes() != null) {
      dto.getForageAuditTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getForageAuditTool().getNotes());
    }
    // 15
    if (dto.getPenTimeBudgetTool() != null && dto.getPenTimeBudgetTool().getNotes() != null) {
      dto.getPenTimeBudgetTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getPenTimeBudgetTool().getNotes());
    }
    // 16
    if (dto.getScorecardTool() != null && dto.getScorecardTool().getNotes() != null) {
      dto.getScorecardTool().getNotes().parallelStream()
          .filter(n -> n.getId() == null)
          .forEach(n -> n.setId(UUID.randomUUID()));
      notes.addAll(dto.getScorecardTool().getNotes());
    }
    dto.setNotes(processNotes(notes));
  }

  private void prepareDataForMilkSoldEvaluation(
      VisitReportDto dto, ResourceBundleMessageSource source, Locale locale) {
    if (dto.getMilkSoldEvaluationTool() != null
        && dto.getMilkSoldEvaluationTool().getGraphs() != null) {
      dto.getMilkSoldEvaluationTool()
          .getGraphs()
          .forEach(graph -> milkSoldEvaluationReportServiceImpl.prepareData(graph, source, locale));
    }
  }

  private void prepareDataForRumenHealthManureScore(VisitReportDto dto) {
    if (dto.getRumenHealthManureScoreTool() != null
        && dto.getRumenHealthManureScoreTool().getHerdAnalysis() != null
        && dto.getRumenHealthManureScoreTool().getHerdAnalysis().getGraph() != null) {
      rumenHealthMSHerdAnalysisReportServiceImpl.prepareData(
          dto.getRumenHealthManureScoreTool().getHerdAnalysis().getGraph());
    }
  }

  private void prepareDataForBCS(VisitReportDto dto) {
    if (dto.getBodyConditionScoreTool() != null
        && dto.getBodyConditionScoreTool().getHerdAnalysis() != null
        && dto.getBodyConditionScoreTool().getHerdAnalysis().getGraph() != null) {
      dto.getBodyConditionScoreTool()
          .getHerdAnalysis()
          .setGraph(
              (BCSToolHeardAnalysisReportDto)
                  bcsHerdAnalysisReportServiceImpl.prepareData(
                      dto.getBodyConditionScoreTool().getHerdAnalysis().getGraph()));
    }
  }

  private void prepareDataForRumenHealthCudChewing(VisitReportDto dto) {
    if (dto.getRumenHealthCudChewingTool() != null
        && dto.getRumenHealthCudChewingTool().getHerdAnalysis() != null
        && dto.getRumenHealthCudChewingTool().getHerdAnalysis().getGraphs() != null) {
      dto.getRumenHealthCudChewingTool()
          .getHerdAnalysis()
          .setGraphs(
              (CudChewingHerdAnalysisReportDto)
                  cudChewingHerdAnalysisReportServiceImpl.prepareData(
                      dto.getRumenHealthCudChewingTool().getHerdAnalysis().getGraphs()));
    }
  }

  private List<NotesDto> processNotes(List<NotesDto> notesList) {
    if (notesList != null) {
      notesList.parallelStream()
          .forEach(
              n ->
                  n.setCratedDateTimeFormatted(
                      DateTimeUtils.instantToFormattedDate(n.getCreateTimeUtc())));
      List<Notes> notes =
          notesRepository.findByNotesIdsIn(
              notesList.stream()
                  .filter(n -> n.getId() != null)
                  .map(n -> n.getId().toString())
                  .toList());
      List<NotesDto> notesDtoList =
          new ArrayList<>(notes.parallelStream().map(this::getNote).toList());
      notesDtoList.stream()
          .forEach(noteDto -> notesList.removeIf(note -> note.getId().equals(noteDto.getId())));
      notesDtoList.addAll(
          notesList.stream()
              .filter(n -> !Strings.isBlank(n.getTitle()) || !Strings.isBlank(n.getNote()))
              .toList());
      return notesDtoList;
    }
    return Collections.emptyList();
  }

  private NotesDto getNote(Notes note) {
    NotesDto build =
        NotesDto.builder()
            .id(note.getNotesDocument().getId())
            .title(note.getNotesDocument().getTitle())
            .note(note.getNotesDocument().getNote())
            .cratedDateTimeFormatted(
                DateTimeUtils.instantToFormattedDate(note.getNotesDocument().getCreateTimeUtc()))
            .mediaItems(null)
            .build();

    if (!CollectionUtils.isEmpty(note.getNotesDocument().getMediaItems())) {
      build.setMediaItems(
          note.getNotesDocument().getMediaItems().parallelStream()
              .filter(
                  media ->
                      (media.getMediaType() == MediaTypes.Photo
                              || media.getMediaType() == MediaTypes.FreeHandNote)
                          && !Strings.isBlank(media.getMediaName()))
              .map(
                  media -> {
                    // download from s3

                    try {
                      String key =
                          note.getNotesDocument().getAccountId().toString()
                              + "/"
                              + media.getMediaName();
                      log.debug("downloading Media = " + key);
                      byte[] objectFromS3 = s3ServiceImpl.getObjectFromS3(key);
                      if (objectFromS3 != null && objectFromS3.length > 0) {
                        String[] mediaName = media.getMediaName().split(Pattern.quote("."));
                        objectFromS3 =
                            compressImage(
                                objectFromS3, mediaName.length >= 2 ? mediaName[1] : "jpeg", 0.45f);
                        String encodedImgString = Base64.getEncoder().encodeToString(objectFromS3);
                        return NoteMediaItemDto.builder()
                            .base64EncodedImage("data:image/jpeg;base64," + encodedImgString)
                            .build();
                      }
                    } catch (IOException | AwsServiceException e) {
                      log.error(
                          "Error while downloading media from s3 :" + e.getLocalizedMessage());
                    } catch (Exception ex) {
                      log.error(
                          "Error while downloading media from s3 :", ex.getLocalizedMessage());
                    }
                    return NoteMediaItemDto.builder().build();
                  })
              .filter(o -> !Strings.isBlank(o.getBase64EncodedImage()))
              .toList());
    }
    return build;
  }

  private void getUserPreferences(VisitReportDto dto) {
    if (Strings.isBlank(dto.getBrandName())) {
      UserPreferences userPreferences =
          userPreferencesRepository.findByUserId(userServiceImpl.getCurrentLoggedInUser());
      if (userPreferences != null) {
        dto.setBrandName(
            CollectionUtils.isEmpty(userPreferences.getUserPreferenceDocument().getBrandList())
                ? UserSettingsBrands.Cargill.name()
                : userPreferences.getUserPreferenceDocument().getBrandList().stream()
                    .findFirst()
                    .get()
                    .name());
      }
    }
  }

  @Override
  public String getFileName(VisitReportDto dto) {
    return Strings.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.VISIT_REPORT.getFileName()
        : dto.getFileName();
  }

  byte[] getBrandLogo(String brandName) throws IOException {
    if (!StringUtil.isNullOrEmpty(brandName)) {
      if (brandName.equalsIgnoreCase("Cargill")) {
        return new ClassPathResource(CLASSPATH_TEMPLATES_IMAGES_CARGILL_LOGO_PNG)
            .getInputStream()
            .readAllBytes();

      } else if (brandName.equalsIgnoreCase("Purina")) {
        return new ClassPathResource("templates/images/purina-logo.png")
            .getInputStream()
            .readAllBytes();

      } else if (brandName.equalsIgnoreCase("Provimi")) {
        return new ClassPathResource("templates/images/provimi-logo.png")
            .getInputStream()
            .readAllBytes();

      } else if (brandName.equalsIgnoreCase("ProvimiUS")) {
        return new ClassPathResource("templates/images/provimius-logo.png")
            .getInputStream()
            .readAllBytes();
      } else {
        return new ClassPathResource(CLASSPATH_TEMPLATES_IMAGES_CARGILL_LOGO_PNG)
            .getInputStream()
            .readAllBytes();
      }

    } else {
      return new ClassPathResource(CLASSPATH_TEMPLATES_IMAGES_CARGILL_LOGO_PNG)
          .getInputStream()
          .readAllBytes();
    }
  }

  /*float getFontWidth(String text, float fontSize) throws IOException {
    PDFont font = PDType1Font.HELVETICA_BOLD; // Or whatever font you want.
    return font.getStringWidth(text) / 1000 * fontSize;
  }*/

  byte[] addHeaderFooterInPdf(
      byte[] file,
      ResourceBundleMessageSource source,
      Locale locale,
      String brandName,
      String customerName)
      throws IOException {
    log.debug("===========================start adding pagination==============================");
    try (PDDocument document = PDDocument.load(file)) {
      PDFont normalFont =
          PDType0Font.load(
              document, new ClassPathResource("fonts/ArialUnicodeMS.ttf").getInputStream());
      PDFont italicFont =
          PDType0Font.load(
              document, new ClassPathResource("fonts/Arial-Unicode-Italic.ttf").getInputStream());
      float fontSize = 5.0F;

      int pageCounter = 1;
      int paginationOffsetXAxis = 70;
      int paginationOffsetYAxis = 20;
      int marginLeft = 15;
      int marginToolHeaderLeft = 25;
      int marginRight = 15;
      float footerScale = 0.57F;
      float headerScale = 0.90F;
      int marginTop = 40;
      int marginVisitReportTop = 25;
      int marginTopToolInfo = 40;
      byte[] brandLogoPath = getBrandLogo(brandName);
      byte[] cargillLogoPath =
          new ClassPathResource("templates/images/enteligen-logo.png")
              .getInputStream()
              .readAllBytes();

      String patent =
          ExcelUtils.getLangValue(
              LangKeys.VISIT_REPORT_FOOTER_PATENT,
              "Cargill Incorporated, its parents and affiliates does not warrant the accuracy of"
                  + " these estimates, due to many factors. There is no guarantee of production or"
                  + " financial results. ©2023 Cargill, Incorporated. All Rights Reserved.",
              null,
              source,
              locale);
      // float patentWidth = getFontWidth(patent, 5F);
      log.debug("Total pages Found = " + document.getPages().getCount());
      ToolHeaderDto toolHeaderDto = new ToolHeaderDto();
      String oldHeader = null;
      byte[] oldLogo = null;
      for (PDPage page : document.getPages()) {

        if (pageCounter != 1) {
          PDFTextStripper stripper = new PDFTextStripper();
          stripper.setStartPage(pageCounter);
          stripper.setEndPage(pageCounter);

          String pageText = stripper.getText(document);
          toolHeaderDto = getToolHeaderContent(pageText, source, locale, customerName);

          if (toolHeaderDto.getToolContent() != null) {
            oldHeader = toolHeaderDto.getToolContent();
            oldLogo = toolHeaderDto.getLogo();

          } else {
            toolHeaderDto.setToolContent(oldHeader);
            toolHeaderDto.setLogo(oldLogo);
          }
        }

        PDPageContentStream contentStream =
            new PDPageContentStream(
                document, page, PDPageContentStream.AppendMode.APPEND, true, true);
        contentStream.setStrokingColor(Color.lightGray);
        if (pageCounter != 1) {
          contentStream.setFont(normalFont, 8);
        } else {
          contentStream.setFont(normalFont, 12);
        }
        PDRectangle pageSize = page.getMediaBox();
        // ========================Header Starts Here===================================
        // visit report text
        contentStream.beginText();
        contentStream.newLineAtOffset(marginLeft, pageSize.getHeight() - marginVisitReportTop);
        String text =
            ExcelUtils.getLangValue(LangKeys.VISIT_REPORT, "Visit Report", null, source, locale);
        contentStream.showText(text);
        contentStream.endText();

        // Tool header content (Tool name + Customer Name)
        if (toolHeaderDto.getToolContent() != null) {
          float toolContentXOffset = marginToolHeaderLeft; // Position of the tool content
          float toolContentYOffset = pageSize.getHeight() - marginTopToolInfo;

          // Draw the tool content
          contentStream.setStrokingColor(Color.LIGHT_GRAY);
          contentStream.setFont(normalFont, 10);
          contentStream.beginText();
          contentStream.newLineAtOffset(toolContentXOffset, toolContentYOffset);
          contentStream.showText(toolHeaderDto.getToolContent());
          contentStream.endText();

          // If there's a logo, draw it
          if (toolHeaderDto.getLogo() != null) {
            PDImageXObject pdToolLogo =
                PDImageXObject.createFromByteArray(document, toolHeaderDto.getLogo(), "tool logo");
            contentStream.setNonStrokingColor(Color.WHITE);
            // Calculate the position of the logo
            float logoWidth = 15; // Width of the logo
            float logoHeight = 15; // Height of the logo
            float logoXOffset = marginLeft - logoWidth + 13; // Adjust the spacing as needed
            float logoYOffset = toolContentYOffset - 4; // Adjust the vertical positioning as needed
            // Draw the logo
            contentStream.drawImage(pdToolLogo, logoXOffset, logoYOffset, logoWidth, logoHeight);

            // Adjust the position of the tool content text

            contentStream.setNonStrokingColor(Color.LIGHT_GRAY);

            toolContentXOffset = marginLeft; // Move it to the right of the logo
          }
        }

        // enteligen logo
        PDImageXObject pdImage =
            PDImageXObject.createFromByteArray(document, cargillLogoPath, "enteligen logo");
        contentStream.drawImage(
            pdImage,
            pageSize.getWidth() - 83,
            pageSize.getHeight() - marginTop,
            pdImage.getWidth() * headerScale,
            pdImage.getHeight() * headerScale);

        // horizontal line
        contentStream.moveTo(marginLeft, pageSize.getHeight() - marginTop - 7);
        contentStream.lineTo(
            pageSize.getWidth() - marginRight, pageSize.getHeight() - marginTop - 7);
        contentStream.stroke();

        // ========================Header Ends Here=====================================
        // =============================================================================
        // ========================Footer Starts Here===================================

        float x = pageSize.getLowerLeftX();
        float y = pageSize.getLowerLeftY();

        // footer patent
        contentStream.setFont(italicFont, fontSize);
        contentStream.beginText();
        contentStream.newLineAtOffset(marginLeft, y + paginationOffsetYAxis + 18);
        contentStream.showText(patent);
        contentStream.endText();

        // update font size
        contentStream.setFont(normalFont, 10);
        // footer horizontal line
        contentStream.moveTo(marginLeft, y + 35);
        contentStream.lineTo(pageSize.getWidth() - marginRight, y + 35);
        contentStream.stroke();

        // footer pagination
        contentStream.beginText();
        contentStream.newLineAtOffset(
            x
                + pageSize.getWidth()
                - paginationOffsetXAxis
                - (pageCounter >= 100 ? 6 : pageCounter >= 10 ? 6 : 0),
            y + paginationOffsetYAxis);
        text =
            MessageFormat.format("Page {0} of {1}", pageCounter++, document.getPages().getCount());
        contentStream.showText(text);
        contentStream.endText();

        // footer brand logo
        pdImage = PDImageXObject.createFromByteArray(document, brandLogoPath, "footer logo");
        contentStream.drawImage(
            pdImage,
            (pageSize.getWidth() / 2) - 42,
            y + 10,
            pdImage.getWidth() * footerScale,
            pdImage.getHeight() * footerScale);

        // footer Visit details
        /*contentStream.beginText();
        contentStream.newLineAtOffset(marginLeft, y + paginationOffsetYAxis);
        text =
            ExcelUtils.getLangValue(
                LangKeys.VISIT_REPORT_FOOTER_DETAILS, "Visit Details", null, source, locale);
        contentStream.showText(text);
        contentStream.endText();*/
        // ========================Footer Ends Here===================================
        contentStream.close();
      }
      OutputStream out = new ByteArrayOutputStream();
      document.save(out);
      log.debug("===========================end adding pagination==============================");
      return ((ByteArrayOutputStream) out).toByteArray();
    }
  }

  public ToolHeaderDto getToolHeaderContent(
      String pageText, ResourceBundleMessageSource source, Locale locale, String customerName)
      throws IOException {

    if (pageText.contains(TMR_PARTICLE_CODE)) {
      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.REPORT_RUMEN_HEALTH_TMR_PARTICLE_SCORE,
                      "TMR penn state",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/nutrition-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(CUD_CHEWING_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.CUD_CHEWING_TITLE, "Rumen health cud chewing", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(CALF_HEIFER_CODE)) {
      //
      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.CALF_HEIFER_TITLE, "Calf and heifer", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/comfort-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(HEAT_STRESS_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.HEAT_STRESS_TITLE, "Heat Stress Evaluation", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/comfort-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(RUMEN_FILL_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.REPORT_FILL_HERD_ANALYSIS_CHART_NAME,
                      "Rumen fill",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(PENTIME_BUDGET_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.PENTIME_BUDGET_TITLE, "Pen time budget", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/comfort-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(BCS_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.BODY_CONDITION_TITLE, "Body condition score", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(LOCOMOTION_CODE)) {
      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.REPORT_LOCOMOTION_SCORE, "Locomotion score", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(METABOLIC_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.METABOLIC_INCIDENCE_TITLE,
                      "Metabolic Incidence",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(MANURE_SCORE_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.RUMEN_HEALTH_MANURE_SCORE_TITLE,
                      "Rumen Health Manure Score",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(MANURE_SCREENER_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING,
                      "Rumen health manure screening",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/health-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(FORAGE_AUDIT_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.FORAGE_AUDIT_TITLE, "Forage audit", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/nutrition-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(FORAGE_INVENTORIES_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.FORAGE_INVENTORIES_TITLE, "Forage inventories", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/nutrition-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(FORAGE_PENNSTATE_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.REPORT_FORAGE_PEN_STATE, "Forage Penn State", null, source, locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/nutrition-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(MILKSOLD_EVALUATION_CODE)) {

      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.MILK_SOLD_EVALUATION_TITLE,
                      "Milk Sold Evaluation",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/productivity-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    } else if (pageText.contains(ROBOTICMILK_EVALUATION_CODE)) {
      return ToolHeaderDto.builder()
          .toolContent(
              "   "
                  + ExcelUtils.getLangValue(
                      LangKeys.ROBOTIC_MILK_EVALUATION_TITLE,
                      "Robotic Milking Evaluation",
                      null,
                      source,
                      locale)
                  + " | "
                  + customerName)
          .logo(
              new ClassPathResource("templates/images/productivity-logo.png")
                  .getInputStream()
                  .readAllBytes())
          .build();
    }
    return ToolHeaderDto.builder().toolContent(null).logo(null).build();
  }

  static byte[] compressImage(byte[] img, String fileExtension, float quality) throws IOException {
    try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
      ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(img);
      ImageWriter writer = ImageIO.getImageWritersBySuffix(fileExtension).next();

      ImageWriteParam param = writer.getDefaultWriteParam();
      param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
      param.setCompressionQuality(quality);
      RenderedImage renderedImage = ImageIO.read(byteArrayInputStream);
      byteArrayInputStream.close();
      IIOImage image = new IIOImage(renderedImage, new ArrayList<>(), null);
      writer.setOutput(ImageIO.createImageOutputStream(out));
      writer.write(null, image, param);
      return out.toByteArray();
    }
  }
}
