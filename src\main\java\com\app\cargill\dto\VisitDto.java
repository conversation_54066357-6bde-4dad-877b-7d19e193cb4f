/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.constants.VisitStatus;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("java:S125") // @TODO remove this ince commented out code is cleaned
public class VisitDto extends BaseDto {

  /// <summary>
  /// Labyrinth Guid for the account this visit belonds to.
  /// </summary>
  private UUID customerId;
  /// <summary>
  /// Labyrinth Guid for the site of this visit.
  /// </summary>
  private UUID siteId;

  private String siteName;

  private String accountName;

  private Integer toolsUsed;

  private UnitOfMeasureKeys unitOfMeasure;

  /// <summary>
  /// The date the visit occurred. This date never changes and is
  /// set when the visit is created.
  /// </summary>
  private Instant visitDate;
  /// <summary>
  /// Indicates if the visit is In Progress or Published.
  /// Once published, a visit and all its contained artifacts cannot
  /// be changed.
  /// </summary>
  ///
  private String firstName;

  /// <summary>
  /// Gets or sets the last name.
  /// </summary>
  /// <value>The last name.</value>
  private String lastName;

  private VisitStatus status; // change

  private Boolean selected;

  private String visitName;

  private String formattedCreationDate;
  /// <summary>
  /// Gets or sets a value indicating whether this <see
  /// cref="T:Labyrinth.Common.Entities.Visit"/> is visit auto published.
  /// </summary>
  /// <value><c>true</c> if is visit auto published; otherwise,
  /// <c>false</c>.</value>
  @Builder.Default private Boolean isVisitAutoPublished = false;

  private Boolean needsSync;

  private BCSPointScale selectedPointScale;
  /// <summary>
  /// The first key is the report id, the second is the account id.
  /// </summary>

  //  private Dictionary<UUID, UUID> generatedPDFReports;
  //
  // private CudChewingTool cudChewing; // change
  //
  private HeatStressToolDto heatStress; // change
  //
  private ScorecardDto forageAuditScorecard; // change
  //
  private PileAndBunkerToolDto pileAndBunker; // change
  //
  private RumenHealthToolDto rumenHealth; // change
  //
  private RumenHealthTMRParticleScoreToolDto tmrParticleScore; // change
  //
  private RumenHealthManureScoreToolDto rumenHealthManureScore; // change
  //
  private LocomotionToolDto locomotionScore;
  //
  private BodyConditionToolDto bodyCondition; // change

  private AnimalAnalysisToolDto animalAnalysis;

  //
  //  private RevenueInputs revenue; // c
  //
  private MetabolicIncidenceToolDto metabolicIncidence; // c
  //
  private PenTimeBudgetToolDto penTimeBudgetTool; // c
  //
  private Currencies selectedCurrency; // c
  //
  //  private VisitWalkThroughReports walkThroughReports; // c
  //  // Addded for ReadyToMilk ELH Tool
  //  private ReadyToMilkTool readyToMilk; // c
  // Added For Milk Sold Evaluation Tool
  private MilkSoldEvaluationToolDto milkSoldEvaluation; // c
  //
  //  private UrinePHTool urinePHTool;
  //
  private CalfHeiferScorecardDto calfHeiferScorecard; // c
  //
  private RoboticMilkEvaluationToolDto roboticMilkEvaluation; // c
  //
  private ManureScreenerToolDto manureScreenerTool; // c
  //
  private RumenFillToolDto rumenFillManureScore; // c

  private ForagePennStateToolDto foragePennState;

  private ProfitabilityAnalysisToolDto profitabilityAnalysisTool;
  //
  //  private List<ReportType> reportType;
  //  // region Backlog - 272095 - Outlook calendar dates extend to extra days for
  // activities
  private Instant visitPublishedDateTimeUtc;

  private String createUser;

  private String lastModifyUser;

  private boolean isNew;

  private Map<String, List<UUID>> usedPens = new HashMap<>();

  // isdeleted changed

  private ReturnOverFeedToolDto returnOverFeedTool;
}
