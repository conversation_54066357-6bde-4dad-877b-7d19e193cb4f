/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.app.cargill.constants.Business;
import com.app.cargill.sf.crescendo.model.BusinessUnitCrescendo;
import org.junit.jupiter.api.Test;

@SuppressWarnings("java:S5961") // Multiple test cases required
class BusinessToBusinessUnitMapperTest {

  @Test
  void businessToCrescendo() {
    assertNull(BusinessToBusinessUnitMapper.toBusinessUnit(Business.Global));
    assertEquals(
        BusinessUnitCrescendo.BRAZIL, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Brazil));
    assertEquals(
        BusinessUnitCrescendo.CANADA, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Canada));
    assertEquals(
        BusinessUnitCrescendo.FRANCE, BusinessToBusinessUnitMapper.toBusinessUnit(Business.France));
    assertEquals(
        BusinessUnitCrescendo.NETHERLANDS,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Netherlands));
    assertEquals(
        BusinessUnitCrescendo.PHILIPPINES,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Philippines));
    assertEquals(
        BusinessUnitCrescendo.POLAND, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Poland));
    assertEquals(
        BusinessUnitCrescendo.US, BusinessToBusinessUnitMapper.toBusinessUnit(Business.US));
    assertEquals(
        BusinessUnitCrescendo.VIETNAM,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Vietnam));
    assertEquals(
        BusinessUnitCrescendo.SPAIN, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Spain));
    assertEquals(
        BusinessUnitCrescendo.ITALY, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Italy));
    assertEquals(
        BusinessUnitCrescendo.KOREA, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Korea));
    assertEquals(
        BusinessUnitCrescendo.INDIA, BusinessToBusinessUnitMapper.toBusinessUnit(Business.India));
    assertEquals(
        BusinessUnitCrescendo.MEXICO, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Mexico));
    assertEquals(
        BusinessUnitCrescendo.RUSSIA, BusinessToBusinessUnitMapper.toBusinessUnit(Business.Russia));
    assertEquals(
        BusinessUnitCrescendo.SOUTH_AFRICA,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.SouthAfrica));
    assertEquals(
        BusinessUnitCrescendo.CPN_BRAZIL,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.CPNBrazil));
    assertEquals(
        BusinessUnitCrescendo.CPN_FRANCE,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.CPNFrance));
    assertEquals(
        BusinessUnitCrescendo.NORTH_AMERICA,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.NorthAmerica));
    assertEquals(
        BusinessUnitCrescendo.CHINA, BusinessToBusinessUnitMapper.toBusinessUnit(Business.China));
    assertEquals(
        BusinessUnitCrescendo.PORTUGAL,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Portugal));
    assertEquals(
        BusinessUnitCrescendo.UKRAINE,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Ukraine));
    assertEquals(
        BusinessUnitCrescendo.CFN_CHINA,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.CFNChina));
    assertEquals(
        BusinessUnitCrescendo.CFN_INDIA,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.CFNIndia));
    assertEquals(
        BusinessUnitCrescendo.CPN_POLAND,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.CPNPoland));
    assertEquals(
        BusinessUnitCrescendo.CPN_US, BusinessToBusinessUnitMapper.toBusinessUnit(Business.CPNUS));
    assertEquals(
        BusinessUnitCrescendo.HUNGARY,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Hungary));
    assertEquals(
        BusinessUnitCrescendo.UNITED_KINGDOM,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.UK));
    assertEquals(
        BusinessUnitCrescendo.PAKISTAN,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Pakistan));
    assertEquals(
        BusinessUnitCrescendo.UNITED_STATES,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.UNITED_STATES));
    assertEquals(
        BusinessUnitCrescendo.ARGENTINA,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Argentina));
    assertEquals(
        BusinessUnitCrescendo.NEOLAIT_FRANCE,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Neolait_FRANCE));
    assertEquals(
        BusinessUnitCrescendo.PROVIMI_FRANCE,
        BusinessToBusinessUnitMapper.toBusinessUnit(Business.Provimi_FRANCE));
  }

  @Test
  void crescendoToBusiness() {
    assertNull(BusinessToBusinessUnitMapper.toBusiness(null));
    assertEquals(
        Business.Brazil, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.BRAZIL));
    assertEquals(
        Business.Canada, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CANADA));
    assertEquals(
        Business.France, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.FRANCE));
    assertEquals(
        Business.Netherlands,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.NETHERLANDS));
    assertEquals(
        Business.Philippines,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.PHILIPPINES));
    assertEquals(
        Business.Poland, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.POLAND));
    assertEquals(Business.US, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.US));
    assertEquals(
        Business.Vietnam, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.VIETNAM));
    assertEquals(
        Business.Spain, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.SPAIN));
    assertEquals(
        Business.Italy, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.ITALY));
    assertEquals(
        Business.Korea, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.KOREA));
    assertEquals(
        Business.India, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.INDIA));
    assertEquals(
        Business.Mexico, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.MEXICO));
    assertEquals(
        Business.Russia, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.RUSSIA));
    assertEquals(
        Business.SouthAfrica,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.SOUTH_AFRICA));
    assertEquals(
        Business.CPNBrazil,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CPN_BRAZIL));
    assertEquals(
        Business.CPNFrance,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CPN_FRANCE));
    assertEquals(
        Business.NorthAmerica,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.NORTH_AMERICA));
    assertEquals(
        Business.China, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CHINA));
    assertEquals(
        Business.Portugal, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.PORTUGAL));
    assertEquals(
        Business.Ukraine, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.UKRAINE));
    assertEquals(
        Business.CFNChina,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CFN_CHINA));
    assertEquals(
        Business.CFNIndia,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CFN_INDIA));
    assertEquals(
        Business.CPNPoland,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CPN_POLAND));
    assertEquals(
        Business.CPNUS, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.CPN_US));
    assertEquals(
        Business.Hungary, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.HUNGARY));
    assertEquals(
        Business.UK, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.UNITED_KINGDOM));
    assertEquals(
        Business.Pakistan, BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.PAKISTAN));
    assertEquals(
        Business.UNITED_STATES,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.UNITED_STATES));
    assertEquals(
        Business.Argentina,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.ARGENTINA));
    assertEquals(
        Business.Neolait_FRANCE,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.NEOLAIT_FRANCE));
    assertEquals(
        Business.Provimi_FRANCE,
        BusinessToBusinessUnitMapper.toBusiness(BusinessUnitCrescendo.PROVIMI_FRANCE));
  }
}
