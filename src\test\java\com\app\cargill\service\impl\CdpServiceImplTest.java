/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.SubTypeId;
import com.app.cargill.document.*;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.*;
import com.app.cargill.repository.*;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
@Slf4j
class CdpServiceImplTest {

  @Mock private AccountsRepository accountsRepository;

  @Mock private SitesRepository sitesRepository;

  @Mock private VisitsRepository visitsRepository;

  @Mock private CdpSitesRepository cdpSitesRepository;

  @InjectMocks private CdpServiceImpl cdpService;

  @Test
  void testGetAllAccountsByFromAndToDate() {

    Page<Accounts> page =
        new PageImpl<>(
            createTestAccounts().stream()
                .filter(
                    accounts -> accounts.getAccountDocument().getActive() && !accounts.isDeleted())
                .collect(Collectors.toList()));

    when(accountsRepository.findAllAccountsByFromAndToDate(any(), any(), any())).thenReturn(page);

    Page<AccountDocumentDTO> result =
        cdpService.getAllAccountsByFromAndToDate(
            Instant.now().minus(1, ChronoUnit.DAYS), Instant.now(), Pageable.ofSize(5).withPage(1));

    assertThat(result).hasSize(3);
    assertThat(result.getContent().get(0).getAccountName()).isEqualTo("TestAccount1");
    assertThat(result.getContent().get(1).getAccountName()).isEqualTo("TestAccount2");
    assertThat(result.getContent().get(2).getAccountName()).isEqualTo("TestAccount3");
  }

  @Test
  void testGetAllSitesByFromAndToDate() {
    // Mock data
    UUID siteId1 = UUID.randomUUID();
    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(siteId1);
    siteDocument1.setSiteName("Site 1");

    UUID siteId2 = UUID.randomUUID();
    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(siteId2);
    siteDocument2.setSiteName("Site 2");

    Sites site1 = new Sites();
    site1.setSiteDocument(siteDocument1);

    Sites site2 = new Sites();
    site2.setSiteDocument(siteDocument2);

    CdpSiteDataWrapper dataWrapper1 = new CdpSiteDataWrapper();
    dataWrapper1.setSiteDocument(site1.getSiteDocument());
    CdpSiteDataWrapper dataWrapper2 = new CdpSiteDataWrapper();
    dataWrapper2.setSiteDocument(site2.getSiteDocument());

    when(cdpSitesRepository.getCdpSitesData(any(), any()))
        .thenReturn(List.of(dataWrapper1, dataWrapper2));

    // Call the method under test
    Page<SiteDocumentDTO> result =
        cdpService.getAllSitesByFromAndToDate(
            Instant.parse("2023-06-01T00:00:00Z"),
            Instant.parse("2023-06-30T23:59:59Z"),
            Pageable.unpaged());

    // Assertions
    assertThat(result).hasSize(2);
    assertThat(result.getContent().get(0).getId()).isEqualTo(siteId1);
    assertThat(result.getContent().get(0).getSiteName()).isEqualTo("Site 1");
    assertThat(result.getContent().get(1).getId()).isEqualTo(siteId2);
    assertThat(result.getContent().get(1).getSiteName()).isEqualTo("Site 2");
  }

  @Test
  void testGetAllVisitsByFromAndToDate() {
    // Mock data
    UUID visitId1 = UUID.randomUUID();
    VisitDocument visitDocument1 = new VisitDocument();
    visitDocument1.setId(visitId1);
    visitDocument1.setVisitName("Visit 1");

    UUID visitId2 = UUID.randomUUID();
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setId(visitId2);
    visitDocument2.setVisitName("Visit 2");

    Visits visit1 = new Visits();
    visit1.setVisitDocument(visitDocument1);

    Visits visit2 = new Visits();
    visit2.setVisitDocument(visitDocument2);

    Page<Visits> page = new PageImpl<>(Arrays.asList(visit1, visit2));

    when(visitsRepository.findAllVisitsByFromAndToDate(any(), any(), any())).thenReturn(page);

    // Call the method under test
    Page<VisitDocumentDTO> result =
        cdpService.getAllVisitsByFromAndToDate(
            Instant.parse("2023-06-01T00:00:00Z"),
            Instant.parse("2023-06-30T23:59:59Z"),
            Pageable.unpaged());

    // Assertions
    assertThat(result).hasSize(2);
    assertThat(result.getContent().get(0).getId()).isEqualTo(visitId1);
    assertThat(result.getContent().get(0).getVisitName()).isEqualTo("Visit 1");
    assertThat(result.getContent().get(1).getId()).isEqualTo(visitId2);
    assertThat(result.getContent().get(1).getVisitName()).isEqualTo("Visit 2");
  }

  // Need a utility class to create these sample data loads and use in integration tests too - if
  // they want any.
  private List<Accounts> createTestAccounts() {
    AccountDocument accountDocument1 =
        createAccountDocuments(true, false, "TestAccount1", Set.of("user1", "user2"));
    AccountDocument accountDocument2 =
        createAccountDocuments(true, false, "TestAccount2", Set.of("user3", "user4"));
    AccountDocument accountDocument3 =
        createAccountDocuments(true, false, "TestAccount3", Set.of("user5", "user6"));
    AccountDocument accountDocument4 =
        createAccountDocuments(false, true, "TestAccount4", Set.of("user7", "user8"));
    AccountDocument accountDocument5 =
        createAccountDocuments(true, true, "TestAccount5", Set.of("user9", "user10"));

    Accounts account1 =
        Accounts.builder()
            .accountDocument(accountDocument1)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument1.isDeleted)
            .build();

    Accounts account2 =
        Accounts.builder()
            .accountDocument(accountDocument2)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument2.isDeleted)
            .build();

    Accounts account3 =
        Accounts.builder()
            .accountDocument(accountDocument3)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument3.isDeleted)
            .build();

    Accounts account4 =
        Accounts.builder()
            .accountDocument(accountDocument4)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument4.isDeleted)
            .build();

    Accounts account5 =
        Accounts.builder()
            .accountDocument(accountDocument5)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument5.isDeleted)
            .build();

    return List.of(account1, account2, account3, account4, account5);
  }

  private static AccountDocument createAccountDocuments(
      boolean isActive, boolean isDeleted, String accountName, Set<String> users) {
    return AccountDocument.builder()
        .id(UUID.randomUUID())
        .accountName(accountName)
        .accountCurrency(1)
        .accountType(1)
        .accountStatus("Test Status")
        .accountValidated(true)
        .users(users)
        .contacts(
            List.of(
                Contact.builder()
                    .contactId(UUID.randomUUID())
                    .sFDCContactId("sf-1")
                    .firstName("John")
                    .lastName("Doe")
                    .build(),
                Contact.builder()
                    .contactId(UUID.randomUUID())
                    .firstName("Jane")
                    .lastName("Doe")
                    .build()))
        .active(isActive)
        .subTypeID(SubTypeId.FarmProducer)
        .physicalAddress(Address.builder().build())
        .dateOfLastVisit(Instant.now())
        .isDeleted(isDeleted)
        .build();
  }
}
