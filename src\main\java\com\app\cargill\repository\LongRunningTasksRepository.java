/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.LongRunningTask;
import com.app.cargill.model.LongRunningTask.TaskName;
import java.io.Serializable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LongRunningTasksRepository<T extends Serializable>
    extends JpaRepository<LongRunningTask<T>, Long> {

  @Query
  LongRunningTask<T> findByLocalId(String localId);

  LongRunningTask<T> findTopByNameOrderByCreatedDateDesc(TaskName taskName);

  @Modifying
  @Query(value = "update tasks set status = 'STOPPED' where status = 'RUNNING'", nativeQuery = true)
  @Transactional
  void stopRunningTasks();
}
