/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.model.StateCosmos;
import com.app.cargill.cosmos.repo.StateCosmosRepository;
import com.app.cargill.repository.StatesRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StateMigrationTest {
  @Mock private StateCosmosRepository repository;

  @Mock private StatesRepository postgresRepository;

  @InjectMocks private StateMigration service;

  @BeforeEach
  void setUp() {
    lenient().when(postgresRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    StateCosmos item =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/state.json"), StateCosmos.class);

    Iterable<StateCosmos> itemsList = List.of(item, item);
    when(repository.findAll()).thenReturn(itemsList);

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenPostMigrationIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result = service.postMigration(CosmosDataMigration.MigrationType.STATE.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    StateCosmos item1 = new StateCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    StateCosmos item2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/state.json"), StateCosmos.class);

    Iterable<StateCosmos> itemsList = List.of(item1, item2);
    when(repository.findAll()).thenReturn(itemsList);

    MigrationResult result = service.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result = service.migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.SITES)).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }
}
