<div class="container mid-body">
    <div class="pt-0">
        <div class="legend-head">
            <div class="count">${toolNumber!}</div>
            <div class="main-title">
                <span class="sm-head">${localization.getMessage("VisitSummaryViewModel.HealthItem", [], "Health",
                    locale)}</span>
                <span class="lg-head">${localization.getMessage("VisitSummaryViewModel.RumenHealthTitle", [], "Rumen
                    Health Cud Chewing", locale)}</span>
            </div>
            <div style="font-size: 1;color: white;">0000111CC</div>
        </div>
        <#if model.rumenHealthCudChewingTool.penAnalysis ??>
            <h3 class="title-secondary mb-1">${localization.getMessage("WalkthroughReportLandingViewModel.PenAnalysis",
                [], "Pen Analysis", locale)}</h3>
            <div class="row mx-neg-4">

                <#list model.rumenHealthCudChewingTool.penAnalysis as obj>
                    <div class="col-4 px-4 table-secondary">
                        <h3 class="title">${obj.penName!}</h3>
                        <table class="table-center">
                            <tr>
                                <th>${localization.getMessage("Report.Chewing", [], "Chewing", locale)}</th>
                                <th>${localization.getMessage("Report.Not.Chewing", [], "Not Chewing", locale)}</th>
                            </tr>
                            <tr>
                                <td style="width:50%">${obj.chewing!}</td>
                                <td style="width:50%">${obj.notChewing!}</td>
                            </tr>
                        </table>

                        <table class="table-center">
                            <tr>
                                <th>${localization.getMessage("Cow", [], "Cows", locale)}</th>
                                <th>${localization.getMessage("EmailReportViewModel.NumOfChews", [], "Number Of Chews",
                                    locale)}</th>
                            </tr>
                            <#if obj.noOfChews ??>
                                <#list obj.noOfChews as chew>
                                    <tr>
                                        <td style="width:50%">${chew.cowName!}</td>
                                        <td style="width:50%">${chew.numberOfChews!}</td>
                                    </tr>
                                </#list>
                            </#if>
                        </table>

                    </div>
                </#list>

            </div>
        </#if>
    </div>
</div>

<#if model.rumenHealthCudChewingTool.penAnalysis ??>
    <div class="break-page"></div>
    <#assign counter=0>
        <div class="container mid-body">
            <div class="pt-0">
                <#if model.rumenHealthCudChewingTool.penAnalysis ??>
                    <#list model.rumenHealthCudChewingTool.penAnalysis as obj>
                        <#if obj.cudChewingChart?? || obj.noOfChewsChart??>
                            <h5 class="title-sub">${obj.penName!}</h5>
                        </#if>
                        <div class="row mx-neg-4">
                            <#if obj.cudChewingChart??>
                                <div class="col-6 px-4">
                                    <div class="card mb-1">
                                        <div class="card-header">
                                            <h4 class="text-center">
                                                ${localization.getMessage("CudChewingDataEntryViewModel.CudChewing", [],
                                                "Cud Chewing", locale)}</h4>
                                        </div>
                                        <div class="card-body">
                                            <#assign linechart=statics["java.util.UUID"].randomUUID()>
                                                <canvas id="${linechart}"></canvas>
                                        </div>
                                        <div class="card-footer  pt-0 mb-1">
                                            <div class="row">
                                                <div class="legend-wrap mb-1">
                                                    <p> </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <script>
                                    (function () {
                                        const colors = {
                                            purple: {
                                                default: "#1baca7",
                                                half: "#1baca778",
                                                quarter: "#1baca73b",
                                                zero: "#1baca71c"
                                            },
                                            indigo: {
                                                default: "#1baca7",
                                                quarter: "#1baca778"
                                            }
                                        };


                                        const yAxis = [
                                            <#list obj.cudChewingChart.chewingPercentages as chewingPercentage >
                                            ${(chewingPercentage.chewingPercentage)!'NaN'} <#sep>, </#sep>
    </#list >
];

                                    const xAxis = [
                                        <#list obj.cudChewingChart.chewingPercentages as chewingPercentage >
                                        "${(chewingPercentage.visitDate)!}" <#sep>, </#sep>
    </#list >
];

                                    const ctx = document.getElementById("${linechart}").getContext("2d");
                                    ctx.canvas.height = 100;

                                    gradient = ctx.createLinearGradient(0, 25, 0, 300);
                                    gradient.addColorStop(0, colors.purple.half);
                                    gradient.addColorStop(0.35, colors.purple.quarter);
                                    gradient.addColorStop(1, colors.purple.zero);

                                    const options = {
                                        type: "line",
                                        data: {
                                            labels: xAxis,
                                            datasets: [
                                                {
                                                    fill: true,
                                                    backgroundColor: gradient,
                                                    borderColor: '#1BACA7',
                                                    pointBackgroundColor: '#1BACA7',
                                                    pointBorderColor: '#fff',
                                                    data: yAxis,
                                                    lineTension: 0.2,
                                                    borderWidth: 1,
                                                    pointRadius: 6,
                                                }
                                            ]
                                        },
                                        options: {
                                            plugins: {
                                                legend: {
                                                    display: false,
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        title: () => null // or function () { return null; }
                                                    },
                                                    yAlign: 'bottom',
                                                    backgroundColor: "#fff",
                                                    borderColor: "rgba(0, 0, 0, 0.25)",
                                                    borderWidth: 1,
                                                    displayColors: false,
                                                    bodyColor: "#1BACA7",
                                                    bodyAlign: "center",
                                                },
                                            },

                                            layout: {
                                                padding: {
                                                    top: 20,
                                                    right: 15
                                                }
                                            },

                                            responsive: true,

                                            scales: {
                                                y: {
                                                    // beginAtZero: true,
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("CudChewsDatesForComparisonViewModel.CudChewsPercent", [], "Cud chewing %", locale)}',
                                                        padding: {
                                                            bottom: 15,
                                                        }
                                                    },

                                                    grid: {
                                                        display: false,
                                                    },
                                                },

                                                x: {
                                                    grid: {
                                                        display: false,
                                                    },
                                                    color: '#6C7782',
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                        padding: {
                                                            bottom: 15,
                                                        }
                                                    }
                                                }
                                            },
                                            animation: {
                                                duration: 0,
                                                onComplete: function () {
                                                    var chart = this;
                                                    var ctx = chart.ctx;
                                                    ctx.textAlign = 'center';
                                                    ctx.textBaseline = 'bottom';
                                                    ctx.fillStyle = '#6C7782';
                                                    this.data.datasets.forEach(function (dataset, i) {
                                                        var meta = chart.getDatasetMeta(i);
                                                        meta.data.forEach(function (bar, index) {
                                                            var data = dataset.data[index];
                                                            data = isNaN(data) ? '' : data;
                                                            var yIndex = bar.y - 5;
                                                            if (data && data < 0) {
                                                                yIndex = bar.y + 15;
                                                            }
                                                            ctx.fillText(data, bar.x, yIndex);
                                                        });
                                                    });
                                                }
                                            }
                                        }
                                    };

                                    window.myLine = new Chart(ctx, options);
}) ();
                                </script>
                            </#if>
                            <#if obj.noOfChewsChart??>
                                <div class="col-6 px-4">
                                    <div class="card mb-1">
                                        <div class="card-header">
                                            <h4 class="text-center">${localization.getMessage("Report.No.OfChews", [],
                                                "No. of Chews", locale)}</h4>
                                        </div>
                                        <div class="card-body">
                                            <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                                <canvas id="${linechart2}"></canvas>
                                        </div>
                                        <div class="card-footer  pt-0">
                                            <div class="row">
                                                <div class="legend-wrap mb-1">
                                                    <p>${localization.getMessage("StandardDeviationScoreTitle", [],
                                                        "Standard Deviation", locale)}:
                                                        ${obj.noOfChewsChart.standardDeviation!0.0}</p>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <script>
                                    (function () {
                                        const colors = {
                                            purple: {
                                                default: "#1baca7",
                                                half: "#1baca778",
                                                quarter: "#1baca73b",
                                                zero: "#1baca71c"
                                            },
                                            indigo: {
                                                default: "#1baca7",
                                                quarter: "#1baca778"
                                            }
                                        };


                                        const yAxis = [
                                            <#list obj.noOfChewsChart.chewingPercentages as chewingPercentage >
                                            ${(chewingPercentage.noOfChews)!'NaN'} <#sep>, </#sep>
    </#list >
];

                                    const xAxis = [
                                        <#list obj.noOfChewsChart.chewingPercentages as chewingPercentage >
                                        "${(chewingPercentage.visitDate)!}" <#sep>, </#sep>
    </#list >
];

                                    const ctx = document.getElementById("${linechart2}").getContext("2d");
                                    ctx.canvas.height = 100;

                                    gradient = ctx.createLinearGradient(0, 25, 0, 300);
                                    gradient.addColorStop(0, colors.purple.half);
                                    gradient.addColorStop(0.35, colors.purple.quarter);
                                    gradient.addColorStop(1, colors.purple.zero);

                                    const options = {
                                        type: "line",
                                        data: {
                                            labels: xAxis,
                                            datasets: [
                                                {
                                                    fill: true,
                                                    backgroundColor: gradient,
                                                    borderColor: '#1BACA7',
                                                    pointBackgroundColor: '#1BACA7',
                                                    pointBorderColor: '#fff',
                                                    data: yAxis,
                                                    lineTension: 0.2,
                                                    borderWidth: 1,
                                                    pointRadius: 6,
                                                }
                                            ]
                                        },
                                        options: {
                                            plugins: {
                                                legend: {
                                                    display: false,
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        title: () => null // or function () { return null; }
                                                    },
                                                    yAlign: 'bottom',
                                                    backgroundColor: "#fff",
                                                    borderColor: "rgba(0, 0, 0, 0.25)",
                                                    borderWidth: 1,
                                                    displayColors: false,
                                                    bodyColor: "#1BACA7",
                                                    bodyAlign: "center",
                                                },
                                            },

                                            layout: {
                                                padding: {
                                                    top: 20,
                                                    right: 15
                                                }
                                            },

                                            responsive: true,

                                            scales: {
                                                y: {
                                                    // beginAtZero: true,
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("Report.No.OfChews", [], "No. of Chews", locale)}',
                                                        padding: {
                                                            bottom: 15,
                                                        }
                                                    },

                                                    grid: {
                                                        display: false,
                                                    },
                                                },

                                                x: {
                                                    grid: {
                                                        display: false,
                                                    },
                                                    color: '#6C7782',
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                        padding: {
                                                            bottom: 15,
                                                        }
                                                    }
                                                }
                                            },
                                            animation: {
                                                duration: 0,
                                                onComplete: function () {
                                                    var chart = this;
                                                    var ctx = chart.ctx;
                                                    ctx.textAlign = 'center';
                                                    ctx.textBaseline = 'bottom';
                                                    ctx.fillStyle = '#6C7782';
                                                    this.data.datasets.forEach(function (dataset, i) {
                                                        var meta = chart.getDatasetMeta(i);
                                                        meta.data.forEach(function (bar, index) {
                                                            var data = dataset.data[index];
                                                            data = isNaN(data) ? '' : data;
                                                            var yIndex = bar.y - 5;
                                                            if (data && data < 0) {
                                                                yIndex = bar.y + 15;
                                                            }
                                                            ctx.fillText(data, bar.x, yIndex);
                                                        });
                                                    });
                                                }
                                            }
                                        }
                                    };


                                    window.myLine = new Chart(ctx, options);
}) ();
                                </script>
                            </#if>
                        </div>
                        <#assign counter=counter+1>
                            <#if counter%4==0>
                                <div class="break-page"></div>
                            </#if>

                    </#list>
                </#if>
            </div>
        </div>
</#if>
<#if model.rumenHealthCudChewingTool.herdAnalysis ??>
	<#if model.rumenHealthCudChewingTool.penAnalysis ??>
		<div class="break-page"></div>
	</#if>
    <div class="container mid-body  mb-0">
        <div class="pt-0">

            <h3 class="title-secondary mb-1">${localization.getMessage("VisitSummaryViewModel.HerdAnalysis", [], "Herd
                Analysis", locale)}</h3>

            <#if model.rumenHealthCudChewingTool.herdAnalysis.graphs.cudChewingPercentage?? ||
                model.rumenHealthCudChewingTool.herdAnalysis.graphs.goalCudChewingPercentage ??>
                <div class="row mx-neg-4">
                    <#if model.rumenHealthCudChewingTool.herdAnalysis.graphs.cudChewingPercentage??>
                        <div class="col-6 px-4 d-flex">
                            <div class="card flex-grow-1 is-relative pb-4">
                                <div class="card-header">
                                    <h4 class="text-center">${localization.getMessage("Report.CudChewingPercentage", [],
                                        "Cud Chewing %", locale)}</h4>
                                </div>

                                <div class="card-body">
                                    <#assign barchart=statics["java.util.UUID"].randomUUID()>
                                        <canvas id="${barchart}"></canvas>

                                </div>

                                <div class="card-footer  pt-0 stick-bottom">
                                    <div class="row">
                                        <div class="legend-wrap mb-1">
                                            <p class="blue-solid">
                                                ${localization.getMessage("Report.Herd.Analysis.CudChewingPercentage",
                                                [], "Cud Chewing %", locale)}</p>
                                        </div>
                                        <div class="legend-wrap mb-1">
                                            <p class="blue-light-solid">
                                                ${localization.getMessage("Report.GoalCudChewingPercentage", [], "Goal
                                                Cud Chewing %", locale)}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <script>

                            (function () {
                                const cudChewingPercentage = [
                                    <#list model.rumenHealthCudChewingTool.herdAnalysis.graphs.lactationStages as lacStage >
                                    ${(model.rumenHealthCudChewingTool.herdAnalysis.graphs.cudChewingPercentage[lacStage])!'NaN'} <#sep>, </#sep>
</#list >
];

                            const goalCudChewingPercentage = [
                                <#list model.rumenHealthCudChewingTool.herdAnalysis.graphs.lactationStages as lacStage >
                                ${(model.rumenHealthCudChewingTool.herdAnalysis.graphs.goalCudChewingPercentage[lacStage])!'NaN'}<#sep>, </#sep>
</#list >
];

                            const xAxis = [
                                <#list model.rumenHealthCudChewingTool.herdAnalysis.graphs.lactationStages as lacStage >
                                '${localization.getMessage(lacStage, [], lacStage, locale)}' <#sep>, </#sep>
</#list >
];

                            const ctx = document.getElementById("${barchart}").getContext("2d");
                            ctx.canvas.height = 100;

                            const options = {
                                type: "bar",

                                data: {
                                    labels: xAxis,
                                    datasets: [
                                        {
                                            data: cudChewingPercentage,
                                            backgroundColor: '#307698',
                                            barThickness: 40,
                                            grouped: true,
                                        },
                                        {
                                            data: goalCudChewingPercentage,
                                            backgroundColor: '#61ADDE',
                                            barThickness: 40,
                                            grouped: true,
                                        }
                                    ]
                                },
                                options: {
                                    plugins: {
                                        legend: {
                                            display: false,
                                        },
                                        tooltip: {
                                            callbacks: {
                                                title: () => null // or function () { return null; }
                                            },
                                            yAlign: 'top',
                                            backgroundColor: "#fff",
                                            borderColor: "rgba(0, 0, 0, 0.25)",
                                            borderWidth: 1,
                                            displayColors: false,
                                            bodyColor: "#307698",
                                            bodyAlign: "center",
                                        },

                                    },

                                    layout: {
                                        padding: {
                                            top: 20,
                                            right: 15
                                        }
                                    },

                                    responsive: true,

                                    scales: {
                                        y: {
                                            // beginAtZero: true,
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("Report.Herd.Analysis.CudChewingPercentage", [], "Cud Chewing %", locale)}',
                                                padding: {
                                                    bottom: 15,
                                                }
                                            },

                                            grid: {
                                                display: false,
                                            },
                                        },

                                        x: {
                                            title: {
                                                display: true,
                                                color: '#6C7782',
                                                text: '${localization.getMessage("Report.BCS.LactationStages", [], "Lactation Stages", locale)}',
                                                padding: {
                                                    top: 15,
                                                }
                                            },
                                            grid: {
                                                display: false,
                                            },
                                        }
                                    },

                                    animation: {
                                        duration: 0,
                                        onComplete: function () {
                                            var chart = this;
                                            var ctx = chart.ctx;

                                            ctx.font = Chart.helpers.fontString(Chart.defaults.font.size, Chart.defaults.font.style, Chart.defaults.font.family);
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'bottom';
                                            ctx.fillStyle = '#6C7782';
                                            this.data.datasets.forEach(function (dataset, i) {
                                                var meta = chart.getDatasetMeta(i);
                                                meta.data.forEach(function (bar, index) {
                                                    var data = dataset.data[index];
                                                    data = isNaN(data) ? '' : data;
                                                    var yIndex = bar.y - 5;
                                                    if (data && data < 0) {
                                                        yIndex = bar.y + 15;
                                                    }
                                                    ctx.fillText(data, bar.x, yIndex);
                                                });
                                            });
                                        }
                                    }
                                }
                            };

                            window.myLine = new Chart(ctx, options);
}) ();
                        </script>
                    </#if>
                    <#if model.rumenHealthCudChewingTool.herdAnalysis.graphs.goalCudChewingPercentage ??>
                        <#if model.rumenHealthCudChewingTool.herdAnalysis.graphs.chewsPerRegurgitation?? ||
                            model.rumenHealthCudChewingTool.herdAnalysis.graphs.goalChews ??>

                            <div class="col-6 px-4 d-flex">
                                <div class="card flex-grow-1 is-relative pb-4">
                                    <div class="card-header">
                                        <h4 class="text-center">
                                            ${localization.getMessage("EmailReportViewModel.NumOfChews", [], "Number Of
                                            Chews", locale)}</h4>
                                    </div>

                                    <div class="card-body">
                                        <#assign barchart1=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${barchart1}"></canvas>
                                    </div>

                                    <div class="card-footer  pt-0 stick-bottom">
                                        <div class="row">
                                            <div class="legend-wrap mb-1">
                                                <p class="blue-solid">
                                                    ${localization.getMessage("Report.No.OfChewsPerRegurgitation", [],
                                                    "No. of Chews per Regurgitation", locale)}</p>
                                            </div>
                                            <div class="legend-wrap mb-1">
                                                <p class="red-strip">${localization.getMessage("Report.goalChews", [],
                                                    "Goal Chews", locale)}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <script>

                                (function () {
                                    const colors = {
                                        purple: {
                                            default: "#307698",
                                            half: "#30769878",
                                            quarter: "#3076983b",
                                            zero: "#3076981c"
                                        },
                                        indigo: {
                                            default: "#307698",
                                            quarter: "#30769878"
                                        }
                                    };

                                    const chewsPerRegurgitation = [
                                        <#list model.rumenHealthCudChewingTool.herdAnalysis.graphs.lactationStages as lacStage >
                                        ${(model.rumenHealthCudChewingTool.herdAnalysis.graphs.chewsPerRegurgitation[lacStage])!'NaN'} <#sep>, </#sep>
</#list >
];

                                const goalChews = [
                                    <#list model.rumenHealthCudChewingTool.herdAnalysis.graphs.lactationStages as lacStage >
                                    ${(model.rumenHealthCudChewingTool.herdAnalysis.graphs.goalChews[lacStage])!'NaN'}<#sep>, </#sep>
</#list >
];

                                const xAxis = [
                                    <#list model.rumenHealthCudChewingTool.herdAnalysis.graphs.lactationStages as lacStage >
                                    '${localization.getMessage(lacStage, [], lacStage, locale)}' <#sep>, </#sep>
</#list >
];

                                const ctx = document.getElementById("${barchart1}").getContext("2d");
                                ctx.canvas.height = 100;

                                gradient = ctx.createLinearGradient(0, 25, 0, 300);
                                gradient.addColorStop(0, colors.purple.half);
                                gradient.addColorStop(0.35, colors.purple.quarter);
                                gradient.addColorStop(1, colors.purple.zero);

                                const options = {
                                    type: "line",

                                    data: {
                                        labels: xAxis,
                                        datasets: [
                                            {
                                                borderColor: '#8D0909',
                                                pointBackgroundColor: '#8D0909',
                                                pointBorderColor: '#fff',
                                                data: goalChews,
                                                lineTension: 0.2,
                                                borderWidth: 1,
                                                pointRadius: 6,
                                                borderDash: [5, 4],
                                            },
                                            {
                                                fill: true,
                                                backgroundColor: gradient,
                                                borderColor: '#307698',
                                                pointBackgroundColor: '#307698',
                                                pointBorderColor: '#fff',
                                                data: chewsPerRegurgitation,
                                                lineTension: 0.2,
                                                borderWidth: 1,
                                                pointRadius: 6,
                                            }
                                        ]
                                    },
                                    options: {
                                        plugins: {
                                            legend: {
                                                display: false,
                                            },
                                            tooltip: {
                                                callbacks: {
                                                    title: () => null // or function () { return null; }
                                                },
                                                yAlign: 'bottom',
                                                backgroundColor: "#fff",
                                                borderColor: "rgba(0, 0, 0, 0.25)",
                                                borderWidth: 1,
                                                displayColors: false,
                                                bodyColor: "#307698",
                                                bodyAlign: "center",
                                            },
                                        },

                                        layout: {
                                            padding: {
                                                top: 20,
                                                right: 15
                                            }
                                        },

                                        responsive: true,

                                        scales: {
                                            y: {
                                                // beginAtZero: true,
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${localization.getMessage("Report.No.OfChews", [], "No. of Chews", locale)}',
                                                    padding: {
                                                        bottom: 15,
                                                    }
                                                },

                                                grid: {
                                                    display: false,
                                                },
                                            },

                                            x: {
                                                title: {
                                                    display: true,
                                                    color: '#6C7782',
                                                    text: '${localization.getMessage("Report.BCS.LactationStages", [], "Lactation Stages", locale)}',
                                                    padding: {
                                                        top: 15,
                                                    }
                                                },
                                                grid: {
                                                    display: false,
                                                },
                                            }
                                        },
                                        animation: {
                                            duration: 0,
                                            onComplete: function () {
                                                var chart = this;
                                                var ctx = chart.ctx;
                                                ctx.textAlign = 'center';
                                                ctx.textBaseline = 'bottom';
                                                ctx.fillStyle = '#6C7782';
                                                this.data.datasets.forEach(function (dataset, i) {
                                                    var meta = chart.getDatasetMeta(i);
                                                    meta.data.forEach(function (bar, index) {
                                                        var data = dataset.data[index];
                                                        data = isNaN(data) ? '' : data;
                                                        var yIndex = bar.y - 5;
                                                        if (data && data < 0) {
                                                            yIndex = bar.y + 15;
                                                        }
                                                        ctx.fillText(data, bar.x, yIndex);
                                                    });
                                                });
                                            }
                                        }
                                    }
                                };

                                window.myLine = new Chart(ctx, options);
}) ();
                            </script>
                        </#if>
                    </#if>
                </div>

            </#if>


        </div>
    </div>
    <#if model.rumenHealthCudChewingTool?? && model.rumenHealthCudChewingTool.herdAnalysis?? &&
        model.rumenHealthCudChewingTool.herdAnalysis.scoreAnalysis[0] ??>
        <div class="container">
            <div class="pt-1">
                <h3 class="title-secondary mb-1">${localization.getMessage("HerdAnalysisTableTitle", [], "Score
                    Analysis", locale)}</h3>
                <div class="row">
                    <div class="col-12 table-secondary">
                        <table>
                            <#if model.rumenHealthCudChewingTool?? && model.rumenHealthCudChewingTool.herdAnalysis?? &&
                                model.rumenHealthCudChewingTool.herdAnalysis.scoreAnalysis[0] ??>
                                <tr>
                                    <#list model.rumenHealthCudChewingTool.herdAnalysis.scoreAnalysis[0] as headings>
                                        <th>${headings.column!}</th>
                                    </#list>
                                </tr>
                            </#if>

                            <#if model.rumenHealthCudChewingTool?? && model.rumenHealthCudChewingTool.herdAnalysis?? &&
                                model.rumenHealthCudChewingTool.herdAnalysis.scoreAnalysis[0] ??>
                                <#list model.rumenHealthCudChewingTool.herdAnalysis.scoreAnalysis as innerlist>
                                    <tr>
                                        <#list innerlist as obj>
                                            <td>${obj.value!}</td>
                                        </#list>
                                    </tr>
                                </#list>
                            </#if>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </#if>
</#if>
<#if model.rumenHealthCudChewingTool?? && model.rumenHealthCudChewingTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.rumenHealthCudChewingTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>