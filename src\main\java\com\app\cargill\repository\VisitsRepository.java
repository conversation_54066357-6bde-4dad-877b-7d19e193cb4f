/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.dto.ISelectDto;
import com.app.cargill.model.Visits;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface VisitsRepository
    extends JpaRepository<Visits, Long>, JpaSpecificationExecutor<Visits> {

  @Query(
      value =
          "select v.* from Visits v where v.visit_document->>'id' in(    select"
              + " unnest((array(select"
              + " jsonb_array_elements(site_document->'Visits')->>'LabyrinthVisitId'))[0\\:7]) from"
              + " sites    where site_document->>'AccountId' in (    SELECT    a.account_document"
              + " ->> 'id' FROM accounts a where CAST(a.account_document ->          'Users' as"
              + " jsonb)  @> CAST(:currentLoggedUser as jsonb)    ) order by"
              + " jsonb_array_elements(site_document->'Visits')->>'VisitDate' desc )AND"
              + " timezone('UTC',v.updated_date) > :lastSyncTime order by"
              + " v.visit_document->>'VisitDate'",
      nativeQuery = true)
  Page<Visits> findByCustomerIdAndUpdatedDateAndAccountsUsers(
      @Param("currentLoggedUser") String currentLoggedUser,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value =
          "Select v.* from Visits v where v.visit_document->>'id' in(:visitIds) AND"
              + " timezone('UTC',v.updated_date) > :lastSyncTime AND v.deleted=false Order by"
              + " v.visit_document->>'VisitDate' desc",
      nativeQuery = true)
  Page<Visits> findByVisitIdsOfCurrentLoggedInUser(
      @Param("visitIds") List<String> visitIds,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value =
          "SELECT v.* FROM Visits v WHERE v.visit_document ->> 'id' = :visitId AND v.deleted=false",
      nativeQuery = true)
  Visits findByVisitId(String visitId);

  boolean existsByLocalId(String localId);

  @Query(
      value =
          "SELECT v.* FROM Visits v WHERE v.visit_document ->> 'RumenHealthManureScore' is NOT NULL"
              + " AND v.deleted=false",
      nativeQuery = true)
  List<Visits> findByRumenHealthManureScore();

  @Query(
      value =
          "Select v.visit_document->>'id' from Visits v where v.visit_document->>'SiteId'"
              + " in(:siteIds) AND v.deleted=false",
      nativeQuery = true)
  List<String> findVisitIdsBySiteIds(@Param("siteIds") List<String> siteIds);

  @Query(
      value =
          "Select v.visit_document->>'id' from Visits v where v.visit_document->>'CustomerId'"
              + " in(:accountId) AND v.deleted=false",
      nativeQuery = true)
  List<String> findVisitIdsByAccountIds(@Param("accountId") List<String> accountIds);

  @Query(
      value =
          "SELECT v.visit_document->>'id' AS id,v.visit_document->>'VisitName' AS name FROM Visits"
              + " v WHERE v.visit_document->>'CustomerId' IN(:accountIds) AND"
              + " timezone('UTC',v.updated_date) > :lastSyncTime AND v.deleted=false ",
      nativeQuery = true)
  Page<ISelectDto> findByAccountIds(
      @Param("accountIds") List<String> accountIds,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value =
          "SELECT v.* FROM Visits v WHERE v.visit_document->>'CustomerId' IN(:accountIds) AND"
              + " v.deleted=false ",
      nativeQuery = true)
  List<Visits> findAllByAccountIds(@Param("accountIds") List<String> accountIds);

  @Query(
      value =
          "SELECT v.* FROM visits as v INNER JOIN accounts as a ON"
              + " a.account_document->>'id'=v.visit_document->>'CustomerId' where"
              + " (v.visit_document->>'LastModifiedTimeUtc' > :dateFrom AND"
              + " v.visit_document->>'LastModifiedTimeUtc' <= :dateTo) AND"
              + " v.visit_document->>'Status'='Published' ORDER BY v.visit_document ->> 'id'",
      nativeQuery = true)
  Page<Visits> findAllVisitsByFromAndToDate(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo, Pageable pageable);

  @Query(
      value =
          "SELECT * FROM visits where visit_document->>'CustomerId' = :customerId and"
              + " visits.deleted = false ORDER BY TO_TIMESTAMP(visit_document->>'VisitDate',"
              + " 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') desc LIMIT 1",
      nativeQuery = true)
  Visits findLatestVisitByCustomerId(String customerId);

  @Query(
      value =
          "select * from visits where visit_document->>'SiteId' = :siteId order by"
              + " visit_document->>'VisitDate' asc",
      nativeQuery = true)
  List<Visits> findVisitsBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "select * from visits where visit_document->>'MobileLastUpdatedTime' is null and deleted"
              + " = false",
      nativeQuery = true)
  List<Visits> findVisitsByLastMobileUpdatedDate();

  @Query(
      value =
          "SELECT * FROM visits where "
              + " visits.deleted = false ORDER BY TO_TIMESTAMP(visit_document->>'VisitDate',"
              + " 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') desc LIMIT 20",
      nativeQuery = true)
  List<Visits> findLatestVisits();

  @Query(
      value =
          "Select * From visits where visit_document->>'TMRParticleScore' is not null and deleted ="
              + " false",
      nativeQuery = true)
  List<Visits> findVisitsWithTmrPennState();

  @Query(
      value =
          "select * from visits where visit_document->>'SiteId' = :siteId order by"
              + " visit_document->>'VisitDate' asc",
      nativeQuery = true)
  List<Visits> getVisitsDocBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "SELECT v.* FROM visits as v INNER JOIN accounts as a ON"
              + " a.account_document->>'id'=v.visit_document->>'CustomerId' where"
              + " (v.visit_document->>'LastModifiedTimeUtc' > :dateFrom AND"
              + " v.visit_document->>'LastModifiedTimeUtc' <= :dateTo) AND"
              + " v.visit_document->>'Status'='Published' ORDER BY v.visit_document ->> 'id'",
      nativeQuery = true)
  List<Visits> findAllVisitsByFromAndToDateV1(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo);

  @Query(
      value =
          "SELECT  v.* FROM visits v where"
              + " (v.visit_document ->> 'VisitDate' > '2023-05-24T05:12:28.406Z' AND "
              + "  v.visit_document ->> 'VisitDate' <= '2024-05-24T05:12:28.406Z') AND"
              + "  v.visit_document->>'LastModifiedTimeUtc' < '2024-05-24T05:12:28.406Z'"
              + "  ORDER BY v.visit_document ->> 'VisitDate'  desc limit 500",
      nativeQuery = true)
  List<Visits> findAllVisitsForOneYear();

  @Query(
      value =
          "select * from visits where LOWER(visit_document->>'CreateUser') = LOWER(:email) "
              + "order by visit_document->>'CreateTimeUtc' ASC LIMIT 1",
      nativeQuery = true)
  Visits findFirstVisitByEmail(String email);

  @Query(
      value =
          "SELECT v.* FROM visits as v INNER JOIN accounts as a ON"
              + " a.account_document->>'id'=v.visit_document->>'CustomerId' where"
              + " (v.visit_document->>'LastModifiedTimeUtc' > :dateFrom AND"
              + " v.visit_document->>'LastModifiedTimeUtc' <= :dateTo) AND"
              + " v.visit_document->>'Status'='Published' "
              + "AND visit_document->>'ProfitabilityAnalysis' is not null"
              + " ORDER BY v.visit_document ->> 'id'",
      nativeQuery = true)
  List<Visits> findAllProtabilityDataByFromAndToDateV1(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo);

  @Query(
      value = "select * from visits where " + " visit_document ->> 'AnimalAnalysis' is not null",
      nativeQuery = true)
  List<Visits> getAnimalAnalysisDetails();

  @Query(
      value =
          "select * from visits where  visit_document ->> 'LocomotionScore' is not null AND"
              + " visit_document->>'Status' in ('Published') AND deleted = false",
      nativeQuery = true)
  List<Visits> findAllLocomotionScorePublishedVisits();

  @Query(
      value =
          "select * from visits where  visit_document ->> 'AnimalAnalysis' is not null AND"
              + " visit_document->>'Status' in ('Published') AND deleted = false",
      nativeQuery = true)
  List<Visits> findAllAnimalAnalysisPublishedVisits();

  @Query(
      value =
          "select * from visits where visit_document->>'VisitName' ilike '%/%' AND deleted = false",
      nativeQuery = true)
  List<Visits> findAllVisitsByVisitNameWithBackSlashes();

  @Query(
      value =
          "select * from visits where visit_document->>'BodyCondition' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getBodyConditionForVisits();

  @Query(
      value =
          "select * from visits where visit_document->>'TMRParticleScore' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getTmrParticleScoreForVisits();

  @Query(
      value =
          "select * from visits where visit_document->>'RumenHealthManureScore' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getRumenHealthManureScoreForVisits();

  @Query(
      value =
          "select * from visits where visit_document->>'RumenFillManureScore' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getRumenFillManureScoreForVisits();

  @Query(
      value =
          "select * from visits where visit_document->>'CudChewing' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getCudChewingForVisits();

  @Query(
      value =
          "select * from visits where visit_document->>'ManureScreenerTool' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getManureScreenerTool();

  @Query(
      value =
          "select * from visits where visit_document->>'PenTimeBudgetTool' "
              + " is not null AND visit_document->>'Status' = 'Published' AND deleted = false ",
      nativeQuery = true)
  List<Visits> getPenTimeBudgetTool();
}
