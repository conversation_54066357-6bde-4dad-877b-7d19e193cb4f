/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CityDocument {

  private String id;
  private String name;

  @JsonProperty("state_id")
  private String stateId;

  @JsonProperty("state_code")
  private String stateCode;

  @JsonProperty("state_name")
  private String stateName;

  @JsonProperty("country_id")
  private String countryId;

  @JsonProperty("country_code")
  private String countryCode;

  @JsonProperty("country_name")
  private String countryName;

  private String latitude;
  private String longitude;
  private String wikiDataId;
}
