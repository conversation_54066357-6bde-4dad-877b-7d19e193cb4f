/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportLocomotionPenAnalysisDto {
  private String penName;
  private Double standardDeviation;
  private Double average;
  private List<List<VisitReportColumnValueDto>> penDetails;
  private List<LocomotionScorePenAnalysisCategoryDto> categoriesChart = new ArrayList<>();
}
