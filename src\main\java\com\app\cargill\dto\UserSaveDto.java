/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.MobileDeviceType;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserSaveDto extends BaseDto {

  private UUID id;

  private String userName;

  private Business countryId;

  private Integer salesforceCountryId;

  private String applicationVersion;

  private MobileDeviceType deviceType; // ios android

  private String deviceModel;

  private String deviceId;
}
