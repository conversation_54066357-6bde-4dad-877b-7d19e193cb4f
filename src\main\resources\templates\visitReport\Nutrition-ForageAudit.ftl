<div class="container">
		<div class="legend-head">
			<div class="count">${toolNumber!}</div>
			<div class="main-title">
				<span class="sm-head">${localization.getMessage("VisitViewModel.NutritionItem", [], "Nutrition",locale)}</span>
				<span class="lg-head">${localization.getMessage("NutritionViewModel.NutritionForage", [], "Forage audit",locale)}</span>
			</div>
			<div style="font-size: 1;color: white;">0000121212FA</div>
		</div>
	</div>
<#if model.forageAuditTool.forageManagement ?? || model.forageAuditTool.forageQualityInRation ?? || model.forageAuditTool.bunkersAndPiles ?? || model.forageAuditTool.towerSilos ?? || model.forageAuditTool.silageBags ?? || model.forageAuditTool.baleage ??>
<div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-secondary mb-1">
					<span>${localization.getMessage("ViewOverallForageScore", [], "View overall forage score",locale)}</span>
				</h3>
            </div>
        </div>
</div>
    <div class="container">

    
        <div class="row mx-neg-4">
		<#if model.forageAuditTool.forageManagement ??>
            <div class="col-4-equal px-4">
                <h3 class="title-primary mb-1">
					<span>${localization.getMessage("SurveyOfForages", [], "Forage management",locale)}</span>
				</h3>
				<#list model.forageAuditTool.forageManagement as row>
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
					<#assign donutchart=statics["java.util.UUID"].randomUUID()>
                        <canvas id="${donutchart!}"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">${row.label!}</h6>
                </div>
			<script>
                            (function () {
        var centerText = {
        id: 'centerText-${donutchart!}',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '${row.hexColor!}';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX+1, textY+2);
				ctx.font =   "bold  10px Helvetica";
                ctx.save();
            }
        };

		const ctx = document.getElementById("${donutchart!}").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['${row.hexColor!}', '#E3E7EB'],
                        borderWidth: 0,
                        percent: ${row.value!0.0},
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 1,
						right: 1,
						top: 1,
						bottom: 1
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '80%',
				animation: {
					duration: 0
				}
			},

            plugins: [centerText]              
		};
 window.myLine = new Chart(ctx, options);
}) ();
                        </script>
	
				</#list>
            </div>
			</#if>
			<#if model.forageAuditTool.forageQualityInRation ??>
            <div class="col-4-equal px-4">
                <h3 class="title-primary mb-1">
					<span>${localization.getMessage("Harvest", [], "Forage quality in the ration",locale)}</span>
				</h3>
                <#list model.forageAuditTool.forageQualityInRation as row>
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
					<#assign donutchart=statics["java.util.UUID"].randomUUID()>
                        <canvas id="${donutchart!}"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">${row.label!}</h6>
                </div>
				
			<script>
                            (function () {
        var centerText = {
        id: 'centerText-${donutchart!}',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '${row.hexColor!}';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX+1, textY+2);
				ctx.font =   "bold  10px Helvetica";
                ctx.save();
            }
        };

		const ctx = document.getElementById("${donutchart!}").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['${row.hexColor!}', '#E3E7EB'],
                        borderWidth: 0,
                        percent: ${row.value!0.0},
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 1,
						right: 1,
						top: 1,
						bottom: 1
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '80%',
				animation: {
					duration: 0
				}
			},

            plugins: [centerText]              
		};
 window.myLine = new Chart(ctx, options);
}) ();
                        </script>
	
				</#list>
            </div>
</#if>
			<#if model.forageAuditTool.bunkersAndPiles ??>
            <div class="col-4-equal px-4">
                <h3 class="title-primary mb-1">
					<span>${localization.getMessage("BunkersAndPiles", [], "Bunkers and piles",locale)}</span>
				</h3>
                <#list model.forageAuditTool.bunkersAndPiles as row>
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
					<#assign donutchart=statics["java.util.UUID"].randomUUID()>
                        <canvas id="${donutchart!}"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">${row.label!}</h6>
                </div>
				
			<script>
                            (function () {
        var centerText = {
        id: 'centerText-${donutchart!}',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '${row.hexColor!}';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX+1, textY+2);
				ctx.font =   "bold  10px Helvetica";
                ctx.save();
            }
        };

		const ctx = document.getElementById("${donutchart!}").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['${row.hexColor!}', '#E3E7EB'],
                        borderWidth: 0,
                        percent: ${row.value!0.0},
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 1,
						right: 1,
						top: 1,
						bottom: 1
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '80%',
				animation: {
					duration: 0
				}
			},

            plugins: [centerText]              
		};
 window.myLine = new Chart(ctx, options);
}) ();
                        </script>
	
				</#list>
            </div>
</#if>
			<#if model.forageAuditTool.towerSilos ??>
<div class="col-4-equal px-4">
                <h3 class="title-primary mb-1">
					<span>${localization.getMessage("TowerSilos", [], "Tower silos",locale)}</span>
				</h3>
                <#list model.forageAuditTool.towerSilos as row>
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
					<#assign donutchart=statics["java.util.UUID"].randomUUID()>
                        <canvas id="${donutchart!}"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">${row.label!}</h6>
                </div>
				
			<script>
                            (function () {
        var centerText = {
        id: 'centerText-${donutchart!}',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '${row.hexColor!}';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX+1, textY+2);
				ctx.font =   "bold  10px Helvetica";
                ctx.save();
            }
        };

		const ctx = document.getElementById("${donutchart!}").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['${row.hexColor!}', '#E3E7EB'],
                        borderWidth: 0,
                        percent: ${row.value!0.0},
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 1,
						right: 1,
						top: 1,
						bottom: 1
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '80%',
				animation: {
					duration: 0
				}
			},

            plugins: [centerText]              
		};
 window.myLine = new Chart(ctx, options);
}) ();
                        </script>
	
				</#list>
            </div>
</#if>
			<#if model.forageAuditTool.silageBags ??>
            <div class="col-4-equal px-4">
                <h3 class="title-primary mb-1">
					<span>${localization.getMessage("SilageBags", [], "Silage bags",locale)}</span>
				</h3>
                <#list model.forageAuditTool.silageBags as row>
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
					<#assign donutchart=statics["java.util.UUID"].randomUUID()>
                        <canvas id="${donutchart!}"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">${row.label!}</h6>
                </div>
				
			<script>
                            (function () {
        var centerText = {
        id: 'centerText-${donutchart!}',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '${row.hexColor!}';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX+1, textY+2);
				ctx.font =   "bold  10px Helvetica";
                ctx.save();
            }
        };

		const ctx = document.getElementById("${donutchart!}").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['${row.hexColor!}', '#E3E7EB'],
                        borderWidth: 0,
                        percent: ${row.value!0.0},
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 1,
						right: 1,
						top: 1,
						bottom: 1
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '80%',
				animation: {
					duration: 0
				}
			},

            plugins: [centerText]              
		};
 window.myLine = new Chart(ctx, options);
}) ();
                        </script>
	
				</#list>
            </div>
</#if>
			<#if model.forageAuditTool.baleage ??>
            <div class="col-4-equal px-4">
                <h3 class="title-primary mb-1">
					<span>${localization.getMessage("Baleage", [], "Baleage",locale)}</span>
				</h3>
                <#list model.forageAuditTool.baleage as row>
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
					<#assign donutchart=statics["java.util.UUID"].randomUUID()>
                        <canvas id="${donutchart!}"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">${row.label!}</h6>
                </div>
				
			<script>
                            (function () {
        var centerText = {
        id: 'centerText-${donutchart!}',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '${row.hexColor!}';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX+1, textY+2);
				ctx.font =   "bold  10px Helvetica";
                ctx.save();
            }
        };

		const ctx = document.getElementById("${donutchart!}").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['${row.hexColor!}', '#E3E7EB'],
                        borderWidth: 0,
                        percent: ${row.value!0.0},
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 1,
						right: 1,
						top: 1,
						bottom: 1
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '80%',
				animation: {
					duration: 0
				}
			},

            plugins: [centerText]              
		};
 window.myLine = new Chart(ctx, options);
}) ();
                        </script>
	
				</#list>
            </div>
			</#if>
        </div>
    </div>
</#if>
<#if model.forageAuditTool.questionAnswers ?? >
<#if model.forageAuditTool.forageManagement ?? || model.forageAuditTool.forageQualityInRation ?? || model.forageAuditTool.bunkersAndPiles ?? || model.forageAuditTool.towerSilos ?? || model.forageAuditTool.silageBags ?? || model.forageAuditTool.baleage ??>

	<div class="break-page"></div>
</#if>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-primary">
					<span>${localization.getMessage("Answers", [], "Answers",locale)}</span>
				</h3>
            </div>
            <div class="col-12">
			<#list model.forageAuditTool.questionAnswers?keys as key>
                <h6 class="title-plain">${key!}</h6>
				<#list model.forageAuditTool.questionAnswers[key] as val>
                <p>${val.question!}</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>${val.answerLeft!}</span>
                    <span class="title-plain">${val.answerRight!}</span>
				</h3>
				</#list>
			</#list>
               

            </div>
        </div>
    </div>
</#if>
<#if model.forageAuditTool?? && model.forageAuditTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.forageAuditTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>
	