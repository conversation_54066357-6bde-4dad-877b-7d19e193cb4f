/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.NoteCategoryType;
import com.app.cargill.dto.*;
import com.app.cargill.service.*;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class NotesControllerTest {

  @Mock private INotesService notesServiceImpl;

  @Mock private IUserService userServiceImpl;

  @InjectMocks private NotesController controller;

  @Test
  void getAllNotes() {
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");
    when(userServiceImpl.getCurrentLoggedInUserAsJsonObj()).thenReturn("[\"test user\"]");

    ResponseEntity<ResponseEntityDto<Page<NotesDto>>> result =
        controller.getAllNotesPaginated(1, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
  }

  @Test
  void getAllNotesOnline() {
    ResponseEntity<ResponseEntityDto<Page<NotesDto>>> result =
        controller.getAllNotesPaginatedInOnlineMode(
            1,
            10,
            "id",
            "desc",
            NotesSearchDto.builder()
                .tools(List.of("CalfHeiferScorecard"))
                .accountIds(List.of(UUID.randomUUID().toString()))
                .creationDateFrom(Instant.now())
                .updatedDateFrom(Instant.now())
                .creationDateTo(Instant.now())
                .updatedDateTo(Instant.now())
                .noteCategoryTypes(List.of(NoteCategoryType.Event.name()))
                .siteIds(List.of(UUID.randomUUID().toString()))
                .visitIds(List.of(UUID.randomUUID().toString()))
                .accountIds(List.of(UUID.randomUUID().toString()))
                .title("test")
                .build());
    assertNotNull(result);
  }

  @Test
  void whenSaveReturnsSuccessResponse() {
    NotesDto input =
        NotesDto.builder()
            .note("test ")
            .localId(UUID.randomUUID().toString())
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    NotesDto output =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("test user");
    when(notesServiceImpl.save(input, userServiceImpl.getCurrentLoggedInUser())).thenReturn(output);
    ResponseEntity<ResponseEntityDto<NotesDto>> result = controller.save(input);
    assertNotNull(result.getBody().getData().getId());
  }

  @Test
  void whenUpdateReturnsSuccessResponse() {
    NotesDto input =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    NotesDto output =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("test user");
    when(notesServiceImpl.update(input, userServiceImpl.getCurrentLoggedInUser()))
        .thenReturn(output);
    ResponseEntity<ResponseEntityDto<NotesDto>> result = controller.update(input);
    assertNotNull(result.getBody().getData().getId());
  }

  @Test
  void whenUpsertReturnsSuccessResponse() {
    NotesDto createInput =
        NotesDto.builder()
            .localId(UUID.randomUUID().toString())
            .note("create test ")
            .title("create test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    NotesDto createOutput =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .note("create test ")
            .title("create test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    NotesDto updateInput =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    List<NotesDto> params = Collections.synchronizedList(new ArrayList<>());
    params.add(createOutput);
    params.add(updateInput);
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("test user");
    when(notesServiceImpl.updateOrInsert(
            List.of(createInput, updateInput), userServiceImpl.getCurrentLoggedInUser()))
        .thenReturn(params);
    ResponseEntity<ResponseEntityDto<List<NotesDto>>> result =
        controller.upsert(List.of(createInput, updateInput));
    assertNotNull(result.getBody().getData().get(0).getId());
  }
}
