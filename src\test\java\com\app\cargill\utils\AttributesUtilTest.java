/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.lang.reflect.Field;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;

public class AttributesUtilTest {

  // Test case for an acceptable class
  @Test
  public void getAttributes_AcceptableClass_ReturnsAttributesDTOList() {
    // Create an instance of the tool (replace ToolClass with the actual class name)
    ToolClass tool = new ToolClass();

    // Call the method under test
    List<AttributesDTO> attributes = AttributesUtil.getAttributes(tool, "path");

    // Assertions
    Assert.assertNotNull(attributes);
    Assert.assertFalse(attributes.isEmpty());
  }

  // Test case for an acceptable list
  @Test
  public void getAttributes_AcceptableList_ReturnsAttributesDTOList() {
    // Create an instance of the tool with a list field (replace Tool<PERSON>lass with the actual class
    // name)
    ToolClass tool = new ToolClass();
    tool.setListField(List.of("value1", "value2"));

    // Call the method under test
    List<AttributesDTO> attributes = AttributesUtil.getAttributes(tool, "path");

    // Assertions
    Assert.assertNotNull(attributes);
    Assert.assertFalse(attributes.isEmpty());
    // Add more assertions based on the expected attributes
  }

  // Test case for a null tool
  @Test
  public void getAttributes_NullTool_ReturnsEmptyList() {
    // Call the method under test with a null tool
    List<AttributesDTO> attributes = AttributesUtil.getAttributes(null, "path");

    // Assertions
    Assert.assertNotNull(attributes);
    Assert.assertTrue(attributes.isEmpty());
  }

  // Test case for a non-null tool
  @Test
  public void getAttributes_NonNullTool_ReturnsAttributesDTOList() {
    // Create an instance of the tool (replace ToolClass with the actual class name)
    ToolClass tool = new ToolClass();

    // Call the method under test
    List<AttributesDTO> attributes = AttributesUtil.getAttributes(tool, "path");

    // Assertions
    Assert.assertNotNull(attributes);
    Assert.assertFalse(attributes.isEmpty());
  }

  // Test case for a sample AttributeDTO
  @Test
  public void getAttributeForSinglePrimitiveType_SampleAttribute_ReturnsAttributeDTO()
      throws NoSuchFieldException {
    // Create an instance of the field (replace ToolClass with the actual class name and fieldName
    // with the actual field name)
    Field field = ToolClass.class.getDeclaredField("fieldName");

    // Call the method under test
    AttributesDTO attribute =
        AttributesUtil.getAttributeForSinglePrimitiveType(field, "name", "value");

    // Assertions
    Assert.assertNotNull(attribute);
    Assert.assertEquals(
        "fieldName",
        attribute.getName()); // It has to overwrite to fieldName as is has JsonProperty annotation
    Assert.assertEquals("ToolClass", attribute.getToolName());
    Assert.assertEquals("value", attribute.getTextValue());
    Assert.assertEquals("Text", attribute.getValueType());
    Assert.assertNull(attribute.getNumericValue());
    Assert.assertEquals("", attribute.getUnitOfMeasure());
  }

  // Example ToolClass for testing
  private static class ToolClass {
    @JsonProperty("fieldName")
    private String fieldName;

    public void setListField(List<String> listField) {
      // Set the list field
    }
  }
}
