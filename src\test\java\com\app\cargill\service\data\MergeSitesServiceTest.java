/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.data;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.app.cargill.document.DietDocument;
import com.app.cargill.document.NotesDocument;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Notes;
import com.app.cargill.model.Pens;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MergeSitesServiceTest {
  @Mock private PensRepository pensRepository;
  @Mock private VisitsRepository visitsRepository;
  @Mock private NotesRepository notesRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private DietRepository dietRepository;
  @Mock private SiteMappingsRepository mappingsRepository;
  @InjectMocks private MergeSitesService mergeSitesService;

  @Test
  void whenTransferInvokedAllPass() {
    UUID source = UUID.randomUUID();
    UUID target = UUID.randomUUID();

    PenDocument penDocument1 = new PenDocument();
    penDocument1.setId(UUID.randomUUID());
    PenDocument penDocument2 = new PenDocument();
    penDocument2.setId(UUID.randomUUID());

    VisitDocument visitDocument1 = new VisitDocument();
    visitDocument1.setId(UUID.randomUUID());
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setId(UUID.randomUUID());

    NotesDocument notesDocument1 = new NotesDocument();
    notesDocument1.setId(UUID.randomUUID());
    NotesDocument notesDocument2 = new NotesDocument();
    notesDocument2.setId(UUID.randomUUID());

    when(sitesRepository.findBySiteId(any())).thenReturn(mock(Sites.class));

    when(pensRepository.findBySiteId(source.toString()))
        .thenReturn(List.of(new Pens(penDocument1), new Pens(penDocument2)));
    when(visitsRepository.findVisitsBySiteId(source.toString()))
        .thenReturn(List.of(new Visits(visitDocument1), new Visits(visitDocument2)));
    when(notesRepository.findByNotesBySiteId(source.toString()))
        .thenReturn(List.of(new Notes(notesDocument1), new Notes(notesDocument2)));

    Map<String, List<String>> result = mergeSitesService.transferSitesData(source, target);
    assertNotNull(result);
    assertEquals(2, result.get("pens").size());
    assertEquals(2, result.get("visits").size());
    assertEquals(2, result.get("notes").size());
  }

  @Test
  void testTransferDiet_TargetSiteNotFound() {
    UUID target = UUID.randomUUID();
    UUID dietId = UUID.randomUUID();

    NotFoundDEException exception =
        assertThrows(
            NotFoundDEException.class,
            () -> mergeSitesService.transferDietFromOneSiteToAnother(target, dietId));

    verifyNoInteractions(dietRepository, pensRepository);
  }

  @Test
  void testTransferDiet_DietNotFound() {
    UUID target = UUID.randomUUID();
    UUID dietId = UUID.randomUUID();

    NotFoundDEException exception =
        assertThrows(
            NotFoundDEException.class,
            () -> mergeSitesService.transferDietFromOneSiteToAnother(target, dietId));
  }

  @Test
  void testTransferDiet_PensListIsEmpty() {
    UUID target = UUID.randomUUID();
    UUID dietId = UUID.randomUUID();

    SiteDocument siteDocument =
        SiteDocument.builder().accountId(UUID.randomUUID()).id(UUID.randomUUID()).build();

    DietDocument dietDocument = DietDocument.builder().id(dietId).siteId(UUID.randomUUID()).build();
    Diets diet = Diets.builder().dietDocument(dietDocument).build();
    when(mappingsRepository.findByMaxSiteId(any()))
        .thenReturn(
            List.of(
                SiteMappings.builder()
                    .siteMappingDocument(
                        SiteMappingDocument.builder()
                            .maxSiteId(UUID.randomUUID())
                            .labyrinthSiteId(UUID.randomUUID())
                            .build())
                    .build()));
    when(dietRepository.findBySiteId(dietId.toString())).thenReturn(List.of(diet, diet));
    when(pensRepository.findByDietId(dietId.toString())).thenReturn(Collections.emptyList());

    Map<String, List<String>> result =
        mergeSitesService.transferDietFromOneSiteToAnother(target, dietId);

    assertNotNull(result);
    assertTrue(result.containsKey("Diet"));
    assertEquals(1, result.size());
  }

  @Test
  void testTransferDiet_SuccessfulTransfer() {
    UUID target = UUID.randomUUID();
    UUID dietId = UUID.randomUUID();
    PenDocument penDocument =
        PenDocument.builder()
            .id(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .customerAccountId(UUID.randomUUID())
            .build();
    PenDocument penDocument2 =
        PenDocument.builder()
            .id(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .customerAccountId(UUID.randomUUID())
            .build();

    Pens pen1 = Pens.builder().penDocument(penDocument).build();
    Pens pen2 = Pens.builder().penDocument(penDocument2).build();

    DietDocument dietDocument = DietDocument.builder().id(dietId).siteId(UUID.randomUUID()).build();
    Diets diet = Diets.builder().dietDocument(dietDocument).build();
    when(mappingsRepository.findByMaxSiteId(any()))
        .thenReturn(
            List.of(
                SiteMappings.builder()
                    .siteMappingDocument(
                        SiteMappingDocument.builder()
                            .maxSiteId(UUID.randomUUID())
                            .labyrinthSiteId(UUID.randomUUID())
                            .build())
                    .build()));
    when(dietRepository.findBySiteId(dietId.toString())).thenReturn(List.of(diet, diet));
    when(pensRepository.findByDietId(dietId.toString())).thenReturn(Arrays.asList(pen1, pen2));

    Map<String, List<String>> result =
        mergeSitesService.transferDietFromOneSiteToAnother(target, dietId);

    assertNotNull(result);
    assertTrue(result.containsKey("Diet"));
    assertTrue(result.containsKey("Pens"));
  }

  @Test
  void testTransferDiet_PartialData() {
    UUID target = UUID.randomUUID();
    UUID dietId = UUID.randomUUID();

    DietDocument dietDocument = DietDocument.builder().id(dietId).siteId(UUID.randomUUID()).build();
    Diets diet = Diets.builder().dietDocument(dietDocument).build();
    when(mappingsRepository.findByMaxSiteId(any()))
        .thenReturn(
            List.of(
                SiteMappings.builder()
                    .siteMappingDocument(
                        SiteMappingDocument.builder()
                            .maxSiteId(UUID.randomUUID())
                            .labyrinthSiteId(UUID.randomUUID())
                            .build())
                    .build()));
    when(dietRepository.findBySiteId(dietId.toString())).thenReturn(List.of(diet));
    when(pensRepository.findByDietId(dietId.toString())).thenReturn(Collections.emptyList());

    Map<String, List<String>> result =
        mergeSitesService.transferDietFromOneSiteToAnother(target, dietId);

    assertNotNull(result);
    assertTrue(result.containsKey("Diet"));
    assertFalse(result.containsKey("Pens"));
    verify(pensRepository).findByDietId(dietId.toString());
  }
}
