/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.authconfig.JwtAuthenticationFilter;
import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.constants.UserAccountType;
import com.app.cargill.document.UserDocument;
import com.app.cargill.dto.*;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Role;
import com.app.cargill.model.User;
import com.app.cargill.repository.RolesRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IConfigurationService;
import com.app.cargill.service.IUserPreferenceService;
import com.app.cargill.service.IUserService;
import com.azure.cosmos.implementation.Strings;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.shaded.gson.Gson;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service("userServiceImpl")
@RequiredArgsConstructor
@Slf4j
public class UserServiceImpl implements IUserService {
  private final OktaProperties oktaProperties;
  private final AzureADProperties azureADProperties;
  private final UserRepository userRepository;

  private final RolesRepository rolesRepository;

  private final PasswordEncoder passwordEncoder;

  private final IUserPreferenceService userPreferenceServiceImpl;
  private final IConfigurationService configurationServiceImpl;

  @Override
  public List<UserDto> getAllUsers() {
    return userRepository.findAll().stream().map(this::modelToObjectMapper).toList();
  }

  @Override
  public UserDto getUserById(Long userId) {
    Optional<User> byId = userRepository.findById(userId);
    return byId.map(this::modelToObjectMapper).orElse(null);
  }

  @Override
  public UserDto getUserByPrincipal(String name) {
    Optional<User> byId = userRepository.findByPrincipalName(name);
    return byId.map(this::modelToObjectMapper).orElse(null);
  }

  @Override
  public UserDto saveOrUpdate(UserDto userDto) {
    Optional<User> byPrincipalName = userRepository.findByPrincipalName(userDto.getPrincipalName());
    User user = null;
    if (byPrincipalName.isPresent()) {
      user = byPrincipalName.get();
      user.setAuthenticationPlatform(userDto.getAuthenticationPlatform());
      user.setAccountType(userDto.getAccountType());
      user.setFullName(userDto.getFullName());
      user.setEmail(userDto.getEmail());
      user.setMobileNumber(userDto.getMobileNumber());
      user.setPassword(
          userDto.getPassword() != null ? passwordEncoder.encode(userDto.getPassword()) : null);
      user.setPrincipalName(userDto.getPrincipalName());
    } else {
      Set<Role> roles =
          rolesRepository.findByCodeIn(userDto.getRoles().stream().map(RoleDto::getCode).toList());
      user = dtoToModel(userDto);
      user.setRoles(roles != null && !roles.isEmpty() ? roles : null);
    }
    user = userRepository.save(user);
    return modelToObjectMapper(user);
  }

  User dtoToModel(UserDto userDto) {
    return User.builder()
        .authenticationPlatform(userDto.getAuthenticationPlatform())
        .accountType(userDto.getAccountType())
        .fullName(userDto.getFullName())
        .email(userDto.getEmail())
        .mobileNumber(userDto.getMobileNumber())
        .password(
            userDto.getPassword() != null ? passwordEncoder.encode(userDto.getPassword()) : null)
        .principalName(userDto.getPrincipalName())
        .build();
  }

  UserDto modelToObjectMapper(User user) {
    if (user != null) {
      List<RoleDto> roles =
          user.getRoles() != null
              ? user.getRoles().stream()
                  .map(
                      r ->
                          RoleDto.builder()
                              .id(r.getId())
                              .roleName(r.getRoleName())
                              .code(r.getCode())
                              .build())
                  .toList()
              : new ArrayList<>();
      return UserDto.builder()
          .id(user.getId())
          .fullName(user.getFullName())
          .email(user.getEmail())
          .mobileNumber(user.getMobileNumber())
          .principalName(user.getPrincipalName())
          .accountType(user.getAccountType())
          .authenticationPlatform(user.getAuthenticationPlatform())
          .roles(roles)
          .build();
    }
    return null;
  }

  @Override
  public String getCurrentLoggedInUser() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    return authentication.getName();
  }

  public User getLoggedUserData() {
    return userRepository.findByUserName(getCurrentLoggedInUser());
  }

  @Override
  public AuthenticationPlatform getCurrentLoggedInUserAuthPlatform() {
    Optional<? extends GrantedAuthority> authentication =
        SecurityContextHolder.getContext().getAuthentication() != null
            ? SecurityContextHolder.getContext().getAuthentication().getAuthorities().stream()
                .findFirst()
            : Optional.empty();
    return authentication.isPresent()
        ? AuthenticationPlatform.valueOf(authentication.get().getAuthority())
        : AuthenticationPlatform.NOT_SET;
  }

  @Override
  public String getCurrentLoggedInUserAsJsonObj() {
    return new Gson().toJson(Collections.singletonList(getCurrentLoggedInUser()));
  }

  @Override
  public UserDto getUserByIdToken(String idToken) throws UnsupportedEncodingException {
    Pair<Map<String, Object>, JWSHeader> mapJWSHeaderPair =
        JwtAuthenticationFilter.parseJWT(
            new String(
                Base64.getDecoder()
                    .decode(java.net.URLDecoder.decode(idToken, StandardCharsets.UTF_8.name())),
                StandardCharsets.UTF_8));
    if (mapJWSHeaderPair != null) {

      Map<String, Object> body = mapJWSHeaderPair.left();
      String email = getCurrentLoggedInUser();
      UserPreferenceDto userPreferencesDto = userPreferenceServiceImpl.getUserPreferences(email);
      ConfigurationDto configurationDto = configurationServiceImpl.fetchConfiguration();
      AuthenticationPlatform platform = null;
      String fullName =
          body.getOrDefault("name", body.getOrDefault("email", "").toString()).toString();
      fullName = Strings.isNullOrEmpty(fullName) ? email : fullName;
      if (body.getOrDefault("iss", "").toString().contentEquals(oktaProperties.getIssuer())) {
        platform = AuthenticationPlatform.OKTA;
      } else if (body.getOrDefault("iss", "")
          .toString()
          .contentEquals(azureADProperties.getIssuer())) {
        platform = AuthenticationPlatform.AZURE_AD;
      }

      return UserDto.builder()
          .email(email)
          .accountType(UserAccountType.EMAIL)
          .userPreferences(userPreferencesDto)
          .countryId(getCountryId(email))
          .configurations(configurationDto)
          .fullName(fullName)
          .principalName(email)
          .authenticationPlatform(platform)
          .build();
    }
    return null;
  }

  private String getCountryId(String email) {
    String countryId = userRepository.findCountryIdByUserName(email);

    return (countryId != null ? countryId : null);
  }

  @Override
  public UserSaveDto save(UserSaveDto userDto) {
    if (userRepository.existsByUserId(userDto.getUserName())) {
      throw new AlreadyExistsDEException("User Id already exists: " + userDto.getUserName());
    }
    User user = mapToModel(userDto);
    return mapToDto(userRepository.save(user));
  }

  @Override
  public UserSaveDto update(UserSaveDto userDto) {
    User userToPersist = userRepository.findByUserName(userDto.getUserName());
    if (userToPersist == null) {
      throw new NotFoundDEException("User Id does not exist: " + userDto.getUserName());
    }
    mapToModelUpdate(userDto, userToPersist);
    return mapToDto(userRepository.save(userToPersist));
  }

  private UserSaveDto mapToDto(User user) {
    return UserSaveDto.builder()
        .countryId(user.getUserDocument().getCountryId())
        .createdDate(user.getCreatedDate() != null ? user.getCreatedDate().toInstant() : null)
        .currentTimeStamp(Instant.now())
        .deleted(user.isDeleted())
        .id(
            user.getUserDocument().getId() != null
                ? user.getUserDocument().getId()
                : UUID.randomUUID())
        .localId(user.getLocalId())
        .salesforceCountryId(user.getUserDocument().getSalesforceCountryId())
        .updatedDate(user.getUpdatedDate() != null ? user.getUpdatedDate().toInstant() : null)
        .userName(user.getUserDocument().getUserName().toLowerCase())
        .applicationVersion(user.getUserDocument().getApplicationVersion())
        .deviceModel(user.getUserDocument().getDeviceModel())
        .deviceId(user.getUserDocument().getDeviceId())
        .deviceType(user.getUserDocument().getDeviceType())
        .build();
  }

  private User mapToModel(UserSaveDto userDto) {
    UserDocument userdocument =
        UserDocument.builder()
            .countryId(userDto.getCountryId())
            .id(userDto.getId() != null ? userDto.getId() : UUID.randomUUID())
            .salesforceCountryId(userDto.getSalesforceCountryId())
            .userName(userDto.getUserName())
            .applicationVersion(userDto.getApplicationVersion())
            .deviceId(userDto.getDeviceId())
            .deviceModel(userDto.getDeviceModel())
            .deviceType(userDto.getDeviceType())
            .build();

    return User.builder()
        .localId(userDto.getLocalId())
        .email(userDto.getUserName())
        .fullName(userDto.getUserName() != null ? userDto.getUserName().split("@")[0] : "")
        .principalName(userDto.getUserName())
        .deleted(userDto.isDeleted())
        .userDocument(userdocument)
        .build();
  }

  private User mapToModelUpdate(UserSaveDto userDto, User userToPersist) {

    userToPersist.getUserDocument().setApplicationVersion(userDto.getApplicationVersion());
    userToPersist.getUserDocument().setDeviceId(userDto.getDeviceId());
    userToPersist.getUserDocument().setDeviceModel(userDto.getDeviceModel());
    userToPersist.getUserDocument().setDeviceType(userDto.getDeviceType());

    return userToPersist;
  }

  @Override
  public UserDto fetchAndUpdateUserInfo(IdTokenDto idTokenDto) throws UnsupportedEncodingException {
    log.info("User logged: {}", idTokenDto.getIdToken());
    UserDto userDto = getUserByIdToken(idTokenDto.getIdToken());

    updateUserLoginStatus(
        userDto.getEmail(), userDto.getAuthenticationPlatform(), userDto.getFullName(), idTokenDto);
    return userDto;
  }

  @Override
  public BulkUserInsertDto bulkInsertUsers(BulkUserInsertDto userDto) {
    List<String> alreadyExists = new ArrayList<>();
    List<String> newUsers = new ArrayList<>();
    List<User> usersToPersist = new ArrayList<>();
    for (String userName : userDto.getUserNames()) {
      userName = userName.trim().strip().toLowerCase();
      if (userRepository.existsByUserId(userName)) {
        alreadyExists.add(userName);
      } else {

        UserDocument userdocument =
            UserDocument.builder()
                .countryId(userDto.getCountryId())
                .id(UUID.randomUUID())
                .salesforceCountryId(userDto.getSalesforceCountryId())
                .userName(userName)
                .build();

        User build =
            User.builder()
                .localId(UUID.randomUUID().toString())
                .email(userName)
                .fullName(userName != null ? userName.split("@")[0] : "")
                .principalName(userName)
                .deleted(false)
                .userDocument(userdocument)
                .build();
        usersToPersist.add(build);
      }
    }
    usersToPersist = userRepository.saveAll(usersToPersist);
    newUsers.addAll(usersToPersist.stream().map(u -> u.getUserDocument().getUserName()).toList());
    userDto.setNewUsers(newUsers);
    userDto.setAlreadyExists(alreadyExists);
    return userDto;
  }

  public void updateUserLoginStatus(
      String email, AuthenticationPlatform platform, String fullName, IdTokenDto idToken) {
    User user = userRepository.findByUserName(email);

    if (user == null) {
      return;
    }
    user.setAuthenticationPlatform(platform);
    user.setFullName(Strings.isNullOrEmpty(user.getFullName()) ? fullName : user.getFullName());
    user.getUserDocument().setApplicationVersion(idToken.getApplicationVersion());
    user.getUserDocument().setLastLoginDateTime(Instant.now());
    user.getUserDocument().setDeviceId(idToken.getDeviceId());
    user.getUserDocument().setDeviceModel(idToken.getDeviceModel());
    user.getUserDocument().setDeviceType(idToken.getDeviceType());

    userRepository.save(user);
  }

  @Override
  public List<User> updateName(String code) {
    List<User> userToPersist = userRepository.findByUserNameWithCode(code);
    if (userToPersist == null) {
      throw new NotFoundDEException("User Id does not exist: ");
    }
    return userToPersist.stream()
        .map(
            user -> {
              user.setFullName(user.getEmail() != null ? user.getEmail().split("@")[0] : "");
              userRepository.save(user);
              return user;
            })
        .toList();
  }
}
