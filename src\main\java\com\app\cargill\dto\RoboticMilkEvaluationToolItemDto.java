/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.CowFlowDesign;
import com.app.cargill.constants.RobotType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RoboticMilkEvaluationToolItemDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private RoboticMilkEvaluationToolOutputToolItemDto outputs;

  private List<UUID> selectedVisits;

  private RobotType robotType;

  private CowFlowDesign cowFlowDesign;

  private Integer robotsInHerd;

  private Integer lactatingCows;

  private Double averageMilkYield;

  private Double milkings;

  private Double robotFreeTime;

  private Double milkingRefusals;

  private Double totalMilkingFailures;

  private Double maximumConcentrate;

  private Double averageConcentrateFed;

  private Double minimumConcentrate;

  private Double averageBoxTime;

  private Double milkingSpeed;

  private Double concentratePer100KGMilk;

  private Double restFeed;
}
