/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BuildInformationDocument extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("AndroidVersion")
  private String androidVersion;

  @JsonProperty("IOSVersion")
  private String iosVersion;

  @JsonProperty("IsForceUpdateIos")
  private Boolean isForceUpdateIos;

  @JsonProperty("IsForceUpdateAndroid")
  private Boolean isForceUpdateAndroid;
}
