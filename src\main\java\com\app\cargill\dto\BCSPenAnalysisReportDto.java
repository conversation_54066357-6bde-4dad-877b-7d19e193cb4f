/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BCSPenAnalysisReportDto extends BaseDto {

  @Builder.Default private List<BCSPenAnalysisCategoryDto> categories = new ArrayList<>();
  private String visitDate;
  private String visitName;
  private String standardDeviationLabel;
  private String fileName;
  private String analysisType;
  private String toolName;
  private Double average;
  private String averageLabel;
  private String penName;
  private Double standardDeviation;
}
