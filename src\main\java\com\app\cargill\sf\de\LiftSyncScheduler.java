/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class LiftSyncScheduler {

  private final SalesforceSyncTrigger syncTrigger;

  private final SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");

  private static final int MINUTE = 1000 * 60;
  private static final int HOUR = MINUTE * 60;
  private boolean liftSyncFirstRun = true;
  private boolean liftPartialSyncFirstRun = true;

  @Scheduled(fixedRate = MINUTE)
  public void reportCurrentTime() {
    log.info("The time is now {}", dateFormat.format(new Date()));
  }

  @Scheduled(fixedRate = HOUR * 12)
  public void startLiftSync() {
    if (!liftSyncFirstRun) {
      log.info("LIFT full sync disabled");
      //      log.info("Starting LIFT full sync");
      //      syncTrigger.triggerSync(false);
    } else {
      log.info("LIFT Sync first run skipped");
      liftSyncFirstRun = false;
    }
  }

  @Scheduled(fixedRate = MINUTE)
  public void startLiftPartialSync() {
    if (!liftPartialSyncFirstRun) {
      log.info("Starting LIFT partial sync");
      syncTrigger.startLiftPullAccountsMergeSync(true);
      syncTrigger.triggerSync(true);
    } else {
      log.info("LIFT Partial Sync first run skipped");
      liftPartialSyncFirstRun = false;
    }
  }
}
