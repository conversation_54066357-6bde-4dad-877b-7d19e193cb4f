/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.app.cargill.constants.Currencies;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum CurrencyCrescendo {
  NOT_SET("NotSet"),
  EUR("EUR"),
  ARA("ARA"),
  AUD("AUD"),
  BAM("BAM"),
  BGL("BGL"),
  BRR("BRR"),
  CAD("CAD"),
  CHF("CHF"),
  CLF("CLF"),
  CNY("CNY"),
  COP("COP"),
  CZK("CZK"),
  DKK("DKK"),
  EGP("EGP"),
  GBP("GBP"),
  HKD("HKD"),
  HRK("HRK"),
  HUF("HUF"),
  INR("INR"),
  JOD("JOD"),
  LKR("LKR"),
  MKD("MKD"),
  MXN("MXN"),
  NOK("NOK"),
  PLN("PLN"),
  ROL("ROL"),
  RUB("RUB"),
  SGD("SGD"),
  UAH("UAH"),
  USD("USD"),
  VND("VND"),
  ZAR("ZAR"),
  SEK("SEK"),
  SKK("SKK"),
  TRY("TRY"),
  HNL("HNL"),
  NIO("NIO"),
  GTQ("GTQ"),
  CRC("CRC"),
  VEF("VEF"),
  PEN("PEN"),
  PHP("PHP"),
  IDR("IDR"),
  THB("THB"),
  MYR("MYR"),
  KRW("KRW"),
  TWD("TWD"),
  JPY("JPY"),
  NGN("NGN"),
  DZD("DZD"),
  ARS("ARS"),
  BRL("BRL"),
  CLP("CLP"),
  PON("PON"),
  SAR("SAR"),
  SRD("SRD"),
  UNDEFINED("");

  private final String value;

  @JsonCreator
  CurrencyCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static CurrencyCrescendo getFromCurrencies(Currencies currencies) {
    CurrencyCrescendo[] values = CurrencyCrescendo.values();
    if (currencies == null) {
      return CurrencyCrescendo.UNDEFINED;
    } else {
      return values[currencies.getValue()];
    }
  }
}
