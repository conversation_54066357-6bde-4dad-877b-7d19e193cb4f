/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Outputs implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Double animalsInHerd;
  private Double milkPrice;
  private Double totalProductionHerd;
  private Double totalDietCost;
  private Double totalProduction;
  private Double milkLitresKgConcentrate; // calculate
  private Double revenuePerCowPerDay;
  private Double feedConcentrate; // calculate
  private Double iofc; // calculate
  private Double totalDietCostPerRevenuePercentage; // calculate
}
