/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportSiteDetailsDto {
  private Map<String, String> generalDetails;
  private Map<String, String> dietInputs;
  private Map<String, String> animalInputs;
  private List<List<VisitReportColumnValueDto>> penDetails;
}
