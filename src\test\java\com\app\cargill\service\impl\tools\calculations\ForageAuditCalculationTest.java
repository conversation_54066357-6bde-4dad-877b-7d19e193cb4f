/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.*;
import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.impl.CdpServiceImpl;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class ForageAuditCalculationTest {

  @InjectMocks private ForageAuditCalculation forageAuditCalculation;

  @InjectMocks private CdpServiceImpl cdpService;

  @Mock private VisitsRepository visitsRepository;

  @Mock private SitesRepository sitesRepository;

  @Test
  void calculateBCSFields() {
    Scorecard tool = Scorecard.builder().sections(getSections()).build();

    tool = forageAuditCalculation.calculateFields(tool);

    assertEquals(
        ToolStatuses.Completed, tool.getSections().get(0).getScorecardSilages().get(0).getStatus());
  }

  @Test
  void calculateBCSFieldsWithNullValues() {
    Scorecard tool =
        Scorecard.builder()
            .sections(
                List.of(
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                ScorecardSilage.builder()
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder().build(),
                                                        ScorecardAnswer.builder().build()))
                                                .selectedAnswer(ScorecardAnswer.builder().build())
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .selectedAnswer(ScorecardAnswer.builder().build())
                                                .build()))
                                    .build()))
                        .build()))
            .build();

    tool = forageAuditCalculation.calculateFields(tool);
    assertEquals(
        ToolStatuses.Completed, tool.getSections().get(0).getScorecardSilages().get(0).getStatus());
  }

  private List<ScorecardSection> getSections() {
    return List.of(ScorecardSection.builder().scorecardSilages(getSilages()).build());
  }

  private List<ScorecardSilage> getSilages() {
    return List.of(
        ScorecardSilage.builder()
            .questions(
                List.of(
                    ScorecardQuestion.builder()
                        .isQuestionRequired(true)
                        .availableAnswers(
                            List.of(
                                ScorecardAnswer.builder().pointValue(2).build(),
                                ScorecardAnswer.builder().pointValue(1).build()))
                        .selectedAnswer(
                            ScorecardAnswer.builder().selected(true).pointValue(2).build())
                        .build(),
                    ScorecardQuestion.builder()
                        .isQuestionRequired(false)
                        .selectedAnswer(
                            ScorecardAnswer.builder().selected(true).pointValue(1).build())
                        .build()))
            .build());
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyCalculations() {

    Scorecard forageAuditTool =
        Scorecard.builder()
            .sections(
                List.of(
                    // section 1
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 1 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 1 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 1 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 2
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 2 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 2 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 2 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 3
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 3 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 3 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 3 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 4
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 4 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 4 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 4 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 5
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 5 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 5 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 5 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 6
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 6 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 6 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 6 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build()))
            .build();

    forageAuditTool = forageAuditCalculation.calculateFields(forageAuditTool);

    assertEquals(ToolStatuses.NotStarted, forageAuditTool.getSections().get(0).getStatus());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(0).getScorecardSilages().get(0).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(0)
            .getScorecardSilages()
            .get(0)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(0)
            .getScorecardSilages()
            .get(0)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(0).getScorecardSilages().get(1).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(0)
            .getScorecardSilages()
            .get(1)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(0)
            .getScorecardSilages()
            .get(1)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(0).getScorecardSilages().get(2).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(0)
            .getScorecardSilages()
            .get(2)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(0)
            .getScorecardSilages()
            .get(2)
            .getAnswersNegativePercent());

    assertEquals(ToolStatuses.NotStarted, forageAuditTool.getSections().get(1).getStatus());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(1).getScorecardSilages().get(0).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(1)
            .getScorecardSilages()
            .get(0)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(1)
            .getScorecardSilages()
            .get(0)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(1).getScorecardSilages().get(1).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(1)
            .getScorecardSilages()
            .get(1)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(1)
            .getScorecardSilages()
            .get(1)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(1).getScorecardSilages().get(2).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(1)
            .getScorecardSilages()
            .get(2)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(1)
            .getScorecardSilages()
            .get(2)
            .getAnswersNegativePercent());

    assertEquals(ToolStatuses.NotStarted, forageAuditTool.getSections().get(2).getStatus());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(2).getScorecardSilages().get(0).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(2)
            .getScorecardSilages()
            .get(0)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(2)
            .getScorecardSilages()
            .get(0)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(2).getScorecardSilages().get(1).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(2)
            .getScorecardSilages()
            .get(1)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(2)
            .getScorecardSilages()
            .get(1)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(2).getScorecardSilages().get(2).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(2)
            .getScorecardSilages()
            .get(2)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(2)
            .getScorecardSilages()
            .get(2)
            .getAnswersNegativePercent());

    assertEquals(ToolStatuses.NotStarted, forageAuditTool.getSections().get(3).getStatus());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(3).getScorecardSilages().get(0).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(3)
            .getScorecardSilages()
            .get(0)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(3)
            .getScorecardSilages()
            .get(0)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(3).getScorecardSilages().get(1).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(3)
            .getScorecardSilages()
            .get(1)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(3)
            .getScorecardSilages()
            .get(1)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(3).getScorecardSilages().get(2).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(3)
            .getScorecardSilages()
            .get(2)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(3)
            .getScorecardSilages()
            .get(2)
            .getAnswersNegativePercent());

    assertEquals(ToolStatuses.NotStarted, forageAuditTool.getSections().get(4).getStatus());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(4).getScorecardSilages().get(0).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(4)
            .getScorecardSilages()
            .get(0)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(4)
            .getScorecardSilages()
            .get(0)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(4).getScorecardSilages().get(1).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(4)
            .getScorecardSilages()
            .get(1)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(4)
            .getScorecardSilages()
            .get(1)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(4).getScorecardSilages().get(2).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(4)
            .getScorecardSilages()
            .get(2)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(4)
            .getScorecardSilages()
            .get(2)
            .getAnswersNegativePercent());

    assertEquals(ToolStatuses.NotStarted, forageAuditTool.getSections().get(5).getStatus());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(5).getScorecardSilages().get(0).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(5)
            .getScorecardSilages()
            .get(0)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(5)
            .getScorecardSilages()
            .get(0)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(5).getScorecardSilages().get(1).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(5)
            .getScorecardSilages()
            .get(1)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(5)
            .getScorecardSilages()
            .get(1)
            .getAnswersNegativePercent());
    assertEquals(
        ToolStatuses.NotStarted,
        forageAuditTool.getSections().get(5).getScorecardSilages().get(2).getStatus());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(5)
            .getScorecardSilages()
            .get(2)
            .getAnswersPositivePercent());
    assertEquals(
        0.0,
        forageAuditTool
            .getSections()
            .get(5)
            .getScorecardSilages()
            .get(2)
            .getAnswersNegativePercent());
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyNameAndToolName() {
    ScorecardSection scorecardSection = sectionOneBuiilder();
    Scorecard forageAuditTool =
        Scorecard.builder()
            .sections(
                List.of(
                    // section 1
                    sectionOneBuiilder(),
                    // section 2
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 2 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 2 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 2 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 3
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 3 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 3 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 3 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 4
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 4 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 4 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 4 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(1)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 5
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 5 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 5 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 5 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(4)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build(),
                    // section 6
                    ScorecardSection.builder()
                        .scorecardSilages(
                            List.of(
                                // section 6 silage 1
                                ScorecardSilage.builder()
                                    .sectionSilageType(0)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(false)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 6 silage 2
                                ScorecardSilage.builder()
                                    .sectionSilageType(1)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build(),
                                // section 6 silage 3
                                ScorecardSilage.builder()
                                    .sectionSilageType(2)
                                    .questions(
                                        List.of(
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(3)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(2)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build(),
                                            ScorecardQuestion.builder()
                                                .isQuestionRequired(true)
                                                .availableAnswers(
                                                    List.of(
                                                        ScorecardAnswer.builder()
                                                            .pointValue(10)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(5)
                                                            .selected(false)
                                                            .build(),
                                                        ScorecardAnswer.builder()
                                                            .pointValue(0)
                                                            .selected(false)
                                                            .build()))
                                                .build()))
                                    .includeInOverallForageScore(true)
                                    .build()))
                        .build()))
            .build();

    VisitDocument visitDocument =
        VisitDocument.builder()
            .siteId(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .visitDate(new Date().toInstant())
            .firstName("Bhajan Dass")
            .selected(false)
            .formattedCreationDate("August 8, 2023")
            .needsSync(true)
            .forageAuditScorecard(forageAuditTool)
            .build();

    List<Visits> visits =
        List.of(
            Visits.builder()
                .id(1L)
                .localId(UUID.randomUUID().toString())
                .visitDocument(visitDocument)
                .build());

    Page<Visits> visitPages = new PageImpl<>(visits);

    when(visitsRepository.findAllVisitsByFromAndToDate(any(), any(), any())).thenReturn(visitPages);

    when(sitesRepository.findMilkBySiteIds(any())).thenReturn(new ArrayList<>());

    Page<VisitDocumentDTO> response = cdpService.getAllVisitsByFromAndToDate(any(), any(), any());

    assertNotNull(response);

    List<AttributesDTO> filteredList =
        response.getContent().get(0).getAttributes().stream()
            .filter(
                attributesDTO ->
                    attributesDTO != null
                        && attributesDTO
                            .getName()
                            .equalsIgnoreCase(
                                "Sections4/ScorecardSilages3/Questions3/AvailableAnswers1/PointValue"))
            .toList();

    assertEquals(
        "Sections4/ScorecardSilages3/Questions3/AvailableAnswers1/PointValue",
        filteredList.get(0).getName());

    assertEquals(BigDecimal.valueOf(10), filteredList.get(0).getNumericValue());
  }

  private ScorecardSection sectionOneBuiilder() {
    return ScorecardSection.builder()
        .scorecardSilages(
            List.of(
                // section 1 silage 1
                ScorecardSilage.builder()
                    .sectionSilageType(0)
                    .questions(
                        List.of(
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(1)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(4)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(4)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(1)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build()))
                    .includeInOverallForageScore(true)
                    .build(),
                // section 1 silage 2
                ScorecardSilage.builder()
                    .sectionSilageType(1)
                    .questions(
                        List.of(
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(1)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(4)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build()))
                    .includeInOverallForageScore(true)
                    .build(),
                // section 1 silage 3
                ScorecardSilage.builder()
                    .sectionSilageType(2)
                    .questions(
                        List.of(
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(1)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(4)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(4)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(3)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(10)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(2)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build(),
                            ScorecardQuestion.builder()
                                .isQuestionRequired(true)
                                .availableAnswers(
                                    List.of(
                                        ScorecardAnswer.builder()
                                            .pointValue(5)
                                            .selected(false)
                                            .build(),
                                        ScorecardAnswer.builder()
                                            .pointValue(0)
                                            .selected(false)
                                            .build()))
                                .build()))
                    .includeInOverallForageScore(true)
                    .build()))
        .build();
  }
}
