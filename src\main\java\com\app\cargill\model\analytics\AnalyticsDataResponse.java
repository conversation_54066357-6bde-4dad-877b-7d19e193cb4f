/* Cargill Inc.(C) 2022 */
package com.app.cargill.model.analytics;

import com.app.cargill.model.AnalyticsDataPoint.DataPointName;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalyticsDataResponse {

  private int totalRecords;
  private List<DataPointContainer> data;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class DataPointContainer {
    private DataPointName dataPointName;
    private int totalRecords = 0;
    private List<? extends Serializable> dataPoints = new ArrayList<>();
  }
}
