/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AnimalAnalysisDetailsToolItemCDP;
import com.app.cargill.document.AnimalAnalysisToolCDP;
import com.app.cargill.document.AnimalAnalysisToolItemCDP;
import com.app.cargill.dto.ProfitabilityAnalysisData;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.service.ICdpV1Service;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class CdpV1ControllerTest {

  @Mock private ICdpV1Service cdpService;
  @InjectMocks private CDPV1Controller cdpController;

  @Test
  void getPaginatedAccountDocuments() {
    when(cdpService.getAllAccountsByFromAndToDateV1(any(), any())).thenReturn(new ArrayList<>());
    ResponseEntity<ResponseEntityDto<List<AccountDocumentDTO>>> result =
        cdpController.getAllAccountsByFromAndToDate(
            Instant.now().minus(1, ChronoUnit.DAYS), Instant.now());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    // `assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void getAllSitesByFromAndToDate_ReturnsSiteDocuments() {
    // Prepare test data
    Instant dateFrom = Instant.now();
    Instant dateTo = Instant.now();
    Pageable pageable = PageRequest.of(0, 100);
    List<SiteDocumentDTO> siteDocuments =
        List.of(SiteDocumentDTO.builder().build(), SiteDocumentDTO.builder().build());

    // Mock the cdpService
    when(cdpService.getAllSitesByFromAndToDateV1(dateFrom, dateTo)).thenReturn(new ArrayList<>());

    // Call the endpoint
    ResponseEntity<ResponseEntityDto<List<SiteDocumentDTO>>> response =
        cdpController.getAllSitesByFromAndToDate(dateFrom, dateTo);

    // Assertions
    assertNotNull(response);
    assertNotNull(response.getBody().getData());
  }

  @Test
  void getAllVisitsByFromAndToDate_ReturnsVisitDocuments() {
    // Prepare test data
    Instant dateFrom = Instant.now();
    Instant dateTo = Instant.now();
    Pageable pageable = PageRequest.of(0, 100);
    List<VisitDocumentDTO> visitDocuments = List.of(new VisitDocumentDTO(), new VisitDocumentDTO());
    Page<VisitDocumentDTO> page = new PageImpl<>(visitDocuments);

    // Mock the cdpService
    when(cdpService.getAllVisitsByFromAndToDateV1(dateFrom, dateTo)).thenReturn(new ArrayList<>());

    // Call the endpoint
    ResponseEntity<ResponseEntityDto<List<VisitDocumentDTO>>> response =
        cdpController.getAllVisitsByFromAndToDate(dateFrom, dateTo);

    // Assertions
    assertNotNull(response);
    assertNotNull(response.getBody().getData());
  }

  @Test
  void getAllProftabilityAnalysisByFromAndToDate_ReturnsVisitDocuments() {
    // Prepare test data
    Instant dateFrom = Instant.now();
    Instant dateTo = Instant.now();

    // Mock the cdpService
    when(cdpService.getAllProftabilityAnalysisDataByFromAndToDateV1(dateFrom, dateTo))
        .thenReturn(new ArrayList<>());

    // Call the endpoint
    ResponseEntity<ResponseEntityDto<List<ProfitabilityAnalysisData>>> response =
        cdpController.getAllProfitabilityAnalysisDataByFromAndToDate(dateFrom, dateTo);

    // Assertions
    assertNotNull(response);
    assertNotNull(response.getBody().getData());
  }

  @Test
  void whenGetAnimalAnalysisDetails() {
    AnimalAnalysisDetailsToolItemCDP animalAnalysisDetailsToolItemCDP =
        AnimalAnalysisDetailsToolItemCDP.builder()
            .penId(UUID.randomUUID())
            .daysInMilk(1)
            .bcsCategory(20.22)
            .build();
    List<AnimalAnalysisDetailsToolItemCDP> animalAnalysisDetailsToolItemCDPList = new ArrayList<>();
    animalAnalysisDetailsToolItemCDPList.add(animalAnalysisDetailsToolItemCDP);

    AnimalAnalysisToolItemCDP animalAnalysisToolItemCDP =
        AnimalAnalysisToolItemCDP.builder()
            .animalDetails(animalAnalysisDetailsToolItemCDPList)
            .penId(UUID.randomUUID())
            .penName("PenName")
            .build();
    List<AnimalAnalysisToolItemCDP> animalAnalysisToolItemCDPList = new ArrayList<>();
    animalAnalysisToolItemCDPList.add(animalAnalysisToolItemCDP);

    AnimalAnalysisToolCDP animalAnalysisToolCDP =
        AnimalAnalysisToolCDP.builder()
            .id(UUID.randomUUID())
            .animals(animalAnalysisToolItemCDPList)
            .createTimeUtc(Instant.now())
            .lastModifiedTimeUtc(Instant.now())
            .lastSyncTimeUtc(Instant.now())
            .createUser("testuser")
            .visitId(UUID.randomUUID())
            .build();
    List<AnimalAnalysisToolCDP> animalAnalysisToolCDPList = new ArrayList<>();
    animalAnalysisToolCDPList.add(animalAnalysisToolCDP);
    // AnimalAnalysisToolCDP
    when(cdpService.getAnimalAnalysisDetails()).thenReturn(animalAnalysisToolCDPList);
    ResponseEntity<ResponseEntityDto<List<AnimalAnalysisToolCDP>>> response =
        cdpController.getAnimalAnalysisDetails();
    assertNotNull(response);
  }
}
