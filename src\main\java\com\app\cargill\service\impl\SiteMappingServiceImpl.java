/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.service.ISiteMappingService;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("siteMappingServiceImpl")
public class SiteMappingServiceImpl implements ISiteMappingService {

  @Autowired SiteMappingsRepository siteMappingsRepository;

  @Override
  public void updateSiteMapping(Sites site) {

    UUID accountId = site.getSiteDocument().getAccountId();

    List<SiteMappings> siteMappingList =
        siteMappingsRepository.findAllByLabyrinthAccountId(accountId.toString());
    if (!siteMappingList.isEmpty()) {
      for (SiteMappings siteMappings : siteMappingList) {
        siteMappings.setDeleted(true);
      }
      siteMappingsRepository.saveAll(siteMappingList);
    }

    // creating new Site mapping in SiteMappings Document from the Site object
    // received.
    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
    if (site.getSiteDocument().getDataSourceMappings() != null) {
      for (DataSourceMapping dataSourceMapping : site.getSiteDocument().getDataSourceMappings()) {

        siteMappingDocument.setLabyrinthAccountId(accountId);
        if (dataSourceMapping
            .getSystemName()
            .equalsIgnoreCase(ApplicationMapping.LM_SITE_SYSTEM_NAME)) {
          siteMappingDocument.setLabyrinthSiteId(UUID.fromString(dataSourceMapping.getSystemId()));
        } else if (dataSourceMapping
            .getSystemName()
            .equalsIgnoreCase(ApplicationMapping.DDW_SYSTEM_NAME)) {
          siteMappingDocument.setDdwHerdId(dataSourceMapping.getSystemId());
        } else if (dataSourceMapping
            .getSystemName()
            .equalsIgnoreCase(ApplicationMapping.MAX_SYSTEM_NAME)) {
          siteMappingDocument.setMaxSiteId(UUID.fromString(dataSourceMapping.getSystemId()));
        }
      }
      SiteMappings siteMappings =
          SiteMappings.builder().siteMappingDocument(siteMappingDocument).build();
      siteMappingsRepository.save(siteMappings);
    }
  }

  @Override
  public void createLiftSiteMapping(Sites site) {
    if (site.getSiteDocument().getId() != null) {
      SiteMappings siteMapping =
          siteMappingsRepository.findBySiteIdAndIsDeleted(
              site.getSiteDocument().getId().toString());

      if (Objects.isNull(siteMapping)) {
        SiteMappings newSiteMapping =
            SiteMappings.builder()
                .localId(UUID.randomUUID().toString())
                .siteMappingDocument(
                    SiteMappingDocument.builder()
                        .id(UUID.randomUUID())
                        .labyrinthAccountId(site.getSiteDocument().getAccountId())
                        .labyrinthSiteId(site.getSiteDocument().getId())
                        .build())
                .build();
        siteMappingsRepository.save(newSiteMapping);
      } else {
        siteMapping
            .getSiteMappingDocument()
            .setId(
                siteMapping.getSiteMappingDocument().getId() != null
                    ? siteMapping.getSiteMappingDocument().getId()
                    : UUID.randomUUID());
        siteMapping
            .getSiteMappingDocument()
            .setLabyrinthAccountId(site.getSiteDocument().getAccountId());
        siteMapping.getSiteMappingDocument().setLabyrinthSiteId(site.getSiteDocument().getId());
        siteMappingsRepository.save(siteMapping);
      }
    }
  }
}
