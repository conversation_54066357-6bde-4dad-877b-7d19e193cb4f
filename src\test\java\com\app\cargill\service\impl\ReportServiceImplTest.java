/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.components.PlayWrightComponent;
import com.app.cargill.constants.LangCodes;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.MediaTypes;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.UserSettingsBrands;
import com.app.cargill.controller.ReportControllerTest;
import com.app.cargill.document.NoteMediaItem;
import com.app.cargill.document.NotesDocument;
import com.app.cargill.document.UserPreferenceDocument;
import com.app.cargill.dto.MetabolicDisorderCostPerCowDto;
import com.app.cargill.dto.MetabolicIncidenceReportDto;
import com.app.cargill.dto.ProfitabilityAnalysisReportDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreHerdAnalysisDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreHerdAnalysisReportDto;
import com.app.cargill.dto.ToolHeaderDto;
import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Notes;
import com.app.cargill.model.UserPreferences;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.UserPreferencesRepository;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.service.IS3Service;
import com.app.cargill.service.IUserService;
import com.app.cargill.sharepoint.service.VisitReportUploadToSharePoint;
import com.app.cargill.utils.MinimalPdf;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.mock.web.MockMultipartFile;

@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
@ExtendWith(MockitoExtension.class)
class ReportServiceImplTest {
  @InjectMocks private VisitReportServiceImpl visitReportServiceImpl;
  // Do not delete to prevent NPE for UserService
  @Mock private IUserService userServiceImpl;
  @Mock UserPreferencesRepository userPreferencesRepository;
  @Mock private ModelMapper modelMapper;
  @Mock private FreeMarkerComponent freeMarkerComponent;
  @Mock private PlayWrightComponent playWrightComponent;
  @Mock IExcelReportService cudChewingHerdAnalysisReportServiceImpl;
  @Mock IExcelReportService bcsHerdAnalysisReportServiceImpl;
  @Mock IS3Service s3ServiceImpl;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @InjectMocks private BCSHerdAnalysisReportServiceImpl bcsHerdAnalysisExcelReportServiceImpl;
  @InjectMocks private MilkSoldEvaluationReportServiceImpl milkSoldEvaluationReportServiceImpl;
  @InjectMocks private MetabolicIncidenceReportServiceImpl metabolicIncidenceReportServiceImpl;
  @Mock private NotesRepository notesRepository;
  @Mock private VisitReportUploadToSharePoint visitReportUploadToSharePoint;

  @InjectMocks
  private RoboticMilkEvaluationReportServiceImpl roboticMilkEvaluationReportServiceImpl;

  @InjectMocks
  private CudChewingPenAnalysisReportServiceImpl cudChewingPenAnalysisExcelReportServiceImpl;

  @InjectMocks private BCSPenAnalysisReportServiceImpl bcsPenAnalysisExcelReportServiceImpl;

  @InjectMocks
  private RumenHealthMSPenAnalysisReportServiceImpl rumenHealthMSPenAnalysisReportService;

  @InjectMocks
  private RumenHealthMSHerdAnalysisReportServiceImpl rumenHealthMSHerdAnalysisReportService;

  @InjectMocks
  private CudChewingHerdAnalysisReportServiceImpl cudChewingHerdAnalysisExcelReportServiceImpl;

  @InjectMocks private BcsAnimalAnalysisReportServiceImpl bcsAnimalAnalysisExcelReportServiceImpl;

  @InjectMocks
  private RumenFillHealthPenAnalysisReportServiceImpl rumenFillHealthPenAnalysisReportServiceImpl;

  @InjectMocks
  private RumenFillHealthHerdAnalysisReportServiceImpl rumenFillHealthHerdAnalysisReportServiceImpl;

  @InjectMocks
  private ForagePennStateHerdAnalysisReportServiceImpl foragePennStateHerdAnalysisReportServiceImpl;

  @InjectMocks
  private RumenHealthManureScreeningPenAnalysisReportServiceImpl
      rumenHealthManureScreeningPenAnalysisReportService;

  @InjectMocks private HeatStressReportServiceImpl heatStressReportServiceImpl;

  @InjectMocks private ReturnOverFeedReportServiceImpl returnOverFeedReportServiceImpl;

  @InjectMocks
  private PenTimeBudgetTimeAvailableForRestingReportServiceImpl
      availableForRestingReportServiceImpl;

  @InjectMocks
  private PenTimeBudgetPotentialMilkLossGainReportServiceImpl
      budgetPotentialMilkLossGainReportServiceImpl;

  @InjectMocks
  private RumenHealthTMRParticleScorePenAnalysisReportServiceImpl
      rumenHealthTMRParticleScorePenAnalysisReportServiceImpl;

  @InjectMocks
  private RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl
      rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl;

  @InjectMocks
  private LocomotionScoreAnimalAnalysisExcelReportServiceImpl
      LocomotionScoreAnimalAnalysisExcelReportServiceImpl;

  @InjectMocks
  private LocomotionScorePenAnalysisReportServiceImpl
      locomotionScorePenAnalysisExcelReportServiceImpl;

  @InjectMocks
  private LocomotionScoreHerdAnalysisReportServiceImpl
      locomotionScoreHerdAnalysisExcelReportServiceImpl;

  @InjectMocks
  private ProfitabilityAnalysisReportServiceImpl profitabilityAnalysisReportServiceImpl;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    lenient()
        .when(freeMarkerComponent.render(any(), any(), any(), any(), any()))
        .thenReturn(MinimalPdf.data);
  }

  @Test
  void whenBCSHerdAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForBcsHerdAnalysis());
    ByteArrayResource report =
        bcsHerdAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForBcsHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        bcsHerdAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForBcsHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        bcsHerdAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenProftabilityAnalysisExcelReportReturnValidResult()
      throws IOException, URISyntaxException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForProfitabilityAnalysis());
    ByteArrayResource report =
        profitabilityAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForProfitabilityAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        profitabilityAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForProfitabilityAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        profitabilityAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForProfitabilityAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenProftabilityAnalysisExcelReportReturnValidResultForRevenuePerCowPerDay()
      throws IOException, URISyntaxException {
    // Initialize mocks and data
    init();

    // Mock the modelMapper to return a pre-defined DTO when mapping is called
    when(modelMapper.map(any(), eq(ProfitabilityAnalysisReportDto.class)))
        .thenReturn(ReportControllerTest.prepareDataForProfitabilityAnalysisRevenuePerCowPerDay());

    // Call the method being tested
    ByteArrayResource report =
        profitabilityAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForProfitabilityAnalysisRevenuePerCowPerDay(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));

    // Assertions to check the result
    assertNotNull(report);
    assertNotNull(
        profitabilityAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForProfitabilityAnalysisRevenuePerCowPerDay()));
    assertTrue(report.getByteArray().length > 0);

    // Test PNG export
    assertNotNull(
        profitabilityAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForProfitabilityAnalysisRevenuePerCowPerDay(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenProftabilityAnalysisExcelReportReturnValidResultForMilkPriceFeedingCost()
      throws IOException, URISyntaxException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForProfitabilityAnalysisMilkPriceFeedingCost());
    ByteArrayResource report =
        profitabilityAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForProfitabilityAnalysisMilkPriceFeedingCost(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        profitabilityAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForProfitabilityAnalysisMilkPriceFeedingCost()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        profitabilityAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForProfitabilityAnalysisMilkPriceFeedingCost(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenGetgetLangKeyForLeftYAxisIsCalledCorrectResultIsReturned() {
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION,
        profitabilityAnalysisReportServiceImpl.getLangKeyForLeftYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY,
        profitabilityAnalysisReportServiceImpl.getLangKeyForLeftYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM,
        profitabilityAnalysisReportServiceImpl.getLangKeyForLeftYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_MILK_PRICE,
        profitabilityAnalysisReportServiceImpl.getLangKeyForLeftYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST));
    assertNull(
        profitabilityAnalysisReportServiceImpl.getLangKeyForLeftYAxis(
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT));
  }

  @Test
  void whenGetLangKeyForRightYAxisIsCalledCorrectResultIsReturned() {
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION_CONCENTRATED,
        profitabilityAnalysisReportServiceImpl.getLangKeyForRightYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_TOTAL_DIET_COST,
        profitabilityAnalysisReportServiceImpl.getLangKeyForRightYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_IOFC,
        profitabilityAnalysisReportServiceImpl.getLangKeyForRightYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_FEEDING_COST_PER_LITRE_OF_MILK,
        profitabilityAnalysisReportServiceImpl.getLangKeyForRightYAxis(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST));
    assertNull(
        profitabilityAnalysisReportServiceImpl.getLangKeyForRightYAxis(
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT));
  }

  @Test
  void whenGetTitleForGraphProfitabilityIsCalledCorrectResultIsReturned() {
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION_CHART_TITLE,
        profitabilityAnalysisReportServiceImpl.getTitleForGraph(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY_CHART_TITLE,
        profitabilityAnalysisReportServiceImpl.getTitleForGraph(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM_CHART_TITLE,
        profitabilityAnalysisReportServiceImpl.getTitleForGraph(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM));
    assertEquals(
        LangKeys.PROFITABILITY_ANALYSIS_MILK_PRICE_CHART_TITLE,
        profitabilityAnalysisReportServiceImpl.getTitleForGraph(
            ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST));
    assertNull(
        profitabilityAnalysisReportServiceImpl.getTitleForGraph(
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT));
  }

  @Test
  void whenRumenHealthManureScreeningReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenHealthManureScreeningPenAnalysis());
    ByteArrayResource report =
        rumenHealthManureScreeningPenAnalysisReportService.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenHealthManureScreeningPenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenHealthManureScreeningPenAnalysisReportService.getFileName(
            ReportControllerTest.prepareDataForRumenHealthManureScreeningPenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenHealthManureScreeningPenAnalysisReportService.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenHealthManureScreeningPenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenHeatStressReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForHeatStressReport());
    ByteArrayResource report =
        heatStressReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForHeatStressReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        heatStressReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForHeatStressReport()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        heatStressReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForHeatStressReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenPenTimeBudgetTimeAvailableForRestingExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForPenTimeBudgetTimeAvailableForResting());
    ByteArrayResource report =
        availableForRestingReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForPenTimeBudgetTimeAvailableForResting(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        availableForRestingReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForPenTimeBudgetTimeAvailableForResting()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        availableForRestingReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForPenTimeBudgetTimeAvailableForResting(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenPenTimeBudgetMilkLossGainExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForPenTimeBudgetMilkLossGain());
    ByteArrayResource report =
        budgetPotentialMilkLossGainReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForPenTimeBudgetMilkLossGain(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        budgetPotentialMilkLossGainReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForPenTimeBudgetMilkLossGain()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        budgetPotentialMilkLossGainReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForPenTimeBudgetMilkLossGain(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenLocomotionScoreAnimalAnalysisExcelReportReturnValidResult()
      throws IOException, URISyntaxException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForLocomotionAnimalAnalysis());
    ByteArrayResource report =
        LocomotionScoreAnimalAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForLocomotionAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        LocomotionScoreAnimalAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForLocomotionAnimalAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test jpeg export
    assertNotNull(
        LocomotionScoreAnimalAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForLocomotionAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenLocomotionHerdAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForLocomotionScoreHerdAnalysis());
    ByteArrayResource report =
        locomotionScoreHerdAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForLocomotionScoreHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        locomotionScoreHerdAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForLocomotionScoreHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        locomotionScoreHerdAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenLocomotionScorePenAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForLocomotionScorePenAnalysis());
    ByteArrayResource report =
        locomotionScorePenAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForLocomotionScorePenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        locomotionScorePenAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForLocomotionScorePenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        locomotionScorePenAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenCudChewingPenAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForCudChewingPenAnalysis());
    ByteArrayResource report =
        cudChewingPenAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForCudChewingPenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        cudChewingPenAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForCudChewingPenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        cudChewingPenAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenCudChewingHerdAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForCudChewingHerdAnalysis());
    ByteArrayResource report =
        cudChewingHerdAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForCudChewingHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        cudChewingHerdAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForCudChewingHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        cudChewingHerdAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenBCSPenAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForBCSPenAnalysis());
    ByteArrayResource report =
        bcsPenAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForBCSPenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        bcsPenAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForBCSPenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        bcsPenAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenBCSAnimalAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForBcsAnimalAnalysis());
    ByteArrayResource report =
        bcsAnimalAnalysisExcelReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        bcsAnimalAnalysisExcelReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis()));
    assertTrue(report.getByteArray().length > 0);

    // test png export
    assertNotNull(
        bcsAnimalAnalysisExcelReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForBcsAnimalAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRumenHealthManureScorePenAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenHealthManureScorePenAnalysis());
    ByteArrayResource report =
        rumenHealthMSPenAnalysisReportService.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenHealthManureScorePenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenHealthMSPenAnalysisReportService.getFileName(
            ReportControllerTest.prepareDataForRumenHealthManureScorePenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenHealthMSPenAnalysisReportService.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenHealthManureScorePenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRumenHealthManureScoreHerdAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenHealthMSHerdAnalysis());
    ByteArrayResource report =
        rumenHealthMSHerdAnalysisReportService.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenHealthMSHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenHealthMSHerdAnalysisReportService.getFileName(
            ReportControllerTest.prepareDataForRumenHealthMSHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenHealthMSHerdAnalysisReportService.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenHealthMSHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenMilkSoldEvaluationReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForMilkSoldEvaluation());
    ByteArrayResource report =
        milkSoldEvaluationReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForMilkSoldEvaluation(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForMilkSoldEvaluation()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForMilkSoldEvaluation(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenMilkSoldEvaluationMilkProductionReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProductionAndDim());
    ByteArrayResource report =
        milkSoldEvaluationReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProductionAndDim(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProductionAndDim()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProductionAndDim(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenReturnOVerFeedReportReturnValidResult() throws IOException, URISyntaxException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForReturnOverFeed());
    ByteArrayResource report =
        returnOverFeedReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForReturnOverFeed(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        returnOverFeedReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForReturnOverFeed()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        returnOverFeedReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForReturnOverFeed(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenMilkSoldEvaluationFatAndProteinPercentReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProteinPercent());
    ByteArrayResource report =
        milkSoldEvaluationReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProteinPercent(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProteinPercent()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForMilkSoldEvaluationMilkProteinPercent(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenMilkSoldEvaluationCellCountAndMilkUreaReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForMilkSoldEvaluationCellCountAndUrea());
    ByteArrayResource report =
        milkSoldEvaluationReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForMilkSoldEvaluationCellCountAndUrea(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForMilkSoldEvaluationCellCountAndUrea()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        milkSoldEvaluationReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForMilkSoldEvaluationCellCountAndUrea(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRoboticMilkEvaluationReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRoboticMilkEvaluation());
    ByteArrayResource report =
        roboticMilkEvaluationReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForRoboticMilkEvaluation(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        roboticMilkEvaluationReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForRoboticMilkEvaluation()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        roboticMilkEvaluationReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForRoboticMilkEvaluation(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenMetabolicIncidenceReportReturnValidResult() throws IOException {
    init();
    MetabolicDisorderCostPerCowDto retainedPlacenta =
        ReportControllerTest.prepareDataForMetabolicIncidence()
            .getMetabolicDisorderCostPerCow()
            .get("RetainedPlacenta")
            .get(0);

    when(modelMapper.map(
            any(MetabolicDisorderCostPerCowDto.class), eq(MetabolicDisorderCostPerCowDto.class)))
        .thenReturn(retainedPlacenta);
    when(modelMapper.map(
            ReportControllerTest.prepareDataForMetabolicIncidence(),
            MetabolicIncidenceReportDto.class))
        .thenReturn(ReportControllerTest.prepareDataForMetabolicIncidence());

    ByteArrayResource report =
        metabolicIncidenceReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForMetabolicIncidence(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        metabolicIncidenceReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForMetabolicIncidence()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        metabolicIncidenceReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForMetabolicIncidence(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRumenFillHealthPenAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenFillHealthPenAnalysis());
    ByteArrayResource report =
        rumenFillHealthPenAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenFillHealthPenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenFillHealthPenAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForRumenFillHealthPenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenFillHealthPenAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenFillHealthPenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRumenFillHealthHerdAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenFillHealthHerdAnalysis());
    ByteArrayResource report =
        rumenFillHealthHerdAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenFillHealthHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenFillHealthHerdAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForRumenFillHealthHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenFillHealthHerdAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenFillHealthHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenForagePennStateHerdAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForForagePennStateHerdAnalysis());
    ByteArrayResource report =
        foragePennStateHerdAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForForagePennStateHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        foragePennStateHerdAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForForagePennStateHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        foragePennStateHerdAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForForagePennStateHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRumenHealthTmrParticleScorePenAnalysisExcelReportReturnValidResult() throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenHealthTmrParticleScorePenAnalysis());
    ByteArrayResource report =
        rumenHealthTMRParticleScorePenAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScorePenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenHealthTMRParticleScorePenAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScorePenAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenHealthTMRParticleScorePenAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScorePenAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenRumenHealthTmrParticleScoreHerdAnalysisExcelReportReturnValidResult()
      throws IOException {
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(ReportControllerTest.prepareDataForRumenHealthTmrParticleScoreHerdAnalysis());
    ByteArrayResource report =
        rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.prepareExportToExcel(
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScoreHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.getFileName(
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScoreHerdAnalysis()));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.prepareExportToImage(
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScoreHerdAnalysis(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void
      whenRumenHealthTmrParticleScoreHerdAnalysisExcelReportHadRecordsGreaterThan10ReturnValidResult()
          throws IOException {
    RumenHealthTMRParticleScoreHerdAnalysisReportDto
        rumenHealthTMRParticleScoreHerdAnalysisReportDto =
            ReportControllerTest.prepareDataForRumenHealthTmrParticleScoreHerdAnalysis();
    rumenHealthTMRParticleScoreHerdAnalysisReportDto
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 7", 43.0, 90.0, 85.0, 73.0));
    rumenHealthTMRParticleScoreHerdAnalysisReportDto
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 8", 43.0, 90.0, 85.0, 73.0));
    rumenHealthTMRParticleScoreHerdAnalysisReportDto
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 9", 43.0, 90.0, 85.0, 73.0));
    rumenHealthTMRParticleScoreHerdAnalysisReportDto
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 9", 43.0, 90.0, 85.0, 73.0));
    rumenHealthTMRParticleScoreHerdAnalysisReportDto
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 999", 43.0, 90.0, 85.0, 73.0));
    init();
    when(modelMapper.map(any(), any()))
        .thenReturn(rumenHealthTMRParticleScoreHerdAnalysisReportDto);
    ByteArrayResource report =
        rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.prepareExportToExcel(
            rumenHealthTMRParticleScoreHerdAnalysisReportDto,
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
    assertNotNull(
        rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.getFileName(
            rumenHealthTMRParticleScoreHerdAnalysisReportDto));
    assertTrue(report.getByteArray().length > 0);
    // test png export
    assertNotNull(
        rumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.prepareExportToImage(
            rumenHealthTMRParticleScoreHerdAnalysisReportDto,
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name())));
  }

  @Test
  void whenDownloadVisitReportReturnValidResult() throws IOException {
    init();
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    VisitReportDto visitReportDto = ReportControllerTest.prepareDataForVisitReport();
    visitReportDto.setBrandName(UserSettingsBrands.Purina.name());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            visitReportDto,
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  byte[] getMockedFile() throws IOException {
    File mockedFile = new File("src/test/resources/s3test.png");
    return Files.readAllBytes(mockedFile.toPath());
  }

  @Test
  void whenDownloadVisitReportCargillReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any()))
        .thenReturn(
            UserPreferences.builder()
                .userPreferenceDocument(
                    UserPreferenceDocument.builder()
                        .brandList(List.of(UserSettingsBrands.Cargill))
                        .build())
                .build());
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportProvimiReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any()))
        .thenReturn(
            UserPreferences.builder()
                .userPreferenceDocument(
                    UserPreferenceDocument.builder()
                        .brandList(List.of(UserSettingsBrands.Agridea))
                        .build())
                .build());
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportProvimiUsReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any()))
        .thenReturn(
            UserPreferences.builder()
                .userPreferenceDocument(
                    UserPreferenceDocument.builder()
                        .brandList(List.of(UserSettingsBrands.Agridea))
                        .build())
                .build());
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportWithWrongBrandLogoReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any()))
        .thenReturn(
            UserPreferences.builder()
                .userPreferenceDocument(
                    UserPreferenceDocument.builder()
                        .brandList(List.of(UserSettingsBrands.None))
                        .build())
                .build());
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportWithNoBrandLogoReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any()))
        .thenReturn(
            UserPreferences.builder()
                .userPreferenceDocument(UserPreferenceDocument.builder().build())
                .build());
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportNullBrandNameReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any())).thenReturn(null);
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.FreeHandNote)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    VisitReportDto visitReportDto = ReportControllerTest.prepareDataForVisitReport();
    visitReportDto.setBodyConditionScoreTool(null);
    visitReportDto.setLocomotionScoreTool(null);
    visitReportDto.setMilkSoldEvaluationTool(null);
    visitReportDto.setRoboticMilkEvaluationTool(null);
    visitReportDto.setRumenHealthManureScoreTool(null);
    visitReportDto.setRumenHealthCudChewingTool(null);
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            visitReportDto,
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportNullAccountIdReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any())).thenReturn(null);
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.FreeHandNote)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenThrow(new IOException());
    VisitReportDto visitReportDto = ReportControllerTest.prepareDataForVisitReport();
    visitReportDto.setBodyConditionScoreTool(null);
    visitReportDto.setLocomotionScoreTool(null);
    visitReportDto.setMilkSoldEvaluationTool(null);
    visitReportDto.setRoboticMilkEvaluationTool(null);
    visitReportDto.setRumenHealthManureScoreTool(null);
    visitReportDto.setRumenHealthCudChewingTool(null);
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            visitReportDto,
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()));
    assertNotNull(report);
  }

  @Test
  void whenDownloadVisitReportInKoReturnValidResult() throws IOException {
    init();
    when(userPreferencesRepository.findByUserId(any()))
        .thenReturn(
            UserPreferences.builder()
                .userPreferenceDocument(
                    UserPreferenceDocument.builder()
                        .brandList(List.of(UserSettingsBrands.Cargill))
                        .build())
                .build());
    when(notesRepository.findByNotesIdsIn(anyList()))
        .thenReturn(
            Collections.singletonList(
                Notes.builder()
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .accountId(UUID.randomUUID())
                            .mediaItems(
                                Collections.singletonList(
                                    NoteMediaItem.builder()
                                        .mediaType(MediaTypes.Photo)
                                        .mediaId(UUID.randomUUID())
                                        .mediaName("test.png")
                                        .build()))
                            .build())
                    .build()));
    assertNotNull(
        visitReportServiceImpl.getFileName(ReportControllerTest.prepareDataForVisitReport()));
    assertNotNull(visitReportServiceImpl.getFileName(VisitReportDto.builder().build()));
    when(s3ServiceImpl.getObjectFromS3(anyString())).thenReturn(getMockedFile());
    ByteArrayResource report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.KO.name()));
    assertNotNull(report);
    report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.PL.name()));
    assertNotNull(report);
    report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.RU.name()));
    assertNotNull(report);
    report =
        visitReportServiceImpl.downloadVisitReport(
            ReportControllerTest.prepareDataForVisitReport(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.ZH.name()));
    assertNotNull(report);
  }

  @Test
  void whenGetHeaderForToolsIsCalledForAllToolsCorrectResultIsReturned() throws IOException {

    ToolHeaderDto dtoCudChewing =
        visitReportServiceImpl.getToolHeaderContent(
            "0000111CC",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoCudChewing.getToolContent());

    // Get ToolHeaderDto for "TMR penn state"
    ToolHeaderDto dtoTmr =
        visitReportServiceImpl.getToolHeaderContent(
            "0000111111TMR",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoTmr.getToolContent());

    // Get ToolHeaderDto for "Calf and heifer"
    ToolHeaderDto dtoCalfHeifer =
        visitReportServiceImpl.getToolHeaderContent(
            "0000222CH",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoCalfHeifer.getToolContent());

    // Get ToolHeaderDto for "Heat stress evaluation"
    ToolHeaderDto dtoHeatStress =
        visitReportServiceImpl.getToolHeaderContent(
            "0000333HS",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoHeatStress.getToolContent());

    // Get ToolHeaderDto for "Pen time budget"
    ToolHeaderDto dtoPenTime =
        visitReportServiceImpl.getToolHeaderContent(
            "0000444PB",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoPenTime.getToolContent());

    // Get ToolHeaderDto for "Rumen fill"
    ToolHeaderDto dtoRumenFill =
        visitReportServiceImpl.getToolHeaderContent(
            "0000888RF",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoRumenFill.getToolContent());

    // Get ToolHeaderDto for "Body condition score"
    ToolHeaderDto dtoBCS =
        visitReportServiceImpl.getToolHeaderContent(
            "0000555BCS",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoBCS.getToolContent());

    // Get ToolHeaderDto for "Metabolic incidence"
    ToolHeaderDto dtoMetabolic =
        visitReportServiceImpl.getToolHeaderContent(
            "0000777MI",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoMetabolic.getToolContent());

    // Locomotion
    ToolHeaderDto dtoLocomotion =
        visitReportServiceImpl.getToolHeaderContent(
            "0000666LS",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoLocomotion.getToolContent());

    // Get ToolHeaderDto for "Rumen health manure score"
    ToolHeaderDto dtoManureScore =
        visitReportServiceImpl.getToolHeaderContent(
            "0000999MS",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoManureScore.getToolContent());

    // Get ToolHeaderDto for "Rumen health manure screening"
    ToolHeaderDto dtoManureScreener =
        visitReportServiceImpl.getToolHeaderContent(
            "0000101010MS",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoManureScreener.getToolContent());

    // Get ToolHeaderDto for "Forage audit"
    ToolHeaderDto dtoForageAudit =
        visitReportServiceImpl.getToolHeaderContent(
            "0000121212FA",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoForageAudit.getToolContent());

    // Get ToolHeaderDto for "Forage inventories"
    ToolHeaderDto dtoForageInventories =
        visitReportServiceImpl.getToolHeaderContent(
            "0000131313FI",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoForageInventories.getToolContent());

    // Get ToolHeaderDto for "Forage penn state"
    ToolHeaderDto dtoForagePennState =
        visitReportServiceImpl.getToolHeaderContent(
            "0000141414FPS",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoForagePennState.getToolContent());

    // Get ToolHeaderDto for "Milk sold evaluation"
    ToolHeaderDto dtoMilkSoldEvaluation =
        visitReportServiceImpl.getToolHeaderContent(
            "0000151515MSE",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoMilkSoldEvaluation.getToolContent());

    // Get ToolHeaderDto for "Robotic milking evaluation"
    ToolHeaderDto dtoRoboticMilkEvaluation =
        visitReportServiceImpl.getToolHeaderContent(
            "0000161616RME",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dtoRoboticMilkEvaluation.getToolContent());

    // Get ToolHeaderDto for empty tool name
    ToolHeaderDto dtoNoTool =
        visitReportServiceImpl.getToolHeaderContent(
            "", resourceBundleMessageSource, Locale.forLanguageTag(LangCodes.EN.name()), "test");
    assertNull(dtoNoTool.getToolContent());
  }

  @Test
  void whenGetHeaderForToolsIsCalledForTmrPenStateCorrectResultIsReturned() throws IOException {

    ToolHeaderDto dto =
        visitReportServiceImpl.getToolHeaderContent(
            "0000111111TMR",
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            "test");
    assertNotNull(dto.getToolContent());
  }

  @Test
  void testShareVisitReportToSharePoint_ValidInput_Success()
      throws CustomDEExceptions, IOException {
    // Arrange
    String visitId = "123";
    byte[] fileBytes = new byte[10];
    MockMultipartFile file =
        new MockMultipartFile("file", "report.pdf", "application/pdf", fileBytes);

    // Act
    visitReportServiceImpl.shareVisitReportToSharePoint(visitId, file);

    // Assert
    verify(visitReportUploadToSharePoint, times(1))
        .uploadOfflineVisitReportToSharePoint(visitId, file.getBytes());
  }

  @Test
  void testShareVisitReportToSharePoint_NullFile_ExceptionThrown() {
    // Arrange
    String visitId = "123";
    MockMultipartFile file = null;

    // Act and Assert
    assertThrows(
        CustomDEExceptions.class,
        () -> visitReportServiceImpl.shareVisitReportToSharePoint(visitId, file));
  }
}
