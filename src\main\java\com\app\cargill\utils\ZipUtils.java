/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@SuppressWarnings("java:S1168")
public class ZipUtils {
  private ZipUtils() {}

  public static byte[] zipFiles(Map<String, byte[]> filesToZip, String extension)
      throws IOException {
    ByteArrayOutputStream baos = new ByteArrayOutputStream();
    ZipOutputStream zos = new ZipOutputStream(baos);
    for (Entry<String, byte[]> fileEntry : filesToZip.entrySet()) {
      ZipEntry entry = new ZipEntry(fileEntry.getKey().replace("/", "⧸") + extension);
      entry.setSize(fileEntry.getValue().length);
      zos.putNextEntry(entry);
      zos.write(fileEntry.getValue());
    }
    zos.closeEntry();
    zos.close();
    return baos.toByteArray();
  }

  public static byte[] zipIfMultipleFiles(Map<String, byte[]> imageTemplates, String extension)
      throws IOException {
    if (imageTemplates.size() > 1) {
      return zipFiles(imageTemplates, extension);
    } else {
      Optional<Entry<String, byte[]>> first = imageTemplates.entrySet().stream().findFirst();
      if (first.isPresent()) {
        return first.get().getValue();
      }
      return null;
    }
  }
}
