/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.ReturnOverFeedKgFatReportDto;
import com.app.cargill.dto.ReturnOverFeedPerKgFatDataPoints;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.BarDirection;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("returnOverFeedPerKgButterFatReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"})
public class ReturnOverFeedPerKgButterFatReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  private final FreeMarkerComponent freeMarkerComponent;

  private void createReturnOverFeedChart(
      XSSFSheet returnOverFeedWBSheet,
      ReturnOverFeedKgFatReportDto returnOverFeedDto,
      int periodHeaderRowNumber,
      int dataStartRowNumber,
      int metricCount) {

    // Calculate chart positioning
    int columnStart = 1;
    int columnEnd = columnStart + returnOverFeedDto.getDataPoints().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    int chartCol0 = columnEnd + 1;

    // Create chart
    XSSFChart chart =
        ExcelUtils.initChart(
            returnOverFeedWBSheet,
            "Return Over Feed Analysis",
            chartCol0,
            3,
            chartCol0
                + (returnOverFeedDto.getDataPoints().size() > 10
                    ? returnOverFeedDto.getDataPoints().size()
                    : 10),
            23);

    ExcelUtils.initLegends(chart);

    // Create axes
    XDDFCategoryAxis bottomAxis =
        ExcelUtils.createBottomAxis(
            chart,
            returnOverFeedDto.getXAxisLabel() != null ? returnOverFeedDto.getXAxisLabel() : "");
    XDDFValueAxis leftAxis =
        ExcelUtils.createLeftAxis(
            chart,
            returnOverFeedDto.getReturnOverFeedKgPerFatLabel() != null
                ? returnOverFeedDto.getReturnOverFeedKgPerFatLabel()
                : "");
    leftAxis.setCrosses(org.apache.poi.xddf.usermodel.chart.AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(org.apache.poi.xddf.usermodel.chart.AxisCrossBetween.BETWEEN);

    // Create chart data
    XDDFBarChartData dataLeft =
        (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);

    // Create data source for categories (periods)
    XDDFDataSource<String> periodDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            returnOverFeedWBSheet,
            new CellRangeAddress(
                periodHeaderRowNumber, periodHeaderRowNumber, columnStart, columnEnd));

    // Create series for each metric
    for (int metricIndex = 0; metricIndex < metricCount; metricIndex++) {
      XDDFNumericalDataSource<Double> metricDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              returnOverFeedWBSheet,
              new CellRangeAddress(
                  dataStartRowNumber + metricIndex,
                  dataStartRowNumber + metricIndex,
                  columnStart,
                  columnEnd));

      org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series series =
          (org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series)
              dataLeft.addSeries(periodDataSource, metricDataSource);

      series.setTitle(
          returnOverFeedWBSheet
              .getRow(dataStartRowNumber + metricIndex)
              .getCell(0)
              .getStringCellValue(),
          new org.apache.poi.ss.util.CellReference(
              returnOverFeedWBSheet.getSheetName(),
              dataStartRowNumber + metricIndex,
              0,
              true,
              true));
    }

    chart.plot(dataLeft);

    // Set colors for the bars
    java.util.List<byte[]> colors =
        java.util.Arrays.asList(
            new byte[] {(byte) 54, (byte) 162, (byte) 235}, // Blue
            new byte[] {(byte) 255, (byte) 99, (byte) 132}, // Red
            new byte[] {(byte) 75, (byte) 192, (byte) 192}, // Green
            new byte[] {(byte) 153, (byte) 102, (byte) 255} // Purple
            );
    ExcelUtils.setBarColorInBarChart(chart, colors, metricCount);
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale)
      throws IOException, URISyntaxException {
    ReturnOverFeedKgFatReportDto mappedDto =
        modelMapper.map(dto, ReturnOverFeedKgFatReportDto.class);
    Map<String, byte[]> imageTemplates = new HashMap<>();

    // create sheet 1
    byte[] returnOverFeedKgPerFat =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.RETURN_OVER_FEED_PER_KG_BUTTERFAT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            LangKeys.REPORT_RETURN_OVER_FEED_KG_PER_FAT,
            "Return Over Feed KG per Fat",
            null,
            source,
            locale),
        returnOverFeedKgPerFat);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  private XSSFSheet addReturnOverFeedSheet(
      XSSFWorkbook returnOverFeedWB,
      ResourceBundleMessageSource source,
      Locale locale,
      ReturnOverFeedKgFatReportDto returnOverFeedPerKgFatDto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {

    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(
            returnOverFeedWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

    XSSFSheet returnOverFeedWBSheet =
        returnOverFeedWB.createSheet(
            ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, "Report", null, source, locale)
                .replace("/", "⧸"));

    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    // Add header information
    prepareHeader(
        returnOverFeedWB,
        returnOverFeedWBSheet,
        rowNumber,
        cellNumber,
        returnOverFeedPerKgFatDto,
        boldStyle,
        source,
        locale);

    // Create data table title
    cellNumber.set(0);
    XSSFRow row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, "Return Over Feed Data");
    returnOverFeedWBSheet.addMergedRegion(
        new CellRangeAddress(
            rowNumber.get() - 1,
            rowNumber.get() - 1,
            0,
            returnOverFeedPerKgFatDto.getDataPoints().size()));

    // Create period headers (Week 1, Week 2, etc.)
    int periodHeaderRowNumber = rowNumber.get();
    cellNumber.set(1);
    row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());

    for (ReturnOverFeedPerKgFatDataPoints dataPoint : returnOverFeedPerKgFatDto.getDataPoints()) {
      ExcelUtils.highlightEmptyCell(
          row, dataPoint.getVisitDate(), cellNumber, centerBlack, greyCellStyle);
    }

    // Create data rows for the three metrics
    int dataStartRowNumber = rowNumber.get();
    int metricCount = 4; // returnOverFeed, totalFeedCost, totalRevenue

    String[] metricLabels = {
      returnOverFeedPerKgFatDto.getRofPerKgButterFatLabel(),
      returnOverFeedPerKgFatDto.getConcentrateCostPerKgButterFatLabel(),
      returnOverFeedPerKgFatDto.getTotalRevenueDollarKgPerFatLabel(),
      returnOverFeedPerKgFatDto.getTotalCostsPerKgPerFatLabel()
    };

    for (int metricIndex = 0; metricIndex < metricCount; metricIndex++) {
      cellNumber.set(0);
      row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());

      // Set metric label
      ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, metricLabels[metricIndex]);

      // Set values for each data point
      for (ReturnOverFeedPerKgFatDataPoints dataPoint : returnOverFeedPerKgFatDto.getDataPoints()) {
        Double value = null;
        if (metricIndex == 0) {
          value = dataPoint.getRofPerKgButterFat();
        } else if (metricIndex == 1) {
          value = dataPoint.getConcentrateCostPerKgButterFat();
        } else if (metricIndex == 2) {
          value = dataPoint.getTotalRevenueDollarKgPerFat();
        } else if (metricIndex == 3) {
          value = dataPoint.getTotalCostsPerKgPerFat();
        }
        ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
      }
    }

    // Create chart
    createReturnOverFeedChart(
        returnOverFeedWBSheet,
        returnOverFeedPerKgFatDto,
        periodHeaderRowNumber,
        dataStartRowNumber,
        metricCount);

    return returnOverFeedWBSheet;
  }

  private void prepareHeader(
      XSSFWorkbook returnOverFeedWB,
      XSSFSheet returnOverFeedWBSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      ReturnOverFeedKgFatReportDto returnOverFeedKgPerFatDto,
      XSSFCellStyle boldStyle,
      ResourceBundleMessageSource source,
      Locale locale) {

    // Add Cargill logo
    ExcelUtils.addCargillLogo(this.getClass(), returnOverFeedWB, returnOverFeedWBSheet, 0, 0);

    // Skip rows for logo
    rowNumber.set(3);

    // Visit Name
    XSSFRow row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, "Visit Name", null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, returnOverFeedKgPerFatDto.getVisitName());

    // Visit Date
    row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, "Visit Date", null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, returnOverFeedKgPerFatDto.getVisitDate());

    // Tool Name
    row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, "Tool Name", null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, returnOverFeedKgPerFatDto.getToolName());

    // Add empty row
    rowNumber.getAndIncrement();
  }

  @Override
  public String getFileName(Object data) {
    ReturnOverFeedKgFatReportDto dto = modelMapper.map(data, ReturnOverFeedKgFatReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.RETURN_OVER_FEED_PER_KG_BUTTERFAT.getFileName()
        : dto.getFileName();
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    ReturnOverFeedKgFatReportDto mappedDto =
        modelMapper.map(dto, ReturnOverFeedKgFatReportDto.class);

    try (XSSFWorkbook returnOverFeedPerKgFatWB = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              returnOverFeedPerKgFatWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(returnOverFeedPerKgFatWB, false, true, IndexedColors.BLACK));

      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              returnOverFeedPerKgFatWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(returnOverFeedPerKgFatWB, false, true, IndexedColors.BLACK));

      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              returnOverFeedPerKgFatWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(returnOverFeedPerKgFatWB, false, true, IndexedColors.BLACK));

      // Create the main sheet
      XSSFSheet sheet =
          addReturnOverFeedSheet(
              returnOverFeedPerKgFatWB,
              source,
              locale,
              mappedDto,
              boldStyle,
              greyCellStyle,
              centerBlack);

      int totalSheetColumns = sheet.getLastRowNum();
      return ExcelUtils.finalizeWorkbook(returnOverFeedPerKgFatWB, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }
}
