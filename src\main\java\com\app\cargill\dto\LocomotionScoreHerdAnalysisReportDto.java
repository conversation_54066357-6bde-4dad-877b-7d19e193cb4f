/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.LinkedHashMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LocomotionScoreHerdAnalysisReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String analysisType;
  private Double average;
  private Double standardDeviation;
  private String averageLabel;
  private String standardDeviationLabel;

  @Builder.Default private LinkedHashMap<String, Double> herdAvg = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> goals = new LinkedHashMap<>();
}
