/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.FeedStorageType;
import com.app.cargill.document.PileAndBunker;
import com.app.cargill.document.PileAndBunkerTool;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;

public class ForageInventoriesCalculation {
  private static final int METER_TO_CENTIMETER = 100;
  private static final double PI = 3.1415926;
  private static final double FEET_VALUE = 3.28083989501312;

  public PileAndBunkerTool calculateFields(PileAndBunkerTool tool, Instant createTimeUtc) {
    if (createTimeUtc == null || tool == null) return null;

    for (PileAndBunker pileBunker : tool.getPileBunkers()) {

      pileBunker.dateGone = dateGone(pileBunker, createTimeUtc);
      pileBunker.tonnesPerDay = Objects.requireNonNullElse(tonnesPerDay(pileBunker), 0.0);
      pileBunker.tonnesOfDryMatter = Objects.requireNonNullElse(tonnesOfDryMatter(pileBunker), 0.0);
      pileBunker.tonnesAsFed = Objects.requireNonNullElse(tonnesAsFed(pileBunker), 0.0);
      pileBunker.centimetersPerDay = Objects.requireNonNullElse(centimetersPerDay(pileBunker), 0.0);
      pileBunker.tonnesAFSilo = Objects.requireNonNullElse(tonnesAFSilo(pileBunker), 0.0);
      pileBunker.tonnesDMSilo = Objects.requireNonNullElse(tonnesDMSilo(pileBunker), 0.0);
      pileBunker.tonnesDMBag = Objects.requireNonNullElse(tonnesDMBag(pileBunker), 0.0);
      pileBunker.tonnesAFBag = Objects.requireNonNullElse(tonnesAFBag(pileBunker), 0.0);
    }
    return tool;
  }

  private Double centimetersPerDay(PileAndBunker tool) {
    Double feedOutSurfaceAreaValue;
    if (tool.isPileOrBunker == FeedStorageType.Pile)
      feedOutSurfaceAreaValue =
          calculateFeedOutSurfaceAreaMetersSquaredPile(
              tool.heightInMeters, tool.bottomWidthInMeters, tool.topWidthInMeters);
    else
      feedOutSurfaceAreaValue =
          calculateFeedOutSurfaceAreaMetersSquaredBunker(
              tool.heightInMeters, tool.bottomWidthInMeters, tool.topWidthInMeters);

    Double kgDMin1MValue =
        calculateKilogramsDryMatterInOneMeter(
            feedOutSurfaceAreaValue, tool.silageDMDensityInKgPerMetersCubed);

    if (kgDMin1MValue != null) kgDMin1MValue = roundAvoid(kgDMin1MValue, 0);

    return calculateCentimetersPerDay(
        tool.cowsToBeFed, tool.feedOutInclusionRate, kgDMin1MValue, tool.dryMatterPercentage);
  }

  private Double tonnesAsFed(PileAndBunker tool) {
    if (tool.tonnesAsFed == null) return 0.0;

    return roundAvoid(tool.tonnesAsFed, 0);
  }

  private Instant dateGone(PileAndBunker tool, Instant createTimeUtc) {
    if (!createTimeUtc.equals(Instant.MIN)
        && tool.feedOutInclusionRate != null
        && tool.cowsToBeFed != null
        && tool.tonnesAsFed != null
        && tool.tonnesAsFed > 0
        && tool.cowsToBeFed > 0
        && tool.feedOutInclusionRate > 0) {
      double dateGone =
          Math.round(tool.tonnesAsFed / (tool.feedOutInclusionRate * tool.cowsToBeFed / 1000));
      Duration duration = Duration.ofDays((long) dateGone);
      return createTimeUtc.plus(duration);
    } else {
      return createTimeUtc;
    }
  }

  private Double tonnesPerDay(PileAndBunker tool) {

    if (tool.feedOutInclusionRate != null && tool.cowsToBeFed != null) {
      double tonesPerDay = (tool.feedOutInclusionRate * tool.cowsToBeFed) / 1000;
      return roundAvoid(tonesPerDay, 1);
    } else return null;
  }

  private Double tonnesOfDryMatter(PileAndBunker tool) {
    if (tool.tonnesOfDryMatter != null) return roundAvoid(tool.tonnesOfDryMatter, 0);
    else return null;
  }

  private Double tonnesDMSilo(PileAndBunker tool) {

    return (tool.dryMatterPercentageSilo != null && tool.tonnesAFSilo != null)
        ? calculateTonnesDMSilo(tool.dryMatterPercentageSilo, tool.tonnesAFSilo)
        : null;
  }

  private Double tonnesAFSilo(PileAndBunker tool) {
    Double tonnesAFSilo = null;
    if (tool.isPileOrBunker == FeedStorageType.TopUnloadingSilo)
      tonnesAFSilo =
          calculateTonnesAFTopSilo(
              tool.dryMatterPercentageSilo,
              tool.filledHeightInMeters,
              tool.diameterInMeters,
              tool.silageLeftInMeters);
    else if (tool.isPileOrBunker == FeedStorageType.BottomUnloadingSilo)
      tonnesAFSilo =
          calculateTonnesAFBottomSilo(
              tool.dryMatterPercentageSilo, tool.diameterInMeters, tool.silageLeftInMeters);
    return tonnesAFSilo;
  }

  private Double tonnesDMBag(PileAndBunker tool) {
    if (tool.diameterBagInMeters != null
        && tool.lengthInMeters != null
        && tool.silageDMDensityBagKgPerMeter != null)
      return calculateTonnesDMBag(
          tool.diameterBagInMeters, tool.lengthInMeters, tool.silageDMDensityBagKgPerMeter);
    else return null;
  }

  private Double tonnesAFBag(PileAndBunker tool) {
    if (tool.dryMatterPercentageBag != null && tool.tonnesDMBag != null)
      return calculateTonnesAFBag(tool.dryMatterPercentageBag, tool.tonnesDMBag);
    else return null;
  }

  private Double calculateFeedOutSurfaceAreaMetersSquaredPile(
      Double heightInMeters, Double bottomWidthInMeters, Double topWidthInMeters) {
    if (heightInMeters != null && bottomWidthInMeters != null && topWidthInMeters != null) {
      double height = roundAvoid(heightInMeters, 2);
      double topWidth = roundAvoid(topWidthInMeters, 2);
      double bottomWidth = roundAvoid(bottomWidthInMeters, 2);

      return roundAvoid((height * ((bottomWidth - topWidth) / 2)) + (height * topWidth), 0);
    } else return null;
  }

  private Double calculateFeedOutSurfaceAreaMetersSquaredBunker(
      Double heightInMeters, Double bottomWidthInMeters, Double topWidthInMeters) {
    if (heightInMeters != null && bottomWidthInMeters != null && topWidthInMeters != null) {
      return roundAvoid(
          (heightInMeters * (topWidthInMeters - bottomWidthInMeters) / 2
              + (bottomWidthInMeters * heightInMeters)),
          0);
    } else return null;
  }

  private Double calculateKilogramsDryMatterInOneMeter(
      Double feedOutSurfaceAreaMetersSquared, Double silageDMDensityInKgPerMetersCubed) {
    if (feedOutSurfaceAreaMetersSquared != null && silageDMDensityInKgPerMetersCubed != null) {
      var silageDMDensity = roundAvoid(silageDMDensityInKgPerMetersCubed, 1);

      return feedOutSurfaceAreaMetersSquared * silageDMDensity;
    } else return null;
  }

  private Double calculateCentimetersPerDay(
      Integer cowsToBeFed,
      Double feedOutInclusionRate,
      Double kilogramsDryMatterInOneMeter,
      Double dryMatter) {

    if (cowsToBeFed != null
        && feedOutInclusionRate != null
        && kilogramsDryMatterInOneMeter != null
        && dryMatter != null
        && feedOutInclusionRate != 0
        && dryMatter != 0
        && kilogramsDryMatterInOneMeter != 0) {

      return roundAvoid(
          (((cowsToBeFed * feedOutInclusionRate * dryMatter / METER_TO_CENTIMETER)
                  / kilogramsDryMatterInOneMeter)
              * METER_TO_CENTIMETER),
          1);
    } else return null;
  }

  private Double calculateTonnesDMSilo(Double dryMatterPerSilo, Double afSilo) {

    if (dryMatterPerSilo != null && afSilo != null && dryMatterPerSilo != 0) {

      double dryMatPer = dryMatterPerSilo;
      double af = afSilo;

      return roundAvoid((af * dryMatPer / 100), 1);
    } else return null;
  }

  private Double calculateTonnesAFTopSilo(
      Double dryMatterPer, Double height, Double diameter, Double silage) {

    if (height != null
        && dryMatterPer != null
        && dryMatterPer != 0
        && diameter != null
        && silage != null) {
      double fillHeight = height;
      double dia = diameter;
      double silageLeft = silage;
      double dryPer = dryMatterPer;

      double finalResult =
          ((((3.14 * (dia * FEET_VALUE * dia * FEET_VALUE) / 4 * fillHeight * FEET_VALUE)
                          * (-0.0012 * fillHeight * FEET_VALUE * fillHeight * FEET_VALUE
                              + 0.2629 * fillHeight * FEET_VALUE
                              + 5.5952))
                      - ((3.14
                              * (dia * FEET_VALUE * dia * FEET_VALUE)
                              / 4
                              * (fillHeight * FEET_VALUE - silageLeft * FEET_VALUE))
                          * (-0.0012
                                  * (fillHeight * FEET_VALUE - silageLeft * FEET_VALUE)
                                  * (fillHeight * FEET_VALUE - silageLeft * FEET_VALUE)
                              + 0.2629 * (fillHeight * FEET_VALUE - silageLeft * FEET_VALUE)
                              + 5.5952)))
                  / (dryPer / 100)
                  / 2000)
              / 1.1023;

      return roundAvoid(finalResult, 1);
    } else return null;
  }

  private Double calculateTonnesAFBottomSilo(Double dryMatterPer, Double diameter, Double silage) {
    if (dryMatterPer != null && dryMatterPer != 0 && diameter != null && silage != null) {

      double dia = roundAvoid(diameter, 1);
      double silageLeft = roundAvoid(silage, 1);
      double dryPer = roundAvoid(dryMatterPer, 1);

      return (((3.14 * (dia * FEET_VALUE * dia * FEET_VALUE) / 4 * (silageLeft * FEET_VALUE))
                  * (-0.0012 * (silageLeft * FEET_VALUE) * (silageLeft * FEET_VALUE)
                      + 0.2629 * (silageLeft * FEET_VALUE)
                      + 5.5952))
              / (dryPer / 100)
              / 2000)
          / 1.1023;
    } else return null;
  }

  private Double calculateTonnesDMBag(Double diameter, Double length, Double silageDM) {
    if (diameter != null && length != null && silageDM != null) {
      double dia = diameter;
      double len = length;
      double dm = silageDM;
      double radius = dia / 2;
      double pow = Math.pow(radius, 2);
      double finalVal = PI * pow;

      return roundAvoid(((finalVal * len) * dm) / 1000, 1);
    } else return null;
  }

  private Double calculateTonnesAFBag(Double dryMatterPer, Double tonnesDMBag) {
    if (dryMatterPer != null && dryMatterPer != 0 && tonnesDMBag != null) {
      double tonsDM = tonnesDMBag;
      double dryPer = dryMatterPer;

      return roundAvoid(tonsDM / (dryPer / 100), 1);
    } else return null;
  }
}
