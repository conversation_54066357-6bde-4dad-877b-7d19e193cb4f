/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.json.JSONArray;
import org.json.JSONObject;

public class JsonUtils {
  private JsonUtils() {}

  public static String toJson(Object object) throws JsonProcessingException {
    ObjectMapper mapper = JsonMapper.builder().addModule(new JavaTimeModule()).build();
    return mapper.writer().withDefaultPrettyPrinter().writeValueAsString(object);
  }

  public static String toJsonWithoutPrettyPrinter(Object object) throws JsonProcessingException {
    ObjectMapper mapper = JsonMapper.builder().addModule(new JavaTimeModule()).build();
    return mapper.writer().writeValueAsString(object);
  }

  public static JSONObject convertStringToJsonObject(String s) {
    return new JSONObject(s);
  }

  public static JSONArray convertStringToJsonArray(String s) {
    return new JSONArray(s.replace("\\", ""));
  }
}
