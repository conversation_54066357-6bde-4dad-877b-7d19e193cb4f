/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

public class MinimalPdf {

  public static final byte[] data =
      new byte[] {
        37, 80, 68, 70, 45, 49, 46, 49, 10, 37, -62, -91, -62, -79, -61, -85, 10, 10, 49, 32, 48,
        32, 111, 98, 106, 10, 32, 32, 60, 60, 32, 47, 84, 121, 112, 101, 32, 47, 67, 97, 116, 97,
        108, 111, 103, 10, 32, 32, 32, 32, 32, 47, 80, 97, 103, 101, 115, 32, 50, 32, 48, 32, 82,
        10, 32, 32, 62, 62, 10, 101, 110, 100, 111, 98, 106, 10, 10, 50, 32, 48, 32, 111, 98, 106,
        10, 32, 32, 60, 60, 32, 47, 84, 121, 112, 101, 32, 47, 80, 97, 103, 101, 115, 10, 32, 32,
        32, 32, 32, 47, 75, 105, 100, 115, 32, 91, 51, 32, 48, 32, 82, 93, 10, 32, 32, 32, 32, 32,
        47, 67, 111, 117, 110, 116, 32, 49, 10, 32, 32, 32, 32, 32, 47, 77, 101, 100, 105, 97, 66,
        111, 120, 32, 91, 48, 32, 48, 32, 51, 48, 48, 32, 49, 52, 52, 93, 10, 32, 32, 62, 62, 10,
        101, 110, 100, 111, 98, 106, 10, 10, 51, 32, 48, 32, 111, 98, 106, 10, 32, 32, 60, 60, 32,
        32, 47, 84, 121, 112, 101, 32, 47, 80, 97, 103, 101, 10, 32, 32, 32, 32, 32, 32, 47, 80, 97,
        114, 101, 110, 116, 32, 50, 32, 48, 32, 82, 10, 32, 32, 32, 32, 32, 32, 47, 82, 101, 115,
        111, 117, 114, 99, 101, 115, 10, 32, 32, 32, 32, 32, 32, 32, 60, 60, 32, 47, 70, 111, 110,
        116, 10, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 60, 60, 32, 47, 70, 49, 10, 32, 32, 32,
        32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 60, 60, 32, 47, 84, 121, 112, 101, 32, 47,
        70, 111, 110, 116, 10, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32,
        32, 47, 83, 117, 98, 116, 121, 112, 101, 32, 47, 84, 121, 112, 101, 49, 10, 32, 32, 32, 32,
        32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 47, 66, 97, 115, 101, 70, 111, 110,
        116, 32, 47, 84, 105, 109, 101, 115, 45, 82, 111, 109, 97, 110, 10, 32, 32, 32, 32, 32, 32,
        32, 32, 32, 32, 32, 32, 32, 32, 32, 62, 62, 10, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32, 32,
        62, 62, 10, 32, 32, 32, 32, 32, 32, 32, 62, 62, 10, 32, 32, 32, 32, 32, 32, 47, 67, 111,
        110, 116, 101, 110, 116, 115, 32, 52, 32, 48, 32, 82, 10, 32, 32, 62, 62, 10, 101, 110, 100,
        111, 98, 106, 10, 10, 52, 32, 48, 32, 111, 98, 106, 10, 32, 32, 60, 60, 32, 47, 76, 101,
        110, 103, 116, 104, 32, 53, 53, 32, 62, 62, 10, 115, 116, 114, 101, 97, 109, 10, 32, 32, 66,
        84, 10, 32, 32, 32, 32, 47, 70, 49, 32, 49, 56, 32, 84, 102, 10, 32, 32, 32, 32, 48, 32, 48,
        32, 84, 100, 10, 32, 32, 32, 32, 40, 72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 41,
        32, 84, 106, 10, 32, 32, 69, 84, 10, 101, 110, 100, 115, 116, 114, 101, 97, 109, 10, 101,
        110, 100, 111, 98, 106, 10, 10, 120, 114, 101, 102, 10, 48, 32, 53, 10, 48, 48, 48, 48, 48,
        48, 48, 48, 48, 48, 32, 54, 53, 53, 51, 53, 32, 102, 32, 10, 48, 48, 48, 48, 48, 48, 48, 48,
        49, 56, 32, 48, 48, 48, 48, 48, 32, 110, 32, 10, 48, 48, 48, 48, 48, 48, 48, 48, 55, 55, 32,
        48, 48, 48, 48, 48, 32, 110, 32, 10, 48, 48, 48, 48, 48, 48, 48, 49, 55, 56, 32, 48, 48, 48,
        48, 48, 32, 110, 32, 10, 48, 48, 48, 48, 48, 48, 48, 52, 53, 55, 32, 48, 48, 48, 48, 48, 32,
        110, 32, 10, 116, 114, 97, 105, 108, 101, 114, 10, 32, 32, 60, 60, 32, 32, 47, 82, 111, 111,
        116, 32, 49, 32, 48, 32, 82, 10, 32, 32, 32, 32, 32, 32, 47, 83, 105, 122, 101, 32, 53, 10,
        32, 32, 62, 62, 10, 115, 116, 97, 114, 116, 120, 114, 101, 102, 10, 53, 54, 53, 10, 37, 37,
        69, 79, 70, 10
      };
}
