/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.forecast;

import com.app.cargill.configurations.DairyForecastConfiguration;
import com.app.cargill.model.AccountRoles;
import java.time.Instant;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction;
import org.springframework.security.oauth2.client.web.reactive.function.client.ServletOAuth2AuthorizedClientExchangeFilterFunction;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class DairyForecastService {

  private final WebClient webClient;

  public DairyForecastService(
      WebClient.Builder webClientBuilder,
      OAuth2AuthorizedClientManager authorizedClientManager,
      DairyForecastConfiguration dairyForecastConfiguration) {
    ServletOAuth2AuthorizedClientExchangeFilterFunction oauth2Client =
        new ServletOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager);
    this.webClient =
        webClientBuilder
            .baseUrl(dairyForecastConfiguration.getApiUrl())
            .filter(oauth2Client)
            .build();
  }

  public Flux<String> getRecords() {
    return webClient
        .get()
        .uri(
            uriBuilder ->
                uriBuilder.path("/GetDairyFarms").queryParam("dateFrom", Instant.now()).build())
        .attributes(
            ServerOAuth2AuthorizedClientExchangeFilterFunction.clientRegistrationId(
                "dairyforecast"))
        .retrieve()
        .bodyToFlux(String.class);
  }

  public Mono<String> syncAccountsRoles(List<AccountRoles> rolesList) {
    return webClient
        .post()
        .uri(uriBuilder -> uriBuilder.path("/SyncNewAppFarmUsers").build())
        .attributes(
            ServerOAuth2AuthorizedClientExchangeFilterFunction.clientRegistrationId(
                "dairyforecast"))
        .bodyValue(rolesList)
        .retrieve()
        .bodyToMono(String.class);
  }
}
