/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.BuildInformation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface BuildInformationRepository extends JpaRepository<BuildInformation, Long> {

  @Query(value = "Select * FROM Build_Information", nativeQuery = true)
  BuildInformation getBuildInfo();
}
