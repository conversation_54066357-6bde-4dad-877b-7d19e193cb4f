/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.model.simple.User;
import java.util.Locale;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class LiftUtilsTest {

  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @InjectMocks private LiftUtils liftUtils;

  @Test
  void whenGetLangKeyIsCalledWithUserNull() {
    User user = null;

    String result =
        liftUtils.getLangKey(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId("test").build())
                .build(),
            null);
    assertEquals(LangKeys.NO_USER_FOUND, result);
  }

  @Test
  void whenGetLangKeyIsCalledWithGoldenRecordIdAsNull() {
    User user = new User();

    String result =
        liftUtils.getLangKey(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId(null).build())
                .build(),
            null);
    assertEquals(LangKeys.ACCOUNT_NOT_SYNCED_TO_LIFT, result);
  }

  @Test
  void whenGetLangKeyIsCalledWithSiteExternalIdAsNull() {
    User user = new User();

    String result =
        liftUtils.getLangKey(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId("Test").build())
                .build(),
            Sites.builder().siteDocument(SiteDocument.builder().externalId(null).build()).build());
    assertEquals(LangKeys.NO_SITE_NOT_SYNCED_TO_LIFT, result);
  }

  @Test
  void whenGetLangKeyIsCalledWithAllAsNotNull() {
    User user = new User();

    String result =
        liftUtils.getLangKey(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId("Test").build())
                .build(),
            Sites.builder()
                .siteDocument(SiteDocument.builder().externalId("Test").build())
                .build());
    assertEquals(LangKeys.LIFT_SYNC_FAILED, result);
  }

  @Test
  void whenGetDefaultValueIsCalledWithUserNull() {
    User user = null;

    String result =
        liftUtils.getDefaultValue(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId("test").build())
                .build(),
            null);
    assertEquals(LiftErrorResponseConstants.USER_NOT_FOUND, result);
  }

  @Test
  void whenGetDefaultValueIsCalledWithGoldenRecordIdAsNull() {
    User user = new User();

    String result =
        liftUtils.getDefaultValue(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId(null).build())
                .build(),
            null);
    assertEquals(LiftErrorResponseConstants.ACCOUNT_NOT_SYNCED_TO_LIFT, result);
  }

  @Test
  void whenGetDefaultValueIsCalledWithSiteExternalIdAsNull() {
    User user = new User();

    String result =
        liftUtils.getDefaultValue(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId("Test").build())
                .build(),
            Sites.builder().siteDocument(SiteDocument.builder().externalId(null).build()).build());
    assertEquals(LiftErrorResponseConstants.SITE_NOT_SYNCED_TO_LIFT, result);
  }

  @Test
  void whenGetDefaultValueIsCalledWithAllAsNotNull() {
    User user = new User();

    String result =
        liftUtils.getDefaultValue(
            user,
            Accounts.builder()
                .accountDocument(AccountDocument.builder().goldenRecordId("Test").build())
                .build(),
            Sites.builder()
                .siteDocument(SiteDocument.builder().externalId("Test").build())
                .build());
    assertEquals(LiftErrorResponseConstants.LIFT_ERROR_MESSAGE, result);
  }

  @Test
  void whenTriggerForUserNotFoundIsCalledExceptionIsThrown() {
    Assertions.assertThrows(
        CustomDEExceptions.class,
        () -> {
          liftUtils.siteValidationError(null, null, null, locale, resourceBundleMessageSource);
        });
  }
}
