/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.NotificationType;
import com.app.cargill.document.NotificationDocument;
import com.app.cargill.dto.NotificationDto;
import com.app.cargill.dto.ReadNotificationDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Notifications;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.NotificationsRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.INotificationService;
import com.app.cargill.service.IUserService;
import com.app.cargill.utils.PageableUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements INotificationService {

  private final IUserService userServiceImpl;
  private final NotificationsRepository notificationsRepository;
  private final AccountsRepository accountsRepository;
  private final VisitsRepository visitsRepository;
  private final NotesRepository notesRepository;

  @Override
  public Page<NotificationDto> getAllNotificationsPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUser();
    Page<Notifications> notifications =
        notificationsRepository.findByUserId(currentLoggedUser, pageable, lastSyncTime);

    if (Objects.isNull(notifications)) {
      return new PageImpl<>(new ArrayList<>());
    }

    return new PageImpl<>(
        notifications.stream().map(this::modelToDto).toList(),
        pageable,
        notifications.getTotalElements());
  }

  private NotificationDto modelToDto(Notifications notification) {

    return NotificationDto.builder()
        .createdDate(
            notification.getCreatedDate() != null
                ? notification.getCreatedDate().toInstant()
                : null)
        .deleted(notification.getNotificationDocument().isDeleted())
        .description(notification.getNotificationDocument().getDescription())
        .id(notification.getNotificationDocument().getId())
        .isRead(notification.getNotificationDocument().getIsRead())
        .keys(notification.getNotificationDocument().getKeys())
        .localId(notification.getLocalId())
        .mobileCreatedAt(notification.getNotificationDocument().getMobileCreatedAt())
        .mobileLastUpdatedTime(notification.getNotificationDocument().getMobileLastUpdatedTime())
        .title(notification.getNotificationDocument().getTitle())
        .type(notification.getNotificationDocument().getType())
        .updatedDate(
            notification.getUpdatedDate() != null
                ? notification.getUpdatedDate().toInstant()
                : null)
        .userId(notification.getNotificationDocument().getUserId())
        .createUser(notification.getNotificationDocument().getCreateUser())
        .build();
  }

  @Override
  public NotificationDto save(NotificationDto notificationDto) {
    if (Objects.isNull(notificationDto.getLocalId())) {
      throw new IllegalArgumentException("LocalId is null");
    }
    List<Notifications> notificationExists =
        notificationsRepository.findByLocalId(notificationDto.getLocalId());
    if (notificationExists != null && !notificationExists.isEmpty()) {
      return modelToDto(notificationExists.get(0));
    }
    Notifications notification = dtoToModel(notificationDto);
    return modelToDto(notificationsRepository.save(notification));
  }

  private Notifications dtoToModel(NotificationDto notificationDto) {
    return Notifications.builder()
        .localId(notificationDto.getLocalId())
        .deleted(notificationDto.isDeleted())
        .notificationDocument(
            NotificationDocument.builder()
                .createTimeUtc(
                    notificationDto.getCreatedDate() != null
                        ? notificationDto.getCreatedDate()
                        : Instant.now())
                .createUser(
                    notificationDto.getCreateUser() != null
                        ? notificationDto.getCreateUser()
                        : userServiceImpl.getCurrentLoggedInUser())
                .description(notificationDto.getDescription())
                .id(notificationDto.getId() != null ? notificationDto.getId() : UUID.randomUUID())
                .isDeleted(notificationDto.isDeleted())
                .isRead(notificationDto.getIsRead())
                .keys(notificationDto.getKeys())
                .lastModifiedTimeUtc(Instant.now())
                .lastModifyUser(userServiceImpl.getCurrentLoggedInUser())
                .lastSyncTimeUtc(Instant.now())
                .mobileCreatedAt(notificationDto.getMobileCreatedAt())
                .mobileLastUpdatedTime(notificationDto.getMobileLastUpdatedTime())
                .title(notificationDto.getTitle())
                .type(notificationDto.getType())
                .userId(
                    notificationDto.getUserId() != null
                        ? notificationDto.getUserId()
                        : userServiceImpl.getCurrentLoggedInUser())
                .build())
        .build();
  }

  @Override
  public NotificationDto update(NotificationDto notificationDto) {

    if (notificationDto.getId() == null) {
      throw new NotFoundDEException("Id is null");
    }
    Notifications notificationById =
        notificationsRepository.findByNotificationId(notificationDto.getId().toString());

    if (notificationById == null) {
      throw new NotFoundDEException(
          "No notification found against ID: " + notificationDto.getId().toString());
    }
    Notifications notificationToPersist = dtoToModel(notificationDto);
    notificationToPersist.setId(notificationById.getId());
    notificationToPersist.setCreatedDate(notificationById.getCreatedDate());
    notificationToPersist.setUpdatedDate(notificationById.getUpdatedDate());

    notificationsRepository.save(notificationToPersist);
    return modelToDto(notificationToPersist);
  }

  @Override
  public ReadNotificationDto markAsRead(ReadNotificationDto notifications) {
    if (notifications.getNotificationIds() == null
        || notifications.getNotificationIds().isEmpty()) {
      throw new NotFoundDEException("No notification ids to update.");
    }

    List<String> success = new ArrayList<>();
    List<String> failure = new ArrayList<>();
    List<Notifications> notificationsToMarkAsRead = new ArrayList<>();
    for (UUID notificationId : notifications.getNotificationIds()) {
      Notifications notification =
          notificationsRepository.findByNotificationId(notificationId.toString());
      if (notification == null) {
        failure.add(notificationId.toString());
      } else {
        notification.getNotificationDocument().setIsRead(true);
        notificationsToMarkAsRead.add(notification);
        success.add(notificationId.toString());
      }
    }

    notificationsRepository.saveAllAndFlush(notificationsToMarkAsRead);
    notifications.setSuccess(success);
    notifications.setFailure(failure);
    return notifications;
  }

  @Override
  public List<String> getFilteredNotificationIds() {
    List<String> filteredAccountIds = getAccountIds();
    List<String> visitIds = getVisitIds(filteredAccountIds);
    List<String> noteIds = getNoteIds(filteredAccountIds, userServiceImpl.getCurrentLoggedInUser());

    List<String> allNotificationIds = new ArrayList<>();

    if (visitIds != null && !visitIds.isEmpty()) {
      allNotificationIds.addAll(
          notificationsRepository.findNotificationIdsByTypeUserIdAndVisitIds(
              NotificationType.VisitAutoPublished.name(),
              visitIds,
              userServiceImpl.getCurrentLoggedInUser()));
    }
    if (noteIds != null && !noteIds.isEmpty()) {
      allNotificationIds.addAll(
          notificationsRepository.findNotificationIdsByTypeUserIdAndNoteIds(
              NotificationType.OneHourBeforeActionIsDue.name(),
              noteIds,
              userServiceImpl.getCurrentLoggedInUser()));
      allNotificationIds.addAll(
          notificationsRepository.findNotificationIdsByTypeUserIdAndNoteIds(
              NotificationType.TwentyFourHoursBeforeActionIsDue.name(),
              noteIds,
              userServiceImpl.getCurrentLoggedInUser()));
    }
    allNotificationIds.addAll(
        notificationsRepository.findNotificationIdsByTypeAndUserId(
            NotificationType.TwentyFourHoursAndNoSync.name(),
            userServiceImpl.getCurrentLoggedInUser()));

    return allNotificationIds;
  }

  private List<String> getAccountIds() {
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    return accountsRepository.findAccountIdsByUserWithAllFlags(
        currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
  }

  private List<String> getNoteIds(List<String> filteredAccountIds, String currentLoggedInUser) {

    return notesRepository.findNoteIdsByAccountId(filteredAccountIds, currentLoggedInUser);
  }

  private List<String> getVisitIds(List<String> filteredAccountIds) {

    return visitsRepository.findVisitIdsByAccountIds(filteredAccountIds);
  }
}
