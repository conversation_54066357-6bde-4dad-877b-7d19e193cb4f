/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.model.UserPreferenceCosmos;
import com.app.cargill.cosmos.repo.UserPreferencesCosmosRepository;
import com.app.cargill.model.UserPreferences;
import com.app.cargill.repository.UserPreferencesRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class UserPreferencesMigrationTest {

  @Mock private UserPreferencesCosmosRepository cosmosRepository;

  @Mock private UserPreferencesRepository postgresRepository;

  @InjectMocks private UserPreferencesMigration service;

  @BeforeEach
  void setUp() {
    lenient().when(postgresRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenUserPreferencesMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    UserPreferenceCosmos userPreference =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/userPreferences.json"),
            UserPreferenceCosmos.class);

    Iterable<UserPreferenceCosmos> userPreferencesList = List.of(userPreference, userPreference);
    when(cosmosRepository.findAll()).thenReturn(userPreferencesList);

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenPostMigrationIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result =
          service.postMigration(CosmosDataMigration.MigrationType.USER_PREFERENCES.name()).get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenUserPreferencesMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    UserPreferenceCosmos userPreference1 = new UserPreferenceCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    UserPreferenceCosmos userPreference2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/userPreferences.json"),
            UserPreferenceCosmos.class);

    Iterable<UserPreferenceCosmos> userPreferencesList = List.of(userPreference1, userPreference2);
    when(cosmosRepository.findAll()).thenReturn(userPreferencesList);

    MigrationResult result = service.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }

  @Test
  void whenUserPreferencesMigrationHasExceptionFailuresReturnProperResults()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    UserPreferenceCosmos userPreference2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/userPreferencesException.json"),
            UserPreferenceCosmos.class);

    Iterable<UserPreferenceCosmos> userPreferencesList = List.of(userPreference2, userPreference2);
    when(cosmosRepository.findAll()).thenReturn(userPreferencesList);

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {

    MigrationResult result = null;
    try {
      result =
          service
              .migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.USER_PREFERENCES))
              .get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenUserPreferencesDataIsOkAndEmptyListNoErrors() {
    when(cosmosRepository.getUserPreferenceCosmosByUserEmail(anyString())).thenReturn(List.of());

    Mono<UserPreferences> result = service.moveRecord("test");

    StepVerifier.create(result).verifyComplete();
  }

  @Test
  void whenUserPreferencesDataIsOkAndMultipleValuesNoErrors() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());

    UserPreferenceCosmos userPreferenceCosmos =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/userPreferences.json"),
            UserPreferenceCosmos.class);
    when(cosmosRepository.getUserPreferenceCosmosByUserEmail(anyString()))
        .thenReturn(List.of(userPreferenceCosmos, userPreferenceCosmos));
    when(postgresRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    Mono<UserPreferences> result = service.moveRecord("test");

    StepVerifier.create(result).expectNextCount(1).verifyComplete();
  }

  @Test
  void whenUserPreferencesDataIsOkNoErrors() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());

    UserPreferenceCosmos userPreferenceCosmos =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/userPreferences.json"),
            UserPreferenceCosmos.class);
    when(cosmosRepository.getUserPreferenceCosmosByUserEmail(anyString()))
        .thenReturn(List.of(userPreferenceCosmos));
    when(postgresRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    Mono<UserPreferences> result = service.moveRecord("test");

    StepVerifier.create(result).expectNextCount(1).verifyComplete();
  }
}
