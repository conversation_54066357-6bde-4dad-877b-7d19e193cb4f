/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.AnalyzeStatus;
import com.app.cargill.dairymax.model.AnalyzeDietOptimizationMax;
import com.azure.cosmos.implementation.guava25.base.CaseFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class AnalyzeDietOptimization extends DietOptimization {
  @JsonProperty("Status")
  public AnalyzeStatus status;
  // Max <PERSON>
  @JsonProperty("NELDAIKG")
  public NelDaiKg nELDAIKG;

  @JsonProperty("AsFedAmount")
  public Double asFedAmount;

  @JsonProperty("DryMatterAmount")
  public Double dryMatterAmount;

  @JsonProperty("TotalCost")
  public Double totalCost;

  @JsonProperty("OptimizationId")
  public Integer optimizationId;

  public AnalyzeDietOptimization(AnalyzeDietOptimizationMax max) {
    super(max.getIngredients(), max.getNutrients());
    this.status =
        AnalyzeStatus.valueOf(
            CaseFormat.UPPER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, max.getStatus().name()));
    this.nELDAIKG = max.getNELDAIKG();
    this.totalCost = max.getTotalCost();
    this.dryMatterAmount = max.getDryMatterAmount();
    this.optimizationId = max.getOptimizationId();
    this.asFedAmount = max.getAsFedAmount();
  }
}
