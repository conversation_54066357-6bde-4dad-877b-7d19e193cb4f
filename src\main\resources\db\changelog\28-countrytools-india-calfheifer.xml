<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="028" author="Taha">
    <sql>
    UPDATE country_tools SET deleted= true WHERE country_tool_document->>'CountryId' = 'India'
    AND country_tool_document->>'ToolId' IN(
    'CalfHeiferScorecard',
        'UrinePHTool','ReadyToMilk',
        'Revenue','RoboticMilkEvaluation','PileAndBunker','ForageAuditScorecard');
    </sql>
  </changeSet>
</databaseChangeLog>