/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.cdp.visit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AttributesDTO implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("ToolName")
  private String toolName;

  @JsonProperty("TextValue")
  private String textValue;

  @JsonProperty("NumericValue")
  private BigDecimal numericValue;

  @JsonProperty("UnitOfMeasure")
  private String unitOfMeasure;

  @JsonProperty("ValueType")
  private String valueType;
}
