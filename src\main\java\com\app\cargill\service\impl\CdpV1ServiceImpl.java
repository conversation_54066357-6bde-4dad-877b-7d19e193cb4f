/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.document.*;
import com.app.cargill.dto.ISelectKeyValueDto;
import com.app.cargill.dto.ProfitabilityAnalysisData;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.CdpSiteDataWrapper;
import com.app.cargill.model.EarTags;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.*;
import com.app.cargill.service.ICdpV1Service;
import com.app.cargill.service.impl.mappers.cdp.CdpAccountDocumentMapper;
import com.app.cargill.service.impl.mappers.cdp.CdpSiteDocumentMapper;
import com.app.cargill.service.impl.mappers.cdp.CdpVisitDocumentMapper;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;

@Slf4j
@Service("cdpV1ServiceImpl")
@RequiredArgsConstructor
public class CdpV1ServiceImpl implements ICdpV1Service {

  private final AccountsRepository accountsRepository;
  private final CdpSitesRepository cdpSitesRepository;
  private final SitesRepository sitesRepository;
  private final VisitsRepository visitsRepository;
  private final EarTagsRepository earTagsRepository;

  @Override
  @Transactional(readOnly = true, isolation = Isolation.SERIALIZABLE)
  public List<AccountDocumentDTO> getAllAccountsByFromAndToDateV1(
      @NotNull Instant dateFrom, @NotNull Instant dateTo) {
    return accountsRepository.findAllAccountsByFromAndToDateV1(dateFrom, dateTo).stream()
        .map(Accounts::getAccountDocument)
        .map(CdpAccountDocumentMapper::mapToDto)
        .toList();
  }

  @Override
  @Transactional(readOnly = true, isolation = Isolation.SERIALIZABLE)
  public List<SiteDocumentDTO> getAllSitesByFromAndToDateV1(Instant dateFrom, Instant dateTo) {

    Instant start = Instant.now();
    List<CdpSiteDataWrapper> cdpSites = cdpSitesRepository.getCdpSitesData(dateFrom, dateTo);

    List<SiteDocumentDTO> sites =
        Flux.fromIterable(cdpSites).map(CdpSiteDocumentMapper::mapV1ToDto).collectList().block();

    Instant end = Instant.now();
    log.info("getAllSitesByFromAndToDate duration: {}", ChronoUnit.MILLIS.between(start, end));

    return sites;
  }

  @Override
  public List<VisitDocumentDTO> getAllVisitsByFromAndToDateV1(Instant dateFrom, Instant dateTo) {
    List<Visits> visits = visitsRepository.findAllVisitsByFromAndToDateV1(dateFrom, dateTo);
    List<String> siteIds =
        visits.stream()
            .map(Visits::getVisitDocument)
            .filter(
                visitDocument ->
                    visitDocument != null
                        && visitDocument.getSiteId() != null
                        && visitDocument.getMilkSoldEvaluation() != null)
            .toList()
            .stream()
            .map(VisitDocument::getSiteId)
            .toList()
            .stream()
            .map(UUID::toString)
            .toList();
    List<ISelectKeyValueDto<UUID, Double>> sites =
        ListUtils.emptyIfNull(sitesRepository.findMilkBySiteIds(siteIds));
    sites.forEach(
        s ->
            visits.stream()
                .filter(v -> v.getVisitDocument().getSiteId().equals(s.getKey()))
                .forEach(sv -> sv.getVisitDocument().setSiteMilk(s.getValue())));

    return visits.stream()
        .map(Visits::getVisitDocument)
        .map(CdpVisitDocumentMapper::mapToDto)
        .toList();
  }

  @Override
  public List<Visits> getAllVisitsForOneYear() {
    List<Visits> visits = visitsRepository.findAllVisitsForOneYear();
    for (Visits visit : visits) {
      log.info("Data {}", visit.getId());
    }

    if (!visits.isEmpty()) {
      visits.stream()
          .forEach(
              visitDocument ->
                  visitDocument.getVisitDocument().setLastModifiedTimeUtc(Instant.now()));
      visitsRepository.saveAll(visits);
    }
    return visits;
  }

  @Override
  public List<ProfitabilityAnalysisData> getAllProftabilityAnalysisDataByFromAndToDateV1(
      Instant dateFrom, Instant dateTo) {
    List<Visits> visits =
        visitsRepository.findAllProtabilityDataByFromAndToDateV1(dateFrom, dateTo);
    List<String> siteIds =
        visits.stream()
            .map(Visits::getVisitDocument)
            .filter(
                visitDocument ->
                    visitDocument != null
                        && visitDocument.getSiteId() != null
                        && visitDocument.getMilkSoldEvaluation() != null)
            .toList()
            .stream()
            .map(VisitDocument::getSiteId)
            .toList()
            .stream()
            .map(UUID::toString)
            .toList();
    List<ISelectKeyValueDto<UUID, Double>> sites =
        ListUtils.emptyIfNull(sitesRepository.findMilkBySiteIds(siteIds));
    sites.forEach(
        s ->
            visits.stream()
                .filter(v -> v.getVisitDocument().getSiteId().equals(s.getKey()))
                .forEach(sv -> sv.getVisitDocument().setSiteMilk(s.getValue())));

    return visits.stream()
        .map(Visits::getVisitDocument)
        .map(CdpVisitDocumentMapper::mapToDtoForProfitabilityAnalysis)
        .toList();
  }

  public List<AnimalAnalysisToolCDP> getAnimalAnalysisDetails() {
    log.info("getAnimalAnalysisDetails started");
    List<AnimalAnalysisToolCDP> animalAnalysisToolsList = new ArrayList<>();
    try {
      List<Visits> visitsList = visitsRepository.getAnimalAnalysisDetails();
      visitsList.parallelStream()
          .forEach(
              visits -> {
                AnimalAnalysisTool animalClassDto = visits.getVisitDocument().getAnimalAnalysis();
                if (animalClassDto != null && animalClassDto.getAnimals() != null) {
                  AnimalAnalysisToolCDP animalAnalysisToolCDP =
                      mapAnimalAnalysisToDto(animalClassDto);
                  animalAnalysisToolsList.add(animalAnalysisToolCDP);
                }
              });
    } catch (Exception e) {
      log.error("Error calling getAnimalAnalysisDetails " + e);
    }
    return animalAnalysisToolsList;
  }

  AnimalAnalysisToolCDP mapAnimalAnalysisToDto(AnimalAnalysisTool animalClassDto) {
    return AnimalAnalysisToolCDP.builder()
        .id(animalClassDto.getId())
        .createUser(animalClassDto.getCreateUser())
        .createTimeUtc(animalClassDto.getCreateTimeUtc())
        .lastModifiedTimeUtc(animalClassDto.getLastModifiedTimeUtc())
        .lastModifyUser(animalClassDto.getLastModifyUser())
        .lastSyncTimeUtc(animalClassDto.getLastSyncTimeUtc())
        .animals(mapAnimalAnalysisToolItemCDP(animalClassDto.getAnimals()))
        .visitId(animalClassDto.getVisitId())
        .build();
  }

  List<AnimalAnalysisToolItemCDP> mapAnimalAnalysisToolItemCDP(
      List<AnimalAnalysisToolItem> animalAnalysisToolItem) {
    List<AnimalAnalysisToolItemCDP> animalAnalysisToolItemCDPList = new ArrayList<>();
    animalAnalysisToolItem.forEach(
        animalAnalysisToolItemTemp -> {
          AnimalAnalysisToolItemCDP animalAnalysisToolItemCDP =
              mapAnimalAnalysisDetailsToolItemCDP(animalAnalysisToolItemTemp);
          animalAnalysisToolItemCDPList.add(animalAnalysisToolItemCDP);
        });
    return animalAnalysisToolItemCDPList;
  }

  AnimalAnalysisToolItemCDP mapAnimalAnalysisDetailsToolItemCDP(
      AnimalAnalysisToolItem animalAnalysisToolItem) {
    List<AnimalAnalysisDetailsToolItemCDP> animalAnalysisDetailsToolItemCDPList = new ArrayList<>();
    AnimalAnalysisToolItemCDP animalAnalysisToolItemCDP = new AnimalAnalysisToolItemCDP();

    animalAnalysisToolItemCDP.setPenId(animalAnalysisToolItem.getPenId());
    animalAnalysisToolItemCDP.setPenName(animalAnalysisToolItem.getPenName());
    animalAnalysisToolItem.getAnimalDetails().parallelStream()
        .forEach(
            animalAnalysisDetailsToolItem -> {
              AnimalAnalysisDetailsToolItemCDP animalAnalysisDetailsToolItemCDP =
                  new AnimalAnalysisDetailsToolItemCDP();
              animalAnalysisDetailsToolItemCDP.setEarTagName(
                  mapEarTagName(animalAnalysisDetailsToolItem.getEarTagId()));
              animalAnalysisDetailsToolItemCDP.setPenId(animalAnalysisDetailsToolItem.getPenId());
              animalAnalysisDetailsToolItemCDP.setDaysInMilk(
                  animalAnalysisDetailsToolItem.getDaysInMilk());
              animalAnalysisDetailsToolItemCDP.setBcsCategory(
                  animalAnalysisDetailsToolItem.getBcsCategory());
              animalAnalysisDetailsToolItemCDP.setLocomotionScore(
                  animalAnalysisDetailsToolItem.getLocomotionScore());
              animalAnalysisDetailsToolItemCDPList.add(animalAnalysisDetailsToolItemCDP);
            });
    animalAnalysisToolItemCDP.setAnimalDetails(animalAnalysisDetailsToolItemCDPList);
    return animalAnalysisToolItemCDP;
  }

  String mapEarTagName(UUID earTagId) {
    EarTags earTagToPersist = earTagsRepository.findByEarTagId(earTagId.toString());
    String earTagName = "";
    if (earTagToPersist != null && earTagToPersist.getEarTagDocument() != null) {
      earTagName = earTagToPersist.getEarTagDocument().getEarTagName();
    }
    return earTagName;
  }
}
