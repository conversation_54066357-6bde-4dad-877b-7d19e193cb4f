/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellValue;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class ExcelService {

  public File processExcel(MultipartFile file) throws IOException {
    Workbook workbook = new XSSFWorkbook(file.getInputStream());
    Sheet sheet = workbook.getSheetAt(0); // Assuming first sheet

    Iterator<Row> rows = sheet.iterator();
    Map<String, String> keyLanguageMap = new HashMap<>();

    // Assuming key column is the first column and language column is the second column
    rows.next();
    while (rows.hasNext()) {
      Row currentRow = rows.next();
      String key = null;
      if (currentRow.getCell(0) != null) {
        key = currentRow.getCell(0).getStringCellValue();
      }
      String language = null;
      if (key != null && !key.isEmpty() && !key.isBlank()) {
        language = getCellValueAsString(currentRow.getCell(7), currentRow.getCell(1));

        keyLanguageMap.put(key, language);
      }
    }

    // Convert map to JSON
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
    String jsonOutput = objectMapper.writeValueAsString(keyLanguageMap);

    File outputFile = new File("french_translations.json");
    try (FileWriter writer = new FileWriter(outputFile)) {
      writer.write(jsonOutput);
    }

    workbook.close();
    return outputFile;
  }

  public String getCellValueAsString(Cell cell, Cell defaultCell) {
    if (cell == null) {
      return ""; // Or handle null cells as needed
    }

    switch (cell.getCellType()) {
      case NUMERIC:
        // Convert numeric value to string
        DataFormatter dataFormatter = new DataFormatter();
        return dataFormatter.formatCellValue(cell);
      case STRING:
        if (cell.getStringCellValue().isEmpty()) return defaultCell.getStringCellValue();
        else return cell.getStringCellValue().replace("\"", "");
      case BOOLEAN:
        return Boolean.toString(cell.getBooleanCellValue());
      case FORMULA:
        // Evaluate formula and retrieve result as string
        Workbook workbook = cell.getSheet().getWorkbook();
        FormulaEvaluator formulaEvaluator = workbook.getCreationHelper().createFormulaEvaluator();
        CellValue cellValue = formulaEvaluator.evaluate(cell);
        return cellValue.formatAsString().replace("\"", "");
      default:
        return ""; // Handle other cell types as needed
    }
  }
}
