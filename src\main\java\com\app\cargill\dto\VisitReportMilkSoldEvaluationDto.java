/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.LinkedHashMap;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportMilkSoldEvaluationDto {
  private LinkedHashMap<String, String> herdLevelInformationLeftTable;
  private LinkedHashMap<String, String> herdLevelInformationRightTable;
  private LinkedHashMap<String, String> outputRightTable;
  private LinkedHashMap<String, String> outputLeftTable;
  private List<LinkedHashMap<String, List<VisitReportColumnValueDto>>> milkProcessorInformation;
  private List<MilkSoldEvaluationReportDto> graphs;
  private List<NotesDto> notes;
}
