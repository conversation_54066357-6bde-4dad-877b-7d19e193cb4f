/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LocomotionScorePenAnalysisReportDto extends BaseDto {

  private String fileName;
  private String standardDeviationLabel;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String analysisType;
  private String averageLabel;
  private String penName;
  private Double average;
  private Double standardDeviation;

  @Builder.Default
  private List<LocomotionScorePenAnalysisCategoryDto> categories = new ArrayList<>();
}
