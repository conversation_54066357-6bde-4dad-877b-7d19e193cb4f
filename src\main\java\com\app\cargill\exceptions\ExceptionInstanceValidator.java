/* Cargill Inc.(C) 2022 */
package com.app.cargill.exceptions;

import java.io.IOException;
import java.net.UnknownHostException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@SuppressWarnings("java:S1118")
public class ExceptionInstanceValidator {

  public static boolean is5xxServerError(Throwable throwable) {
    return throwable instanceof WebClientResponseException exception
        && exception.getStatusCode().is5xxServerError();
  }

  public static boolean isUnknownHostExceptionError(Throwable throwable) {
    return throwable instanceof UnknownHostException;
  }

  public static boolean isIOException(Throwable throwable) {
    return throwable instanceof IOException;
  }
}
