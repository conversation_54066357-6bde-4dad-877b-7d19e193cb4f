/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.data;

import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Notes;
import com.app.cargill.model.Pens;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MergeSitesService {

  private final PensRepository pensRepository;
  private final VisitsRepository visitsRepository;
  private final NotesRepository notesRepository;
  private final SitesRepository sitesRepository;
  private final DietRepository dietRepository;
  private final SiteMappingsRepository siteMappingsRepository;
  private int count = 0;

  public Map<String, List<String>> transferSitesData(UUID source, UUID target) {

    if (sitesRepository.findBySiteId(source.toString()) == null) {
      throw new MergeDataException(String.format("Source Site not found: %1$s", source));
    }

    if (sitesRepository.findBySiteId(target.toString()) == null) {
      throw new MergeDataException(String.format("Target Site not found: %1$s", target));
    }

    Map<String, List<String>> result = initResult(source, target);

    List<Pens> pens = pensRepository.findBySiteId(source.toString());
    for (Pens pen : pens) {
      pen.getPenDocument().setSiteId(target);
      pensRepository.save(pen);
      result.get("pens").add(pen.getPenDocument().getId().toString());
    }

    List<Visits> visits = visitsRepository.findVisitsBySiteId(source.toString());
    for (Visits visit : visits) {
      visit.getVisitDocument().setSiteId(target);
      visitsRepository.save(visit);
      result.get("visits").add(visit.getVisitDocument().getId().toString());
    }

    List<Notes> notes = notesRepository.findByNotesBySiteId(source.toString());
    for (Notes note : notes) {
      note.getNotesDocument().setSiteId(target);
      notesRepository.save(note);
      result.get("notes").add(note.getNotesDocument().getId().toString());
    }
    return result;
  }

  public Map<String, List<String>> transferDietFromOneSiteToAnother(
      UUID maxSiteId, UUID oldSiteId) {

    List<SiteMappings> siteMappings = siteMappingsRepository.findByMaxSiteId(maxSiteId.toString());
    if (siteMappings.isEmpty()) {
      throw new NotFoundDEException("SiteMapping not found against max site Id");
    }
    SiteMappings siteMapping = siteMappings.get(0);

    Map<String, List<String>> result = new HashMap<>();

    List<Diets> diets = dietRepository.findBySiteId(oldSiteId.toString());
    if (diets.isEmpty()) {
      throw new NotFoundDEException("No Diets found against old site Id");
    }
    diets.stream()
        .forEach(
            diet -> {
              count++;
              diet.getDietDocument()
                  .setSiteId(siteMapping.getSiteMappingDocument().getLabyrinthSiteId());
              result.put("Diet", List.of(diet.getDietDocument().getId().toString()));
              diet.getDietDocument()
                  .setLabyrinthAccountId(
                      siteMapping.getSiteMappingDocument().getLabyrinthAccountId());
              dietRepository.save(diet);

              List<Pens> pens =
                  pensRepository.findByDietId(diet.getDietDocument().getId().toString());

              if (!pens.isEmpty()) {
                pens.stream()
                    .forEach(
                        pen -> {
                          pen.getPenDocument().setDietId(null);
                          pen.getPenDocument().setOptimizationId(null);
                          pen.getPenDocument().setOptimizationType(null);
                        });

                pensRepository.saveAll(pens);

                result.put(
                    "Pens",
                    pens.stream()
                        .filter(
                            pen ->
                                pen.getPenDocument() != null
                                    && pen.getPenDocument().getId() != null)
                        .map(pen -> pen.getPenDocument().getId().toString())
                        .toList());
              }
            });

    log.info("All diets fixed, total count: {}", count);
    return result;
  }

  private Map<String, List<String>> initResult(UUID source, UUID target) {
    Map<String, List<String>> result = new LinkedHashMap<>();
    result.put("source", List.of(source.toString()));
    result.put("target", List.of(target.toString()));
    result.put("pens", new ArrayList<>());
    result.put("visits", new ArrayList<>());
    result.put("notes", new ArrayList<>());
    return result;
  }
}
