/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.FastByteArrayOutputStream;

/**
 * Utility for making deep copies (vs. clone()'s shallow copies) of objects. Objects are first
 * serialized and then deserialized. Error checking is fairly minimal in this implementation. If an
 * object is encountered that cannot be serialized (or that references an object that cannot be
 * serialized) an error is printed to System.err and null is returned. Depending on your specific
 * application, it might make more sense to have copy(...) re-throw the exception.
 */
@Slf4j
public class DeepCopy {

  private DeepCopy() {}

  /** Returns a copy of the object, or null if the object cannot be serialized. */
  public static Object copy(Object orig) {
    Object obj = null;
    try (FastByteArrayOutputStream fbos = new FastByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(fbos)) {
      // Write the object out to a byte array

      out.writeObject(orig);
      out.flush();

      // Retrieve an input stream from the byte array and read
      // a copy of the object back in.
      ObjectInputStream in = new ObjectInputStream(fbos.getInputStream());
      obj = in.readObject();
    } catch (Exception e) {
      log.error("DEEP_COPY_ERROR", e);
    }
    return obj;
  }
}
