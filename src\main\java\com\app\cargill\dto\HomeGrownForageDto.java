/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.HomeGrownForageTypes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomeGrownForageDto implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private HomeGrownForageTypes homeGrownForageType;
  private String forageName;
  private Double totalHerdPerDay;
  private Double dryMatter;
  private Double totalDryMatter;
  private Double pricePerTon; // From API call
}
