/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.LabyrinthContentType;
import com.app.cargill.constants.MediaTypes;
import com.app.cargill.constants.VisitReportType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = false)
public class NoteMediaItemDto implements Serializable {

  private UUID mediaId;
  private UUID noteId;
  private MediaTypes mediaType;
  private Instant createUtc;
  private String createUserId;
  private Double latitude;
  private Double longitude;
  private Integer attachmentMediaSequence;
  private LabyrinthContentType labyrinthContentType;
  private String mediaName;
  private VisitReportType reportType;
  private Boolean isNew;
  private String lastModifyUser;
  private String base64EncodedImage;
}
