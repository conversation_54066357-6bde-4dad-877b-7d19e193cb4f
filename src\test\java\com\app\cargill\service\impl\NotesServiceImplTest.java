/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.NoteCategoryType;
import com.app.cargill.document.*;
import com.app.cargill.dto.NoteMediaItemDto;
import com.app.cargill.dto.NotesDto;
import com.app.cargill.dto.NotesSearchDto;
import com.app.cargill.model.Notes;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.IUserService;
import com.app.cargill.utils.PageableUtil;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class NotesServiceImplTest {
  @Mock private IUserService userServiceImpl;
  @Mock private NotesRepository notesRepository;
  @Mock private VisitsRepository visitsRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private AccountsRepository accountsRepository;
  @InjectMocks private NotesServiceImpl notesServiceimpl;

  @Test
  void whenGetAllNotesReturnSuccess() {

    Page<Notes> notes =
        new PageImpl<>(
            List.of(
                Notes.builder()
                    .id((long) 12)
                    .updatedDate(Date.from(Instant.ofEpochSecond(**********)))
                    .createdDate(Date.from(Instant.ofEpochSecond(**********)))
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .note("test ")
                            .title("test")
                            .mediaItems(
                                List.of(
                                    NoteMediaItem.builder()
                                        .noteId(UUID.randomUUID())
                                        .noteId(UUID.randomUUID())
                                        .build()))
                            .build())
                    .build()));
    when(userServiceImpl.getCurrentLoggedInUserAsJsonObj()).thenReturn("[\"test user\"]");
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("<EMAIL>");

    when(notesRepository.findByAccountIdAndUpdatedDate(any(), any(), any(), any()))
        .thenReturn(notes);
    Page<NotesDto> result =
        notesServiceimpl.getAllNotesPaginated(
            0,
            10,
            "id",
            "desc",
            Instant.now(),
            userServiceImpl.getCurrentLoggedInUserAsJsonObj(),
            userServiceImpl.getCurrentLoggedInUser());
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenGetAllNotesInOnlineModeReturnSuccess() {

    Page<Notes> notes =
        new PageImpl<>(
            List.of(
                Notes.builder()
                    .id((long) 12)
                    .updatedDate(Date.from(Instant.ofEpochSecond(**********)))
                    .createdDate(Date.from(Instant.ofEpochSecond(**********)))
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .note("test ")
                            .title("test")
                            .mediaItems(
                                List.of(
                                    NoteMediaItem.builder()
                                        .noteId(UUID.randomUUID())
                                        .noteId(UUID.randomUUID())
                                        .build()))
                            .build())
                    .build()));
    NotesSearchDto notesSearchDto =
        NotesSearchDto.builder()
            .tools(List.of("CalfHeiferScorecard"))
            .accountIds(List.of(UUID.randomUUID().toString()))
            .creationDateFrom(Instant.now())
            .updatedDateFrom(Instant.now())
            .creationDateTo(Instant.now())
            .updatedDateTo(Instant.now())
            .noteCategoryTypes(List.of(NoteCategoryType.Event.name()))
            .siteIds(List.of(UUID.randomUUID().toString()))
            .visitIds(List.of(UUID.randomUUID().toString()))
            .accountIds(List.of(UUID.randomUUID().toString()))
            .title("test")
            .build();
    Pageable pageable = PageableUtil.getPageable(0, 10, "id", "desc");
    when(notesRepository.findAll(notesServiceimpl.setSpecifications(notesSearchDto), pageable))
        .thenReturn(notes);

    Page<NotesDto> result =
        notesServiceimpl.getAllNotesOnlinePaginated(0, 10, "id", "desc", notesSearchDto);
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenGetAllNotesInOnlineModeWithNoFiltersReturnSuccess() {

    Page<Notes> notes =
        new PageImpl<>(
            List.of(
                Notes.builder()
                    .id((long) 12)
                    .updatedDate(Date.from(Instant.ofEpochSecond(**********)))
                    .createdDate(Date.from(Instant.ofEpochSecond(**********)))
                    .notesDocument(
                        NotesDocument.builder()
                            .id(UUID.randomUUID())
                            .note("test ")
                            .title("test")
                            .mediaItems(
                                List.of(
                                    NoteMediaItem.builder()
                                        .noteId(UUID.randomUUID())
                                        .noteId(UUID.randomUUID())
                                        .build()))
                            .build())
                    .build()));
    NotesSearchDto notesSearchDto = NotesSearchDto.builder().build();
    Pageable pageable = PageableUtil.getPageable(0, 10, "id", "desc");
    when(notesRepository.findAll(notesServiceimpl.setSpecifications(notesSearchDto), pageable))
        .thenReturn(notes);

    Page<NotesDto> result =
        notesServiceimpl.getAllNotesOnlinePaginated(0, 10, "id", "desc", notesSearchDto);
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenSaveReturnSuccessResponse() {
    NotesDto input =
        NotesDto.builder()
            .note("test ")
            .localId(UUID.randomUUID().toString())
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    Notes output =
        Notes.builder()
            .updatedDate(Date.from(Instant.ofEpochSecond(**********)))
            .createdDate(Date.from(Instant.ofEpochSecond(**********)))
            .localId(UUID.randomUUID().toString())
            .notesDocument(
                NotesDocument.builder()
                    .id(UUID.randomUUID())
                    .note("test ")
                    .title("test")
                    .mediaItems(
                        List.of(
                            NoteMediaItem.builder()
                                .noteId(UUID.randomUUID())
                                .noteId(UUID.randomUUID())
                                .build()))
                    .build())
            .build();
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("test user");
    when(notesRepository.existsByLocalId(any())).thenReturn(false);
    when(notesRepository.save(any())).thenReturn(output);
    NotesDto result = notesServiceimpl.save(input, userServiceImpl.getCurrentLoggedInUser());
    assertNotNull(result.getId());
  }

  @Test
  void whenUpdateReturnSuccessResponse() {
    Notes foundRecord =
        Notes.builder()
            .updatedDate(Date.from(Instant.ofEpochSecond(**********)))
            .createdDate(Date.from(Instant.ofEpochSecond(**********)))
            .localId(UUID.randomUUID().toString())
            .notesDocument(
                NotesDocument.builder()
                    .id(UUID.randomUUID())
                    .note("test ")
                    .title("test")
                    .mediaItems(
                        List.of(
                            NoteMediaItem.builder()
                                .noteId(UUID.randomUUID())
                                .noteId(UUID.randomUUID())
                                .build()))
                    .build())
            .build();
    NotesDto output =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .localId(UUID.randomUUID().toString())
            .title("testUpdate")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("test user");
    when(notesRepository.findByNoteId(any())).thenReturn(foundRecord);
    when(notesRepository.save(any())).thenReturn(foundRecord);
    NotesDto result = notesServiceimpl.update(output, userServiceImpl.getCurrentLoggedInUser());
    assertTrue(result.getTitle().contentEquals("testUpdate"));
  }

  @Test
  void whenUpsertReturnSuccessResponse() {
    NotesDto outputCreate =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .localId(UUID.randomUUID().toString())
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    NotesDto inputCreate =
        NotesDto.builder()
            .note("test ")
            .localId(UUID.randomUUID().toString())
            .title("test")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();

    Notes foundRecord =
        Notes.builder()
            .updatedDate(Date.from(Instant.ofEpochSecond(**********)))
            .createdDate(Date.from(Instant.ofEpochSecond(**********)))
            .localId(UUID.randomUUID().toString())
            .notesDocument(
                NotesDocument.builder()
                    .id(UUID.randomUUID())
                    .note("test ")
                    .title("test")
                    .mediaItems(
                        List.of(
                            NoteMediaItem.builder()
                                .noteId(UUID.randomUUID())
                                .noteId(UUID.randomUUID())
                                .build()))
                    .build())
            .build();
    NotesDto output =
        NotesDto.builder()
            .id(UUID.randomUUID())
            .note("test ")
            .localId(UUID.randomUUID().toString())
            .title("testUpdate")
            .mediaItems(
                List.of(
                    NoteMediaItemDto.builder()
                        .noteId(UUID.randomUUID())
                        .noteId(UUID.randomUUID())
                        .build()))
            .build();
    when(userServiceImpl.getCurrentLoggedInUser()).thenReturn("test user");
    when(notesRepository.save(any())).thenReturn(foundRecord);
    // when(notesServiceimpl.save(inputCreate, any())).thenReturn(outputCreate);
    when(notesRepository.findByNoteId(any())).thenReturn(foundRecord);
    when(notesServiceimpl.update(output, any())).thenReturn(output);
    List<NotesDto> upsert =
        notesServiceimpl.updateOrInsert(
            List.of(inputCreate, output), userServiceImpl.getCurrentLoggedInUser());
    assertEquals(2, upsert.size());
  }
}
