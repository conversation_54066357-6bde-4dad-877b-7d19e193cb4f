/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Business;
import com.app.cargill.cosmos.model.UserCosmos;
import com.app.cargill.cosmos.repo.UsersCosmosRepository;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class UsersMigrationTest {

  @Mock private UsersCosmosRepository usersCosmosRepository;

  @Mock private UserRepository postgresUsersRepository;

  @InjectMocks private UsersMigration usersMigration;

  @BeforeEach
  void setUp() {
    lenient().when(postgresUsersRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenUsersMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    UserCosmos user =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/users.json"), UserCosmos.class);

    Iterable<UserCosmos> usersList = List.of(user, user);
    when(usersCosmosRepository.findAll()).thenReturn(usersList);

    MigrationResult result = usersMigration.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  //  @Test
  //  void whenPostMigrationIsCalledCorrectResponseIsReturned() {
  //
  //    MigrationResult result = null;
  //    try {
  //      result =
  // usersMigration.postMigration(CosmosDataMigration.MigrationType.USER.name()).get();
  //    } catch (InterruptedException | ExecutionException e) {
  //      e.printStackTrace();
  //    }
  //    assertNotNull(result);
  //  }

  @Test
  void whenUsersMigrationHasExceptionFailuresReturn()
      throws IOException, ExecutionException, InterruptedException {
    UserCosmos item1 = new UserCosmos();
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    UserCosmos item2 =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/users.json"), UserCosmos.class);

    Iterable<UserCosmos> itemsList = List.of(item1, item2);
    when(usersCosmosRepository.findAll()).thenReturn(itemsList);

    MigrationResult result = usersMigration.moveAll().get();
    assertEquals(1, result.getSucceeded());
    assertEquals(1, result.getFailed());
  }

  @Test
  void whenUserIsMigratedAllIsOk() {
    String email = "<EMAIL>";

    UserCosmos userCosmos = mock(UserCosmos.class);
    when(userCosmos.getId()).thenReturn(UUID.randomUUID().toString());
    when(userCosmos.getSalesforceCountryId()).thenReturn(0);
    when(userCosmos.getCountryId()).thenReturn(Business.Global);
    when(userCosmos.getUserName()).thenReturn(email);
    when(postgresUsersRepository.findByUserName(email)).thenReturn(null);
    when(usersCosmosRepository.getUsersByEmail(email)).thenReturn(List.of(userCosmos));
    when(postgresUsersRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    StepVerifier.create(usersMigration.moveRecord(email))
        .assertNext(
            user -> {
              assertNotNull(user);
              assertEquals(email, user.getUserDocument().getUserName());
              assertNotNull(user.getUserDocument().getId());
            })
        .verifyComplete();
  }

  @Test
  void whenThereIsDbErrorExceptionIsReturned() {
    String email = "<EMAIL>";
    when(postgresUsersRepository.findByUserName(email))
        .thenThrow(new IllegalArgumentException("Error saving in db"));
    StepVerifier.create(usersMigration.moveRecord(email))
        .expectErrorMatches(
            p ->
                p instanceof MigrationException
                    && p.getMessage().contains("There was an error getting record from the DB"))
        .verify();
  }

  @Test
  void whenThereIsNoSuchRecordInCosmosReturnException() {
    String email = "<EMAIL>";
    when(postgresUsersRepository.findByUserName(email)).thenReturn(null);
    when(usersCosmosRepository.getUsersByEmail(email)).thenReturn(List.of());
    StepVerifier.create(usersMigration.moveRecord(email))
        .expectErrorMatches(
            p -> p instanceof MigrationException && p.getMessage().contains("User not found"))
        .verify();
  }

  @Test
  void whenThereAreMultipleRecordsInCosmosReturnException() {
    String email = "<EMAIL>";
    when(postgresUsersRepository.findByUserName(email)).thenReturn(null);
    when(usersCosmosRepository.getUsersByEmail(email))
        .thenReturn(List.of(mock(UserCosmos.class), mock(UserCosmos.class)));
    StepVerifier.create(usersMigration.moveRecord(email))
        .expectErrorMatches(
            p ->
                p instanceof MigrationException
                    && p.getMessage().contains("More than one record found"))
        .verify();
  }

  @Test
  void whenUserAlreadyExistsReturnTheResult() {
    String email = "<EMAIL>";
    User mockedUser = mock(User.class);
    when(postgresUsersRepository.findByUserName(email)).thenReturn(mockedUser);
    StepVerifier.create(usersMigration.moveRecord(email))
        .assertNext(user -> assertEquals(mockedUser, user))
        .verifyComplete();
  }

  @Test
  void whenUserIsMigratedHandleCountryId() {
    String email = "<EMAIL>";

    UserCosmos userCosmos = mock(UserCosmos.class);
    when(userCosmos.getId()).thenReturn(UUID.randomUUID().toString());
    when(userCosmos.getSalesforceCountryId()).thenReturn(0);
    when(userCosmos.getCountryId()).thenReturn(Business.CPNBrazil);
    when(userCosmos.getUserName()).thenReturn(email);
    when(postgresUsersRepository.findByUserName(email)).thenReturn(null);
    when(usersCosmosRepository.getUsersByEmail(email)).thenReturn(List.of(userCosmos));
    when(postgresUsersRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    StepVerifier.create(usersMigration.moveRecord(email))
        .assertNext(
            user -> {
              assertNotNull(user);
              assertEquals(email, user.getUserDocument().getUserName());
              assertNotNull(user.getUserDocument().getId());
            })
        .verifyComplete();
  }
}
