/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.app.cargill.service.ILiftRetriggerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class LiftRetriggerControllerTest {

  @Mock private ILiftRetriggerService liftRetriggerService;

  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;

  @InjectMocks private LiftRetriggerController liftRetriggerController;

  private MockMvc mockMvc;

  private ObjectMapper objectMapper = new ObjectMapper();

  @BeforeEach
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    mockMvc = MockMvcBuilders.standaloneSetup(liftRetriggerController).build();
  }

  @Test
  void testRetriggerAccounts() throws Exception {
    List<String> accountIds = Arrays.asList("account1", "account2");

    mockMvc
        .perform(
            post("/lift/retrigger/accounts")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Accept-Language", "en")
                .content(objectMapper.writeValueAsString(accountIds)))
        .andExpect(status().isOk());
  }

  @Test
  void testRetriggerSites() throws Exception {
    List<String> siteIds = Arrays.asList("site1", "site2");

    mockMvc
        .perform(
            post("/lift/retrigger/sites")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Accept-Language", "en")
                .content(objectMapper.writeValueAsString(siteIds)))
        .andExpect(status().isOk());
  }

  @Test
  void testRetriggerEvents() throws Exception {
    List<String> visitIds = Arrays.asList("visit1", "visit2");

    mockMvc
        .perform(
            post("/lift/retrigger/events")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Accept-Language", "en")
                .content(objectMapper.writeValueAsString(visitIds)))
        .andExpect(status().isOk());
  }
}
