version: "1"

stages:
  build:
    steps:
    - name: build-pr
      image: markhobson/maven-chrome:jdk-19
      ruleset:
        event: [ pull_request ]
      commands:
        - mvn clean install -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn
        - mvn spotless:check -B

    - name: build-push
      image: markhobson/maven-chrome:jdk-19
      ruleset:
        if:
          branch: [ develop, stage, main ]
          event: [ push, tag ]
      commands:
        - mvn clean install -B -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn
        - mvn spotless:check -B

    - name: copy-dependencies-pr
      image: markhobson/maven-chrome:jdk-19
      ruleset:
        event: [ pull_request ]
      commands:
        - mvn dependency:copy-dependencies

    - name: copy-dependencies-push
      image: markhobson/maven-chrome:jdk-19
      ruleset:
        if:
          branch: [ develop ]
          event: [ push ]
      commands:
        - mvn dependency:copy-dependencies

    - name: sonar-qube-pr
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/sonarqube-plugin:1-stable
      ruleset:
        event: [ pull_request ]
      parameters:
        exclusions: src/test/**, src/main/java/com/app/cargill/authconfig/**,src/main/java/com/app/cargill/interceptors/**
        sonar_args:
          - sonar.java.binaries=target/classes
          - sonar.java.libraries=target/dependency
          - sonar.java.source=19
          - sonar.tests=src/test
        sources: .
        team: dairyenteligenfoliothree

    - name: sonar-qube-push
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/sonarqube-plugin:1-stable
      ruleset:
        if:
          branch: [ develop ]
          event: [ push ]
      parameters:
        exclusions: src/test/**,docs/**
        sonar_args:
          - sonar.java.binaries=target/classes
          - sonar.java.libraries=target/dependency
          - sonar.java.source=19
          - sonar.tests=src/test
        sources: .
        team: dairyenteligenfoliothree

    - name: build-push-docker-pr
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
      ruleset:
        event: [ pull_request ]
      parameters:
        repo: dairyenteligen-service-api
        sysdig_cli_scanner: true
        skip_existing: true
        tags: '${VELA_BUILD_COMMIT:0:7}-pr'

    - name: cleanup-pr-docker
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
      ruleset:
        if:
          event: [pull_request]
      parameters:
        delete: true
        repo: dairyenteligen-service-api
        tags: '${VELA_BUILD_COMMIT:0:7}-pr'

    - name: build-push-docker-dev
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
      ruleset:
        if:
          branch: [develop]
          event: [push]
      parameters:
        repo: dairyenteligen-service-api
        sysdig_cli_scanner: true
        skip_existing: false
        tags:
          - 'latest-dev'
          - '${VELA_BUILD_COMMIT:0:7}-dev'

    - name: build-push-docker-stage
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
      ruleset:
        if:
          branch: [stage]
          event: [push]
      parameters:
        repo: dairyenteligen-service-api
        sysdig_cli_scanner: true
        skip_existing: false
        tags:
          - 'latest-stage'
          - '${VELA_BUILD_COMMIT:0:7}-stage'

    - name: build-push-docker-prod
      image: docker-utilities.binrepo.cglcloud.in/enablementtools/docker-plugin:3-stable
      ruleset:
        if:
          event: [push]
          branch: [main]
      parameters:
        repo: dairyenteligen-service-api
        sysdig_cli_scanner: true
        skip_existing: false
        tags:
          - 'latest-prod'
          - '${VELA_BUILD_COMMIT:0:7}-prod'

    - name: captain-deploy-dev
      image: docker-utilities.binrepo.cglcloud.in/captain:1-stable
      ruleset:
        if:
          event: [ push ]
          branch: [ develop ]
      parameters:
        env: dev
        run_apply: true
        version: '${VELA_BUILD_COMMIT:0:7}-dev'

    - name: captain-deploy-stage
      image: docker-utilities.binrepo.cglcloud.in/captain:1-stable
      ruleset:
        if:
          event: [ push ]
          branch: [ stage ]
      parameters:
        env: stage
        run_apply: true
        version: '${VELA_BUILD_COMMIT:0:7}-stage'

    - name: captain-deploy-prod
      image: docker-utilities.binrepo.cglcloud.in/captain:1-stable
      ruleset:
        if:
          event: [ push ]
          branch: [ main ]
      parameters:
        env: prod
        run_apply: true
        version: '${VELA_BUILD_COMMIT:0:7}-prod'

    

    - name: ms-teams-build-notify
      image: docker.binrepo.cglcloud.in/enablementtools/msteams-plugin:1-stable
      ruleset:
        if:
          status: [success, failure]
      parameters:
        team_hook: https://cargillonline.webhook.office.com/webhookb2/3be3a631-150c-4949-86c4-a1752a19d975@57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/IncomingWebhook/1bad3a72c071468b8be3b472e16238fc/2241c66e-2848-41e5-a067-9c704f5b25cd/V2Yg7m1NCZnoqXekPTFNBAKVQqii-7Umy7fXY-kBsfdvE1
        user_message: Build Complete. Click link above to view results.

#  # [START deploy]
#  dairyapi-deploy-dev:
#    needs:
#      - build
#      - scan
#    steps:
#      - name:   dairyapi-deploy-dev
#        image: docker-utilities.binrepo.cglcloud.in/captain:1.22.9-rc1
#        ruleset:
#          event: [push, tag]
#          branch: [develop]
#        parameters:
#          captain_file: .captain.yml
#          run_apply: true
#          env: 'dev'
#          version: "${VELA_BUILD_COMMIT:0:7}"
#
#  dairyapi-deploy-stage:
#    needs: build
#    steps:
#      - name:  dairyapi-deploy-stage
#        image: docker-utilities.binrepo.cglcloud.in/captain:1.22.9-rc1
#        ruleset:
#          operator: and
#          matcher: regexp
#          if:
#            event: [push, tag]
#            branch: [stage]
#        parameters:
#          captain_file: .captain.yml
#          run_apply: true
#          env: 'stage'
#          version: "stage-${VELA_BUILD_COMMIT:0:7}"
#
#  dairyapi-deploy-prod:
#    needs: build
#    steps:
#      - name:  dairyapi-deploy-prod
#        image: docker-utilities.binrepo.cglcloud.in/captain:1.22.9-rc1
#        ruleset:
#          operator: and
#          matcher: regexp
#          if:
#            event: [push, tag]
#            branch: [master]
#        parameters:
#          captain_file: .captain.yml
#          run_apply: true
#          env: 'prod'
#          version: "prod-${VELA_BUILD_COMMIT:0:7}"
#  # [END deploy]

secrets:
  - name: veracode_user
    key: TGRC-AppSec/Veracode/VERACODE_ID
    engine: native
    type: shared

  - name: veracode_pass
    key: TGRC-AppSec/Veracode/VERACODE_KEY
    engine: native
    type: shared