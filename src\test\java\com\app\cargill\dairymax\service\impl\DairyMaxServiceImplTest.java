/* Cargill Inc.(C) 2022 */
package com.app.cargill.dairymax.service.impl;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.Nutrients;
import com.app.cargill.constants.SortOrder;
import com.app.cargill.dairymax.constants.AnalyzeStatusMax;
import com.app.cargill.dairymax.constants.FormulateStatusMax;
import com.app.cargill.dairymax.model.AnalyzeDietOptimizationMax;
import com.app.cargill.dairymax.model.DairyMax;
import com.app.cargill.dairymax.model.FormulateDietOptimizationMax;
import com.app.cargill.dairymax.model.MaxDiet;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.DietDocument;
import com.app.cargill.document.DietOptimizationNutrient;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.IPensService;
import com.app.cargill.service.ISiteService;
import com.app.cargill.service.data.MergeSitesService;
import com.app.cargill.utils.PageableUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
class DairyMaxServiceImplTest {

  @InjectMocks private DairyMaxServiceImpl dairyMaxService;
  @Mock private SiteMappingsRepository siteMappingsRepository;
  @Mock private AccountsRepository accountsRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private DietRepository dietRepository;
  @Mock private PensRepository penRepository;
  @Mock private IPensService pensService;
  @Mock private ISiteService siteService;
  @Mock private MergeSitesService mergeSitesService;

  //  @Test
  //  void getData() throws CustomDEExceptions {
  //    Date date = new Date();
  //    List<Sites> sites = new ArrayList<>();
  //    List<Pens> pens = new ArrayList<>();
  //    List<Diets> diets = new ArrayList<>();
  //
  //    List<String> siteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    for (int s = 0; s < 3; s++) {
  //      sites.add(
  //          Sites.builder()
  //              .siteDocument(SiteDocument.builder().id(UUID.fromString(siteIds.get(s))).build())
  //              .updatedDate(Date.from(Instant.now()))
  //              .createdDate(Date.from(Instant.now()))
  //              .build());
  //      pens.add(
  //          Pens.builder()
  //
  // .penDocument(PenDocument.builder().siteId(UUID.fromString(siteIds.get(s))).build())
  //              .build());
  //      diets.add(
  //          Diets.builder()
  //
  // .dietDocument(DietDocument.builder().siteId(UUID.fromString(siteIds.get(s))).build())
  //              .build());
  //    }
  //    pens.get(0).getPenDocument().setDietId(diets.get(0).getDietDocument().getId());
  //    diets.get(0).getDietDocument().setIsSystemGenerated(true);
  //    diets.get(1).getDietDocument().setSource(DietSource.MAX);
  //    diets
  //        .get(1)
  //        .getDietDocument()
  //        .setFormulateOptimization(
  //            FormulateDietOptimization.builder().status(FormulateStatus.FEASIBLE).build());
  //    diets
  //        .get(1)
  //        .getDietDocument()
  //        .setAnalyzeOptimization(
  //            AnalyzeDietOptimization.builder()
  //                .asFedAmount(0.0)
  //                .dryMatterAmount(0.0)
  //                .status(AnalyzeStatus.NA)
  //                .build());
  //    diets.get(2).getDietDocument().setSource(DietSource.USER_CREATED);
  //    diets
  //        .get(2)
  //        .getDietDocument()
  //        .setFormulateOptimization(
  //            FormulateDietOptimization.builder().status(FormulateStatus.FEASIBLE).build());
  //
  //    when(sitesRepository.findByUpdatedDate(date)).thenReturn(sites);
  //    when(penRepository.findBySiteIds(siteIds)).thenReturn(pens);
  //    when(dietRepository.findBySiteIds(siteIds)).thenReturn(diets);
  //
  //    Assertions.assertNotNull(dairyMaxService.getSitesToMax(date));
  //  }

  @Test
  void postData() {
    List<String> maxSiteIds =
        List.of(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString());
    List<String> siteIds =
        List.of(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString());
    List<String> dietIds =
        List.of(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString());
    List<DataSourceMapping> dataSourceMappings = getDietSiteMappingsForPost(maxSiteIds);
    List<SiteMappings> dbSiteMappings = getSiteMappingsForPost(maxSiteIds, siteIds);
    List<Diets> dbDiets = getDietsForPost(siteIds);
    dbDiets.get(0).getDietDocument().setId(UUID.fromString(dietIds.get(0)));
    List<Sites> sites = getSitesForPost(siteIds);
    List<DairyMax> dairyMaxList =
        getDairyMaxListForPost(dataSourceMappings, dietIds, maxSiteIds.size());

    Pageable pageable = PageableUtil.getPageable(0, 1, "created_date", SortOrder.ASCENDING);
    when(siteMappingsRepository.findByDairyMaxSiteId(maxSiteIds.get(0), pageable))
        .thenReturn(List.of(dbSiteMappings.get(0)));
    when(accountsRepository.findByAccountId(
            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId().toString()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder()
                        .id(dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId())
                        .build())
                .build());
    when(sitesRepository.findBySiteId(
            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthSiteId().toString()))
        .thenReturn(sites.get(0));
    when(dietRepository.findAllBySiteId(siteIds.get(0))).thenReturn(List.of(dbDiets.get(0)));
    when(penRepository.findBySiteId(siteIds.get(0)))
        .thenReturn(List.of(Pens.builder().penDocument(PenDocument.builder().build()).build()));

    Assertions.assertDoesNotThrow(() -> dairyMaxService.updateSiteFromMax(dairyMaxList));
  }

  @Test
  void postWithInvalidData() {
    List<DairyMax> dairyMaxList =
        List.of(
            DairyMax.builder().maxOrigination(null).build(),
            DairyMax.builder().maxOrigination("test").build(),
            DairyMax.builder()
                .maxOrigination(DietSource.MAX.name())
                .maxDataSourceMappings(null)
                .build(),
            DairyMax.builder()
                .maxOrigination(DietSource.MAX.name())
                .maxDataSourceMappings(new ArrayList<>())
                .build(),
            DairyMax.builder()
                .maxOrigination(DietSource.MAX.name())
                .maxDataSourceMappings(List.of(DataSourceMapping.builder().systemId(null).build()))
                .build(),
            DairyMax.builder()
                .maxOrigination(DietSource.MAX.name())
                .maxDataSourceMappings(List.of(DataSourceMapping.builder().systemId("").build()))
                .build());

    Assertions.assertDoesNotThrow(() -> dairyMaxService.updateSiteFromMax(dairyMaxList));
  }

  //  @Test
  //  void postWithCustomExceptionsAtAccount() {
  //    List<String> maxSiteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<String> siteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<String> dietIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<SiteMapping> siteMappings = getDietSiteMappingsForPost(maxSiteIds);
  //    List<SiteMappings> dbSiteMappings = getSiteMappingsForPost(maxSiteIds, siteIds);
  //    List<DairyMax> dairyMaxList = getDairyMaxListForPost(siteMappings, dietIds,
  // maxSiteIds.size());
  //
  //    Pageable pageable = PageableUtil.getPageable(0, 1, "created_date", SortOrder.ASCENDING);
  //    when(siteMappingsRepository.findByDairyMaxSiteId(maxSiteIds.get(0), pageable))
  //        .thenReturn(List.of(dbSiteMappings.get(0)));
  //    when(accountsRepository.findByAccountId(
  //            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId().toString()))
  //        .thenReturn(null);
  //    Assertions.assertThrows(
  //        CustomDEExceptions.class, () -> dairyMaxService.updateSiteFromMax(dairyMaxList));
  //  }
  //
  //  @Test
  //  void postWithCustomExceptionsAtDeleteDiets() {
  //    List<String> maxSiteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<String> siteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<String> dietIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<SiteMapping> siteMappings = getDietSiteMappingsForPost(maxSiteIds);
  //    List<SiteMappings> dbSiteMappings = getSiteMappingsForPost(maxSiteIds, siteIds);
  //    // List<Diets> dbDiets = getDietsForPost(siteIds);
  //    List<Sites> sites = getSitesForPost(siteIds);
  //    List<DairyMax> dairyMaxList = getDairyMaxListForPost(siteMappings, dietIds,
  // maxSiteIds.size());
  //
  //    Pageable pageable = PageableUtil.getPageable(0, 1, "created_date", SortOrder.ASCENDING);
  //    when(siteMappingsRepository.findByDairyMaxSiteId(maxSiteIds.get(0), pageable))
  //        .thenReturn(List.of(dbSiteMappings.get(0)));
  //    when(accountsRepository.findByAccountId(
  //            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId().toString()))
  //        .thenReturn(
  //            Accounts.builder()
  //                .accountDocument(
  //                    AccountDocument.builder()
  //
  // .id(dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId())
  //                        .build())
  //                .build());
  //    when(sitesRepository.findBySiteId(
  //            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthSiteId().toString()))
  //        .thenReturn(sites.get(0));
  //    when(dietRepository.findBySiteId(siteIds.get(0))).thenReturn(null);
  //
  //    Assertions.assertThrows(
  //        CustomDEExceptions.class, () -> dairyMaxService.updateSiteFromMax(dairyMaxList));
  //  }
  //
  //  @Test
  //  void postWithCustomExceptionsAtProcessDiets() {
  //    List<String> maxSiteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<String> siteIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<String> dietIds =
  //        List.of(
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString(),
  //            UUID.randomUUID().toString());
  //    List<SiteMapping> siteMappings = getDietSiteMappingsForPost(maxSiteIds);
  //    List<SiteMappings> dbSiteMappings = getSiteMappingsForPost(maxSiteIds, siteIds);
  //    List<Diets> dbDiets = getDietsForPost(siteIds);
  //    List<Sites> sites = getSitesForPost(siteIds);
  //    List<DairyMax> dairyMaxList = getDairyMaxListForPost(siteMappings, dietIds,
  // maxSiteIds.size());
  //
  //    Pageable pageable = PageableUtil.getPageable(0, 1, "created_date", SortOrder.ASCENDING);
  //    when(siteMappingsRepository.findByDairyMaxSiteId(maxSiteIds.get(0), pageable))
  //        .thenReturn(List.of(dbSiteMappings.get(0)));
  //    when(accountsRepository.findByAccountId(
  //            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId().toString()))
  //        .thenReturn(
  //            Accounts.builder()
  //                .accountDocument(
  //                    AccountDocument.builder()
  //
  // .id(dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId())
  //                        .build())
  //                .build());
  //    when(sitesRepository.findBySiteId(
  //            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthSiteId().toString()))
  //        .thenReturn(sites.get(0));
  //    when(dietRepository.findBySiteId(siteIds.get(0))).thenReturn(List.of(dbDiets.get(0)));
  //    when(sitesRepository.findBySiteId(
  //            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthSiteId().toString()))
  //        .thenReturn(null);
  //
  //    Assertions.assertThrows(
  //        CustomDEExceptions.class, () -> dairyMaxService.updateSiteFromMax(dairyMaxList));
  //  }

  @Test
  void postWithDeletingDietsAndPens() {
    List<String> maxSiteIds = List.of(UUID.randomUUID().toString());
    List<String> siteIds = List.of(UUID.randomUUID().toString());
    List<String> dietIds =
        List.of(
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString());
    List<DataSourceMapping> dataSourceMappings = getDietSiteMappingsForPost(maxSiteIds);
    List<SiteMappings> dbSiteMappings = getSiteMappingsForPost(maxSiteIds, siteIds);
    List<Diets> dbDiets = getDietsForPost(siteIds);
    dbDiets.get(0).getDietDocument().setId(UUID.fromString(dietIds.get(0)));
    dbDiets.add(
        Diets.builder()
            .dietDocument(
                DietDocument.builder()
                    .id(UUID.randomUUID())
                    .siteId(UUID.fromString(siteIds.get(0)))
                    .source(DietSource.MAX)
                    .build())
            .build());
    List<Sites> sites = getSitesForPost(siteIds);
    List<DairyMax> dairyMaxList =
        getDairyMaxListForPost(dataSourceMappings, dietIds, maxSiteIds.size());
    List<Pens> pens =
        List.of(
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .dietId(UUID.fromString(dietIds.get(0)))
                        .siteId(UUID.fromString(siteIds.get(0)))
                        .build())
                .build(),
            Pens.builder()
                .penDocument(
                    PenDocument.builder()
                        .dietId(UUID.fromString(dietIds.get(1)))
                        .siteId(UUID.fromString(siteIds.get(0)))
                        .build())
                .build());

    Pageable pageable = PageableUtil.getPageable(0, 1, "created_date", SortOrder.ASCENDING);
    when(siteMappingsRepository.findByDairyMaxSiteId(maxSiteIds.get(0), pageable))
        .thenReturn(List.of(dbSiteMappings.get(0)));
    when(dietRepository.findById(anyString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(DietDocument.builder().siteId(UUID.randomUUID()).build())
                .build());
    when(accountsRepository.findByAccountId(
            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId().toString()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder()
                        .id(dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthAccountId())
                        .build())
                .build());
    when(sitesRepository.findBySiteId(
            dbSiteMappings.get(0).getSiteMappingDocument().getLabyrinthSiteId().toString()))
        .thenReturn(sites.get(0));
    when(dietRepository.findAllBySiteId(siteIds.get(0))).thenReturn(dbDiets);
    when(penRepository.findBySiteId(siteIds.get(0))).thenReturn(pens);

    Assertions.assertDoesNotThrow(() -> dairyMaxService.updateSiteFromMax(dairyMaxList));
  }

  private List<SiteMappings> getSiteMappingsForPost(List<String> maxSiteIds, List<String> siteIds) {
    List<SiteMappings> dbSiteMappings = new ArrayList<>();
    for (int i = 0; i < maxSiteIds.size(); i++) {
      dbSiteMappings.add(
          SiteMappings.builder()
              .siteMappingDocument(
                  SiteMappingDocument.builder()
                      .id(UUID.fromString(maxSiteIds.get(i)))
                      .labyrinthAccountId(UUID.randomUUID())
                      .labyrinthSiteId(UUID.fromString(siteIds.get(i)))
                      .maxSiteId(UUID.fromString(maxSiteIds.get(i)))
                      .build())
              .build());
    }
    return dbSiteMappings;
  }

  private List<Diets> getDietsForPost(List<String> siteIds) {
    List<Diets> dbDiets = new ArrayList<>();
    for (String siteId : siteIds) {
      dbDiets.add(
          Diets.builder()
              .dietDocument(
                  DietDocument.builder()
                      .siteId(UUID.fromString(siteId))
                      .source(DietSource.MAX)
                      .build())
              .build());
    }
    return dbDiets;
  }

  private List<Sites> getSitesForPost(List<String> siteIds) {
    List<Sites> sites = new ArrayList<>();
    for (String siteId : siteIds) {
      sites.add(
          Sites.builder()
              .siteDocument(SiteDocument.builder().id(UUID.fromString(siteId)).build())
              .build());
    }
    return sites;
  }

  private List<DataSourceMapping> getDietSiteMappingsForPost(List<String> maxSiteIds) {
    List<DataSourceMapping> mappings = new ArrayList<>();
    for (String siteId : maxSiteIds) {
      mappings.add(DataSourceMapping.builder().systemId(siteId).build());
    }
    return mappings;
  }

  private List<DairyMax> getDairyMaxListForPost(
      List<DataSourceMapping> dataSourceMappings, List<String> dietIds, int size) {
    List<DairyMax> dairyMaxList = new ArrayList<>();
    for (int i = 0; i < size; i++) {
      dairyMaxList.add(
          DairyMax.builder()
              .maxOrigination(DietSource.MAX.name())
              .maxDataSourceMappings(List.of(dataSourceMappings.get(i)))
              .maxId(UUID.fromString(dietIds.get(i)))
              .maxDiets(
                  List.of(
                      MaxDiet.builder()
                          .maxId(UUID.fromString(dietIds.get(i)))
                          .maxAnalyzeOptimization(
                              AnalyzeDietOptimizationMax.builder()
                                  .nutrients(
                                      List.of(
                                          DietOptimizationNutrient.builder()
                                              .nutrientSpeciesId("123")
                                              .build(),
                                          DietOptimizationNutrient.builder()
                                              .nutrientSpeciesId(
                                                  Nutrients.DEFAULT_NEL_DAI_KG.getNutrientValue())
                                              .build()))
                                  .status(AnalyzeStatusMax.Analyzed)
                                  .build())
                          .maxFormulateOptimization(
                              FormulateDietOptimizationMax.builder()
                                  .nutrients(
                                      List.of(
                                          DietOptimizationNutrient.builder()
                                              .nutrientSpeciesId("123")
                                              .build(),
                                          DietOptimizationNutrient.builder()
                                              .nutrientSpeciesId(
                                                  Nutrients.DEFAULT_NEL_DAI_KG.getNutrientValue())
                                              .build()))
                                  .status(FormulateStatusMax.Feasible)
                                  .build())
                          .build()))
              .build());
    }
    return dairyMaxList;
  }
}
