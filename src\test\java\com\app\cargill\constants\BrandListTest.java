/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class BrandListTest {

  @Test
  void test() {
    UserSettingsBrands brandsItaly = UserSettingsBrands.valueOf("Agridea");
    assertEquals(UserSettingsBrands.Agridea, brandsItaly);
  }

  @Test
  void testFromId() {
    UserSettingsBrands brandsItaly = UserSettingsBrands.fromId(11);
    assertEquals(UserSettingsBrands.RaggioDiSole, brandsItaly);
  }
}
