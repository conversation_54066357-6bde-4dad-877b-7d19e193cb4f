/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.CityDto;
import com.app.cargill.dto.CountryDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.StateDto;
import com.app.cargill.service.IGeoLocationService;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class GeoLocationControllerTest {

  @Mock private IGeoLocationService geoLocationService;

  @InjectMocks private GeoLocationController controller;
  @Mock private static ResourceBundleMessageSource resourceBundleMessageSource;

  @BeforeAll
  static void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
  }

  @Test
  void fetchAllCountries() {
    when(geoLocationService.fetchAllCountries(any(), any())).thenReturn(generateCountries());
    ResponseEntity<ResponseEntityDto<List<CountryDto>>> result = controller.fetchAllCountries("EN");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void fetchAllCountriesPaginated() {
    when(geoLocationService.fetchAllCountriesPaginated(
            anyInt(), anyInt(), any(), any(), any(), any(), any()))
        .thenReturn(generateCountriesPage());
    ResponseEntity<ResponseEntityDto<Page<CountryDto>>> result =
        controller.fetchAllCountriesPaginated(1, 10, "sb", Instant.now(), "s", "EN");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  @Test
  void fetchStates() {
    when(geoLocationService.fetchStateByCountryCode(any(), any(), any()))
        .thenReturn(generateStates());
    ResponseEntity<ResponseEntityDto<List<StateDto>>> result = controller.fetchStates("cc", "EN");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void fetchStatesPaginated() {
    when(geoLocationService.fetchStateByCountryCodePaginated(
            any(), anyInt(), anyInt(), any(), any(), any(), any(), any()))
        .thenReturn(generateStatesPage());
    ResponseEntity<ResponseEntityDto<Page<StateDto>>> result =
        controller.fetchStatesPaginated("cc", 1, 10, "sb", Instant.now(), "s", "EN");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  @Test
  void fetchStatesByCountryCodesPaginated() {
    when(geoLocationService.fetchStatesByCountryCodesPaginated(
            any(), anyInt(), anyInt(), any(), any(), any(), any(), any()))
        .thenReturn(generateStatesPage());
    ResponseEntity<ResponseEntityDto<Page<StateDto>>> result =
        controller.fetchStatesByCountryCodesPaginated(
            Arrays.asList("AU"), 1, 10, "sb", Instant.now(), "s", "EN");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  @Test
  void fetchCities() {
    when(geoLocationService.fetchCitiesByStateCodeAndCountryCode(any(), any()))
        .thenReturn(generateCities());
    ResponseEntity<ResponseEntityDto<List<CityDto>>> result = controller.fetchCities("sc", "cc");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void fetchCitiesPaginated() {
    when(geoLocationService.fetchCitiesByStateCodeAndCountryCodePaginated(
            any(), any(), anyInt(), anyInt(), any(), any(), any()))
        .thenReturn(generateCitiesPage());
    ResponseEntity<ResponseEntityDto<Page<CityDto>>> result =
        controller.fetchCitiesPaginated("sc", "cc", 1, 10, "sb", Instant.now(), "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  private List<CountryDto> generateCountries() {
    return List.of(CountryDto.builder().build(), CountryDto.builder().build());
  }

  private Page<CountryDto> generateCountriesPage() {
    return new PageImpl<>(generateCountries());
  }

  private List<StateDto> generateStates() {
    return List.of(StateDto.builder().build(), StateDto.builder().build());
  }

  private Page<StateDto> generateStatesPage() {
    return new PageImpl<>(generateStates());
  }

  private List<CityDto> generateCities() {
    return List.of(CityDto.builder().build(), CityDto.builder().build());
  }

  private Page<CityDto> generateCitiesPage() {
    return new PageImpl<>(generateCities());
  }
}
