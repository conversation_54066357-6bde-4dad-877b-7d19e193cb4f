/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.ActivityType;
import com.app.cargill.constants.NameType;
import com.app.cargill.constants.RelatedToType;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.EventDocument;
import com.app.cargill.document.ReportType;
import com.app.cargill.document.UserRole;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.ILiftRetriggerService;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import com.app.cargill.sf.cc.service.LiftUserAccessService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

@Service("liftRetriggerServiceImpl")
@RequiredArgsConstructor
public class LiftRetriggerServiceImpl implements ILiftRetriggerService {

  private static final Logger logger = LoggerFactory.getLogger(LiftRetriggerServiceImpl.class);

  private final AccountsRepository accountsRepository;
  private final LiftAccountService liftAccountService;
  private final LiftUserService liftUserService;
  private final LiftSiteMappingsService liftSiteMappingsService;
  private final LiftSitesService liftSitesService;
  private final SiteMappingsRepository siteMappingsRepository;
  private final SitesRepository sitesRepository;
  private final SiteMappingServiceImpl siteMappingServiceImpl;
  private final VisitsRepository visitsRepository;
  private final UserServiceImpl userServiceImpl;
  private final ActivitiesRepository activitiesRepository;
  private final LiftEventService liftEventService;
  private final LiftContactService liftContactService;
  private static final String ACCOUNT_REPRESENTATIVE = "Account Representative";
  private static final String CHATTER_FREE = "Chatter Free";
  private final LiftUserAccessService liftUserAccessService;
  private Boolean isExistUserRoles = false;

  @Override
  public List<String> retriggerAccounts(
      List<String> accountIds, Locale locale, ResourceBundleMessageSource source) throws Exception {
    List<String> success = new ArrayList<>();

    for (String accountId : accountIds) {
      Accounts account = accountsRepository.findByAccountId(accountId);
      try {

        if (account == null || account.getAccountDocument() == null) {
          logger.warn("Account or AccountDocument is null for accountId");
          throw new NotFoundDEException("Account is Null or Account Document is null");
        }

        accountsRepository.deleteById(account.getId());
        if (DataSource.LIFT.equals(account.getAccountDocument().getDataSource())) {
          User user = liftUserService.findUserByEmail(account.getAccountDocument().getOwnerId());
          logger.info("User: {}", user);
          if (user != null) {
            liftAccountService.createAccount(account.getAccountDocument(), locale, source);
            account.getAccountDocument().setOwnerId(user.getEmail());
            if (account.getAccountDocument().getContacts() != null
                && !account.getAccountDocument().getContacts().isEmpty()) {
              account
                  .getAccountDocument()
                  .getContacts()
                  .forEach(
                      contact ->
                          contact.setGoldenRecordAcountId(
                              account.getAccountDocument().getGoldenRecordId() != null
                                  ? account.getAccountDocument().getGoldenRecordId()
                                  : null));
              account
                  .getAccountDocument()
                  .setContacts(
                      liftContactService.saveToLift(
                          account.getAccountDocument().getContacts(), locale, source));
            }
            User liftUser =
                liftUserService.findUserByEmail(account.getAccountDocument().getCreateUser());
            if (!Objects.isNull(liftUser)
                && liftUser
                    .getProfile()
                    .getUserLicense()
                    .getName()
                    .equalsIgnoreCase(CHATTER_FREE)) {
              if (account.getAccountDocument().getUserRoles() != null) {
                account.getAccountDocument().getUserRoles().stream()
                    .forEach(
                        userRole -> {
                          if (userRole
                              .getUserName()
                              .equalsIgnoreCase(account.getAccountDocument().getCreateUser())) {
                            isExistUserRoles = true;
                          }
                        });
              }
              ;
              if (Boolean.FALSE.equals(isExistUserRoles)) {
                account
                    .getAccountDocument()
                    .setUserRoles(
                        createUserRoles(
                            account.getAccountDocument().getUserRoles(),
                            account.getAccountDocument().getCreateUser()));
              }
              liftUserAccessService.createUserAccess(
                  liftUser.getId(),
                  account.getAccountDocument().getGoldenRecordId(),
                  ACCOUNT_REPRESENTATIVE);
              accountsRepository.save(account);
            }
          } else {
            logger.warn("User not found for email: {}", account.getAccountDocument().getOwnerId());
          }
        }

        account.getAccountDocument().setNeedsSync(false);
        success.add(accountId);
        accountsRepository.save(account);
        logger.info("Account Saved");
      } catch (JsonProcessingException | CustomDEExceptions e) {
        logger.error("Error processing accountId: {} - {}", e.getMessage(), e);
      }
    }

    return success;
  }

  public List<UserRole> createUserRoles(List<UserRole> userRolesList, String currentLoggedInUser) {

    List<UserRole> userRoles = new ArrayList<>();
    for (UserRole userRole : userRolesList) {
      userRoles.add(userRole);
    }
    UserRole userRole =
        UserRole.builder().roleType(ACCOUNT_REPRESENTATIVE).userName(currentLoggedInUser).build();
    userRoles.add(userRole);
    return userRoles;
  }

  @Override
  public List<String> retriggerSites(
      List<String> siteIds, Locale locale, ResourceBundleMessageSource source) {
    List<String> success = new ArrayList<>();

    for (String siteId : siteIds) {
      try {
        Sites site = sitesRepository.findBySiteId(siteId);
        if (site == null || site.getSiteDocument() == null) {
          logger.warn("Site or SiteDocument is null for siteId");
          throw new NotFoundDEException("Site is null");
        }

        Accounts account =
            accountsRepository.findByAccountId(site.getSiteDocument().getAccountId().toString());
        if (account == null || account.getAccountDocument() == null) {
          logger.warn("Account or AccountDocument is null for siteId");
          throw new NotFoundDEException("Account or Account document is null");
        }

        if (DataSource.LIFT.equals(account.getAccountDocument().getDataSource())) {
          site.getSiteDocument()
              .setExternalAccountId(account.getAccountDocument().getGoldenRecordId());
          site.getSiteDocument()
              .setExternalId(liftSitesService.createSite(site.getSiteDocument(), locale, source));
          sitesRepository.save(site);

          SiteMappings siteMapping = siteMappingsRepository.findBySiteId(siteId);
          if (Objects.isNull(siteMapping)) {
            if (Objects.nonNull(site.getSiteDocument().getDataSourceMappings())) {
              siteMappingServiceImpl.createLiftSiteMapping(site);
            }
            liftSiteMappingsService.createSiteMapping(site.getSiteDocument(), locale, source);
          } else {
            liftSiteMappingsService.createSiteMapping(site.getSiteDocument(), locale, source);
          }

          success.add(siteId);
        }
      } catch (JsonProcessingException | CustomDEExceptions e) {
        logger.error("Error processing siteId: {} - {}", e.getMessage(), e);
      }
    }

    return success;
  }

  @Override
  public List<String> retriggerEvents(
      List<String> visitIds,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource) {

    List<String> success = new ArrayList<>();
    visitIds.stream()
        .forEach(
            visitId -> {
              Visits visit = visitsRepository.findByVisitId(visitId);

              Accounts account =
                  accountsRepository.findByAccountId(
                      visit.getVisitDocument().getCustomerId().toString());

              if (account == null) {
                throw new NotFoundDEException("AccountId Not found");
              }
              String sfdcAccountId = account.getAccountDocument().getGoldenRecordId();
              UUID externalRelatedListId = account.getAccountDocument().getId();
              List<UUID> externalNameListId = new ArrayList<>();
              List<String> sfdcContactId = new ArrayList<>();
              if (account.getAccountDocument().getContacts() != null) {
                for (Contact contact : account.getAccountDocument().getContacts()) {
                  externalNameListId.add(contact.getContactId());
                  sfdcContactId.add(contact.getSFDCContactId());
                }
              }
              Sites site =
                  sitesRepository.findBySiteId(visit.getVisitDocument().getSiteId().toString());
              if (site == null) {
                throw new NotFoundDEException("SiteId Not found");
              }

              ActivityDocument activityDocument =
                  setActivityDocumentDetails(
                      visit,
                      account,
                      sfdcAccountId,
                      sfdcContactId,
                      externalNameListId,
                      externalRelatedListId);

              Activities activity = Activities.builder().activityDocument(activityDocument).build();
              activity = activitiesRepository.save(activity);
              EventDocument eventDocument = setEventDocumentDetail(activityDocument, visit, site);

              eventDocument.setReportLinkC(getReportLink(visit.getVisitDocument()));

              Boolean isSuccess =
                  saveEventToLift(eventDocument, visit, locale, resourceBundleMessageSource);

              if (Boolean.TRUE.equals(isSuccess)) {
                success.add(visitId);
              }
            });

    return success;
  }

  private Boolean saveEventToLift(
      EventDocument eventDocument,
      Visits visit,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource) {

    User user = liftUserService.findOwner(visit.getVisitDocument().getCreateUser());

    eventDocument.setOwnerId(user.getId());

    Activities activity =
        activitiesRepository.findByDocumentIdAndDeleted(eventDocument.getDeActivityExternalIdC());

    try {
      liftEventService.createEvent(eventDocument, locale, resourceBundleMessageSource);
    } catch (JsonProcessingException | CustomDEExceptions e) {
      logger.error(e.getLocalizedMessage());
      return false;
    }
    activity.getActivityDocument().setEventSfdcId(eventDocument.getEventId());
    activity.getActivityDocument().setReportLink(eventDocument.getReportLinkC());
    activity.getActivityDocument().setOwnerId(eventDocument.getOwnerId());
    activitiesRepository.save(activity);

    return true;
  }

  private EventDocument setEventDocumentDetail(
      ActivityDocument activityDocument, Visits visit, Sites site) {
    return EventDocument.builder()
        .activityDateTime(
            activityDocument.getActivityDateTime() != null
                ? activityDocument.getActivityDateTime().toString()
                : null)
        .businessC(null)
        .deActivityExternalIdC(
            activityDocument.getId() != null
                ? activityDocument.getId().toString()
                : UUID.randomUUID().toString())
        .deSiteVisitIdC(
            visit.getVisitDocument().getId() != null
                ? visit.getVisitDocument().getId().toString()
                : UUID.randomUUID().toString())
        .endDateTime(
            activityDocument.getEndDateTime() != null
                ? activityDocument.getEndDateTime().toString()
                : null)
        .isAllDayEvent(false)
        .isReminderSet(true)
        .whatId(
            activityDocument.getSfdcAccountId() != null
                ? activityDocument.getSfdcAccountId()
                : null)
        .deSiteId(site != null ? site.getSiteDocument().getExternalId() : null)
        .startDateTime(
            activityDocument.getActivityDateTime() != null
                ? activityDocument.getActivityDateTime().toString()
                : null)
        .subject(activityDocument.getSubject())
        .typeC("Dairy Site Visit")
        .build();
  }

  private ActivityDocument setActivityDocumentDetails(
      Visits visit,
      Accounts account,
      String sfdcAccountId,
      List<String> sfdcContactId,
      List<UUID> externalNameListId,
      UUID externalRelatedListId) {

    return ActivityDocument.builder()
        .id(UUID.randomUUID())
        .visitId(visit.getVisitDocument().getId())
        .sfdcAccountId(sfdcAccountId)
        .name(visit.getVisitDocument().getVisitName())
        .subject(
            visit.getVisitDocument().getVisitDate() != null
                ? "Site Visit - "
                    + visit.getVisitDocument().getVisitDate()
                    + " - "
                    + visit.getVisitDocument().getVisitName()
                : "Site Visit - " + Instant.now() + " - " + visit.getVisitDocument().getVisitName())
        .needsSync(true)
        .nameListId(CollectionUtils.isNotEmpty(sfdcContactId) ? sfdcContactId.get(0) : null)
        .relatedToTypeID(RelatedToType.Account.ordinal())
        .nameTypeID(NameType.Contact.ordinal())
        .relatedToListId(sfdcAccountId)
        .externalNameListId(
            CollectionUtils.isNotEmpty(externalNameListId) ? externalNameListId.get(0) : null)
        .externalRelatedToListId(externalRelatedListId)
        .accountId(visit.getVisitDocument().getCustomerId())
        .eventStartDate(
            visit.getVisitDocument().getVisitDate() != null
                ? visit.getVisitDocument().getVisitDate()
                : Instant.now())
        .endDateTime(
            visit.getVisitDocument().getMobileLastUpdatedTime() != null
                ? visit.getVisitDocument().getMobileLastUpdatedTime()
                : Instant.now())
        .activityType(ActivityType.Event.getValue())
        .lastModifiedTimeUtc(Instant.now())
        .lastSyncTimeUtc(Instant.now())
        .activityDateTime(
            visit.getVisitDocument().getVisitDate() != null
                ? visit.getVisitDocument().getVisitDate()
                : Instant.now())
        .createTimeUtc(
            visit.getCreatedDate() != null ? visit.getCreatedDate().toInstant() : Instant.now())
        .createUser(
            visit.getVisitDocument().getCreateUser() != null
                ? visit.getVisitDocument().getCreateUser()
                : userServiceImpl.getCurrentLoggedInUser())
        .lastModifyUser(
            visit.getVisitDocument().getLastModifyUser() != null
                ? visit.getVisitDocument().getLastModifyUser()
                : userServiceImpl.getCurrentLoggedInUser())
        .isNew(true)
        .assignedToID(
            (visit.getVisitDocument().getCreateUser() != null
                ? visit.getVisitDocument().getCreateUser()
                : userServiceImpl.getCurrentLoggedInUser()))
        .dataSource(account.getAccountDocument().getDataSource())
        .build();
  }

  public String getReportLink(VisitDocument visitDocument) {
    if (!Objects.isNull(visitDocument.getReportType())
        && !visitDocument.getReportType().isEmpty()) {
      List<ReportType> reportType =
          visitDocument.getReportType().stream().filter(report -> report.getUrl() != null).toList();
      if (!reportType.isEmpty()) {
        return reportType.get(0).getUrl();
      }
    }
    return null;
  }
}
