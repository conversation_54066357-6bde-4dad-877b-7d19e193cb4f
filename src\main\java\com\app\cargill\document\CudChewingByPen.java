/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CudChewingByPen implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("PenId")
  public UUID penId;

  @JsonProperty("PenName")
  public String penName;

  @JsonProperty("CountYes")
  @Builder.Default
  public Integer countYes = 0;

  @Builder.Default
  @JsonProperty("CountNo")
  public Integer countNo = 0;

  @JsonProperty("YesPercent")
  public Double yesPercent;

  @JsonProperty("NoPercent")
  public Double noPercent;
}
