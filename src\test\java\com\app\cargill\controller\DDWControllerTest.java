/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.controller.external.DDWController;
import com.app.cargill.ddw.model.HerdData;
import com.app.cargill.ddw.model.HerdPeriod;
import com.app.cargill.ddw.service.IHerdService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class DDWControllerTest {
  @InjectMocks private DDWController controller;

  @Mock private IHerdService herdService;

  @Test
  void updateDataFromDDWControllerSuccess() {

    List<HerdData> herdDataList = new ArrayList<>();

    HerdPeriod period =
        new HerdPeriod(new Date(), "********", "IT-049", "12156", "3", new Date(), "********");

    herdDataList.add(HerdData.builder().period(period).build());

    Mockito.when(herdService.updateHerds(herdDataList))
        .thenReturn(
            List.of(
                "Staging DDW Herd Profile 12156",
                "herd profile 12156 was applied to account e2642c64-3578-4c52-ba03-b15aeaac088f."));

    ResponseEntity<List<String>> response = controller.post(herdDataList);
    assertEquals("Staging DDW Herd Profile 12156", response.getBody().get(0));
  }
}
