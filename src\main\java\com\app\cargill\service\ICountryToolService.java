/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.constants.Business;
import com.app.cargill.dto.CountryToolDto;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;

public interface ICountryToolService {

  Page<CountryToolDto> getAllCountryToolsByCurrentLoggedInUserPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime);

  List<CountryToolDto> save(List<CountryToolDto> countryToolDto);

  CountryToolDto deleteById(UUID id);

  List<CountryToolDto> deleteByCountryId(Business countryId);
}
