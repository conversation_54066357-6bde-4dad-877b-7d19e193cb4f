/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum AccountStatusCrescendo {
  NEW("New"),
  ON_HOLD("On Hold"),
  IN_PROGRESS("In Progress"),
  APPROVED("Approved"),
  REJECTED("Rejected"),
  RECALLED("Recalled"),

  PENDING("Pending"),
  CONVERSION_IN_PROGRESS("Conversion In progress"),
  FOLLOW_UP_NEEDED("Follow Up Needed"),
  DRAFT("Draft"),
  COMPLETE("Complete");

  private final String value;

  @JsonCreator
  AccountStatusCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
