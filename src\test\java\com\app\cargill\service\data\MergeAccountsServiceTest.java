/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.data;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.NotesDocument;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Notes;
import com.app.cargill.model.Pens;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MergeAccountsServiceTest {

  @Mock private AccountsRepository accountsRepository;
  @Mock private SitesRepository sitesRepository;
  @Mock private PensRepository pensRepository;
  @Mock private VisitsRepository visitsRepository;
  @Mock private NotesRepository notesRepository;
  @Mock private LiftAccountService liftAccountService;
  @Mock private SiteMappingsRepository siteMappingsRepository;
  @Mock private LiftSiteMappingsService liftSiteMappingsService;

  @InjectMocks private MergeAccountsService mergeAccountsService;

  @Test
  void whenTransferIsInvokedAllPasses() {
    UUID source = UUID.randomUUID();
    UUID target = UUID.randomUUID();

    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(source);
    accountDocument.setGoldenRecordId("test");

    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(UUID.randomUUID());
    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(UUID.randomUUID());

    PenDocument penDocument1 = new PenDocument();
    penDocument1.setId(UUID.randomUUID());
    PenDocument penDocument2 = new PenDocument();
    penDocument2.setId(UUID.randomUUID());

    VisitDocument visitDocument1 = new VisitDocument();
    visitDocument1.setId(UUID.randomUUID());
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setId(UUID.randomUUID());

    NotesDocument notesDocument1 = new NotesDocument();
    notesDocument1.setId(UUID.randomUUID());
    NotesDocument notesDocument2 = new NotesDocument();
    notesDocument2.setId(UUID.randomUUID());

    when(accountsRepository.findAllByGoldenRecordId(any()))
        .thenReturn(List.of(new Accounts(accountDocument)));
    when(sitesRepository.findAllByAccountId(any()))
        .thenReturn(List.of(new Sites(siteDocument1), new Sites(siteDocument2)));
    when(pensRepository.findByCustomerAccountIds(anyList()))
        .thenReturn(List.of(new Pens(penDocument1), new Pens(penDocument2)));
    when(visitsRepository.findAllByAccountIds(anyList()))
        .thenReturn(List.of(new Visits(visitDocument1), new Visits(visitDocument2)));
    when(notesRepository.findAllByAccountId(anyList()))
        .thenReturn(List.of(new Notes(notesDocument1), new Notes(notesDocument2)));
    when(accountsRepository.findByAccountId(any())).thenReturn(new Accounts(accountDocument));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(any()))
        .thenReturn(
            List.of(
                SiteMappings.builder()
                    .siteMappingDocument(SiteMappingDocument.builder().build())
                    .build()));
    Map<String, List<String>> result =
        mergeAccountsService.transferAccountData(source.toString(), target.toString());

    assertTrue(result.get("source").contains(source.toString()));
    assertTrue(result.get("target").contains(target.toString()));
    assertEquals(1, result.get("accounts").size());
    assertEquals(2, result.get("sites").size());
    assertEquals(2, result.get("pens").size());
    assertEquals(2, result.get("visits").size());
    assertEquals(2, result.get("notes").size());
  }

  @Test
  void whenTransferNotesAndVisitsIsInvokedAllPasses() {
    UUID source = UUID.randomUUID();
    UUID target = UUID.randomUUID();

    VisitDocument visitDocument1 = new VisitDocument();
    visitDocument1.setId(UUID.randomUUID());
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setId(UUID.randomUUID());

    NotesDocument notesDocument1 = new NotesDocument();
    notesDocument1.setId(UUID.randomUUID());
    NotesDocument notesDocument2 = new NotesDocument();
    notesDocument2.setId(UUID.randomUUID());

    when(visitsRepository.findAllByAccountIds(anyList()))
        .thenReturn(List.of(new Visits(visitDocument1), new Visits(visitDocument2)));
    when(notesRepository.findAllByAccountId(anyList()))
        .thenReturn(List.of(new Notes(notesDocument1), new Notes(notesDocument2)));

    Map<String, List<String>> result = mergeAccountsService.transferNotesAndVisits(source, target);

    assertTrue(result.get("source").contains(source.toString()));
    assertTrue(result.get("target").contains(target.toString()));
    assertEquals(0, result.get("accounts").size());
    assertEquals(0, result.get("sites").size());
    assertEquals(0, result.get("pens").size());
    assertEquals(2, result.get("visits").size());
    assertEquals(2, result.get("notes").size());
  }

  @Test
  void fixMergedAccountsSuccess() {
    UUID targetUuid = UUID.randomUUID();
    UUID mismatchedId = UUID.randomUUID();

    ExternalDataSource liftExternalDataSource = new ExternalDataSource();
    liftExternalDataSource.setUniqueExternalKey(mismatchedId.toString());
    liftExternalDataSource.setSystem("LM");
    AccountDocument liftAccount = new AccountDocument();
    liftAccount.setGoldenRecordId("record-id");
    liftAccount.setId(targetUuid);
    liftAccount.setApplicationMappings(List.of(liftExternalDataSource));

    AccountDocument dbAccountDocument1 = new AccountDocument();
    dbAccountDocument1.setId(targetUuid);
    dbAccountDocument1.setGoldenRecordId("test2");
    Accounts dbAccount1 = new Accounts(dbAccountDocument1);

    AccountDocument dbAccountDocument2 = new AccountDocument();
    dbAccountDocument2.setId(mismatchedId);
    dbAccountDocument2.setGoldenRecordId("test");
    Accounts dbAccount2 = new Accounts(dbAccountDocument2);

    when(liftAccountService.getAllAccounts(anyList())).thenReturn(List.of(liftAccount));
    when(accountsRepository.findAllByGoldenRecordId("record-id"))
        .thenReturn(List.of(dbAccount1, dbAccount2));

    List<Map<String, List<String>>> result = mergeAccountsService.fixMergedAccounts("record-id");
    assertFalse(result.isEmpty());
    assertEquals("test2", result.get(0).get("target").get(0));
    assertEquals("test2", result.get(0).get("source").get(0));
  }

  @Test
  void whenLiftAccountNotFoundNothingIsUpdated() {
    when(liftAccountService.getAllAccounts(anyList())).thenReturn(new ArrayList<>());
    List<Map<String, List<String>>> result = mergeAccountsService.fixMergedAccounts("record-id");
    assertTrue(result.isEmpty());
  }

  @Test
  void whenDbAccountsNotFoundNothingIsUpdated() {
    UUID targetUuid = UUID.randomUUID();
    UUID mismatchedId = UUID.randomUUID();

    ExternalDataSource liftExternalDataSource = new ExternalDataSource();
    liftExternalDataSource.setUniqueExternalKey(mismatchedId.toString());
    liftExternalDataSource.setSystem("LM");
    AccountDocument liftAccount = new AccountDocument();
    liftAccount.setGoldenRecordId("record-id");
    liftAccount.setId(targetUuid);
    liftAccount.setApplicationMappings(List.of(liftExternalDataSource));

    when(liftAccountService.getAllAccounts(anyList())).thenReturn(List.of(liftAccount));
    when(accountsRepository.findAllByGoldenRecordId(any())).thenReturn(List.of());
    List<Map<String, List<String>>> result = mergeAccountsService.fixMergedAccounts("record-id");
    assertTrue(result.isEmpty());
  }

  @Test
  void whenLmMappingIsMissingExceptionIsThrown() {
    UUID targetUuid = UUID.randomUUID();
    UUID mismatchedId = UUID.randomUUID();

    AccountDocument liftAccount = new AccountDocument();
    liftAccount.setGoldenRecordId("record-id");
    liftAccount.setId(targetUuid);
    liftAccount.setApplicationMappings(List.of());

    AccountDocument dbAccountDocument1 = new AccountDocument();
    dbAccountDocument1.setId(targetUuid);
    dbAccountDocument1.setGoldenRecordId("test");
    Accounts dbAccount1 = new Accounts(dbAccountDocument1);

    AccountDocument dbAccountDocument2 = new AccountDocument();
    dbAccountDocument2.setGoldenRecordId("test2");
    dbAccountDocument2.setId(mismatchedId);
    Accounts dbAccount2 = new Accounts(dbAccountDocument2);

    when(liftAccountService.getAllAccounts(anyList())).thenReturn(List.of(liftAccount));
    when(accountsRepository.findAllByGoldenRecordId("record-id"))
        .thenReturn(List.of(dbAccount1, dbAccount2));

    assertThrows(
        MergeDataException.class, () -> mergeAccountsService.fixMergedAccounts("record-id"));
  }

  @Test
  void whenMultipleLmMappingsAreFoundExceptionIsThrown() {
    UUID targetUuid = UUID.randomUUID();
    UUID mismatchedId = UUID.randomUUID();

    ExternalDataSource liftExternalDataSource = new ExternalDataSource();
    liftExternalDataSource.setUniqueExternalKey(mismatchedId.toString());
    liftExternalDataSource.setSystem("LM");

    AccountDocument liftAccount = new AccountDocument();
    liftAccount.setGoldenRecordId("record-id");
    liftAccount.setId(targetUuid);
    liftAccount.setApplicationMappings(List.of(liftExternalDataSource, liftExternalDataSource));

    AccountDocument dbAccountDocument1 = new AccountDocument();
    dbAccountDocument1.setId(targetUuid);
    dbAccountDocument1.setGoldenRecordId("test");
    Accounts dbAccount1 = new Accounts(dbAccountDocument1);

    AccountDocument dbAccountDocument2 = new AccountDocument();
    dbAccountDocument2.setId(mismatchedId);
    dbAccountDocument2.setGoldenRecordId("test2");
    Accounts dbAccount2 = new Accounts(dbAccountDocument2);

    when(liftAccountService.getAllAccounts(anyList())).thenReturn(List.of(liftAccount));
    when(accountsRepository.findAllByGoldenRecordId("record-id"))
        .thenReturn(List.of(dbAccount1, dbAccount2));

    assertThrows(
        MergeDataException.class, () -> mergeAccountsService.fixMergedAccounts("record-id"));
  }
}
