/* Cargill Inc.(C) 2022 */
package com.app.cargill.schedulers;

import com.app.cargill.service.forecast.AccountRolesService;
import com.app.cargill.service.forecast.DairyForecastService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DairyForecastUserRolesScheduler {

  private boolean userRolesFirstRun = true;

  private final DairyForecastService dairyForecastService;
  private final AccountRolesService accountRolesService;

  // Every hour
  @Scheduled(fixedRate = 1000 * 60 * 60)
  public void updateDairyForecastRoles() {
    if (userRolesFirstRun) {
      log.debug("updateDairyForecastRoles disabled on first run");
      userRolesFirstRun = false;
    } else {
      dairyForecastService
          .syncAccountsRoles(accountRolesService.getAllAccountsRoles())
          .doOnNext(
              result -> log.debug("updateDairyForecastRoles completed. {} roles synced", result))
          .onErrorComplete(
              t -> {
                log.error("Error with updateDairyForecastRoles", t);
                return true;
              })
          .subscribe();
    }
  }
}
