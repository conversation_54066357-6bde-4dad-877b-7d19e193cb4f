{"encoding": "UTF-8", "maxBatchSize": 200, "sobjects": [{"activateable": false, "associateEntityType": null, "associateParentEntity": null, "createable": false, "custom": false, "customSetting": false, "deepCloneable": false, "deletable": false, "deprecatedAndHidden": false, "feedEnabled": false, "hasSubtypes": false, "isInterface": false, "isSubtype": false, "keyPrefix": "0Pp", "label": "AI Application", "labelPlural": "AI Applications", "layoutable": false, "mergeable": false, "mruEnabled": false, "name": "AIApplication", "queryable": true, "replicateable": false, "retrieveable": true, "searchable": false, "triggerable": false, "undeletable": false, "updateable": false, "urls": {"rowTemplate": "/services/data/v56.0/sobjects/AIApplication/{ID}", "describe": "/services/data/v56.0/sobjects/AIApplication/describe", "sobject": "/services/data/v56.0/sobjects/AIApplication"}}]}