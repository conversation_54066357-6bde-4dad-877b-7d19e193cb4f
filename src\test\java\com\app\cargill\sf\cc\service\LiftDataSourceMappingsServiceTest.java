/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.SiteDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.web.client.HttpClientErrorException;

@ExtendWith(MockitoExtension.class)
class LiftDataSourceMappingsServiceTest {
  @Mock private LiftApiService liftApiService;
  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private LiftSiteMappingsService siteMappingsService;

  @Test
  void whenDataIsCorrectSiteMappingIsCreated() throws Exception {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalAccountId("sf-account-id");
    siteDocument.setExternalId("sf-site-id");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApiService.getTokenAndApiPath())
        .thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApiService.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    String result = siteMappingsService.createSiteMapping(siteDocument, null, bundleMessageSource);
    assertEquals("new-id", result);
  }

  @Test
  void whenSiteIdIsMissingExceptionIsThrown() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setExternalAccountId("sf-account-id");
    siteDocument.setExternalId("sf-site-id");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApiService.getTokenAndApiPath())
        .thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        IllegalArgumentException.class,
        () -> siteMappingsService.createSiteMapping(siteDocument, null, bundleMessageSource));
  }

  @Test
  void whenExternalAccountIdIsMissingExceptionIsThrown() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("sf-site-id");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApiService.getTokenAndApiPath())
        .thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        IllegalArgumentException.class,
        () -> siteMappingsService.createSiteMapping(siteDocument, null, bundleMessageSource));
  }

  @Test
  void whenExternalSiteIdIsMissingExceptionIsThrown() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalAccountId("sf-account-id");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApiService.getTokenAndApiPath())
        .thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        IllegalArgumentException.class,
        () -> siteMappingsService.createSiteMapping(siteDocument, null, bundleMessageSource));
  }

  @Test
  void whenThereIsRequestErrorExceptionIsThrown()
      throws JsonProcessingException, CustomDEExceptions {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalAccountId("sf-account-id");
    siteDocument.setExternalId("sf-site-id");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApiService.getTokenAndApiPath())
        .thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApiService.createRecord(any(), any(), any(), any()))
        .thenThrow(new HttpClientErrorException(HttpStatus.BAD_REQUEST));
    assertThrows(
        CustomDEExceptions.class,
        () -> siteMappingsService.createSiteMapping(siteDocument, null, bundleMessageSource));
  }
}
