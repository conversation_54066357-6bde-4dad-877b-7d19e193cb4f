/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.MetabolicIncidenceGraphExportType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.LinkedHashMap;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MetabolicIncidenceReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private MetabolicIncidenceGraphExportType graphType;
  private String metabolicDisorderCostPerCowLabel;
  // below are for Metabolic Disorder Cost/Cow ({currency})
  @Builder.Default
  private LinkedHashMap<String, List<MetabolicDisorderCostPerCowDto>> metabolicDisorderCostPerCow =
      new LinkedHashMap<>();

  @JsonIgnore private MetabolicIncidenceExportImageReportDto metabolicIncidenceExportImageReportDto;
  // below are for Metabolic Incidence %
  @Builder.Default
  private LinkedHashMap<String, Double> incidencePercentage = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> goalPercentage = new LinkedHashMap<>();
  @JsonIgnore private String[] metabolicTypeKeys;
}
