<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="../../app/generatedTemplateResources/css/main.css" />
    <title>Cargil</title>
</head>
<body>
    <div class="evaluation-wrapper">
        <header>
            <a href="#" class="logo"><img src="../../app/generatedTemplateResources/images/cargill-logo.svg" alt="cargil-logo" /> </a>
        </header>
        <main class="main">
            <div class="main-header">
                <h3>${model.visitName!}</h3>
                <div class="main-header-row">
                    <div>
                        <p>${localization.getMessage("Report.Tool.Name", [], "Tool Name", locale)}:</p>
                        <strong>${model.toolName!}</strong>
                    </div>
                    <div>
                        <p>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}:</p>
                        <strong>${model.visitDate!}</strong>
                    </div>
                </div>
            </div>
            <div class="main-body">
                <div class="fullwidth-card-row">
                    <div class="fullwidth-card-img">
                    <#if model.data.stressColour=="GREEN">
                        <svg width="58" height="58" viewBox="0 0 58 58" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="29" cy="29" r="28.25" fill="#4EBA7D" stroke="#4EBA7D" stroke-width="1.5"/>
                            <path d="M34.4023 33.0395V13.9818C34.4023 11.0033 31.9791 8.58008 29.0006 8.58008C26.022 8.58008 23.5984 11.0033 23.5984 13.9818V33.0395C21.2853 34.7508 19.9211 37.436 19.9211 40.3349C19.9211 45.3411 23.994 49.4139 29.0002 49.4139C34.0063 49.4139 38.0792 45.3411 38.0792 40.3349C38.0792 37.436 36.715 34.7508 34.4019 33.0395H34.4023ZM29.0006 47.2775C25.1721 47.2775 22.0576 44.163 22.0576 40.3345C22.0576 37.9659 23.2493 35.7835 25.2452 34.4965L25.7345 34.181V13.9818C25.7345 12.181 27.1993 10.7161 29.0002 10.7161C30.801 10.7161 32.2659 12.181 32.2659 13.9818V34.181L32.7551 34.4965C34.7511 35.7835 35.9427 37.9659 35.9427 40.3345C35.9427 44.1625 32.8282 47.2775 28.9998 47.2775H29.0006Z" fill="white"/>
                            <path d="M30.4857 32.2168H27.5145V37.1026H30.4857V32.2168Z" fill="#2C7321"/>
                            <path d="M28.9999 33.711C29.8203 33.711 30.4853 33.046 30.4853 32.2256C30.4853 31.4053 29.8203 30.7402 28.9999 30.7402C28.1796 30.7402 27.5145 31.4053 27.5145 32.2256C27.5145 33.046 28.1796 33.711 28.9999 33.711Z" fill="#2C7321"/>
                            <path d="M29 45.7451C31.9643 45.7451 34.3673 43.3443 34.3673 40.3828C34.3673 37.4213 31.9643 35.0205 29 35.0205C26.0358 35.0205 23.6328 37.4213 23.6328 40.3828C23.6328 43.3443 26.0358 45.7451 29 45.7451Z" fill="#2C7321"/>
                            </svg>
                            </#if>
                            <#if model.data.stressColour=="YELLOW">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="23.25" fill="#FFCB29" stroke="#FFCB29" stroke-width="1.5"/>
                            <path d="M28.6654 27.4825V11.7122C28.6654 9.24739 26.6602 7.24219 24.1951 7.24219C21.73 7.24219 19.7251 9.24739 19.7251 11.7122V27.4825C17.811 28.8987 16.6821 31.1207 16.6821 33.5196C16.6821 37.6622 20.0525 41.0325 24.1951 41.0325C28.3377 41.0325 31.7081 37.6622 31.7081 33.5196C31.7081 31.1207 30.5792 28.8987 28.6651 27.4825H28.6654ZM24.1954 39.2646C21.0274 39.2646 18.4501 36.6873 18.4501 33.5192C18.4501 31.5592 19.4362 29.7532 21.0879 28.6882L21.4927 28.4272V11.7122C21.4927 10.2219 22.7049 9.00979 24.1951 9.00979C25.6853 9.00979 26.8975 10.2219 26.8975 11.7122V28.4272L27.3023 28.6882C28.954 29.7532 29.9401 31.5592 29.9401 33.5192C29.9401 36.687 27.3628 39.2646 24.1948 39.2646H24.1954Z" fill="white"/>
                            <path d="M25.4246 21.7402H22.9659V30.3658H25.4246V21.7402Z" fill="#CD9C1F"/>
                            <path d="M24.1951 22.9388C24.8739 22.9388 25.4243 22.3885 25.4243 21.7096C25.4243 21.0308 24.8739 20.4805 24.1951 20.4805C23.5163 20.4805 22.9659 21.0308 22.9659 21.7096C22.9659 22.3885 23.5163 22.9388 24.1951 22.9388Z" fill="#CD9C1F"/>
                            <path d="M24.1951 37.9987C26.648 37.9987 28.6365 36.0121 28.6365 33.5614C28.6365 31.1107 26.648 29.124 24.1951 29.124C21.7422 29.124 19.7537 31.1107 19.7537 33.5614C19.7537 36.0121 21.7422 37.9987 24.1951 37.9987Z" fill="#CD9C1F"/>
                            </svg>
                            </#if>
                            <#if model.data.stressColour=="ORANGE">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="23.25" fill="#F58837" stroke="#F58837" stroke-width="1.5"/>
                            <path d="M28.4251 27.4728V11.7024C28.4251 9.23763 26.4199 7.23242 23.9551 7.23242C21.4903 7.23242 19.4851 9.23763 19.4851 11.7024V27.4728C17.571 28.8889 16.4421 31.111 16.4421 33.5098C16.4421 37.6524 19.8125 41.0228 23.9551 41.0228C28.0977 41.0228 31.4681 37.6524 31.4681 33.5098C31.4681 31.111 30.3392 28.8889 28.4251 27.4728ZM23.9551 39.2548C20.787 39.2548 18.2097 36.6775 18.2097 33.5095C18.2097 31.5495 19.1959 29.7435 20.8475 28.6785L21.2524 28.4174V11.7024C21.2524 10.2122 22.4645 9.00002 23.9548 9.00002C25.445 9.00002 26.6572 10.2122 26.6572 11.7024V28.4174L27.062 28.6785C28.7137 29.7435 29.6998 31.5495 29.6998 33.5095C29.6998 36.6772 27.1225 39.2548 23.9544 39.2548H23.9551Z" fill="white"/>
                            <path d="M25.1842 16.4053H22.7256V30.8326H25.1842V16.4053Z" fill="#96511D"/>
                            <path d="M23.9551 17.4905C24.634 17.4905 25.1843 16.9402 25.1843 16.2614C25.1843 15.5825 24.634 15.0322 23.9551 15.0322C23.2763 15.0322 22.726 15.5825 22.726 16.2614C22.726 16.9402 23.2763 17.4905 23.9551 17.4905Z" fill="#96511D"/>
                            <path d="M23.9551 37.989C26.408 37.989 28.3965 36.0023 28.3965 33.5516C28.3965 31.1009 26.408 29.1143 23.9551 29.1143C21.5022 29.1143 19.5137 31.1009 19.5137 33.5516C19.5137 36.0023 21.5022 37.989 23.9551 37.989Z" fill="#96511D"/>
                            </svg>
                            </#if>
                            <#if model.data.stressColour=="RED">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="24" fill="#F75D5E"/>
                            <path d="M28.7642 27.4728V11.7024C28.7642 9.23763 26.759 7.23242 24.2942 7.23242C21.8294 7.23242 19.8242 9.23763 19.8242 11.7024V27.4728C17.9101 28.8889 16.7812 31.111 16.7812 33.5098C16.7812 37.6524 20.1516 41.0228 24.2942 41.0228C28.4369 41.0228 31.8072 37.6524 31.8072 33.5098C31.8072 31.111 30.6783 28.8889 28.7642 27.4728ZM24.2942 39.2548C21.1261 39.2548 18.5488 36.6775 18.5488 33.5095C18.5488 31.5495 19.535 29.7435 21.1866 28.6785L21.5915 28.4174V11.7024C21.5915 10.2122 22.8037 9.00002 24.2939 9.00002C25.7841 9.00002 26.9963 10.2122 26.9963 11.7024V28.4174L27.4011 28.6785C29.0528 29.7435 30.0389 31.5495 30.0389 33.5095C30.0389 36.6772 27.4616 39.2548 24.2935 39.2548H24.2942Z" fill="white"/>
                            <path d="M25.5234 11.5049H23.0647V30.8319H25.5234V11.5049Z" fill="#912727"/>
                            <path d="M24.2939 12.7318C24.9727 12.7318 25.523 12.1814 25.523 11.5026C25.523 10.8238 24.9727 10.2734 24.2939 10.2734C23.615 10.2734 23.0647 10.8238 23.0647 11.5026C23.0647 12.1814 23.615 12.7318 24.2939 12.7318Z" fill="#912727"/>
                            <path d="M24.2938 37.989C26.7468 37.989 28.7353 36.0023 28.7353 33.5516C28.7353 31.1009 26.7468 29.1143 24.2938 29.1143C21.8409 29.1143 19.8524 31.1009 19.8524 33.5516C19.8524 36.0023 21.8409 37.989 24.2938 37.989Z" fill="#912727"/>
                            </svg>
                                </#if>
                    </div>
                    <div class="fullwidth-card-content">
                        <p>${localization.getMessage("Report.Heatstress.TemperatureHumidityIndex", [], "Temperature Humidity Index", locale)}: </p>
                        <#if model.data.stressColour=="GREEN">
                        <h5 style="color: #4EBA7D;">${model.data.temperatureInFahrenheitLabel!} °F  ${model.data.temperatureInCelsiusLabel!} °C</h5>
                        <span style="color: #4EBA7D;">${model.data.stressName!}</span>
                        </#if>
                        <#if model.data.stressColour=="YELLOW">
                        <h5 style="color: #FFCB29;">${model.data.temperatureInFahrenheitLabel!} °F  ${model.data.temperatureInCelsiusLabel!} °C</h5>
                        <span style="color: #FFCB29;">${model.data.stressName!}</span>
                        </#if>
                        <#if model.data.stressColour=="ORANGE">
                        <h5 style="color: #F58837;">${model.data.temperatureInFahrenheitLabel!} °F  ${model.data.temperatureInCelsiusLabel!} °C</h5>
                        <span style="color: #F58837;">${model.data.stressName!}</span>
                        </#if>
                        <#if model.data.stressColour=="RED">
                        <h5 style="color: #F75D5E;">${model.data.temperatureInFahrenheitLabel!} °F  ${model.data.temperatureInCelsiusLabel!} °C</h5>
                        <span style="color: #F75D5E;">${model.data.stressName!}</span>
                        </#if>
                        
                    </div>
                </div>
                <div class="cards-row">
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Intake.Adjustment", [], "Intake Adjustment", locale)}:</p>
                        <strong>${model.data.inTakeAdjustmentLabel!} %</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Dmi.Adjustment", [], "DMI Adjustment", locale)}:</p>
                        <strong>${model.data.dmiAdjustmentPercentageLabel!} %</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Estimated.Dry.Matter.Intake", ["${model.data.weightLabel!}"], "Estimated Dry Matter Intake ( ${model.data.weightLabel!})", locale)}:</p>
                        <strong>${model.data.estimatedDryMatterIntakeLabel!} ${model.data.weightLabel!}</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Reduction.In.Dmi", ["${model.data.weightLabel!}"], "Reduction in DMI ( ${model.data.weightLabel!})", locale)}:</p>
                        <strong>${model.data.reductionInDmiLabel!} ${model.data.weightLabel!}</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Loss.Of.Energy.Consumed", [], "Loss of Energy Consumed (Mcal)", locale)}:</p>
                        <strong>${model.data.lossOfEnergyConsumedLabel!} Mcal</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Energy.Equivalent.Milk.Loss", ["${model.data.weightLabel!}"], "Energy Equivalent Milk Loss ( ${model.data.weightLabel!})", locale)}:</p>
                        <strong>${model.data.energyEquivalentMilkLossLabel!} ${model.data.weightLabel!}</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Milk.Value.Loss.Perday", ["${model.data.currencyLabel!}"], "Milk Value Loss (per day)", locale)}:</p>
                        <strong>${model.data.currencyLabel!} ${model.data.milkValueLossPerDayLabel!}</strong>
                    </div>
                    <div class="card-col">
                        <p>${localization.getMessage("Report.Heatstress.Milk.Value.Loss.PerMonth", ["${model.data.currencyLabel!}"], "Milk Value Loss (per month)", locale)}:</p>

                        <strong>${model.data.currencyLabel!} ${model.data.milkValueLossPerMonthLabel!}</strong>
                    </div>
                </div>

				<#if model.unitOfMeasure == "Metric">
                <table class="custom-table">
                    <thead>
                        <tr class="navy-blue navy-blue">
                            <th colspan="2">${localization.getMessage("Report.Heatstress.Temperature", [], "Temperature", locale)}:</th>
                            <th colspan="6">${localization.getMessage("Report.Heatstress.Relative.Humidity", [], "Relative Humidity (%)", locale)}:</th>
                        </tr>
                        <tr class="blue">
                            <th>(°F)</th>
                            <th>(°C)</th>
                            <th>40</th>
                            <th>50</th>
                            <th>60</th>
                            <th>70</th>
                            <th>80</th>
                            <th>90</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="sky-blue">64</td>
                            <td class="blue">18</td>
                            <td class="green">17</td>
                            <td class="green">17</td>
                            <td class="green">17</td>
                            <td class="green">17</td>
                            <td class="green">18</td>
                            <td class="green">18</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">68</td>
                            <td class="blue">20</td>
                            <td class="green">18</td>
                            <td class="green">19</td>
                            <td class="green">19</td>
                            <td class="green">19</td>
                            <td class="green">19</td>
                            <td class="green">20</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">72</td>
                            <td class="blue">22</td>
                            <td class="green">20</td>
                            <td class="green">20</td>
                            <td class="green">20</td>
                            <td class="green">21</td>
                            <td class="green">21</td>
                            <td class="green">22</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">75</td>
                            <td class="blue">24</td>
                            <td class="green">21</td>
                            <td class="green">21</td>
                            <td class="green">22</td>
                            <td class="green">22</td>
                            <td class="yellow">23</td>
                            <td class="yellow">23</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">79</td>
                            <td class="blue">26</td>
                            <td class="green">22</td>
                            <td class="yellow">23</td>
                            <td class="yellow">24</td>
                            <td class="yellow">24</td>
                            <td class="yellow">25</td>
                            <td class="yellow">25</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">82</td>
                            <td class="blue">28</td>
                            <td class="yellow">24</td>
                            <td class="yellow">24</td>
                            <td class="yellow">25</td>
                            <td class="yellow">26</td>
                            <td class="yellow">27</td>
                            <td class="yellow">27</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">86</td>
                            <td class="blue">30</td>
                            <td class="yellow">25</td>
                            <td class="yellow">26</td>
                            <td class="yellow">27</td>
                            <td class="orange">28</td>
                            <td class="orange">28</td>
                            <td class="orange">29</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">90</td>
                            <td class="blue">32</td>
                            <td class="yellow">26</td>
                            <td class="yellow">27</td>
                            <td class="orange">28</td>
                            <td class="orange">29</td>
                            <td class="orange">30</td>
                            <td class="orange">31</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">93</td>
                            <td class="blue">34</td>
                            <td class="orange">28</td>
                            <td class="orange">29</td>
                            <td class="orange">30</td>
                            <td class="orange">31</td>
                            <td class="orange">32</td>
                            <td class="red">33</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">97</td>
                            <td class="blue">36</td>
                            <td class="orange">29</td>
                            <td class="orange">30</td>
                            <td class="orange">31</td>
                            <td class="red">33</td>
                            <td class="red">34</td>
                            <td class="red">35</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">100</td>
                            <td class="blue">38</td>
                            <td class="orange">30</td>
                            <td class="orange">32</td>
                            <td class="red">33</td>
                            <td class="red">34</td>
                            <td class="red">35</td>
                            <td class="red">37</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">104</td>
                            <td class="blue">40</td>
                            <td class="orange">32</td>
                            <td class="red">33</td>
                            <td class="red">34</td>
                            <td class="red">36</td>
                            <td class="red">37</td>
                            <td class="red">39</td>
                        </tr>
                    </tbody>
                </table>
                </#if>
                <#if model.unitOfMeasure == "Imperial">
                <table class="custom-table">
                    <thead>
                        <tr class="navy-blue navy-blue">
                            <th colspan="2">${localization.getMessage("Report.Heatstress.Temperature", [], "Temperature", locale)}:</th>
                            <th colspan="6">${localization.getMessage("Report.Heatstress.Relative.Humidity", [], "Relative Humidity (%)", locale)}:</th>
                        </tr>
                        <tr class="blue">
                            <th>(°F)</th>
                            <th>(°C)</th>
                            <th>40</th>
                            <th>50</th>
                            <th>60</th>
                            <th>70</th>
                            <th>80</th>
                            <th>90</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="sky-blue">64</td>
                            <td class="blue">18</td>
                            <td class="green">63</td>
                            <td class="green">63</td>
                            <td class="green">63</td>
                            <td class="green">63</td>
                            <td class="green">64</td>
                            <td class="green">64</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">68</td>
                            <td class="blue">20</td>
                            <td class="green">65</td>
                            <td class="green">65</td>
                            <td class="green">66</td>
                            <td class="green">66</td>
                            <td class="green">67</td>
                            <td class="green">67</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">72</td>
                            <td class="blue">22</td>
                            <td class="green">67</td>
                            <td class="green">68</td>
                            <td class="green">69</td>
                            <td class="green">69</td>
                            <td class="green">70</td>
                            <td class="green">71</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">75</td>
                            <td class="blue">24</td>
                            <td class="green">70</td>
                            <td class="green">71</td>
                            <td class="green">72</td>
                            <td class="green">72</td>
                            <td class="yellow">73</td>
                            <td class="yellow">74</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">79</td>
                            <td class="blue">26</td>
                            <td class="green">72</td>
                            <td class="yellow">73</td>
                            <td class="yellow">74</td>
                            <td class="yellow">76</td>
                            <td class="yellow">77</td>
                            <td class="yellow">78</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">82</td>
                            <td class="blue">28</td>
                            <td class="yellow">75</td>
                            <td class="yellow">76</td>
                            <td class="yellow">77</td>
                            <td class="yellow">79</td>
                            <td class="yellow">80</td>
                            <td class="orange">81</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">86</td>
                            <td class="blue">30</td>
                            <td class="yellow">77</td>
                            <td class="yellow">79</td>
                            <td class="yellow">80</td>
                            <td class="orange">82</td>
                            <td class="orange">83</td>
                            <td class="orange">85</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">90</td>
                            <td class="blue">32</td>
                            <td class="yellow">79</td>
                            <td class="orange">81</td>
                            <td class="orange">83</td>
                            <td class="orange">85</td>
                            <td class="orange">86</td>
                            <td class="orange">88</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">93</td>
                            <td class="blue">34</td>
                            <td class="orange">82</td>
                            <td class="orange">84</td>
                            <td class="orange">86</td>
                            <td class="orange">88</td>
                            <td class="orange">89</td>
                            <td class="red">91</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">97</td>
                            <td class="blue">36</td>
                            <td class="orange">84</td>
                            <td class="orange">86</td>
                            <td class="orange">88</td>
                            <td class="red">91</td>
                            <td class="red">93</td>
                            <td class="red">95</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">100</td>
                            <td class="blue">38</td>
                            <td class="orange">87</td>
                            <td class="orange">89</td>
                            <td class="red">91</td>
                            <td class="red">94</td>
                            <td class="red">96</td>
                            <td class="red">98</td>
                        </tr>
                        <tr>
                            <td class="sky-blue">104</td>
                            <td class="blue">40</td>
                            <td class="orange">89</td>
                            <td class="red">92</td>
                            <td class="red">94</td>
                            <td class="red">97</td>
                            <td class="red">99</td>
                            <td class="red">102</td>
                        </tr>
                    </tbody>
                </table>
                
                </#if>
                <h3 class="legend-title">${localization.getMessage("Report.Heatstress.Legend", [], "Legend", locale)}:</h3>
                <div class="fullwidth-card-row">
                    <div class="fullwidth-card-img">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="23.25" fill="#4EBA7D" stroke="#4EBA7D" stroke-width="1.5"/>
                            <path d="M28.4709 27.8126V12.0407C28.4709 9.57571 26.4655 7.57031 24.0005 7.57031C21.5355 7.57031 19.5297 9.57571 19.5297 12.0407V27.8126C17.6154 29.2288 16.4865 31.4511 16.4865 33.8502C16.4865 37.9932 19.8571 41.3638 24.0001 41.3638C28.1432 41.3638 31.5138 37.9932 31.5138 33.8502C31.5138 31.4511 30.3848 29.2288 28.4705 27.8126H28.4709ZM24.0005 39.5957C20.8321 39.5957 18.2546 37.0182 18.2546 33.8498C18.2546 31.8897 19.2408 30.0835 20.8926 29.0184L21.2975 28.7573V12.0407C21.2975 10.5504 22.5098 9.33808 24.0001 9.33808C25.4905 9.33808 26.7028 10.5504 26.7028 12.0407V28.7573L27.1077 29.0184C28.7595 30.0835 29.7457 31.8897 29.7457 33.8498C29.7457 37.0179 27.1682 39.5957 23.9998 39.5957H24.0005Z" fill="white"/>
                            <path d="M25.2295 27.1318H22.7706V31.1753H25.2295V27.1318Z" fill="#2C7321"/>
                            <path d="M23.9999 28.3687C24.6788 28.3687 25.2292 27.8183 25.2292 27.1394C25.2292 26.4605 24.6788 25.9102 23.9999 25.9102C23.321 25.9102 22.7706 26.4605 22.7706 27.1394C22.7706 27.8183 23.321 28.3687 23.9999 28.3687Z" fill="#2C7321"/>
                            <path d="M24.0001 38.3277C26.4532 38.3277 28.4419 36.3408 28.4419 33.8899C28.4419 31.439 26.4532 29.4521 24.0001 29.4521C21.5469 29.4521 19.5582 31.439 19.5582 33.8899C19.5582 36.3408 21.5469 38.3277 24.0001 38.3277Z" fill="#2C7321"/>
                        </svg>
                    </div>
                    <div class="fullwidth-card-content">
                        <h4>${localization.getMessage("Report.Heatstress.Stress.Threshold", [], "Stress Threshold", locale)}:</h4>
                        <p>${localization.getMessage("Report.Heatstress.Stress.Threshold.Message", [], "Respiration exceeds 60 BPM  |  Repro losses detectable  |  Rectal temperature exceeds 38.5°C (101.3°F)", locale)}:</p>
                    </div>
                </div>
                <div class="fullwidth-card-row">
                    <div class="fullwidth-card-img">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="23.25" fill="#FFCB29" stroke="#FFCB29" stroke-width="1.5"/>
                            <path d="M28.6654 27.4825V11.7122C28.6654 9.24739 26.6602 7.24219 24.1951 7.24219C21.73 7.24219 19.7251 9.24739 19.7251 11.7122V27.4825C17.811 28.8987 16.6821 31.1207 16.6821 33.5196C16.6821 37.6622 20.0525 41.0325 24.1951 41.0325C28.3377 41.0325 31.7081 37.6622 31.7081 33.5196C31.7081 31.1207 30.5792 28.8987 28.6651 27.4825H28.6654ZM24.1954 39.2646C21.0274 39.2646 18.4501 36.6873 18.4501 33.5192C18.4501 31.5592 19.4362 29.7532 21.0879 28.6882L21.4927 28.4272V11.7122C21.4927 10.2219 22.7049 9.00979 24.1951 9.00979C25.6853 9.00979 26.8975 10.2219 26.8975 11.7122V28.4272L27.3023 28.6882C28.954 29.7532 29.9401 31.5592 29.9401 33.5192C29.9401 36.687 27.3628 39.2646 24.1948 39.2646H24.1954Z" fill="white"/>
                            <path d="M25.4246 21.7402H22.9659V30.3658H25.4246V21.7402Z" fill="#CD9C1F"/>
                            <path d="M24.1951 22.9388C24.8739 22.9388 25.4243 22.3885 25.4243 21.7096C25.4243 21.0308 24.8739 20.4805 24.1951 20.4805C23.5163 20.4805 22.9659 21.0308 22.9659 21.7096C22.9659 22.3885 23.5163 22.9388 24.1951 22.9388Z" fill="#CD9C1F"/>
                            <path d="M24.1951 37.9987C26.648 37.9987 28.6365 36.0121 28.6365 33.5614C28.6365 31.1107 26.648 29.124 24.1951 29.124C21.7422 29.124 19.7537 31.1107 19.7537 33.5614C19.7537 36.0121 21.7422 37.9987 24.1951 37.9987Z" fill="#CD9C1F"/>
                            </svg>     
                    </div>
                    <div class="fullwidth-card-content">
                        <h4>${localization.getMessage("Report.Heatstress.Mild.Moderate.Stress", [], "Mild - Moderate Stress", locale)}:</h4>
                        <p>${localization.getMessage("Report.Heatstress.Mild.Moderate.Stress.Message", [], "Respiration exceeds 75 BPM  |  Rectal temperature exceeds 39°C (102.2°F)", locale)}:</p>
                    </div>
                </div>
                <div class="fullwidth-card-row">
                    <div class="fullwidth-card-img">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="23.25" fill="#F58837" stroke="#F58837" stroke-width="1.5"/>
                            <path d="M28.4251 27.4728V11.7024C28.4251 9.23763 26.4199 7.23242 23.9551 7.23242C21.4903 7.23242 19.4851 9.23763 19.4851 11.7024V27.4728C17.571 28.8889 16.4421 31.111 16.4421 33.5098C16.4421 37.6524 19.8125 41.0228 23.9551 41.0228C28.0977 41.0228 31.4681 37.6524 31.4681 33.5098C31.4681 31.111 30.3392 28.8889 28.4251 27.4728ZM23.9551 39.2548C20.787 39.2548 18.2097 36.6775 18.2097 33.5095C18.2097 31.5495 19.1959 29.7435 20.8475 28.6785L21.2524 28.4174V11.7024C21.2524 10.2122 22.4645 9.00002 23.9548 9.00002C25.445 9.00002 26.6572 10.2122 26.6572 11.7024V28.4174L27.062 28.6785C28.7137 29.7435 29.6998 31.5495 29.6998 33.5095C29.6998 36.6772 27.1225 39.2548 23.9544 39.2548H23.9551Z" fill="white"/>
                            <path d="M25.1842 16.4053H22.7256V30.8326H25.1842V16.4053Z" fill="#96511D"/>
                            <path d="M23.9551 17.4905C24.634 17.4905 25.1843 16.9402 25.1843 16.2614C25.1843 15.5825 24.634 15.0322 23.9551 15.0322C23.2763 15.0322 22.726 15.5825 22.726 16.2614C22.726 16.9402 23.2763 17.4905 23.9551 17.4905Z" fill="#96511D"/>
                            <path d="M23.9551 37.989C26.408 37.989 28.3965 36.0023 28.3965 33.5516C28.3965 31.1009 26.408 29.1143 23.9551 29.1143C21.5022 29.1143 19.5137 31.1009 19.5137 33.5516C19.5137 36.0023 21.5022 37.989 23.9551 37.989Z" fill="#96511D"/>
                            </svg>                             
                    </div>
                    <div class="fullwidth-card-content">
                        <h4>${localization.getMessage("Report.Heatstress.Moderate.Severe.Stress", [], "Moderate - Severe Stress", locale)}:</h4>
                        <p>${localization.getMessage("Report.Heatstress.Moderate.Severe.Stress.Message", [], "Respiration exceeds 85 BPM  |  Rectal temperature exceeds 40°C (104°F)", locale)}:</p>
                    </div>
                </div>
                <div class="fullwidth-card-row">
                    <div class="fullwidth-card-img">
                        <svg width="48" height="49" viewBox="0 0 48 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="24" cy="24.4697" r="24" fill="#F75D5E"/>
                            <path d="M28.7642 27.4728V11.7024C28.7642 9.23763 26.759 7.23242 24.2942 7.23242C21.8294 7.23242 19.8242 9.23763 19.8242 11.7024V27.4728C17.9101 28.8889 16.7812 31.111 16.7812 33.5098C16.7812 37.6524 20.1516 41.0228 24.2942 41.0228C28.4369 41.0228 31.8072 37.6524 31.8072 33.5098C31.8072 31.111 30.6783 28.8889 28.7642 27.4728ZM24.2942 39.2548C21.1261 39.2548 18.5488 36.6775 18.5488 33.5095C18.5488 31.5495 19.535 29.7435 21.1866 28.6785L21.5915 28.4174V11.7024C21.5915 10.2122 22.8037 9.00002 24.2939 9.00002C25.7841 9.00002 26.9963 10.2122 26.9963 11.7024V28.4174L27.4011 28.6785C29.0528 29.7435 30.0389 31.5495 30.0389 33.5095C30.0389 36.6772 27.4616 39.2548 24.2935 39.2548H24.2942Z" fill="white"/>
                            <path d="M25.5234 11.5049H23.0647V30.8319H25.5234V11.5049Z" fill="#912727"/>
                            <path d="M24.2939 12.7318C24.9727 12.7318 25.523 12.1814 25.523 11.5026C25.523 10.8238 24.9727 10.2734 24.2939 10.2734C23.615 10.2734 23.0647 10.8238 23.0647 11.5026C23.0647 12.1814 23.615 12.7318 24.2939 12.7318Z" fill="#912727"/>
                            <path d="M24.2938 37.989C26.7468 37.989 28.7353 36.0023 28.7353 33.5516C28.7353 31.1009 26.7468 29.1143 24.2938 29.1143C21.8409 29.1143 19.8524 31.1009 19.8524 33.5516C19.8524 36.0023 21.8409 37.989 24.2938 37.989Z" fill="#912727"/>
                            </svg>                               
                    </div>
                    <div class="fullwidth-card-content">
                        <h4>${localization.getMessage("Report.Heatstress.Severe.Stress", [], "Severe Stress", locale)}:</h4>
                        <p>${localization.getMessage("Report.Heatstress.Severe.Stress.Message", [], "Respiration exceeds 120-140 BPM |  Rectal temperature exceeds 41°C (106°F)", locale)}:</p>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>