/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.FeedStorageType;
import com.app.cargill.constants.RumenHealthTmrScores;
import com.app.cargill.document.AnimalAnalysisDetailsToolItem;
import com.app.cargill.document.AnimalAnalysisTool;
import com.app.cargill.document.AnimalAnalysisToolItem;
import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.CalfHeiferScorecard;
import com.app.cargill.document.ForagePennStateTool;
import com.app.cargill.document.HeatStressTool;
import com.app.cargill.document.LocomotionHerdToolItem;
import com.app.cargill.document.LocomotionTool;
import com.app.cargill.document.ManureScreenerScoreGoalToolItem;
import com.app.cargill.document.ManureScreenerTool;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MetabolicIncidenceToolItem;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.PenTimeBudgetTool;
import com.app.cargill.document.PileAndBunker;
import com.app.cargill.document.PileAndBunkerTool;
import com.app.cargill.document.ProfitabilityAnalysisTool;
import com.app.cargill.document.ReturnOverFeedTool;
import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RoboticMilkEvaluationToolItem;
import com.app.cargill.document.RoboticMilkEvaluationToolOutputToolItem;
import com.app.cargill.document.RumenFillTool;
import com.app.cargill.document.RumenHealthManureScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreToolItem;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.Scorecard;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.AnimalAnalysisDetailsToolItemDto;
import com.app.cargill.dto.AnimalAnalysisToolDto;
import com.app.cargill.dto.AnimalAnalysisToolItemDto;
import com.app.cargill.dto.BodyConditionToolDto;
import com.app.cargill.dto.CalfHeiferScorecardDto;
import com.app.cargill.dto.ForagePennStateToolDto;
import com.app.cargill.dto.HeatStressToolDto;
import com.app.cargill.dto.LocomotionHerdToolItemDto;
import com.app.cargill.dto.LocomotionToolDto;
import com.app.cargill.dto.ManureScreenerScoreGoalToolItemDto;
import com.app.cargill.dto.ManureScreenerToolDto;
import com.app.cargill.dto.MetabolicIncidenceToolDto;
import com.app.cargill.dto.MetabolicIncidenceToolItemDto;
import com.app.cargill.dto.PenTimeBudgetToolDto;
import com.app.cargill.dto.PileAndBunkerDto;
import com.app.cargill.dto.PileAndBunkerToolDto;
import com.app.cargill.dto.ProfitabilityAnalysisToolDto;
import com.app.cargill.dto.ReturnOverFeedToolDto;
import com.app.cargill.dto.RoboticMilkEvaluationToolDto;
import com.app.cargill.dto.RoboticMilkEvaluationToolItemDto;
import com.app.cargill.dto.RoboticMilkEvaluationToolOutputToolItemDto;
import com.app.cargill.dto.RumenFillToolDto;
import com.app.cargill.dto.RumenHealthManureScoreToolDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreToolDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreToolItemDto;
import com.app.cargill.dto.RumenHealthToolDto;
import com.app.cargill.dto.ScorecardDto;
import com.app.cargill.model.Visits;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.modelmapper.config.Configuration;

@ExtendWith(MockitoExtension.class)
class VisitMapperTest {

  @Mock ModelMapper modelMapper;

  private VisitMapper visitMapper;

  @BeforeEach
  void setUp() {
    visitMapper = new VisitMapper(modelMapper, new ObjectMapper());
  }

  @Test
  void whenAllToolsAreNull() {

    assertNull(visitMapper.modelToDtoForAnimalAnalysis(null));
    assertNull(visitMapper.modelToDtoForRumenHealth(null));
    assertNull(visitMapper.modelToDtoForBCS(null));
    assertNull(visitMapper.modelToDtoForLocomotion(null));
    assertNull(visitMapper.dtoToModelForRumenHealth(null));
    assertNull(visitMapper.dtoToModelForBCS(null));
    assertNull(visitMapper.dtoToModelForLocomotion(null));
    assertNull(visitMapper.dtoToModelForAnimalAnalysis(null));
    assertNull(visitMapper.dtoToModelForRoboticMilkEvaluation(null));
    assertNull(visitMapper.modelToDtoForRoboticMilkEvaluation(null));
    assertNull(visitMapper.modelToDtoForPileAndBunker(null));
    assertNull(visitMapper.modelToDtoForTmrParticleScore(null));
    assertNull(visitMapper.modelToDtoForForagePennState(null));
    assertNull(visitMapper.modelToDtoForPenTimeBudget(null));
    assertNull(visitMapper.modelToDtoForHeatStress(null));
    assertNull(visitMapper.modelToDtoForManureScreenerTool(null));
    assertNull(visitMapper.modelToDtoForForageAuditScorecard(null));
    assertNull(visitMapper.modelToDtoForReturnOverFeed(null));
  }

  @Test
  void whenDtoToModelForBCSNotNull() {
    BodyConditionTool bcs =
        BodyConditionTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    BodyConditionToolDto bcsDto =
        BodyConditionToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(bcs);
    assertNotNull(visitMapper.dtoToModelForBCS(bcsDto));
  }

  @Test
  void whenDtoToModelForProfatibilityAnalysisNotNull() {
    ProfitabilityAnalysisTool profitabilityAnalysisTool =
        ProfitabilityAnalysisTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    ProfitabilityAnalysisToolDto profitabilityDto =
        ProfitabilityAnalysisToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(profitabilityAnalysisTool);
    assertNotNull(visitMapper.dtoToModelForProfitabilityAnalysis(profitabilityDto));
  }

  @Test
  void whenDtoToModelForForagePennStateNotNull() {
    ForagePennStateTool foragePennStateTool =
        ForagePennStateTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .inputs(new ArrayList<>())
            .goals(new ArrayList<>())
            .scorer(RumenHealthTmrScores.FourScreenNew)
            .build();

    ForagePennStateToolDto foragePennStateToolDto =
        ForagePennStateToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .inputs(new ArrayList<>())
            .goals(new ArrayList<>())
            .scorer(RumenHealthTmrScores.FourScreenNew)
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(foragePennStateTool);
    assertNotNull(visitMapper.dtoToModelForForagePennState(foragePennStateToolDto));
  }

  @Test
  void whenDtoToModelForTmrParticleScoreNotNull() {
    RumenHealthTMRParticleScoreTool rumenHealthTMRParticleScoreTool =
        RumenHealthTMRParticleScoreTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .tmrScores(
                List.of(
                    RumenHealthTMRParticleScoreToolItem.builder()
                        .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
                        .build()))
            .selectedScorer(RumenHealthTmrScores.FourScreenOld)
            .build();
    RumenHealthTMRParticleScoreToolDto rumenHealthTMRParticleScoreToolDto =
        RumenHealthTMRParticleScoreToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .tmrScores(
                List.of(
                    RumenHealthTMRParticleScoreToolItemDto.builder()
                        .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
                        .build()))
            .selectedScorer(RumenHealthTmrScores.FourScreenOld)
            .build();
    lenient()
        .when(modelMapper.map(any(), eq(RumenHealthTMRParticleScoreTool.class)))
        .thenReturn(rumenHealthTMRParticleScoreTool);
    when(modelMapper.map(any(), eq(RumenHealthTMRParticleScoreToolItem.class)))
        .thenReturn(RumenHealthTMRParticleScoreToolItem.builder().penId(UUID.randomUUID()).build());
    assertNotNull(visitMapper.dtoToModelForTmrParticleScore(rumenHealthTMRParticleScoreToolDto));
  }

  @Test
  void whenDtoToModelForTmrParticleScoreNotNullAnd2Pens() {
    RumenHealthTMRParticleScoreTool rumenHealthTMRParticleScoreTool =
        RumenHealthTMRParticleScoreTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .tmrScores(
                List.of(
                    RumenHealthTMRParticleScoreToolItem.builder()
                        .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
                        .build(),
                    RumenHealthTMRParticleScoreToolItem.builder()
                        .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
                        .build()))
            .selectedScorer(RumenHealthTmrScores.FourScreenOld)
            .build();
    RumenHealthTMRParticleScoreToolDto rumenHealthTMRParticleScoreToolDto =
        RumenHealthTMRParticleScoreToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .tmrScores(
                List.of(
                    RumenHealthTMRParticleScoreToolItemDto.builder()
                        .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
                        .build(),
                    RumenHealthTMRParticleScoreToolItemDto.builder()
                        .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
                        .build()))
            .selectedScorer(RumenHealthTmrScores.FourScreenOld)
            .build();
    lenient()
        .when(modelMapper.map(any(), eq(RumenHealthTMRParticleScoreTool.class)))
        .thenReturn(rumenHealthTMRParticleScoreTool);
    when(modelMapper.map(any(), eq(RumenHealthTMRParticleScoreToolItem.class)))
        .thenReturn(new RumenHealthTMRParticleScoreToolItem());
    assertNotNull(visitMapper.dtoToModelForTmrParticleScore(rumenHealthTMRParticleScoreToolDto));
  }

  @Test
  void whenDtoToModelForPileAndBunkerNotNull() {
    PileAndBunkerTool pileAndBunkerTool =
        PileAndBunkerTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pileBunkers(
                List.of(
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bunker)
                        .id(UUID.randomUUID())
                        .build()))
            .build();
    PileAndBunkerToolDto pileAndBunkerToolDto =
        PileAndBunkerToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pileBunkers(
                List.of(
                    PileAndBunkerDto.builder()
                        .isPileOrBunker(FeedStorageType.Bunker)
                        .id(UUID.randomUUID())
                        .build()))
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(pileAndBunkerTool);
    assertNotNull(visitMapper.dtoToModelForPileAndBunker(pileAndBunkerToolDto));
  }

  @Test
  void whenModelToDtoForPileAndBunkerNotNull() {
    // models
    PileAndBunkerTool pileAndBunkerTool =
        PileAndBunkerTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pileBunkers(
                List.of(
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bunker)
                        .id(UUID.randomUUID())
                        .build()))
            .build();
    PileAndBunkerToolDto pileAndBunkerToolDto =
        PileAndBunkerToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pileBunkers(
                List.of(
                    PileAndBunkerDto.builder()
                        .isPileOrBunker(FeedStorageType.Bunker)
                        .id(UUID.randomUUID())
                        .build()))
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(pileAndBunkerToolDto);
    assertNotNull(visitMapper.modelToDtoForPileAndBunker(pileAndBunkerTool));
  }

  @Test
  void whenModelToDtoForProfitabilityAnalysisNotNull() {
    // models
    ProfitabilityAnalysisTool profitabilityAnalysisTool =
        ProfitabilityAnalysisTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    ProfitabilityAnalysisToolDto profitabilityAnalysisToolDto =
        ProfitabilityAnalysisToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(profitabilityAnalysisToolDto);
    assertNotNull(visitMapper.modelToDtoForProfitabilityAnalysis(profitabilityAnalysisTool));
  }

  @Test
  void whenModelToDtoForCalfHeiferScorecardNotNull() {
    // models
    CalfHeiferScorecard calfHeiferScorecard =
        CalfHeiferScorecard.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    CalfHeiferScorecardDto calfHeiferScorecardDto =
        CalfHeiferScorecardDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(calfHeiferScorecardDto);
    assertNotNull(visitMapper.modelToDtoForCalfHeiferScorecard(calfHeiferScorecard));
  }

  @Test
  void whenDtoToModelForCalfHeiferScorecardNotNull() {
    // models
    CalfHeiferScorecard calfHeiferScorecard =
        CalfHeiferScorecard.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    CalfHeiferScorecardDto calfHeiferScorecardDto =
        CalfHeiferScorecardDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(calfHeiferScorecard);
    assertNotNull(visitMapper.dtoToModelForCalfHeiferScorecard(calfHeiferScorecardDto));
  }

  @Test
  void whenModelToDtoForTmrParticleScoreNotNull() {
    // models
    RumenHealthTMRParticleScoreTool rumenHealthTMRParticleScoreTool =
        RumenHealthTMRParticleScoreTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .tmrScores(new ArrayList<>())
            .selectedScorer(RumenHealthTmrScores.FourScreenOld)
            .build();
    RumenHealthTMRParticleScoreToolDto rumenHealthTMRParticleScoreToolDto =
        RumenHealthTMRParticleScoreToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .tmrScores(new ArrayList<>())
            .selectedScorer(RumenHealthTmrScores.FourScreenOld)
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(rumenHealthTMRParticleScoreToolDto);
    assertNotNull(visitMapper.modelToDtoForTmrParticleScore(rumenHealthTMRParticleScoreTool));
  }

  @Test
  void whenModelToDtoForForagePennStateNotNull() {
    ForagePennStateTool foragePennStateTool =
        ForagePennStateTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .inputs(new ArrayList<>())
            .goals(new ArrayList<>())
            .scorer(RumenHealthTmrScores.FourScreenNew)
            .build();

    ForagePennStateToolDto foragePennStateToolDto =
        ForagePennStateToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .inputs(new ArrayList<>())
            .goals(new ArrayList<>())
            .scorer(RumenHealthTmrScores.FourScreenNew)
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(foragePennStateToolDto);
    assertNotNull(visitMapper.modelToDtoForForagePennState(foragePennStateTool));
  }

  @Test
  void whenModelToDtoForBCSNotNull() {
    // models
    BodyConditionTool bcs =
        BodyConditionTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    BodyConditionToolDto bcsDto =
        BodyConditionToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(bcsDto);
    assertNotNull(visitMapper.modelToDtoForBCS(bcs));
  }

  @Test
  void whenModelToDtoForPenTimeBudgetNotNull() {
    PenTimeBudgetTool penTimeBudgetTool =
        PenTimeBudgetTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .build();

    PenTimeBudgetToolDto penTimeBudgetToolDto =
        PenTimeBudgetToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(penTimeBudgetToolDto);
    assertNotNull(visitMapper.modelToDtoForPenTimeBudget(penTimeBudgetTool));
  }

  @Test
  void whenDtoToModelForPenTimeBudgetNotNull() {
    PenTimeBudgetTool penTimeBudgetTool =
        PenTimeBudgetTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .build();

    PenTimeBudgetToolDto penTimeBudgetToolDto =
        PenTimeBudgetToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(penTimeBudgetTool);
    assertNotNull(visitMapper.dtoToModelForPenTimeBudget(penTimeBudgetToolDto));
  }

  @Test
  void whenModelToDtoForHeatStressNotNull() {
    HeatStressTool heatStressTool =
        HeatStressTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    HeatStressToolDto heatStressToolDto =
        HeatStressToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(heatStressToolDto);
    assertNotNull(visitMapper.modelToDtoForHeatStress(heatStressTool));
  }

  @Test
  void whenDtoToModelForHeatStressNotNull() {
    HeatStressTool heatStressTool =
        HeatStressTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    HeatStressToolDto heatStressToolDto =
        HeatStressToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(heatStressTool);
    assertNotNull(visitMapper.dtoToModelForHeatStress(heatStressToolDto));
  }

  @Test
  void whenDtoToModelForReturnOverFeedNotNull() {
    ReturnOverFeedTool returnOverFeedTool =
        ReturnOverFeedTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    ReturnOverFeedToolDto returnOverFeedToolDto =
        ReturnOverFeedToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(returnOverFeedTool);
    assertNotNull(visitMapper.dtoToModelForReturnOverFeed(returnOverFeedToolDto));
  }

  @Test
  void whenDtoToModelForReturnOverFeedNull() {

    ReturnOverFeedToolDto returnOverFeedToolDto = null;

    assertNull(visitMapper.dtoToModelForReturnOverFeed(returnOverFeedToolDto));
  }

  @Test
  void whenDtoToModelForForageAuditScorecardNotNull() {
    Scorecard forageAuditScorecard =
        Scorecard.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .sections(new ArrayList<>())
            .build();
    ScorecardDto forageAuditScorecardDto =
        ScorecardDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .sections(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(forageAuditScorecard);
    assertNotNull(visitMapper.dtoToModelForForageAuditScorecard(forageAuditScorecardDto));
  }

  @Test
  void whenModelToDtoForForageAuditScorecardNotNull() {
    Scorecard forageAuditScorecard =
        Scorecard.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .sections(new ArrayList<>())
            .build();
    ScorecardDto forageAuditScorecardDto =
        ScorecardDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .sections(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(forageAuditScorecardDto);
    assertNotNull(visitMapper.modelToDtoForForageAuditScorecard(forageAuditScorecard));
  }

  @Test
  void whenModelToDtoForRumenHealthManureScoreNotNull() {
    // models
    RumenHealthManureScoreTool rumenHealthManureScoreTool =
        RumenHealthManureScoreTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    RumenHealthManureScoreToolDto rumenHealthManureScoreToolDto =
        RumenHealthManureScoreToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(rumenHealthManureScoreToolDto);
    assertNotNull(visitMapper.modeltoDtoForRumenHealthManureScore(rumenHealthManureScoreTool));
  }

  @Test
  void whenModelToDtoForReturnOverFeedNotNull() {
    // models
    ReturnOverFeedTool returnOverFeed =
        ReturnOverFeedTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    ReturnOverFeedToolDto returnOverFeedToolDto =
        ReturnOverFeedToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(returnOverFeedToolDto);
    assertNotNull(visitMapper.modelToDtoForReturnOverFeed(returnOverFeed));
  }

  @Test
  void whenModelToDtoForMetabolicIncidenceNotNull() {
    // models
    MetabolicIncidenceTool metabolicIncidence =
        MetabolicIncidenceTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .visitMetabolicIncidenceData(MetabolicIncidenceToolItem.builder().build())
            .build();

    MetabolicIncidenceToolDto metabolicIncidenceDto =
        MetabolicIncidenceToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .visitMetabolicIncidenceData(MetabolicIncidenceToolItemDto.builder().build())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(metabolicIncidenceDto);
    assertNotNull(visitMapper.modelToDtoForMetabolicIncidence(metabolicIncidence));
  }

  @Test
  void whenModelToDtoForRumenHealthNotNull() {

    when(modelMapper.getConfiguration()).thenReturn(mock(Configuration.class));

    RumenHealthToolDto rumenHealthToolDto =
        RumenHealthToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    RumenHealthTool rumenHealthTool =
        RumenHealthTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(rumenHealthToolDto);
    assertNotNull(visitMapper.modelToDtoForRumenHealth(rumenHealthTool));
  }

  @Test
  void whenDtoToModelForRumenHealthNotNull() {

    when(modelMapper.getConfiguration()).thenReturn(mock(Configuration.class));
    // models
    RumenHealthToolDto rumenHealthToolDto =
        RumenHealthToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    RumenHealthTool rumenHealthTool =
        RumenHealthTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .goals(new ArrayList<>())
            .build();
    lenient().when(modelMapper.map(any(), any())).thenReturn(rumenHealthTool);
    assertNotNull(visitMapper.dtoToModelForRumenHealth(rumenHealthToolDto));
  }

  @Test
  void whenDtoToModelForLocomotionNotNull() {
    LocomotionTool locomotionTool =
        LocomotionTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .herd(LocomotionHerdToolItem.builder().build())
            .build();

    LocomotionToolDto locomotionToolDto =
        LocomotionToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .herd(LocomotionHerdToolItemDto.builder().build())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(locomotionTool);
    assertNotNull(visitMapper.dtoToModelForLocomotion(locomotionToolDto));
  }

  @Test
  void whenDtoToModelForManureScreenerToolNotNull() {
    ManureScreenerTool manureScreenerTool =
        ManureScreenerTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .mstGoal(ManureScreenerScoreGoalToolItem.builder().build())
            .mstScores(new ArrayList<>())
            .build();
    ManureScreenerToolDto manureScreenerToolDto =
        ManureScreenerToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .mstGoal(ManureScreenerScoreGoalToolItemDto.builder().build())
            .mstScores(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(manureScreenerTool);
    assertNotNull(visitMapper.dtoToModelForManureScreenerTool(manureScreenerToolDto));
  }

  @Test
  void whenModelToDtoForManureScreenerToolNotNull() {
    ManureScreenerTool manureScreenerTool =
        ManureScreenerTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .mstGoal(ManureScreenerScoreGoalToolItem.builder().build())
            .mstScores(new ArrayList<>())
            .build();
    ManureScreenerToolDto manureScreenerToolDto =
        ManureScreenerToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .mstGoal(ManureScreenerScoreGoalToolItemDto.builder().build())
            .mstScores(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(manureScreenerToolDto);
    assertNotNull(visitMapper.modelToDtoForManureScreenerTool(manureScreenerTool));
  }

  @Test
  void whenDtoToModelForRoboticMilkEvaluationNotNull() {
    RoboticMilkEvaluationTool roboticMilkEvaluationTool =
        RoboticMilkEvaluationTool.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItem.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItem.builder().build())
                    .build())
            .build();

    RoboticMilkEvaluationToolDto roboticMilkEvaluationToolDto =
        RoboticMilkEvaluationToolDto.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItemDto.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItemDto.builder().build())
                    .build())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(roboticMilkEvaluationTool);
    assertNotNull(visitMapper.dtoToModelForRoboticMilkEvaluation(roboticMilkEvaluationToolDto));
  }

  @Test
  void whenDtoToModelForRumenFillNotNull() {
    RumenFillTool rumenFillTool =
        RumenFillTool.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .goals(new ArrayList<>())
            .pens(new ArrayList<>())
            .build();

    RumenFillToolDto rumenFillToolDto =
        RumenFillToolDto.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .goals(new ArrayList<>())
            .pens(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(rumenFillTool);
    assertNotNull(visitMapper.dtoToModelForRumenFillManureScore(rumenFillToolDto));
  }

  @Test
  void whenModelToDtoForRumenFillNotNull() {
    RumenFillTool rumenFillTool =
        RumenFillTool.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .goals(new ArrayList<>())
            .pens(new ArrayList<>())
            .build();

    RumenFillToolDto rumenFillToolDto =
        RumenFillToolDto.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .goals(new ArrayList<>())
            .pens(new ArrayList<>())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(rumenFillToolDto);
    assertNotNull(visitMapper.modelToDtoForRumenFillManureScore(rumenFillTool));
  }

  @Test
  void whenModelToDtoForRoboticMilkEvaluationNotNull() {
    RoboticMilkEvaluationTool roboticMilkEvaluationTool =
        RoboticMilkEvaluationTool.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItem.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItem.builder().build())
                    .build())
            .build();

    RoboticMilkEvaluationToolDto roboticMilkEvaluationToolDto =
        RoboticMilkEvaluationToolDto.builder()
            .visitId(UUID.randomUUID())
            .id(UUID.randomUUID())
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItemDto.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItemDto.builder().build())
                    .build())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(roboticMilkEvaluationToolDto);
    assertNotNull(visitMapper.modelToDtoForRoboticMilkEvaluation(roboticMilkEvaluationTool));
  }

  @Test
  void whenModelToDtoForLocomotionNotNull() {
    LocomotionTool locomotionTool =
        LocomotionTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .herd(LocomotionHerdToolItem.builder().build())
            .build();

    LocomotionToolDto locomotionToolDto =
        LocomotionToolDto.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pens(new ArrayList<>())
            .herd(LocomotionHerdToolItemDto.builder().build())
            .build();

    lenient().when(modelMapper.map(any(), any())).thenReturn(locomotionToolDto);
    assertNotNull(visitMapper.modelToDtoForLocomotion(locomotionTool));
  }

  @Test
  void whenDtoToModelForAnimalAnalysisNotNull() {
    AnimalAnalysisToolItem animalAnalysis =
        AnimalAnalysisToolItem.builder()
            .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .penName("00000000-0000-0000-0000-000000000002")
            .animalDetails(List.of(AnimalAnalysisDetailsToolItem.builder().build()))
            .build();

    AnimalAnalysisToolItemDto animalAnalysisDto =
        AnimalAnalysisToolItemDto.builder()
            .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .penName("00000000-0000-0000-0000-000000000002")
            .animalDetails(List.of(AnimalAnalysisDetailsToolItemDto.builder().build()))
            .build();

    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(
            AnimalAnalysisTool.builder()
                .visitId(UUID.randomUUID())
                .animals(List.of(animalAnalysis))
                .build());
    assertNotNull(
        visitMapper.dtoToModelForAnimalAnalysis(
            AnimalAnalysisToolDto.builder()
                .visitId(UUID.randomUUID())
                .animals(List.of(animalAnalysisDto))
                .build()));
  }

  @Test
  void whenModelToDtoForAnimalAnalysisNotNull() {
    AnimalAnalysisToolItem animalAnalysis =
        AnimalAnalysisToolItem.builder()
            .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .penName("00000000-0000-0000-0000-000000000002")
            .animalDetails(List.of(AnimalAnalysisDetailsToolItem.builder().build()))
            .build();

    AnimalAnalysisToolItemDto animalAnalysisDto =
        AnimalAnalysisToolItemDto.builder()
            .penId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .penName("00000000-0000-0000-0000-000000000002")
            .animalDetails(List.of(AnimalAnalysisDetailsToolItemDto.builder().build()))
            .build();

    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(
            AnimalAnalysisToolDto.builder()
                .visitId(UUID.randomUUID())
                .animals(List.of(animalAnalysisDto))
                .build());
    assertNotNull(
        visitMapper.modelToDtoForAnimalAnalysis(
            AnimalAnalysisTool.builder()
                .visitId(UUID.randomUUID())
                .animals(List.of(animalAnalysis))
                .build()));
  }

  @Test
  void whenIdIsNullForAllTools() {
    Visits visit =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder()
                    .animalAnalysis(AnimalAnalysisTool.builder().build())
                    .rumenHealth(RumenHealthTool.builder().build())
                    .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
                    .bodyCondition(BodyConditionTool.builder().build())
                    .locomotionScore(LocomotionTool.builder().build())
                    .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
                    .metabolicIncidence(MetabolicIncidenceTool.builder().build())
                    .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
                    .pileAndBunker(PileAndBunkerTool.builder().build())
                    .rumenFillManureScore(RumenFillTool.builder().build())
                    .tmrParticleScore(RumenHealthTMRParticleScoreTool.builder().build())
                    .foragePennState(ForagePennStateTool.builder().id(UUID.randomUUID()).build())
                    .penTimeBudgetTool(PenTimeBudgetTool.builder().build())
                    .heatStress(HeatStressTool.builder().build())
                    .manureScreenerTool(ManureScreenerTool.builder().build())
                    .forageAuditScorecard(Scorecard.builder().build())
                    .profitabilityAnalysis(ProfitabilityAnalysisTool.builder().build())
                    .calfHeiferScorecard(CalfHeiferScorecard.builder().build())
                    .returnOverFeedTool(ReturnOverFeedTool.builder().build())
                    .build())
            .build();
    visitMapper.updateDefaultAttributesForAnimalAnalysis(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForLocomotionScore(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForBCS(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForRumenHealthCudChewing(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForRoboticMilkEvaluation(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForMilkSoldEvaluation(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForMetabolicIncidence(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForRumenHealthManureScore(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForPileAndBunker(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForRumenFillManureScore(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForTmrParticleScore(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForForagePennState(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForHeatStress(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForPenTimeBudget(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForManureScreenerTool(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForForageAuditScorecard(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForProfatibilityAnalysis(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForCalfHeiferScorecard(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForReturnOverFeed(visit, "test", "test", false);

    assertNotNull(visit.getVisitDocument().getBodyCondition().getId());
    assertNotNull(visit.getVisitDocument().getAnimalAnalysis().getId());
    assertNotNull(visit.getVisitDocument().getLocomotionScore().getId());
    assertNotNull(visit.getVisitDocument().getRoboticMilkEvaluation().getId());
    assertNotNull(visit.getVisitDocument().getMilkSoldEvaluation().getId());
    assertNotNull(visit.getVisitDocument().getRumenHealth().getId());
    assertNotNull(visit.getVisitDocument().getMetabolicIncidence().getId());
    assertNotNull(visit.getVisitDocument().getRumenHealthManureScore().getId());
    assertNotNull(visit.getVisitDocument().getPileAndBunker().getId());
    assertNotNull(visit.getVisitDocument().getRumenFillManureScore().getId());
    assertNotNull(visit.getVisitDocument().getTmrParticleScore().getId());
    assertNotNull(visit.getVisitDocument().getForagePennState().getId());
    assertNotNull(visit.getVisitDocument().getPenTimeBudgetTool().getId());
    assertNotNull(visit.getVisitDocument().getHeatStress().getId());
    assertNotNull(visit.getVisitDocument().getManureScreenerTool().getId());
    assertNotNull(visit.getVisitDocument().getForageAuditScorecard().getId());
    assertNotNull(visit.getVisitDocument().getProfitabilityAnalysis().getId());
    assertNotNull(visit.getVisitDocument().getCalfHeiferScorecard().getId());
    assertNotNull(visit.getVisitDocument().getReturnOverFeedTool().getId());
  }

  @Test
  void whenIdIsNotNullForAllTools() {
    Visits visit =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder()
                    .animalAnalysis(AnimalAnalysisTool.builder().id(UUID.randomUUID()).build())
                    .rumenHealth(RumenHealthTool.builder().id(UUID.randomUUID()).build())
                    .milkSoldEvaluation(
                        MilkSoldEvaluationTool.builder().id(UUID.randomUUID()).build())
                    .bodyCondition(BodyConditionTool.builder().id(UUID.randomUUID()).build())
                    .locomotionScore(LocomotionTool.builder().id(UUID.randomUUID()).build())
                    .roboticMilkEvaluation(
                        RoboticMilkEvaluationTool.builder().id(UUID.randomUUID()).build())
                    .metabolicIncidence(
                        MetabolicIncidenceTool.builder().id(UUID.randomUUID()).build())
                    .rumenHealthManureScore(
                        RumenHealthManureScoreTool.builder().id(UUID.randomUUID()).build())
                    .pileAndBunker(PileAndBunkerTool.builder().id(UUID.randomUUID()).build())
                    .rumenFillManureScore(RumenFillTool.builder().id(UUID.randomUUID()).build())
                    .tmrParticleScore(
                        RumenHealthTMRParticleScoreTool.builder().id(UUID.randomUUID()).build())
                    .foragePennState(ForagePennStateTool.builder().id(UUID.randomUUID()).build())
                    .penTimeBudgetTool(PenTimeBudgetTool.builder().id(UUID.randomUUID()).build())
                    .heatStress(HeatStressTool.builder().id(UUID.randomUUID()).build())
                    .manureScreenerTool(ManureScreenerTool.builder().id(UUID.randomUUID()).build())
                    .forageAuditScorecard(Scorecard.builder().id(UUID.randomUUID()).build())
                    .profitabilityAnalysis(
                        ProfitabilityAnalysisTool.builder().id(UUID.randomUUID()).build())
                    .calfHeiferScorecard(CalfHeiferScorecard.builder().build())
                    .returnOverFeedTool(ReturnOverFeedTool.builder().id(UUID.randomUUID()).build())
                    .build())
            .build();
    visitMapper.updateDefaultAttributesForAnimalAnalysis(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForLocomotionScore(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForBCS(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForRoboticMilkEvaluation(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForRumenHealthCudChewing(visit, "test", "Test", false);
    visitMapper.updateDefaultAttributesForMilkSoldEvaluation(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForMetabolicIncidence(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForRumenHealthManureScore(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForPileAndBunker(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForRumenFillManureScore(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForTmrParticleScore(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForForagePennState(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForHeatStress(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForPenTimeBudget(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForManureScreenerTool(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForForageAuditScorecard(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForProfatibilityAnalysis(visit, "test", "test", false);
    visitMapper.updateDefaultAttributesForCalfHeiferScorecard(visit, "Test", "test", false);
    visitMapper.updateDefaultAttributesForReturnOverFeed(visit, "test", "test", false);

    assertNotNull(visit.getVisitDocument().getBodyCondition().getId());
    assertNotNull(visit.getVisitDocument().getAnimalAnalysis().getId());
    assertNotNull(visit.getVisitDocument().getLocomotionScore().getId());
    assertNotNull(visit.getVisitDocument().getMilkSoldEvaluation().getId());
    assertNotNull(visit.getVisitDocument().getRumenHealth().getId());
    assertNotNull(visit.getVisitDocument().getRoboticMilkEvaluation().getId());
    assertNotNull(visit.getVisitDocument().getMetabolicIncidence().getId());
    assertNotNull(visit.getVisitDocument().getRumenHealthManureScore().getId());
    assertNotNull(visit.getVisitDocument().getPileAndBunker().getId());
    assertNotNull(visit.getVisitDocument().getRumenFillManureScore().getId());
    assertNotNull(visit.getVisitDocument().getTmrParticleScore().getId());
    assertNotNull(visit.getVisitDocument().getForagePennState().getId());
    assertNotNull(visit.getVisitDocument().getPenTimeBudgetTool().getId());
    assertNotNull(visit.getVisitDocument().getHeatStress().getId());
    assertNotNull(visit.getVisitDocument().getManureScreenerTool().getId());
    assertNotNull(visit.getVisitDocument().getForageAuditScorecard().getId());
    assertNotNull(visit.getVisitDocument().getProfitabilityAnalysis().getId());
    assertNotNull(visit.getVisitDocument().getCalfHeiferScorecard().getId());
    assertNotNull(visit.getVisitDocument().getReturnOverFeedTool().getId());
  }

  @Test
  void whenIdIsNotNullForAllToolsInUpdateCase() {
    Visits visit =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder()
                    .animalAnalysis(AnimalAnalysisTool.builder().id(UUID.randomUUID()).build())
                    .rumenHealth(RumenHealthTool.builder().id(UUID.randomUUID()).build())
                    .roboticMilkEvaluation(
                        RoboticMilkEvaluationTool.builder().id(UUID.randomUUID()).build())
                    .milkSoldEvaluation(
                        MilkSoldEvaluationTool.builder().id(UUID.randomUUID()).build())
                    .bodyCondition(BodyConditionTool.builder().id(UUID.randomUUID()).build())
                    .locomotionScore(LocomotionTool.builder().id(UUID.randomUUID()).build())
                    .metabolicIncidence(
                        MetabolicIncidenceTool.builder().id(UUID.randomUUID()).build())
                    .rumenHealthManureScore(
                        RumenHealthManureScoreTool.builder().id(UUID.randomUUID()).build())
                    .pileAndBunker(PileAndBunkerTool.builder().id(UUID.randomUUID()).build())
                    .rumenFillManureScore(RumenFillTool.builder().id(UUID.randomUUID()).build())
                    .tmrParticleScore(
                        RumenHealthTMRParticleScoreTool.builder().id(UUID.randomUUID()).build())
                    .foragePennState(ForagePennStateTool.builder().id(UUID.randomUUID()).build())
                    .penTimeBudgetTool(PenTimeBudgetTool.builder().id(UUID.randomUUID()).build())
                    .heatStress(HeatStressTool.builder().id(UUID.randomUUID()).build())
                    .manureScreenerTool(ManureScreenerTool.builder().id(UUID.randomUUID()).build())
                    .forageAuditScorecard(Scorecard.builder().id(UUID.randomUUID()).build())
                    .profitabilityAnalysis(
                        ProfitabilityAnalysisTool.builder().id(UUID.randomUUID()).build())
                    .calfHeiferScorecard(
                        CalfHeiferScorecard.builder().id(UUID.randomUUID()).build())
                    .returnOverFeedTool(ReturnOverFeedTool.builder().id(UUID.randomUUID()).build())
                    .build())
            .build();
    visitMapper.updateDefaultAttributesForAnimalAnalysis(visit, "test", "Test", true);
    visitMapper.updateDefaultAttributesForLocomotionScore(visit, "test", "Test", true);
    visitMapper.updateDefaultAttributesForBCS(visit, "test", "Test", true);
    visitMapper.updateDefaultAttributesForRoboticMilkEvaluation(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForRumenHealthCudChewing(visit, "test", "Test", true);
    visitMapper.updateDefaultAttributesForMilkSoldEvaluation(visit, "Test", "test", true);
    visitMapper.updateDefaultAttributesForMetabolicIncidence(visit, "Test", "test", true);
    visitMapper.updateDefaultAttributesForRumenHealthManureScore(visit, "Test", "test", true);
    visitMapper.updateDefaultAttributesForPileAndBunker(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForRumenFillManureScore(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForTmrParticleScore(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForForagePennState(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForHeatStress(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForPenTimeBudget(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForManureScreenerTool(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForForageAuditScorecard(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForProfatibilityAnalysis(visit, "test", "test", true);
    visitMapper.updateDefaultAttributesForCalfHeiferScorecard(visit, "Test", "test", true);
    visitMapper.updateDefaultAttributesForReturnOverFeed(visit, "test", "test", true);

    assertNotNull(visit.getVisitDocument().getBodyCondition().getId());
    assertNotNull(visit.getVisitDocument().getAnimalAnalysis().getId());
    assertNotNull(visit.getVisitDocument().getLocomotionScore().getId());
    assertNotNull(visit.getVisitDocument().getRoboticMilkEvaluation().getId());
    assertNotNull(visit.getVisitDocument().getMilkSoldEvaluation().getId());
    assertNotNull(visit.getVisitDocument().getRumenHealth().getId());
    assertNotNull(visit.getVisitDocument().getMetabolicIncidence().getId());
    assertNotNull(visit.getVisitDocument().getRumenHealthManureScore().getId());
    assertNotNull(visit.getVisitDocument().getPileAndBunker().getId());
    assertNotNull(visit.getVisitDocument().getRumenFillManureScore().getId());
    assertNotNull(visit.getVisitDocument().getTmrParticleScore().getId());
    assertNotNull(visit.getVisitDocument().getForagePennState().getId());
    assertNotNull(visit.getVisitDocument().getPenTimeBudgetTool().getId());
    assertNotNull(visit.getVisitDocument().getHeatStress().getId());
    assertNotNull(visit.getVisitDocument().getManureScreenerTool().getId());
    assertNotNull(visit.getVisitDocument().getForageAuditScorecard().getId());
    assertNotNull(visit.getVisitDocument().getProfitabilityAnalysis().getId());
    assertNotNull(visit.getVisitDocument().getCalfHeiferScorecard().getId());
  }

  @Test
  void settingDefaultValuesForPileAndBunkerTool() {
    PileAndBunkerTool pileAndBunkerTool =
        PileAndBunkerTool.builder()
            .visitId(UUID.fromString("00000000-0000-0000-0000-000000000002"))
            .pileBunkers(
                List.of(
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bunker)
                        .id(UUID.randomUUID())
                        .build()))
            .build();

    visitMapper.setDefaultValuesForPileAndBunkerList(pileAndBunkerTool);
    assertNotNull(pileAndBunkerTool);
  }

  @Test
  void whenToolIsNullForObjectToHashMapEmptyMapIsReturned() {
    Map<String, Object> result = visitMapper.toolToHashMap(null);
    assertNotNull(result);
    assertEquals(0, result.size());
  }

  @Test
  void whenToolIsMappedToHashMapProperlyMapIsReturned() {
    Map<String, Object> result =
        visitMapper.toolToHashMap(RumenHealthTool.builder().visitId(UUID.randomUUID()).build());
    assertNotNull(result);
  }
}
