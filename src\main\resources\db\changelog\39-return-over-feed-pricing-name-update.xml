<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="039" author="Taha">
	 <sql splitStatements="false">
    
    UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    ('"' || REPLACE(pricing_return_over_feed_document->>'Name', '_', '') || '"')::jsonb
)
WHERE pricing_return_over_feed_document->>'Name' LIKE '%_%';
    


</sql>


	</changeSet>

</databaseChangeLog>