/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkSoldMilkProcessorToolItemDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Integer pickUpIndex;

  private Double milkSold;

  private Integer animalsInTank;

  private Integer daysInTank;

  private Double milkFatPer;

  private Double milkProteinPer;

  private Double mun;

  private Integer somaticCellCount;

  private Double bacteriaCellCount;

  private Double nonFatSolid;

  private Double mastitis;
}
