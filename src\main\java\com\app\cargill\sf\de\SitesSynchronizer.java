/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.cosmos.migration.AccountsDataFixService;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Component
@RequiredArgsConstructor
@Slf4j
public class SitesSynchronizer implements DataSynchronizer<SiteDocument> {

  private final SitesRepository repository;
  private final AccountsRepository accountsRepository;
  private final LiftSiteMappingsService siteMappingsService;
  private final LiftSitesService liftSitesService;
  private final AccountsDataFixService accountsDataFixService;
  private final ResourceBundleMessageSource bundleMessageSource;

  private static final String SUMMARY_REPORT_LINK =
      "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%%20Documents/DE_Reports/%1$S_summary.pdf";
  private static final String STATUS_REPORT_LINK =
      "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%%20Documents/DE_Reports/%1$S_detailed.pdf";

  @Override
  public SyncResult sync(List<SiteDocument> inputList) {
    List<Sites> modifiedSites = new ArrayList<>();
    List<Sites> newSites = new ArrayList<>();
    for (SiteDocument sfSite : inputList) {
      try {
        Sites deSite = getSiteFromDb(sfSite);
        if (deSite == null) {
          if (sfSite.getId() == null) {
            sfSite.setId(UUID.randomUUID());
          }
          deSite = new Sites(sfSite);
          deSite.setLocalId(sfSite.getId().toString());
          if (liftSitesService.updateSiteExternalData(deSite.getSiteDocument())) {
            updateAccounts(deSite);
            if (saveToDb(deSite)) {
              newSites.add(deSite);
            }
          } else {
            log.warn(
                "Unable to update site with sfId: {}", deSite.getSiteDocument().getExternalId());
          }
        } else if (siteIsEligibleForUpdate(deSite, sfSite)) {
          deSite.setSiteDocument(updateSite(sfSite, deSite.getSiteDocument()));
          if (saveToDb(deSite)) {
            modifiedSites.add(deSite);
          }
        }
      } catch (Exception e) {
        log.error("Error syncing site", e);
      }
    }
    logResults(newSites, modifiedSites);
    // We can save each record individually as well but is a matter of more memory allocation vs
    // more DB connections
    recalculateSiteCount(newSites);
    recalculateSiteCount(modifiedSites);
    checkSiteMappings(newSites);
    checkSiteMappings(modifiedSites);
    liftSitesService.autoDeleteLiftdeletedSites();

    return new SyncResult(
        "Sites",
        new AtomicInteger(newSites.size()),
        new AtomicInteger(modifiedSites.size()),
        new AtomicInteger(0),
        "",
        "");
  }

  private boolean saveToDb(Sites site) {
    try {
      repository.save(site);
    } catch (Exception e) {
      log.error("SITE_DB_SAVE_ERROR", e);
      return false;
    }
    return true;
  }

  private void recalculateSiteCount(List<Sites> sites) {
    Flux<Accounts> accountsFlux =
        Flux.fromIterable(sites)
            .filter(site -> site.getSiteDocument().getAccountId() != null)
            .map(site -> site.getSiteDocument().getAccountId().toString())
            .distinct()
            .flatMap(
                accountId ->
                    Mono.fromCallable(
                            () ->
                                Optional.ofNullable(accountsRepository.findByAccountId(accountId)))
                        .subscribeOn(Schedulers.boundedElastic()))
            .filter(Optional::isPresent)
            .map(Optional::get);
    accountsDataFixService
        .runAccountsFix(accountsFlux)
        .onErrorResume(
            (t -> {
              log.error("ACCOUNT_FIX_ERROR {}", t.getMessage());
              return Mono.empty();
            }))
        .collectList()
        .toFuture();
  }

  private void checkSiteMappings(List<Sites> sites) {
    List<Sites> sitesWithMissingMappings =
        sites.stream()
            // Find all sites that miss Site Mappings for LM_SITE
            .filter(
                s ->
                    s.getSiteDocument().getDataSourceMappings().stream()
                        .noneMatch(
                            sm ->
                                sm.getSystemName()
                                    .equalsIgnoreCase(ApplicationMapping.LM_SITE_SYSTEM_NAME)))
            .toList();
    for (Sites site : sitesWithMissingMappings) {
      try {
        siteMappingsService.createSiteMapping(
            site.getSiteDocument(), Locale.ENGLISH, bundleMessageSource);
        // Local DB site mappings should update by themselves on next sync
      } catch (Exception e) {
        log.error(
            "Failed to update SiteMapping for new Site with id: {}",
            site.getSiteDocument().getId(),
            e);
      }
    }
  }

  private void updateAccounts(Sites deSite) {

    if (deSite.getSiteDocument().getAccountId() != null) {
      Accounts account =
          accountsRepository.findByAccountId(deSite.getSiteDocument().getAccountId().toString());
      if (account == null) {
        return;
      }
      account
          .getAccountDocument()
          .setSiteCount(
              repository.countByAccountId(account.getAccountDocument().getId().toString()));
      // time stamps update
      account
          .getAccountDocument()
          .setLastUpdateUserId(deSite.getSiteDocument().getLastModifyUser());
      account
          .getAccountDocument()
          .setLastModificationDate(DateEpoch.builder().date(Instant.now()).build());
      account.getAccountDocument().setLastModifiedBy(deSite.getSiteDocument().getLastModifyUser());
      account.getAccountDocument().setLastModifiedTimeUtc(Instant.now());
      account.getAccountDocument().setLastSyncTimeUtc(Instant.now());

      accountsRepository.saveAndFlush(account);
    }
  }

  @Override
  public Class<SiteDocument> getType() {
    return SiteDocument.class;
  }

  @SuppressWarnings("java:S3776") // Complexity is not an issue since code is trivial
  private SiteDocument updateSite(SiteDocument source, SiteDocument target) {
    if (source.getId() != null) {
      target.setId(source.getId());
    }

    target.setExternalId(source.getExternalId());
    target.setCreateUser(source.getCreateUser());
    target.setDeleted(source.isDeleted());
    target.setLastModifyUser(source.getLastModifyUser());
    target.setCreateTimeUtc(source.getCreateTimeUtc());
    target.setAccountId(source.getAccountId());
    target.setExternalAccountId(source.getExternalAccountId());
    target.setSiteName(source.getSiteName());
    target.setCurrentMilkPrice(source.getCurrentMilkPrice());
    target.setMilkingSystemType(source.getMilkingSystemType());
    target.setLactatingAnimal(source.getLactatingAnimal());
    target.setDataSourceMappings(source.getDataSourceMappings());
    target.setLastModifiedTimeUtc(source.getLastModifiedTimeUtc());
    target.setHasReport(source.getHasReport());

    if (updatedReportsLink(target, source)) {
      if (StringUtils.isNotEmpty(source.getHerdSummaryReport())
          && StringUtils.isNotEmpty(source.getHerdStatusReport())) {
        // Already set on SF
        target.setHerdSummaryReport(source.getHerdSummaryReport());
        target.setHerdStatusReport(source.getHerdStatusReport());
      } else {
        DataSourceMapping dataSourceMapping =
            source.getDataSourceMappings().stream()
                .filter(dsm -> dsm.getSystemName().equals("DDW"))
                .findAny()
                .orElse(null);
        if (dataSourceMapping != null) {
          String summaryReportLink =
              String.format(SUMMARY_REPORT_LINK, dataSourceMapping.getSystemId());
          String statusReportLink =
              String.format(STATUS_REPORT_LINK, dataSourceMapping.getSystemId());
          target.setHerdSummaryReport(summaryReportLink);
          target.setHerdStatusReport(statusReportLink);
          target.setNeedsSync(true);
        }
      }
    }

    return target;
  }

  private Sites getSiteFromDb(SiteDocument sfSite) {
    Sites site = null;
    if (sfSite.getId() != null) {
      site = repository.findBySiteIdOrExternalId(sfSite.getId().toString(), sfSite.getExternalId());
    }
    return site;
  }

  private boolean siteIsEligibleForUpdate(Sites deSite, SiteDocument sfSite) {
    if (deSite.getUpdatedDate().toInstant().getEpochSecond()
        < sfSite.getLastModifiedTimeUtc().getEpochSecond()) {
      return true;
    }

    if (updatedReportsLink(deSite.getSiteDocument(), sfSite)) {
      return true;
    }

    if (deSite.getSiteDocument().getId() != null
        && !deSite.getSiteDocument().getId().equals(sfSite.getId())) {
      log.warn(
          "SITE_ID_MISMATCH deSite: {} sfSite: {}",
          deSite.getSiteDocument().getId(),
          sfSite.getId());
    }

    if (sfSite.getExternalId() != null
        && !sfSite.getExternalId().equals(deSite.getSiteDocument().getExternalId())) {
      return true;
    }

    return false;
  }

  private void logResults(List<Sites> newSites, List<Sites> modifiedSites) {
    if (!modifiedSites.isEmpty()) {
      log.info("Updated {} modifiedSites Sites", modifiedSites);
      log.info("Updated {} Sites", modifiedSites.size());
    }
    if (!newSites.isEmpty()) {
      log.info("Added {} new Sites", newSites.size());
    }
  }

  private boolean updatedReportsLink(SiteDocument deSite, SiteDocument sfSite) {
    if (StringUtils.isNotEmpty(deSite.getHerdSummaryReport())
        && StringUtils.isNotEmpty(deSite.getHerdStatusReport())
        && StringUtils.isNotEmpty(sfSite.getHerdSummaryReport())
        && StringUtils.isNotEmpty(sfSite.getHerdStatusReport())) {
      // If links already exist do nothing
      return false;
    }

    Optional<DataSourceMapping> dataSourceMapping =
        sfSite.getDataSourceMappings().stream()
            .filter(dsm -> dsm.getSystemName().equals("DDW"))
            .findAny();
    // DDW Herd id is setup
    return dataSourceMapping.isPresent();
  }
}
