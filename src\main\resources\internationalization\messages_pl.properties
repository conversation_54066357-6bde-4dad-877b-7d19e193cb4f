(1.18=mm)
(19=mm)
(Adequate=teat preparation\: 10-20 seconds; Unit attachment within 60 to 90 seconds)
(Inadequate=teat preparation\: &lt;10 seconds; Unit attachment\:  &lt; 60 or &gt; 120 seconds)
(Unit=attachment only)
AAEfficiency=Efektywność AA
AMSUtilization=Wykorzystane jednostki robotowej
AMSUtilizationChart=Tabela użytkowania robota leczenia
ARA=ARA
ARS=Argentyna ($ ARS)
AUD=Australia ($ AUD)
Abomasum=
Account=Konta
Account-Not-Synced-To-Lift=Konto nie zostało zsynchronizowane z LIFT; skontaktuj się z administratorem w celu rozwiązania
Acre=Akr
Action=Akcja
AddBunker=Dodaj Silos
AddPile=Dodaj Pryzmę
AddTMRScore=Add TMR Score
AdjustingKPtoAssureSuccess=Dostosowanie rozdrobnienia ziarniaków gwarancją sukcesu
Afghanistan=Afganistan
Agrigento=Agrigento
Aguascalientes=Aguascalientes
Alabama=Alabama
Alagoas=Alagoas
Aland_Islands=Wyspy Alandzkie
Alaska=W dół
Albania=Albania
Alberta=Alberta
Alessandria=Aleksandria
Algeria=Algeria
Amapá=Ampass
Amazonas=Amazonas
Amount=Ilość
Analiza=fermentacji i testowanie jakości kiszonki\=Fermentation Analysis &amp; Silage Quality Testing
Ancona=Ancona
Andaman_and_Nicobar_Islands=Wyspy Andaman i Nicobar
Andhra_Pradesh=Andhra Pradesh
Andorra=Andorra
Angola=Angola
Anguilla=Anguilla
Anhui=Anhui
AnimalInformation=Animal Information
AnimalListViewModel.Title=Klasa/Podklasa Zwierzęcia
Animals=Animals
AnimalsInHerd=Animals in Herd
AnimalsInPen=Animals in Pen
AnimalsObserved=Zwierzęta Obserwowane
Annually=Corocznie
Answers=Odpowiedzi
Antarctica=Antarktyda
Antigua_and_Barbuda=Antigua i Barbuda
Aosta=Aosta
AppName=Labyrinth
Arezzo=Arezzo
Argentina=Argentyna
Arizona=Arizona
Arkansas=Arkansa
Armenia=Armenia
Aruba=Aruba
Arunachal_Pradesh=Arunachal Pradesh
Ascoli_Piceno=ASCOLI PICENO
Assam=Assam
Asti=Dopóki
AtSixLengthPerDayImperial=6 cali/Dzień
AtSixLengthPerDayMetric=15cm/Dzień
AtThreeLengthPerDayImperial=3 cale/Dzień
AtThreeLengthPerDayMetric=7cm/Dzień
Australia=Australia
Australian_Capital_Territory=Terytorium Stolicy Australii
Austria=Austria
Auto_Sync=Auto synchronizacja
Avellino=Avellino
Average=Średni
AverageMilkLoss=Średnia Starta Mleka ({0})
AverageScoreTitle=Avg. TMR Particle Score
AvgBCS=Średni BCS
AvgLocomotionScore=Średnia Ocena Ruchu
Azerbaijan=Azerbejdżan
BAM=BAM
BCS=BCS
BCSCategory1=BCS 1.0 pkt
BCSCategory1pt5=BCS 1.5 pkt
BCSCategory2=BCS 2.0 pkt
BCSCategory2pt5=BCS 2.5 pkt
BCSCategory3=BCS 3.0 pkt
BCSCategory3pt5=BCS 3.5 pkt
BCSCategory4=BCS 4.0 pkt
BCSCategory4pt5=BCS 4.5 pkt
BCSCategory5=BCS 5.0 pkt
BCSEditMilkAndDimViewModel.BCSDIMTitle=Dni Laktacji (DL)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Mleko
BCSEditMilkAndDimViewModel.Title=Edytuj Dni laktacji i Mleko
BCSHerdAnalysisInputsViewModel.BCS=BCS
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Body Condition Score Analysis
BCSHerdAnalysisInputsViewModel.BCSDIM=DL
BCSHerdAnalysisInputsViewModel.BCSEdit=Edit
BCSHerdAnalysisInputsViewModel.BCSMilk=Mleko
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Analiza Stada
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Goals
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Inputs
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Results
BCSHerdAnalysisMasterViewModel.BCSTitle=BCS
BCSHerdAnalysisMasterViewModel.Title=BCS
BCSHerdAnalysisResultsViewModel.BCSAvg=Średnia BCS
BCSHerdAnalysisResultsViewModel.GraphTitle=Analiza BCS
BCSHerdAnalysisResultsViewModel.MaxBCS=Max. BCS
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Mleko/Szt./Dzień
BCSHerdAnalysisResultsViewModel.MinBCS=Min. BCS
BCSHerdAnalysisResultsViewModel.SubHeading=Analiza Stada
BCSHerdAnalysisResultsViewModel.Title=BCS
BCSPenSelectionViewModel.PenSelectionList=KOJCE
BCSPenSelectionViewModel.Pens=Kojce
BCSPenSelectionViewModel.SelectPointScale=Wybierz Skalę Punktową
BCSPenSelectionViewModel.Title=BCS
BCSSelectPointScaleViewModel.FooterText=Tylko jedna skala punktowa może być użyta podaczas jednej wizyty. Zmiana skali spowoduje utratę danych.
BCSSelectPointScaleViewModel.SelectPointScale=Wybierz Skalę Punktową
BCSSelectPointScaleViewModel.Title=BCS
BGL=BGL
BRL=Brazylia (R$ BRL)
BRR=BRR
Bad=Bad
Bag=Rękaw
BaggedConventionalSilage=Kiszonka w rękawach
Bahamas=Bahamy
Bahia=Bahia
Bahrain=Bahrajn
Baja_California=Baja California
Baja_California_Sur=Baja California Sur
Baleage=Baloty
BaleageFQAs=FAQ Baloty
Baleage_AreBalesWrappedWith=W ile warstw folii zawinięte są baloty?
Baleage_BagsPlacedOnStableWellManagedSurface=Baloty są umieszczane na stabilnym, całorocznym podłożu (asfalt lub beton)?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Kontrolowane pod kątem dziur po szkodnikach, regularnie naprawiane? (raz/tydz) 
Baleage_TrashVegRodentControlledAroundBags=Odpadki, roślinność i szkodniki są pod kontrolą dookoła rękawów
Baleage_WaterShedsOffPlasticNotIntoBaleage=Woda spływa z folii a nie do wewnątrz balotów? (wyzwanie przy dużych kwadratowych balotach)
Bangladesh=Bangladesz
Barbados=Barbados
Bari=Byli
Barletta-Andria-Trani=BARLETTA-ANDRIA-TRANI
Bayern=Bayern
BeddedPack=Głęboka Ściólka
Beijing=Pekin
Belarus=Białoruś
Belgium=Belgia
Belize=Belize
Belluno=Belluno
Benchmarks_Serum_ToolTip=<value />
Benevento=Benevento
Benin=Benin
Bergamo=Bergamo
Bermuda=Bermudy
BetweenFifteenTwenty=Between 15 and 20
Bhutan=Bhutan
BiWeekly=Bi-Weekly
Biella=Bieella
Bihar=Bihar
BodyConditionHerdGoals=BCS Analiza Stada Cele
BodyConditionHerdInputs=BCS Analiza Stada Dane Wejściowe
BodyConditionHerdResults=BCS Analiza Stada Wyniki
BodyConditionInputs=BCS Dane Wejściowe
BodyConditionResults=BCS Wyniki
BodyConditionScoreCategory=BCS {0}
BodyConditionScoreEditInputsViewModel.Count=Liczba
BodyConditionScoreEditInputsViewModel.NumberOfCows=Liczba Krów
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Proszę podać liczbę krów.
BodyConditionScoreEditInputsViewModel.Title=Liczba Krów
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=PRZED WYCIELENIEM (-20 DO -1)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=WCZESNA LAKTACJA (16-60)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=ZASUSZENIE WŁAŚCIWE (\#x20-21)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=PO WYCIELENIU (0 DO 15)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=PÓŹNA LAKTACJA (&gt;201)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=BCS Max
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=ŚRODEK LAKTACJI (121 DO 200)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=BCS Min
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=SZCZYT LAKTACJI (61-120)
BodyConditionScoreHerdEditGoalsViewModel.Title=Edytuj Cele
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Przed Wycieleniem (-20 do -1)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Wczesna Laktacja (16-60)
BodyConditionScoreHerdGoalsViewModel.Edit=Edytuj
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Zasuszenie Właściwe (\#x20 -21)
BodyConditionScoreHerdGoalsViewModel.Fresh=Po Wycieleniu (0 do 15)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Cel Max BCS
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Cel Min BCS
BodyConditionScoreHerdGoalsViewModel.LateLactation=Późna Laktacja (&gt;201)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Środek Laktacji (121 do 200)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Szczyt Laktacji (61-120)
BodyConditionScoreHerdGoalsViewModel.TableTitle=BCS Według Fazy Laktacji
BodyConditionScoreInputsViewModel.AnimalsObserved=Zwierzęta Obserwowane
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Średni BCS (Obliczony)
BodyConditionScoreInputsViewModel.BCSCategory=Kategoria BCS
BodyConditionScoreInputsViewModel.BCSPercentOfPen=Procent Kojca (%)
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=BCS
BodyConditionScoreInputsViewModel.Edit=Edytuj
BodyConditionScoreInputsViewModel.StdDevCalculated=Odchylenie Standardowe (obliczone)
BodyConditionScoreMasterViewModel.Goals=Cele
BodyConditionScoreMasterViewModel.Inputs=Dane Wejściowe
BodyConditionScoreMasterViewModel.Results=Wyniki
BodyConditionScoreMasterViewModel.SubHeading=Analiza Stada
BodyConditionScoreMasterViewModel.Title=BCS
BodyConditionScoreResultsViewModel.BCSAverageTitle=Sredni Wynik
BodyConditionScoreResultsViewModel.PercentPen=Procent Kojca (%)
BodyConditionScoreResultsViewModel.SelectedDates=Wybierz Daty
BodyConditionScoreResultsViewModel.Title=Wyniki BCS
BodyConditionScoresMasterViewModel.BodyConditionScore=BCS
BodyConditionScoresMasterViewModel.Inputs=Dane Wejściowe
BodyConditionScoresMasterViewModel.Results=Wyniki
BodyConditionScoresMasterViewModel.Title=BCS
BodyConditionScoresMasterViewModel.VisitNotebook=Notatnik
Bolivia,_Plurinational_State_of=Boliwia, stan plurinacyjny
Bologna=Bolonia
Bolzano=Bolzano
Bonaire=Bonaire
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius i Saba
Bosnia_and_Herzegovina=Bośnia i Hercegowina
Botswana=Botswana
BottomUnloadingSilo=Silos wieżowy dolnego rozładunku
Bouvet_Island=Wyspa Bouvet
Brazil=Brazylia
Brescia=Brescia
Brindisi=Tosty
British_Columbia=Brytyjska Kolumbia
British_Indian_Ocean_Territory=Terytorium Brytyjskiego Oceanu Indyjskiego
Brunei_Darussalam=Brunei Darussalam
Bulgaria=Bułgaria
Bull=Buhaj
Bunker=Silos
BunkerCapacity=Pojemność Silosu
BunkerFeedOutRate=Opróżnianie Silosu
Bunkers=Silosy
BunkersAndPiles=Silosy i Pryzmy
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Bonus\: dwie warstwy folii, z jedną warstwą nieprzepuszczalną.
BunkersAndPiles_CleanlinessOfFeedArea=Czystość okolicy przechowywania pasz? Punkty 1-10, 10 najlepiej
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=Jak często usuwana jest folia?
BunkersAndPiles_FaceRemoveRate=Prędkość ubywania ściany
BunkersAndPiles_LooseOrFacedFeedIsFed=Luźna pasza jest skarmiana w przeciągu\: 
BunkersAndPiles_PackingInitialSpreadLayers=Pakowanie\: wstępna grubość warstwy 15cm lub mniej?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Nachylenie pryzmy i zbocza silosu nie powinna być mniejsza niż 3\:1 długość do wysokości (18 stopni)
BunkersAndPiles_PorosityScoresConsistently=Wyniki porowatości
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Zamknięcie natychmiast po pakowaniu 6 mm folią?
BunkersAndPiles_SideWallsSealedPlastic=Czy ściany boczne są wyłożone folią?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Gładka ściana, brak naruszonych warstw powodujących dostawanie się tlenu
BunkersAndPiles_TiresSplitsTouching=Czy opony się stykają?
Burkina_Faso=Burkina Faso
Burundi=Burundi
CAD=Kanada (CA$ CAD)
CFNChina=CFN Chiny
CFNIndia=CFN Indie
CHF=Szwajcaria (CHF CHF)
CLF=CLF
CLP=Chile ($ CLP)
CNY=Chiny (CNY CNY)
COP=COP
CPNBrazil=CPN Brazylia
CPNFrance=CPN Francja
CPNPoland=CPN Polska
CPNUS=CPN USA
CRC=CRC
CZK=Republika Czeska (CZK CZK)
Cagliari=Cagliari
Calf=Cielę
CalfHeiferColostrum=Karta oceny Cieląt i Jałówek - Siara
CalfHeiferGrowerPuberty=Karta oceny Cieląt i Jałówek - Wzrost, Dojrzewanie, Cielność, Przed wycieleniem
CalfHeiferKeyBenchmarks=Karta oceny Cieląt i Jałówek - Kluczowe benchmarki
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=Kolorystyka sekcji zależy od zakresu\: &amp;lt;75%\: czerwony, &amp;gt;\=75 and &amp;lt;90\: Pomarańczowy, &amp;gt;\=90\: Zielony
CalfHeiferPostweaned=Karta oceny Cieląt i Jałówek - Po odsadzeniu
CalfHeiferPreweaned=Karta oceny Cieląt i Jałówek - Przed odsadzeniem
CalfHeiferQuestionViewModel.Close=Zamknij
CalfHeiferQuestionViewModel.Colostrum=Siara
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Wzrost, Dojrzewanie, Cielność, Przed wycieleniem
CalfHeiferQuestionViewModel.KeyBenchmarks=Kluczowe benchmarki
CalfHeiferQuestionViewModel.Postweaned=Po odsadzeniu
CalfHeiferQuestionViewModel.Preweaned=Przed odsadzeniem
CalfHeiferQuestionViewModel.Resources=Informacje/ zasoby
CalfHeiferQuestionViewModel.VisitNotebook=Odwiedź notatnik
CalfHeiferResources=Karta oceny Cieląt i Jałówek - Informacje (zasoby)
CalfHeiferResults=Karta oceny Cieląt i Jałówek - Wyniki
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Karta oceny Cieląt i Jałówek
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Wzrost, Dojrzewanie, Cielność, Przed wycieleniem
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Uwzględnij w ogólnej karcie oceny
CalfHeiferScoreCardScoreViewModel.PhaseOne=Siara
CalfHeiferScoreCardScoreViewModel.PhaseThree=Po odsadzeniu
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Przed odsadzeniem
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Stuknij w poniższe kategorie, aby zobaczyć wynik fazowy
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=Kolorystyka sekcji zależy od zakresu\: wynosi 100%\: zielony, &amp;lt;100%\: czerwony   
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Dane?
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Kluczowe benchmarki
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Faza 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Faza 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Faza 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Faza 7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Faza 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Faza 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Zachowaj dokładną dokumentację wzrostu i zdrowia
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Cielność&amp;\#xA;15 - 23 mies.
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Dojrzewanie&amp;\#xA;9 - 15 mies.
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Siara&amp;\#xA;1 - 3 dn.
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Przed wycieleniem / Produkcja&amp;\#xA;23 - 26 mies.
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Wzrost&amp;\#xA;3 - 9 mies.
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Przed/Po Odsadzeniu&amp;\#xA;0 - 3 mies.
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Odwiedź notatnik
CalfHeiferScorecardLanding=Karta oceny Cieląt i Jałówek
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Kluczowe benchmarki
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Do poprawy
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Odpowiedzi
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Wynik
CalfHeiferScorecardResultsViewModel.VisitNotebook=Odwiedź notatnik
CalfHeiferScorecardViewModel.Colostrum=Siara
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Wzrost, Dojrzewanie, Cielność, Przed wycieleniem
CalfHeiferScorecardViewModel.KeyBenchmarks=Kluczowe benchmarki
CalfHeiferScorecardViewModel.Postweaned=Po odsadzeniu
CalfHeiferScorecardViewModel.Preweaned=Przed odsadzeniem
CalfHeiferScorecardViewModel.Resources=Informacje/ zasoby
CalfHeiferScorecardViewModel.Title=Karta oceny
CalfHeiferScorecardViewModel.VisitNotebook=Odwiedź notatnik
CalfHeiferTools=Cielęta i jałówki narzędzia
CalfHeiferToolsViewModel.CalfHeiferScorecard=Karta oceny
CalfHeiferToolsViewModel.CalfHeiferTools=Cielęta i jałówki narzędzia
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Narzędzia
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Wybierz narzędzie z poniższej listy, aby rozpocząć wizytę
CalfHeiferToolsViewModel.CalfHeiferToolsList=Narzędzia
CalfHeiferToolsViewModel.Title=Cielęta i jałówki narzędzia
CalfHeiferToolsViewModel.VisitNotebook=Odwiedź notatnik
California=Kalifornia
Caltanissetta=Caltanissetta
Cambodia=Kambodża
Cameroon=Kamerun
Campeche=Campeche
Campobasso=Campobasso
Canada=Kanada
Capacity=CAPACITY 
Cape_Verde=Zielona peleryna
Carbonia-Iglesias=Carbonia-Iglesias
Cargill=Cargill
CargillForageLabKPTest=Laboratoryjny test KP firmy Cargill
Carlow=Carlow
Caserta=Caserta
Catania=Catania
Catanzaro=Catanzaro
Category1=Ocena Odchodów 1.0 pkt
Category2=Ocena Odchodów 2.0 pkt
Category3=Ocena Odchodów 3.0 pkt
Category4=Ocena Odchodów 4.0 pkt
Category5=Ocena Odchodów 5.0 pkt
Cavan=Cavan
Cayman_Islands=Kajmany
Ceará=Kwadrat
Central_African_Republic=Republika Środkowoafrykańska
Chad=Chad
Chandigarh=Chandigarh
ChewsPerCud=Chews Per Cud
ChewsPerCudMasterViewModel.AddNew=Dodaj Nnową
ChewsPerCudMasterViewModel.CudChewingInputs=Dane Wejściowe
ChewsPerCudMasterViewModel.CudChewingResults=Wyniki
ChewsPerCudMasterViewModel.NumOfChews=\# Przeżuć
ChewsPerCudMasterViewModel.Title={0} / \# Przeżuć
Chhattisgarh=Chhattisgarh
Chiapas=Chiapas
Chieti=Chieti
Chihuahua=Chihuahua
Chile=Chile
China=Chiny
Chinese_Taipei=Chińskie Tajpei
Chongqing=Chongqing
ChooseAppPDF=Proszę wybrać aplikację by wyświetlić PDFF
ChoosingtheCorrectAdditive=Wybór odpowiedniego dodatku
Christmas_Island=Wyspa Bożego Narodzenia
Clare=Clare
ClassSubClass=Klasa/Podklasa Zwierzęcia
Clean=Clean
ClinicalMastitisLosses=Strata Mleka w Wyniku Mastitis
CloseUp=Przed Wycieleniem
CloseUpDry=Przed Wycieleniem
CloseUpHeifer=Przed Wycieleniem Jałówki
Coahuila=Coahuila
Cocos_(Keeling)_Islands=Wyspy Cocos (Keeling)
Colima=Colima
Colombia=Kolumbia
Colorado=Kolorado
Colostrum=Siara
Colostrum_AmountOfColostrumOrFed=Ilość podawanej siary
Colostrum_BrixPercentOfColostrumFed=Brix % podawanej siary
Colostrum_CleanAndDryCalvingArea=Czysta i sucha porodówka
Colostrum_CleanAndDryCalvingArea_ToolTip=Użyj testu "mokrego kolana", aby określić, czy obszar jest czysty i suchy
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Czysty i dezynfekowany sprzęt między karmieniami
Colostrum_CleanCalfCartToTransportCalf=Czyste urządzenie do transportowania cieląt
Colostrum_HoursTillCalfIsRemovedFromMother=Godziny między porodem a oddzieleniem cielęcia od krowy
Colostrum_HoursTillCalfReceivesColostrum=Godziny między porodem a podaniem siary
Colostrum_NumberOfCowsInCalvingArea=Liczba krów na porodówce
Colostrum_PasteurizeColostrumBeforeFeeding=Czy podawana siara jest pasteryzowana?
Colostrum_PercentageOfNavelsDippedInSevenPercent=%  pępków zanurzonych w 7% roztworze jodyny w ciągu 1. godziny
Colostrum_RefrigeratedColostrumStoredLess=Chłodzona siara przechowywana przez mniej niż 24 godziny
ComfortToolsViewModel.ComfortHeading=Wybierz narzędzie z poniższej listy, aby rozpocząć wizytę.
ComfortToolsViewModel.ComfortToolsList=NARZĘDZIA
ComfortToolsViewModel.ComfortToolsTitle=Narzędzia Komfortu
ComfortToolsViewModel.HealthHeading=Wybierz narzędzie z poniższej listy, aby rozpocząć wizytę.
ComfortToolsViewModel.HeatstressEvaluationTitle=Ocena stresu cieplnego
ComfortToolsViewModel.PenTimeTitle=Czas w kojcu
ComfortToolsViewModel.Title=Narzędzia Komfortu
ComfortToolsViewModel.VisitNotebook=Notatnik
Comments=Komentarze
CommonSpinnerViewModel.BodyConditionPDF=Body Condition Scoring guide
CommonSpinnerViewModel.InadequateStimulation=Inadequate stimulation
CommonSpinnerViewModel.LocomotionPDF=Locomotion Scoring guide
CommonSpinnerViewModel.ManureScorePDF=Manure score guide
CommonSpinnerViewModel.NoStimulation=No Stimulation 
CommonSpinnerViewModel.OptimalStimulation=Optimal Stimulation
Como=Jak
Comoros=Comoros
Competitor=Konkurent
CompletedTimeKey=Time Completed
Component=Żywienie z ręki
Compostbarn=Compostbarn
ConcentrateDistribution=Rozkład paszy treściwej
ConfirmExport=Wciśnięcie OK spowoduje załadowanie każdej z wybranych stron i zrobienie zdjęcia, a następnie porót do tego ekranu.
ConfirmScalePointSwitch=Zmiana skali spowoduje skasowanie wprowadzonych danych.
ConfirmScorerSwitch=Zmiana sit zresetuje wprowadzone dane.
Congo=Kongo
Congo,_the_Democratic_Republic_of_the=Kongo, demokratyczna Republika
Connecticut=Connecticut
Consumer=Klienci
ConsumerDetailsViewModel.DeleteProspect=Usuń klienta
ConsumerDetailsViewModel.DeleteProspectPrompt=Czy na pewno usunąć tego klienta? Obiekt i przebieg wizyty zostaną utracone.
ConsumerDetailsViewModel.MainHeading=SITES
ConsumerDetailsViewModel.NewSite=Add a New Site
ConsumerDetailsViewModel.ProspectTitle=Szzcegóły klienta
ConsumersViewModel.NewConsumer=Dodaj nowego klienta
Continue=Kontynuuj
Cook_Islands=Wyspy Cooka
Cork=Korek
Corn=kukurydza
CornSilage=Corn Silage
CornSilageKernel=FAQ Baloty
CornSilageResources=Materiały dotyczące kiszonki z kukurydzy
CornSilageStopGo=Kiszonka z kukurydzy - zileone światło
Cosenza=Cosenza
Costa_Rica=Kostaryka
Costs=COSTS 
Cote_d'Ivoire=Wybrzeże Kości Słoniowej
Count=Liczba
Cow=Krowa 
CowEfficiency=Wydajność krów
CowPerRobot=Krowy na jednostkę
CowsOutsideTargetRangeToolTip=Celem jest posiadanie &lt;20% krów poza zakresem docelowym.
CowsPerDayNeeded=ZAPOTRZEBOWANIE KROWY/DZIEŃ
CowsSectionToolTip=Grupa 8 lub więcej krów powinna zostać przebadana, aby wyciągnąć wnioski. W małych stadach należy przetestować wszystkie świeżowycielone krowy.
CowsToBeFed=Ilość Śywionych Krów
CreateDuplicateDiet=Dla tego typu istnieje już dawka. Czy i tak stworzyć?
CreateDuplicateNameDiet=Dawka o tej nazwie już istnieje, proszę wprowadź unikalną nazwę dla dawki.
Created=Utworzono
Cremona=Cremona
Croatia=Chorwacja
CropCharacteristicsDecisionGuide=Przewodnik - decyzje optymalna chrakterystyka zbiorów
Crotone=Crotone
Cuba=Kuba
CudChewingAverageNumber=Average number
CudChewingDataEntryViewModel.CudChewing=Przeżuwanie
CudChewingDataEntryViewModel.HerdCudChewingDescription=Dla tej wizyty, proszę policzyć ile zwierząt przeżuwa w tym kojcu używając licznika poniżej. Musisz policzyć co najmniej 10 krów.
CudChewingDataEntryViewModel.No=Nie
CudChewingDataEntryViewModel.Yes=Tak
CudChewingHerdEditScoreViewModel.AverageChewsItem=Przeżuwanie Średnio
CudChewingHerdEditScoreViewModel.Close=Zamknij
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Dzień Laktacji
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Edytuj Cele Przeżuwania
CudChewingHerdEditScoreViewModel.EditScoreTitle=Edytuj Wyniki Przeżuwania
CudChewingHerdEditScoreViewModel.PercentChewingItem=Procent Krów Przeżuwających
CudChewingMasterViewModel.CudChewing=Przeżuwanie
CudChewingMasterViewModel.CudChewingInputs=Dane Wejściowe
CudChewingMasterViewModel.CudChewingResults=Wyniki
CudChewingPen=Pen
CudChewingPercentChewing=% Chewing
CudChewingPercentGoal={0}% Celu
CudChewingPercentOfPen=Cud Chewing(% of Pen)
CudChewingViewModel.CudChewing=Kojce
CudChewingViewModel.CudChewingList=KOJCE (TYLKO LAKTACJA I SUCHE)
CudChewingViewModel.CudChewingTitle=Nazwa Kojca
CudChewingViewModel.Title=Kojce
CudChewsCalculatorViewModel.CalculatorHeading=Proszę wybrać krowę by policzyć liczbę przeżuć. Wybierz "dodaj nową" powyżej alby dodać krowę do listy.
CudChewsCalculatorViewModel.CudChewCategorySection=Krowy
CudChewsCalculatorViewModel.NumOfChews=\# Przeżuć
CudChewsDatesForComparisonViewModel.CudChewsPercent=Przeżuwanie % /
Cundinamarca=Cundinamarca
Cuneo=Klin
Curaçao=Curaãçao
Current=Obecnie
CurrentDownResponse=Current Let Down Response ({0}/cow/day)
CurrentMilkPrice=Aktualna cena mleka ({0}/{1})
CurrentSCC=Aktualna LKS (kom/{0}))
CurrentVisitSummary=Current Visit Summary
Customer=Klient
CustomerDetailViewModel.CustomerTitle=Profil Klienta
CustomerDetailViewModel.MainHeading=LOKALIZACJE
CustomerDetailViewModel.NewSite=Dodaj Nową Lokalizację
CustomerDetailViewModel.NewVisit=Nowa Wizyta
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=Użytkownik Końcowy
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Wybierz Segment
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Title=Szczegóły
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=Customer Name - Site Name
Cyprus=Cypr
CzechRepublic=Republika Czeska
Czech_Republic=Republika Czeska
DDW=DDW
DDWOfflineMessage=Since there is no network, would you like to view the offline report?
DDWUpdatedTime=Ostatnio aktualizowane Dane Stada\: {0}
DKK=DKK
DZD=Algeria (DA DZD)
Dadra_and_Nagar_Haveli=Dadra i Nagar Haveli
Daily=Codziennie
DairyEnteligenFarmReportsources=Źródła informacji Dairy Enteligen
Daman_and_Diu=Daman i Diu
DashboardViewModel.Alert=Uwaga\!
DashboardViewModel.AlertMessage=Dane nie były synchronizowane od ponad {0} dni.
DashboardViewModel.GoodAfternoon=Dobry Wieczór, 
DashboardViewModel.GoodMorning=Dzień Dobry, 
DashboardViewModel.MessageBody=Przypominamy o wykorzystaniu notatnika podczas każdej wizyty w celu udokumentowania konkretnej wizyty.
DashboardViewModel.MessageHeader=Wiadomość
DashboardViewModel.RecentSiteVisit=Ostatnie Wizyty
DashboardViewModel.UserPreferences=Preferencje Użytkownika
Date=Data
DateGone=Date Gone
DatesForComparison=Daty Wizyt do Porównania
Days=Days
DaysInMilkItem=Dzien Laktacji (DL)
DeLaval=DeLaval
DeathLoss=Upadki
DecidingSilageStorage=Decydowanie o sposobie składowania paszy objętościowej
Delaware=Delaware
Delete=Usuń
DeleteMatrixValue=Czy jesteś pewny, że chcesz usunąć te wartości matrycy?
Delhi=Delhi
Denmark=Dania
DensityLossesinPressedBagSilos=Density &amp; Losses in Pressed Bag Silos
Diet=Diet
DietDCAD=DCAD dawki mEq/100g
DietDetailViewModel.Created=Utworzono
DietDetailViewModel.DDW=Dane Fermy
DietDetailViewModel.Max=MAX
DietDetailViewModel.Title=Szczegóły Dawki
DietDetailViewModel.UserCreated=Przez Użytkownika
DietListViewModel.InfoNewDiet=Diet names will be updated automatically if MAX is connected to the farm site in Dairy Enteligen. Otherwise, add Diets manually or leave this list blank and select the correct Animal Class / Subclass for each Pen. 
DietListViewModel.MainHeading=Dawki
DietListViewModel.New=Nowy
DietListViewModel.NewDiet=Dodaj Nową Dawkę
DietListViewModel.Title=Dawki
Dirty=Dirty
DisplacedAbomasum=Przemieszczenie Trawieńca
District_of_Columbia=Dystrykt Kolumbii
Distrito_Federal=Dystrykt federalny
Djibouti=Dżibuti
DoNotTest=&lt;20% or do not test
Dominica=Dominika
Dominican_Republic=Republika Dominikany
Donegal=Donegal
DontKnow=brak informacji
DownResponse=Negatywna Odpowiedź ({0}/krowa/dzień)
Dry=Zasuszone
DryCow=Sucha krowa
DryLot=Suchy Kojec
Dryhay=Suche siano
Dublin=Dublin
DuplicatePenName=Kojec o tej nazwie już istnieje. Proszę wybrać inną nazwę.
Durango=Durango
Dystocia=Trudne Porody
EGP=EGP
EarlyLactation=Wczesna Laktacja
Ecuador=Ekwador
Edit=Edytuj
EditDatesForComparison=Edytuj Daty do Porównania
EditDatesForComparisonViewModel.Chews=Przeżucia
EditDatesForComparisonViewModel.EditDatesClose=Zamknij
EditDatesForComparisonViewModel.EditDatesLabel=Proszę wybrać daty do porównania dla tego kojca z listy poniżej.
EditDatesForComparisonViewModel.EditDatesTitle=Edytuj Daty do Porównania
EditDatesForComparisonViewModel.EditDatesVisits=Wizyty
EditDatesForComparisonViewModel.LocomotionScoreAverage=Średnia Ocena Ruchu\:
EditDatesForComparisonViewModel.ManureScoreAverage=Średnia Ocena Odchodów\:
EditDatesForComparisonViewModel.MetabolicIncidence=Proszę wybrać do 5 dat wizyt dla porównania z listy poniżej.
EditDatesForComparisonViewModel.PenTimeBudget=Proszę wybrać do 7 dat wizyt dla porównania z listy poniżej.
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Proszę wybrać datę do porównania z obecną wizytą.
EditDatesForComparisonViewModel.TimeRemainingForResting=Czas Pozostający na Odpoczynek\:
EditDatesForComparisonViewModel.Title=Edytuj Daty do Porównania
EditDatesForComparisonViewModel.Visits=Wizyty
EditGoalsCudChewingViewModel.Close=Zamknij
EditGoalsCudChewingViewModel.CloseUpDry=PRZED WYCIELENIEM
EditGoalsCudChewingViewModel.CudChews=Przeżuwanie Średnio
EditGoalsCudChewingViewModel.EarlyLactation=WCZESNA LAKTACJA
EditGoalsCudChewingViewModel.EditGoalsTitle=Edytuj Cele Przeżuwania
EditGoalsCudChewingViewModel.FarOffDry=ZASUSZENIE WŁAŚCIWE
EditGoalsCudChewingViewModel.Fresh=PO WYCIELENIU
EditGoalsCudChewingViewModel.LateLactation=PÓŹNA LAKTACJA
EditGoalsCudChewingViewModel.MidLactation=ŚRODEK LAKTACJI
EditGoalsCudChewingViewModel.PeakMilk=SZCZYT LAKTACJI
EditGoalsCudChewingViewModel.PercentChewing=Procent Krów Przeżuwających
EditNoteViewModel.Action=Akcja
EditNoteViewModel.Cancel=Anuluj
EditNoteViewModel.Category=Kategoria
EditNoteViewModel.Close=Zamknij
EditNoteViewModel.CreatedByMetadata=Utworzone na {0} @ {1} by {2}
EditNoteViewModel.Delete=Usuń
EditNoteViewModel.DeleteImageButtonText=Usuń obraz
EditNoteViewModel.DeleteImagePrompt=Czy chcesz usunąć to zdjęcie?
EditNoteViewModel.DeletePrompt=Czy chcesz usunąć tę notatkę?
EditNoteViewModel.DeleteVideoButtonText=Usuń wideo
EditNoteViewModel.DeleteVideoPrompt=Czy chcesz usunąć to nagranie?
EditNoteViewModel.Event=Zdarzenie
EditNoteViewModel.LastUpdatedByMetadata=Ostatnia Modyfikacja {0} @ {1} by {2}
EditNoteViewModel.NoteCamcorderNotImplemented=Opcja Nagrywania Nie Jest Jeszcze Dostępna
EditNoteViewModel.NoteGalleryNotImplemented=Opcja Galerii Nie Jest Jeszcze Dostępna
EditNoteViewModel.NoteLabel=Notatka
EditNoteViewModel.NoteOnlyOneImage=Można dodać tylko jedno wideo do notatki. Proszę usunąć najpierw obecne wideo.
EditNoteViewModel.NoteOnlyOneVideo=Można dodać tylko jedno wideo do notatki. Proszę usunąć najpierw obecne wideo.
EditNoteViewModel.Observation=Obserwacja
EditNoteViewModel.Save=Zapisz
EditNoteViewModel.Task=Zadanie
EditNoteViewModel.Title=Notatka
EditNoteViewModel.TitleLabel=Tyluł
Egypt=Egipt
El_Salvador=Zbawiciel
EmailReportViewModel.AnimalImpact=Animal Impact
EmailReportViewModel.CalfHeiferItem=Cielęta i jałówki 
EmailReportViewModel.CalfHeiferScorecard=Karta oceny 
EmailReportViewModel.Capacity=Pojemność
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Lista Kategorii
EmailReportViewModel.Charts=Wykresy
EmailReportViewModel.CoefficientVariation=Współczynnik zmienności (C.V.) (%)
EmailReportViewModel.ComfortHeatStressBanner=Narzędzie Oceny Stresu Cieplnego- Kojec
EmailReportViewModel.ComfortItem=Narzędzie Komfortu
EmailReportViewModel.ComfortPenTimeBanner=Narzędzie Zarządzania Czasem w Kojcu
EmailReportViewModel.ComfortToolsTitle=Narzędzia Komfortu
EmailReportViewModel.CowsOutsideTargetRange=Krowy poza zakresem docelowym (%)
EmailReportViewModel.CudChewingTitle=Przeżuwanie
EmailReportViewModel.DietDCADStr=DCAD dawki
EmailReportViewModel.EmailBody={0}-{1} Raport
EmailReportViewModel.EmailSelectedTools=Wyślij Wybrane Narzędzia
EmailReportViewModel.EmailSubject={0} Raport
EmailReportViewModel.ExportSelected=Wyślij Wybrane Narzędzias
EmailReportViewModel.FeedOut=Zużycie
EmailReportViewModel.ForageAuditScorecard=Karta Oceny Pasz Objętościowych
EmailReportViewModel.ForageImprovements=Forage Audit Scorecard
EmailReportViewModel.ForageLanding=Audyt Pasz Objetościowych Landing Page
EmailReportViewModel.ForageScorecard=Audyt Pasz Objetościowych Scorecard
EmailReportViewModel.GeneratingReport=Generowanie raportu…
EmailReportViewModel.GotoMarketBranding=Marka
EmailReportViewModel.HealthItem=Narzędzie Zdrowotności
EmailReportViewModel.HeatstressEvaluationTitle=Ocena Stresu Cieplnego
EmailReportViewModel.Herd=Stado
EmailReportViewModel.HerdAnalysis=Analiza Stada
EmailReportViewModel.HerdGoals=Analiza Stada - Cele
EmailReportViewModel.HerdInputs=Analiza Stada - Dane Wejściowe   
EmailReportViewModel.HerdResults=Analiza Stada - Wyniki
EmailReportViewModel.HerdRevenue=Analiza Stada - Dochody
EmailReportViewModel.Improvements=Możliwości poprawy
EmailReportViewModel.Improvements1=Do poprawy
EmailReportViewModel.Inputs=Dane Wejściowe
EmailReportViewModel.InputsOutputsChart=Dane Wejściowe / Wyjściowe / Wykresy
EmailReportViewModel.LocomotionScoreTitle=Ocena Ruchu
EmailReportViewModel.ManureScoreTitle=Ocena Odchodów
EmailReportViewModel.MarketBranding=MARKA
EmailReportViewModel.MetabolicIncidenceTitle=Zaburzenia metaboliczne
EmailReportViewModel.MilkProcessCalcInputsTab=kalkulator udojonego mleka – dane wejściowe
EmailReportViewModel.MilkProcessCalcResourcesTab=kalkulator udojonego mleka- materiały
EmailReportViewModel.MilkProcessCalcResultsTab=kalkulator udojonego mleka - rezultat
EmailReportViewModel.MilkProcessRevenue=kalkulator udojonego mleka
EmailReportViewModel.MilkProcessRevenueCalculator=kalkulator udojonego mleka
EmailReportViewModel.MilkingTime=Milking Time
EmailReportViewModel.Notes=Notes
EmailReportViewModel.NumOfChews=Liczba Przeżuć
EmailReportViewModel.NutritionForage=Audyt Pasz Objetościowych
EmailReportViewModel.NutritionItem=Żywienie
EmailReportViewModel.NutritionPile=Pojemność Pryzmy i silosu
EmailReportViewModel.Outputs=Dane Wyjściowe
EmailReportViewModel.PenCompare=Analiza Kojca - Porównanie
EmailReportViewModel.PenDensity=Pen Density
EmailReportViewModel.PenInputs=Analiza Kojca - Dane Wejściowe
EmailReportViewModel.PenResults=Analiza Kojca - Wyniki
EmailReportViewModel.PenTimeTitle=Zarządzanie czasem w kojcu
EmailReportViewModel.PileAndBunkerTitle=Pryzma i Silos
EmailReportViewModel.ProductivityItem=Narzędzie Produktywności
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Zasoby
EmailReportViewModel.Results=Wyniki
EmailReportViewModel.RumenHealthBodyConditionTitle=BCS
EmailReportViewModel.RumenHealthLocomotionTitle=Ocena Ruchu
EmailReportViewModel.RumenHealthManureTitle=Zdrowotność Żwacza Ocena Odchodów
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Zaburzenia metaboliczne
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
EmailReportViewModel.RumenHealthTMRTitle=Zdrowotność Żwacza Sita TMR 
EmailReportViewModel.RumenHealthTitle=Zdrowotność Żwacza Przeżuwanie
EmailReportViewModel.RumenHealthUrinePHTitle=pH moczu
EmailReportViewModel.ScoreScreen=Wyniki
EmailReportViewModel.TMRParticleScoreTitle=Zdrowotność Żwacza Sita TMR
EmailReportViewModel.TimeBudget=Time Budget
EmailReportViewModel.Title=Wyślij Raport
EmailReportViewModel.UrinePhSTDDEV=Odchylenie Standardowe
EmailReportViewModel.UserPreferences=Ustawienia Użytkownika
EmailReportViewModel.UserSettings=Ustawienia Użytkownika
EmailReportViewModel.WalkthroughReportTitle=Raport Z Wizyty
EnergyImperial=Mcal/lb
EnergyMetric=Mcal/kg
Enna=Enna
Equatorial_Guinea=Gwinea Równikowa
Eritrea=Erytrea
ErrorDescription=Wystąpił błąd podczas odczytu lub zapisu informacji.
ErrorTitle=Błąd
Espírito_Santo=Espirito Santo
Estonia=Estonia
Ethiopia=Etiopia
Eula=EULA
Euro=Kraje Unii Europejskiej (€ EUR)
Event=Wydarzenie
EveryOtherDay=Co drugi dzień
Excessive=&gt;0.5BCS unit 
ExtraDaysOpenCostInfoMessage=It typically varies from $3 to $5 per additional open day.
FAQDairyEnteligenFarmReportandDDW=Dairy Enteligen Raport - FAQ
Falkland_Islands_(Malvinas)=Wyspy Falkland (Malvinas)
FarOff=Zasuszenie Właściwe
FarOffDry=Zasuszenie właściwe
Faroe_Islands=Wyspy Owcze
Federal_District=Dystrykt federalny
FeedFirst=Pierwsza pasza
FeedOutRateInfo=INFORMACJE O ZUŻYCIU PASZ
FeedOutRatesFilmsStorageSysExamined=Analiza systemów składowania i opróżniania silosów
FeedOutSurfaceAreaImperial=Feed Out Surface Area (ft^2)
FeedOutSurfaceAreaMetric=Feed Out Surface Area (m^2)
FeedingRate=Tempo Żywenia (świeża masa/krowę)
FeedoutLossesForageStorageSys=Straty w różnych systemach składowania pasz objętościowych
Fermo=Zatrzymany
Ferrara=Ferrara
Fever=
FieldKPTest=Polowy test rozdrobnienia ziarniaków
Fiji=Fidżi
FillAllFields=Proszę wypełnić wszystkie pola.
FillAllMandatoryFields=Please fill in all mandatory fields.
FinalObservations=Końcowe Obserwacje
Finish=Koniec
Finland=Finlandia
Florence=Florencja
Florida=Floryda
Foggia=Foggia
ForageAuditScorecard=Karta Oceny Pasz Objętościowych
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Audyt pasz objętościowych możliwości poprawy
ForageAuditScorecardResponsesViewModel.ResponsesTab=Audyt pasz objętościowych odpowiedzi
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Możliwości Poprawy
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Odpowiedzi
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Ocena
ForageAuditScorecardResultsViewModel.ImprovementsTab=Możliwości poprawy
ForageAuditScorecardResultsViewModel.ResponsesTab=Odpowiedzi
ForageAuditScorecardResultsViewModel.ScoreTab=Ocena
ForageAuditScorecardResultsViewModel.Title=Silos Wieżowy
ForageAuditScorecardResultsViewModel.VisitNotebook=Notatnik
ForageAuditScorecardScoreViewModel.GoodIndicator=Dobrze
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Możliwości poprawy
ForageAuditScorecardScoreViewModel.OverallForageScore=Włączyć do ogólnej oceny paszy objętościowej
ForageAuditScorecardScoreViewModel.Title=Audyt pasz objętościowych ogólny wynik
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Forage silage types
ForageAuditViewModel.ForageAuditScorecard=Karta Oceny Pasz Objętościowych
ForageAuditViewModel.ForageDetail=Jakość paszy objętościowej jest podstawą każdego programu żywienia krów i kluczem do opłacalności fermy. Audyt pasz objętościowych może być stosowany do oceny aktualnych praktyk w zarządzaniu paszami oraz wskazania kluczowych obszarów możliwych do poprawy. Karta oceny zorganizowana jest według kluczowych obszarów zarządzania. Możesz wypełnić wszystkie obszary podczas wizyty lub wybrać jedynie te, które są ważne w danym momencie. Dodatkowe materiały odnośnie zarządzania są dostępne by pomóc poprawić obszary, w których jest taka możłiwość.
ForageAuditViewModel.ForageHeading=Podstawowe Informacje o Kiszonce
ForageAuditViewModel.Resources=Zasoby
ForageAuditViewModel.Title=Audyt Pasz Objetościowych
ForageAuditViewModel.VisitNotebook=Notatnik
ForageManagement_ForagesHarvestedAtProperMaturity=Are forages harvested at proper maturity for crop type and storage facility?
ForageManagement_ForagesHarvestedAtProperMoisture=Are forages harvested at proper moisture for crop type and storage facility?
ForageScorecardResultsViewModel.Title=Baloty
ForageScorecardViewModel.Baleage=Baloty
ForageScorecardViewModel.BunkersAndPiles=Silosy i Pryzmy
ForageScorecardViewModel.ForageAuditCategories=Forage Audit Categories
ForageScorecardViewModel.ForageAuditScore=Forage Audit Score
ForageScorecardViewModel.ForageAuditScorecard=Karta Oceny Audytu Pasz Objętościowych
ForageScorecardViewModel.ForageCategoryTooltip=Forage quality is the foundation of any dairy nutrition program and is a key to the overall profitability of the farm. This tools can be used to evaluate the current forage management practices used on the farm and recommend the key opportunity areas for improvement
ForageScorecardViewModel.Harvest=Zbiór
ForageScorecardViewModel.MaintainingForageQuality=Utrzymanie Jakości Paszy Objętościowej
ForageScorecardViewModel.No=Nie
ForageScorecardViewModel.SilageBags=Rękawy Kiszonkowe
ForageScorecardViewModel.SurveyCategories=Karta Oceny Pasz Objętościowych
ForageScorecardViewModel.SurveyOfForages=Badania Pasz Objętościowych
ForageScorecardViewModel.Title=Karta Oceny Pasz Objętościowych
ForageScorecardViewModel.TowerSilos=Silos Wieżowy
ForageScorecardViewModel.ViewOverallForageScore=Wyświetl Ogólną Ocenę Paszy Objetościowej
ForageScorecardViewModel.VisitNotebook=Notatnik
ForageScorecardViewModel.Yes=Tak
ForlÃ¬-Cesena=Forlésena
FourScreenNew=4 Sita Nowe
FourScreenNewType=(4 mm)
FourScreenOld=4 Sita Stare
FourScreenOldType=(1.18 mm)
FourToSevenDays=4-7 dni
France=Francja
FreeFlow=Swobodny
FreeFormReportViewModel.CalfHeiferItem=Cielęta i jałówki 
FreeFormReportViewModel.CalfHeiferScorecard=Karta oceny 
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Wykresy
FreeFormReportViewModel.ComfortHeatStressBanner=Narzędzie Oceny Stresu Cieplnego- Kojec
FreeFormReportViewModel.ComfortItem=Narzędzie Komfortu
FreeFormReportViewModel.ExportSelected=Eksportuj Wybrane Narzędzia
FreeFormReportViewModel.GeneralNotes=Generalne Notatki z Wizyty
FreeFormReportViewModel.HealthItem=Narzędzie Zdrowotności
FreeFormReportViewModel.Inputs=Dane Wejściowe
FreeFormReportViewModel.KeyBenchmarks=Kluczowe benchmarki
FreeFormReportViewModel.MarketingBranding=MARKA
FreeFormReportViewModel.MilkProcessCalcInputsTab=kalkulator udojonego mleka – dane wejściowe
FreeFormReportViewModel.MilkProcessCalcResourcesTab=kalkulator udojonego mleka - rezultat
FreeFormReportViewModel.MilkProcessCalcResultsTab=kalkulator udojonego mleka - rezultat
FreeFormReportViewModel.MilkProcessRevenueCalculator=kalkulator udojonego mleka
FreeFormReportViewModel.MilkSoldEvaluation=Ocena Mleka Sprzedanego
FreeFormReportViewModel.Notes=Notatki 
FreeFormReportViewModel.NutritionForage=Audyt Pasz Objetościowych
FreeFormReportViewModel.NutritionItem=Narzędzie Żywienia
FreeFormReportViewModel.NutritionPile=Pojemność Pryzmy i silosu
FreeFormReportViewModel.Outputs=Dane Wyjściowe
FreeFormReportViewModel.OverallImprovements=Ogólne Punkty do Poprawy
FreeFormReportViewModel.OverallResponses=Ogólne Odpowiedzi
FreeFormReportViewModel.OverallScore=Wynik Ogólny
FreeFormReportViewModel.PenTimeTitle=Zarządzanie czasem w kojcu
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Opróżnianie Pryzmy i Silosu
FreeFormReportViewModel.ProductivityItem=Narzędzie Produktywności
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Wyniki
FreeFormReportViewModel.RumenHealthBodyConditionTitle=BCS
FreeFormReportViewModel.RumenHealthLocomotionTitle=Ocena Ruchu
FreeFormReportViewModel.RumenHealthManureTitle=Zdrowotność Żwacza Ocena Odchodów
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Zaburzenia metaboliczne
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Zdrowotność żwacza Sita TMR Ocena Stada
FreeFormReportViewModel.RumenHealthTMRTitle=Zdrowotność Żwacza Sita TMR
FreeFormReportViewModel.RumenHealthTitle=Zdrowotność Żwacza Przeżuwanie
FreeFormReportViewModel.RumenHealthUrinePHTitle=pH moczu
FreeFormReportViewModel.Title=Raport Indywidualny
FreeFormReportViewModel.VisitTitle=NAZWA WIZYTY
FreeFormReportViewModel.WalkThroughNotes=WalkThrough Notes
FreeHandNoteClearPaletteDialogMessage=Do you want to delete everything on the screen ? 
FreeHandNoteEditorPageTitle=Free Hand Note
FreeHandNoteSaveUserDialogMessage=Do you want save this note ?
FreeHandNotesViewModel.Save=Save
Freestall=Freestall
French_Guiana=Gujana Francuska
French_Polynesia=Polinezja Francuska
French_Southern_Territories=Francuskie Terytoria Południowe
Fresh=Po Wycieleniu
FreshCow=Świeża krowa
FreshHeifer=Po Wycieleniu Jałówki
Frosinone=Frosinone
Fujian=Fujian
GBP=Wielka Brytania (GBP GBP)
GEA=GEA
GTQ=Gwatemala (Q GTQ)
Gabon=Gabon
Galway=Galway
Gambia=Gambia
Gansu=Gansu
General=Ogólne
Genoa=Genua
Georgia=Gruzja
Germany=Niemcy
GettingtheMostOutofYourForage=Maksymalne wykorzystanie Twojej paszy objętościowej
Ghana=Ghana
Gibraltar=Gibraltar
Girolando=Girolando
Global=Światowy
Goa=Goa
Goal=Cel
Goiás=Goião
Good=Good
Gorizia=Gorizia
GreaterThan8Hours=Więcej niż 8 godzin
GreaterThanFive=więcej niż 5 ziarniaków
GreaterThanSevenDays=więcej niż 7 dni
GreaterThanSixHours=Więcej niż 6 godzin
GreaterThanThirtySixInchesPerDay=Więcej niż 90cm dzienie
GreaterThanTwelveHours=więcej niż 12 godzin
GreaterThanTwenty=&gt;20
Greece=Grecja
Greenland=Grenlandia
Grenada=Grenada
Grosseto=Grosseto
GrowerPubertyPregnancyCloseup=Wzrost, Dojrzewanie, Cielność, Przed wycieleniem
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Czysty i suchy kojec
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Użyj testu "mokrego kolana", aby określić, czy obszar jest czysty i suchy
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=Pożądany BCS jest osiągany na każdym etapie wzrostu
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Ślady luźnego kału
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=Stół paszowy (koryto) czyszczone codzienne, a niewyjady usuwane
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Wolny wybór, czysta woda do woli
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=Brak śladów zanieczyszczenia wody
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Grupy jałowek o jednolitej wielkości
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Zwierzęta w grupie powinny być tego samego rozmiaru
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% przepełnienia
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Dawki zbilansowane do celów wzrostu, często weryfikowane
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Wielkość stołu paszowego dostosowana do wielkości jałówek
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg potrzeba 30cm, 270-400kg potrzeba 38cm, &gt;400 potrzeba 46cm
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Wielkość kojca dostosowana do wielkości jałówek
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg potrzeba 4m2, 270-400kg potrzeba 5m2,  &gt;400 potrzeba 7m2
Guadeloupe=Guadelupa
Guam=Guam
Guanajuato=Guanajuato
Guangdong=Guangdong
Guangxi=Guangxi
Guatemala=Gwatemala
Guernsey=Guernsey
Guerrero=Guerrero
Guinea=Gwinea
Guinea-Bissau=Gwinea-Bissau
Guizhou=Guizhou
Gujarat=Gujarat
Guyana=Gujana
HKD=HKD
HNL=Honduras (HNL HNL)
HRK=HRK
HUF=Węgry (Ft HUF)
Hainan=Hainan
Haiti=Haiti
HalfPointScale=Skala 0.5 punktu
Harvest=Zbiór
Harvest_AdequateEquipmentAndLabor=Odpowiedni sprzęt i zasoby ludzkie do zbioru plonów z sianokiszonki
Harvest_CornSilageMoistureRangeConsistent=Zakres wilgotności kiszonki z kukurydzy jest zgodny z ponad 90% próbek?
Harvest_ForageHarvestingDocumented=Warunki zbioru paszy objętościowej, pola i miejsca składowania są udokumentowane?
Harvest_ForagesHarvestedAtProper=Kiszonki zbierane w odpowiej fazie i wilgotności dla rodzaju upraw i przechowywania
Harvest_KPScoreIsMonitored=Ocena rozdrobnienia ziarniaka jest monitorowana przy użyciu kliogramowego naczynia lub metody flotacyjnej
Harvest_LengthOfCutMonitored=Długość sieczki monitorowana przy użyciu sit Penn State?
Harvest_UseSilageAdditive=Czy użyto dodatku kiszonkarkiego lub stabilizatora tlenowego?
Harvest_WholePlantMoistureDetermined=Czy określana jest wilgotność całej rośliny dla każdego pola?
Haryana=Haryana
Hawaii=Hawaje
Haylage=Sianokiszonka
HealthToolsViewModel.HealthHeading=Proszę wybrać narzędzie z listy poniżej.
HealthToolsViewModel.HealthToolsList=NARZĘDZIA
HealthToolsViewModel.RumenHealthBodyConditionTitle=BCS
HealthToolsViewModel.RumenHealthLocomotionTitle=Ocena Ruchu
HealthToolsViewModel.RumenHealthManureScreening=Zdrowotność żwacza Analiza strawności
HealthToolsViewModel.RumenHealthManureTitle=Zdrowotność Żwacza Ocena Odchodów
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Zaburzenia metaboliczne
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
HealthToolsViewModel.RumenHealthTMRTitle=Zdrowotność Żwacza Sita TMR
HealthToolsViewModel.RumenHealthTitle=Zdrowotność Żwacza Przeżuwanie
HealthToolsViewModel.RumenHealthUrinePHTitle=pH moczu
HealthToolsViewModel.Title=Narzędzia Zdrowotności
Heard_Island_and_McDonald_Islands=Wyspy wyspowe i McDonald Islands
HeatstressCalculations=Kalkulacje Stresu Cieplnego
HeatstressChart=Wykres Stresu Cieplnego
HeatstressChartViewModel.DMIReduction=Redukcja PSM
HeatstressChartViewModel.EnergyEquivMilkLoss=Energetyczny Ekwiwalent Utraty Mleka
HeatstressChartViewModel.EstimateDryMatter=Szacowane Pobranie Suchej Masy
HeatstressChartViewModel.HeatstressEvalLabel=Temperatury skorygowane dla średniej temperatury i wilgotności względnej (bez nasłonecznienia)
HeatstressChartViewModel.IntakeAdjustment=Korekta Pobrania
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=Utrata Skonsumowanej Energii
HeatstressChartViewModel.Mcal=Mcal
HeatstressChartViewModel.MilkValueLossPerDay=Milk Value Loss / Day
HeatstressChartViewModel.MilkValueLossPerMonth=Milk Value Loss / Month
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.ReductionDMI=Redukcja w PSM
HeatstressChartViewModel.TempHumidIndex=Indeks Temperatury i Wilgotności
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Notatnik
HeatstressData=Dane Stresu Cieplnego
HeatstressDataEntryViewModel.AnimalInputs=Dane Wejściowe Zwierząt
HeatstressDataEntryViewModel.CurrentMilkPrice=Aktualna cena mleka ({0}/{1})
HeatstressDataEntryViewModel.DMI=PSM ({0})
HeatstressDataEntryViewModel.Exposure=Ekspozycja
HeatstressDataEntryViewModel.HoursExposed=Godziny na Słońcu
HeatstressDataEntryViewModel.Humidity=Wilgotność (%)
HeatstressDataEntryViewModel.LactatingAnimals=Krowy w Laktacji
HeatstressDataEntryViewModel.Milk=Mleko ({0})
HeatstressDataEntryViewModel.MilkFat=Tłuszcz Mleka (%)
HeatstressDataEntryViewModel.MilkProtein=Białko Mleka (%)
HeatstressDataEntryViewModel.NEL=NEL (Mcal/{0})
HeatstressDataEntryViewModel.Temperature=Temperatura ({0})
HeatstressDataEntryViewModel.VisitNotebook=Notatnik
HeatstressDataEntryViewModel.Weather=Pogoda
HeatstressGreen=Próg Stresu
HeatstressOrange=Umiarkowany Stres
HeatstressRed=Ciężki Stres
HeatstressTableViewModel.HeatstressChartTab=Wykresy
HeatstressTableViewModel.HeatstressDataTab=Dane Wejściowe
HeatstressTableViewModel.Title=Ocena stresu cieplnego
HeatstressTableViewModel.VisitNotebook=Notatnik
HeatstressYellow=Lekko-Umiarkowany Stres
Hebei=Hebei
Heifer=Jałówki
Heilongjiang=Heilongjiang
Henan=Henan
HerdAnalysisGoalsViewModel.CloseUpDry=Przed Wycieleniem
HerdAnalysisGoalsViewModel.CudChewingGoals=Cele Przeżuwania
HerdAnalysisGoalsViewModel.CudChews=Przeżuwanie
HerdAnalysisGoalsViewModel.DIM=Dzień Laktacji
HerdAnalysisGoalsViewModel.EarlyLactation=Wczesna Laktacja
HerdAnalysisGoalsViewModel.FarOffDry=Zasuszenie Właściwe
HerdAnalysisGoalsViewModel.Fresh=Po Wycieleniu
HerdAnalysisGoalsViewModel.LateLactation=Późna Laktacja
HerdAnalysisGoalsViewModel.MidLactation=Środek Laktacji
HerdAnalysisGoalsViewModel.PeakMilk=Szczyt Laktacji
HerdAnalysisGoalsViewModel.PercentChewing=%
HerdAnalysisGoalsViewModel.Title=Analiza Stada
HerdAnalysisGoalsViewModel.To=do
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Zdrowotność Żwacza Przeżuwanie
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Analiza Stada
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Analiza Stada
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Cele
HerdAnalysisMasterViewModel.Title=Zdrowotność Żwacza Przeżuwanie
HerdAnalysisTableTitle=Analiza Wyników Przeżuwania
HerdAnalysisViewModel.AverageChews=Przeżuwanie
HerdAnalysisViewModel.DaysInMilk=Dzień Laktacji
HerdAnalysisViewModel.Edit=Edytuj
HerdAnalysisViewModel.EditLabel=Edytuj
HerdAnalysisViewModel.HerdAnalysisTableTitle=Analiza Wyników Przeżuwania
HerdAnalysisViewModel.HerdCudChewing=Herd Cud Chewing %
HerdAnalysisViewModel.NoOfCows=Dokończ analizę kojca dla wszystkich kojców, które zacząłeś.
HerdAnalysisViewModel.NumberofChewsPerCud=Number of Chews Per Cud
HerdAnalysisViewModel.PenNameLabel=Nazwa Kojca
HerdAnalysisViewModel.PercentChewing=Procent Krów
HerdAnalysisViewModel.TableTitle=Analiza Wyników Przeżuwania
HerdAverage=Średnia Stada (%)
HerdGoal=Cel Stada (%)
HerdInformation=Herd Information
HerdReporting=Raportowanie Stada\: Przeżuwanie
Hidalgo=Hidalgo
Himachal_Pradesh=Himachal Pradesh
Hokkaido=Hokkaido
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=Święta patrz (stan miasta Watykański)
HomeViewModel.AutoSync=Auto synchronizacja
HomeViewModel.ConsumersTab=Klienci
HomeViewModel.CustomersTab=Klienci
HomeViewModel.DashboardTab=Pulpit
HomeViewModel.Eula=EULA
HomeViewModel.Logout=Wylogowanie
HomeViewModel.PrivacyStatement=Privacy Statement
HomeViewModel.ProspectsTab=Klienci Potencjalni
HomeViewModel.Settings=Settings
HomeViewModel.SyncWithDash=Sync -
HomeViewModel.SyncWithDate=Sync - Ostatnia Data Synchronizacji\: {0\:MM/dd/rr}
HomeViewModel.SyncWithTime=Sync - Ostatni Czas Synchronizacji \: {0\:gg\:mm tt}
HomeViewModel.Title=Narzędzie sprzedażowe
Honduras=Honduras
Hong_Kong=Hongkong
HowtoGetBetterKPResults=Jak uzyskać lepsze wyniki rozdrobnienia ziarniaków
Hubei=Hubei
Hunan=Samego siebie
Hungary=Węgry
IDR=Indonezja (Rp IDR)
INR=Indie (INR INR)
Iceland=Islandia
Idaho=Idaho
Illinois=Illinois
Imperia=Imperia
Imperial=Imperialne
Improvements=Możliwości poprawy
India=Indie
Indiana=Indiana
Indonesia=Indonezja
InoculantFQAs=Dodatki kiszonkarskie FAQ
Iowa=Iowa
Iran,_Islamic_Republic_of=Iran (Islamska Republika
Iraq=Irak
Ireland=Irlandia
Isernia=Isernia
Isle_of_Man=Wyspa Man
Israel=Izrael
Italy=Włochy
JOD=JOD
JPY=JPY
Jalisco=Jalisco
Jamaica=Jamajka
Jammu_and_Kashmir=Jammu i Kaszmir
Japan=Japonia
Jersey=Jersey
Jharkhand=Jharkhand
Jiangsu=Jiangsu
Jiangxi=Jiangxi
Jilin=Jilin
Jordan=Jordania
KRW=Korea (Pd) (₩ KRW)
Kansas=Kansas
Karnataka=Karnataka
Kazakhstan=Kazachstan
Kentucky=Kentucky
Kenya=Kenia
Kerala=Kerala
Kerry=Kerry
Ketosis=Ketoza
KeyBenchmarks=Kluczowe benchmarki
KeyBenchmarks_AgeInMonthAtFirstCalving=Wiek pierwszego wycielenia w miesiącach
KeyBenchmarks_CalvingAndHeiferReocrd=Używany system ewidencji cieląt i jałówek
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=15% masy ciała dojrzełego osobnika po 90 dniach
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=55% masy ciała dojrzełego osobnika w zacieleniu
KeyBenchmarks_HeiferPeakProduce=Szczyt laktacji jałówek % do średniej wydajności stada
KeyBenchmarks_NintyDaysMorbidityf=Zachorowalność w ciągu pierwszych 90 dni
KeyBenchmarks_NintyDaysMortality=Śmiertelność w ciągu pierwszych 90 dni
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=94% masy ciała dojrzełego osobnika przed wycieleniem
KeyBenchmarks_PercentOfHeifersPregnant=Procentowy udział jałówek cielnych w wieku 15 miesięcy
KeyBenchmarks_SerumlgG=Serum IgG (g/L) w 48 godzinie
Kildare=Kildare
Kilkenny=Kilkenny
Kiribati=Kiribati
Korea=Korea
Korea,_Democratic_People's_Republic_of=Korea, demokratyczna Republika Ludowa
Korea,_Republic_of=Republika Korei
Kuwait=Kuwejt
Kyrgyzstan=Kirgistan
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=Przyprawa
Lactating=Laktacja
Lactation=Laktacja
Lakshadweep=Lakshadweep
Lao_People's_Democratic_Republic=Laotańska Republika Ludowo-Demokratyczna
Laois=Laois
Last_Synced=Synchronizowano Ostatnio
LateLactation=Późna Laktacja
Latina=Latina
Latvia=Łotwa
Lebanon=Liban
Lecce=Lecce
Lecco=Lecco
Leczenia=
Leitrim=Leitrim
Lely=Lely
Length-exceed-allowed-limit=Dłużość przekracza limit
LengthPerDayImperial=Cali/Dzień
LengthPerDayMetric=Cm/Dzień
Lesotho=Lesotho
LessThan4Days=mniej niż 4 dni
LessThanFifteen=&lt;15 (kernal hardness)
LessThanFiveWholeKernals=mniej niż 5 ziarniaków
LessThanOneHour=Mniej niż 1 godzina
LessThanSixInches=mniej niż 15cm
LessThanSixLayers=Mniej niż 6 warstw folii
LessThanTwentFourInchesPerDay=Mniej niż 60 cm dziennie
Liaoning=Liaoning
Liberia=Liberia
Libyan_Arab_Jamahiriya=Libian Arab Jamahiriya
Liechtenstein=Liechtenstein
Lift-Sync-Fail=Nie udało się zsynchronizować
Limerick=Limeryk
LinkToPens=Powiąż z Kojcem (opcjonalnie)
Lithuania=Litwa
Livorno=Livorno
LocoCategory1=1.0 pkt
LocoCategory2=2.0 pkt
LocoCategory3=3.0 pkt
LocoCategory4=4.0 pkt
LocoCategory5=5.0 pkt
LocomotionEditTableViewModel.Category1=1.0 pkt
LocomotionEditTableViewModel.Category2=2.0 pkt
LocomotionEditTableViewModel.Category3=3.0 pkt
LocomotionEditTableViewModel.Category4=4.0 pkt
LocomotionEditTableViewModel.Category5=5.0 pkt
LocomotionEditTableViewModel.EnterNumberOfCows=Proszę Podać Liczbę Krów.
LocomotionEditTableViewModel.Title=Liczba Krów
LocomotionHerdEditGoalViewModel.Category1=1.0 pkt
LocomotionHerdEditGoalViewModel.Category2=2.0 pkt
LocomotionHerdEditGoalViewModel.Category3=3.0 pkt
LocomotionHerdEditGoalViewModel.Category4=4.0 pkt
LocomotionHerdEditGoalViewModel.Category5=5.0 pkt
LocomotionHerdEditGoalViewModel.HerdGoal=CEL STADA
LocomotionHerdEditGoalViewModel.Title=Edytuj Ilość Docelową
LocomotionHerdInputsViewModel.Herd=STADO
LocomotionHerdMasterViewModel.Inputs=Dane Wejściowe
LocomotionHerdMasterViewModel.Results=Wyniki
LocomotionHerdMasterViewModel.Revenue=Dochód
LocomotionHerdMasterViewModel.SubHeading=Analiza Stada
LocomotionHerdMasterViewModel.Title=Ocena ruchu
LocomotionHerdResultsViewModel.Category1=1.0 pkt
LocomotionHerdResultsViewModel.Category2=2.0 pkt
LocomotionHerdResultsViewModel.Category3=3.0 pkt
LocomotionHerdResultsViewModel.Category4=4.0 pkt
LocomotionHerdResultsViewModel.Category5=5.0 pkt
LocomotionHerdResultsViewModel.HerdAverage=Średnia Stada
LocomotionHerdResultsViewModel.HerdGoal=Cel
LocomotionHerdResultsViewModel.Title=Analiza Oceny Ruchu
LocomotionHerdRevenueViewModel.Revenue=Dochód
LocomotionHerdRevenueViewModel.Title=Ruch Stada - Dochód
LocomotionNumberinHerd=Locomotion (Number in Herd)
LocomotionNumberinPen=Locomotion (Number in Pen)
LocomotionPenInputsViewModel.FromPenSetup=USTAWIENIA KOJCA
LocomotionPenInputsViewModel.Milk=MLEKO
LocomotionPenMasterViewModel.Inputs=Dane Wejściowe
LocomotionPenMasterViewModel.Results=Wyniki
LocomotionPenMasterViewModel.Title=Ocena ruchu
LocomotionPercentofHerd=Locomotion(% of Herd)
LocomotionPercentofPen=Locomotion(% of Pen)
LocomotionPreviousVisitsViewModel.AverageScore=Średn Wynik
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Średni Wynik
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Data
LocomotionPreviousVisitsViewModel.PercentPen=Procent Kojca (%))
LocomotionPreviousVisitsViewModel.SelectedDates=Wybierz Daty
LocomotionPreviousVisitsViewModel.Title=Wyniki Oceny Ruchu
LocomotionScore=Ocena Ruchu
LocomotionScoreAverage=Średnia Ocena Ruchu
LocomotionScoreHerd=Analiza Oceny Ruchu
LocomotionScoreReference=Referencje Oceny Ruchu
LocomotionSelectPenViewModel.MissingDiet=Please enter a valid maxDiet for this pen.
LocomotionSelectPenViewModel.PenSelectionList=KOJCE
LocomotionSelectPenViewModel.Title=Ocena ruchu
Lodi=Przeciwnie
LoginViewModel.Copyright=© {0} Cargill, Incorporated. All Rights Reserved.
LoginViewModel.EmailLabel=Email
LoginViewModel.ErrorDescription=Nazwa użytkownika oraz hasło nie mogą być puste.
LoginViewModel.ErrorTitle=Błąd logowania
LoginViewModel.InvalidMessage=Podana nazwa użytkownika lub hasło nie jest poprawna, aby się zalogować.
LoginViewModel.InvalidMessageTitle=Niepoprawny Login
LoginViewModel.LoginPrompt=Login
LoginViewModel.LoginPromptConsumer=Other login
LoginViewModel.NetworkErrorMessage=Obecnie żadna sieć nie jest dostępna.
LoginViewModel.NetworkErrorMessageTitle=Błąd Sieci
LoginViewModel.PasswordLabel=Hasło
LoginViewModel.Title=Login
LoginViewModel.Unauthorized=Nieautoryzowany dostęp.
LoginViewModel.UnauthorizedTitle=Nieautoryzowany
Longford=Longford
Louisiana=Luizjana
Louth=Louth
LowForage=Niski Udział Pasz Objętościowych
Lucca=Lucca
Luxembourg=Luksemburg
MEQ100G=mEq/100g
MKD=MKD
MUN=Azot Mocznikowy w Mleku MUN (mg/dL)
MXN=Meksyk (PESO MXN)
MYR=Malezja (MYR MYR)
Macao=Makao
Macedonia,_the_former_Yugoslav_Republic_of=Macedonia, była Jugosłowiańska Republika
Macerata=Macerować
Madagascar=Madagaskar
Madhya_Pradesh=Madhya Pradesh
Maharashtra=Maharashtra
MainViewModel.EmailLabel=Email
Maine=Maine
MaintainingForageQuality=Utrzymanie Jakości Paszy Objętościowej
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Bonus\: Stabilizator/inhibitor pleśni jest stosowany do TMR podczas gorącej/wilgotnej pogody?
MaintainingForageQuality_TMRMixHasPleasantAroma=TMR ma przyjemny zapach?
MaintainingForageQuality_TMRMixIsCoolToTouch=TMR jest chłodny w dotyku?
Making_Feed_InventoryFOF=Sporządzanie zestawienia pasz
Malawi=Płomień
Malaysia=Malezja
Maldives=Malediwy
Male=Samiec
Mali=Musiałem
Malta=Malta
ManagingForageinSiloBags=Zarządzanie paszą objętościową w rękawach
ManagingForageinTowerSilos=Zarządzanie paszą objętościową w silosach wieżowych
Manipur=Manipur
Manitoba=Manitoba
Mantua=Mantua
ManureEditScores=Ocena Odchodów - Edytuj Wyniki
ManureScoreHerdAnalysisEditInputsViewModel.Close=Zamknij
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Dni Laktacji (DL)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Edytuj DL
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Ocena Odchodów
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Analiza Oceny Odchodów
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Dni Laktacji (DL)
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Edytuj
ManureScoreHerdAnalysisMasterViewModel.Goals=Cele
ManureScoreHerdAnalysisMasterViewModel.Inputs=Dane Wejściowe
ManureScoreHerdAnalysisMasterViewModel.Results=Wyniki
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Analiza Stada
ManureScoreHerdAnalysisMasterViewModel.Title=Zdrowotność Żwacza Ocena Odchodów
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Analiza Oceny Odchodów
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Ocena Odchodów
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Średnia
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Punktów Max.
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Punktów Min.
ManureScoreHerdEditGoalsViewModel.CloseUpDry=PRZED WYCIELENIEM (-20 DO -1)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=WCZESNA LAKTACJA (16-60)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Zamknij
ManureScoreHerdEditGoalsViewModel.FarOffDry=ZASUSZENIE WŁAŚCIWE (\#x20-21)
ManureScoreHerdEditGoalsViewModel.Fresh=PO WYCIELENIU (0 DO 15)
ManureScoreHerdEditGoalsViewModel.LateLactation=PÓŹNA LAKTACJA (&gt;201)
ManureScoreHerdEditGoalsViewModel.MaxGoal=Ocena odchodów Max.
ManureScoreHerdEditGoalsViewModel.MidLactation=ŚRODEK LAKTACJI (121 DO 200)
ManureScoreHerdEditGoalsViewModel.MinGoal=Ocena odchodów Min.
ManureScoreHerdEditGoalsViewModel.PeakMilk=SZCZYT LAKTACJI (61-120)
ManureScoreHerdEditGoalsViewModel.Title=Edytuj Cele
ManureScoreHerdGoalsViewModel.CloseUpDry=Przed Wycieleniem (-20 do -1)
ManureScoreHerdGoalsViewModel.EarlyLactation=Wczesna Laktacja (16-60)
ManureScoreHerdGoalsViewModel.Edit=Edytuj
ManureScoreHerdGoalsViewModel.FarOffDry=Zasuszenie Właściwe (\#x20-21)
ManureScoreHerdGoalsViewModel.Fresh=Po Wycieleniu (0 do 15)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Ocena Odchodów Cel Max.
ManureScoreHerdGoalsViewModel.GoalMinTitle=Ocena Odchodów Cel Min.
ManureScoreHerdGoalsViewModel.LateLactation=Późna Laktacja (&gt;201)
ManureScoreHerdGoalsViewModel.MidLactation=Środek Laktacji (121 do 200)
ManureScoreHerdGoalsViewModel.PeakMilk=Szczyt Laktacji (61-120)
ManureScoreHerdGoalsViewModel.TableTitle=Wynik Według Fazy Laktacji
ManureScorePenSelectionViewModel.ManureScoreTitle=Zdrowotność Żwacza Ocena Odchodów
ManureScorePenSelectionViewModel.PenSelectionList=KOJCE
ManureScorePercentOfPen=Manure Score(% of Pen)
ManureScoresChart=Ocena Odchodów - Wykres
ManureScoresResult=Ocena Odchodów - Wyniki
Maranhão=Maranhé £ o
Martinique=Martinique
Maryland=Maryland
Massa_and_Carrara=Massa i Carrara
Massachusetts=Massachusetts
Matera=Matera
Mato_Grosso=Mato Grosso
Mato_Grosso_do_Sul=Mato Grosso do sul
Mauritania=Mauritania
Mauritius=Mauritius
Max=MAX
Mayo=Mayo
Mayotte=Mayotte
Mcal=Mcal
Meath=Meath
Medio_Campidano=MEDIO CAMPIDANO
Medium=Medium
Meghalaya=Meghalaya
MenuViewModel.Close=Zamknij
MenuViewModel.LogoutPrompt=Wyloguj
MenuViewModel.Menu=Menu
MenuViewModel.ResetDatabaseCancel=Anuluj
MenuViewModel.ResetDatabasePrompt=To zastąpi wszystkie istniejące dane, w tym preferencje użytkownika ze startowym zestawem danych testowych. Narzędzia wizyty, nowo utworzone wizyty oraz mleczarnie zostaną utracone. Zostanież również wylogowany z aplikacji. Czy kontynuować?
MenuViewModel.ResetDatabaseReset=Zresetuj
MenuViewModel.ResetDatabaseTitle=Zresetuj Dane Testowe
MenuViewModel.Sync_PopUp=Synchronizacja danych. Proszę pozostawić aplikację otwartą na czas synchronizacji. Czas może się wydużyć przy słabym łączu.
Messina=Messina
MetabolicIncidenceChartsViewModel.Current=Obecnie
MetabolicIncidenceChartsViewModel.DeathLoss=Upadki
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Koszt Zaburzenia Metabolicznego/ Krowę
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Przemieszczenie Trawieńca
MetabolicIncidenceChartsViewModel.Dystocia=Trudne Porody
MetabolicIncidenceChartsViewModel.GoalPercent=Cel (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Zaburzenia metaboliczne (%)
MetabolicIncidenceChartsViewModel.IncidencePercent=Zaburzenie (%)
MetabolicIncidenceChartsViewModel.Ketosis=Ketoza
MetabolicIncidenceChartsViewModel.Metritis=Metritis
MetabolicIncidenceChartsViewModel.MilkFever=Gorączka mleczna
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Zatrzymanie Łożyska
MetabolicIncidenceChartsViewModel.Title=Wykresy Zaburzeń Metabolicznych
MetabolicIncidenceEditOutputsViewModel.Close=Zamknij
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Upadki
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Przemieszczenie Trawieńca
MetabolicIncidenceEditOutputsViewModel.Dystocia=Trudne Porody
MetabolicIncidenceEditOutputsViewModel.Ketosis=Ketoza
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Cel (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Metritis
MetabolicIncidenceEditOutputsViewModel.MilkFever=Gorączka mleczna
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Zatrzymanie Łożyska
MetabolicIncidenceEditOutputsViewModel.Title=Edytuj Cele
MetabolicIncidenceInputsEditViewModel.Close=Zamknij
MetabolicIncidenceInputsEditViewModel.DeathLoss=Upadki
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Przemieszczenie Trawieńca
MetabolicIncidenceInputsEditViewModel.Dystocia=Trudne Porody
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Dni Otwarte
MetabolicIncidenceInputsEditViewModel.Ketosis=Ketoza
MetabolicIncidenceInputsEditViewModel.Metritis=Metritis
MetabolicIncidenceInputsEditViewModel.MilkCow=Mleko /Krowę ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Gorączka mleczna
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Zatrzymanie Łożyska
MetabolicIncidenceInputsEditViewModel.Title=Edytuj Cechy Kosztów
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Koszty Leczenia
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Koszt Dodatkowych Dni Otwartych
MetabolicIncidenceInputsViewModel.Costs=Koszty
MetabolicIncidenceInputsViewModel.DeathLoss=Upadki
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Przemieszczenie Trawieńca
MetabolicIncidenceInputsViewModel.Dystocia=Trudne Porody
MetabolicIncidenceInputsViewModel.Herd=INFORMACJE O STADZIE
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Wprowadź liczbę wycielonych krów oraz zaburzeń metabolicznych w wyznaczonym okresie. Wyniki zostaną przeliczone na roczny koszt zaburzeń w tabeli wynikowej.
MetabolicIncidenceInputsViewModel.IncidenceCases=Przypadki Zaburzeń Metabolicznych
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Zwiększone Dni Otwarte
MetabolicIncidenceInputsViewModel.Ketosis=Ketoza
MetabolicIncidenceInputsViewModel.Mastitis=Mastitis
MetabolicIncidenceInputsViewModel.Metritis=Metritis
MetabolicIncidenceInputsViewModel.MilkFever=Gorączka mleczna
MetabolicIncidenceInputsViewModel.MilkLossKg=Strata Mleka w Laktacji ({0})
MetabolicIncidenceInputsViewModel.MilkPrice=Cena Mleka
MetabolicIncidenceInputsViewModel.PerformanceMessage=Dane referencyjne są wykorzystywane do obliczenia wpływu ekonomicznego każdego ze zaburzeń metabolicznych.
MetabolicIncidenceInputsViewModel.PerformanceTreatment=KOSZTY UŻYTKOWE I WETERYNARYJNE
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Koszt Sztuki Przeznaczonej na Remont Stada
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Zatrzymanie Łożyska
MetabolicIncidenceInputsViewModel.Title=Dane wejściowe zaburzeń metabolicznych
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Total Fresh Cows for Evaluation
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Ilość Wycieleń Krowy /Rok
MetabolicIncidenceInputsViewModel.TreatmentCost=Koszty Leczenia
MetabolicIncidenceMasterViewModel.Charts=Wykresy
MetabolicIncidenceMasterViewModel.Inputs=Dane Wejściowe
MetabolicIncidenceMasterViewModel.Outputs=Dane Wyjściowe
MetabolicIncidenceMasterViewModel.Title=Zaburzenia metaboliczne
MetabolicIncidenceOutputsViewModel.DeathLoss=Upadki
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Przemieszczenie Trawieńca
MetabolicIncidenceOutputsViewModel.Dystocia=Trudne Porody
MetabolicIncidenceOutputsViewModel.Ketosis=Ketoza
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Zaburzenia (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Koszt / Krowę
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Zwiększone Dni Otwarte
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Różnica (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Edytuj
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Cel (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Wpływ ekonomiczny zaburzeń metabolicznych jest powyżej poziomu zdefiniowanego w celach dla stada.
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Roczny Wpływ Ekonomiczny
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Roczny Wpływ Ekonomiczny - Całkowity
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Wartość Utratconego
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Procent zaburzeń metabolicznych
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Całkowity Koszt
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Koszty
MetabolicIncidenceOutputsViewModel.Metritis=Metritis
MetabolicIncidenceOutputsViewModel.MilkFever=Gorączka mleczna
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Zatrzymanie Łożyska
MetabolicIncidenceOutputsViewModel.Title=Zaburzenia metaboliczne Dane wyjściowe
MetabolicIncidenceOutputsViewModel.TotalLosses=Całkowite Straty Rocznie
Metric=Metryczne
MetricTonsAF=TON Świeżej Masy
MetricTonsDM=Metric TONS DM
Metritis=Metritis
Mexico=Meksyk
Mexico_State=Stan Meksyku
Michigan=Michigan
Michoacán=Michoacu
MidLactation=Środek Laktacji
MidOne=Średnie 1
MidOneValue=(8mm)
MidTwo=Średnie 2
Milan=Mediolan
Milk=Mleko ({0})
MilkChange=Zmiana Mlek ({0})
MilkFever=Gorączka mleczna
MilkLetDownResponse=Negatywna Odpowiedź w Mleku
MilkLossDay=Starta Mleka ({0}/Dzień)
MilkLossGain=Potencjalna Strata/Zysk Mleka
MilkLossKg=Strata Mleka (kg)
MilkLossPounds=Strata Mleka (lb)
MilkLossYear=Starta Mleka ({0}/Rok)
MilkPrice=Cena Mleka ({0}/{1})
MilkPricePremiums=Premie za Mleko
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=WYKRES REFERENCYJNY
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Dane Wejściowe
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Materiały
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Wyniki
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=kalkulator udojonego mleka
MilkProcessRevenueCalculatorMasterViewModel.Title=kalkulator udojonego mleka
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Notatnik
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Inadequate Stimulation
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Cena Mleka ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=No Stimulation
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Optimal Stimulation
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Scenariusz 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Scenariusz 2
MilkProcessorEditComparisonValuesViewModel.Title=Edytuj Wartości Porównywalne
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorInputViewModel.ComparisonValues=Wartości Porównywalne
MilkProcessorInputViewModel.Edit=Edytuj
MilkProcessorInputViewModel.MilkPrice=Cena Mleka ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=Wcześniej wybrana mleczarnia została usunięta. Proszę wybrać inną mleczarnię aby kontynuować.
MilkProcessorInputViewModel.ScenarioOne=Scenariusz 1
MilkProcessorInputViewModel.ScenarioTwo=Scenariusz 2
MilkProcessorInputViewModel.SelectProcessor=Wybierz Mleczarnię
MilkProcessorInputViewModel.Title=Dane wejściowe porównywania pozyskiwania mleka
MilkProcessorInputViewModel.WeightImperialCWT=CWT
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorResourcesViewModel.ApproxSCC=Szaczowany LKS (komórki/ml)
MilkProcessorResourcesViewModel.LinearScore=Linear Score (wynik liniowy)
MilkProcessorResourcesViewModel.Mastitis=Strata Mleka w Wyniku Mastitis ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Wykres Referencyjny
MilkProcessorResourcesViewModel.Title=kalkulator udojonego mleka- materiały
MilkProcessorResultsViewModel.Change=Zmień
MilkProcessorResultsViewModel.HundredWeight=CWT
MilkProcessorResultsViewModel.MilkPrice=Cena Mleka ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Roczne Zmiany Wartości
MilkProcessorResultsViewModel.ScenarioOne=Scenariusz 1
MilkProcessorResultsViewModel.ScenarioTwo=Scenariusz 2
MilkProcessorResultsViewModel.Title=kalkulator udojonego mleka - rezultat
MilkProcessorResultsViewModel.WeightImperialCWT=CWT
MilkProcessorResultsViewModel.WeightMetric=kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Komponenty
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Koncentracja
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Komponent
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Koncentracja
MilkProcessorSettingsMasterViewModel.MilkProcNew=Nowy
MilkProcessorSettingsMasterViewModel.Title=Ustawienia Mleczarni
MilkProcessorViewModel.Amount=Liczba (1000 kom / ml)
MilkProcessorViewModel.AmountCFU=Liczba (1000 cfu / ml)
MilkProcessorViewModel.BasePriceMilkFat=Tłuszcz Mleka ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Mleko ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Białko Mleka ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Inne komponenty ({0}/{1})
MilkProcessorViewModel.BasePrices=CENA PODSTAWOWA
MilkProcessorViewModel.ComponentProcessor=Komponent Mleczarnia
MilkProcessorViewModel.ConcentrationProcessor=Koncentracja Mleczarnia
MilkProcessorViewModel.Delete=Usuń
MilkProcessorViewModel.DeletePrompt=Usunięcie tej mleczarni wpłynie na Narzędzie Kalkulatora Dochodu z Mleka. Czy chcesz usunąć tą mleczarnię?
MilkProcessorViewModel.HundredWeight=CWT
MilkProcessorViewModel.Name=NAZWA
MilkProcessorViewModel.NameNotUnique=Mleczarnia "{0}" już istnieje. Nazwa musi być niepowtarzalna.
MilkProcessorViewModel.NewComponentProcessorName=Komponent Mleczarnia \#{0}
MilkProcessorViewModel.NewConcentrationProcessorName=Koncentracja Mleczarnia \#{0}
MilkProcessorViewModel.PricingMatrices=MATRYCE CEN
MilkProcessorViewModel.SelectCurrency=Wybierz Walutę
MilkProcessorViewModel.WeightImperial=LBS
MilkProcessorViewModel.WeightImperialCWT=CWT
MilkProcessorViewModel.WeightMetric=KG
MilkProduction=Produkcja Mleka ({0})
MilkProductionKg=Produkcja Mleka (kg)
MilkProductionPounds=Produkcja Mleka (lb)
MilkProductionRevenue=Dochód z Produkcji Mleka
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Wydajność Komponentów i Efektywność
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=PSM i Efektywność Żywienia
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=Tłuszcz Mleka % i Białko Mleka %
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Produkcja Mleka i Dni Laktacji
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Komórki Somatyczne i Mocznik Mleka
MilkSoldEvaluationChartsListViewModel.VisitComparison=Proszę wybrać wizytę dla porównania
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Efektywność Komponentów
MilkSoldEvaluationChartsViewModel.ComponentYield=Wydajność Komponentów
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Wydajność Komponentów i Efektywność
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=Pobranie Suchej Masy i Efektywność Żywienia
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Dni Laktacji  
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Pobranie Suchej Masy
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Efektywność Żywienia
MilkSoldEvaluationChartsViewModel.MilkFat=Tłuszcz Mleka (%)
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=Tłuszcz Mleka % i Białko Mleka %
MilkSoldEvaluationChartsViewModel.MilkProduction=Produkcja mleka
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Milk Production and Days in Milk
MilkSoldEvaluationChartsViewModel.MilkProtein=Białko Mleka (%)
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Mocznik w mleku
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Liczba komórek somatycznych
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Komórki Somatyczne i Mocznik Mekaa
MilkSoldEvaluationChartsViewModel.Title=Ocena Mleka Sprzedanego
MilkSoldEvaluationInputsViewModel.AddPickup=Add Pickup
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Liczba Zwierząt Dojonych do Zbiornika ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Dni Laktacji (DL) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Pobranie Suchej Masy ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=HERD LEVEL INFORMATION
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Krowy w Laktacji ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Odstawa Mleka ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Informacje z Mleczarni
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Pomiar Mocznika w Mleku ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Dodaj Nową
MilkSoldEvaluationMasterViewModel.Charts=Wykresy
MilkSoldEvaluationMasterViewModel.Inputs=Inputs
MilkSoldEvaluationMasterViewModel.Outputs=Outputs
MilkSoldEvaluationMasterViewModel.Title=Ocena Mleka Sprzedanego
MilkSoldEvaluationOutputsViewModel.AvgBCC=Średnia bakterii (1,000 cfu/mL)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Średni Tłuszcz w Mleku % 
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Średnia Produkcja Mleka, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Średnia Produkcja Mleka, {0} (liczba zwierząt dojonych do zbiornika)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Średnie Białko w Mleku % 
MilkSoldEvaluationOutputsViewModel.AvgSCC=Średnia LKS (1,000 kom/mL)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Efektywność Komponentów (%PSM)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Dni Oceny
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Efektywność Żywienia (stosunek)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Wydajność Białka+Tłuszczu w mleku ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Wydajność Tłuszczu ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Wydajność Białka ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=ZAKTUALIZUJ USTAWIENIA LOKALIZACJI
MilkSoldPickupViewModel.AnimalsInTank=Liczba Zwierząt Dojonych do Zbiornika ⃰
MilkSoldPickupViewModel.BCC=Liczba Bakterii (1,000 cfu/mL)
MilkSoldPickupViewModel.DaysInTank=Days in Tank ⃰
MilkSoldPickupViewModel.MilkFat=Tłuszcz Mleka (%)
MilkSoldPickupViewModel.MilkProtein=Białko Mleka (%)
MilkSoldPickupViewModel.MilkSold=Mleko Sprzedane, {0} ⃰
MilkSoldPickupViewModel.SCC=Liczba Komórek Somatycznych (1,000 kom/mL)
MilkSoldPickupViewModel.Title=Edytuj Odstawę {0}
MilkSoldSpinnerViewModel.Title=Ocena Mleka Sprzedanego
MilkUrea=Mocznik w Mleku (mg/dL)
Milking=W doju
MilkingFailure=Doje nieudane
MilkingFirst=Pierwszy dój
MilkingProcessRevenueInputs=kalkulator udojonego mleka - dane wejściowe
MilkingProcessRevenueResources=kalkulator udojonego mleka - materiały
MilkingProcessRevenueResults=kalkulator udojonego mleka - rezultat
Minas_Gerais=Minas Gerais
Minnesota=Minnesota
Mississippi=Mississippi
Missouri=Missouri
Mizoram=Mizoram
Mleka=
Modena=Modena
Moderate=Moderate
ModeratelyClean=Względnie Czysty
Moldova,_Republic_of=Mołdawia, Republika
Monaco=Monako
Monaghan=Monaghan
Mongolia=Mongolia
Montana=Montana
Montenegro=Czarnogóra
Monthly=Monthly
Montserrat=Montserrat
Monza_and_Brianza=Monza i Brianza
MoreThan8LayersOfPlastic=Więcej niż 8 warstw folii
Morelos=Morelos
Morocco=Maroko
Mozambique=Mozambik
Myanmar=Myanmar
NGN=NGN
NIO=Nikaragua (NIO NIO)
NOK=NOK
Nagaland=Nagaland
Namibia=Namibia
Naples=Neapol
Nauru=Nauru
Nayarit=Nayarit
Nebraska=Nebraska
Nei_Mongol=Nei Mongol
Nepal=Nepal
Netherlands=Holandia
Nevada=Nevada
NewBunker=Proszę podać nazwę nowego silosu.
NewDietClassViewModel.Title=Klasa/Podklasa Zwierzęcia
NewDietPensViewModel.AssociatePens=Możesz powiązać tą dawkę z wieloma kojcami.
NewDietPensViewModel.Title=Powiąż z Kojcem
NewDietViewModel.Cancel=Anuluj
NewDietViewModel.MainHeading=Nazwa Dawki ⃰
NewDietViewModel.Save=Zapisz
NewDietViewModel.Title=Nowa dawka
NewPenDietViewModel.New=Nowa
NewPenDietViewModel.Title=Diet
NewPenViewModel.Animals=Liczba Zwięrząt w Kojcu
NewPenViewModel.AnimalsInputsPen=Dane Wejściowe Zwierząt, Kojec
NewPenViewModel.AsFedIntake=Pobranie Świeżej Masy ({0})
NewPenViewModel.Barn=Nazwa Obory
NewPenViewModel.Cancel=Anuluj
NewPenViewModel.DaysInMilk=Dni Laktacji (DL)
NewPenViewModel.Diet=Diet
NewPenViewModel.DietInputsPen=Dane Wejściowe Dawki, Kojec
NewPenViewModel.DryMatterIntake=Pobranie Suchej Masy ({0})
NewPenViewModel.FeedingSystem=System Żywienia
NewPenViewModel.General=Ogólne
NewPenViewModel.HousingSystem=System Utrzymania
NewPenViewModel.InfoNewPenDetails=Add or update Pen specific data on this page. You can also add or update data within the tools as you use them. 
NewPenViewModel.Milk=Wydajność Mleka ({0})
NewPenViewModel.MilkingFrequency=Częstotliwość Doju
NewPenViewModel.NumberOfStalls=Liczba Stanowisk
NewPenViewModel.OnlyOnePen=Błąd autoryzacji, musisz się ponownie zalogować.
NewPenViewModel.PenDetail=Szczegóły Kojca
NewPenViewModel.PenMapping=Pen Mapping
NewPenViewModel.PenName=Nazwa Kojca
NewPenViewModel.PenSelection=Select Pen
NewPenViewModel.PublishPenAlert=Please publish all visit related to pen you want to merge.
NewPenViewModel.RationCostPerAnimal=Koszt Dawki na Zwierzę ({0})
NewPenViewModel.Save=Zapisz
NewPenViewModel.Title=Nowy Kojec
NewPenViewModel.UserCreatedPen=User Created Pen
NewPile=Proszę podać nazwę nowej pryzmy.
NewProspectViewModel.Address1=ADRES 1
NewProspectViewModel.Address2=ADRES 2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.BusinessName=NAZWA BIZNESOWA
NewProspectViewModel.City=MIASTO
NewProspectViewModel.ConsumerDetails=SZCZEGÓŁY KLIENTA
NewProspectViewModel.Country=PAŃSTWO
NewProspectViewModel.Customer=Klient
NewProspectViewModel.CustomerDetail=SZCZEGÓŁY KLIENTA
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=E-MAIL KONTAKTU
NewProspectViewModel.EndUser=End User
NewProspectViewModel.FarmProducer=Farm Producer
NewProspectViewModel.Image=Kliknij by edytować zdjęcie
NewProspectViewModel.InvalidEmail=Please enter a valid email.
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=Klient potencjalny "{0}" już istnieje. Nazwa musi być niepowtarzalna.
NewProspectViewModel.NameNotUniqueForConsumer=NAZWA KLIENTA "{0}" JUŻ ISTNIEJE. NAZWA MUSI BYĆ UNIKALNA.
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=- 
NewProspectViewModel.NullBusinessName=Business Name is required.
NewProspectViewModel.NullFirstName=First Name is required.
NewProspectViewModel.NullSecondName=Last Name is required.
NewProspectViewModel.PostalCode=KOD POCZTOWY
NewProspectViewModel.PrimaryContactFirstName=IMIĘ KONTAKTU
NewProspectViewModel.PrimaryContactLastName=NAZWISKO KONTAKTU
NewProspectViewModel.PrimaryPhone=NUMER TELEFONU KONTAKTU
NewProspectViewModel.Prospect=Klient Potencjalny
NewProspectViewModel.ProspectDetail=SZCZEGÓŁY KLIENTA POTENCJALNEGO
NewProspectViewModel.Segment=SEGMENT
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Spence=Spence
NewProspectViewModel.State=WOJEWÓDZTWO
NewProspectViewModel.Title=Szczegóły
NewProspectViewModel.Type=TYP
NewProspectViewModel.Walton=Walton
NewVisitViewModel.Title=Szczegóły Wizyty
New_Brunswick=Nowy Brunszwik
New_Caledonia=Nowa Kaledonia
New_Hampshire=New Hampshire
New_Jersey=New Jersey
New_Mexico=Nowy Meksyk
New_South_Wales=Nowa Południowa Walia
New_York=Nowy Jork
New_Zealand=Nowa Zelandia
Newfoundland_and_Labrador=Nowa Fundlandia i Labrador
Next=Następny
Nicaragua=Nikaragua
Niedersachsen=Dolna Saksonia
Niger=Niger
Nigeria=Nigeria
Ningxia=Ningxia
Niue=Niue
No=Nie
No-User-Found=Nie znaleziono użytkownika w LIFT; skontaktuj się z administratorem w celu rozwiązania
NoResourcesAvailable=Materiały niedostępne.
NoResults=Nie Znaleziono Wyników
NoWholeKernals=brak całych ziarniaków
Noah=Noah
None=Brak
NoneSelected=Nic Nie Wybrano
Nordrhein=Północna Ren
Norfolk_Island=Wyspa Norfolk
Normal=&lt;0.5BCS unit 
NorthAmerica=Ameryka północna
North_Carolina=Karolina Północna
North_Dakota=Północna Dakota
Northern_Territory=Północne terytorium
Northwest_Territories=Północno - zachodnie terytoria
Norway=Norwegia
Not-matching-with-allowed-values=Poza zakresem dopuszczalnych wartości
NotMeasured=Not measured
NotRemoved=Not removed
NotSet=-
NoteCamcorderNotImplemented=Opcja Nagrywania Nie Jest Jeszcze Dostępna
NoteCategoryViewModel.FooterText=TYLKO jedna kategoria może być wybrana na notatkę.
NoteCategoryViewModel.SelectCategory=WYBIERZ KATEGORIĘ
NoteCategoryViewModel.Title=Notatka
NotebookBCSHerdAnalysisGoals=BCS Cele Analizy Stada
NotebookBCSHerdAnalysisInputs=BCS Dane Wejściowe Analizy Stada
NotebookBCSHerdAnalysisResults=BCS Wyniki Analizy Stada
NotebookBCSSelectPointScale=BCS - wybierz skalę
NotebookBodyConditionEdit=BCS Edytuj Tabelę
NotebookCudCalculators=Zdrowotność Żwacza - Kalkulator Przeżuwania
NotebookCudChewing=Zdrowotność Żwacza - Przeżuwanie Wybór Kojca
NotebookCudChewingDataEntry=Zdrowotność Żwacza - Dane Wejściowe Przeżuwania
NotebookCudChewingResults=Zdrowotność Żwacza - Wyniki Przeżuwania
NotebookLocomotionEditTable=Ocena ruchu - Liczba Krów
NotebookLocomotionHerdInputs=Ocena ruchu - Analiza Stada Dane Wejściowe
NotebookLocomotionHerdResults=Ocena ruchu - Analiza Stada Wyniki
NotebookLocomotionHerdRevenue=Ocena ruchu - Analiza Stada Dochód
NotebookLocomotionLanding=Locomotion-Main
NotebookLocomotionPenInputs=Ocena ruchu - Dane Wejścioe
NotebookLocomotionPenResults=Ocena ruchu - Wyniki Kojca
NotebookLocomotionPenSelection=Ocena ruchu - Wybór Kojca
NotebookManurePenSelection=Ocena Odchodów - Wybór Kojca
NotebookManureScoreHerdAnalysisGoals=Ocena Odchodów Stado - Cele
NotebookManureScoreHerdAnalysisInputs=Ocena Odchodów Stado - Dane Wejściowe
NotebookManureScoreHerdAnalysisResults=Ocena Odchodów Stado - Wyniki
NotebookManureScoreLanding=Ocena Odchodów
NotebookMetabolicIncidenceCharts=Zaburzenia Metaboliczne - Wykresy
NotebookMetabolicIncidenceInputs=Zaburzenia Metaboliczne - Dane Wejściowe
NotebookMetabolicIncidenceOutputs=Metabolic Incidence Outputs
NotebookParticleScoreHerdAnalysisEdit=Ocena Struktury TMR Analiza Stada- Edytuj PSM
NotebookParticleScoreLanding=Ocena Struktury TMR
NotebookParticleScoreSelectPen=TMR Particle Score-Select Pen
NotebookParticleScoreSelectScorer=Ocena Struktury TMR - Wybór Sit
NotebookPenTimeComparison=Zarządzanie Czasem W Kojcu - Porównanie
NotebookPenTimeInputs=Zarządzanie Czasem W Kojcu - Dane Wejściowe
NotebookPenTimePenSelection=Zarządzanie Czasem W Kojcu- Wybór Kojca
NotebookPenTimeResults=Zarządzanie Czasem W Kojcu - Wyniki
NotebookReadyToMilkCharts=ReadyToMilk Charts
NotebookReadyToMilkInputs=ReadyToMilk Inputs
NotebookReadyToMilkOutputs=ReadyToMilk Outputs
NotebookRumenHealthNumberOfChewsInput=Zdrowotność Żwacza - Ilość Przeżuć Dane Wejściowe
NotebookRumenHealthNumberOfChewsResults=Zdrowotność Żwacza - Ilość Przeżuć Wyniki
NotebookRumenHealthTMRParticlePercent=TMR Procent Na Sicie
NotebookRumenHealthTMRParticleScore=TMR Particle Score
NotebookSectionComfortTools=Narzędzia Komfortu
NotebookSectionForageAudit=Karta Oceny Audytu Pasz Objętościowych
NotebookSectionForageAuditBaleage=Audyt Pasz Objętościowych - Baloty
NotebookSectionForageAuditBunkersPiles=Audyt Pasz Objętościowych - Silosy i pryzmy
NotebookSectionForageAuditHarvest=Audyt Pasz Objętościowych - Zbiór
NotebookSectionForageAuditLanding=Ocena Pasz Objętościowych
NotebookSectionForageAuditMaintainingQuality=Audyt Pasz Objętościowych - Zarządzanie jakością pasz
NotebookSectionForageAuditSilageBags=Audyt Pasz Objętościowych - Rękawy
NotebookSectionForageAuditSurveyOfForages=Audyt Pasz Objętościowych - Ocena pasz objętościowych
NotebookSectionForageAuditTowerSilos=Audyt Pasz Objętościowych - Silosy wieżowe
NotebookSectionHealthTools=Health Tools
NotebookSectionHeatstressCalculations=Stres Cieplny - Kalkulacje
NotebookSectionHeatstressChart=Stres Cieplny - Wykresy
NotebookSectionHeatstressData=Stres Cieplny - Dane
NotebookSectionHerdAnalysisGoals=Zdrowotność Żwacza - Cele Analizy Stada
NotebookSectionMilkSoldEvaluationCharts=Ocena Mleka Sprzedanego - Wykresy
NotebookSectionMilkSoldEvaluationEditPickup=Ocena Mleka Sprzedanego - Edytuj Odstawę
NotebookSectionMilkSoldEvaluationInputs=Ocena Mleka Sprzedanego - Dane Wejściowe
NotebookSectionMilkSoldEvaluationOutputs=Ocena Mleka Sprzedanego - Dane Wyjściowe
NotebookSectionNutritionTools=Narzędzia Żywieniowe
NotebookSectionRumenHealthLanding=Zdrowotność Żwacza
NotebookSectionVisit=Wizyta
NotebookTMRParticleHerdAnalysisPenInputs=Ocena Struktury TMR Analiza Stada- Dane Wejściowe
NotebookTMRParticleHerdAnalysisPenResults=Ocena Struktury TMR Analiza Stada- Wyniki
NotebookUrinePHEditGoals=pH moczu edytuj cele
NotebookUrinePHInputs=Dane wejściowe pH moczu
NotebookUrinePHOutputs=Wyniki pH moczu
NotebookVisitSummary=Podsumowanie Wizyty
NotebookWalkthroughReport=Raport Z Wizyty
NotebookWalkthroughReportLanding=Raport Z Wizyty
NotebookWalkthroughReportPen=Raport Z Wizyty - Analiza Kojca
Nova_Scotia=Nowa Szkocja
Novara=Novara
Nuevo_León=Nowy Leín
Null-values-not-allowed=Wartości zerowe nie są dozwolone
NumChewsGoal={0} Cel
NumOfCows=Liczba Krów
NumberOfChewsReportsViewModel.Average=Średnio \# Przeżuć
NumberOfChewsReportsViewModel.AverageChews=Przeżuwanie Średnio
NumberOfChewsReportsViewModel.AverageNumberChews=Średnio \# Przeżuć
NumberOfChewsReportsViewModel.DateDescription=Data
NumberOfChewsReportsViewModel.DatesComparison=Daty do Porównania
NumberOfChewsReportsViewModel.EditVisits=Wybierz
NumberOfChewsReportsViewModel.StdDevCalculated=Odchylenie Standardowe (obliczone)
NumberOfChewsReportsViewModel.VisitDate=Data
NumberOfChewsViewModel.Count=Liczba
NumberOfChewsViewModel.CountHeader=Proszę policzyć liczbę przeżuć dla tej krowy.
NumberOfChewsViewModel.NextCow=Następna Krowa
NumberOfChewsViewModel.NumberOfChewsCow=Liczba Przeżuć/ Krowę \#
NumberOfChewsViewModel.Title=Liczba przeżuć/ Krowę \# {0}
NumberOfChewsViewModel.ValidCudInput=Please enter a valid input.
NumberOfCows=Number of Cows
Nunavut=Nunavut
Nuoro=Nuoro
NutritionViewModel.NutritionForage=Audyt Pasz Objetościowych
NutritionViewModel.NutritionLabel=Wybierz narzędzie z poniższej listy, aby rozpocząć wizytę.
NutritionViewModel.NutritionPile=Pojemność Pryzmy i silosu
NutritionViewModel.NutritionTools=Narzędzia Żywieniowe
NutritionViewModel.NutritionToolsCaption=Narzędzia
NutritionViewModel.NutritionToolsInstructions=Proszę wybrać kategorię lub narzędzie z listy poniżej by rozpocząć wizytę
NutritionViewModel.NutritionToolsList=Narzędzia
NutritionViewModel.Title=Narzędzia Żywieniowe
NutritionViewModel.VisitNotebook=Notatnik
OKLabel=OK
Oaxaca=Oxaca
Observation=Obserwacja
Odisha=Odisha
Offaly=Offaly
Ogliastra=Ogliastra
Ohio=Ohio
Oklahoma=Oklahola
Olbia-Tempio=Olbia-Tempio
Oman=Własny
Once=the Diet is created, select the Class / Subclass of animal associated with the Diet.
OncePerWeek=raz w tygodniu
One=Jedna
OneHourBeforeActionIsDue=Godzinę przed terminem akcji
OneToSixHours=1-6 godzin
Ontario=Ontario
Opportunities=Możliwości
Optimal=Optymalna odpowiedź
Oregon=Oregon
Oristano=Oristano
Other=Inna
OtherSilage=Other Silage
Overall=Ogólne
OverallCalfHeiferDetails=Aby wyświetlić ogólną kartę oceny cieląt i jałówek, najpierw wypełnij co najmniej jedno z powyższej listy.
OverallForageScoreDetails=Aby wyświetlić ogólną ocenę paszy objętościowej, najpierw wypełnij przynajmniej jedną z ocen na powyższej liście.
PDFDisclaimer=Cargill, Inc. Oraz jego jednostki nie gwarantują dokładności tych szacunków, ze względu na wiele czynników. Nie ma gwarancji w produkcji oraz wynikach finansowych.  ©{0} Cargill, Incorporated. Wszelkie prawa zastrzeżone
PDFDisclaimer_ProvimiUS=Provimi North America, Oraz jego jednostki nie gwarantują dokładności tych szacunków, ze względu na wiele czynników. Nie ma gwarancji w produkcji oraz wynikach finansowych. ©{0} Provimi North America. Wszelkie prawa zastrzeżone
PDFPageNumber=Strona {0} of {1}
PEN=Peru (S/. PEN)
PHP=Filipiny ($ PHP)
PLN=Polska (zł PLN)
PMRConcentrate=PMR + Pasza Treściwa
PON=Rumunia (lei PON)
Padua=Padwa
Pakistan=Pakistan
Palermo=Palermo
Palestinian_Territory,_Occupied=Teretorium Paleństynskie, Okupowane
Panama=Panama
Papua_New_Guinea=Papua Nowa Gwinea
Paraguay=Paragwaj
Paraná=Paranã
Paraíba=Paraãba
Parlor=Hala Udojowa
Parma=Parma
ParticleScorePreviousVisitsViewModel.MidOne=Średnie 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Średnie 2
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Procent na Sici (%)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Mid 1 (8mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Mid 2 (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Top (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Tray
ParticleScorePreviousVisitsViewModel.SelectDates=Wybierz Daty
ParticleScorePreviousVisitsViewModel.Top=Góra
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.Tray=Taca
Pará=Paraatory
PasteurizedWholeMilk=Mleko pełne pasteryzowane karmione
Pasto=Pasto
Pasture=Pastwisko
PastureOther=Pastwisko + Inne
Pavia=Pavia
Pays=Płaci
PeakMilk=Szczyt Laktacji
Pen=Setup data will be updated automatically for sites with farm data downloads in Dairy Enteligen. 
PenDetailViewModel.Title=Szczegóły Kojca
PenListViewModel.DietSetup=USTAWIENIA DAWKI
PenListViewModel.MainHeading=KOJCE
PenListViewModel.NewPen=Dodaj Nowy Kojec 
PenListViewModel.Title=Kojce
PenName=Nazwa Kojca
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Zmiana BCS(per 100 days)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Zmiana Masy Ciała ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Krów w Kojcu
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Krów Wydojonych na Godzinę
PenTimeBudgetComparisonViewModel.Current=Obecny
PenTimeBudgetComparisonViewModel.EnergyChange=Zmiana Energii (Mcal)
PenTimeBudgetComparisonViewModel.Overcrowding=Nadmierna Obsada (%)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Obrotów Hali na Godzinę
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Potencjalna Strata/Zysk Mleka ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Różnica w Odpoczynku (h)
PenTimeBudgetComparisonViewModel.TableTitle=Porównanie
PenTimeBudgetComparisonViewModel.TimePerMilking=Długość Doju (h)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Czas Pozostający na Odpoczynek (h)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Czas Wymagany na Odpoczynek (h)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Całkowity Czas Braku Odpoczynku (h)
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Całkowity Czas Doju (h)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Czas na Zanlezienie Legowiska (h)
PenTimeBudgetPenMasterViewModel.Compare=Porównaj
PenTimeBudgetPenMasterViewModel.Inputs=Dane Wejściowe
PenTimeBudgetPenMasterViewModel.Results=Wyniki
PenTimeBudgetPenMasterViewModel.Title=Zarządzanie czasem w kojcu
PenTimeBudgetResultsViewModel.Hours=Godziny
PenTimeBudgetResultsViewModel.MilkDifference=Potencjalna Różnica Mleka
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=lbs
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Potencjalna Strata/Zysk Mleka
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Czas Dostępny na Odpoczynek
PenTimeBudgetResultsViewModel.TimeRemaining=Czas Pozostały
PenTimeBudgetResultsViewModel.TimeRequired=Czas Wymagany
PenTimeBudgetResultsViewModel.Title=Zarządzanie Czasem w Kojcu- wyniki
PenTimeInputsViewModel.CowsPen=Krów w Kojcu
PenTimeInputsViewModel.Drinking=Czas Pobierania Wody/Czochrania (h)
PenTimeInputsViewModel.Eating=Czas Pobierania Paszy (h)
PenTimeInputsViewModel.Frequency=Częstotliwość Doju (Dzienie)
PenTimeInputsViewModel.LockUp=Czas w Zamknięciu (h)
PenTimeInputsViewModel.NonRestTime=Inny czas braku odpoczynku (h)
PenTimeInputsViewModel.ParlorTime=Czas na Hali Udojowej (h)
PenTimeInputsViewModel.PenTimeTitle=Zarządzanie czasem w kojcu
PenTimeInputsViewModel.Resting=Czas Wymagany na Odpoczynek (h)
PenTimeInputsViewModel.StallsPen=Legowiska w Kojcu
PenTimeInputsViewModel.TotalStalls=Liczba Miejsc na Hali Udojowejr
PenTimeInputsViewModel.WalkingTimeFrom=Czas Powrotu z Doju (h)
PenTimeInputsViewModel.WalkingTimeTo=Czas Chodzenia do Doju (h)
PenTimePenSelectionViewModel.NoLactatingPen=By użyć tego narzędzia upewnij się, że dysponujesz przynajmniej jednym kojcem z dawką laktacyjną.
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Zarządzanie czasem w kojcu
PenTimePenSelectionViewModel.PenTimeSection=KOJCE
PenTimePenSelectionViewModel.Title=Zarządzanie czasem w kojcu
Pendetails=Pen Details
PennStateShakerBoxForageResults=Sita Penn State - wyniki pasz objętościowych
Pennsylvania=Pensylwania
Pens=Kojce
Per=Średnio
PerceivedHeatStressDietInfoMessage=Moderate heat stress\: panting, drool or foam but no open mouth, respiration rate 40 to 120 bpm
PercentLossPerCow=% Straty/Krowę
PercentOnScreenTitle=Procent na sicie (%)
PercentPen=Procent Kojca (%)
PercentageOnScreen=Procent na Sicie (%)
PercentageOnScreenCurrentVisit=Procent na sicie (%) - Aktualna wizyta
PercentageOnScreenTrend=Procent na sicie (%) - Tendencja
Pernambuco=Pernambuco
Peru=Peru
Perugia=Perugia
Pesaro_and_Urbino=Pesaro i Urbino
Pescara=Pescara
Philippines=Filipiny
PhotoExamples=Przykłady zdjęć
Piacenza=Piacenza
Piauí=Piauã
Pickup=Odstawa {0}
Pile=Pryzma
PileAndBunkerCapacitiesDensity=Pile and bunker capacities density reference guide
PileAndBunkerCapacity=Pojemność Pryzmy i Silosu
PileAndBunkerCapacityViewModel.AddBunker=Dodaj Silos
PileAndBunkerCapacityViewModel.AddPile=Dodaj Pryzmę
PileAndBunkerCapacityViewModel.Bunker=Silos
PileAndBunkerCapacityViewModel.Bunkers=Silosy
PileAndBunkerCapacityViewModel.NameNotUnique=Pryzma lub silos "{0}" już istnieje. Nazwa musi być niepowtarzalna.
PileAndBunkerCapacityViewModel.NameTooLong=Nazwa pryzmy lub silosu musi mieć maksymalnie 40 znaków.
PileAndBunkerCapacityViewModel.Pile=Pryzma
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Pojemność Pryzmy i Silosu
PileAndBunkerCapacityViewModel.Piles=Pryzmy
PileAndBunkerCapacityViewModel.Resources=RESOURCES
PileAndBunkerCapacityViewModel.Title=Pojemność Pryzmy i Silosu
PileAndBunkerCapacityViewModel.VisitNotebook=Notatnik
PileAndBunkerName=PILES AND BUNKER NAME
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Długość Dołu ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Szerokość Dołu ({0})
PileAndBunkerResultsCapacityInputViewModel.Capacity=Pojemność
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=Sucha Masa (%)
PileAndBunkerResultsCapacityInputViewModel.Height=Wysokość ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=TON Świeżej Masy
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Metric TONS DM
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Długość Dołu (stopy)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Długość Dołu (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Szerokość Dołu (stopy)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Szerokość Dołu (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Wysokość (stopy)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Wysokość (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Długość Góry (stopy)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Długość Góry (m)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Szerokość Góry (stopy)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Szerokość Góry (m)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Gęstość SM Kiszonki ({0})
PileAndBunkerResultsCapacityInputViewModel.Title=Pojemność
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Gęstość Świeżej Masy Kiszonki {0} (Cel\: &gt; 44)
PileAndBunkerResultsCapacityInputViewModel.TonsAF=TON Świeżej Masy
PileAndBunkerResultsCapacityInputViewModel.TonsDM=TONS DM
PileAndBunkerResultsCapacityInputViewModel.TopLength=Długość Góry ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Szerokość Góry ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=6 cali/Dzień
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=15cm/Dzień
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=3 cale/Dzień
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=7cm/Dzień
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=ZAPOTRZEBOWANIE KROWY/DZIEŃ
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Ilość Śywionych Krów
PileAndBunkerResultsFeedOutViewModel.DateGone=Date Gone
PileAndBunkerResultsFeedOutViewModel.Days=Days
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=INFORMACJE O ZUŻYCIU PASZ
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Powierzchnia Skarmiania (ft^2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Powierzchnia Skarmiania (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Tempo Żywenia (świeża masa/krowę)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=Cali/Dzień
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=Cm/Dzień
PileAndBunkerResultsFeedOutViewModel.StartDate=Start date
PileAndBunkerResultsFeedOutViewModel.Title=Skarmianie
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tons Per Day
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Funtów SM w 1 Stopie
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kg SM w 1 Metrze
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0.0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Pojemność
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Skarmianie
PileAndBunkerResultsMasterViewModel.VisitNotebook=Notatnik
PileAndBunkerTitle=Pile and Bunker
PileBunkerCapacities=Pojemność Pryzmy i silosu
PileCapacity=Pojemność Pryzmy
PileFeedOutRate=Opróżnianie Pryzmy
Piles=Pryzmy
Pisa=Pisa
Pistoia=Pistoia
Pitcairn=Pitcairn
PlBacteriaCell=Bakterie
PlMilkFat=Tłuszcz Mleka
PlMilkProtein=Białko Mleka
PlSomaticCell=Komórki Somatyczne
Placenta=
Poland=Polska
Poor=Słaby
Pordenone=Pordenone
Porosity=Porowatość
Portugal=Portugalia
Postweaned=Po odsadzeniu
Postweaned_CleanAndDryPen=Czyste i suche kojce
Postweaned_CleanAndDryPen_ToolTip=Użyj testu "mokrego kolana", aby określić, czy obszar jest czysty i suchy
Postweaned_EvidenceOfAcidosisInManure=Ślady kwasicy w odchodach
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Czy są bąbelki w luźnych odchodach?
Postweaned_EvidenceOfScoursOrPneumonia=Ślady biegunek lub chorób płucnych
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% cieląt ma biegunki lub choroby płucne
Postweaned_FeedBunkIsClaanedDaily=Stół paszowy (koryto) czyszczone codzienne, a niewyjady usuwane
Postweaned_ForageAvailability=Dostępność kiszonki
Postweaned_ForageAvailability_ToolTip=Dostępność kiszonki
Postweaned_FreeChoiceCleanWaterIsAvailable=Wolny wybór, czysta woda do woli
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=Brak śladów zanieczyszczenia wody
Postweaned_FreshQualityStarterAvailable=Świeży, dobry jakościowo starter jest dostępny
Postweaned_FreshQualityStarterAvailable_ToolTip=Starter jest pozbawiony zanieczyszczeń oraz nie jest mokry
Postweaned_SizeOfBunkSpace=Wielkość stołu paszowego dostosowana do wielkości cielęcia
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45cm na cielę
Postweaned_SizeOfPenAdequate=Wielkość kojca dostosowana do wielkości jałówki
Postweaned_SizeOfPenAdequate_ToolTip=Kojec indywidualny\: 3m2, Kojec grupowy\: 2.75m2
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Dobrze wentylowane kojce bez przeciągów
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=Jeśli odzież ma zapach amoniaku po wyjściu z obiektu jego stężenie jest zbyt wysokie 
PotentialDownResponse=Potential Let Down Response ({0}/cow/day)
PotentialSCC=Potencjalna LKS (komórki/{0})
Potenza=Moc
Prato=Danie
PreWeaned_CMRisProperlyMixed_ToolTip=Ilość preparatu mlekozastępczego (PM)\: &gt;600g &lt;800g, Temp\: 39-41C, Solids 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Użyj testu "mokrego kolana", aby określić, czy obszar jest czysty i suchy
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% cieląt ma biegunki lub choroby płucne
PreWeaned_Forageavailability_ToolTip=Jeśli jest pasza teksturyzowana nie ma potrzeby kiszonek
PreWeaned_FreeChoiceCleanWater_ToolTip=Dostęp od pierwszego dnia, nie ma śladów zanieczyszczenia wody
PreWeaned_FreeChoiceFreshCalf_ToolTip=Starter jest pozbawiony zanieczyszczeń oraz nie jest mokry
PreWeaned_SizeOfPen_ToolTip=Kojec indywidualny\: 3m2, Kojec grupowy\: 2.5m2
PreWeaned_WellVenilated_ToolTip=Jeśli odzież ma zapach amoniaku po wyjściu z obiektu jego stężenie jest zbyt wysokie 
PrematureKelvingsKeyInfoMessage=The delivery of one or more alive calves at least 10 days before due delivery date.
PreventingStorageLosses=Zapobieganie stratom magazynowym
Previous=Poprzedni
Preweaned=Przed odsadzeniem
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=PM jest prawidłowo mieszany i odpowiednio karmiony
Preweaned_CleanAndDryPen=Czyste i suche kojce
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Czysty i dezynfekowany sprzęt między karmieniami
Preweaned_ConsistentFeedingTimesAndProtocols=Stała pora karmienia
Preweaned_EvidenceOfScoursOrPneumonia=Ślady biegunek lub chorób płucnych
Preweaned_ForageAvailability=Dostępność kiszonki
Preweaned_FreeChoiceCleanWaterIsAvailable=Wolny wybór, czysta woda do woli
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Świeży, dobry jakościowo starter jest dostępny
Preweaned_SizeOfPenAadequatePerHeifer=Wielkość kojca dostosowana do wielkości jałówek
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Odsadzenie w momencie pobrania 1 kg paszy starter/dzień
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Dobrze wentylowane kojce bez przeciągów
PricingMatrixEditViewModel.Cancel=Anuluj
PricingMatrixEditViewModel.Save=Zapisz
PricingMatrixEditViewModel.Title=Szczegóły Macierzy
PricingMatrixPickListViewModel.PricingMatrix=Macierz Cen
PricingMatrixViewModel.Amount=Liczba (1000 kom/ml)
PricingMatrixViewModel.AmountCFU=Liczba (1000 cfu/ml)
PricingMatrixViewModel.New=Nowy
PricingMatrixViewModel.Title=Edytuj Macierz
Prince_Edward_Island=Wyspa Księcia Edwarda
Privacy_Statement=Oświadczenie o Prywatności
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Waluty
ProcessorCurrencyPickListViewModel.Title=Waluty
ProductivityToolsViewModel.MilkProcessRevenueCalculator=kalkulator udojonego mleka
ProductivityToolsViewModel.MilkRevenueAnalysis=Analiza Dochodu z Mleka
ProductivityToolsViewModel.MilkSoldEvaluation=Ocena Mleka Sprzedanego
ProductivityToolsViewModel.ProductivityTitle=Analiza Dochodu z Mleka
ProductivityToolsViewModel.ProductivityTools=NARZĘDZIA
ProductivityToolsViewModel.VisitNotebook=Notatnik
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=Anuluj
PromptOK=Ok
ProspectProfileViewModel.DeleteProspect=Usuń Klienta Potencjalnego
ProspectProfileViewModel.DeleteProspectPrompt=Czy jesteś pewny, że chcesz usunąć Klienta Potencjalnego? Klient Potencjalny, jego lokalizacje i informacje o wizytach w toku zostaną utracone.
ProspectProfileViewModel.MainHeading=LOKALIZACJE
ProspectProfileViewModel.NewSite=Dodaj Nową Lokalizację
ProspectProfileViewModel.ProspectInfo=Informacje Klienta Potencjalnego
ProspectProfileViewModel.ProspectTitle=Szczegóły Klienta Potencjalnego
ProspectsViewModel.NewProspect=Dodaj Nowego Klienta Potencjalnego
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=Provimi
ProvimiUS=Provimi US
Przeżuwających=
Przeżuwania=
PublishVisit=Opublikowano
Puducherry=Puducherry
Puebla=Puebla
Puerto_Rico=Portoryko
Punjab=Pendżab
Purina=Purina
Qatar=Katar
Qinghai=Qinghai
QuarterPointScale=Skala 0.25 punktu
Quarterly=Kwartalnie
Quebec=Quebec
Queensland=Queensland
Querétaro=Zapytanie é Taro
QuestionTableTitle=Pytanie {0} of {1}
QuestionViewModel.Baleage=Baloty
QuestionViewModel.BunkersAndPiles=Silosy i Pryzmy
QuestionViewModel.Close=Zamknij
QuestionViewModel.Harvest=Zbiór
QuestionViewModel.MaintainingForageQuality=Utrzymanie Jakości Paszy Objętościowej
QuestionViewModel.SilageBags=Rękawy Kiszonkowe
QuestionViewModel.SurveyOfForages=Badania Pasz Objętościowych
QuestionViewModel.TowerSilos=Silos Wieżowy
QuestionViewModel.VisitNotebook=Notatnik
Quintana_Roo=Quintana Roo
ROL=ROL
RUB=Rosja (₽‎ RUB)
Ragusa=Ragusa
Rajasthan=Radżastan
Rationcost=Ration Cost ({0})
Ravenna=Ravenna
ReadyToMilkChartViewModel.Current=Obecnie
ReadyToMilkChartViewModel.DeathLoss=Mastitis
ReadyToMilkChartViewModel.DisorderGraphTitle=Roczne Koszty Zaburzeń Metabolicznych Koszt/ Krowę
ReadyToMilkChartViewModel.DisplacedAbomasum=Displaced
ReadyToMilkChartViewModel.Dystocia=Trudne Porody
ReadyToMilkChartViewModel.Ketosis=Ketoza
ReadyToMilkChartViewModel.Metritis=Metritis
ReadyToMilkChartViewModel.MilkFever=Milk
ReadyToMilkChartViewModel.RetainedPlacenta=Retained
ReadyToMilkChartViewModel.Title=Ready2Milk™ Charts
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Ready2Milk™ Indeks
ReadyToMilkInputViewModel.Back=Cofnij
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Zmiany BCS Zasuszenie Właściwe do 21 dni po Wycieleniu
ReadyToMilkInputViewModel.CloseUp=Krowy Przed Wycieleniem
ReadyToMilkInputViewModel.ComfortCloseUp=Komfort Przed Wycieleniem
ReadyToMilkInputViewModel.ComfortDiet=Komfort po Wycieleniu 0-21 dni
ReadyToMilkInputViewModel.CostExtraDaysOpen=Cost of Extra Days Open
ReadyToMilkInputViewModel.CudChewingDiet=Przeżuwanie Krowy po Wycieleniu
ReadyToMilkInputViewModel.DeadCowsOrCulled=Upadki i brakowanie z przyczyn zdrowotnych
ReadyToMilkInputViewModel.DisplacedAbomasum=Przemieszczenie Trawieńca
ReadyToMilkInputViewModel.Dystocia=Trudne Porody
ReadyToMilkInputViewModel.FreshCows=Krowy po Wycieleniu
ReadyToMilkInputViewModel.Health=ZDROWOTNOŚĆ
ReadyToMilkInputViewModel.HealthDesc=Enter the number of fresh cows and the number of metabolic incidence cases during the evaluation period. This will be converted to an annualized incidence cost on the Outputs tab.
ReadyToMilkInputViewModel.HealthRecords=Health records
ReadyToMilkInputViewModel.Herd=HERD LEVEL INFORMATION
ReadyToMilkInputViewModel.Ketosis=Ketoza
ReadyToMilkInputViewModel.LocomotionScore=Ocena Ruchu
ReadyToMilkInputViewModel.Mastitis=Mastitis 0-21 dni
ReadyToMilkInputViewModel.Metritis=Metritis
ReadyToMilkInputViewModel.MilkFever=Gorączka mleczna
ReadyToMilkInputViewModel.MilkPrice=Milk Price
ReadyToMilkInputViewModel.MilkYield=Wydajność Mleczna 15-60 dni Laktacji
ReadyToMilkInputViewModel.Next=Następny
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Przewidywany Stres Cieplny po Wycieleniu 0-21 dni
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Przewidywany Stres Cieplny Przed Wycieleniem
ReadyToMilkInputViewModel.PrematureCalvings=Przedwczesne Wycielenia
ReadyToMilkInputViewModel.ReplacementCowCost=Replacement Cow Cost
ReadyToMilkInputViewModel.RetainedPlacenta=Zatrzymanie Łożyska
ReadyToMilkInputViewModel.RumenFill=Wypełnienie Żwacza
ReadyToMilkInputViewModel.SccFirstTest=Pierwszy test LKS (x1000 kom/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Osobna Dawka Przed Wycieleniem
ReadyToMilkInputViewModel.SpecificDiet=Osobna Dawka po Wycieleniu 0-21 dni
ReadyToMilkInputViewModel.Title=Ready2Milk™ Inputs
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Total Fresh Cows/Year
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Całkowita Liczba Krów Wycielonych do Oceny
ReadyToMilkListViewModel.Title=Ready2Milk™ Indeks
ReadyToMilkMasterViewModel.Charts=Wykresy
ReadyToMilkMasterViewModel.Inputs=Inputs
ReadyToMilkMasterViewModel.MastitisNotPresent=Mastitis is not filled.
ReadyToMilkMasterViewModel.Outputs=Outputs
ReadyToMilkMasterViewModel.Title=Ready2Milk™ Indeks
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Ready2Milk™ Indeks
ReadyToMilkOutputViewModel.Title=Ready2Milk™ Outputs
RecommendedTLCSettings=Ustawienia zbioru
RefreshTokenFailed=Błąd autoryzacji, musisz się ponownie zalogować.
Reggio_Calabria=Reggio Calabria
Reggio_Emilia=Reggio Emilia
RemovedAndMeasured=Removed and measured
RemovedOnly=Removed only
Report=Raport
Report.Analysis.Type=Typ analizy
Report.Animal.Analysis=Analiza zwierząt
Report.Animal.Tag.Name=Identyfikator zwierząt
Report.AvgRumenFillScore=Średnie wypełnienie żwacza
Report.BCS.EvalDataTitle=Obliczone dane oceny BCS
Report.BCS.LactationStages=Etapy laktacji
Report.BCS.Max=Max. BCS
Report.BCS.MilkHeadDay=Mleko/HD/dzień
Report.BCSAvg=Średni BCS
Report.Bcs=BCS
Report.Bcs.ChartName=Ocena stanu ciała - zwierzę {0}
Report.Bcs.HerdAnalysis.ChartName=BCS vs Mleko
Report.Bcs.Milk=mleko
Report.Bcs.Min=Min. BCS
Report.Calving.Date=Cielenie się
Report.Cargill.Report=Cargill raport
Report.Chewing=Przeżuwania
Report.Chews=Żucie
Report.CudChewing.EvalDataTitle=Obliczone dane oceny żucia CUD
Report.CudChewingPercentage=Przeżuwanie % /
Report.CudChewingPercentage.Vs.LactStages=Przeżuwanie % /
Report.EvalDataTitle=Obliczone dane oceny
Report.ForagePennState=Penn State - pasze objętościowe
Report.General.Comments=Ogólne Komentarze
Report.GoalCudChewingPercentage=Cel Przeżuwanie % /
Report.Heatstress.Dmi.Adjustment=Regulacja DMI
Report.Heatstress.Energy.Equivalent.Milk.Loss=Energia równoważna utrata mleka ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Szacowane spożycie suchej materii ({0})
Report.Heatstress.Intake.Adjustment=Dostosowanie wlotu
Report.Heatstress.Legend=Legenda
Report.Heatstress.Legends=Legendy
Report.Heatstress.Loss.Of.Energy.Consumed=Utrata energii zużywanej (MCAL)
Report.Heatstress.Mild.Moderate.Stress=Łagodny - umiarkowany stres
Report.Heatstress.Mild.Moderate.Stress.Message=Oddychanie przekracza 75 BPM | Temperatura odbytnicy przekracza 39 \\ u2103 (102,2 \\ u2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Utrata wartości mleka (miesięcznie) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Utrata wartości mleka (dziennie) ({0})
Report.Heatstress.Moderate.Severe.Stress=Umiarkowany - silny stres
Report.Heatstress.Moderate.Severe.Stress.Message=Oddychanie przekracza 85 BPM | Temperatura odbytnicy przekracza 40 \\ u2103 (104 \\ u2109)
Report.Heatstress.Reduction.In.Dmi=Zmniejszenie DMI ({0})
Report.Heatstress.Relative.Humidity=Wilgotność względna (%)
Report.Heatstress.Severe.Stress=Poważny stres
Report.Heatstress.Severe.Stress.Message=Oddychanie przekracza 120-140 BPM | Temperatura odbytnicy przekracza 41 \\ u2103 (106 \\ u2109)
Report.Heatstress.Stress.Threshold=Próg stresu
Report.Heatstress.Stress.Threshold.Message=Oddychanie przekracza 60 BPM | Wykrywalne straty Repro | Temperatura odbytnicy przekracza 38,5 \\ u2103 (101,3 \\ u2109)
Report.Heatstress.Temperature=Temperatura
Report.Heatstress.Temperature.In.Celcius=Temperatura \\ u2103
Report.Heatstress.Temperature.In.Farenhiet=Temperatura \\ u2109
Report.Heatstress.TemperatureHumidityIndex=Wskaźnik wilgotności temperatury
Report.Herd.Analysis.CudChewingPercentage=Przeżuwanie % /
Report.Locomotion.HerdAnalysis.ChartName=Procent wyników lokomocji
Report.LocomotionScore.X.Axis=Wynik lokomocji
Report.LocomotionScore.Y.Axis=Procent %
Report.LocomotionScore.chartName=Wynik lokomocji - zwierzę {0}
Report.No.OfChews=\# Przeżuć
Report.No.OfChewsPerRegurgitation=Liczba żuć na niedomykalność
Report.NoOfChews.Vs.LactStages=\# Przeżuć
Report.Not.Chewing=Nie do żucia
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Czas Dostępny na Odpoczynek
Report.PenTimeBudget.TimeAvailableForResting.Label=Godziny
Report.PenTimeBudgetTimeRemaining=Czas Pozostały
Report.PenTimeBudgetTimeRequired=Czas Wymagany
Report.Pentime.Budget.Hours=godziny
Report.PercentageOnScreen=Procent na Sicie (%)
Report.RumenHealthManureScreening.Bottom=Spód
Report.RumenHealthManureScreening.BottomGoalMax=MAX GOLTY DOLNY
Report.RumenHealthManureScreening.BottomGoalMin=Dolna bramka min
Report.RumenHealthManureScreening.Middle=Środek
Report.RumenHealthManureScreening.MiddleGoalMax=Środkowy cel max
Report.RumenHealthManureScreening.MiddleGoalMin=Middle Min
Report.RumenHealthManureScreening.Top=Szczyt
Report.RumenHealthManureScreening.TopGoalMax=Najwyższy cel max
Report.RumenHealthManureScreening.TopGoalMin=Najwyższy cel min
Report.SheetName=Raport Master
Report.Tool.Details=Narzędzia Szczegóły
Report.Tool.Name=Nazwa narzędzia
Report.Visit.Date=Data odwiedzenia
Report.Visit.Report=Wizyta  Raport
Report.Visit.name=Odwiedź nazwę
Report.goalChews=Cel żuć
Report.locomotionScore.Pen.Analysis.ChartName=Kategorie vs Daty wizyty
ReportDate=Data raportu
ReportPDFNote=Notatka\:  
Reset_Database=Zresetuj bazę danych
Resources=Materiały
ResourcesViewModel.Title=Materiały audytu pasz objętościowych
ResourcesViewModel.VisitNotebook=Notatnik
RetainedPlacenta=Zatrzymanie Łożyska
Reunion=Zjazd
Revenue=Revenue
RevenueEditComparisonValuesViewModel.CurrentSCC=Obecny LKS (komórki/{0})
RevenueEditComparisonValuesViewModel.DownResponse=Negatywna odpowiedź ({0}/krowa/dzień)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Edytuj Wartości Porównywalne
RevenueEditComparisonValuesViewModel.MilkChange=Zmiana Mlek (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Cena Mleka ($/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Produkcja Mleka
RevenueEditComparisonValuesViewModel.NumOfCows=Liczba Krów
RevenueEditComparisonValuesViewModel.PotentialSCC=Potencjalna LKS (komórki/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Scenariusz 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Scenariusz 2
RevenueEditComparisonValuesViewModel.Title=Edytuj Wartości Porównywalne
RevenueInputViewModel.ComparisonValues=Wartości Porównywalne
RevenueInputViewModel.Edit=Edytuj
RevenueInputViewModel.ScenarioOne=Scenariusz 1
RevenueInputViewModel.ScenarioTwo=Scenariusz 2
RevenueInputViewModel.Title=kalkulator udojonego mleka – dane wejściowe
RevenueLossDay=Strata Dochodu ({0}/Dzień)
RevenueLossYear=Strata Dochodu ({0}/Rok)
Rheinland=Rhineland
Rhode_Island=Rhode Island
Rieti=Rieti
Rimini=Rimini
Rio_Grande_do_Norte=Duża rzeka północna
Rio_Grande_do_Sul=Rio Grande do Sul
Rio_de_Janeiro=Rio de Janeiro
Robot=Robot
RoboticMilkEvaluationSpinnerViewModel.Title=Analiza doju robotowego
Romania=Rumunia
Rome=Rzym
RondÃ´nia=Rondã´nia
Roraima=Roraima
Roscommon=Roscommon
Rovigo=Rovigo
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Analiza Stada
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Analiza Kojca
RumenHealthBodyConditionLandingViewModel.Pens=Kojce
RumenHealthBodyConditionLandingViewModel.Resources=Zasoby
RumenHealthBodyConditionLandingViewModel.Title=BCS
RumenHealthEditManureScoresViewModel.Close=Zamknij
RumenHealthEditManureScoresViewModel.Count=Liczba
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Proszę Podać Liczbę Krów
RumenHealthEditManureScoresViewModel.NumOfCows=Liczba Krów
RumenHealthEditManureScoresViewModel.NumberOfCows=Liczba Krów
RumenHealthEditManureScoresViewModel.VisitNotebook=Notatnik
RumenHealthLandingViewModel.HerdAnalysis=Analiza Stada
RumenHealthLandingViewModel.PenAnalysis=Analiza Kojca
RumenHealthLandingViewModel.Pens=Kojce
RumenHealthLandingViewModel.Title=Zdrowotność Żwacza Przeżuwanie
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Analiza Stada
RumenHealthLocomotionLandingViewModel.PenAnalysis=Analiza Kojca
RumenHealthLocomotionLandingViewModel.Pens=Kojce
RumenHealthLocomotionLandingViewModel.Resources=Zasoby
RumenHealthLocomotionLandingViewModel.Title=Ocena ruchu
RumenHealthManureLandingViewModel.HerdAnalysis=Analiza Stada
RumenHealthManureLandingViewModel.PenAnalysis=Analiza Kojca
RumenHealthManureLandingViewModel.Pens=Kojce
RumenHealthManureLandingViewModel.Resources=Zasoby
RumenHealthManureLandingViewModel.Title=Zdrowotność Żwacza Ocena Odchodów
RumenHealthManureMasterViewModel.Inputs=Dane Wejściowe
RumenHealthManureMasterViewModel.Results=Wyniki
RumenHealthManureMasterViewModel.RumenHealthManureScore=Zdrowotność Żwacza Ocena Odchodów
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Zdrowotność Żwacza Ocena Żdchodów
RumenHealthManureMasterViewModel.VisitNotebook=Notatnik
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Średni Wynik
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Data
RumenHealthManureScoresResultsViewModel.PercentPen=Procent Kojca (%)
RumenHealthManureScoresResultsViewModel.SelectedDates=Wybierz Daty
RumenHealthManureScoresResultsViewModel.Title=Wyniki Oceny Odchodów
RumenHealthManureScoresViewModel.AnimalsObserved=Zwierzęta Obserwowane
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Średnia Ocena Odchodów (Obliczona)
RumenHealthManureScoresViewModel.Edit=Edytuj
RumenHealthManureScoresViewModel.ManureScore=Ocena Odchodów
RumenHealthManureScoresViewModel.PercentOfPen=Procent Kojca (%)
RumenHealthManureScoresViewModel.ScoreCategory=Kategoria Oceny Odchodów
RumenHealthManureScoresViewModel.StdDevCalculated=Odchylenie Standardowe (obliczone)
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=KALKULATORY PRZEŻUWANIA
RumenHealthPenCudCalculatorViewModel.CudChewing=Przeżuwanie
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Zapisz Liczbę Krów Przeżuwających
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Liczba Przeżuć
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Zapisz Liczbę Przeżuć na Krowę
RumenHealthTMRLandingViewModel.HerdAnalysis=Analiza Stada
RumenHealthTMRLandingViewModel.PenAnalysis=Analiza Kojca
RumenHealthTMRLandingViewModel.Pens=Kojce
RumenHealthTMRLandingViewModel.Title=Zdrowotność Żwacza Sita TMR
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Wprowadź Masę (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Wprowadź Tarę (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Cel - Max (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Cel - Min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Goals
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Średnie 1(8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Średnie 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Średnie 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=TMR Particle Score name
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=TMR Particle Score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Tara
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Wprowadź Masę
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Góra (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Taca
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Add TMR Score
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Śr. Wynik oceny  Sit TMR
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Current
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Masa (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Cel - Max (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Goal Mid 1 (8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Goal Mid 2 (1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Goal Mid 2 (4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Cel - Min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Goals
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Goal Top (19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Goal Tray
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Max
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Sito 2\n(8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Średnie 2
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Sito 3\n(4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Min
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Particle Score (% on Screen)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Procent na Sicie (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Odchylenie Standardowe
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Sita TMR informacja
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=TMR Particle Score
RumenHealthTMRParticleScorePenTableInputViewModel.Title=Sita TMR
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Góra
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Taca
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Dane Wejściowe
RumenHealthTMRPenScorerTableMasterViewModel.Results=Wyniki
RumenHealthTMRPenScorerTableMasterViewModel.Title=Zdrowotność Żwacza Sita TMR
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Nic Nie Wybrano
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Rodzaj sit musi być wybrany by wyświetlić kojec.
RumenHealthTMRSelectPenViewModel.SelectPen=Kojce (Tylko Laktacja i Zasuszenie)
RumenHealthTMRSelectPenViewModel.SelectScorer=Wybierz Sita
RumenHealthTMRSelectPenViewModel.Title=Zdrowotność Żwacza Sita TMR
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Nic Nie Wybrano
RumenHealthTMRSelectScorerViewModel.FooterText=TYLKO jeden rodzaj sit może być użyty podczas wizyty. Zmiana sit spowoduje utratę wyników.
RumenHealthTMRSelectScorerViewModel.SelectScorer=Wybierz Sita
RumenHealthTMRSelectScorerViewModel.Title=Zdrowotność Żwacza Sita TMR
Russia=Rosja
Russian_Federation=Federacja Rosyjska
Rwanda=Rwanda
SAR=Arabia Saudyjska (﷼ SAR)
SCCPremiumDeduction=LKS Premium/Potrącenie ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Surinam ($ SRD)
Saint_Barthélemy=Święty Barthelemy
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Wniebowstąpienie i Tristan da Cunha
Saint_Kitts_and_Nevis=Saint Kitts i Nevis
Saint_Lucia=święta Lucia
Saint_Martin_(French_part)=Saint Martin (część francuska)
Saint_Pierre_and_Miquelon=Saint Pierre i Miquelon
Saint_Vincent_and_the_Grenadines=Saint Vincent i Grenadyny
Salerno=Salerno
Samoa=Samoa
San_Luis_Potosí=San Luis Potosã
San_Marino=San Marino
Santa_Catarina=Santa Catarina
Sao_Tome_and_Principe=Sao Tome i Principe
Saskatchewan=Saskatchewan
Sassari=Sassari
SaudiArabia=Arabia Saudyjska
Saudi_Arabia=Arabia Saudyjska
Savona=Savona
Schleswig=Schleswig
ScorecardPrompt=Ukończ ocenę by wyświetlić {0} krtę oceny
Screen=Sita
ScreenNew=Sita Nowe
ScreenOld=Sita Stare
Search=Szukaj
SegmentViewModel.SegmentTitle=WYBIERZ SEGMENT
SegmentViewModel.Title=Szczegóły
Select=Wybierz
SelectCurrencyViewModel.Title=Waluta
SelectDates=Wybierz Daty
SelectFeedingSystemViewModel.SelectFeedingSystem=Wybierz System Żywienia
SelectFeedingSystemViewModel.Title=Ustawienia Kojca
SelectForageImprovement=Please select only 12 improvement from list.
SelectHousingSystemViewModel.SelectHousingSystem=Wybierz System Utrzymania
SelectHousingSystemViewModel.Title=Ustawienia Kojca
SelectImprovement=Wybierz tylko 10 ulepszeń z listy
SelectMatrix=Wybierz Macierz
SelectMilkingSystemViewModel.NoneSelected=Nic Nie Wybrano
SelectMilkingSystemViewModel.SelectMilkingSystem=Wybierz System Doju
SelectMilkingSystemViewModel.Title=Ustawienia Lokalizacji
SelectOnlyThreeNotes=Please select only 3 notes in the tool.
SelectOnlyTwoNotes=Please select only 2 notes per tool.
SelectPen=Proszę wybrać dawkę dla nowego kojca.
SelectProcessor=Wybierz Mleczarnię
SelectProcessorViewModel.COMPONENT=Komponent
SelectProcessorViewModel.CONCENTRATION=Koncentracja
SelectProcessorViewModel.Edit=Edytuj
SelectProcessorViewModel.MilkProcessors=Mleczarnia
SelectVisitComparison=Wybierz wizyty w celu porównania
SemiAnnually=Co pół roku
Semiconfinamento=Semiconfinamento
Send=Wyślij
Senegal=Senegal
Seoul=Seul
Serbia=Serbia
Sergipe=Sergipe
SettingsViewModel.Imperial=Imperialne
SettingsViewModel.Metric=Metryczna
SettingsViewModel.Milk_Processor_Set_Up=Ustawienia Mleczarni
SettingsViewModel.More_Settings=Więcej Ustawień
SettingsViewModel.Select_Unit_Of_Measure=Wybierz Jednostkę Miary
Severe=heat stress\: open mouth + drooling, respiration rate 120 to more than 160 bpm
Seychelles=Seszele
Shaanxi=Shaanxi
Shandong=Shandong
Shanghai=Szanghaj
Shanxi=Shanxi
ShortDryPeriod=Krótkie Zasuszenie
ShowEulaViewModel.Accept=Akceptuj
ShowEulaViewModel.ConfirmationNo=Nie
ShowEulaViewModel.ConfirmationText=Czy zgadzasz się z warunkami Licencji Użytkownika Końcowego Aplikacji Mobilnej?
ShowEulaViewModel.ConfirmationTitle=Potwierdzenie
ShowEulaViewModel.ConfirmationYes=Tak
ShowEulaViewModel.Decline=Odrzuć
ShowEulaViewModel.Eula=EULA
ShowEulaViewModel.EulaError=Nie można wyświetlić Licencji Użytkownika Końcowego. Proszę połączyć się z internetem i spróbować ponownie.
ShowEulaViewModel.EulaScreenTitle=Licencja Użytkownika Końcowego
ShowPrivacyStatementViewModel.PrivacyStatement=&lt;p&gt;&lt;b&gt;Privacy Statement&lt;/b&gt;&lt;/p&gt;&lt;p&gt;Ostatnio aktualizowano\: Styczeń 3,201. Cargill. Inc ("Cargill" lub "My") zbiera informacje o Tobie kiedy używasz tej aplikacji mobilnej ("aplikacja"), która jest przeznaczona dla konsultantów oferujących usługi Dairy Enteligen™ w imieniu Cargill. Informacje osobiste. Cargill może zbierać następujące informacje osobiste bezpośrednio od Ciebie, np. gdy się rejestrujesz oraz przez używanie aplikacji. Twoje Imię. Twoja lokalizacja (przez GPS lub analogiczną technologię), Twoje zdjęcia (jeśli dzielone z Cargill przez aplikację)&lt;/p&gt;&lt;p&gt; Możemy używać wspólnych technologii, jak ciasteczka lub wskaźniki w aplikacji by zbierać takie informacje osobiste. Użytkowanie i dzielenie - Informacj ebiznesowe. Na stronie  http\://www.cargill.com/privacy/business-notice/index.jsp"&gt; Business Information Notice wyjaśnia jak wykorzystujemy informacje prywatne zbirane o Tobie w ujęciu biznesowym. Zbieranie danych o lokalizacji. Poprzez używanie aplikacji zgadzasz się na zbieranie informacji o Twojej lokalizacji w czasie rzeczywistym oraz ostępujesz i zwalniasz Cargill z wszelkiej odpowiedzialności, roszczeń i przyczyn działania lub szkód wynikających z Twojego użytkowania aplikacji, lub w jakikolwiek inny sposób związanych z wykorzystaniem danych o lokalizacji.&lt;/p&gt;
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Privacy Statement
ShowSyncStatusViewModel.GetAccounts=Otrzymano dane konta \:
ShowSyncStatusViewModel.GetNotes=Otrzymano dane notatek \:
ShowSyncStatusViewModel.GetVisits=Otrzymano dane wizyt \:
ShowSyncStatusViewModel.Title=Podsumiwanie synchronizacji danych
Sichuan=Syczuan
Siena=Siena
Sierra_Leone=Sierra Leone
Sikkim=Sikkim
SilageBags=Rękawy Kiszonkowe
SilageBags_BagsPlacedOnStableWellManagedSurface=Rękawy są umieszczane na stabilnym, całorocznym podłożu (asfalt lub beton)
SilageBags_BonusSecureCoverIsUsed=Bonus\: zastosowano okrywę zabezpieczającą?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Czysta dobrze zarządzana ściana, brak widocznego przegrzewania luźnej paszy
SilageBags_FaceRemovalRate=Prędkość ubywania ściany\:
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Kontrolowane pod kątem dziur po szkodnikach, regularnie naprawiane?
SilageBags_PorosityScoresConsistently=Wyniki porowatości\:
SilageBags_TrashVegRodentControlledAroundBags=Odpadki, roślinność i szkodniki są pod kontrolą dookoła rękawów
SilagePrevention1st=Prewencja dla kiszonek\: najpierw najważniejsze
Sinaloa=Sinaloa
Singapore=Singapur
Sint_Maarten_(Dutch_part)=Sint Marthes (część holenderska)
Site-Not-Synced-To-Lift=Witryna nie została zsynchronizowana z LIFT; skontaktuj się z administratorem w celu rozwiązania
SiteDetailViewModel.AnimalInputsSite=Dane Wejściowe Zwierzat
SiteDetailViewModel.DairyEnteligenReport=Raport Dairy Enteligen
SiteDetailViewModel.Detailed=Szczegóły
SiteDetailViewModel.DietInputsSiteLactating=Dane Wejściowe Dawki (Zwierzęta w Laktacji)
SiteDetailViewModel.DownloadingVisit=Ściąganie Wizytyt...
SiteDetailViewModel.GeneralCustomerSiteSetup=Ogólne Ustawienia Lokalizacji Klienta
SiteDetailViewModel.GetReportMsg=Ściąganie Raportu...
SiteDetailViewModel.MainHeading=WIZYTY
SiteDetailViewModel.NetworkErrorMessage=Aktualnie nie ma dostępnych sieci.
SiteDetailViewModel.NetworkErrorMessageTitle=Błąd Sieci
SiteDetailViewModel.NewVisit=Rozpocznij Nową Wizytę
SiteDetailViewModel.ReportDownloadTimeout=Unable to download the file, please try when you have better connectivity
SiteDetailViewModel.ReportNotAvailable=Raport niedostępny do ściągnięcia.
SiteDetailViewModel.ReportNotAvailableTitle=Status
SiteDetailViewModel.Reports=RAPORT DAIRY ENTELIGEN
SiteDetailViewModel.Resources=Zasoby
SiteDetailViewModel.SiteSetup=Ustawienia Lokalizacji
SiteDetailViewModel.Summary=Podsumowanie
SiteDetailViewModel.Title=Szczegóły Lokalizacji
SiteDetailViewModel.VisitDownloadPrompt=Czy chcesz odzyskać tą wizytę?
SiteDetailViewModel.VisitNotDownloaded=Wizyta nie została ściągnięta
SiteDetailViewModel.VisitUnavailable=Dane wizyty niedostępne.
SiteDetailsResourcesViewModel.Title=Zasoby
SiteDetailsSetupViewModel.AnimalInputsSite=Dane Wejściowe Zwierzat
SiteDetailsSetupViewModel.AsFedIntake=Pobranie Świeżej Masy ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=Liczba Bakterii (1,000 cfu/mL)
SiteDetailsSetupViewModel.Continue=Kontynuuj
SiteDetailsSetupViewModel.CurrentMilkPrice=Aktualna cena mleka ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Dni Laktacji (DL)
SiteDetailsSetupViewModel.Delete=Usuń
SiteDetailsSetupViewModel.DietInputsSiteLactating=Dane Wejściowe Dawki (Krowy w Laktacji)
SiteDetailsSetupViewModel.DietSetup=DIET SETUP
SiteDetailsSetupViewModel.Diets=Dawki
SiteDetailsSetupViewModel.DryMatterIntake=Pobranie Suchej Masy (PSM) ({0})
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Ogólne Ustawienia Lokalizacji Klienta
SiteDetailsSetupViewModel.LactatingAnimals=Krowy w Laktacji
SiteDetailsSetupViewModel.MilkFatPercent=Tłuszcz Mleka (%)
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Pozostałe komponenty mleka (%)
SiteDetailsSetupViewModel.MilkProteinPercent=Białko Mleka (%)
SiteDetailsSetupViewModel.MilkYield=Wydajność Mleka ({0})
SiteDetailsSetupViewModel.MilkingSystem=System Doju
SiteDetailsSetupViewModel.NameNotUnique=Lokalizacja "{0}" już istnieje. Nazwa musi być niepowtarzalna.
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=NEL Dairy (Mcal/{0})
SiteDetailsSetupViewModel.NewSite=Nowa Lokalizacja
SiteDetailsSetupViewModel.NullSiteName=Lokalizacja musi mieć nazwę. Chcesz kontynuować czy usunąć lokalizację?
SiteDetailsSetupViewModel.NumberOfStalls=Total Stalls In Parlor
SiteDetailsSetupViewModel.PenSetup=PEN SETUP
SiteDetailsSetupViewModel.Pens=Kojce
SiteDetailsSetupViewModel.RationCost=Koszt Dawki na Zwierzę ({0})
SiteDetailsSetupViewModel.SiteMandatoryFields=Site Name, Milk Price, Milking System and Pen are mandatory fields. Fill all the mandatory fields to continue.
SiteDetailsSetupViewModel.SiteName=Nazwa lokalizacji
SiteDetailsSetupViewModel.SiteSetup=Ustawienie lokalizacji
SiteDetailsSetupViewModel.SomaticCellCount=Liczba Komórek Somatycznych (1,000 kom/mL)
SiteDetailsSetupViewModel.Title=Szczegóły Lokalizacji
SiteDetailsSetupViewModel.WeightImperialCWT=CWT
SiteVisitSummary=Podsumowanie Wizyty
SiteVisitSummaryReport=Raport Podsumowania Wizyty Lokalizacji
SixToEightLayers=6-8 warstw folii
SixToTwelveHours=6-12 godzin
SixToTwelveInches=15-30 cm
Sligo=Sligo
Slovakia=Słowacja
Slovenia=Słowenia
Solomon_Islands=Wyspy Salomona
Somalia=Somali
SomanticCellCount=Somatic Cell Count
SomaticCellPerML=Somatic Cell Count (cells/mL)
Sondrio=Sondrio
Sonora=Sonora
SouthAfrica=RPA
SouthKorea=Korea (Pd)
South_Africa=Afryka Południowa
South_Australia=Południowa Australia
South_Carolina=Karolina Południowa
South_Dakota=Południowa Dakota
South_Georgia_and_the_South_Sandwich_Islands=Południowa Georgia i wyspy South Sandwich
South_Sudan=Południowy Sudan
Spain=Hiszpania
Sri_Lanka=Sri Lanka
StandardDeviationScoreTitle=Odchylenie Standardowe
StartDate=Start date
StatusArchived=Archived
StatusCompleted=Ukończono
StatusInProgress=W toku
StdDevCalculated=Odchylenie Standardowe (obliczone)
Steer=Wolec
StorageCalculators=Kalkulator magazynu
StrategyToReduceDisplacedAbomasum=Przemieszczenie Trawieńca
StrategyToReduceDisplacedAbomasumDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategy to reduce displaced abomasum incidence&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;The prevention strategy relies on managing the risk factors, as we currently do not have a clear direct cause.&lt;/p&gt;&lt;p&gt;Prevention includes appropriate nutrition and management, and management of concomitant diseases\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Control of nutritional risk factors\:&lt;ul&gt;&lt;li&gt;Avoid over-conditioning cows (ideal 3.00 BCS at dry-off and calving).&lt;/li&gt;&lt;li&gt;Provide enough forage NDF.&lt;/li&gt;&lt;li&gt;Manage physical form of the maxDiet.&lt;/li&gt;&lt;li&gt;Pay attention to mineral requirements.&lt;/li&gt;&lt;li&gt;Avoid other metabolic disorders such as hypocalcemia, and also infectious disease that could reduce intake.&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Best management practices\:&lt;ul&gt;&lt;li&gt;Assure feed intake in fresh cows especially during the hours/days after calving&lt;/li&gt;&lt;li&gt;Manage feed bunks properly&lt;/li&gt;&lt;li&gt;Increase cow comfort, reduce any stressors&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceDystocia=Trudne Porody
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategy to reduce dystocia incidence&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Help prevent dystocia by\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Ensuring heifers are inseminated at the proper age and bodyweight.&lt;/li&gt;&lt;li&gt;Selecting potential sires on the basis of known calving ease.&lt;/li&gt;&lt;li&gt;Improving personnel training regarding proper timing and methods of intervention during calving, plus appropriate methods to care for compromised newborn calves&lt;/li&gt;&lt;li&gt;Review the current ration program following MAX&lt;sup&gt;TM&lt;/sup&gt; requirements for far-off and close-up cows and heifers focusing on\:&lt;ul&gt;&lt;li&gt;Energy to maintain body condition score and fetal growth&lt;/li&gt;&lt;li&gt;Prevention of over-conditioned cows at calving&lt;/li&gt;&lt;li&gt;Control of hypocalcemia risk in the herd&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=Strategies to reduce incidence
StrategyToReduceKetosis=Ketoza
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategy to reduce ketosis incidence&lt;/strong&gt;&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;Assure adequate cow comfort (bedding, heat stress control and ventilation, avoid stress and overcrowding, etc.)&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="2"&gt;&lt;li&gt;Balance diets according to MAX&lt;sup&gt;TM&lt;/sup&gt; requirements for nutrients and physical maxDiet characteristics. Consult the Transition Cow products inventory for specific product formulations developed for prevention of ketosis. Assure use of good quality forages that will increase intake and rumen buffer capacity.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="3"&gt;&lt;li&gt;Monitor BCS change between far-off and calving\: Dry cows off at 3.00 BCS and maintain BCS during the dry period to avoid excessive lipid mobilization around calving.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="4"&gt;&lt;li&gt;Implement a specific health protocol developed for fresh cows to detect and prevent metabolic disorders and infectious diseases. Monitor intake, rumen fill and rumination.&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=Mastitis
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategy to reduce mastitis incidence&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Microorganisms that most frequently cause mastitis can be divided into two main categories\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Contagious pathogens, which spray from cow to cow primarily during the milking, process &lt;em&gt;(i.e. Strep. agalactiae&amp;nbsp;and&amp;nbsp;Staph. Aureus)&lt;/em&gt;&lt;/li&gt;&lt;li&gt;Environmental pathogens, which come from the dairy cows environment &lt;em&gt;(i.e. E. coli&amp;nbsp;and&amp;nbsp;Strep. Uberis)&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Depending on which bacteria category is causing mastitis, the intervention focus has to be adapted.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Contagious mastitis control&lt;/strong&gt;\: there is no magic silver bullet preventing infections for all pathogens but the following steps will help\:&lt;ul&gt;&lt;li&gt;Focus on hygiene around and during milking , including teat dipping&lt;/li&gt;&lt;li&gt;Milk infected cows at last&lt;/li&gt;&lt;li&gt;Carry out regular milking machine maintenance&lt;/li&gt;&lt;li&gt;Review the dry cow nutrition plan using MAX&lt;sup&gt;TM&lt;/sup&gt; guidelines to ensure a proper nutrient supply (energy, antioxidant) to support immune function&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Controlling environmental mastitis&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;Ensure a clean and dry bedding, well-ventilated barn&lt;/li&gt;&lt;li&gt;Correct stocking density&lt;/li&gt;&lt;li&gt;General hygiene of the udder&lt;/li&gt;&lt;li&gt;Parlor routine&lt;/li&gt;&lt;li&gt;Review the dry cow nutrition plan using MAX&lt;sup&gt;TM&lt;/sup&gt; guidelines to ensure a proper nutrient supply (energy, antioxidant) to support immune function&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=Metritis
StrategyToReduceMetritisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategy to reduce metritis incidence&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Risk factors for metritis include retained placenta, injury to the reproductive tract due to a difficult calving, improper calving protocol , unsanitary calving area, nutritional deficiency such as vitamin E or selenium deficiencies, and over-conditioned cows.&lt;/p&gt;&lt;p&gt;Monitor the following areas, in descending order of importance&amp;nbsp;\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Calving practices&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Are employees carrying bacteria into the uterus when assisting in calving?&lt;/li&gt;&lt;li&gt;Are cows being helped too early or too late?&lt;/li&gt;&lt;li&gt;Are calves being pulled too often?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Treatment practices&lt;br /&gt;&lt;ul&gt;&lt;li&gt;How are cows with RP's or metritis treated?&lt;/li&gt;&lt;li&gt;Any chance of carrying bacteria from the outside environment or vagina into the uterus at that time?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="3"&gt;&lt;li&gt;Animal Stress&lt;br /&gt;&lt;ul style\="list-style-type\: circle;"&gt;&lt;li&gt;Excess stress before calving may be depleting the cow immune system , lowering the resistance to any infection after calving.&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="4"&gt;&lt;li&gt;Nutrition&lt;ul&gt;&lt;li&gt;Review maxDiet balance in dry cow, focusing on BCS control, mineral balance and supply of antioxidant nutrients (vit E, A, selenium, zinc and copper)&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Other disease check (ketosis, LDA)&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMilkFever=Gorączka mleczna
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Strategy to reduce milk fever incidence&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Two alternative strategies for the prevention of hypocalcemia in dairy cows rely 100% on dietary management.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Use dry cow diets low in Ca&lt;/li&gt;&lt;li&gt;Use dry cow diets formulated to a low DCAD&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Area to consider to keep hypocalcemia under control are\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Testing forages for minerals ( calcium, phosphorus, magnesium, potassium, sodium, sulfur, and chloride) in a validated laboratory&lt;/li&gt;&lt;li&gt;Review the current ration program following MAX&lt;sup&gt;TM&lt;/sup&gt; requirements for close-up cows and feeding management practices (special attention to ad libitum forages, K concentration in forages, free-choice minerals fed to dry cows, dry cows sorting out their maxDiet)&lt;/li&gt;&lt;li&gt;Consult the Transition cow product inventory for specific formulations developed to prevent hypocalcemia&lt;/li&gt;&lt;li&gt;When using DCAD diets\:&lt;ul&gt;&lt;li&gt;Carefully monitor feed intake as anionic salts are unpalatable and can reduce dry matter intake&lt;/li&gt;&lt;li&gt;Monitor urine pH to check the efficacy of the maxDiet changes&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=Zatrzymanie Łożyska
StrategyToReduceRetainedPlacentaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;span&gt;&lt;strong&gt;Strategy to reduce retained placenta incidence&lt;/strong&gt;&lt;/span&gt;&lt;/h4&gt;&lt;p&gt;Retained placenta incidence is positively correlated with immune suppression, high stress hormones, hypocalcemia (including subclinical), assisted calving and abortions, endemic infectious agents, and genetics.&lt;/p&gt;&lt;p&gt;Monitor the following areas, in descending order of importance\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Nutrition &lt;br/&gt; &lt;p style\="padding-left\: 30px;"&gt;Review close-up diets following MAX&lt;sup&gt;TM&lt;/sup&gt; guidelines with a special focus on\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Se and vitamin E&lt;/li&gt;&lt;li&gt;Minerals levels (K, Ca, Mg) to decrease risk of subclinical and clinical hypocalcemia&lt;/li&gt;&lt;li&gt;Use a low or negative DCAD to decrease the incidence of hypocalcemia&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Management&lt;/li&gt;&lt;ul&gt;&lt;li&gt;Manage BCS (3.00 at calving) during late lactation and the dry period&lt;/li&gt;&lt;li&gt;Minimize stressors in the close up and postpartum area to decrease fat mobilization and immunosuppression around parturition.&lt;/li&gt;&lt;li&gt;Improve cow comfort and provide enough bunk space&lt;/li&gt;&lt;li&gt;Follow a calving procedure set by your veterinarian&lt;/li&gt;&lt;li&gt;Do not help cows calving unnecessarily&lt;/li&gt;&lt;li&gt;Maintain a clean environment to decrease chances of uterine infection.&lt;/li&gt;&lt;/ul&gt;&lt;/ol&gt;&lt;/span&gt;
Straw=Słoma
Sudan=Sudan
Surinam=Surinam
Suriname=Surinam
SurveyCategories=Kategorie Oceny
SurveyOfForages=Badania Pasz Objętościowych
SurveyOfForages_AnnualCowNumAndForageNeeds=Roczne zapasy pasz objętościowych zgodne z ilością zwierząt?
SurveyOfForages_AshLevelsInCornSilage=Jaki jest poziom popiołu w kiszonce z kukurydzy?
SurveyOfForages_AshLevelsInHaylage=Jaki jest poziom popiołu w sianokiszonce?
SurveyOfForages_ButyricAcidLevelsInHaylage=Jaki jest poziom kwasu masłowego w sianokiszonce?
SurveyOfForages_CornSilageProcessingScore=Wynik rozdrobnienia ziarniaka?
SurveyOfForages_CornSilageScoreMonitored=Rozdrobnienie ziarniaka kiszonki z kukurydzy jest monitorowane z wykorzystaniem testów KP firmy Cargill
SurveyOfForages_InspectedForSpoilageAndMold=Wszystkie kiszonki są sprawdzane pod kątem zepsucia i pleśni, zepsute pasze są odrzucane?
SurveyOfForages_InventoryIsMonitored=Zapasy są monitorowane?
SurveyOfForages_LacticAcidToAceticAcidLevels=Stosunek kwasu mlekowego do octowego?
SurveyOfForages_LooseOrFacedFeedWithin=Pasza obsypana jest skarmiana w ciągu\:
SurveyOfForages_NoLooseFeedRemaining=Brak obsypanych elementów kiedy karmienie jest zakończone?
SurveyOfForages_SilosSizedForCapacity=Silosy są dostosowane wielkością do stada? Niewymagają przepełnienia.
SurveyOfForages_VisibleSignsOfSoil=Czy kiszonka nie zawiera żadnych widocznych śladów zanieczyszczenia gleby?
Svalbard_and_Jan_Mayen=Svalbard i Jan Mayen
Swaziland=Suaziland
Sweden=Szwecja
Switzerland=Szwajcaria
Sync-failed-due-to-unknown-reason=La sincronizzazione non è riuscita a causa di ragioni sconosciute
SyncFailed=Nie udało się dokończyć synchronizacji, spróbuj ponownie.
Sync_Data=Synchronizuj Dane
Syracuse=Syracuse
Syrian_Arab_Republic=Republika Syryjsko-Arabska
SystemGenerated=Wygenerowany przez system
São_Paulo=Sé £ o Paulo
THB=Tajlandia (THB THB)
TMR=TMR
TMRHerdAnalysisTableTitle=Analiza Stada Struktura TMR
TMRParticleScore=Analiza przesiewu TMR
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Zamknij
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Dni Laktacji (DL)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Edytuj DL
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=DL
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Edytuj
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Średnie 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Średnie 2
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Analiza Struktury TMR
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Góra
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Taca
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Analiza Stada
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Dane Wejściowe
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Wyniki
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Zdrowotność Żwacza Sita TMR
TMRParticleScoreHerdAnalysisResultsText=Analiza Struktury TMR
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Średnie 1 (8mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Średnie 2 (4mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Góra (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Taca
TRY=TRY
TWD=Tajwan (NT$ TWD)
Tabasco=sos Tabasco
Taipei_City=miasto Taipei
Taiwan=Tajwan
Tajikistan=Tadżykistan
Tamaulipas=Tamaulipas
Tamil_Nadu=Tamil Nadu
Tanzania,_United_Republic_of=Tanzania, United Republic of
Taranto=Taranto
Task=Zadanie
Tasmania=Tasmania
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=Tennessee
Teramo=Teramo
Terni=Terni
Texas=Teksas
TextureFeed=Pasza teksturyzowana
Thailand=Tajlandia
Thirdparty=Strona trzecia
ThisVisit=(Ta Wizyta)
ThreePotentialStorageSolutions=Trzy potencjalne rozwiązania składowania
ThreeScreen=3 Sita
ThreeTimesPerWeek=3 razy w tygodniu
Tianjin=Tianjin
Tiestall=Uwięziowe
TimeRemaining=Czas Pozostały na Odpoczynek
Timor-Leste=Timor Read
Tipperary=Tipperary
Tlaxcala=Tlaxcala
Tocantins=Tocantins
Togo=Iść
Tokelau=Tokelau
Tokyo=Tokio
Tonga=Przybył
TonsAF=TONS AF
TonsDM=TONS DM
TonsPerDay=Tons Per Day
ToolNotSelected=Musisz wybrać co najmniej jedno narzędzie
Top=Sito 1
TopUnloadingSilo=Góra silosu (silos wieżowy)
TopValue=(19mm)
Total=Total
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=Całkowita Liczba Zwierząt w Kojcu
TotalAnimalsHerd=Łącznie Zwierząt
TotalPenPerScore=Łącznie \# zwierząt na Kojec na Ocenę
TotalRevenue=Całkowity Dochód
TowerSilos=Silos Wieżowy
TowerSilos_FaceRemovalRateGreaterThan4Inches=Prędkość usuwania ściany większa niż 10 cm/dzień?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Czy silos jest zakrywany po napełnieniu jeśli nie jest używany przez 14 dni?
TowerSilos_SiloFillingTimeof3DaysOrLess=Czas napełnienia silosu, 3 dni lub mniej?
TransitionCow=Krowa trans
Trapani=Trapani
Tray=Taca
Trends=Pozytywne Trendy
Trento=Trento
Treviso=Treviso
Trieste=Triest
Trinidad_and_Tobago=Trynidad i Tobago
Tripura=Tripura
Tunisia=Tunezja
Turin=Turyn
Turkey=Indyk
Turkmenistan=Turkmenia
Turks_and_Caicos_Islands=Wyspy Turks i Caicos
Tuvalu=Tuvalu
TwelveInchesOrGreater=30cm lub więcej
TwentyFourHoursAndNoSync=Dwadzieścia cztery godziny bez synchronizacji
TwentyFourHoursBeforeActionIsDue=Dwadzieścia cztery godziny przed terminem akcji
TwentyFourToThirtySixInchesPerDay=60-90cm dziennie
TwicePerWeek=dwa razy w tygodniu
UAH=Ukraina (UAH UAH)
UK=Wielka Brytania
UNITED_STATES=Stany Zjednoczone
US=USA
USD=USA ($ USD)
Udine=Udine
Uganda=Uganda
Ukraine=Ukraina
UnitedKingdom=Wielka Brytania
UnitedStates=Stany Zjednoczone Ameryki Północnej
United_Arab_Emirates=Zjednoczone Emiraty Arabskie
United_Kingdom=Zjednoczone Królestwo
United_States=Stany Zjednoczone
UrinePH=pH moczu
UrinePHAVG=Średnie pH moczu
UrinePHAverageNumber=Średni numer
UrinePHDensity=Informacje na temat pH moczu
UrinePHEditCowViewModel.AddNew=Następna krowa
UrinePHEditCowViewModel.CowName=Nazwa krowy
UrinePHEditCowViewModel.CowValue=Wartość krowy
UrinePHEditCowViewModel.UrinePHEnterCowValue=Wprowadź wartość krowy
UrinePHEditCowViewModel.ValidCudInput=Wprowadź poprawne dane wejściowe.
UrinePHEditGoalViewModel.GoalMax=Cel - Max
UrinePHEditGoalViewModel.GoalMin=Cel - Min
UrinePHEditGoalViewModel.TargetUrinePHRange=Docelowy zakres pH moczu
UrinePHEditGoalViewModel.Title=Edytuj cele
UrinePHInputsViewModel.AddNew=Dodaj nowy
UrinePHInputsViewModel.CalculatorHeading=Wybierz krowę, aby wprowadzić wartość pH moczu. Dotknij „Dodaj nowy”, aby dodać krowy do listy.
UrinePHInputsViewModel.CoefficientVariation=Współczynnik zmienności (C.V.) (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Krowy poza zakresem docelowym (%)
UrinePHInputsViewModel.CudChewCategorySection=Krowy
UrinePHInputsViewModel.DietDCAD=DCAD dawki, mEq/100g
UrinePHInputsViewModel.Resources=Informacje (zasoby)
UrinePHInputsViewModel.TargetUrinePHRange=Docelowy zakres pH moczu
UrinePHInputsViewModel.UrinePHAVG=Średnie pH moczy (obliczone)
UrinePHInputsViewModel.UrinePhSTDDEV=Odchylenie Standardowe (obliczone)
UrinePHMasterViewModel.Inputs=Dane wejściowe
UrinePHMasterViewModel.Results=Wyniki
UrinePHMasterViewModel.Title=pH moczu
UrinePHPenSelectionViewModel.Title=pH moczu
UrinePHPenSelectionViewModel.UrinePHPenList=KOJCE (MLECZNE I ZASUSZONE JEDYNIE)
UrinePHResultsViewModel.DietDCAD=Diet DCAD, mEq/100g
UrinePHResultsViewModel.MaxpH=Maks. pH
UrinePHResultsViewModel.MinpH=Min. pH
UrinePHResultsViewModel.UrinePHAVG=Średnie pH moczu
Uruguay=Urugwaj
UserCreated=Utworzone Przez Użytkownika
UserPreferencesViewModel.Branding=WYBÓR MARKI
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=WYBÓR WALUTY
UserPreferencesViewModel.Imperial=Imperialna
UserPreferencesViewModel.MainHeading=Następująca opcja może zostać zmieniona później w ustawieniach aplikacji.
UserPreferencesViewModel.Metric=Metryczna
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Wybierz walutę
UserPreferencesViewModel.SelectPointScale=Wybierz Skalę Punktową
UserPreferencesViewModel.Title=Ustawienia Użytkownika
UserPreferencesViewModel.UnitOfMeasure=WYBÓR JEDNOSTKI MIARY
UserPreferencesViewModel.UserPreferencesMilkProcessor=Ustawienia Mleczarni
UserPreferencesViewModel.UserPreferencesMoreSettings=WIĘCEJ USTAWIEŃ
User_Settings=Ustawienia Użytkownika
Utah=Utah
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
Uzbekistan=Uzbekistan
VEF=Wenezuela (Bs VEF)
VND=Wietnam (₫ VND)
Vanuatu=Vanuatu
Varese=Varese
Venezuela=Wenezuela
Venezuela,_Bolivarian_Republic_of=Wenezuela, Bolivarian Republic of
Venice=Wenecja
Veracruz=Veracruz
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Vermont=Vermont
Verona=Werona
Vestland=Westland
Vibo_Valentia=VIBO Valentia
Vicenza=Vicenza
Victoria=Wiktoria
Viet_Nam=Wietnam
Vietnam=Wietnam
ViewOverallCalfHaiferScore=Wyświetl ogólną kartę oceny cieląt i jałówek
ViewOverallForageScore=Wyświetl Ogólną Ocenę Paszy Objetościowej
Virgin_Islands,_British=Wyspy Dziewicze, brytyjskie
Virginia=Virginia
Visit.Report.Footer.Patent=Firma Cargill Incorporated, jej spółka dominująca i spółki stowarzyszone nie gwarantują dokładności tych szacunków ze względu na wiele czynników. Nie ma gwarancji wyników produkcyjnych ani finansowych. ©2023 Cargill, Incorporated. Wszelkie prawa zastrzeżone.
VisitAutoPublished=Odwiedź opublikowane automatycznie
VisitDate=Data wizyty
VisitDownloadProceed=Kontunuuj
VisitNotebook=Notatnik
VisitNotesViewModel.Action=Akcja
VisitNotesViewModel.Close=Zamknij
VisitNotesViewModel.DownloadingNotes=Downloading Notes...
VisitNotesViewModel.DownloadingNotes1=Ściąganie notatek…
VisitNotesViewModel.Event=Wydarzenie
VisitNotesViewModel.New=Nowy
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=Obserwacja
VisitNotesViewModel.Task=Zadanie
VisitNotesViewModel.Title=Notatnik
VisitNotesViewModel.VisitNotebook=Notatnik
VisitSummaryViewModel.CalfHeiferItem=Cielęta i jałówki
VisitSummaryViewModel.CalfHeiferScorecard=Karta oceny
VisitSummaryViewModel.CategorySection=Kategorie Narzędzi
VisitSummaryViewModel.ComfortHeatStressBanner=Narzędzie Oceny Stresu Cieplnego - Kojec
VisitSummaryViewModel.ComfortItem=Komfort
VisitSummaryViewModel.EmailReport=Wyślij Raport
VisitSummaryViewModel.FreeFormReport=Raport Indywidualny
VisitSummaryViewModel.HealthItem=Zdrowotność
VisitSummaryViewModel.HeatstressEvaluationTitle=Ocena Stresu Cieplnego
VisitSummaryViewModel.HerdAnalysis=Analiza Stada
VisitSummaryViewModel.InputsOutputsChart=Dane Wejściowe / Wyjściowe / Wykresy
VisitSummaryViewModel.MilkProcessRevenueCalculator=kalkulator udojonego mleka
VisitSummaryViewModel.NoToolPrompt=No tools have been completed.
VisitSummaryViewModel.NutritionForage=Audyt Pasz Objetościowych
VisitSummaryViewModel.NutritionItem=Żywienie
VisitSummaryViewModel.NutritionPile=Pojemność Pryzmy i silosu
VisitSummaryViewModel.PenTimeTitle=Zarządzanie czasem w kojcu
VisitSummaryViewModel.ProductivityItem=Produktywność
VisitSummaryViewModel.RumenHealthBodyConditionTitle=BCS
VisitSummaryViewModel.RumenHealthLocomotionTitle=Ocena Ruchu
VisitSummaryViewModel.RumenHealthManureTitle=Zdrowotność Żwacza Ocena Odchodów
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Zaburzenia Metaboliczne
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
VisitSummaryViewModel.RumenHealthTMRTitle=Zdrowotność Żwacza Sita TMR
VisitSummaryViewModel.RumenHealthTitle=Zdrowotność Żwacza Przeżuwanie
VisitSummaryViewModel.RumenHealthUrinePHTitle=pH moczu
VisitSummaryViewModel.Title=Podsumowanie Wizyty
VisitSummaryViewModel.VisitSummaryMilkCalc=Dane Wejściowe / Wyniki / Materiały
VisitSummaryViewModel.VisitTitle=NAZWA WIZYTY
VisitViewModel.CalfHeiferItem=Cielęta i jałówki
VisitViewModel.CategorySection=Kategorie Narzędzi
VisitViewModel.ComfortItem=Komfort
VisitViewModel.Delete=Usuń Wizytę
VisitViewModel.DeletePrompt=Czy na pewno chcesz usunąć tę wizytę? Nie można tego cofnąć.
VisitViewModel.HealthItem=Zdrowie
VisitViewModel.Instructions=Proszę wybrać kategorię lub raport z listy poniżej
VisitViewModel.NullVisitName=Nazwa wizyty nie może być pusta. Wprowadź nazwę wizyty.
VisitViewModel.NutritionItem=Żywienie
VisitViewModel.ProductivityItem=Produktywność
VisitViewModel.Publish=Publikuj
VisitViewModel.PublishNotes=Opublikuj Notatki
VisitViewModel.PublishNotesPrompt=Czy jesteś pewny, że chcesz opublikować notatki z wizyty? Nie da się tego cofnąć.
VisitViewModel.PublishPrompt=Czy na pewno chcesz opublikować tę wizytę? Nie można tego cofnąć.
VisitViewModel.PublishVisit=Publikuj Wizytę
VisitViewModel.SiteVisitSummary=Podsumowanie Wizyty
VisitViewModel.Title=Szczegóły Wizyty
VisitViewModel.ToolCategories=Kategorie Narzędzi
VisitViewModel.VisitNotebook=Notatnik
VisitViewModel.VisitTitle=NAZWA WIZYTY
VisitViewModel.WalkthroughReport=Raport Z Wizyty
Viterbo=Viterbo
VolumeImperial=gal
VolumeMetric=ml
WalkthroughPenSelectionViewModel.Pens=KOJCE
WalkthroughPenSelectionViewModel.Title=Raport Z Wizyty
WalkthroughReport=Raport Z Wizyty
WalkthroughReportHerdAnalysisViewModel.Appearance=Wygląd
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Czystość Ściółki/Legowisk
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Głębokość/Wygoda Ściółki/Legowisk
WalkthroughReportHerdAnalysisViewModel.Branding=MARKA
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Komfort Krów, % Krów Keżących
WalkthroughReportHerdAnalysisViewModel.Comments=KOMENTARZE
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Przeżuwanie, Liczba Przeżuć
WalkthroughReportHerdAnalysisViewModel.CudChewing=Przeżuwanie, % Przeżuwających
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} Raport
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} Raport
WalkthroughReportHerdAnalysisViewModel.ExportSelected=Wyślij Wybrane Narzędzia
WalkthroughReportHerdAnalysisViewModel.FinalObservations=OBSERWACJE KOŃCOWE
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Generowanie raportu…
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Otarcia Pęcin, % Zwierząt
WalkthroughReportHerdAnalysisViewModel.MainHeading=Raport Z Wizyty
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Wycieki z Nosa, % Zwierząt
WalkthroughReportHerdAnalysisViewModel.Notes=Notes
WalkthroughReportHerdAnalysisViewModel.Opportunities=MOŻLIWOŚCI POPRAWY
WalkthroughReportHerdAnalysisViewModel.PensForExport=KOJCE DO EKSPORTU
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Wypełnienie Żwacza
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=BCS
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Ocena Ruchu
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Ocena Odchodów
WalkthroughReportHerdAnalysisViewModel.SubHeading=Analiza Stada
WalkthroughReportHerdAnalysisViewModel.Title=Raport z Wizyty Analiza Stada
WalkthroughReportHerdAnalysisViewModel.Trends=POZYTYWNE TRENDY
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Wycieki ze Sromu, % Zwierząt
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Jakość Wody
WalkthroughReportLandingViewModel.HerdAnalysis=Analiza Stada
WalkthroughReportLandingViewModel.PenAnalysis=Analiza Kojca
WalkthroughReportLandingViewModel.Title=Raport Z Wizyty
WalkthroughReportQualityViewModel.BeddingCleanliness=Wybierz Jakość Ściółki/Legowiska
WalkthroughReportQualityViewModel.Clean=Czysty
WalkthroughReportQualityViewModel.Dirty=Brudny
WalkthroughReportQualityViewModel.ModeratelyClean=Względnie Czysty
WalkthroughReportQualityViewModel.Title=Raport Z Wizyty
WalkthroughReportQualityViewModel.WaterQuality=Wybierz Jakość Wody
WalkthroughReportViewModel.Appearance=Wygląd
WalkthroughReportViewModel.BeddingCleanliness=Czystość Ściółki/Legowisk
WalkthroughReportViewModel.BeddingDepthSoft=Głębokość/Wygoda ściółki/Legowisk
WalkthroughReportViewModel.Clean=Czysty
WalkthroughReportViewModel.ComfortItem=Komfort Krów, % Krów Leżących
WalkthroughReportViewModel.Comments=Komentarze
WalkthroughReportViewModel.CudChewCategorySection=Liczba Przeżuć
WalkthroughReportViewModel.CudChewing=Przeżuwanie, % Przeżuwających
WalkthroughReportViewModel.Current=Obecnie
WalkthroughReportViewModel.Dirty=Brudny
WalkthroughReportViewModel.Goals=Cel
WalkthroughReportViewModel.HockAbrasion=Otarcia pęcin, % zwierząt
WalkthroughReportViewModel.ModeratelyClean=Względnie Czysty
WalkthroughReportViewModel.NasalDischarge=Wycieki z Nosa, % Zwierząt
WalkthroughReportViewModel.Opportunities=Możliwości Poprawy
WalkthroughReportViewModel.Previous=Poprzedni
WalkthroughReportViewModel.RumenFill=Wypełnienie Żwacza
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=BCS
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Ocena Ruchu
WalkthroughReportViewModel.RumenHealthManureTitle=Ocena Odchodów
WalkthroughReportViewModel.Title=Raport Z Wizyty
WalkthroughReportViewModel.Trends=Pozytywne Trendy
WalkthroughReportViewModel.UterineDischarge=Wycieki ze Sromu, % Zwierząt
WalkthroughReportViewModel.WaterQuality=Jakość Wody
Wallis_and_Futuna=Wallis i Futuna
Washington=Waszyngton
Waterford=Waterford
Weekly=Weekly
WeightDMInLengthImperial=Funtów SM w 1 Stopie
WeightDMInLengthMetric=Kg SM w 1 Metrze
WeightImperial=lbs
WeightImperialCWT=CWT
WeightMetric=kg
West_Bengal=Bengal Zachodni
West_Virginia=Wirginia Zachodnia
Western_Australia=Zachodnia australia
Western_Sahara=Sahara Zachodnia
Westmeath=Westmeath
Wexford=Wexford
When=creating a new Pen the only items required are the Pen Name, Diet, Housing System, and Feeding System. 
Wicklow=Wicklow
Wisconsin=Wisconsin
WithinEightHours=W ciągu 8 godzin
Wyoming=Wyoming
Właściwe=
Xinjiang=Xinjiang
Xizang=Xizang
Yemen=Jemen
Yes=Tak
Yucatán=Yucatăn
Yukon_Territories=Terytoria Yukon
Yunnan=Yunnan
ZAR=Południowa Afryka (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=Zambia
Zhejiang=Zhejiang
Zimbabwe=Zimbabwe
welcome.message=Hallo {0}
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein= Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole


