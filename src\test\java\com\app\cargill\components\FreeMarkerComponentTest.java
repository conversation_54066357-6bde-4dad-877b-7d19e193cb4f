/* Cargill Inc.(C) 2022 */
package com.app.cargill.components;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.LangCodes;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.controller.ReportControllerTest;
import freemarker.template.Configuration;
import freemarker.template.Template;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class FreeMarkerComponentTest {
  @Mock private Configuration fmConfiguration;
  @Mock private PlayWrightComponent playWrightComponent;
  @InjectMocks private FreeMarkerComponent freeMarkerComponent;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;

    fmConfiguration.setEncoding(
        Locale.forLanguageTag(LangCodes.EN.name()), StandardCharsets.UTF_8.name());
    fmConfiguration.setClassForTemplateLoading(this.getClass(), "/templates/");
  }

  @Test
  void renderTemplateReturnValidResult() throws IOException {
    when(fmConfiguration.getTemplate(any()))
        .thenReturn(
            Template.getPlainTextTemplate(
                "src/main/resources/templates/CudChewingPenAnalysis.ftl",
                "ABC",
                new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS)));

    when(playWrightComponent.initAndPrepareExport(any(), any())).thenReturn(new byte[0]);

    byte[] render =
        freeMarkerComponent.render(
            ReportControllerTest.prepareDataForBCSPenAnalysis(),
            ReportsToBeanMappings.BCS_PEN_ANALYSIS_REPORT.getImageTemplateName0(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            TemplateExportType.EXPORT_IMAGE);
    Assertions.assertNotNull(render);
    render =
        freeMarkerComponent.render(
            ReportControllerTest.prepareDataForBCSPenAnalysis(),
            ReportsToBeanMappings.VISIT_REPORT.getImageTemplateName0(),
            resourceBundleMessageSource,
            Locale.forLanguageTag(LangCodes.EN.name()),
            TemplateExportType.EXPORT_PDF);
    Assertions.assertNotNull(render);
  }
}
