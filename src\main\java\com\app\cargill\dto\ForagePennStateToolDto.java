/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.RumenHealthTmrScores;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class ForagePennStateToolDto extends EditableDocumentBaseDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private UUID visitId;

  private RumenHealthTmrScores scorer;

  private List<ForagePennStateToolItemDto> inputs;

  private List<ForagePennStateToolGoalItemDto> goals;
}
