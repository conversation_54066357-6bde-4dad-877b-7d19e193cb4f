/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.app.cargill.exceptions.CustomDEExceptions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class BlobServiceClientFactoryTest {

  private BlobServiceClientFactory blobServiceClientFactory;

  @BeforeEach
  void setup() {
    blobServiceClientFactory = new BlobServiceClientFactory();
  }

  @Test
  void withInValidConnection() {
    blobServiceClientFactory.setBlobStorageConnectionString("invalidConnectionString");
    assertThrows(
        CustomDEExceptions.class,
        () -> {
          blobServiceClientFactory.getBlobClient();
        });
  }
}
