/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class CountryToCurrencyConverterTest {

  @Test
  void testCountryToCode() {
    assertEquals("BRL", CountryToCurrencyConverter.countryToCurrency("Brazil"));
    assertEquals("ARS", CountryToCurrencyConverter.countryToCurrency("Argentina"));
    assertEquals("CAD", CountryToCurrencyConverter.countryToCurrency("Canada"));
    assertEquals("USD", CountryToCurrencyConverter.countryToCurrency("United States"));
  }
}
