/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum NineBoxStepTwoIDCrescendo {
  NOT_DEFINED("0 - Not Defined"),
  LARGE_EXTENSIVE("1 - Large/Extensive"),
  MEDIUM_EXTENSIVE("2 - Medium/Extensive"),
  LARGE_SELECTED("3 - Large/Selected"),
  MEDIUM_SELECTED("4 - Medium/Selected"),
  LARGE_LIMITED("5 - Large/Limited"),
  MEDIUM_LIMITED("6 - Medium/Limited"),
  SMALL_EXTENSIVE("7 - Small/Extensive"),
  SMALL_SELECTED("8 - Small/Selected"),
  SMALL_LIMITED("9 - Small/Limited");

  private final String value;

  @JsonCreator
  NineBoxStepTwoIDCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static NineBoxStepTwoIDCrescendo fromString(String text) {
    for (NineBoxStepTwoIDCrescendo b : NineBoxStepTwoIDCrescendo.values()) {
      if (b.getValue().equalsIgnoreCase(text)) {
        return b;
      }
    }
    return null;
  }
}
