/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode
public class AnimalAnalysisToolItem implements Serializable {

  @JsonProperty("PenId")
  public UUID penId;

  @JsonProperty("PenName")
  public String penName;

  @JsonProperty("AnimalDetails")
  private List<AnimalAnalysisDetailsToolItem> animalDetails;
}
