/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.constants.UserSettingsBrands;
import com.app.cargill.document.UserFavourites;
import com.app.cargill.document.UserPreferenceDocument;
import com.app.cargill.dto.CountryToolResponseDto;
import com.app.cargill.dto.UserPreferenceDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.CountryTools;
import com.app.cargill.model.DefaultAppSettings;
import com.app.cargill.model.UserPreferences;
import com.app.cargill.repository.CountryToolsRepository;
import com.app.cargill.repository.DefaultAppSettingsRepository;
import com.app.cargill.repository.UserPreferencesRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IUserPreferenceService;
import com.app.cargill.service.impl.mappers.CountryToolMapper;
import com.app.cargill.service.impl.mappers.UserPreferenceMapper;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserPreferenceServiceImpl implements IUserPreferenceService {

  private final UserPreferencesRepository userPreferencesRepository;

  private final CountryToolsRepository countryToolsRepository;

  private final UserRepository usersRepository;

  private final DefaultAppSettingsRepository defaultAppSettingsRepository;

  private final ApplicationContext ctx;

  private List<CountryToolResponseDto> fetchCountryTools(String email) {
    String countryId = usersRepository.findCountryIdByUserName(email);
    List<CountryTools> countryTools = countryToolsRepository.findByCountryId(countryId);
    if (countryTools.isEmpty()) {
      return new ArrayList<>();
    }
    List<CountryToolResponseDto> countryToolsDto = new ArrayList<>();

    for (CountryTools countryTool : countryTools) {
      countryToolsDto.add(CountryToolMapper.mapToDto(countryTool));
    }
    return countryToolsDto;
  }

  @Override
  public UserPreferenceDto save(UserPreferenceDto userPreferenceDto) {
    UserServiceImpl bean = ctx.getBean(UserServiceImpl.class);
    if (userPreferencesRepository.existsByLocalId(userPreferenceDto.getLocalId())) {
      throw new AlreadyExistsDEException("Local ID exists. Already synced.");
    }
    if (userPreferencesRepository.existsByUserId(
        userPreferenceDto.getUserId() != null
            ? userPreferenceDto.getUserId()
            : bean.getCurrentLoggedInUser())) {
      throw new AlreadyExistsDEException("User ID already exists.");
    }
    UserPreferences userPreference = mapToModel(userPreferenceDto);

    UserPreferenceDto dto =
        UserPreferenceMapper.mapToDto(userPreferencesRepository.save(userPreference));
    dto.setCountryTools(fetchCountryTools(bean.getCurrentLoggedInUser()));
    return dto;
  }

  private UserPreferences mapToModel(UserPreferenceDto userPreferenceDto) {
    UserServiceImpl bean = ctx.getBean(UserServiceImpl.class);
    UserPreferenceDocument userPreferenceDocument =
        UserPreferenceDocument.builder()
            .brandList(userPreferenceDto.getBrandList())
            .createTimeUtc(
                userPreferenceDto.getCreatedDate() != null
                    ? userPreferenceDto.getCreatedDate()
                    : Instant.now())
            .createUser(
                userPreferenceDto.getCreateUser() != null
                    ? userPreferenceDto.getCreateUser()
                    : bean.getCurrentLoggedInUser())
            .eulaContent(userPreferenceDto.getEulaContent())
            .favourites(userPreferenceDto.getFavourites())
            .id(userPreferenceDto.getId() != null ? userPreferenceDto.getId() : UUID.randomUUID())
            .isDeleted(userPreferenceDto.isDeleted())
            .showBCSAnimalAnalysisToast(userPreferenceDto.isShowBCSAnimalAnalysisToast())
            .isNew(true)
            .lastEulaVersionAccepted(userPreferenceDto.getLastEulaVersionAccepted())
            .lastModifiedTimeUtc(Instant.now())
            .lastModifyUser(bean.getCurrentLoggedInUser())
            .lastPrivacyVersionAccepted(userPreferenceDto.getLastPrivacyVersionAccepted())
            .lastSyncOperationDateTime(userPreferenceDto.getLastSyncOperationDateTime())
            .selectedCurrency(userPreferenceDto.getSelectedCurrency())
            .unitOfMeasure(userPreferenceDto.getUnitOfMeasure())
            .defaultMilkUreaMeasure(userPreferenceDto.getDefaultMilkUreaMeasure())
            .defaultMilkPickup(userPreferenceDto.getDefaultMilkPickup())
            .defaultValues(
                userPreferenceDto.getDefaultValues() != null
                    ? userPreferenceDto.getDefaultValues()
                    : new HashMap<>())
            .bcsPointScale(userPreferenceDto.getBcsPointScale())
            .userId(
                userPreferenceDto.getUserId() != null
                    ? userPreferenceDto.getUserId()
                    : bean.getCurrentLoggedInUser())
            .build();
    return UserPreferences.builder()
        .localId(userPreferenceDto.getLocalId())
        .userPreferenceDocument(userPreferenceDocument)
        .deleted(userPreferenceDto.isDeleted())
        .build();
  }

  @Override
  public UserPreferenceDto update(UserPreferenceDto userPreferenceDto) {
    UserServiceImpl bean = ctx.getBean(UserServiceImpl.class);
    UserPreferences userPreferencesToPersist =
        userPreferencesRepository.findByUserId(userPreferenceDto.getUserId());
    if (Objects.isNull(userPreferencesToPersist)) {
      throw new NotFoundDEException(
          "No user Preference found against User Id: " + userPreferenceDto.getUserId());
    }
    UserPreferences userPreference = mapToModel(userPreferenceDto);
    userPreference.setCreatedDate(userPreferencesToPersist.getCreatedDate());
    userPreference.setId(userPreferencesToPersist.getId());
    UserPreferenceDto dto =
        UserPreferenceMapper.mapToDto(userPreferencesRepository.save(userPreference));
    dto.setCountryTools(fetchCountryTools(bean.getCurrentLoggedInUser()));
    return dto;
  }

  @Override
  public UserPreferenceDto getUserPreferences(String email) {
    UserPreferences userPreference = userPreferencesRepository.findByUserId(email);
    String countryId = usersRepository.findCountryIdByUserName(email);
    UserPreferenceDto userPreferenceDto = null;
    if (Objects.isNull(userPreference)) {
      userPreferenceDto = save(getDefaultUserPreferenceDto(countryId, email));
    } else {
      userPreferenceDto = UserPreferenceMapper.mapToDto(userPreference);
    }
    userPreferenceDto.setCountryTools(fetchCountryTools(email));
    return userPreferenceDto;
  }

  private UserPreferenceDto getDefaultUserPreferenceDto(String country, String email) {
    DefaultAppSettings defaultAppSettings =
        defaultAppSettingsRepository.findDefaultAppSettingsByCountry(country);

    return UserPreferenceDto.builder()
        .createUser(email)
        .lastSyncOperationDateTime(Instant.now())
        .brandList(List.of(UserSettingsBrands.Cargill))
        .deleted(false)
        .eulaContent(null)
        .favourites(
            UserFavourites.builder()
                .accounts(new ArrayList<>())
                .tools(new ArrayList<>())
                .notes(new ArrayList<>())
                .build())
        .lastEulaVersionAccepted(null)
        .selectedCurrency(
            (defaultAppSettings != null
                    && defaultAppSettings.getDefaultAppSettingDocument() != null)
                ? defaultAppSettings.getDefaultAppSettingDocument().getDefaultCurrency()
                : Currencies.NotSet)
        .localId(UUID.randomUUID().toString())
        .unitOfMeasure(
            (defaultAppSettings != null
                    && defaultAppSettings.getDefaultAppSettingDocument() != null)
                ? defaultAppSettings.getDefaultAppSettingDocument().getUnitOfMeasure()
                : UnitOfMeasureKeys.Metric)
        .bcsPointScale(
            (defaultAppSettings != null
                    && defaultAppSettings.getDefaultAppSettingDocument() != null)
                ? defaultAppSettings.getDefaultAppSettingDocument().getBcsPointScale()
                : BCSPointScale.HalfPointScale)
        .userId(email)
        .build();
  }
}
