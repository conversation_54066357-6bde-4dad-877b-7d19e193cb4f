/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportFileCrescendo implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ContentId")
  private String contentId;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("GoldenRecordId")
  private String goldenRecordId;

  @JsonProperty("ReportDate")
  private Instant reportDate;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("FileSize")
  private String fileSize;

  @JsonProperty("LastModifiedDateUtc")
  private Instant lastModifiedDateUtc;

  @JsonProperty("ReportType")
  private ReportTypeCrescendo reportType;

  @JsonProperty("Url")
  private String url;

  @JsonProperty("VisitId")
  private String visitId;

  @JsonProperty("VisitReportFilePathDateTime")
  private Instant visitReportFilePathDateTime;
}
