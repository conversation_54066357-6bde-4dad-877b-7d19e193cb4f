/* Cargill Inc.(C) 2022 */
package com.app.cargill.exceptions;

import java.io.Serial;
import lombok.Getter;

@Getter
public class NotFoundDEException extends RuntimeException {
  @Serial private static final long serialVersionUID = 1L;

  public NotFoundDEException() {}

  public NotFoundDEException(String message) {
    super(message);
  }

  public NotFoundDEException(String message, Throwable cause) {
    super(message, cause);
  }
}
