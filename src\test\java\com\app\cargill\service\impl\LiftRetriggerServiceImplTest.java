/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.EventDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.UserRole;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.sf.cc.model.ProfileRecord;
import com.app.cargill.sf.cc.model.UserLicenseRecord;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import com.app.cargill.sf.cc.service.LiftUserAccessService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class LiftRetriggerServiceImplTest {

  @Mock private AccountsRepository accountsRepository;

  @Mock private LiftAccountService liftAccountService;

  @Mock private LiftUserAccessService liftUserAccessService;

  @Mock private LiftContactService liftContactService;

  @Mock private LiftUserService liftUserService;

  @Mock private LiftSiteMappingsService liftSiteMappingsService;

  @Mock private LiftSitesService liftSitesService;

  @Mock private SiteMappingsRepository siteMappingsRepository;

  @Mock private SitesRepository sitesRepository;

  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @Mock private SiteMappingServiceImpl siteMappingServiceImpl;

  @Mock private ActivitiesRepository activitiesRepository;
  @Mock private LiftEventService liftEventService;

  @Mock private VisitsRepository visitsRepository;

  @Mock private UserServiceImpl userServiceImpl;

  @InjectMocks private LiftRetriggerServiceImpl liftRetriggerService;

  @Test
  void testRetriggerSites() throws JsonProcessingException, CustomDEExceptions {
    List<String> siteIds = Arrays.asList("site1", "site2");

    Sites mockSite = new Sites();
    mockSite.setSiteDocument(new SiteDocument());
    mockSite.getSiteDocument().setAccountId(UUID.randomUUID());

    SiteMappings mockSiteMapping = mock(SiteMappings.class);
    when(sitesRepository.findBySiteId(anyString())).thenReturn(mockSite);
    when(siteMappingsRepository.findBySiteId(anyString())).thenReturn(mockSiteMapping);

    Accounts mockAccount =
        Accounts.builder()
            .accountDocument(AccountDocument.builder().dataSource(DataSource.LIFT).build())
            .build();
    when(accountsRepository.findByAccountId(anyString())).thenReturn(mockAccount);

    when(liftSitesService.createSite(
            any(), any(Locale.class), any(ResourceBundleMessageSource.class)))
        .thenReturn("externalSiteId");
    when(liftSiteMappingsService.createSiteMapping(
            any(), any(Locale.class), any(ResourceBundleMessageSource.class)))
        .thenReturn("siteMappingId");

    List<String> result =
        liftRetriggerService.retriggerSites(siteIds, Locale.ENGLISH, resourceBundleMessageSource);

    verify(sitesRepository, times(siteIds.size())).findBySiteId(anyString());
    verify(accountsRepository, times(siteIds.size())).findByAccountId(anyString());
    verify(liftSitesService, times(siteIds.size()))
        .createSite(any(), any(Locale.class), any(ResourceBundleMessageSource.class));
    verify(sitesRepository, times(siteIds.size())).save(any(Sites.class));
    verify(liftSiteMappingsService, times(siteIds.size()))
        .createSiteMapping(any(), any(Locale.class), any(ResourceBundleMessageSource.class));

    assert (result.size() == siteIds.size());
  }

  @Test
  void testRetriggerSitesSiteMappingIsNull() throws JsonProcessingException, CustomDEExceptions {
    List<String> siteIds = Arrays.asList("site1");

    Sites mockSite = new Sites();
    mockSite.setSiteDocument(new SiteDocument());
    mockSite.getSiteDocument().setAccountId(UUID.randomUUID());

    when(sitesRepository.findBySiteId(anyString())).thenReturn(mockSite);
    when(siteMappingsRepository.findBySiteId(anyString())).thenReturn(null);

    Accounts mockAccount =
        Accounts.builder()
            .accountDocument(AccountDocument.builder().dataSource(DataSource.LIFT).build())
            .build();
    when(accountsRepository.findByAccountId(anyString())).thenReturn(mockAccount);

    when(liftSitesService.createSite(
            any(), any(Locale.class), any(ResourceBundleMessageSource.class)))
        .thenReturn("externalSiteId");
    when(liftSiteMappingsService.createSiteMapping(
            any(), any(Locale.class), any(ResourceBundleMessageSource.class)))
        .thenReturn("siteMappingId");

    List<String> result =
        liftRetriggerService.retriggerSites(siteIds, Locale.ENGLISH, resourceBundleMessageSource);

    verify(sitesRepository, times(siteIds.size())).findBySiteId(anyString());
    verify(accountsRepository, times(siteIds.size())).findByAccountId(anyString());
    verify(liftSitesService, times(siteIds.size()))
        .createSite(any(), any(Locale.class), any(ResourceBundleMessageSource.class));
    verify(sitesRepository, times(siteIds.size())).save(any(Sites.class));
    verify(liftSiteMappingsService, times(siteIds.size()))
        .createSiteMapping(any(), any(Locale.class), any(ResourceBundleMessageSource.class));
    verify(siteMappingServiceImpl, times(siteIds.size())).createLiftSiteMapping(any(Sites.class));

    assert (result.size() == siteIds.size());
  }

  @Test
  void testRetriggerSitesSiteOrSiteDocumentNull() {
    List<String> siteIds = List.of("site1");

    when(sitesRepository.findBySiteId(anyString())).thenReturn(null);

    NotFoundDEException thrown =
        assertThrows(
            NotFoundDEException.class,
            () -> {
              liftRetriggerService.retriggerSites(
                  siteIds, Locale.ENGLISH, new ResourceBundleMessageSource());
            });

    assertEquals("Site is null", thrown.getMessage());
  }

  @Test
  void testRetriggerSitesAccountOrAccountDocumentNull() {
    List<String> siteIds = List.of("site1");

    Sites site = new Sites();
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setAccountId(UUID.randomUUID());
    site.setSiteDocument(siteDocument);

    when(sitesRepository.findBySiteId(anyString())).thenReturn(site);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(null);

    NotFoundDEException thrown =
        assertThrows(
            NotFoundDEException.class,
            () -> {
              liftRetriggerService.retriggerSites(
                  siteIds, Locale.ENGLISH, new ResourceBundleMessageSource());
            });

    assertEquals("Account or Account document is null", thrown.getMessage());
  }

  @Test
  void testRetriggerSitesDataSourceMismatch() throws Exception {
    List<String> siteIds = List.of("site1");

    Sites site = new Sites();
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setAccountId(UUID.randomUUID());
    site.setSiteDocument(siteDocument);

    Accounts account = new Accounts();
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setDataSource(DataSource.UNKNOWN);
    accountDocument.setOwnerId("test");
    accountDocument.setGoldenRecordId("testtt");
    account.setAccountDocument(accountDocument);

    when(sitesRepository.findBySiteId(anyString())).thenReturn(site);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);

    List<String> result =
        liftRetriggerService.retriggerSites(
            siteIds, Locale.ENGLISH, new ResourceBundleMessageSource());

    assertTrue(result.isEmpty());
    verify(sitesRepository, never()).save(site);
    verify(liftSiteMappingsService, never()).createSiteMapping(any(), any(), any());
  }

  @Test
  void testRetriggerSitesExceptionHandling() throws Exception {
    List<String> siteIds = List.of("site1");

    Sites site = new Sites();
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setAccountId(UUID.randomUUID());
    site.setSiteDocument(siteDocument);

    Accounts account = new Accounts();
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setDataSource(DataSource.UNKNOWN);
    accountDocument.setOwnerId("test");
    accountDocument.setGoldenRecordId("testtt");
    account.setAccountDocument(accountDocument);

    when(sitesRepository.findBySiteId(anyString())).thenReturn(site);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);

    List<String> result =
        liftRetriggerService.retriggerSites(
            siteIds, Locale.ENGLISH, new ResourceBundleMessageSource());

    assertTrue(result.isEmpty());
    verify(sitesRepository, never()).save(site);
    verify(liftSiteMappingsService, never()).createSiteMapping(any(), any(), any());
  }

  @Test
  void testRetriggerEventsSuccess() throws Exception {
    List<String> visitIds = List.of("visit1");

    Visits visit = new Visits();
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setCustomerId(UUID.randomUUID());
    visitDocument.setSiteId(UUID.randomUUID());
    visitDocument.setVisitName("VisitName");
    visitDocument.setVisitDate(Instant.now());
    visitDocument.setMobileLastUpdatedTime(Instant.now());
    visitDocument.setCreateUser("createUser");
    visit.setVisitDocument(visitDocument);

    Accounts account = new Accounts();
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setDataSource(DataSource.LIFT);
    accountDocument.setGoldenRecordId("test");
    accountDocument.setOwnerId("test");
    accountDocument.setContacts(
        List.of(
            new Contact(
                UUID.randomUUID(),
                null,
                "sfdcContactId",
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                false,
                null,
                null,
                null,
                null,
                null,
                null)));
    account.setAccountDocument(accountDocument);

    Sites site = new Sites();
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setExternalId("siteExternalId");
    site.setSiteDocument(siteDocument);

    User user = new User();
    user.setId("userId");

    ActivityDocument activityDocument = new ActivityDocument();
    Activities activity = Activities.builder().activityDocument(activityDocument).build();
    EventDocument eventDocument = new EventDocument();
    eventDocument.setEventId("eventId");

    when(visitsRepository.findByVisitId(anyString())).thenReturn(visit);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);
    when(sitesRepository.findBySiteId(anyString())).thenReturn(site);
    when(liftUserService.findOwner(anyString())).thenReturn(user);
    when(activitiesRepository.save(any())).thenReturn(new Activities());
    when(liftEventService.createEvent(any(), any(), any())).thenReturn(eventDocument);
    when(activitiesRepository.findByDocumentIdAndDeleted(any())).thenReturn(activity);

    List<String> result =
        liftRetriggerService.retriggerEvents(
            visitIds, Locale.ENGLISH, new ResourceBundleMessageSource());

    assertTrue(result.contains("visit1"));
    verify(liftEventService).createEvent(any(), any(), any());
  }

  @Test
  void testRetriggerEventsAccountNotFound() {
    List<String> visitIds = List.of("visit1");

    Visits visit = new Visits();
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setCustomerId(UUID.randomUUID());
    visit.setVisitDocument(visitDocument);

    when(visitsRepository.findByVisitId(anyString())).thenReturn(visit);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(null);

    assertThrows(
        NotFoundDEException.class,
        () ->
            liftRetriggerService.retriggerEvents(
                visitIds, Locale.ENGLISH, new ResourceBundleMessageSource()));
  }

  @Test
  void testRetriggerEventsSiteNotFound() {
    List<String> visitIds = List.of("visit1");

    Visits visit = new Visits();
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setCustomerId(UUID.randomUUID());
    visitDocument.setSiteId(UUID.randomUUID());
    visit.setVisitDocument(visitDocument);

    Accounts account = new Accounts();
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setDataSource(DataSource.LIFT);
    accountDocument.setGoldenRecordId("test");
    accountDocument.setOwnerId("test");
    account.setAccountDocument(accountDocument);

    when(visitsRepository.findByVisitId(anyString())).thenReturn(visit);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);
    when(sitesRepository.findBySiteId(anyString())).thenReturn(null);

    NotFoundDEException thrown =
        assertThrows(
            NotFoundDEException.class,
            () -> {
              liftRetriggerService.retriggerEvents(
                  visitIds, Locale.ENGLISH, new ResourceBundleMessageSource());
            });

    assertEquals("SiteId Not found", thrown.getMessage());
  }

  @Test
  void testRetriggerEventsExceptionHandling() throws Exception {
    List<String> visitIds = List.of("visit1");

    Visits visit = new Visits();
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setCustomerId(UUID.randomUUID());
    visitDocument.setSiteId(UUID.randomUUID());
    visit.setVisitDocument(visitDocument);

    Accounts account = new Accounts();
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setDataSource(DataSource.LIFT);
    accountDocument.setGoldenRecordId("test");
    accountDocument.setOwnerId("test");
    account.setAccountDocument(accountDocument);

    Sites site = new Sites();
    SiteDocument siteDocument = new SiteDocument();
    site.setSiteDocument(siteDocument);

    User user = new User();
    user.setId("userId");

    when(visitsRepository.findByVisitId(anyString())).thenReturn(visit);
    when(accountsRepository.findByAccountId(anyString())).thenReturn(account);
    when(sitesRepository.findBySiteId(anyString())).thenReturn(site);
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftEventService.createEvent(any(), any(), any()))
        .thenThrow(JsonProcessingException.class);

    List<String> result =
        liftRetriggerService.retriggerEvents(
            visitIds, Locale.ENGLISH, new ResourceBundleMessageSource());

    assertTrue(result.isEmpty());
  }

  @Test
  void testRetriggerAccountsSuccess() throws Exception {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);
    when(account.getAccountDocument()).thenReturn(mock(AccountDocument.class));
    when(account.getId()).thenReturn(1L);
    when(account.getAccountDocument().getDataSource()).thenReturn(DataSource.LIFT);

    when(account.getAccountDocument().getOwnerId()).thenReturn("test");

    User user = mock(User.class);
    when(user.getEmail()).thenReturn("<EMAIL>");
    when(liftUserService.findUserByEmail(anyString())).thenReturn(user);
    ProfileRecord profileRecord = new ProfileRecord();
    UserLicenseRecord userLicenseRecord = new UserLicenseRecord();
    userLicenseRecord.setName("test");
    profileRecord.setUserLicense(userLicenseRecord);
    user.setProfile(profileRecord);
    // Act
    List<String> result =
        liftRetriggerService.retriggerAccounts(accountIds, locale, resourceBundleMessageSource);

    // Assert
    assertEquals(1, result.size());
    assertTrue(result.contains(accountId));
    verify(accountsRepository, times(1)).deleteById(account.getId());
  }

  @Test
  void testRetriggerAccountsAccountNotFound() {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    when(accountsRepository.findByAccountId(accountId)).thenReturn(null);

    // Act & Assert
    assertThrows(
        NotFoundDEException.class,
        () ->
            liftRetriggerService.retriggerAccounts(
                accountIds, locale, resourceBundleMessageSource));
  }

  @Test
  void testRetriggerAccountsAccountDocumentNotFound() {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);
    when(account.getAccountDocument()).thenReturn(null);

    // Act & Assert
    assertThrows(
        NotFoundDEException.class,
        () ->
            liftRetriggerService.retriggerAccounts(
                accountIds, locale, resourceBundleMessageSource));
  }

  @Test
  void testRetriggerAccountsUserNotFound() throws Exception {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);
    when(account.getAccountDocument()).thenReturn(mock(AccountDocument.class));
    when(account.getId()).thenReturn(1L);
    when(account.getAccountDocument().getDataSource()).thenReturn(DataSource.LIFT);

    when(liftUserService.findUserByEmail(any())).thenReturn(null);

    // Act
    List<String> result =
        liftRetriggerService.retriggerAccounts(accountIds, locale, resourceBundleMessageSource);

    // Assert
    assertTrue(!result.isEmpty());
  }

  @Test
  void testRetriggerAccountsWithNonLiftDataSource() throws Exception {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);
    when(account.getAccountDocument()).thenReturn(mock(AccountDocument.class));
    when(account.getId()).thenReturn(1L);
    when(account.getAccountDocument().getDataSource()).thenReturn(DataSource.UNKNOWN);
    // Act
    List<String> result =
        liftRetriggerService.retriggerAccounts(accountIds, locale, resourceBundleMessageSource);

    // Assert
    assertEquals(1, result.size());
    assertTrue(result.contains(accountId));
  }

  @Test
  void testRetriggerAccountsWithJsonProcessingException() throws Exception {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);
    when(account.getAccountDocument()).thenReturn(mock(AccountDocument.class));
    when(account.getId()).thenReturn(1L);
    when(account.getAccountDocument().getDataSource()).thenReturn(DataSource.LIFT);

    // Act
    List<String> result =
        liftRetriggerService.retriggerAccounts(accountIds, locale, resourceBundleMessageSource);
    System.out.println(result.toString());

    assertTrue(!result.isEmpty());
  }

  @Test
  void testRetriggerAccountsWithNullAccountDocument() throws Exception {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);
    when(account.getAccountDocument()).thenReturn(null);

    // Act & Assert
    assertThrows(
        NotFoundDEException.class,
        () ->
            liftRetriggerService.retriggerAccounts(
                accountIds, locale, resourceBundleMessageSource));
    verify(accountsRepository, never()).deleteById(any());
    verify(liftAccountService, never())
        .createAccount(any(), eq(locale), eq(resourceBundleMessageSource));
  }

  @Test
  void testRetriggerAccountsSuccessWithContactsAndUserAccess() throws Exception {
    // Arrange
    String accountId = "123";
    List<String> accountIds = Arrays.asList(accountId);

    Accounts account = mock(Accounts.class);

    when(account.getAccountDocument()).thenReturn(mock(AccountDocument.class));
    when(account.getId()).thenReturn(1L);
    when(account.getAccountDocument().getDataSource()).thenReturn(DataSource.LIFT);
    when(account.getAccountDocument().getContacts())
        .thenReturn(
            List.of(
                Contact.builder()
                    .accountId(UUID.randomUUID())
                    .contactId(UUID.randomUUID())
                    .name("test")
                    .build()));

    when(account.getAccountDocument().getOwnerId()).thenReturn("test");
    when(accountsRepository.findByAccountId(accountId)).thenReturn(account);

    User user = mock(User.class);
    ProfileRecord profileRecord = mock(ProfileRecord.class);
    UserLicenseRecord userLicenseRecord = mock(UserLicenseRecord.class);

    when(user.getId()).thenReturn("user123");
    when(user.getEmail()).thenReturn("<EMAIL>");
    when(user.getProfile()).thenReturn(profileRecord);
    when(profileRecord.getUserLicense()).thenReturn(userLicenseRecord);
    when(userLicenseRecord.getName()).thenReturn("Chatter Free");

    when(liftUserService.findUserByEmail(anyString())).thenReturn(user);
    when(account.getAccountDocument().getUserRoles())
        .thenReturn(List.of(UserRole.builder().roleType("test").userName("<EMAIL>").build()));
    UserRole userRole = mock(UserRole.class);
    lenient().when(userRole.getUserName()).thenReturn("test");
    when(account.getAccountDocument().getCreateUser()).thenReturn("test");

    when(liftContactService.saveToLift(any(), eq(locale), eq(resourceBundleMessageSource)))
        .thenReturn(
            List.of(
                Contact.builder()
                    .accountId(UUID.randomUUID())
                    .contactId(UUID.randomUUID())
                    .name("test")
                    .build()));

    when(liftUserAccessService.createUserAccess(any(), any(), any())).thenReturn(Boolean.TRUE);

    // Act
    List<String> result =
        liftRetriggerService.retriggerAccounts(accountIds, locale, resourceBundleMessageSource);

    // Assert
    assertEquals(1, result.size());
    assertTrue(result.contains(accountId));
    verify(liftAccountService, times(1))
        .createAccount(any(), eq(locale), eq(resourceBundleMessageSource));
  }
}
