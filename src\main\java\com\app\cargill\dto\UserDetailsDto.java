/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDetailsDto implements UserDetails, Serializable {

  private Collection<? extends GrantedAuthority> authorities;

  private static final long serialVersionUID = -1378991736284474552L;

  @Builder.Default private transient Map<String, Object> additionalProperties = new HashMap<>();

  private String password;

  private String username;

  @Builder.Default private boolean accountExpired = false;

  @Builder.Default private boolean accountNonLocked = true;

  @Builder.Default private boolean isCredentialsNonExpired = true;

  @Builder.Default private boolean enabled = true;

  public UserDetailsDto(String username, String password) {
    this.username = username;
    this.password = password;
  }

  @Override
  public Collection<? extends GrantedAuthority> getAuthorities() {
    return authorities;
  }

  public void setAuthorities(Collection<? extends GrantedAuthority> authorities) {
    this.authorities = authorities;
  }

  @Override
  public String getPassword() {
    return this.password;
  }

  @Override
  public String getUsername() {
    return this.username;
  }

  @Override
  public boolean isAccountNonExpired() {
    return !this.accountExpired;
  }

  @Override
  public boolean isAccountNonLocked() {
    return this.accountNonLocked;
  }

  @Override
  public boolean isCredentialsNonExpired() {
    return this.isCredentialsNonExpired;
  }

  @Override
  public boolean isEnabled() {
    return this.enabled;
  }

  public Map<String, Object> getAdditionalProperties() {
    return additionalProperties;
  }
}
