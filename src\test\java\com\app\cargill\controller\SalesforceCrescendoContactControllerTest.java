/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import com.app.cargill.sf.crescendo.service.CrescendoContactService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoContactControllerTest {

  @Mock private CrescendoContactService crescendoContactService;
  @InjectMocks private SalesforceCrescendoContactController controller;

  @Test
  void whenGetContactsIsCalledCorrectResponseIsReturned() {
    when(crescendoContactService.getContactsForSync(any()))
        .thenReturn(List.of(mock(ContactCrescendo.class)));
    List<ContactCrescendo> result = controller.getContacts(Instant.now());
    assertNotNull(result);
  }

  @Test
  void whenContactsAreProvidedCorrectResponseIsReturned() {
    ResponseEntity<Void> result = controller.upsertContacts(new ArrayList<>());
    assertEquals(HttpStatus.ACCEPTED, result.getStatusCode());
  }
}
