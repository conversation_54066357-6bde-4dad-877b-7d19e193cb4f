<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="date=no">
    <meta name="format-detection" content="address=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="x-apple-disable-message-reformatting">
    <link href="../../app/generatedTemplateResources/css/main.css" rel="stylesheet">
    <script src="../../app/generatedTemplateResources/js/chart.js"></script>
    
</head>
<body>

    <div class="container">
        <!-- Header Section -->
        <div class="template-header">
            <figure>
                <img src="../../app/generatedTemplateResources/images/cargill-logo.svg" alt="Cargill Logo">
            </figure>
        </div>

        <!-- Card Section -->
        <div class="card mb-5">
            <!-- Visit and Tool Details -->
            <div class="card-header pt-5">
                <h4 class="mb-2">${model.visitName!}</h4>

                <div class="row">
                    <div class="content-set">
                        <label>${localization.getMessage("Report.Tool.Name", [], "Tool Name", locale)}:</label>
                        <h4>${model.toolName!}</h4>
                    </div>
                    <div class="content-set mx-2">
                        <label>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}:</label>
                        <h4>${model.visitDate!}</h4>
                    </div>
                </div>
            </div>

            <!-- Chart Canvas -->
            <canvas id="chartCanvas"></canvas>
        </div>

        </div>
					<div id="region" style="opacity: 0;">${region}</div>
				</div>

    <!-- Chart.js Script -->
    <script>
    // Ensure null safety when accessing model data
    const labels = [
        <#if model.leftYAxis??>
            <#list model.leftYAxis as left>
                "${left.x!}"<#if left_has_next>,</#if>
            </#list>
        <#else>
            "No Data Available"
        </#if>
    ];

    const leftYAxisData = [
        <#if model.leftYAxis??>
            <#list model.leftYAxis as left>
                ${left.y?default(0)}<#if left_has_next>,</#if>
            </#list>
        <#else>
            0
        </#if>
    ];

    const rightYAxisData = [
        <#if model.rightYAxis??>
            <#list model.rightYAxis as right>
                ${right.y?default(0)}<#if right_has_next>,</#if>
            </#list>
        <#else>
            0
        </#if>
    ];

    const maxLeftYAxis = Math.max(...leftYAxisData) + 10;  // Maximum for the left Y-axis with padding
    const maxRightYAxis = Math.max(...rightYAxisData) + 10; // Maximum for the right Y-axis with padding

    const chartData = {
        labels: labels,
        datasets: [
            {
                label: "${localization.getMessage("ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day", [], "Revenue Per Cow Per Day", locale)}",
                data: leftYAxisData,
                backgroundColor: '#1baca7',  // Same as bar chart color
                borderColor: '#1baca7',      // Same as bar chart border
                yAxisID: 'y1',
                type: 'bar',
                order: 2, // Ensure this dataset is rendered after the line chart
            },
            {
                label: "${localization.getMessage("ProfitabilityAnalysis.Total.Diet.Cost", [], "Total Diet Cost($/Cow/Day)", locale)?replace("{0}", model.currency)}",
                data: rightYAxisData,
                borderColor: '#DA9E44',
                backgroundColor: 'rgba(0,0,0,0)', // Make sure background is transparent
                yAxisID: 'y',
                type: 'line',
                order: 1, // Ensure this dataset is rendered before the bars
            }
        ]
    };

    const config = {
        type: 'bar',
        data: chartData,
        options: {
            responsive: true,
            scales: {
                y1: {
                    type: 'linear',
                    position: 'left',
                    beginAtZero: true,
                    max: maxLeftYAxis,  // Set maximum dynamically for left Y-axis
                    title: {
                        display: true,
                        text: "${localization.getMessage("ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day", [], "Revenue Per Cow Per Day", locale)}",
                        font: {
                            family: 'Arial',
                            size: 14,
                            weight: 'bold'
                        },
                    }
                },
                y: {
                    type: 'linear',
                    position: 'right',
                    beginAtZero: true,
                    max: maxRightYAxis,  // Set maximum dynamically for right Y-axis
                    title: {
                        display: true,
                        text: "${localization.getMessage("ProfitabilityAnalysis.Total.Diet.Cost", [], "Total Diet Cost($/Cow/Day)", locale)?replace("{0}", model.currency)}",
                        font: {
                            family: 'Arial',
                            size: 14,
                            weight: 'bold'
                        },
                    }
                }
            },
            animation: {
                duration: 0,
                onComplete: function() {
                    var chart = this;
                    var ctx = chart.ctx;
                    ctx.save();
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillStyle = '#6C7782';
                    ctx.font = 'bold 12px Arial';  // Set bold font

                    this.data.datasets.forEach(function(dataset, i) {
                        var meta = chart.getDatasetMeta(i);
                        meta.data.forEach(function(element, index) {
                            var data = dataset.data[index];

                            // Fetch locale from region element
                            let regionText = document.getElementById("region").innerHTML || "en-US";
                            try {
                                data = isNaN(data) ? '' : new Intl.NumberFormat(regionText).format(data);
                            } catch (e) {
                                data = data; // Fallback if formatting fails
                            }

                            // Determine position for text
                            var position = element.tooltipPosition();
                            var yIndex;

                            if (dataset.type === 'bar') {
                                yIndex = (element.y + element.base) / 2; // Move the text to the middle of the bar
                            } else {
                                yIndex = position.y - 10; // Slightly higher for line chart points
                            }

                            // Adjust for negative values if needed
                            if (data < 0) {
                                yIndex = element.y + 15;
                            }

                            // Render formatted data in bold
                            ctx.fillText(data, position.x, yIndex);
                        });
                    });
                    ctx.restore();
                }
            },
            // Legend configuration to place it at the bottom
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Arial',
                            size: 12,
                            weight: 'bold',
                        },
                    },
                }
            }
        }
    };

    // Ensure chart initialization only after the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function () {
        const ctx = document.getElementById('chartCanvas').getContext('2d');
        const myChart = new Chart(ctx, config);
    });
    </script>

</body>
</html>
