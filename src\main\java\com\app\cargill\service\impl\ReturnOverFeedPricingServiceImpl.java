/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.ReturnOverFeedType;
import com.app.cargill.dto.ReturnOverFeedPricePerTonDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.ReturnOverFeedPricings;
import com.app.cargill.repository.ReturnOverFeedPricingsRepository;
import com.app.cargill.service.IReturnOverFeedPricingService;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("returnOverFeedPricingServiceImpl")
@RequiredArgsConstructor
public class ReturnOverFeedPricingServiceImpl implements IReturnOverFeedPricingService {
  private final ReturnOverFeedPricingsRepository returnOverFeedPricingsRepository;

  @Override
  public List<ReturnOverFeedPricePerTonDto> getPricePerTon(ReturnOverFeedType returnOverFeedType) {

    List<ReturnOverFeedPricings> returnOverFeedPricings;
    if (returnOverFeedType.equals(ReturnOverFeedType.ALL)) {
      returnOverFeedPricings = returnOverFeedPricingsRepository.findAll();
    } else {
      returnOverFeedPricings =
          returnOverFeedPricingsRepository.findAllByType(returnOverFeedType.toString());
    }

    if (returnOverFeedPricings.isEmpty()) {
      return Collections.emptyList();
    }

    return returnOverFeedPricings.stream().map(this::mapToDto).toList();
  }

  private ReturnOverFeedPricePerTonDto mapToDto(ReturnOverFeedPricings returnOverFeedPricings) {
    return ReturnOverFeedPricePerTonDto.builder()
        .name(returnOverFeedPricings.getPricingReturnOverFeedDocument().getName())
        .price(returnOverFeedPricings.getPricingReturnOverFeedDocument().getPrice())
        .returnOverFeedType(
            returnOverFeedPricings.getPricingReturnOverFeedDocument().getReturnOverFeedType())
        .id(returnOverFeedPricings.getPricingReturnOverFeedDocument().getId())
        .value(returnOverFeedPricings.getPricingReturnOverFeedDocument().getValue())
        .build();
  }

  @Override
  public ReturnOverFeedPricePerTonDto update(
      ReturnOverFeedPricePerTonDto returnOverFeedPricePerTon) {

    ReturnOverFeedPricings feedPricingsById =
        returnOverFeedPricingsRepository.findByUUID(returnOverFeedPricePerTon.getId().toString());
    if (feedPricingsById == null) {
      throw new NotFoundDEException(
          "No record found against: " + returnOverFeedPricePerTon.getId());
    }
    mapToModel(returnOverFeedPricePerTon, feedPricingsById);

    returnOverFeedPricingsRepository.save(feedPricingsById);

    return mapToDto(feedPricingsById);
  }

  private ReturnOverFeedPricings mapToModel(
      ReturnOverFeedPricePerTonDto pricing, ReturnOverFeedPricings feedPricingsById) {

    feedPricingsById.getPricingReturnOverFeedDocument().setPrice(pricing.getPrice());
    feedPricingsById.getPricingReturnOverFeedDocument().setName(pricing.getName());
    feedPricingsById
        .getPricingReturnOverFeedDocument()
        .setReturnOverFeedType(pricing.getReturnOverFeedType());
    feedPricingsById.getPricingReturnOverFeedDocument().setLastModifiedTimeUtc(Instant.now());
    feedPricingsById.getPricingReturnOverFeedDocument().setValue(pricing.getValue());
    return feedPricingsById;
  }
}
