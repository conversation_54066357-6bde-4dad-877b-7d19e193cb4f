/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.migration.CosmosDataMigration.MigrationType;
import com.app.cargill.cosmos.model.ContentDetailsCosmos;
import com.app.cargill.cosmos.model.NotesCosmos;
import com.app.cargill.cosmos.model.NotesCosmos.NotesCosmosMediaItem;
import com.app.cargill.cosmos.model.VisitCosmos;
import com.app.cargill.cosmos.repo.ContentDetailsCosmosRepository;
import com.app.cargill.cosmos.repo.NotesCosmosRepository;
import com.app.cargill.cosmos.repo.VisitsCosmosRepository;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.model.Notes;
import com.app.cargill.repository.NotesRepository;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@Slf4j
class NotesMigrationTest {

  @Mock private VisitsCosmosRepository visitsCosmosRepository;
  @Mock private NotesCosmosRepository cosmosRepository;

  @Mock private NotesRepository dbRepository;

  @Mock private ContentDetailsCosmosRepository contentDetailsCosmosRepository;

  @InjectMocks private NotesMigration migration;

  @Test
  void whenMigrationPassesCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {

    Flux<NotesCosmos> cosmosFlux = loadNotes();

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(contentDetailsCosmosRepository.findAll()).thenReturn(loadContentDetails(cosmosFlux));
    when(dbRepository.save(any())).thenReturn(mock(Notes.class));
    when(visitsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(VisitCosmos.class)));
    when(contentDetailsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(ContentDetailsCosmos.class)));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(3, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenMigrationPassesCorrectWithNullIdsResultIsReturned()
      throws ExecutionException, InterruptedException {

    Flux<NotesCosmos> cosmosFlux = loadNotes();
    cosmosFlux
        .toStream()
        .forEach(
            row -> {
              row.setVisitId(null);
              row.getMediaItems().stream()
                  .forEach(
                      media -> {
                        media.setMediaId(null);
                      });
            });
    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(contentDetailsCosmosRepository.findAll()).thenReturn(loadContentDetails(cosmosFlux));
    when(dbRepository.save(any())).thenReturn(mock(Notes.class));
    when(visitsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(VisitCosmos.class)));
    when(contentDetailsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(ContentDetailsCosmos.class)));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(3, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenMigrationPassesButSomeRecordsFailCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    NotesCosmos noteCosmos1 = new NotesCosmos();
    noteCosmos1.setId(UUID.randomUUID().toString());
    noteCosmos1.setAccountId(UUID.randomUUID().toString());
    noteCosmos1.setVisitId(UUID.randomUUID().toString());

    NotesCosmos noteCosmos2 = new NotesCosmos();
    noteCosmos2.setId(UUID.randomUUID().toString());
    noteCosmos2.setAccountId(UUID.randomUUID().toString());
    noteCosmos2.setVisitId(UUID.randomUUID().toString());

    NotesCosmos noteCosmos3 = new NotesCosmos();
    noteCosmos3.setId(UUID.randomUUID().toString());
    noteCosmos3.setAccountId(UUID.randomUUID().toString());
    noteCosmos3.setVisitId(UUID.randomUUID().toString());

    Flux<NotesCosmos> cosmosFlux = Flux.just(noteCosmos1, noteCosmos2, noteCosmos3);
    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(contentDetailsCosmosRepository.findAll()).thenReturn(loadContentDetails(cosmosFlux));
    when(dbRepository.findByDocumentId(any()))
        .thenThrow(new IllegalStateException())
        .thenReturn(null);
    when(dbRepository.save(any())).thenThrow(new RuntimeException()).thenReturn(mock(Notes.class));
    when(visitsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(VisitCosmos.class)));

    MigrationResult migrationResult = migration.moveAll().get();

    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(2, migrationResult.getFailed());
  }

  @Test
  void whenDataIsWrongThePipelinePassesButFailsAreMarked()
      throws ExecutionException, InterruptedException {
    NotesCosmos noteCosmos1 = new NotesCosmos();
    noteCosmos1.setId("random-string");
    noteCosmos1.setAccountId(UUID.randomUUID().toString());
    noteCosmos1.setVisitId(UUID.randomUUID().toString());

    NotesCosmos noteCosmos2 = new NotesCosmos();
    noteCosmos2.setId(UUID.randomUUID().toString());
    noteCosmos2.setAccountId(UUID.randomUUID().toString());
    noteCosmos2.setVisitId(UUID.randomUUID().toString());

    Flux<NotesCosmos> cosmosFlux = Flux.just(noteCosmos1, noteCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(contentDetailsCosmosRepository.findAll()).thenReturn(loadContentDetails(cosmosFlux));
    when(dbRepository.save(any())).thenReturn(mock(Notes.class));
    when(visitsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(VisitCosmos.class)));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(1, migrationResult.getFailed());
  }

  @Test
  void whenAccountIdIsMissingTheRecordIsSkipped() throws ExecutionException, InterruptedException {
    NotesCosmos noteCosmos1 = new NotesCosmos();
    noteCosmos1.setId(UUID.randomUUID().toString());
    noteCosmos1.setAccountId(null);
    noteCosmos1.setVisitId(UUID.randomUUID().toString());

    NotesCosmos noteCosmos2 = new NotesCosmos();
    noteCosmos2.setId(UUID.randomUUID().toString());
    noteCosmos2.setAccountId(UUID.randomUUID().toString());
    noteCosmos2.setVisitId(UUID.randomUUID().toString());

    Flux<NotesCosmos> cosmosFlux = Flux.just(noteCosmos1, noteCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(contentDetailsCosmosRepository.findAll()).thenReturn(loadContentDetails(cosmosFlux));
    when(dbRepository.save(any())).thenReturn(mock(Notes.class));
    when(visitsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(VisitCosmos.class)));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenAccountIdIsInvalidTheRecordIsSkipped() throws ExecutionException, InterruptedException {
    NotesCosmos noteCosmos1 = new NotesCosmos();
    noteCosmos1.setId(UUID.randomUUID().toString());
    noteCosmos1.setAccountId("invalid-id");
    noteCosmos1.setVisitId(UUID.randomUUID().toString());

    NotesCosmos noteCosmos2 = new NotesCosmos();
    noteCosmos2.setId(UUID.randomUUID().toString());
    noteCosmos2.setAccountId(UUID.randomUUID().toString());
    noteCosmos2.setVisitId(UUID.randomUUID().toString());

    Flux<NotesCosmos> cosmosFlux = Flux.just(noteCosmos1, noteCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(contentDetailsCosmosRepository.findAll()).thenReturn(loadContentDetails(cosmosFlux));
    when(dbRepository.save(any())).thenReturn(mock(Notes.class));
    when(visitsCosmosRepository.findById(anyString()))
        .thenReturn(Mono.just(mock(VisitCosmos.class)));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void correctTypeIsReturned() {
    assertEquals(MigrationType.NOTES, migration.migrationType());
  }

  private Flux<NotesCosmos> loadNotes() {
    List<NotesCosmos> notes = new ArrayList<>();
    for (int i = 0; i < 3; i++) {
      NotesCosmos noteCosmos = new NotesCosmos();
      noteCosmos.setId(UUID.randomUUID().toString());
      noteCosmos.setAccountId(UUID.randomUUID().toString());
      noteCosmos.setNote("some note " + i);
      noteCosmos.setTitle("some title " + i);
      noteCosmos.setVisitId(UUID.randomUUID().toString());
      noteCosmos.setSection(i);
      noteCosmos.setCategory(i);
      noteCosmos.setCreateUser(UUID.randomUUID().toString());
      noteCosmos.setDeleted(false);
      noteCosmos.setLastModifyUser(UUID.randomUUID().toString());
      noteCosmos.setCreateTimeUtc(Instant.now());
      noteCosmos.setLastModifiedTimeUtc(new DateEpoch());
      noteCosmos.setLastSyncTimeUtc(Instant.now());
      noteCosmos.setNew(true);
      noteCosmos.setLastModifiedTimeUtc(new DateEpoch(Instant.now()));
      noteCosmos.setMediaItems(new ArrayList<>());

      NotesCosmosMediaItem notesCosmosMediaItem = new NotesCosmosMediaItem();
      notesCosmosMediaItem.setMediaId(UUID.randomUUID().toString());
      notesCosmosMediaItem.setNoteId(noteCosmos.getId());
      notesCosmosMediaItem.setMediaType(i);
      notesCosmosMediaItem.setCreateUtc(Instant.now());
      notesCosmosMediaItem.setLatitude(34.2343243);
      notesCosmosMediaItem.setLongitude(108.4235231);
      notesCosmosMediaItem.setCreateUserId(UUID.randomUUID().toString());
      notesCosmosMediaItem.setIsNew(true);

      noteCosmos.getMediaItems().add(notesCosmosMediaItem);

      notes.add(noteCosmos);
    }
    Flux<NotesCosmos> cosmosFlux = Flux.just(notes.toArray(NotesCosmos[]::new));

    return cosmosFlux;
  }

  private Flux<ContentDetailsCosmos> loadContentDetails(Flux<NotesCosmos> cosmosNotes) {
    List<ContentDetailsCosmos> contentDetails = new ArrayList<>();
    List<NotesCosmos> notes = cosmosNotes.collectList().block();
    for (NotesCosmos note : notes) {
      ContentDetailsCosmos detail = new ContentDetailsCosmos();
      detail.setAccountId(note.getAccountId());
      detail.setCreateUser(note.getCreateUser());
      detail.setDeleted(false);
      detail.setName("some content");
      detail.setReportType(1);
      if (note.getMediaItems() != null && !note.getMediaItems().isEmpty()) {
        detail.setId(note.getMediaItems().get(0).getMediaId());
        detail.setLabyrinthContentType(note.getMediaItems().get(0).getMediaType());
      } else {
        detail.setId(UUID.randomUUID().toString());
        detail.setLabyrinthContentType(1);
      }

      detail.setNew(true);
      //      detail.setReportType(note.getMediaItems().get(0).getReportType());

      contentDetails.add(detail);
    }

    return Flux.just(contentDetails.toArray(ContentDetailsCosmos[]::new));
  }

  //  @Test
  //  void test() {
  //    Flux<String> flux =
  //    flux.map(this::somethingWrong)
  //        .retryWhen(Retry.fixedDelay(3, Duration.ofSeconds(5)))
  //        .blockLast();
  //        ;
  //  }
  //
  //  private Flux<String> getData() {
  //    return Flux.just("A", "B", "C").map(this::somethingWrong)
  //        .retryWhen(Retry.fixedDelay(3, Duration.ofSeconds(5)));
  //  }
  //
  //  private String somethingWrong(String input) {
  //    log.error(input);
  //    if(input.equals("B")) {
  //      log.error("Something happened");
  //      throw new NullPointerException();
  //    }
  //    return input;
  //  }
}
