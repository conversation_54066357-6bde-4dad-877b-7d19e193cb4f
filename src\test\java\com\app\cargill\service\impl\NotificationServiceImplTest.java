/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.NotificationType;
import com.app.cargill.document.NotificationDocument;
import com.app.cargill.dto.NotificationDto;
import com.app.cargill.dto.ReadNotificationDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Notifications;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.NotificationsRepository;
import com.app.cargill.repository.VisitsRepository;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class NotificationServiceImplTest {

  @Mock private UserServiceImpl userServiceImpl;

  @Mock private NotificationsRepository notificationsRepository;

  @Mock private NotesRepository notesRepository;
  @Mock private VisitsRepository visitsRepository;
  @Mock private AccountsRepository accountsRepository;

  @InjectMocks private NotificationServiceImpl notificationServiceImpl;

  @Test
  void whenFetchingDataResultIsReturnCorrectly() {
    Page<Notifications> notifications = new PageImpl<>(List.of(loadNotifications()));
    when(notificationsRepository.findByUserId(any(), any(), any())).thenReturn(notifications);

    Page<NotificationDto> result =
        notificationServiceImpl.getAllNotificationsPaginated(0, 1, "id", "desc", Instant.now());

    assertNotNull(result);
    assertEquals(1, result.getSize());
  }

  @Test
  void whenFetchingDataEmptyPaginatedListIsReturnedWhenNoDataFound() {
    when(notificationsRepository.findByUserId(any(), any(), any())).thenReturn(null);

    Page<NotificationDto> result =
        notificationServiceImpl.getAllNotificationsPaginated(0, 10, "id", "desc", Instant.now());

    assertNotNull(result);
    assertEquals(0, result.getSize());
  }

  private Notifications loadNotifications() {
    return Notifications.builder()
        .notificationDocument(
            NotificationDocument.builder()
                .createTimeUtc(Instant.now())
                .type(NotificationType.OneHourBeforeActionIsDue)
                .description("test")
                .id(UUID.randomUUID())
                .isDeleted(false)
                .isRead(true)
                .keys(new HashMap<>())
                .mobileCreatedAt(Instant.now())
                .title("test")
                .userId("<EMAIL>")
                .build())
        .build();
  }

  @Test
  void whenSavingDataIsSavedCorrectly() {
    when(notificationsRepository.findByLocalId(any())).thenReturn(null);
    when(notificationsRepository.save(any())).thenReturn(loadNotifications());
    NotificationDto notificationDto =
        NotificationDto.builder()
            .localId(UUID.randomUUID().toString())
            .type(NotificationType.OneHourBeforeActionIsDue)
            .description("test")
            .id(UUID.randomUUID())
            .deleted(false)
            .isRead(true)
            .keys(new HashMap<>())
            .mobileCreatedAt(Instant.now())
            .title("test")
            .userId("<EMAIL>")
            .build();

    NotificationDto result = notificationServiceImpl.save(notificationDto);
    assertNotNull(result.getId());
  }

  @Test
  void whenObjectIsAlreadySyncedInSave() {
    when(notificationsRepository.findByLocalId(any())).thenReturn(List.of(loadNotifications()));

    NotificationDto notificationDto =
        NotificationDto.builder()
            .localId(UUID.randomUUID().toString())
            .type(NotificationType.OneHourBeforeActionIsDue)
            .description("test")
            .id(UUID.randomUUID())
            .deleted(false)
            .isRead(true)
            .keys(new HashMap<>())
            .mobileCreatedAt(Instant.now())
            .title("test")
            .userId("<EMAIL>")
            .build();
    NotificationDto result = notificationServiceImpl.save(notificationDto);
    assertNotNull(result);
  }

  @Test
  void whenUpdateIsCalledCorrectResultIsUpdated() {
    when(notificationsRepository.findByNotificationId(any())).thenReturn(loadNotifications());
    when(notificationsRepository.save(any())).thenReturn(loadNotifications());

    NotificationDto notificationDto =
        NotificationDto.builder()
            .localId(UUID.randomUUID().toString())
            .type(NotificationType.OneHourBeforeActionIsDue)
            .description("test")
            .id(UUID.randomUUID())
            .deleted(false)
            .isRead(true)
            .keys(new HashMap<>())
            .mobileCreatedAt(Instant.now())
            .title("test")
            .userId("<EMAIL>")
            .build();
    NotificationDto result = notificationServiceImpl.update(notificationDto);

    assertNotNull(result);
  }

  @Test
  void whenNotificationsAreMarkedAsReadAllAreMarkedCorrectly() {
    when(notificationsRepository.findByNotificationId(any())).thenReturn(loadNotifications());
    when(notificationsRepository.saveAllAndFlush(any())).thenReturn(List.of(loadNotifications()));

    ReadNotificationDto readNotificationDto =
        ReadNotificationDto.builder().notificationIds(List.of(UUID.randomUUID())).build();
    ReadNotificationDto result = notificationServiceImpl.markAsRead(readNotificationDto);

    assertNotNull(result.getSuccess());
  }

  @Test
  void whenNotificationsAreNotFoundCorrectResultIsReturned() {
    when(notificationsRepository.findByNotificationId(any())).thenReturn(loadNotifications());
    when(notificationsRepository.saveAllAndFlush(any())).thenReturn(null);

    ReadNotificationDto readNotificationDto =
        ReadNotificationDto.builder().notificationIds(List.of(UUID.randomUUID())).build();
    ReadNotificationDto result = notificationServiceImpl.markAsRead(readNotificationDto);

    assertNotNull(result.getFailure());
  }

  @Test
  void whenNotificationsListNotExistReadAsMarkThrowsAnException() {
    ReadNotificationDto readNotificationDto = ReadNotificationDto.builder().build();
    Assertions.assertThrows(
        NotFoundDEException.class, () -> notificationServiceImpl.markAsRead(readNotificationDto));
  }

  @Test
  void whenNotificationIdsFilterIsCalledCorrectResultIsReturned() {
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(visitsRepository.findVisitIdsByAccountIds(anyList()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(notesRepository.findNoteIdsByAccountId(anyList(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(notificationsRepository.findNotificationIdsByTypeAndUserId(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(notificationsRepository.findNotificationIdsByTypeUserIdAndNoteIds(any(), anyList(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(notificationsRepository.findNotificationIdsByTypeUserIdAndVisitIds(
            any(), anyList(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));

    List<String> notificationIds = notificationServiceImpl.getFilteredNotificationIds();

    assertNotNull(notificationIds);
  }
}
