/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.SegmentsDto;
import com.app.cargill.model.Segments;
import com.app.cargill.repository.SegmentsRepository;
import com.app.cargill.service.ISegmentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service("segmentServiceImpl")
@RequiredArgsConstructor
public class SegmentServiceImpl implements ISegmentService {

  private final SegmentsRepository segmentsRepository;

  @Override
  public List<SegmentsDto<Integer, String, Boolean>> getAllSegments() {
    List<Segments> segments = segmentsRepository.findAll();

    return segments.stream().map(this::mapToSegmentsDto).toList();
  }

  private SegmentsDto<Integer, String, Boolean> mapToSegmentsDto(Segments segment) {
    return new SegmentsDto<>(
        segment.getSegmentDocument().getKey(),
        segment.getSegmentDocument().getValue(),
        segment.getSegmentDocument().getDefaultValue());
  }
}
