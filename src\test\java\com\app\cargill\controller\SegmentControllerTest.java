/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.SegmentsDto;
import com.app.cargill.service.ISegmentService;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class SegmentControllerTest {

  @Mock private ISegmentService segmentService;

  @InjectMocks private SegmentController controller;

  @Test
  void getAllSegments() {

    when(segmentService.getAllSegments())
        .thenReturn(List.of(new SegmentsDto<>(1, "1", true), new SegmentsDto<>(2, "2", true)));

    ResponseEntity<ResponseEntityDto<List<SegmentsDto<Integer, String, Boolean>>>> result =
        controller.getAllSegments();

    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }
}
