<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="026" author="Moiz">
        <sql>
            DROP INDEX IF EXISTS pens_document_id_uidx;
            CREATE UNIQUE INDEX IF NOT EXISTS pens_document_Id_uidx ON pens((pen_document->>'Id'));
        </sql>
    </changeSet>
</databaseChangeLog>
