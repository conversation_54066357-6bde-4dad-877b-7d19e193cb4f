/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.LinkedHashMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CudChewingHerdAnalysisReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String analysisType;

  // below are for no of chews sheet

  @Builder.Default
  private LinkedHashMap<String, Double> chewsPerRegurgitation = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> goalChews = new LinkedHashMap<>();

  // below are for Cud Chewing % sheet
  @Builder.Default
  private LinkedHashMap<String, Double> cudChewingPercentage = new LinkedHashMap<>();

  @Builder.Default
  private LinkedHashMap<String, Double> goalCudChewingPercentage = new LinkedHashMap<>();

  @JsonIgnore private String[] lactationStages;
}
