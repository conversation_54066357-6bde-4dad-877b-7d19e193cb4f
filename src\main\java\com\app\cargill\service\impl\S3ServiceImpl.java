/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import com.app.cargill.constants.HttpMethods;
import com.app.cargill.dto.S3PresignedUrlsDto;
import com.app.cargill.service.IS3Service;
import java.io.*;
import java.net.URLConnection;
import java.time.Duration;
import java.util.Comparator;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

@Slf4j
@Service("s3ServiceImpl")
@RequiredArgsConstructor
public class S3ServiceImpl implements IS3Service {

  private static final String DEFAULT_CONTENT_TYPE = "image/png";

  @Value("${cloud.aws.s3.bucket-name}")
  @Setter
  private String bucketName;

  @Value("${cloud.aws.region}")
  @Setter
  private String awsRegion;

  private final S3ClientFactory clientFactory;
  private final AwsCredentialsFactory credentialsFactory;
  private final S3Presigner s3Presigner;
  @Setter private S3Client client;

  @EventListener(ApplicationReadyEvent.class)
  public void init() {
    client =
        S3Client.builder()
            .region(Region.of(awsRegion))
            .credentialsProvider(credentialsFactory.getCredentialsProvider())
            .httpClientBuilder(
                ApacheHttpClient.builder()
                    .maxConnections(50)
                    .connectionTimeout(Duration.ofSeconds(5)))
            .build();
  }

  @Override
  public String uploadFile(byte[] bytes, String fileName, boolean isPublic) {
    return uploadFile(bytes, fileName, this.bucketName, isPublic);
  }

  @Override
  public String uploadFile(byte[] bytes, String fileName, String bucketName, Boolean isPublic) {
    InputStream inputStream = new ByteArrayInputStream(bytes);
    return store(fileName, bucketName, inputStream, isPublic);
  }

  private String store(
      String fileName, String bucketName, InputStream fileStream, boolean isPublic) {
    try {
      AmazonS3 s3Client = clientFactory.getS3Client();
      String fileContentType = getContentType(fileStream);
      log.debug(" - file type: {}", fileContentType);
      ObjectMetadata meta = new ObjectMetadata();
      meta.setContentType(fileContentType);

      log.debug(" - Putting s3://{}/{}", bucketName, fileName);
      if (isPublic) {
        log.debug(" - Setting ACL PublicRead");
        s3Client.putObject(
            new com.amazonaws.services.s3.model.PutObjectRequest(
                    bucketName, fileName, fileStream, meta)
                .withCannedAcl(CannedAccessControlList.PublicRead));
      } else {
        log.debug(" - Setting ACL Private");
        s3Client.putObject(
            new com.amazonaws.services.s3.model.PutObjectRequest(
                    bucketName, fileName, fileStream, meta)
                .withCannedAcl(CannedAccessControlList.Private));
      }

      fileStream.close();
      String resourceUrl = ((AmazonS3Client) s3Client).getResourceUrl(bucketName, fileName);
      log.debug("Upload to S3 succeeded: {}", resourceUrl);
      return resourceUrl;
    } catch (Exception e) {
      log.error("error in uploading {} to s3://{}, {}", fileName, bucketName, e.getMessage(), e);
    }
    return null;
  }

  private String getContentType(InputStream fileStream) throws IOException {
    String guessedContentType = URLConnection.guessContentTypeFromStream(fileStream);

    if (StringUtils.isBlank(guessedContentType)) {
      guessedContentType = DEFAULT_CONTENT_TYPE;
    }
    return guessedContentType;
  }

  // snippet-start:[presigned.java2.generatepresignedurl.main]
  public String signBucket(String keyName, String contentTpe) {
    log.info(
        "bucket: "
            + bucketName
            + ", awsRegion: "
            + awsRegion
            + ", keyName: "
            + keyName
            + ", contentTpe: "
            + contentTpe);

    try {
      log.trace("signBucket: connection success ");

      PutObjectPresignRequest presignRequest =
          PutObjectPresignRequest.builder()
              .signatureDuration(Duration.ofMinutes(10))
              .putObjectRequest(o -> o.bucket(bucketName).key(keyName).contentType(contentTpe))
              .build();
      log.trace("PutObjectPresignRequest:  success ");

      PresignedPutObjectRequest putObjectPresignRequest =
          s3Presigner.presignPutObject(presignRequest);
      String myURL =
          putObjectPresignRequest.url() != null ? putObjectPresignRequest.url().toString() : null;
      log.info("Presigned URL to upload a file to: " + myURL);
      return myURL;
    } catch (Exception e) {
      log.error(e.getLocalizedMessage(), e);
    }
    return null;
  }

  // snippet-start:[presigned.java2.getobjectpresigned.main]
  public String getPresignedUrl(String keyName) {
    log.trace("bucket: " + bucketName + ", awsRegion: " + awsRegion + ", keyName: " + keyName);

    try {
      log.trace("getPresignedUrl: connection success ");

      GetObjectPresignRequest getObjectPresignRequest =
          GetObjectPresignRequest.builder()
              .signatureDuration(Duration.ofMinutes(60))
              .getObjectRequest(o -> o.bucket(bucketName).key(keyName))
              .build();
      log.trace("GetObjectPresignRequest:  success ");

      PresignedGetObjectRequest presignedGetObjectRequest =
          s3Presigner.presignGetObject(getObjectPresignRequest);
      String theUrl =
          presignedGetObjectRequest.url() != null
              ? presignedGetObjectRequest.url().toString()
              : null;
      log.trace("Presign URL: " + theUrl);

      return theUrl;
    } catch (Exception e) {
      log.error(e.getLocalizedMessage(), e);
    }
    return null;
  }
  // snippet-end:[presigned.java2.getobjectpresigned.main]

  @Override
  public List<S3PresignedUrlsDto> generatePresignedUrls(List<S3PresignedUrlsDto> dto) {
    if (!CollectionUtils.isEmpty(dto)) {
      dto.parallelStream()
          .forEach(
              urlInfo -> {
                if (urlInfo.getUrlType() == HttpMethods.GET) {
                  String url = getPresignedUrl(urlInfo.getId());
                  urlInfo.setGeneratedUrl(url);
                } else if (urlInfo.getUrlType() == HttpMethods.PUT) {
                  String url = signBucket(urlInfo.getId(), urlInfo.getContentType());
                  urlInfo.setGeneratedUrl(url);
                }
              });
    }
    return dto;
  }

  public byte[] getObjectFromS3(String keyName) throws AwsServiceException, IOException {
    GetObjectRequest request = GetObjectRequest.builder().bucket(bucketName).key(keyName).build();
    ResponseInputStream<GetObjectResponse> response = client.getObject(request);
    return response.readAllBytes();
  }

  @Override
  public List<S3PresignedUrlsDto> findObjects(String key) {
    ListObjectsV2Request request =
        ListObjectsV2Request.builder().bucket(bucketName).prefix(key).build();
    List<S3PresignedUrlsDto> s3PresignedUrlsDtos = null;
    try {
      ListObjectsV2Response result = client.listObjectsV2(request);
      if (result != null && result.contents() != null) {
        s3PresignedUrlsDtos =
            result.contents().parallelStream()
                .map(
                    res ->
                        S3PresignedUrlsDto.builder()
                            .id(res.key())
                            .generatedUrl(getPresignedUrl(res.key()))
                            .lastModified(res.lastModified())
                            .fileSize(res.size())
                            .build())
                .sorted(Comparator.comparing(S3PresignedUrlsDto::getLastModified))
                .toList();
      }
    } catch (AmazonS3Exception e) {
      log.error(e.getAdditionalDetails().entrySet().toString());
      throw e;
    }
    return s3PresignedUrlsDtos;
  }
}
