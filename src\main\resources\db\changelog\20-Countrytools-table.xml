<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="020" author="Taha">
    <sql>
    CREATE TABLE IF NOT EXISTS country_tools
(
    id bigint NOT NULL DEFAULT nextval('country_tools_id_seq'::regclass),
    created_date timestamp without time zone,
    deleted boolean DEFAULT false,
    local_id character varying(255) COLLATE pg_catalog."default",
    updated_date timestamp without time zone,
    country_tool_document jsonb,
    CONSTRAINT country_tools_pkey PRIMARY KEY (id));
    
          CREATE UNIQUE INDEX IF NOT EXISTS country_tool_document_id_uidx ON country_tools((country_tool_document->>'id'));
    </sql>
  </changeSet>

</databaseChangeLog>
