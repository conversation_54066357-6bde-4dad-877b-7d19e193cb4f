/* Cargill Inc.(C) 2022 */
package com.app.cargill.filterspecification;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.SearchKey;
import com.app.cargill.constants.SearchOperation;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.VisitsRepository;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaBuilder.In;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.time.Instant;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FilterSpecificationsTest {

  @Mock private Root<Visits> root;
  @Mock private CriteriaQuery<?> criteriaQuery;
  @Mock private CriteriaBuilder criteriaBuilder;
  @Mock private In<Object> expression;
  @Mock private Predicate predicate;
  @Mock VisitsRepository visitsRepository;
  @InjectMocks private FilterSpecification filterSpecification = new FilterSpecification();

  @Test
  void WhenSpecificationsListIsNull() {
    filterSpecification = setSpecifications(false, false, false, false, false);
    Predicate actualPredicate =
        filterSpecification.toPredicate(root, criteriaQuery, criteriaBuilder);
    assertEquals(null, actualPredicate);
  }

  @Test
  void WhenSpecificationsListIsNotNull() {
    filterSpecification = setSpecifications(true, true, true, true, true);
    when(criteriaBuilder.in(
            criteriaBuilder.function(
                "jsonb_extract_path_text",
                Object.class,
                root.<Instant>get("visit_document"),
                criteriaBuilder.literal("Status"))))
        .thenReturn(expression);
    lenient().when(criteriaBuilder.and(any(Predicate.class))).thenReturn(predicate);
    Predicate actualPredicate =
        filterSpecification.toPredicate(root, criteriaQuery, criteriaBuilder);
    assertEquals(null, actualPredicate);
  }

  private FilterSpecification setSpecifications(
      boolean visitStatus, boolean customerId, boolean siteId, boolean visitDate, boolean tools) {
    FilterSpecification specification = new FilterSpecification();
    if (visitStatus) {
      specification.add(
          new SearchCriteria(SearchKey.VISIT_STATUS, SearchOperation.IN, VisitStatus.InProgress));
    }
    if (customerId) {
      specification.add(
          new SearchCriteria(
              SearchKey.CUSTOMER_ID, SearchOperation.IN, "508c3253-a00b-4d58-bfaa-3a87f68d91ae"));
    }
    if (siteId) {
      specification.add(
          new SearchCriteria(
              SearchKey.SITE_ID, SearchOperation.IN, "07315216-9ea9-475a-a0fc-39dc8357d44a"));
    }
    if (visitDate) {
      specification.add(
          new SearchCriteria(
              SearchKey.VISIT_DATE,
              SearchOperation.BETWEEN,
              "2022-10-05T09:50:03.028Z,2022-10-10T09:50:03.028Z"));
    }
    if (tools) {
      specification.add(new SearchCriteria("RumenHealth", SearchOperation.NOT_NULL, null));
    }
    return specification;
  }
}
