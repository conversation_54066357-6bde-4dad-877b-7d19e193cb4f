/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.document.UserRole;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AccountDto extends BaseDto {

  private Instant lastSyncTime;
  private Instant currentTime;
  private Set<String> users;
  private String businessName;
  private String customerCode;
  private Integer accountType;
  private Integer type;
  private String country;
  private String imageToUploadBase64;
  private String photoId;
  private AddressDto physicalAddress;
  private String segmentId;
  private List<ContactDto> contacts;
  private boolean isFavourite;
  private Instant dateOfLastVisit;
  private Integer siteCount;
  private Boolean active;
  private List<UserRole> userRoles;
}
