/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint;

import static com.app.cargill.service.impl.DDWReportServiceImpl.MAX_RETRY_ATTEMPTS;
import static com.app.cargill.service.impl.DDWReportServiceImpl.MAX_RETRY_ATTEMPTS_DURATION;

import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.exceptions.ExceptionInstanceValidator;
import com.app.cargill.sharepoint.constant.Constants;
import com.app.cargill.sharepoint.service.SharePointWebClientFactory;
import java.io.Serializable;
import java.text.MessageFormat;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

@Component
@RequiredArgsConstructor
@Slf4j
public class SharepointServiceCalls {

  /** The Constant logger. */
  private final SharePointWebClientFactory clientFactory;

  private static final String SINGLE_INVERTED_COMMA = "'";

  private final SharepointProperties sharepointProperties;

  public ResponseEntity<String> postFileToSharePoint(
      Oauth2Dto auth, byte[] fileData, String folderName, String fileName) {
    log.info("Entering postFileToSharePoint()-----");
    String url = sharepointProperties.getInstanceUrl() + sharepointProperties.getPostFileUrl();
    final String queryUrl =
        MessageFormat.format(
            url,
            SINGLE_INVERTED_COMMA,
            folderName,
            SINGLE_INVERTED_COMMA,
            SINGLE_INVERTED_COMMA,
            fileName,
            SINGLE_INVERTED_COMMA);
    log.info("Query Url Successfully Generated");
    String sharepointResponse;

    try {

      sharepointResponse = doPost(auth, queryUrl, fileData, String.class).block();

      log.info(
          "Response from postFileToSharePoint :- {}",
          StringEscapeUtils.escapeJava(sharepointResponse));
    } catch (Exception e) {
      log.error("Error occur in post file to sharepoint");
      log.error("Unable to  post file to sharepoint - {}", e.getMessage());
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
    }
    log.info("Leaving postFileToSharePoint()-----");
    return new ResponseEntity<>(sharepointResponse, HttpStatus.OK);
  }

  public HttpStatus createFolderInSharePoint(Oauth2Dto auth, String folderName) {
    log.info("Entering createFolderInSharePoint()-----");
    final String url =
        sharepointProperties.getInstanceUrl() + sharepointProperties.getCreateFolderUrl();
    final String queryUrl =
        MessageFormat.format(url, SINGLE_INVERTED_COMMA, folderName, SINGLE_INVERTED_COMMA);
    log.info("Query Url for createFolderInSharePoint :- {}", queryUrl);
    String sharepointFolderExistsResponse;
    try {

      sharepointFolderExistsResponse = doPost(auth, queryUrl, String.class).block();

      log.info(
          "Response from createFolderInSharePoint :- {}",
          StringEscapeUtils.escapeJava(sharepointFolderExistsResponse));
    } catch (Exception e) {
      log.error("Error occur in create folder in sharepoint");
      log.error("Unable to  create folder in sharepoint - {}", e.getMessage());
      return HttpStatus.INTERNAL_SERVER_ERROR;
    }
    log.info("Leaving createFolderInSharePoint()-----");
    return HttpStatus.CREATED;
  }

  public String checkFolderExistsInSharePoint(Oauth2Dto auth, String folderName) {
    log.info("Entering checkFolderExistsInSharePoint()-----");
    final String url =
        sharepointProperties.getInstanceUrl() + sharepointProperties.getCheckFolderExistsUrl();
    final String queryUrl =
        MessageFormat.format(url, SINGLE_INVERTED_COMMA, folderName, SINGLE_INVERTED_COMMA);
    log.info("Query Url for checkFolderExistsInSharePoint :- {}", queryUrl);

    String checkFolderExistInSharePointResponse;
    try {

      checkFolderExistInSharePointResponse = doPost(auth, queryUrl, String.class).block();

      log.info(
          "Response from checkFolderExistsInSharePoint :- {}",
          org.apache.commons.text.StringEscapeUtils.escapeJava(
              checkFolderExistInSharePointResponse));
    } catch (Exception e) {
      log.error("Error occur in while checking folder exists in sharepoint");
      log.error("Exception occur while checking folder exists in sharepoint - {}", e.getMessage());
      return "{\"d\":{\"Exists\":false}}";
    }
    log.info("Leaving checkFolderExistsInSharePoint()-----");
    return checkFolderExistInSharePointResponse;
  }

  private HttpHeaders getHeaders(Oauth2Dto auth) {
    HttpHeaders headers = new HttpHeaders();
    headers.set(Constants.ACCEPT, Constants.APPLICATION_JSON_ODATA_VERBOSE);
    headers.set(Constants.CONTENT_TYPE, Constants.APPLICATION_JSON_ODATA_VERBOSE);
    headers.set(Constants.BINARY_STRING_REQUEST_BODY, "true");
    headers.set(Constants.AUTHORIZATION, Constants.TOKEN_TYPE + auth.getAccessToken());
    headers.setConnection(Constants.CLOSE);
    return headers;
  }

  public <T extends Serializable> Mono<T> doPost(
      Oauth2Dto authToken, String queryUrl, Object body, Class<T> clazz) {
    return clientFactory
        .createWebClient()
        .post()
        .uri(uriBuilder -> UriComponentsBuilder.fromUriString(queryUrl).build().toUri())
        .headers(header -> header.addAll(getHeaders(authToken)))
        .bodyValue(body)
        .exchangeToMono(
            clientResponse -> {
              if (clientResponse.statusCode().isError()) {
                log.info("Error While calling sharepoint api");
                return clientResponse.createException().flatMap(Mono::error);
              } else {
                log.info("calling sharepoint api success");
                return clientResponse.bodyToMono(clazz);
              }
            })
        .retryWhen(
            Retry.fixedDelay(MAX_RETRY_ATTEMPTS, Duration.ofSeconds(MAX_RETRY_ATTEMPTS_DURATION))
                .filter(
                    exp ->
                        ExceptionInstanceValidator.isUnknownHostExceptionError(exp)
                            || ExceptionInstanceValidator.isIOException(exp)));
  }

  public <T extends Serializable> Mono<T> doPost(
      Oauth2Dto authToken, String queryUrl, Class<T> clazz) {
    return clientFactory
        .createWebClient()
        .post()
        .uri(uriBuilder -> UriComponentsBuilder.fromUriString(queryUrl).build().toUri())
        .headers(header -> header.addAll(getHeaders(authToken)))
        .exchangeToMono(
            clientResponse -> {
              if (clientResponse.statusCode().isError()) {
                log.info(String.format("Error While calling sharepoint api :  %s ", queryUrl));
                return clientResponse.createException().flatMap(Mono::error);
              } else {
                log.info(String.format("calling sharepoint api success:  %s", queryUrl));
                return clientResponse.bodyToMono(clazz);
              }
            })
        .retryWhen(
            Retry.fixedDelay(MAX_RETRY_ATTEMPTS, Duration.ofSeconds(MAX_RETRY_ATTEMPTS_DURATION))
                .filter(
                    exp ->
                        ExceptionInstanceValidator.isUnknownHostExceptionError(exp)
                            || ExceptionInstanceValidator.isIOException(exp)));
  }
}
