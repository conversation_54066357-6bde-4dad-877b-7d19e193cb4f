/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.*;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IVisitService;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class VisitControllerTest {

  @Mock private IVisitService visitService;

  @InjectMocks private VisitController controller;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void fetchAllVisitsPaginatedOffline() {
    when(visitService.getAllVisitsByCurrentLoggedInUser(anyInt(), anyInt(), any(), any(), any()))
        .thenReturn(generatePage());
    ResponseEntity<ResponseEntityDto<Page<VisitDto>>> result =
        controller.fetchAllVisitsPaginatedInOfflineMode(1, 10, "sb", Instant.now(), "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  @Test
  void fetchAllVisitsPaginatedOnline() {
    when(visitService.getAllVisitsByCurrentLoggedInUser(anyInt(), anyInt(), any(), any(), any()))
        .thenReturn(generatePage());
    ResponseEntity<ResponseEntityDto<Page<VisitDto>>> result =
        controller.fetchAllVisitsPaginatedInOfflineMode(1, 10, "sb", Instant.now(), "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  @Test
  void whenGetAllVisitIdAndNameListReturnSuccess() {
    when(visitService.getVisitIdAndNameList(anyInt(), anyInt(), any(), any(), any()))
        .thenReturn(new PageImpl<>(Collections.emptyList()));
    ResponseEntity<ResponseEntityDto<Page<ISelectDto>>> result =
        controller.getVisitIdAndNameList(1, 10, "sb", Instant.now(), "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(0, result.getBody().getData().getTotalElements());
  }

  @Test
  void save() throws Exception {
    when(visitService.save(any(), any(), any())).thenReturn(VisitDto.builder().build());
    ResponseEntity<ResponseEntityDto<Object>> result =
        controller.save(VisitDto.builder().build(), locale.getLanguage());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void testSaveVisitFailure()
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {

    VisitDto visitDto = new VisitDto();
    when(visitService.save(any(), any(), any()))
        .thenThrow(new CustomDEExceptions("Test exception message"));

    ResponseEntity<ResponseEntityDto<Object>> response =
        controller.save(visitDto, Locale.ENGLISH.getLanguage());

    verify(visitService, times(1)).save(any(), any(), any());

    assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
  }

  @Test
  void testUpdateVisitsFailure()
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {

    VisitDto visitDto = new VisitDto();
    when(visitService.update(eq(visitDto), any(), any()))
        .thenThrow(new CustomDEExceptions("Test exception message"));

    ResponseEntity<ResponseEntityDto<Object>> response =
        controller.update(visitDto, locale.getLanguage());

    verify(visitService, times(1)).update(any(), any(), any());

    assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
  }

  @Test
  void testautoPublish()
      throws ClassNotFoundException, CustomDEExceptions, IllegalAccessException,
          JsonProcessingException {
    UUID ID_VISIT = UUID.fromString("00000000-0000-0000-0000-000000000002");
    VisitPublishDto visitPublicDto = new VisitPublishDto(ID_VISIT, ID_VISIT, ID_VISIT);
    visitPublicDto.setVisitId(ID_VISIT);
    List<VisitPublishDto> visitPublishDtoList = new ArrayList<>();
    visitPublishDtoList.add(visitPublicDto);

    ResponseEntity<ResponseEntityDto<VisitPublishResponseDto>> result =
        controller.autoPublish(visitPublishDtoList, "en");
    assertNotNull(result);
  }

  private List<VisitDto> generateVisits() {
    return List.of(VisitDto.builder().build(), VisitDto.builder().build());
  }

  private Page<VisitDto> generatePage() {
    return new PageImpl<>(generateVisits());
  }
}
