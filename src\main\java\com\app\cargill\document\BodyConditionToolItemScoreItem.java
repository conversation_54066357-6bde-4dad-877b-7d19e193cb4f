/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.BCSPointScale;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BodyConditionToolItemScoreItem implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("BCSCategory")
  public Double bcsCategory;

  @JsonProperty("AnimalsObserved")
  public Integer animalsObserved;

  @JsonProperty("PercentOfPen")
  public Double percentOfPen;

  @JsonProperty("SelectedPointScale")
  public BCSPointScale selectedPointScale;
}
