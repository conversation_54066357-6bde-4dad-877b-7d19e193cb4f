/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.simple.UserAccessUpdateModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class LiftUserAccessService {

  private final LiftApiService liftApi;

  public boolean createUserAccess(String userId, String accountId, String role) {
    if (!inputModelIsValid(userId, accountId, role)) {
      throw new IllegalArgumentException("Some of the required fields are null.");
    }
    AuthToken authToken = liftApi.getToken();
    String apiPath = liftApi.getLatestApiPath(authToken);
    return createUserAccess(authToken, apiPath, userId, accountId, role);
  }

  public boolean createUserAccess(
      AuthToken authToken, String apiPath, String userId, String accountId, String role) {
    try {
      String eventUrl = String.format("%s/sobjects/DE_User_Access__c", apiPath);
      UserAccessUpdateModel userAccessUpdateModel = documentToModel(userId, accountId, role);

      log.debug("CREATE_USER_ACCESS");
      CreateRecordResponse recordResponse =
          liftApi.createRecord(
              authToken, userAccessUpdateModel, new ParameterizedTypeReference<>() {}, eventUrl);
      log.debug("USER_ACCESS_CREATED {}", userAccessUpdateModel);
      return recordResponse.isSuccess();
    } catch (Exception e) {
      log.error("USER_ACCESS_CREATION_ERROR", e);
      throw new LiftException(e);
    }
  }

  private boolean inputModelIsValid(String userId, String accountId, String role) {
    return userId != null
        && accountId != null
        && role != null
        && (role.equals("Account Representative") || role.equals("Technical Specialist"));
  }

  private UserAccessUpdateModel documentToModel(String userId, String accountId, String role) {
    UserAccessUpdateModel userAccessUpdateModel = new UserAccessUpdateModel();
    userAccessUpdateModel.setUserId(userId);
    userAccessUpdateModel.setAccountId(accountId);
    userAccessUpdateModel.setRoleName(role);
    return userAccessUpdateModel;
  }
}
