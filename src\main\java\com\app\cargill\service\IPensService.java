/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.constants.OptimizationType;
import com.app.cargill.dto.DuplicatePenIdDTO;
import com.app.cargill.dto.PenDto;
import com.app.cargill.dto.PenGroupingDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.PageImpl;

public interface IPensService {

  PageImpl<PenDto> getAllPensByCurrentLoggedInUser(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting);

  PageImpl<PenDto> getAllPensBySiteId(
      String siteId, int page, int size, String sortBy, Instant lastSyncTime, String sorting);

  PenGroupingDto save(PenGroupingDto pensDto) throws NotFoundDEException;

  PenGroupingDto update(PenGroupingDto pensDto) throws NotFoundDEException;

  Double getNetEnergyOfLactationDairy(List<Pens> pens) throws CustomDEExceptions;

  Boolean dietValidation(Diets diet, OptimizationType optimizationType);

  PenDto saveAndFlushPen(Pens pen, Boolean isValidDiet);

  List<DuplicatePenIdDTO> getAllPensDuplicate(int offset, int limit);
}
