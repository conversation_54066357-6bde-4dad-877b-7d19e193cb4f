/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.app.cargill.cosmos.model.SiteMappingCosmos;
import org.junit.jupiter.api.Test;

class DataSourceMappingsMapperTest {
  @Test
  void whenExceptionIsProducedItIsPropagated() {
    SiteMappingCosmos siteMappingCosmos = new SiteMappingCosmos();
    siteMappingCosmos.setId("1234");
    assertThrows(RuntimeException.class, () -> SiteMappingsMapper.map(siteMappingCosmos));
  }
}
