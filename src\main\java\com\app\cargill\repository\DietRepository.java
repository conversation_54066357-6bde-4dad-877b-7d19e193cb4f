/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.document.DietDocument;
import com.app.cargill.model.Diets;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DietRepository extends JpaRepository<Diets, Long> {

  @Query(
      value =
          "SELECT d.* FROM diets d WHERE d.diet_document ->> 'Source' = :dietSource AND"
              + " d.diet_document ->>'LabyrinthAccountId' IN (:accountIdsByUser)  AND"
              + " timezone('UTC',d.updated_date) > :lastSyncTime ",
      nativeQuery = true)
  Page<Diets> findByAccountIdAndDietSourceAndUpdatedDate(
      @Param("accountIdsByUser") List<String> accountIdsByUser,
      @Param("dietSource") String dietSource,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value =
          "SELECT d.* FROM Diets d WHERE d.diet_document ->> 'id' IN (:ids) AND "
              + " d.deleted=false ",
      nativeQuery = true)
  List<Diets> findByIds(@Param("ids") List<String> ids);

  @Query(
      value =
          "SELECT d.* FROM Diets d WHERE d.diet_document ->> 'SiteId' IN (:siteIds) AND "
              + " d.deleted=false ",
      nativeQuery = true)
  List<Diets> findBySiteIds(@Param("siteIds") List<String> siteIds);

  @Query(
      value =
          "SELECT d.* FROM Diets d WHERE d.diet_document ->> 'SiteId' = :siteId AND "
              + " d.deleted=false ",
      nativeQuery = true)
  List<Diets> findBySiteId(@Param("siteId") String siteId);

  @Query(
      value = "SELECT d.* FROM Diets d WHERE d.diet_document ->> 'SiteId' = :siteId ",
      nativeQuery = true)
  List<Diets> findAllBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "SELECT d.diet_document FROM Diets d WHERE d.diet_document ->> 'SiteId' = :siteId AND "
              + " d.deleted=false ",
      nativeQuery = true)
  List<DietDocument> findDietDocumentBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "SELECT d.* FROM Diets d WHERE d.diet_document ->> 'id' = :id AND " + " d.deleted=false ",
      nativeQuery = true)
  Diets findById(@Param("id") String id);
}
