/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.LeadSource;
import com.app.cargill.constants.Level;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CrescendoContactServiceTest {

  @Mock private AccountsRepository accountsRepository;

  @InjectMocks private CrescendoContactService contactService;

  @Test
  void withContactsForSyncCorrectResultIsReturned() {

    when(accountsRepository.getAccountsWithNeedsSyncContactsForCrescendo())
        .thenReturn(List.of(generateRandomAccount()));
    List<ContactCrescendo> result = contactService.getContactsForSync(null);
    assertEquals(3, result.size());
  }

  @Test
  void withContactsFromDateCorrectResultIsReturned() {

    when(accountsRepository.getAccountsByUpdatedDateAfterForCrescendo(any()))
        .thenReturn(List.of(generateRandomAccount()));
    List<ContactCrescendo> result = contactService.getContactsForSync(Instant.now());
    assertEquals(3, result.size());
  }

  @Test
  void whenUpdatedContactsArriveTheyAreUpdated() {
    ContactCrescendo contact_1 = generateRandomContactCrescendoFullData();
    contact_1.setAccountId(UUID.randomUUID().toString());
    ContactCrescendo contact_2 = generateRandomContactCrescendoFullData();
    contact_2.setGoldenRecordAccountId("random-uuid");
    ContactCrescendo contact_3 = generateRandomContactCrescendoFullData();
    contact_3.setGoldenRecordAccountId("random-uuid");
    ContactCrescendo contact_4 = generateRandomContactCrescendoFullData();
    contact_4.setGoldenRecordAccountId("random-uuid");
    ContactCrescendo contact_5 = generateRandomContactCrescendo();

    Accounts accountWithNullContacts = generateRandomAccount();
    accountWithNullContacts.getAccountDocument().setContacts(null);
    Accounts accountWithUpdatedContact = generateRandomAccount();
    Contact contact = generateRandomContactFullData();
    contact.setSFDCContactId(contact_4.getSfdcContactId());
    contact.setContactId(UUID.fromString(contact_4.getExternalId()));
    accountWithUpdatedContact.getAccountDocument().setContacts(List.of(contact));

    when(accountsRepository.save(any())).thenReturn(new Accounts());
    when(accountsRepository.findByAccountId(any()))
        .thenReturn(generateRandomAccount())
        .thenReturn(null);

    when(accountsRepository.findByExternalId(any()))
        .thenReturn(generateRandomAccount())
        .thenReturn(accountWithNullContacts)
        .thenReturn(accountWithUpdatedContact)
        .thenReturn(null);

    CompletableFuture<Integer> completableFuture =
        contactService.updateContacts(
            List.of(contact_1, contact_2, contact_3, contact_4, contact_5));

    Integer result = completableFuture.join();

    assertTrue(completableFuture.isDone());
    // One should fail
    assertEquals(4, result);
    verify(accountsRepository, times(4)).save(any());
  }

  private Accounts generateRandomAccount() {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setGoldenRecordId("123");
    List<Contact> contacts = new ArrayList<>();
    contacts.add(generateRandomContactFullData());
    contacts.add(generateRandomContact());
    contacts.add(generateRandomContact());
    accountDocument.setContacts(contacts);
    return new Accounts(accountDocument);
  }

  private Contact generateRandomContact() {
    Contact contact = new Contact();
    contact.setSFDCContactId(UUID.randomUUID().toString());
    contact.setContactId(UUID.randomUUID());
    contact.setNeedsSync(true);
    return contact;
  }

  private Contact generateRandomContactFullData() {
    Contact contact = new Contact();
    contact.setContactId(UUID.randomUUID());
    contact.setIsNew(false);
    contact.setLevel(Level.Primary);
    contact.setPrimary(true);
    contact.setLastSyncTimeUtc(Instant.now());
    contact.setLeadSource(LeadSource.Partner);
    contact.setWebsite("random.site");
    contact.setNeedsSync(true);
    contact.setSFDCContactId("random-id");
    return contact;
  }

  private ContactCrescendo generateRandomContactCrescendo() {
    ContactCrescendo contactCrescendo = new ContactCrescendo();
    contactCrescendo.setSfdcContactId("random-id");

    return contactCrescendo;
  }

  private ContactCrescendo generateRandomContactCrescendoFullData() {
    ContactCrescendo contactCrescendo = new ContactCrescendo();
    contactCrescendo.setExternalId(UUID.randomUUID().toString());
    contactCrescendo.setSfdcContactId("random-id");

    return contactCrescendo;
  }
}
