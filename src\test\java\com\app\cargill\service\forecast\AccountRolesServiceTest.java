/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.forecast;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Country;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.UserRole;
import com.app.cargill.model.AccountRoles;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AccountRolesServiceTest {

  @Mock private AccountsRepository accountsRepository;

  @InjectMocks private AccountRolesService accountRolesService;

  @Test
  void whenAccountsRolesAreRequestedCorrectDataIsReturned() {
    AccountDocument accountDocument1 = new AccountDocument();
    accountDocument1.setId(UUID.randomUUID());
    accountDocument1.setOwnerId("user-2");
    accountDocument1.setGoldenRecordId("gr-id-1");
    accountDocument1.setUserRoles(List.of(new UserRole("role-type", "user-1", null)));
    accountDocument1.setBusinessID(1);
    Accounts account1 = new Accounts(accountDocument1);

    AccountDocument accountDocument2 = new AccountDocument();
    accountDocument2.setId(UUID.randomUUID());
    accountDocument2.setGoldenRecordId("gr-id-2");
    accountDocument2.setUsers(Set.of("user-1", "user-2"));
    accountDocument2.setBusinessID(2);
    Accounts account2 = new Accounts(accountDocument2);

    AccountDocument accountDocument3 = new AccountDocument();
    accountDocument3.setId(UUID.randomUUID());
    accountDocument3.setGoldenRecordId("gr-id-3");
    accountDocument3.setUsers(Set.of("user-1", "user-2"));
    Accounts account3 = new Accounts(accountDocument3);

    Accounts account4 = new Accounts();
    account4.setDeleted(true);

    when(accountsRepository.findAll()).thenReturn(List.of(account1, account2, account3, account4));
    List<AccountRoles> result = accountRolesService.getAllAccountsRoles();
    assertEquals(3, result.size());

    Optional<AccountRoles> accountRolesOptional1 =
        result.stream().filter(ar -> "gr-id-1".equals(ar.getGoldenRecordId())).findAny();
    assertTrue(accountRolesOptional1.isPresent());
    assertEquals(Country.BRAZIL, accountRolesOptional1.get().getCountry());
    assertEquals(2, accountRolesOptional1.get().getUsers().size());

    Optional<AccountRoles> accountRolesOptional2 =
        result.stream().filter(ar -> "gr-id-2".equals(ar.getGoldenRecordId())).findAny();
    assertTrue(accountRolesOptional2.isPresent());
    assertEquals(Country.CANADA, accountRolesOptional2.get().getCountry());
    assertEquals(2, accountRolesOptional2.get().getUsers().size());

    Optional<AccountRoles> accountRolesOptional3 =
        result.stream().filter(ar -> "gr-id-3".equals(ar.getGoldenRecordId())).findAny();
    assertTrue(accountRolesOptional3.isPresent());
    assertEquals(Country.UNDEFINED, accountRolesOptional3.get().getCountry());
    assertEquals(2, accountRolesOptional3.get().getUsers().size());
  }
}
