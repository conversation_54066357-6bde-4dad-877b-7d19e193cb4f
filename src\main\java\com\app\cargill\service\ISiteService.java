/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.dto.SiteDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Sites;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;

public interface ISiteService {
  Page<SiteDto> getAllSitesByAccountId(
      String accountId, int page, int size, String sortBy, Instant lastSyncTime, String sorting);

  SiteDto save(SiteDto siteDto, Locale locale, ResourceBundleMessageSource source)
      throws NotFoundDEException, AlreadyExistsDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions;

  SiteDto update(SiteDto siteDto, Locale locale, ResourceBundleMessageSource source)
      throws NotFoundDEException, JsonProcessingException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions;

  Page<SiteDto> getAllSitesByCurrentLoggedInUser(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting);

  void updateSiteMappingsMax(Sites site, SiteMappingDocument mapping);

  List<String> getFilteredSiteIds();
}
