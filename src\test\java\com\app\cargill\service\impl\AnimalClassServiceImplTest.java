/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.dto.AnimalClassDto;
import com.app.cargill.service.IUserService;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
@ImportAutoConfiguration(ResourceBundleMessageSource.class)
class AnimalClassServiceImplTest {

  // Do not delete to prevent NPE for UserService
  @Mock private IUserService userServiceImpl;

  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @InjectMocks private AnimalClassServiceImpl animalClassServiceImpl;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
  }

  @Test
  void whenAnimalClassListReturnValidResult() {

    AnimalClassDto animalClasses =
        animalClassServiceImpl.getAnimalClasses(resourceBundleMessageSource);

    assertEquals(10, animalClasses.getLactatingFresh().size());
    assertEquals(10, animalClasses.getLactatingFreshHeifer().size());
    assertEquals(10, animalClasses.getLactatingMilking().size());
    assertEquals(10, animalClasses.getLactatingPasture().size());
    assertEquals(10, animalClasses.getLactatingLowForage().size());
    assertEquals(10, animalClasses.getLactatingAAEfficiency().size());
    assertEquals(10, animalClasses.getDryFarOff().size());
    assertEquals(10, animalClasses.getDryShortDryPeriod().size());
    assertEquals(10, animalClasses.getDryCloseUp().size());
    assertEquals(10, animalClasses.getDryCloseUpHeifer().size());
    assertEquals(10, animalClasses.getHeifer().size());
    assertEquals(10, animalClasses.getCalf().size());
    assertEquals(10, animalClasses.getMaleBull().size());
    assertEquals(10, animalClasses.getMaleSteer().size());
  }

  @Test
  void whenValidAnimalSubClassProvidedReturnId() {
    assertEquals(
        UUID.fromString("00000000-0000-0000-0000-000000000001"),
        animalClassServiceImpl.getAnimalTypeId("Fresh"));
  }

  @Test
  void whenInValidAnimalSubClassProvidedReturnDefaultId() {
    assertEquals(
        UUID.fromString("00000000-0000-0000-0000-000000000000"),
        animalClassServiceImpl.getAnimalTypeId("something"));
  }

  @Test
  void whenNullAnimalSubClassProvidedReturnDefaultId() {
    assertEquals(
        UUID.fromString("00000000-0000-0000-0000-000000000000"),
        animalClassServiceImpl.getAnimalTypeId(null));
  }
}
