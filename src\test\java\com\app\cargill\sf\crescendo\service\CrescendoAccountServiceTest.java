/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import com.app.cargill.sf.crescendo.model.AccountStatusCrescendo;
import com.app.cargill.sf.crescendo.model.AccountTypeCrescendo;
import com.app.cargill.sf.crescendo.model.AddressCrescendo;
import com.app.cargill.sf.crescendo.model.BusinessUnitCrescendo;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import com.app.cargill.sf.crescendo.model.ContactLevelCrescendo;
import com.app.cargill.sf.crescendo.model.CurrencyCrescendo;
import com.app.cargill.sf.crescendo.model.LeadSourceCrescendo;
import com.app.cargill.sf.crescendo.model.NineBoxStepTwoIDCrescendo;
import com.app.cargill.sf.crescendo.model.SegmentStepOneIdCrescendo;
import com.app.cargill.sf.crescendo.model.SubTypeIdCrescendo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CrescendoAccountServiceTest {

  @Mock private AccountsRepository accountsRepository;
  @Mock private SiteMappingSyncService siteMappingSyncService;
  @Mock private SitesRepository sitesRepository;

  @InjectMocks private CrescendoAccountService accountService;

  @Test
  void withAccountsForSyncCorrectResultIsReturned() throws ParseException {

    when(accountsRepository.findAllByAccountDocumentNeedsSyncForCrescendo("true"))
        .thenReturn(
            List.of(
                generateRandomAccountFullData(), generateRandomAccount(), generateRandomAccount()));
    List<AccountCrescendo> result = accountService.getAccountsForSync(null);
    assertEquals(3, result.size());
  }

  @Test
  void whenUpdatedAccountsArriveTheyAreUpdated() throws ParseException {
    AccountCrescendo account_1 = generateRandomAccountCrescendoFullData();
    AccountCrescendo account_2 = generateRandomAccountCrescendo();
    AccountCrescendo account_3 = generateRandomAccountCrescendo();

    when(accountsRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    when(accountsRepository.findByAccountId(any())).thenReturn(generateRandomAccount());
    when(siteMappingSyncService.processMappings(any())).thenAnswer(i -> i.getArgument(0));
    when(sitesRepository.findAllByAccountId(any()))
        .thenReturn(List.of(new Sites(new SiteDocument())));

    CompletableFuture<Integer> completableFuture =
        accountService.updateAccounts(List.of(account_1, account_2, account_3));

    Integer result = completableFuture.join();

    assertTrue(completableFuture.isDone());
    assertEquals(3, result);
    verify(accountsRepository, times(3)).save(any());
  }

  @Test
  void whenThereIsAnErrorTheResultIsCorrect() throws ParseException {
    AccountCrescendo account_1 = generateRandomAccountCrescendo();
    account_1.setId(UUID.randomUUID().toString());
    AccountCrescendo account_2 = generateRandomAccountCrescendo();
    AccountCrescendo account_3 = generateRandomAccountCrescendo();

    when(accountsRepository.save(any()))
        .thenAnswer(i -> i.getArgument(0))
        .thenThrow(new RuntimeException("Custom Error Message"));
    when(accountsRepository.findByAccountId(any())).thenReturn(generateRandomAccount());
    when(siteMappingSyncService.processMappings(any())).thenAnswer(i -> i.getArgument(0));
    when(sitesRepository.findAllByAccountId(any()))
        .thenReturn(List.of(new Sites(new SiteDocument())));

    CompletableFuture<Integer> completableFuture =
        accountService.updateAccounts(List.of(account_1, account_2, account_3));

    Integer result = completableFuture.join();

    assertTrue(completableFuture.isDone());
    assertEquals(1, result);
  }

  @Test
  void whenNewAccountIsFetchedAllRequiredFieldsExist() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<Accounts> dbItems =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_db_account.json"),
            new TypeReference<>() {});
    when(accountsRepository.getAccountsByUpdatedDateAfterForCrescendo(any())).thenReturn(dbItems);
    List<AccountCrescendo> items = accountService.getAccountsForSync(Instant.now());
    assertEquals(1, items.size());

    AccountCrescendo accountCrescendo = items.get(0);
    assertNull(accountCrescendo.getGoldenRecordId());
    assertNotNull(accountCrescendo.getAccountName());
    assertNotNull(accountCrescendo.getAccountType());
    assertNotNull(accountCrescendo.getContacts());
    assertNotNull(accountCrescendo.getUsers());
    assertNotNull(accountCrescendo.getOwnerId());
    assertNotNull(accountCrescendo.getBusinessId().getValue());
    assertNotNull(accountCrescendo.getNineBoxStepTwoID());
    assertNotNull(accountCrescendo.getSegmentStepOneId());
    assertNotNull(accountCrescendo.getPhysicalAddress());
    assertNotNull(accountCrescendo.getAccountCurrency());
    assertNotNull(accountCrescendo.getLstOtherBU());
    assertNotNull(accountCrescendo.getLastModifiedBy());
    assertNotNull(accountCrescendo.getUserRoles());
    assertNotNull(accountCrescendo.getId());
    assertNotNull(accountCrescendo.getCreateUser());
    assertNotNull(accountCrescendo.getLastModifyUser());
    assertNotNull(accountCrescendo.getCreateTimeUtc());
    assertNotNull(accountCrescendo.getLastModifiedTimeUtc());
    assertNotNull(accountCrescendo.getLastSyncTimeUtc());

    assertAccountValues(accountCrescendo);

    assertFalse(accountCrescendo.isNew());
    assertFalse(accountCrescendo.isDeleted());

    assertContact(accountCrescendo.getContacts().get(0));
  }

  private void assertAccountValues(AccountCrescendo accountCrescendo) {
    assertEquals("LM", accountCrescendo.getSourceSystem());
    assertEquals("Farm Producer", accountCrescendo.getSubTypeId().getValue());
    assertEquals(1, accountCrescendo.getUsers().size());
    assertEquals("Standard", accountCrescendo.getAccountType().getValue());
  }

  private void assertContact(ContactCrescendo contactCrescendo) {
    assertNotNull(contactCrescendo.getAccountId());
    assertNotNull(contactCrescendo.getFirstName());
    assertNotNull(contactCrescendo.getLastName());
    assertNotNull(contactCrescendo.getMarketingId());
    assertNotNull(contactCrescendo.getId());
    assertNotNull(contactCrescendo.getCreateTimeUtc());
    assertNotNull(contactCrescendo.getLastModifiedTimeUtc());
    assertNotNull(contactCrescendo.getLastSyncTimeUtc());
    assertFalse(contactCrescendo.getIsNew());
  }

  private Accounts generateRandomAccount() throws ParseException {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setNeedsSync(true);
    accountDocument.setGoldenRecordId("1234");
    return setAccountFields(accountDocument);
  }

  private Accounts generateRandomAccountFullData() throws ParseException {
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(UUID.randomUUID());
    accountDocument.setSegmentStepOneId("Kobe");
    accountDocument.setNeedsSync(true);
    Address address = new Address();
    accountDocument.setPhysicalAddress(address);
    Contact contact = new Contact();
    contact.setContactId(UUID.randomUUID());
    accountDocument.setContacts(List.of(contact));
    ExternalDataSource externalDataSource = new ExternalDataSource();
    externalDataSource.setAccountId(accountDocument.getId().toString());
    externalDataSource.setSystem("LM");
    externalDataSource.setUniqueExternalKey(accountDocument.getId().toString());
    accountDocument.setApplicationMappings(List.of(externalDataSource));
    return setAccountFields(accountDocument);
  }

  private AccountCrescendo generateRandomAccountCrescendo() {
    AccountCrescendo accountCrescendo = new AccountCrescendo();
    accountCrescendo.setGoldenRecordId("1234");
    return accountCrescendo;
  }

  private Accounts setAccountFields(AccountDocument accountDocument) throws ParseException {
    Accounts account = new Accounts(accountDocument);

    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");

    account.setId(8066L);
    account.setLocalId(UUID.randomUUID().toString());
    account.setDeleted(false);

    account.setCreatedDate(formatter.parse("2023-06-07 08:39:56.983"));
    account.setUpdatedDate(formatter.parse("2023-06-07 08:42:57.287"));
    return account;
  }

  private AccountCrescendo generateRandomAccountCrescendoFullData() {
    AccountCrescendo accountCrescendo = new AccountCrescendo();
    accountCrescendo.setId(UUID.randomUUID().toString());
    accountCrescendo.setAccountType(AccountTypeCrescendo.CONSUMER);
    accountCrescendo.setSegmentStepOneId(SegmentStepOneIdCrescendo.KOBE);
    accountCrescendo.setNineBoxStepTwoID(NineBoxStepTwoIDCrescendo.LARGE_SELECTED);
    accountCrescendo.setSubTypeId(SubTypeIdCrescendo.BARN);
    accountCrescendo.setBusinessId(BusinessUnitCrescendo.BRAZIL);
    accountCrescendo.setExternalParentAccountId(UUID.randomUUID().toString());
    accountCrescendo.setAccountCurrency(CurrencyCrescendo.BGL);
    accountCrescendo.setAccountStatus(AccountStatusCrescendo.COMPLETE);
    accountCrescendo.setUsers(Set.of("User-X"));
    accountCrescendo.setPhysicalAddress(new AddressCrescendo());
    accountCrescendo.setGoldenRecordId("1234");

    ContactCrescendo contactCrescendo = new ContactCrescendo();
    contactCrescendo.setMailingAddress(new AddressCrescendo());
    contactCrescendo.setOtherAddress(new AddressCrescendo());
    contactCrescendo.setBusinessId(BusinessUnitCrescendo.BRAZIL);
    contactCrescendo.setReportsToID(UUID.randomUUID().toString());
    contactCrescendo.setExternalId(UUID.randomUUID().toString());
    contactCrescendo.setLeadSource(LeadSourceCrescendo.PARTNER);
    contactCrescendo.setLevel(ContactLevelCrescendo.PRIMARY);
    contactCrescendo.setLastSyncTimeUtc(Instant.now());
    contactCrescendo.setAccountId(accountCrescendo.getId());

    accountCrescendo.setContacts(List.of(contactCrescendo));

    return accountCrescendo;
  }
}
