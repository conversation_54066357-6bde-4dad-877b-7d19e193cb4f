/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.BeddingQuality;
import com.app.cargill.constants.Breed;
import com.app.cargill.constants.ProductionSystem;
import com.app.cargill.constants.ProfitabilityAnalysisQuality;
import com.app.cargill.constants.WaterQuality;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Inputs implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Boolean energyIce;
  private Boolean monensin;
  private Boolean soyPassBr;
  private Double concentrateTotalConsumed;
  private ProfitabilityAnalysisQuality silage;
  private ProfitabilityAnalysisQuality haylage;
  private ProductionSystem productionSystem;
  private String numberOfMilkings;
  private Double totalProductionHerd;

  private Boolean commercialConcentrateToggle;
  private Boolean mineralBaseMix;
  private Double mineralBaseMixValue;
  private Double dim;
  private Boolean nutritek;
  private Boolean xpcUltra;
  private Boolean actiforBoost;
  private Boolean buffer;
  private Boolean nutrigorduraLac;
  private Boolean ice;
  private Double temperatureInC;
  private Double airRuPercentage;
  private Double thi;
  private Double respiratoryMovement;
  private Double cowLayingDownPercentage;
  private Double totalDietCost;
  private ProfitabilityAnalysisQuality hay;
  private ProfitabilityAnalysisQuality pasture;
  private WaterQuality waterQuality;
  private BeddingQuality beddingQuality;
  private Boolean ventilation;
  private Boolean sprinkler;
  private Double animalsInHerd;
  private Double totalNumberOfCows;
  private Double totalNumberOfLactatingAnimals;
  private Double milkPrice;
  private Double milkFatPercentage;
  private Double milkProteinPercentage;
  private Double somanticCellCount;
  private Double bacteriaCellCount;
  private Double commercialConcentrate;
  private Breed breed;
  private Double mun;
}
