/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Segments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SegmentsRepository extends JpaRepository<Segments, Long> {

  @Query(
      value =
          "Select s.segment_document->>'value' FROM Segments s where s.segment_document->>'key' ="
              + " :key",
      nativeQuery = true)
  String findValueByKey(@Param("key") String key);

  @Query(
      value =
          "Select s.segment_document->>'value' FROM Segments s where s.segment_document->>'default'"
              + " = 'true'",
      nativeQuery = true)
  String findValueByDefaultValue();
}
