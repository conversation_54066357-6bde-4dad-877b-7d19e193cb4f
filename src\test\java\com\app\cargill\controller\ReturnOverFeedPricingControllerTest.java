/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.app.cargill.constants.ReturnOverFeedType;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.ReturnOverFeedPricePerTonDto;
import com.app.cargill.service.IReturnOverFeedPricingService;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class ReturnOverFeedPricingControllerTest {

  @Mock private IReturnOverFeedPricingService returnOverFeedPricingService;

  @InjectMocks private ReturnOverFeedPricingController controller;

  private ReturnOverFeedPricePerTonDto dto;

  @BeforeEach
  void setUp() {
    dto =
        ReturnOverFeedPricePerTonDto.builder()
            .id(UUID.randomUUID())
            .name("Corn")
            .price(1200.0)
            .returnOverFeedType(ReturnOverFeedType.HOME_GROWN_GRAINS)
            .build();
  }

  @Test
  void testGetPricePerTonWithType() {
    when(returnOverFeedPricingService.getPricePerTon(ReturnOverFeedType.HOME_GROWN_GRAINS))
        .thenReturn(List.of(dto));

    ResponseEntity<ResponseEntityDto<List<ReturnOverFeedPricePerTonDto>>> result =
        controller.getPricePerTon(ReturnOverFeedType.HOME_GROWN_GRAINS);

    assertNotNull(result);
    assertEquals(200, result.getStatusCodeValue());
    assertNotNull(result.getBody());
    assertEquals(1, result.getBody().getData().size());
    assertEquals("Corn", result.getBody().getData().get(0).getName());
  }

  @Test
  void testUpdatePricePerTon() {
    when(returnOverFeedPricingService.update(any())).thenReturn(dto);

    ResponseEntity<ResponseEntityDto<ReturnOverFeedPricePerTonDto>> result =
        controller.getPricePerTon(dto);

    assertNotNull(result);
    assertEquals(200, result.getStatusCodeValue());
    assertNotNull(result.getBody());
    assertEquals("Corn", Objects.requireNonNull(result.getBody().getData()).getName());
    verify(returnOverFeedPricingService, times(1)).update(any());
  }
}
