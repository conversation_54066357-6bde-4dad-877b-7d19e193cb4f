/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.constants.Business;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "user_daily_login")
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class UserDailyLogin extends BaseEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @Column(length = 100)
  @NotNull
  @Email
  private String email;

  @NotNull
  @Column(name = "Name")
  private String name;

  @NotNull
  @Column(name = "Country")
  private Business countryId;

  @Column(name = "lastLoginDateTime")
  private Instant lastLoginDateTime;

  @Column(name = "count")
  private Integer count;

  @Column(name = "datapoint")
  private Integer datapoint;
}
