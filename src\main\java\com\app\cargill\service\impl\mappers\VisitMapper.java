/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.document.AnimalAnalysisTool;
import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.CalfHeiferScorecard;
import com.app.cargill.document.ForagePennStateTool;
import com.app.cargill.document.HeatStressTool;
import com.app.cargill.document.LocomotionTool;
import com.app.cargill.document.ManureScreenerTool;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.PenTimeBudgetTool;
import com.app.cargill.document.PileAndBunkerTool;
import com.app.cargill.document.ProfitabilityAnalysisTool;
import com.app.cargill.document.ReturnOverFeedTool;
import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RumenFillTool;
import com.app.cargill.document.RumenHealthManureScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreTool;
import com.app.cargill.document.RumenHealthTMRParticleScoreToolItem;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.Scorecard;
import com.app.cargill.dto.AnimalAnalysisToolDto;
import com.app.cargill.dto.BodyConditionToolDto;
import com.app.cargill.dto.CalfHeiferScorecardDto;
import com.app.cargill.dto.ForagePennStateToolDto;
import com.app.cargill.dto.HeatStressToolDto;
import com.app.cargill.dto.LocomotionToolDto;
import com.app.cargill.dto.ManureScreenerToolDto;
import com.app.cargill.dto.MetabolicIncidenceToolDto;
import com.app.cargill.dto.MilkSoldEvaluationToolDto;
import com.app.cargill.dto.PenTimeBudgetToolDto;
import com.app.cargill.dto.PileAndBunkerToolDto;
import com.app.cargill.dto.ProfitabilityAnalysisToolDto;
import com.app.cargill.dto.ReturnOverFeedToolDto;
import com.app.cargill.dto.RoboticMilkEvaluationToolDto;
import com.app.cargill.dto.RumenFillToolDto;
import com.app.cargill.dto.RumenHealthManureScoreToolDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreToolDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreToolItemDto;
import com.app.cargill.dto.RumenHealthToolDto;
import com.app.cargill.dto.ScorecardDto;
import com.app.cargill.model.Visits;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class VisitMapper {
  private final ModelMapper modelMapper;
  private final ObjectMapper objectMapper;
  private int index;

  public BodyConditionToolDto modelToDtoForBCS(BodyConditionTool bodyConditionTool) {
    if (Objects.isNull(bodyConditionTool)) {
      return null;
    }
    return modelMapper.map(bodyConditionTool, BodyConditionToolDto.class);
  }

  public BodyConditionTool dtoToModelForBCS(BodyConditionToolDto dto) {

    if (Objects.isNull(dto)) {
      return null;
    }
    return modelMapper.map(dto, BodyConditionTool.class);
  }

  public LocomotionToolDto modelToDtoForLocomotion(LocomotionTool locomotionScore) {

    if (Objects.isNull(locomotionScore)) {
      return null;
    }
    return modelMapper.map(locomotionScore, LocomotionToolDto.class);
  }

  public LocomotionTool dtoToModelForLocomotion(LocomotionToolDto locomotionScore) {

    if (Objects.isNull(locomotionScore)) {
      return null;
    }
    return modelMapper.map(locomotionScore, LocomotionTool.class);
  }

  public RumenHealthToolDto modelToDtoForRumenHealth(RumenHealthTool rumenHealth) {
    if (Objects.isNull(rumenHealth)) {
      return null;
    }

    ModelMapper rumenHealthModelMapper = modelMapper;
    rumenHealthModelMapper.getConfiguration().setAmbiguityIgnored(true);
    return rumenHealthModelMapper.map(rumenHealth, RumenHealthToolDto.class);
  }

  public ReturnOverFeedTool dtoToModelForReturnOverFeed(ReturnOverFeedToolDto returnOverFeedTool) {
    if (Objects.isNull(returnOverFeedTool)) {
      return null;
    }

    return modelMapper.map(returnOverFeedTool, ReturnOverFeedTool.class);
  }

  public RumenFillToolDto modelToDtoForRumenFillManureScore(RumenFillTool rumenFillManureScore) {
    if (Objects.isNull(rumenFillManureScore)) {
      return null;
    }

    return modelMapper.map(rumenFillManureScore, RumenFillToolDto.class);
  }

  public RumenHealthTool dtoToModelForRumenHealth(RumenHealthToolDto rumenHealth) {
    if (Objects.isNull(rumenHealth)) {
      return null;
    }

    ModelMapper rumenHealthModelMapper = modelMapper;
    rumenHealthModelMapper.getConfiguration().setAmbiguityIgnored(true);
    return rumenHealthModelMapper.map(rumenHealth, RumenHealthTool.class);
  }

  public AnimalAnalysisTool dtoToModelForAnimalAnalysis(AnimalAnalysisToolDto animalAnalysis) {
    if (Objects.isNull(animalAnalysis)) {
      return null;
    }
    return modelMapper.map(animalAnalysis, AnimalAnalysisTool.class);
  }

  public ProfitabilityAnalysisTool dtoToModelForProfitabilityAnalysis(
      ProfitabilityAnalysisToolDto profitabilityAnalysisToolDto) {
    if (Objects.isNull(profitabilityAnalysisToolDto)) {
      return null;
    }
    return modelMapper.map(profitabilityAnalysisToolDto, ProfitabilityAnalysisTool.class);
  }

  public CalfHeiferScorecard dtoToModelForCalfHeiferScorecard(
      CalfHeiferScorecardDto calfHeiferScorecardDto) {
    if (Objects.isNull(calfHeiferScorecardDto)) {
      return null;
    }
    return modelMapper.map(calfHeiferScorecardDto, CalfHeiferScorecard.class);
  }

  public AnimalAnalysisToolDto modelToDtoForAnimalAnalysis(AnimalAnalysisTool animalAnalysis) {

    if (Objects.isNull(animalAnalysis)) {
      return null;
    }
    return modelMapper.map(animalAnalysis, AnimalAnalysisToolDto.class);
  }

  public MetabolicIncidenceToolDto modelToDtoForMetabolicIncidence(
      MetabolicIncidenceTool metabolicIncidence) {
    if (Objects.isNull(metabolicIncidence)) {
      return null;
    }
    return modelMapper.map(metabolicIncidence, MetabolicIncidenceToolDto.class);
  }

  public RoboticMilkEvaluationToolDto modelToDtoForRoboticMilkEvaluation(
      RoboticMilkEvaluationTool roboticMilkEvaluation) {
    if (Objects.isNull(roboticMilkEvaluation)) {
      return null;
    }
    return modelMapper.map(roboticMilkEvaluation, RoboticMilkEvaluationToolDto.class);
  }

  public MilkSoldEvaluationTool dtoToModelForMilkSoldEvaluation(
      MilkSoldEvaluationToolDto milkSoldEvaluationToolDto) {
    if (Objects.isNull(milkSoldEvaluationToolDto)) {
      return null;
    }
    return modelMapper.map(milkSoldEvaluationToolDto, MilkSoldEvaluationTool.class);
  }

  public RumenFillTool dtoToModelForRumenFillManureScore(RumenFillToolDto rumenFillManureScoreDto) {
    if (Objects.isNull(rumenFillManureScoreDto)) {
      return null;
    }
    return modelMapper.map(rumenFillManureScoreDto, RumenFillTool.class);
  }

  public RoboticMilkEvaluationTool dtoToModelForRoboticMilkEvaluation(
      RoboticMilkEvaluationToolDto roboticMilkEvaluationDto) {
    if (Objects.isNull(roboticMilkEvaluationDto)) {
      return null;
    }
    return modelMapper.map(roboticMilkEvaluationDto, RoboticMilkEvaluationTool.class);
  }

  public RumenHealthManureScoreTool dtoToModelForRumenHealthManureScore(
      RumenHealthManureScoreToolDto rumenHealthManureScore) {
    if (Objects.isNull(rumenHealthManureScore)) {
      return null;
    }
    return modelMapper.map(rumenHealthManureScore, RumenHealthManureScoreTool.class);
  }

  public HeatStressTool dtoToModelForHeatStress(HeatStressToolDto heatStressDto) {
    if (Objects.isNull(heatStressDto)) {
      return null;
    }
    return modelMapper.map(heatStressDto, HeatStressTool.class);
  }

  public PenTimeBudgetTool dtoToModelForPenTimeBudget(PenTimeBudgetToolDto penTimeBudgetToolDto) {
    if (Objects.isNull(penTimeBudgetToolDto)) {
      return null;
    }
    return modelMapper.map(penTimeBudgetToolDto, PenTimeBudgetTool.class);
  }

  public MetabolicIncidenceTool dtoToModelForMetabolicIncidence(
      MetabolicIncidenceToolDto metabolicIncidenceDto) {
    if (Objects.isNull(metabolicIncidenceDto)) {
      return null;
    }
    return modelMapper.map(metabolicIncidenceDto, MetabolicIncidenceTool.class);
  }

  public RumenHealthTMRParticleScoreTool dtoToModelForTmrParticleScore(
      RumenHealthTMRParticleScoreToolDto tmrParticleScoreDto) {
    if (Objects.isNull(tmrParticleScoreDto)) {
      return null;
    }
    RumenHealthTMRParticleScoreTool model =
        modelMapper.map(tmrParticleScoreDto, RumenHealthTMRParticleScoreTool.class);

    // Map TMR scores to Model and set tmrScoreIndex
    HashMap<UUID, Integer> penIdsMap = new HashMap<>();
    List<RumenHealthTMRParticleScoreToolItem> tmrScoresModels =
        tmrParticleScoreDto.getTmrScores().stream()
            .map(
                tmrScores -> {
                  if (!penIdsMap.containsKey(tmrScores.getPenId())) {
                    penIdsMap.put(tmrScores.getPenId(), 1);
                  } else {
                    penIdsMap.put(tmrScores.getPenId(), penIdsMap.get(tmrScores.getPenId()) + 1);
                  }
                  RumenHealthTMRParticleScoreToolItem tmrScoresModel =
                      modelMapper.map(tmrScores, RumenHealthTMRParticleScoreToolItem.class);
                  tmrScoresModel.setTmrScoreIndex(
                      Integer.toString(penIdsMap.get(tmrScores.getPenId())));
                  return tmrScoresModel;
                })
            .toList();

    model.setTmrScores(tmrScoresModels); // Set the TMR scores in Model

    return model;
  }

  public ForagePennStateTool dtoToModelForForagePennState(
      ForagePennStateToolDto foragePennStateDto) {
    if (Objects.isNull(foragePennStateDto)) {
      return null;
    }
    return modelMapper.map(foragePennStateDto, ForagePennStateTool.class);
  }

  public PileAndBunkerTool dtoToModelForPileAndBunker(PileAndBunkerToolDto pileAndBunkerDto) {
    if (Objects.isNull(pileAndBunkerDto)) {
      return null;
    }
    return modelMapper.map(pileAndBunkerDto, PileAndBunkerTool.class);
  }

  public ManureScreenerTool dtoToModelForManureScreenerTool(
      ManureScreenerToolDto manureScreenerTool) {
    if (Objects.isNull(manureScreenerTool)) {
      return null;
    }
    return modelMapper.map(manureScreenerTool, ManureScreenerTool.class);
  }

  public Scorecard dtoToModelForForageAuditScorecard(ScorecardDto forageAuditScorecard) {
    if (Objects.isNull(forageAuditScorecard)) {
      return null;
    }
    return modelMapper.map(forageAuditScorecard, Scorecard.class);
  }

  public RumenHealthManureScoreToolDto modeltoDtoForRumenHealthManureScore(
      RumenHealthManureScoreTool rumenHealthManureScore) {
    if (Objects.isNull(rumenHealthManureScore)) {
      return null;
    }
    return modelMapper.map(rumenHealthManureScore, RumenHealthManureScoreToolDto.class);
  }

  public CalfHeiferScorecardDto modelToDtoForCalfHeiferScorecard(
      CalfHeiferScorecard calfHeiferScorecard) {
    if (Objects.isNull(calfHeiferScorecard)) {
      return null;
    }
    return modelMapper.map(calfHeiferScorecard, CalfHeiferScorecardDto.class);
  }

  public HeatStressToolDto modelToDtoForHeatStress(HeatStressTool heatStress) {
    if (Objects.isNull(heatStress)) {
      return null;
    }
    return modelMapper.map(heatStress, HeatStressToolDto.class);
  }

  public ProfitabilityAnalysisToolDto modelToDtoForProfitabilityAnalysis(
      ProfitabilityAnalysisTool profitabilityAnalysis) {
    if (Objects.isNull(profitabilityAnalysis)) {
      return null;
    }
    return modelMapper.map(profitabilityAnalysis, ProfitabilityAnalysisToolDto.class);
  }

  public ManureScreenerToolDto modelToDtoForManureScreenerTool(
      ManureScreenerTool manureScreenerTool) {
    if (Objects.isNull(manureScreenerTool)) {
      return null;
    }
    return modelMapper.map(manureScreenerTool, ManureScreenerToolDto.class);
  }

  public PenTimeBudgetToolDto modelToDtoForPenTimeBudget(PenTimeBudgetTool penTimeBudgetTool) {
    if (Objects.isNull(penTimeBudgetTool)) {
      return null;
    }
    return modelMapper.map(penTimeBudgetTool, PenTimeBudgetToolDto.class);
  }

  public RumenHealthTMRParticleScoreToolDto modelToDtoForTmrParticleScore(
      RumenHealthTMRParticleScoreTool tmrParticleScore) {
    if (Objects.isNull(tmrParticleScore)) {
      return null;
    }
    RumenHealthTMRParticleScoreToolDto dto =
        modelMapper.map(tmrParticleScore, RumenHealthTMRParticleScoreToolDto.class);

    // Map TMR scores to DTOs and set tmrScoreId
    List<RumenHealthTMRParticleScoreToolItemDto> tmrScoresDtos =
        tmrParticleScore.getTmrScores().stream()
            .map(
                tmrScores -> {
                  RumenHealthTMRParticleScoreToolItemDto tmrScoresDto =
                      modelMapper.map(tmrScores, RumenHealthTMRParticleScoreToolItemDto.class);
                  tmrScoresDto.setTmrScoreId(tmrScores.getTmrScoreIndex());
                  return tmrScoresDto;
                })
            .toList();

    dto.setTmrScores(tmrScoresDtos); // Set the TMR scores in dto

    return dto;
  }

  public ScorecardDto modelToDtoForForageAuditScorecard(Scorecard forageAuditScorecard) {
    if (Objects.isNull(forageAuditScorecard)) {
      return null;
    }
    return modelMapper.map(forageAuditScorecard, ScorecardDto.class);
  }

  public ReturnOverFeedToolDto modelToDtoForReturnOverFeed(ReturnOverFeedTool returnOverFeedTool) {
    if (Objects.isNull(returnOverFeedTool)) {
      return null;
    }
    return modelMapper.map(returnOverFeedTool, ReturnOverFeedToolDto.class);
  }

  public ForagePennStateToolDto modelToDtoForForagePennState(ForagePennStateTool foragePennState) {
    if (Objects.isNull(foragePennState)) {
      return null;
    }
    return modelMapper.map(foragePennState, ForagePennStateToolDto.class);
  }

  public MilkSoldEvaluationToolDto modelToDtoForMilkSoldEvaluation(
      MilkSoldEvaluationTool milkSoldEvaluationTool) {

    if (Objects.isNull(milkSoldEvaluationTool)) {
      return null;
    }
    return modelMapper.map(milkSoldEvaluationTool, MilkSoldEvaluationToolDto.class);
  }

  public PileAndBunkerToolDto modelToDtoForPileAndBunker(PileAndBunkerTool pileAndBunker) {
    if (Objects.isNull(pileAndBunker)) {
      return null;
    }
    return modelMapper.map(pileAndBunker, PileAndBunkerToolDto.class);
  }

  public void updateDefaultAttributesForMilkSoldEvaluation(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    MilkSoldEvaluationTool milkSoldEvaluation = visit.getVisitDocument().getMilkSoldEvaluation();
    if (milkSoldEvaluation != null) {
      if (!isUpdate) {
        if (milkSoldEvaluation.getId() == null) {
          milkSoldEvaluation.setId(UUID.randomUUID());
        }
        milkSoldEvaluation.setVisitId(visit.getVisitDocument().getId());

        milkSoldEvaluation.setCreateUser(createdBy);
        milkSoldEvaluation.setCreateTimeUtc(Instant.now());
        milkSoldEvaluation.setLastModifiedTimeUtc(Instant.now());
        milkSoldEvaluation.setLastSyncTimeUtc(Instant.now());
        milkSoldEvaluation.setLastModifyUser(createdBy);
      } else {
        milkSoldEvaluation.setLastModifiedTimeUtc(Instant.now());
        milkSoldEvaluation.setLastModifyUser(updatedBy);
        milkSoldEvaluation.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setMilkSoldEvaluation(milkSoldEvaluation);
    }
  }

  public void updateDefaultAttributesForRumenHealthCudChewing(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {

    RumenHealthTool rumenHealth = visit.getVisitDocument().getRumenHealth();
    if (rumenHealth != null) {
      if (!isUpdate) {
        if (rumenHealth.getId() == null) {
          rumenHealth.setId(UUID.randomUUID());
        }
        rumenHealth.setVisitId(visit.getVisitDocument().getId());

        rumenHealth.setCreateUser(createdBy);
        rumenHealth.setCreateTimeUtc(Instant.now());
        rumenHealth.setLastModifiedTimeUtc(Instant.now());
        rumenHealth.setLastSyncTimeUtc(Instant.now());
        rumenHealth.setLastModifyUser(createdBy);
      } else {
        rumenHealth.setLastModifiedTimeUtc(Instant.now());
        rumenHealth.setLastModifyUser(updatedBy);
        rumenHealth.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setRumenHealth(rumenHealth);
    }
  }

  public void updateDefaultAttributesForBCS(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    BodyConditionTool bodyConditionScore = visit.getVisitDocument().getBodyCondition();
    if (bodyConditionScore != null) {
      if (!isUpdate) {
        if (bodyConditionScore.getId() == null) {
          bodyConditionScore.setId(UUID.randomUUID());
        }
        bodyConditionScore.setVisitId(visit.getVisitDocument().getId());

        bodyConditionScore.setCreateUser(createdBy);
        bodyConditionScore.setCreateTimeUtc(Instant.now());
        bodyConditionScore.setLastModifiedTimeUtc(Instant.now());
        bodyConditionScore.setLastSyncTimeUtc(Instant.now());
        bodyConditionScore.setLastModifyUser(createdBy);
      } else {
        bodyConditionScore.setLastModifiedTimeUtc(Instant.now());
        bodyConditionScore.setLastModifyUser(updatedBy);
        bodyConditionScore.setLastSyncTimeUtc(Instant.now());
      }

      visit.getVisitDocument().setBodyCondition(bodyConditionScore);
    }
  }

  public void updateDefaultAttributesForLocomotionScore(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    LocomotionTool locomotionScore = visit.getVisitDocument().getLocomotionScore();
    if (locomotionScore != null) {
      if (!isUpdate) {
        if (locomotionScore.getId() == null) {
          locomotionScore.setId(UUID.randomUUID());
        }
        locomotionScore.setVisitId(visit.getVisitDocument().getId());

        locomotionScore.setCreateUser(createdBy);
        locomotionScore.setCreateTimeUtc(Instant.now());
        locomotionScore.setLastModifiedTimeUtc(Instant.now());
        locomotionScore.setLastSyncTimeUtc(Instant.now());
        locomotionScore.setLastModifyUser(createdBy);
      } else {
        locomotionScore.setLastModifiedTimeUtc(Instant.now());
        locomotionScore.setLastModifyUser(updatedBy);
        locomotionScore.setLastSyncTimeUtc(Instant.now());
      }

      visit.getVisitDocument().setLocomotionScore(locomotionScore);
    }
  }

  public void updateDefaultAttributesForAnimalAnalysis(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    AnimalAnalysisTool animalAnalysis = visit.getVisitDocument().getAnimalAnalysis();
    if (animalAnalysis != null) {
      if (!isUpdate) {

        if (animalAnalysis.getId() == null) {
          animalAnalysis.setId(UUID.randomUUID());
        }

        animalAnalysis.setCreateUser(createdBy);
        animalAnalysis.setCreateTimeUtc(Instant.now());
        animalAnalysis.setLastModifiedTimeUtc(Instant.now());
        animalAnalysis.setLastSyncTimeUtc(Instant.now());
        animalAnalysis.setLastModifyUser(createdBy);

      } else {

        animalAnalysis.setLastModifiedTimeUtc(Instant.now());
        animalAnalysis.setLastModifyUser(updatedBy);
        animalAnalysis.setLastSyncTimeUtc(Instant.now());
      }
    }
  }

  public void updateDefaultAttributesForMetabolicIncidence(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    MetabolicIncidenceTool metabolicIncidenceTool =
        visit.getVisitDocument().getMetabolicIncidence();
    if (metabolicIncidenceTool != null) {
      if (!isUpdate) {
        if (metabolicIncidenceTool.getId() == null) {
          metabolicIncidenceTool.setId(UUID.randomUUID());
        }
        metabolicIncidenceTool.setVisitId(visit.getVisitDocument().getId());

        metabolicIncidenceTool.setCreateUser(createdBy);
        metabolicIncidenceTool.setCreateTimeUtc(Instant.now());
        metabolicIncidenceTool.setLastModifiedTimeUtc(Instant.now());
        metabolicIncidenceTool.setLastSyncTimeUtc(Instant.now());
        metabolicIncidenceTool.setLastModifyUser(createdBy);
      } else {
        metabolicIncidenceTool.setLastModifiedTimeUtc(Instant.now());
        metabolicIncidenceTool.setLastModifyUser(updatedBy);
        metabolicIncidenceTool.setLastSyncTimeUtc(Instant.now());
      }

      visit.getVisitDocument().setMetabolicIncidence(metabolicIncidenceTool);
    }
  }

  public void updateDefaultAttributesForRoboticMilkEvaluation(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    RoboticMilkEvaluationTool roboticMilkEvaluation =
        visit.getVisitDocument().getRoboticMilkEvaluation();
    if (roboticMilkEvaluation != null) {
      if (!isUpdate) {
        if (roboticMilkEvaluation.getId() == null) {
          roboticMilkEvaluation.setId(UUID.randomUUID());
        }
        roboticMilkEvaluation.setVisitId(visit.getVisitDocument().getId());

        roboticMilkEvaluation.setCreateUser(createdBy);
        roboticMilkEvaluation.setCreateTimeUtc(Instant.now());
        roboticMilkEvaluation.setLastModifiedTimeUtc(Instant.now());
        roboticMilkEvaluation.setLastSyncTimeUtc(Instant.now());
        roboticMilkEvaluation.setLastModifyUser(createdBy);
      } else {
        roboticMilkEvaluation.setLastModifiedTimeUtc(Instant.now());
        roboticMilkEvaluation.setLastModifyUser(updatedBy);
        roboticMilkEvaluation.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setRoboticMilkEvaluation(roboticMilkEvaluation);
    }
  }

  public void updateDefaultAttributesForRumenHealthManureScore(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {

    RumenHealthManureScoreTool rumenHealthManureScore =
        visit.getVisitDocument().getRumenHealthManureScore();
    if (rumenHealthManureScore != null) {
      if (!isUpdate) {
        if (rumenHealthManureScore.getId() == null) {
          rumenHealthManureScore.setId(UUID.randomUUID());
        }
        rumenHealthManureScore.setVisitId(visit.getVisitDocument().getId());

        rumenHealthManureScore.setCreateUser(createdBy);
        rumenHealthManureScore.setCreateTimeUtc(Instant.now());
        rumenHealthManureScore.setLastModifiedTimeUtc(Instant.now());
        rumenHealthManureScore.setLastSyncTimeUtc(Instant.now());
        rumenHealthManureScore.setLastModifyUser(createdBy);
      } else {
        rumenHealthManureScore.setLastModifiedTimeUtc(Instant.now());
        rumenHealthManureScore.setLastModifyUser(updatedBy);
        rumenHealthManureScore.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setRumenHealthManureScore(rumenHealthManureScore);
    }
  }

  public void updateDefaultAttributesForPileAndBunker(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    PileAndBunkerTool pileAndBunkerTool = visit.getVisitDocument().getPileAndBunker();
    if (pileAndBunkerTool != null) {
      if (!isUpdate) {
        if (pileAndBunkerTool.getId() == null) {
          pileAndBunkerTool.setId(UUID.randomUUID());
        }
        pileAndBunkerTool.setVisitId(visit.getVisitDocument().getId());

        pileAndBunkerTool.setCreateUser(createdBy);
        pileAndBunkerTool.setCreateTimeUtc(Instant.now());
        pileAndBunkerTool.setLastModifiedTimeUtc(Instant.now());
        pileAndBunkerTool.setLastSyncTimeUtc(Instant.now());
        pileAndBunkerTool.setLastModifyUser(createdBy);

      } else {
        pileAndBunkerTool.setLastModifiedTimeUtc(Instant.now());
        pileAndBunkerTool.setLastModifyUser(updatedBy);
        pileAndBunkerTool.setLastSyncTimeUtc(Instant.now());
      }
      setDefaultValuesForPileAndBunkerList(pileAndBunkerTool);
      visit.getVisitDocument().setPileAndBunker(pileAndBunkerTool);
    }
  }

  public void updateDefaultAttributesForRumenFillManureScore(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    RumenFillTool rumenFillTool = visit.getVisitDocument().getRumenFillManureScore();
    if (rumenFillTool != null) {
      if (!isUpdate) {
        if (rumenFillTool.getId() == null) {
          rumenFillTool.setId(UUID.randomUUID());
        }
        rumenFillTool.setVisitId(visit.getVisitDocument().getId());

        rumenFillTool.setCreateUser(createdBy);
        rumenFillTool.setCreateTimeUtc(Instant.now());
        rumenFillTool.setLastModifiedTimeUtc(Instant.now());
        rumenFillTool.setLastSyncTimeUtc(Instant.now());
        rumenFillTool.setLastModifyUser(createdBy);
      } else {
        rumenFillTool.setLastModifiedTimeUtc(Instant.now());
        rumenFillTool.setLastModifyUser(updatedBy);
        rumenFillTool.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setRumenFillManureScore(rumenFillTool);
    }
  }

  public void updateDefaultAttributesForForagePennState(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    ForagePennStateTool foragePennStateTool = visit.getVisitDocument().getForagePennState();
    if (foragePennStateTool != null) {
      if (!isUpdate) {
        if (foragePennStateTool.getId() == null) {
          foragePennStateTool.setId(UUID.randomUUID());
        }
        foragePennStateTool.setVisitId(visit.getVisitDocument().getId());

        foragePennStateTool.setCreateUser(createdBy);
        foragePennStateTool.setCreateTimeUtc(Instant.now());
        foragePennStateTool.setLastModifiedTimeUtc(Instant.now());
        foragePennStateTool.setLastSyncTimeUtc(Instant.now());
        foragePennStateTool.setLastModifyUser(createdBy);
      } else {
        foragePennStateTool.setLastModifiedTimeUtc(Instant.now());
        foragePennStateTool.setLastModifyUser(updatedBy);
        foragePennStateTool.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setForagePennState(foragePennStateTool);
    }
  }

  public void updateDefaultAttributesForTmrParticleScore(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    RumenHealthTMRParticleScoreTool rumenHealthTmrParticleScoreTool =
        visit.getVisitDocument().getTmrParticleScore();
    if (rumenHealthTmrParticleScoreTool != null) {
      if (!isUpdate) {
        if (rumenHealthTmrParticleScoreTool.getId() == null) {
          rumenHealthTmrParticleScoreTool.setId(UUID.randomUUID());
        }
        rumenHealthTmrParticleScoreTool.setVisitId(visit.getVisitDocument().getId());

        rumenHealthTmrParticleScoreTool.setCreateUser(createdBy);
        rumenHealthTmrParticleScoreTool.setCreateTimeUtc(Instant.now());
        rumenHealthTmrParticleScoreTool.setLastModifiedTimeUtc(Instant.now());
        rumenHealthTmrParticleScoreTool.setLastSyncTimeUtc(Instant.now());
        rumenHealthTmrParticleScoreTool.setLastModifyUser(createdBy);
      } else {
        rumenHealthTmrParticleScoreTool.setLastModifiedTimeUtc(Instant.now());
        rumenHealthTmrParticleScoreTool.setLastModifyUser(updatedBy);
        rumenHealthTmrParticleScoreTool.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setTmrParticleScore(rumenHealthTmrParticleScoreTool);
    }
  }

  public void updateDefaultAttributesForHeatStress(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    HeatStressTool heatStressTool = visit.getVisitDocument().getHeatStress();
    if (heatStressTool != null) {
      if (!isUpdate) {
        if (heatStressTool.getId() == null) {
          heatStressTool.setId(UUID.randomUUID());
        }
        heatStressTool.setVisitId(visit.getVisitDocument().getId());

        heatStressTool.setCreateUser(createdBy);
        heatStressTool.setCreateTimeUtc(Instant.now());
        heatStressTool.setLastModifiedTimeUtc(Instant.now());
        heatStressTool.setLastSyncTimeUtc(Instant.now());
        heatStressTool.setLastModifyUser(createdBy);
      } else {
        heatStressTool.setLastModifiedTimeUtc(Instant.now());
        heatStressTool.setLastModifyUser(updatedBy);
        heatStressTool.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setHeatStress(heatStressTool);
    }
  }

  public void updateDefaultAttributesForPenTimeBudget(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    PenTimeBudgetTool penTimeBudget = visit.getVisitDocument().getPenTimeBudgetTool();
    if (penTimeBudget != null) {
      if (!isUpdate) {
        if (penTimeBudget.getId() == null) {
          penTimeBudget.setId(UUID.randomUUID());
        }
        penTimeBudget.setVisitId(visit.getVisitDocument().getId());

        penTimeBudget.setCreateUser(createdBy);
        penTimeBudget.setCreateTimeUtc(Instant.now());
        penTimeBudget.setLastModifiedTimeUtc(Instant.now());
        penTimeBudget.setLastSyncTimeUtc(Instant.now());
        penTimeBudget.setLastModifyUser(createdBy);
      } else {
        penTimeBudget.setLastModifiedTimeUtc(Instant.now());
        penTimeBudget.setLastModifyUser(updatedBy);
        penTimeBudget.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setPenTimeBudgetTool(penTimeBudget);
    }
  }

  public void updateDefaultAttributesForManureScreenerTool(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {

    ManureScreenerTool manureScreenerTool = visit.getVisitDocument().getManureScreenerTool();
    if (manureScreenerTool != null) {
      if (!isUpdate) {
        if (manureScreenerTool.getId() == null) {
          manureScreenerTool.setId(UUID.randomUUID());
        }
        manureScreenerTool.setVisitId(visit.getVisitDocument().getId());

        manureScreenerTool.setCreateUser(createdBy);
        manureScreenerTool.setCreateTimeUtc(Instant.now());
        manureScreenerTool.setLastModifiedTimeUtc(Instant.now());
        manureScreenerTool.setLastSyncTimeUtc(Instant.now());
        manureScreenerTool.setLastModifyUser(createdBy);
      } else {
        manureScreenerTool.setLastModifiedTimeUtc(Instant.now());
        manureScreenerTool.setLastModifyUser(updatedBy);
        manureScreenerTool.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setManureScreenerTool(manureScreenerTool);
    }
  }

  public void updateDefaultAttributesForForageAuditScorecard(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {

    Scorecard forageAuditScorecard = visit.getVisitDocument().getForageAuditScorecard();
    if (forageAuditScorecard != null) {
      if (!isUpdate) {
        if (forageAuditScorecard.getId() == null) {
          forageAuditScorecard.setId(UUID.randomUUID());
        }
        forageAuditScorecard.setVisitId(visit.getVisitDocument().getId());

        forageAuditScorecard.setCreateUser(createdBy);
        forageAuditScorecard.setCreateTimeUtc(Instant.now());
        forageAuditScorecard.setLastModifiedTimeUtc(Instant.now());
        forageAuditScorecard.setLastSyncTimeUtc(Instant.now());
        forageAuditScorecard.setLastModifyUser(createdBy);
      } else {
        forageAuditScorecard.setLastModifiedTimeUtc(Instant.now());
        forageAuditScorecard.setLastModifyUser(updatedBy);
        forageAuditScorecard.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setForageAuditScorecard(forageAuditScorecard);
    }
  }

  public void updateDefaultAttributesForProfatibilityAnalysis(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    ProfitabilityAnalysisTool profitabilityAnalysisTool =
        visit.getVisitDocument().getProfitabilityAnalysis();
    if (profitabilityAnalysisTool != null) {
      if (!isUpdate) {
        if (profitabilityAnalysisTool.getId() == null) {
          profitabilityAnalysisTool.setId(UUID.randomUUID());
        }
        profitabilityAnalysisTool.setVisitId(visit.getVisitDocument().getId());

        profitabilityAnalysisTool.setCreateUser(createdBy);
        profitabilityAnalysisTool.setCreateTimeUtc(Instant.now());
        profitabilityAnalysisTool.setLastModifiedTimeUtc(Instant.now());
        profitabilityAnalysisTool.setLastSyncTimeUtc(Instant.now());
        profitabilityAnalysisTool.setLastModifyUser(createdBy);
      } else {
        profitabilityAnalysisTool.setLastModifiedTimeUtc(Instant.now());
        profitabilityAnalysisTool.setLastModifyUser(updatedBy);
        profitabilityAnalysisTool.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setProfitabilityAnalysis(profitabilityAnalysisTool);
    }
  }

  public void updateDefaultAttributesForCalfHeiferScorecard(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    CalfHeiferScorecard calfHeiferScorecard = visit.getVisitDocument().getCalfHeiferScorecard();
    if (calfHeiferScorecard != null) {
      if (!isUpdate) {
        if (calfHeiferScorecard.getId() == null) {
          calfHeiferScorecard.setId(UUID.randomUUID());
        }
        calfHeiferScorecard.setVisitId(visit.getVisitDocument().getId());

        calfHeiferScorecard.setCreateUser(createdBy);
        calfHeiferScorecard.setCreateTimeUtc(Instant.now());
        calfHeiferScorecard.setLastModifiedTimeUtc(Instant.now());
        calfHeiferScorecard.setLastSyncTimeUtc(Instant.now());
        calfHeiferScorecard.setLastModifyUser(createdBy);
      } else {
        calfHeiferScorecard.setLastModifiedTimeUtc(Instant.now());
        calfHeiferScorecard.setLastModifyUser(updatedBy);
        calfHeiferScorecard.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setCalfHeiferScorecard(calfHeiferScorecard);
    }
  }

  public void updateDefaultAttributesForReturnOverFeed(
      Visits visit, String createdBy, String updatedBy, boolean isUpdate) {
    ReturnOverFeedTool returnOverFeed = visit.getVisitDocument().getReturnOverFeedTool();
    if (returnOverFeed != null) {
      if (!isUpdate) {
        if (returnOverFeed.getId() == null) {
          returnOverFeed.setId(UUID.randomUUID());
        }
        returnOverFeed.setVisitId(visit.getVisitDocument().getId());

        returnOverFeed.setCreateUser(createdBy);
        returnOverFeed.setCreateTimeUtc(Instant.now());
        returnOverFeed.setLastModifiedTimeUtc(Instant.now());
        returnOverFeed.setLastSyncTimeUtc(Instant.now());
        returnOverFeed.setLastModifyUser(createdBy);
      } else {
        returnOverFeed.setLastModifiedTimeUtc(Instant.now());
        returnOverFeed.setLastModifyUser(updatedBy);
        returnOverFeed.setLastSyncTimeUtc(Instant.now());
      }
      visit.getVisitDocument().setReturnOverFeedTool(returnOverFeed);
    }
  }

  public void setDefaultValuesForPileAndBunkerList(PileAndBunkerTool pileAndBunkerTool) {

    if (pileAndBunkerTool.getPileBunkers() != null
        && !pileAndBunkerTool.getPileBunkers().isEmpty()) {
      pileAndBunkerTool.getPileBunkers().stream()
          .forEach(
              pileAndBunker -> {
                if (pileAndBunker.getId() == null) {
                  pileAndBunker.setId(UUID.randomUUID());
                }
                if (pileAndBunker.getCreateTimeUtc() == null) {
                  pileAndBunker.setCreateTimeUtc(Instant.now());
                }
              });
    }
  }

  public Map<String, Object> toolToHashMap(Object tool) {
    if (tool == null) {
      return Collections.emptyMap();
    }
    return objectMapper.convertValue(tool, new TypeReference<>() {});
  }
}
