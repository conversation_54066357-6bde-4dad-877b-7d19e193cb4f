/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.cdp.site;

import com.app.cargill.document.DietDocument;
import com.app.cargill.document.SiteDocument;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@EqualsAndHashCode(callSuper = false)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SiteDocumentDTO extends SiteDocument {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Barns")
  @Builder.Default
  private List<BarnDTO> barnsDTO = new ArrayList<>();

  @JsonProperty("Diets")
  @Builder.Default
  private List<DietDocument> diets = new ArrayList<>();

  @JsonProperty("AccountId")
  private String cdpAccountId;

  @JsonProperty("ExternalAccountId")
  private String cdpExternalAccountId;
}
