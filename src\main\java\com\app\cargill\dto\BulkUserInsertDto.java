/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.Business;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BulkUserInsertDto {

  private List<String> userNames;

  private Business countryId = Business.Global;

  private Integer salesforceCountryId;
  private List<String> alreadyExists;
  private List<String> newUsers;
}
