/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class ContactAccountRecord implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("externalId")
  @JsonAlias({"externalId", "DE_Account_External_ID__c"})
  private String externalId;

  @JsonProperty("BusinessID")
  @JsonAlias({"BusinessID", "Cargill_Business__c"})
  private String businessId;
}
