/* Cargill Inc.(C) 2022 */
package com.app.cargill.model.analytics;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ReportDownloadMeta implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String siteId;
  private String userId;
  private String userEmail;
  private String userCountry;
  private String accountId;
  private String accountName;
}
