<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="../../app/generatedTemplateResources/css/visitreport.css" rel="stylesheet">
    <script src="../../app/generatedTemplateResources/js/chart.js"></script>
    <script src="../../app/generatedTemplateResources/js/echarts.min.js"></script>
    <title>${localization.getMessage("Report.Cargill.Report", [], "Cargill - Report", locale)}</title>
</head>

<body>

    <div class="container mid-body">
        <div class="flex-points">
            <div class="prime-wrapper">
                <label>${localization.getMessage("Report.Visit.name", [], "Visit Name", locale)}</label>
                <h4 class="prime-value">${model.visitName!}</h4>
            </div>
            <div class="prime-wrapper">
                <label>${localization.getMessage("Account", [], "Account", locale)}</label>
                <h4 class="prime-value">${model.accountName!}</h4>
            </div>
            <div class="prime-wrapper">
                <label>${localization.getMessage("SiteDetailsSetupViewModel.SiteName", [], "Site Name", locale)}</label>
                <h4 class="prime-value">${model.siteName!}</h4>
            </div>
            <div class="prime-wrapper">
                <label>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}</label>
                <h4 class="prime-value">${model.visitDate!}</h4>
            </div>
        </div>
        <#if model.siteDetails??>
            <div class="table-primary">
                
				<#if model.siteDetails?? && (model.siteDetails.generalDetails?? || model.siteDetails.dietInputs?? || model.siteDetails.animalInputs??) >
				<div class="row">
                    <div class="col-12">
                        <h3 class="title-primary">${localization.getMessage("SiteDetailViewModel.Title", [], "Site
                            Details",
                            locale)}</h3>
                    </div>
                </div>
                <div class="row mx-neg-4 mb-1">
					<#if model.siteDetails?? && (model.siteDetails.generalDetails?? || model.siteDetails.dietInputs??)>
                    <div class="col-6 px-4">
					<#if model.siteDetails?? && model.siteDetails.generalDetails??>
                        <table>
                            <tr>
                                <th>${localization.getMessage("General", [], "General", locale)}</th>
                                <th></th>
                            </tr>
							<#list model.siteDetails.generalDetails?keys as key>
								<tr>
									<td style="width:70%"> ${key!}</td>
									<td style="width:30%">${model.siteDetails.generalDetails[key]!}</td>
								</tr>
							</#list>
                        </table>
					</#if>
					<#if model.siteDetails?? && model.siteDetails.dietInputs??>
                        <table>
                            <tr>
                                <th>${localization.getMessage("SiteDetailViewModel.DietInputsSiteLactating", [], "Diet
                                    Inputs, Site (Lactating Animals)", locale)}</th>
                                <th></th>
                            </tr>
							<#list model.siteDetails.dietInputs?keys as key>
								<tr>
									<td style="width:70%"> ${key!}</td>
									<td style="width:30%">${model.siteDetails.dietInputs[key]!}</td>
								</tr>
							</#list>
                        </table>
					</#if>
                    </div>
					</#if>
					<#if model.siteDetails?? && model.siteDetails.animalInputs??>
                    <div class="col-6 px-4">
                        <table>
                            <tr>
                                <th>${localization.getMessage("HeatstressDataEntryViewModel.AnimalInputs", [], "Animal
                                    Inputs", locale)}</th>
                                <th></th>
                            </tr>
							<#list model.siteDetails.animalInputs?keys as key>
								<tr>
									<td> ${key!}</td>
									<td>${model.siteDetails.animalInputs[key]!}</td>
								</tr>
							</#list>
                        </table>
                    </div>
					</#if>
                </div>
				</#if>
				<#if model.siteDetails?? && model.siteDetails.penDetails?? &&  model.siteDetails.penDetails[0] ??>
                <div class="row mb-1">
                    <div class="col-12">
                        <h3 class="title-primary">${localization.getMessage("Pendetails", [], "Pen Details", locale)}
                        </h3>
                        <table>
							<tr>
								<#list model.siteDetails.penDetails[0] as headings>
									<th>${headings.column!}</th>
								</#list>
							</tr>
                            <#if model.siteDetails?? && model.siteDetails.penDetails?? &&
                                model.siteDetails.penDetails[0] ??>
                                <#list model.siteDetails.penDetails as innerlist>
                                    <tr>
                                        <#list innerlist as obj>
                                            <td>${obj.value!}</td>
                                        </#list>
                                    </tr>
                                </#list>
                            </#if>
                        </table>
                    </div>
                </div>
				</#if>
            </div>
        </#if>
    </div>
    <#assign isFirstTool=true>
        <#function addPageBreakIfNotFirstTool isFirstTool>
            <#assign pageBreak=isFirstTool?then('', 'break-page' )>
                <#if isFirstTool>
                    <#assign isFirstTool=false>
                </#if>
                <#return pageBreak>
        </#function>
        <#if model.rumenHealthCudChewingTool?? || model.bodyConditionScoreTool?? || model.locomotionScoreTool?? ||
            model.rumenHealthManureScoreTool?? || model.milkSoldEvaluationTool?? || model.roboticMilkEvaluationTool?? ||
            model.metabolicIncidenceTool?? || model.rumenFillTool?? || model.forageInventoriesTool?? ||
            model.foragePennStateTool?? || model.rumenHealthTMRParticleScoreTool?? ||
            model.rumenHealthManureScreeningTool?? || model.heatStressEvaluationTool?? || model.forageAuditTool?? || model.scorecardTool?? || (model.generalComments?? && model.generalComments[0]??)>
            <div class="break-page"></div>
            <div class="container mid-body">
                <div class="pt-0">
                    <h3 class="title-primary">${localization.getMessage("Report.Tool.Details", [], "Tool Details",
                        locale)}
                    </h3>
                </div>
            </div>
        </#if>
        <#global toolNumber=1>
            <#if model.rumenHealthCudChewingTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-RumenHealthCudChewing.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.bodyConditionScoreTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-BodyConditionScore.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.locomotionScoreTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-LocomotionScore.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.rumenHealthManureScoreTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-RumenHealthManureScore.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.milkSoldEvaluationTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Productivity-MilkSoldEvaluation.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.roboticMilkEvaluationTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Productivity-RoboticMilkEvaluation.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.metabolicIncidenceTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-MetabolicIncidence.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.rumenFillTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-RumenFill.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.forageInventoriesTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Nutrition-ForageInventories.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.foragePennStateTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Nutrition-ForagePennState.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.rumenHealthTMRParticleScoreTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-RumenHealthTMRParticleScore.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
            <#if model.rumenHealthManureScreeningTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Health-RumenHealthManureScreening.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
			<#if model.heatStressEvaluationTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Comfort-HeatStressEvaluation.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
			<#if model.forageAuditTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Nutrition-ForageAudit.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
			<#if model.penTimeBudgetTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/Comfort-PenTimeBudget.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
			<#if model.scorecardTool??>
                <div class="${addPageBreakIfNotFirstTool(isFirstTool)}"></div>
                <#include "visitReport/CalfAndHeifer-Scorecard.ftl">
                    <#global toolNumber=toolNumber+1>
            </#if>
			
            <#if model.generalComments??>
                <#include "visitReport/GeneralComments.ftl">
            </#if>
</body>

</html>