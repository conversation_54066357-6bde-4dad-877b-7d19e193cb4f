/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimalAnalysisDetailsToolItemCDP implements Serializable {

  @JsonProperty("PenId")
  public UUID penId;

  @JsonProperty("EarTagName")
  public String earTagName;

  @JsonProperty("BCSCategory")
  public Double bcsCategory;

  @JsonProperty("LocomotionScore")
  public Double locomotionScore;

  @JsonProperty("DaysInMilk")
  public Integer daysInMilk;
}
