/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.mapper;

import com.app.cargill.document.Address;
import com.app.cargill.sf.crescendo.model.AddressCrescendo;

public class CrescendoAddressMapper {

  private CrescendoAddressMapper() {}

  public static Address crescendoToModel(AddressCrescendo addressCrescendo) {
    Address address = new Address();
    address.setCity(addressCrescendo.getCity());
    address.setCountry(addressCrescendo.getCountry());
    address.setStreet(addressCrescendo.getStreet());
    address.setPostalCode(addressCrescendo.getPostalCode());
    address.setCountyCommunity(addressCrescendo.getCountyCommunity());
    address.setStateOrProvince(addressCrescendo.getStateOrProvince());
    return address;
  }

  public static AddressCrescendo modelToCrescendo(Address address) {
    AddressCrescendo addressCrescendo = new AddressCrescendo();
    addressCrescendo.setStreet(address.getStreet());
    addressCrescendo.setCity(address.getCity());
    addressCrescendo.setStateOrProvince(address.getStateOrProvince());
    addressCrescendo.setCountry(address.getCountry());
    addressCrescendo.setPostalCode(address.getPostalCode());
    addressCrescendo.setCountyCommunity(address.getCountyCommunity());
    return addressCrescendo;
  }
}
