/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.dto.ISelectKeyValueDto;
import com.app.cargill.model.Sites;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SitesRepository extends JpaRepository<Sites, Long> {

  @Query(
      value = "Select a.* FROM Sites a where a.site_document ->> 'id' = :id and a.deleted=false",
      nativeQuery = true)
  Sites findBySiteId(@Param("id") String siteId);

  @Query(value = "Select a.* FROM Sites a where a.site_document ->> 'id' = :id", nativeQuery = true)
  Sites findBySiteIdUnfiltered(@Param("id") String siteId);

  @Query(
      value =
          "SELECT s.* FROM sites s WHERE s.site_document ->> 'AccountId' = :accountId AND"
              + " timezone('UTC',s.updated_date) > :lastSyncTime AND s.deleted=false",
      nativeQuery = true)
  Page<Sites> findByAccountIdAndUpdatedDate(
      @Param("accountId") String accountId,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value = "Select s.* FROM Sites s where s.site_document ->> 'AccountId' = :accountId",
      nativeQuery = true)
  List<Sites> findAllByAccountId(@Param("accountId") String accountId);

  List<Sites> findByLocalId(String localId);

  @Query(
      value =
          "Select COUNT(s.*) FROM Sites s WHERE s.site_document ->> 'AccountId' = :id AND"
              + " s.deleted=false",
      nativeQuery = true)
  Integer countByAccountId(String id);

  @Query(
      value =
          "SELECT s.* FROM Sites s WHERE s.site_document ->> 'AccountId' IN (:accountIds) AND"
              + " timezone('UTC',s.updated_date) > :lastSyncTime AND s.deleted=false ",
      nativeQuery = true)
  Page<Sites> findByUpdatedDateAndAccountsUsers(
      @Param("accountIds") List<String> accountIds,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value =
          "SELECT s.site_document->>'id' FROM Sites s WHERE s.site_document ->> 'AccountId' IN"
              + " (:accountIds) AND s.deleted=false ",
      nativeQuery = true)
  List<String> findSiteIdsByAccountIds(@Param("accountIds") List<String> accountIds);

  @Query(
      value =
          "SELECT s.* FROM Sites s WHERE s.site_document ->> 'AccountId' IN"
              + " (:accountIds) AND s.deleted=false ",
      nativeQuery = true)
  List<Sites> findSitesByAccountIds(@Param("accountIds") List<String> accountIds);

  @Query(
      value =
          "SELECT s.* FROM Sites s WHERE "
              + " timezone('UTC',s.updated_date) > :lastSyncTime AND s.deleted=false ",
      nativeQuery = true)
  List<Sites> findByUpdatedDate(@Param("lastSyncTime") Date lastSyncTime);

  @Query(
      value =
          "SELECT s.* FROM Sites s WHERE s.site_document ->> 'AccountId' IN (SELECT"
              + " a.account_document ->> 'id' FROM accounts a WHERE CAST(a.account_document ->"
              + " 'Users' as jsonb)  @> CAST(:currentLoggedUser as jsonb)) AND s.deleted=false",
      nativeQuery = true)
  List<Sites> findByCurrentLoggedInUser(@Param("currentLoggedUser") String currentLoggedUser);

  @Query(
      value =
          "SELECT s.site_document ->> 'id' FROM Sites s WHERE s.site_document ->> 'AccountId' IN"
              + " (SELECT a.account_document ->> 'id' FROM accounts a WHERE CAST(a.account_document"
              + " -> 'Users' as jsonb)  @> CAST(:currentLoggedUser as jsonb)) AND s.deleted=false",
      nativeQuery = true)
  List<String> findSiteIdsByCurrentLoggedInUser(
      @Param("currentLoggedUser") String currentLoggedUser);

  @Query(
      value =
          "select unnest((array(select"
              + " jsonb_array_elements(site_document->'Visits')->>'LabyrinthVisitId'))[CASE when"
              + " jsonb_array_length(site_document->'Visits') <7 then 0 when"
              + " jsonb_array_length(site_document->'Visits') >=7 then"
              + " jsonb_array_length(site_document->'Visits')-6"
              + " END\\:jsonb_array_length(site_document->'Visits')]) from sites    where"
              + " site_document->>'AccountId' in(:accountIds) and deleted = false",
      nativeQuery = true)
  List<String> findVisitidsByAccountIds(@Param("accountIds") List<String> accountIds);

  @Query(
      value = "SELECT * FROM SITES where site_document->>'id' IN (:siteIds) and deleted = false",
      nativeQuery = true)
  List<Sites> findBySiteIds(@Param("siteIds") List<String> siteIds);

  @Query(
      value =
          "SELECT s.site_document->>'id' as key, s.site_document->>'Milk' as value FROM SITES s"
              + " where site_document->>'id' IN (:siteIds) and deleted = false",
      nativeQuery = true)
  List<ISelectKeyValueDto<UUID, Double>> findMilkBySiteIds(@Param("siteIds") List<String> siteIds);

  @Query(
      value =
          "SELECT * FROM sites WHERE site_document->>'NeedsSync' = :needsSync and"
              + " site_document->>'DataSource' = :dataSource and deleted = false",
      nativeQuery = true)
  List<Sites> findAllBySiteDocumentNeedsSyncAndSiteDocumentDataSource(
      @Param("needsSync") String needsSync, @Param("dataSource") String dataSource);

  @Query(
      value =
          "Select a.* FROM Sites a where (a.site_document ->> 'id' = :id or a.site_document->>"
              + " 'ExternalId' = :externalId)",
      nativeQuery = true)
  Sites findBySiteIdOrExternalId(
      @Param("id") String siteId, @Param("externalId") String externalId);

  @Query(
      value =
          "SELECT * FROM sites where (timezone('UTC',updated_date) > :dateFrom AND"
              + " timezone('UTC',updated_date) <= :dateTo)"
              + " ORDER BY site_document->> 'id'",
      nativeQuery = true)
  Page<Sites> findAllSitesByFromAndToDate(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo, Pageable pageable);

  @Query(
      value =
          "select * from sites where site_document->>'id' in (select site_id from  (select"
              + " site_document->>'id' as site_id,  trim('\"' from"
              + " text(site_mapping_document->'LabyrinthSiteId')) as smd from sites  left outer"
              + " join site_mappings on  sites.site_document->>'id' ="
              + " site_mappings.site_mapping_document->>'LabyrinthSiteId') s where smd is null)",
      nativeQuery = true)
  List<Sites> findSitesWithMissingSiteMappings();

  @Query(
      value = "select * from sites where site_document->>'ExternalId' is null AND deleted =false",
      nativeQuery = true)
  List<Sites> findSitesWithMissingExternalId();

  @Query(
      value =
          "select * from (select * from sites where site_document->>'id' not in (select site_id"
              + " from (select site_document->>'id' as site_id, site_document->>'DataSource' as"
              + " data_source,  jsonb_array_elements(CAST(site_document->>'SiteMappings' as jsonb))"
              + " as mappings from sites where deleted=false and site_document->>'DataSource' in"
              + " ('UNKNOWN', 'CRESCENDO')) m  where m.mappings->>'SystemName' = 'DDW')) s  where"
              + " s.deleted = false and s.site_document->>'DataSource' in ('UNKNOWN', 'CRESCENDO')",
      nativeQuery = true)
  List<Sites> findCrescendoSitesWithMissingDdw();

  @Query(
      value =
          "Select a.* FROM Sites a where a.site_document->> 'ExternalId' = :externalId and"
              + " a.deleted=false",
      nativeQuery = true)
  Sites findByExternalId(@Param("externalId") String externalId);
}
