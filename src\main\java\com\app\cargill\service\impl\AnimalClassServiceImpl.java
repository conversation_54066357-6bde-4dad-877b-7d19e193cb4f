/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.LangCodes;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.dto.AnimalClassDto;
import com.app.cargill.dto.AnimalClassTriplet;
import com.app.cargill.service.IAnimalClassService;
import java.util.ArrayList;
import java.util.Locale;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.javatuples.Triplet;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AnimalClassServiceImpl implements IAnimalClassService {

  private static final UUID LACTATING_FRESH_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000001");
  private static final UUID LACTATING_MILKING_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000002");
  private static final UUID LACTATING_LOW_FORGE_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000003");
  private static final UUID LACTATING_PASTURE_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000004");
  private static final UUID LACTATING_FRESH_HEIFER_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000005");
  private static final UUID LACTATING_AA_EFFICIENCY_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000006");
  private static final UUID DRY_CLOSEUP_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000007");
  private static final UUID DRY_FAR_OFF_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000008");
  private static final UUID DRY_CLOSEUP_HEIFER_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000009");
  private static final UUID DRY_SHORT_DRY_PERIOD_ID =
      UUID.fromString("00000000-0000-0000-0000-000000000010");
  private static final UUID HEIFER_ID = UUID.fromString("00000000-0000-0000-0000-000000000011");
  private static final UUID CALF_ID = UUID.fromString("00000000-0000-0000-0000-000000000012");
  private static final UUID MALE_BULL_ID = UUID.fromString("00000000-0000-0000-0000-000000000013");
  private static final UUID MALE_STEER_ID = UUID.fromString("00000000-0000-0000-0000-000000000014");

  private static ArrayList<Triplet<UUID, String, String>> createAnimalList() {
    var animals = new ArrayList<Triplet<UUID, String, String>>();

    animals.add(new Triplet<>(LACTATING_FRESH_ID, LangKeys.LACTATING, LangKeys.FRESH));
    animals.add(new Triplet<>(LACTATING_MILKING_ID, LangKeys.LACTATING, LangKeys.MILKING));
    animals.add(new Triplet<>(LACTATING_LOW_FORGE_ID, LangKeys.LACTATING, LangKeys.LOWFORGE));
    animals.add(new Triplet<>(LACTATING_PASTURE_ID, LangKeys.LACTATING, LangKeys.PASTURE));
    animals.add(
        new Triplet<>(LACTATING_FRESH_HEIFER_ID, LangKeys.LACTATING, LangKeys.FRESH_HEIFER));
    animals.add(
        new Triplet<>(LACTATING_AA_EFFICIENCY_ID, LangKeys.LACTATING, LangKeys.AAEFFICIENCY));

    animals.add(new Triplet<>(DRY_CLOSEUP_ID, LangKeys.DRY, LangKeys.CLOSE_UP));
    animals.add(new Triplet<>(DRY_FAR_OFF_ID, LangKeys.DRY, LangKeys.FAR_OFF));
    animals.add(new Triplet<>(DRY_CLOSEUP_HEIFER_ID, LangKeys.DRY, LangKeys.CLOSE_UP_HEIFER));
    animals.add(new Triplet<>(DRY_SHORT_DRY_PERIOD_ID, LangKeys.DRY, LangKeys.SHORT_DRY_PERIOD));

    animals.add(new Triplet<>(HEIFER_ID, LangKeys.HEIFER, LangKeys.HEIFER));

    animals.add(new Triplet<>(CALF_ID, LangKeys.CALF, LangKeys.CALF));

    animals.add(new Triplet<>(MALE_BULL_ID, LangKeys.MALE, LangKeys.BULL));
    animals.add(new Triplet<>(MALE_STEER_ID, LangKeys.MALE, LangKeys.STEER));

    return animals;
  }

  @Override
  public AnimalClassDto getAnimalClasses(ResourceBundleMessageSource source) {
    ArrayList<Triplet<UUID, String, String>> animalList = createAnimalList();
    AnimalClassDto dto = new AnimalClassDto();

    animalList.forEach(
        animalClass -> {
          for (LangCodes code : LangCodes.values()) {
            Locale locale = Locale.forLanguageTag(code.name());
            String clazz = source.getMessage(animalClass.getValue1(), new Object[] {}, locale);
            String subClass =
                animalClass.getValue2() != null
                    ? source.getMessage(animalClass.getValue2(), new Object[] {}, locale)
                    : null;
            mapToDto(dto, animalClass, code, clazz, subClass);
          }
        });

    return dto;
  }

  @Override
  public UUID getAnimalTypeId(String subClass) {
    if (subClass == null) return UUID.fromString("00000000-0000-0000-0000-000000000000");
    ArrayList<Triplet<UUID, String, String>> animalList = createAnimalList();
    Triplet<UUID, String, String> classs =
        animalList.stream()
            .filter(at -> at.getValue2().equalsIgnoreCase(subClass.replaceAll("[^a-zA-Z0-9]", "")))
            .findFirst()
            .orElse(null);
    if (classs != null) return classs.getValue0();
    else return UUID.fromString("00000000-0000-0000-0000-000000000000");
  }

  void mapToDto(
      AnimalClassDto dto,
      Triplet<UUID, String, String> animalClass,
      LangCodes code,
      String clazz,
      String subClass) {
    if ((animalClass.getValue0()).compareTo(LACTATING_FRESH_ID) == 0) {
      dto.getLactatingFresh()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(LACTATING_FRESH_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(LACTATING_FRESH_HEIFER_ID) == 0) {
      dto.getLactatingFreshHeifer()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(LACTATING_FRESH_HEIFER_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(LACTATING_MILKING_ID) == 0) {
      dto.getLactatingMilking()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(LACTATING_MILKING_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(LACTATING_PASTURE_ID) == 0) {
      dto.getLactatingPasture()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(LACTATING_PASTURE_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(LACTATING_LOW_FORGE_ID) == 0) {
      dto.getLactatingLowForage()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(LACTATING_LOW_FORGE_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(LACTATING_AA_EFFICIENCY_ID) == 0) {
      dto.getLactatingAAEfficiency()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(LACTATING_AA_EFFICIENCY_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(DRY_FAR_OFF_ID) == 0) {
      dto.getDryFarOff()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(DRY_FAR_OFF_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(DRY_SHORT_DRY_PERIOD_ID) == 0) {
      dto.getDryShortDryPeriod()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(DRY_SHORT_DRY_PERIOD_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(DRY_CLOSEUP_ID) == 0) {
      dto.getDryCloseUp()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(DRY_CLOSEUP_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(DRY_CLOSEUP_HEIFER_ID) == 0) {
      dto.getDryCloseUpHeifer()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(DRY_CLOSEUP_HEIFER_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(HEIFER_ID) == 0) {
      dto.getHeifer()
          .put(code.toString().toLowerCase(), new AnimalClassTriplet<>(HEIFER_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(CALF_ID) == 0) {
      dto.getCalf()
          .put(code.toString().toLowerCase(), new AnimalClassTriplet<>(CALF_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(MALE_BULL_ID) == 0) {
      dto.getMaleBull()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(MALE_BULL_ID, clazz, subClass));
    } else if ((animalClass.getValue0()).compareTo(MALE_STEER_ID) == 0) {
      dto.getMaleSteer()
          .put(
              code.toString().toLowerCase(),
              new AnimalClassTriplet<>(MALE_STEER_ID, clazz, subClass));
    }
  }
}
