/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.crescendo.mapper.CrescendoAccountMapper;
import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SiteMappingSyncServiceTest {

  @Mock private SiteMappingsRepository siteMappingsRepository;
  @Mock private SitesRepository sitesRepository;

  @InjectMocks private SiteMappingSyncService siteMappingSyncService;

  @Captor ArgumentCaptor<List<SiteMappings>> siteMappingsCaptor;
  @Captor ArgumentCaptor<Sites> sitesCaptor;

  @Test
  void whenSiteMappingsArriveTheyAreProcessedCorrect() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/account_with_mappings.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenReturn(new ArrayList<>());
    SiteDocument siteDocument = SiteDocument.builder().build();
    Sites dbSite = new Sites(siteDocument);
    when(sitesRepository.findBySiteId(anyString())).thenReturn(dbSite);

    siteMappingSyncService.processMappings(account);
    verify(siteMappingsRepository).saveAll(siteMappingsCaptor.capture());
    verify(sitesRepository).save(sitesCaptor.capture());

    List<SiteMappings> result = siteMappingsCaptor.getValue();

    assertEquals(1, result.size());
    SiteMappingDocument smResult = result.get(0).getSiteMappingDocument();
    assertNotNull(smResult.getLabyrinthSiteId());
    assertNotNull(smResult.getMaxSiteId());
    assertNotNull(smResult.getDcgoId());
    assertNotNull(smResult.getDdwHerdId());
    assertNotNull(smResult.getMilkProcessorId());
    assertNotNull(smResult.getLabyrinthAccountId());
    Sites sitesResult = sitesCaptor.getValue();
    assertEquals(5, sitesResult.getSiteDocument().getDataSourceMappings().size());
  }

  @Test
  void whenSiteMappingsArriveTheyAreProcessedCorrect_2() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/account_with_mappings_2.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenReturn(new ArrayList<>());

    siteMappingSyncService.processMappings(account);
    verify(siteMappingsRepository).saveAll(siteMappingsCaptor.capture());

    List<SiteMappings> result = siteMappingsCaptor.getValue();

    assertEquals(4, result.size());
  }

  @Test
  void whenThereIsAnExceptionItIsNotThrown() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/account_with_mappings.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenThrow(new IllegalArgumentException("TEST_EXCEPTION"));

    Accounts result = siteMappingSyncService.processMappings(account);

    assertEquals(account, result);
  }

  @Test
  void whenThereIsDDWMissing() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_app_account_ddwmissing.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenReturn(new ArrayList<>());

    Accounts result = siteMappingSyncService.processMappings(account);

    assertEquals(account, result);
  }

  @Test
  void whenThereIsDCGOmissing() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_app_account_dcgomissing.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenReturn(new ArrayList<>());

    Accounts result = siteMappingSyncService.processMappings(account);

    assertEquals(account, result);
  }

  @Test
  void whenThereIsMAXmissing() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_app_account_maxmissing.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenReturn(new ArrayList<>());

    Accounts result = siteMappingSyncService.processMappings(account);

    assertEquals(account, result);
  }

  @Test
  void whenThereIsMilkProcessormissing() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> crescendoAccounts =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_app_account_mpmissing.json"),
            new TypeReference<>() {});
    Accounts account = CrescendoAccountMapper.crescendoToModel(crescendoAccounts.get(0));
    when(siteMappingsRepository.findAllByLabyrinthAccountId(anyString()))
        .thenReturn(new ArrayList<>());

    Accounts result = siteMappingSyncService.processMappings(account);

    assertEquals(account, result);
  }

  @Test
  void whenCheckDcgo() {
    SiteMappings sm = new SiteMappings();
    SiteMappingDocument smd = new SiteMappingDocument();
    smd.setDcgoId("id");
    sm.setSiteMappingDocument(smd);
    ExternalDataSource eds = new ExternalDataSource();
    eds.setSystem("DDW");
    siteMappingSyncService.checkDCGO(sm, true, eds);
    assertTrue(true);
  }

  @Test
  void whenCheckDDDW() {
    SiteMappings sm = new SiteMappings();
    SiteMappingDocument smd = new SiteMappingDocument();
    smd.setDdwHerdId("id");
    sm.setSiteMappingDocument(smd);
    ExternalDataSource eds = new ExternalDataSource();
    eds.setSystem("DDW");
    siteMappingSyncService.checkDDW(sm, true, eds);
    assertTrue(true);
  }

  @Test
  void whenCheckDDDWSystemNull() {
    SiteMappings sm = new SiteMappings();
    SiteMappingDocument smd = new SiteMappingDocument();
    smd.setDdwHerdId("id");
    sm.setSiteMappingDocument(smd);
    ExternalDataSource eds = new ExternalDataSource();
    eds.setSystem(null);
    siteMappingSyncService.checkDDW(sm, true, eds);
    assertTrue(true);
  }

  @Test
  void whenCheckMP() {
    SiteMappings sm = new SiteMappings();
    SiteMappingDocument smd = new SiteMappingDocument();
    smd.setMilkProcessorId("id");
    sm.setSiteMappingDocument(smd);
    ExternalDataSource eds = new ExternalDataSource();
    eds.setSystem("Something");
    siteMappingSyncService.checkMP(sm, true, eds);
    assertTrue(true);
  }
}
