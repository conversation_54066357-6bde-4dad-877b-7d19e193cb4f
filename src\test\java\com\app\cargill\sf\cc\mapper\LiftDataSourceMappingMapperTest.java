/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.sf.cc.model.AccountRecord;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.RecordAttributes;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.Site;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class LiftDataSourceMappingMapperTest {

  @Test
  void whenDataIsCorrectExpectedResultIsReturned() {
    Site site = new Site();
    List<ExternalDataSource> externalDataSources = new ArrayList<>();
    externalDataSources.add(
        new ExternalDataSource(
            new RecordAttributes(),
            UUID.randomUUID().toString(),
            "id",
            "LM_SITE",
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            null));
    externalDataSources.add(
        new ExternalDataSource(
            new RecordAttributes(),
            UUID.randomUUID().toString(),
            "id",
            "DDW",
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            null));
    externalDataSources.add(
        new ExternalDataSource(
            new RecordAttributes(),
            UUID.randomUUID().toString(),
            "id",
            "MAX",
            UUID.randomUUID().toString(),
            UUID.randomUUID().toString(),
            null));
    SalesforceRecordsResponse<ExternalDataSource> recordsResponse =
        new SalesforceRecordsResponse<>();
    recordsResponse.setRecords(externalDataSources);
    site.setExternalDataSources(recordsResponse);
    site.setAccountRecord(
        new AccountRecord(new RecordAttributes(), "1234", UUID.randomUUID().toString()));
    SiteMappingDocument result = LiftSiteMappingMapper.transform(site);
    assertNotNull(result);
  }

  @Test
  void whenThereIsAnExceptionCorrectItIsNotPropagated() {
    Site site = new Site();
    List<ExternalDataSource> externalDataSources = new ArrayList<>();
    externalDataSources.add(
        new ExternalDataSource(
            new RecordAttributes(), "1", "id", "LM_SITE", "1", UUID.randomUUID().toString(), null));
    SalesforceRecordsResponse<ExternalDataSource> recordsResponse =
        new SalesforceRecordsResponse<>();
    recordsResponse.setRecords(externalDataSources);
    site.setExternalDataSources(recordsResponse);
    site.setAccountRecord(
        new AccountRecord(new RecordAttributes(), "1234", UUID.randomUUID().toString()));
    assertDoesNotThrow(() -> LiftSiteMappingMapper.transform(site));
  }
}
