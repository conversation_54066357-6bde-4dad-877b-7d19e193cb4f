/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocomotionHerdToolItemDto implements Serializable {

  private List<LocomotionHerdToolItemCategoryItemDto> categories;

  private List<LocomotionToolItemDto> pensForVisit;

  private Double daysInMilk;

  private Integer totalAnimalsInHerd;

  private Double milkProductionInKg;

  private Double milkPriceAtSiteLevel;
}
