/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.migration.CosmosDataMigration.MigrationType;
import com.app.cargill.cosmos.model.MilkProcessorCosmos;
import com.app.cargill.cosmos.model.MilkProcessorDataCosmos;
import com.app.cargill.cosmos.repo.MilkProcessorsCosmosRepository;
import com.app.cargill.model.MilkProcessors;
import com.app.cargill.repository.MilkProcessorsRepository;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
class MilkProcessorsMigrationTest {
  @Mock private MilkProcessorsCosmosRepository cosmosRepository;

  @Mock private MilkProcessorsRepository dbRepository;

  @InjectMocks private MilkProcessorsMigration migration;

  @Test
  void whenMigrationPassesCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    MilkProcessorCosmos milkProcessorCosmos1 = new MilkProcessorCosmos();
    milkProcessorCosmos1.setUserId("<EMAIL>");
    milkProcessorCosmos1.setId(UUID.randomUUID().toString());
    milkProcessorCosmos1.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos1.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    MilkProcessorCosmos milkProcessorCosmos2 = new MilkProcessorCosmos();
    milkProcessorCosmos2.setUserId("<EMAIL>");
    milkProcessorCosmos2.setId(UUID.randomUUID().toString());
    milkProcessorCosmos2.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos2.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    Flux<MilkProcessorCosmos> cosmosFlux = Flux.just(milkProcessorCosmos1, milkProcessorCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(MilkProcessors.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(2, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenMigrationPassesButSomeRecordsFailCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    MilkProcessorCosmos milkProcessorCosmos1 = new MilkProcessorCosmos();
    milkProcessorCosmos1.setUserId("<EMAIL>");
    milkProcessorCosmos1.setId(UUID.randomUUID().toString());
    milkProcessorCosmos1.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos1.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    MilkProcessorCosmos milkProcessorCosmos2 = new MilkProcessorCosmos();
    milkProcessorCosmos2.setUserId("<EMAIL>");
    milkProcessorCosmos2.setId(UUID.randomUUID().toString());
    milkProcessorCosmos2.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos2.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    MilkProcessorCosmos milkProcessorCosmos3 = new MilkProcessorCosmos();
    milkProcessorCosmos3.setUserId("<EMAIL>");
    milkProcessorCosmos3.setId(UUID.randomUUID().toString());
    milkProcessorCosmos3.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos3.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    Flux<MilkProcessorCosmos> cosmosFlux =
        Flux.just(milkProcessorCosmos1, milkProcessorCosmos2, milkProcessorCosmos3);
    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.findByDocumentId(any()))
        .thenThrow(new IllegalStateException())
        .thenReturn(null);
    when(dbRepository.save(any()))
        .thenThrow(new RuntimeException())
        .thenReturn(mock(MilkProcessors.class));

    MigrationResult migrationResult = migration.moveAll().get();

    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(2, migrationResult.getFailed());
  }

  @Test
  void whenDataIsWrongThePipelinePassesButFailsAreMarked()
      throws ExecutionException, InterruptedException {
    MilkProcessorCosmos milkProcessorCosmos1 = new MilkProcessorCosmos();
    milkProcessorCosmos1.setUserId("<EMAIL>");
    milkProcessorCosmos1.setId(UUID.randomUUID().toString());
    milkProcessorCosmos1.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos1.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    MilkProcessorCosmos milkProcessorCosmos2 = new MilkProcessorCosmos();
    milkProcessorCosmos2.setUserId("<EMAIL>");
    milkProcessorCosmos2.setId("random-string");
    milkProcessorCosmos2.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos2.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    Flux<MilkProcessorCosmos> cosmosFlux = Flux.just(milkProcessorCosmos1, milkProcessorCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(MilkProcessors.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(1, migrationResult.getFailed());
  }

  @Test
  void whenUserIdIsMissingTheRecordIsSkipped() throws ExecutionException, InterruptedException {
    MilkProcessorCosmos milkProcessorCosmos1 = new MilkProcessorCosmos();
    milkProcessorCosmos1.setId(UUID.randomUUID().toString());
    milkProcessorCosmos1.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos1.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    MilkProcessorCosmos milkProcessorCosmos2 = new MilkProcessorCosmos();
    milkProcessorCosmos2.setUserId("<EMAIL>");
    milkProcessorCosmos2.setId(UUID.randomUUID().toString());
    milkProcessorCosmos2.setConcentrationProcessors(List.of(new MilkProcessorDataCosmos()));
    milkProcessorCosmos2.setComponentProcessors(List.of(new MilkProcessorDataCosmos()));

    Flux<MilkProcessorCosmos> cosmosFlux = Flux.just(milkProcessorCosmos1, milkProcessorCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(MilkProcessors.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void correctTypeIsReturned() {
    assertEquals(MigrationType.MILK_PROCESSORS, migration.migrationType());
  }
}
