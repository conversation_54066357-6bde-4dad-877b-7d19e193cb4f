/* Cargill Inc.(C) 2022 */
package com.app.cargill.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class SubTypeIdDeserializerTest {
  private ObjectMapper mapper;
  private SubTypeIdDeserializer deserializer;

  @BeforeEach
  void setup() {
    mapper = new ObjectMapper();
    deserializer = new SubTypeIdDeserializer();
  }

  @Test
  void whenIntegerIsProvidedCorrectTypeIsReturned() {
    String json = String.format("{\"value\":%s}", "8");
    Integer type = deserialiseString(json);
    assertEquals(8, type);
  }

  @Test
  void whenStringIsProvidedCorrectTypeIsReturned() {
    String json = String.format("{\"value\":%s}", "\"Farm Producer\"");
    Integer type = deserialiseString(json);
    assertEquals(8, type);
  }

  @Test
  void whenUnknownValueIsProvidedCorrectTypeIsReturned() {
    String json = String.format("{\"value\":%s}", "\"Unknown\"");
    Integer type = deserialiseString(json);
    assertEquals(8, type);
  }

  @SneakyThrows({JsonParseException.class, IOException.class})
  private Integer deserialiseString(String json) {
    InputStream stream = new ByteArrayInputStream(json.getBytes(StandardCharsets.UTF_8));
    JsonParser parser = mapper.getFactory().createParser(stream);
    DeserializationContext ctxt = mapper.getDeserializationContext();
    parser.nextToken();
    parser.nextToken();
    parser.nextToken();
    return deserializer.deserialize(parser, ctxt);
  }
}
