/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.cdp.site;

import com.app.cargill.document.PenDocument;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BarnDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("Id")
  private UUID id;

  @JsonProperty("BarnName")
  private String barnName;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("Pens")
  private List<PenDocument> pens;
}
