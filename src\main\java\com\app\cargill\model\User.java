/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.constants.AuthenticationPlatform;
import com.app.cargill.constants.UserAccountType;
import com.app.cargill.document.UserDocument;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.validator.constraints.Length;

@Entity
@Table(name = "users")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class User extends BaseEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  public User(UserDocument userDocument) {
    this.fullName = "";
    this.email = userDocument.getUserName();
    this.userDocument = userDocument;
  }

  @NotNull
  @NotEmpty
  @Column(name = "full_name", nullable = false)
  private String fullName;

  @Column(length = 100)
  @NotNull
  @Email
  private String email;

  @Length(max = 45)
  @Column(name = "mobile_number", length = 45)
  private String mobileNumber;

  @Column(length = 70)
  @Length(max = 70)
  private String password;

  @Length(max = 45)
  @Column(name = "principal_name", length = 45)
  private String principalName;

  @Column(name = "account_type")
  @Enumerated(EnumType.STRING)
  private UserAccountType accountType;

  @ManyToMany(cascade = CascadeType.MERGE)
  @JoinTable(
      name = "users_to_role",
      joinColumns = @JoinColumn(name = "user_id"),
      inverseJoinColumns = @JoinColumn(name = "role_id"))
  private Set<Role> roles;

  @Column(name = "authentication_platform")
  @Enumerated(EnumType.STRING)
  private AuthenticationPlatform authenticationPlatform;

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb") // or, json
  private UserDocument userDocument;
}
