/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Oauth2Dto {
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonProperty("token_type")
  private String tokenType;

  @JsonProperty("access_token")
  private String accessToken;

  @JsonProperty("expires_in")
  private Integer expiresIn;

  @JsonProperty("not_before")
  private Integer notBefore;

  @JsonProperty("expires_on")
  private Integer expireOn;

  private String scope;

  private String resource;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonProperty("id_token")
  private String idToken;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  @JsonProperty("refresh_token")
  private String refreshToken;

  @JsonInclude(JsonInclude.Include.NON_NULL)
  private String jti;
}
