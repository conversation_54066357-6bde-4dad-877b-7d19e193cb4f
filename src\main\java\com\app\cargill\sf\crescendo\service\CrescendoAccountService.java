/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.service;

import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.crescendo.mapper.CrescendoAccountMapper;
import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Service
@RequiredArgsConstructor
@Slf4j
public class CrescendoAccountService {

  private final AccountsRepository accountsRepository;
  private final SiteMappingSyncService siteMappingSyncService;
  private final SitesRepository sitesRepository;

  public List<AccountCrescendo> getAccountsForSync(Instant from) {
    List<Accounts> accounts;
    if (from != null) {
      accounts = accountsRepository.getAccountsByUpdatedDateAfterForCrescendo(from);
    } else {
      accounts = accountsRepository.findAllByAccountDocumentNeedsSyncForCrescendo("true");
    }

    List<Accounts> needSyncAccounts =
        accounts.stream()
            .filter(a -> Boolean.TRUE.equals(a.getAccountDocument().getNeedsSync()))
            .toList();

    List<AccountCrescendo> needSyncAccountsCrescendo =
        needSyncAccounts.stream().map(CrescendoAccountMapper::modelToCrescendo).toList();

    markNotNeedSync(needSyncAccounts)
        .whenComplete((i, e) -> log.info("{} accounts synced to Crescendo", i));

    return needSyncAccountsCrescendo;
  }

  public CompletableFuture<Integer> updateAccounts(List<AccountCrescendo> accountsCrescendo) {
    CompletableFuture<Integer> completableFuture = updateAccountsAsync(accountsCrescendo);
    completableFuture.whenComplete((i, e) -> log.info("{} accounts synced from Crescendo", i));
    return completableFuture;
  }

  private CompletableFuture<Integer> updateAccountsAsync(List<AccountCrescendo> accountsCrescendo) {
    Flux<AccountCrescendo> accountCrescendoFlux = Flux.fromIterable(accountsCrescendo);
    AtomicInteger failedItems = new AtomicInteger(0);
    return accountCrescendoFlux
        .map(CrescendoAccountMapper::crescendoToModel)
        .flatMap(
            a ->
                Mono.fromCallable(() -> mapSingleAccount(a))
                    .subscribeOn(Schedulers.boundedElastic())
                    .onErrorResume(
                        t -> {
                          failedItems.incrementAndGet();
                          log.error("CRESCENDO_UPDATE_ACCOUNT_MAPPING {} {}", t.getMessage(), a);
                          return Mono.empty();
                        }))
        .flatMap(
            a ->
                Mono.fromCallable(() -> accountsRepository.save(a))
                    .map(siteMappingSyncService::processMappings)
                    .map(this::updateSitesExternalId)
                    .subscribeOn(Schedulers.boundedElastic())
                    .onErrorResume(
                        t -> {
                          failedItems.incrementAndGet();
                          log.error("CRESCENDO_UPDATE_ACCOUNT_SAVE {} {}", t.getMessage(), a);
                          return Mono.empty();
                        }))
        .onErrorContinue(
            (t, o) -> {
              failedItems.incrementAndGet();
              log.error("CRESCENDO_UPDATE_ACCOUNT_PROCESS {}", o, t);
            })
        .reduce(0, (accumulator, a) -> accumulator + 1)
        .toFuture();
  }

  private Accounts mapSingleAccount(Accounts crescendoAccount) {
    Accounts existingAccount = null;
    if (crescendoAccount.getAccountDocument().getId() != null) {
      existingAccount =
          accountsRepository.findByAccountId(
              crescendoAccount.getAccountDocument().getId().toString());
    }
    if (existingAccount == null) {
      // This is new account
      Accounts account = new Accounts(crescendoAccount.getAccountDocument());
      if (crescendoAccount.getAccountDocument().getId() == null) {
        crescendoAccount.getAccountDocument().setId(UUID.randomUUID());
      }
      account.setLocalId(crescendoAccount.getAccountDocument().getId().toString());
      existingAccount = account;
      // Crescendo sends only goldenRecordId as contact information
      existingAccount.getAccountDocument().setContacts(new ArrayList<>());
    }
    return CrescendoAccountMapper.modelUpdate(existingAccount, crescendoAccount);
  }

  private CompletableFuture<Integer> markNotNeedSync(List<Accounts> accounts) {
    List<Accounts> updatedAccounts =
        accounts.stream()
            .map(
                a -> {
                  a.getAccountDocument().setNeedsSync(false);
                  return a;
                })
            .toList();
    accountsRepository.saveAll(updatedAccounts);
    return CompletableFuture.completedFuture(accounts.size());
  }

  private Accounts updateSitesExternalId(Accounts account) {
    if (account.getAccountDocument().getGoldenRecordId() == null) {
      return account;
    }
    List<Sites> accountSites =
        sitesRepository.findAllByAccountId(account.getAccountDocument().getId().toString());
    for (Sites dbSite : accountSites) {
      if (dbSite.getSiteDocument().getExternalAccountId() == null
          || !dbSite
              .getSiteDocument()
              .getExternalAccountId()
              .equals(account.getAccountDocument().getGoldenRecordId())) {
        dbSite
            .getSiteDocument()
            .setExternalAccountId(account.getAccountDocument().getGoldenRecordId());
        sitesRepository.save(dbSite);
      }
    }
    return account;
  }
}
