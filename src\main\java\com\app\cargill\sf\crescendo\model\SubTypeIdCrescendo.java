/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum SubTypeIdCrescendo {
  BARN("Barn"),
  BUILDING("Building"),
  BUYING_GROUP("Buying Group"),
  CLUB_ASSOCIATION("Club / Association"),
  COMPETITOR_DEALER("Competitor Dealer"),
  <PERSON><PERSON><PERSON>("Dealer"),
  END_USER("End User"),
  FARM_PRODUCER("Farm Producer"),
  FOOD_PLANT("Food Processing Plant"),
  HATCHERY("Hatchery"),
  IMPORTER("Importer"),
  INDEPENDENT("Independent"),
  INTERMEDIARY("Intermediary"),
  OTHER_THIRD_PARTY("Other Third Party"),
  PARTIAL_CUTSOMER("Partial Customer"),
  POINT_OF_SALES("Point of Sales"),
  PRODUCER("Producer"),
  PROSPECT("Prospect"),
  ROUTER_TRUCK_OR_VAN("Router Truck / Van"),
  SALES_REPRESENTATIVE("Sales Representative"),
  VETERINARIAN("Veterinarian"),
  MILK_PROCESSING("Milk Processing"),
  SLAUGHTERHOUSE("Slaughterhouse - Beef"),
  SLAUGHTERHOUSE_BROILER("Slaughterhouse - Broiler"),
  SLAUGHTERHOUSE_BROILER_AND_TURKEY("Slaughterhouse - Broiler & Turkey"),
  SLAUGHTERHOUSE_DUCKS("Slaughterhouse - Ducks"),
  SLAUGHTERHOUSE_GEESE("Slaughterhouse - Geese"),
  SLAUGHTERHOUSE_TURKEY("Slaughterhouse - Turkey"),
  SLAUGHTERHOUSE_WATERFOWL("Slaughterhouse - Waterfowl"),
  SLAUGHTERHOUSE_PORK("Slaughterhouse - Pork"),
  SLAUGHTERHOUSE_PORK_AND_BEEF("Slaughterhouse - Pork & Beef"),
  SLAUGHTERHOUSE_MULTI_SPECIE("Slaughterhouse - Multi Specie"),
  RIVA("Riva");

  private final String value;

  @JsonCreator
  SubTypeIdCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
