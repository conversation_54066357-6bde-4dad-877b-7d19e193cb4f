/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.BCSAnimalAnalysisRecordDto;
import com.app.cargill.dto.BcsAnimalAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.chart.XDDFScatterChartData.Series;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("bcsAnimalAnalysisExcelReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class BcsAnimalAnalysisReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public String getFileName(Object data) {
    BcsAnimalAnalysisReportDto dto = modelMapper.map(data, BcsAnimalAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    BcsAnimalAnalysisReportDto dto = modelMapper.map(data, BcsAnimalAnalysisReportDto.class);
    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));

      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      // create sheet1
      XSSFSheet sheet1 =
          wb.createSheet(ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      prepareHeader(wb, sheet1, rowNumber, cellNumber, dto, boldStyle, locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      XSSFRow row = sheet1.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      sheet1.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 6));

      // days in milk DIM
      cellNumber.set(0);

      row = sheet1.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_PEN_NAME, null, source, locale));
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_DIM, null, source, locale));
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_BODY_CONDITION_SCORE, null, source, locale));
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_LOCOMOTION_SCORE, null, source, locale));

      // Categories
      int bcsStartRowNumber = rowNumber.get();

      for (BCSAnimalAnalysisRecordDto entry : dto.getAnimalAnalysis()) {
        cellNumber.set(0);
        row = sheet1.createRow(rowNumber.getAndIncrement());
        ExcelUtils.highlightEmptyCell(
            row, entry.getVisitDate(), cellNumber, decimalStyle, greyCellStyle);
        ExcelUtils.highlightEmptyCell(
            row, entry.getPenName(), cellNumber, decimalStyle, greyCellStyle);
        ExcelUtils.highlightEmptyCell(row, entry.getDim(), cellNumber, decimalStyle, greyCellStyle);
        ExcelUtils.highlightEmptyCell(row, entry.getBcs(), cellNumber, decimalStyle, greyCellStyle);
        ExcelUtils.highlightEmptyCell(
            row, entry.getLocomotionScore(), cellNumber, decimalStyle, greyCellStyle);
      }
      // create data sources
      // y0 axis
      int columnStart = 2;
      int bcsEndRowNumber =
          ExcelUtils.fixColumnEndIndex(
              bcsStartRowNumber, bcsStartRowNumber + dto.getAnimalAnalysis().size() - 1);
      XDDFNumericalDataSource<Double> dimDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet1,
              new CellRangeAddress(bcsStartRowNumber, bcsEndRowNumber, columnStart, columnStart++));
      XDDFNumericalDataSource<Double> bcsDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet1,
              new CellRangeAddress(bcsStartRowNumber, bcsEndRowNumber, columnStart, columnStart++));
      XDDFNumericalDataSource<Double> locomotionDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet1,
              new CellRangeAddress(bcsStartRowNumber, bcsEndRowNumber, columnStart, columnStart++));

      // needed objects for the charts
      XSSFChart chart;
      XDDFValueAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFScatterChartData dataLeft;
      Series series;

      // ====== scatter chart ======
      int chartCol0 = columnStart + 2;
      chart =
          ExcelUtils.initChart(
              sheet1,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_BODY_CONDITION_SCORE_CHART_NAME,
                  new Object[] {dto.getAnimalTagName()},
                  source,
                  locale),
              chartCol0,
              4,
              chartCol0 + 10,
              24);

      // ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createValueBottomAxis(
              chart, ExcelUtils.getLangValue(LangKeys.REPORT_DIM, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart,
              ExcelUtils.getLangValue(LangKeys.REPORT_BODY_CONDITION_SCORE, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      dataLeft = (XDDFScatterChartData) chart.createData(ChartTypes.SCATTER, bottomAxis, leftAxis);

      // create series
      series = (XDDFScatterChartData.Series) dataLeft.addSeries(dimDataSource, bcsDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_BODY_CONDITION_SCORE, null, source, locale),
          new CellReference(sheet1.getSheetName(), bcsStartRowNumber, 3, true, true));

      chart.plot(dataLeft);
      ExcelUtils.setLineMarker(series, MarkerStyle.CIRCLE);
      ExcelUtils.setLineNoFill(series);

      // ExcelUtils.drawGridLinesInChart(chart, true);
      // end bcs chart
      // draw locomotion chart
      drawLocomotionChart(
          sheet1,
          locale,
          dimDataSource,
          locomotionDataSource,
          bcsStartRowNumber,
          dto.getAnimalTagName(),
          chartCol0);
      return ExcelUtils.finalizeWorkbook(wb, sheet1.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {

    Map<String, byte[]> imageTemplates = new HashMap<>();

    byte[] bodyConditionScore =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            "BCSPenSelectionViewModel.Title", "Body Condition Score", null, source, locale),
        bodyConditionScore);

    byte[] locomotionScore =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT.getImageTemplateName1(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue("LocomotionScore", "Locomotion Score", null, source, locale),
        locomotionScore);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  void drawLocomotionChart(
      XSSFSheet sheet,
      Locale locale,
      XDDFNumericalDataSource<Double> dimDataSource,
      XDDFNumericalDataSource<Double> locomotionDataSource,
      int locomotionStartRowNumber,
      String animalTagName,
      int columnEnd) {
    // needed objects for the charts
    XSSFChart chart;
    XDDFValueAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFScatterChartData dataLeft;
    Series series;

    chart =
        ExcelUtils.initChart(
            sheet,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_LOCOMOTION_SCORE_CHART_NAME,
                new Object[] {animalTagName},
                source,
                locale),
            columnEnd,
            24,
            columnEnd + 10,
            44);

    // ExcelUtils.initLegends(chart);

    bottomAxis =
        ExcelUtils.createValueBottomAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_DIM, null, source, locale));

    leftAxis =
        ExcelUtils.createLeftAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_LOCOMOTION_SCORE, null, source, locale));
    bottomAxis.crossAxis(leftAxis);
    bottomAxis.setCrosses(AxisCrosses.MIN);
    dataLeft = (XDDFScatterChartData) chart.createData(ChartTypes.SCATTER, bottomAxis, leftAxis);

    // create series
    series = (XDDFScatterChartData.Series) dataLeft.addSeries(dimDataSource, locomotionDataSource);
    series.setTitle(
        ExcelUtils.getLangValue(LangKeys.REPORT_LOCOMOTION_SCORE, null, source, locale),
        new CellReference(sheet.getSheetName(), locomotionStartRowNumber, 4, true, true));
    chart.plot(dataLeft);
    ExcelUtils.setLineMarker(series, MarkerStyle.CIRCLE);
    ExcelUtils.setLineNoFill(series);
  }

  void prepareHeader(
      XSSFWorkbook xSSFWorkbook,
      XSSFSheet xSSFSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      BcsAnimalAnalysisReportDto bcsAnimalAnalysisReportDto,
      XSSFCellStyle cellStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(), xSSFWorkbook, xSSFSheet, rowNumber.get(), cellNumber.getAndIncrement());
    // headings
    XSSFRow row = xSSFSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, bcsAnimalAnalysisReportDto.getVisitName());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, bcsAnimalAnalysisReportDto.getVisitDate());
    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = xSSFSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, bcsAnimalAnalysisReportDto.getToolName());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, bcsAnimalAnalysisReportDto.getAnalysisType());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    // third row
    cellNumber.set(1);
    row = xSSFSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANIMAL_TAG_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, bcsAnimalAnalysisReportDto.getAnimalTagName());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    if (bcsAnimalAnalysisReportDto.getCalvingDate() != null) {
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          cellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_CALVING_DATE, null, source, locale));
      ExcelUtils.createAndSetCellValue(
          row, cellNumber, null, bcsAnimalAnalysisReportDto.getCalvingDate());

      xSSFSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
      ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    }
  }
}
