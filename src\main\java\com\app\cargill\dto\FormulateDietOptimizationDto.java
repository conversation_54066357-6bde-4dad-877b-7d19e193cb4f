/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.FormulateStatus;
import com.app.cargill.document.FormulateDietOptimization;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FormulateDietOptimizationDto {

  private FormulateStatus status;
  private Double asFedAmount;
  private Double dryMatterAmount;
  private Double totalCost;
  private Integer optimizationId;

  public static FormulateDietOptimizationDto mapFormulateDietOptimizationToDto(
      FormulateDietOptimization formulateDietOptimization) {

    if (formulateDietOptimization != null) {
      return FormulateDietOptimizationDto.builder()
          .optimizationId(formulateDietOptimization.getOptimizationId())
          .totalCost(formulateDietOptimization.getTotalCost())
          .dryMatterAmount(formulateDietOptimization.getDryMatterAmount())
          .asFedAmount(formulateDietOptimization.getAsFedAmount())
          .status(formulateDietOptimization.getStatus())
          .build();
    } else {
      return null;
    }
  }
}
