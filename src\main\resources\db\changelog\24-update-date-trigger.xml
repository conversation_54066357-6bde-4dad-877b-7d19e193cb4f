<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="024" author="Taha">
        <sql splitStatements="false">
        
        CREATE OR REPLACE FUNCTION update_related_tables_updated_date()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.account_document->>'id' IS NOT NULL THEN
        -- Update sites
        UPDATE sites
        SET updated_date = now()
        WHERE sites.site_document->>'AccountId' = NEW.account_document->>'id';
        
        -- Update visits
        UPDATE visits
        SET updated_date = now()
        WHERE visits.visit_document->>'CustomerId' = NEW.account_document->>'id';
        
        -- Update pens
        UPDATE pens
        SET updated_date = now()
        WHERE pens.pen_document->>'CustomerAccountId' = NEW.account_document->>'id';
        
        -- Update notes
        UPDATE notes
        SET updated_date = now()
        WHERE notes.notes_document->>'AccountId' = NEW.account_document->>'id';
        
        -- Update diets
        UPDATE diets
        SET updated_date = now()
        WHERE diets.diet_document->>'LabyrinthAccountId' = NEW.account_document->>'id';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


CREATE TRIGGER update_date_related_tables_trigger
AFTER UPDATE OF account_document ON accounts
FOR EACH ROW
EXECUTE FUNCTION update_related_tables_updated_date();
        
          
                  </sql>
    </changeSet>

</databaseChangeLog>