welcome.message=Hallo {0}
AppName=\uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820
LoginViewModel.Copyright=\u00A9 {0} <PERSON>gill, Incorporated. All Rights Reserved.
LoginViewModel.ErrorDescription=\uC544\uC774\uB514\uC640 \uD328\uC2A4\uC6CC\uB4DC\uB97C \uB2E4\uC2DC \uD655\uC778\uD558\uC2DC\uAE30 \uBC14\uB78D\uB2C8\uB2E4.
LoginViewModel.ErrorTitle=\uB85C\uADF8\uC778 \uC624\uB958
LoginViewModel.InvalidMessage=\uB354 \uC774\uC0C1 \uC720\uD6A8\uD558\uC9C0 \uC54A\uC740 \uC544\uC774\uB514 \uB610\uB294 \uD328\uC2A4\uC6CC\uB4DC \uC785\uB2C8\uB2E4.
LoginViewModel.InvalidMessageTitle=\uC720\uD6A8\uD558\uC9C0 \uC54A\uC740 \uB85C\uADF8\uC778
LoginViewModel.LoginPrompt=\uB85C\uADF8\uC778
LoginViewModel.PasswordLabel=\uD328\uC2A4\uC6CC\uB4DC
LoginViewModel.EmailLabel=\uC774\uBA54\uC77C
MenuViewModel.LogoutPrompt=\uB85C\uADF8\uC544\uC6C3
MenuViewModel.Menu=\uB85C\uADF8\uC544\uC6C3
MainViewModel.EmailLabel=\uC774\uBA54\uC77C
OKLabel=\uD655\uC778
LoginViewModel.NetworkErrorMessageTitle=\uB124\uD2B8\uC6CC\uD06C\uC624\uB958
LoginViewModel.NetworkErrorMessage=\uD604\uC7AC \uB124\uD2B8\uC6CC\uD06C\uC5D0 \uC5F0\uACB0\uB418\uC5B4 \uC788\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.
UserPreferencesViewModel.MainHeading=\uB2E4\uC74C\uC758 \uC635\uC158\uC740 \uCD94\uD6C4\uC571 \uC124\uC815\uC5D0\uC11C \uBCC0\uACBD \uAC00\uB2A5\uD569\uB2C8\uB2E4.
UserPreferencesViewModel.UnitOfMeasure=\uCE21\uC815\uB2E8\uC704\uC120\uD0DD
UserPreferencesViewModel.Metric=\uBBF8\uD130\uBC95
UserPreferencesViewModel.Imperial=\uC57C\uB4DC/\uD30C\uC6B4\uB4DC\uBC95
UserPreferencesViewModel.Title=\uC0AC\uC6A9\uC790\uC138\uD305
UserPreferencesViewModel.Branding=\uC81C\uD488 \uBE0C\uB79C\uB4DC
UserPreferencesViewModel.Cargill=\uCE74\uAE38
UserPreferencesViewModel.Purina=\uD4E8\uB9AC\uB098
UserPreferencesViewModel.Provimi=\uD504\uB85C\uBE44\uBBF8
UserPreferencesViewModel.CurrencySelection=\uD1B5\uD654 \uC120\uD0DD
UserPreferencesViewModel.SelectCurrency=\uC8FC\uC694 \uD1B5\uD654\uB97C \uC120\uD0DD\uD558\uC138\uC694
SelectCurrencyViewModel.Title=\uD1B5\uD654
HomeViewModel.Title=\uD604\uC7A5 \uBC29\uBB38 \uB3C4\uAD6C
HomeViewModel.DashboardTab=\uB300\uC2DC\uBCF4\uB4DC
HomeViewModel.CustomersTab=\uAE30\uC874 \uACE0\uAC1D
HomeViewModel.ProspectsTab=\uC608\uC0C1 \uACE0\uAC1D
HomeViewModel.SyncWithDate=\uB3D9\uAE30\uD654 - \uB9C8\uC9C0\uB9C9 \uB3D9\uAE30\uD654 \uB0A0\uC9DC {0:\uC6D4/\uC77C/\uC5F0\uB3C4}
HomeViewModel.SyncWithTime=\uB3D9\uAE30\uD654 - \uB9C8\uC9C0\uB9C9 \uB3D9\uAE30\uD654 \uC2DC\uAC04 (0:\uC2DC\uAC04/\uBD84/\uCD08)
HomeViewModel.SyncWithDash=\uB3D9\uAE30\uD654 -
HomeViewModel.Settings=\uC124\uC815
HomeViewModel.PrivacyStatement=\uAC1C\uC778\uBCF4\uD638 \uC815\uCC45
HomeViewModel.AutoSync=\uC790\uB3D9 \uB3D9\uAE30\uD654
HomeViewModel.Logout=\uB85C\uADF8\uC544\uC6C3
ComfortToolsViewModel.HealthHeading=\uBC29\uBB38\uC744 \uC2DC\uC791\uD558\uC2DC\uB824\uBA74 \uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uB3C4\uAD6C\uB97C \uC120\uD0DD\uD574 \uC8FC\uC2ED\uC2DC\uC624.
ComfortToolsViewModel.ComfortHeading=\uBC29\uBB38\uC744 \uC2DC\uC791\uD558\uC2DC\uB824\uBA74 \uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uB3C4\uAD6C\uB97C \uC120\uD0DD\uD574 \uC8FC\uC2ED\uC2DC\uC624.
ComfortToolsViewModel.ComfortToolsTitle=\uC816\uC18C \uC548\uB77D\uD568 \uD3C9\uAC00 \uB3C4\uAD6C
ComfortToolsViewModel.ComfortToolsList=\uB3C4\uAD6C
ComfortToolsViewModel.HeatstressEvaluationTitle=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uD3C9\uAC00
EmailReportViewModel.PenTimeTitle=\uC6B0\uC0AC \uC2DC\uAC04 \uBE44\uC6A9
ComfortToolsViewModel.PenTimeTitle=\uC6B0\uC0AC \uB300\uAE30 \uC2DC\uAC04
CustomerDetailViewModel.CustomerTitle=\uAE30\uC874 \uACE0\uAC1D \uC815\uBCF4
ProspectProfileViewModel.ProspectTitle=\uC608\uC0C1 \uACE0\uAC1D \uC138\uBD80
ProspectProfileViewModel.ProspectInfo=\uC608\uC0C1 \uACE0\uAC1D \uC815\uBCF4
NewProspectViewModel.Customer=\uAE30\uC874 \uACE0\uAC1D
SiteDetailViewModel.NewVisit=\uC0C8\uB85C\uC6B4 \uBC29\uBB38 \uC2DC\uC791
SiteDetailsResourcesViewModel.Title=\uB9AC\uC18C\uC2A4
NewVisitViewModel.Title=\uBC29\uBB38 \uC0C1\uC138\uC815\uBCF4
CustomerDetailViewModel.MainHeading=\uC0AC\uC774\uD2B8
SiteDetailViewModel.Reports=\uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820 \uBCF4\uACE0\uC11C
SiteDetailViewModel.Summary=\uC694\uC57D
SiteDetailViewModel.Detailed=\uC138\uBD80
SiteDetailViewModel.SiteSetup=\uC0AC\uC774\uD2B8 \uC124\uC815
SiteDetailViewModel.Title=\uC0AC\uC774\uD2B8 \uC138\uBD80\uC815\uBCF4
SiteDetailViewModel.MainHeading=\uBC29\uBB38
SiteDetailViewModel.DairyEnteligenReport=\uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820 \uBCF4\uACE0\uC11C
SiteDetailViewModel.GeneralCustomerSiteSetup=\uC77C\uBC18 \uACE0\uAC1D \uC0AC\uC774\uD2B8 \uC124\uC815
SiteDetailViewModel.AnimalInputsSite=\uAC1C\uCCB4 \uC785\uB825, \uC0AC\uC774\uD2B8
SiteDetailViewModel.DietInputsSiteLactating=\uC0AC\uB8CC \uC785\uB825, \uC0AC\uC774\uD2B8 (\uCC29\uC720\uC6B0)
VisitViewModel.VisitTitle=\uBC29\uBB38 \uC774\uB984
VisitViewModel.ComfortItem=\uCF8C\uC801\uC9C0\uC218\uD3C9\uAC00
VisitViewModel.HealthItem=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00
VisitViewModel.NutritionItem=\uC870\uC0AC\uB8CC\uD3C9\uAC00
VisitViewModel.ProductivityItem=\uC0DD\uC0B0\uC9C0\uC218\uD3C9\uAC00
VisitViewModel.CategorySection=\uB3C4\uAD6C \uCE74\uD14C\uACE0\uB9AC
VisitViewModel.SiteVisitSummary=\uC0AC\uC774\uD2B8 \uBC29\uBB38 \uC694\uC57D
VisitViewModel.WalkthroughReport=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
VisitViewModel.PublishVisit=\uBC29\uBB38 \uC815\uBCF4 \uAC8C\uC2DC
VisitViewModel.PublishNotes=\uB178\uD2B8 \uAC8C\uC2DC
VisitViewModel.PublishNotesPrompt=\uC774 \uBC29\uBB38\uC5D0\uC11C \uC791\uC131\uD55C \uB178\uD2B8\uB97C \uAC8C\uC2DC\uD558\uACA0\uC2B5\uB2C8\uAE4C? \uC774\uB294 \uCDE8\uC18C\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
VisitViewModel.PublishPrompt=\uC774 \uBC29\uBB38\uC744 \uAC8C\uC2DC \uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C? \uC2E4\uD589 \uCDE8\uC18C \uD558\uC2E4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
VisitViewModel.Publish=\uAC8C\uC2DC
VisitViewModel.Delete=\uBC29\uBB38 \uC0AD\uC81C
VisitViewModel.DeletePrompt=\uC774 \uBC29\uBB38\uC744 \uC0AD\uC81C \uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C? \uC2E4\uD589 \uCDE8\uC18C \uD558\uC2E4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
CustomerDetailViewModel.NewVisit=\uC0C8\uB85C\uC6B4 \uBC29\uBB38
CustomerDetailViewModel.NewSite=\uC0C8\uB85C\uC6B4 \uC0AC\uC774\uD2B8 \uCD94\uAC00
HeatstressData=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uB370\uC774\uD130
HeatstressCalculations=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uACC4\uC0B0
HeatstressChart=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uCC28\uD2B8
VisitNotesViewModel.Close=\uB2EB\uAE30
VisitNotesViewModel.New=\uC2E0\uADDC
VisitNotesViewModel.Title=\uB178\uD2B8\uBD81 \uBC29\uBB38
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=\uAD00\uCC30
VisitNotesViewModel.Action=\uC791\uB3D9
VisitNotesViewModel.Task=\uC5C5\uBB34
VisitNotesViewModel.Event=\uC774\uBCA4\uD2B8
EditNoteViewModel.Cancel=\uCDE8\uC18C
HeatstressTableViewModel.Title=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uD3C9\uAC00
HeatstressTableViewModel.HeatstressDataTab=\uB370\uC774\uD130 \uC785\uB825
HeatstressTableViewModel.HeatstressChartTab=\uCC28\uD2B8
HeatstressDataEntryViewModel.AnimalInputs=\uAC1C\uCCB4 \uC785\uB825
HeatstressDataEntryViewModel.Milk=\uC720\uB7C9 ({0})
Milk=\uC720\uB7C9 ({0})
EditNoteViewModel.Save=\uC800\uC7A5
EditNoteViewModel.Title=\uB178\uD2B8
EditNoteViewModel.Close=\uB2EB\uAE30
EditNoteViewModel.CreatedByMetadata={2}\uC5D0 \uC758\uD574 {0} @ {1}\uC5D0 \uC0DD\uC131\uB428
EditNoteViewModel.LastUpdatedByMetadata={2}\uC5D0 \uC758\uD574 {0} @ {1}\uC5D0 \uB9C8\uC9C0\uB9C9\uC73C\uB85C \uC218\uC815
Yes=\uC608
No=\uC544\uB2C8\uC624
Delete=\uC0AD\uC81C
Rationcost=\uC0AC\uB8CC\uBE44 ({0})
EditNoteViewModel.DeletePrompt=\uB178\uD2B8\uB97C \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?
EditNoteViewModel.DeleteImagePrompt=\uC774\uBBF8\uC9C0\uB97C \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?
EditNoteViewModel.DeleteVideoPrompt=\uBE44\uB514\uC624\uB97C \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?
HeatstressDataEntryViewModel.DMI=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 ({0})
HeatstressDataEntryViewModel.NEL=NEL ({0})
HeatstressDataEntryViewModel.MilkFat=\uC720\uC9C0\uBC29 (%)
HeatstressDataEntryViewModel.LactatingAnimals=\uCC29\uC720\uC6B0
HeatstressDataEntryViewModel.CurrentMilkPrice=\uD604\uC7AC \uC720\uB300 ({0}/{1})
CurrentMilkPrice=\uD604\uC7AC \uC720\uB300 ({0}/{1})
HeatstressDataEntryViewModel.MilkProtein=\uC720\uB2E8\uBC31 (%)
HeatstressDataEntryViewModel.Weather=\uB0A0\uC528
HeatstressDataEntryViewModel.Temperature=\uC628\uB3C4({0})
HeatstressDataEntryViewModel.Humidity=\uC2B5\uB3C4(%)
HeatstressDataEntryViewModel.Exposure=\uC9C1\uC0AC\uAD11\uC120 \uB178\uCD9C
HeatstressDataEntryViewModel.HoursExposed=\uC77C\uC870\uC2DC\uAC04
HeatstressChartViewModel.HeatstressEvalLabel=\uD3C9\uADE0 \uC628\uB3C4 \uBC0F \uC0C1\uB300 \uC2B5\uB3C4\uC5D0 \uB300\uD55C \uBCF4\uC815 \uC628\uB3C4(\uD587\uBE5B\uC774 \uC5C6\uB294 \uACBD\uC6B0)
HeatstressChartViewModel.TempHumidIndex=\uC628\uC2B5\uB3C4 \uC9C0\uC218(THI)
HeatstressChartViewModel.IntakeAdjustment=\uC12D\uCDE8\uB7C9 \uC870\uC815
HeatstressChartViewModel.DMIReduction=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 \uAC10\uC18C
HeatstressChartViewModel.EstimateDryMatter=\uCD94\uC815\uB41C \uAC74\uBB3C\uC12D\uCDE8\uB7C9
HeatstressChartViewModel.ReductionDMI=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 \uAC10\uC18C
HeatstressChartViewModel.LossEnergyConsumed=\uC12D\uCDE8\uD55C \uC5D0\uB108\uC9C0 \uC190\uC2E4
HeatstressChartViewModel.EnergyEquivMilkLoss=\uC5D0\uB108\uC9C0 \uBCF4\uC815 \uC720\uC0DD\uC0B0 \uC190\uC2E4
HeatstressChartViewModel.MilkValueLossPerDay=\uC77C\uC77C \uC720\uB300 \uC190\uC2E4
HeatstressChartViewModel.MilkValueLossPerMonth=\uC6D4\uAC04 \uC720\uB300 \uC190\uC2E4
HeatstressChartViewModel.Mcal=Mcal
ErrorDescription=\uADC0\uD558\uC758 \uC815\uBCF4\uB97C \uC77D\uAC70\uB098 \uC4F0\uB294 \uC911 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4.
ErrorTitle=\uC624\uB958
NutritionViewModel.NutritionLabel=\uBC29\uBB38\uC744 \uC2DC\uC791\uD558\uC2DC\uB824\uBA74 \uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uB3C4\uAD6C\uB97C \uC120\uD0DD\uD574 \uC8FC\uC2ED\uC2DC\uC624.
NutritionViewModel.NutritionTools=\uC601\uC591 \uB3C4\uAD6C
NutritionViewModel.NutritionForage=\uC870\uC0AC\uB8CC \uAC80\uC0AC
NutritionViewModel.NutritionPile=\uC0AC\uC77C\uB85C \uC6A9\uB7C9
NutritionViewModel.NutritionToolsList=\uB3C4\uAD6C
ForageAuditViewModel.Title=\uC870\uC0AC\uB8CC \uAC80\uC0AC
ForageAuditViewModel.ForageHeading=\uAE30\uBCF8 \uC870\uC0AC\uB8CC \uC815\uBCF4
ForageAuditViewModel.ForageDetail=\uC870\uC0AC\uB8CC \uD488\uC9C8\uC740 \uC601\uC591\uC18C \uACF5\uAE09\uC758 \uC8FC\uC694 \uC694\uC18C\uB85C \uBAA9\uC7A5\uC758 \uC804\uBC18\uC801 \uC218\uC775\uC131\uC5D0 \uB9E4\uC6B0 \uC911\uC694\uD569\uB2C8\uB2E4. \uC870\uC0AC\uB8CC \uD3C9\uAC00 \uC2A4\uCF54\uC5B4\uCE74\uB4DC\uB294 \uBAA9\uC7A5\uC758 \uC870\uC0AC\uB8CC \uAD00\uB9AC\uB97C \uAC1C\uC120\uD560 \uC218 \uC788\uB3C4\uB85D \uB3C4\uC640\uC90D\uB2C8\uB2E4. \uC774 \uC2A4\uCF54\uC5B4\uCE74\uB4DC\uB294 \uC8FC\uC694 \uAD00\uB9AC \uD56D\uBAA9\uC73C\uB85C \uB098\uB258\uC5B4 \uC788\uC2B5\uB2C8\uB2E4. \uBAA9\uC7A5 \uBC29\uBB38 \uC911 \uC804\uCCB4 \uD56D\uBAA9\uC744 \uD3C9\uAC00\uD560 \uC218\uB3C4 \uC788\uC73C\uBA70, \uC911\uC694\uD55C \uD56D\uBAA9\uB9CC \uC120\uD0DD\uD560 \uC218\uB3C4 \uC788\uC2B5\uB2C8\uB2E4. \uB610\uD55C \uAC01 \uD56D\uBAA9\uBCC4 \uCD94\uAC00\uC801\uC778 \uAE30\uC220 \uC790\uB8CC\uAC00 \uC900\uBE44\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4.
ForageAuditViewModel.ForageAuditScorecard=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
ForageAuditScorecard=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
ForageAuditViewModel.Resources=\uCC38\uC870
HeatstressGreen=\uC2A4\uD2B8\uB808\uC2A4 \uC784\uACC4\uAC12
HeatstressYellow=\uACBD\uBBF8\uD55C \uC2A4\uD2B8\uB808\uC2A4
HeatstressOrange=\uBCF4\uD1B5-\uC2EC\uAC01 \uC2A4\uD2B8\uB808\uC2A4
HeatstressRed=\uC2EC\uD55C \uC2A4\uD2B8\uB808\uC2A4
WeightMetric=kg
WeightImperial=\uD30C\uC6B4\uB4DC
WeightImperialCWT=CWT(100\uD30C\uC6B4\uB4DC)
EnergyMetric=Mcal/kg
EnergyImperial=Mcal/lb
Mcal=Mcal
TemperatureMetric=\u00B0C
TemperatureImperial=\u00B0F
VolumeImperial=\uAC24\uB7F0
VolumeMetric=ml
ForageScorecardViewModel.Title=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
ForageScorecardViewModel.SurveyCategories=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
ForageScorecardViewModel.SurveyOfForages=\uC870\uC0AC\uB8CC \uAC80\uC0AC
ForageScorecardViewModel.Harvest=\uC218\uD655
ForageScorecardViewModel.BunkersAndPiles=\uBC99\uCEE4 \uBC0F \uC0AC\uC77C\uB85C
ForageScorecardViewModel.TowerSilos=\uD0C0\uC6CC \uC0AC\uC77C\uB85C
ForageScorecardViewModel.ViewOverallForageScore=\uC804\uCCB4 \uC870\uC0AC\uB8CC \uC810\uC218 \uBCF4\uAE30
ViewOverallForageScore=\uC804\uCCB4 \uC870\uC0AC\uB8CC \uC810\uC218 \uBCF4\uAE30
OverallForageScoreDetails=\uC804\uBC18\uC801\uC778 \uC870\uC0AC\uB8CC \uC810\uC218\uB97C \uBCF4\uC2DC\uB824\uBA74, \uC704\uC758 \uBAA9\uB85D\uC5D0\uC11C \uCD5C\uC18C \uD558\uB098 \uC774\uC0C1\uC758 \uC124\uBB38 \uC870\uC0AC\uB97C \uC644\uB8CC\uD558\uC2ED\uC2DC\uC624.
ForageScorecardViewModel.SilageBags=\uC0AC\uC77C\uB9AC\uC9C0 \uBC31
ForageScorecardViewModel.Baleage=\uBCA0\uC77C\uB9AC\uC9C0
ForageScorecardViewModel.MaintainingForageQuality=\uC870\uC0AC\uB8CC \uD488\uC9C8 \uC720\uC9C0
SurveyOfForages_InventoryIsMonitored=\uC6D0\uB8CC\uC7AC\uACE0\uAC00 \uBAA8\uB2C8\uD130\uB9C1 \uB418\uACE0 \uC788\uC2B5\uB2C8\uAE4C? 
SurveyOfForages_AnnualCowNumAndForageNeeds=\uB9E4\uB144 \uACBD\uC0B0\uC6B0 \uB450\uC218\uC640 \uC870\uC0AC\uB8CC \uC218\uC694\uB97C \uACC4\uD68D\uD558\uACE0 \uC788\uC2B5\uB2C8\uAE4C?
SurveyOfForages_SilosSizedForCapacity=\uC0AC\uC77C\uB85C\uB294 \uCD1D \uC0AC\uC77C\uB9AC\uC9C0 \uD544\uC694\uB7C9\uC5D0 \uBE44\uD574 \uC801\uC815\uD55C \uD06C\uAE30 \uC785\uB2C8\uAE4C? \uC6A9\uB7C9\uC744 \uCD08\uACFC\uD55C \uC0AC\uC77C\uB9AC\uC9C0 \uC801\uC7AC\uB294 \uACE0\uB824\uD558\uC9C0 \uC54A\uC74C
SurveyOfForages_LooseOrFacedFeedWithin=\uC0AC\uC77C\uB9AC\uC9C0 \uB2E8\uBA74\uC740 \uC5BC\uB9C8 \uC774\uB0B4\uC5D0 \uC18C\uC9C4\uB418\uB098\uC694?
SurveyOfForages_NoLooseFeedRemaining=\uAE09\uC5EC\uAC00 \uC644\uB8CC\uB41C \uC774\uD6C4 \uB0A8\uB294 \uC0AC\uB8CC\uAC00 \uC5C6\uC2B5\uB2C8\uAE4C?
SurveyOfForages_InspectedForSpoilageAndMold=\uBAA8\uB4E0 \uC870\uC0AC\uB8CC\uB294 \uACF0\uD321\uC774 \uBC1C\uC0DD \uBC0F \uBD80\uD328\uC0C1\uD0DC\uB97C \uD655\uC778\uD558\uACE0, \uBD80\uD328\uB41C \uC870\uC0AC\uB8CC\uB294 \uD3D0\uAE30\uB429\uB2C8\uAE4C?
SurveyOfForages_VisibleSignsOfSoil=\uC0AC\uC77C\uB9AC\uC9C0\uC5D0 \uD759\uC774 \uBB4D\uC740 \uC790\uAD6D\uC774 \uC5C6\uC2B5\uB2C8\uAE4C?
SurveyOfForages_AshLevelsInHaylage=\uD5E4\uC77C\uB9AC\uC9C0\uB0B4 \uC870\uD68C\uBD84 \uD568\uB7C9\uC740 \uC5BC\uB9C8\uC785\uB2C8\uAE4C?
SurveyOfForages_AshLevelsInCornSilage=\uC625\uC218\uC218 \uC0AC\uC77C\uB9AC\uC9C0\uB0B4 \uC870\uD68C\uBD84 \uD568\uB7C9\uC740 \uC5BC\uB9C8\uC785\uB2C8\uAE4C?
SurveyOfForages_CornSilageScoreMonitored=\uC625\uC218\uC218 \uC0AC\uC77C\uB9AC\uC9C0\uC758 \uC625\uC218\uC218 \uACBD\uB3C4\uB294 \uCE74\uAE38\uC758 KP \uD14C\uC2A4\uD2B8\uC744 \uD1B5\uD574 \uAD00\uB9AC\uB418\uACE0 \uC788\uC2B5\uB2C8\uAE4C?
SurveyOfForages_CornSilageProcessingScore=\uC625\uC218\uC218 \uC0AC\uC77C\uB9AC\uC9C0\uB97C \uB9CC\uB4DC\uB294 \uC810\uC218?
SurveyOfForages_ButyricAcidLevelsInHaylage=\uD5E4\uC77C\uB9AC\uC9C0\uB0B4 \uBDF0\uD2F0\uB9AD\uC0B0 \uD568\uB7C9\uC740 \uC5BC\uB9C8\uC785\uB2C8\uAE4C?
SurveyOfForages_LacticAcidToAceticAcidLevels=\uC816\uC0B0 \uB300 \uCD08\uC0B0 \uD568\uB7C9\uC740?
Harvest_AdequateEquipmentAndLabor=\uD5E4\uC77C\uB9AC\uC9C0\uC6A9 \uC791\uBB3C\uC744 \uC218\uD655\uD558\uB294\uB370 \uC7A5\uBE44\uC640 \uB178\uB3D9\uB825\uC740 \uCDA9\uBD84\uD569\uB2C8\uAE4C?
Harvest_WholePlantMoistureDetermined=\uC791\uBB3C \uCD1D\uCCB4 \uC218\uBD84\uC740 \uCE21\uC815\uD569\uB2C8\uAE4C?
Harvest_CornSilageMoistureRangeConsistent=\uC625\uC218\uC218 \uC0AC\uC77C\uB9AC\uC9C0\uC758 \uC218\uBD84 \uD568\uB7C9\uC740 90% \uC774\uC0C1\uC758 \uC0D8\uD50C\uC5D0\uC11C \uC77C\uC815\uD569\uB2C8\uAE4C?
Harvest_ForagesHarvestedAtProper=\uC870\uC0AC\uB8CC\uB294 \uC791\uBB3C\uC758 \uC885\uB958\uC640 \uC800\uC7A5\uC124\uBE44\uC5D0 \uC801\uD569\uD55C \uC218\uBD84\uACFC \uC131\uC219\uB3C4\uC5D0\uC11C \uC218\uD655\uD569\uB2C8\uAE4C?
Harvest_LengthOfCutMonitored=\uD39C\uC2A4\uD14C\uC774\uD2B8 \uC785\uC790\uB3C4 \uBD84\uC11D\uAE30\uB97C \uD1B5\uD574\uC11C \uAE38\uC774\uB97C \uCE21\uC815\uD569\uB2C8\uAE4C?
Harvest_KPScoreIsMonitored=KP \uC810\uC218\uB294 32\uC628\uC2A4 \uCEF5\uC774\uB098 Float\uBC95\uC744 \uD1B5\uD574 \uCE21\uC815\uD569\uB2C8\uAE4C?
Harvest_UseSilageAdditive=\uC0AC\uC77C\uB9AC\uC9C0 \uCCA8\uAC00\uC81C(\uC774\uB178\uD058\uB7F0\uD2B8 \uB610\uB294 \uD638\uAE30\uC131 \uC548\uC815\uC81C)\uB97C \uC0AC\uC6A9\uD569\uB2C8\uAE4C?
Harvest_ForageHarvestingDocumented=\uC870\uC0AC\uB8CC \uC218\uD655\uC2DC \uC0C1\uD669, \uBC2D \uC704\uCE58 \uBC0F \uC800\uC7A5\uC704\uCE58\uB97C \uAE30\uB85D\uD569\uB2C8\uAE4C?
BunkersAndPiles_CleanlinessOfFeedArea=\uCCAD\uACB0\uD55C \uC815\uB3C4\uB294? 1-10(\uB9E4\uC6B0 \uCCAD\uACB0\uD568)\uAE4C\uC9C0 \uC120\uD0DD\uD558\uC138\uC694.
BunkersAndPiles_PackingInitialSpreadLayers=\uD3EC\uC7A5 : \uCD5C\uCD08 \uD3BC\uCC98\uC9C0\uB294 \uCE35\uC740 6\uC778\uCE58 \uC774\uD558\uC785\uB2C8\uAE4C?
BunkersAndPiles_PorosityScoresConsistently=\uACF5\uADF9 \uC810\uC218\uAC00 \uC77C\uC815\uD569\uB2C8\uAE4C?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=\uC313\uC774\uB294 \uB354\uBBF8\uC640 \uBC99\uCEE4 \uD06C\uB77C\uC6B4\uC740 3:1\uC774\uD558\uC5EC\uC57C \uD569\uB2C8\uB2E4.(18\uB3C4 \uAE30\uC6B8\uAE30)
BunkersAndPiles_TiresSplitsTouching=\uD0C0\uC774\uC5B4\uAC00 \uD3EC\uC7A5\uC7AC\uAC00 \uC5F0\uACB0\uB418\uB294 \uBD80\uC704\uB97C \uC798 \uB365\uACE0 \uC788\uC2B5\uB2C8\uAE4C?
BunkersAndPiles_SideWallsSealedPlastic=\uCE21\uBA74 \uBCBD\uC740 \uD50C\uB77C\uC2A4\uD2F1\uC73C\uB85C \uD3EC\uC7A5\uB418\uC5B4 \uC788\uC2B5\uB2C8\uAE4C?
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=\uBCF4\uB108\uC2A4 : \uBE44\uD22C\uACFC\uC131 \uD50C\uB77C\uC2A4\uD2F1 \uCE35\uC744 \uD3EC\uD568\uD574 \uB450\uACB9\uC758 \uD50C\uB77C\uC2A4\uD2F1\uC73C\uB85C \uD3EC\uC7A5\uB429\uB2C8\uAE4C?
BunkersAndPiles_SealedImmedAfterPack6milPlastic=6mm\uD50C\uB77C\uC2A4\uD2F1\uC744 \uD1B5\uD574 \uD3EC\uC7A5\uB41C \uD6C4 \uC989\uC2DC \uBC00\uBD09\uD569\uB2C8\uAE4C?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=\uC0B0\uC18C\uAC00 \uB4E4\uC5B4\uC624\uB294 \uCC22\uC5B4\uC9C4 \uD45C\uBA74\uC774 \uC5C6\uACE0, \uD45C\uBA74\uC774 \uB9E4\uB048\uD569\uB2C8\uAE4C?
BunkersAndPiles_FaceRemoveRate=\uC0AC\uC77C\uB9AC\uC9C0 \uC808\uB2E8\uBA74 \uAE09\uC5EC \uC18D\uB3C4
BunkersAndPiles_LooseOrFacedFeedIsFed=\uB5A8\uC5B4\uC838 \uC788\uC5B4\uB098 \uC808\uB2E8\uBA74\uC5D0\uC11C \uCC44\uCDE8\uB41C \uC0AC\uC77C\uB9AC\uC9C0\uAC00 \uAE09\uC5EC\uB429\uB2C8\uAE4C?
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=\uC0AC\uC77C\uB9AC\uC9C0\uC5D0\uC11C \uCEE4\uBC84 \uD50C\uB77C\uC2A4\uD2F1\uB9CC \uC81C\uAC70\uB429\uB2C8\uAE4C?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=14\uC77C \uC774\uB0B4 \uAE09\uC5EC\uB418\uC9C0 \uC54A\uC744 \uB54C \uC0AC\uC77C\uB85C\uB97C \uB365\uC5B4\uB461\uB2C8\uAE4C?
TowerSilos_SiloFillingTimeof3DaysOrLess=\uC0AC\uC77C\uB85C\uB97C \uCC44\uC6B0\uB294 \uB370 3\uC77C \uC774\uB0B4\uAC00 \uAC78\uB9BD\uB2C8\uAE4C?
TowerSilos_FaceRemovalRateGreaterThan4Inches=\uC808\uB2E8\uBCC0\uC774 \uC81C\uAC70\uB418\uB294 \uC591\uC774 \uC77C\uC77C 4\uC778\uCE58\uB97C \uCD08\uACFC\uD569\uB2C8\uAE4C?
SilageBags_BagsPlacedOnStableWellManagedSurface=\uC0AC\uC77C\uB9AC\uC9C0 \uBC31\uC740 \uC798 \uAD00\uB9AC\uB418\uB294 \uD45C\uBA74(\uC544\uC2A4\uD314\uD2B8 \uB610\uB294 \uCF58\uD06C\uB9AC\uD2B8)\uC5D0 \uBCF4\uAD00\uB429\uB2C8\uAE4C??
SilageBags_TrashVegRodentControlledAroundBags=\uC0AC\uC77C\uB9AC\uC9C0 \uBC31 \uC8FC\uBCC0\uC5D0 \uC4F0\uB808\uAE30, \uC7A1\uCD08 \uBC0F \uC124\uCE58\uB958\uAC00 \uC81C\uAC70\uB429\uB2C8\uAE4C?
SilageBags_BonusSecureCoverIsUsed=\uBCF4\uB108\uC2A4 : \uC548\uC804\uD55C \uCEE4\uBC84\uAC00 \uC0AC\uC6A9\uB429\uB2C8\uAE4C?
SilageBags_InspectedForPestHoleDamageRepairOnBasis=\uD574\uCDA9\uC774 \uB6AB\uC5B4\uB193\uC740 \uAD6C\uBA4D\uC774 \uC815\uAE30\uC801\uC73C\uB85C \uC218\uB9AC\uB429\uB2C8\uAE4C?
SilageBags_FaceRemovalRate=\uC0AC\uC77C\uB9AC\uC9C0 \uC808\uB2E8\uBA74 \uAE09\uC5EC \uC18D\uB3C4:
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=\uC808\uB2E8\uBA74\uC774 \uAE54\uB054\uD558\uAC8C \uAD00\uB9AC\uB418\uACE0 \uC788\uC73C\uBA70, \uC774\uB85C \uC778\uD574 \uB5A8\uC5B4\uC838 \uC5F4\uC774 \uB098\uAC70\uB098 \uBC8C\uB824\uC9C0\uB294 \uC0AC\uC77C\uB9AC\uC9C0\uAC00 \uC5C6\uC2B5\uB2C8\uAE4C?
SilageBags_PorosityScoresConsistently=\uACF5\uADF9 \uC810\uC218\uAC00 \uC77C\uC815\uD569\uB2C8\uAE4C?
Baleage_BagsPlacedOnStableWellManagedSurface=\uC0AC\uC77C\uB9AC\uC9C0 \uBC31\uC740 \uC798 \uAD00\uB9AC\uB418\uB294 \uD45C\uBA74(\uC544\uC2A4\uD314\uD2B8 \uB610\uB294 \uCF58\uD06C\uB9AC\uD2B8)\uC5D0 \uBCF4\uAD00\uB429\uB2C8\uAE4C?
Baleage_TrashVegRodentControlledAroundBags=\uC0AC\uC77C\uB9AC\uC9C0 \uBC31 \uC8FC\uBCC0\uC5D0 \uC4F0\uB808\uAE30, \uC7A1\uCD08 \uBC0F \uC124\uCE58\uB958\uAC00 \uC81C\uAC70\uB429\uB2C8\uAE4C?
Baleage_InspectedForPestHoleDamageRepairOnBasis=\uD574\uCDA9\uC774 \uB6AB\uC5B4\uB193\uC740 \uAD6C\uBA4D\uC774 \uC815\uAE30\uC801\uC73C\uB85C \uC218\uB9AC\uB429\uB2C8\uAE4C? (\uC8FC\uB2F9 1\uD68C)
Baleage_WaterShedsOffPlasticNotIntoBaleage=\uBB3C\uC774 \uBCA0\uC77C\uB9AC\uC9C0\uB85C \uC9C1\uC811\uB5A8\uC5B4\uC9C0\uC9C0 \uC54A\uACE0 \uD50C\uB77C\uC2A4\uD2F1\uC744 \uD1B5\uD574 \uBC30\uCD9C\uB429\uB2C8\uAE4C? (\uB300\uD615 \uC0AC\uAC01\uBCA0\uC77C\uC758 \uACBD\uC6B0)?
Baleage_AreBalesWrappedWith=\uBAA8\uB4E0 \uBCA0\uC77C\uC740 \uB798\uD551\uC774 \uB418\uC5B4\uC788\uC2B5\uB2C8\uAE4C?
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=\uBCF4\uB108\uC2A4 : \uC548\uC815\uC81C/\uACF0\uD321\uC774 \uBC29\uC9C0\uC81C\uAC00 \uB365\uACE0 \uB2E4\uC2B5\uD55C \uACC4\uC808\uC5D0 TMR\uC5D0 \uC0AC\uC6A9\uB429\uB2C8\uAE4C?
MaintainingForageQuality_TMRMixHasPleasantAroma=\uBC30\uD569\uB41C TMR\uC5D0\uC11C \uD5A5\uAE30\uB85C\uC6B4 \uB0C4\uC0C8\uAC00 \uB0A9\uB2C8\uAE4C?
MaintainingForageQuality_TMRMixIsCoolToTouch=\uBC30\uD569\uB41C TMR\uC740 \uB9CC\uC838\uBCF4\uBA74 \uC2DC\uC6D0\uD569\uB2C8\uAE4C?
Quarterly=\uBD84\uAE30\uBCC4
SemiAnnually=\uBC18\uAE30\uBCC4
Annually=\uC5F0\uBCC4
OneToSixHours=1~6 \uC2DC\uAC04
SixToTwelveHours=6~12\uC2DC\uAC04
GreaterThanTwelveHours=12\uC2DC\uAC04 \uC774\uC0C1
LessThan4Days=4\uC77C \uC774\uB0B4
FourToSevenDays=4~7\uC77C
GreaterThanSevenDays=7\uC77C \uC774\uC0C1
NoWholeKernals=\uC625\uC218\uC218 \uC54C\uACE1 \uC5C6\uC74C
LessThanFiveWholeKernals=5\uAC1C \uBBF8\uB9CC\uC758 \uC54C\uACE1
GreaterThanFive=5\uAC1C \uC774\uC0C1\uC758 \uC54C\uACE1
DontKnow=\uC798 \uBAA8\uB984
LessThanOneHour=1\uC2DC\uAC04 \uC774\uB0B4
WithinEightHours=8\uC2DC\uAC04 \uC774\uB0B4
GreaterThan8Hours=8\uC2DC\uAC04 \uC774\uC0C1
TwelveInchesOrGreater=12\uC778\uCE58 \uC774\uC0C1
SixToTwelveInches=6~12\uC778\uCE58
LessThanSixInches=6\uC778\uCE58 \uC774\uD558
ForageScorecardViewModel.Yes=\uB124
ForageScorecardViewModel.No=\uC544\uB2C8\uC624
GreaterThanSixHours=6\uC2DC\uAC04 \uC774\uC0C1
ThreeTimesPerWeek=\uC8FC\uB2F9 3\uD68C
TwicePerWeek=\uC8FC\uB2F9 2\uD68C
OncePerWeek=\uC8FC\uB2F9 1\uD68C
GreaterThanThirtySixInchesPerDay=\uC77C\uC77C 36\uC778\uCE58 \uC774\uC0C1
TwentyFourToThirtySixInchesPerDay=\uC77C\uC77C 24~36\uC778\uCE58
LessThanTwentFourInchesPerDay=\uC77C\uC77C 24\uC778\uCE58 \uC774\uB0B4
MoreThan8LayersOfPlastic=8\uACB9 \uC774\uC0C1\uC758 \uD50C\uB77C\uC2A4\uD2F1
SixToEightLayers=6~8\uACB9 \uD50C\uB77C\uC2A4\uD2F1
LessThanSixLayers=6\uACB9 \uC774\uD558\uC758 \uD50C\uB77C\uC2A4\uD2F1
ForageAuditScorecardScoreViewModel.OverallForageScore=\uC804\uBC18\uC801 \uC870\uC0AC\uB8CC \uD3C9\uC218\uC5D0 \uD3EC\uD568\uB428.
Next=\uB2E4\uC74C
Previous=\uC774\uC804
Finish=\uC885\uB8CC
QuestionViewModel.Close=\uB2EB\uAE30
ForageAuditScorecardResultsViewModel.ScoreTab=\uC810\uC218
ForageAuditScorecardResultsViewModel.ResponsesTab=\uC751\uB2F5
ForageAuditScorecardResponsesViewModel.ImprovementsTab=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uAC1C\uC120\uC810
ForageAuditScorecardResponsesViewModel.ResponsesTab=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC751\uB2F5
ForageAuditScorecardResultsViewModel.ImprovementsTab=\uAC1C\uC120 \uC0AC\uD56D
ForageAuditScorecardScoreViewModel.GoodIndicator=\uC88B\uC74C
ForageAuditScorecardScoreViewModel.Title=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC804\uCCB4 \uC810\uC218
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=\uAC1C\uC120 \uC0AC\uD56D
ForageScorecardResultsViewModel.Title=\uACE4\uD3EC \uC0AC\uC77C\uB9AC\uC9C0
SurveyOfForages=\uC870\uC0AC\uB8CC \uC870\uC0AC
Harvest=\uC218\uD655
BunkersAndPiles=\uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uC870\uC0AC\uB8CC \uB354\uBBF8
TowerSilos=\uD0C0\uC6CC \uC0AC\uC77C\uB85C
SilageBags=\uC0AC\uC77C\uB9AC\uC9C0
Baleage=\uACE4\uD3EC \uC0AC\uC77C\uB9AC\uC9C0
MaintainingForageQuality=\uC0AC\uB8CC \uD488\uC9C8 \uC720\uC9C0
Improvements=\uAC1C\uC120 \uC0AC\uD56D
QuestionViewModel.SurveyOfForages=\uC870\uC0AC\uB8CC \uC870\uC0AC
QuestionViewModel.Harvest=\uC218\uD655
QuestionViewModel.BunkersAndPiles=\uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uC870\uC0AC\uB8CC \uB354\uBBF8
QuestionViewModel.TowerSilos=\uD0C0\uC6CC \uC0AC\uC77C\uB85C
QuestionViewModel.SilageBags=\uC0AC\uC77C\uB9AC\uC9C0
QuestionViewModel.Baleage=\uACE4\uD3EC \uC0AC\uC77C\uB9AC\uC9C0
QuestionViewModel.MaintainingForageQuality=\uC0AC\uB8CC \uD488\uC9C8 \uC720\uC9C0
QuestionTableTitle=\uC9C8\uBB38 {0} of {1}
ResourcesViewModel.Title=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC790\uB8CC
Making_Feed_InventoryFOF=\uC0AC\uB8CC \uC800\uC7A5\uACE0 \uB9CC\uB4E4\uAE30
DairyEnteligenFarmReportsources=\uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820 \uC815\uBCF4\uCD9C\uCC98
FAQDairyEnteligenFarmReportandDDW=FAQ \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820 \uB18D\uC7A5\uBCF4\uACE0\uC11C
StorageCalculators=\uC800\uC7A5\uB7C9 \uACC4\uC0B0\uAE30
DecidingSilageStorage=\uC0AC\uC77C\uB9AC\uC9C0 \uC800\uC7A5 \uBC29\uBC95 \uACB0\uC815
PreventingStorageLosses=\uC800\uC7A5\uC2DC \uC190\uC2E4 \uBC29\uC9C0
FeedoutLossesForageStorageSys=\uC870\uC0AC\uB8CC \uC800\uC7A5\uACE0\uC5D0\uC11C \uAE09\uC5EC\uC2DC \uBC1C\uC0DD\uD558\uB294 \uC190\uC2E4
CargillForageLabKPTest=\uCE74\uAE38 \uC870\uC0AC\uB8CC \uBD84\uC11D\uC2E4\uD5D8\uC2E4 KP \uD14C\uC2A4\uD2B8
FermentationAnalysisSilageQT=\uBC1C\uD6A8\uC0C1\uD0DC \uBD84\uC11D &amp; \uC870\uC0AC\uB8CC \uD488\uC9C8 \uAC80\uC0AC
GettingtheMostOutofYourForage=\uB2F9\uC2E0\uC758 \uC870\uC0AC\uB8CC\uB97C \uCD5C\uB300\uD55C \uD65C\uC6A9\uD558\uAE30
SilagePrevention1st=\uC0AC\uC77C\uB9AC\uC9C0 \uBCF4\uAD00 : \uAC00\uC7A5 \uC911\uC694\uD55C \uAC83\uC744 \uBA3C\uC800 \uD558\uB77C!
CornSilageResources=\uC0AC\uC77C\uB9AC\uC9C0 \uBCF4\uAD00 : \uAC00\uC7A5 \uC911\uC694\uD55C \uAC83\uC744 \uBA3C\uC800 \uD558\uB77C!
CornSilageStopGo=\uC625\uC218\uC218 \uC0AC\uC77C\uB9AC\uC9C0 Stop-n-Go
CropCharacteristicsDecisionGuide=\uC791\uBB3C \uD2B9\uC131 \uACB0\uC815 \uAC00\uC774\uB4DC
RecommendedTLCSettings=\uCD94\uCC9C TLC \uC124\uC815
PennStateShakerBoxForageResults=\uD39C\uC2A4\uD14C\uC774\uD2B8 \uC785\uC790\uB3C4 \uBD84\uC11D\uAE30 \uAC80\uC0AC \uACB0\uACFC
FieldKPTest=\uD604\uC7A5 Kernel Hardness \uD14C\uC2A4\uD2B8 \uACB0\uACFC
AdjustingKPtoAssureSuccess=\uC131\uACF5 \uD655\uC2E0\uC744 \uC704\uD55C KP \uC870\uC808
HowtoGetBetterKPResults=\uB0B3\uC740 KP\uAC12\uC744 \uC5BB\uB294 \uBC29\uBC95?
InoculantFQAs=\uC0AC\uC77C\uB9AC\uC9C0 \uC774\uB178\uD058\uB7F0\uD2B8 FAQ
ChoosingtheCorrectAdditive=\uC801\uC808\uD55C \uCCA8\uAC00\uC81C \uACE0\uB974\uAE30
PhotoExamples=\uC0AC\uC9C4 \uC608\uC2DC
Porosity=\uC0AC\uC77C\uB9AC\uC9C0 \uB2E4\uACF5\uC131(Porosity)
ThreePotentialStorageSolutions=\uC138\uAC00\uC9C0 \uC7A0\uC7AC\uC801 \uBCF4\uAD00 \uBC29\uBC95
FeedOutRatesFilmsStorageSysExamined=\uAE09\uC5EC \uC18D\uB3C4, \uD544\uB984 \uBC0F \uBCF4\uAD00 \uC2DC\uC2A4\uD15C \uC810\uAC80
ManagingForageinTowerSilos=\uD0C0\uC6CC \uC0AC\uC77C\uB85C\uB0B4 \uC870\uC0AC\uB8CC \uAD00\uB9AC
BaggedConventionalSilage=\uBC31\uC5D0 \uB123\uC5B4 \uBCF4\uAD00\uD558\uB294 \uC0AC\uC77C\uB9AC\uC9C0
ManagingForageinSiloBags=\uC0AC\uC77C\uB85C \uBC31\uB0B4 \uC870\uC0AC\uB8CC \uAD00\uB9AC
DensityLossesinPressedBagSilos=\uC555\uCD95\uB41C \uC0AC\uC77C\uB85C\uBC31\uB0B4 \uBC00\uB3C4 \uBC0F \uC190\uC2E4
BaleageFQAs=\uACE4\uD3EC \uC0AC\uC77C\uB9AC\uC9C0 FAQs
CornSilageKernel=\uACE4\uD3EC \uC0AC\uC77C\uB9AC\uC9C0 FAQs
ScorecardPrompt={0} \uC2A4\uCF54\uC5B4\uCE74\uB4DC\uB97C \uBCF4\uAE30 \uC704\uD574\uC11C\uB294 \uB2E4\uC74C \uC870\uC0AC\uB97C \uC644\uB8CC\uD558\uC138\uC694
PromptOK=\uC2B9\uC778
PromptCancel=\uCDE8\uC18C
StatusInProgress=\uC9C4\uD589\uC911
StatusCompleted=\uC644\uB8CC
Resources=\uCC38\uC870
SiteDetailViewModel.Resources=\uCD9C\uCC98
Answers=\uC751\uB2F5
SurveyCategories=\uC870\uC0AC \uBC94\uC8FC
NotebookSectionForageAuditLanding=\uC870\uC0AC\uB8CC \uAC80\uC0AC
NotebookSectionForageAudit=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
NotebookSectionForageAuditBaleage=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uACE4\uD3EC \uC0AC\uC77C\uB9AC\uC9C0
NotebookSectionForageAuditBunkersPiles=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uB354\uBBF8
NotebookSectionForageAuditHarvest=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uC218\uD655
NotebookSectionForageAuditMaintainingQuality=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uC870\uC0AC\uB8CC \uD488\uC9C8 \uC720\uC9C0
NotebookSectionForageAuditSilageBags=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uC0AC\uC77C\uB9AC\uC9C0 \uBC31
NotebookSectionForageAuditSurveyOfForages=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uC870\uC0AC\uB8CC \uC870\uC0AC
NotebookSectionForageAuditTowerSilos=\uC870\uC0AC\uB8CC \uAC80\uC0AC - \uD0C0\uC6CC \uC0AC\uC77C\uB85C
NotebookSectionComfortTools=\uC816\uC18C \uC548\uB77D\uD568 \uD3C9\uAC00 \uB3C4\uAD6C
NotebookSectionHeatstressCalculations=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4-\uACC4\uC0B0
NotebookSectionHeatstressChart=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4-\uCC28\uD2B8
NotebookSectionHeatstressData=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4-\uB370\uC774\uD130
NotebookSectionNutritionTools=\uC601\uC591 \uC810\uAC80 \uB3C4\uAD6C
NotebookSectionVisit=\uBC29\uBB38
NotebookWalkthroughReportLanding=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
NotebookManureScoreHerdAnalysisInputs=\uBD84\uBCC0\uC9C0\uC218 (\uC6B0\uAD70) - \uC785\uB825
NotebookManureScoreHerdAnalysisGoals=\uBD84\uBCC0\uC9C0\uC218 (\uC6B0\uAD70) - \uBAA9\uD45C
NotebookManureScoreHerdAnalysisResults=\uBD84\uBCC0\uC9C0\uC218 (\uC6B0\uAD70) - \uACB0\uACFC
NotebookMetabolicIncidenceInputs=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 \uC785\uB825
NotebookMetabolicIncidenceOutputs=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 \uC0B0\uCD9C
NotebookMetabolicIncidenceCharts=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 \uCC28\uD2B8
NotebookPenTimePenSelection=\uC6B0\uC0AC \uB300\uAE30 \uC2DC\uAC04 \uACC4\uD68D - \uC6B0\uC0AC \uC120\uD0DD
NotebookPenTimeInputs=\uC6B0\uC0AC \uB300\uAE30 \uC2DC\uAC04 \uACC4\uD68D - \uC785\uB825
NotebookPenTimeComparison=\uC6B0\uC0AC \uB300\uAE30 \uC2DC\uAC04 \uACC4\uD68D - \uBE44\uAD50
NotebookPenTimeResults=\uC6B0\uC0AC \uB300\uAE30 \uC2DC\uAC04 \uACC4\uD68D - \uACB0\uACFC
NotebookWalkthroughReport=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
VisitViewModel.ToolCategories=\uB3C4\uAD6C \uCE74\uD14C\uACE0\uB9AC
PileAndBunkerCapacity=\uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uB354\uBBF8 \uC6A9\uB7C9
PileCapacity=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uC6A9\uB7C9
PileFeedOutRate=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uAE09\uC5EC\uB7C9
BunkerCapacity=\uC0AC\uC77C\uB9AC\uC9C0 \uBC99\uCEE4 \uC6A9\uB7C9
BunkerFeedOutRate=\uC0AC\uC77C\uB9AC\uC9C0 \uBC99\uCEE4 \uAE09\uC5EC\uB7C9
NotebookRumenHealthTMRParticleScore=TMR \uC785\uC790\uB3C4 \uC9C0\uC218
NotebookRumenHealthTMRParticlePercent=\uC785\uC790\uB3C4 \uC2A4\uD06C\uB9B0\uC5D0 \uAC78\uB9B0 TMR \uBE44\uC728
NotebookLocomotionLanding=\uBCF4\uD589\uC9C0\uC218 - \uBA54\uC778
NotebookLocomotionPenSelection=\uBCF4\uD589\uC9C0\uC218 - \uC6B0\uC0AC \uC120\uD0DD
NotebookLocomotionPenInputs=\uBCF4\uD589\uC9C0\uC218 - \uC6B0\uC0AC \uC785\uB825
NotebookLocomotionPenResults=\uBCF4\uD589\uC9C0\uC218 - \uC6B0\uC0AC \uACB0\uACFC
NotebookLocomotionHerdInputs=\uBCF4\uD589\uC9C0\uC218 - \uC6B0\uAD70\uBD84\uC11D \uC785\uB825
NotebookLocomotionHerdRevenue=\uBCF4\uD589\uC9C0\uC218 - \uC6B0\uAD70\uBD84\uC11D \uC218\uC775
NotebookLocomotionHerdResults=\uBCF4\uD589\uC9C0\uC218 - \uC6B0\uAD70\uBD84\uC11D \uACB0\uACFC
NotebookCudCalculators=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uB418\uC0C8\uAE40\uC9C8 \uACC4\uC0B0\uAE30
NotebookCudChewingDataEntry=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uB418\uC0C8\uAE40\uC9C8 \uC218
NotebookCudChewingResults=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uB418\uC0C8\uAE40\uC9C8 \uACB0\uACFC
NotebookCudChewing=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uB418\uC0C8\uAE40\uC9C8 \uC6B0\uC0AC \uC120\uD0DD
NotebookRumenHealthNumberOfChewsInput=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uC800\uC791 \uAC1C\uCCB4 \uC218 \uC785\uB825
NotebookRumenHealthNumberOfChewsResults=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uC800\uC791 \uAC1C\uCCB4 \uC218 \uACB0\uACFC
NotebookParticleScoreLanding=TMR \uC785\uC790\uB3C4 \uC9C0\uC218
NotebookParticleScoreSelectPen=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 - \uC6B0\uC0AC \uC120\uD0DD
NotebookParticleScoreSelectScorer=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 - Scorer \uC120\uD0DD
NotebookLocomotionEditTable=\uBCF4\uD589\uC9C0\uC218 - \uCC29\uC720\uB450\uC218
NotebookManureScoreLanding=\uBD84\uBCC0\uC9C0\uC218
NotebookManurePenSelection=\uBD84\uBCC0\uC9C0\uC218 - \uC6B0\uC0AC \uC120\uD0DD
NotebookVisitSummary=\uBC29\uBB38\uC694\uC57D
NotebookTMRParticleHerdAnalysisPenInputs=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 \uC6B0\uAD70\uBD84\uC11D - \uC785\uB825
NotebookTMRParticleHerdAnalysisPenResults=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 \uC6B0\uAD70\uBD84\uC11D - \uACB0\uACFC
NotebookParticleScoreHerdAnalysisEdit=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 \uC6B0\uAD70 \uBD84\uC11D - \uBE44\uC720\uC77C\uB839 \uD3B8\uC9D1
NotebookWalkthroughReportPen=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C - \uC6B0\uC0AC \uBD84\uC11D
Overall=\uC804\uCCB4\uC801\uC778 \uC751\uB2F5
ResourcesViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
VisitViewModel.Instructions=\uC544\uB798\uC758 \uB9AC\uC2A4\uD2B8\uC5D0\uC11C \uCE74\uD14C\uACE0\uB9AC\uB098 \uBCF4\uACE0\uC11C\uB97C \uC120\uD0DD\uD574 \uC8FC\uC2ED\uC2DC\uC624.
SiteDetailsSetupViewModel.Title=\uC0AC\uC774\uD2B8 \uC138\uBD80\uC815\uBCF4
SiteDetailsSetupViewModel.Pens=\uC6B0\uC0AC
SiteDetailsSetupViewModel.Diets=\uB2E4\uC774\uC5B4\uD2B8
SiteDetailsSetupViewModel.PenSetup=\uC6B0\uC0AC \uC124\uC815
SiteDetailsSetupViewModel.DietSetup=\uB2E4\uC774\uC5B4\uD2B8 \uC124\uC815
SiteDetailsSetupViewModel.SiteName=\uC0AC\uC774\uD2B8 \uC774\uB984
SiteDetailsSetupViewModel.Continue=\uACC4\uC18D
Continue=\uACC4\uC18D
SiteDetailsSetupViewModel.CurrentMilkPrice=\uD604\uC7AC \uC720\uB300 ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=\uBE44\uC720\uC77C\uB839 (DIM)
SiteDetailsSetupViewModel.DryMatterIntake=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 (DMI) ({0})
SiteDetailsSetupViewModel.MilkYield=\uC6B0\uC720 \uC0DD\uC0B0\uB7C9 ({0})
SiteDetailsSetupViewModel.AsFedIntake=\uC6D0\uBB3C \uC12D\uCDE8\uB7C9 ({0})
SiteDetailsSetupViewModel.LactatingAnimals=\uCC29\uC720\uC6B0
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=\uB099\uB18D \uBE44\uC720 \uC815\uBBF8\uC5D0\uB108\uC9C0 (NEL Dairy)
SiteDetailsSetupViewModel.MilkingSystem=\uCC29\uC720 \uC2DC\uC2A4\uD15C
SiteDetailsSetupViewModel.MilkFatPercent=\uC720\uC9C0\uBC29 %
SiteDetailsSetupViewModel.MilkProteinPercent=\uC720\uB2E8\uBC31 %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=\uC6B0\uC720 \uAE30\uD0C0 \uACE0\uD615\uBD84 %
SiteDetailsSetupViewModel.SomaticCellCount=\uCCB4\uC138\uD3EC\uC218 (1,000 cells/ml)
SiteDetailsSetupViewModel.BacteriaCellCount=\uC138\uADE0\uC218 (1,000 cfu/ml)
SiteDetailsSetupViewModel.RationCost=\uC0AC\uB8CC\uBE44 ({0})
SiteDetailsSetupViewModel.NewSite=\uC0C8\uB85C\uC6B4 \uC0AC\uC774\uD2B8
SiteDetailsSetupViewModel.NullSiteName=\uC0AC\uC774\uD2B8 \uC774\uB984, \uC720\uB300, \uCC29\uC720 \uC2DC\uC2A4\uD15C \uBC0F \uC6B0\uC0AC\uB294 \uAF2D \uC785\uB825\uD574\uC57C \uD558\uB294 \uD56D\uBAA9\uC785\uB2C8\uB2E4. \uACC4\uC18D \uC9C4\uD589\uD560\uAE4C\uC694? \uB610\uB294 \uC0AC\uC774\uD2B8\uB97C \uC0AD\uC81C\uD560\uAE4C\uC694?
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=\uC77C\uBC18 \uACE0\uAC1D \uC0AC\uC774\uD2B8 \uC124\uC815
SiteDetailsSetupViewModel.AnimalInputsSite=\uAC1C\uCCB4 \uC785\uB825, \uC0AC\uC774\uD2B8
SiteDetailsSetupViewModel.DietInputsSiteLactating=\uB2E4\uC774\uC5B4\uD2B8 \uC785\uB825, \uC0AC\uC774\uD2B8 (\uCC29\uC720\uC6B0)
SiteDetailsSetupViewModel.NameNotUnique={0}\uC774\uB77C\uB294 \uC774\uB984\uC758 \uC0AC\uC774\uD2B8\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uC774\uB984\uC740 \uACE0\uC720\uD574\uC57C \uD569\uB2C8\uB2E4.
SiteDetailsSetupViewModel.SiteSetup=\uC0AC\uC774\uD2B8 \uC124\uC815
SiteDetailsSetupViewModel.InfoSiteSetup=\uACE0\uAC1D \uBAA9\uC7A5\uBCC4\uB85C \uC0C8\uB85C\uC6B4 \uC0AC\uC774\uD2B8\uB97C \uCD94\uAC00 \uD558\uC2ED\uC2DC\uC694. \uCD5C\uC18C\uD55C 1\uAC1C\uC758 \uC0AC\uC774\uD2B8\uAC00 \uB4F1\uB85D\uB418\uC5B4 \uC788\uC5B4\uC57C \uC0AC\uC774\uD2B8 \uBC29\uBB38\uACFC \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820 \uAE30\uB2A5\uC744 \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uC0C8\uB85C\uC6B4 \uC0AC\uC774\uD2B8\uB97C \uC0DD\uC131\uD560 \uB54C \uD544\uC694\uD55C \uC815\uBCF4\uB294 \uC0AC\uC774\uD2B8 \uC774\uB984, \uD604\uC7AC \uC720\uB300\uAC00\uACA9, \uCC29\uC720 \uC2DC\uC124 \uBC0F \uC6B0\uAD70 (\uC6B0\uAD70 \uC124\uC815 \uD074\uB9AD) \uC815\uBCF4\uAC00 \uD544\uC694\uD569\uB2C8\uB2E4. \uC6B0\uAD70 \uAD00\uB828 \uC815\uBCF4\uB97C \uD65C\uC6A9\uD558\uB294 \uB3C4\uAD6C\uC640 \uBAA9\uC7A5\uC810\uAC80 \uB9AC\uD3EC\uD2B8\uB97C \uC0AC\uC6A9\uD558\uAE30 \uC704\uD574\uC11C\uB294 \uC6B0\uAD70\uC774 \uC785\uB825\uB418\uC5B4\uC57C \uD569\uB2C8\uB2E4. \uC774 \uD398\uC774\uC9C0\uC5D0\uC11C \uD2B9\uC815 \uC0AC\uC774\uD2B8\uC5D0\uB9CC \uD544\uC694\uD55C \uC815\uBCF4\uB97C \uCD94\uAC00\uD558\uAC70\uB098 \uC5C5\uB370\uC774\uD2B8 \uD558\uC2ED\uC2DC\uC694. \uC0AC\uC6A9 \uC911\uC778 \uB3C4\uAD6C\uB0B4\uC5D0\uC11C \uC815\uBCF4\uB97C \uCD94\uAC00\uD558\uAC70\uB098 \uC5C5\uB370\uC774\uD2B8 \uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uC0AC\uC774\uD2B8 \uC124\uC815 \uC815\uBCF4\uB294 \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820\uC774 \uBAA9\uC7A5 \uC815\uBCF4\uB97C \uB2E4\uC6B4\uB85C\uB4DC\uD560 \uB54C \uC790\uB3D9\uC73C\uB85C \uC5C5\uB370\uC774\uD2B8 \uB429\uB2C8\uB2E4.
SiteDetailsSetupViewModel.WeightImperialCWT=CWT(100\uD30C\uC6B4\uB4DC)
SiteDetailsSetupViewModel.Delete=\uC0AD\uC81C
Parlor=\uCC29\uC720\uC2E4
Robot=\uB85C\uBD07
Other=\uAE30\uD0C0
SelectMilkingSystemViewModel.Title=\uC0AC\uC774\uD2B8 \uC124\uC815
SelectMilkingSystemViewModel.SelectMilkingSystem=\uCC29\uC720 \uC2DC\uC2A4\uD15C \uC120\uD0DD
SelectMilkingSystemViewModel.NoneSelected=\uC120\uD0DD\uC548\uD568
DietListViewModel.NewDiet=\uC0C8\uB85C\uC6B4 \uB2E4\uC774\uC5B4\uD2B8 \uCD94\uAC00
DietListViewModel.MainHeading=\uB2E4\uC774\uC5B4\uD2B8
DietListViewModel.InfoNewDiet=\uB9E5\uC2A4 \uC2DC\uC2A4\uD15C\uC774 \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820\uC758 \uBAA9\uC7A5 \uC0AC\uC774\uD2B8\uC640 \uC5F0\uACB0\uB418\uBA74, \uB2E4\uC774\uC5B4\uD2B8 \uC774\uB984\uC740 \uC790\uB3D9\uC73C\uB85C \uC5C5\uB370\uC774\uD2B8 \uB429\uB2C8\uB2E4.
\uADF8\uB807\uC9C0 \uC54A\uC744 \uACBD\uC6B0, \uB2E4\uC774\uC5B4\uD2B8\uB97C \uC218\uB3D9\uC73C\uB85C \uC785\uB825\uD558\uAC70\uB098 \uBE48\uCE78\uC73C\uB85C \uB0A8\uACA8\uB450\uACE0 \uAC01\uC6B0\uAD70\uBCC4\uB85C \uC815\uD655\uD55C Animal Class / Subclass\uB97C \uC120\uD0DD\uD558\uC2ED\uB2C8\uB2E4. \uC131\uACF5\uC801\uC73C\uB85C \uB2E4\uC774\uC5B4\uD2B8\uAC00 \uC0DD\uC131\uB418\uBA74 \uAC01 \uB2E4\uC774\uC5B4\uD2B8\uBCC4\uB85C \uC815\uD655\uD55C Animal Class / Subclass\uB97C \uC120\uD0DD\uD558\uC2ED\uB2C8\uB2E4.
DietListViewModel.New=\uC2E0\uADDC
NutritionViewModel.NutritionToolsInstructions=\uBC29\uBB38\uC744 \uC2DC\uC791\uD558\uC2DC\uB824\uBA74 \uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uB3C4\uAD6C\uB97C \uC120\uD0DD\uD574 \uC8FC\uC2ED\uC2DC\uC624.
NutritionViewModel.NutritionToolsCaption=\uB3C4\uAD6C
ForageScorecardViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
ForageScorecardViewModel.ForageAuditScorecard=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
PileAndBunkerCapacityViewModel.PileBunkerCapacities=\uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uB354\uBBF8 \uC6A9\uB7C9
AddPile=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uCD94\uAC00
AddBunker=\uC0AC\uC77C\uB85C \uBC99\uCEE4 \uCD94\uAC00
PileAndBunkerCapacityViewModel.Pile=\uC870\uC0AC\uB8CC \uB354\uBBF8
Pile=\uC870\uC0AC\uB8CC \uB354\uBBF8
PileAndBunkerCapacityViewModel.Bunker=\uC0AC\uC77C\uB85C \uBC99\uCEE4
PileAndBunkerCapacityViewModel.NameNotUnique=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uB610\uB294 \uBC99\uCEE4 \uC774\uB984 "{0}"\uC774 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uC0AC\uC6A9\uD558\uC9C0 \uC54A\uC740 \uC774\uB984\uC744 \uC9C0\uC815\uD574 \uC8FC\uC2ED\uC2DC\uC624.
PileAndBunkerCapacityViewModel.NameTooLong=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uB610\uB294 \uBC99\uCEE4 \uC774\uB984\uC740 40\uC790 \uC774\uD558\uC5EC\uC57C \uD569\uB2C8\uB2E4.
Piles=\uC870\uC0AC\uB8CC \uB354\uBBF8
Bunkers=\uBC99\uCEE4
NewPile=\uC0C8\uB85C\uC6B4 \uC870\uC0AC\uB8CC \uB354\uBBF8 \uC774\uB984\uC744 \uC9C0\uC815\uD574 \uC8FC\uC2ED\uC2DC\uC624.
NewBunker=\uC0C8\uB85C\uC6B4 \uBC99\uCEE4 \uC774\uB984\uC744 \uC9C0\uC815\uD574 \uC8FC\uC2ED\uC2DC\uC624
NewDietViewModel.Title=\uC0C8\uB85C\uC6B4 \uB2E4\uC774\uC5B4\uD2B8
ClassSubClass=\uAC00\uCD95 \uD074\uB798\uC2A4 / \uC11C\uBE0C \uD074\uB798\uC2A4
LinkToPens=\uB2E4\uB978 \uC6B0\uAD70 \uC5F0\uACB0 (Optional)
NewDietViewModel.MainHeading=\uB2E4\uC774\uC5B4\uD2B8 \uC774\uB984 \u20F0
NewDietViewModel.Save=\uC800\uC7A5
NewDietViewModel.Cancel=\uCDE8\uC18C
DietListViewModel.Title=\uB2E4\uC774\uC5B4\uD2B8
NewDietClassViewModel.Title=\uAC00\uCD95 \uD074\uB798\uC2A4 / \uC11C\uBE0C \uD074\uB798\uC2A4
Lactating=\uBE44\uC720
Dry=\uAC74\uC720
Heifer=\uD070\uC1A1\uC544\uC9C0
Calf=\uC1A1\uC544\uC9C0
Male=\uC22B\uC18C
Fresh=\uBD84\uB9CC\uC6B0
Milking=\uCC29\uC720
LowForage=\uD55C\uAD6D\uD615 \uC870\uC0AC\uB8CC \uD658\uACBD
Pasture=\uBC29\uBAA9
FreshHeifer=\uBE44\uC720\uCD08\uAE30 \uCD08\uC0B0\uC6B0
AAEfficiency=\uC544\uBBF8\uB178\uC0B0 \uD6A8\uC728
CloseUp=\uAC74\uC720\uB9D0\uAE30
FarOff=\uAC74\uC720\uCD08\uAE30
CloseUpHeifer=\uAC74\uC720\uB9D0\uAE30 \uCD08\uC0B0\uC6B0
ShortDryPeriod=\uB2E8\uAE30 \uAC74\uC720
Bull=\uBE44\uAC70\uC138\uC6B0
Steer=\uAC70\uC138\uC6B0
Max=\uCD5C\uB300\uAC12
PenListViewModel.DietSetup=\uB2E4\uC774\uC5B4\uD2B8 \uC124\uC815
UserCreated=\uC0AC\uC6A9\uC790 \uC0DD\uC131
DietDetailViewModel.DDW=\uBAA9\uC7A5 \uB370\uC774\uD130
DDW=\uB18D\uC7A5 \uC815\uBCF4
DDWUpdatedTime=\uCD5C\uC885 \uC5C5\uB370\uC774\uD2B8\uB41C \uC6B0\uAD70 \uC790\uB8CC: {0}
PenListViewModel.MainHeading=\uC6B0\uAD70
PenListViewModel.Title=\uC6B0\uC0AC
PenListViewModel.NewPen=\uC0C8\uB85C\uC6B4 \uC6B0\uC0AC \uCD94\uAC00
PenListViewModel.InfoPenList=\uC0AC\uC774\uD2B8 \uBC29\uBB38 \uC911 \uC6B0\uAD70 \uAD00\uB828 \uC815\uBCF4\uB97C \uD65C\uC6A9\uD558\uB294 \uB3C4\uAD6C\uC640 \uBAA9\uC7A5\uC810\uAC80 \uB9AC\uD3EC\uD2B8\uB97C \uC0AC\uC6A9\uD558\uAE30 \uC704\uD574\uC11C\uB294 \uC6B0\uAD70\uC774 \uC785\uB825\uB418\uC5B4\uC57C \uD569\uB2C8\uB2E4. \uC6B0\uAD70 \uC774\uB984\uACFC \uB370\uC774\uD130\uB294 \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820\uC774 \uBAA9\uC7A5 \uB370\uC774\uD130\uB97C \uB2E4\uC6B4\uB85C\uB4DC\uD560 \uB54C \uC790\uB3D9\uC73C\uB85C \uC5C5\uB370\uC774\uD2B8 \uB429\uB2C8\uB2E4. \uB18D\uC7A5 \uC815\uBCF4 \uB2E4\uC6B4\uB85C\uB4DC\uAC00 \uC124\uC815\uB418\uC9C0 \uC54A\uC744 \uACBD\uC6B0 \uC218\uB3D9\uC73C\uB85C \uC6B0\uAD70\uC744 \uCD94\uAC00\uD558\uC2ED\uC2DC\uC624.
\uB9E5\uC2A4(\uC635\uD2F8\uB77D) \uC2DC\uC2A4\uD15C\uC774 \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820\uC758 \uBAA9\uC7A5 \uC0AC\uC774\uD2B8\uC640 \uC5F0\uACB0\uB418\uBA74, \uB2E4\uC774\uC5B4\uD2B8 \uC774\uB984\uC740 \uC790\uB3D9\uC73C\uB85C \uC5C5\uB370\uC774\uD2B8 \uB429\uB2C8\uB2E4. \uC815\uD655\uD55C \uBCF4\uACE0\uC11C\uB97C \uC791\uC131\uD558\uAE30 \uC704\uD574\uC11C, \uB2E4\uC774\uC5B4\uD2B8 \uC774\uB984\uC744 \uB20C\uB7EC \uB2E4\uC774\uC5B4\uD2B8 \uC5F0\uACB0\uC744 \uC5C5\uB370\uC774\uD2B8 \uD558\uC2ED\uC2DC\uC694. \uB9E5\uC2A4 \uC2DC\uC2A4\uD15C\uACFC \uBAA9\uC7A5\uC758 \uC5F0\uACB0\uC774 \uC548\uB418\uC5B4 \uC788\uB294 \uACBD\uC6B0 \uC218\uB3D9\uC73C\uB85C \uB2E4\uC774\uC5B4\uD2B8 \uB610\uB294 \uC815\uD655\uD55C Animal class / Subclass\uB97C \uC120\uD0DD \uD558\uC2ED\uC2DC\uC694.
\uBAA9\uC7A5\uC758 \uC6B0\uAD70 \uC218\uC900 \uC815\uBCF4\uB97C \uC5C5\uB370\uC774\uD2B8 \uD558\uAE30 \uC704\uD574 \uC6B0\uAD70 \uC774\uB984\uC744 \uB204\uB974\uC2ED\uC2DC\uC694.
NewPenViewModel.Save=\uC800\uC7A5
NewPenViewModel.InfoNewPenDetails=\uC774 \uD398\uC774\uC9C0\uC5D0\uC11C \uC6B0\uAD70 \uAD00\uB828 \uB370\uC774\uD130\uB97C \uCD94\uAC00\uD558\uAC70\uB098 \uC5C5\uB370\uC774\uD2B8 \uD558\uC2ED\uC2DC\uC694. \uC0AC\uC6A9 \uC911\uC778 \uB3C4\uAD6C\uB0B4\uC5D0\uC11C \uC815\uBCF4\uB97C \uCD94\uAC00\uD558\uAC70\uB098 \uC5C5\uB370\uC774\uD2B8 \uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
\uC0C8\uB85C\uC6B4 \uC6B0\uAD70\uC744 \uC791\uC131\uD560 \uB54C \uC6B0\uAD70 \uC774\uB984, \uB2E4\uC774\uC5B4\uD2B8, \uC6B0\uC0AC \uAD6C\uC870 \uBC0F \uC0AC\uB8CC\uAE09\uC5EC \uC2DC\uC2A4\uD15C\uC5D0 \uB300\uD55C \uC815\uBCF4\uAC00 \uD544\uC694\uD569\uB2C8\uB2E4.
\uC6B0\uAD70 \uC124\uC815 \uC815\uBCF4\uB294 \uB370\uC5B4\uB9AC \uC5D4\uD154\uB9AC\uC820\uC774 \uB18D\uC7A5 \uC790\uB8CC\uB97C \uB2E4\uC6B4\uB85C\uB4DC\uD560 \uB54C \uC790\uB3D9\uC73C\uB85C \uC5C5\uB370\uC774\uD2B8 \uB429\uB2C8\uB2E4.
NewPenViewModel.Cancel=\uCDE8\uC18C
NewPenViewModel.Title=\uC0C8\uB85C\uC6B4 \uC6B0\uC0AC
NewPenViewModel.PenDetail=\uC6B0\uC0AC \uC138\uBD80\uC0AC\uD56D
Diet=\uB2E4\uC774\uC5B4\uD2B8
NewPenViewModel.Diet=\uB2E4\uC774\uC5B4\uD2B8
NewPenViewModel.PenName=\uC6B0\uC0AC \uC774\uB984
NewPenViewModel.Barn=\uCC3D\uACE0 \uC774\uB984
NewPenViewModel.HousingSystem=\uC6B0\uC0AC \uC2DC\uC2A4\uD15C
NewPenViewModel.NumberOfStalls=\uC2A4\uD1A8 \uC22B\uC790
NewPenViewModel.FeedingSystem=\uAE09\uC5EC \uC2DC\uC2A4\uD15C
NewPenViewModel.MilkingFrequency=\uCC29\uC720 \uD69F\uC218
NewPenViewModel.Animals=\uC6B0\uC0AC\uB2F9 \uAC1C\uCCB4\uC218
NewPenViewModel.DaysInMilk=\uBE44\uC720\uC77C\uB839 (DIM)
NewPenViewModel.Milk=\uC6B0\uC720 \uC0DD\uC0B0\uB7C9 ({0})
NewPenViewModel.DryMatterIntake=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 (DMI) ({0})
NewPenViewModel.AsFedIntake=\uC6D0\uBB3C\uC12D\uCDE8\uB7C9 ({0})
NewPenViewModel.RationCostPerAnimal=\uB450\uB2F9 \uC0AC\uB8CC\uBE44\uC6A9 ({0})
NewPenViewModel.General=\uC77C\uBC18\uC815\uBCF4
General=\uC77C\uBC18\uC815\uBCF4
NewPenViewModel.AnimalsInputsPen=\uAC1C\uCCB4 \uC785\uB825, \uC6B0\uC0AC
NewPenViewModel.DietInputsPen=\uB2E4\uC774\uC5B4\uD2B8 \uC785\uB825, \uC6B0\uC0AC
SelectHousingSystemViewModel.Title=\uC6B0\uC0AC \uC124\uC815
SelectHousingSystemViewModel.SelectHousingSystem=\uC6B0\uC0AC \uC2DC\uC2A4\uD15C \uC120\uD0DD
Freestall=\uD504\uB9AC\uC2A4\uD1A8
Tiestall=\uD0C0\uC774\uC2A4\uD1A8
DryLot=\uB9C8\uB978 \uC6B4\uB3D9\uC7A5
BeddedPack=\uAE54\uC9D1 \uBC14\uB2E5
PastureOther=\uBC29\uBAA9 + \uAE30\uD0C0
SelectFeedingSystemViewModel.SelectFeedingSystem=\uAE09\uC5EC \uC2DC\uC2A4\uD15C \uC120\uD0DD
SelectFeedingSystemViewModel.Title=\uC6B0\uC0AC \uC124\uC815
TMR=TMR
PMRConcentrate=PMR + \uB18D\uCD95\uC0AC\uB8CC
Component=\uAD6C\uC131\uC694\uC18C
SelectPen=\uC0C8\uB85C\uC6B4 \uC6B0\uC0AC\uB97C \uC704\uD55C \uB2E4\uC774\uC5B4\uD2B8\uB97C \uC120\uD0DD\uD558\uC138\uC694
NewPenDietViewModel.Title=\uB2E4\uC774\uC5B4\uD2B8
NewPenDietViewModel.New=\uC2E0\uADDC
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=\uC810\uC218
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=\uC751\uB2F5
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=\uAC1C\uC120 \uC0AC\uD56D
FillAllFields=\uBAA8\uB4E0 \uC785\uB825\uB780\uC744 \uC791\uC131\uD558\uC2ED\uC2DC\uC624
FillAllMandatoryFields=\uBAA8\uB4E0 \uD544\uC218 \uC785\uB825\uB780\uC744 \uC791\uC131\uD558\uC2ED\uC2DC\uC624.
NewDietPensViewModel.Title=\uC6B0\uC0AC\uB85C \uC804\uD658
ComfortToolsViewModel.Title=\uC548\uB77D\uD568 \uB3C4\uAD6C
NutritionViewModel.Title=\uC601\uC591 \uB3C4\uAD6C
VisitViewModel.Title=\uBC29\uBB38 \uC0C1\uC138\uC815\uBCF4
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=\uC0AC\uB8CC \uAE09\uC5EC\uB7C9 \uC815\uBCF4
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=\uB450/\uC77C \uC694\uAD6C\uB7C9
PileAndBunkerResultsFeedOutViewModel.FeedingRate=\uAE09\uC5EC\uB7C9 (\uC6D0\uBB3C/\uB450)
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=\uC0AC\uB8CC \uAE09\uC5EC \uB450\uC218
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kgs. DM/1 \uBBF8\uD130
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Lbs. DM/1 \uD53C\uD2B8
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=\uAE09\uC5EC\uC0AC\uB8CC \uD45C\uBA74\uC801 (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=\uAE09\uC5EC\uC0AC\uB8CC \uD45C\uBA74\uC801 (ft^2)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=Cm/\uC77C
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=In./\uC77C
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=\uC77C\uC77C 3in.
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=\uC77C\uC77C 7cm
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=\uC77C\uC77C 6in.
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=\uC77C\uC77C 15cm
PileAndBunkerResultsFeedOutViewModel.Title=\uAE09\uC5EC\uB7C9
PileAndBunkerResultsCapacityInputViewModel.TopWidth=\uC0C1\uB2E8 \uB108\uBE44 ({0})
PileAndBunkerResultsCapacityInputViewModel.Height=\uB192\uC774 ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=\uBC14\uB2E5 \uB108\uBE44 ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomLength=\uBC14\uB2E5 \uAE38\uC774 ({0})
PileAndBunkerResultsCapacityInputViewModel.TopLength=\uC0C1\uB2E8 \uAE38\uC774 ({0})
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=\uAC74\uBB3C %
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=\uC0AC\uC77C\uB9AC\uC9C0 \uAC74\uBB3C \uBC00\uB3C4 ({0})
PileAndBunkerResultsCapacityInputViewModel.TonsDM=TONS \uAC74\uBB3C
PileAndBunkerResultsCapacityInputViewModel.TonsAF=TONS \uC6D0\uBB3C
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=\uBBF8\uD130 \uD1A4 \uAC74\uBB3C
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=\uBBF8\uD130 \uD1A4 \uC6D0\uBB3C
NewDietPensViewModel.AssociatePens=\uC774 \uC0AC\uB8CC\uB97C \uB2E4\uB978 \uC6B0\uC0AC\uC5D0 \uC801\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
DietDetailViewModel.Title=\uB2E4\uC774\uC5B4\uD2B8 \uC138\uBD80\uC815\uBCF4
DietDetailViewModel.Created=\uC0DD\uC131
DietDetailViewModel.UserCreated=\uC0AC\uC6A9\uC790\uBCC4
DietDetailViewModel.Max=\uCD5C\uB300\uAC12
SystemGenerated=\uC2DC\uC2A4\uD15C \uC0DD\uC131
DietDetailViewModel.SystemGenerated=\uC2DC\uC2A4\uD15C \uC0DD\uC131
Pens=\uC6B0\uC0AC
Created=\uC0DD\uC131
PenName=\uC6B0\uC0AC \uC774\uB984
PenDetailViewModel.Title=\uC6B0\uC0AC \uC138\uBD80\uC0AC\uD56D
CreateDuplicateDiet=\uD574\uB2F9 \uAC00\uCD95 \uB2E8\uACC4\uC5D0 \uB2E4\uC774\uC5B4\uD2B8\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uC0C8\uB85C \uB9CC\uB4E4\uAE4C\uC694?
DuplicatePenName=\uD574\uB2F9 \uC774\uB984\uC758 \uC6B0\uC0AC\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uC0C8\uB85C\uC6B4 \uC774\uB984\uC744 \uC120\uD0DD\uD558\uC138\uC694.
LoginViewModel.Title=\uB85C\uADF8\uC778
VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
ForageAuditScorecardResultsViewModel.Title=\uD0C0\uC6CC \uC0AC\uC77C\uB85C
VisitNotesViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
EditNoteViewModel.TitleLabel=\uC81C\uBAA9
EditNoteViewModel.NoteLabel=\uB178\uD2B8
EditNoteViewModel.Delete=\uC0AD\uC81C
PileAndBunkerCapacityViewModel.Title=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uBC0F \uBC99\uCEE4 \uC6A9\uB7C9
PileAndBunkerCapacityViewModel.AddBunker=\uBC99\uCEE4 \uCD94\uAC00
PileAndBunkerCapacityViewModel.AddPile=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uCD94\uAC00
PileAndBunkerCapacityViewModel.Bunkers=\uBC99\uCEE4
PileAndBunkerCapacityViewModel.Piles=\uC870\uC0AC\uB8CC \uB354\uBBF8
SettingsViewModel.Milk_Processor_Set_Up=\uC720\uC5C5\uCCB4 \uC124\uC815
SettingsViewModel.Metric=\uBBF8\uD130\uBC95
SettingsViewModel.Imperial=\uC57C\uB4DC\uD30C\uC6B4\uB4DC\uBC95
SettingsViewModel.Select_Unit_Of_Measure=\uCE21\uC815 \uB2E8\uC704 \uC120\uD0DD
SettingsViewModel.More_Settings=\uAE30\uD0C0 \uC124\uC815
User_Settings=\uC0AC\uC6A9\uC790 \uC138\uD305
PileAndBunkerCapacityViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=\uC6A9\uB7C9
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=\uAE09\uC5EC\uB7C9
PileAndBunkerResultsCapacityInputViewModel.Title=\uC6A9\uB7C9
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=\uB192\uC774 (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=\uB192\uC774 (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=\uC0C1\uB2E8 \uB108\uBE44 (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=\uC0C1\uB2E8 \uB108\uBE44 (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=\uBC14\uB2E5 \uB108\uBE44 (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=\uBC14\uB2E5 \uB108\uBE44 (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=\uBC14\uB2E5 \uAE38\uC774 (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=\uBC14\uB2E5 \uAE38\uC774 (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=\uC0C1\uB2E8 \uAE38\uC774 (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=\uC0C1\uB2E8 \uAE38\uC774 (f.)
UserPreferencesViewModel.UserPreferencesMoreSettings=\uCD94\uAC00 \uC124\uC815
UserPreferencesViewModel.UserPreferencesMilkProcessor=\uC720\uC5C5\uCCB4 \uC124\uC815
PricingMatrixViewModel.Title=Matrix \uD3B8\uC9D1
PricingMatrixViewModel.Amount=\uC591 (1000 cells/ml)
PricingMatrixViewModel.AmountCFU=\uC591 (1000 cfu/ml)
MilkProcessorViewModel.Delete=\uC0AD\uC81C
MilkProcessorViewModel.ConcentrationProcessor=\uC720\uC131\uBD84\uCD1D\uB7C9 \uC720\uB300\uC0B0\uC815
MilkProcessorViewModel.ComponentProcessor=\uC720\uC131\uBD84\uD568\uB7C9 \uC720\uB300\uC0B0\uC815
MilkProcessorViewModel.Name=\uC774\uB984
MilkProcessorViewModel.BasePrices=\uAE30\uBCF8 \uAC00\uACA9
MilkProcessorViewModel.PricingMatrices=\uAC00\uACA9 \uACB0\uC815 \uC694\uC18C
MilkProcessorSettingsMasterViewModel.MilkProcNew=\uC2E0\uADDC
MilkProcessorSettingsMasterViewModel.Title=\uC720\uC5C5\uCCB4 \uC14B\uC5C5
MilkProcessorSettingsMasterViewModel.MilkProcComponent=\uC720\uC131\uBD84\uD568\uB7C9
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=\uB18D\uCD95\uC131\uBD84
Amount=\uC591
PricingMatrixEditViewModel.Title=Matrix \uD56D\uBAA9 \uC138\uBD80\uC815\uBCF4
PricingMatrixEditViewModel.Save=\uC800\uC7A5
PricingMatrixEditViewModel.Cancel=\uCDE8\uC18C
PricingMatrixViewModel.New=\uC2E0\uADDC
DeleteMatrixValue=\uC774 Matrix \uAC12\uC744 \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?
UnitedStates=\uBBF8\uAD6D
US=\uBBF8\uAD6D
Euro=유럽 (\u20AC EUR)
UnitedKingdom=\uC601\uAD6D
UK=\uC601\uAD6D
Canada=\uCE90\uB098\uB2E4
Algeria=\uC54C\uC81C\uB9AC
Argentina=\uC544\uB974\uD5E8\uD2F0\uB098
Australia=\uD638\uC8FC
Brazil=\uBE0C\uB77C\uC9C8
Chile=\uCE60\uB808
China=\uC911\uAD6D
CzechRepublic=\uCCB4\uCF54\uACF5\uD654\uAD6D
Guatemala=\uACFC\uD14C\uB9D0\uB77C
Honduras=\uC628\uB450\uB77C\uC2A4
Hungary=\uD5DD\uAC00\uB9AC
India=\uC778\uB3C4
Indonesia=\uC778\uB3C4\uB124\uC2DC\uC544
SouthKorea=\uD55C\uAD6D
Malaysia=\uB9D0\uB808\uC774\uC2DC\uC544
Mexico=\uBA55\uC2DC\uCF54
Nicaragua=\uB2C8\uCE74\uB77C\uACFC
Peru=\uD398\uB8E8
Philippines=\uD544\uB9AC\uD540
Poland=\uD3F4\uB780\uB4DC
Romania=\uB8E8\uB9C8\uB2C8\uC544
Russia=\uB7EC\uC2DC\uC544
SaudiArabia=\uC0AC\uC6B0\uB514\uC544\uB77C\uBE44\uC544
SouthAfrica=\uB0A8\uC544\uD504\uB9AC\uCE74
Surinam=\uC218\uB9AC\uB0A8
Switzerland=\uC2A4\uC704\uC2A4
Taiwan=\uB300\uB9CC
Thailand=\uD0DC\uAD6D
Ukraine=\uC6B0\uD06C\uB77C\uB2C8\uC544
Venezuela=\uBCA0\uB124\uC218\uC5D8\uB77C
Vietnam=\uBCA0\uD2B8\uB0A8
ProcessorCurrencyPickListViewModel.CurrenciesLabel=\uD1B5\uD654
ProcessorCurrencyPickListViewModel.Title=\uD1B5\uD654
PricingMatrixPickListViewModel.PricingMatrix=\uAC00\uACA9 \uACB0\uC815 \uC694\uC18C
PlBacteriaCell=\uC138\uADE0\uC218 (1,000 cfu/ml)
PlSomaticCell=\uCCB4\uC138\uD3EC\uC218 (1,000 cells/ml)
SomaticCellPerML=\uCCB4\uC138\uD3EC\uC218 (cells/ml)
SomanticCellCount=\uCCB4\uC138\uD3EC\uC218
PlMilkFat=\uC720\uC9C0\uBC29
PlMilkProtein=\uC720\uB2E8\uBC31
MilkProcessorViewModel.SelectCurrency=\uD1B5\uD654 \uC120\uD0DD
SelectMatrix=Matrix \uC120\uD0DD
MilkProcessorViewModel.NewComponentProcessorName=\uC720\uC131\uBD84\uD568\uB7C9 \uC720\uB300\uC0B0\uC815 #{0}
MilkProcessorViewModel.NewConcentrationProcessorName=\uC720\uC131\uBD84\uCD1D\uB7C9 \uC720\uB300\uC0B0\uC815 #{0}
MilkProcessorViewModel.BasePriceMilkFat=\uC720\uC9C0\uBC29 ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=\uC6B0\uC720 ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=\uC720\uB2E8\uBC31 ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=\uAE30\uD0C0 \uACE0\uD615\uBD84 ({0}/{1})
MilkProcessorViewModel.Amount=\uC591 (1000 cells / ml)
MilkProcessorViewModel.AmountCFU=\uC591 (1000 cfu / ml)
MilkProcessorViewModel.HundredWeight=CWT (100\uD30C\uC6B4\uB4DC)
MilkProcessorViewModel.WeightMetric=kg
MilkProcessorViewModel.WeightImperialCWT=CWT (100\uD30C\uC6B4\uB4DC)
MilkProcessorViewModel.WeightImperial=\uD30C\uC6B4\uB4DC(0.45kg)
MilkProcessorViewModel.DeletePrompt=\uC774 \uC720\uC5C5\uCCB4\uB97C \uC0AD\uC81C\uD558\uBA74 \uCC29\uC720 \uC808\uCC28 \uB9E4\uCD9C\uC561 \uACC4\uC0B0\uAE30 \uD56D\uBAA9\uC758 \uACB0\uACFC\uB97C \uC783\uAC8C\uB429\uB2C8\uB2E4. \uC774 \uC720\uC5C5\uCCB4\uB97C \uC0AD\uC81C\uD560\uAE4C\uC694?
Edit=\uD3B8\uC9D1
MenuViewModel.Close=\uB2EB\uAE30
ForageAuditViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
NutritionViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
ComfortToolsViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
HeatstressTableViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
RevenueInputViewModel.ComparisonValues=\uBE44\uAD50\uAC12
SelectProcessor=\uC720\uC5C5\uCCB4 \uC120\uD0DD
PotentialSCC=\uCD94\uC815 \uCCB4\uC138\uD3EC\uC218 (cells/{0})
RevenueEditComparisonValuesViewModel.PotentialSCC=\uCD94\uC815 \uCCB4\uC138\uD3EC\uC218 (cells/{0})
RevenueEditComparisonValuesViewModel.NumOfCows=\uCC29\uC720\uB450\uC218
RumenHealthEditManureScoresViewModel.NumOfCows=\uCC29\uC720\uB450\uC218
NumOfCows=\uCC29\uC720\uB450\uC218
RevenueEditComparisonValuesViewModel.MilkProduction=\uC720\uB7C9
MilkProduction=\uC720\uB7C9 ({0})
MilkPrice=\uC720\uB300(\uC6D0/kg)({0}/{1})
RevenueEditComparisonValuesViewModel.MilkPrice=\uC720\uB300(\uC6D0/kg) ($/{0}})
RevenueEditComparisonValuesViewModel.MilkChange=\uC720\uB7C9 \uBCC0\uD654 (%)
MilkChange=\uC720\uB7C9 \uBCC0\uD654 ({0})
DownResponse=\uBE44\uC720\uC18D\uB3C4 \uBC18\uC751 ({0}/cow/day)
RevenueEditComparisonValuesViewModel.DownResponse=\uBE44\uC720\uC18D\uB3C4 \uBC18\uC751 ({0}/cow/day)
RevenueEditComparisonValuesViewModel.CurrentSCC=\uD604\uC7AC \uCCB4\uC138\uD3EC\uC218 (cells/{0})
RevenueInputViewModel.ScenarioOne=\uC2DC\uB098\uB9AC\uC624 1
RevenueInputViewModel.ScenarioTwo=\uC2DC\uB098\uB9AC\uC624 2
MilkProcessorInputViewModel.ProcessorDeletedPrompt=\uC774\uC804\uC5D0 \uC120\uD0DD\uD55C \uC720\uC5C5\uCCB4\uAC00 \uC0AD\uC81C\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uACC4\uC18D\uD558\uB824\uBA74 \uB2E4\uB978 \uC720\uC5C5\uCCB4\uB97C \uC120\uD0DD\uD558\uC2ED\uC2DC\uC624.
MilkProcessorInputViewModel.Title=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \uC785\uB825
HeatstressDataEntryViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
HeatstressChartViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
HeatstressChartViewModel.TemperatureMetric=\u00B0C(\uC12D\uC528)
HeatstressChartViewModel.TemperatureImperial=\u00B0F(\uD654\uC528)
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Kilograms=kg
HeatstressChartViewModel.Pounds=\uD30C\uC6B4\uB4DC(0.45kg)
CudChewingViewModel.CudChewing=\uC6B0\uC0AC
CudChewingViewModel.CudChewingList=\uC6B0\uAD70
CudChewingViewModel.CudChewingTitle=\uC6B0\uC0AC \uC774\uB984
HealthToolsViewModel.Title=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00 \uB3C4\uAD6C
HealthToolsViewModel.HealthHeading=\uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uB3C4\uAD6C\uB97C \uC120\uD0DD\uD574 \uC8FC\uC2ED\uC2DC\uC624.
HealthToolsViewModel.HealthToolsList=\uB3C4\uAD6C
HealthToolsViewModel.RumenHealthTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
HealthToolsViewModel.RumenHealthTMRTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 TMR \uC785\uC790\uB3C4 \uC9C0\uC218
HealthToolsViewModel.RumenHealthManureTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
HealthToolsViewModel.RumenHealthLocomotionTitle=\uBCF4\uD589\uC9C0\uC218
HealthToolsViewModel.RumenHealthBodyConditionTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728
ProductivityToolsViewModel.ProductivityTools=\uB3C4\uAD6C
ProductivityToolsViewModel.MilkProcessRevenueCalculator=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
HerdInformation=\uC6B0\uAD70 \uC815\uBCF4
ProductivityToolsViewModel.MilkRevenueAnalysis=\uC6B0\uC720 \uC218\uC775 \uBD84\uC11D
ProductivityToolsViewModel.ProductivityTitle=\uC0DD\uC0B0\uC131 \uB3C4\uAD6C
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=\uC785\uB825\uAC12
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=\uACB0\uACFC
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=\uCC38\uC870
MilkProcessorResourcesViewModel.ResourcesReferenceChart=\uCC38\uC870 \uCC28\uD2B8
MilkProcessorResourcesViewModel.LinearScore=\uC120\uD615 \uC810\uC218
MilkProcessorResourcesViewModel.ApproxSCC=\uCCB4\uC138\uD3EC\uC218 \uADFC\uC0AC\uCE58(cells/ml))
MilkProcessorResourcesViewModel.Mastitis=\uC720\uBC29\uC5FC\uC5D0 \uC758\uD55C \uC720\uC190\uC2E4 ({0})
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=\uB18D\uCD95\uC131\uBD84
MilkProcessorSettingsComponentViewModel.MilkProcComponent=\uC720\uC131\uBD84\uD568\uB7C9
DashboardViewModel.UserPreferences=\uC0AC\uC6A9\uC790 \uC124\uC815
MilkProcessRevenueCalculatorMasterViewModel.Title=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
MilkProcessorInputViewModel.SelectProcessor=\uC720\uC5C5\uCCB4 \uC120\uD0DD
MilkProcessorInputViewModel.ComparisonValues=\uBE44\uAD50\uAC12
MilkProcessorInputViewModel.Edit=\uD3B8\uC9D1
MilkProcessorInputViewModel.ScenarioOne=\uC2DC\uB098\uB9AC\uC624 1
MilkProcessorInputViewModel.ScenarioTwo=\uC2DC\uB098\uB9AC\uC624 2
MilkProcessorEditComparisonValuesViewModel.Title=\uBE44\uAD50\uAC12 \uD3B8\uC9D1
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=\uC2DC\uB098\uB9AC\uC624 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=\uC2DC\uB098\uB9AC\uC624 2
CudChewsCalculatorViewModel.CalculatorHeading=\uC800\uC791 \uD68C\uC218\uB97C \uC138\uAE30 \uC704\uD574 \uC816\uC18C\uB97C \uC120\uD0DD\uD558\uC138\uC694. \uCE74\uC6B4\uD2B8 \uBAA9\uB85D\uC5D0 \uC816\uC18C\uB97C \uCD94\uAC00\uD558\uB824\uBA74 \uC704\uC758 "\uC2E0\uADDC \uCD94\uAC00"\uB97C \uD0ED\uD558\uC2ED\uC2DC\uC624.
CudChewsCalculatorViewModel.CudChewCategorySection=\uC816\uC18C
ChewsPerCudMasterViewModel.AddNew=\uC2E0\uADDC \uCD94\uAC00
ChewsPerCudMasterViewModel.CudChewingInputs=\uC785\uB825
ChewsPerCudMasterViewModel.CudChewingResults=\uACB0\uACFC
RevenueEditComparisonValuesViewModel.Title=\uBE44\uAD50\uAC12 \uD3B8\uC9D1
RevenueEditComparisonValuesViewModel.ScenarioOne=\uC2DC\uB098\uB9AC\uC624 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=\uC2DC\uB098\uB9AC\uC624 2
MilkProcessorResultsViewModel.ScenarioOne=\uC2DC\uB098\uB9AC\uC624 1
MilkProcessorResultsViewModel.ScenarioTwo=\uC2DC\uB098\uB9AC\uC624 2
MilkProcessorResultsViewModel.Change=\uBCC0\uD654
MilkProcessorResultsViewModel.ResultsHeader=\uC5F0\uAC04 \uAC00\uCE58 \uBCC0\uD654
MilkProcessorResultsViewModel.HundredWeight=CWT (100\uD30C\uC6B4\uB4DC)
MilkProcessorResultsViewModel.WeightMetric=kg
MilkProductionRevenue=\uC720\uC0DD\uC0B0 \uB9E4\uCD9C
ClinicalMastitisLosses=\uC784\uC0C1\uD615 \uC720\uBC29\uC5FC \uC190\uC2E4
MilkPricePremiums=\uC720\uB300 \uD504\uB9AC\uBBF8\uC5C4
MilkLetDownResponse=\uC6B0\uC720 \uBE44\uC720\uC18D\uB3C4 \uBC18\uC751
Revenue=\uB9E4\uCD9C
TotalRevenue=\uCD1D \uB9E4\uCD9C
Total=\uCD1D
NumberOfChewsViewModel.CountHeader=\uC774 \uC816\uC18C\uC758 \uBC18\uCD94\uD68C\uC218\uB97C \uC138\uC5B4 \uBCF4\uC138\uC694
NumberOfChewsViewModel.Count=\uCE74\uC6B4\uD2B8
NumberOfChewsViewModel.NextCow=\uB2E4\uC74C \uC816\uC18C
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=\uCC38\uC870 \uCC28\uD2B8
SelectProcessorViewModel.COMPONENT=\uC720\uC131\uBD84\uD568\uB7C9
SelectProcessorViewModel.CONCENTRATION=\uB18D\uCD95\uC131\uBD84
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=\uC800\uC791 \uD69F\uC218 \uACC4\uC0B0\uAE30
RumenHealthPenCudCalculatorViewModel.CudChewing=\uBC18\uCD94\uB450\uC218
CudChewingDataEntryViewModel.CudChewing=\uBC18\uCD94\uB450\uC218
RumenHealthPenCudCalculatorViewModel.NumberOfChews=\uBC18\uCD94\uB2F9 \uC800\uC791 \uD69F\uC218
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=\uBC18\uCD94 \uAC1C\uCCB4\uC218 \uC785\uB825
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=\uB450\uB2F9 \uBC18\uCD94\uD69F\uC218 \uC785\uB825
RumenHealthLandingViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
RumenHealthLandingViewModel.PenAnalysis=\uC6B0\uC0AC \uBD84\uC11D
RumenHealthLandingViewModel.Pens=\uC6B0\uC0AC
RumenHealthLandingViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
HerdAnalysisMasterViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
MilkProcessorViewModel.NameNotUnique="{0}"\uC774\uB984\uC758 Component processor\uAC00 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uB2E4\uB978 \uC774\uB984\uC744 \uC785\uB825\uD574\uC8FC\uC138\uC694.
CudChewingHerdEditScoreViewModel.EditScoreTitle=\uBC18\uCD94 \uC800\uC791 \uC9C0\uC218 \uD3B8\uC9D1
CudChewingHerdEditScoreViewModel.PercentChewingItem=\uC800\uC791 \uBE44\uC728
CudChewingHerdEditScoreViewModel.AverageChewsItem=\uBC18\uCD94\uB2F9 \uD3C9\uADE0 \uB418\uC0C8\uAE40\uC9C8
CudChewingHerdEditScoreViewModel.DaysInMilkItem=\uBE44\uC720\uC77C\uB839 (DIM)
Cow=\uC816\uC18C
ChewsPerCudMasterViewModel.NumOfChews=\uBC18\uCD94\uD69F\uC218
CudChewsCalculatorViewModel.NumOfChews=\uBC18\uCD94\uD69F\uC218
NumberOfChewsViewModel.NumberOfChewsCow=\uB450\uB2F9 \uBC18\uCD94\uD69F\uC218 #
PileAndBunkerResultsCapacityInputViewModel.Capacity=\uC6A9\uB7C9
HerdAnalysisMasterViewModel.HerdAnalysisHeading=\uC6B0\uAD70 \uBD84\uC11D
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=\uC6B0\uAD70 \uBD84\uC11D
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=\uBAA9\uD45C
CudChewingHerdEditScoreViewModel.Close=\uB2EB\uAE30
HerdAnalysisGoalsViewModel.Title=\uC6B0\uAD70 \uBD84\uC11D
HerdAnalysisGoalsViewModel.CudChewingGoals=\uBC18\uCD94 \uC800\uC791 \uBAA9\uD45C
HerdAnalysisGoalsViewModel.DIM=\uBE44\uC720\uC77C\uB839 (DIM)
HerdAnalysisGoalsViewModel.PercentChewing=% \uBC18\uCD94\uB450\uC218
HerdAnalysisGoalsViewModel.CudChews=\uBC18\uCD94\uD69F\uC218
HerdAnalysisGoalsViewModel.FarOffDry=\uAC74\uC720\uCD08\uAE30
HerdAnalysisGoalsViewModel.CloseUpDry=\uAC74\uC720\uB9D0\uAE30
HerdAnalysisGoalsViewModel.Fresh=\uBD84\uB9CC\uC6B0
HerdAnalysisGoalsViewModel.EarlyLactation=\uBE44\uC720\uCD08\uAE30
HerdAnalysisGoalsViewModel.PeakMilk=\uBE44\uC720\uD53C\uD06C\uAE30
HerdAnalysisGoalsViewModel.MidLactation=\uBE44\uC720\uC911\uAE30
HerdAnalysisGoalsViewModel.LateLactation=\uBE44\uC720\uB9D0\uAE30
HerdAnalysisGoalsViewModel.To=to
NotebookSectionRumenHealthLanding=\uBC18\uCD94\uC704 \uAC74\uAC15
NotebookSectionHerdAnalysisGoals=\uBC18\uCD94\uC704 \uAC74\uAC15 - \uC6B0\uAD70 \uBD84\uC11D \uBAA9\uD45C
CudChewingMasterViewModel.CudChewingInputs=\uC785\uB825
CudChewingMasterViewModel.CudChewingResults=\uACB0\uACFC
CudChewingDataEntryViewModel.Yes=\uC608
CudChewingDataEntryViewModel.No=\uC544\uB2C8\uC624
CudChewingDataEntryViewModel.HerdCudChewingDescription=\uC774\uBC88 \uBC29\uBB38\uC5D0\uC11C \uC544\uB798\uC758 \uCE74\uC6B4\uD130\uB97C \uC0AC\uC6A9\uD558\uC5EC \uC774 \uC6B0\uC0AC\uB0B4\uC5D0 \uBC18\uCD94 \uC800\uC791\uD65C\uB3D9\uD558\uB294 \uAC1C\uCCB4\uC758 \uC218\uB97C \uC138\uC2DC\uC624. \uCD5C\uC18C 10\uB450 \uC774\uC0C1\uC758 \uAC1C\uCCB4\uB97C \uC138\uC5B4\uC57C \uD569\uB2C8\uB2E4.
CudChewingMasterViewModel.CudChewing=\uBC18\uCD94\uB450\uC218
HerdAnalysisViewModel.TableTitle=\uBC18\uCD94 \uC800\uC791 \uC9C0\uC218 \uBD84\uC11D
HerdAnalysisViewModel.NumberofChewsPerCud=\uBC18\uCD94\uB2F9 \uC800\uC791 \uD69F\uC218
HerdAnalysisViewModel.HerdCudChewing=\uBC18\uCD94\uB450\uC218 %
HerdAnalysisViewModel.EditLabel=\uD3B8\uC9D1
HerdAnalysisViewModel.PercentChewing=\uBC18\uCD94\uB450\uC218 \uBE44\uC728
HerdAnalysisViewModel.AverageChews=\uBC18\uCD94\uB2F9 \uD3C9\uADE0 \uB418\uC0C8\uAE40\uC9C8 \uD69F\uC218
HerdAnalysisViewModel.DaysInMilk=\uBE44\uC720\uC77C\uB839 (DIM)
HerdAnalysisViewModel.PenNameLabel=\uC6B0\uC0AC \uC774\uB984
HerdAnalysisViewModel.NoOfCows=\uC2DC\uC791\uD55C \uBAA8\uB4E0 \uC6B0\uBC29\uC5D0 \uB300\uD574 \uC6B0\uBC29 \uBD84\uC11D\uC744 \uC644\uB8CC\uD558\uC2ED\uC2DC\uC624.
VisitViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
PileAndBunkerResultsMasterViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
ForageAuditScorecardResultsViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
ProductivityToolsViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
EditNoteViewModel.DeleteImageButtonText=\uC774\uBBF8\uC9C0 \uC0AD\uC81C
EditNoteViewModel.DeleteVideoButtonText=\uBE44\uB514\uC624 \uC0AD\uC81C
EditNoteViewModel.NoteOnlyOneVideo=\uB178\uD2B8\uB2F9 \uD558\uB098\uC758 \uB3D9\uC601\uC0C1\uB9CC \uD5C8\uC6A9\uB429\uB2C8\uB2E4. \uBA3C\uC800 \uD604\uC7AC \uB3D9\uC601\uC0C1\uC744 \uC0AD\uC81C\uD558\uC138\uC694
EditNoteViewModel.NoteOnlyOneImage=\uB178\uD2B8\uB2F9 \uD558\uB098\uC758 \uC774\uBBF8\uC9C0\uB9CC \uD5C8\uC6A9\uB429\uB2C8\uB2E4. \uBA3C\uC800 \uD604\uC7AC \uC774\uBBF8\uC9C0\uB97C \uC0AD\uC81C\uD558\uC138\uC694.
EditNoteViewModel.NoteGalleryNotImplemented=\uAC24\uB7EC\uB9AC \uAE30\uB2A5\uC774 \uC544\uC9C1 \uC801\uC6A9\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
EditNoteViewModel.NoteCamcorderNotImplemented=\uCEA0\uCF54\uB354 \uAE30\uB2A5\uC774 \uC544\uC9C1 \uC801\uC6A9\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
EditNoteViewModel.Category=\uAD6C\uBD84
EditNoteViewModel.Observation=\uAD00\uCC30
EditNoteViewModel.Action=\uC791\uB3D9
EditNoteViewModel.Task=\uC5C5\uBB34
EditNoteViewModel.Event=\uC774\uBCA4\uD2B8
HerdAnalysisTableTitle=\uBC18\uCD94 \uC800\uC791 \uC9C0\uC218 \uBD84\uC11D
HerdAnalysisViewModel.HerdAnalysisTableTitle=\uBC18\uCD94 \uC800\uC791 \uC9C0\uC218 \uBD84\uC11D
HerdAnalysisViewModel.Edit=\uD3B8\uC9D1
NoteCamcorderNotImplemented=\uCEA0\uCF54\uB354 \uAE30\uB2A5\uC774 \uC544\uC9C1 \uC801\uC6A9\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
Date=\uB0A0\uC9DC
DatesForComparison=\uBE44\uAD50 \uB0A0\uC9DC
RumenHealthTMRLandingViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uC785\uC790\uB3C4 \uC9C0\uC218
RumenHealthManureLandingViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
RumenHealthManureLandingViewModel.Pens=\uC6B0\uC0AC
RumenHealthManureLandingViewModel.PenAnalysis=\uC6B0\uC0AC \uBD84\uC11D
RumenHealthManureLandingViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
RumenHealthBodyConditionLandingViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
RumenHealthBodyConditionLandingViewModel.Pens=\uC6B0\uC0AC
RumenHealthBodyConditionLandingViewModel.PenAnalysis=\uC6B0\uC0AC \uBD84\uC11D
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
BodyConditionScoreHerdGoalsViewModel.TableTitle=\uBE44\uC720 \uB2E8\uACC4\uBCC4 BCS
BodyConditionScoreHerdGoalsViewModel.Edit=\uD3B8\uC9D1
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=BCS \uBAA9\uD45C \uCD5C\uC18C\uAC12
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=BCS \uBAA9\uD45C \uCD5C\uB300\uAC12
BodyConditionScoreHerdGoalsViewModel.FarOffDry=\uAC74\uC720 \uCD08\uAE30 (-21\uC77C \uC774\uC804)
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=\uAC74\uC720 \uB9D0\uAE30 (-20~-1\uC77C))
BodyConditionScoreHerdGoalsViewModel.Fresh=\uBD84\uB9CC\uC6B0 (0~15\uC77C))
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=\uBE44\uC720\uCD08\uAE30 (16~60\uC77C)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=\uC6B0\uC720 \uD53C\uD06C (61~120\uC77C)
BodyConditionScoreHerdGoalsViewModel.MidLactation=\uBE44\uC720\uC911\uAE30 (121~200\uC77C)
BodyConditionScoreHerdGoalsViewModel.LateLactation=\uBE44\uC720\uB9D0\uAE30 (201\uC77C \uC774\uC0C1)
BodyConditionScoreHerdEditGoalsViewModel.Title=\uBAA9\uD45C \uD3B8\uC9D1
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=BCS \uCD5C\uC18C\uAC12
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=BCS \uCD5C\uB300\uAC12
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=\uAC74\uC720 \uCD08\uAE30 (-21\uC77C \uC774\uC804)
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=\uAC74\uC720 \uB9D0\uAE30 (-20~-1\uC77C)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=\uBD84\uB9CC\uC6B0 (0~15\uC77C)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=\uBE44\uC720\uCD08\uAE30 (16~60\uC77C)
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=\uC6B0\uC720 \uD53C\uD06C (61~120\uC77C)
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=\uBE44\uC720\uC911\uAE30 (121~200\uC77C)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=\uBE44\uC720\uB9D0\uAE30 (201\uC77C \uC774\uC0C1)
PenTimeBudgetComparisonViewModel.TableTitle=\uBE44\uAD50
PenTimeBudgetComparisonViewModel.Current=\uD604\uC7AC
PenTimeBudgetComparisonViewModel.CowsInPen=\uC6B0\uC0AC \uB0B4 \uC816\uC18C\uB450\uC218
PenTimeBudgetComparisonViewModel.Overcrowding=\uBC00\uC0AC \uC815\uB3C4 (%)
PenTimeBudgetComparisonViewModel.TimePerMilking=\uCC29\uC720 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=\uC2DC\uAC04\uB2F9 \uD314\uB7EC \uD68C\uC804\uC218
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=\uC2DC\uAC04\uB2F9 \uCC29\uC720 \uB450\uC218
PenTimeBudgetComparisonViewModel.TotalTimeMilking=\uCD1D \uCC29\uC720 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=\uC2A4\uD1A8\uAE4C\uC9C0 \uC774\uB3D9\uD558\uB294 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=\uCD1D \uBE44\uD734\uC2DD\uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=\uD734\uC2DD\uC744 \uC704\uD574 \uB0A8\uC740 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=\uD734\uC2DD\uC744 \uC704\uD55C \uC18C\uC694 \uC2DC\uAC04(\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.RestingDifference=\uD734\uC2DD\uC2DC\uAC04 \uCC28\uC774 (\uC2DC\uAC04)
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=\uC7A0\uC7AC\uC801 \uC720\uC190\uC2E4 / \uC774\uB4DD ({0})
PenTimeBudgetComparisonViewModel.EnergyChange=\uC5D0\uB108\uC9C0 \uBCC0\uD658 (Mcals)
PenTimeBudgetComparisonViewModel.BodyWeightChange=\uCCB4\uC911 \uBCC0\uD654 ({0})
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=BCS \uBCC0\uD654 (per 100 days)
RumenHealthTMRLandingViewModel.Pens=\uC6B0\uC0AC
RumenHealthTMRLandingViewModel.PenAnalysis=\uC6B0\uC0AC \uBD84\uC11D
RumenHealthTMRLandingViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
RumenHealthLocomotionLandingViewModel.Title=\uBCF4\uD589\uC9C0\uC218
RumenHealthLocomotionLandingViewModel.Pens=\uC6B0\uC0AC
RumenHealthLocomotionLandingViewModel.PenAnalysis=\uC6B0\uC0AC \uBD84\uC11D
RumenHealthLocomotionLandingViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
RevenueEditComparisonValuesViewModel.EditComparisonValues=\uBE44\uAD50\uAC12 \uD3B8\uC9D1
SelectProcessorViewModel.MilkProcessors=\uC6B0\uC720 \uD504\uB85C\uC138\uC11C
CudChewingViewModel.Title=\uC6B0\uC0AC
ChewsPerCudMasterViewModel.Title={0} / \uC800\uC791\uD69F\uC218
EditGoalsCudChewingViewModel.EditGoalsTitle=\uBC18\uCD94 \uC800\uC791 \uBAA9\uD45C \uD3B8\uC9D1
CudChewingHerdEditScoreViewModel.EditGoalsTitle=\uBC18\uCD94 \uC800\uC791 \uC9C0\uC218 \uD3B8\uC9D1
EditGoalsCudChewingViewModel.Close=\uB2EB\uAE30
Select=\uC120\uD0DD
EditGoalsCudChewingViewModel.PercentChewing=\uC800\uC791 \uBE44\uC728
EditGoalsCudChewingViewModel.CudChews=\uBC18\uCD94\uB2F9 \uD3C9\uADE0 \uB418\uC0C8\uAE40\uC9C8
EditGoalsCudChewingViewModel.FarOffDry=\uAC74\uC720\uC804\uAE30
EditGoalsCudChewingViewModel.CloseUpDry=\uAC74\uC720\uB9D0\uAE30
EditGoalsCudChewingViewModel.Fresh=\uBD84\uB9CC\uC6B0
EditGoalsCudChewingViewModel.EarlyLactation=\uBE44\uC720\uCD08\uAE30
EditGoalsCudChewingViewModel.PeakMilk=\uC6B0\uC720 \uD53C\uD06C
EditGoalsCudChewingViewModel.MidLactation=\uBE44\uC720\uC911\uAE30
EditGoalsCudChewingViewModel.LateLactation=\uBE44\uC720\uB9D0\uAE30
ThisVisit= (\uC774\uBC88 \uBC29\uBB38)
EditDatesForComparison=\uBE44\uAD50 \uB0A0\uC9DC \uD3B8\uC9D1
EditDatesForComparisonViewModel.EditDatesTitle=\uBE44\uAD50 \uB0A0\uC9DC \uD3B8\uC9D1
EditDatesForComparisonViewModel.EditDatesLabel=\uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uC774 \uC6B0\uC0AC\uC640 \uBE44\uAD50\uD560 \uC218 \uC788\uB294 \uB0A0\uC9DC\uB97C \uC120\uD0DD\uD558\uC138\uC694.
EditDatesForComparisonViewModel.EditDatesVisits=\uBC29\uBB38
EditDatesForComparisonViewModel.EditDatesClose=\uB2EB\uAE30
NumberOfChewsReportsViewModel.VisitDate=\uB0A0\uC9DC
NumberOfChewsReportsViewModel.AverageNumberChews=\uD3C9\uADE0 \uBC18\uCD94\uD69F\uC218
NumberOfChewsReportsViewModel.DatesComparison=\uBE44\uAD50 \uB0A0\uC9DC
NumberOfChewsReportsViewModel.EditVisits=\uC120\uD0DD
NumberOfChewsReportsViewModel.DateDescription=\uB0A0\uC9DC
NumberOfChewsReportsViewModel.Average=\uD3C9\uADE0 \uBC18\uCD94\uD69F\uC218
MilkingProcessRevenueInputs=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC785\uB825
MilkingProcessRevenueResults=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uACB0\uACFC
MilkingProcessRevenueResources= \uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC790\uB8CC
NumberOfCows=\uCC29\uC720\uB450\uC218
ManureEditScores=\uBD84\uBCC0\uC9C0\uC218 - \uC9C0\uC218 \uD3B8\uC9D1
NumberOfChewsViewModel.Title=\uB450\uB2F9 \uBC18\uCD94\uD69F\uC218 {0}
NumberOfChewsViewModel.ValidCudInput=\uC720\uD6A8\uD55C \uAC12\uC744 \uC785\uB825\uD558\uC138\uC694.
EditDatesForComparisonViewModel.Chews=\uB418\uC0C8\uAE40\uC9C8
EditDatesForComparisonViewModel.Visits=\uBC29\uBB38
EditDatesForComparisonViewModel.Title=\uBE44\uAD50 \uB0A0\uC9DC \uD3B8\uC9D1
EditDatesForComparisonViewModel.ManureScoreAverage=\uD3C9\uADE0 \uBD84\uBCC0\uC9C0\uC218
EditDatesForComparisonViewModel.LocomotionScoreAverage=\uD3C9\uADE0 \uBCF4\uD589\uC9C0\uC218:
EditDatesForComparisonViewModel.TimeRemainingForResting=\uD734\uC2DD\uC744 \uC704\uD574 \uB0A8\uC740 \uC2DC\uAC04:
EditDatesForComparisonViewModel.PenTimeBudgetTitle=\uD604\uC7AC \uBC29\uBB38\uACFC \uBE44\uAD50\uD560 \uC218 \uC788\uB294 \uB0A0\uC9DC\uB97C \uC120\uD0DD\uD558\uC138\uC694.
BodyConditionHerdInputs=BCS \uC6B0\uAD70 \uBD84\uC11D \uC785\uB825
BodyConditionHerdGoals=BCS \uC6B0\uAD70 \uBD84\uC11D \uBAA9\uD45C
FarOffDry=\uAC74\uC720\uC804\uAE30
BodyConditionHerdResults=BCS \uC6B0\uAD70 \uACB0\uACFC
HerdReporting=\uC6B0\uAD70 \uBCF4\uACE0 : \uBC18\uCD94 \uC800\uC791
NumberOfChewsReportsViewModel.AverageChews=\uD3C9\uADE0 \uB418\uC0C8\uAE40\uC9C8
CloseUpDry=\uAC74\uC720\uB9D0\uAE30
EarlyLactation=\uBE44\uC720\uCD08\uAE30
PeakMilk=\uC6B0\uC720 \uD53C\uD06C
MidLactation=\uBE44\uC720\uC911\uAE30
LateLactation=\uBE44\uC720\uB9D0\uAE30
CudChewingPercentGoal=\uBAA9\uD45C\uB300\uBE44 {0}%
NumChewsGoal=\uBAA9\uD45C {0}
CudChewsDatesForComparisonViewModel.CudChewsPercent=반추 저작 % /
RumenHealthTMRSelectPenViewModel.SelectScorer=Scorer \uC120\uD0DD
RumenHealthTMRSelectPenViewModel.SelectPen=\uC6B0\uC0AC (\uCC29\uC720\uC6B0\uC640 \uAC74\uC720\uC6B0\uB9CC))
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=\uC120\uD0DD \uC548\uD568
RumenHealthTMRSelectPenViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uC785\uC790\uB3C4 \uC9C0\uC218
RumenHealthTMRSelectPenViewModel.NoScorerSelected=\uC6B0\uC0AC\uB97C \uBCF4\uAE30\uC704\uD574\uC11C Scorer\uB97C \uC120\uD0DD\uD574\uC57C \uD569\uB2C8\uB2E4.
RumenHealthManureScoresResultsViewModel.PercentPen=\uC6B0\uC0AC \uBE44\uC728 (%)
RumenHealthManureScoresResultsViewModel.Title=\uBD84\uBCC0\uC9C0\uC218 \uACB0\uACFC
RumenHealthManureScoresResultsViewModel.SelectedDates=\uB0A0\uC9DC \uC120\uD0DD
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=\uB0A0\uC9DC
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=\uD3C9\uADE0 \uC810\uC218
RumenHealthTMRSelectScorerViewModel.SelectScorer=Scorer \uC120\uD0DD
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=\uC120\uD0DD\uC548\uD568
RumenHealthTMRSelectScorerViewModel.FooterText=\uBC29\uBB38\uB2F9 \uD558\uB098\uC758 Scorer\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. Scorer \uBCC0\uACBD\uC2DC \uAC12\uC774 \uC190\uC2E4\uB429\uB2C8\uB2E4.
RumenHealthTMRSelectScorerViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uC785\uC790\uB3C4 \uC9C0\uC218
NoneSelected=\uC120\uD0DD\uC548\uD568
FourScreenNew=4\uB2E8 \uC2A4\uD06C\uB9B0(\uC2E0\uD615)
ThreeScreen=3\uB2E8 \uC2A4\uD06C\uB9B0
FourScreenOld=4\uB2E8 \uC2A4\uD06C\uB9B0(\uAD6C\uD615)
RumenHealthManureScoresViewModel.ManureScore=\uBD84\uBCC0\uC9C0\uC218
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=\uD3C9\uADE0 \uBD84\uBCC0\uC9C0\uC218
RumenHealthManureScoresViewModel.StdDevCalculated=\uD45C\uC900\uD3B8\uCC28(\uACC4\uC0B0\uAC12)
RumenHealthManureScoresViewModel.PercentOfPen=\uC6B0\uC0AC\uB0B4 \uBE44\uC728 (%))
RumenHealthManureScoresViewModel.AnimalsObserved=\uAD00\uCC30\uB41C \uAC1C\uCCB4
RumenHealthManureScoresViewModel.Edit=\uD3B8\uC9D1
RumenHealthManureScoresViewModel.ScoreCategory=\uBD84\uBCC0\uC9C0\uC218 \uAD6C\uBD84
RumenHealthManureMasterViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
RumenHealthManureMasterViewModel.Inputs=\uC785\uB825
RumenHealthManureMasterViewModel.Results=\uACB0\uACFC
RumenHealthManureMasterViewModel.RumenHealthManureScore=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
RumenHealthEditManureScoresViewModel.NumberOfCows=\uCC29\uC720\uB450\uC218
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=\uC816\uC18C\uC758 \uC218\uB97C \uC138\uC2DC\uC624
RumenHealthEditManureScoresViewModel.Count=\uCE74\uC6B4\uD2B8
Category1=\uBD84\uBCC0\uC9C0\uC218 \uAD6C\uBD84 1.0
Category2=\uBD84\uBCC0\uC9C0\uC218 \uAD6C\uBD84 2.0
Category3=\uBD84\uBCC0\uC9C0\uC218 \uAD6C\uBD84 3.0
Category4=\uBD84\uBCC0\uC9C0\uC218 \uAD6C\uBD84 4.0
Category5=\uBD84\uBCC0\uC9C0\uC218 \uAD6C\uBD84 5.0
RumenHealthEditManureScoresViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
ManureScorePenSelectionViewModel.ManureScoreTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
ManureScorePenSelectionViewModel.PenSelectionList=\uC6B0\uAD70
LocomotionSelectPenViewModel.Title=\uBCF4\uD589\uC9C0\uC218
LocomotionSelectPenViewModel.PenSelectionList=\uC6B0\uAD70
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=\uC785\uB825
RumenHealthTMRPenScorerTableMasterViewModel.Results=\uACB0\uACFC
RumenHealthTMRPenScorerTableMasterViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uC785\uC790\uB3C4 \uC9C0\uC218
RumenHealthTMRParticleScorePenTableInputViewModel.Title=TMR \uC785\uC790\uB3C4 \uC9C0\uC218
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=\uC0C1\uB2E8
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=\uC911\uB2E8 1
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=\uC911\uB2E8 2
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=\uC911\uB2E8 2
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=\uD2B8\uB808\uC774
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=\uC2A4\uD06C\uB9B0 \uB0B4 \uBE44\uC728 (%)
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=\uBB34\uAC8C \uCE21\uC815\uAC12 (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=\uBAA9\uD45C - \uCD5C\uC18C (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=\uBAA9\uD45C - \uCD5C\uB300 (%)
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=\uC2A4\uD06C\uB9B0 \uB0B4 \uBE44\uC728 (%)
ParticleScorePreviousVisitsViewModel.SelectDates=\uB0A0\uC9DC \uC120\uD0DD
ParticleScorePreviousVisitsViewModel.Top=\uC0C1\uB2E8
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.MidOne=\uC911\uB2E8 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=\uC911\uB2E8 2
ParticleScorePreviousVisitsViewModel.Tray=\uD2B8\uB808\uC774
PercentageOnScreen=\uC2A4\uD06C\uB9B0 \uB0B4 \uBE44\uC728 (%)
SelectDates=\uB0A0\uC9DC \uC120\uD0DD
Top=\uC0C1\uB2E8
TopValue=(19mm)
MidOne=\uC911\uB2E8 1
MidOneValue=(8mm)
MidTwo=\uC911\uB2E8 2
Tray=\uD2B8\uB808\uC774
FourScreenOldType=(1.18 mm)
FourScreenNewType=(4 mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
PercentOnScreenTitle=\uC2A4\uD06C\uB9B0 \uB0B4 \uBE44\uC728 (%)
MenuViewModel.ResetDatabaseTitle=\uD14C\uC2A4\uD2B8 \uB0A0\uC9DC \uCD08\uAE30\uD654
MenuViewModel.ResetDatabasePrompt=\uC0AC\uC6A9\uC790 \uAE30\uBCF8 \uC124\uC815\uC744 \uD3EC\uD568\uD55C \uAE30\uC874 \uB370\uC774\uD130\uAC00 \uD14C\uC2A4\uD2B8 \uB370\uC774\uD130\uC758 \uAE30\uBCF8\uAC12\uC73C\uB85C \uBC14\uB01D\uB2C8\uB2E4. \uBC29\uBB38\uD55C \uB3C4\uAD6C\uC640 \uC0C8\uB86D\uAC8C \uC0DD\uC131\uB41C \uBC29\uBB38, \uC720\uC5C5\uCCB4 \uC815\uBCF4\uAC00 \uC190\uC2E4\uB429\uB2C8\uB2E4. \uB610\uD55C
MenuViewModel.ResetDatabaseReset=\uCD08\uAE30\uD654
MenuViewModel.ResetDatabaseCancel=\uCDE8\uC18C
Reset_Database=\uB370\uC774\uD0C0\uBCA0\uC774\uC2A4 \uCD08\uAE30\uD654
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=\uC2A4\uD06C\uB9B0 \uBB34\uAC8C
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=\uC0C1\uB2E8 (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=\uC911\uB2E8 1(8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=\uC911\uB2E8 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=\uC911\uB2E8 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=\uD2B8\uB808\uC774
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=\uC2A4\uD06C\uB9B0 \uC601\uC810 \uBB34\uAC8C \uC785\uB825 (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=\uBAA9\uD45C - \uCD5C\uC18C (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=\uBAA9\uD45C - \uCD5C\uB300 (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=\uC800\uC6B8 \uBB34\uAC8C \uC785\uB825 (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=\uC800\uC6B8 \uBB34\uAC8C \uC785\uB825
BodyConditionScoresMasterViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BodyConditionScoresMasterViewModel.Inputs=\uC785\uB825
BodyConditionScoresMasterViewModel.Results=\uACB0\uACFC
BodyConditionScoresMasterViewModel.BodyConditionScore=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BodyConditionScoresMasterViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
RumenHealthManureMasterViewModel.RumenHealthManureTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BodyConditionScoreInputsViewModel.AvgBCSCalculated=\uD3C9\uADE0 BCS
BodyConditionScoreInputsViewModel.StdDevCalculated=\uD45C\uC900 \uD3B8\uCC28 (\uACC4\uC0B0\uAC12)
BodyConditionScoreInputsViewModel.BCSPercentOfPen=\uC6B0\uC0AC\uB0B4 \uBE44\uC728 (%)
BodyConditionScoreInputsViewModel.AnimalsObserved=\uAD00\uCC30\uB41C \uAC1C\uCCB4
BodyConditionScoreInputsViewModel.BCSCategory=BCS \uBD84\uB958
BodyConditionScoreInputsViewModel.Edit=\uD3B8\uC9D1
LocomotionPenMasterViewModel.Title=\uBCF4\uD589\uC9C0\uC218
LocomotionPenMasterViewModel.Inputs=\uC785\uB825
LocomotionPenMasterViewModel.Results=\uACB0\uACFC
LocomotionHerdMasterViewModel.Title=\uBCF4\uD589\uC9C0\uC218
LocomotionHerdMasterViewModel.SubHeading=\uC6B0\uAD70 \uBD84\uC11D
LocomotionHerdMasterViewModel.Inputs=\uC785\uB825
LocomotionHerdMasterViewModel.Revenue=\uC218\uC775
LocomotionHerdMasterViewModel.Results=\uACB0\uACFC
BodyConditionScoreEditInputsViewModel.Title=\uCC29\uC720\uB450\uC218
BodyConditionScoreEditInputsViewModel.NumberOfCows=\uCC29\uC720\uB450\uC218
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=\uC816\uC18C\uC758 \uC218\uB97C \uC138\uC2DC\uC624
BodyConditionScoreEditInputsViewModel.Count=\uCE74\uC6B4\uD2B8
BodyConditionScoreCategory=BCS \uAD6C\uBD84 {0}
BCSCategory1=BCS \uAD6C\uBD84 1.0
BCSCategory1pt5=BCS \uAD6C\uBD84 1.5
BCSCategory2=BCS \uAD6C\uBD84 2.0
BCSCategory2pt5=BCS \uAD6C\uBD84 2.5
BCSCategory3=BCS \uAD6C\uBD84 3.0
BCSCategory3pt5=BCS \uAD6C\uBD84 3.5
BCSCategory4=BCS \uAD6C\uBD84 4.0
BCSCategory4pt5=BCS \uAD6C\uBD84 4.5
BCSCategory5=BCS \uAD6C\uBD84 5.0
BodyConditionInputs=BCS \uC785\uB825
BodyConditionResults=BCS \uACB0\uACFC
NotebookBodyConditionEdit=BCS \uC218\uC815 Table
ShowEulaViewModel.Accept=\uB3D9\uC758
ShowEulaViewModel.Decline=\uCDE8\uC18C
ShowEulaViewModel.ConfirmationTitle=\uD655\uC778
ShowEulaViewModel.ConfirmationText=\uBAA8\uBC14\uC77C \uC571 \uCD5C\uC885 \uC0AC\uC6A9\uC790 \uACC4\uC57D \uC870\uAC74\uC5D0 \uB3D9\uC758\uD569\uB2C8\uAE4C?
ShowEulaViewModel.ConfirmationYes=\uC608
ShowEulaViewModel.ConfirmationNo=\uC544\uB2C8\uC624
ShowEulaViewModel.EulaScreenTitle=\uCD5C\uC885 \uC0AC\uC6A9\uC790 \uB77C\uC774\uC120\uC2A4
SelectProcessorViewModel.Edit=\uD3B8\uC9D1
LocomotionScore=\uBCF4\uD589\uC9C0\uC218
AvgLocomotionScore=\uD3C9\uADE0 \uBCF4\uD589\uC9C0\uC218
StdDevCalculated=\uD45C\uC900\uD3B8\uCC28 (\uACC4\uC0B0)
LocoCategory1=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 1.0
LocoCategory2=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 2.0
LocoCategory3=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 3.0
LocoCategory4=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 4.0
LocoCategory5=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 5.0
AnimalsObserved=\uAD00\uCC30\uB41C \uAC1C\uCCB4
LocomotionScoreHerd=\uBCF4\uD589\uC9C0\uC218 \uBD84\uC11D
HerdGoal=\uC6B0\uAD70 \uBAA9\uD45C(%)
HerdAverage=\uC6B0\uAD70 \uD3C9\uADE0(%)
LocomotionHerdResultsViewModel.HerdGoal=\uBAA9\uD45C
LocomotionHerdResultsViewModel.HerdAverage=\uC6B0\uAD70 \uD3C9\uADE0
TotalAnimalsHerd=\uCD1D \uAC1C\uCCB4
LocomotionScoreAverage=\uD3C9\uADE0 \uBCF4\uD589\uC9C0\uC218
PercentPen=\uC6B0\uC0AC\uB0B4 \uBE44\uC728 (%)
TotalPenPerScore=\uAC01 \uC810\uC218\uBCC4 \uC6B0\uC0AC\uB0B4 \uAC1C\uCCB4\uC218
LocomotionPenInputsViewModel.FromPenSetup=\uC6B0\uC0AC \uC124\uC815 \uC720\uB798
TotalAnimals=\uC6B0\uC0AC\uB0B4 \uCD1D \uAC1C\uCCB4 \uC218
DaysInMilkItem=\uBE44\uC720\uC77C\uB839 (DIM)
MilkProductionPounds=\uC720\uC0DD\uC0B0\uB7C9 (lbs)
MilkProductionKg=\uC720\uC0DD\uC0B0\uB7C9 (kgs)
LocomotionPenInputsViewModel.Milk=\uC720\uB7C9
LocomotionHerdInputsViewModel.Herd=\uC6B0\uAD70
LocomotionHerdEditGoalViewModel.Title=\uBAA9\uD45C\uB7C9 \uD3B8\uC9D1
LocomotionHerdEditGoalViewModel.HerdGoal=\uC6B0\uAD70 \uBAA9\uD45C
LocomotionHerdEditGoalViewModel.Category1=\uAD6C\uBD84 1.0
LocomotionHerdEditGoalViewModel.Category2=\uAD6C\uBD84 2.0
LocomotionHerdEditGoalViewModel.Category3=\uAD6C\uBD84 3.0
LocomotionHerdEditGoalViewModel.Category4=\uAD6C\uBD84 4.0
LocomotionHerdEditGoalViewModel.Category5=\uAD6C\uBD84 5.0
MilkLossPounds=\uC720\uC190\uC2E4 (lbs)
MilkLossKg=\uC720\uC190\uC2E4 (kgs)
LocomotionScoreReference=\uBCF4\uD589\uC9C0\uC218 \uCC38\uC870\uD45C
PercentLossPerCow=% \uC190\uC2E4 / \uC816\uC18C
AvgBCS=\uD3C9\uADE0 BCS
LocomotionEditTableViewModel.Title=\uCC29\uC720\uB450\uC218
LocomotionEditTableViewModel.EnterNumberOfCows=\uC816\uC18C\uC758 \uC218\uB97C \uC138\uC2DC\uC624
LocomotionEditTableViewModel.Category1=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 1.0
LocomotionEditTableViewModel.Category2=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 2.0
LocomotionEditTableViewModel.Category3=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 3.0
LocomotionEditTableViewModel.Category4=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 4.0
LocomotionEditTableViewModel.Category5=\uBCF4\uD589\uC9C0\uC218 \uAD6C\uBD84 5.0
RumenHealthEditManureScoresViewModel.Close=\uB2EB\uAE30
Count=\uCE74\uC6B4\uD2B8
BCSPenSelectionViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BCSPenSelectionViewModel.SelectPointScale=\uAE30\uC900\uC774 \uB418\uB294 BCS 1/2/3/4/5\uC911 \uD558\uB098\uB97C \uC120\uD0DD\uD558\uC138\uC694.
HalfPointScale=0.5 \uB2E8\uC704
QuarterPointScale=0.25 \uB2E8\uC704
BCSPenSelectionViewModel.Pens=\uC6B0\uC0AC
BodyConditionScoreResultsViewModel.BCSAverageTitle=\uD3C9\uADE0 \uC810\uC218
BodyConditionScoreResultsViewModel.Title=BCS \uACB0\uACFC
BodyConditionScoreResultsViewModel.PercentPen=\uC6B0\uC0AC\uB0B4 \uBE44\uC728 (%)
BodyConditionScoreResultsViewModel.SelectedDates=\uB0A0\uC9DC \uC120\uD0DD
LocomotionPreviousVisitsViewModel.PercentPen=\uC6B0\uC0AC\uB0B4 \uBE44\uC728 (%)
LocomotionPreviousVisitsViewModel.Title=\uBCF4\uD589\uC9C0\uC218 \uACB0\uACFC
LocomotionPreviousVisitsViewModel.SelectedDates=\uB0A0\uC9DC \uC120\uD0DD
LocomotionPreviousVisitsViewModel.AverageScore=\uD3C9\uADE0 \uC810\uC218
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=\uB0A0\uC9DC
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=\uD3C9\uADE0 \uC810\uC218
ProspectsViewModel.NewProspect=\uC0C8\uB85C\uC6B4 \uBAA9\uD45C\uACE0\uAC1D \uCD94\uAC00
VisitSummaryViewModel.Title=\uC0AC\uC774\uD2B8 \uBC29\uBB38 \uC694\uC57D
VisitSummaryViewModel.VisitTitle=\uBC29\uBB38 \uC774\uB984
VisitSummaryViewModel.ComfortItem=\uCF8C\uC801\uC9C0\uC218\uD3C9\uAC00
VisitSummaryViewModel.HealthItem=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00
VisitSummaryViewModel.NutritionItem=\uC870\uC0AC\uB8CC\uD3C9\uAC00
VisitSummaryViewModel.ProductivityItem=\uC0DD\uC0B0\uC9C0\uC218\uD3C9\uAC00
VisitSummaryViewModel.CategorySection=\uB3C4\uAD6C \uCE74\uD14C\uACE0\uB9AC
VisitSummaryViewModel.ComfortHeatStressBanner=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uB3C4\uAD6C \uC6B0\uC0AC
VisitSummaryViewModel.HeatstressEvaluationTitle=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uD3C9\uAC00
VisitSummaryViewModel.RumenHealthTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
VisitSummaryViewModel.RumenHealthTMRTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 TMR \uC785\uC790\uB3C4 \uC9C0\uC218
VisitSummaryViewModel.RumenHealthManureTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
VisitSummaryViewModel.RumenHealthLocomotionTitle=\uBCF4\uD589\uC9C0\uC218
VisitSummaryViewModel.RumenHealthBodyConditionTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728
VisitSummaryViewModel.InputsOutputsChart=\uC785\uB825 / \uCD9C\uB825 / \uCC28\uD2B8
VisitSummaryViewModel.NutritionForage=\uC870\uC0AC\uB8CC \uAC80\uC0AC
VisitSummaryViewModel.NutritionPile=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uBC0F \uBC99\uCEE4 \uC6A9\uB7C9
VisitSummaryViewModel.MilkProcessRevenueCalculator=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
VisitSummaryViewModel.NoToolPrompt=\uC5B4\uB5A4 \uB3C4\uAD6C\uB3C4 \uC644\uB8CC\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
VisitSummaryViewModel.VisitSummaryMilkCalc=\uC785\uB825 / \uACB0\uACFC / \uB9AC\uC18C\uC2A4
VisitSummaryViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
VisitSummaryViewModel.EmailReport=\uC774\uBA54\uC77C \uBCF4\uACE0\uC11C
VisitSummaryViewModel.FreeFormReport=\uC790\uC720\uD615\uC2DD \uBCF4\uACE0\uC11C
VisitSummaryViewModel.PenTimeTitle=우사 시간 비용
ProspectProfileViewModel.MainHeading=\uC0AC\uC774\uD2B8
ProspectProfileViewModel.NewSite=\uC0C8\uB85C\uC6B4 \uC0AC\uC774\uD2B8 \uCD94\uAC00
ProspectProfileViewModel.DeleteProspect=\uC7A0\uC7AC\uACE0\uAC1D \uC0AD\uC81C
ProspectProfileViewModel.DeleteProspectPrompt=\uC774 \uC7A0\uC7AC \uACE0\uAC1D\uC744 \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C? \uC7A0\uC7AC\uACE0\uAC1D, \uC0AC\uC774\uD2B8 \uBC0F \uC9C4\uD589\uC911\uC778 \uBC29\uBB38 \uC815\uBCF4\uAC00 \uC190\uC2E4\uB429\uB2C8\uB2E4.
NewProspectViewModel.Title=\uC138\uBD80\uC0AC\uD56D
NewProspectViewModel.ProspectDetail=\uC7A0\uC7AC\uACE0\uAC1D \uC138\uBD80\uC0AC\uD56D
NewProspectViewModel.CustomerDetail=\uACE0\uAC1D \uC138\uBD80\uC0AC\uD56D
NewProspectViewModel.BusinessName=\uC0AC\uC5C5\uCCB4 \uBA85
NewProspectViewModel.Image=\uC0AC\uC9C4\uC744 \uD3B8\uC9D1\uD558\uB824\uBA74 \uB204\uB974\uC138\uC694
NewProspectViewModel.PrimaryContactFirstName=\uAE30\uBCF8 \uC5F0\uB77D\uCC98 \uC774\uB984
NewProspectViewModel.PrimaryContactLastName=\uAE30\uBCF8 \uC5F0\uB77D\uCC98 \uC131
NewProspectViewModel.Address1=\uC0AC\uC5C5\uC7A5 \uC8FC\uC18C 1
NewProspectViewModel.Address2=\uC0AC\uC5C5\uC7A5 \uC8FC\uC18C 2
NewProspectViewModel.City=\uC2DC/\uAD70
NewProspectViewModel.State=\uB3C4
NewProspectViewModel.PostalCode=\uC6B0\uD3B8\uBC88\uD638
NewProspectViewModel.Country=\uAD6D\uAC00
NewProspectViewModel.PrimaryPhone=\uC5F0\uB77D\uCC98(\uC804\uD654\uBC88\uD638)
NewProspectViewModel.EmailAddress=\uC5F0\uB77D\uCC98(\uC774\uBA54\uC77C)
NewProspectViewModel.Type=\uD0C0\uC785
NewProspectViewModel.Segment=\uBD80\uBD84
NewProspectViewModel.Prospect=\uC804\uB9DD
NewProspectViewModel.NameNotUnique="{0}" \uC774\uB984\uC758 \uC7A0\uC7AC\uACE0\uAC1D\uC774 \uC774\uBBF8 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uC0C8\uB85C\uC6B4 \uC774\uB984\uC744 \uC785\uB825\uD558\uC138\uC694.
NewProspectViewModel.NullBusinessName=\uC0AC\uC5C5\uCCB4\uBA85\uC744 \uC785\uB825\uD574 \uC8FC\uC138\uC694.
NewProspectViewModel.NullFirstName=\uC774\uB984\uC774 \uC785\uB825\uD558\uC138\uC694.
NewProspectViewModel.NullSecondName=\uC131\uC744 \uC785\uB825\uD558\uC138\uC694.
NewProspectViewModel.InvalidEmail=\uC720\uD6A8\uD55C \uC774\uBA54\uC77C\uC8FC\uC18C\uB97C \uC785\uB825\uD558\uC138\uC694.
NewProspectViewModel.NotSet= -
Last_Synced=\uB9C8\uC9C0\uB9C9 \uB3D9\uAE30\uD654
Sync_Data=\uB370\uC774\uD130 \uB3D9\uAE30\uD654
FreeFormReportViewModel.Title=\uC790\uC720\uD615\uC2DD \uBCF4\uACE0\uC11C
FreeFormReportViewModel.VisitTitle=\uBC29\uBB38 \uC774\uB984
FreeFormReportViewModel.ExportSelected=\uC120\uD0DD\uD55C \uB3C4\uAD6C \uB0B4\uBCF4\uB0B4\uAE30
FreeFormReportViewModel.Inputs=\uC785\uB825
FreeFormReportViewModel.Results=\uACB0\uACFC
FreeFormReportViewModel.Outputs=\uCD9C\uB825
FreeFormReportViewModel.Charts=\uCC28\uD2B8
FreeFormReportViewModel.Notes=\uB178\uD2B8
FreeFormReportViewModel.ComfortItem=\uC548\uB77D\uD568 \uB3C4\uAD6C
FreeFormReportViewModel.HealthItem=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00 \uB3C4\uAD6C
FreeFormReportViewModel.NutritionItem=\uC601\uC591 \uB3C4\uAD6C
FreeFormReportViewModel.ProductivityItem=\uC0DD\uC0B0\uC131 \uB3C4\uAD6C
FreeFormReportViewModel.ComfortHeatStressBanner=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uB3C4\uAD6C \uC6B0\uC0AC
FreeFormReportViewModel.PenTimeTitle=\uC6B0\uC0AC \uC2DC\uAC04 \uBE44\uC6A9
FreeFormReportViewModel.RumenHealthTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
FreeFormReportViewModel.RumenHealthTMRTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 TMR \uC785\uC790\uB3C4 \uC9C0\uC218
FreeFormReportViewModel.RumenHealthTMRHerdTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 TMR \uC785\uC790\uB3C4 \uC9C0\uC218-\uC6B0\uAD70
FreeFormReportViewModel.RumenHealthManureTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
FreeFormReportViewModel.RumenHealthLocomotionTitle=\uBCF4\uD589\uC9C0\uC218
FreeFormReportViewModel.RumenHealthBodyConditionTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728
FreeFormReportViewModel.NutritionForage=\uC870\uC0AC\uB8CC \uAC80\uC0AC
FreeFormReportViewModel.NutritionPile=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uBC0F \uBC99\uCEE4 \uC6A9\uB7C9
FreeFormReportViewModel.PileAndBunkerFeedOutTab=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uBC0F \uBC99\uCEE4 \uAE09\uC5EC\uB7C9
FreeFormReportViewModel.MilkProcessRevenueCalculator=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
FreeFormReportViewModel.OverallScore=\uC804\uCCB4 \uC810\uC218
FreeFormReportViewModel.OverallResponses=\uC804\uCCB4\uC801\uC778 \uC751\uB2F5
FreeFormReportViewModel.OverallImprovements=\uC804\uCCB4\uC801\uC778 \uAC1C\uC120
FreeFormReportViewModel.MilkProcessCalcInputsTab=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC785\uB825
FreeFormReportViewModel.MilkProcessCalcResultsTab=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uACB0\uACFC
FreeFormReportViewModel.MilkProcessCalcResourcesTab=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC790\uB8CC
FreeFormReportViewModel.MarketingBranding=\uC870\uC9C1 \uBE0C\uB79C\uB4DC
FreeFormReportViewModel.Cargill=\uCE74\uAE38
FreeFormReportViewModel.Purina=\uD4E8\uB9AC\uB098
FreeFormReportViewModel.Provimi=\uD504\uB85C\uBE44\uBBF8
RevenueInputViewModel.Title=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC785\uB825
RevenueInputViewModel.Edit=\uD3B8\uC9D1
MilkProcessorResultsViewModel.Title=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uACB0\uACFC
MilkProcessorResourcesViewModel.Title=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC790\uB8CC
FreeFormReportViewModel.GeneralNotes=\uC77C\uBC18 \uBC29\uBB38 \uB178\uD2B8
FreeFormReportViewModel.WalkThroughNotes=\uBAA9\uC7A5 \uC810\uAC80 \uB178\uD2B8
MenuViewModel.Sync_PopUp=\uB370\uC774\uD130 \uB3D9\uAE30\uD654 \uC911..
\uB3D9\uAE30\uD654\uAC00 \uC9C4\uD589\uB418\uB294 \uB3D9\uC548 \uC571\uC744 \uC885\uB8CC\uD558\uC9C0 \uB9C8\uC2ED\uC2DC\uC694. \uC5F0\uACB0\uC18D\uB3C4\uAC00 \uB290\uB9B4\uACBD\uC6B0 \uB3D9\uAE30\uD654\uC5D0 \uB354 \uC624\uB79C \uC2DC\uAC04\uC774 \uAC78\uB9B4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
DashboardViewModel.GoodMorning=\uC548\uB155\uD558\uC138\uC694
DashboardViewModel.GoodAfternoon=\uC548\uB155\uD558\uC138\uC694
DashboardViewModel.MessageHeader=\uBA54\uC2DC\uC9C0
DashboardViewModel.MessageBody=\uAC01 \uC0AC\uC774\uD2B8 \uBC29\uBB38\uC5D0 \uB300\uD55C \uAD6C\uCCB4\uC801 \uC0AC\uD56D\uC744 \uAE30\uB85D\uD558\uAE30 \uC704\uD574 \uB178\uD2B8\uBD81\uC744 \uD65C\uC6A9\uD558\uC138\uC694.
DashboardViewModel.RecentSiteVisit=\uCD5C\uADFC \uC0AC\uC774\uD2B8 \uBC29\uBB38
DashboardViewModel.Alert=\uACBD\uACE0!!
DashboardViewModel.AlertMessage=\uB370\uC774\uD130\uAC00 {0} \uC77C \uC774\uC0C1 \uB3D9\uAE30\uD654\uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
ConfirmExport=\uD655\uC778\uC744 \uB204\uB974\uBA74 \uC120\uD0DD\uD55C \uBAA8\uB4E0 \uD398\uC774\uC9C0\uAC00 \uB85C\uB4DC\uB418\uACE0, \uC774 \uD654\uBA74\uC73C\uB85C \uB3CC\uC544 \uAC00\uAE30 \uC804\uC5D0 \uC0AC\uC9C4\uC774 \uCD2C\uC601\uB429\uB2C8\uB2E4.
ToolNotSelected=\uD558\uB098 \uC774\uC0C1\uC758 \uB3C4\uAD6C\uB97C \uC120\uD0DD\uD574\uC57C \uD569\uB2C8\uB2E4.
SelectOnlyTwoNotes=\uAC01 \uD56D\uBAA9\uBCC4\uB85C 2\uAC1C\uC758 \uB178\uD2B8\uB9CC \uC120\uD0DD\uD558\uC138\uC694.
ManureScoresChart=\uBD84\uBCC0\uC9C0\uC218 - \uCC28\uD2B8
ManureScoresResult=\uBD84\uBCC0\uC9C0\uC218 - \uACB0\uACFC
TMRParticleScoreHerdAnalysisMasterViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uC785\uC790\uB3C4 \uC9C0\uC218
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=\uC785\uB825
TMRParticleScoreHerdAnalysisMasterViewModel.Results=\uACB0\uACFC
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Top
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=\uC911\uB2E8 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=M\uC911\uB2E8 2
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=\uD2B8\uB808\uC774
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=\uBE44\uC720\uC77C\uB839 (DIM)
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=\uD3B8\uC9D1
TMRParticleScoreHerdAnalysisInputsViewModel.Title=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 \uBD84\uC11D
TMRParticleScoreHerdAnalysisResultsText=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 \uBD84\uC11D
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=\uBE44\uC720\uC77C\uB839 \uC785\uB825
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=\uB2EB\uAE30
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=\uBE44\uC720\uC77C\uB839 (DIM)
BCSHerdAnalysisMasterViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BCSHerdAnalysisMasterViewModel.BCSTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=\uC785\uB825
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=\uBAA9\uD45C
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=\uACB0\uACFC
BCSHerdAnalysisInputsViewModel.BCSAnalysis=BCS \uBD84\uC11D
BCSHerdAnalysisInputsViewModel.BCSEdit=\uD3B8\uC9D1
BCSHerdAnalysisInputsViewModel.BCS=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BCS=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BCSHerdAnalysisInputsViewModel.BCSDIM=\uBE44\uC720\uC77C\uB839 (DIM)
BCSHerdAnalysisInputsViewModel.BCSMilk=\uC720\uB7C9
BCSEditMilkAndDimViewModel.Title=\uBE44\uC720\uC77C\uB839 \uBC0F \uC720\uB7C9
BCSEditMilkAndDimViewModel.BCSDIMTitle=\uBE44\uC720\uC77C\uB839 (DIM)
BCSEditMilkAndDimViewModel.BCSMilkTitle=\uC720\uB7C9
NotebookBCSHerdAnalysisInputs=BCS \uC6B0\uAD70 \uBD84\uC11D \uC785\uB825
NotebookBCSHerdAnalysisGoals=BCS \uC6B0\uAD70 \uBD84\uC11D \uBAA9\uD45C
NotebookBCSHerdAnalysisResults=BCS \uC6B0\uAD70 \uBD84\uC11D \uACB0\uACFC
BodyConditionScoreMasterViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BodyConditionScoreMasterViewModel.SubHeading=\uC6B0\uAD70 \uBD84\uC11D
BodyConditionScoreMasterViewModel.Inputs=\uC785\uB825
BodyConditionScoreMasterViewModel.Goals=\uBAA9\uD45C
BodyConditionScoreMasterViewModel.Results=\uACB0\uACFC
TMRHerdAnalysisTableTitle=TMR \uC785\uC790\uB3C4 \uC9C0\uC218 \uC6B0\uAD70 \uBD84\uC11D
BCSHerdAnalysisResultsViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
BCSHerdAnalysisResultsViewModel.SubHeading=\uC6B0\uAD70 \uBD84\uC11D
BCSHerdAnalysisResultsViewModel.GraphTitle=BCS \uBD84\uC11D
BCSHerdAnalysisResultsViewModel.MinBCS=\uCD5C\uC18C\uAC12. BCS
BCSHerdAnalysisResultsViewModel.MaxBCS=\uCD5C\uB300\uAC12. BCS
BCSHerdAnalysisResultsViewModel.BCSAvg=BCS \uD3C9\uADE0
BCSHerdAnalysisResultsViewModel.MilkHeadDay=\uC720\uB7C9/\uB450/\uC77C
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Top (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=\uC911\uB2E8 1 (8mm))
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=\uC911\uB2E8 2 (4mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=\uD2B8\uB808\uC774
MilkProcessorInputViewModel.MilkPrice=\uC720\uC720\uB300(\uC6D0/kg)\uB300 ({0}/{1})
MilkProcessorInputViewModel.WeightImperialCWT=CWT (100\uD30C\uC6B4\uB4DC)
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorEditComparisonValuesViewModel.MilkPrice=\uC720\uB300(\uC6D0/kg) ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT (100\uD30C\uC6B4\uB4DC)
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorResultsViewModel.MilkPrice=\uC720\uB300 ({0}/{1})
MilkProcessorResultsViewModel.WeightImperialCWT=CWT (100\uD30C\uC6B4\uB4DC)
SyncFailed=\uD604\uC7AC \uB3D9\uAE30\uD654\uB97C \uC644\uB8CC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uC2DC\uB3C4\uD558\uC2ED\uC2DC\uC624.
NoResourcesAvailable=\uAC00\uC6A9\uD55C \uC815\uBCF4 \uBD80\uC871
ConfirmScorerSwitch=Scorer\uB97C \uBCC0\uACBD\uD558\uBA74 \uC785\uB825\uD55C \uB370\uC774\uD130\uAC00 \uCD08\uAE30\uD654\uB429\uB2C8\uB2E4.
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=\uC0AC\uC77C\uB9AC\uC9C0 \uC6D0\uBB3C\uAE30\uC900 \uBC00\uB3C4 {0} (\uBAA9\uD45C: &gt; {1})
VisitViewModel.NullVisitName=\uBC29\uBB38 \uC774\uB984\uC740 \uBE44\uC6CC\uB458 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uBC29\uBB38 \uC774\uB984\uC744 \uC785\uB825\uD558\uC138\uC694.
ManureScoreHerdAnalysisMasterViewModel.Inputs=\uC785\uB825
ManureScoreHerdAnalysisMasterViewModel.Goals=\uBAA9\uD45C
ManureScoreHerdAnalysisMasterViewModel.Results=\uACB0\uACFC
ManureScoreHerdAnalysisMasterViewModel.Title=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
ManureScoreHerdAnalysisMasterViewModel.SubHeading=\uC6B0\uAD70 \uBD84\uC11D
CustomerProspectsSegmentViewModel.Title=\uC138\uBD80\uC0AC\uD56D
CustomerProspectsSegmentViewModel.SelectSegment=\uBD80\uBD84 \uC120\uD0DD
CustomerProspectsSegmentViewModel.NotSet= -
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=\uCD5C\uC885 \uC0AC\uC6A9\uC790
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Walton=Walton
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=\uBD84\uBCC0\uC9C0\uC218 \uBD84\uC11D
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=\uD3B8\uC9D1
ManureScoreHerdAnalysisInputsViewModel.ManureScore=\uBD84\uBCC0\uC9C0\uC218
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=\uBE44\uC720\uC77C\uB839 (DIM)
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=\uBD84\uBCC0\uC9C0\uC218 \uBD84\uC11D
SCCPremiumDeduction=\uCCB4\uC138\uD3EC\uC218 \uC778\uC13C\uD2F0\uBE0C/\uD398\uB110\uD2F0(\uC6D0/kg) ({0}/{1})
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=\uBE44\uC720\uC77C\uB839 (DIM)
ManureScoreHerdAnalysisEditInputsViewModel.Title=\uBE44\uC720\uC77C\uB839 \uC785\uB825
ManureScoreHerdAnalysisEditInputsViewModel.Close=\uB2EB\uAE30
LocomotionHerdResultsViewModel.Title=\uBCF4\uD589\uC9C0\uC218 \uBD84\uC11D
LocomotionHerdRevenueViewModel.Revenue=\uC218\uC775
AverageMilkLoss=\uD3C9\uADE0 \uC720\uC190\uC2E4 ({0})
MilkLossDay=\uC720\uC190\uC2E4(kg/\uC77C)
MilkLossYear=\uC720\uC190\uC2E4(kg/\uB144)
RevenueLossDay=\uC218\uC775 \uC190\uC2E4 (\uC6D0/\uC77C)
RevenueLossYear=\uC218\uC775 \uC190\uC2E4 (\uC6D0/\uB144)
SegmentViewModel.Title=\uC138\uBD80\uC0AC\uD56D
SegmentViewModel.SegmentTitle=\uACE0\uAC1D \uC138\uADF8\uBA3C\uD2B8 \uC120\uD0DD
ManureScoreHerdGoalsViewModel.TableTitle=\uCC29\uC720 \uB2E8\uACC4\uBCC4 \uC810\uC218
ManureScoreHerdGoalsViewModel.Edit=\uD3B8\uC9D1
ManureScoreHerdGoalsViewModel.GoalMinTitle=\uBD84\uBCC0\uC9C0\uC218 \uBAA9\uD45C \uCD5C\uC18C\uAC12
ManureScoreHerdGoalsViewModel.GoalMaxTitle=\uBD84\uBCC0\uC9C0\uC218 \uBAA9\uD45C \uCD5C\uB300\uAC12
ManureScoreHerdGoalsViewModel.FarOffDry=\uAC74\uC720\uCD08\uAE30 (-21\uC77C \uC774\uC804)
ManureScoreHerdGoalsViewModel.CloseUpDry=\uAC74\uC720\uB9D0\uAE30 (-20~-1\uC77C)
ManureScoreHerdGoalsViewModel.Fresh=\uBD84\uB9CC\uC6B0 (0~15\uC77C)
ManureScoreHerdGoalsViewModel.EarlyLactation=\uBE44\uC720\uCD08\uAE30 (16~60\uC77C)
ManureScoreHerdGoalsViewModel.PeakMilk=\uC6B0\uC720 \uD53C\uD06C (61~120\uC77C)
ManureScoreHerdGoalsViewModel.MidLactation=\uBE44\uC720\uC911\uAE30 (121~200\uC77C)
ManureScoreHerdGoalsViewModel.LateLactation=\uBE44\uC720\uB9D0\uAE30 (201\uC77C \uC774\uC0C1)
ManureScoreHerdEditGoalsViewModel.Title=\uBAA9\uD45C \uD3B8\uC9D1
ManureScoreHerdEditGoalsViewModel.MinGoal=\uBD84\uBCC0\uC9C0\uC218 \uCD5C\uC18C\uAC12
ManureScoreHerdEditGoalsViewModel.MaxGoal=\uBD84\uBCC0\uC9C0\uC218 \uCD5C\uB300\uAC12
ManureScoreHerdEditGoalsViewModel.FarOffDry=\uAC74\uC720\uCD08\uAE30 (-21\uC77C \uC774\uC804)
ManureScoreHerdEditGoalsViewModel.CloseUpDry=\uAC74\uC720\uB9D0\uAE30 (-20~-1\uC77C)
ManureScoreHerdEditGoalsViewModel.Fresh=\uBD84\uB9CC\uC6B0 (0~15\uC77C)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=\uBE44\uC720\uCD08\uAE30 (16~60\uC77C)
ManureScoreHerdEditGoalsViewModel.PeakMilk=\uC6B0\uC720 \uD53C\uD06C (61~120\uC77C)
ManureScoreHerdEditGoalsViewModel.MidLactation=\uBE44\uC720\uC911\uAE30 (121~200\uC77C)
ManureScoreHerdEditGoalsViewModel.LateLactation=\uBE44\uC720\uB9D0\uAE30 (201\uC77C \uC774\uC0C1)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=\uB2EB\uAE30
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=\uCD5C\uC18C\uAC12. \uC810\uC218
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=\uCD5C\uB300\uAC12. \uC810\uC218
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=\uD3C9\uADE0
ManureScoreHerdAnalysisResultsViewModel.ManureScore=\uBD84\uBCC0\uC9C0\uC218
LocomotionHerdResultsViewModel.Category1=\uAD6C\uBD84 1.0
LocomotionHerdResultsViewModel.Category2=\uAD6C\uBD84 2.0
LocomotionHerdResultsViewModel.Category3=\uAD6C\uBD84 3.0
LocomotionHerdResultsViewModel.Category4=\uAD6C\uBD84 4.0
LocomotionHerdResultsViewModel.Category5=\uAD6C\uBD84 5.0
MetabolicIncidenceMasterViewModel.Title=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD
MetabolicIncidenceMasterViewModel.Inputs=\uC785\uB825
MetabolicIncidenceMasterViewModel.Outputs=\uCD9C\uB825
MetabolicIncidenceMasterViewModel.Charts=\uCC28\uD2B8
Eula=\uC18C\uD504\uD2B8\uC6E8\uC5B4 \uC0AC\uC6A9\uAD8C \uB3D9\uC758
Privacy_Statement=\uAC1C\uC778\uC815\uBCF4 \uBCF4\uD638\uC815\uCC45
Auto_Sync=\uC790\uB3D9 \uB3D9\uAE30\uD654
ShowEulaViewModel.Eula=\uB77C\uC774\uC120\uC2A4 \uB3D9\uC758
ShowEulaViewModel.EulaError=\uCD5C\uC885 \uC0AC\uC6A9\uC790 \uC0AC\uC6A9\uAD8C \uACC4\uC57D\uC740 \uD45C\uC2DC \uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uB2E4\uC2DC \uC2DC\uB3C4\uD558\uAE30 \uC804\uC5D0 \uC778\uD130\uB137\uC5D0 \uC5F0\uACB0\uD558\uC2ED\uC2DC\uC624.
NoResults=\uAC80\uC0C9 \uACB0\uACFC\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.
Search=\uAC80\uC0C9
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD \uBE44\uC728
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=\uD3B8\uC9D1
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=\uBC1C\uC0DD\uB960 (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=\uBAA9\uD45C (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=\uCC28\uC774 (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=\uB300\uC0AC\uC131\uC9C8\uBCD1 \uBC1C\uC0DD\uC728\uC774 \uC6B0\uAD70\uC758 \uC218\uC775\uC131\uC5D0 \uBBF8\uCE58\uB294 \uC601\uD5A5
MetabolicIncidenceOutputsViewModel.Title=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 \uC0B0\uCD9C
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=\uC5F0\uAC04 \uACBD\uC81C\uC801 \uC601\uD5A5
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=\uC720\uC190\uC2E4 \uAC00\uCE58
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=\uACF5\uD0DC\uC77C\uC218 \uC99D\uAC00
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=\uCE58\uB8CC \uBC0F \uAE30\uD0C0 \uBE44\uC6A9(\uB3C4\uD0DC, \uD3D0\uC0AC)-USD
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=\uC5F0\uAC04 \uACBD\uC81C\uC801 \uC601\uD5A5 - \uCD1D
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=\uCD1D \uBE44\uC6A9
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=\uBE44\uC6A9 / \uC816\uC18C
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=\uBAA9\uD45C (%)
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
MetabolicIncidenceEditOutputsViewModel.Metritis=\uC790\uAD81\uC5FC
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=\uC804\uC704
MetabolicIncidenceEditOutputsViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
MetabolicIncidenceEditOutputsViewModel.MilkFever=\uC720\uC5F4
MetabolicIncidenceEditOutputsViewModel.Dystocia=\uB09C\uC0B0
MetabolicIncidenceEditOutputsViewModel.DeathLoss=\uD3D0\uC0AC \uC190\uC2E4
MetabolicIncidenceEditOutputsViewModel.Title=\uBAA9\uD45C \uD3B8\uC9D1
MetabolicIncidenceEditOutputsViewModel.Close=\uB2EB\uAE30
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
MetabolicIncidenceOutputsViewModel.Metritis=\uC790\uAD81\uC5FC
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=\uC804\uC704
MetabolicIncidenceOutputsViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
MetabolicIncidenceOutputsViewModel.MilkFever=\uC720\uC5F4
MetabolicIncidenceOutputsViewModel.Dystocia=\uB09C\uC0B0
MetabolicIncidenceOutputsViewModel.DeathLoss=\uD3D0\uC0AC \uC190\uC2E4
MetabolicIncidenceOutputsViewModel.TotalLosses=\uCD1D \uC5F0\uAC04 \uC190\uC2E4
MetabolicIncidenceInputsViewModel.Herd=\uC6B0\uAD70 \uC218\uC900 \uC815\uBCF4
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=\uCD1D \uBD84\uB9CC\uC6B0 / \uB144
MetabolicIncidenceInputsViewModel.MilkPrice=\uC720\uB300(\uC6D0/kg)
MetabolicIncidenceInputsViewModel.ReplacementCowCost=\uD6C4\uBCF4\uCD95 \uBE44\uC6A9
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=\uACF5\uD0DC\uC77C\uC218 \uC99D\uAC00\uC5D0 \uB530\uB978 \uBE44\uC6A9
MetabolicIncidenceInputsViewModel.IncidenceCases=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD \uC0AC\uB840
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=\uD3C9\uAC00 \uAE30\uAC04 \uB3D9\uC548 \uBD84\uB9CC\uD55C \uC816\uC18C \uB450\uC218\uC640 \uB300\uC0AC\uC7A5\uC560 \uBC1C\uBCD1 \uAC74\uC218\uB97C \uC785\uB825\uD558\uC2ED\uC2DC\uC624. \uC774\uAC83\uC740 \uCD9C\uB825 \uD0ED\uC5D0\uC11C \uC5F0\uAC04 \uBC1C\uC0DD\uB960 \uBE44\uC6A9\uC73C\uB85C \uBCC0\uD658\uB429\uB2C8\uB2E4.
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=\uD3C9\uAC00\uB97C \uC704\uD55C \uCD1D \uBD84\uB9CC\uC6B0
MetabolicIncidenceInputsViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
MetabolicIncidenceInputsViewModel.Metritis=\uC790\uAD81\uC5FC
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=\uC804\uC704
MetabolicIncidenceInputsViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
MetabolicIncidenceInputsViewModel.MilkFever=\uC720\uC5F4
MetabolicIncidenceInputsViewModel.Dystocia=\uB09C\uC0B0
MetabolicIncidenceInputsViewModel.DeathLoss=\uD3D0\uC0AC \uC190\uC2E4
MetabolicIncidenceInputsViewModel.Mastitis=\uD3D0\uC0AC \uC190\uC2E4
MetabolicIncidenceInputsViewModel.PerformanceTreatment=\uC131\uACFC \uBC0F \uCC98\uB9AC \uBE44\uC6A9
MetabolicIncidenceInputsViewModel.PerformanceMessage=\uAC01 \uB300\uC0AC\uC131 \uC9C8\uBCD1\uC758 \uACBD\uC81C\uC801 \uC601\uD5A5\uC744 \uACC4\uC0B0\uD558\uAE30 \uC704\uD55C \uCC38\uC870 \uB370\uC774\uD130
MetabolicIncidenceInputsViewModel.Costs=\uBE44\uC6A9
MetabolicIncidenceInputsViewModel.MilkLossKg=\uCC29\uC720\uB2F9 \uC720\uC190\uC2E4 ({0})
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=\uACF5\uD0DC\uC77C\uC218 \uC99D\uAC00
MetabolicIncidenceInputsViewModel.TreatmentCost=\uCE58\uB8CC \uBE44\uC6A9
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
MetabolicIncidenceInputsEditViewModel.Metritis=\uC790\uAD81\uC5FC
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=\uC804\uC704
MetabolicIncidenceInputsEditViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
MetabolicIncidenceInputsEditViewModel.MilkFever=\uC720\uC5F4
MetabolicIncidenceInputsEditViewModel.Dystocia=\uB09C\uC0B0
MetabolicIncidenceInputsEditViewModel.DeathLoss=\uD3D0\uC0AC \uC190\uC2E4
MetabolicIncidenceInputsEditViewModel.Title=\uBE44\uC6A9 \uC18D\uC131 \uD3B8\uC9D1
MetabolicIncidenceInputsEditViewModel.Close=\uB2EB\uAE30
MetabolicIncidenceInputsEditViewModel.MilkCow=\uC720\uB7C9/\uB450({0})
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=\uACF5\uD0DC\uC77C\uC218
MetabolicIncidenceInputsEditViewModel.TreatmentCost=\uCE58\uB8CC \uBE44\uC6A9
MetabolicIncidenceInputsViewModel.Title=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 \uC785\uB825
PenTimePenSelectionViewModel.PenTimeSection=\uC6B0\uAD70
PenTimePenSelectionViewModel.Title=\uC6B0\uC0AC \uC2DC\uAC04 \uACC4\uD68D
PenTimePenSelectionViewModel.PenTimeBudgetTitle=\uC6B0\uC0AC \uC2DC\uAC04 \uACC4\uD68D
PenTimeBudgetPenMasterViewModel.Inputs=\uC785\uB825
PenTimeBudgetPenMasterViewModel.Compare=\uBE44\uAD50
PenTimeBudgetPenMasterViewModel.Results=\uACB0\uACFC
PenTimeBudgetPenMasterViewModel.Title=\uC6B0\uC0AC \uC2DC\uAC04 \uBE44\uC6A9
EmailReportViewModel.MarketBranding=\uC870\uC9C1 \uBE0C\uB79C\uB4DC
EmailReportViewModel.Title=\uC774\uBA54\uC77C \uBCF4\uACE0\uC11C
EmailReportViewModel.UserPreferences=\uC0AC\uC6A9\uC790 \uC138\uD305
EmailReportViewModel.Cargill=\uCE74\uAE38
EmailReportViewModel.Provimi=\uD504\uB85C\uBE44\uBBF8
EmailReportViewModel.Purina=\uD4E8\uB9AC\uB098
EmailReportViewModel.ExportSelected=\uC120\uD0DD\uD55C \uB3C4\uAD6C \uC774\uBA54\uC77C \uBCF4\uB0B4\uAE30
EmailReportViewModel.HealthItem=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00 \uB3C4\uAD6C
EmailReportViewModel.RumenHealthManureTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uBD84\uBCC0\uC9C0\uC218
EmailReportViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
EmailReportViewModel.ComfortHeatStressBanner=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uB3C4\uAD6C \uC6B0\uC0AC
EmailReportViewModel.ComfortPenTimeBanner=\uC6B0\uC0AC \uC2DC\uAC04 \uBE44\uC6A9 \uB3C4\uAD6C
EmailReportViewModel.HeatstressEvaluationTitle=\uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 \uD3C9\uAC00
EmailReportViewModel.ComfortItem=\uC548\uB77D\uD568 \uB3C4\uAD6C
EmailReportViewModel.RumenHealthTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uB418\uC0C8\uAE40\uC9C8
EmailReportViewModel.RumenHealthTMRTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 TMR \uC785\uC790\uB3C4 \uC9C0\uC218
EmailReportViewModel.RumenHealthLocomotionTitle=\uBCF4\uD589\uC9C0\uC218
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728
EmailReportViewModel.InputsOutputsChart=\uC785\uB825 / \uCD9C\uB825 / \uCC28\uD2B8
EmailReportViewModel.RumenHealthBodyConditionTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
EmailReportViewModel.NutritionForage=\uC870\uC0AC\uB8CC \uAC80\uC0AC
EmailReportViewModel.ForageLanding=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uB79C\uB529 \uD398\uC774\uC9C0
EmailReportViewModel.ForageScorecard=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
EmailReportViewModel.NutritionPile=\uC870\uC0AC\uB8CC \uB354\uBBF8 \uBC0F \uBC99\uCEE4 \uC6A9\uB7C9
EmailReportViewModel.NutritionItem=\uC870\uC0AC\uB8CC\uD3C9\uAC00
EmailReportViewModel.ProductivityItem=\uC0DD\uC0B0\uC131 \uB3C4\uAD6C
EmailReportViewModel.MilkProcessRevenueCalculator=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
EmailReportViewModel.MilkProcessCalcInputsTab=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC785\uB825
EmailReportViewModel.MilkProcessCalcResultsTab=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uACB0\uACFC
EmailReportViewModel.MilkProcessCalcResourcesTab=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50 \u2013 \uC790\uB8CC
EmailReportViewModel.GotoMarketBranding=\uC870\uC9C1 \uBE0C\uB79C\uB4DC
EmailReportViewModel.EmailSelectedTools=\uC120\uD0DD\uD55C \uB3C4\uAD6C \uC774\uBA54\uC77C \uBCF4\uB0B4\uAE30
EmailReportViewModel.UserSettings=\uC0AC\uC6A9\uC790 \uC138\uD305
EmailReportViewModel.ComfortToolsTitle=\uC548\uB77D\uD568 \uB3C4\uAD6C
EmailReportViewModel.Herd=\uC6B0\uAD70
EmailReportViewModel.Inputs=\uC785\uB825
EmailReportViewModel.Outputs=\uCD9C\uB825
EmailReportViewModel.Charts=\uCC28\uD2B8
EmailReportViewModel.ForageAuditScorecard=\uC870\uC0AC\uB8CC \uAC80\uC0AC \uC810\uC218\uD45C
EmailReportViewModel.EmailSubject={0} \uBCF4\uACE0\uC11C
EmailReportViewModel.EmailBody={0}-{1} \uBCF4\uACE0\uC11C
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} \uBCF4\uACE0\uC11C
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} \uBCF4\uACE0\uC11C
PenTimeInputsViewModel.CowsPen=\uC6B0\uC0AC \uB0B4 \uC816\uC18C
PenTimeInputsViewModel.StallsPen=\uC6B0\uC0AC\uB0B4 \uC2A4\uD1A8
PenTimeInputsViewModel.WalkingTimeTo=\uD314\uB7EC\uB85C \uAC00\uB294 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeInputsViewModel.ParlorTime=\uD314\uB7EC\uB0B4 \uC18C\uC694 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeInputsViewModel.WalkingTimeFrom=\uD314\uB7EC\uB85C\uBD80\uD130 \uB3CC\uC544\uC624\uB294 \uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeInputsViewModel.Frequency=\uCC29\uC720 \uBE48\uB3C4(\uC77C)
PenTimeInputsViewModel.TotalStalls=\uD314\uB7EC\uC5D0 \uCD1D \uC2A4\uD1A8\uC218
PenTimeInputsViewModel.LockUp=\uC7A0\uAE08 \uC2DC\uAC04(\uC2DC\uAC04)
PenTimeInputsViewModel.NonRestTime=\uAE30\uD0C0 \uBE44\uD734\uC2DD\uC2DC\uAC04(\uC2DC)
PenTimeInputsViewModel.Resting=\uD544\uC218 \uD734\uC2DD\uC2DC\uAC04 (\uC2DC\uAC04)
PenTimeInputsViewModel.Eating=\uC12D\uCDE8 \uC2DC\uAC04(\uC2DC)
PenTimeInputsViewModel.Drinking=\uC74C\uC218 / \uD138\uC190\uC9C8 \uC2DC\uAC04 (\uC2DC)
MetabolicIncidenceChartsViewModel.Title=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 \uCC28\uD2B8
MetabolicIncidenceChartsViewModel.GraphTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728 %
MetabolicIncidenceChartsViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
MetabolicIncidenceChartsViewModel.Metritis=\uC790\uAD81\uC5FC
Metritis=\uC790\uAD81\uC5FC
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=\uC804\uC704
DisplacedAbomasum=\uC804\uC704
MetabolicIncidenceChartsViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
MetabolicIncidenceChartsViewModel.MilkFever=\uC720\uC5F4
MilkFever=\uC720\uC5F4
MetabolicIncidenceChartsViewModel.Dystocia=\uB09C\uC0B0
Dystocia=\uB09C\uC0B0
MetabolicIncidenceChartsViewModel.DeathLoss=\uD3D0\uC0AC \uC190\uC2E4
DeathLoss=\uD3D0\uC0AC \uC190\uC2E4
MetabolicIncidenceChartsViewModel.GoalPercent=\uBAA9\uD45C (%)
MetabolicIncidenceChartsViewModel.IncidencePercent=\uBC1C\uC0DD\uB960 (%)
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=\uB300\uC0AC\uC7A5\uC560 \uBE44\uC6A9/\uC816\uC18C
MetabolicIncidenceChartsViewModel.Current=\uD604\uC7AC
EditDatesForComparisonViewModel.MetabolicIncidence=\uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uBE44\uAD50\uD558\uAE30 \uC704\uD574 \uBC29\uBB38 \uB0A0\uC9DC\uB97C \uCD5C\uB300 5 \uAC1C\uAE4C\uC9C0 \uC120\uD0DD\uD558\uC138\uC694.
PenTimeInputsViewModel.PenTimeTitle=\uC6B0\uC0AC \uC2DC\uAC04 \uACC4\uD68D
ChooseAppPDF=PDF\uB97C \uBCF4\uAE30 \uC704\uD574\uC11C\uB294 \uC571\uC744 \uC120\uD0DD\uD558\uC138\uC694
PenTimeBudgetResultsViewModel.Title=\uC6B0\uC0AC \uC2DC\uAC04 \uACC4\uD68D - \uACB0\uACFC
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=\uC26C\uB294 \uC2DC\uAC04
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=\uC7A0\uC7AC\uC801 \uC720\uC190\uC2E4 / \uC774\uB4DD
PenTimeBudgetResultsViewModel.TimeRequired=\uC18C\uC694 \uC2DC\uAC04
PenTimeBudgetResultsViewModel.TimeRemaining=\uB0A8\uC740 \uC2DC\uAC04
PenTimeBudgetResultsViewModel.Hours=\uC2DC\uAC04
PenTimeBudgetResultsViewModel.MilkDifference=\uC7A0\uC7AC\uC801\uC778 \uC6B0\uC720 \uCC28\uC774
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=\uD30C\uC6B4\uB4DC(0.45kg)
TimeRemaining=\uD734\uC2DD\uC744 \uC704\uD574 \uB0A8\uC740 \uC2DC\uAC04
MilkLossGain=\uC7A0\uC7AC\uC801 \uC720\uC190\uC2E4 / \uC774\uB4DD
EditDatesForComparisonViewModel.PenTimeBudget=\uC544\uB798 \uBAA9\uB85D\uC5D0\uC11C \uBE44\uAD50\uD558\uAE30 \uC704\uD574 \uBC29\uBB38 \uB0A0\uC9DC\uB97C \uCD5C\uB300 7 \uAC1C\uAE4C\uC9C0 \uC120\uD0DD\uD558\uC138\uC694.
WalkthroughReportLandingViewModel.Title=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
WalkthroughReportLandingViewModel.PenAnalysis=\uC6B0\uC0AC \uBD84\uC11D
WalkthroughReportLandingViewModel.HerdAnalysis=\uC6B0\uAD70 \uBD84\uC11D
ShowPrivacyStatementViewModel.PrivacyStatementTitle=\uAC1C\uC778\uC815\uBCF4 \uBCF4\uD638\uC815\uCC45
ShowPrivacyStatementViewModel.PrivacyStatement=<value />
NoteCategoryViewModel.Title=\uB178\uD2B8
NoteCategoryViewModel.SelectCategory=\uCE74\uD14C\uACE0\uB9AC \uC120\uD0DD
NoteCategoryViewModel.FooterText=\uB178\uD2B8\uB2F9 \uD558\uB098\uC758 \uCE74\uD0DC\uACE0\uB9AC\uB9CC \uC120\uD0DD\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
Observation=\uAD00\uCC30
Action=\uC791\uB3D9
WalkthroughPenSelectionViewModel.Title=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
WalkthroughPenSelectionViewModel.Pens=\uC6B0\uAD70
Task=\uC791\uC5C5
Event=\uC774\uBCA4\uD2B8
SiteVisitSummaryReport=\uC0AC\uC774\uD2B8 \uBC29\uBB38 \uC694\uC57D \uBCF4\uACE0\uC11C
WalkthroughReport=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
Customer=\uACE0\uAC1D
CustomerWithSiteName=\uACE0\uAC1D \uC774\uB984 - \uC0AC\uC774\uD2B8 \uC774\uB984
VisitDate=\uBC29\uBB38 \uB0A0\uC9DC
ReportDate=\uBCF4\uACE0\uC11C \uB0A0\uC9DC
Report=\uBCF4\uACE0\uC11C
PDFPageNumber=\uD398\uC774\uC9C0 {0} of {1}
PDFDisclaimer=\uCE74\uAE38 Inc. \uBC0F \uAD00\uB828 \uC885\uC0AC\uC790\uB294 \uC5EC\uB7EC \uC694\uC18C\uB85C \uC778\uD574 \uC81C\uACF5\uB418\uB294 \uCD94\uC815\uCE58\uAC00 \uC815\uD655\uD558\uB2E4\uACE0 \uBCF4\uC7A5\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC0DD\uC0B0\uB7C9 \uB610\uB294 \uC7AC\uBB34\uC801 \uACB0\uACFC\uC5D0 \uB300\uD55C \uBCF4\uC7A5\uB3C4 \uC5C6\uC2B5\uB2C8\uB2E4.  \u00A9{0} Cargill, Incorporated. All Rights Reserved
SiteVisitSummary=\uC0AC\uC774\uD2B8 \uBC29\uBB38 \uC694\uC57D
AnimalsInPen=\uC6B0\uC0AC\uB0B4 \uC0AC\uC721\uB450\uC218
AnimalInformation=\uAC00\uCD95 \uC815\uBCF4
LocomotionNumberinPen=\uBCF4\uD589\uC9C0\uC218 (\uC6B0\uC0AC\uB0B4 \uC810\uC218)
LocomotionPercentofPen=\uBCF4\uD589\uC9C0\uC218(\uC6B0\uC0AC\uB0B4 \uBE44\uC728)
AnimalsInHerd=\uC6B0\uAD70\uB0B4 \uC0AC\uC721\uB450\uC218
LocomotionNumberinHerd=\uBCF4\uD589\uC9C0\uC218 (\uC6B0\uAD70\uB0B4 \uC810\uC218)
LocomotionPercentofHerd=\uBCF4\uD589\uC9C0\uC218 (\uC6B0\uAD70\uB0B4 \uBE44\uC728)
CudChewingPercentOfPen=\uBC18\uCD94\uBE44\uC728(\uC6B0\uC0AC\uB0B4 \uBE44\uC728)
ManureScorePercentOfPen=\uBD84\uC2A4\uCF54\uC5B4(\uC6B0\uC0AC\uB0B4 \uBE44\uC728)
CudChewingAverageNumber=\uD3C9\uADE0 \uBC18\uCD94\uD69F\uC218
CudChewingPen=\uC6B0\uC0AC
CudChewingPercentChewing=\uBC18\uCD94 \uBE44\uC728(%)
ChewsPerCud=\uBC18\uCD94\uD68C\uB2F9 \uC539\uB294 \uD69F\uC218
WalkthroughReportViewModel.Title=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
WalkthroughReportViewModel.CudChewing=\uBC18\uCD94, \uC800\uC791 %
WalkthroughReportViewModel.CudChewCategorySection=\uBC18\uCD94 \uD68C\uC218, \uBC18\uCD94\uB2F9 \uC800\uC791 \uD69F\uC218
WalkthroughReportViewModel.RumenHealthManureTitle=\uBD84\uBCC0\uC9C0\uC218
WalkthroughReportViewModel.RumenHealthLocomotionTitle=\uBCF4\uD589\uC9C0\uC218
WalkthroughReportViewModel.HockAbrasion=\uAD00\uC808 \uCC30\uACFC\uC0C1 : \uAC1C\uCCB4 %
WalkthroughReportViewModel.Appearance=\uC678\uAD00
WalkthroughReportViewModel.RumenFill=\uBC18\uCD94\uC704 \uCDA9\uB9CC\uB3C4
WalkthroughReportViewModel.UterineDischarge=\uC790\uAD81 \uBD84\uBE44\uBB3C, \uAC1C\uCCB4\uC758   %
WalkthroughReportViewModel.NasalDischarge=\uCF67\uBB3C \uD758\uB9AC\uB294 \uAC1C\uCCB4 %
WalkthroughReportViewModel.ComfortItem=\uC816\uC18C \uCF8C\uC801\uC9C0\uC218, \uC549\uC544 \uC788\uB294 \uC816\uC18C \uBE44\uC728(%)
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
WalkthroughReportViewModel.WaterQuality=\uC218\uC9C8
WalkthroughReportViewModel.BeddingCleanliness=\uC6B0\uC0C1 \uCCAD\uACB0\uB3C4
WalkthroughReportViewModel.BeddingDepthSoft=\uC6B0\uC0C1: \uAE4A\uC774/\uBD80\uB4DC\uB7EC\uC6C0
WalkthroughReportViewModel.Trends=\uAE0D\uC815\uC801\uC778 \uB3D9\uD5A5
WalkthroughReportViewModel.Opportunities=\uAE30\uD68C
WalkthroughReportViewModel.Comments=\uB17C\uD3C9
WalkthroughReportViewModel.Current=\uD604\uD669
WalkthroughReportViewModel.Previous=\uC774\uC804
WalkthroughReportViewModel.Goals=\uBAA9\uD45C
WalkthroughReportViewModel.Clean=\uAE68\uB057\uD568
WalkthroughReportViewModel.ModeratelyClean=\uC911\uAC04\uB2E8\uACC4 \uCCAD\uACB0
WalkthroughReportViewModel.Dirty=\uC9C0\uC800\uBD84\uD568
WalkthroughReportQualityViewModel.Title=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
WalkthroughReportQualityViewModel.Clean=\uAE68\uB057\uD568
WalkthroughReportQualityViewModel.ModeratelyClean=\uC911\uAC04\uB2E8\uACC4 \uCCAD\uACB0
WalkthroughReportQualityViewModel.Dirty=\uC9C0\uC800\uBD84\uD568
WalkthroughReportQualityViewModel.WaterQuality=\uC218\uC9C8 \uC120\uD0DD
WalkthroughReportQualityViewModel.BeddingCleanliness=\uC6B0\uC0C1 \uCCAD\uACB0 \uC120\uD0DD
Send=\uBCF4\uB0B4\uAE30
WalkthroughReportHerdAnalysisViewModel.PensForExport=\uC6B0\uC0AC \uC815\uBCF4 \uB0B4\uBCF4\uB0B4\uAE30
WalkthroughReportHerdAnalysisViewModel.FinalObservations=\uCD5C\uC885 \uAD00\uCC30
WalkthroughReportHerdAnalysisViewModel.Trends=\uAE0D\uC815\uC801\uC778 \uB3D9\uD5A5
WalkthroughReportHerdAnalysisViewModel.Opportunities=\uAE30\uD68C
WalkthroughReportHerdAnalysisViewModel.Comments=\uB17C\uD3C9
WalkthroughReportHerdAnalysisViewModel.Branding=\uC870\uC9C1 \uBE0C\uB79C\uB4DC
WalkthroughReportHerdAnalysisViewModel.Cargill=\uCE74\uAE38
WalkthroughReportHerdAnalysisViewModel.Provimi=\uD504\uB85C\uBE44\uBBF8
WalkthroughReportHerdAnalysisViewModel.Purina=\uD4E8\uB9AC\uB098
WalkthroughReportHerdAnalysisViewModel.Title=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C - \uC6B0\uAD70 \uBD84\uC11D
WalkthroughReportHerdAnalysisViewModel.MainHeading=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
WalkthroughReportHerdAnalysisViewModel.SubHeading=\uC6B0\uAD70 \uBD84\uC11D
WalkthroughReportHerdAnalysisViewModel.ExportSelected=\uC120\uD0DD\uD55C Tools \uC774\uBA54\uC77C \uBCF4\uB0B4\uAE30
EmailReportViewModel.CudChewingTitle=\uBC18\uCD94\uB450\uC218
EmailReportViewModel.Results=\uACB0\uACFC
EmailReportViewModel.NumOfChews=\uC800\uC791 \uAC1C\uCCB4 \uC218
EmailReportViewModel.PenInputs=\uC6B0\uC0AC \uBD84\uC11D - \uC785\uB825
EmailReportViewModel.PenResults=\uC6B0\uC0AC \uBD84\uC11D - \uACB0\uACFC
EmailReportViewModel.HerdInputs=\uC6B0\uAD70 \uBD84\uC11D - \uC785\uB825
EmailReportViewModel.HerdResults=\uC6B0\uAD70 \uBD84\uC11D - \uACB0\uACFC
EmailReportViewModel.TMRParticleScoreTitle=\uBC18\uCD94\uC704 \uAC74\uAC15 \uC785\uC790\uB3C4 \uC9C0\uC218
EmailReportViewModel.HerdGoals=\uC6B0\uAD70 \uBD84\uC11D - \uBAA9\uD45C
EmailReportViewModel.ManureScoreTitle=\uBD84\uBCC0\uC9C0\uC218
EmailReportViewModel.LocomotionScoreTitle=\uBCF4\uD589\uC9C0\uC218
EmailReportViewModel.HerdRevenue=\uC6B0\uAD70 \uBD84\uC11D - \uC218\uC775
EmailReportViewModel.MetabolicIncidenceTitle=\uB300\uC0AC\uC7A5\uC560 \uBC1C\uC0DD\uC728
EmailReportViewModel.PileAndBunkerTitle=\uD30C\uC77C\uACFC \uBC99\uCEE4
EmailReportViewModel.PileBunkerCapacities=\uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uB354\uBBF8 \uC6A9\uB7C9
EmailReportViewModel.Capacity=\uC6A9\uB7C9
EmailReportViewModel.FeedOut=\uC0AC\uB8CC\uAE09\uC5EC\uB7C9
LocomotionHerdRevenueViewModel.Title=\uBCF4\uD589\uC9C0\uC218 \uC6B0\uAD70 \uC218\uC775
EmailReportViewModel.PenCompare=\uC6B0\uC0AC \uBD84\uC11D - \uBE44\uAD50
EmailReportViewModel.MilkProcessRevenue=\uCC29\uC720 \uC21C\uC11C \uBE44\uAD50
EmailReportViewModel.Resources=\uCC38\uC870
EmailReportViewModel.CategoryList=\uAD6C\uBD84 \uBAA9\uB85D
EmailReportViewModel.Improvements=\uAC1C\uC120
EmailReportViewModel.ScoreScreen=\uC810\uC218
FinalObservations=\uCD5C\uC885 \uAD00\uCC30
Trends=\uAE0D\uC815\uC801\uC778 \uB3D9\uD5A5
Opportunities=\uAE30\uD68C
Comments=\uB17C\uD3C9
Current=\uD604\uD669
Goal=\uBAA9\uD45C
Clean=\uAE68\uB057\uD568
ModeratelyClean=\uC911\uAC04\uB2E8\uACC4 \uCCAD\uACB0
Dirty=\uB354\uB7EC\uC6C0
WalkthroughReportHerdAnalysisViewModel.CudChewing=\uBC18\uCD94, \uC800\uC791 %
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=\uBC18\uCD94 \uD68C\uC218, \uBC18\uCD94\uB2F9 \uC800\uC791 \uD69F\uC218
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=\uBD84\uBCC0\uC9C0\uC218
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=\uBCF4\uD589\uC9C0\uC218
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=\uAD00\uC808 \uCC30\uACFC\uC0C1 : \uAC1C\uCCB4 %
WalkthroughReportHerdAnalysisViewModel.Appearance=\uC678\uAD00
WalkthroughReportHerdAnalysisViewModel.RumenFill=\uBC18\uCD94\uC704 \uCDA9\uB9CC
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=\uC790\uAD81 \uBD84\uBE44\uBB3C, \uAC1C\uCCB4\uC758 %
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=\uCF67\uBB3C \uD758\uB9AC\uB294 \uAC1C\uCCB4 %
WalkthroughReportHerdAnalysisViewModel.ComfortItem=\uC816\uC18C \uCF8C\uC801\uC9C0\uC218, \uAC1C\uCCB4 \uBC30\uCE58 \uBE44\uC728(%)
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
WalkthroughReportHerdAnalysisViewModel.WaterQuality=\uC218\uC9C8
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=\uC6B0\uC0C1 \uCCAD\uACB0
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=\uC6B0\uC0C1: \uAE4A\uC774/\uBD80\uB4DC\uB7EC\uC6C0
SiteDetailViewModel.ReportNotAvailable=\uBCF4\uACE0\uC11C\uB97C \uB2E4\uC6B4\uB85C\uB4DC \uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.
SiteDetailViewModel.ReportNotAvailableTitle=\uC790\uACA9
SiteDetailViewModel.ReportDownloadTimeout=\uD30C\uC77C\uC744 \uB2E4\uC6B4\uB85C\uB4DC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC778\uD130\uB137 \uC5F0\uACB0\uC774 \uC6D0\uD65C\uD560 \uB54C \uB2E4\uC2DC \uC2DC\uB3C4\uD574 \uC8FC\uC138\uC694.
SiteDetailViewModel.GetReportMsg=\uBCF4\uACE0\uC11C \uB2E4\uC6B4\uB85C\uB4DC\uC911\u2026
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0.0
PublishVisit=\uAC8C\uC2DC\uB428
CurrentSCC=\uD604 \uCCB4\uC138\uD3EC\uC218 (\uAC1C/{0})
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=\uC785\uC790\uB3C4 (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=\uC0C1\uB2E8(19mm) \uBAA9\uD45C
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=\uC911\uB2E81(8mm) \uBAA9\uD45C
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=\uC911\uB2E82(4mm) \uBAA9\uD45C
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=\uC911\uB2E82(1.18mm) \uBAA9\uD45C
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=\uD558\uB2E8 \uBAA9\uD45C
RumenHealthTMRParticleScorePenTableInputViewModel.Min=\uCD5C\uC18C\uAC12
RumenHealthTMRParticleScorePenTableInputViewModel.Max=\uCD5C\uB300\uAC12
RumenHealthTMRParticleScorePenTableInputViewModel.Current=\uD604\uC7AC
ReadyToMilkMasterViewModel.Title=Ready2Milk\u2122 \uC9C0\uC218
ReadyToMilkInputViewModel.Title=Ready2Milk\u2122 \uC785\uB825
ReadyToMilkOutputViewModel.Title=Ready2Milk\u2122 \uCD9C\uB825
ReadyToMilkChartViewModel.Title=Ready2Milk\u2122 \uCC28\uD2B8
ReadyToMilkMasterViewModel.Inputs=\uC785\uB825
ReadyToMilkMasterViewModel.Outputs=\uCD9C\uB825
ReadyToMilkMasterViewModel.Charts=\uCC28\uD2B8
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Ready2Milk\u2122 \uC9C0\uC218
ReadyToMilkChartViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
ReadyToMilkChartViewModel.Metritis=\uC790\uAD81\uC5FC
ReadyToMilkChartViewModel.DisplacedAbomasum=\uC804\uC704
ReadyToMilkChartViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
ReadyToMilkChartViewModel.MilkFever=\uC720\uC5F4
ReadyToMilkChartViewModel.Dystocia=\uB09C\uC0B0
ReadyToMilkChartViewModel.DeathLoss=\uC77C\uB839 \uC720\uBC29\uC5FC
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Ready2Milk\u2122 \uC9C0\uC218
ReadyToMilkChartViewModel.DisorderGraphTitle=\uC5F0\uAC04 \uB300\uC0AC\uC131 \uC9C8\uBCD1 \uBE44\uC6A9/\uCC29\uC720\uC6B0
ReadyToMilkChartViewModel.Current=\uD604\uC7AC
ReadyToMilkListViewModel.Title=Ready2Milk\u2122 \uC9C0\uC218
NotebookReadyToMilkInputs=ReadyToMilk \uC785\uB825
NotebookReadyToMilkOutputs=ReadyToMilk \uCD9C\uB825
NotebookReadyToMilkCharts=ReadyToMilk \uCC28\uD2B8
None=\uC5C6\uC74C
Moderate=\uC801\uB2F9\uD55C
Severe=\uC2EC\uD55C
Good=\uC6B0\uC218\uD55C
Medium=\uC801\uC808\uD55C
Poor=\uBD88\uB7C9\uD55C
Normal= &lt;0.5\uC774\uD558 \uB2E8\uC704
Excessive= &gt;0.5\uC774\uC0C1 \uB2E8\uC704
ReadyToMilkInputViewModel.HealthRecords=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00
ReadyToMilkInputViewModel.Herd=\uC6B0\uAD70 \uC218\uC900 \uC815\uBCF4
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=\uCD1D \uBD84\uB9CC\uC6B0 / \uB144
ReadyToMilkInputViewModel.MilkPrice=\uC720\uB300
ReadyToMilkInputViewModel.ReplacementCowCost=\uD6C4\uBCF4\uCD95 \uBE44\uC6A9
ReadyToMilkInputViewModel.CostExtraDaysOpen=\uACF5\uD0DC\uC77C\uC218 \uC99D\uAC00\uC5D0 \uB530\uB978 \uBE44\uC6A9
ReadyToMilkInputViewModel.CloseUp=\uC804\uD658\uAE30 \uC816\uC18C
ReadyToMilkInputViewModel.SpecificCloseUpDiet=\uC804\uD658\uAE30 \uC804\uC6A9 \uC0AC\uB8CC
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=\uC804\uD658\uAE30 \uB354\uC704 \uC2A4\uD2B8\uB808\uC2A4 \uC815\uB3C4
ReadyToMilkInputViewModel.ComfortCloseUp=\uC804\uD658\uAE30 \uD3B8\uC548\uD568 \uC815\uB3C4
ReadyToMilkInputViewModel.FreshCows=\uAC13 \uBD84\uB9CC\uD55C \uC816\uC18C(DIM 0-21\uC77C)
ReadyToMilkInputViewModel.SpecificDiet=DIM 0-21\uC77C\uB839 \uC804\uC6A9 \uC0AC\uB8CC
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=DIM 0-21\uC77C\uB839 \uB354\uC704 \uC2A4\uD2B8\uB808\uC2A4 \uC815\uB3C4
ReadyToMilkInputViewModel.ComfortDiet=DIM 0-21\uC77C\uB839 \uD3B8\uC548\uD568 \uC815\uB3C4
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=\uAC74\uC720-DIM 21\uC77C\uB839 BCS \uBCC0\uD654
ReadyToMilkInputViewModel.CudChewingDiet=DIM 0-21\uC77C\uB839 \uBC18\uCD94\uD69F\uC218
ReadyToMilkInputViewModel.LocomotionScore=\uBCF4\uD589\uC9C0\uC218
ReadyToMilkInputViewModel.RumenFill=\uBC18\uCD94\uC704 \uCDA9\uB9CC\uC9C0\uC218
ReadyToMilkInputViewModel.MilkYield=DIM 15-60\uC77C\uB839 \uC720\uC0DD\uC0B0\uB7C9
ReadyToMilkInputViewModel.Health=\uAC74\uAC15
ReadyToMilkInputViewModel.HealthDesc=\uD3C9\uAC00 \uAE30\uAC04 \uB3D9\uC548 \uBD84\uB9CC\uD55C \uC816\uC18C \uB450\uC218\uC640 \uB300\uC0AC\uC7A5\uC560 \uBC1C\uBCD1 \uAC74\uC218\uB97C \uC785\uB825\uD558\uC2ED\uC2DC\uC624. \uC774\uAC83\uC740 \uCD9C\uB825 \uD0ED\uC5D0\uC11C \uC5F0\uAC04 \uBC1C\uC0DD\uB960 \uBE44\uC6A9\uC73C\uB85C \uBCC0\uD658\uB429\uB2C8\uB2E4.
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=\uD3C9\uAC00\uB97C \uC704\uD55C \uCD1D \uBD84\uB9CC\uC6B0
ReadyToMilkInputViewModel.MilkFever=\uC720\uC5F4
ReadyToMilkInputViewModel.Ketosis=\uCF00\uD1A0\uC2DC\uC2A4
ReadyToMilkInputViewModel.DisplacedAbomasum=\uC804\uC704
ReadyToMilkInputViewModel.PrematureCalvings=\uBBF8\uC131\uC219 \uBD84\uB9CC
ReadyToMilkInputViewModel.Dystocia=\uB09C\uC0B0
ReadyToMilkInputViewModel.RetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
ReadyToMilkInputViewModel.Metritis=\uC790\uAD81\uC5FC
ReadyToMilkInputViewModel.Mastitis=DIM 0-21\uC77C\uB839 \uC720\uBC29\uC5FC
ReadyToMilkInputViewModel.SccFirstTest=\uCCB4\uC138\uD3EC 1\uCC28 \uAC80\uC0AC\uACB0\uACFC(x1000 SCC/ml)
ReadyToMilkInputViewModel.DeadCowsOrCulled=\uAC74\uAC15\uAD00\uB828 \uC0AC\uB9DD \uB610\uB294 \uB3C4\uD0DC
ReadyToMilkInputViewModel.Next=\uB4A4\uB85C
ReadyToMilkInputViewModel.Back=\uB2E4\uC74C
StrategyToReduceIncidence=\uBC1C\uBCD1\uC744 \uC904\uC774\uAE30 \uC704\uD55C \uC804\uB7B5
StrategyToReduceRetainedPlacenta=\uD6C4\uC0B0\uC815\uCCB4
StrategyToReduceMetritis=\uC790\uAD81\uC5FC
StrategyToReduceDisplacedAbomasum=\uC804\uC704
StrategyToReduceKetosis=\uCF00\uD1A0\uC2DC\uC2A4
StrategyToReduceMilkFever=\uC720\uC5F4
StrategyToReduceDystocia=\uB09C\uC0B0
StrategyToReduceMastitis=\uC77C\uB839 \uC720\uBC29\uC5FC
EmailReportViewModel.PenDensity=\uC6B0\uC0AC\uB0B4 \uC0AC\uC721\uBC00\uB3C4
EmailReportViewModel.MilkingTime=\uCC29\uC720 \uC2DC\uAC04
EmailReportViewModel.TimeBudget=\uC2DC\uAC04 \uACC4\uD68D
EmailReportViewModel.AnimalImpact=\uAC00\uCD95 \uC601\uD5A5
BCSSelectPointScaleViewModel.SelectPointScale=\uAE30\uC900\uC774 \uB418\uB294 BCS 1/2/3/4/5\uC911 \uD558\uB098\uB97C \uC120\uD0DD\uD558\uC138\uC694
BCSSelectPointScaleViewModel.FooterText=\uB9E4 \uBC29\uBB38\uC2DC 1\uC774\uB0B4\uC758 \uAC12\uB9CC \uC785\uB825\uB429\uB2C8\uB2E4. \uAE30\uC900\uC774 \uB418\uB294 BCS\uC758 \uBCC0\uD654\uAC00 \uC0DD\uAE30\uBA74 \uAE30\uC874 \uAC12\uC774 \uC5C6\uC5B4\uC9D1\uB2C8\uB2E4.
BCSSelectPointScaleViewModel.Title=\uC2E0\uCCB4\uCDA9\uC2E4\uC9C0\uC218 (BCS)
ConfirmScalePointSwitch=\uAE30\uC900\uC774 \uB418\uB294 BCS\uB97C \uBC14\uAFB8\uBA74 \uAE30\uC874\uC5D0 \uC785\uB825\uB41C \uAC12\uC774 \uCD08\uAE30\uD654\uB429\uB2C8\uB2E4.
NotebookBCSSelectPointScale=\uB9E4 \uBC29\uBB38\uC2DC 1\uC774\uB0B4\uC758 \uAC12\uB9CC \uC785\uB825\uB429\uB2C8\uB2E4. \uAE30\uC900\uC774 \uB418\uB294 BCS\uC758 \uBCC0\uD654\uAC00 \uC0DD\uAE30\uBA74 \uAE30\uC874 \uAC12\uC774 \uC5C6\uC5B4\uC9D1\uB2C8\uB2E4.
NotebookSectionHealthTools=\uAC74\uAC15\uC9C0\uC218\uD3C9\uAC00 \uB3C4\uAD6C
SiteDetailViewModel.VisitNotDownloaded=\uBC29\uBB38\uAE30\uB85D\uC774 \uB2E4\uC6B4\uB85C\uB4DC \uB418\uC9C0 \uC54A\uC558\uC2B5\uB2C8\uB2E4.
SiteDetailViewModel.VisitDownloadPrompt=\uC774 \uBC29\uBB38 \uC815\uBCF4\uB97C \uBCF5\uAD6C\uD558\uACA0\uC2B5\uB2C8\uAE4C?
VisitDownloadProceed=\uC9C4\uD589
SiteDetailViewModel.DownloadingVisit=\uBC29\uBB38\uAE30\uB85D \uB2E4\uC6B4\uB85C\uB4DC\uC911\u2026
SiteDetailViewModel.NetworkErrorMessageTitle=\uB124\uD2B8\uC6CC\uD06C \uC5D0\uB7EC
SiteDetailViewModel.NetworkErrorMessage=\uC811\uC18D\uAC00\uB2A5\uD55C \uB124\uD2B8\uC6CC\uD06C\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.
SiteDetailViewModel.VisitUnavailable=\uBC29\uBB38\uAE30\uB85D\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.
PenTimePenSelectionViewModel.NoLactatingPen=\uC774 \uD234\uC744 \uC0AC\uC6A9\uD558\uAE30 \uC704\uD574\uC11C\uB294 \uCC29\uC720\uC6A9 \uB2E4\uC774\uC5B4\uD2B8\uB97C \uAE09\uC5EC\uD558\uB294 \uC6B0\uAD70\uC774 \uCD5C\uC18C \uD55C \uAC1C\uAC00 \uC788\uC5B4\uC57C \uD569\uB2C8\uB2E4.
SiteDetailsSetupViewModel.SiteMandatoryFields=\uC0AC\uC774\uD2B8 \uC774\uB984, \uC720\uB300, \uCC29\uC720 \uC2DC\uC2A4\uD15C \uBC0F \uC6B0\uC0AC\uB294 \uAF2D \uC785\uB825\uD574\uC57C \uD558\uB294 \uD56D\uBAA9\uC785\uB2C8\uB2E4. \uBAA8\uB450 \uC785\uB825\uD574 \uC8FC\uC2DC\uAE30 \uBC14\uB78D\uB2C8\uB2E4.
ProductivityToolsViewModel.MilkSoldEvaluation=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00
FreeFormReportViewModel.MilkSoldEvaluation=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00
MilkSoldEvaluationMasterViewModel.Title=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00
MilkSoldSpinnerViewModel.Title=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00
MilkSoldEvaluationChartsViewModel.Title=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00
MilkSoldEvaluationMasterViewModel.Inputs=\uC785\uB825\uAC12
MilkSoldEvaluationMasterViewModel.Outputs=\uCD9C\uB825\uAC12
MilkSoldEvaluationMasterViewModel.Charts=\uCC60\uD2B8
MilkSoldEvaluationInputsViewModel.Herd=\uC6B0\uAD70 \uC218\uC900 \uC815\uBCF4
MilkSoldEvaluationInputsViewModel.LactatingAnimals=\uCC29\uC720\uC6B0s \u20F0
MilkSoldEvaluationInputsViewModel.AnimalsInTank=\uB0A9\uC720 \uB450\uC218 \u20F0
MilkSoldEvaluationInputsViewModel.MilkPickup=\uC9D1\uC720 \u20F0
MilkSoldEvaluationInputsViewModel.DryMatterIntake=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 ({0}) \u20F0
MilkSoldEvaluationInputsViewModel.DaysInMilk=\uBE44\uC720\uC77C\uB839 (DIM) \u20F0
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=\uC6B0\uC720\uB0B4 \uC694\uC18C \uCE21\uC815 \u20F0
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=\uC720\uC5C5\uCCB4 \uC815\uBCF4
Pickup=\uC9D1\uC720 {0}
MilkSoldEvaluationMasterViewModel.AddNew=\uC2E0\uADDC \uCD94\uAC00
Daily=\uB9E4\uC77C
EveryOtherDay=\uACA9\uC77C
MUN=MUN (mg/dL)
MilkUrea=\uC6B0\uC720\uB0B4 \uC694\uC18C (mg/dL) 
MilkSoldPickupViewModel.Title=\uC9D1\uC720 \uD3B8\uC9D1 {0}
MilkSoldPickupViewModel.MilkSold=\uC6D0\uC720 \uD310\uB9E4, {0} \u20F0
MilkSoldPickupViewModel.AnimalsInTank=\uB0A9\uC720 \uB450\uC218 \u20F0
MilkSoldPickupViewModel.DaysInTank=\uB0A9\uC720 \uC77C\uC218 \u20F0
MilkSoldPickupViewModel.MilkFat=\uC720\uC9C0\uBC29 %
MilkSoldPickupViewModel.MilkProtein=\uC720\uB2E8\uBC31%
MilkSoldPickupViewModel.SCC=\uCCB4\uC138\uD3EC\uC218  (1,000 \uAC1C/mL)
MilkSoldPickupViewModel.BCC=\uC138\uADE0\uC218 (1,000 cfu/mL)
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=\uC0AC\uC774\uD2B8 \uC14B\uC5C5 \uC5C5\uB370\uC774\uD2B8
MilkSoldEvaluationOutputsViewModel.EvaluationDays=\uD3C9\uAC00 \uC77C\uC218
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=\uD3C9\uADE0 \uC720\uC0DD\uC0B0\uB7C9, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=\uD3C9\uADE0 \uC720\uC0DD\uC0B0\uB7C9, {0} (\uB0A9\uC720\uB450\uC218)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=\uD3C9\uADE0 \uC720\uC9C0\uBC29 %
MilkSoldEvaluationOutputsViewModel.MilkFatYield=\uC720\uC9C0\uBC29\uB7C9 ({0})
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=\uD3C9\uADE0 \uC720\uB2E8\uBC31 %
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=\uC720\uB2E8\uBC31\uB7C9 ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=\uC720\uC9C0\uBC29+\uC720\uB2E8\uBC31\uB7C9 ({0})
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=\uC720\uC131\uBD84 \uD6A8\uC728 (\uAC74\uBB3C\uC12D\uCDE8\uB7C9 \uB300\uBE44 %)
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=\uC0AC\uB8CC \uD6A8\uC728 (\uBE44\uC728)
MilkSoldEvaluationOutputsViewModel.AvgSCC=\uD3C9\uADE0 \uCCB4\uC138\uD3EC\uC218 (1,000\uAC1C/mL)
MilkSoldEvaluationOutputsViewModel.AvgBCC=\uD3C9\uADE0 \uC138\uADE0\uC218 (1,000 cfu/mL)
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=\uC720\uB7C9 \uBC0F \uBE44\uC720\uC77C\uB839
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=\uC720\uC131\uBD84\uB7C9 \uBC0F \uD6A8\uC728
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=\uC720\uC9C0\uBC29% \uBC0F \uC720\uB2E8\uBC31%
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=\uCCB4\uC138\uD3EC\uC218 \uBC0F \uC6B0\uC720\uB0B4 \uC694\uC18C
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 \uBC0F \uC0AC\uB8CC\uD6A8\uC728
MilkSoldEvaluationChartsListViewModel.VisitComparison=\uBE44\uAD50\uB97C \uC704\uD574 \uBC29\uBB38\uAE30\uB85D\uC744 \uC120\uD0DD\uD558\uC2DC\uC694
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=\uBE44\uC720\uC77C\uB839
MilkSoldEvaluationChartsViewModel.MilkProduction=\uC720\uC0DD\uC0B0\uB7C9
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=\uC720\uC0DD\uC0B0\uB7C9 \uBC0F \uBE44\uC720\uC77C\uB839
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=\uC720\uC131\uBD84\uB7C9 \uBC0F \uD6A8\uC728
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=\uC720\uC9C0\uBC29% \uBC0F \uC720\uB2E8\uBC31%
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=\uCCB4\uC138\uD3EC\uC218 \uBC0F \uC6B0\uC720\uB0B4 \uC694\uC18C
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=\uAC74\uBB3C\uC12D\uCDE8\uB7C9 \uBC0F \uC0AC\uB8CC\uD6A8\uC728
MilkSoldEvaluationChartsViewModel.MilkFat=\uC720\uC9C0\uBC29%
MilkSoldEvaluationChartsViewModel.MilkProtein=\uC720\uB2E8\uBC31%
MilkSoldEvaluationChartsViewModel.SomanticCellCount=\uCCB4\uC138\uD3EC\uC218
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=\uC6B0\uC720\uB0B4 \uC694\uC18C
NotebookSectionMilkSoldEvaluationInputs=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00 \uC785\uB825
NotebookSectionMilkSoldEvaluationOutputs=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00 \uCD9C\uB825
NotebookSectionMilkSoldEvaluationCharts=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00 \uCC28\uD2B8
NotebookSectionMilkSoldEvaluationEditPickup=\uC6D0\uC720 \uD310\uB9E4 \uD3C9\uAC00 \uC9D1\uC720 \uC218\uC815
MilkSoldEvaluationChartsViewModel.FeedEfficiency=\uC0AC\uB8CC \uD6A8\uC728
MilkSoldEvaluationChartsViewModel.DryMatterIntake=\uAC74\uBB3C\uC12D\uCDE8\uB7C9
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=\uC720\uC131\uBD84 \uD6A8\uC728
MilkSoldEvaluationChartsViewModel.ComponentYield=\uC720\uC131\uBD84\uB7C9 \uBC0F \uD6A8\uC728
MilkSoldEvaluationInputsViewModel.AddPickup=\uC9D1\uC720\uCC98 \uCD94\uAC00
CurrentVisitSummary=\uAE30\uC874 \uBC29\uBB38 \uC694\uC57D
TonsDM=TONS \uAC74\uBB3C
TonsAF=TONS \uC6D0\uBB3C
MetricTonsDM=Metric TONS \uAC74\uBB3C
MetricTonsAF=Metric TONS \uC6D0\uBB3C
FeedingRate=\uAE09\uC5EC \uC18D\uB3C4(\uC6D0\uBB3C\uAE30\uC900/\uB450)
CowsToBeFed=\uAE09\uC5EC\uB418\uB294 \uB450\uC218
WeightDMInLengthMetric=Kgs. DM/1 \uBBF8\uD130
WeightDMInLengthImperial=Lbs. DM/1 \uD53C\uD2B8
FeedOutSurfaceAreaMetric=\uAE09\uC5EC\uB418\uB294 \uD45C\uBA74\uC801 (m^2)
FeedOutSurfaceAreaImperial=\uAE09\uC5EC\uC0AC\uB8CC \uD45C\uBA74\uC801 (ft^2)
LengthPerDayMetric=Cm. / \uC77C
LengthPerDayImperial=In./\uC77C
AtThreeLengthPerDayImperial=\uC77C\uC77C 3\uC778\uCE58
AtSixLengthPerDayImperial=\uC77C\uC77C 6in.
AtThreeLengthPerDayMetric=\uC77C\uC77C 7cm
AtSixLengthPerDayMetric=\uC77C\uC77C 15cm
FeedOutRateInfo=\uAE09\uC5EC \uC18D\uB3C4 \uC815\uBCF4
Days=\uC77C\uC218
StartDate=\uC2DC\uC791\uC77C
DateGone=\uACBD\uACFC\uC77C\uC218
CowsPerDayNeeded=\uC77C\uC77C \uC18C\uC694 \uB450\uC218
PileAndBunkerName=\uD30C\uC77C\uACFC \uBC99\uCEE4 \uC774\uB984
Capacity=\uC6A9\uB7C9
Costs=\uBE44\uC6A9$/\uB450
PileAndBunkerTitle=\uD30C\uC77C\uACFC \uBC99\uCEE4
PileBunkerCapacities=\uD30C\uC77C\uACFC \uBC99\uCEE4 \uC6A9\uB7C9
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=\uD1A4/\uC77C
MilkProcessorEditComparisonValuesViewModel.NoStimulation=\uC790\uADF9 \uC5C6\uC74C
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=\uBD80\uC801\uC808\uD55C \uC720\uBC29\uC790\uADF9
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=\uC801\uB2F9\uD55C \uC720\uB450\uC790\uADF9
CommonSpinnerViewModel.NoStimulation=\uC790\uADF9 \uC5C6\uC74C 
(\uC720\uB2C8\uD2B8\uB9CC \uBD80\uCC29\uD568)
CommonSpinnerViewModel.InadequateStimulation=\uBD80\uC801\uC808\uD55C \uC720\uBC29\uC790\uADF9
(\uBD80\uC801\uC808\uD55C \uC720\uB450 \uC900\uBE44: &lt;10\uCD08; \uC720\uB2C8\uD2B8 \uBD80\uD0C1; &lt; 60 \uB610\uB294 &gt; 120\uCD08)
CommonSpinnerViewModel.OptimalStimulation=\uC801\uB2F9\uD55C \uC720\uB450\uC790\uADF9
(\uC801\uC808\uD55C \uC720\uB450 \uC790\uADF9 :10~20\uCD08; 60~90\uCD08 \uC774\uB0B4 \uC720\uB2C8\uD2B8 \uBD80\uCC29)
CompletedTimeKey=\uC885\uB8CC \uC2DC\uAC04
WalkthroughReportHerdAnalysisViewModel.Notes=\uB178\uD2B8
EmailReportViewModel.WalkthroughReportTitle=\uBAA9\uC7A5\uC810\uAC80 \uBCF4\uACE0\uC11C
SelectOnlyThreeNotes=\uB3C4\uAD6C\uC5D0\uC11C 3\uAC00\uC9C0 \uB178\uD2B8\uB9CC \uC120\uD0DD\uD558\uC138\uC694.
EmailReportViewModel.Notes=\uB178\uD2B8
QuestionViewModel.VisitNotebook=\uB178\uD2B8\uBD81 \uBC29\uBB38
SiteDetailsSetupViewModel.NumberOfStalls=\uD314\uB7EC\uC5D0 \uCD1D \uC2A4\uD1A8\uC218
FreeHandNoteEditorPageTitle=\uB178\uD2B8
FreeHandNoteSaveUserDialogMessage=\uC774 \uB178\uD2B8\uB97C \uC800\uC7A5\uD560\uAE4C\uC694?
FreeHandNoteClearPaletteDialogMessage=\uD654\uBCC0\uC758 \uBAA8\uB4E0 \uC815\uBCF4\uB97C \uC0AD\uC81C\uD560\uAE4C\uC694?
DDWOfflineMessage=\uB124\uD2B8\uC6CC\uD06C \uC5F0\uACB0\uC774 \uC5C6\uC2B5\uB2C8\uB2E4. \uC624\uD504\uB77C\uC778 \uB9AC\uD3EC\uD2B8\uB9CC \uBCF4\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?
BCSPenSelectionViewModel.PenSelectionList=\uC6B0\uAD70
PerceivedHeatStressDietInfoMessage=\uC911\uC815\uB3C4 \uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 : \uAC00\uBE60\uB978 \uD638\uD761, \uAC70\uD488\uACFC \uCE68\uD758\uB9BC\uC774 \uBC1C\uC0DD\uD558\uB098 \uC785\uC740 \uBC8C\uB9AC\uC9C0 \uC54A\uC74C, \uD638\uD761\uC218 40~120\uD68C/\uBD84.
\uADF9\uC2EC\uD55C \uB354\uC704\uC2A4\uD2B8\uB808\uC2A4 : \uC785\uC744 \uBC8C\uB9B0\uCC44 \uCE68\uD758\uB9BC \uBC1C\uC0DD, \uD638\uD761\uC218 120~160\uD68C/\uBD84
PrematureKelvingsKeyInfoMessage=\uBD84\uB9CC \uC608\uC815\uC77C 10\uC77C \uC774\uC804\uC5D0 \uC0B4\uC544\uC788\uB294 \uC1A1\uC544\uC9C0 \uBD84\uB9CC
ExtraDaysOpenCostInfoMessage=\uACF5\uD0DC\uC77C \uD558\uB8E8\uB2F9 \uC804\uD615\uC801\uC73C\uB85C 3~5USD\uAC00 \uBCC0\uD654\uD569\uB2C8\uB2E4.
ReadyToMilkMasterViewModel.MastitisNotPresent=\uC720\uBC29\uC5FC \uAE30\uB85D \uC548\uB428
PileAndBunkerResultsFeedOutViewModel.Resources=\uCC38\uC870
RumenHealthManureLandingViewModel.Resources=\uCC38\uC870
RumenHealthLocomotionLandingViewModel.Resources=\uCC38\uC870
RumenHealthBodyConditionLandingViewModel.Resources=\uCC38\uC870
PileAndBunkerCapacitiesDensity=\uBC99\uCEE4 \uC0AC\uC77C\uB85C\uC640 \uC870\uC0AC\uB8CC \uB354\uBBF8 \uC6A9\uB7C9 \uCE21\uC815 \uAC00\uC774\uB4DC
CommonSpinnerViewModel.ManureScorePDF=\uBD84 \uC2A4\uCF54\uC5B4 \uAC00\uC774\uB4DC
CommonSpinnerViewModel.LocomotionPDF=\uBCF4\uD589\uC9C0\uC218 \uD3C9\uAC00 \uAC00\uC774\uB4DC
CommonSpinnerViewModel.BodyConditionPDF=\uC2E0\uCCB4 \uCDA9\uC2E4 \uC810\uC218 \uAC00\uC774\uB4DC
TonsPerDay=\uD1A4/\uC77C
CurrentDownResponse=Current Let Down Response ({0}/cow/day)
PotentialDownResponse=\uAC00\uB2A5\uD55C Let Down \uBC18\uC751\uB7C9 ({0}/cow/day)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=TMR \uC785\uC790\uB3C4 \uBD84\uC11D \uC774\uB984
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=\uC6B0\uAD70 TMR \uC785\uC790\uB3C4 \uCD94\uAC00
AddTMRScore=TMR \uC785\uC790\uB3C4 \uCD94\uAC00
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=\uD3C9\uADE0 \uC810\uC218
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=\uD45C\uC900 \uD3B8\uCC28
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=\uBAA9\uD45C
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=\uC0C1\uB2E8 (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=\uC911\uB2E8 1 (8mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=\uC911\uB2E8 2 (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=\uC911\uB2E8
PercentageOnScreenCurrentVisit=\uD654\uBA74\uC0C1 \uBE44\uC728 (%) - \uD604\uC7AC \uBC29\uBB38
PercentageOnScreenTrend=\uD654\uBA74\uC0C1 \uBE44\uC728 (%) - \uCD94\uC138
StandardDeviationScoreTitle=\uD45C\uC900\uD3B8\uCC28
AverageScoreTitle=\uD3C9\uADE0 TMR \uC785\uC790\uB3C4 \uC810\uC218
PileAndBunkerResultsFeedOutViewModel.DateGone=\uACBD\uACFC \uC77C\uC218
PileAndBunkerResultsFeedOutViewModel.Days=\uC77C\uC218
PileAndBunkerResultsFeedOutViewModel.StartDate=\uC2DC\uC791 \uC77C
CreateDuplicateNameDiet=\uAC19\uC740 \uC774\uB984\uC758 \uB2E4\uC774\uC5B4\uD2B8\uAC00 \uC874\uC7AC\uD569\uB2C8\uB2E4. \uC0C8\uB85C\uC6B4 \uC774\uB984\uC744 \uC785\uB825\uD558\uC138\uC694.
NewPenViewModel.PenMapping=\uC6B0\uAD70 \uB9E4\uD551
NewPenViewModel.PenSelection=\uC6B0\uAD70 \uC120\uD0DD
NewPenViewModel.OnlyOnePen=\uB2E8\uC9C0 \uD558\uB098\uC758 \uC6B0\uAD70\uB9CC \uC874\uC7AC\uD569\uB2C8\uB2E4.
NewPenViewModel.PublishPenAlert=\uBC29\uBB38 \uC815\uBCF4\uB97C \uD1B5\uD569\uD558\uACE0 \uC2F6\uC740 \uBAA8\uB4E0 \uBC29\uBB38\uC815\uBCF4\uB97C \uC804\uC1A1\uD558\uC2ED\uC2DC\uC694.
NewPenViewModel.UserCreatedPen=\uC0AC\uC6A9\uC790\uAC00 \uB9CC\uB4E0 \uC6B0\uAD70
ShowSyncStatusViewModel.Title=\uC790\uB8CC \uB3D9\uAE30\uD654 \uC694\uC57D
ShowSyncStatusViewModel.GetAccounts=\uACE0\uAC1D \uC815\uBCF4 \uC218\uC2E0 :
ShowSyncStatusViewModel.GetVisits=\uBC29\uBB38 \uC815\uBCF4 \uC218\uC2E0 :
ShowSyncStatusViewModel.GetNotes=\uB178\uD2B8 \uC815\uBCF4 \uC218\uC2E0 :
HomeViewModel.ConsumersTab=\uC0AC\uC591\uAC00
ConsumerDetailsViewModel.ProspectTitle=\uC0AC\uC591\uAC00 \uC138\uBD80\uC0AC\uD56D
ConsumersViewModel.NewConsumer=\uC0C8\uB85C\uC6B4 \uC0AC\uC591\uAC00 \uCD94\uAC00
ConsumerDetailsViewModel.MainHeading=\uB18D\uC7A5 \uC0AC\uC774\uD2B8
ConsumerDetailsViewModel.NewSite=\uC0C8\uB85C\uC6B4 \uC0AC\uC774\uD2B8 \uCD94\uAC00
ConsumerDetailsViewModel.DeleteProspect=\uC0AC\uC591\uAC00 \uC0AD\uC81C
ConsumerDetailsViewModel.DeleteProspectPrompt=\uC774 \uC0AC\uC591\uAC00\uB97C \uC9C0\uC6B0\uACA0\uC2B5\uB2C8\uAE4C? \uC0AC\uC591\uAC00, \uC0AC\uC774\uD2B8 \uBC0F \uD604\uC7AC \uC9C4\uD589\uC911\uC778 \uBC29\uBB38\uC815\uBCF4\uAC00 \uC0AD\uC81C\uB429\uB2C8\uB2E4.
NewProspectViewModel.ConsumerDetails=\uC0AC\uC591\uAC00 \uC138\uBD80\uC0AC\uD56D
NewProspectViewModel.NameNotUniqueForConsumer="{0}" \uC0AC\uC591\uAC00\uAC00 \uC774\uBBF8 \uC874\uC9C0\uD569\uB2C8\uB2E4. \uB2E4\uB978 \uC774\uB984\uC744 \uC785\uB825\uD558\uC138\uC694.
LocomotionSelectPenViewModel.MissingDiet=\uC774 \uC6B0\uBC29\uC5D0 \uAE09\uC5EC\uC911\uC778 \uB2E4\uC774\uC5B4\uD2B8\uB97C \uC785\uB825\uD558\uC138\uC694.
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=TMR \uC785\uC790\uB3C4 \uC815\uBCF4
FreeHandNotesViewModel.Save=Save
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=TMR \uC785\uC790\uB3C4 \uC810\uC218
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=\uBAA9\uD45C
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=TMR \uC785\uC790\uB3C4 \uC810\uC218
StatusArchived=\uC800\uC7A5\uB41C
HealthToolsViewModel.RumenHealthUrinePHTitle=\uB1E8 pH
UrinePHPenSelectionViewModel.Title=\uB1E8 pH
UrinePHPenSelectionViewModel.UrinePHPenList=\uC6B0\uBC29 (\uCC29\uC720 \uBC0F \uAC74\uC720)
UrinePHMasterViewModel.Title=\uB1E8 pH
UrinePHMasterViewModel.Inputs=\uC785\uB825
UrinePHMasterViewModel.Results=\uACB0\uACFC
UrinePHInputsViewModel.CalculatorHeading=\uB1E8 pH\uAC12\uC744 \uC785\uB825\uD560 \uC816\uC18C\uB97C \uC120\uD0DD\uD558\uC138\uB1E8. "\uC2E0\uADDC \uCD94\uAC00"\uB97C \uB20C\uB7EC \uB9AC\uC2A4\uD2B8\uC5D0 \uCD94\uAC00\uD560 \uC816\uC18C\uB97C \uC120\uD0DD\uD558\uC138\uC694.
UrinePHInputsViewModel.CudChewCategorySection=\uC816\uC18C
UrinePHInputsViewModel.DietDCAD=\uC0AC\uB8CC\uC911 \uC74C\uC774\uC628\uAC00(DCAD), mEq/100g
UrinePHInputsViewModel.UrinePhSTDDEV=\uD45C\uC900\uD3B8\uCC28 (\uACC4\uC0B0\uAC12)
UrinePHInputsViewModel.UrinePHAVG=\uD3C9\uADE0 \uB1E8 pH (\uACC4\uC0B0\uAC12)
UrinePHInputsViewModel.Resources=\uC790\uB8CC
UrinePHDensity=\uB1E8 pH \uC790\uB8CC
UrinePHInputsViewModel.AddNew=\uC2E0\uADDC \uCD94\uAC00
UrinePHInputsViewModel.CowsOutsideTargetRange=\uC815\uC0C1\uBC94\uC704\uB97C \uBC97\uC5B4\uB09C \uC816\uC18C (%)
UrinePHInputsViewModel.TargetUrinePHRange=\uBAA9\uD45C \uB1E8 pH \uBC94\uC704
UrinePHResultsViewModel.UrinePHAVG=\uD3C9\uADE0 \uB1E8 pH
UrinePHResultsViewModel.DietDCAD=\uC0AC\uB8CC\uC911 \uC74C\uC774\uC628\uAC00(DCAD), mEq/100g
UrinePHAVG=\uD3C9\uADE0\uB1E8 pH
DietDCAD=\uC0AC\uB8CC \uC74C\uC774\uC628\uAC00(DCAD) mEq/100g
UrinePHEditGoalViewModel.GoalMin=\uBAA9\uD45C - \uCD5C\uC18C
UrinePHEditGoalViewModel.GoalMax=\uBAA9\uD45C - \uCD5C\uB300
UrinePHEditGoalViewModel.Title=\uBAA9\uD45C \uC218\uC815
UrinePHEditGoalViewModel.TargetUrinePHRange=\uBAA9\uD45C \uB1E8 pH \uBC94\uC704
NotebookUrinePHInputs=\uB1E8 pH \uC785\uB825
NotebookUrinePHOutputs=\uB1E8 pH \uACB0\uACFC
NotebookUrinePHEditGoals=\uB1E8 pH \uBAA9\uD45C \uC218\uC815
UrinePHInputsViewModel.CoefficientVariation=\uBCC0\uC774\uACC4\uC218 (C.V.) (%)
VisitSummaryViewModel.RumenHealthUrinePHTitle=\uB1E8 pH
EmailReportViewModel.RumenHealthUrinePHTitle=\uB1E8 pH
FreeFormReportViewModel.RumenHealthUrinePHTitle=\uB1E8 pH
UrinePHResultsViewModel.MinpH=\uCD5C\uC18C pH
UrinePHResultsViewModel.MaxpH=\uCD5C\uB300 pH
UrinePH=\uB1E8 pH
EmailReportViewModel.DietDCADStr=\uC0AC\uB8CC \uC74C\uC774\uC628\uAC00(DCAD)
UrinePHAverageNumber=\uD3C9\uADE0 \uAC12
MEQ100G=mEq/100g
CowsOutsideTargetRangeToolTip=20% \uBBF8\uB9CC\uC758 \uCC29\uC720\uC6B0\uAC00 \uC815\uC0C1\uBC94\uC704\uB97C \uBC97\uC5B4\uB098\uB294 \uAC83\uC774 \uBAA9\uD45C\uC785\uB2C8\uB2E4.
CowsSectionToolTip=8\uB450 \uC774\uC0C1\uC758 \uAC80\uC0AC\uACB0\uACFC\uAC00 \uC788\uC5B4\uC57C \uACB0\uB860\uC744 \uB3C4\uCD9C\uD560 \uC218 \uC788\uC73C\uBA70, \uC6B0\uAD70\uC774 \uC801\uC744 \uACBD\uC6B0 \uBAA8\uB4E0 \uBD84\uB9CC\uC804 \uC816\uC18C\uB97C \uAC80\uC0AC\uD574\uC57C \uD569\uB2C8\uB2E4.
UrinePHEditCowViewModel.CowName=\uC816\uC18C \uC774\uB984
UrinePHEditCowViewModel.UrinePHEnterCowValue=\uC816\uC18C \uAC12 \uC785\uB825
UrinePHEditCowViewModel.CowValue=\uC816\uC18C \uAC12
UrinePHEditCowViewModel.AddNew=\uB2E4\uC74C \uC816\uC18C
UrinePHEditCowViewModel.ValidCudInput=\uC815\uD655\uD55C \uAC12\uC744 \uC785\uB825\uD558\uC138\uB1E8.
EmailReportViewModel.CowsOutsideTargetRange=\uC815\uC0C1\uBC94\uC704\uB97C \uBC97\uC5B4\uB09C \uC816\uC18C (%)
EmailReportViewModel.UrinePhSTDDEV=\uD45C\uC900\uD3B8\uC790
EmailReportViewModel.CoefficientVariation=\uBCC0\uC774\uACC4\uC218 (C.V.) (%)
VisitNotesViewModel.DownloadingNotes=\uB178\uD2B8 \uB0B4\uB824\uBC1B\uAE30\uC911\u2026
Benchmarks_Serum_ToolTip=<value />
CalfHeiferColostrum=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C - \uCD08\uC720
CalfHeiferGrowerPuberty=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C - \uC721\uC131\uAE30, \uC131\uC131\uC219\uAE30, \uC784\uC2E0\uAE30, \uC804\uD658\uAE30CalfHeiferKeyBenchmarks
CalfHeiferKeyBenchmarks=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C - \uC8FC\uC694 \uC9C0\uD45C
CalfHeiferPostweaned=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C - \uC774\uC720\uD6C4
CalfHeiferPreweaned=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C - \uC774\uC720\uC804
CalfHeiferQuestionViewModel.Close=\uB2EB\uAE30
CalfHeiferQuestionViewModel.Colostrum=\uCD08\uC720
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=\uC721\uC131\uAE30, \uC131\uC131\uC219\uAE30, \uC784\uC2E0\uAE30, \uC804\uD658\uAE30
CalfHeiferQuestionViewModel.KeyBenchmarks=\uC8FC\uC694 \uC9C0\uD45C
CalfHeiferQuestionViewModel.Postweaned=\uC774\uC720\uD6C4
CalfHeiferQuestionViewModel.Preweaned=\uC774\uC720\uC804
CalfHeiferQuestionViewModel.Resources=\uC790\uB8CC
CalfHeiferQuestionViewModel.VisitNotebook=\uB178\uD2B8 \uBCF4\uAE30
CalfHeiferResources=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C - \uC790\uB8CC
CalfHeiferResults=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uC810\uC218 - \uACB0\uACFC
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=\uC8FC\uC694 \uC9C0\uD45C
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=\uC131\uC131\uC219\uAE30
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=\uC721\uC131\uAE30
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=\uCD08\uC720
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=\uC804\uD658\uAE30
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=\uC784\uC2E0\uAE30
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseThree=\uC774\uC720\uD6C4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwo=\uC774\uC720\uC804
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=15 - 23 \uAC1C\uC6D4\uB839
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=9 - 15 \uAC1C\uC6D4\uB839
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=1 - 3 \uC77C\uB839
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=23 - 26 \uAC1C\uC6D4\uB839
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=3 - 9 \uAC1C\uC6D4\uB839
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=0 - 3 \uAC1C\uC6D4\uB839
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=\uB178\uD2B8 \uBCF4\uAE30
CalfHeiferScorecardLanding=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uD45C
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=\uC8FC\uC694 \uC9C0\uD45C
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=\uAC1C\uC120\uC0AC\uD56D
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=\uC810\uC218
CalfHeiferScorecardResultsViewModel.VisitNotebook=\uB178\uD2B8 \uBCF4\uAE30
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uC810\uC218
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=\uC721\uC131\uAE30, \uC131\uC131\uC219\uAE30, \uC784\uC2E0\uAE30, \uC804\uD658\uAE30
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=\uC804\uCCB4 scorecard \uC810\uC218\uC5D0 \uD3EC\uD568
CalfHeiferScoreCardScoreViewModel.PhaseOne=\uCD08\uC720
CalfHeiferScoreCardScoreViewModel.PhaseThree=\uC774\uC720\uD6C4
CalfHeiferScoreCardScoreViewModel.PhaseTwo=\uC774\uC720\uC804
CalfHeiferScorecardViewModel.Colostrum=\uCD08\uC720
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=\uC721\uC131\uAE30, \uC131\uC131\uC219\uAE30, \uC784\uC2E0\uAE30, \uC804\uD658\uAE30
CalfHeiferScorecardViewModel.KeyBenchmarks=\uC8FC\uC694 \uC9C0\uD45C
CalfHeiferScorecardViewModel.Postweaned=\uC774\uC720\uD6C4
CalfHeiferScorecardViewModel.Preweaned=\uC774\uC720\uC804
CalfHeiferScorecardViewModel.Resources=\uC790\uB8CC
CalfHeiferScorecardViewModel.Title=\uC810\uC218\uD45C
CalfHeiferScorecardViewModel.VisitNotebook=\uB178\uD2B8 \uBCF4\uAE30
CalfHeiferTools=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uB3C4\uAD6C
CalfHeiferToolsViewModel.CalfHeiferScorecard=\uC810\uC218\uD45C
CalfHeiferToolsViewModel.CalfHeiferTools=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uB3C4\uAD6C
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=\uB3C4\uAD6C
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=\uB9AC\uC2A4\uD2B8\uC5D0\uC11C \uB3C4\uAD6C\uB97C \uC120\uCC45\uD574 \uBC29\uBB38\uC744 \uC2DC\uC791\uD558\uC138\uC694
CalfHeiferToolsViewModel.CalfHeiferToolsList=\uB3C4\uAD6C
CalfHeiferToolsViewModel.Title=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00\uB3C4\uAD6C
CalfHeiferToolsViewModel.VisitNotebook=\uB178\uD2B8 \uBCF4\uAE30
Colostrum=\uCD08\uC720
Colostrum_AmountOfColostrumOrFed=\uAE09\uC5EC\uB41C \uCD08\uC720\uB7C9
Colostrum_BrixPercentOfColostrumFed=\uAE09\uC694\uB41C \uCD08\uC720 Brix %
Colostrum_CleanAndDryCalvingArea=\uCCAD\uACB0\uD558\uACE0 \uAC74\uC870\uD55C \uBD84\uB9CC\uD658\uACBD
Colostrum_CleanAndDryCalvingArea_ToolTip=Wet knee test\uB97C \uD1B5\uD574 \uBD84\uB9CC\uC0AC\uAC00 \uCCAD\uACB0\uD558\uACE0 \uAC74\uC870\uD55C\uC9C0 \uD655\uC778\uD55C\uB2E4
Colostrum_CleanAndSanitizeCalfFeedingEquipment=\uC1A1\uC544\uC9C0\uC5D0 \uAE09\uC5EC\uD558\uB294 \uAE30\uAD6C\uB97C \uAE09\uC5EC\uC804 \uCCAD\uACB0\uD558\uACE0 \uC704\uC0DD\uC801\uC73C\uB85C \uAD00\uB9AC\uD558\uB2E4.
Colostrum_CleanCalfCartToTransportCalf=\uC1A1\uC544\uC9C0\uB97C \uC62E\uAE30\uB294 \uAE30\uAD6C(\uC1A1\uC544\uC9C0 \uCE74\uD2B8)\uB97C \uCCAD\uACB0\uD558\uAC8C \uAD00\uB9AC\uD55C\uB2E4.
Colostrum_HoursTillCalfIsRemovedFromMother=\uC1A1\uC544\uC9C0\uAC00 \uBD84\uB9CC\uD6C4 \uBD84\uB9CC\uC6B0\uC640 \uB5A8\uC5B4\uC9C0\uB294 \uC2DC\uAC04
Colostrum_HoursTillCalfReceivesColostrum=\uC1A1\uC544\uC9C0\uAC00 \uCCAB \uCD08\uC720\uB97C \uAE09\uC5EC\uBC1B\uB294 \uC2DC\uAC04
Colostrum_NumberOfCowsInCalvingArea=\uBD84\uB9CC\uC2E4\uB0B4 \uBD84\uB9CC\uC608\uC815\uC6B0 \uB450\uC218
Colostrum_PasteurizeColostrumBeforeFeeding=\uBA78\uADE0\uB41C \uCD08\uC720\uB97C \uAE09\uC5EC\uD55C\uB2E4
Colostrum_PercentageOfNavelsDippedInSevenPercent=7% \uC694\uC624\uB4DC \uC6A9\uC561\uC5D0 1\uC2DC\uAC04\uC774\uB0B4 \uC18C\uB3C5\uD55C \uD0EF\uC904%
Colostrum_RefrigeratedColostrumStoredLess=24\uC2DC\uAC04 \uC774\uB0B4 \uB0C9\uB3D9\uB41C \uCD08\uC720
EmailReportViewModel.CalfHeiferItem=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00
EmailReportViewModel.CalfHeiferScorecard=\uC810\uC218\uD45C
EmailReportViewModel.GeneratingReport=\uB9AC\uD3EC\uD2B8 \uC791\uC131
EmailReportViewModel.Improvements1=\uAC1C\uC120\uC0AC\uD56D
FreeFormReportViewModel.CalfHeiferItem=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00
FreeFormReportViewModel.CalfHeiferScorecard=\uC810\uC218\uD45C
FreeFormReportViewModel.KeyBenchmarks=\uC8FC\uC694 \uC9C0\uD45C
GrowerPubertyPregnancyCloseup=\uC721\uC131\uAE30, \uC131\uC131\uC219\uAE30, \uC784\uC2E0\uAE30, \uC804\uD658\uAE30
GrowerPubertyPregnancyCloseup_CleanAndDryPen=\uAE68\uB057\uD558\uACE0 \uAC74\uC870\uD55C \uC6B0\uBC29
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Wet knee test\uB97C \uD1B5\uD574 \uBD84\uB9CC\uC0AC\uAC00 \uCCAD\uACB0\uD558\uACE0 \uAC74\uC870\uD55C\uC9C0 \uD655\uC778\uD55C\uB2E4.
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=\uC131\uC131\uC219\uAE30\uC5D0 \uC801\uC815 BCS\uAC00 \uC644\uC131\uB41C\uB2E4
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=\uC5F0\uBCC0\uC758 \uC99D\uAC70
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=\uC0AC\uC870\uB294 \uB9E4\uC77C \uCCAD\uC18C\uD558\uBA70, \uB0A8\uC740 \uC0AC\uB8CC\uB294 \uC81C\uAC70\uB41C\uB2E4.
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=\uC790\uC720\uB86D\uAC8C \uBA39\uC744 \uC218 \uC788\uB294 \uAE68\uB057\uD55C \uBB3C\uC774 \uACF5\uAE09\uB41C\uB2E4.
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=\uC218\uC9C8 \uC624\uC5FC\uC758 \uC99D\uAC70\uAC00 \uC5C6\uB2E4.
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=\uC6B0\uAD70\uB0B4 \uAC00\uCD95\uC758 \uD06C\uAE30\uAC00 \uBE44\uC2B7\uD558\uB2E4.
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=\uB3D9\uC77C\uD55C \uD06C\uAE30\uC758 \uC721\uC131\uC6B0\uAC00 \uBAA8\uC5EC\uC788\uB294 \uC6B0\uAD70
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=\uBC00\uC9D1\uC0AC\uC721\uBE44\uC728(%)
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=\uC131\uC7A5 \uBAA9\uD45C\uB97C \uB2EC\uC131\uD560 \uC218 \uC788\uB3C4\uB85D \uC124\uACC4\uB418\uACE0 \uC790\uC8FC \uC810\uAC80\uD558\uB294 \uAE09\uC5EC\uD504\uB85C\uADF8\uB7A8
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=\uC0AC\uC870 \uAE38\uC774\uAC00 \uC721\uC131\uC6B0 \uB450\uC218\uC5D0 \uC801\uD569\uD558\uB2E4.
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg\uC740 30cm, 270-400kg\uC740 38cm, &gt;400kg\uC774\uC0C1\uC740 46cm
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=\uAD6C\uBC29\uC758 \uD06C\uAE30\uAC00 \uC721\uC131\uC6B0 \uB450\uC218\uC5D0 \uC801\uD569\uD558\uB2E4.
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg\uC740 40ft2 / 4m2, 270-400kg \uC740 50ft2 / 5m2, &gt;400kg \uC774\uC0C1\uC740 70ft2 / 7m2
KeyBenchmarks=\uC8FC\uC694 \uAE30\uC900\uC810
KeyBenchmarks_AgeInMonthAtFirstCalving=\uCCAB \uBD84\uB9CC \uC6D4\uB839
KeyBenchmarks_CalvingAndHeiferReocrd=\uBD84\uB9CC \uBC0F \uC721\uC131\uC6B0 \uAE30\uB85D\uAD00\uB9AC \uC2DC\uC2A4\uD15C\uC744 \uC0AC\uC6A9\uD55C\uB2E4.
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=90\uC77C\uB839\uC5D0 \uC131\uC6B0 \uCCB4\uC911(Mature Body Weight)\uC758 15% \uB3C4\uB2EC
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=\uC784\uC2E0\uAE30\uC5D0 \uC131\uC6B0 \uCCB4\uC911\uC758 55% \uB3C4\uB2EC
KeyBenchmarks_HeiferPeakProduce=\uCD08\uC784\uC6B0 \uD53C\uD06C \uC720\uB7C9\uC774 \uC6B0\uAD70 \uD3C9\uADE0\uC5D0\uC11C \uCC28\uC9C0\uD558\uB294 \uBE44\uC728
KeyBenchmarks_NintyDaysMorbidityf=90\uC77C\uB839 \uC9C8\uBCCC\uAC10\uC5FC\uC728
KeyBenchmarks_NintyDaysMortality=90\uC77C\uB839 \uD3D0\uC0AC\uC728
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=\uBD84\uB9CC\uC804 \uC131\uC6B0 \uCCB4\uC911\uC758 94% \uB3C4\uB2EC
KeyBenchmarks_PercentOfHeifersPregnant=15\uAC1C\uC6D4\uB839 \uC721\uC131\uC6B0\uC758 \uC784\uC2E0\uBE44\uC728
KeyBenchmarks_SerumlgG=48\uC2DC\uAC04\uD6C4 \uD608\uCCAD\uB0B4 IgG (g/L)
One=\uD558\uB098
OverallCalfHeiferDetails=\uC804\uCCB4 Calf &amp; Heifer score\uB97C \uBCF4\uAE30\uC704\uD574, \uBA3C\uC800 \uC704 \uB9AC\uC2A4\uD2B8\uC5D0 \uC788\uB294 \uC870\uC0AC\uB4E4 \uC911 \uCD5C\uC18C \uD558\uB098\uC774\uC0C1\uC744 \uC644\uB8CC\uD574\uC57C \uD569\uB2C8\uB2E4.
Postweaned=\uC774\uC720\uD6C4
Postweaned_CleanAndDryPen=\uAE68\uB057\uD558\uACE0 \uAC74\uC870\uD55C \uC6B0\uBC29
Postweaned_CleanAndDryPen_ToolTip=Wet knee test\uB97C \uD1B5\uD574 \uBD84\uB9CC\uC0AC\uAC00 \uCCAD\uACB0\uD558\uACE0 \uAC74\uC870\uD55C\uC9C0 \uD655\uC778\uD55C\uB2E4.
Postweaned_EvidenceOfAcidosisInManure=\uBD84\uBCC0\uB0B4 \uACFC\uC0B0\uC99D \uC99D\uAC70
Postweaned_EvidenceOfAcidosisInManure_ToolTip=\uC5F0\uBCC0\uB0B4\uC5D0 \uAC70\uD488\uC774 \uC874\uC7AC\uD569\uB2C8\uAE4C?
Postweaned_EvidenceOfScoursOrPneumonia=\uC124\uC0AC \uB610\uB294 \uD3D0\uB834\uC758 \uC99D\uAC70
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=20%\uC774\uD558\uC758 \uC1A1\uC544\uC9C0\uAC00 \uC124\uC0AC \uB610\uB294 \uD3D0\uB834\uC5D0 \uAC10\uC5FC\uB418\uC5C8\uB2E4.
Postweaned_FeedBunkIsClaanedDaily=\uC0AC\uC870\uB294 \uB9E4\uC77C \uCCAD\uC18C\uD558\uBA70, \uB0A8\uC740 \uC0AC\uB8CC\uB294 \uC81C\uAC70\uB41C\uB2E4.
Postweaned_ForageAvailability=\uC870\uC0AC\uB8CC \uAE09\uC5EC\uC5EC\uBD80
Postweaned_ForageAvailability_ToolTip=\uC904\uAC00\uAE30 5cm\uBCF4\uB2E4 \uD06C\uB2E4.
Postweaned_FreeChoiceCleanWaterIsAvailable=\uC790\uC720\uB86D\uAC8C \uBA39\uC744 \uC218 \uC788\uB294 \uAE68\uB057\uD55C \uBB3C\uC774 \uACF5\uAE09\uB41C\uB2E4.
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=\uC218\uC9C8\uC774 \uC624\uC5FC\uB41C \uC99D\uAC70\uAC00 \uC5C6\uC74C
Postweaned_FreshQualityStarterAvailable=\uC2E0\uC120\uD558\uACE0 \uD488\uC9C8\uC774 \uC88B\uC740 \uC5B4\uB9B0\uC1A1\uC544\uC9C0/\uC911\uC1A1\uC544\uC9C0 \uC0AC\uB8CC\uB97C \uAE09\uC5EC\uD55C\uB2E4.
Postweaned_FreshQualityStarterAvailable_ToolTip=\uC5B4\uB9B0\uC1A1\uC544\uC9C0/\uC911\uC1A1\uC544\uC9C0 \uC81C\uD488\uC5D0 \uAC00\uB8E8\uAC00 \uC5C6\uACE0, \uACF0\uD321\uC774\uAC00 \uC5C6\uC73C\uBA70, \uC816\uC9C0 \uC54A\uC544\uC57C \uD55C\uB2E4.
Postweaned_SizeOfBunkSpace=\uC1A1\uC544\uC9C0 \uB450\uB2F9 \uC0AC\uC870 \uACF5\uAC04\uC774 \uC801\uC808\uD558\uB2E4.
Postweaned_SizeOfBunkSpace_ToolTip=\uC1A1\uC544\uC9C0 \uB2F9 45cm\uC774\uC0C1
Postweaned_SizeOfPenAdequate=\uC721\uC131\uC6B0 \uB450\uB2F9 \uC6B0\uBC29\uC758 \uD06C\uAE30\uAC00 \uC801\uC808\uD558\uB2E4.
Postweaned_SizeOfPenAdequate_ToolTip=\uAC1C\uCCB4 \uC6B0\uBC29: 32ft2 / 3m2, \uAD70\uC0AC \uC6B0\uBC29: 28ft2 / 2.75m2
Postweaned_WellVentilatedPenWithNoDraftOnCalf=\uD658\uAE30\uAC00 \uC798 \uB418\uBA74\uC11C \uC1A1\uC544\uC9C0\uC5D0 \uC9C1\uC811 \uBD80\uB294 \uC678\uD48D\uC774 \uC5C6\uB294 \uC6B0\uBC29
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=\uC6B0\uC0AC\uB97C \uBC29\uBB38\uD55C \uD6C4 \uC637\uC5D0\uC11C \uC554\uBAA8\uB2C8\uC544 \uB0C4\uC0C8\uAC00 \uB098\uBA74, \uC554\uBAA8\uB2C8\uC544 \uC218\uCE58\uAC00 \uB9E4\uC6B0 \uB192\uC740 \uAC83\uC774\uB2E4.
Preweaned=\uC774\uC720\uC804
Preweaned_CleanAndDryPen=\uAE68\uB057\uD558\uACE0 \uAC74\uC870\uD55C \uC6B0\uBC29
PreWeaned_CleanAndDryPen_ToolTip=Wet knee test\uB97C \uD1B5\uD574 \uBD84\uB9CC\uC0AC\uAC00 \uCCAD\uACB0\uD558\uACE0 \uAC74\uC870\uD55C\uC9C0 \uD655\uC778\uD55C\uB2E4.
Preweaned_CleanAndSanitizeCalfFeedingEquipment=\uC1A1\uC544\uC9C0 \uD3EC\uC720\uAE30\uB294 \uAE09\uC5EC\uC804 \uAE68\uB057\uD558\uAC8C \uC0B4\uADE0\uD558\uACE0 \uC704\uC0DD\uC801\uC73C\uB85C \uAD00\uB9AC\uD558\uB2E4.
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=\uC1A1\uC544\uC9C0 \uB300\uC6A9\uC720\uB294 \uC798 \uC11E\uC5B4\uC11C \uC815\uD655\uD558\uAC8C \uAE09\uC5EC\uD55C\uB2E4.
PreWeaned_CMRisProperlyMixed_ToolTip=\uB300\uC6A9\uC720 \uAE09\uC5EC\uC591 : 600g~800g, \uC628\uB3C4 : 39-41C, \uACE0\uD615\uBD84 12-18%
Preweaned_ConsistentFeedingTimesAndProtocols=\uAE09\uC5EC \uC2DC\uAC04\uACFC \uAE09\uC5EC \uBC29\uBC95\uC744 \uC77C\uC815\uD558\uAC8C \uC900\uC218\uD55C\uB2E4.
Preweaned_EvidenceOfScoursOrPneumonia=\uC124\uC0AC \uB610\uB294 \uD3D0\uB834\uC758 \uC99D\uAC70
PreWeaned_EvidenceOfSource_ToolTip=20%\uC774\uD558\uC758 \uC1A1\uC544\uC9C0\uAC00 \uC124\uC0AC \uB610\uB294 \uD3D0\uB834\uC5D0 \uAC10\uC5FC\uB418\uC5C8\uB2E4.
Preweaned_ForageAvailability=\uC870\uC0AC\uB8CC \uAE09\uC5EC\uC5EC\uBD80
PreWeaned_Forageavailability_ToolTip=\uBA40\uD2F0\uD30C\uD2F0\uD074 \uC1A1\uC544\uC9C0 \uC0AC\uB8CC\uC77C \uACBD\uC6B0 \uC870\uC0AC\uB8CC\uB97C \uCD94\uAC00\uB85C \uAE09\uC5EC\uD558\uC9C0 \uC54A\uB294\uB2E4.
Preweaned_FreeChoiceCleanWaterIsAvailable=\uC790\uC720\uB86D\uAC8C \uBA39\uC744 \uC218 \uC788\uB294 \uAE68\uB057\uD55C \uBB3C\uC774 \uACF5\uAE09\uB41C\uB2E4.
PreWeaned_FreeChoiceCleanWater_ToolTip=\uBD84\uB9CC \uB2F9\uC77C\uBD80\uD130 \uAE09\uC5EC\uD558\uBA70, \uC218\uC9C8 \uC624\uC5FC\uC744 \uC8FC\uC758\uD55C\uB2E4.
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=\uC790\uC720\uB86D\uAC8C \uBA39\uC744 \uC218 \uC788\uACE0 \uC2E0\uC120\uD55C \uC5B4\uB9B0\uC1A1\uC544\uC9C0 \uC0AC\uB8CC\uAC00 \uACF5\uAE09\uB41C\uB2E4.
PreWeaned_FreeChoiceFreshCalf_ToolTip=\uC5B4\uB9B0\uC1A1\uC544\uC9C0 \uC81C\uD488\uC5D0 \uAC00\uB8E8\uAC00 \uC5C6\uACE0, \uACF0\uD321\uC774\uAC00 \uC5C6\uC73C\uBA70, \uC816\uC9C0 \uC54A\uC544\uC57C \uD55C\uB2E4.
Preweaned_SizeOfPenAadequatePerHeifer=\uC721\uC131\uC6B0 \uB450\uB2F9 \uC0AC\uC721\uBA74\uC801\uC774 \uC801\uB2F9\uD558\uB2E4.
PreWeaned_SizeOfPen_ToolTip=\uAC1C\uCCB4 \uC6B0\uBC29: 32ft2 / 3m2, \uAD70\uC0AC \uC6B0\uBC29: 25ft2 / 2.5m2
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=\uC77C\uC77C 1kg\uC758 \uC5B4\uB9B0\uC1A1\uC544\uC9C0 \uC0AC\uB8CC\uB97C \uC12D\uCDE8\uD558\uBA74 \uC774\uC720\uD55C\uB2E4.
PreWeaned_WellVenilated_ToolTip=\uC6B0\uC0AC\uB97C \uBC29\uBB38\uD55C \uD6C4 \uC637\uC5D0\uC11C \uC554\uBAA8\uB2C8\uC544 \uB0C4\uC0C8\uAC00 \uB098\uBA74, \uC554\uBAA8\uB2C8\uC544 \uC218\uCE58\uAC00 \uB9E4\uC6B0 \uB192\uC740 \uAC83\uC774\uB2E4.
Preweaned_WellVentilatedPenWithNoDraftOnCalf=\uD658\uAE30\uAC00 \uC798 \uB418\uBA74\uC11C \uC1A1\uC544\uC9C0\uC5D0 \uC9C1\uC811 \uBD80\uB294 \uC678\uD48D\uC774 \uC5C6\uB294 \uC6B0\uBC29
SelectImprovement=\uB9AC\uC2A4\uD2B8\uC5D0\uC11C 10\uAC1C\uC758 \uAC1C\uC120\uC0AC\uD56D\uB9CC \uC120\uD0DD\uD558\uC138\uC694.
ViewOverallCalfHaiferScore=\uC804\uCCB4 Calf &amp; Heifer Score\uBCF4\uAE30
VisitNotesViewModel.DownloadingNotes1=Notes \uB2E4\uC6B4\uB85C\uB529
VisitSummaryViewModel.CalfHeiferItem=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00
VisitSummaryViewModel.CalfHeiferScorecard=\uC810\uAC80\uD45C
VisitViewModel.CalfHeiferItem=\uC1A1\uC544\uC9C0/\uC721\uC131\uC6B0\uD3C9\uAC00
DoNotTest=&lt;20% \uD639\uC740 \uD14C\uC2A4\uD2B8 \uC548\uD568
TextureFeed=\uB2E4\uACE1\uC885 \uC0AC\uB8CC
PasteurizedWholeMilk=\uBA78\uADE0 \uC804\uC720 \uAE09\uC5EC
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=\uB9AC\uD3EC\uD2B8 \uC791\uC131
RefreshTokenFailed=\uC778\uC99D\uC624\uB958, \uB85C\uADF8\uC778\uC744 \uB2E4\uC2DC \uD574\uC8FC\uC138\uC694
LoginViewModel.UnauthorizedTitle=\uBBF8\uC2B9\uC778
LoginViewModel.Unauthorized=\uC2B9\uC778\uB418\uC9C0 \uC54A\uC740 \uC811\uADFC
NewProspectViewModel.FarmProducer=\uB18D\uC7A5\uC8FC
LoginViewModel.LoginPromptConsumer=\uAE30\uD0C0 \uB85C\uADF8\uC778
UserPreferencesViewModel.ProvimiUS=\uBBF8\uAD6D \uD504\uB85C\uBE44\uBBF8
EmailReportViewModel.ProvimiUS=\uBBF8\uAD6D \uD504\uB85C\uBE44\uBBF8
FreeFormReportViewModel.ProvimiUS=\uBBF8\uAD6D \uD504\uB85C\uBE44\uBBF8
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=\uBBF8\uAD6D \uD504\uB85C\uBE44\uBBF8
PDFDisclaimer_ProvimiUS=Provimi North America, \uBC0F \uAD00\uB828 \uC885\uC0AC\uC790\uB294 \uC5EC\uB7EC \uC694\uC18C\uB85C \uC778\uD574 \uC81C\uACF5\uB418\uB294 \uCD94\uC815\uCE58\uAC00 \uC815\uD655\uD558\uB2E4\uACE0 \uBCF4\uC7A5\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC0DD\uC0B0\uB7C9 \uB610\uB294 \uC7AC\uBB34\uC801 \uACB0\uACFC\uC5D0 \uB300\uD55C \uBCF4\uC7A5\uB3C4 \uC5C6\uC2B5\uB2C8\uB2E4.  \u00A9{0} Provimi North America. All Rights Reserved
AnimalListViewModel.Title=\uAC00\uCD95 \uBD84\uB958 / \uD558\uC704 \uBD84\uB958
Animals=\uAC00\uCD95
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=\uAC01 \uAD6C\uC5ED\uC758 \uC0C9\uAE54\uC740 \uC544\uB798 \uACB0\uACFC\uB85C \uD45C\uC2DC\uB429\uB2C8\uB2E4: 75%\uBBF8\uB9CC: \uBD89\uC740\uC0C9, 75%\uC774\uC0C1, 90% \uBBF8\uB9CC: \uC624\uB80C\uC9C0\uC0C9, 90%\uC774\uC0C1: \uB179\uC0C9
CalfHeiferScorecardImprovementViewModel.Colostrum=\uCD08\uC720
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=\uC721\uC131, \uC131\uC131\uC219, \uC784\uC2E0, \uAC74\uC720\uB9D0\uAE30
CalfHeiferScorecardImprovementViewModel.Postweaned=\uC774\uC720 \uD6C4
CalfHeiferScorecardImprovementViewModel.Preweaned=\uC774\uC720 \uC804
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=\uAE30\uBCC4 \uACB0\uACFC\uB97C \uBCF4\uB824\uBA74, \uC544\uB798 \uBC94\uC8FC\uB97C \uD0ED\uD558\uC138\uC694.
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=\uB2E4 \uAD6C\uC5ED\uC758 \uC0C9\uAE54\uC740 \uC544\uB798 \uAE30\uC900\uC73C\uB85C \uACB0\uC815\uB429\uB2C8\uB2E4: 100%\uB294 \uB179\uC0C9, 100% \uBBF8\uB9CC: \uBD89\uC740\uC0C9
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=\uAE30\uB85D
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=2-3\uAE30
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=\uC815\uD655\uD55C \uC131\uC7A5 \uBC0F \uAC74\uAC15 \uAE30\uB85D \uC720\uC9C0
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=\uBC18\uC751
HomeViewModel.Eula=\uC0AC\uC6A9\uC790 \uB77C\uC774\uC13C\uC2A4
KBLastPhase=\uC815\uD655\uD55C \uC131\uC7A5 \uBC0F \uAC74\uAC15 \uAE30\uB85D \uC720\uC9C0
MetricTonsDMSilo=\uBBF8\uD130\uBC95 \uD1A4 \uAC74\uBB3C (\uC0AC\uC77C\uB85C \uC794\uB7C9)
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EndUser=\uCD5C\uC885\uC0AC\uC6A9\uC790
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.Noah=Noah
NumberOfChewsReportsViewModel.StdDevCalculated=\uD45C\uC900\uD3B8\uCC28(\uACC4\uC0B0\uAC12)
Optimal=\uCD5C\uC801 \uB2F5\uC548
Pendetails=\uC6B0\uC0AC \uC138\uBD80\uC0AC\uD56D
PhaseFive=\uC131\uC131\uC219
PhaseFour=\uC721\uC131
PhaseOne=\uCD08\uC720
PhaseTwoThree=\uC774\uC720 \uC804/\uD6C4
PhaseSix=\uC784\uC2E0
PhaseSeven=\uAC74\uC720\uD6C4\uAE30 / \uC0DD\uC0B0
PileAndBunkerCapacityViewModel.Resources=\uC790\uC6D0
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=\uC6A9\uB7C9
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=TONS DM (Remaining in Silo)
PromptPermissionMsg=\uD5C8\uAC00 \uC5C6\uC774
PromptPermissionMsgIMSure=\uD655\uC2E4\uD568
PromptPermissionMsgRetry=\uC7AC\uC2DC\uB3C4
PromptPermissionTitle=\uD5C8\uAC00 \uC548\uB428
ReportPDFNote=\uBCF4\uACE0\uC11C
ForageScorecardViewModel.ForageCategoryTooltip=\uC870\uC0AC\uB8CC\uC758 \uD488\uC9C8\uC740 \uB099\uB18D \uC601\uC591 \uD504\uB85C\uADF8\uB7A8\uC758 \uAE30\uCD08\uC774\uBA70, \uBAA9\uC7A5 \uC804\uCCB4\uC758 \uC218\uC775\uC131\uC744 \uC704\uD55C \uD575\uC2EC\uC694\uC18C\uC785\uB2C8\uB2E4.  \uC774 \uB3C4\uAD6C\uB294 \uBAA9\uC7A5\uC5D0\uC11C \uD604\uC7AC \uC0AC\uC6A9\uD558\uB294 \uC870\uC0AC\uB8CC \uAD00\uB9AC \uBC29\uBC95\uC744 \uD3C9\uAC00\uD558\uACE0 \uAC1C\uC120\uD560 \uC218 \uC788\uB294 \uC911\uC694\uD55C \uAE30\uD68C\uB97C \uC81C\uC548\uD558\uB294\uB370 \uC0AC\uC6A9\uB420 \uC218 \uC788\uC2B5\uB2C8\uB2E4.
LessThanFifteen=&lt;15 (\uC54C\uACE1\uC758 \uB2E8\uB2E8\uD55C \uC815\uB3C4)
BetweenFifteenTwenty=15~20
GreaterThanTwenty=&gt;20
Average=\uD3C9\uADE0
Weekly=\uC8FC\uAC04
BiWeekly=\uACA9\uC8FC\uAC04
Monthly=\uC6D4\uAC04
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=\uBAA9\uCD08 \uC0AC\uC77C\uB9AC\uC9C0 \uC720\uD615
CornSilage=\uC625\uC218\uC218 \uC0AC\uC77C\uB9AC\uC9C0
Haylage=헤일리지
OtherSilage=\uAE30\uD0C0 \uC0AC\uC77C\uB9AC\uC9C0
ForageScorecardViewModel.ForageAuditCategories=\uC870\uC0AC\uB8CC \uD3C9\uAC00 \uAD6C\uBD84
EmailReportViewModel.ForageImprovements=\uC870\uC0AC\uB8CC \uD3C9\uAC00 \uC810\uC218\uD45C
ForageScorecardViewModel.ForageAuditScore=\uC870\uC0AC\uB8CC \uD3C9\uAC00 \uC810\uC218
NotMeasured=\uBBF8\uCE21\uC815
RemovedAndMeasured=\uC81C\uAC70 \uBC0F \uCE21\uC815
RemovedOnly=\uC81C\uAC70\uB9CCy
NotRemoved=\uBBF8\uC81C\uAC70
SelectForageImprovement=\uB9AC\uC2A4\uD2B8\uC5D0\uC11C 12 \uAC1C\uC120\uC810\uC744 \uC120\uD0DD\uD558\uC138\uC694
ForageManagement_ForagesHarvestedAtProperMoisture=\uC870\uC0AC\uB8CC\uC758 \uC218\uD655\uC740 \uCD08\uC885\uC758 \uC131\uC219\uB3C4\uC640 \uC800\uC7A5\uBC29\uBC95\uC5D0 \uC801\uD569\uD558\uAC8C \uB418\uC5C8\uC2B5\uB2C8\uAE4C?
ForageManagement_ForagesHarvestedAtProperMaturity=\uC870\uC0AC\uB8CC\uC758 \uC218\uD655\uC740 \uCD08\uC885\uC758 \uC131\uC219\uB3C4\uC640 \uC800\uC7A5\uBC29\uBC95\uC5D0 \uC801\uD569\uD558\uAC8C \uB418\uC5C8\uC2B5\uB2C8\uAE4C?
ForageAudit_Sample_ToolTip=\uB3C4\uAD6C \uB3C4\uC6C0\uB9D0\uC744 \uC704\uD55C \uC0D8\uD50C. \uC2E4\uC81C \uC785\uB825\uAC12\uC774 \uC788\uC744\uB54C \uC81C\uAC70\uB428
UserPreferencesViewModel.SelectPointScale=\uAE30\uC900\uC774 \uB418\uB294 BCS 1/2/3/4/5\uC911 \uD558\uB098\uB97C \uC120\uD0DD\uD558\uC138\uC694.
Global=글로벌
France=프랑스
CPNFrance=프랑스
Netherlands=네덜란드
Spain=스페인
Italy=이탈리아
Korea=한국
CPNBrazil=브라질
NorthAmerica=북아메리카
Portugal=포르투갈
CFNChina=중국
CFNIndia=인도
CPNPoland=폴란드
CPNUS=미국
Report.SheetName=보고서 마스터
Report.BCS.EvalDataTitle=계산 된 BCS 평가 데이터
Report.EvalDataTitle=계산 된 평가 데이터
Report.Bcs.Min=최소값. BCS
Report.BCS.Max=최대값. BCS
Report.BCS.MilkHeadDay=우유/HD/일
Report.BCSAvg=평균 BCS
Report.BCS.LactationStages=수유 단계
Report.Visit.name=이름을 방문하십시오
Report.Visit.Date=방문 날짜
Report.Tool.Name=도구 이름
Report.Analysis.Type=분석 유형
Report.Bcs.HerdAnalysis.ChartName=BCS 대 우유
Report.Bcs=신체충실지수 (BCS)
Report.Bcs.Milk=우유
Report.CudChewing.EvalDataTitle=계산 된 CUD 씹기 평가 데이터
Report.CudChewingPercentage=반추 저작 % /
Report.No.OfChews=반추횟수
Report.No.OfChewsPerRegurgitation=역류 당 씹는 수
Report.goalChews=목표 씹기
Report.Chews=씹다
Report.GoalCudChewingPercentage=목표 반추 저작 % /
Report.CudChewingPercentage.Vs.LactStages=반추 저작 % /
Report.NoOfChews.Vs.LactStages=반추횟수
Report.Herd.Analysis.CudChewingPercentage=반추 저작 % /
Report.locomotionScore.Pen.Analysis.ChartName=카테고리 대 방문 날짜
Report.Bcs.ChartName=신체 상태 점수 - 동물 {0}
Report.Animal.Tag.Name=동물 ID
Report.Calving.Date=분만
Report.LocomotionScore.chartName=운동 점수 - 동물 {0}
Report.Locomotion.HerdAnalysis.ChartName=운동 점수 백분율
Report.LocomotionScore.X.Axis=운동 점수
Report.LocomotionScore.Y.Axis=% %
Lactation=젖 분비
FreshCow=신선한 소
DryCow=마른 소
TransitionCow=전환 소
Report.AvgRumenFillScore=
Report.ForagePennState=TMR Penn State
Report.PercentageOnScreen=스크린 내 비율 (%)
Report.Cargill.Report=카길 보고서
Report.Visit.Report=방문 보고서
Report.Tool.Details=도구 세부사항
Visit.Report.Footer.Patent=
USD=미국 ($ USD)
GBP=영국 (GBP GBP)
CAD=캐나다 (CA$ CAD)
DZD=알제리 (DA DZD)
ARS=아르헨티나 ($ ARS)
AUD=호주 ($ AUD)
CNY=중국 (CNY CNY)
CZK=체코공화국 (CZK CZK)
GTQ=과테말라 (Q GTQ)
HNL=온두라스 (HNL HNL)
HUF=헝가리 (Ft HUF)
BRL=브라질 (R$ BRL)
INR=인도 (INR INR)
IDR=인도네시아 (Rp IDR)
MYR=말레이시아 (MYR MYR)
MXN=멕시코 (PESO MXN)
NIO=니카라과 (NIO NIO)
PEN=페루 (S/. PEN)
PHP=필리핀 ($ PHP)
PLN=폴란드 (z\u0142 PLN)
PON=루마니아 (lei PON)
RUB=러시아 (\u20BD\u200E RUB)
SAR=사우디아라비아 (\uFDFC SAR)
ZAR=남아프리카 (ZAR ZAR)
KRW=한국 (\u20A9 KRW)
SRD=수리남 ($ SRD)
CHF=스위스 (CHF CHF)
TWD=대만 (NT$ TWD)
THB=태국 (THB THB)
UAH=우크라니아 (UAH UAH)
VEF=베네수엘라 (Bs VEF)
VND=베트남 (\u20AB VND)
CLP=칠레 ($ CLP)
Cargill=카길
Purina=퓨리나
Provimi=프로비미
ProvimiUS=Provimi US
Imperial=야드파운드법
Metric=미터법
Screen=단 스크린
ScreenOld=단 스크린(구형)
ScreenNew=단 스크린(신형)
Straw=빨대
Dryhay=마른 건초
Corn=옥수수
Report.PenTimeBudgetTimeRemaining=남은 시간
Report.PenTimeBudgetTimeRequired=소요 시간
Report.PenTimeBudget.TimeAvailableForResting.Label=시간
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=쉬는 시간
Report.Heatstress.Temperature.In.Celcius=온도 \ U2103
Report.Heatstress.Temperature.In.Farenhiet=온도 \ U2109
Report.Heatstress.Intake.Adjustment=섭취 조정
Report.Heatstress.Estimated.Dry.Matter.Intake=예상 건조 물질 섭취 ({0})
Report.Heatstress.Loss.Of.Energy.Consumed=소비 된 에너지 손실 (MCAL)
Report.Heatstress.Milk.Value.Loss.Perday=우유 가치 손실 (하루) ({0})
Report.Heatstress.Dmi.Adjustment=DMI 조정
Report.Heatstress.Reduction.In.Dmi=DMI 감소 ({0})
Report.Heatstress.Energy.Equivalent.Milk.Loss=에너지 등가 우유 손실 ({0})
Report.Heatstress.Milk.Value.Loss.PerMonth=우유 가치 손실 (매월) ({0})
Report.Pentime.Budget.Hours=시간
Report.Heatstress.TemperatureHumidityIndex=온도 습도 지수
Report.Heatstress.Legend=전설
Report.Heatstress.Legends=전설
Report.Heatstress.Stress.Threshold=스트레스 임계 값
Report.Heatstress.Mild.Moderate.Stress=온화한 - 적당한 스트레스
Report.Heatstress.Moderate.Severe.Stress=보통 - 심각한 스트레스
Report.Heatstress.Severe.Stress=심한 스트레스
Report.Heatstress.Mild.Moderate.Stress.Message=호흡은 75 bpm을 초과합니다 | 직장 온도는 39 \ U2103 (102.2 \ U2109)을 초과합니다.
Report.Heatstress.Stress.Threshold.Message=호흡은 60 bpm을 초과합니다 | 재구성 손실 감지 가능 | 직장 온도는 38.5 \ U2103을 초과합니다 (101.3 \ U2109)
Report.Heatstress.Moderate.Severe.Stress.Message=호흡은 85 bpm |를 초과합니다 직장 온도는 40 \ u2103 (104 \ u2109)을 초과합니다.
Report.Heatstress.Severe.Stress.Message=호흡은 120-140 bpm을 초과합니다 직장 온도는 41 \ U2103 (106 \ U2109)을 초과합니다.
Report.RumenHealthManureScreening.Top=맨 위
Report.RumenHealthManureScreening.Middle=가운데
Report.RumenHealthManureScreening.Bottom=맨 아래
Report.RumenHealthManureScreening.TopGoalMin=최고 목표 분
Report.RumenHealthManureScreening.TopGoalMax=최고의 골 맥스
Report.RumenHealthManureScreening.MiddleGoalMin=중간 목표 분
Report.RumenHealthManureScreening.MiddleGoalMax=중간 목표 최대
Report.RumenHealthManureScreening.BottomGoalMin=하단 목표 분
Report.RumenHealthManureScreening.BottomGoalMax=하단 목표 최대
Report.Heatstress.Temperature=온도
Report.Heatstress.Relative.Humidity=상대 습도 (%)
Thirdparty=제3자
Consumer=사양가
Competitor=경쟁자
Report.Chewing=반추 비율()
Report.Not.Chewing=씹지 않음
Report.Animal.Analysis=동물 분석
Report.General.Comments=일반정보 논평
Bunker=사일로 벙커
TopUnloadingSilo=상부 하역 사일로
BottomUnloadingSilo=하부 하역 사일로
Bag=포대
NotSet= - 
Andorra=안도라
United_Arab_Emirates=아랍 에미리트
Afghanistan=아프가니스탄
Antigua_and_Barbuda=안티구아와 바부 다
Anguilla=앵 guilla
Albania=알바니아
Armenia=아르메니아
Angola=앙골라
Antarctica=남극 대륙
Austria=오스트리아
Aruba=아루바
Aland_Islands=앨랜드 제도
Azerbaijan=아제르바이잔
Bosnia_and_Herzegovina=보스니아 헤르체고비나
Barbados=바베이도스
Bangladesh=방글라데시
Belgium=벨기에
Burkina_Faso=부키 나 파소
Bulgaria=불가리아
Bahrain=바레인
Burundi=부룬디
Benin=베닌
Saint_Barthélemy=생 바르텔레미
Bermuda=버뮤다
Brunei_Darussalam=브루나이 다 루살람
Bolivia,_Plurinational_State_of=볼리비아, 다국적 상태
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius 및 Saba
Bahamas=바하마
Bhutan=부탄
Bouvet_Island=Bouvet Island
Botswana=보츠와나
Belarus=벨라루스
Belize=벨리즈
Cocos_(Keeling)_Islands=코코 (킬링) 섬
Congo,_the_Democratic_Republic_of_the=콩고, 민주 공화국
Central_African_Republic=중앙 아프리카 공화국
Congo=콩고
Cote_d'Ivoire=아이보리 해안
Cook_Islands=쿡 제도
Cameroon=카메룬
Colombia=콜롬비아
Costa_Rica=코스타리카
Cuba=쿠바
Cape_Verde=그린 케이프
Curaçao=Curaãçao
Christmas_Island=크리스마스 섬
Cyprus=키프로스
Czech_Republic=체코 공화국
Germany=독일
Djibouti=지부티
Denmark=덴마크
Dominica=도미니카
Dominican_Republic=도미니카 공화국
Ecuador=에콰도르
Estonia=에스토니아
Egypt=이집트
Western_Sahara=서부 사하라
Eritrea=에리트레아
Ethiopia=에티오피아
Finland=핀란드
Fiji=피지
Falkland_Islands_(Malvinas)=포클랜드 제도 (Malvinas)
Faroe_Islands=Faroe Islands
Gabon=가봉
United_Kingdom=영국
Grenada=그레나다
Georgia=그루지야
French_Guiana=프랑스 기아나
Guernsey=건지
Ghana=가나
Gibraltar=지브롤터
Greenland=그린란드
Gambia=감비아
Guinea=기니
Guadeloupe=과드루프
Equatorial_Guinea=적도 기니
Greece=그리스
South_Georgia_and_the_South_Sandwich_Islands=사우스 조지아와 사우스 샌드위치 제도
Guam=괌
Guinea-Bissau=기니 비시
Guyana=가이아나
Heard_Island_and_McDonald_Islands=섬과 맥도날드 제도가 들렸다
Croatia=크로아티아
Haiti=아이티
Ireland=아일랜드
Israel=이스라엘
Isle_of_Man=사람의 섬
British_Indian_Ocean_Territory=영국 인도양 영토
Iraq=이라크
Iran,_Islamic_Republic_of=이란, 이슬람 공화국
Iceland=아이슬란드
Jersey=저지
Jamaica=자메이카
Jordan=요르단
Japan=일본
Kenya=케냐
Kyrgyzstan=키르기스스탄
Cambodia=캄보디아
Kiribati=키리 바티
Comoros=코 모로
Saint_Kitts_and_Nevis=세인트 키츠와 네비스
Korea,_Democratic_People's_Republic_of=한국, 민주당 인민 공화국
Korea,_Republic_of=대한민국
Kuwait=쿠웨이트
Cayman_Islands=케이맨 제도
Kazakhstan=카자흐스탄
Lao_People's_Democratic_Republic=라오스 민주 공화국
Lebanon=레바논
Saint_Lucia=세인트 루시아
Liechtenstein=리히텐슈타인
Sri_Lanka=스리랑카
Liberia=라이베리아
Lesotho=레소토
Lithuania=리투아니아
Luxembourg=룩셈부르크
Latvia=라트비아
Libyan_Arab_Jamahiriya=리비아 아랍 자마 이리 야
Morocco=모로코
Monaco=모나코
Moldova,_Republic_of=몰도바, 공화국
Montenegro=몬테네그로
Saint_Martin_(French_part)=세인트 마틴 (프랑스 부분)
Madagascar=마다가스카르
Chad=차드
Macedonia,_the_former_Yugoslav_Republic_of=마케도니아, 전 유고 슬라비아 공화국
Mali=해야 했어
Myanmar=미얀마
Mongolia=몽골리아
Macao=마카오
Martinique=마르티니크
Mauritania=모리타니
Montserrat=Montserrat
Malta=몰타
Mauritius=모리셔스
Maldives=몰디브
Malawi=불꽃
Mozambique=모잠비크
Namibia=나미비아
New_Caledonia=뉴 칼레도니아
Niger=니제르
Norfolk_Island=노퍽 섬
Nigeria=나이지리아
Norway=노르웨이
Nepal=네팔
Nauru=나우루
Niue=니우
New_Zealand=뉴질랜드
Oman=자신의
Panama=파나마
French_Polynesia=프랑스 령 폴리네시아의
Papua_New_Guinea=파푸아 뉴기니
Pakistan=파키스탄
Saint_Pierre_and_Miquelon=세인트 피에르와 미 킬론
Pitcairn=핏 케언
Puerto_Rico=푸에르토 리코
Palestinian_Territory,_Occupied=팔레스타인 영토, 점령
Paraguay=파라과이
Qatar=카타르
Reunion=재결합
Serbia=세르비아
Russian_Federation=러시아 연맹
Rwanda=르완다
Saudi_Arabia=사우디 아라비아
Solomon_Islands=솔로몬 제도
Seychelles=세이셸
Sudan=수단
Sweden=스웨덴
Singapore=싱가포르
Saint_Helena,_Ascension_and_Tristan_da_Cunha=세인트 헬레나, 승천 및 트리스탄 다 쿠 하
Slovenia=슬로베니아
Svalbard_and_Jan_Mayen=Svalbard와 Jan Mayen
Slovakia=슬로바키아
Sierra_Leone=시에라 리온
San_Marino=산 마리노
Senegal=세네갈
Somalia=소말리아
Suriname=수리남
South_Sudan=남 수단
Sao_Tome_and_Principe=Sao Tome과 Principe
El_Salvador=구주
Sint_Maarten_(Dutch_part)=Sint Marthes (네덜란드 부분)
Syrian_Arab_Republic=시리아 아랍 공화국
Swaziland=스와질란드
Turks_and_Caicos_Islands=터키와 카이코스 제도
French_Southern_Territories=프랑스 남부 영토
Togo=토고
Tajikistan=타지키스탄
Tokelau=Tokelau
Timor-Leste=동 티모르 읽기
Turkmenistan=투르크 메니스탄
Tunisia=튀니지
Tonga=도착했다
Turkey=칠면조
Trinidad_and_Tobago=트리니다드 토바고
Tuvalu=투발루
Chinese_Taipei=차이니즈 타이베이
Tanzania,_United_Republic_of=탄자니아, 연합 공화국
Uganda=우간다
United_States=미국
Uruguay=우루과이
Uzbekistan=우즈베키스탄
Holy_See_(Vatican_City_State)=거룩한 참조 (바티칸 시티 주)
Saint_Vincent_and_the_Grenadines=세인트 빈센트와 그레나딘
Venezuela,_Bolivarian_Republic_of=베네수엘라, 볼리바리아 공화국
Virgin_Islands,_British=버진 아일랜드, 영국
Viet_Nam=베트남
Vanuatu=바누아투
Wallis_and_Futuna=Wallis와 Futuna
Samoa=사모아
Yemen=예멘
Mayotte=Mayotte
South_Africa=남아프리카
Zambia=잠비아
Zimbabwe=짐바브웨

Australian_Capital_Territory=호주 수도 영토
New_South_Wales=뉴 사우스 웨일즈
Northern_Territory=노던 테리토리
Queensland=퀸즐랜드
South_Australia=남호주
Tasmania=태즈 매니아
Victoria=빅토리아
Western_Australia=웨스턴 오스트레일리아
Bonaire=보네이
Acre=에이커
Alagoas=Alagoas
Amazonas=아마존
Amapá=ampass
Bahia=바히아
Ceará=정사각형
Distrito_Federal=연방 지구
Goiás=Goião
Maranhão=Maranhã £ o
Minas_Gerais=미나스 게라 이스
Mato_Grosso_do_Sul=Mato Grosso do sul
Mato_Grosso=마토 그로소
Pará=PARAIATORY
Paraíba=파라 바
Pernambuco=Pernambuco
Piauí=피아우
Paraná=Paranã
Rio_de_Janeiro=리오 데 자네이로
Rio_Grande_do_Norte=큰 북부 강
RondÃ´nia=론 니아
Roraima=로라 이마
Rio_Grande_do_Sul=리오 그란데는 술
Santa_Catarina=산타 카타리나
Sergipe=Sergipe
São_Paulo=상 파울로
Tocantins=Tocantins
Alberta=앨버타
British_Columbia=브리티시 컬럼비아
Manitoba=매니토바
New_Brunswick=뉴 브런 즈윅
Newfoundland_and_Labrador=뉴 펀들 랜드와 래브라도
Nova_Scotia=새로운 스코샤
Northwest_Territories=노스 웨스트 영토
Nunavut=누나 부트
Ontario=온타리오
Prince_Edward_Island=프린스 에드워드 아일랜드
Quebec=퀘벡
Saskatchewan=서스 캐처 원
Yukon_Territories=유콘 영토
Beijing=베이징
Tianjin=Tianjin
Hebei=헤베이
Shanxi=샨시
Nei_Mongol=Nei Mongol
Liaoning=리아 온
Jilin=Jilin
Heilongjiang=헤일 롱즈 판
Shanghai=상하이
Jiangsu=Jiangsu
Zhejiang=잔기 조그
Anhui=안후이
Fujian=후지안
Jiangxi=Jiangxi
Shandong=산동
Henan=HENAN
Hubei=후베이
Hunan=본인
Guangdong=광동
Guangxi=광시
Hainan=하이난
Chongqing=충칭
Sichuan=사천
Guizhou=구이 저우
Yunnan=윈난
Xizang=시짱
Shaanxi=Shaanxi
Gansu=간수
Qinghai=Qinghai
Ningxia=ningxia
Xinjiang=신장
Hong_Kong=홍콩
Cundinamarca=Cundinamarca
Bayern=바이에른
Niedersachsen=더 낮은 색슨 슨
Nordrhein=노스 라인
Rheinland=라인 랜드
Schleswig=슐레스비히
Pays=지불합니다
Clare=클레어
Cavan=카반
Cork=코르크
Carlow=카를로우
Dublin=더블린
Donegal=도네 갈
Galway=골웨이
Kildare=킬 다레
Kilkenny=킬 케니
Kerry=케리
Longford=Longford
Louth=Louth
Limerick=오행 희시
Leitrim=레트림
Laois=라오스
Meath=미트
Monaghan=모나한
Mayo=마요
Offaly=오프탈리
Roscommon=로스 커먼
Sligo=슬리고
Tipperary=티퍼 러리
Waterford=워터 포드
Westmeath=웨스트 미스
Wicklow=위 클로
Wexford=xford
Andaman_and_Nicobar_Islands=안다만과 니코 바르 제도
Andhra_Pradesh=안드라 프라데시
Arunachal_Pradesh=아루나 찰 프라데시
Assam=아삼
Bihar=비하르
Chandigarh=찬디 가르
Chhattisgarh=Chhattisgarh
Daman_and_Diu=다만과 디우
Delhi=델리
Dadra_and_Nagar_Haveli=Dadra와 Nagar Haveli
Goa=고아
Gujarat=구자라트
Himachal_Pradesh=히 마찰 프라데시
Haryana=하리 아나
Jharkhand=Jharkhand
Jammu_and_Kashmir=잠무와 카슈미르
Karnataka=카르 나 타카
Kerala=케 랄라
Lakshadweep=Lakshadweep
Maharashtra=마하라 슈트라
Meghalaya=메갈 라야
Manipur=마니 푸르
Madhya_Pradesh=마디 야 프라데시
Mizoram=미조 람
Nagaland=나갈 란드
Odisha=오디 샤
Punjab=펀 자브
Puducherry=푸두 체리
Rajasthan=라자스탄
Sikkim=시킴
Tamil_Nadu=타밀 나두
Tripura=트리 푸라
Uttar_Pradesh=우타르 프라데시
Uttarakhand=Uttarakhand
West_Bengal=서 뱅갈
Agrigento=agrigento
Alessandria=알렉산드리아
Ancona=안코나
Aosta=오스타
Ascoli_Piceno=Ascoli Piceno
L'Aquila=L 'Aquila
Arezzo=Arezzo
Asti=까지
Avellino=아벨 리노
Bari=그들은
Bergamo=베르가모
Biella=비젤라
Belluno=벨루노
Benevento=베네 벤토
Bologna=볼로냐
Brindisi=토스트
Brescia=브레시아
Barletta-Andria-Trani=Barletta-Andria-Trani
Bolzano=볼 자노
Cagliari=Cagliari
Campobasso=Campobasso
Caserta=카세타
Chieti=Chieti
Carbonia-Iglesias=카르 보니아-이글레시아
Caltanissetta=Caltanissetta
Cuneo=쐐기
Como=처럼
Cremona=크레모나
Cosenza=코센자
Catania=카타니아
Catanzaro=카탄 자로
Enna=Enna
ForlÃ¬-Cesena=Forlã¬-Cesena
Ferrara=페라라
Foggia=Foggia
Florence=피렌체
Fermo=멈췄다
Frosinone=Frosinone
Genoa=제노아
Gorizia=고리 지아
Grosseto=그로스 세토
Imperia=임페리리아
Isernia=Isernia
Crotone=크로톤
Lecco=레코
Lecce=레체
Livorno=리본
Lodi=반대되는
Latina=라티 나
Lucca=루카
Monza_and_Brianza=Monza와 Brianza
Macerata=MacRate
Messina=메시나
Milan=밀라노
Mantua=Mantua
Modena=모데나
Massa_and_Carrara=마사와 카라라
Matera=Matera
Naples=나폴리
Novara=노바라
Nuoro=누오로
Ogliastra=Ogliastra
Oristano=오리 스탄
Olbia-Tempio=Olbia-Tempio
Palermo=팔레르모
Piacenza=Piacenza
Padua=Padua
Pescara=페스 카라
Perugia=페루지아
Pisa=피사
Pordenone=포르 데논
Prato=접시
Parma=파마
Pistoia=피스토리아
Pesaro_and_Urbino=페사로와 우르 비노
Pavia=파비아
Potenza=힘
Ravenna=라벤나
Reggio_Calabria=Reggio Calabria
Reggio_Emilia=레지오 에밀리아
Ragusa=라구 사
Rieti=리에티
Rome=로마
Rimini=리 미니
Rovigo=로보
Salerno=살레르노
Siena=시에나
Sondrio=Sondrio
La_Spezia=기미
Syracuse=시러큐스
Sassari=사 사리
Savona=사보나
Taranto=타란토
Teramo=테라모
Trento=트 렌토
Turin=토리노
Trapani=Trapani
Terni=테르니
Trieste=트리 에스테
Treviso=트레비소
Udine=우딘
Varese=varese
Verbano-Cusio-Ossola=Verbano-Cusio-Sossola
Vercelli=Vercelli
Venice=베니스
Vicenza=비켄자
Verona=베로나
Medio_Campidano=메디오 캄 피노
Viterbo=viterbo
Vibo_Valentia=Vibo Valentia
Hokkaido=hokkaido
Tokyo=도쿄
Seoul=서울
Aguascalientes=aguascalientes
Baja_California=바하 캘리포니아
Baja_California_Sur=Baja California Sur
Chihuahua=치와와
Colima=콜리마
Campeche=캠치
Coahuila=Coahuila
Chiapas=치아파스
Federal_District=연방 지구
Durango=두랑고
Guerrero=게레로
Guanajuato=구아나후토
Hidalgo=Hidalgo
Jalisco=Jalisco
Mexico_State=멕시코 주
Michoacán=Michoacán
Morelos=모렐 로스
Nayarit=Nayarit
Nuevo_León=새로운 리언
Oaxaca=옥사카
Puebla=푸에블라
Querétaro=쿼터 é Taro
Quintana_Roo=Quintana Roo
Sinaloa=Sinaloa
San_Luis_Potosí=산 루이스 포토토스
Sonora=소노라
Tabasco=타바스코
Tlaxcala=tlaxcala
Tamaulipas=타마 울리 파스
Veracruz=베라 크루즈
Yucatán=유카탄
Zacatecas=Zacatecas
Vestland=웨스트 랜드
Taipei_City=타이페이시
Alaska=아래에
Alabama=앨라배마
Arkansas=아칸소
Arizona=애리조나
California=캘리포니아
Colorado=콜로라도
Connecticut=코네티컷
District_of_Columbia=컬럼비아 특별구
Delaware=델라웨어
Florida=플로리다
Hawaii=하와이
Iowa=아이오와
Idaho=아이다 호
Illinois=일리노이
Indiana=인디애나
Kansas=캔자스
Kentucky=켄터키
Louisiana=루이지애나
Massachusetts=매사추세츠 주
Maryland=메릴랜드
Maine=메인
Michigan=미시간
Minnesota=미네소타
Missouri=미주리
Mississippi=미시시피
Montana=몬태나
North_Carolina=노스 캐롤라이나
North_Dakota=노스 다코타
Nebraska=네브래스카
New_Hampshire=뉴햄프셔
New_Jersey=뉴저지
New_Mexico=뉴 멕시코
Nevada=네바다
New_York=뉴욕
Ohio=오하이오
Oklahoma=오클라 올라
Oregon=오레곤
Pennsylvania=펜실베이니아
Rhode_Island=로드 아일랜드
South_Carolina=사우스 캐롤라이나
South_Dakota=사우스 다코타
Tennessee=테네시
Texas=텍사스
Utah=유타
Virginia=여자 이름
Vermont=버몬트
Washington=워싱턴
Wisconsin=위스콘신
West_Virginia=웨스트 버지니아
Wyoming=와이오밍
Espírito_Santo=에스피리토 산투

AMSUtilization=로봇 사용
CowEfficiency=능률
ConcentrateDistribution=집중된 분포
AMSUtilizationChart=처리 로봇 사용 테이블
CowPerRobot=로봇 당 소
MilkingFailure=착유 고장
SelectVisitComparison=비교를 위해 방문을 선택하십시오
TwentyFourHoursBeforeActionIsDue=행동이 24 시간 전일 예정입니다
OneHourBeforeActionIsDue=행동이 1 시간 전에 마감되기 전에
TwentyFourHoursAndNoSync=24 시간, 동기화 없음
VisitAutoPublished=Auto Published를 방문하십시오
Sync-failed-due-to-unknown-reason=La synchronisation a échoué pour des raisons inconnues
Null-values-not-allowed={0}의 NULL 값은 허용되지 않습니다
Length-exceed-allowed-limit={0} 허용 된 총 {0} 문자의 길이를 초과합니다
Not-matching-with-allowed-values={0} 허용 값과 일치하지 않습니다
Lift-Sync-Fail=\uC54C \uC218\uC5C6\uB294 \uC774\uC720\uB85C \uB3D9\uAE30\uD654 \uC2E4\uD328, \uC5F0\uB77D\uCC98 \uC138\uBD80 \uC0AC\uD56D\uC758 \uD574\uC0C1\uB3C4\uB294 \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD558\uC2ED\uC2DC\uC624.
No-User-Found= USER not found on LIFT; please contact the admin for resolution
Account-Not-Synced-To-Lift= Account was not synced to LIFT; please contact the admin for resolution
Site-Not-Synced-To-Lift= Site was not synced to LIFT; please contact the admin for resolution
Account=Account
Clean=Clean
Moderate=Moderate
Dirty=Dirty
Holandesa=Holandesa
Jersey=Jersey
Girolando=Girolando
Good=Good
Medium=Medium
Bad=Bad
Freestall=Freestall
Compostbarn=Compostbarn
Semiconfinamento=Semiconfinamento
Pasto=Pasto
Noah=Noah
ProfitablityAnalysis.Date=Date
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole
Rof.Kg.Per.Fat=Return Over Feed Kg Per Fat






