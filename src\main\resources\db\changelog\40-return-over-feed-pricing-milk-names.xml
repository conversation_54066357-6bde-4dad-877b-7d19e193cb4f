<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="040" author="Taha">
		<sql>
			UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    '"butterfat"'
)
WHERE pricing_return_over_feed_document->>'Name' = 'Fat, $/kg';

UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    '"protein"'
)
WHERE pricing_return_over_feed_document->>'Name' = 'Protein, $/kg';

UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    '"lactoseAndOtherSolids"'
)
WHERE pricing_return_over_feed_document->>'Name' = 'Lactose and other solids, $/kg';

UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    '"deductions"'
)
WHERE pricing_return_over_feed_document->>'Name' = 'Deductions, $/l';

UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    '"class2Protein"'
)
WHERE pricing_return_over_feed_document->>'Name' = 'Class 2 Protein, $/kg';

UPDATE return_over_feed_pricings
SET pricing_return_over_feed_document = jsonb_set(
    pricing_return_over_feed_document,
    '{Name}', 
    '"class2LactoseAndOtherSolids"'
)
WHERE pricing_return_over_feed_document->>'Name' = 'Class 2 Lactose and other solids, $/kg';

			
		</sql>
	</changeSet>

</databaseChangeLog>