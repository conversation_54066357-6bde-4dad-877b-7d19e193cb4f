/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.mapper;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.LeadSource;
import com.app.cargill.constants.Level;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import java.time.Instant;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrescendoContactMapper {

  private CrescendoContactMapper() {}

  public static Contact crescendoToModel(ContactCrescendo contactCrescendo) {
    Contact contact = new Contact();
    try {
      contact.setAccountId(UUID.fromString(contactCrescendo.getAccountId()));
    } catch (Exception e) {
      log.warn("Unable to convert accountId to UUID: {}", contactCrescendo.getAccountId());
    }

    contact.setGoldenRecordAcountId(contactCrescendo.getGoldenRecordAccountId());
    contact.setSFDCContactId(
        contactCrescendo.getSfdcContactId() != null
            ? contactCrescendo.getSfdcContactId()
            : contactCrescendo.getId());
    contact.setFirstName(contactCrescendo.getFirstName());
    contact.setLastName(contactCrescendo.getLastName());
    contact.setPhoneNumber(contactCrescendo.getPhoneNumber());
    contact.setEmailAddress(contactCrescendo.getEmailAddress());
    contact.setComments(contactCrescendo.getComments());
    contact.setSalutation(contactCrescendo.getSalutation());
    contact.setTitle(contactCrescendo.getTitle());
    if (contactCrescendo.getMailingAddress() != null) {
      contact.setMailingAddress(
          CrescendoAddressMapper.crescendoToModel(contactCrescendo.getMailingAddress()));
    }
    if (contactCrescendo.getOtherAddress() != null) {
      contact.setOtherAddress(
          CrescendoAddressMapper.crescendoToModel(contactCrescendo.getOtherAddress()));
    }

    contact.setDescription(contactCrescendo.getDescription());
    contact.setPrimaryOwner(contactCrescendo.getPrimaryOwner());
    if (contactCrescendo.getBusinessId() != null) {
      contact.setBusinessId(
          Business.valueOf(contactCrescendo.getBusinessId().getValue()).getBusinessId());
    }
    contact.setReportsToID(contactCrescendo.getReportsToID());
    if (contactCrescendo.getExternalReportsToID() != null) {
      try {
        contact.setExternalReportsToID(UUID.fromString(contactCrescendo.getExternalReportsToID()));
      } catch (Exception e) {
        log.warn(
            "Unable to convert ExternalReportsToID: {}", contactCrescendo.getExternalReportsToID());
      }
    }

    contact.setWebsite(contactCrescendo.getWebsite());
    contact.setDepartment(contactCrescendo.getDepartment());
    try {
      contact.setContactId(UUID.fromString(contactCrescendo.getExternalId()));
    } catch (Exception e) {
      contact.setContactId(UUID.randomUUID());
      contact.setNeedsSync(true);
    }
    contact.setFlowName(contactCrescendo.getFlowName());
    contact.setLanguages(contactCrescendo.getLanguages());
    if (contactCrescendo.getLeadSource() != null) {
      contact.setLeadSource(LeadSource.valueOf(contactCrescendo.getLeadSource().getValue()));
    }
    if (contactCrescendo.getLevel() != null) {
      contact.setLevel(Level.valueOf(contactCrescendo.getLevel().getValue()));
    }
    contact.setPrimary(contactCrescendo.getPrimary());
    contact.setDeleted(isTrue(contactCrescendo.getIsDeleted()));
    contact.setCreateTimeUtc(contactCrescendo.getCreateTimeUtc());

    if (contactCrescendo.getLastModifiedTimeUtc() != null) {
      contact.setLastModifiedTimeUtc(
          DateEpoch.builder().date(contactCrescendo.getLastModifiedTimeUtc()).build());
    }
    contact.setLastSyncTimeUtc(contactCrescendo.getLastSyncTimeUtc());
    contact.setIsNew(contactCrescendo.getIsNew());

    return contact;
  }

  public static ContactCrescendo documentToCrescendo(Contact contactDocument) {
    ContactCrescendo contactCrescendo = new ContactCrescendo();
    contactCrescendo.setSfdcContactId(contactDocument.getSFDCContactId());
    contactCrescendo.setAccountId(
        contactDocument.getAccountId() != null ? contactDocument.getAccountId().toString() : null);
    contactCrescendo.setId(contactDocument.getContactId().toString());
    contactCrescendo.setFirstName(contactDocument.getFirstName());
    contactCrescendo.setLastName(contactDocument.getLastName());
    contactCrescendo.setPhone(contactDocument.getPhoneNumber());
    contactCrescendo.setPhoneNumber(contactDocument.getPhoneNumber());
    contactCrescendo.setEmailAddress(contactDocument.getEmailAddress());
    contactCrescendo.setCreateTimeUtc(contactDocument.getCreateTimeUtc());
    contactCrescendo.setLastModifiedTimeUtc(
        contactDocument.getLastModifiedTimeUtc() != null
            ? Instant.ofEpochSecond(contactDocument.getLastModifiedTimeUtc().getEpoch())
            : null);
    contactCrescendo.setLastSyncTimeUtc(contactDocument.getLastSyncTimeUtc());
    contactCrescendo.setGoldenRecordAccountId(contactDocument.getGoldenRecordAcountId());
    if (contactDocument.getMailingAddress() != null) {
      contactCrescendo.setMailingAddress(
          CrescendoAddressMapper.modelToCrescendo(contactDocument.getMailingAddress()));
    }
    contactCrescendo.setIsDeleted(contactDocument.isDeleted());
    return contactCrescendo;
  }

  public static Contact modelUpdate(Contact existing, Contact updated) {
    existing.setGoldenRecordAcountId(updated.getGoldenRecordAcountId());
    existing.setSFDCContactId(updated.getSFDCContactId());
    existing.setFirstName(updated.getFirstName());
    existing.setLastName(updated.getLastName());
    existing.setFunctionId(updated.getFunctionId());
    existing.setPhoneNumber(updated.getPhoneNumber());
    existing.setEmailAddress(updated.getEmailAddress());
    existing.setComments(updated.getComments());
    existing.setSalutation(updated.getSalutation());
    existing.setTitle(updated.getTitle());
    existing.setMailingAddress(updated.getMailingAddress());
    existing.setOtherAddress(updated.getOtherAddress());
    existing.setHomePhone(updated.getHomePhone());
    existing.setDescription(updated.getDescription());
    existing.setPrimaryOwner(updated.getPrimaryOwner());
    existing.setBusinessId(updated.getBusinessId());
    existing.setReportsToID(updated.getReportsToID());
    existing.setExternalReportsToID(updated.getExternalReportsToID());
    existing.setSecondaryEmail(updated.getSecondaryEmail());
    existing.setPreferredMethodId(updated.getPreferredMethodId());
    existing.setWebsite(updated.getWebsite());
    existing.setWebsite(updated.getWebsite());
    existing.setDepartment(updated.getDepartment());
    existing.setFlowName(updated.getFlowName());
    existing.setLanguages(updated.getLanguages());
    existing.setLeadSource(updated.getLeadSource());
    existing.setLevel(updated.getLevel());
    existing.setPrimary(updated.getPrimary());
    existing.setDeleted(updated.isDeleted());
    existing.setCreateTimeUtc(updated.getCreateTimeUtc());
    existing.setLastModifiedTimeUtc(updated.getLastModifiedTimeUtc());
    existing.setLastSyncTimeUtc(updated.getLastSyncTimeUtc());
    existing.setIsNew(updated.getIsNew());
    existing.setNeedsSync(false);
    return existing;
  }
}
