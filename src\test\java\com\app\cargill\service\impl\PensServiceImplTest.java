/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.AnalyzeStatus;
import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.FormulateStatus;
import com.app.cargill.constants.Nutrients;
import com.app.cargill.constants.OptimizationType;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.*;
import com.app.cargill.dto.DuplicatePenIdDTO;
import com.app.cargill.dto.PenDto;
import com.app.cargill.dto.PenGroupingDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.model.tasks.DuplicatePens;
import com.app.cargill.repository.*;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class PensServiceImplTest {

  // Do not delete to prevent NPE for UserService
  @Mock private IUserService userServiceImpl;
  @Mock private SitesRepository sitesRepository;
  @Mock private PensRepository pensRepository;
  @Mock private AccountsRepository accountsRepository;
  @Mock private DietRepository dietRepository;
  @InjectMocks private PensServiceImpl pensService;

  @Mock private DuplicatePens duplicatePens;
  @Mock private VisitsRepository visitsRepository;

  @Test
  void getAllPensByCurrentLoggedInUserReturnsExpected() {

    Page<Pens> pens =
        new PageImpl<>(
            List.of(
                Pens.builder()
                    .penDocument(new PenDocument())
                    .updatedDate(Date.from(Instant.now()))
                    .build()));
    when(pensRepository.findByCustomerAccountIdAndUpdatedDate(any(), any(), any()))
        .thenReturn(pens);
    PageImpl<PenDto> result =
        pensService.getAllPensByCurrentLoggedInUser(0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenNoPensForCurrentUserAreFoundEmptyPageIsReturned() {
    when(pensRepository.findByCustomerAccountIdAndUpdatedDate(any(), any(), any()))
        .thenReturn(null);
    PageImpl<PenDto> result =
        pensService.getAllPensByCurrentLoggedInUser(0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
  }

  @Test
  void getAllPensBySiteIdReturnsExpected() {

    Page<Pens> pens =
        new PageImpl<>(
            List.of(
                Pens.builder()
                    .penDocument(new PenDocument())
                    .updatedDate(Date.from(Instant.now()))
                    .build()));
    when(pensRepository.findBySiteIdAndUpdatedDate(any(), any(), any())).thenReturn(pens);
    PageImpl<PenDto> result =
        pensService.getAllPensBySiteId("111", 0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenNoPensForSiteIdAreFoundEmptyPageIsReturned() {
    when(pensRepository.findBySiteIdAndUpdatedDate(any(), any(), any())).thenReturn(null);
    PageImpl<PenDto> result =
        pensService.getAllPensBySiteId("111", 0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
  }

  @Test
  void whenSiteCannotBeFoundOnSaveExceptionIsThrown() {
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(UUID.randomUUID()).build();
    when(sitesRepository.findBySiteId(any())).thenReturn(null);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> {
          pensService.save(penGroupingDto);
        });
  }

  @Test
  void whenSaveSucceedsCorrectStatusIsSet() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> pens = Collections.singletonList(PenDto.builder().build());
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(pens).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .barns(
                            Collections.singletonList(Barn.builder().id(UUID.randomUUID()).build()))
                        .build())
                .build());
    when(pensRepository.saveAndFlush(any()))
        .thenReturn(
            Pens.builder()
                .penDocument(PenDocument.builder().build())
                .updatedDate(Date.from(Instant.now()))
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void whenSaveFailsCorrectStatusIsSet() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> pens = Collections.singletonList(PenDto.builder().siteId(siteId).build());
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(pens).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(SiteDocument.builder().barns(new ArrayList<>()).build())
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.FAILED, result.getPens().get(0).getStatus());
  }

  @Test
  void whenPenExistsStatusIsFail() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> pens = Collections.singletonList(PenDto.builder().siteId(siteId).build());
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(pens).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(SiteDocument.builder().barns(new ArrayList<>()).build())
                .build());
    when(pensRepository.findByLocalId(any()))
        .thenReturn(
            Collections.singletonList(
                Pens.builder()
                    .penDocument(PenDocument.builder().build())
                    .updatedDate(Date.from(Instant.now()))
                    .build()));

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.FAILED, result.getPens().get(0).getStatus());
  }

  @Test
  void whenSiteCannotBeFoundOnUpdateExceptionIsThrown() {
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(UUID.randomUUID()).build();
    when(sitesRepository.findBySiteId(any())).thenReturn(null);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> {
          pensService.update(penGroupingDto);
        });
  }

  @Test
  void whenPenIdCannotBeFoundOnUpdateFailStatusIsReturned() {
    UUID siteId = UUID.randomUUID();
    UUID penId = UUID.randomUUID();
    List<PenDto> pens =
        Collections.singletonList(PenDto.builder().id(penId).siteId(siteId).build());
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(pens).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(SiteDocument.builder().barns(new ArrayList<>()).build())
                .build());

    PenGroupingDto result = pensService.update(penGroupingDto);
    assertEquals(ResponseStatus.FAILED, result.getPens().get(0).getStatus());
  }

  @Test
  void whenUpdateSucceedsCorrectStatusIsSet() {
    UUID siteId = UUID.randomUUID();
    UUID penId = UUID.randomUUID();
    Pens pen =
        Pens.builder()
            .penDocument(PenDocument.builder().build())
            .updatedDate(Date.from(Instant.now()))
            .build();
    List<PenDto> pens =
        Collections.singletonList(PenDto.builder().id(penId).siteId(siteId).build());
    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(pens).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(SiteDocument.builder().barns(new ArrayList<>()).build())
                .build());
    when(pensRepository.findByPenId(any())).thenReturn(pen);
    when(pensRepository.saveAndFlush(any())).thenReturn(pen);

    PenGroupingDto result = pensService.update(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void getNetEnergyOfLactationDairy() throws CustomDEExceptions {
    List<Pens> pens = new ArrayList<>();
    List<Diets> diets = new ArrayList<>();
    diets.add(
        Diets.builder()
            .dietDocument(
                DietDocument.builder()
                    .id(UUID.randomUUID())
                    .formulateOptimization(
                        FormulateDietOptimization.builder()
                            .status(FormulateStatus.FEASIBLE)
                            .nutrients(
                                List.of(
                                    DietOptimizationNutrient.builder()
                                        .nutrientSpeciesId(
                                            Nutrients.NEL_DAIRY_KG.getNutrientValue())
                                        .result(2.0)
                                        .build()))
                            .build())
                    .build())
            .build());

    diets.add(
        Diets.builder()
            .dietDocument(
                DietDocument.builder()
                    .id(UUID.randomUUID())
                    .analyzeOptimization(
                        AnalyzeDietOptimization.builder()
                            .status(AnalyzeStatus.UNSAFE)
                            .nutrients(
                                List.of(
                                    DietOptimizationNutrient.builder()
                                        .nutrientSpeciesId(
                                            Nutrients.NEL_DAIRY_KG.getNutrientValue())
                                        .result(2.0)
                                        .build()))
                            .build())
                    .build())
            .build());

    pens.add(
        Pens.builder()
            .penDocument(
                PenDocument.builder()
                    .dietId(diets.get(0).getDietDocument().getId())
                    .animals(3)
                    .build())
            .build());

    pens.add(
        Pens.builder()
            .penDocument(
                PenDocument.builder()
                    .dietId(diets.get(1).getDietDocument().getId())
                    .animals(4)
                    .build())
            .build());

    when(dietRepository.findById(pens.get(0).getPenDocument().getDietId().toString()))
        .thenReturn(diets.get(0));
    when(dietRepository.findById(pens.get(1).getPenDocument().getDietId().toString()))
        .thenReturn(diets.get(1));

    Assertions.assertNotEquals(0.0, pensService.getNetEnergyOfLactationDairy(pens));
  }

  @Test
  void exceptionOnGetNetEnergyOfLactationDairy() {
    List<Pens> pens = new ArrayList<>();

    pens.add(new Pens());

    Assertions.assertThrows(
        CustomDEExceptions.class, () -> pensService.getNetEnergyOfLactationDairy(pens));
  }

  @Test
  void whenDietNotFoundThenSkipDiet() {
    List<Pens> pens = new ArrayList<>();

    pens.add(
        Pens.builder()
            .penDocument(PenDocument.builder().dietId(UUID.randomUUID()).animals(3).build())
            .build());

    when(dietRepository.findById(pens.get(0).getPenDocument().getDietId().toString()))
        .thenReturn(null);

    Assertions.assertDoesNotThrow(() -> pensService.getNetEnergyOfLactationDairy(pens));
  }

  @Test
  void whenDietIdIsNullThenSkipDiet() {
    List<Pens> pens = new ArrayList<>();

    pens.add(Pens.builder().penDocument(PenDocument.builder().build()).build());

    Assertions.assertDoesNotThrow(() -> pensService.getNetEnergyOfLactationDairy(pens));
  }

  @Test
  void whenFormulateDietIsInvalid() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> penDtos =
        Collections.singletonList(
            PenDto.builder()
                .dietId(UUID.randomUUID())
                .optimizationType(OptimizationType.FORMULATE)
                .build());

    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(penDtos).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .barns(
                            Collections.singletonList(Barn.builder().id(UUID.randomUUID()).build()))
                        .build())
                .build());

    when(dietRepository.findById(anyString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder()
                        .id(UUID.randomUUID())
                        .source(DietSource.MAX)
                        .isActive(true)
                        .isDeleted(false)
                        .formulateOptimization(
                            FormulateDietOptimization.builder()
                                .status(FormulateStatus.NOT_FORMULATED)
                                .nutrients(
                                    List.of(
                                        DietOptimizationNutrient.builder()
                                            .nutrientSpeciesId(
                                                Nutrients.NEL_DAIRY_KG.getNutrientValue())
                                            .result(2.0)
                                            .build()))
                                .build())
                        .build())
                .build());

    when(pensRepository.saveAndFlush(any()))
        .thenReturn(
            Pens.builder()
                .penDocument(PenDocument.builder().dietId(UUID.randomUUID()).build())
                .updatedDate(Date.from(Instant.now()))
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void whenAnalyzeDietIsInvalid() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> penDtos =
        Collections.singletonList(
            PenDto.builder()
                .dietId(UUID.randomUUID())
                .optimizationType(OptimizationType.ANALYZE)
                .build());

    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(penDtos).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .barns(
                            Collections.singletonList(Barn.builder().id(UUID.randomUUID()).build()))
                        .build())
                .build());
    when(dietRepository.findById(anyString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder()
                        .id(UUID.randomUUID())
                        .source(DietSource.MAX)
                        .isActive(true)
                        .isDeleted(false)
                        .analyzeOptimization(
                            AnalyzeDietOptimization.builder()
                                .status(AnalyzeStatus.NOT_ANALYZED)
                                .nutrients(
                                    List.of(
                                        DietOptimizationNutrient.builder()
                                            .nutrientSpeciesId(
                                                Nutrients.NEL_DAIRY_KG.getNutrientValue())
                                            .result(2.0)
                                            .build()))
                                .build())
                        .build())
                .build());
    when(pensRepository.saveAndFlush(any()))
        .thenReturn(
            Pens.builder()
                .penDocument(PenDocument.builder().dietId(UUID.randomUUID()).build())
                .updatedDate(Date.from(Instant.now()))
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void whenDietIsNotActive() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> penDtos =
        Collections.singletonList(
            PenDto.builder()
                .dietId(UUID.randomUUID())
                .optimizationType(OptimizationType.ANALYZE)
                .build());

    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(penDtos).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .barns(
                            Collections.singletonList(Barn.builder().id(UUID.randomUUID()).build()))
                        .build())
                .build());

    when(dietRepository.findById(anyString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder()
                        .id(UUID.randomUUID())
                        .source(DietSource.MAX)
                        .isActive(false)
                        .isDeleted(false)
                        .build())
                .build());

    when(pensRepository.saveAndFlush(any()))
        .thenReturn(
            Pens.builder()
                .penDocument(PenDocument.builder().dietId(UUID.randomUUID()).build())
                .updatedDate(Date.from(Instant.now()))
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void whenDietIsDeleted() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> penDtos =
        Collections.singletonList(
            PenDto.builder()
                .dietId(UUID.randomUUID())
                .optimizationType(OptimizationType.ANALYZE)
                .build());

    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(penDtos).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .barns(
                            Collections.singletonList(Barn.builder().id(UUID.randomUUID()).build()))
                        .build())
                .build());

    when(dietRepository.findById(anyString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder()
                        .id(UUID.randomUUID())
                        .source(DietSource.MAX)
                        .isActive(true)
                        .isDeleted(true)
                        .build())
                .build());

    when(pensRepository.saveAndFlush(any()))
        .thenReturn(
            Pens.builder()
                .penDocument(PenDocument.builder().dietId(UUID.randomUUID()).build())
                .updatedDate(Date.from(Instant.now()))
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void whenDietIsNotFromMAX() {
    UUID siteId = UUID.randomUUID();
    List<PenDto> penDtos =
        Collections.singletonList(
            PenDto.builder()
                .dietId(UUID.randomUUID())
                .optimizationType(OptimizationType.ANALYZE)
                .build());

    PenGroupingDto penGroupingDto = PenGroupingDto.builder().siteId(siteId).pens(penDtos).build();
    when(sitesRepository.findBySiteId(any()))
        .thenReturn(
            Sites.builder()
                .siteDocument(
                    SiteDocument.builder()
                        .barns(
                            Collections.singletonList(Barn.builder().id(UUID.randomUUID()).build()))
                        .build())
                .build());

    when(dietRepository.findById(anyString()))
        .thenReturn(
            Diets.builder()
                .dietDocument(
                    DietDocument.builder().id(UUID.randomUUID()).source(DietSource.DDW).build())
                .build());

    when(pensRepository.saveAndFlush(any()))
        .thenReturn(
            Pens.builder()
                .penDocument(PenDocument.builder().dietId(UUID.randomUUID()).build())
                .updatedDate(Date.from(Instant.now()))
                .build());

    PenGroupingDto result = pensService.save(penGroupingDto);
    assertNotNull(result);
    assertEquals(ResponseStatus.SUCCESS, result.getPens().get(0).getStatus());
  }

  @Test
  void whenDuplicatePenIds() {
    Visits visits = new Visits();
    int i = 1;
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setSiteId(UUID.fromString("0be369a7-2e03-4aad-8efd-f7ab84669d7f"));
    visitDocument.setId(UUID.fromString("22292896-badb-426e-9234-ae5c81d64937"));
    visits.setVisitDocument(visitDocument);
    List<Visits> visitsList = new ArrayList<>();
    visitsList.add(visits);
    List<DuplicatePens> list = new ArrayList<>(List.of());
    when(duplicatePens.getPenIds()).thenReturn("penId");
    when(duplicatePens.getPenCount()).thenReturn("3");
    when(duplicatePens.getPenName()).thenReturn("penName");
    when(duplicatePens.getSiteId()).thenReturn("siteId");
    when(duplicatePens.getSource()).thenReturn("source");
    Mockito.when(pensRepository.findDuplicatePens(i)).thenReturn(List.of(duplicatePens));
    when(visitsRepository.getVisitsDocBySiteId(any())).thenReturn(visitsList);
    List<DuplicatePenIdDTO> duplicatePenIdDTO = pensService.getAllPensDuplicate(1, 1);
    assertNotNull(duplicatePenIdDTO);
  }

  @Test
  void whenDuplicatePenIdsSetflag() {
    Visits visits = new Visits();
    int i = 1;
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setSiteId(UUID.fromString("0be369a7-2e03-4aad-8efd-f7ab84669d7f"));
    visitDocument.setId(UUID.fromString("22292896-badb-426e-9234-ae5c81d64937"));
    visitDocument.setVisitName("penId2");
    visits.setVisitDocument(visitDocument);
    List<Visits> visitsList = new ArrayList<>();
    visitsList.add(visits);
    List<DuplicatePens> list = new ArrayList<>(List.of());
    when(duplicatePens.getPenIds()).thenReturn("penId1,penId2");
    when(duplicatePens.getPenCount()).thenReturn("34");
    when(duplicatePens.getPenName()).thenReturn("penName");
    when(duplicatePens.getSiteId()).thenReturn("siteId");
    when(duplicatePens.getSource()).thenReturn("source");
    Mockito.when(pensRepository.findDuplicatePens(i)).thenReturn(List.of(duplicatePens));
    when(visitsRepository.getVisitsDocBySiteId(any())).thenReturn(visitsList);
    List<DuplicatePenIdDTO> duplicatePenIdDTO = pensService.getAllPensDuplicate(1, 1);
    assertNotNull(duplicatePenIdDTO);
  }
}
