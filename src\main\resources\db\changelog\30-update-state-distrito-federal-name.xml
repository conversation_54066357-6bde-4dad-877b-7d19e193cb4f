<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="030" author="Taha">
    <sql>
   UPDATE states
SET state_document = jsonb_set(
    state_document,
    '{StateName}',
    '"Brasília"',
    true
)
WHERE state_document->>'StateCode' = 'DF' AND state_document->>'StateName' = 'Distrito Federal';
    </sql>
  </changeSet>

</databaseChangeLog>
