/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import static org.junit.jupiter.api.Assertions.*;

import java.time.Instant;
import org.junit.jupiter.api.Test;

class TimeConverterTest {

  @Test
  void whenTimeStringIsOkCorrectResultIsReturned() {
    Instant result = TimeConverter.fromSfTime("2022-06-08T12:47:26.000+0000");
    assertEquals(Instant.parse("2022-06-08T12:47:26Z"), result);
  }

  @Test
  void whenTimeStringIsWrongExceptionIsReturned() {
    assertThrows(
        IllegalArgumentException.class, () -> TimeConverter.fromSfTime("2022-06-08T12:47"));
  }
}
