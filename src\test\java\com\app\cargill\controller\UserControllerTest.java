/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.MobileDeviceType;
import com.app.cargill.dto.BulkUserInsertDto;
import com.app.cargill.dto.IdTokenDto;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.UserDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.User;
import com.app.cargill.service.IUserService;
import java.io.UnsupportedEncodingException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class UserControllerTest {

  @Mock private IUserService userService;

  @InjectMocks private UserController controller;

  @Test
  void getAllUsers() {
    when(userService.getAllUsers()).thenReturn(generateUsers());
    ResponseEntity<ResponseEntityDto<List<UserDto>>> result = controller.getAllUsers();
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  @Test
  void getUserById() {
    when(userService.getUserById(anyLong())).thenReturn(UserDto.builder().build());
    ResponseEntity<ResponseEntityDto<UserDto>> result = controller.getUserById(1L);
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void whenIdIsNullThrowException() {
    Assertions.assertThrows(NotFoundDEException.class, () -> controller.getUserById(null));
  }

  @Test
  void getUserInfoByEmail() {
    when(userService.getUserByPrincipal(any())).thenReturn(UserDto.builder().build());
    ResponseEntity<ResponseEntityDto<UserDto>> result = controller.getUserInfoByEmail("x@x");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  private List<UserDto> generateUsers() {
    return List.of(UserDto.builder().build(), UserDto.builder().build());
  }

  @Test
  void getUserInfoByIdToken() throws UnsupportedEncodingException {
    when(userService.getUserByIdToken(any())).thenReturn(UserDto.builder().build());
    ResponseEntity<ResponseEntityDto<UserDto>> result =
        controller.getUserInfoByIdToken(
            "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D%3D");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void fetchUserInfoByIdToken() throws UnsupportedEncodingException {
    when(userService.fetchAndUpdateUserInfo(any())).thenReturn(UserDto.builder().build());
    IdTokenDto idTokenDto =
        IdTokenDto.builder()
            .idToken(
                "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D%3D")
            .applicationVersion("Test")
            .deviceType(MobileDeviceType.ANDROID)
            .build();
    ResponseEntity<ResponseEntityDto<UserDto>> result =
        controller.fetchAndUpdateUserInfo(idTokenDto);
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void whenBulkInsertUsersReturnValidResult() {
    when(userService.bulkInsertUsers(any()))
        .thenReturn(
            BulkUserInsertDto.builder()
                .countryId(Business.US)
                .salesforceCountryId(2)
                .userNames(Arrays.asList("test"))
                .build());
    ResponseEntity<ResponseEntityDto<BulkUserInsertDto>> result =
        controller.bulkInsertUsers(BulkUserInsertDto.builder().build());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void updateName() throws UnsupportedEncodingException {

    ResponseEntity<ResponseEntityDto<List<User>>> result = controller.updateName("code");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }
}
