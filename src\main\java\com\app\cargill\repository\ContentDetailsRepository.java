/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.ContentDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ContentDetailsRepository extends JpaRepository<ContentDetails, Long> {

  @Query(
      value =
          "Select cd.* FROM content_details cd where cd.content_details_document ->> 'id' = :id",
      nativeQuery = true)
  ContentDetails findByDocumentId(@Param("id") String id);

  @Query(value = "Select cd.* FROM content_details cd where cd.local_id = :id", nativeQuery = true)
  ContentDetails findByLocalId(@Param("id") String localId);
}
