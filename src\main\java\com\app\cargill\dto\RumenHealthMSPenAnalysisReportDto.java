/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RumenHealthMSPenAnalysisReportDto extends BaseDto {

  @Builder.Default private List<RumenHealthMSPenAnalysisCategoryDto> categories = new ArrayList<>();
  private String visitDate;
  private String visitName;
  private String fileName;
  private String analysisType;
  private String toolName;
  private Double average;
  private String penName;
  private Double standardDeviation;
  private String averageLabel;
  private String standardDeviationLabel;
}
