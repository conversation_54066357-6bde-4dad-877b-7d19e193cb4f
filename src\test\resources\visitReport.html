<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
    <meta http-equiv="Content-type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="date=no">
    <meta name="format-detection" content="address=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="x-apple-disable-message-reformatting">
    <link href="./css/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <title>Cargill</title>
    <style>
	body {
  counter-set: headings 0;
}
#pageFooter:after {
  content: "Page " counter(headings, decimal);
  top: 100%;
  white-space: nowrap;
  z-index: 20;
  -moz-border-radius: 5px;
  -moz-box-shadow: 0px 0px 4px #222;
  background-image: -moz-linear-gradient(top, #eeeeee, #cccccc);
  float: right;
}
.counterinc::after {
  counter-increment: headings;
}
</style>
</head>
<body>
<div class="container">
    <div class="template-header">
        <figure>
            <img src="./images/cargill-logo.svg">
        </figure>
    </div>

    <div class="card mb-5">
        <div class="card-header pt-5">
            <h4 class="mb-2">Hidden Agro 12/12/21</h4>

            <div class="row">
                <div class="content-set">
                    <label>Tool:</label>
                    <h4>Cud Chewing</h4>
                </div>
                <div class="content-set mx-2">
                    <label>Type:</label>
                    <h4>Herd</h4>
                </div>
                <div class="content-set">
                    <label>Visit Date:</label>
                    <h4>12/01/21</h4>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="legend-wrap mb-2">
                    <p class="blue-solid">Cud Chewing %</p>
                </div>
                <div class="legend-wrap mb-2">
                    <p class="blue-light-solid">Goal Cud Chewing % </p>
                </div>
            </div>

            <canvas id="barchart"></canvas>
        </div>

    </div>
    <script>

const weight1 = [60.0, 60.2, 59.1, 61.4, 59.9, 60.2, 59.8, 58.6, 59.6, 59.2];
const datasettwo1 = [70.0, 80.2, 75.1, 68.4, 65.9, 78.5, 44.8, 20.6, 30.6, 68.2];
const labels1 = ["One", "Two", "Three", "Four", "Five", "Six", "Seven",];

const ctx = document.getElementById("barchart").getContext("2d");
ctx.canvas.height = 100;
const options1 = {
  type: "bar",

  data: {
    labels: labels1,
    datasets: [
		{
			data: datasettwo1,
			backgroundColor: '#307698',
			barThickness: 40,
			grouped:true,
		},
      {
		data: weight1,
        backgroundColor: '#61ADDE',
		barThickness: 40,
		grouped:true,
      }
    ]
  },
  options: {
	plugins: {
		legend: {
			display: false,
		},
		tooltip: {
			callbacks: {
            title : () => null // or function () { return null; }
         },
			yAlign: 'top',
			backgroundColor: "#fff",
			borderColor: "rgba(0, 0, 0, 0.25)",
			borderWidth: 1,
			displayColors: false,
			bodyColor: "#307698",
			bodyAlign: "center",
        },

  	},

     layout: {
		padding: {
			top:20,
			right: 15
		}
	},

    responsive: true,

    scales: {
		y: {
        	// beginAtZero: true,
			title: {
        	display: true,
			color: '#6C7782',
        	text: 'Cud Chewing %',
				padding: {
					bottom: 15,
				}
      		},

			grid: {
            	display: false,
          	},
      	},

		x: {
			title: {
        	display: true,
			color: '#6C7782',
        	text: 'Lactation Stages',
				padding: {
					top: 15,
				}
      		},
			grid: {
				display: false,
			},
		}
    },

	animation: {
		duration: 0,
			onComplete: function() {
			var chart = this;
			var ctx = chart.ctx;

			ctx.font = Chart.helpers.fontString(Chart.defaults.font.size, Chart.defaults.font.style, Chart.defaults.font.family);
			ctx.textAlign = 'center';
			ctx.textBaseline = 'bottom';

			this.data.datasets.forEach(function(dataset, i) {
				var meta = chart.getDatasetMeta(i);
				meta.data.forEach(function(bar, index) {
				var data = dataset.data[index];
				ctx.fillText(data, bar.x, bar.y - 5);
				});
			});
			}
		},
	}
};



</script>
</div>
<div style="break-after:page">
    <div class = "counterinc" id="pageFooter"> </div>
</div>
<div class="container">
    <div class="template-header">
        <figure>
            <img src="./images/cargill-logo.svg">
        </figure>
    </div>

    <div class="card mb-5">
        <div class="card-header pt-5">
            <h4 class="mb-2">Hidden Agro 12/12/21</h4>

            <div class="row">
                <div class="content-set">
                    <label>Tool:</label>
                    <h4>Cud Chewing</h4>
                </div>
                <div class="content-set mx-2">
                    <label>Type:</label>
                    <h4>Herd</h4>
                </div>
                <div class="content-set">
                    <label>Visit Date:</label>
                    <h4>12/01/21</h4>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="legend-wrap mb-2">
                    <p class="blue-solid">No. of Chews per Regurigation</p>
                </div>
                <div class="legend-wrap mb-2">
                    <p class="red-strip">Goal Chews</p>
                </div>
            </div>

            <canvas id="linechart"></canvas>
        </div>

    </div>
</div>

<div style="break-after:page">
    <div class = "counterinc" id="pageFooter"> </div>
</div>
<script>
	const colors = {
		purple: {
			default: "#307698",
			half: "#30769878",
			quarter: "#3076983b",
			zero: "#3076981c"
	},
	indigo: {
		default: "#307698",
		quarter: "#30769878"
	}
};

const weight = [60.0, 60.2, 59.1, 61.4, 59.9, 60.2, 59.8, 58.6, 59.6, 59.2];
const datasettwo = [70.0, 80.2, 75.1, 68.4, 65.9, 78.5, 44.8, 20.6, 30.6, 68.2];

const labels = [
  "One",
  "Two",
  "Three",
  "Four",
  "Five",
  "Six",
  "Seven",

];

const ctx1 = document.getElementById("linechart").getContext("2d");
ctx1.canvas.height = 100;

gradient = ctx1.createLinearGradient(0, 25, 0, 300);
gradient.addColorStop(0, colors.purple.half);
gradient.addColorStop(0.35, colors.purple.quarter);
gradient.addColorStop(1, colors.purple.zero);

const options = {
  type: "line",

  data: {
    labels: labels,
    datasets: [
		{
			borderColor: '#8D0909',
			pointBackgroundColor: '#8D0909',
			pointBorderColor: '#fff',
        	data: datasettwo,
        	lineTension: 0.2,
        	borderWidth: 1,
        	pointRadius: 6,
			borderDash: [5,4],
		},
      {
        fill: true,
        backgroundColor: gradient,
		borderColor: '#307698',
		pointBackgroundColor: '#307698',
		pointBorderColor: '#fff',
        data: weight,
        lineTension: 0.2,
        borderWidth: 1,
        pointRadius: 6,
      }
    ]
  },
  options: {
	plugins: {
		legend: {
			display: false,
		},
		tooltip: {
			callbacks: {
            title : () => null // or function () { return null; }
         },
			yAlign: 'bottom',
			backgroundColor: "#fff",
			borderColor: "rgba(0, 0, 0, 0.25)",
			borderWidth: 1,
			displayColors: false,
			bodyColor: "#307698",
			bodyAlign: "center",
        },
  	},

  layout: {
		padding: {
			top:20,
			right: 30
		}
	},

    responsive: true,

    scales: {
		y: {
        	// beginAtZero: true,
			title: {
        	display: true,
			color: '#6C7782',
        	text: 'No. of Chews',
				padding: {
					bottom: 15,
					top:20,
			right: 30
				}
      		},

			grid: {
            	display: false,
          	},
      	},

		x: {
			title: {
        	display: true,
			color: '#6C7782',
        	text: 'Lactation Stages',
				padding: {
					top:20,
					right: 30

				}
      		},
			grid: {
				display: false,
			},
		}
    }
  }
};

window.onload = function () {
  window.myLine = new Chart(ctx1, options);
   window.myLine = new Chart(ctx, options1);
};

</script>

</body>
</html>