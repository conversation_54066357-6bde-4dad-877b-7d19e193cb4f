/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.PenTimeBudgetTool;
import com.app.cargill.document.PenTimeBudgetToolItem;
import java.util.Objects;

public class PenTimeBudgetCalculation {
  public static final double WALKING_TO_FIND_STALL_VALUE_1 = 1.8;
  public static final double WALKING_TO_FIND_STALL_VALUE_2 = 2;
  public static final double WALKING_TO_FIND_STALL_VALUE_3 = 2.3;
  public static final double WALKING_TO_FIND_STALL_VALUE_4 = 2.5;
  public static final double WALKING_TO_FIND_STALL_RANGE_1_MAX = 95;
  public static final double WALKING_TO_FIND_STALL_RANGE_2_MIN = 95;
  public static final double WALKING_TO_FIND_STALL_RANGE_2_MAX = 115;
  public static final double WALKING_TO_FIND_STALL_RANGE_3_MIN = 115;
  public static final double WALKING_TO_FIND_STALL_RANGE_3_MAX = 130;
  public static final double WALKING_TO_FIND_STALL_RANGE_4_MIN = 130;

  public PenTimeBudgetTool calculateFields(PenTimeBudgetTool tool) {
    if (tool == null) return null;

    if (tool.getPens() != null) {
      for (PenTimeBudgetToolItem pen : tool.getPens()) {
        pen.overcrowding = roundAvoid(Objects.requireNonNullElse(overcrowding(pen), 0.0), 0);
        pen.timePerMilking = roundAvoid(Objects.requireNonNullElse(timePerMilking(pen), 0.0), 2);
        pen.totalTimeMilking =
            roundAvoid(Objects.requireNonNullElse(totalTimeMilking(pen), 0.0), 2);
        pen.walkingTimeToStall =
            roundAvoid(Objects.requireNonNullElse(walkingTimeToStall(pen), 0.0), 2);
        pen.restingRemaining =
            roundAvoid(Objects.requireNonNullElse(restingRemaining(pen), 0.0), 2);
        pen.restingDifference = roundAvoid(restingDifference(pen), 2);
        pen.potentialMilkGainLoss =
            roundAvoid(Objects.requireNonNullElse(potentialMilkGainLoss(pen), 0.0), 2);
        pen.parlorTurnsPerHour = roundAvoid(parlorTurnsPerHour(pen), 2);
        pen.cowsMilkedPerHour =
            roundAvoid(Objects.requireNonNullElse(cowsMilkedPerHour(pen), 0.0), 2);
        pen.walkingToFindStall = roundAvoid(walkingToFindStall(pen), 2);
        pen.totalNonRestingTime = roundAvoid(totalNonRestingTime(pen), 2);
        pen.timeRemainingForResting = roundAvoid(timeRemainingForResting(pen), 2);
        pen.timeRequiredForResting = pen.getRestingRequirement();
        pen.energyChange = roundAvoid(energyChange(pen), 2);
        pen.bodyWeightChange = roundAvoid(bodyWeightChange(pen), 2);
        pen.bodyConditionScoreChange = roundAvoid(bodyConditionScoreChange(pen), 2);
      }
    }

    return tool;
  }

  private Double overcrowding(PenTimeBudgetToolItem pen) {

    Double animal = pen.getAnimals();
    Integer stallsInPen = pen.getStallsInPen();

    if (stallsInPen == null) return null;
    if (stallsInPen == 0) return 0.0;

    return (Objects.requireNonNullElse(animal, 0.0) / stallsInPen) * 100;
  }

  private Double timePerMilking(PenTimeBudgetToolItem pen) {
    return Objects.requireNonNullElse(pen.getWalkingTimeToParlor(), 0.0)
        + Objects.requireNonNullElse(pen.getTimeInParlor(), 0.0)
        + Objects.requireNonNullElse(pen.getWalkingTimeFromParlor(), 0.0);
  }

  private Double totalTimeMilking(PenTimeBudgetToolItem pen) {
    if (pen.milkingFrequency == null || pen.milkingFrequency == 0) return null;
    return pen.getTimePerMilking() * pen.milkingFrequency;
  }

  private Double walkingToFindStall(PenTimeBudgetToolItem pen) {
    Double walkingTimeToStall = null;

    if (pen.getOvercrowding() < WALKING_TO_FIND_STALL_RANGE_1_MAX)
      walkingTimeToStall = WALKING_TO_FIND_STALL_VALUE_1;
    else if (pen.getOvercrowding() < WALKING_TO_FIND_STALL_RANGE_2_MAX
        && pen.getOvercrowding() >= WALKING_TO_FIND_STALL_RANGE_2_MIN)
      walkingTimeToStall = WALKING_TO_FIND_STALL_VALUE_2;
    else if (pen.getOvercrowding() < WALKING_TO_FIND_STALL_RANGE_3_MAX
        && pen.getOvercrowding() >= WALKING_TO_FIND_STALL_RANGE_3_MIN)
      walkingTimeToStall = WALKING_TO_FIND_STALL_VALUE_3;
    else if (pen.getOvercrowding() >= WALKING_TO_FIND_STALL_RANGE_4_MIN)
      walkingTimeToStall = WALKING_TO_FIND_STALL_VALUE_4;
    return walkingTimeToStall;
  }

  private Double restingRemaining(PenTimeBudgetToolItem pen) {

    return 24
        - (pen.getWalkingTimeToStall()
            + pen.getTotalTimeMilking()
            + Objects.requireNonNullElse(pen.getEatingTime(), 0.0)
            + Objects.requireNonNullElse(pen.getDrinkingGroomingTime(), 0.0)
            + Objects.requireNonNullElse(pen.getOtherNonRestTime(), 0.0)
            + Objects.requireNonNullElse(pen.getTimeInLockUp(), 0.0));
  }

  private Double restingDifference(PenTimeBudgetToolItem pen) {
    if (pen.getRestingRequirement() == null) return 0.0;

    return pen.getRestingRemaining() - pen.getRestingRequirement();
  }

  private Double potentialMilkGainLoss(PenTimeBudgetToolItem pen) {

    if (pen.getRestingRequirement() == null
        || pen.getRestingRemaining() == 0.0
        || pen.getRestingRequirement() == 0.0) return null;

    return pen.getRestingDifference() * 1.5873;
  }

  private Double parlorTurnsPerHour(PenTimeBudgetToolItem pen) {
    if (pen.getStallsInParlor() == null
        || pen.getTimePerMilking() == 0.0
        || pen.getStallsInParlor() == 0.0) return 0.0;

    double resultValue =
        Objects.requireNonNullElse(pen.getAnimals(), 0.0)
            / pen.getTimePerMilking()
            / pen.getStallsInParlor();
    return roundAvoid(resultValue, 2);
  }

  private Double cowsMilkedPerHour(PenTimeBudgetToolItem pen) {
    Double animal = pen.getAnimals();
    if (animal != null && animal > 0 && pen.getTimePerMilking() > 0)
      return animal / pen.getTimePerMilking();

    return null;
  }

  private Double walkingTimeToStall(PenTimeBudgetToolItem pen) {

    Double overcrowding = pen.overcrowding;
    if (overcrowding == null) return null;
    if (overcrowding < 95) return 1.8;
    else if (overcrowding < 115) return 2.0;
    else if (overcrowding < 130) return 2.3;
    else if (overcrowding >= 130) return 2.5;
    else return null;
  }

  private Double totalNonRestingTime(PenTimeBudgetToolItem pen) {

    return Objects.requireNonNullElse(pen.getWalkingTimeToStall(), 0.0)
        + Objects.requireNonNullElse(pen.getTotalTimeMilking(), 0.0)
        + Objects.requireNonNullElse(pen.getEatingTime(), 0.0)
        + Objects.requireNonNullElse(pen.getDrinkingGroomingTime(), 0.0)
        + Objects.requireNonNullElse(pen.getOtherNonRestTime(), 0.0)
        + Objects.requireNonNullElse(pen.getTimeInLockUp(), 0.0);
  }

  private Double timeRemainingForResting(PenTimeBudgetToolItem pen) {

    return 24
        - (Objects.requireNonNullElse(pen.getWalkingTimeToStall(), 0.0)
            + Objects.requireNonNullElse(pen.getTotalTimeMilking(), 0.0)
            + Objects.requireNonNullElse(pen.getEatingTime(), 0.0)
            + Objects.requireNonNullElse(pen.getDrinkingGroomingTime(), 0.0)
            + Objects.requireNonNullElse(pen.getOtherNonRestTime(), 0.0)
            + Objects.requireNonNullElse(pen.getTimeInLockUp(), 0.0));
  }

  private Double energyChange(PenTimeBudgetToolItem pen) {
    return pen.getPotentialMilkGainLoss() * 0.7276;
  }

  private Double bodyWeightChange(PenTimeBudgetToolItem pen) {
    return pen.getEnergyChange() / 4.9171;
  }

  private Double bodyConditionScoreChange(PenTimeBudgetToolItem pen) {
    return pen.getBodyWeightChange() * 1.8375;
  }
}
