/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class UserReferenceRecord implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("attributes")
  private RecordAttributes attributes;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("Username")
  private String username;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("ProfileId")
  private String profileId;

  @JsonProperty("Profile")
  private PersonRecord profile;

  public String getEmail() {
    if (username != null && username.contains("integration")) {
      return username;
    }
    return email;
  }
}
