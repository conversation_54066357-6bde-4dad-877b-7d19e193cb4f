/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Tool;
import com.app.cargill.constants.ToolGroup;
import com.app.cargill.document.CountryToolDocument;
import com.app.cargill.dto.CountryToolDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.CountryTools;
import com.app.cargill.repository.CountryToolsRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class CountryToolServiceImplTest {

  @Mock CountryToolsRepository countryToolsRepository;
  @Mock IUserService userServiceImpl;
  @Mock UserRepository usersRepository;

  @InjectMocks CountryToolServiceImpl countryToolServiceImpl;

  @Test
  void whenAllDataIsProvidedExpectedResultIsReturned() {
    Page<CountryTools> countryTools = new PageImpl<>(List.of(loadCountryTools()));
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("7");
    when(countryToolsRepository.findByCountryId(any(), any(), any())).thenReturn(countryTools);
    Page<CountryToolDto> result =
        countryToolServiceImpl.getAllCountryToolsByCurrentLoggedInUserPaginated(
            0, 10, "id", "desc", Instant.now());
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenCountryIdIsNullThrowNotFoundException() {
    when(usersRepository.findCountryIdByUserName(any())).thenReturn(null);
    Assertions.assertThrows(
        NotFoundDEException.class,
        () ->
            countryToolServiceImpl.getAllCountryToolsByCurrentLoggedInUserPaginated(
                0, 10, "id", "desc", null));
  }

  @Test
  void whenAllDataIsProvidedButResultIsEmpty() {
    Page<CountryTools> countryTools = new PageImpl<>(new ArrayList<>());
    when(usersRepository.findCountryIdByUserName(any())).thenReturn("7");
    when(countryToolsRepository.findByCountryId(any(), any(), any())).thenReturn(countryTools);
    Page<CountryToolDto> result =
        countryToolServiceImpl.getAllCountryToolsByCurrentLoggedInUserPaginated(
            0, 10, "id", "desc", Instant.now());
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void whenCorrectDtoIsSentDataIsSavedSuccesfully() {
    when(countryToolsRepository.saveAll(any())).thenReturn(List.of(loadCountryTools()));
    List<CountryToolDto> result = countryToolServiceImpl.save(List.of(loadCountryToolDto()));
    assertNotNull(result);
    assertNotNull(result.get(0).getCreatedDate());
    assertNotNull(result.get(0).getUpdatedDate());
  }

  @Test
  void whenIdIsProvidedResultIsDeletedSuccessfully() {
    when(countryToolsRepository.findById(any(String.class))).thenReturn(loadCountryTools());
    when(countryToolsRepository.save(any())).thenReturn(loadCountryTools());
    CountryToolDto result = countryToolServiceImpl.deleteById(UUID.randomUUID());
    assertNotNull(result);
    assertEquals(true, result.isDeleted());
  }

  @Test
  void whenCountryIdIsProvidedResultsAreDeletedSuccessfully() {
    when(countryToolsRepository.findByCountryId(any(String.class)))
        .thenReturn(List.of(loadCountryTools()));
    when(countryToolsRepository.saveAll(any())).thenReturn(List.of(loadCountryTools()));
    List<CountryToolDto> result = countryToolServiceImpl.deleteByCountryId(Business.UK);
    assertNotNull(result);
    assertEquals(1, result.size());
  }

  private CountryToolDto loadCountryToolDto() {
    return CountryToolDto.builder()
        .countryId(Business.Canada)
        .toolGroupId(ToolGroup.Health)
        .toolId(Tool.BodyCondition)
        .deleted(true)
        .build();
  }

  private CountryTools loadCountryTools() {
    CountryToolDocument countryToolDocument =
        CountryToolDocument.builder()
            .countryId(Business.US)
            .id(UUID.randomUUID())
            .toolGroupId(ToolGroup.CalfandHeifer)
            .toolId(Tool.LocomotionScore)
            .isDeleted(true)
            .build();

    return CountryTools.builder()
        .countryToolDocument(countryToolDocument)
        .updatedDate(Date.from(Instant.now()))
        .createdDate(Date.from(Instant.now()))
        .build();
  }
}
