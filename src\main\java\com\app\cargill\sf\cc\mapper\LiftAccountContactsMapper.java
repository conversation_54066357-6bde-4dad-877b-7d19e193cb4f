/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import com.app.cargill.constants.PreferredMethod;
import com.app.cargill.constants.PrimaryLang;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.sf.cc.model.simple.ContactSalesforce;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class LiftAccountContactsMapper {

  private LiftAccountContactsMapper() {}

  public static List<Contact> transform(List<ContactSalesforce> sfContacts) {

    if (sfContacts == null) {
      return new ArrayList<>();
    }
    return sfContacts.stream().map(LiftAccountContactsMapper::transform).toList();
  }

  public static Contact transform(ContactSalesforce input) {
    return transform(input, new Contact());
  }

  public static Contact transform(ContactSalesforce input, Contact contact) {
    contact.setAccountId(UUID.fromString(input.getAccount().getExternalId()));
    // for unknown reason there are records (on dev) without an id.
    // It is not critical and should be handled for new records as well
    contact.setContactId(
        input.getContactExternalId() != null
            ? UUID.fromString(input.getContactExternalId())
            : UUID.randomUUID());
    contact.setGoldenRecordAcountId(input.getAccount().getId());
    contact.setSFDCContactId(input.getId());
    contact.setFirstName(input.getFirstName());
    contact.setLastName(input.getLastName());
    contact.setMailingAddress(
        input.getMailingAddress() != null
            ? LiftAddressMapper.transform(input.getMailingAddress())
            : null);
    contact.setPhoneNumber(input.getPhone());
    contact.setEmailAddress(input.getEmail());
    contact.setLastUpdateDateTime(
        DateEpoch.builder()
            .date(
                Instant.ofEpochMilli(
                    org.joda.time.Instant.parse(input.getLastModifiedDate()).getMillis()))
            .build());
    contact.setNeedsSync(false);
    contact.setName(input.getName());
    contact.setOtherAddress(
        input.getOtherAddress() != null
            ? LiftAddressMapper.transform(input.getOtherAddress())
            : null);
    contact.setPrimaryLangId(
        input.getPrimaryLanguage() != null
            ? PrimaryLang.valueOf(
                input.getPrimaryLanguage().replace("(", "").replace(")", "").replaceAll("\\s+", ""))
            : null);
    contact.setPreferredMethodId(
        input.getPreferredContactMethod() != null
            ? PreferredMethod.valueOf(input.getPreferredContactMethod())
            : null);
    contact.setSalutation(input.getSalutation());
    contact.setDeleted(false);
    contact.setCreateTimeUtc(
        Instant.ofEpochMilli(org.joda.time.Instant.parse(input.getCreatedDate()).getMillis()));
    contact.setLastModifiedTimeUtc(
        DateEpoch.builder()
            .date(
                Instant.ofEpochMilli(
                    org.joda.time.Instant.parse(input.getLastModifiedDate()).getMillis()))
            .build());
    return contact;
  }
}
