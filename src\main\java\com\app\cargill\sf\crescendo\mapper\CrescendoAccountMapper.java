/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.mapper;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.UserRole;
import com.app.cargill.model.Accounts;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import com.app.cargill.sf.crescendo.model.AccountTypeCrescendo;
import com.app.cargill.sf.crescendo.model.ApplicationMappingCrescendo;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import com.app.cargill.sf.crescendo.model.CurrencyCrescendo;
import com.app.cargill.sf.crescendo.model.NineBoxStepTwoIDCrescendo;
import com.app.cargill.sf.crescendo.model.SegmentStepOneIdCrescendo;
import com.app.cargill.sf.crescendo.model.SubTypeIdCrescendo;
import com.app.cargill.sf.crescendo.model.UserRoleCrescendo;
import com.app.cargill.utils.BusinessToBusinessUnitMapper;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

@Slf4j
public class CrescendoAccountMapper {

  private CrescendoAccountMapper() {}

  public static Accounts crescendoToModel(AccountCrescendo accountCrescendo) {

    try {
      return new Accounts(crescendoToDocument(accountCrescendo));
    } catch (Exception e) {
      log.error("Cannot transform AccountCrescendo to Accounts", e);
      throw new IllegalArgumentException("Account transformation error");
    }
  }

  public static AccountCrescendo modelToCrescendo(Accounts account) {
    AccountCrescendo accountCrescendo = documentToCrescendo(account.getAccountDocument());
    accountCrescendo.setCreateTimeUtc(account.getCreatedDate().toInstant());
    accountCrescendo.setLastModifiedTimeUtc(account.getUpdatedDate().toInstant());
    accountCrescendo.setLastSyncTimeUtc(account.getUpdatedDate().toInstant());
    return accountCrescendo;
  }

  public static Accounts modelUpdate(Accounts existingAccount, Accounts accountCrescendo) {
    AccountDocument crescendoDocument = accountCrescendo.getAccountDocument();
    AccountDocument existingDocument = existingAccount.getAccountDocument();
    existingDocument.setId(crescendoDocument.getId());
    existingDocument.setDataSource(crescendoDocument.getDataSource());
    existingDocument.setGoldenRecordId(crescendoDocument.getGoldenRecordId());
    existingDocument.setAccountName(crescendoDocument.getAccountName());
    existingDocument.setLegalName(crescendoDocument.getLegalName());
    existingDocument.setPhysicalAddress(crescendoDocument.getPhysicalAddress());
    existingDocument.setAccountType(crescendoDocument.getAccountType());
    existingDocument.setSegmentStepOneId(crescendoDocument.getSegmentStepOneId());
    existingDocument.setNineBoxStepTwoID(crescendoDocument.getNineBoxStepTwoID());
    existingDocument.setSourceSystem(crescendoDocument.getSourceSystem());
    existingDocument.setSubTypeID(crescendoDocument.getSubTypeID());
    existingDocument.setBusinessID(crescendoDocument.getBusinessID());
    existingDocument.setParentAccountID(crescendoDocument.getParentAccountID());
    existingDocument.setExternalParentAccountID(crescendoDocument.getExternalParentAccountID());
    existingDocument.setOwnerId(crescendoDocument.getOwnerId());
    existingDocument.setOwnerProfileNameandId(crescendoDocument.getOwnerProfileNameandId());
    existingDocument.setAccountValidated(crescendoDocument.getAccountValidated());
    existingDocument.setActive(crescendoDocument.getActive());
    existingDocument.setCompanyEmail(crescendoDocument.getCompanyEmail());
    existingDocument.setPhone(crescendoDocument.getPhone());
    existingDocument.setAccountCurrency(crescendoDocument.getAccountCurrency());
    existingDocument.setAccountNumber(crescendoDocument.getCustomerCode());
    existingDocument.setCustomerStatus(crescendoDocument.getCustomerStatus());
    existingDocument.setAccountStatus(crescendoDocument.getAccountStatus());

    existingDocument.setLastModifiedBy(crescendoDocument.getLastModifiedBy());
    existingDocument.setLastModifiedTimeUtc(crescendoDocument.getLastModifiedTimeUtc());
    existingDocument.setDescription(crescendoDocument.getDescription());
    existingDocument.setCustomerCode(crescendoDocument.getCustomerCode());
    existingDocument.setContacts(
        mapUpdatedAccountContacts(existingDocument.getContacts(), crescendoDocument.getContacts()));
    existingDocument.setUsers(crescendoDocument.getUsers());
    existingDocument.setUserRoles(crescendoDocument.getUserRoles());
    existingDocument.setApplicationMappings(crescendoDocument.getApplicationMappings());
    existingAccount.setAccountDocument(existingDocument);
    return existingAccount;
  }

  private static List<Contact> mapUpdatedAccountContacts(
      List<Contact> existingContacts, List<Contact> crescendoContacts) {
    if (ObjectUtils.isEmpty(crescendoContacts)) {
      return existingContacts;
    } else if (ObjectUtils.isEmpty(existingContacts)) {
      return new ArrayList<>();
    }

    Contact crescendoContact = crescendoContacts.get(0);

    for (Contact existingContact : existingContacts) {
      existingContact.setGoldenRecordAcountId(crescendoContact.getGoldenRecordAcountId());
    }

    return existingContacts;
  }

  public static AccountCrescendo documentToCrescendo(AccountDocument existingDocument) {
    AccountCrescendo accountCrescendo = new AccountCrescendo();
    // Changed due to different setup for Crescendo
    accountCrescendo.setId(existingDocument.getId().toString());
    accountCrescendo.setExternalId(existingDocument.getGoldenRecordId());
    accountCrescendo.setGoldenRecordId(existingDocument.getGoldenRecordId());
    accountCrescendo.setAccountName(existingDocument.getAccountName());
    if (existingDocument.getSegmentStepOneId() != null
        && !existingDocument.getSegmentStepOneId().isEmpty()) {
      accountCrescendo.setSegmentStepOneId(
          SegmentStepOneIdCrescendo.fromValue(existingDocument.getSegmentStepOneId()));
    } else {
      accountCrescendo.setSegmentStepOneId(SegmentStepOneIdCrescendo.NOAH);
    }

    if (existingDocument.getNineBoxStepTwoID() != null) {
      accountCrescendo.setNineBoxStepTwoID(
          NineBoxStepTwoIDCrescendo.fromString(existingDocument.getNineBoxStepTwoID()));
    }
    accountCrescendo.setCompanyEmail(existingDocument.getCompanyEmail());
    accountCrescendo.setPhone(existingDocument.getPhone());
    accountCrescendo.setCustomerCode(existingDocument.getCustomerCode());
    accountCrescendo.setOwnerId(existingDocument.getOwnerId());
    if (existingDocument.getPhysicalAddress() != null) {
      accountCrescendo.setPhysicalAddress(
          CrescendoAddressMapper.modelToCrescendo(existingDocument.getPhysicalAddress()));
    }
    accountCrescendo.setIsMobileFirst(existingDocument.getIsMobileFirst());
    accountCrescendo.setSubTypeId(SubTypeIdCrescendo.FARM_PRODUCER);
    accountCrescendo.setAccountType(
        AccountTypeCrescendo.getFromInt(existingDocument.getAccountType()));
    if (existingDocument.getContacts() != null) {
      List<ContactCrescendo> contacts =
          existingDocument.getContacts().stream()
              .map(CrescendoContactMapper::documentToCrescendo)
              .toList();
      accountCrescendo.setContacts(contacts);
      Business businessId = Business.fromId(existingDocument.getBusinessID());
      if (businessId != null) {
        accountCrescendo.setBusinessId(BusinessToBusinessUnitMapper.toBusinessUnit(businessId));
      }
    }

    accountCrescendo.setUsers(existingDocument.getUsers());
    accountCrescendo.setSourceSystem("LM");
    accountCrescendo.setAccountCurrency(CurrencyCrescendo.UNDEFINED);
    accountCrescendo.setLastModifiedBy(
        existingDocument.getLastModifiedBy() != null
            ? existingDocument.getLastModifiedBy()
            : existingDocument.getOwnerId());
    accountCrescendo.setLastModifyUser(
        existingDocument.getLastModifyUser() != null
            ? existingDocument.getLastModifyUser()
            : existingDocument.getOwnerId());
    accountCrescendo.setCreateUser(
        existingDocument.getCreateUser() != null
            ? existingDocument.getCreateUser()
            : existingDocument.getOwnerId());
    accountCrescendo.setDeleted(existingDocument.isDeleted());
    accountCrescendo.setType(existingDocument.getType());
    accountCrescendo.setApplicationMapping(new ArrayList<>());

    return accountCrescendo;
  }

  private static List<ExternalDataSource> mappingToDataSource(
      List<ApplicationMappingCrescendo> mappings) {
    List<ApplicationMappingCrescendo> filteredMappings =
        mappings.stream().filter(m -> "LM_SITE".equals(m.getExternalSystemName())).toList();
    List<ExternalDataSource> output = new ArrayList<>();
    for (ApplicationMappingCrescendo mapping : filteredMappings) {
      ExternalDataSource externalDataSource = new ExternalDataSource();
      externalDataSource.setUniqueExternalKey(mapping.getXRefExternalSystemId());
      externalDataSource.setSystem(mapping.getXRefExternalSystemName());
      externalDataSource.setSiteId(mapping.getExternalSystemId());
      externalDataSource.setAccountId(mapping.getSfdcAccountId());
      output.add(externalDataSource);
    }
    return output;
  }

  private static AccountDocument crescendoToDocument(AccountCrescendo accountCrescendo) {
    AccountDocument account = new AccountDocument();
    try {
      account.setId(
          accountCrescendo.getId() != null ? UUID.fromString(accountCrescendo.getId()) : null);
    } catch (IllegalArgumentException e) {
      log.error(
          "Illegal external UUID for account with golden record id of: {}",
          accountCrescendo.getId());
    }

    account.setDataSource(DataSource.CRESCENDO);
    account.setGoldenRecordId(accountCrescendo.getGoldenRecordId());
    account.setAccountName(accountCrescendo.getAccountName());
    account.setLegalName(accountCrescendo.getLegalName());
    if (accountCrescendo.getPhysicalAddress() != null) {
      account.setPhysicalAddress(
          CrescendoAddressMapper.crescendoToModel(accountCrescendo.getPhysicalAddress()));
    }
    account.setAccountType(AccountTypeCrescendo.toInt(accountCrescendo.getAccountType()));
    if (accountCrescendo.getSegmentStepOneId() != null) {
      account.setSegmentStepOneId(accountCrescendo.getSegmentStepOneId().getValue());
    }
    if (accountCrescendo.getNineBoxStepTwoID() != null) {
      account.setNineBoxStepTwoID(accountCrescendo.getNineBoxStepTwoID().getValue());
    }
    account.setSourceSystem(accountCrescendo.getSourceSystem());
    account.setSubTypeID(
        accountCrescendo.getSubTypeId() != null
            ? SubTypeId.fromString(
                accountCrescendo.getSubTypeId().getValue().replaceAll("\\s+", ""))
            : null);
    Business business = BusinessToBusinessUnitMapper.toBusiness(accountCrescendo.getBusinessId());
    account.setBusinessID(business != null ? business.getBusinessId() : null);
    account.setParentAccountID(accountCrescendo.getParentAccountID());
    account.setExternalParentAccountID(
        accountCrescendo.getExternalParentAccountId() != null
            ? UUID.fromString(accountCrescendo.getExternalParentAccountId())
            : null);
    account.setOwnerId(accountCrescendo.getOwnerId());
    account.setOwnerProfileNameandId(accountCrescendo.getOwnerProfileNameAndId());
    account.setAccountValidated(accountCrescendo.getProspectValidated());
    account.setActive(accountCrescendo.getActive());
    account.setCompanyEmail(accountCrescendo.getCompanyEmail());
    account.setPhone(accountCrescendo.getPhone());
    account.setAccountCurrency(crescendoToCurrency(accountCrescendo).getValue());
    account.setAccountNumber(accountCrescendo.getCustomerCode());
    if (accountCrescendo.getAccountStatus() != null) {
      account.setAccountStatus(accountCrescendo.getAccountStatus().getValue());
    }
    account.setLastModifiedBy(accountCrescendo.getLastModifiedBy());
    account.setLastModifiedTimeUtc(accountCrescendo.getLastModifiedTimeUtc());
    account.setDescription(accountCrescendo.getDescription());
    account.setCustomerCode(accountCrescendo.getCustomerCode());
    if (accountCrescendo.getContacts() != null) {
      List<Contact> crescendoContacts =
          accountCrescendo.getContacts().stream()
              .map(CrescendoContactMapper::crescendoToModel)
              .toList();
      account.setContacts(crescendoContacts);
    }
    account.setUsers(mapUsers(accountCrescendo));
    account.setUserRoles(mapUserRoles(accountCrescendo));
    account.setApplicationMappings(mappingToDataSource(accountCrescendo.getApplicationMapping()));
    return account;
  }

  private static Set<String> mapUsers(AccountCrescendo accountCrescendo) {
    Set<String> users = new HashSet<>();
    users.add(accountCrescendo.getOwnerId());

    users.addAll(
        accountCrescendo.getUserRoles().stream()
            .filter(r -> !"Technical Specialist".equals(r.getUserRole()))
            .map(UserRoleCrescendo::getUserName)
            .collect(Collectors.toSet()));
    return users;
  }

  private static List<UserRole> mapUserRoles(AccountCrescendo accountCrescendo) {
    return accountCrescendo.getUserRoles().stream()
        .map(
            cr -> {
              UserRole userRole = new UserRole();
              userRole.setUserName(cr.getUserName());
              userRole.setRoleType(cr.getUserRole());
              userRole.setUserBusinessUnit(cr.getUserBusinessUnit().ordinal());
              return userRole;
            })
        .toList();
  }

  private static Currencies crescendoToCurrency(AccountCrescendo accountCrescendo) {
    if (accountCrescendo.getAccountCurrency() == null) {
      return Currencies.NotSet;
    } else if (accountCrescendo.getAccountCurrency() == CurrencyCrescendo.EUR) {
      return Currencies.Euro;
    } else {
      return Currencies.valueOf(accountCrescendo.getAccountCurrency().getValue());
    }
  }
}
