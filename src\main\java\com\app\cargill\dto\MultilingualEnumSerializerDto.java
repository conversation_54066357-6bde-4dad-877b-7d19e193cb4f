/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultilingualEnumSerializerDto {

  @Builder.Default List<Map<String, Map<String, String>>> housingSystem = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> feedingSystems = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> penSource = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> milkingSystem = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> bcsPointScales = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> lactationStages = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> business = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> bcsPointScale = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> cowFlowDesign = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> robotType = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> milkPickup = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> milkUreaMeasure = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> metabolicTypeKeys = new ArrayList<>();

  @Builder.Default
  List<Map<String, Map<String, String>>> roboticMilkTrendsListType = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> currencies = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> brandList = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> unitOfMeasureKeys = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> noteCategoryType = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> scorers = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> silageType = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> feedStorageType = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> notificationType = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> waterQuality = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> breed = new ArrayList<>();

  @Builder.Default
  List<Map<String, Map<String, String>>> profitabilityQualityAnalysis = new ArrayList<>();

  @Builder.Default List<Map<String, Map<String, String>>> productionSystem = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> beddingQuality = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> breedReturnOverFeed = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> feeding = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> supplementTypes = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> homeGrownForageTypes = new ArrayList<>();
  @Builder.Default List<Map<String, Map<String, String>>> homegrownGrainTypes = new ArrayList<>();

  @Builder.Default
  List<Map<String, Map<String, String>>> returnOverFeedMilkProductionType = new ArrayList<>();
}
