/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.EarTags;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EarTagsRepository extends JpaRepository<EarTags, Long> {

  @Query(
      value =
          "Select e.* from Ear_Tags e where e.Ear_Tag_Document->>'accountId' in(:accountIdsByUser) "
              + " AND e.deleted= false AND timezone('UTC',e.updated_date) > :lastSyncTime",
      nativeQuery = true)
  Page<EarTags> findByAccountIds(
      @Param("accountIdsByUser") List<String> accountIdsByUser,
      Pageable pageable,
      @Param("lastSyncTime") Instant lastSyncTime);

  boolean existsByLocalId(@Param("localId") String localId);

  @Query(
      value =
          "Select e.* from Ear_Tags e where e.Ear_Tag_Document->>'id' = :id and  e.deleted= false",
      nativeQuery = true)
  EarTags findByEarTagId(@Param("id") String id);
}
