/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.NotificationType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class NotificationDto extends BaseDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private String title;
  private String createUser;
  private String description;
  private NotificationType type;
  private Instant mobileCreatedAt;
  private Boolean isRead;
  private String userId;
  @Builder.Default private Map<String, String> keys = new HashMap<>();
}
