/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.MilkSoldEvaluationExportChartVariations;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MilkSoldEvaluationReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private MilkSoldEvaluationExportChartVariations chartType;
  private String yaxisLeftLabel;
  private String yaxisRightLabel;

  @Builder.Default private List<MilkSoldEvaluationExportChartDto> dataPoints = new ArrayList<>();
  @JsonIgnore private Map<String, String> chartLabels;
}
