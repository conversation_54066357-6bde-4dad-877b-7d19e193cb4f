{"UserId": "<EMAIL>", "UnitOfMeasure": 0, "LastSyncOperationDateTime": "2017-05-22T13:58:50.125Z", "LastEulaVersionAccepted": "2017-03-01T00:00:00Z", "BrandList": [], "EulaContent": "cbsmobilityteam Accepted <PERSON><PERSON> on 3/1/2017 12:00:00 AM", "SelectedCurrency": 0, "LastPrivacyVersionAccepted": null, "id": "************************************", "CreateUser": null, "IsDeleted": false, "LastModifyUser": "<EMAIL>", "CreateTimeUtc": "2017-05-04T09:32:21.917Z", "LastModifiedTimeUtc": {"Date": "2017-05-22T13:58:50.164Z", "Epoch": 1495461530}, "LastSyncTimeUtc": "2017-05-22T14:00:26.663Z", "IsNew": false}