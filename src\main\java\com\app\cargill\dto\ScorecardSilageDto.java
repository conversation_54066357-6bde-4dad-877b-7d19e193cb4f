/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ScorecardSilageDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Silage type.
  /// </summary>
  /// <value>Type of the silage.</value>
  /// <remarks>This will differentiate section silage type.</remarks>
  private Integer sectionIndex;
  /// <summary>
  /// Silage type.
  /// </summary>
  /// <value>Type of the silage.</value>
  /// <remarks>This will differentiate section silage type.</remarks>
  private Integer sectionSilageType;
  /// <summary>
  /// The name of the silage type to display to the user.
  /// </summary>
  /// <value>The name of the silage type.</value>
  /// <remarks>This will be a key to the string resources file.</remarks>
  private String silageTypeName;
  /// <summary>
  /// A set of questions that make up this section of a scorecard.
  /// </summary>
  /// <value>The questions.</value>
  private List<ScorecardQuestionDto> questions;
  /// <summary>
  /// Indicates if this sections score should be included in the overall scorecard
  /// score
  /// </summary>
  /// <value><c>true</c> if include in overall forage score; otherwise,
  /// <c>false</c>.</value>
  private Boolean includeInOverallForageScore;
}
