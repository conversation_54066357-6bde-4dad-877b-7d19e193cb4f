/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.CityDto;
import com.app.cargill.dto.CountryDto;
import com.app.cargill.dto.StateDto;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;

@SuppressWarnings("java:S107") // Methods have multiple parameters intentionally
public interface IGeoLocationService {

  List<CountryDto> fetchAllCountries(
      Locale locale, ResourceBundleMessageSource resourceBundleMessageSource);

  Page<CountryDto> fetchAllCountriesPaginated(
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource);

  List<StateDto> fetchStateByCountryCode(
      String countryCode, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource);

  Page<StateDto> fetchStateByCountryCodePaginated(
      String countryCode,
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource);

  List<CityDto> fetchCitiesByStateCodeAndCountryCode(String stateCode, String countryCode);

  Page<CityDto> fetchCitiesByStateCodeAndCountryCodePaginated(
      String stateCode,
      String countryCode,
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting);

  Page<StateDto> fetchStatesByCountryCodesPaginated(
      List<String> countryCodes,
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource);
}
