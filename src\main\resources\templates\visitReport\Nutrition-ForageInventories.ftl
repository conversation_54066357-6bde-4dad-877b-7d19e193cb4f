<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitViewModel.NutritionItem", [], "Nutrition",
                locale)}</span>
            <span class="lg-head">${localization.getMessage("PileBunkerCapacities", [], "Forage inventories",
                locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000131313FI</div>
    </div>
</div>
<#if model.forageInventoriesTool.details?? && model.forageInventoriesTool.details[0]??>
    <#assign start=0>
        <#assign end=7>
            <#assign listSize=-1>
                <#assign isFirstIteration=true>
                    <!-- Details Table -->
                    <div class="container">
                        <#assign maxIterations=999999>
                            <#list 1..maxIterations as i>
                                <div class="row mx-neg-4">
                                    <div class="col-12 px-4 table-secondary">
                                        <table>
                                            <thead>
                                                <tr>
                                                    <#list model.forageInventoriesTool.details[0]?keys as prop>
                                                        <#if model.forageInventoriesTool.details[0][prop]?? &&
                                                            model.forageInventoriesTool.details[0][prop][0]??>
                                                            <#if listSize==-1>
                                                                <#assign
                                                                    listSize=model.forageInventoriesTool.details[0][prop][0]?size-1>
                                                            </#if>
                                                            <#if end gte listSize>
                                                                <#assign end=listSize>
                                                            </#if>
															<#if start gte listSize>
                                                                <#assign start=listSize>
                                                            </#if>
                                                            <#list model.forageInventoriesTool.details[0][prop] as key>
                                                                <#if !isFirstIteration>
                                                                    <th>${key[0].column!}</th>
                                                                </#if>
                                                                <#list key[start..end] as keyVal>
                                                                    <th>${keyVal.column!}</th>
                                                                </#list>
                                                                <#break>
                                                            </#list>
                                                        </#if>
                                                    </#list>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <#list model.forageInventoriesTool.details as item>
                                                    <#list item?keys as prop>
                                                        <tr>
                                                            <td colspan="10">
                                                                <h3 class="title mb-0">${prop!}</h3>
                                                            </td>
                                                        </tr>
                                                        <#list item[prop] as keyRow>

                                                            <tr>
                                                                <#if !isFirstIteration>
                                                                    <td>${keyRow[0].value!}</td>
                                                                </#if>
                                                                <#list keyRow[start..end] as keyValuesSliced>

                                                                    <td>${keyValuesSliced.value!}</td>

                                                                </#list>
                                                            </tr>

                                                        </#list>
                                                    </#list>
                                                </#list>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>


                                <#if end gte listSize>
                                    <#break>
								<#else>
									<div class="break-page"></div>
									<#assign isFirstIteration=false>
                                </#if>
                                <#assign start=end+1>
                                <#assign end=end+7>

                            </#list>
                    </div>
</#if>
<#if model.forageInventoriesTool?? && model.forageInventoriesTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.forageInventoriesTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>