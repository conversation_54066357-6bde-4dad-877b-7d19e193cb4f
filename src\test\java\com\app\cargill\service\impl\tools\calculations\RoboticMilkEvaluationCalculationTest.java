/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.app.cargill.constants.CowFlowDesign;
import com.app.cargill.constants.RobotType;
import com.app.cargill.document.RoboticMilkEvaluationTool;
import com.app.cargill.document.RoboticMilkEvaluationToolItem;
import com.app.cargill.document.RoboticMilkEvaluationToolOutputToolItem;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RoboticMilkEvaluationCalculationTest {

  @InjectMocks private RoboticMilkEvaluationCalculation roboticMilkEvaluationCalculation;

  @Test
  void calculateFields() {
    RoboticMilkEvaluationTool roboticMilkEvaluationTool =
        RoboticMilkEvaluationTool.builder()
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItem.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItem.builder().build())
                    .lactatingCows(12)
                    .robotsInHerd(2)
                    .milkings(42.1)
                    .robotFreeTime(21.2)
                    .averageMilkYield(21.1)
                    .milkingRefusals(2.1)
                    .totalMilkingFailures(1.1)
                    .milkingSpeed(21.1)
                    .averageBoxTime(2.1)
                    .maximumConcentrate(2.2)
                    .averageConcentrateFed(1.9)
                    .minimumConcentrate(1.1)
                    .concentratePer100KGMilk(21.1)
                    .restFeed(1.1)
                    .build())
            .build();

    roboticMilkEvaluationTool =
        roboticMilkEvaluationCalculation.calculateFields(roboticMilkEvaluationTool);
    assertNotNull(roboticMilkEvaluationTool.getVisitRoboticMilkEvaluationData());

    RoboticMilkEvaluationToolOutputToolItem roboticMilkEvaluationToolOutputToolItem =
        roboticMilkEvaluationTool.getVisitRoboticMilkEvaluationData().getOutputs();
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getCowsPerRobot());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMilkPerRobot());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getRobotFreeTime());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMilkPerRobot());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMilkings());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMilkingRefusals());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMilkingFailures());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMilkingSpeed());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getAverageBoxTime());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMaximumConcentrate());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getAverageConcentrate());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMinimumConcentrate());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getMinimumConcentrate());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getConcentratePer100KGMilk());
    assertNotNull(roboticMilkEvaluationToolOutputToolItem.getRestFeed());
  }

  @Test
  void calculateFieldsWithNullValues() {
    RoboticMilkEvaluationTool roboticMilkEvaluationTool =
        RoboticMilkEvaluationTool.builder()
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItem.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItem.builder().build())
                    .build())
            .build();

    roboticMilkEvaluationTool =
        roboticMilkEvaluationCalculation.calculateFields(roboticMilkEvaluationTool);

    RoboticMilkEvaluationToolOutputToolItem roboticMilkEvaluationToolOutputToolItem =
        roboticMilkEvaluationTool.getVisitRoboticMilkEvaluationData().getOutputs();
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getCowsPerRobot());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getMilkPerRobot());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getRobotFreeTime());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getMilkings());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getMilkingRefusals());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getMilkingFailures());
    assertNull(roboticMilkEvaluationToolOutputToolItem.getMilkingSpeed());
    assertNull(roboticMilkEvaluationToolOutputToolItem.getAverageBoxTime());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getMaximumConcentrate());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getAverageConcentrate());
    assertEquals(0.0, roboticMilkEvaluationToolOutputToolItem.getMinimumConcentrate());
    assertNull(roboticMilkEvaluationToolOutputToolItem.getConcentratePer100KGMilk());
    assertNull(roboticMilkEvaluationToolOutputToolItem.getRestFeed());
  }

  @Test
  void verifyCalculations() {
    RoboticMilkEvaluationTool roboticMilkEvaluationTool =
        RoboticMilkEvaluationTool.builder()
            .visitRoboticMilkEvaluationData(
                RoboticMilkEvaluationToolItem.builder()
                    .outputs(RoboticMilkEvaluationToolOutputToolItem.builder().build())
                    .robotType(RobotType.GEA)
                    .cowFlowDesign(CowFlowDesign.FreeFlow)
                    .robotsInHerd(2)
                    .lactatingCows(92)
                    .averageMilkYield(30.98)
                    .milkings(2.75)
                    .robotFreeTime(42.6)
                    .milkingRefusals(0.75)
                    .totalMilkingFailures(26.0)
                    .maximumConcentrate(9.0)
                    .averageConcentrateFed(4.0)
                    .minimumConcentrate(2.0)
                    .averageBoxTime(6.5)
                    .milkingSpeed(2.5)
                    .concentratePer100KGMilk(13.2)
                    .build())
            .build();

    roboticMilkEvaluationTool =
        roboticMilkEvaluationCalculation.calculateFields(roboticMilkEvaluationTool);

    assertEquals(
        46.0,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getCowsPerRobot());

    assertEquals(
        126.0,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMilkingsPerRobot());

    assertEquals(
        42.6,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getRobotFreeTime());

    assertEquals(
        1425.0,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMilkPerRobot());

    assertEquals(
        2.75,
        roboticMilkEvaluationTool.getVisitRoboticMilkEvaluationData().getOutputs().getMilkings());

    assertEquals(
        0.75,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMilkingRefusals());

    assertEquals(
        13.00,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMilkingFailures());

    assertEquals(
        2.5,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMilkingSpeed());

    assertEquals(
        6.5,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getAverageBoxTime());

    assertEquals(
        9.0,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMaximumConcentrate());

    assertEquals(
        4.0,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getAverageConcentrate());

    assertEquals(
        2.0,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getMinimumConcentrate());

    assertEquals(
        13.2,
        roboticMilkEvaluationTool
            .getVisitRoboticMilkEvaluationData()
            .getOutputs()
            .getConcentratePer100KGMilk());

    assertNull(
        roboticMilkEvaluationTool.getVisitRoboticMilkEvaluationData().getOutputs().getRestFeed());
  }
}
