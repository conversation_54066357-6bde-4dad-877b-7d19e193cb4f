/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.*;
import java.util.Arrays;
import java.util.List;

public class ManureScoreCalculation {

  public RumenHealthManureScoreTool calculateFields(RumenHealthManureScoreTool tool) {
    if (tool == null) return null;

    if (tool.getPens() != null) {
      for (RumenHealthManureScoreToolItem pen : tool.getPens()) {
        pen.getManureScores().averageValue = averageManureScore(pen);
        pen.getManureScores().standardDeviation = calculateStandardDeviation(pen);
      }
    }
    return tool;
  }

  private Double averageManureScore(RumenHealthManureScoreToolItem pen) {
    double totalSum = 0;
    double totalNumberOfAnimals = 0;
    double averageValue = 0;

    if (pen.getManureScores().getItems() != null) {
      for (RumenHealthManureScoreItem item : pen.getManureScores().getItems())
        if (item.getAnimalNumbers() != null) {
          totalNumberOfAnimals += item.getAnimalNumbers();
        }
    }

    if (totalNumberOfAnimals > 0) {
      for (RumenHealthManureScoreItem item : pen.getManureScores().getItems()) {
        double pOp = (item.getAnimalNumbers() / totalNumberOfAnimals) * 100;
        item.setPercentOfPen(roundAvoid(pOp, 2));
      }
      for (RumenHealthManureScoreItem item : pen.getManureScores().getItems())
        totalSum += (item.getScoreCategory() * item.getPercentOfPen());

      averageValue = totalSum / 100;
    }
    return averageValue;
  }

  private Double calculateStandardDeviation(RumenHealthManureScoreToolItem pen) {
    double sumOfSquareDelta = 0;
    double stdDev;
    double n = 0;
    final int perAnimal = 1;
    Double avg = pen.getManureScores().averageValue;
    List<RumenHealthManureScoreItem> activeScores =
        Arrays.stream(pen.getManureScores().getItems())
            .filter(arg -> arg.getPercentOfPen() != null && arg.getPercentOfPen() > 0)
            .toList();

    for (RumenHealthManureScoreItem score : activeScores) {
      int i = 0;
      for (; i < score.getAnimalNumbers(); i++) {
        double delta = Math.abs((perAnimal * score.getScoreCategory() - avg));
        double sqDelta = Math.pow(delta, 2);
        sumOfSquareDelta += sqDelta;
      }
      n += i;
    }

    if (n != 0) {
      double variance = sumOfSquareDelta / n;
      double sd = Math.sqrt(variance);
      return roundAvoid(sd, 2);
    } else stdDev = 0;
    return stdDev;
  }
}
