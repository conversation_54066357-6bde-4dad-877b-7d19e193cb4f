<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="029" author="anenkov">
    <sql>
      CREATE UNIQUE INDEX IF NOT EXISTS site_mappings_site_id_uidx ON site_mappings((site_mapping_document->>'LabyrinthSiteId'));
    </sql>
  </changeSet>

</databaseChangeLog>
