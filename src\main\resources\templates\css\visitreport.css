@font-face {
  font-family: "Helvetica";
  src: url("../fonts/Helvetica.eot");
  src: url("../fonts/Helvetica.eot?#iefix") format("embedded-opentype"), url("../fonts/Helvetica.woff2") format("woff2"), url("../fonts/Helvetica.woff") format("woff"), url("../fonts/Helvetica.ttf") format("truetype"), url("../fonts/Helvetica.svg#Helvetica") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: "HelveticaNeueMedium";
  src: url("../fonts/HelveticaNeueMedium.eot");
  src: url("../fonts/HelveticaNeueMedium.eot?#iefix") format("embedded-opentype"), url("../fonts/HelveticaNeueMedium.woff2") format("woff2"), url("../fonts/HelveticaNeueMedium.woff") format("woff"), url("../fonts/HelveticaNeueMedium.ttf") format("truetype"), url("../fonts/HelveticaNeueMedium.svg#HelveticaNeueMedium") format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
*,
*::before,
*::after {
  box-sizing: border-box;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

body {
  font-family: "Helvetica", sans-serif;
  counter-set: headings 0;
}

.container {
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
}

.col-4-equal {
  display: block;
  flex: 0 0 33.33%;
  max-width: 33.33%;
}

.col-4 {
  display: block;
  flex: 0 0 25%;
  max-width: 25%;
}

.col-5 {
  display: block;
  flex: 0 0 41%;
  max-width: 41%;
}

.col-6 {
  display: block;
  flex: 0 0 50%;
  max-width: 50%;
}

.col-7 {
  display: block;
  flex: 0 0 59%;
  max-width: 59%;
}

.col-8 {
  display: block;
  flex: 0 0 75%;
  max-width: 75%;
}

.col-12 {
  display: block;
  flex: 0 0 100%;
  max-width: 100%;
}

body,
figure,
p {
  margin: 0;
}

table {
  border-collapse: collapse;
  width: 100%;
}
table td,
table th {
  text-align: left;
}
table th {
  font-size: 9px;
  padding: 8px 8px;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
}
table td {
  font-size: 8px;
  padding: 4px 8px;
  color: #323F4B;
}
table tr:nth-child(even) {
  background-color: #ECECEC;
}
table tr:nth-child(odd) {
  background-color: #F6F6F6;
}
table.table-center td,
table.table-center th {
  text-align: center;
}
table.w-50 td,
table.w-50 th {
  width: 50%;
}

.report-header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1;
  padding: 20px 0px 6px;
  background: #fff;
  height: 68px;
}
.report-header .title-sm {
  color: #323F4B;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  font-family: "HelveticaNeueMedium", sans-serif;
}

.report-footer {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 1;
  padding: 6px 0px 20px;
  background: #fff;
  height: 78px;
}
.report-footer .title-sm {
  color: #323F4B;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  font-family: "HelveticaNeueMedium", sans-serif;
}
.report-footer .page-num {
  color: #323F4B;
  font-size: 10px;
  font-weight: 400;
}
.report-footer p {
  font-style: italic;
  color: #323F4B;
  font-size: 8px;
}
.report-footer .footer-logo img {
  max-width: 50px;
}

@media print {
  @page {
    size: A4 portrait;
    margin: 18mm 0mm 15mm 0mm;
  }
}
#content {
  display: table;
}

.flex-points {
  display: flex;
  padding: 6px 0;
}
.flex-points .prime-wrapper {
  flex: 1;
}
.flex-points .prime-wrapper label {
  font-size: 8px;
  color: #6C7782;
  font-weight: 400;
}
.flex-points .prime-wrapper .prime-value {
  font-weight: 500;
  margin: 6px 0 0;
  color: #323F4B;
  font-size: 10px;
  font-family: "HelveticaNeueMedium", sans-serif;
}

hr {
  border-color: #E1DEF2;
  border-top: 0;
}

.tag {
  border-radius: 2px;
  display: inline-flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 6px;
  font-size: 7px;
}
.tag.info {
  color: #459ED8;
  background: #E6F2FA;
}

/* Table Primary */
.table-primary table th {
  color: #0B5E86;
  background-color: #fff;
}

.table-secondary .title {
  font-size: 10px;
  color: #0B5E86;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 700;
  margin-top: 0;
}
.table-secondary table th {
  color: #fff;
  background: #0B5E86;
}

.legend-head {
  display: flex;
  align-items: center;
  padding: 12px 0px;
}
.legend-head .count {
  padding: 12px;
  border-right: 2px solid #0B5E86;
  margin-right: 12px;
  font-size: 15px;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
}
.legend-head .main-title {
  display: flex;
  flex-direction: column;
}
.legend-head .main-title .sm-head {
  color: #D9A25E;
  font-size: 10px;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
  line-height: 18px;
}
.legend-head .main-title .lg-head {
  font-size: 14px;
  color: #323F4B;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
}

.title-primary {
  background: #0B5E86;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  font-family: "HelveticaNeueMedium", sans-serif;
  margin: 0;
  padding: 6px 8px;
}

.title-secondary {
  font-size: 10px;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
  color: #323F4B;
  background: #F4F4F4;
  letter-spacing: 0.2px;
  padding: 4px 8px;
  margin: 0;
  display: flex;
  align-items: center;
}

.title-sub {
  font-size: 9px;
  color: #323F4B;
  margin: 0 0 8px;
}

.followup {
  margin: 0;
  font-size: 10px;
  display: flex;
  justify-content: space-between;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
  color: #0B5E86;
}
.followup .date {
  color: #1BACA7;
}

p {
  font-size: 10px;
  color: #323F4B;
}

img {
  max-width: 100%;
  vertical-align: middle;
}

.notes-images {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 8px;
}
.notes-images figure {
  height: 96px;
  width: 120px;
  overflow: hidden;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  align-content: stretch;
}
.notes-images figure img {
  max-height: 100%;
  max-width: 100%;
  border-radius: 4px;
  border: 1px solid #E1DEF2;
}

.v-center {
  display: flex;
  align-items: center;
}

h6 {
  font-weight: 700;
  font-size: 9px;
  line-height: 12px;
  /* identical to box height, or 133% */
  letter-spacing: 0.2px;
  /* Text colors/Main text */
  color: #323F4B;
  margin: 10px 0;
}

.w-right,
.w-left {
  display: block;
  box-sizing: border-box;
}
.w-right.w-left,
.w-left.w-left {
  width: 55%;
  float: left;
  padding: 20px 0 0 20px;
}
.w-right #chart-container,
.w-left #chart-container {
  position: relative;
  overflow: hidden;
}
.w-right.w-right,
.w-left.w-right {
  width: 45%;
  float: left;
  padding-left: 20px;
}
.w-right.w-right ul,
.w-left.w-right ul {
  padding: 0;
  margin: 0;
}
.w-right.w-right ul li,
.w-left.w-right ul li {
  list-style: none;
  font-weight: 400;
  font-size: 10px;
  line-height: 15px;
  color: #6e6e73;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  padding-left: 20px;
}
.w-right.w-right ul li::before,
.w-left.w-right ul li::before {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 1px;
  margin-right: 8px;
  position: absolute;
  left: 0;
}
.w-right.w-right ul li.red::before,
.w-left.w-right ul li.red::before {
  background: #C2531B;
}
.w-right.w-right ul li.yellow::before,
.w-left.w-right ul li.yellow::before {
  background: #D9A106;
}
.w-right.w-right ul li.green::before,
.w-left.w-right ul li.green::before {
  background: #638B1B;
}

.comments {
  padding: 0 0 0 18px;
}
.comments li {
  font-size: 10px;
  color: #323F4B;
  line-height: 22px;
}

.card {
  background: #fff;
  border-radius: 4px;
  border: 1px solid #DFE9EE;
}

.card-header {
  padding: 5px;
}

.card-header h4 {
  margin: 0;
  font-size: 10px;
  color: #0B5E86;
  text-transform: capitalize;
  font-family: "HelveticaNeueMedium", sans-serif;
  font-weight: 500;
}

.card-body {
  padding: 10px;
  background: #fff;
  overflow: hidden;
}
.card-body canvas {
  max-width: 100% !important;
  height: auto !important;
}

.card-footer {
  padding: 5px;
}

.content-set {
  padding: 0 15px;
}
.content-set label {
  color: #6C7782;
  font-size: 14px;
  margin-bottom: 7px;
  display: inline-flex;
}
.content-set h4 {
  font-size: 14px;
}

.attribute-name {
  font-size: 12px;
  color: #6C7782;
}
.attribute-name span {
  font-family: "HelveticaNeueMedium", sans-serif;
}

.single-line-data {
  margin: 0 0 20px;
  font-weight: 400;
}
.single-line-data span {
  font-weight: 600;
}

.legend-wrap {
  padding: 0 15px;
}
.legend-wrap p {
  font-size: 8px;
  font-family: "HelveticaNeueMedium", sans-serif;
  letter-spacing: 0.3px;
  color: #323F4B;
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 16px;
  text-transform: capitalize;
}
.legend-wrap .solid-circle-sm::before, .legend-wrap .tulip-solid::before, .legend-wrap .blue-purple-solid-shadow::before, .legend-wrap .blue-purple-solid-dark::before, .legend-wrap .blue-purple-solid-light::before, .legend-wrap .blue-purple-solid::before, .legend-wrap .jordy-blue-solid-shadow::before, .legend-wrap .jordy-blue-solid-dark::before, .legend-wrap .jordy-blue-solid-light::before, .legend-wrap .jordy-blue-solid::before, .legend-wrap .middle-blue-solid-dark::before, .legend-wrap .middle-blue-solid-light::before, .legend-wrap .middle-blue-solid::before, .legend-wrap .gold-solid::before, .legend-wrap .red-2-solid::before, .legend-wrap .pink-solid::before, .legend-wrap .voilet-3-solid::before, .legend-wrap .voilet-2-solid::before, .legend-wrap .voilet-solid::before, .legend-wrap .dynamic-solid-right::before, .legend-wrap .copper-solid::before, .legend-wrap .dynamic-solid-left::before, .legend-wrap .jupiter-solid::before, .legend-wrap .indigo-light-solid::before, .legend-wrap .lagoon-solid-2::before, .legend-wrap .lagoon-solid::before, .legend-wrap .aqua-solid::before, .legend-wrap .yellow-light-solid::before, .legend-wrap .green-light-extreme::before, .legend-wrap .green-light-solid::before, .legend-wrap .pink-light-solid::before, .legend-wrap .red-strip::before, .legend-wrap .orange-strip::before, .legend-wrap .purple-strip::before, .legend-wrap .blue-light-solid::before, .legend-wrap .orange-solid-2::before, .legend-wrap .orange-solid::before, .legend-wrap .red-solid::before, .legend-wrap .blue-solid-prime::before, .legend-wrap .blue-solid::before, .legend-wrap .purple-solid::before, .legend-wrap .green-solid::before {
  content: "";
  display: inline-flex;
  border-radius: 100%;
  height: 8px;
  width: 8px;
  position: absolute;
  left: 0;
}
.legend-wrap.legend-sm p {
  padding-left: 10px;
  font-size: 7px;
}
.legend-wrap.legend-sm .solid-circle-sm::before, .legend-wrap.legend-sm .tulip-solid::before, .legend-wrap.legend-sm .blue-purple-solid-shadow::before, .legend-wrap.legend-sm .blue-purple-solid-dark::before, .legend-wrap.legend-sm .blue-purple-solid-light::before, .legend-wrap.legend-sm .blue-purple-solid::before, .legend-wrap.legend-sm .jordy-blue-solid-shadow::before, .legend-wrap.legend-sm .jordy-blue-solid-dark::before, .legend-wrap.legend-sm .jordy-blue-solid-light::before, .legend-wrap.legend-sm .jordy-blue-solid::before, .legend-wrap.legend-sm .middle-blue-solid-dark::before, .legend-wrap.legend-sm .middle-blue-solid-light::before, .legend-wrap.legend-sm .middle-blue-solid::before, .legend-wrap.legend-sm .gold-solid::before, .legend-wrap.legend-sm .red-2-solid::before, .legend-wrap.legend-sm .pink-solid::before, .legend-wrap.legend-sm .voilet-3-solid::before, .legend-wrap.legend-sm .voilet-2-solid::before, .legend-wrap.legend-sm .voilet-solid::before, .legend-wrap.legend-sm .dynamic-solid-right::before, .legend-wrap.legend-sm .copper-solid::before, .legend-wrap.legend-sm .dynamic-solid-left::before, .legend-wrap.legend-sm .jupiter-solid::before, .legend-wrap.legend-sm .indigo-light-solid::before, .legend-wrap.legend-sm .lagoon-solid-2::before, .legend-wrap.legend-sm .lagoon-solid::before, .legend-wrap.legend-sm .aqua-solid::before, .legend-wrap.legend-sm .yellow-light-solid::before, .legend-wrap.legend-sm .green-light-extreme::before, .legend-wrap.legend-sm .green-light-solid::before, .legend-wrap.legend-sm .pink-light-solid::before, .legend-wrap.legend-sm .red-strip::before, .legend-wrap.legend-sm .orange-strip::before, .legend-wrap.legend-sm .purple-strip::before, .legend-wrap.legend-sm .blue-light-solid::before, .legend-wrap.legend-sm .orange-solid-2::before, .legend-wrap.legend-sm .orange-solid::before, .legend-wrap.legend-sm .red-solid::before, .legend-wrap.legend-sm .blue-solid-prime::before, .legend-wrap.legend-sm .blue-solid::before, .legend-wrap.legend-sm .purple-solid::before, .legend-wrap.legend-sm .green-solid::before {
  height: 7px;
  width: 7px;
}
.legend-wrap .green-solid::before {
  background: #1BACA7;
}
.legend-wrap .purple-solid::before {
  background: #A160E6;
}
.legend-wrap .blue-solid::before {
  background: #307698;
}
.legend-wrap .blue-solid-prime::before {
  background: #4E93E6;
}
.legend-wrap .red-solid::before {
  background: #8D0909;
}
.legend-wrap .orange-solid::before {
  background: #D98773;
}
.legend-wrap .orange-solid-2::before {
  background: #FFA500;
}
.legend-wrap .blue-light-solid::before {
  background: #61ADDE;
}
.legend-wrap .purple-strip::before {
  background: #fff;
  border: 1px dashed #A160E6;
}
.legend-wrap .orange-strip::before {
  background: #fff;
  border: 1px dashed #DA9E44;
}
.legend-wrap .red-strip::before {
  background: #fff;
  border: 1px dashed #8D0909;
}
.legend-wrap .pink-light-solid::before {
  background: #EE6E81;
}
.legend-wrap .green-light-solid::before {
  content: "";
  background: #5FBB7A;
}
.legend-wrap .green-light-extreme::before {
  content: "";
  background: #55C2BE;
}
.legend-wrap .yellow-light-solid::before {
  background: #E4BF60;
}
.legend-wrap .aqua-solid::before {
  background: #7AD8DC;
}
.legend-wrap .lagoon-solid::before {
  background: #83BEF4;
}
.legend-wrap .lagoon-solid-2::before {
  background: #67B7DC;
}
.legend-wrap .indigo-light-solid::before {
  background: #ABA1E3;
}
.legend-wrap .jupiter-solid::before {
  background: #F18494;
}
.legend-wrap .dynamic-solid-left::before {
  background: var(--background);
}
.legend-wrap .copper-solid:before {
  background: #D98773;
}
.legend-wrap .dynamic-solid-right::before {
  background: var(--background);
}
.legend-wrap .voilet-solid::before {
  background: #6794DC;
}
.legend-wrap .voilet-2-solid::before {
  background: #8067DC;
}
.legend-wrap .voilet-3-solid::before {
  background: #A367DC;
}
.legend-wrap .pink-solid::before {
  background: #DC67CE;
}
.legend-wrap .red-2-solid::before {
  background: #DC6967;
}
.legend-wrap .gold-solid::before {
  background: #DCAF67;
}
.legend-wrap .middle-blue-solid::before {
  background: #7AD8DC;
}
.legend-wrap .middle-blue-solid-light::before {
  background: rgba(122, 216, 220, 0.3019607843);
}
.legend-wrap .middle-blue-solid-dark::before {
  background: #60B5B9;
}
.legend-wrap .jordy-blue-solid::before {
  background: #83BEF4;
}
.legend-wrap .jordy-blue-solid-light::before {
  background: rgba(131, 190, 244, 0.3019607843);
}
.legend-wrap .jordy-blue-solid-dark::before {
  background: #4D88BE;
}
.legend-wrap .jordy-blue-solid-shadow::before {
  background: #D7F3F5;
}
.legend-wrap .blue-purple-solid::before {
  background: #ABA1E3;
}
.legend-wrap .blue-purple-solid-light::before {
  background: rgba(156, 144, 222, 0.3019607843);
}
.legend-wrap .blue-purple-solid-dark::before {
  background: #7A6EBE;
}
.legend-wrap .blue-purple-solid-shadow::before {
  background: #E1DEF5;
}
.legend-wrap .tulip-solid::before {
  background: #F18494;
}

.break-page {
  -moz-column-break-after: page;
       break-after: page;
}

/* Utlities */
.pt-5 {
  padding-top: 50px !important;
}

.pt-4 {
  padding-top: 40px !important;
}

.pt-3 {
  padding-top: 30px !important;
}

.pt-2 {
  padding-top: 20px !important;
}

.pt-1 {
  padding-top: 10px !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pb-4 {
  padding-bottom: 40px;
}

.pb-5 {
  padding-bottom: 50px;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pa-10 {
  padding: 10px;
}

.mb-5 {
  margin-bottom: 50px !important;
}

.mb-4 {
  margin-bottom: 40px !important;
}

.mb-3 {
  margin-bottom: 30px !important;
}

.mb-2 {
  margin-bottom: 20px !important;
}

.mb-1 {
  margin-bottom: 10px !important;
}

.mb-1-1 {
  margin-bottom: 5px;
}

.mt-5 {
  margin-top: 50px;
}

.mx-5 {
  margin: 0 50px;
}

.mx-4 {
  margin: 0 40px;
}

.mx-3 {
  margin: 0 30px;
}

.mx-2 {
  margin: 0 20px;
}

.my-0 {
  margin: 0 0;
}

.my-1 {
  margin: 10px 0;
}

.mt-1 {
  margin-top: 10px;
}

.mt-2 {
  margin-top: 20px;
}

.ml-1 {
  margin-left: 10px;
}

.mt-0 {
  margin-top: 0;
}

.mb-0 {
  margin-bottom: 0;
}

.px-4 {
  padding: 0 4px;
}

.mx-neg-4 {
  margin-left: -4px;
  margin-right: -4px;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.justify-space-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.flex-grow-1 {
  flex: 1;
}

.is-relative {
  position: relative;
}

.stick-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
}

.fullwidth-card-row {
  display: flex;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E3E7EB;
  border-radius: 8px;
  padding: 15px;
}
.fullwidth-card-row .fullwidth-card-content {
  padding-left: 20px;
}
.fullwidth-card-row p {
  font-size: 10px;
  color: #323F4B;
  line-height: 2;
}
.fullwidth-card-row h4 {
  color: #323F4B;
  font-size: 11px;
  margin: 0 0 10px;
}
.fullwidth-card-row h5 {
  font-size: 18px;
  margin-bottom: 3px;
  margin-top: 5px;
}
.fullwidth-card-row span {
  font-size: 14px;
  color: #323F4B;
  font-weight: 600;
}

.fullwidth-card-img svg {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cards-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  grid-gap: 8px;
}

.cards-row-2 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-gap: 8px;
}

.card-col {
  background: #FFFFFF;
  border: 1px solid #E3E7EB;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 93px;
}
.card-col p {
  font-size: 10px;
}
.card-col strong {
  color: #323F4B;
  font-size: 14px;
}

/* Start Custom Table */
.custom-table {
  table-layout: fixed;
}
.custom-table * {
  color: #fff;
}
.custom-table th {
  font-size: 11px;
}
.custom-table td {
  font-size: 11px;
}
.custom-table td, .custom-table th {
  vertical-align: middle;
  text-align: center;
  padding: 9px;
}

.navy-blue-bg {
  background: #0F4C6A;
}

.blue-bg {
  background: #0B5E86;
}

.sky-blue-bg {
  background: #459ED8;
}

.green-bg {
  background: #6C8A32;
}

.yellow-bg {
  background: #D3AA39;
}

.orange-bg {
  background: #B4592C;
}

.red-bg {
  background: #9A3532;
}

.title-plain {
  color: #0B5E86;
}

.donut-wrapper-sm {
  height: 45px;
  width: 45px;
}/*# sourceMappingURL=visitreport.css.map */