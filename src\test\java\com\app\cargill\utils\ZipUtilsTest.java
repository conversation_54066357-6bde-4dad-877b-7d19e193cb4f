/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.constants.ExportFileExtensions;
import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ZipUtilsTest {
  @Test
  void zipFiles() throws IOException {
    Assertions.assertNotNull(
        ZipUtils.zipFiles(
            Collections.singletonMap("test", new byte[0]),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Test
  void zipIfMultipleFiles() throws IOException {
    Assertions.assertNotNull(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap("test", new byte[0]),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Test
  void zipIfMultipleFilesReturnSuccess() throws IOException {
    Map<String, byte[]> imageTemplates = new HashMap<>();
    imageTemplates.put("test", new byte[0]);
    imageTemplates.put("test1", new byte[0]);
    Assertions.assertNotNull(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Test
  void whenNoPngAndZipIfMultipleFilesReturnNull() throws IOException {
    Map<String, byte[]> imageTemplates = new HashMap<>();
    Assertions.assertNull(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }
}
