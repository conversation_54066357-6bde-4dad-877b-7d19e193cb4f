/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.document.SilageDocument;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "Silages")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
public class Silages extends BaseEntity {

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb") // or, json
  private SilageDocument silageDocument;
}
