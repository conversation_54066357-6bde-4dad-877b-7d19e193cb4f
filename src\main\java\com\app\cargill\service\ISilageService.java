/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.SilageDto;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;

public interface ISilageService {

  Page<SilageDto> getAllSilagesPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime);

  List<SilageDto> save(SilageDto silageDto);

  List<SilageDto> update(SilageDto silageDto);
}
