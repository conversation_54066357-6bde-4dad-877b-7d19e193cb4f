/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.admin;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.document.UserDocument;
import com.app.cargill.dto.admin.User;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.SalesForceUser;
import com.app.cargill.model.UserDailyLogin;
import com.app.cargill.repository.SalesForceUsersRepository;
import com.app.cargill.repository.UserDailyLoginRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.sf.cc.service.LiftUserService;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class UserAdminServiceTest {

  @Mock private UserRepository userRepository;
  @Mock private SalesForceUsersRepository salesForceUsersRepository;
  @Mock private UserDailyLoginRepository userDailyLoginRepository;
  @Mock private LiftUserService liftUserService;

  @InjectMocks private UserAdminService userAdminService;

  @Test
  void whenGetAllUsersIsCalledDataIsReturned() {
    com.app.cargill.model.User user1 =
        new com.app.cargill.model.User(UserDocument.builder().build());
    com.app.cargill.model.User user2 =
        new com.app.cargill.model.User(UserDocument.builder().build());

    when(userRepository.findAllWithDeleted()).thenReturn(List.of(user1, user2));
    List<User> result = userAdminService.getAllUsers();
    assertEquals(2, result.size());
  }

  @Test
  void whenSingleUserDetailsIsCalledDataIsReturned() {
    com.app.cargill.model.User user =
        new com.app.cargill.model.User(UserDocument.builder().build());

    when(userRepository.findByUuid(any())).thenReturn(user);
    assertNotNull(userAdminService.getUser(UUID.randomUUID()));
  }

  @Test
  void whenSingleUserDetailsAnUserNotFoundExceptionIsThrown() {
    com.app.cargill.model.User user =
        new com.app.cargill.model.User(UserDocument.builder().build());

    when(userRepository.findByUuid(any())).thenReturn(null);
    assertThrows(NotFoundDEException.class, () -> userAdminService.getUser(UUID.randomUUID()));
  }

  @Test
  void whenGetUsersDetailFromLiftIsCalledDataIsReturned() {
    com.app.cargill.model.User user1 =
        new com.app.cargill.model.User(UserDocument.builder().build());
    com.app.cargill.model.User user2 =
        new com.app.cargill.model.User(UserDocument.builder().build());

    com.app.cargill.sf.cc.model.simple.User sfUser = new com.app.cargill.sf.cc.model.simple.User();
    sfUser.setEmail("<EMAIL>");

    when(userRepository.findAllUsersforLiftDeactive()).thenReturn(List.of(user1, user2));
    when(liftUserService.fetchUserEmailData(any())).thenReturn(List.of(sfUser));
    when(salesForceUsersRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    Flux<SalesForceUser> result = userAdminService.updateLiftUsersData();
    StepVerifier.create(result).expectNextCount(2).verifyComplete();
  }

  @Test
  void whenUserIsAddedResultIsReturned() {
    when(userRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    User result = userAdminService.addUser("<EMAIL>", "US", "User 1");
    assertEquals(result.getEmail(), "<EMAIL>");
  }

  @Test
  void whenUserIsAddedResultIsReturned_2() {
    when(userRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    User result = userAdminService.addUser("<EMAIL>", "UK", "User 1");
    assertEquals(result.getEmail(), "<EMAIL>");
  }

  @Test
  void whenUserIsAddedResultIsReturned_3() {
    when(userRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    User result = userAdminService.addUser("<EMAIL>", "United Kingdom", "User 1");
    assertEquals(result.getEmail(), "<EMAIL>");
  }

  @Test
  void whenUserEmailIsEmptyErrorIsThrown() {
    UserAdminException ex =
        assertThrows(UserAdminException.class, () -> userAdminService.addUser("", "US", "User 1"));
    assertTrue(ex.getMessage().contains("Email is required"));
  }

  @Test
  void whenUserCountryIsEmptyErrorIsThrown() {
    UserAdminException ex =
        assertThrows(
            UserAdminException.class, () -> userAdminService.addUser("test", null, "User 1"));
    assertTrue(ex.getMessage().contains("Country is required"));
  }

  @Test
  void whenUserCountryIsNotSupportedErrorIsThrown() {
    UserAdminException ex =
        assertThrows(
            UserAdminException.class,
            () -> userAdminService.addUser("test", "Non-existing", "User 1"));
    assertTrue(ex.getMessage().contains("Unsupported country"));
  }

  @Test
  void whenGetUsersAllLoginIsCalledDataIsReturned() {
    com.app.cargill.model.User user1 =
        new com.app.cargill.model.User(UserDocument.builder().build());
    com.app.cargill.model.User user2 =
        new com.app.cargill.model.User(UserDocument.builder().build());

    when(userRepository.findAllUsersRecentUsed(any(), any())).thenReturn(List.of(user1, user2));

    when(userDailyLoginRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    List<UserDailyLogin> result = userAdminService.getUsersAllLogin(any(), any());
    assertNotNull(result);
  }

  @Test
  void whenGetUsersAllLoginIsCalledDbExists() {
    com.app.cargill.model.User user1 =
        new com.app.cargill.model.User(UserDocument.builder().build());
    com.app.cargill.model.User user2 =
        new com.app.cargill.model.User(UserDocument.builder().build());
    when(userRepository.findAllUsersRecentUsed(any(), any())).thenReturn(List.of(user1, user2));
    when(userDailyLoginRepository.findByEmail(any())).thenReturn(new UserDailyLogin());
    when(userDailyLoginRepository.save(any())).thenAnswer(i -> i.getArgument(0));
    List<UserDailyLogin> result = userAdminService.getUsersAllLogin(any(), any());
    assertNotNull(result);
  }
}
