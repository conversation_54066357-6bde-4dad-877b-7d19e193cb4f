/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint.constant;

import lombok.AllArgsConstructor;

@SuppressWarnings("java:S1118")
@AllArgsConstructor
public class Constants {

  /** The Constant APP_ID_CAN. */
  public static final String APP_ID_CAN = "can";

  /** The Constant PARAM_GRANT_TYPE. */
  public static final String PARAM_GRANT_TYPE = "grant_type";

  /** The Constant PARAM_CLIENTID. */
  public static final String PARAM_CLIENTID = "client_id";

  /** The Constant PARAM_CLIENTSECRET. */
  public static final String PARAM_CLIENTSECRET = "client_secret";

  /** The Constant PARAM_RESOURCE. */
  public static final String PARAM_RESOURCE = "resource";

  /** The Constant PARAM_SCOPE. */
  public static final String PARAM_SCOPE = "scope";

  /** The Constant PARAM_RESPONSE_TYPE. */
  public static final String PARAM_RESPONSE_TYPE = "response_type";

  /** The Constant PARAM_USERNAME. */
  public static final String PARAM_USERNAME = "username";

  /** The Constant PARAM_PASSWORD. */
  public static final String PARAM_PASSWORD = "password";

  /** The Constant SUCCESS literal. */
  public static final String SUCCESS = "success";

  /*The Constant FAILED literal */
  public static final String FAILED = "failed";

  /** The Constant STATUS_CODE literal. */
  public static final String STATUS_CODE = "status_code";

  /** The Constant KEY_TYPE_STRING literal. */
  public static final String KEY_TYPE_STRING = "String";

  /** The Constant KEY_TYPE_INTEGER literal. */
  public static final String KEY_TYPE_INTEGER = "Integer";

  public static final String CLOSE = "close";

  /** The Constant MESSAGE. */
  public static final String MESSAGE = "message";

  /** The Constant AUTHORIZATION. */
  public static final String AUTHORIZATION = "Authorization";

  /** The Constant CONTENT_TYPE. */
  public static final String CONTENT_TYPE = "Content-Type";

  /** The Constant ACCEPT. */
  public static final String ACCEPT = "Accept";

  /** The Constant ACCESS_TOKEN_KEY. */
  public static final String ACCESS_TOKEN_KEY = "access_token";

  /** The Constant ID_TOKEN. */
  public static final String ID_TOKEN = "idToken";

  /** The Constant ID_TOKEN_KEY. */
  public static final String ID_TOKEN_KEY = "id_token";

  /** The Constant APPLICATION_JSON_ODATA_VERBOSE. */
  public static final String APPLICATION_JSON_ODATA_VERBOSE = "application/json;odata=verbose";

  /** The Constant BINARY_STRING_REQUEST_BODY. */
  public static final String BINARY_STRING_REQUEST_BODY = "binaryStringRequestBody";

  /** The Constant ROLE_NOT_AVAILABLE. */
  public static final String ROLE_NOT_AVAILABLE = "Authorization Failed Due To ROLE";

  /** The Constant HTTP_STATUS_401. */
  public static final int HTTP_STATUS_401 = 401;

  /** The Constant ROLE_NOT_VALID. */
  public static final String ROLE_NOT_VALID = "1010";

  /** The Constant TOKEN_TYPE. */
  public static final String TOKEN_TYPE = "Bearer ";

  /** The Constant MAX_RETRIES. */
  public static final int MAX_RETRIES = 3;

  /*Redis constants*/

  /*account azure to lift date */
  public static final String ACCOUNT_AZURE_TO_LIFT_TIMESTAMP = "account_azure_to_lift_timestamp";

  /*account azure to lift job status */
  public static final String ACCOUNT_AZURE_TO_LIFT_JOB_STATUS = "account_azure_to_lift_job_status";

  /*account azure to lift count status */
  public static final String ACCOUNT_AZURE_TO_LIFT_COUNT_STATUS =
      "account_azure_to_lift_count_status";

  public static final String ACCOUNT_AZURE_TO_LIFT_IS_RUNNING_FIRST_TIME =
      "account_azure_to_lift_is_running_first_time";

  /*site visit azure to lift date */
  public static final String SITE_VISIT_AZURE_TO_LIFT_TIMESTAMP =
      "site_visit_azure_to_lift_timestamp";

  /*site visit  azure to lift job status */
  public static final String SITE_VISIT_AZURE_TO_LIFT_JOB_STATUS =
      "site_visit_azure_to_lift_job_status";

  /*site visit azure to lift count status */
  public static final String SITE_VISIT_AZURE_TO_LIFT_COUNT_STATUS =
      "site_visit_azure_to_lift_count_status";

  public static final String SITE_VISIT_AZURE_TO_LIFT_IS_RUNNING_FIRST_TIME =
      "site_visit_azure_to_lift_is_running_first_time";

  /*activity azure to lift date */
  public static final String ACTIVITY_AZURE_TO_LIFT_TIMESTAMP = "activity_azure_to_lift_timestamp";

  /*activity azure to lift job status */
  public static final String ACTIVITY_AZURE_TO_LIFT_JOB_STATUS =
      "activity_azure_to_lift_job_status";

  /*activity azure to lift count status */
  public static final String ACTIVITY_AZURE_TO_LIFT_COUNT_STATUS =
      "activity_azure_to_lift_count_status";

  public static final String ACTIVITY_AZURE_TO_LIFT_IS_RUNNING_FIRST_TIME =
      "activity_azure_to_lift_is_running_first_time";

  /*site_report azure to lift date */
  public static final String SITE_REPORT_AZURE_TO_LIFT_TIMESTAMP =
      "site_report_azure_to_lift_timestamp";

  /*site_report azure to lift job status */
  public static final String SITE_REPORT_AZURE_TO_LIFT_JOB_STATUS =
      "site_report_azure_to_lift_job_status";

  /*site_report azure to lift count status */
  public static final String SITE_REPORT_AZURE_TO_LIFT_COUNT_STATUS =
      "site_report_azure_to_lift_count_status";

  public static final String SITE_REPORT_AZURE_TO_LIFT_IS_RUNNING_FIRST_TIME =
      "site_report_azure_to_lift_is_running_first_time";

  /*account lift_to_azure date */
  public static final String ACCOUNT_LIFT_TO_AZURE_TIMESTAMP = "account_lift_to_azure_timestamp";

  /*account lift_to_azure job status */
  public static final String ACCOUNT_LIFT_TO_AZURE_JOB_STATUS = "account_azure_to_lift_job_status";

  /*account lift_to_azure count status */
  public static final String ACCOUNT_LIFT_TO_AZURE_COUNT_STATUS =
      "account_lift_to_azure_count_status";

  public static final String ACCOUNT_LIFT_TO_AZURE_IS_RUNNING_FIRST_TIME =
      "account_lift_to_azure_is_running_first_time";

  /*site visit lift_to_azure date */
  public static final String SITE_VISIT_LIFT_TO_AZURE_TIMESTAMP =
      "site_visit_lift_to_azure_timestamp";

  /*site visit  lift_to_azure job status */
  public static final String SITE_VISIT_LIFT_TO_AZURE_JOB_STATUS =
      "site_visit_lift_to_azure_job_status";

  /*site visit lift_to_azure count status */
  public static final String SITE_VISIT_LIFT_TO_AZURE_COUNT_STATUS =
      "site_visit_lift_to_azure_count_status";

  public static final String SITE_VISIT_LIFT_TO_AZURE_IS_RUNNING_FIRST_TIME =
      "site_visit_lift_to_azure_is_running_first_time";

  /** The Constant BROWSER. */
  public static final String BROWSER =
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
          + "(KHTML, like Gecko) Chrome/54.0.2840.99 Safari/537.36";

  /** The Constant CC. */
  public static final String CC = "cc";

  /** The Constant TO. */
  public static final String TO = "to";
  /** The Constant FROM. */
  public static final String FROM = "from";

  /** The Constant SUBJECT. */
  public static final String SUBJECT = "subject";
  /** The Constant BODY. */
  public static final String BODY = "body";

  /** The Constant RUNTIME_EXCEPTION. */
  public static final String RUNTIME_EXCEPTION = "2007";

  /** The Constant COMMA STRING. */
  public static final char COMMA_STRING = ',';

  /** The Constant PARSING_ERROR. */
  public static final String PARSING_ERROR = "Json Parsing Exception occured";

  /** The Constant RESPONSE. */
  public static final String RESPONSE = "response";
}
