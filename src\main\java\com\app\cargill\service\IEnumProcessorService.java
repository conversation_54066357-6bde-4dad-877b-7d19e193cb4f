/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.EnumSerializerDto;
import com.app.cargill.dto.MultilingualEnumSerializerDto;
import org.springframework.context.support.ResourceBundleMessageSource;

public interface IEnumProcessorService {

  EnumSerializerDto fetchEnums();

  MultilingualEnumSerializerDto fetchEnumsMultilingual(
      ResourceBundleMessageSource resourceBundleMessageSource);
}
