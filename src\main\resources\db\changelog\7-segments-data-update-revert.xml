<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
	xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="005" author="Aweem">
		<sql>
			TRUNCATE TABLE segments;
			INSERT INTO public.segments(segment_document)
			VALUES
			('{"key":1, "value":"<PERSON>","default":false}'),
			('{"key":9, "value":"Baxter","default":false}'),
			('{"key":8, "value":"Dennis","default":false}'),
			('{"key":10, "value":"End-User","default":false}'),
			('{"key":4, "value":"Kobe","default":false}'),
			('{"key":2, "value":"<PERSON><PERSON>","default":false}'),
			('{"key":6, "value":"Noah","default":true}'),
			('{"key":3, "value":"Spence","default":false}'),
			('{"key":5, "value":"Walton","default":false}'),
			('{"key":7, "value":"Sonya","default":false}');
		</sql>
	</changeSet>

</databaseChangeLog>