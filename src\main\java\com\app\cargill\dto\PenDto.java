/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.FeedingSystem;
import com.app.cargill.constants.HousingSystem;
import com.app.cargill.constants.OptimizationType;
import com.app.cargill.constants.PenSource;
import com.app.cargill.constants.ResponseStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S1068") // false positive for unused fields
/// <summary>
/// A pen is contained within a barn.
/// </summary>
public class PenDto {
  // region general

  private UUID id;

  private String localId;
  private UUID accountId;

  private UUID siteId;

  private UUID barnId;

  // @Enumerated(EnumType.ORDINAL)
  private PenSource source;

  @NotNull private String name;

  private String barnName;

  private HousingSystem housingSystemType;

  private FeedingSystem feedingSystemType;

  /// Required if the HousingSystem is Type Freestall or TieStall

  private Integer numberOfStalls;

  /// accept decimal (2 decimal places) values from 0 to 9.
  /// accepts decimal (1 decimal place) inputs, including zero (0),
  /// and displays either kg label for Metric or lbs for Imperial unit of measure.
  private Double milkingFrequency;

  // endregion
  // region animals input
  /// accepts positive whole number inputs
  private Integer animals;

  /// accepts both positive and negative whole number inputs
  private Integer daysInMilk;

  /// This is the milk yield.
  /// accepts decimal (1 decimal place) inputs, including zero (0),
  /// and displays either kg label for Metric or lbs for Imperial UoM.
  private Double milk;

  // endregion
  // region diet
  /// The diet name and animal class can be derived from the dietId
  private UUID dietId;

  /// accepts decimal (1 decimal place) inputs, and displays either
  /// kg label for Metric or lbs for Imperial unit of measure.
  private Double dryMatterIntake;

  /// accepts decimal (1 decimal place) inputs, and displays either kg
  ///  label for Metric or lbs for Imperial unit of measure
  private Double asFedIntake;

  /// accepts currency(2 decimal places) inputs
  private Double rationCostPerAnimal;

  // endregion
  @Builder.Default private Boolean isDeleted = false;

  @Builder.Default private Boolean isMapped = false;

  private List<UUID> associatedPens;

  private String createUser;
  private String netEnergyOfLactationDairy;
  private DietSource dietSource;
  private OptimizationType optimizationType;
  private String groupId;
  private Boolean selected;
  private Instant updatedDate;
  @NotNull private UUID animalClassId;
  private ResponseStatus status;
  private String message;
  private Instant dDWLastUpdatedDate;
}
