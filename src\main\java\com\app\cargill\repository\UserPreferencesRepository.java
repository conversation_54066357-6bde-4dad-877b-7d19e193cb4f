/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.UserPreferences;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserPreferencesRepository extends JpaRepository<UserPreferences, Long> {

  @Query(
      value =
          "Select * from User_Preferences u where LOWER(u.user_preference_document->>'UserId') ="
              + " LOWER(:email) and u.deleted = false",
      nativeQuery = true)
  UserPreferences findByUserId(@Param("email") String email);

  boolean existsByLocalId(String localId);

  @Query(
      value =
          "Select * from User_Preferences u where u.user_preference_document->>'id' = :id and"
              + " u.deleted = false",
      nativeQuery = true)
  UserPreferences findbyUUID(String id);

  @Query(
      value =
          "select count(*) > 0 from User_preferences  where"
              + " LOWER(user_preference_document->>'UserId')= LOWER(:currentLoggedInUser) AND"
              + " deleted=false",
      nativeQuery = true)
  boolean existsByUserId(@Param("currentLoggedInUser") String currentLoggedInUser);
}
