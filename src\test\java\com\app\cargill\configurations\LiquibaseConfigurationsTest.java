/* Cargill Inc.(C) 2022 */
package com.app.cargill.configurations;

import java.sql.ResultSet;
import java.sql.SQLException;
import javax.sql.DataSource;
import liquibase.exception.LiquibaseException;
import liquibase.exception.UnexpectedLiquibaseException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiquibaseConfigurationsTest {
  @Mock DataSource dataSource;
  @Mock ResultSet resultSet;
  @InjectMocks LiquibaseConfigurations liquibaseConfigurations;

  @Test
  void whenLiquibaseReturnsSuccess() throws LiquibaseException, SQLException {
    Assertions.assertNotNull(liquibaseConfigurations.liquibaseProperties());
    Assertions.assertNotNull(liquibaseConfigurations.initSpringLiquibase());
  }

  @Test
  void whenRunLiquibaseThrowsNullPointerException() {
    Assertions.assertNotNull(liquibaseConfigurations.liquibaseProperties());
    Assertions.assertThrows(
        UnexpectedLiquibaseException.class,
        () -> liquibaseConfigurations.runLiquibaseMigration(),
        "failed");
  }
}
