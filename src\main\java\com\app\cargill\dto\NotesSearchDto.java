/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class NotesSearchDto {

  private String title;
  private Instant creationDateFrom;
  private Instant creationDateTo;

  private Instant updatedDateFrom;
  private Instant updatedDateTo;

  private List<String> accountIds;
  private List<String> siteIds;
  private List<String> visitIds;
  private List<String> tools;
  private List<String> noteCategoryTypes;
}
