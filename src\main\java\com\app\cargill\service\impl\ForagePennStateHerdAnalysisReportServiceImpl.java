/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.*;
import com.app.cargill.dto.ForagePennStateOnScreenPercentageDto;
import com.app.cargill.dto.ForagePennStateReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("foragePennStateReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class ForagePennStateHerdAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;
  private List<byte[]> onCostPercentageColors =
      Arrays.asList(
          new byte[] {(byte) 122, (byte) 216, (byte) 220},
          new byte[] {(byte) 131, (byte) 190, (byte) 244},
          new byte[] {(byte) 171, (byte) 161, (byte) 227},
          new byte[] {(byte) 241, (byte) 132, (byte) 148});

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    ForagePennStateReportDto dto = modelMapper.map(data, ForagePennStateReportDto.class);
    try (XSSFWorkbook foragePennStateWB = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              foragePennStateWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(foragePennStateWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              foragePennStateWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(foragePennStateWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              foragePennStateWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(foragePennStateWB, false, true, IndexedColors.BLACK));

      // forage penn state
      XSSFSheet sheet =
          addForagePennStateHerdAnalysisSheet(
              foragePennStateWB, source, locale, dto, boldStyle, greyCellStyle, centerBlack);
      int totalSheetColumns = sheet.getLastRowNum();
      return ExcelUtils.finalizeWorkbook(foragePennStateWB, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  private XSSFSheet addForagePennStateHerdAnalysisSheet(
      XSSFWorkbook foragePennStateWB,
      ResourceBundleMessageSource source,
      Locale locale,
      ForagePennStateReportDto foragePennStateDto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(
            foragePennStateWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);
    decimalStyle.setDataFormat(
        foragePennStateWB.createDataFormat().getFormat("0.00%")); // percentage number format
    XSSFSheet foragePennStateWBSheet =
        foragePennStateWB.createSheet(
            ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, "Report", null, source, locale)
                .replace("/", "⧸"));
    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    prepareHeader(
        foragePennStateWB,
        foragePennStateWBSheet,
        rowNumber,
        cellNumber,
        foragePennStateDto,
        boldStyle,
        locale);

    // create the data
    // calculated table heading
    XSSFRow row;
    if (!StringUtils.isBlank(foragePennStateDto.getScorerLabel())) {
      cellNumber.set(0);
      row = foragePennStateWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row, cellNumber, greyCellStyle, foragePennStateDto.getScorerLabel());
      foragePennStateWBSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));
    }
    XSSFCellStyle blueCellStyle =
        ExcelUtils.applyCellStyle(
            foragePennStateWB,
            IndexedColors.GREY_25_PERCENT,
            FillPatternType.SOLID_FOREGROUND,
            HorizontalAlignment.CENTER,
            ExcelUtils.getFont(foragePennStateWB, false, true, IndexedColors.ROYAL_BLUE));
    cellNumber.set(0);
    row = foragePennStateWBSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, blueCellStyle, foragePennStateDto.getPspsLabel());
    foragePennStateWBSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // setting visit date first
    int visitDateStartRowNumber = rowNumber.get();
    cellNumber.set(1);
    row = foragePennStateWBSheet.createRow(rowNumber.getAndIncrement());

    for (ForagePennStateOnScreenPercentageDto onScreenPercentage :
        foragePennStateDto.getOnScreenPercentage()) {
      // set date in first cell
      ExcelUtils.highlightEmptyCell(
          row, onScreenPercentage.getVisitDate(), cellNumber, centerBlack, greyCellStyle);
    }

    // forage penn state

    int onScreenPercentageStartRowNumber = rowNumber.get();
    // first rest cell and create row
    int goalSize = 4;

    Double value = null;
    for (int goalIndex = 0; goalIndex < goalSize; goalIndex++) {
      cellNumber.set(0);
      row = foragePennStateWBSheet.createRow(rowNumber.getAndIncrement());

      if (goalIndex == 0) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_TOP, "Top", null, source, locale));
        for (ForagePennStateOnScreenPercentageDto onScreenPercentage :
            foragePennStateDto.getOnScreenPercentage()) {
          value = onScreenPercentage.getTop() == null ? null : onScreenPercentage.getTop() / 100;
          ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 1) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_MID_1, "Mid 1 (8 mm)", null, source, locale));
        for (ForagePennStateOnScreenPercentageDto onScreenPercentage :
            foragePennStateDto.getOnScreenPercentage()) {
          value = onScreenPercentage.getMid1() == null ? null : onScreenPercentage.getMid1() / 100;
          ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
        }

      } else if (goalIndex == 2) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_MID_2, "Mid 2 (4 mm)", null, source, locale));
        for (ForagePennStateOnScreenPercentageDto onScreenPercentage :
            foragePennStateDto.getOnScreenPercentage()) {
          value = onScreenPercentage.getMid2() == null ? null : onScreenPercentage.getMid2() / 100;
          ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 3) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(LangKeys.REPORT_TRAY, "Tray", null, source, locale));
        for (ForagePennStateOnScreenPercentageDto onScreenPercentage :
            foragePennStateDto.getOnScreenPercentage()) {
          value = onScreenPercentage.getTray() == null ? null : onScreenPercentage.getTray() / 100;
          ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
        }
      }
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    // int columnStart = 1;
    int columnEnd = columnStart + foragePennStateDto.getOnScreenPercentage().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFBarChartData dataLeft;
    Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first Bar chart======================
    chart =
        ExcelUtils.initChart(
            foragePennStateWBSheet,
            ExcelUtils.getLangValue(LangKeys.REPORT_ON_SCREEN_PERCENTAGE, null, source, locale),
            chartCol0,
            3,
            chartCol0 + 10,
            23);

    ExcelUtils.initLegends(chart);

    bottomAxis =
        ExcelUtils.createBottomAxis(
            chart, ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

    leftAxis =
        ExcelUtils.createLeftAxis(
            chart,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_ON_SCREEN_PERCENTAGE, "On Screen %", null, source, locale));
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
    // create chart data
    dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);
    // create series
    XDDFDataSource<String> visitDatesDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            foragePennStateWBSheet,
            new CellRangeAddress(
                visitDateStartRowNumber, visitDateStartRowNumber, columnStart, columnEnd));
    for (int ds = 0; ds < goalSize; ds++) {
      XDDFNumericalDataSource<Double> onScreenPercentDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              foragePennStateWBSheet,
              new CellRangeAddress(
                  onScreenPercentageStartRowNumber,
                  onScreenPercentageStartRowNumber,
                  columnStart,
                  columnEnd));
      series = (Series) dataLeft.addSeries(visitDatesDataSource, onScreenPercentDataSource);
      series.setTitle(
          foragePennStateWBSheet
              .getRow(onScreenPercentageStartRowNumber)
              .getCell(0)
              .getStringCellValue(),
          new CellReference(
              foragePennStateWBSheet.getSheetName(),
              onScreenPercentageStartRowNumber++,
              0,
              true,
              true));
    }
    chart.plot(dataLeft);
    ExcelUtils.setBarColorInBarChart(chart, onCostPercentageColors, goalSize);

    return foragePennStateWBSheet;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    ForagePennStateReportDto mappedDto = modelMapper.map(data, ForagePennStateReportDto.class);

    Map<String, byte[]> imageTemplates = new HashMap<>();

    // create sheet 1
    byte[] foragePenState =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.FORAGE_PENN_STATE_HERD_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            LangKeys.REPORT_FORAGE_PEN_STATE, "Forage Penn State", null, source, locale),
        foragePenState);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    ForagePennStateReportDto dto = modelMapper.map(data, ForagePennStateReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.FORAGE_PENN_STATE_HERD_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook foragePennStateWorkBook,
      XSSFSheet foragePennStateSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      ForagePennStateReportDto foragePennStateReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        foragePennStateWorkBook,
        foragePennStateSheet,
        rowNum.get(),
        cellNum.getAndIncrement());
    // headings
    XSSFRow row = foragePennStateSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, foragePennStateReportDto.getVisitName());
    foragePennStateSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, foragePennStateReportDto.getVisitDate());
    foragePennStateSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = foragePennStateSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, foragePennStateReportDto.getToolName());
    foragePennStateSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, foragePennStateReportDto.getAnalysisType());
    foragePennStateSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));
  }
}
