/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Barns are contained within a site. */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitReportBodyConditionScoreDto {

  private String bcsPointScale;
  private List<VisitReportAnimalDetailsDto> animalAnalysis;
  private List<VisitReportBCSPenAnalysisDto> penAnalysis;
  private VisitReportBCSHerdAnalysisDto herdAnalysis;
  private List<NotesDto> notes;
}
