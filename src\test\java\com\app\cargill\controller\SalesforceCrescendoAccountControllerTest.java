/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.sf.crescendo.model.AccountCrescendo;
import com.app.cargill.sf.crescendo.service.CrescendoAccountService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoAccountControllerTest {

  @Mock private CrescendoAccountService accountService;
  @InjectMocks private SalesforceCrescendoAccountController controller;

  @Test
  void whenGetAccountsIsCalledCorrectResponseIsReturned() {
    when(accountService.getAccountsForSync(any())).thenReturn(new ArrayList<>());
    List<AccountCrescendo> result = controller.getAccounts(Instant.now());
    assertNotNull(result);
  }

  @Test
  void whenAccountsAreProvidedCorrectResponseIsReturned() {
    ResponseEntity<Void> result = controller.upsertAccounts(new ArrayList<>());
    assertEquals(HttpStatus.ACCEPTED, result.getStatusCode());
  }

  @Test
  void readWriteJson() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    List<AccountCrescendo> acc =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_app_account.json"),
            new TypeReference<>() {});
    assertDoesNotThrow(
        () -> {
          controller.upsertAccounts(acc);
        });
  }
}
