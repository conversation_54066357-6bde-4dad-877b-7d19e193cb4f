/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static com.app.cargill.utils.PageableUtil.getPageable;

import com.app.cargill.constants.*;
import com.app.cargill.document.*;
import com.app.cargill.dto.DuplicatePenIdDTO;
import com.app.cargill.dto.PenDto;
import com.app.cargill.dto.PenGroupingDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.model.tasks.DuplicatePens;
import com.app.cargill.repository.*;
import com.app.cargill.service.IPensService;
import com.app.cargill.service.IUserService;
import java.time.Instant;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service("pensServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings("java:S125") // remove when all commented code is removed
public class PensServiceImpl implements IPensService {

  private final IUserService userServiceImpl;

  private final SitesRepository sitesRepository;

  private final PensRepository pensRepository;
  private final AccountsRepository accountsRepository;
  private final DietRepository dietRepository;
  private final VisitsRepository visitsRepository;

  @Override
  public PageImpl<PenDto> getAllPensByCurrentLoggedInUser(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting) {

    Pageable pageable = getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    Page<Pens> pens =
        pensRepository.findByCustomerAccountIdAndUpdatedDate(
            accountIdsByUser, lastSyncTime, pageable);
    if (Objects.isNull(pens)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        pens.stream().parallel().map(this::modelToDto).toList(), pageable, pens.getTotalElements());
  }

  @Override
  public PageImpl<PenDto> getAllPensBySiteId(
      String siteId, int page, int size, String sortBy, Instant lastSyncTime, String sorting) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);

    Page<Pens> pens = pensRepository.findBySiteIdAndUpdatedDate(siteId, lastSyncTime, pageable);
    if (Objects.isNull(pens)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        pens.stream().parallel().map(this::modelToDto).toList(), pageable, pens.getTotalElements());
  }

  @Override
  public PenGroupingDto save(PenGroupingDto pensDtos) throws NotFoundDEException {
    Sites site = sitesRepository.findBySiteId(pensDtos.getSiteId().toString());
    if (site == null) {
      throw new NotFoundDEException("Site with id = " + pensDtos.getSiteId() + " not found");
    }

    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUser();
    List<PenDto> updatedPenDtoList =
        pensDtos.getPens().parallelStream()
            .map(penDto -> processSaveDto(penDto, site, currentLoggedUser))
            .toList();
    pensDtos.setPens(updatedPenDtoList);

    site.setUpdatedDate(
        Date.from(Instant.now())); // This time is updated to handle sending data to CDP
    sitesRepository.save(site);

    return pensDtos;
  }

  @Override
  public PenGroupingDto update(PenGroupingDto pensDtos) throws NotFoundDEException {
    Sites site = sitesRepository.findBySiteId(pensDtos.getSiteId().toString());
    if (site == null) {
      throw new NotFoundDEException("Site with id = " + pensDtos.getSiteId() + " not found");
    }
    List<PenDto> updatedModels =
        pensDtos.getPens().parallelStream().map(this::processUpdateDto).toList();
    pensDtos.setPens(updatedModels);
    site.setUpdatedDate(
        Date.from(Instant.now())); // This time is updated to handle sending data to CDP
    sitesRepository.save(site);

    return pensDtos;
  }

  @Override
  public Double getNetEnergyOfLactationDairy(List<Pens> pens) throws CustomDEExceptions {
    log.debug("[PensServiceImpl][getNetEnergyOfLactationDairy] Entering...");
    int count = 0;
    double total = 0.0;
    try {
      for (Pens p : pens) {
        log.debug(
            "[PensServiceImpl][getNetEnergyOfLactationDairy] Processing pen: "
                + p.getPenDocument().getId());
        PenDocument pen = p.getPenDocument();
        if (pen.getDietId() != null) {
          log.debug(
              "[PensServiceImpl][getNetEnergyOfLactationDairy] fetching diet: " + pen.getDietId());
          Diets diet = dietRepository.findById(pen.getDietId().toString());
          if (diet != null) {
            Double nel = getNutrientOptimizationResult(diet);
            Integer animalCount = pen.getAnimals() == null ? 0 : pen.getAnimals();
            count = count + animalCount;
            total = nel * animalCount;
          }
        }
      }
    } catch (Exception e) {
      log.error("[PensServiceImpl][getNetEnergyOfLactationDairy] " + e.getMessage());
      throw new CustomDEExceptions(e.getMessage(), HttpStatus.BAD_REQUEST.value());
    }
    return count > 0 ? total / count : 0.0;
  }

  private Double getNutrientOptimizationResult(Diets diet) throws CustomDEExceptions {
    log.debug("[PensServiceImpl][getNutrientOptimizationResult] Entering... ");
    List<DietOptimizationNutrient> nutrients = null;
    if (diet.getDietDocument().getFormulateOptimization() != null
        && diet.getDietDocument().getFormulateOptimization().getStatus()
            == FormulateStatus.FEASIBLE) {
      nutrients = diet.getDietDocument().getFormulateOptimization().getNutrients();
    } else if (diet.getDietDocument().getAnalyzeOptimization() != null
        && diet.getDietDocument().getAnalyzeOptimization().getStatus() == AnalyzeStatus.UNSAFE) {
      nutrients = diet.getDietDocument().getAnalyzeOptimization().getNutrients();
    }

    if (nutrients != null) {
      DietOptimizationNutrient don =
          nutrients.stream()
              .filter(
                  n -> n.getNutrientSpeciesId().equals(Nutrients.NEL_DAIRY_KG.getNutrientValue()))
              .findFirst()
              .orElse(null);

      if (don != null) return don.getResult();
    }
    return 0.0;
  }

  private PenDto processSaveDto(PenDto penDto, Sites site, String currentLoggedUser) {
    try {

      // set to previous record id local id already exists
      List<Pens> pens = pensRepository.findByLocalId(penDto.getLocalId());
      if (!pens.isEmpty()) {
        penDto = modelToDto(pens.get(0));
        throw new AlreadyExistsDEException("local Id Already Exists");
      }
      Pens pen = dtoToModel(penDto);
      pen.getPenDocument().setCreateUser(currentLoggedUser);
      if (pen.getPenDocument().getId() == null) {
        pen.getPenDocument().setId(UUID.randomUUID());
      }
      if (pen.getPenDocument().getSiteId() == null) {
        pen.getPenDocument().setSiteId(site.getSiteDocument().getId());
      }

      if (pen.getPenDocument().getBarnId() == null
          && !site.getSiteDocument().getBarns().isEmpty()) {
        pen.getPenDocument().setBarnId(site.getSiteDocument().getBarns().get(0).getId());
      }
      // TODO : need to fix this after diet integration
      //                if (pen.getPenDocument().getDietId() == null) {
      //
      //                  // TODO
      //                }
      if (pen.getPenDocument().getCustomerAccountId() == null) {
        pen.getPenDocument().setCustomerAccountId(site.getSiteDocument().getAccountId());
      }

      Boolean isValidDiet = Boolean.TRUE;
      Diets diet = null;

      if (pen.getPenDocument() != null && pen.getPenDocument().getDietId() != null) {
        diet = dietRepository.findById(pen.getPenDocument().getDietId().toString());
      }

      if (diet != null && diet.getDietDocument().getSource().equals(DietSource.MAX)) {
        isValidDiet = dietValidation(diet, pen.getPenDocument().getOptimizationType());
      }

      pen.setDeleted(penDto.getIsDeleted() != null && penDto.getIsDeleted());
      penDto = saveAndFlushPen(pen, isValidDiet);
      penDto.setStatus(ResponseStatus.SUCCESS);
    } catch (Exception e) {
      penDto.setStatus(ResponseStatus.FAILED);
      penDto.setMessage(e.getLocalizedMessage());
      log.error("Error saving a pen object", e);
    }

    return penDto;
  }

  private PenDto processUpdateDto(PenDto penDto) {
    try {
      Pens pen = pensRepository.findByPenId(penDto.getId().toString());
      if (pen == null) {
        throw new NotFoundDEException("Pen With Given Id not Found");
      }
      Pens penToUpdate = updateModel(pen, penDto);
      penToUpdate.getPenDocument().setId(pen.getPenDocument().getId());

      penDto = modelToDto(pensRepository.saveAndFlush(penToUpdate));
      penDto.setStatus(ResponseStatus.SUCCESS);
    } catch (Exception e) {
      penDto.setStatus(ResponseStatus.FAILED);
      penDto.setMessage(e.getLocalizedMessage());
    }

    return penDto;
  }

  private PenDto modelToDto(Pens pen) {
    return PenDto.builder()
        .id(pen.getPenDocument().getId())
        .localId(pen.getLocalId())
        .accountId(pen.getPenDocument().getCustomerAccountId())
        .siteId(pen.getPenDocument().getSiteId())
        .barnId(pen.getPenDocument().getBarnId())
        .source(pen.getPenDocument().getSource())
        .name(pen.getPenDocument().getName())
        .barnName(pen.getPenDocument().getBarn())
        .housingSystemType(pen.getPenDocument().getHousingSystemType())
        .feedingSystemType(pen.getPenDocument().getFeedingSystemType())
        .numberOfStalls(pen.getPenDocument().getNumberOfStalls())
        .selected(pen.getPenDocument().getSelected())
        .groupId(pen.getPenDocument().getGroupId())
        .optimizationType(pen.getPenDocument().getOptimizationType())
        .dietSource(pen.getPenDocument().getDietSource())
        .netEnergyOfLactationDairy(pen.getPenDocument().getNetEnergyOfLactationDairy())
        .createUser(pen.getPenDocument().getCreateUser())
        .associatedPens(pen.getPenDocument().getAssociatedPens())
        .isMapped(pen.getPenDocument().getIsMapped())
        .isDeleted(pen.isDeleted())
        .rationCostPerAnimal(pen.getPenDocument().getRationCostPerAnimal())
        .asFedIntake(pen.getPenDocument().getAsFedIntake())
        .dryMatterIntake(pen.getPenDocument().getDryMatterIntake())
        .dietId(pen.getPenDocument().getDietId())
        .milk(pen.getPenDocument().getMilk())
        .daysInMilk(pen.getPenDocument().getDaysInMilk())
        .animals(pen.getPenDocument().getAnimals())
        .animalClassId(pen.getPenDocument().getAnimalClassId())
        .milkingFrequency(pen.getPenDocument().getMilkingFrequency())
        .updatedDate(pen.getUpdatedDate().toInstant())
        .dDWLastUpdatedDate(pen.getPenDocument().getDDWLastUpdatedDate())
        .build();
  }

  private Pens dtoToModel(PenDto penDto) {

    PenDocument build =
        PenDocument.builder()
            .id(penDto.getId())
            .customerAccountId(penDto.getAccountId())
            .siteId(penDto.getSiteId())
            .barnId(penDto.getBarnId())
            .source(penDto.getSource())
            .name(penDto.getName())
            .barn(penDto.getBarnName())
            .housingSystemType(penDto.getHousingSystemType())
            .feedingSystemType(penDto.getFeedingSystemType())
            .numberOfStalls(penDto.getNumberOfStalls())
            .selected(penDto.getSelected())
            .groupId(penDto.getGroupId())
            .optimizationType(penDto.getOptimizationType())
            .dietSource(penDto.getDietSource())
            .netEnergyOfLactationDairy(penDto.getNetEnergyOfLactationDairy())
            .createUser(penDto.getCreateUser())
            .associatedPens(penDto.getAssociatedPens())
            .isMapped(penDto.getIsMapped())
            .isDeleted(penDto.getIsDeleted())
            .rationCostPerAnimal(penDto.getRationCostPerAnimal())
            .asFedIntake(penDto.getAsFedIntake())
            .dryMatterIntake(penDto.getDryMatterIntake())
            .dietId(penDto.getDietId())
            .milk(penDto.getMilk())
            .animalClassId(penDto.getAnimalClassId())
            .daysInMilk(penDto.getDaysInMilk())
            .animals(penDto.getAnimals())
            .milkingFrequency(penDto.getMilkingFrequency())
            .build();

    return Pens.builder()
        .penDocument(build)
        .localId(penDto.getLocalId() == null ? null : penDto.getLocalId())
        .build();
  }

  private Pens updateModel(Pens pen, PenDto penDto) {

    // pen.getPenDocument().setId(penDto.getId());
    pen.getPenDocument().setCustomerAccountId(penDto.getAccountId());
    pen.getPenDocument().setSiteId(penDto.getSiteId());
    pen.getPenDocument().setBarnId(penDto.getBarnId());
    pen.getPenDocument().setSource(penDto.getSource());
    pen.getPenDocument().setName(penDto.getName());
    pen.getPenDocument().setBarn(penDto.getBarnName());
    pen.getPenDocument().setHousingSystemType(penDto.getHousingSystemType());
    pen.getPenDocument().setFeedingSystemType(penDto.getFeedingSystemType());
    pen.getPenDocument().setNumberOfStalls(penDto.getNumberOfStalls());
    pen.getPenDocument().setSelected(penDto.getSelected());
    pen.getPenDocument().setGroupId(penDto.getGroupId());
    pen.getPenDocument().setOptimizationType(penDto.getOptimizationType());
    pen.getPenDocument().setDietSource(penDto.getDietSource());
    pen.getPenDocument().setNetEnergyOfLactationDairy(penDto.getNetEnergyOfLactationDairy());
    pen.getPenDocument().setCreateUser(penDto.getCreateUser());
    pen.getPenDocument().setAssociatedPens(penDto.getAssociatedPens());
    pen.getPenDocument().setIsMapped(penDto.getIsMapped());
    pen.getPenDocument().setIsDeleted(penDto.getIsDeleted());
    pen.setDeleted(penDto.getIsDeleted() != null && penDto.getIsDeleted());
    pen.getPenDocument().setRationCostPerAnimal(penDto.getRationCostPerAnimal());
    pen.getPenDocument().setAsFedIntake(penDto.getAsFedIntake());
    pen.getPenDocument().setDryMatterIntake(penDto.getDryMatterIntake());
    pen.getPenDocument().setDietId(penDto.getDietId());
    pen.getPenDocument().setMilk(penDto.getMilk());
    pen.getPenDocument().setDaysInMilk(penDto.getDaysInMilk());
    pen.getPenDocument().setAnimalClassId(penDto.getAnimalClassId());
    pen.getPenDocument().setAnimals(penDto.getAnimals());
    pen.getPenDocument().setMilkingFrequency(penDto.getMilkingFrequency());
    pen.getPenDocument().setDDWLastUpdatedDate(penDto.getDDWLastUpdatedDate());

    return pen;
  }

  public Boolean dietValidation(Diets diet, OptimizationType optimizationType) {

    DietDocument dietDocument = diet.getDietDocument();

    if (dietDocument.getIsActive() != null && !dietDocument.getIsActive()) {
      return Boolean.FALSE;
    }

    if (dietDocument.getIsDeleted() != null && dietDocument.getIsDeleted()) {
      return Boolean.FALSE;
    }

    if (optimizationType != null) {

      if (optimizationType.equals(OptimizationType.ANALYZE)) {
        AnalyzeDietOptimization analyzeDiet = dietDocument.getAnalyzeOptimization();
        if (!analyzeDiet.getStatus().equals(AnalyzeStatus.ANALYZED)
            && !analyzeDiet.getStatus().equals(AnalyzeStatus.UNSAFE)) {
          return Boolean.FALSE;
        }
      } else if (optimizationType.equals(OptimizationType.FORMULATE)) {
        FormulateDietOptimization formulateDiet = dietDocument.getFormulateOptimization();
        if (!formulateDiet.getStatus().equals(FormulateStatus.FEASIBLE)) {
          return Boolean.FALSE;
        }
      }
    }
    return Boolean.TRUE;
  }

  public PenDto saveAndFlushPen(Pens pen, Boolean isValidDiet) {

    if (isValidDiet.equals(Boolean.FALSE)) {
      pen.getPenDocument().setOptimizationType(null);
      pen.getPenDocument().setOptimizationId(null);
      pen.getPenDocument().setDietId(null);
    }

    return modelToDto(pensRepository.saveAndFlush(pen));
  }

  @Override
  public List<DuplicatePenIdDTO> getAllPensDuplicate(int offset, int limit) {

    List<DuplicatePens> duplicatePensList = pensRepository.findDuplicatePens(offset);
    List<DuplicatePenIdDTO> duplicatePenIdDTOList = new ArrayList<>();

    /*  List<DuplicatePenIdDTO> duplicatePenIdDTOListRet =
    Flux.fromIterable(duplicatePensList)
        .publishOn(Schedulers.parallel())
        .flatMap(
            accDoc ->
                Mono.fromCallable(() -> findMismatchedId(accDoc, duplicatePenIdDTOList))
                    .subscribeOn(Schedulers.boundedElastic()))
        .blockFirst();*/
    for (DuplicatePens duplicatePens : duplicatePensList) {
      log.info("duplicate Pen Loop Count {} ", duplicatePens.getSiteId());
      List<String> convertedPenIds = Arrays.asList(duplicatePens.getPenIds().split(",", -1));
      DuplicatePenIdDTO duplicatePenIdDTO = new DuplicatePenIdDTO();
      List<String> visitPenIds = new ArrayList<>();

      setValues(duplicatePens, convertedPenIds, duplicatePenIdDTO);
      boolean setflag = false;
      List<Visits> visits = visitsRepository.getVisitsDocBySiteId(duplicatePens.getSiteId());
      for (String penIdTemp : convertedPenIds) {

        for (Visits visit : visits) {
          log.info(
              "visDoc SiteID {} VisitId {} ",
              visit.getVisitDocument().getSiteId(),
              visit.getVisitDocument().getId());
          String visDoc = visit.getVisitDocument().toString();
          if (visDoc.contains(penIdTemp)) {
            log.info(
                "DUPLICATE_PEN_ID_EXISTS for PenId {} ,SiteId {} ",
                penIdTemp,
                duplicatePens.getSiteId());
            setflag = true;
            visitPenIds.add(penIdTemp);
          }
        }
      }
      duplicatePenIdDTO.setVisitPenIds(visitPenIds);

      duplicatePenIdDTO.setDuplicateFlag(setflag);
      duplicatePenIdDTOList.add(duplicatePenIdDTO);
    }
    return duplicatePenIdDTOList;
  }

  /*public List<DuplicatePenIdDTO> findMismatchedId(
        DuplicatePens duplicatePens, List<DuplicatePenIdDTO> duplicatePenIdDTOList) {
      List<Visits> visits = visitsRepository.getVisitsDocBySiteId(duplicatePens.getSiteId());
      List<String> convertedPenIds = Arrays.asList(duplicatePens.getPenIds().split(",", -1));
      for (String penIdTemp : convertedPenIds) {

        DuplicatePenIdDTO duplicatePenIdDTO = new DuplicatePenIdDTO();
        duplicatePenIdDTO = setValues(duplicatePens, penIdTemp, duplicatePenIdDTO);

        for (Visits visit : visits) {
          log.info(
              "visDoc SiteID {} VisitId {} ",
              visit.getVisitDocument().getSiteId(),
              visit.getVisitDocument().getId());
          String visDoc = visit.getVisitDocument().toString();
          if (visDoc.contains(penIdTemp)) {
            log.info(
                "DUPLICATE_PEN_ID_EXISTS for PenId {} ,SiteId {} ",
                penIdTemp,
                duplicatePens.getSiteId());
            duplicatePenIdDTO.setDeleteFlag("True");
          }
        }
        duplicatePenIdDTOList.add(duplicatePenIdDTO);
      }
      return duplicatePenIdDTOList;
    }
  */
  private DuplicatePenIdDTO setValues(
      DuplicatePens duplicatePens, List<String> penIds, DuplicatePenIdDTO duplicatePenIdDTO) {
    duplicatePenIdDTO.setPenCount(duplicatePens.getPenCount());
    // duplicatePenIdDTO.setPenId(penIdTemp);
    duplicatePenIdDTO.setPenName(duplicatePens.getPenName());
    duplicatePenIdDTO.setSiteId(duplicatePens.getSiteId());
    duplicatePenIdDTO.setSource(duplicatePens.getSource());
    duplicatePenIdDTO.setPenIds(penIds);
    return duplicatePenIdDTO;
  }
}
