/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DataSynchronizerFactory {

  private final List<DataSynchronizer<?>> synchronizers;

  public <T> DataSynchronizer<T> getSynchronizer(Class<T> tClass) {
    @SuppressWarnings("unchecked")
    DataSynchronizer<T> synchronizer =
        (DataSynchronizer<T>)
            synchronizers.stream()
                .filter(s -> s.getType().equals(tClass))
                .findAny()
                .orElseThrow(() -> new RuntimeException("Can't find suitable synchronizer"));
    return synchronizer;
  }
}
