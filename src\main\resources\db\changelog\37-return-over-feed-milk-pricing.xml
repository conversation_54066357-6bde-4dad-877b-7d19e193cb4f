<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="037" author="Taha">
		<sql>
		
		
			INSERT INTO return_over_feed_pricings (
    created_date, deleted, local_id, updated_date, pricing_return_over_feed_document
) VALUES (
    CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP,
    jsonb_build_object(
        'id', 'f1b8f5d4-b4e7-4f1a-9a88-79e3a3c6d50a',
        'Price', 0.0659,
        'Name', 'Deductions, $/l',
        'ReturnOverFeedType', 'MILK_PRICE_PER_TON'
    )
);

INSERT INTO return_over_feed_pricings (
    created_date, deleted, local_id, updated_date, pricing_return_over_feed_document
) VALUES (
    CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP,
    jsonb_build_object(
        'id', 'a3f32de5-9ab1-4b5d-b2a7-d82c03c94b3f',
        'Price', 13.8546,
        'Name', 'Fat, $/kg',
        'ReturnOverFeedType', 'MILK_PRICE_PER_TON'
    )
);

INSERT INTO return_over_feed_pricings (
    created_date, deleted, local_id, updated_date, pricing_return_over_feed_document
) VALUES (
    CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP,
    jsonb_build_object(
        'id', 'c0f3a1bb-f125-4fd8-a11e-6dbb8e5c6014',
        'Price', 10.9950,
        'Name', 'Protein, $/kg',
        'ReturnOverFeedType', 'MILK_PRICE_PER_TON'
    )
);

INSERT INTO return_over_feed_pricings (
    created_date, deleted, local_id, updated_date, pricing_return_over_feed_document
) VALUES (
    CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP,
    jsonb_build_object(
        'id', '9e401fc1-429e-49de-88e3-dc86d8d08f5a',
        'Price', 0.9000,
        'Name', 'Lactose and other solids, $/kg',
        'ReturnOverFeedType', 'MILK_PRICE_PER_TON'
    )
);

INSERT INTO return_over_feed_pricings (
    created_date, deleted, local_id, updated_date, pricing_return_over_feed_document
) VALUES (
    CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP,
    jsonb_build_object(
        'id', '60a34d7b-f3cd-4606-9390-3e5fef8fa9e9',
        'Price', 2.0404,
        'Name', 'Class 2 Protein, $/kg',
        'ReturnOverFeedType', 'MILK_PRICE_PER_TON'
    )
);

INSERT INTO return_over_feed_pricings (
    created_date, deleted, local_id, updated_date, pricing_return_over_feed_document
) VALUES (
    CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP,
    jsonb_build_object(
        'id', '9b20560a-47f3-4dd4-84fd-c05b2697794a',
        'Price', 0.6300,
        'Name', 'Class 2 Lactose and other solids, $/kg',
        'ReturnOverFeedType', 'MILK_PRICE_PER_TON'
    )
);


		</sql>
	</changeSet>

</databaseChangeLog>