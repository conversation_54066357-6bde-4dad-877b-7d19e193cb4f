/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.DeletedRecords;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.SitesAndMappingsWrapper;
import com.app.cargill.document.SitesLiftDeletedWrapperList;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.mapper.LiftSiteMapper;
import com.app.cargill.sf.cc.mapper.LiftSiteMappingMapper;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.Site;
import com.app.cargill.sf.cc.model.simple.SiteUpdateModel;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.joda.time.Instant;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class LiftSitesService {
  private static final String SITES_QUERY =
      "SELECT+Id,Name,Site_Name__c,IsDeleted,LM_Site_ID__c,"
          + "Housing_Type__c,CreatedDate,LastModifiedDate,Number_of_Animals__c,Current_Milk_Price__c,Sub_Species__c,"
          + "CreatedBy.Id,CreatedBy.Name,LastModifiedBy.Id,LastModifiedBy.Name,Account__r.Id,"
          + "Account__r.DE_Account_External_ID__c,DE_Herd_Status_Report_Latest__c,DE_Herd_Summary_Report_Latest__c,"
          + "(SELECT+Id,Species__c,System__c,Unique_External_Key__c+FROM+External_Data_Sources__r+WHERE+isDeleted=false)"
          + "+FROM+Species__c+WHERE+DE_Site__c=true";
  private final LiftApiService liftApi;

  private final LiftApiReactiveService liftApiReactiveService;

  private final SitesRepository repository;

  private final AccountsRepository accountsRepository;

  private final SiteMappingsRepository siteMappingsRepository;

  public SitesAndMappingsWrapper getAllSites(Instant timestamp) {
    List<SiteDocument> sitesList = new ArrayList<>();
    List<SiteMappingDocument> siteMappings = new ArrayList<>();
    try {
      AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
      String query =
          timestamp == null
              ? SITES_QUERY
              : String.format("%s+AND+LastModifiedDate>%s", SITES_QUERY, timestamp);

      SalesforceRecordsResponse<Site> sitesBatch =
          liftApi.getRecordsQuery(
              tokenAndApiPath.getAuthToken(),
              tokenAndApiPath.getApiPath(),
              query,
              new ParameterizedTypeReference<>() {});
      sitesBatch.getRecords().forEach(s -> mapSite(s, sitesList, siteMappings));
      while (sitesBatch.getNextRecordsUrl() != null) {
        sitesBatch =
            liftApi.getRecordsPage(
                tokenAndApiPath.getAuthToken(),
                sitesBatch.getNextRecordsUrl(),
                new ParameterizedTypeReference<>() {});
        sitesBatch.getRecords().forEach(s -> mapSite(s, sitesList, siteMappings));
      }
      log.info("{} LIFT sites obtained", sitesList.size());
      log.info("{} LIFT Site Mappings obtained", siteMappings.size());
    } catch (Exception e) {
      log.error("Error", e);
    }
    return new SitesAndMappingsWrapper(sitesList, siteMappings);
  }

  public SitesAndMappingsWrapper getSite(String siteId) {
    List<SiteDocument> sitesList = new ArrayList<>();
    List<SiteMappingDocument> siteMappings = new ArrayList<>();

    try {
      AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
      String query = String.format("%s+AND+LM_Site_ID__c='%s'", SITES_QUERY, siteId);

      SalesforceRecordsResponse<Site> sitesBatch =
          liftApi.getRecordsQuery(
              tokenAndApiPath.getAuthToken(),
              tokenAndApiPath.getApiPath(),
              query,
              new ParameterizedTypeReference<>() {});
      sitesBatch.getRecords().forEach(s -> mapSite(s, sitesList, siteMappings));
      log.trace("{} LIFT sites obtained", sitesList.size());
      log.trace("{} LIFT Site Mappings obtained", siteMappings.size());
    } catch (Exception e) {
      log.error("Error", e);
    }
    return new SitesAndMappingsWrapper(sitesList, siteMappings);
  }

  public String createSite(
      SiteDocument siteDocument, Locale locale, ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return createSite(
        tokenAndApiPath.getAuthToken(), tokenAndApiPath.getApiPath(), siteDocument, locale, source);
  }

  public String createSite(
      AuthToken authToken,
      String apiPath,
      SiteDocument siteDocument,
      Locale locale,
      ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {
    if (siteDocument.getId() == null) {
      throw new IllegalArgumentException("SiteId is a required field");
    }
    try {

      SiteUpdateModel siteUpdateModel = documentToModel(siteDocument);
      String siteUrl = String.format("%s/sobjects/Species__c", apiPath);
      CreateRecordResponse recordResponse =
          liftApi.createRecord(
              authToken, siteUpdateModel, new ParameterizedTypeReference<>() {}, siteUrl);
      log.info(
          "NEW_LIFT_SITE_CREATION Success: {} Id: {}",
          recordResponse.isSuccess(),
          recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception e) {
      log.error(String.format("Error on site creation: %s", siteDocument.getId()), e);
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  source.getMessage(
                      LangKeys.LIFT_SYNC_FAILED,
                      new Object[] {},
                      LiftErrorResponseConstants.LIFT_ERROR_MESSAGE,
                      locale))
              .entity("Site")
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()));
    }
  }

  public boolean updateSite(SiteDocument siteDocument) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return updateSite(tokenAndApiPath.getAuthToken(), tokenAndApiPath.getApiPath(), siteDocument);
  }

  public boolean updateSite(AuthToken authToken, String apiPath, SiteDocument siteDocument) {
    return updateRecord(
        authToken, apiPath, siteDocument.getExternalId(), documentToModel(siteDocument));
  }

  public boolean updateSiteExternalData(SiteDocument siteDocument) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    SiteUpdateModel siteUpdateModel = new SiteUpdateModel();
    siteUpdateModel.setExternalId(siteDocument.getId().toString());
    return updateRecord(
        tokenAndApiPath.getAuthToken(),
        tokenAndApiPath.getApiPath(),
        siteDocument.getExternalId(),
        siteUpdateModel);
  }

  public boolean updateSiteReport(
      String siteId, String herdSummaryReport, String herdStatusReport) {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    SiteUpdateModel siteUpdateModel = new SiteUpdateModel();
    siteUpdateModel.setHerdSummaryReport(herdSummaryReport);
    siteUpdateModel.setHerdStatusReport(herdStatusReport);
    return updateRecord(
        tokenAndApiPath.getAuthToken(), tokenAndApiPath.getApiPath(), siteId, siteUpdateModel);
  }

  public boolean updateRecord(
      AuthToken authToken, String apiPath, String sfId, SiteUpdateModel siteUpdateModel) {
    try {
      if (sfId == null) {
        throw new IllegalArgumentException("Missing site external id.");
      }
      String siteUrl = String.format("%s/sobjects/Species__c/%s", apiPath, sfId);
      liftApi.updateRecord(
          authToken, siteUpdateModel, new ParameterizedTypeReference<>() {}, siteUrl);
      log.info("LIFT_SITE_UPDATE {}", StringEscapeUtils.escapeJava(sfId));
      log.debug(
          "LIFT_SITE_UPDATE_MODEL {}", StringEscapeUtils.escapeJava(siteUpdateModel.toString()));
      log.debug("LIFT_SITE_UPDATE_STACK", new RuntimeException("LIFT_SITE_UPDATE_STACK"));
      return true;
    } catch (Exception e) {
      log.error(String.format("LIFT_SITE_UPDATE_ERROR %s", StringEscapeUtils.escapeJava(sfId)), e);
      return false;
    }
  }

  private void mapSite(
      Site site, List<SiteDocument> sitesList, List<SiteMappingDocument> siteMappings) {
    sitesList.add(LiftSiteMapper.transform(site));
    if (site.getExternalDataSources() != null) {
      siteMappings.add(LiftSiteMappingMapper.transform(site));
    }
  }

  public SiteUpdateModel documentToModel(SiteDocument siteDocument) {
    SiteUpdateModel updateModel = new SiteUpdateModel();
    updateModel.setAccountId(siteDocument.getExternalAccountId());
    updateModel.setSiteName(siteDocument.getSiteName());
    updateModel.setNumberOfAnimals(
        siteDocument.getLactatingAnimal() != null
            ? siteDocument.getLactatingAnimal().toString()
            : "");
    updateModel.setExternalId(siteDocument.getId().toString());
    updateModel.setCurrentMilkPrice(siteDocument.getCurrentMilkPrice());

    if (reportLinksAreSet(siteDocument)) {
      updateModel.setHerdStatusReport(siteDocument.getHerdStatusReport());
      updateModel.setHerdSummaryReport(siteDocument.getHerdSummaryReport());
    }

    if (siteDocument.getMilkingSystemType() != null) {
      updateModel.setHousingType(siteDocument.getMilkingSystemType().name());
    }
    return updateModel;
  }

  private boolean reportLinksAreSet(SiteDocument siteDocument) {
    return StringUtils.isNotEmpty(siteDocument.getHerdStatusReport())
        && StringUtils.isNotEmpty(siteDocument.getHerdSummaryReport());
  }

  public void autoDeleteLiftdeletedSites() {
    List<SitesLiftDeletedWrapperList> sitesLiftDeletedWrappers = new ArrayList<>();
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    java.time.Instant dateTo = java.time.Instant.now();
    java.time.Instant dateFrom = dateTo.minus(10, ChronoUnit.DAYS);
    String siteUrl = tokenAndApiPath.getApiPath() + "/sobjects/Species__c/deleted";
    log.debug("GET_DELETED_SITES_FROM_LIFT {} {} {}", siteUrl, Instant.now(), dateFrom);
    SitesLiftDeletedWrapperList result =
        liftApi.getRecordsWithQueryParam(
            tokenAndApiPath.getAuthToken(),
            siteUrl,
            SitesLiftDeletedWrapperList.class,
            dateFrom,
            dateTo);
    if (result != null && result.getDeletedRecords() != null) {
      List<DeletedRecords> deletedRecordsList = result.getDeletedRecords();
      deletedRecordsList.stream()
          .forEach(
              site -> {
                Sites sites = repository.findByExternalId(site.getId());
                if (sites != null) {
                  log.debug("GET_DELETED_SITE_ID {}", site.getId());
                  sites.getSiteDocument().setDeleted(true);
                  sites.setDeleted(true);
                  repository.save(sites);
                  updateSiteMapping(sites.getSiteDocument().getId().toString());
                  updateAccounts(sites.getSiteDocument().getAccountId().toString());
                }
              });
    }
    log.debug("GET_DELETED_SITES_FROM_LIFT End");
  }

  private void updateAccounts(String accountId) {
    Accounts account = accountsRepository.findByAccountId(accountId);
    account
        .getAccountDocument()
        .setSiteCount(repository.countByAccountId(account.getAccountDocument().getId().toString()));
    account.setRecordModified(true);
    accountsRepository.saveAndFlush(account);
  }

  private void updateSiteMapping(String siteId) {
    SiteMappings siteMapping = siteMappingsRepository.findBySiteId(siteId);
    if (!Objects.isNull(siteMapping)) {
      siteMapping.setDeleted(true);
      siteMappingsRepository.save(siteMapping);
    }
  }
}
