/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LocomotionToolItemDto implements Serializable {
  /// <summary>
  /// These values are used in the Locomotion Milk Loss Calculations
  /// for lactating pens only,
  /// and are displayed in the Locomotion Score
  /// % Loss / Cow row. These values are, at the moment, constant
  /// and cannot be changed by the user.
  /// The scores are only used for Categories 3, 4, and 5.
  /// </summary>

  @Builder.Default private Double milkScoreThree = 5.1;

  @Builder.Default private Double milkScoreFour = 16.8;

  @Builder.Default private Double milkScoreFive = 36.0;

  private UUID penId;

  private String penName;

  private Integer totalAnimalsInPen;

  private Integer daysInMilk;

  private Double milkProductionInKg;

  private List<LocomotionToolItemCategoryItemDto> categories;

  private List<UUID> visitsSelected;

  private Boolean isToolItemNew;
}
