/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.service.ICdpService;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class CdpControllerTest {

  @Mock private ICdpService cdpService;

  @InjectMocks private CDPController cdpController;

  @Test
  void getPaginatedAccountDocuments() {
    when(cdpService.getAllAccountsByFromAndToDate(any(), any(), any())).thenReturn(generatePage());
    ResponseEntity<ResponseEntityDto<List<AccountDocumentDTO>>> result =
        cdpController.getAllAccountsByFromAndToDate(
            Instant.now().minus(1, ChronoUnit.DAYS), Instant.now(), Pageable.ofSize(5).withPage(1));
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().size());
  }

  private List<AccountDocumentDTO> generateAccountDocuments() {
    return List.of(AccountDocumentDTO.builder().build(), AccountDocumentDTO.builder().build());
  }

  private Page<AccountDocumentDTO> generatePage() {
    return new PageImpl<>(generateAccountDocuments());
  }

  @Test
  void getAllSitesByFromAndToDate_ReturnsSiteDocuments() {
    // Prepare test data
    Instant dateFrom = Instant.now();
    Instant dateTo = Instant.now();
    Pageable pageable = PageRequest.of(0, 100);
    List<SiteDocumentDTO> siteDocuments =
        List.of(SiteDocumentDTO.builder().build(), SiteDocumentDTO.builder().build());
    Page<SiteDocumentDTO> page = new PageImpl<>(siteDocuments);

    // Mock the cdpService
    when(cdpService.getAllSitesByFromAndToDate(dateFrom, dateTo, pageable)).thenReturn(page);

    // Call the endpoint
    ResponseEntity<ResponseEntityDto<List<SiteDocumentDTO>>> response =
        cdpController.getAllSitesByFromAndToDate(dateFrom, dateTo, pageable);

    // Assertions
    assertEquals(siteDocuments, Objects.requireNonNull(response.getBody()).getData());
  }

  @Test
  void getAllVisitsByFromAndToDate_ReturnsVisitDocuments() {
    // Prepare test data
    Instant dateFrom = Instant.now();
    Instant dateTo = Instant.now();
    Pageable pageable = PageRequest.of(0, 100);
    List<VisitDocumentDTO> visitDocuments = List.of(new VisitDocumentDTO(), new VisitDocumentDTO());
    Page<VisitDocumentDTO> page = new PageImpl<>(visitDocuments);

    // Mock the cdpService
    when(cdpService.getAllVisitsByFromAndToDate(dateFrom, dateTo, pageable)).thenReturn(page);

    // Call the endpoint
    ResponseEntity<ResponseEntityDto<List<VisitDocumentDTO>>> response =
        cdpController.getAllVisitsByFromAndToDate(dateFrom, dateTo, pageable);

    // Assertions
    assertEquals(visitDocuments, Objects.requireNonNull(response.getBody()).getData());
  }
}
