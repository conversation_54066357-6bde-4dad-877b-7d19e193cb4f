/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import java.time.Instant;
import java.util.List;
import java.util.Locale;

import org.apache.commons.text.StringEscapeUtils;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.app.cargill.crescendo.service.ICrescendoContactService;
import com.app.cargill.document.Contact;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.crescendo.model.ContactCrescendo;
import com.app.cargill.sf.crescendo.service.CrescendoContactService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/salesforce/crescendo/contact")
@Tag(
    name = "Salesforce Crescendo Contact",
    description = "Controller related to actions over Contact objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoContactController {

  private final CrescendoContactService crescendoContactService;
  private final ICrescendoContactService crescendoContactServiceImpl;
  private final ResourceBundleMessageSource bundleMessageSource;

  @GetMapping
  @Operation(
      summary = "Get list of contacts in Crescendo format",
      description =
          "Get list of contacts in Crescendo format modified since the provided timestamp")
  public List<ContactCrescendo> getContacts(@RequestParam(required = false) Instant from) {
    log.debug("CRESCENDO_CONTACTS_GET_REQUEST {}", StringEscapeUtils.escapeJava(from.toString()));
    List<ContactCrescendo> result = crescendoContactService.getContactsForSync(from);
    log.debug("CRESCENDO_CONTACTS_GET_RESULT {}", result);
    return result;
  }

  @PostMapping
  @Operation(
      summary = "Update contacts from Crescendo",
      description = "Update contacts from Crescendo")
  public ResponseEntity<Void> upsertContacts(@RequestBody List<ContactCrescendo> contacts) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.registerModule(new JavaTimeModule());
      String requestBody = objectMapper.writeValueAsString(contacts);
      log.debug("CRESCENDO_CONTACTS_POST_REQUEST {}", StringEscapeUtils.escapeJava(requestBody));
    } catch (JsonProcessingException ex) {
      log.error("CANNOT_PROCESS_CRESCENDO_CONTACTS_JSON", ex);
    }
    crescendoContactService.updateContacts(contacts);
    return ResponseEntity.accepted().build();
  }
  
  @PostMapping("/create")
  @Operation(
      summary = "Create a Contact directly from APP to CRESCENDO",
      description = "This api will create a Contact Directly from APP to Crescendo")
  public String createContact(@RequestBody Contact contact)
      throws CustomDEExceptions {
    return crescendoContactServiceImpl.createContact(
    		contact, Locale.ENGLISH, bundleMessageSource);
  }
}
