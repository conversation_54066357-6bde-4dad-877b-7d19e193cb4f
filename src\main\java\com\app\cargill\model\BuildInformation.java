/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.document.BuildInformationDocument;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

@Entity
@SuperBuilder
@Data
@EqualsAndHashCode(callSuper = true)
@Table(name = "BuildInformation")
@ToString(callSuper = true)
@Where(clause = "deleted = false")
@NoArgsConstructor
@AllArgsConstructor
public class BuildInformation extends BaseEntity {

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb") // or, json
  private BuildInformationDocument buildInfoDocument;
}
