<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitViewModel.NutritionItem", [], "Nutrition",
                locale)}</span>
            <span class="lg-head">${localization.getMessage("TMRParticleScore", [],
                "TMR penn state",locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000111111TMR</div>
    </div>
</div>
<!-- Pen Analysis -->
<#if model.rumenHealthTMRParticleScoreTool.penAnalysis?? && model.rumenHealthTMRParticleScoreTool.penAnalysis[0]??>
    <div class="container">
        <div class="row mx-neg-4">
            <div class="col-12 px-4">
                <h3 class="title-secondary">
                    <span>${localization.getMessage("WalkthroughReportLandingViewModel.PenAnalysis", [], "Pen Analysis",
                        locale)}</span>
                </h3>

            </div>
        </div>
    </div>
    <#assign counter=0>
        <#assign penRows=0>
		<#assign isFirstIteration=true>
            <#list model.rumenHealthTMRParticleScoreTool.penAnalysis as penAnalysis>
                <#if counter gt 0>
                    <div class="break-page"></div>
                    <#assign counter=0>
					<#assign isFirstIteration=true>
                </#if>
                <#if penAnalysis.summaryDetails??>
                <div class="container mb-1">
                <div class="row mx-neg-4">
                    <div class="col-12 px-4 d-flex justify-space-between">
                        <h6>${penAnalysis.penName!}</h6>
                        <h6>${penAnalysis.scorerLabel!}</h6>
                    </div>
                <div class="col-12 px-4 table-secondary">
                    <table>
                        <thead>
                            <tr>
                                <#list penAnalysis.summaryDetails[0]?keys as prop>
                                    <#list penAnalysis.summaryDetails[0][prop] as key>
                                        <th>${key.column!}</th>
                                    </#list>
                                </#list>
                            </tr>
                        </thead>
                        <tbody>
                          <#assign colourCounter=0>
                            <#list penAnalysis.summaryDetails as item>
                                <#list item?keys as prop>
                                        <tr>
                                            <td colspan="10">
                                                <h3 class="title mb-0">${prop!}</h3>
                                            </td>
                                        </tr>
                                         <#if colourCounter gt 1>
                                        <tr style = "background-color: #CFDFE7">
                                            <#list item[prop] as key>
                                                   <td>${key.value!}</td>
                                            </#list>
                                        </tr>
                                        </#if>
                                        <#if colourCounter lte 1>
                                         <tr>
                                            <#list item[prop] as key>
                                                
                                                   <td>${key.value!}</td>
                                            </#list>
                                        </tr>
                                        </#if>
                                        <#assign colourCounter=colourCounter+1>
                                </#list>
                            </#list>
                        </tbody>
                    </table>
                </div>
                </div>
                </div>

                </#if>
                <#assign penRows=0>
                    <#if penAnalysis.penAnalysisDetails??>
                        <div class="container mb-1">

                                <div class="col-12 px-4 table-secondary">
                                    <table>
                                        <thead>
                                            <tr>
                                                <#list penAnalysis.penAnalysisDetails[0]?keys as prop>
                                                    <#list penAnalysis.penAnalysisDetails[0][prop] as key>
                                                        <th>${key.column!}</th>
                                                    </#list>
                                                </#list>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <#list penAnalysis.penAnalysisDetails as item>
                                                <#list item?keys as prop>
                                                    <#assign penRows=penRows+1>
                                                        <tr>
                                                            <td colspan="10">
                                                                <h3 class="title mb-0">${prop!}</h3>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <#list item[prop] as key>
                                                                <td>${key.value!}</td>
                                                            </#list>
                                                        </tr>
                                                </#list>
                                            </#list>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <#if penRows gte 15>
                            <div class="break-page"></div>
                        </#if>
                    </#if>
                    <#if penAnalysis.graph?? && penAnalysis.graph[0]??>

                        <!-- TMR Particle Score 1 & 2 -->
                        <div class="container">
                            <#list penAnalysis.graph?chunk(2) as row>
                                <div class="row mx-neg-4">
                                    <#list row as column>
                                        <div class="col-6 px-4">
                                            <h5 class="title-sub">${column.tmrLabel!}</h5>
                                            <div class="card mb-1">
                                                <div class="card-body">
                                                    <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                                        <canvas id="${linechart2}"></canvas>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="row">
                                                        <div class="legend-wrap mb-2">
                                                            <p class="middle-blue-solid">
                                                                ${localization.getMessage("Top", [],
                                                                "Top", locale)}</p>
                                                        </div>
                                                        <div class="legend-wrap mb-2">
                                                            <p class="jordy-blue-solid">
                                                                ${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title",
                                                                [], "Mid 1", locale)}</p>
                                                        </div>
                                                        <div class="legend-wrap mb-2">
                                                            <p class="blue-purple-solid">
                                                                ${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle",
                                                                [], "Mid 2", locale)}</p>
                                                        </div>
                                                        <div class="legend-wrap mb-2">
                                                            <p class="tulip-solid">${localization.getMessage("Tray", [],
                                                                "Tray",
                                                                locale)}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <script>
                                            (function () {

                                                const bar1 = [
                                                    <#list column.onScreenPercentage as val >
                                                    ${(val.top)!'NaN'} <#sep>, </#sep>
</#list >
];

                                            const bar2 = [
                                                <#list column.onScreenPercentage as val >
                                                ${(val.mid1)!'NaN'} <#sep>, </#sep>
</#list >
];

                                            const bar3 = [
                                                <#list column.onScreenPercentage as val >
                                                ${(val.mid2)!'NaN'} <#sep>, </#sep>
</#list >
];

                                            const bar4 = [
                                                <#list column.onScreenPercentage as val >
                                                ${(val.tray)!'NaN'} <#sep>, </#sep>
</#list >
];

                                            const xAxis = [
                                                <#list column.onScreenPercentage as val >
                                                '${val.visitDate!}' <#sep>, </#sep>
</#list >
];

                                            const ctx = document.getElementById("${linechart2}").getContext("2d");
                                            ctx.canvas.height = 100;

                                            const options = {
                                                type: "bar",
                                                data: {
                                                    labels: xAxis,
                                                    datasets: [
                                                        {
                                                            data: bar1,
                                                            grouped: true,
                                                            backgroundColor: "#7AD8DC",
                                                            barThickness: 20,
                                                            skipNull : true,
                                                            borderColor: "rgba(0,0,0,0)",
                                                            borderWidth: 4,
                                                        },

                                                        {
                                                            data: bar2,
                                                            backgroundColor: "#83BEF4",
                                                            barThickness: 20,
                                                            skipNull: true,
                                                            borderColor: "rgba(0,0,0,0)",
                                                            borderWidth: 4,
                                                        },

                                                        {
                                                            data: bar3,
                                                            backgroundColor: "#ABA1E3",
                                                            barThickness: 20,
                                                            skipNull: true,
                                                            borderColor: "rgba(0,0,0,0)",
                                                            borderWidth: 4,
                                                        },

                                                        {
                                                            data: bar4,
                                                            backgroundColor: "#F18494",
                                                            barThickness: 20,
                                                            skipNull: true,
                                                            borderColor: "rgba(0,0,0,0)",
                                                            borderWidth: 4,
                                                        }
                                                    ]
                                                },
                                                options: {
                                                    plugins: {
                                                        legend: {
                                                            display: false,
                                                        },
                                                        tooltip: {
                                                            callbacks: {
                                                                title: () => null // or function () { return null; }
                                                            },
                                                            yAlign: 'bottom',
                                                            backgroundColor: "#fff",
                                                            borderColor: "rgba(0, 0, 0, 0.25)",
                                                            borderWidth: 1,
                                                            displayColors: false,
                                                            bodyColor: "#307698",
                                                            bodyAlign: "center",
                                                        },
                                                    },

                                                    layout: {
                                                        padding: {
                                                            top: 25,
                                                            right: 15
                                                        }
                                                    },

                                                    responsive: true,

                                                    scales: {
                                                        y: {
                                                            // beginAtZero: true,
                                                            title: {
                                                                display: true,
                                                                color: '#6C7782',
                                                                text: '${localization.getMessage("Report.PercentageOnScreen", [], "On Screen %", locale)}',
                                                                padding: {
                                                                    bottom: 0,
                                                                }
                                                            },

                                                            grid: {
                                                                display: false,
                                                            },
                                                            ticks: {
                                                                // Include a % sign in the ticks
                                                                callback: function (value, index, ticks) {
                                                                    return value + '%';
                                                                }
                                                            }
                                                        },

                                                        x: {
                                                            title: {
                                                                display: true,
                                                                color: '#6C7782',
                                                                text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                                padding: {
                                                                    top: 0,
                                                                }
                                                            },
                                                            grid: {
                                                                display: false,
                                                            },
                                                        }
                                                    },
                                                    animation: {
                                                        duration: 0,
                                                        onComplete: function () {
                                                            var chart = this;
                                                            var ctx = chart.ctx;
                                                            ctx.textAlign = 'center';
                                                            ctx.textBaseline = 'bottom';
                                                            ctx.fillStyle = '#6C7782';
                                                            this.data.datasets.forEach(function (dataset, i) {
                                                                var meta = chart.getDatasetMeta(i);
                                                                meta.data.forEach(function (bar, index) {
                                                                    var data = dataset.data[index];
                                                                    data = isNaN(data) ? '' : data + '%';
                                                                    var yIndex = bar.y - 5;
                                                                    if (data && data < 0) {
                                                                        yIndex = bar.y + 15;
                                                                    }
                                                                    var fontSize = Math.round(chart.chartArea.width / dataset.data.length / 3);
                                                                    if (fontSize > 9) {
                                                                        fontSize = 9;
                                                                    } else if (fontSize < 5) {
                                                                        fontSize = 5;
                                                                    }
                                                                    ctx.font = fontSize + 'px "Helvetica Neue", Helvetica, Arial, sans-serif'
                                                                    ctx.save();
                                                                    // Translate 0,0 to the point you want the text
                                                                    ctx.translate(bar.x + 5, yIndex);
                                                                    // Rotate context by -90 degrees
                                                                    ctx.rotate(-0.4 * Math.PI);
                                                                    ctx.textAlign = "center";
                                                                    ctx.fillText(data, 5, 0);
                                                                    ctx.restore();
                                                                    chart.resize();
                                                                });
                                                            });
                                                        }
                                                    }
                                                }
                                            };

                                            window.myLine = new Chart(ctx, options);
                            }) ();

                                        </script>
                                        <#assign counter=counter+1>
                                    </#list>
                                </div>
                                <#if counter%8==0 && counter lt penAnalysis.graph?size-1>
                                    <div class="break-page"></div>
								<#elseif penRows lte 5 && counter==6 && counter lt penAnalysis.graph?size-1 && isFirstIteration>
									<div class="break-page"></div>
									<#assign counter=0>
									<#assign isFirstIteration=false>
								<#elseif penRows gt 5 && penRows lt 14 && counter==4 && counter lt
									penAnalysis.graph?size-1 && isFirstIteration>
									<div class="break-page"></div>
									<#assign counter=0>
									<#assign isFirstIteration=false>
								<#elseif penRows gte 14 && penRows lt 20 && counter==2 && counter lt
									penAnalysis.graph?size-1 && isFirstIteration>
									<div class="break-page"></div>
									<#assign counter=0>
									<#assign isFirstIteration=false>
                                </#if>
								
                            </#list>
                        </div>
                    </#if>
            </#list>
</#if>
<#if model.rumenHealthTMRParticleScoreTool.herdAnalysis??>
    <#assign counter=0>
        <#if model.rumenHealthTMRParticleScoreTool.penAnalysis??>
            <div class="break-page"></div>
        </#if>
        <#if model.rumenHealthTMRParticleScoreTool.herdAnalysis.herdAnalysisDetails?? &&
            model.rumenHealthTMRParticleScoreTool.herdAnalysis.herdAnalysisDetails[0]??>
            <!-- Herd Analysis -->
            <div class="container mb-1">
                <div class="row mx-neg-4">
                    <div class="col-12">
                        <h3 class="title-secondary mb-1">
                            <span>${localization.getMessage("RumenHealthLandingViewModel.HerdAnalysis", [],"Herd
                                Analysis", locale)}</span>
                        </h3>
                    </div>
                    <div class="col-12 table-secondary">
                        <table>
                            <tbody>
                                <tr>
                                    <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.herdAnalysisDetails[0] as
                                        herdAnalysis>
                                        <th>${herdAnalysis.column!}</th>
                                    </#list>
                                </tr>
                                <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.herdAnalysisDetails as
                                    herdAnalysis>
                                    <#assign counter=counter+1>
                                        <tr>
                                            <#list herdAnalysis as innerlistObj>
                                                <td>${innerlistObj.value!}</td>
                                            </#list>
                                        </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </#if>
        <#if model.rumenHealthTMRParticleScoreTool.herdAnalysis.graph??>
            <#if model.rumenHealthTMRParticleScoreTool.herdAnalysis.herdAnalysisDetails?? &&
                model.rumenHealthTMRParticleScoreTool.herdAnalysis.herdAnalysisDetails[0]?? && counter gte 20>
                <div class="break-page"></div>
            </#if>
            <!-- Graph -->
            <div class="container">
                <div class="row mx-neg-4">
                    <div class="col-12 px-4">
                        <div class="card mb-1">
                            <div class="card-body">
                                <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                    <canvas id="${linechart2}"></canvas>
                            </div>
                            <div class="card-footer">
                                <div class="row">
                                    <div class="legend-wrap mb-2">
                                        <p class="middle-blue-solid">${localization.getMessage("Top", [], "Top",
                                            locale)}</p>
                                    </div>
                                    <div class="legend-wrap mb-2">
                                        <p class="jordy-blue-solid">
                                            ${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title",
                                            [], "Mid 1", locale)}</p>
                                    </div>
                                    <div class="legend-wrap mb-2">
                                        <p class="blue-purple-solid">
                                            ${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle",
                                            [], "Mid 2", locale)}</p>
                                    </div>
                                    <div class="legend-wrap mb-2">
                                        <p class="tulip-solid">${localization.getMessage("Tray", [], "Tray", locale)}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <script>

                const bar1 = [
                    <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.graph.dataPoints as val >
                    ${(val.top)!'NaN'} <#sep>, </#sep>
</#list >
];

                const bar2 = [
                    <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.graph.dataPoints as val >
                    ${(val.mid1)!'NaN'} <#sep>, </#sep>
</#list >
];

                const bar3 = [
                    <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.graph.dataPoints as val >
                    ${(val.mid2)!'NaN'} <#sep>, </#sep>
</#list >
];

                const bar4 = [
                    <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.graph.dataPoints as val >
                    ${(val.tray)!'NaN'} <#sep>, </#sep>
</#list >
];

                const xAxis = [
                    <#list model.rumenHealthTMRParticleScoreTool.herdAnalysis.graph.dataPoints as val >
                    '${val.penName!}' <#sep>, </#sep>
</#list >
];

                const ctx = document.getElementById("${linechart2}").getContext("2d");
                ctx.canvas.height = 100;
                const borderWidth = 1;
                const categoryPercentage = 0.8;
                const barPercentage = 0.5;
                const options = {
                    type: "bar",
                    data: {
                        labels: xAxis,
                        datasets: [{
                            data: bar1,
                            grouped: true,
                            backgroundColor: "#7AD8DC",
                            skipNull: true,
                            categoryPercentage: categoryPercentage,
                            barPercentage: barPercentage,
                            borderColor: "rgba(0,0,0,0)",
                            borderWidth: borderWidth,
                        },

                        {
                            data: bar2,
                            backgroundColor: "#83BEF4",
                            categoryPercentage: categoryPercentage,
                            barPercentage: barPercentage,
                            skipNull: true,
                            borderColor: "rgba(0,0,0,0)",
                            borderWidth: borderWidth,
                        },

                        {
                            data: bar3,
                            backgroundColor: "#ABA1E3",
                            categoryPercentage: categoryPercentage,
                            barPercentage: barPercentage,
                            skipNull: true,
                            borderColor: "rgba(0,0,0,0)",
                            borderWidth: borderWidth,
                        },

                        {
                            data: bar4,
                            backgroundColor: "#F18494",
                            categoryPercentage: categoryPercentage,
                            barPercentage: barPercentage,
                            skipNull: true,
                            borderColor: "rgba(0,0,0,0)",
                            borderWidth: borderWidth,
                        }
                        ]
                    },
                    options: {
                        plugins: {
                            legend: {
                                display: false,
                            },
                            tooltip: {
                                callbacks: {
                                    title: () => null // or function () { return null; }
                                },
                                yAlign: 'bottom',
                                backgroundColor: "#fff",
                                borderColor: "rgba(0, 0, 0, 0.25)",
                                borderWidth: 1,
                                displayColors: false,
                                bodyColor: "#307698",
                                bodyAlign: "center",
                            },
                        },

                        layout: {
                            padding: {
                                top: 25,
                                right: 15
                            }
                        },

                        responsive: true,


                        scales: {
                            y: {
                                // beginAtZero: true,
                                title: {
                                    display: true,
                                    color: '#6C7782',
                                    text: '${localization.getMessage("Report.PercentageOnScreen", [], "On Screen %", locale)}',
                                    padding: {
                                        bottom: 0,
                                    }
                                },

                                grid: {
                                    display: false,
                                },
                                suggestedMax: (scale) => {

                                    var curr = scale.chart.data.datasets;
                                    var arr = [];
                                    for (let i = 0; i < curr.length; i++) {
                                        arr.push(Math.max.apply(null, curr[i].data.filter(x => !isNaN(x))));
                                    }
                                    return Math.round(Math.max(...arr) + (Math.max(...arr) * 0.05));
                                },
                                suggestedMin: (scale) => {
                                    var curr = scale.chart.data.datasets;
                                    var arr = [];
                                    for (let i = 0; i < curr.length; i++) {
                                        arr.push(Math.min.apply(null, curr[i].data.filter(x => !isNaN(x))));
                                    }
                                    return Math.floor(Math.min(...arr) + (Math.min(...arr) * 0.15));
                                },
                                ticks: {
                                    // Include a % sign in the ticks
                                    callback: function (value, index, ticks) {
                                        return value;
                                    }
                                }
                            },

                            x: {
                                title: {
                                    display: true,
                                    color: '#6C7782',
                                    text: '${localization.getMessage("", [], "", locale)}',
                                    padding: {
                                        top: 0,
                                    }
                                },
                                grid: {
                                    display: false,
                                },
                            }
                        },
                        animation: {
                            duration: 0,
                            onComplete: function () {
                                var chart = this;
                                var ctx = chart.ctx;
                                ctx.textAlign = 'center';
                                ctx.textBaseline = 'bottom';
                                ctx.fillStyle = '#6C7782';
                                this.data.datasets.forEach(function (dataset, i) {
                                    var meta = chart.getDatasetMeta(i);
                                    meta.data.forEach(function (bar, index) {
                                        var data = dataset.data[index];
                                        data = isNaN(data) ? '' : data;
                                        var yIndex = bar.y - 12;
                                        if (data && data < 0) {
                                            yIndex = bar.y + 15;
                                        }

                                        var fontSize = Math.round(chart.chartArea.width / dataset.data.length / 3);
                                        if (fontSize > 9) {
                                            fontSize = 9;
                                        } else if (fontSize < 5) {
                                            fontSize = 5;
                                        }
                                        ctx.font = fontSize + 'px "Helvetica Neue", Helvetica, Arial, sans-serif'
                                        ctx.save();
                                        // Translate 0,0 to the point you want the text
                                        ctx.translate(bar.x + 5, yIndex);
                                        // Rotate context by -90 degrees
                                        ctx.rotate(-0.4 * Math.PI);
                                        ctx.textAlign = "center";
                                        ctx.fillText(data, 0, 0);
                                        ctx.restore();
                                        chart.resize();
                                    });
                                });
                            }
                        }
                    }
                };
                window.onload = function () {
                    window.myLine = new Chart(ctx, options);
                };

            </script>
        </#if>
</#if>
<#if model.rumenHealthTMRParticleScoreTool?? && model.rumenHealthTMRParticleScoreTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.rumenHealthTMRParticleScoreTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>
