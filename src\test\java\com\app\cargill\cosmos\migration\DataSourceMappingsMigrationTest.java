/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.model.SiteMappingCosmos;
import com.app.cargill.cosmos.repo.SiteMappingsCosmosRepository;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.repository.SiteMappingsRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class DataSourceMappingsMigrationTest {

  @Mock private SiteMappingsCosmosRepository cosmosRepository;
  @Mock private SiteMappingsRepository siteMappingsRepository;

  @InjectMocks private SiteMappingsMigration service;

  @BeforeEach
  void setUp() {
    lenient().when(siteMappingsRepository.saveAll(any())).thenReturn(new ArrayList<>());
  }

  @Test
  void whenMigrationIsInvokedEverythingPasses()
      throws IOException, ExecutionException, InterruptedException {
    ObjectMapper objectMapper = new ObjectMapper();
    SiteMappingCosmos item =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/siteMapping.json"),
            SiteMappingCosmos.class);

    Iterable<SiteMappingCosmos> itemsList = List.of(item, item);
    when(cosmosRepository.findAll()).thenReturn(Flux.fromIterable(itemsList));

    MigrationResult result = service.moveAll().get();
    assertEquals(2, result.getSucceeded());
    assertEquals(0, result.getFailed());
  }

  @Test
  void whenMigrationFixIsCalledCorrectResponseIsReturned() {
    when(cosmosRepository.findAllSiteMappings()).thenReturn(Flux.empty());
    MigrationResult result = null;
    try {
      result =
          service
              .migrationFix(String.valueOf(CosmosDataMigration.MigrationFix.SITE_MAPPINGS))
              .get();
    } catch (InterruptedException | ExecutionException e) {
      e.printStackTrace();
    }
    assertNotNull(result);
  }

  @Test
  void whenSiteMappingsDataIsOkNoErrors() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();

    SiteMappingCosmos siteMappingCosmos =
        objectMapper.readValue(
            new File("src/test/resources/cosmos/samples/siteMapping.json"),
            SiteMappingCosmos.class);
    when(cosmosRepository.findAllBySiteId(anyString())).thenReturn(Flux.just(siteMappingCosmos));
    when(siteMappingsRepository.save(any())).thenAnswer(i -> i.getArgument(0));

    Flux<SiteMappings> result = service.moveRecords("test");

    StepVerifier.create(result).expectNextCount(1).verifyComplete();
  }
}
