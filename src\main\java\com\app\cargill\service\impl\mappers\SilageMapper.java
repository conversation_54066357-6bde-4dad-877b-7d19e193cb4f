/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.dto.SilageDto;
import com.app.cargill.model.Silages;

public class SilageMapper {

  private SilageMapper() {}

  public static SilageDto modelToDto(Silages silages) {
    return SilageDto.builder()
        .accountId(silages.getSilageDocument().getAccountId())
        .silageName(silages.getSilageDocument().getSilageName())
        .id(silages.getSilageDocument().getId())
        .localId(silages.getLocalId())
        .deleted(silages.isDeleted())
        .updatedDate(silages.getUpdatedDate() != null ? silages.getUpdatedDate().toInstant() : null)
        .createdDate(silages.getCreatedDate() != null ? silages.getCreatedDate().toInstant() : null)
        .siteId(silages.getSilageDocument().getSiteId())
        .build();
  }
}
