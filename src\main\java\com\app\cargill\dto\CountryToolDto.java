/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Tool;
import com.app.cargill.constants.ToolGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CountryToolDto extends BaseDto {

  private String createUser;
  private Business countryId;
  private ToolGroup toolGroupId;
  private Tool toolId;
}
