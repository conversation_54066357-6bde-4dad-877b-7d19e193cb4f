/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.de;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.migration.AccountsDataFixService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Sites;
import com.app.cargill.model.tasks.SyncResult;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.mapper.LiftSiteMapper;
import com.app.cargill.sf.cc.model.simple.Site;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
@Slf4j
class SitesSynchronizerTest {

  private final ObjectMapper objectMapper = new ObjectMapper();

  @Mock private SitesRepository repository;
  @Mock private AccountsRepository accountsRepository;
  @Mock private LiftSiteMappingsService siteMappingsService;
  @Mock private LiftSitesService liftSitesService;

  @Mock private AccountsDataFixService accountsDataFixService;

  @InjectMocks private SitesSynchronizer synchronizer;

  @Test
  void whenNewSiteIsAddedEverythingPasses() {
    List<DataSourceMapping> dataSourceMappings = new ArrayList<>();
    DataSourceMapping dataSourceMapping = new DataSourceMapping();
    dataSourceMapping.setSystemName("LM_SITE");
    dataSourceMapping.setSystemId("123");
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setDataSourceMappings(dataSourceMappings);
    Sites dbSite = new Sites(siteDocument);

    dbSite.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(liftSitesService.updateSiteExternalData(any())).thenReturn(true);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());
    when(accountsRepository.findByAccountId(anyString())).thenReturn(null);

    SiteDocument sfSite = createDocument();
    sfSite.setId(null);
    SyncResult syncResult = synchronizer.sync(List.of(sfSite));

    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(1, syncResult.getNewRecords().get());
  }

  @Test
  void whenExternalDataUpdateFailsSiteIsNotAdded() {
    List<DataSourceMapping> dataSourceMappings = new ArrayList<>();
    DataSourceMapping dataSourceMapping = new DataSourceMapping();
    dataSourceMapping.setSystemName("LM_SITE");
    dataSourceMapping.setSystemId("123");
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setDataSourceMappings(dataSourceMappings);
    Sites dbSite = new Sites(siteDocument);

    dbSite.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(liftSitesService.updateSiteExternalData(any())).thenReturn(false);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));

    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void whenSameSiteWithDifferentIdArrivesItIsNotUpdated() {

    Date updatedTime = DateUtils.addDays(new Date(), -30);
    List<DataSourceMapping> dataSourceMappings = new ArrayList<>();
    DataSourceMapping dataSourceMapping = new DataSourceMapping();
    dataSourceMapping.setSystemName("LM_SITE");
    dataSourceMapping.setSystemId("123");
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setDataSourceMappings(dataSourceMappings);
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("sf-id");
    Sites dbSite = new Sites(siteDocument);

    dbSite.setUpdatedDate(updatedTime);
    dbSite.getSiteDocument().setId(UUID.randomUUID());

    SiteDocument sfSiteDocument = new SiteDocument();
    sfSiteDocument.setDataSourceMappings(dataSourceMappings);
    sfSiteDocument.setId(UUID.randomUUID());
    sfSiteDocument.setExternalId("sf-id");
    sfSiteDocument.setLastModifiedTimeUtc(updatedTime.toInstant());

    when(repository.findBySiteIdOrExternalId(anyString(), anyString())).thenReturn(dbSite);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    SyncResult syncResult = synchronizer.sync(List.of(sfSiteDocument));

    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void whenNewSiteIsAddedAndSiteMappingsAreMissingTheyAreUpdated() throws Exception {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setExternalId("sfId");
    siteDocument.setId(UUID.randomUUID());
    Sites dbSite = new Sites(siteDocument);
    dbSite.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    List<DataSourceMapping> dataSourceMappings = new ArrayList<>();
    DataSourceMapping dataSourceMapping = new DataSourceMapping();
    dataSourceMapping.setSystemName("DDW");
    dataSourceMapping.setSystemId("123");
    dataSourceMappings.add(dataSourceMapping);

    SiteDocument liftSiteDocument = createDocument();
    liftSiteDocument.setDataSourceMappings(dataSourceMappings);
    liftSiteDocument.setExternalId("sfId");
    liftSiteDocument.setId(UUID.randomUUID());

    when(liftSitesService.updateSiteExternalData(any())).thenReturn(true);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    SyncResult syncResult = synchronizer.sync(List.of(liftSiteDocument));

    verify(siteMappingsService, times(1)).createSiteMapping(any(), any(), any());
    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(1, syncResult.getNewRecords().get());
  }

  @Test
  void whenSiteIsUpdateEverythingPasses() {
    Sites dbSite = new Sites(new SiteDocument());
    dbSite.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(repository.findBySiteIdOrExternalId(anyString(), any())).thenReturn(dbSite);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  @Test
  void whenSiteIsCreatedEverythingPasses() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setExternalId("1234");
    Sites dbSite = new Sites(siteDocument);
    dbSite.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(accountsRepository.findByAccountId(any())).thenReturn(loadAccount());
    when(liftSitesService.updateSiteExternalData(any())).thenReturn(true);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
  }

  private Accounts loadAccount() {
    return Accounts.builder()
        .accountDocument(AccountDocument.builder().id(UUID.randomUUID()).siteCount(0).build())
        .build();
  }

  @Test
  void whenThereIsAnExceptionMethodDoesNotFail() {
    Sites dbSite = new Sites(new SiteDocument());
    dbSite.setUpdatedDate(DateUtils.addDays(new Date(), -30));

    when(repository.findBySiteIdOrExternalId(anyString(), any())).thenThrow(RuntimeException.class);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    SyncResult syncResult = synchronizer.sync(List.of(createDocument()));
    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
  }

  private SiteDocument createDocument() {
    SiteDocument result = new SiteDocument();
    List<DataSourceMapping> dataSourceMappings = new ArrayList<>();
    dataSourceMappings.add(new DataSourceMapping("DDW", "123", "1234"));
    dataSourceMappings.add(new DataSourceMapping("LM_SITE", UUID.randomUUID().toString(), "1234"));

    result.setId(UUID.randomUUID());
    result.setCreateUser("name");
    result.setDeleted(false);
    result.setLastModifyUser("123");
    result.setLastModifyUser("test");
    result.setCreateTimeUtc(Instant.now());
    result.setAccountId(UUID.randomUUID());
    result.setExternalAccountId("123");
    result.setSiteName("site_name");
    result.setCurrentMilkPrice(0.0);
    result.setMilkingSystemType(null);
    result.setLactatingAnimal(0);
    result.setDataSourceMappings(dataSourceMappings);
    result.setLastModifiedTimeUtc(Instant.now());

    return result;
  }

  @Test
  void sitesDataTest() throws IOException, ParseException {
    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Site liftSite =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_site.json"), Site.class);
    SiteDocument mappedSite = LiftSiteMapper.transform(liftSite);

    SiteDocument dbDocument =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/db_site.json"), SiteDocument.class);
    Sites dbSite = new Sites();
    dbSite.setSiteDocument(dbDocument);
    dbSite.setId(8066L);
    dbSite.setLocalId(UUID.randomUUID().toString());
    dbSite.setDeleted(false);

    dbSite.setCreatedDate(formatter.parse("2023-06-07 08:39:56.983"));
    dbSite.setUpdatedDate(formatter.parse("2023-06-07 08:42:57.287"));

    when(repository.findBySiteIdOrExternalId(anyString(), anyString())).thenReturn(dbSite);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());

    List<SiteDocument> input = List.of(mappedSite);
    SyncResult syncResult = synchronizer.sync(input);
    assertNotNull(syncResult);
    log.info("Data: {}", syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
    assertEquals(0, syncResult.getFailures().get());
  }

  @Test
  void whenNoNeedToUpdateNothingIsChanged() throws IOException, ParseException {
    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Site liftSite =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_site.json"), Site.class);
    SiteDocument mappedSite = LiftSiteMapper.transform(liftSite);

    SiteDocument dbDocument =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/db_site.json"), SiteDocument.class);
    Sites dbSite = new Sites();
    dbSite.setSiteDocument(dbDocument);
    dbSite.setId(8066L);
    dbSite.setLocalId(UUID.randomUUID().toString());
    dbSite.setDeleted(false);

    dbSite.setCreatedDate(formatter.parse("2023-06-07 08:39:56.983"));
    dbSite.setUpdatedDate(formatter.parse("2023-07-07 08:42:57.287"));

    when(repository.findBySiteIdOrExternalId(anyString(), anyString())).thenReturn(dbSite);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());
    List<SiteDocument> input = List.of(mappedSite);
    SyncResult syncResult = synchronizer.sync(input);
    assertNotNull(syncResult);
    assertEquals(0, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
    assertEquals(0, syncResult.getFailures().get());
  }

  @Test
  void whenDdwIdIsProvidedSiteIsUpdated() throws ParseException, IOException {
    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Site liftSite =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_site.json"), Site.class);
    SiteDocument mappedSite = LiftSiteMapper.transform(liftSite);

    SiteDocument dbDocument =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/db_site.json"), SiteDocument.class);
    dbDocument.setHerdStatusReport(null);
    dbDocument.setHerdSummaryReport(null);
    Sites dbSite = new Sites();
    dbSite.setSiteDocument(dbDocument);
    dbSite.setId(8066L);
    dbSite.setLocalId(UUID.randomUUID().toString());
    dbSite.setDeleted(false);

    dbSite.setCreatedDate(formatter.parse("2023-06-07 08:39:56.983"));
    dbSite.setUpdatedDate(formatter.parse("2023-07-07 08:42:57.287"));

    when(repository.findBySiteIdOrExternalId(anyString(), anyString())).thenReturn(dbSite);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());
    List<SiteDocument> input = List.of(mappedSite);
    SyncResult syncResult = synchronizer.sync(input);
    assertNotNull(syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
    assertEquals(0, syncResult.getFailures().get());
  }

  @Test
  void whenDdwIdIsProvidedSiteIsUpdated_2() throws ParseException, IOException {
    objectMapper.registerModule(new JavaTimeModule());
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-M-dd hh:mm:ss");
    Site liftSite =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_site_no_report_links.json"), Site.class);
    SiteDocument mappedSite = LiftSiteMapper.transform(liftSite);

    SiteDocument dbDocument =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/db_site.json"), SiteDocument.class);
    dbDocument.setHerdStatusReport(null);
    dbDocument.setHerdSummaryReport(null);
    Sites dbSite = new Sites();
    dbSite.setSiteDocument(dbDocument);
    dbSite.setId(8066L);
    dbSite.setLocalId(UUID.randomUUID().toString());
    dbSite.setDeleted(false);

    dbSite.setCreatedDate(formatter.parse("2023-06-07 08:39:56.983"));
    dbSite.setUpdatedDate(formatter.parse("2023-07-07 08:42:57.287"));

    when(repository.findBySiteIdOrExternalId(anyString(), anyString())).thenReturn(dbSite);
    when(accountsDataFixService.runAccountsFix(any())).thenReturn(Flux.empty());
    List<SiteDocument> input = List.of(mappedSite);
    SyncResult syncResult = synchronizer.sync(input);
    assertNotNull(syncResult);
    assertEquals(1, syncResult.getModifiedRecords().get());
    assertEquals(0, syncResult.getNewRecords().get());
    assertEquals(0, syncResult.getFailures().get());
  }
}
