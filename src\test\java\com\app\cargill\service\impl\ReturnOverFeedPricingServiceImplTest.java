/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.ReturnOverFeedType;
import com.app.cargill.document.PricingReturnOverFeedDocument;
import com.app.cargill.dto.ReturnOverFeedPricePerTonDto;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.ReturnOverFeedPricings;
import com.app.cargill.repository.ReturnOverFeedPricingsRepository;
import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ReturnOverFeedPricingServiceImplTest {

  @Mock private ReturnOverFeedPricingsRepository repository;

  @InjectMocks private ReturnOverFeedPricingServiceImpl service;

  private ReturnOverFeedPricings pricing;

  @BeforeEach
  public void setup() {
    MockitoAnnotations.openMocks(this);

    PricingReturnOverFeedDocument doc = new PricingReturnOverFeedDocument();
    doc.setId(UUID.randomUUID());
    doc.setName("Corn");
    doc.setPrice(1200.0);
    doc.setReturnOverFeedType(ReturnOverFeedType.HOME_GROWN_GRAINS);

    pricing = new ReturnOverFeedPricings();
    pricing.setPricingReturnOverFeedDocument(doc);
  }

  @Test
  void testGetPricePerTon_All_ReturnsList() {
    when(repository.findAll()).thenReturn(Arrays.asList(pricing));

    var result = service.getPricePerTon(ReturnOverFeedType.ALL);

    assertEquals(1, result.size());
    assertEquals("Corn", result.get(0).getName());
  }

  @Test
  void testGetPricePerTon_SpecificType_ReturnsList() {
    when(repository.findAllByType("HOME_GROWN_GRAINS")).thenReturn(Arrays.asList(pricing));

    var result = service.getPricePerTon(ReturnOverFeedType.HOME_GROWN_GRAINS);

    assertEquals(1, result.size());
    assertEquals("Corn", result.get(0).getName());
  }

  @Test
  void testGetPricePerTon_Empty_ReturnsEmptyList() {
    when(repository.findAllByType("PURCHASED_BULK_FEED")).thenReturn(Collections.emptyList());

    var result = service.getPricePerTon(ReturnOverFeedType.PURCHASE_BULK_FEED);

    assertTrue(result.isEmpty());
  }

  @Test
  void testUpdate_ValidId_UpdatesAndReturnsDto() {
    UUID id = pricing.getPricingReturnOverFeedDocument().getId();
    when(repository.findByUUID(id.toString())).thenReturn(pricing);

    ReturnOverFeedPricePerTonDto dto =
        ReturnOverFeedPricePerTonDto.builder()
            .id(id)
            .name("UpdatedCorn")
            .price(1500.0)
            .returnOverFeedType(ReturnOverFeedType.HOME_GROWN_GRAINS)
            .build();

    var result = service.update(dto);

    assertEquals("UpdatedCorn", result.getName());
    assertEquals(1500.0, result.getPrice());
    verify(repository, times(1)).save(any());
  }

  @Test
  void testUpdate_InvalidId_ThrowsException() {
    UUID id = UUID.randomUUID();
    when(repository.findByUUID(id.toString())).thenReturn(null);

    ReturnOverFeedPricePerTonDto dto =
        ReturnOverFeedPricePerTonDto.builder()
            .id(id)
            .name("NotFound")
            .price(100.0)
            .returnOverFeedType(ReturnOverFeedType.HOME_GROWN_FORAGES)
            .build();

    assertThrows(NotFoundDEException.class, () -> service.update(dto));
  }
}
