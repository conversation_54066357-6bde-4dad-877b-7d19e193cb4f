/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;

import com.app.cargill.constants.HttpMethods;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.S3PresignedUrlsDto;
import com.app.cargill.service.IS3Service;
import com.app.cargill.utils.MinimalPdf;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class AWSControllerTest {

  @InjectMocks private AWSController controller;
  @Mock IS3Service s3ServiceImpl;

  @Test
  void getS3PresignedUrls() {
    List<S3PresignedUrlsDto> urlInfos = new ArrayList<>();
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.PUT)
            .id("7531f4b0-48a9-11ed-b878-0242ac120002")
            .contentType("image/jpeg")
            .build());
    urlInfos.add(
        S3PresignedUrlsDto.builder()
            .urlType(HttpMethods.GET)
            .id("7531f4b0-48a9-11ed-b878-0242ac120002")
            .build());

    ResponseEntity<?> result = controller.generatePresignedUrls(urlInfos);

    ResponseEntityDto body = (ResponseEntityDto) result.getBody();
    assertNotNull(body.getData());
  }

  @Test
  void whenGetObjectFromS3ReturnsValidResult() throws IOException {
    Mockito.when(s3ServiceImpl.getObjectFromS3(any())).thenReturn(MinimalPdf.data);
    byte[] tests = s3ServiceImpl.getObjectFromS3("test");
    assertNotNull(tests);
  }

  @Test
  void searchObjects() {

    ResponseEntity<?> result = controller.searchFiles("TestFolder/testFile");
    ResponseEntityDto body = (ResponseEntityDto) result.getBody();
    assertNotNull(body.getData());
  }

  @Test
  void returnErrorWhenSearchKeyNotProvided() {

    ResponseEntity<?> result = controller.searchFiles("");
    assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
  }
}
