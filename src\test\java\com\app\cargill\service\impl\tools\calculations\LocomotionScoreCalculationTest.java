/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.*;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LocomotionScoreCalculationTest {
  @InjectMocks private LocomotionScoreCalculation locomotionScoreCalculation;

  @Test
  void calculateFields() {
    LocomotionTool locomotionTool =
        LocomotionTool.builder()
            .pens(
                List.of(
                    LocomotionToolItem.builder()
                        .categories(
                            List.of(
                                LocomotionToolItemCategoryItem.builder()
                                    .animalsObserved(15)
                                    .category(3)
                                    .totalCountOfAnimalsInPenPerScore(21)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .animalsObserved(16)
                                    .category(2)
                                    .totalCountOfAnimalsInPenPerScore(24)
                                    .build()))
                        .milkProductionInKg(12.2)
                        .totalAnimalsInPen(22)
                        .toolStatus(ToolStatuses.Completed)
                        .build()))
            .herd(
                LocomotionHerdToolItem.builder()
                    .pensForVisit(
                        List.of(
                            LocomotionToolItem.builder()
                                .categories(
                                    List.of(
                                        LocomotionToolItemCategoryItem.builder()
                                            .animalsObserved(15)
                                            .category(1)
                                            .percentOfPen(21.1)
                                            .totalCountOfAnimalsInPenPerScore(21)
                                            .build(),
                                        LocomotionToolItemCategoryItem.builder()
                                            .animalsObserved(16)
                                            .category(2)
                                            .percentOfPen(21.1)
                                            .totalCountOfAnimalsInPenPerScore(24)
                                            .build()))
                                .milkProductionInKg(12.2)
                                .totalAnimalsInPen(22)
                                .toolStatus(ToolStatuses.NotStarted)
                                .build()))
                    .categories(
                        List.of(
                            LocomotionHerdToolItemCategoryItem.builder()
                                .animalsObserved(22)
                                .category(3)
                                .totalAnimals(21)
                                .build(),
                            LocomotionHerdToolItemCategoryItem.builder()
                                .animalsObserved(22)
                                .category(2)
                                .totalAnimals(21)
                                .build()))
                    .milkPriceAtSiteLevel(51.1)
                    .totalAnimalsInHerd(31)
                    .build())
            .build();

    locomotionTool = locomotionScoreCalculation.calculateFields(locomotionTool);

    assertTrue(
        locomotionTool.getPens().stream()
            .allMatch(
                locomotionToolItem ->
                    locomotionToolItem.getCategories().stream()
                        .allMatch(
                            locomotionToolItemCategoryItem ->
                                locomotionToolItemCategoryItem != null
                                    && locomotionToolItemCategoryItem.getPercentOfPen() != null
                                    && locomotionToolItemCategoryItem
                                            .getTotalCountOfAnimalsInPenPerScore()
                                        != null
                                    && locomotionToolItem.getAverageLocomotionScore() != null
                                    && locomotionToolItem.getStandardDeviation() != null
                                    && locomotionToolItem.getMilkLoss() != null
                                    && locomotionToolItem.getToolStatus() != null)));

    LocomotionHerdToolItem locomotionHerdToolItem = locomotionTool.getHerd();

    assertTrue(
        locomotionHerdToolItem.getCategories().stream()
            .allMatch(
                locomotionHerdToolItemCategoryItem ->
                    locomotionHerdToolItemCategoryItem != null
                        && locomotionHerdToolItemCategoryItem.getAnimalsObserved() != null
                        && locomotionHerdToolItemCategoryItem.getHerdAverage() != null
                        && locomotionHerdToolItemCategoryItem.getTotalAnimals() != null));

    assertNotNull(locomotionHerdToolItem);
    assertNotNull(locomotionHerdToolItem.getAverageLocomotionScore());
    assertNotNull(locomotionHerdToolItem.getToolStatus());
    assertNotNull(locomotionHerdToolItem.getMilkLoss());
    assertNotNull(locomotionHerdToolItem.getMilkLossInKgPerDay());
    assertNotNull(locomotionHerdToolItem.getMilkLossInKgPerYear());
    assertNotNull(locomotionHerdToolItem.getRevenueLossInDollarPerDay());
    assertNotNull(locomotionHerdToolItem.getStandardDeviationScore());
  }

  @Test
  void calculateFieldsWithNullValues() {
    LocomotionTool locomotionTool =
        LocomotionTool.builder()
            .pens(
                List.of(
                    LocomotionToolItem.builder()
                        .categories(List.of(LocomotionToolItemCategoryItem.builder().build()))
                        .build()))
            .herd(
                LocomotionHerdToolItem.builder()
                    .pensForVisit(
                        List.of(
                            LocomotionToolItem.builder()
                                .categories(
                                    List.of(
                                        LocomotionToolItemCategoryItem.builder().build(),
                                        LocomotionToolItemCategoryItem.builder().build()))
                                .build()))
                    .categories(List.of(LocomotionHerdToolItemCategoryItem.builder().build()))
                    .build())
            .build();

    locomotionTool = locomotionScoreCalculation.calculateFields(locomotionTool);

    assertTrue(
        locomotionTool.getPens().stream()
            .allMatch(
                locomotionToolItem ->
                    locomotionToolItem.getCategories().stream()
                        .allMatch(
                            locomotionToolItemCategoryItem ->
                                locomotionToolItemCategoryItem != null
                                    && locomotionToolItemCategoryItem.getPercentOfPen() != null
                                    && locomotionToolItemCategoryItem
                                            .getTotalCountOfAnimalsInPenPerScore()
                                        != null
                                    && locomotionToolItem.getAverageLocomotionScore() != null
                                    && locomotionToolItem.getStandardDeviation() != null
                                    && locomotionToolItem.getMilkLoss() != null
                                    && locomotionToolItem.getToolStatus() != null)));

    LocomotionHerdToolItem locomotionHerdToolItem = locomotionTool.getHerd();

    assertTrue(
        locomotionHerdToolItem.getCategories().stream()
            .allMatch(
                locomotionHerdToolItemCategoryItem ->
                    locomotionHerdToolItemCategoryItem != null
                        && locomotionHerdToolItemCategoryItem.getAnimalsObserved() != null
                        && locomotionHerdToolItemCategoryItem.getHerdAverage() != null
                        && locomotionHerdToolItemCategoryItem.getTotalAnimals() != null));

    assertNotNull(locomotionHerdToolItem);
    assertNotNull(locomotionHerdToolItem.getAverageLocomotionScore());
    assertNotNull(locomotionHerdToolItem.getToolStatus());
    assertNotNull(locomotionHerdToolItem.getMilkLoss());
    assertNotNull(locomotionHerdToolItem.getMilkLossInKgPerDay());
    assertNotNull(locomotionHerdToolItem.getMilkLossInKgPerYear());
    assertNull(locomotionHerdToolItem.getRevenueLossInDollarPerDay());
    assertNull(locomotionHerdToolItem.getStandardDeviationScore());
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyCalculations() {
    UUID visitedPenId = UUID.randomUUID();
    LocomotionTool locomotionTool =
        LocomotionTool.builder()
            .pens(
                List.of(
                    LocomotionToolItem.builder()
                        .totalAnimalsInPen(12)
                        .daysInMilk(251)
                        .milkProductionInKg(25.0)
                        .categories(
                            List.of(
                                LocomotionToolItemCategoryItem.builder()
                                    .category(1)
                                    .animalsObserved(9)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(2)
                                    .animalsObserved(2)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(3)
                                    .animalsObserved(1)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(4)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(5)
                                    .animalsObserved(0)
                                    .build()))
                        .penId(visitedPenId)
                        .build(),
                    LocomotionToolItem.builder()
                        .totalAnimalsInPen(12)
                        .daysInMilk(276)
                        .milkProductionInKg(20.0)
                        .categories(
                            List.of(
                                LocomotionToolItemCategoryItem.builder()
                                    .category(1)
                                    .animalsObserved(12)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(2)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(3)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(4)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(5)
                                    .animalsObserved(0)
                                    .build()))
                        .penId(UUID.randomUUID())
                        .build(),
                    LocomotionToolItem.builder()
                        .totalAnimalsInPen(15)
                        .daysInMilk(167)
                        .milkProductionInKg(25.0)
                        .categories(
                            List.of(
                                LocomotionToolItemCategoryItem.builder()
                                    .category(1)
                                    .animalsObserved(2)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(2)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(3)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(4)
                                    .animalsObserved(0)
                                    .build(),
                                LocomotionToolItemCategoryItem.builder()
                                    .category(5)
                                    .animalsObserved(0)
                                    .build()))
                        .penId(UUID.randomUUID())
                        .build()))
            .herd(
                LocomotionHerdToolItem.builder()
                    .categories(
                        List.of(
                            LocomotionHerdToolItemCategoryItem.builder()
                                .category(1)
                                .herdGoal(75.0)
                                .build(),
                            LocomotionHerdToolItemCategoryItem.builder()
                                .category(2)
                                .herdGoal(15.0)
                                .build(),
                            LocomotionHerdToolItemCategoryItem.builder()
                                .category(3)
                                .herdGoal(9.0)
                                .build(),
                            LocomotionHerdToolItemCategoryItem.builder()
                                .category(4)
                                .herdGoal(0.5)
                                .build(),
                            LocomotionHerdToolItemCategoryItem.builder()
                                .category(5)
                                .herdGoal(0.5)
                                .build()))
                    .pensForVisit(
                        List.of(
                            LocomotionToolItem.builder()
                                .totalAnimalsInPen(12)
                                .daysInMilk(251)
                                .milkProductionInKg(25.0)
                                .categories(
                                    List.of(
                                        LocomotionToolItemCategoryItem.builder()
                                            .category(1)
                                            .animalsObserved(0)
                                            .build(),
                                        LocomotionToolItemCategoryItem.builder()
                                            .category(2)
                                            .animalsObserved(0)
                                            .build(),
                                        LocomotionToolItemCategoryItem.builder()
                                            .category(3)
                                            .animalsObserved(0)
                                            .build(),
                                        LocomotionToolItemCategoryItem.builder()
                                            .category(4)
                                            .animalsObserved(0)
                                            .build(),
                                        LocomotionToolItemCategoryItem.builder()
                                            .category(5)
                                            .animalsObserved(0)
                                            .build()))
                                .penId(visitedPenId)
                                .build()))
                    .daysInMilk(0.0)
                    .totalAnimalsInHerd(29)
                    .milkProductionInKg(19.0)
                    .milkPriceAtSiteLevel(41.0)
                    .build())
            .build();

    locomotionTool = locomotionScoreCalculation.calculateFields(locomotionTool);

    assertEquals(75.0, locomotionTool.getPens().get(0).getCategories().get(0).getPercentOfPen());
    assertEquals(
        9,
        locomotionTool
            .getPens()
            .get(0)
            .getCategories()
            .get(0)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(16.7, locomotionTool.getPens().get(0).getCategories().get(1).getPercentOfPen());
    assertEquals(
        2,
        locomotionTool
            .getPens()
            .get(0)
            .getCategories()
            .get(1)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(8.3, locomotionTool.getPens().get(0).getCategories().get(2).getPercentOfPen());
    assertEquals(
        1,
        locomotionTool
            .getPens()
            .get(0)
            .getCategories()
            .get(2)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(0).getCategories().get(3).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(0)
            .getCategories()
            .get(3)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(0).getCategories().get(4).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(0)
            .getCategories()
            .get(4)
            .getTotalCountOfAnimalsInPenPerScore());

    assertEquals(1.3, locomotionTool.getPens().get(0).getAverageLocomotionScore());
    assertEquals(0.62, locomotionTool.getPens().get(0).getStandardDeviation());
    assertEquals(0.11, locomotionTool.getPens().get(0).getMilkLoss());
    assertEquals(ToolStatuses.Completed, locomotionTool.getPens().get(0).getToolStatus());

    assertEquals(100.0, locomotionTool.getPens().get(1).getCategories().get(0).getPercentOfPen());
    assertEquals(
        12,
        locomotionTool
            .getPens()
            .get(1)
            .getCategories()
            .get(0)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(1).getCategories().get(1).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(1)
            .getCategories()
            .get(1)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(1).getCategories().get(2).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(1)
            .getCategories()
            .get(2)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(1).getCategories().get(3).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(1)
            .getCategories()
            .get(3)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(1).getCategories().get(4).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(1)
            .getCategories()
            .get(4)
            .getTotalCountOfAnimalsInPenPerScore());

    assertEquals(1.0, locomotionTool.getPens().get(1).getAverageLocomotionScore());
    assertEquals(0.0, locomotionTool.getPens().get(1).getStandardDeviation());
    assertEquals(0.0, locomotionTool.getPens().get(1).getMilkLoss());
    assertEquals(ToolStatuses.Completed, locomotionTool.getPens().get(1).getToolStatus());

    assertEquals(100.0, locomotionTool.getPens().get(2).getCategories().get(0).getPercentOfPen());
    assertEquals(
        15,
        locomotionTool
            .getPens()
            .get(2)
            .getCategories()
            .get(0)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(2).getCategories().get(1).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(2)
            .getCategories()
            .get(1)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(2).getCategories().get(2).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(2)
            .getCategories()
            .get(2)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(2).getCategories().get(3).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(2)
            .getCategories()
            .get(3)
            .getTotalCountOfAnimalsInPenPerScore());
    assertEquals(0.0, locomotionTool.getPens().get(2).getCategories().get(4).getPercentOfPen());
    assertEquals(
        0,
        locomotionTool
            .getPens()
            .get(2)
            .getCategories()
            .get(4)
            .getTotalCountOfAnimalsInPenPerScore());

    assertEquals(1.0, locomotionTool.getPens().get(2).getAverageLocomotionScore());
    assertEquals(0.0, locomotionTool.getPens().get(2).getStandardDeviation());
    assertEquals(0.0, locomotionTool.getPens().get(2).getMilkLoss());
    assertEquals(ToolStatuses.Completed, locomotionTool.getPens().get(2).getToolStatus());

    assertEquals(1.0923076923076924, locomotionTool.getHerd().getAverageLocomotionScore());
    assertEquals(ToolStatuses.Completed, locomotionTool.getHerd().getToolStatus());
    assertEquals(0.0, locomotionTool.getHerd().getMilkLoss());
    assertEquals(0.0, locomotionTool.getHerd().getMilkLossInKgPerDay());
    assertEquals(0.0, locomotionTool.getHerd().getMilkLossInKgPerYear());
    assertEquals(0.0, locomotionTool.getHerd().getRevenueLossInDollarPerDay());
    assertEquals(0.0, locomotionTool.getHerd().getRevenueLossInDollarPerYear());
    assertNull(locomotionTool.getHerd().getStandardDeviationScore());

    assertEquals(0, locomotionTool.getHerd().getCategories().get(0).getAnimalsObserved());
    assertEquals(0.0, locomotionTool.getHerd().getCategories().get(0).getHerdAverage());
    assertEquals(0, locomotionTool.getHerd().getCategories().get(0).getTotalAnimals());

    assertEquals(0, locomotionTool.getHerd().getCategories().get(1).getAnimalsObserved());
    assertEquals(0.0, locomotionTool.getHerd().getCategories().get(1).getHerdAverage());
    assertEquals(0, locomotionTool.getHerd().getCategories().get(1).getTotalAnimals());

    assertEquals(0, locomotionTool.getHerd().getCategories().get(2).getAnimalsObserved());
    assertEquals(0.0, locomotionTool.getHerd().getCategories().get(2).getHerdAverage());
    assertEquals(0, locomotionTool.getHerd().getCategories().get(2).getTotalAnimals());

    assertEquals(0, locomotionTool.getHerd().getCategories().get(3).getAnimalsObserved());
    assertEquals(0.0, locomotionTool.getHerd().getCategories().get(3).getHerdAverage());
    assertEquals(0, locomotionTool.getHerd().getCategories().get(3).getTotalAnimals());

    assertEquals(0, locomotionTool.getHerd().getCategories().get(4).getAnimalsObserved());
    assertEquals(0.0, locomotionTool.getHerd().getCategories().get(4).getHerdAverage());
    assertEquals(0, locomotionTool.getHerd().getCategories().get(4).getTotalAnimals());
  }
}
