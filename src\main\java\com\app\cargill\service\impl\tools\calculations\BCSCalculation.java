/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.BodyConditionTool;
import com.app.cargill.document.BodyConditionToolItem;
import com.app.cargill.document.BodyConditionToolItemScoreItem;
import java.util.List;
import java.util.Objects;

public class BCSCalculation {

  public BodyConditionTool calculateFields(BodyConditionTool tool) {
    if (tool == null) return null;

    if (tool.getPens() != null) {
      for (BodyConditionToolItem pen : tool.getPens()) {
        if (pen.getBodyConditionScores() != null) {
          int totalObservedAnimals =
              pen.getBodyConditionScores().stream()
                  .filter(i -> i.getAnimalsObserved() != null)
                  .mapToInt(BodyConditionToolItemScoreItem::getAnimalsObserved)
                  .sum();

          for (BodyConditionToolItemScoreItem item : pen.getBodyConditionScores()) {
            item.percentOfPen = percentOfPen(item, totalObservedAnimals);
            item.selectedPointScale = tool.getSelectedPointScale();
          }
          pen.averageBCS = averageBCS(pen);
          pen.standardDeviationBCS = standardDeviationBCS(pen);
        }
      }
    }
    return tool;
  }

  private Double averageBCS(BodyConditionToolItem pen) {
    double sumOfPercentOfPen = 0.0;
    double sumOfPercentOfPenTimesBCS = 0.0;

    for (BodyConditionToolItemScoreItem score : pen.getBodyConditionScores()) {
      if (score != null && score.getPercentOfPen() != null) {
        sumOfPercentOfPen += score.getPercentOfPen();
        sumOfPercentOfPenTimesBCS +=
            (score.getPercentOfPen() * Objects.requireNonNullElse(score.getBcsCategory(), 0.0));
      }
    }

    if (sumOfPercentOfPen == 0) return 0.0;

    return roundAvoid(sumOfPercentOfPenTimesBCS / sumOfPercentOfPen, 2);
  }

  private Double standardDeviationBCS(BodyConditionToolItem pen) {
    Double avgBCS = pen.getAverageBCS();
    double sumOfSquareDelta = 0.0;
    int n = 0;
    final int perAnimal = 1;

    List<BodyConditionToolItemScoreItem> activeBCSs =
        pen.getBodyConditionScores().stream()
            .filter(arg -> arg.percentOfPen != null && arg.percentOfPen > 0)
            .toList();

    for (BodyConditionToolItemScoreItem score : activeBCSs) {
      int i = 0;
      for (; i < Objects.requireNonNullElse(score.animalsObserved, 0); i++) {
        double delta =
            Math.abs((perAnimal * Objects.requireNonNullElse(score.bcsCategory, 0.0) - avgBCS));
        double sqDelta = Math.pow(delta, 2);
        sumOfSquareDelta += sqDelta;
      }
      n += i;
    }

    if (n == 0) return 0.0;
    double variance = sumOfSquareDelta / n;
    double sd = Math.sqrt(variance);
    return roundAvoid(sd, 2);
  }

  private Double percentOfPen(BodyConditionToolItemScoreItem penItem, int total) {
    if (penItem.animalsObserved == null || penItem.animalsObserved == 0 || total == 0) return 0.0;
    return roundAvoid(((double) penItem.animalsObserved / total) * 100, 1);
  }
}
