/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import java.text.DecimalFormat;

public class MathUtils {

  private MathUtils() {}

  public static double roundAvoid(double value, int places) {
    DecimalFormat decimalFormat = new DecimalFormat("#." + "0".repeat(places));
    return Double.parseDouble(decimalFormat.format(value));
  }

  public static int customRound(String str) {
    try {
      if (str != null && !str.isEmpty()) {
        double doubleValue = Double.parseDouble(str);
        double roundedValue = getRoundedValue(doubleValue);

        return (int) roundedValue;
      }
      return 0;
    } catch (NumberFormatException e) {
      // Handle the case where the input string is not a valid double
      // You can choose to return a default value or throw an exception as needed
      throw new IllegalArgumentException("Input string is not a valid double: " + str);
    }
  }

  private static double getRoundedValue(double doubleValue) {
    double roundedValue;

    int integerPart = (int) doubleValue;
    // Check if the value is odd and has 5 after the decimal point
    if (integerPart % 2 != 0 && (doubleValue * 10) % 10 == 5) {
      // Round the value
      roundedValue = Math.round(doubleValue);
      //                Check if the value is even and has 5 after the decimal point
    } else if (integerPart % 2 == 0 && (doubleValue * 10) % 10 == 5) {
      // Don't round the value
      roundedValue = doubleValue;
    } else {
      roundedValue = Math.round(doubleValue);
    }
    return roundedValue;
  }
}
