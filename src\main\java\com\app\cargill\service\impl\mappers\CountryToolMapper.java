/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers;

import com.app.cargill.dto.CountryToolResponseDto;
import com.app.cargill.model.CountryTools;

public class CountryToolMapper {

  private CountryToolMapper() {}

  public static CountryToolResponseDto mapToDto(CountryTools countryTool) {
    return CountryToolResponseDto.builder()
        .toolGroupId(countryTool.getCountryToolDocument().getToolGroupId())
        .toolId(countryTool.getCountryToolDocument().getToolId())
        .build();
  }
}
