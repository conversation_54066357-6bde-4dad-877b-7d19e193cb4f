/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.document.MetabolicIncidenceOutputToolItem;
import com.app.cargill.document.MetabolicIncidenceTool;
import com.app.cargill.document.MetabolicIncidenceToolItem;
import com.app.cargill.document.VisitDocument;
import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.impl.CdpServiceImpl;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class MetabolicIncidenceCalculationTest {

  @InjectMocks private MetabolicIncidenceCalculation metabolicIncidenceCalculation;

  @Mock private VisitsRepository visitsRepository;

  @Mock private SitesRepository sitesRepository;

  @InjectMocks private CdpServiceImpl cdpService;

  @Test
  void calculateFields() {
    MetabolicIncidenceTool metabolicIncidenceTool =
        MetabolicIncidenceTool.builder()
            .visitMetabolicIncidenceData(
                MetabolicIncidenceToolItem.builder()
                    .outputs(
                        MetabolicIncidenceOutputToolItem.builder()
                            .retainedPlacentaGoal(11.1)
                            .metritisGoal(11.1)
                            .displacedAbomasumGoal(11.1)
                            .ketosisGoal(11.1)
                            .milkFeverGoal(11.1)
                            .deathLossGoal(11.1)
                            .dystociaGoal(11.1)
                            .build())
                    .totalFreshCowsPerYear(12)
                    .replacementCowCost(12.1)
                    .costOfExtraDaysOpen(12.1)
                    .totalFreshCowsForEvaluation(12)
                    .retainedPlacentaIncidence(12)
                    .metritisIncidence(12)
                    .displacedAbomasumIncidence(12)
                    .ketosisIncidence(12)
                    .milkFeverIncidence(12)
                    .dystociaIncidence(12)
                    .deathLossIncidence(12)
                    .retainedPlacentaWeight(12.1)
                    .retainedPlacentaDaysOpen(12)
                    .retainedPlacentaCost(12.1)
                    .metritisWeight(12.1)
                    .metritisDaysOpen(12)
                    .metritisCost(12.1)
                    .displacedAbomasumWeight(12.1)
                    .displacedAbomasumDaysOpen(12)
                    .displacedAbomasumCost(12.1)
                    .ketosisWeight(12.1)
                    .ketosisDaysOpen(12)
                    .ketosisCost(12.1)
                    .milkFeverWeight(12.1)
                    .milkFeverDaysOpen(12)
                    .milkFeverCost(12.1)
                    .dystociaWeight(12.1)
                    .dystociaOpen(12)
                    .dystociaCost(12.1)
                    .deathLossWeight(12.1)
                    .deathLossOpen(1)
                    .deathLossCost(11.1)
                    .milkPrice(12.1)
                    .build())
            .build();

    metabolicIncidenceTool = metabolicIncidenceCalculation.calculateFields(metabolicIncidenceTool);
    assertNotNull(metabolicIncidenceTool);
    assertNotNull(metabolicIncidenceTool.getVisitMetabolicIncidenceData());

    MetabolicIncidenceOutputToolItem metabolicIncidenceOutputToolItem =
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs();

    assertTrue(
        metabolicIncidenceOutputToolItem.getRetainedPlacentaIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getMetritisIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getKetosisIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getDystociaIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getDeathLossIncidencePercent() != null
            && metabolicIncidenceOutputToolItem.getRetainedPlacentaIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getMetritisIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getKetosisIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getDystociaIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getDeathLossIncidenceDifference() != null
            && metabolicIncidenceOutputToolItem.getRetainedPlacentaMilkLoss() != null
            && metabolicIncidenceOutputToolItem.getMetritisMilkLoss() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumMilkLoss() != null
            && metabolicIncidenceOutputToolItem.getKetosisMilkLoss() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverMilkLoss() != null
            && metabolicIncidenceOutputToolItem.getDystociaMilkLoss() != null
            && metabolicIncidenceOutputToolItem.getMetritisIncreasedDaysOpen() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumIncreasedDaysOpen() != null
            && metabolicIncidenceOutputToolItem.getKetosisIncreasedDaysOpen() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverIncreasedDaysOpen() != null
            && metabolicIncidenceOutputToolItem.getRetainedPlacentaIncreasedDaysOpen() != null
            && metabolicIncidenceOutputToolItem.getDystociaIncreasedDaysOpen() != null
            && metabolicIncidenceOutputToolItem.getMetritisTreatmentCost() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumTreatmentCost() != null
            && metabolicIncidenceOutputToolItem.getKetosisTreatmentCost() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverTreatmentCost() != null
            && metabolicIncidenceOutputToolItem.getRetainedPlacentaTreatmentCost() != null
            && metabolicIncidenceOutputToolItem.getDystociaTreatmentCost() != null
            && metabolicIncidenceOutputToolItem.getMilkLossTotalLosses() != null
            && metabolicIncidenceOutputToolItem.getIncreasedDaysOpenTotalLosses() != null
            && metabolicIncidenceOutputToolItem.getTreatmentCostTotalLosses() != null
            && metabolicIncidenceOutputToolItem.getRetainedPlacentaTotalCost() != null
            && metabolicIncidenceOutputToolItem.getMetritisTotalCost() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumTotalCost() != null
            && metabolicIncidenceOutputToolItem.getKetosisTotalCost() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverTotalCost() != null
            && metabolicIncidenceOutputToolItem.getDystociaTotalCost() != null
            && metabolicIncidenceOutputToolItem.getDeathLossTotalCost() != null
            && metabolicIncidenceOutputToolItem.getTotalCost() != null
            && metabolicIncidenceOutputToolItem.getRetainedPlacentaCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getMetritisCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getDisplacedAbomasumCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getKetosisCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getMilkFeverCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getDystociaCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getDeathLossCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getTotalCostPerCow() != null
            && metabolicIncidenceOutputToolItem.getToolStatus() != null);
  }

  @SuppressWarnings("java:S5961")
  @Test
  void calculateFieldsWithNullValues() {
    MetabolicIncidenceTool metabolicIncidenceTool =
        MetabolicIncidenceTool.builder()
            .visitMetabolicIncidenceData(
                MetabolicIncidenceToolItem.builder()
                    .outputs(MetabolicIncidenceOutputToolItem.builder().build())
                    .build())
            .build();

    metabolicIncidenceTool = metabolicIncidenceCalculation.calculateFields(metabolicIncidenceTool);
    assertNotNull(metabolicIncidenceTool);
    assertNotNull(metabolicIncidenceTool.getVisitMetabolicIncidenceData());

    MetabolicIncidenceOutputToolItem metabolicIncidenceOutputToolItem =
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs();

    assertEquals(0.0, metabolicIncidenceOutputToolItem.getRetainedPlacentaIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getMetritisIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getDisplacedAbomasumIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getKetosisIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getMilkFeverIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getDystociaIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getDeathLossIncidencePercent());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getRetainedPlacentaIncidenceDifference());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getMetritisIncidenceDifference());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getDisplacedAbomasumIncidenceDifference());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getKetosisIncidenceDifference());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getMilkFeverIncidenceDifference());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getDystociaIncidenceDifference());
    assertEquals(0.0, metabolicIncidenceOutputToolItem.getDeathLossIncidenceDifference());
    assertEquals(0, metabolicIncidenceOutputToolItem.getRetainedPlacentaMilkLoss());
    assertEquals(0, metabolicIncidenceOutputToolItem.getMetritisMilkLoss());
    assertEquals(0, metabolicIncidenceOutputToolItem.getDisplacedAbomasumMilkLoss());
    assertEquals(0, metabolicIncidenceOutputToolItem.getKetosisMilkLoss());
    assertEquals(0, metabolicIncidenceOutputToolItem.getMilkFeverMilkLoss());
    assertEquals(0, metabolicIncidenceOutputToolItem.getDystociaMilkLoss());
    assertNull(metabolicIncidenceOutputToolItem.getMetritisIncreasedDaysOpen());
    assertNull(metabolicIncidenceOutputToolItem.getDisplacedAbomasumIncreasedDaysOpen());
    assertNull(metabolicIncidenceOutputToolItem.getKetosisIncreasedDaysOpen());
    assertNull(metabolicIncidenceOutputToolItem.getMilkFeverIncreasedDaysOpen());
    assertNull(metabolicIncidenceOutputToolItem.getRetainedPlacentaIncreasedDaysOpen());
    assertNull(metabolicIncidenceOutputToolItem.getDystociaIncreasedDaysOpen());
    assertNull(metabolicIncidenceOutputToolItem.getMetritisTreatmentCost());
    assertNull(metabolicIncidenceOutputToolItem.getDisplacedAbomasumTreatmentCost());
    assertNull(metabolicIncidenceOutputToolItem.getKetosisTreatmentCost());
    assertNull(metabolicIncidenceOutputToolItem.getMilkFeverTreatmentCost());
    assertNull(metabolicIncidenceOutputToolItem.getRetainedPlacentaTreatmentCost());
    assertNull(metabolicIncidenceOutputToolItem.getDystociaTreatmentCost());
    assertNull(metabolicIncidenceOutputToolItem.getMilkLossTotalLosses());
    assertNull(metabolicIncidenceOutputToolItem.getIncreasedDaysOpenTotalLosses());
    assertNull(metabolicIncidenceOutputToolItem.getTreatmentCostTotalLosses());
    assertEquals(0, metabolicIncidenceOutputToolItem.getRetainedPlacentaTotalCost());
    assertEquals(0, metabolicIncidenceOutputToolItem.getMetritisTotalCost());
    assertEquals(0, metabolicIncidenceOutputToolItem.getDisplacedAbomasumTotalCost());
    assertEquals(0, metabolicIncidenceOutputToolItem.getKetosisTotalCost());
    assertEquals(0, metabolicIncidenceOutputToolItem.getMilkFeverTotalCost());
    assertEquals(0, metabolicIncidenceOutputToolItem.getDystociaTotalCost());
    assertNull(metabolicIncidenceOutputToolItem.getDeathLossTotalCost());
    assertNull(metabolicIncidenceOutputToolItem.getTotalCost());
    assertTrue(metabolicIncidenceOutputToolItem.getRetainedPlacentaCostPerCow() <= 0.0);
    assertTrue(metabolicIncidenceOutputToolItem.getMetritisCostPerCow() <= 0.0);
    assertTrue(metabolicIncidenceOutputToolItem.getDisplacedAbomasumCostPerCow() <= 0.0);
    assertTrue(metabolicIncidenceOutputToolItem.getKetosisCostPerCow() <= 0.0);
    assertTrue(metabolicIncidenceOutputToolItem.getMilkFeverCostPerCow() <= 0.0);
    assertTrue(metabolicIncidenceOutputToolItem.getDystociaCostPerCow() <= 0.0);
    assertTrue(metabolicIncidenceOutputToolItem.getDeathLossCostPerCow() <= 0.0);
    assertNull(metabolicIncidenceOutputToolItem.getTotalCostPerCow());
    // assertEquals(0.0, metabolicIncidenceOutputToolItem.getToolStatus());
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyCalculations() {
    MetabolicIncidenceTool metabolicIncidenceTool =
        MetabolicIncidenceTool.builder()
            .visitMetabolicIncidenceData(
                MetabolicIncidenceToolItem.builder()
                    .outputs(
                        MetabolicIncidenceOutputToolItem.builder()
                            .retainedPlacentaGoal(6.0)
                            .metritisGoal(6.0)
                            .displacedAbomasumGoal(4.0)
                            .ketosisGoal(4.0)
                            .milkFeverGoal(4.0)
                            .deathLossGoal(0.0)
                            .dystociaGoal(4.0)
                            .build())
                    .totalFreshCowsPerYear(44)
                    .replacementCowCost(223263.75)
                    .costOfExtraDaysOpen(59537.0)
                    .totalFreshCowsForEvaluation(44)
                    .retainedPlacentaIncidence(24)
                    .metritisIncidence(24)
                    .milkFeverIncidence(1)
                    .dystociaIncidence(1)
                    .deathLossIncidence(1)
                    .retainedPlacentaWeight(264.0)
                    .retainedPlacentaDaysOpen(16)
                    .retainedPlacentaCost(7218.86125)
                    .metritisWeight(18.0)
                    .metritisDaysOpen(16)
                    .metritisCost(9377.0775)
                    .displacedAbomasumWeight(595.0)
                    .displacedAbomasumDaysOpen(16)
                    .displacedAbomasumCost(20093.7375)
                    .ketosisWeight(72.0)
                    .ketosisDaysOpen(10)
                    .ketosisCost(3497.79875)
                    .milkFeverWeight(130.0)
                    .milkFeverDaysOpen(21)
                    .milkFeverCost(11386.45125)
                    .dystociaWeight(177.0)
                    .dystociaOpen(12)
                    .dystociaCost(9153.81375)
                    .deathLossCost(8335.18)
                    .deathLossOpen(15)
                    .deathLossWeight(391.0)
                    .milkPrice(41.0)
                    .build())
            .build();

    metabolicIncidenceTool = metabolicIncidenceCalculation.calculateFields(metabolicIncidenceTool);

    assertEquals(
        54.5,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaIncidencePercent);
    assertEquals(
        54.5,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .metritisIncidencePercent);
    assertEquals(
        0.0,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumIncidencePercent);
    assertEquals(
        0.0,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .ketosisIncidencePercent);
    assertEquals(
        2.3,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .milkFeverIncidencePercent);
    assertEquals(
        2.3,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .dystociaIncidencePercent);
    assertEquals(
        2.3,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .deathLossIncidencePercent);
    assertEquals(
        48.5,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaIncidenceDifference);
    assertEquals(
        48.5,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .metritisIncidenceDifference);
    assertEquals(
        -4.0,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumIncidenceDifference);
    assertEquals(
        -4.0,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .ketosisIncidenceDifference);
    assertEquals(
        -1.7,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .milkFeverIncidenceDifference);
    assertEquals(
        -1.7,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .dystociaIncidenceDifference);
    assertEquals(
        2.3,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .deathLossIncidenceDifference);
    assertEquals(
        230984,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaMilkLoss);
    assertEquals(
        15749,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().metritisMilkLoss);
    assertEquals(
        -42935,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumMilkLoss);
    assertEquals(
        -5196,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().ketosisMilkLoss);
    assertEquals(
        -3987,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().milkFeverMilkLoss);
    assertEquals(
        -5428,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().dystociaMilkLoss);
    assertEquals(
        20328313,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .metritisIncreasedDaysOpen);
    assertEquals(
        -1676562,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumIncreasedDaysOpen);
    assertEquals(
        -1047851,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .ketosisIncreasedDaysOpen);
    assertEquals(
        -935207,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .milkFeverIncreasedDaysOpen);
    assertEquals(
        20328313,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaIncreasedDaysOpen);
    assertEquals(
        -534404,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .dystociaIncreasedDaysOpen);
    assertEquals(
        200107,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().metritisTreatmentCost);
    assertEquals(
        -35365,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumTreatmentCost);
    assertEquals(
        -6156,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().ketosisTreatmentCost);
    assertEquals(
        -8517,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .milkFeverTreatmentCost);
    assertEquals(
        154050,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaTreatmentCost);
    assertEquals(
        -6847,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().dystociaTreatmentCost);
    assertEquals(
        246733,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().milkLossTotalLosses);
    assertEquals(
        40656626,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .increasedDaysOpenTotalLosses);
    assertEquals(
        354157,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .treatmentCostTotalLosses);
    assertEquals(
        20713347,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaTotalCost);
    assertEquals(
        20544169,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().metritisTotalCost);
    assertEquals(
        -1754862,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumTotalCost);
    assertEquals(
        -1059203,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().ketosisTotalCost);
    assertEquals(
        -947711,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().milkFeverTotalCost);
    assertEquals(
        -546679,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().dystociaTotalCost);
    assertEquals(
        225943,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().deathLossTotalCost);
    assertEquals(
        41483459, metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().totalCost);
    assertEquals(
        470757.89,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .retainedPlacentaCostPerCow);
    assertEquals(
        466912.93,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().metritisCostPerCow);
    assertEquals(
        -39883.23,
        metabolicIncidenceTool
            .getVisitMetabolicIncidenceData()
            .getOutputs()
            .displacedAbomasumCostPerCow);
    assertEquals(
        -24072.80,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().ketosisCostPerCow);
    assertEquals(
        -21538.89,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().milkFeverCostPerCow);
    assertEquals(
        -12424.52,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().dystociaCostPerCow);
    assertEquals(
        5135.07,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().deathLossCostPerCow);
    assertEquals(
        942805.89,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().totalCostPerCow);
    assertEquals(
        ToolStatuses.Completed,
        metabolicIncidenceTool.getVisitMetabolicIncidenceData().getOutputs().toolStatus);
  }

  @Test
  void verifyNameAndToolName() {
    MetabolicIncidenceTool metabolicIncidenceTool =
        MetabolicIncidenceTool.builder()
            .visitMetabolicIncidenceData(
                MetabolicIncidenceToolItem.builder()
                    .outputs(
                        MetabolicIncidenceOutputToolItem.builder()
                            .retainedPlacentaGoal(6.0)
                            .metritisGoal(6.0)
                            .displacedAbomasumGoal(4.0)
                            .ketosisGoal(4.0)
                            .milkFeverGoal(4.0)
                            .deathLossGoal(0.0)
                            .dystociaGoal(4.0)
                            .build())
                    .totalFreshCowsPerYear(30)
                    .replacementCowCost(50000.0)
                    .costOfExtraDaysOpen(470.0)
                    .totalFreshCowsForEvaluation(27)
                    .retainedPlacentaIncidence(1)
                    .metritisIncidence(1)
                    .milkFeverIncidence(0)
                    .dystociaIncidence(0)
                    .retainedPlacentaWeight(264.0)
                    .retainedPlacentaDaysOpen(16)
                    .retainedPlacentaCost(7218.86125)
                    .metritisWeight(18.0)
                    .metritisDaysOpen(16)
                    .metritisCost(9377.0775)
                    .displacedAbomasumWeight(595.0)
                    .displacedAbomasumDaysOpen(16)
                    .displacedAbomasumCost(20093.7375)
                    .ketosisWeight(72.0)
                    .ketosisDaysOpen(10)
                    .ketosisCost(3497.79875)
                    .milkFeverWeight(130.0)
                    .milkFeverDaysOpen(21)
                    .milkFeverCost(11386.45125)
                    .dystociaWeight(177.0)
                    .dystociaOpen(12)
                    .dystociaCost(9153.81375)
                    .deathLossCost(8335.18)
                    .deathLossOpen(15)
                    .deathLossWeight(391.0)
                    .milkPrice(38.0)
                    .build())
            .visitId(UUID.fromString("54d12714-baf9-4920-a133-7b874ad82eda"))
            .build();

    VisitDocument visitDocument =
        VisitDocument.builder()
            .siteId(UUID.fromString("54d12714-baf9-4920-a133-7b874ad82eda"))
            .customerId(UUID.fromString("534b1e3d-8717-4ec8-80ea-4c972c26d619"))
            .visitDate(Instant.parse("2023-08-16T09:12:18.118148Z"))
            .firstName("Bhajan Dass")
            .selected(false)
            .visitName("August 16, 2023")
            .status(VisitStatus.Published)
            .formattedCreationDate("August 8, 2023")
            .needsSync(true)
            .metabolicIncidence(metabolicIncidenceTool)
            .build();

    List<Visits> visits =
        List.of(
            Visits.builder()
                .id(1L)
                .localId(UUID.randomUUID().toString())
                .visitDocument(visitDocument)
                .build());

    Page<Visits> visitPages = new PageImpl<>(visits);

    when(visitsRepository.findAllVisitsByFromAndToDate(any(), any(), any())).thenReturn(visitPages);

    when(sitesRepository.findMilkBySiteIds(any())).thenReturn(new ArrayList<>());

    Page<VisitDocumentDTO> response = cdpService.getAllVisitsByFromAndToDate(any(), any(), any());

    assertNotNull(response);

    List<AttributesDTO> filteredList =
        response.getContent().get(0).getAttributes().stream()
            .filter(
                attributesDTO ->
                    attributesDTO != null
                        && attributesDTO
                            .getName()
                            .equalsIgnoreCase(
                                "VisitMetabolicIncidenceData/Outputs/RetainedPlacentaGoal"))
            .toList();

    assertEquals(
        "VisitMetabolicIncidenceData/Outputs/RetainedPlacentaGoal", filteredList.get(0).getName());
    assertEquals(BigDecimal.valueOf(6.0), filteredList.get(0).getNumericValue());
  }
}
