[{"GoldenRecordId": "string", "AccountName": "string", "LegalName": "string", "AccountType": "Standard", "Contacts": [{"AccountId": "string", "GoldenRecordAcountId": "string", "SFDCContactId": "string", "FirstName": "string", "LastName": "string", "FunctionId": "Owner", "PhoneNumber": "string", "MobilePhoneNumber": "string", "EmailAddress": "string", "Comments": "string", "Salutation": "string", "GoldenRecordId": "string", "NameId": 0, "Title": "string", "MailingAddress": {"Street": "string", "City": "string", "StateOrProvince": "string", "PostalCode": "string", "Country": "string", "AddressID": "string", "CountyCommunity": "string"}, "OtherAddress": {"Street": "string", "City": "string", "StateOrProvince": "string", "PostalCode": "string", "Country": "string", "AddressID": "string", "CountyCommunity": "string"}, "Phone": "string", "Mobile": "string", "EmailId": "string", "Description": "string", "PrimaryOwner": true, "ContactOwner": "string", "BusinessId": "Brazil", "PrimaryLangId": "Afrikaans", "SecondaryLangId": "Afrikaans", "ReportsToID": "string", "ExternalReportsToID": "string", "SecondaryEmail": "string", "PreferredMethodId": "Email", "Assitant": "string", "AssitantPhone": "string", "Website": "string", "Department": "string", "UserID": "string", "ContactCurrency": "NotSet", "CreatedBy": "string", "DataDotComKey": "string", "DoNotCall": true, "EmailOptOut": true, "ExternalId": "string", "Fax": "string", "FaxOptOut": true, "FlowName": "string", "HomePhone": "string", "Languages": "string", "LastModifiedBy": "string", "LastModifiedID": "string", "LastStayInTouchRequestDate": "2023-04-10T13:00:36.219Z", "LastStayInTouchSaveDate": "2023-04-10T13:00:36.219Z", "LeadSource": "Advertisement", "Level": "Secondary", "MobileFirst": true, "OtherPhoneInUse": "string", "Primary": true, "MarketingId": ["SM"], "TempMarketingId": "string", "id": "string", "CreateUser": "string", "IsDeleted": true, "LastModifyUser": "string", "CreateTimeUtc": "2023-04-10T13:00:36.219Z", "LastModifiedTimeUtc": "2023-04-10T13:00:36.219Z", "LastSyncTimeUtc": "2023-04-10T13:00:36.219Z", "IsNew": true}], "Users": ["string"], "ProspectValidated": true, "OwnerId": "string", "ApplicationMapping": [{"ExternalSystemId": "string", "ExternalSystemName": "string", "SFDCAccountId": "string", "LabyrinthAccountId": "string", "XRefExternalSystemId": "string", "XRefExternalSystemName": "string"}], "IsDuplicate": true, "AutoValidate": true, "SourceSystem": "string", "Type": "string", "SocialMediaAddress": "string", "WebSiteAddress": "string", "ParentAccountID": "string", "ExternalParentAccountID": "string", "BuyingGroupID": "string", "ExternalBuyingGroupID": "string", "SubTypeID": "<PERSON>n", "ExternalLeadSourceID": "Advertisement", "AccountStatus": "New", "DateOfLastVisit": "2023-04-10T13:00:36.219Z", "DateOfLastCall": "2023-04-10T13:00:36.219Z", "WonLostId": "Won", "WonLostComments": "string", "CreditFlag": true, "PriceFlag": true, "ServiceFlag": true, "PerformanceFlag": true, "PortfolioFlag": true, "BusinessSolutionFlag": true, "QualityFlag": true, "OtherFlag": true, "BusinessID": "Brazil", "DefaultCargillPlantID": "Abilene, TX - (49)", "DefaultCustServiceID": "R0 - Pet Food", "LastModificationDate": {"Date": "2023-04-10T13:00:36.219Z", "Epoch": 0}, "Active": true, "BrandId": "Cargill", "NineBoxStepTwoID": "0 - Not Defined", "SegmentStepOneId": "0 - Not Defined", "CompanyEmail": "string", "LastInvoiceDate": "2023-04-10T13:00:36.219Z", "LastOrderDate": "2023-04-10T13:00:36.219Z", "DeliveryInstructions": "string", "ERPPayerId": "string", "ERPShipToId": "string", "IsServicedbyCSPro": true, "LastAdminUpdate": "2023-04-10T13:00:36.219Z", "LastInvoicesInfo": "string", "LastOrdersInfo": "string", "Phone": "string", "ReqProcessingLog": "string", "Liabilities": "string", "LimitChangeReasonId": "New Customer", "MarketInfluencer": true, "OtherActivityProduction": "string", "PersonalID": "string", "PreviousStatus": "string", "ReasonDescription": "string", "Securities": "string", "ApprovalStatus": "New", "Assets": "string", "AccountTeamLastModifiedDate": {"Date": "2023-04-10T13:00:36.219Z", "Epoch": 0}, "ApplicationMappingLastModifiedDate": {"Date": "2023-04-10T13:00:36.219Z", "Epoch": 0}, "VolumeEstimate": 0, "MarginEstimate": 0, "PhysicalAddress": {"Street": "string", "City": "string", "StateOrProvince": "string", "PostalCode": "string", "Country": "string", "AddressID": "string", "CountyCommunity": "string"}, "CorrespondenceAddress": {"Street": "string", "City": "string", "StateOrProvince": "string", "PostalCode": "string", "Country": "string", "AddressID": "string", "CountyCommunity": "string"}, "AdditionalInfo": {"ID": "string", "AccountID": "string", "PaymentTerms": "string", "VAT": "string", "DUNS": "string", "CANBusinessID": "CFN", "StatisticalID": "string", "CustomerServiceNotes": "string", "VATFarmer": true}, "lstOtherBU": ["GOSCE"], "TemplstOtherBU": "string", "AccountCurrency": "NotSet", "AccountNumber": "string", "AvailabilityOnMarket": "Available", "ChangeAccountType": true, "NewAccountType": "Standard", "ConsumerStatus": "Prospecting", "CourtId": "string", "CustomerStatus": "None", "ERPIdLength": "string", "ERPPayerIdLength": "string", "ERPShiptoIdLength": "string", "IsMobileFirst": true, "VeterinaryId": "string", "WonLostReasonCode": "Available", "CANBusiness": "CFN", "SubBrandId": "<PERSON><PERSON><PERSON>", "CurrentUserProfileNameandId": "string", "ExternalId": "string", "LastModifiedBy": "string", "OwnerProfileNameandId": "string", "Description": "string", "SalesTerritory": "string", "CustomerCode": "string", "UserRoles": [{"UserRole": "string", "UserName": "string", "UserBusinessUnit": "Brazil"}], "id": "string", "CreateUser": "string", "IsDeleted": true, "LastModifyUser": "string", "CreateTimeUtc": "2023-04-10T13:00:36.219Z", "LastModifiedTimeUtc": "2023-04-10T13:00:36.219Z", "LastSyncTimeUtc": "2023-04-10T13:00:36.219Z", "IsNew": true}]