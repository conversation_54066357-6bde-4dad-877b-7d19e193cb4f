/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static java.util.UUID.randomUUID;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.VisitStatus;
import com.app.cargill.document.*;
import com.app.cargill.model.Diets;
import com.app.cargill.model.Notes;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.DietRepository;
import com.app.cargill.repository.NotesRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.VisitsRepository;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class VisitsDataFixServiceTest {

  @Mock private VisitsRepository visitsRepository;

  @Mock private NotesRepository notesRepository;

  @Mock private DietRepository dietRepository;

  @InjectMocks private VisitsDataFixService visitsDataFixService;

  @Mock private PensRepository pensRepository;

  @Test
  void whenMobileLastUpdatedFixIsCalledCorrectResultIsReturned() {
    Visits visit =
        Visits.builder()
            .visitDocument(VisitDocument.builder().lastModifiedTimeUtc(Instant.now()).build())
            .build();
    Visits visit2 =
        Visits.builder()
            .visitDocument(VisitDocument.builder().visitPublishedDateTimeUtc(Instant.now()).build())
            .build();
    when(visitsRepository.save(any())).thenReturn(visit2);
    Mono<Visits> result = visitsDataFixService.updateMobileDate(List.of(visit, visit2));

    assertNotNull(result);
  }

  @Test
  void whenMobileLastUpdatedFixIsCalledAndVisitsListIsEmptyCorrectResultIsReturned() {
    Mono<Visits> result = visitsDataFixService.updateMobileDate(new ArrayList<>());

    assertNotNull(result);
  }

  @Test
  void whenMobileLastUpdatedFixIsCalledNoIssuesOccur() {
    Mono<Visits> result = visitsDataFixService.updateMobileLastUpdatedDate();

    assertNotNull(result);
  }

  @Test
  void whenTmrComparisonFixIsCalledNoIssuesOccur() {
    Mono<Visits> result = visitsDataFixService.tmrPenStateIndexesFix();

    assertNotNull(result);
  }

  @Test
  void whenTmrComparisonFixIsCalledAndVisitsListIsEmptyCorrectResultIsReturned() {
    Mono<Visits> result = visitsDataFixService.fixAndSaveTmrPenStateIndexes(new ArrayList<>());

    assertNotNull(result);
  }

  @Test
  void whenTmrComparisonFixIsCalledCorrectResultIsReturned() {
    UUID uuid = randomUUID();
    Visits visit =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder()
                    .tmrParticleScore(
                        RumenHealthTMRParticleScoreTool.builder()
                            .tmrScores(
                                List.of(
                                    RumenHealthTMRParticleScoreToolItem.builder()
                                        .penId(uuid)
                                        .build()))
                            .build())
                    .lastModifiedTimeUtc(Instant.now())
                    .build())
            .build();
    Visits visit3 =
        Visits.builder()
            .visitDocument(
                VisitDocument.builder()
                    .tmrParticleScore(
                        RumenHealthTMRParticleScoreTool.builder()
                            .tmrScores(
                                List.of(
                                    RumenHealthTMRParticleScoreToolItem.builder()
                                        .penId(uuid)
                                        .build()))
                            .build())
                    .lastModifiedTimeUtc(Instant.now())
                    .build())
            .build();

    Visits visit2 =
        Visits.builder()
            .visitDocument(VisitDocument.builder().visitPublishedDateTimeUtc(Instant.now()).build())
            .build();
    when(visitsRepository.save(any())).thenReturn(visit2);
    Mono<Visits> result =
        visitsDataFixService.fixAndSaveTmrPenStateIndexes(List.of(visit, visit2, visit3));

    assertNotNull(result);
  }

  @Test
  void whenUpdateVisitsForDeletedSiteIds() {
    SitesDeletedTransform sitesDeletedTransform = new SitesDeletedTransform();
    List<UUID> sitesFrom = List.of(randomUUID(), randomUUID(), randomUUID());
    sitesDeletedTransform.setSitesFrom(sitesFrom);
    sitesDeletedTransform.setSitesTo("913e9408-68c8-4c7c-ba1a-33dfec70ed27");
    Visits visits = new Visits();
    visits.setId(9l);
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setSiteId(UUID.fromString("913e9408-68c8-4c7c-ba1a-33dfec70ed27"));
    visits.setVisitDocument(visitDocument);
    List<Visits> visitsList = new ArrayList<>();
    visitsList.add(visits);

    Notes notes = new Notes();
    NotesDocument notesDocument = new NotesDocument();
    notesDocument.setSiteId(UUID.fromString("913e9408-68c8-4c7c-ba1a-33dfec70ed27"));
    notes.setNotesDocument(notesDocument);
    List<Notes> notesList = new ArrayList<>();
    notesList.add(notes);

    Diets diets = new Diets();
    DietDocument dietDocument = new DietDocument();
    dietDocument.setSiteId(UUID.fromString("913e9408-68c8-4c7c-ba1a-33dfec70ed27"));
    diets.setDietDocument(dietDocument);
    List<Diets> dietsList = new ArrayList<>();
    dietsList.add(diets);

    when(visitsRepository.getVisitsDocBySiteId(any())).thenReturn(visitsList);
    when(notesRepository.findByNotesBySiteId(any())).thenReturn(notesList);
    when(dietRepository.findAllBySiteId(any())).thenReturn(dietsList);
    visitsDataFixService.updateVisitsNotesDietsForDeletedSiteIds(sitesDeletedTransform);

    assertTrue(true);
  }

  @Test
  void shouldHandleEmptyListGracefully() {
    // Arrange
    when(visitsRepository.findAllVisitsByVisitNameWithBackSlashes()).thenReturn(List.of());

    // Act & Assert
    StepVerifier.create(visitsDataFixService.fixVisitName()).verifyComplete();

    // Verify interactions
    verify(visitsRepository, times(1)).findAllVisitsByVisitNameWithBackSlashes();
    verify(visitsRepository, never()).save(any());
  }

  @Test
  void whenMigrationRetainBodyConditionPens() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateBodyConditionPensData();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainBodyConditionPensWithData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.fromString("005ce3ed-2b6e-4310-8a4b-a37649bf6c3a"))
            .customerId(UUID.fromString("508c3276-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8357d44"))
            .status(VisitStatus.Published)
            .rumenHealth(RumenHealthTool.builder().build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .cudChewing(
                CudChewingTool.builder()
                    .createTimeUtc(Instant.now())
                    .createUser("admin@admin")
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder()
                                .countNo(5)
                                .countYes(5)
                                .noPercent(0.0)
                                .penId(UUID.fromString("f34c2117-9153-4a86-af12-5555a8689d49"))
                                .penName("Test")
                                .yesPercent(0.0)
                                .build()))
                    .build())
            .animalAnalysis(AnimalAnalysisTool.builder().build())
            .metabolicIncidence(MetabolicIncidenceTool.builder().build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(
                BodyConditionTool.builder()
                    .pens(
                        List.of(
                            BodyConditionToolItem.builder()
                                .averageBCS(2.0)
                                .milk(2.3)
                                .penId(UUID.randomUUID())
                                .penName("TestB")
                                .build()))
                    .build())
            .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
            .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
            .visitDate(Instant.parse("2025-02-11T09:50:03.028Z"))
            .visitName("Roman Doe 2025-02-11T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistsbc =
        Visits.builder()
            .id(2345L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Pens pen =
        Pens.builder()
            .penDocument(PenDocument.builder().milk(12.9).name("pen1").build())
            .id(123L)
            .updatedDate(Date.from(Instant.now()))
            .build();
    when(pensRepository.findByPenId(any())).thenReturn(pen);
    Visits result = visitsDataFixService.updateBodyConditionPensData(vistsbc);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationRetainTmrParticleScore() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateTmrParticleScore();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainTmrParticleScoreWithData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.fromString("005ce4dd-2b6e-4310-8a4b-a37649bf6c3a"))
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .tmrParticleScore(
                RumenHealthTMRParticleScoreTool.builder()
                    .tmrScores(
                        List.of(
                            RumenHealthTMRParticleScoreToolItem.builder()
                                .tmrScoreName("Name")
                                .tmrScoreId("122")
                                .penName("PenName")
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2345L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateTmrParticleScore(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationRetainTmrParticleScoreWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .tmrParticleScore(
                RumenHealthTMRParticleScoreTool.builder()
                    .tmrScores(
                        List.of(
                            RumenHealthTMRParticleScoreToolItem.builder()
                                .tmrScoreName("Name")
                                .penId(UUID.randomUUID())
                                .tmrScoreId("122")
                                .penName("PenName")
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(2345L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateTmrParticleScore(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationRetainTmrParticleScoreWithDatPensUsed() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .tmrParticleScore(
                RumenHealthTMRParticleScoreTool.builder()
                    .tmrScores(
                        List.of(
                            RumenHealthTMRParticleScoreToolItem.builder()
                                .tmrScoreName("Name")
                                .penId(UUID.randomUUID())
                                .tmrScoreId("122")
                                .penName("PenName")
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(2345L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateTmrParticleScore(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  public void whenCheckPensUsed() {
    UUID penId1 = UUID.randomUUID();
    UUID penId2 = UUID.randomUUID();
    List<UUID> penIds = List.of(penId1, penId2);
    Map<String, List<UUID>> pensUsed = new HashMap<>();
    visitsDataFixService.checkPensUsed(
        loadVisitsForCheckPensUsed(), penIds, pensUsed, "TMR_PARTICLE");
    assertTrue(true);
  }

  @Test
  public void whenCheckPensUsedHasNoValue() {
    UUID penId1 = UUID.randomUUID();
    UUID penId2 = UUID.randomUUID();
    List<UUID> penIds = List.of(penId1, penId2);
    Map<String, List<UUID>> pensUsed1 = new HashMap<String, List<UUID>>() {};
    Visits visit1 = loadVisitsForCheckPensUsed();
    visit1.getVisitDocument().setPensUsed(pensUsed1);
    visitsDataFixService.checkPensUsed(visit1, penIds, pensUsed1, "TMR_PARTICLE");
    assertTrue(true);
  }

  private Visits loadVisitsForCheckPensUsed() {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .rumenHealth(RumenHealthTool.builder().build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .cudChewing(CudChewingTool.builder().build())
            .animalAnalysis(AnimalAnalysisTool.builder().build())
            .metabolicIncidence(MetabolicIncidenceTool.builder().build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .tmrParticleScore(
                RumenHealthTMRParticleScoreTool.builder()
                    .tmrScores(
                        List.of(
                            RumenHealthTMRParticleScoreToolItem.builder()
                                .tmrScoreName("Name")
                                .penId(UUID.randomUUID())
                                .tmrScoreId("122")
                                .penName("PenName")
                                .build()))
                    .build())
            .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
            .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    return Visits.builder()
        .id(2345L)
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }

  @Test
  void whenMigrationRetainRumenHealthScore() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateRumenHealthManureScore();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainRumenHealthScoreWithData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .rumenHealthManureScore(
                RumenHealthManureScoreTool.builder()
                    .pens(
                        List.of(
                            RumenHealthManureScoreToolItem.builder()
                                .penName("Rumen")
                                .daysInMilk(12)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-21T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-21T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateRumenHealthManureScore(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationRetainRumenHealthsWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .rumenHealthManureScore(
                RumenHealthManureScoreTool.builder()
                    .pens(
                        List.of(
                            RumenHealthManureScoreToolItem.builder()
                                .penName("Rumen")
                                .daysInMilk(12)
                                .penId(randomUUID())
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateRumenHealthManureScore(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigration() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .rumenHealth(
                RumenHealthTool.builder()
                    .pens(
                        List.of(
                            RumenHealthToolItem.builder()
                                .penName("Rumen")
                                .daysInMilk(12)
                                .penId(randomUUID())
                                .build()))
                    .build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .cudChewing(CudChewingTool.builder().build())
            .animalAnalysis(AnimalAnalysisTool.builder().build())
            .metabolicIncidence(MetabolicIncidenceTool.builder().build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .tmrParticleScore(RumenHealthTMRParticleScoreTool.builder().build())
            .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
            .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
            .visitDate(Instant.parse("2025-03-21T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-21T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateRumenHealthManureScore(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationRetainRumenFillScore() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateRumenFillManureScore();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainRumenFillScoreWithData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .rumenFillManureScore(
                RumenFillTool.builder()
                    .pens(
                        List.of(
                            RumenFillScoreToolItem.builder()
                                .penName("Rumen")
                                .penId(randomUUID())
                                .daysInMilk(12)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-24T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-24T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateRumenFillManureScore(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationRetainRumenFillWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .rumenFillManureScore(
                RumenFillTool.builder()
                    .pens(
                        List.of(
                            RumenFillScoreToolItem.builder()
                                .penName("Rumen")
                                .daysInMilk(12)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateRumenFillManureScore(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationCudChewing() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateCudChewing();
    assertNotNull(result);
  }

  @Test
  void whenMigrationCudChewingWithData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .cudChewing(
                CudChewingTool.builder()
                    .cudChewingReports(
                        List.of(CudChewingByPen.builder().penId(randomUUID()).build()))
                    .build())
            .visitDate(Instant.parse("2025-03-24T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-24T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateCudChewing(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationCudChewingWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .cudChewing(
                CudChewingTool.builder()
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder().penName("sdhsd").penId(randomUUID()).build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateCudChewing(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationManureScreenerTool() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateManureScreenerTool();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainManureScreenerTool() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .manureScreenerTool(
                ManureScreenerTool.builder()
                    .mstScores(
                        List.of(
                            ManureScreenerToolItem.builder()
                                .penId(randomUUID())
                                .penName("Frost")
                                .isToolItemNew(true)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-24T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-24T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateManureScreenerTool(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationManureScreenerToolWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .manureScreenerTool(
                ManureScreenerTool.builder()
                    .mstScores(
                        List.of(
                            ManureScreenerToolItem.builder()
                                .penName("Frost")
                                .isToolItemNew(true)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateManureScreenerTool(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationPenTimeBudgetTool() throws IOException {
    Flux<Visits> result = visitsDataFixService.updatePenTimeBudgetTool();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainPenTimeBudgetTool() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .penTimeBudgetTool(
                PenTimeBudgetTool.builder()
                    .pens(
                        List.of(
                            PenTimeBudgetToolItem.builder()
                                .penId(randomUUID())
                                .penName("Frost")
                                .animals(2.0)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-24T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-24T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updatePenTimeBudgetTool(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationPenTimeBudgetToolWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .penTimeBudgetTool(
                PenTimeBudgetTool.builder()
                    .pens(
                        List.of(
                            PenTimeBudgetToolItem.builder()
                                .penId(randomUUID())
                                .penName("Frost")
                                .animals(2.0)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updatePenTimeBudgetTool(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationLocomotionScorePensData() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateLocomotionScorePensData();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainLocomotionScorePensData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .locomotionScore(
                LocomotionTool.builder()
                    .pens(
                        List.of(
                            LocomotionToolItem.builder()
                                .penId(randomUUID())
                                .penName("Thomas")
                                .daysInMilk(1)
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-24T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-24T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateLocomotionScorePensData(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationLocomotionScorePensDataWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .locomotionScore(
                LocomotionTool.builder()
                    .pens(
                        List.of(
                            LocomotionToolItem.builder().penName("Thomas").daysInMilk(1).build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateLocomotionScorePensData(vistTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationAnimalAnalysisPensData() throws IOException {
    Flux<Visits> result = visitsDataFixService.updateAnimalAnalysisPensData();
    assertNotNull(result);
  }

  @Test
  void whenMigrationRetainAnimalAnalysisPensData() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(randomUUID())
            .customerId(UUID.fromString("508c5676-a00b-4d58-2323-3a87f68d91ae"))
            .siteId(UUID.fromString("07315678-9ea9-475a-3245-39dc8347d44"))
            .status(VisitStatus.Published)
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .animals(
                        List.of(
                            AnimalAnalysisToolItem.builder()
                                .penId(randomUUID())
                                .penName("Edison")
                                .build()))
                    .build())
            .visitDate(Instant.parse("2025-03-24T09:50:03.028Z"))
            .visitName("Michael Kors 2025-03-24T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits visitTmr =
        Visits.builder()
            .id(2323L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();
    Visits result = visitsDataFixService.updateAnimalAnalysisPensData(visitTmr);
    assertNotNull(result == null);
  }

  @Test
  void whenMigrationAnimalAnalysisPensDataWithDataNull() throws IOException {
    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.randomUUID())
            .siteId(UUID.randomUUID())
            .status(VisitStatus.Published)
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .animals(List.of(AnimalAnalysisToolItem.builder().penName("Edison").build()))
                    .build())
            .visitDate(Instant.parse("2025-03-03T09:50:03.028Z"))
            .visitName("Roman Doe 2025-03-03T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    Visits vistTmr =
        Visits.builder()
            .id(4545L)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .localId(visitDocument.getId().toString())
            .visitDocument(visitDocument)
            .build();

    Visits result = visitsDataFixService.updateAnimalAnalysisPensData(vistTmr);
    assertNotNull(result == null);
  }
}
