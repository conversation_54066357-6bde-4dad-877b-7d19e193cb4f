/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.model.UserDailyLogin;
import com.app.cargill.schedulers.APPUsageAnalyticsScheduler;
import com.app.cargill.service.admin.UserAdminService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
class AppUsageAnalyticsSchedulerTest {

  @InjectMocks private APPUsageAnalyticsScheduler appUsageAnalyticsScheduler;
  @Mock UserAdminService userAdminService;

  @Test
  void userDailyLoginAnalytics() {
    // Mock data
    List<UserDailyLogin> userDailyLoginList = new ArrayList<>();
    UserDailyLogin userDailyLogin = new UserDailyLogin();
    userDailyLogin.setEmail("<EMAIL>");

    userDailyLogin.setName("TestUser");
    userDailyLogin.setEmail("<EMAIL>");
    userDailyLogin.setLastLoginDateTime(Instant.now());
    userDailyLoginList.add(userDailyLogin);
    when(userAdminService.getUsersAllLogin(any(), any())).thenReturn(userDailyLoginList);

    appUsageAnalyticsScheduler.userDailyLoginAnalytics();
    assertNotNull("TEst");
  }
}
