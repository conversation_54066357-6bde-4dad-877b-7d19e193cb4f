/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.document.SilageDocument;
import com.app.cargill.dto.SaveSilageDto;
import com.app.cargill.dto.SilageDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.model.Silages;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SilagesRepository;
import com.app.cargill.service.ISilageService;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.mappers.SilageMapper;
import com.app.cargill.utils.PageableUtil;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SilageServiceImpl implements ISilageService {

  private final SilagesRepository silagesRepository;
  private final IUserService userServiceImpl;
  private final AccountsRepository accountsRepository;

  @Override
  public Page<SilageDto> getAllSilagesPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    String currentLoggedUser = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    List<String> accountIdsByUser =
        accountsRepository.findAccountIdsByUserWithAllFlags(
            currentLoggedUser, userServiceImpl.getCurrentLoggedInUser());
    Page<Silages> silages =
        silagesRepository.findByAccountIdsPaginated(accountIdsByUser, pageable, lastSyncTime);
    if (Objects.isNull(silages)) {
      return new PageImpl<>(new ArrayList<>());
    }

    return new PageImpl<>(
        silages.stream().map(SilageMapper::modelToDto).toList(),
        pageable,
        silages.getTotalElements());
  }

  @Override
  public List<SilageDto> save(SilageDto silageDto) {
    if (Boolean.TRUE.equals(silagesRepository.existsByLocalId(silageDto.getLocalId()))) {
      throw new AlreadyExistsDEException("LocalId exists. Object already synced.");
    }

    List<Silages> silagesList = new ArrayList<>();
    silageDto.getSilages().stream()
        .forEach(silage -> silagesList.add(mapToModel(silage, silageDto)));
    return silagesRepository.saveAll(silagesList).stream().map(SilageMapper::modelToDto).toList();
  }

  private Silages mapToModel(SaveSilageDto silage, SilageDto silageDto) {
    SilageDocument silageDocument =
        SilageDocument.builder()
            .accountId(silageDto.getAccountId())
            .id(silage.getId() != null ? silage.getId() : UUID.randomUUID())
            .silageName(silage.getSilageName())
            .siteId(silageDto.getSiteId())
            .build();

    return Silages.builder()
        .silageDocument(silageDocument)
        .localId(silageDto.getLocalId())
        .deleted(silage.getDeleted())
        .build();
  }

  @Override
  public List<SilageDto> update(SilageDto silageDto) {
    List<Silages> silagesList = new ArrayList<>();
    silageDto.getSilages().stream()
        .forEach(
            silage -> {
              Silages silageToPersist = silagesRepository.findBySilageId(silage.getId().toString());
              Silages updatedSilage = mapToModel(silage, silageDto);
              if (silageToPersist != null) {
                updatedSilage.setId(silageToPersist.getId());
                updatedSilage.setCreatedDate(silageToPersist.getCreatedDate());
              }
              silagesList.add(updatedSilage);
            });

    return silagesRepository.saveAll(silagesList).stream().map(SilageMapper::modelToDto).toList();
  }
}
