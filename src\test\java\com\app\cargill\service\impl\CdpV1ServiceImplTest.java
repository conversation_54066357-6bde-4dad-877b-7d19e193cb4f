/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.*;
import com.app.cargill.document.*;
import com.app.cargill.dto.ISelectKeyValueDto;
import com.app.cargill.dto.ProfitabilityAnalysisData;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.CdpSiteDataWrapper;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.*;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

@ExtendWith(MockitoExtension.class)
@Slf4j
class CdpV1ServiceImplTest {

  @Mock private AccountsRepository accountsRepository;

  @InjectMocks private CdpV1ServiceImpl cdpService;

  @Mock private CdpSitesRepository cdpSitesRepository;

  @Mock private VisitsRepository visitsRepository;

  @Mock private SitesRepository sitesRepository;
  @Mock ModelMapper modelMapper;

  @Mock EarTagsRepository earTagsRepository;

  @Test
  void testGetAllAccountsByFromAndToDate() {

    List<Accounts> page =
        new ArrayList<>(
            createTestAccounts().stream()
                .filter(
                    accounts -> accounts.getAccountDocument().getActive() && !accounts.isDeleted())
                .collect(Collectors.toList()));

    when(accountsRepository.findAllAccountsByFromAndToDateV1(any(), any())).thenReturn(page);

    List<AccountDocumentDTO> result =
        cdpService.getAllAccountsByFromAndToDateV1(
            Instant.now().minus(1, ChronoUnit.DAYS), Instant.now());

    assertThat(result).hasSize(3);
    assertThat(result.get(0).getAccountName()).isEqualTo("TestAccount1");
    assertThat(result.get(1).getAccountName()).isEqualTo("TestAccount2");
    assertThat(result.get(2).getAccountName()).isEqualTo("TestAccount3");
  }

  // Need a utility class to create these sample data loads and use in integration tests too - if
  // they want any.
  private List<Accounts> createTestAccounts() {
    AccountDocument accountDocument1 =
        createAccountDocuments(true, false, "TestAccount1", Set.of("user1", "user2"));
    AccountDocument accountDocument2 =
        createAccountDocuments(true, false, "TestAccount2", Set.of("user3", "user4"));
    AccountDocument accountDocument3 =
        createAccountDocuments(true, false, "TestAccount3", Set.of("user5", "user6"));
    AccountDocument accountDocument4 =
        createAccountDocuments(false, true, "TestAccount4", Set.of("user7", "user8"));
    AccountDocument accountDocument5 =
        createAccountDocuments(true, true, "TestAccount5", Set.of("user9", "user10"));

    Accounts account1 =
        Accounts.builder()
            .accountDocument(accountDocument1)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument1.isDeleted)
            .build();

    Accounts account2 =
        Accounts.builder()
            .accountDocument(accountDocument2)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument2.isDeleted)
            .build();

    Accounts account3 =
        Accounts.builder()
            .accountDocument(accountDocument3)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument3.isDeleted)
            .build();

    Accounts account4 =
        Accounts.builder()
            .accountDocument(accountDocument4)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument4.isDeleted)
            .build();

    Accounts account5 =
        Accounts.builder()
            .accountDocument(accountDocument5)
            .createdDate(Date.from(Instant.now()))
            .updatedDate(Date.from(Instant.now()))
            .deleted(accountDocument5.isDeleted)
            .build();

    return List.of(account1, account2, account3, account4, account5);
  }

  private static AccountDocument createAccountDocuments(
      boolean isActive, boolean isDeleted, String accountName, Set<String> users) {
    return AccountDocument.builder()
        .id(UUID.randomUUID())
        .accountName(accountName)
        .accountCurrency(1)
        .accountType(1)
        .accountStatus("Test Status")
        .accountValidated(true)
        .users(users)
        .contacts(
            List.of(
                Contact.builder()
                    .contactId(UUID.randomUUID())
                    .sFDCContactId("sf-1")
                    .firstName("John")
                    .lastName("Doe")
                    .build(),
                Contact.builder()
                    .contactId(UUID.randomUUID())
                    .firstName("Jane")
                    .lastName("Doe")
                    .build()))
        .active(isActive)
        .subTypeID(SubTypeId.FarmProducer)
        .physicalAddress(Address.builder().build())
        .dateOfLastVisit(Instant.now())
        .isDeleted(isDeleted)
        .build();
  }

  @Test
  void testGetAllSitesByFromAndToDate() {
    // Mock data
    UUID siteId1 = UUID.randomUUID();
    SiteDocument siteDocument1 = new SiteDocument();
    siteDocument1.setId(siteId1);
    siteDocument1.setSiteName("Site 1");

    UUID siteId2 = UUID.randomUUID();
    SiteDocument siteDocument2 = new SiteDocument();
    siteDocument2.setId(siteId2);
    siteDocument2.setSiteName("Site 2");

    Sites site1 = new Sites();
    site1.setSiteDocument(siteDocument1);

    Sites site2 = new Sites();
    site2.setSiteDocument(siteDocument2);

    CdpSiteDataWrapper dataWrapper1 = new CdpSiteDataWrapper();
    dataWrapper1.setSiteDocument(site1.getSiteDocument());
    CdpSiteDataWrapper dataWrapper2 = new CdpSiteDataWrapper();
    dataWrapper2.setSiteDocument(site2.getSiteDocument());

    when(cdpSitesRepository.getCdpSitesData(any(), any()))
        .thenReturn(List.of(dataWrapper1, dataWrapper2));

    // Call the method under test
    List<SiteDocumentDTO> result =
        cdpService.getAllSitesByFromAndToDateV1(
            Instant.parse("2023-06-01T00:00:00Z"), Instant.parse("2023-06-30T23:59:59Z"));

    // Assertions
    assertThat(result).hasSize(2);
    assertThat(result.get(0).getId()).isEqualTo(siteId1);
    assertThat(result.get(0).getSiteName()).isEqualTo("Site 1");
    assertThat(result.get(1).getId()).isEqualTo(siteId2);
    assertThat(result.get(1).getSiteName()).isEqualTo("Site 2");
  }

  @Test
  void testGetAllVisitsByFromAndToDateV1() {
    // Mock data
    UUID visitId1 = UUID.randomUUID();
    VisitDocument visitDocument1 = new VisitDocument();
    visitDocument1.setId(visitId1);
    visitDocument1.setVisitName("Visit 1");

    UUID visitId2 = UUID.randomUUID();
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setId(visitId2);
    visitDocument2.setVisitName("Visit 2");

    Visits visit1 = new Visits();
    visit1.setVisitDocument(visitDocument1);

    Visits visit2 = new Visits();
    visit2.setVisitDocument(visitDocument2);

    when(visitsRepository.findAllVisitsByFromAndToDateV1(any(), any()))
        .thenReturn(Arrays.asList(visit1, visit2));

    // Call the method under test
    List<VisitDocumentDTO> result =
        cdpService.getAllVisitsByFromAndToDateV1(
            Instant.parse("2023-06-01T00:00:00Z"), Instant.parse("2023-06-30T23:59:59Z"));

    // Assertions
    assertNotNull(result);
    assertThat(result).hasSize(2);
    assertThat(result.get(0).getId()).isEqualTo(visitId1);
    assertThat(result.get(0).getVisitName()).isEqualTo("Visit 1");
    assertThat(result.get(1).getId()).isEqualTo(visitId2);
    assertThat(result.get(1).getVisitName()).isEqualTo("Visit 2");
  }

  @Test
  void forceUpdateVisits() {
    // Mock data
    Long visitId1 = 121L;
    VisitDocument visitDocument1 = new VisitDocument();

    visitDocument1.setVisitName("Visit 1");

    Long visitId2 = 122L;
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setVisitName("Visit 2");

    Visits visit1 = new Visits();
    visit1.setVisitDocument(visitDocument1);
    visit1.setId(121L);

    Visits visit2 = new Visits();
    visit2.setVisitDocument(visitDocument2);
    visit2.setId(122L);

    List<Visits> retVisit = new ArrayList<>();
    retVisit.add(visit1);
    retVisit.add(visit2);

    when(visitsRepository.findAllVisitsForOneYear()).thenReturn(retVisit);

    // Call the method under test
    List<Visits> result = cdpService.getAllVisitsForOneYear();

    // Assertions
    assertNotNull(result);

    assertThat(result.get(0).getId()).isEqualTo(visitId1);

    assertThat(result.get(1).getId()).isEqualTo(visitId2);
  }

  @Test
  void shouldReturnProfitabilityAnalysisDataForValidDates() {
    // Arrange
    Instant dateFrom = Instant.parse("2024-01-01T00:00:00Z");
    Instant dateTo = Instant.parse("2024-12-31T23:59:59Z");
    List<Visits> visits =
        List.of(
            Visits.builder()
                .visitDocument(
                    VisitDocument.builder()
                        .id(UUID.randomUUID())
                        .siteId(UUID.randomUUID())
                        .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
                        .profitabilityAnalysis(getProfitabilityAnalysisData())
                        .build())
                .build(),
            Visits.builder()
                .visitDocument(
                    VisitDocument.builder()
                        .id(UUID.randomUUID())
                        .siteId(UUID.randomUUID())
                        .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
                        .profitabilityAnalysis(getProfitabilityAnalysisData())
                        .build())
                .build());

    ISelectKeyValueDto<UUID, Double> key =
        new ISelectKeyValueDto<UUID, Double>() {

          @Override
          public Double getValue() {
            return 160.2;
          }

          @Override
          public UUID getKey() {
            return UUID.randomUUID();
          }
        };

    ISelectKeyValueDto<UUID, Double> key2 =
        new ISelectKeyValueDto<UUID, Double>() {

          @Override
          public Double getValue() {
            return 170.2;
          }

          @Override
          public UUID getKey() {
            return UUID.randomUUID();
          }
        };

    List<ISelectKeyValueDto<UUID, Double>> siteMilkData = List.of(key, key2);

    when(visitsRepository.findAllProtabilityDataByFromAndToDateV1(dateFrom, dateTo))
        .thenReturn(visits);
    when(sitesRepository.findMilkBySiteIds(anyList())).thenReturn(siteMilkData);
    // Act
    List<ProfitabilityAnalysisData> result =
        cdpService.getAllProftabilityAnalysisDataByFromAndToDateV1(dateFrom, dateTo);

    // Assert
    assertEquals(2, result.size());
  }

  private ProfitabilityAnalysisTool getProfitabilityAnalysisData() {

    return ProfitabilityAnalysisTool.builder()
        .visitId(UUID.randomUUID())
        .animalInput(
            AnimalInput.builder()
                .animalsInHerd(100.0)
                .totalNumberOfCows(90.0)
                .totalNumberOfLactatingAnimals(80.0)
                .breed(Breed.Holandesa)
                .productionSystem(ProductionSystem.Compostbarn)
                .build())
        .milkInformation(
            MilkInformation.builder()
                .numberOfMilkings("test")
                .totalProductionHerd(2500.0)
                .milkPrice(1.5)
                .DIM(150.3)
                .milkFatPercentage(3.5)
                .milkProteinPercentage(3.2)
                .somanticCellCount(200000.3)
                .bacteriaCellCount(50000.1)
                .MUN(15.0)
                .totalProduction(2700.0)
                .productionIn150DIM(400.0)
                .build())
        .feedingInformation(
            FeedingInformation.builder()
                .commercialConcentrate(150.0)
                .commercialConcentrateToggle(true)
                .mineralBaseMix(true)
                .mineralBaseMixValue(30.0)
                .nutritek(true)
                .xpcUltra(false)
                .actiforBoost(true)
                .buffer(true)
                .nutrigorduraLac(false)
                .ice(true)
                .energyIce(true)
                .monensin(true)
                .soyPassBr(true)
                .concentrateTotalConsumed(500.0)
                .silage(ProfitabilityAnalysisQuality.Bad)
                .haylage(ProfitabilityAnalysisQuality.Bad)
                .hay(ProfitabilityAnalysisQuality.Good)
                .pasture(ProfitabilityAnalysisQuality.Bad)
                .waterQuality(WaterQuality.Clean)
                .beddingQuality(BeddingQuality.Clean)
                .ventilation(true)
                .sprinkler(true)
                .temperatureInC(25.0)
                .airRuPercentage(60.0)
                .THI(75.0)
                .respiratoryMovement(2.0)
                .cowLayingDownPercentage(70.0)
                .totalDietCost(1200.0)
                .revenuePerCowPerDay(5.0)
                .build())
        .build();
  }

  @Test
  void getAnimalAnalysisDetails() throws Exception {
    List<Visits> visits = new ArrayList<>();
    visits.add(loadVisit());
    for (Visits visitsT : visits) {
      for (AnimalAnalysisToolItem animalAnalysisToolItem :
          visitsT.getVisitDocument().getAnimalAnalysis().getAnimals()) {
        for (AnimalAnalysisDetailsToolItem animalAnalysisDetailsToolItem :
            animalAnalysisToolItem.getAnimalDetails()) {
          animalAnalysisDetailsToolItem.setEarTagId(UUID.randomUUID());
        }
      }
    }
    when(visitsRepository.getAnimalAnalysisDetails()).thenReturn(visits);
    List<AnimalAnalysisToolCDP> analysisTool = cdpService.getAnimalAnalysisDetails();
    Assert.assertTrue(analysisTool != null);
  }

  @Test
  void getAnimalAnalysisDetailsException() throws Exception {
    List<Visits> visits = new ArrayList<>();
    visits.add(loadVisit());
    for (Visits visitsT : visits) {
      for (AnimalAnalysisToolItem animalAnalysisToolItem :
          visitsT.getVisitDocument().getAnimalAnalysis().getAnimals()) {
        for (AnimalAnalysisDetailsToolItem animalAnalysisDetailsToolItem :
            animalAnalysisToolItem.getAnimalDetails()) {
          animalAnalysisDetailsToolItem.setEarTagId(UUID.randomUUID());
        }
      }
    }
    when(cdpService.getAnimalAnalysisDetails())
        .thenAnswer(
            i -> {
              throw new Exception("custom test exception");
            });
    List<AnimalAnalysisToolCDP> analysisTool = cdpService.getAnimalAnalysisDetails();
    Assert.assertTrue(analysisTool != null);
  }

  private Visits loadVisit() {

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.randomUUID())
            .customerId(UUID.fromString("508c3253-a00b-4d58-bfaa-3a87f68d91ae"))
            .siteId(UUID.fromString("07315216-9ea9-475a-a0fc-39dc8357d44"))
            .status(VisitStatus.InProgress)
            .rumenHealth(
                RumenHealthTool.builder()
                    .pens(
                        List.of(
                            RumenHealthToolItem.builder()
                                .cudChewingCowsCount(
                                    CudChewingCowCount.builder()
                                        .countNo(5)
                                        .countYes(10)
                                        .noPercent(50.5)
                                        .yesPercent(60.5)
                                        .totalCount(0.0)
                                        .build())
                                .cudChewsCount(
                                    List.of(
                                        CudChewingCount.builder()
                                            .chewsCount(10)
                                            .cowNumber(10)
                                            .build()))
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .build()))
                    .visitId(UUID.randomUUID())
                    .goals(
                        List.of(
                            HerdAnalysisGoal.builder()
                                .cudChews(3.6)
                                .percentChewing(50.6)
                                .stage(LactationStage.FarOffDry)
                                .build()))
                    .build())
            .milkSoldEvaluation(MilkSoldEvaluationTool.builder().build())
            .rumenFillManureScore(RumenFillTool.builder().build())
            .cudChewing(
                CudChewingTool.builder()
                    .createTimeUtc(Instant.now())
                    .createUser("admin@admin")
                    .cudChewingReports(
                        List.of(
                            CudChewingByPen.builder()
                                .countNo(5)
                                .countYes(5)
                                .noPercent(0.0)
                                .penId(UUID.randomUUID())
                                .penName("Test")
                                .yesPercent(0.0)
                                .build()))
                    .build())
            .animalAnalysis(
                AnimalAnalysisTool.builder()
                    .visitId(UUID.randomUUID())
                    .animals(
                        Arrays.asList(
                            AnimalAnalysisToolItem.builder()
                                .penId(UUID.randomUUID())
                                .animalDetails(
                                    Arrays.asList(
                                        AnimalAnalysisDetailsToolItem.builder()
                                            .locomotionScore(1.0)
                                            .build()))
                                .build()))
                    .build())
            .metabolicIncidence(MetabolicIncidenceTool.builder().build())
            .locomotionScore(LocomotionTool.builder().build())
            .bodyCondition(BodyConditionTool.builder().build())
            .roboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build())
            .rumenHealthManureScore(RumenHealthManureScoreTool.builder().build())
            .tmrParticleScore(RumenHealthTMRParticleScoreTool.builder().build())
            .foragePennState(ForagePennStateTool.builder().build())
            .penTimeBudgetTool(PenTimeBudgetTool.builder().build())
            .heatStress(HeatStressTool.builder().build())
            .manureScreenerTool(ManureScreenerTool.builder().build())
            .forageAuditScorecard(Scorecard.builder().build())
            .profitabilityAnalysis(ProfitabilityAnalysisTool.builder().build())
            .calfHeiferScorecard(CalfHeiferScorecard.builder().build())
            .visitDate(Instant.parse("2022-10-05T09:50:03.028Z"))
            .visitName("John Doe 2022-09-29T08:56:34.390Z")
            .mobileLastUpdatedTime(Instant.now())
            .build();

    return Visits.builder()
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .localId(visitDocument.getId().toString())
        .visitDocument(visitDocument)
        .build();
  }
}
