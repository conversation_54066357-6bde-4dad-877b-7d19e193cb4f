/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.CdpSiteDataWrapper;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CdpSitesRepository extends JpaRepository<CdpSiteDataWrapper, Long> {
  @Query(
      value =
          "SELECT s.site_document->>'id' as site_id, s.site_document as site_document,"
              + " COALESCE(json_agg(p.pen_document) FILTER (WHERE p.pen_document IS NOT NULL),"
              + " '[]') as pens_data, COALESCE(json_agg(d.diet_document) FILTER (WHERE"
              + " d.diet_document IS NOT NULL), '[]') as diets_data FROM sites s LEFT JOIN pens p"
              + " ON p.pen_document->>'SiteId' = s.site_document->>'id' AND p.deleted = false LEFT"
              + " JOIN diets d ON d.diet_document->>'SiteId' = s.site_document->>'id' AND d.deleted"
              + " = false WHERE (s.updated_date at time zone 'UTC' BETWEEN :dateFrom AND :dateTo)"
              + " AND s.deleted=false GROUP BY s.id ",
      nativeQuery = true)
  List<CdpSiteDataWrapper> getCdpSitesData(
      @Param("dateFrom") Instant dateFrom, @Param("dateTo") Instant dateTo);
}
