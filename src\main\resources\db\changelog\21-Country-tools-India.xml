<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="021" author="Taha">
    <sql>
   INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.023', false, 'ea16ec50-8dc3-4369-91c7-cebcd6546bb0', '2023-01-15 11:39:46.023', '{"id": "ea16ec50-8dc3-4369-91c7-cebcd6546bb0", "IsNew": false, "ToolId": "RumenHealth", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.101', false, '4108257b-37d6-418c-9076-680d67b1cbff', '2023-01-15 11:39:46.101', '{"id": "4108257b-37d6-418c-9076-680d67b1cbff", "IsNew": false, "ToolId": "LocomotionScore", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.953826500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.115', false, '8239c9e0-0592-40f0-babd-bc211d3aacfb', '2023-01-15 11:39:46.115', '{"id": "8239c9e0-0592-40f0-babd-bc211d3aacfb", "IsNew": false, "ToolId": "BodyCondition", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.969489800Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-04-12 12:14:39.563', false, 'fe8551e8-2ee6-4b5a-aeb6-bacea49ee0d1', '2023-04-12 12:14:39.563', '{"id": "fe8551e8-2ee6-4b5a-aeb6-bacea49ee0d1", "IsNew": true, "ToolId": "ForagePennState", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:31:45.121003600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:31:45Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.008', false, '2e101871-6601-48b5-be7d-8d96269f5e87', '2023-01-15 11:39:46.008', '{"id": "2e101871-6601-48b5-be7d-8d96269f5e87", "IsNew": false, "ToolId": "HeatStress", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2021-11-11T03:30:29.875713900Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.018', false, '1229b879-5a52-4ffb-9ede-ce2765a2e677', '2023-01-15 11:39:46.018', '{"id": "1229b879-5a52-4ffb-9ede-ce2765a2e677", "IsNew": false, "ToolId": "PenTimeBudgetTool", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2021-11-11T03:30:29.891411Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:45.924', false, '*************-47c9-8522-b2315e75a374', '2023-01-15 11:39:45.924', '{"id": "*************-47c9-8522-b2315e75a374", "IsNew": false, "ToolId": "CalfHeiferScorecard", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "CalfandHeifer", "CreateTimeUtc": "2021-11-11T03:30:29.828819900Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.058', false, 'd3728dda-ba0e-480a-8588-a398fec2ab45', '2023-01-15 11:39:46.058', '{"id": "d3728dda-ba0e-480a-8588-a398fec2ab45", "IsNew": false, "ToolId": "ManureScreener", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.938249600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.036', false, '55693cee-d791-4c15-908a-d223a47f1a3b', '2023-01-15 11:39:46.036', '{"id": "55693cee-d791-4c15-908a-d223a47f1a3b", "IsNew": false, "ToolId": "TMRParticleScore", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.922599300Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.090', false, '22e8568f-1faa-46f3-af1a-11101994a74d', '2023-01-15 11:39:46.090', '{"id": "22e8568f-1faa-46f3-af1a-11101994a74d", "IsNew": false, "ToolId": "RumenFill", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.953826500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.048', false, '45955c0e-ea62-4d2d-9189-a57704354caa', '2023-01-15 11:39:46.048', '{"id": "45955c0e-ea62-4d2d-9189-a57704354caa", "IsNew": false, "ToolId": "RumenHealthManureScore", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.922599300Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.144', false, 'a4723c37-aab4-401c-b2a7-1a7db9491c50', '2023-01-15 11:39:46.144', '{"id": "a4723c37-aab4-401c-b2a7-1a7db9491c50", "IsNew": false, "ToolId": "MetabolicIncidence", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.985091900Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.133', false, 'ba5b3360-0b1d-44fa-95a6-b9e08887ea92', '2023-01-15 11:39:46.133', '{"id": "ba5b3360-0b1d-44fa-95a6-b9e08887ea92", "IsNew": false, "ToolId": "UrinePHTool", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:29.969489800Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:29Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.156', false, '54c98877-c2da-42af-98d7-d56170d30e41', '2023-01-15 11:39:46.156', '{"id": "54c98877-c2da-42af-98d7-d56170d30e41", "IsNew": false, "ToolId": "ReadyToMilk", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2021-11-11T03:30:30.000718Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.201', false, '35946634-95b1-4de8-9270-b0d9eda01f2f', '2023-01-15 11:39:46.201', '{"id": "35946634-95b1-4de8-9270-b0d9eda01f2f", "IsNew": false, "ToolId": "Revenue", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2021-11-11T03:30:30.031972600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.220', false, '21e85709-b775-4a91-9599-ff4dfee9263b', '2023-01-15 11:39:46.220', '{"id": "21e85709-b775-4a91-9599-ff4dfee9263b", "IsNew": false, "ToolId": "MilkSoldEvaluation", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2021-11-11T03:30:30.031972600Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.237', false, '892ea505-c2c3-4325-bc9c-8487d7d254cb', '2023-01-15 11:39:46.237', '{"id": "892ea505-c2c3-4325-bc9c-8487d7d254cb", "IsNew": false, "ToolId": "RoboticMilkEvaluation", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2021-11-11T03:30:30.047580300Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.185', false, 'e17b6819-033c-4e0c-9be2-86a811cd3a63', '2023-01-15 11:39:46.185', '{"id": "e17b6819-033c-4e0c-9be2-86a811cd3a63", "IsNew": false, "ToolId": "PileAndBunker", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:30:30.016331500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.171', false, '88e8eb5e-5f8d-40e7-a871-45530a9f3dc4', '2023-01-15 11:39:46.171', '{"id": "88e8eb5e-5f8d-40e7-a871-45530a9f3dc4", "IsNew": false, "ToolId": "ForageAuditScorecard", "CountryId": "India", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2021-11-11T03:30:30.000718Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2021-11-11T03:30:30Z"}'::jsonb);
    </sql>
  </changeSet>

</databaseChangeLog>
