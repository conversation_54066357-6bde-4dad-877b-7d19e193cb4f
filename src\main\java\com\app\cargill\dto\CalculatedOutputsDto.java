/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CalculatedOutputsDto implements Serializable {
  private static final long serialVersionUID = 1L;

  private Double forageFeedCostPerDay;
  private Double grainsFeedCostPerDay;
  private Double purchasedBulkFeedCostPerDay;
  private Double purchasedBagsFeedCostPerDay;
  private Double totalPurchasedCostPerDay;
  private Double totalConcentrateCostPerDay;
  private Double totalFeedCostPerDay;

  private Double forageFeedCostPerCowPerDay;
  private Double grainsCostPerCowPerDay;
  private Double purchasedBulkFeedPerCowPerDay;
  private Double purchasedBagsFeedPerCowPerDay;
  private Double totalPurchasedCostPerCowPerDay;
  private Double totalConcentrateCostPerCowPerDay;
  private Double totalFeedCostPerCowPerDay;

  private Double forageKgDMPerDay;
  private Double grainsKgDMPerDay;
  private Double purchasedBulkKgDMPerDay;
  private Double purchasedBagsKgDMPerDay;
  private Double totalPurchasedCostKgDMPerDay;
  private Double totalConcentrateCostKgDMPerDay;
  private Double totalFeedCostKgDMPerDay;

  private Double foragePercentage;
}
