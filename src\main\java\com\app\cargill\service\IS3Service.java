/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.S3PresignedUrlsDto;
import java.io.IOException;
import java.util.List;

public interface IS3Service {

  String uploadFile(byte[] bytes, String fileName, boolean isPublic);

  String uploadFile(byte[] bytes, String fileName, String bucketName, Boolean isPublic);

  List<S3PresignedUrlsDto> generatePresignedUrls(List<S3PresignedUrlsDto> dto);

  byte[] getObjectFromS3(String keyName) throws IOException;

  List<S3PresignedUrlsDto> findObjects(String name);
}
