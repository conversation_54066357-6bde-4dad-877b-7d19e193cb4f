/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.constants.SortOrder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public class PageableUtil {

  private PageableUtil() {}

  public static Pageable getPageable(int page, int size, String sortBy, String sorting) {
    Pageable pageable;
    if (sorting.equalsIgnoreCase(SortOrder.DESCENDING)) {
      pageable = PageRequest.of(page, size, Sort.by(sortBy).descending());
    } else {
      pageable = PageRequest.of(page, size, Sort.by(sortBy).ascending());
    }
    return pageable;
  }
}
