/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.*;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("rumenFillHealthPenAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class RumenFillHealthPenAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;

  private final FreeMarkerComponent freeMarkerComponent;
  ResourceBundleMessageSource source;

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    RumenFillHealthPenAnalysisReportDto dto =
        modelMapper.map(data, RumenFillHealthPenAnalysisReportDto.class);

    try (XSSFWorkbook rumenFillWB = new XSSFWorkbook()) {
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              rumenFillWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(rumenFillWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              rumenFillWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(rumenFillWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              rumenFillWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(rumenFillWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(
              rumenFillWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      // create rumenFillWBSheet
      XSSFSheet rumenFillWBSheet =
          rumenFillWB.createSheet(
              ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      prepareHeader(rumenFillWB, rumenFillWBSheet, rowNumber, cellNumber, dto, boldStyle, locale);

      // create the data for Rumen Health pen analysis
      // calculated table heading
      cellNumber.set(0);
      XSSFRow row = rumenFillWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      rumenFillWBSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 9));

      // add standard deviation for Rumen Health pen analysis
      ExcelUtils.addAvgAndStdDeviation(
          rumenFillWBSheet,
          rowNumber,
          centerBlack,
          decimalStyle,
          source,
          locale,
          new Pair<>(dto.getAverage(), dto.getStandardDeviation()));
      // visit Dates
      cellNumber.set(0);

      int visitDateStartRowNumber = rowNumber.get();
      // categories
      row = rumenFillWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      for (RumenFillHealthPenAnalysisCategoryDto entry : dto.getCategories()) {

        ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, entry.getVisitDate());
      }

      // % chewing
      cellNumber.set(0);

      int categoryStartRowNumber = rowNumber.get();
      row = rumenFillWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CATEGORIES, null, source, locale));

      for (RumenFillHealthPenAnalysisCategoryDto entry : dto.getCategories()) {
        ExcelUtils.highlightEmptyCell(
            row, entry.getCategoryAverage(), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      // y0 axis
      int columnStart = 1;
      int columnEnd = columnStart + dto.getCategories().size() - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);
      XDDFDataSource<String> visitDateDataSource =
          XDDFDataSourcesFactory.fromStringCellRange(
              rumenFillWBSheet,
              new CellRangeAddress(
                  visitDateStartRowNumber, visitDateStartRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> categoryDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenFillWBSheet,
              new CellRangeAddress(
                  categoryStartRowNumber, categoryStartRowNumber, columnStart, columnEnd));

      // needed objects for the charts
      XSSFChart lineCharts;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData leftAxisData;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 3;
      // =======================first lineChart=====================
      lineCharts =
          ExcelUtils.initChart(
              rumenFillWBSheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CHART_NAME, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              lineCharts,
              ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              lineCharts,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CATEGORIES, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create lineCharts data
      leftAxisData =
          (XDDFLineChartData) lineCharts.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series =
          (XDDFLineChartData.Series)
              leftAxisData.addSeries(visitDateDataSource, categoryDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CATEGORIES, null, source, locale),
          new CellReference(
              rumenFillWBSheet.getSheetName(), categoryStartRowNumber, 0, true, true));
      lineCharts.plot(leftAxisData);
      ExcelUtils.setLineMarker(series, MarkerStyle.CIRCLE);
      ExcelUtils.drawLineSeries(leftAxisData, 0, PresetColor.GREEN, false);

      // ExcelUtils.drawGridLinesInChart(lineCharts, true);

      return ExcelUtils.finalizeWorkbook(rumenFillWB, rumenFillWBSheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    byte[] report =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.RUMEN_FILL_HEALTH_PEN_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    RumenFillHealthPenAnalysisReportDto dto =
        modelMapper.map(data, RumenFillHealthPenAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.RUMEN_FILL_HEALTH_PEN_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook wb,
      XSSFSheet wbSheet,
      AtomicInteger rowIndex,
      AtomicInteger cellIndex,
      RumenFillHealthPenAnalysisReportDto rumenFillPenAnalysisReportDto,
      XSSFCellStyle style,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(getClass(), wb, wbSheet, rowIndex.get(), cellIndex.getAndIncrement());
    // headings
    XSSFRow row = wbSheet.createRow(rowIndex.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellIndex,
        style,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellIndex, null, rumenFillPenAnalysisReportDto.getVisitName());

    wbSheet.addMergedRegion(new CellRangeAddress(rowIndex.get() - 1, rowIndex.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellIndex, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellIndex,
        style,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellIndex, null, rumenFillPenAnalysisReportDto.getVisitDate());
    wbSheet.addMergedRegion(new CellRangeAddress(rowIndex.get() - 1, rowIndex.get() - 1, 5, 6));

    // second row
    cellIndex.set(1);
    row = wbSheet.createRow(rowIndex.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellIndex,
        style,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellIndex, null, rumenFillPenAnalysisReportDto.getToolName());

    wbSheet.addMergedRegion(new CellRangeAddress(rowIndex.get() - 1, rowIndex.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellIndex, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellIndex,
        style,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellIndex, null, rumenFillPenAnalysisReportDto.getAnalysisType());

    wbSheet.addMergedRegion(new CellRangeAddress(rowIndex.get() - 1, rowIndex.get() - 1, 5, 6));
    ExcelUtils.createAndSetCellValue(row, cellIndex, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellIndex,
        style,
        ExcelUtils.getLangValue(LangKeys.REPORT_PEN_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellIndex, null, rumenFillPenAnalysisReportDto.getPenName());
    wbSheet.addMergedRegion(new CellRangeAddress(rowIndex.get() - 1, rowIndex.get() - 1, 8, 9));
  }
}
