/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.controller.ReportControllerTest;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class JsonUtilsTest {
  @Test
  void toJsonReturnsJsonString() throws JsonProcessingException {
    assertNotNull(JsonUtils.toJson(ReportControllerTest.prepareDataForCudChewingHerdAnalysis()));
  }

  @Test
  void whenStringifiedObjectIsConvertedToJsonObjectSuccessfully() {
    assertNotNull(JsonUtils.convertStringToJsonObject("{}"));
  }

  @Test
  void whenStringifiedArrayIsConvertedToJsonArraySuccessfully() {
    assertNotNull(JsonUtils.convertStringToJsonArray("[]"));
  }
}
