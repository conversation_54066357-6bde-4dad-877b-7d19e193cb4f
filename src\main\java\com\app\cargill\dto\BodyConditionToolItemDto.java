/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BodyConditionToolItemDto implements Serializable {

  private UUID penId;

  private String penName;

  private List<UUID> bodyConditionScoreVisitsSelected;

  private List<BodyConditionToolItemScoreItemDto> bodyConditionScores;

  private ToolStatuses toolStatus;

  private int daysInMilk;

  private Double milk;

  private boolean isToolItemNew;

  private boolean isFirstTimeWithScore;
}
