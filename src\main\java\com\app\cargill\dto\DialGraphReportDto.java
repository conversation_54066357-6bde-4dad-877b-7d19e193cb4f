/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.List;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DialGraphReportDto extends BaseDto {
  private String label;
  private double minValue;
  private double maxValue;
  private double value;
  private int totalGaugeLabels; // splitNumber
  private String redRange;
  private String greenRange;
  private String yellowRange;
  @Builder.Default private double gaugeTotal = 4.86;
  private List<GaugeColorDto> gaugeColors;
}
