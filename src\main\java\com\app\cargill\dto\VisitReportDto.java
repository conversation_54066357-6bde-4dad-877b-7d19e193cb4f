/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.List;
import java.util.UUID;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@SuppressWarnings("java:S125")
public class VisitReportDto {
  private UUID visitId;
  private String fileName;
  private String brandName;
  private String visitName;
  private String accountName;
  private String
      accountType; //  Prospect(0),  Customer(1),  Thirdparty(2),  Consumer(3),  Competitor(4);
  private String siteName;
  private String visitDate;
  private VisitReportSiteDetailsDto siteDetails;
  private List<NotesDto> notes;
  // 1 tool
  private VisitReportRumenHealthCudChewingDto rumenHealthCudChewingTool;
  // 2 tool
  private VisitReportBodyConditionScoreDto bodyConditionScoreTool;
  // 3 tool
  private VisitReportLocomotionScoreDto locomotionScoreTool;
  // 4 tool
  private VisitReportRumenHealthManureScoreDto rumenHealthManureScoreTool;
  // 5 tool
  private VisitReportMilkSoldEvaluationDto milkSoldEvaluationTool;
  // 6 tool
  private VisitReportRoboticMilkEvaluationDto roboticMilkEvaluationTool;
  // 7 tool
  private VisitReportMetabolicIncidenceDto metabolicIncidenceTool;
  // 8 tool
  private VisitReportRumenFillDto rumenFillTool;
  // 9 tool
  private VisitReportForageInventoriesDto forageInventoriesTool;
  // 10 tool
  private VisitReportForagePennStateDto foragePennStateTool;
  // 11 tool
  private VisitReportRumenHealthTMRParticleScoreDto rumenHealthTMRParticleScoreTool;
  // 12 tool
  private VisitReportRumenHealthManureScreeningDto rumenHealthManureScreeningTool;
  // 13 tool
  private VisitReportHeatStressEvaluationDto heatStressEvaluationTool;
  // 14 tool
  private VisitReportForageAuditDto forageAuditTool;
  // 15 tool
  private VisitReportPenTimeBudgetDto penTimeBudgetTool;
  // 16 tool
  private VisitReportScorecardDto scorecardTool;
  private List<String> generalComments;
}
