/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class PenTimeBudgetToolItemDto extends EditableToolPenEntityBaseDto {

  private static final long serialVersionUID = 1L;

  private UUID visitForComparison;

  private List<UUID> selectedRestingVisits;

  private List<UUID> selectedMilkVisits;

  private Integer stallsInPen;

  private Double walkingTimeToParlor;

  private Double timeInParlor;

  private Double walkingTimeFromParlor;

  private Double stallsInParlor;

  private Double timeInLockUp;

  private Double otherNonRestTime;

  private Double restingRequirement;

  private Double eatingTime;

  private Double drinkingGroomingTime;

  private ToolStatuses toolStatus;

  private Double animals;

  private Double milkingFrequency;
}
