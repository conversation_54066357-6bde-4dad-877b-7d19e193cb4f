<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="023" author="Taha">
        <sql>
            CREATE UNIQUE INDEX IF NOT EXISTS accounts_document_id_uidx ON accounts((account_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS sites_document_id_uidx ON sites((site_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS pens_document_id_uidx ON pens((pen_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS country_tool_document_id_uidx ON country_tools((country_tool_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS users_document_id_uidx ON users((user_document->>'UserName'));
            CREATE UNIQUE INDEX IF NOT EXISTS diets_document_id_uidx ON diets((diet_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS user_preferences_document_id_uidx ON user_preferences((user_preference_document->>'UserId'));
            CREATE UNIQUE INDEX IF NOT EXISTS cities_document_id_uidx ON cities((city_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS states_document_id_uidx ON states((state_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS countries_document_id_uidx ON countries((country_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS content_details_document_id_uidx ON content_details((content_details_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS milk_processors_document_id_uidx ON milk_processors((milk_processors_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS notifications_document_id_uidx ON notifications((notification_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS notes_document_id_uidx ON notes((notes_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS visits_document_id_uidx ON visits((visit_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS ear_tags_document_id_uidx ON ear_tags((ear_tag_document->>'id'));
            CREATE UNIQUE INDEX IF NOT EXISTS silages_document_id_uidx ON silages((silage_document->>'id'));
        </sql>
    </changeSet>

</databaseChangeLog>