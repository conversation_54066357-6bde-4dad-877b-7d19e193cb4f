/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.SitesLiftDeletedWrapperList;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.sf.cc.api.model.SObjectsResponse;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.config.LiftConfig;
import com.app.cargill.sf.cc.config.PickListValueLookUp;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.FieldsMetadata;
import com.app.cargill.sf.cc.model.LiftMetadata;
import com.app.cargill.sf.cc.model.MetaDataFields;
import com.app.cargill.sf.cc.model.MetaDataPicklistValues;
import com.app.cargill.sf.cc.model.PhysicalAddressSalesforce;
import com.app.cargill.sf.cc.model.simple.Account;
import com.app.cargill.sf.cc.model.simple.AccountUpdateModel;
import com.app.cargill.sf.cc.model.simple.ContactSalesforce;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.Arrays;
import java.util.Locale;
import java.util.UUID;
import lombok.Data;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class LiftApiServiceTest {
  private LiftConfig config;
  private LiftApiService service;
  private LiftApiReactiveService liftApiReactiveService;

  private static MockWebServer mockBackEnd;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeAll
  static void setUp() throws IOException {
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();
  }

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @BeforeEach
  void initialize() {
    config = mock(LiftConfig.class);
    lenient().when(config.getGrantType()).thenReturn("client_credentials");
    lenient().when(config.getTokenAuthHeader()).thenCallRealMethod();
    service =
        new LiftApiService(
            new LiftApiReactiveService(new SalesforceClientFactory(WebClient.builder()), config));
    liftApiReactiveService =
        new LiftApiReactiveService(new SalesforceClientFactory(WebClient.builder()), config);
  }

  @AfterAll
  static void tearDown() throws IOException {
    mockBackEnd.shutdown();
  }

  @Test
  void whenVersionsAreFetchedTheCorrectLatestIsReturned() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(Path.of("src/test/resources/salesforce/get-versions-resp.json")))
            .addHeader("Content-Type", "application/json"));

    VersionObject versionObject = service.getLatestApiVersion(authToken);

    assertNotNull(versionObject);
    assertEquals("56.0", versionObject.getVersion());
    assertEquals("Winter '23", versionObject.getLabel());
    assertEquals("/services/data/v56.0", versionObject.getUrl());
  }

  @Test
  void whenSobjectsAreFethedTheCorrectResponseIsReturned() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(
                    Path.of("src/test/resources/salesforce/get-sobjects-partial-resp.json")))
            .addHeader("Content-Type", "application/json"));

    SObjectsResponse sObjects = service.getSObjectsDefinitions(authToken);
    assertNotNull(sObjects);
    assertEquals(1, sObjects.getSobjects().size());
  }

  @Test
  void whenCredentialsAreWrongCorrectExceptionIsThrown() {
    when(config.getTokenHost()).thenReturn("localhost");
    when(config.getTokenPath()).thenReturn("/");
    when(config.getPort()).thenReturn(String.valueOf(mockBackEnd.getPort()));
    when(config.getScheme()).thenReturn("http");

    String tokenResponse =
        "{\"error\": \"invalid_client_id\",\"error_description\": \"client identifier invalid\"}";
    mockBackEnd.enqueue(
        new MockResponse()
            .setResponseCode(400)
            .setBody(tokenResponse)
            .addHeader("Content-Type", "application/json"));

    WebClientResponseException thrown =
        assertThrows(WebClientResponseException.class, () -> service.getToken());

    assertTrue(thrown.getResponseBodyAsString().contentEquals(tokenResponse));
  }

  private AuthToken getToken() {
    when(config.getTokenHost()).thenReturn("localhost");
    when(config.getTokenPath()).thenReturn("/");
    when(config.getPort()).thenReturn(String.valueOf(mockBackEnd.getPort()));
    when(config.getScheme()).thenReturn("http");

    String tokenResponse =
        "{"
            + "\"access_token\":\"fake-token\",\"instance_url\":\"http://localhost:"
            + mockBackEnd.getPort()
            + "\","
            + "\"id\":\"https://some-id.com\","
            + "\"token_type\":\"Bearer\","
            + "\"issued_at\":\"*************\","
            + "\"signature\":\"agkg9etMNx9DsS9uXiXy6rcAModj88NmlNF6byfBMd4=\"}";
    mockBackEnd.enqueue(
        new MockResponse().setBody(tokenResponse).addHeader("Content-Type", "application/json"));
    return service.getToken();
  }

  @Test
  void whenAccountIsUpdatedWithoutErrorsEverythingPasses() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    assertDoesNotThrow(
        () -> {
          service.updateRecord(
              authToken, accountUpdateModel, new ParameterizedTypeReference<>() {}, "/anything");
        });
  }

  @Test
  void whenAccountIsUpdatedWithErrorsExceptionIsThrown() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(new MockResponse().setResponseCode(400));
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    ParameterizedTypeReference<AccountUpdateModel> typeReference =
        new ParameterizedTypeReference<>() {};
    assertThrows(
        WebClientResponseException.class,
        () -> service.updateRecord(authToken, accountUpdateModel, typeReference, "/anything"));
  }

  @Test
  void testCreateResponseWithEmptyJsonArray() {

    String responseBody = "[]";
    byte[] byteArray = responseBody.getBytes(java.nio.charset.StandardCharsets.UTF_8);
    PayloadValidationDto result =
        service.createResponse(
            new WebClientResponseException(404, "Not Found", null, byteArray, null));

    assertNull(result);
  }

  @Test
  void testCreateResponseWithNullJsonObj() {

    PayloadValidationDto result =
        service.createResponse(
            new WebClientResponseException(422, "Unprocessable Entity", null, null, null));

    assertNull(result);
  }

  @Test
  void testCreateResponseWithValidInput() {
    String responseBody =
        "[{\"duplicateResult\":{\"allowSave\":false,\"duplicateRule\":\"Standard_Contact_Duplicate_Rule\",\"duplicateRuleEntityType\":\"Contact\",\"errorMessage\":\"You're"
            + " creating a duplicate record. We recommend you use an existing record"
            + " instead.\",\"matchResults\":[{\"entityType\":\"Contact\",\"errors\":[],\"matchEngine\":\"ExactMatchEngine\",\"matchRecords\":[{\"additionalInformation\":[],\"fieldDiffs\":[],\"matchConfidence\":100.0,\"record\":{\"attributes\":{\"type\":\"Contact\",\"url\":\"/services/data/v59.0/sobjects/Contact/0037j00000yCsopAAC\"},\"Id\":\"0037j00000yCsopAAC\"}}],\"rule\":\"Restrict_Contact_Creation\",\"size\":1,\"success\":true}]},\"errorCode\":\"DUPLICATES_DETECTED\",\"message\":\"You're"
            + " creating a duplicate record. We recommend you use an existing record instead.\"}]";

    byte[] byteArray = responseBody.getBytes(java.nio.charset.StandardCharsets.UTF_8);

    PayloadValidationDto result =
        service.createResponse(
            new WebClientResponseException(422, "Unprocessable Entity", null, byteArray, null));

    assertNotNull(result);
    assertEquals(1, result.getErrorDetails().size());

    LiftResponseEntityDto dto = result.getErrorDetails().get(0);
    assertEquals(ResponseStatus.FAILED, dto.getStatus());
  }

  @Test
  void whenAccountIsCreatedWithoutErrorsEverythingPasses() throws IOException {
    // because of the mockwebserver response chaining, this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    assertDoesNotThrow(
        () -> {
          service.createRecord(
              authToken, accountUpdateModel, new ParameterizedTypeReference<>() {}, "/anything");
        });
  }

  @Test
  void whenAccountIsCreatedWithErrorsExceptionIsThrown() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(new MockResponse().setResponseCode(400));
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    ParameterizedTypeReference<AccountUpdateModel> typeReference =
        new ParameterizedTypeReference<>() {};
    assertThrows(
        WebClientResponseException.class,
        () -> service.createRecord(authToken, accountUpdateModel, typeReference, "/anything"));
  }

  @Test
  void whenGetRecordReturnsNull() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();
    mockBackEnd.enqueue(new MockResponse().setResponseCode(200));
    Mono<Serializable> records =
        service
            .getLiftApiReactiveService()
            .getRecords(authToken, "/sobjects/Account/describe", Serializable.class);
    assertNull(records.block());
  }

  @Test
  void whenInitPopulatesMetaDataCorrectly() throws IOException, IllegalAccessException {
    // because of the mockwebserver response chaining this should be first
    when(config.getTokenHost()).thenReturn("localhost");
    when(config.getTokenPath()).thenReturn("/");
    when(config.getPort()).thenReturn(String.valueOf(mockBackEnd.getPort()));
    when(config.getScheme()).thenReturn("http");

    String tokenResponse =
        "{"
            + "\"access_token\":\"fake-token\",\"instance_url\":\"http://localhost:"
            + mockBackEnd.getPort()
            + "\","
            + "\"id\":\"https://some-id.com\","
            + "\"token_type\":\"Bearer\","
            + "\"issued_at\":\"*************\","
            + "\"signature\":\"agkg9etMNx9DsS9uXiXy6rcAModj88NmlNF6byfBMd4=\"}";
    mockBackEnd.enqueue(
        new MockResponse().setBody(tokenResponse).addHeader("Content-Type", "application/json"));
    mockBackEnd.enqueue(
        new MockResponse()
            .setBody(
                Files.readString(Path.of("src/test/resources/salesforce/get-versions-resp.json")))
            .addHeader("Content-Type", "application/json"));
    for (int i = 0; i < 5; i++) {
      mockBackEnd.enqueue(
          new MockResponse().addHeader("Content-Type", "application/json").setResponseCode(200));
    }
    LiftMetadata records = service.init();
    assertNotNull(records);
  }

  @Test
  void whenValidateReturnsSuccess() throws IllegalAccessException, ClassNotFoundException {
    LiftMetadata metadata =
        LiftMetadata.builder()
            .authToken(getToken())
            .apiPath("/test/")
            .fieldsMetadataForAccount(
                FieldsMetadata.builder()
                    .fields(
                        Arrays.asList(
                            MetaDataFields.builder()
                                .name("Salutation")
                                .nillable(true)
                                .type("string")
                                .picklistValues(
                                    Arrays.asList(
                                        MetaDataPicklistValues.builder()
                                            .active(true)
                                            .value("Mr.")
                                            .label("Mr.")
                                            .build()))
                                .aggregatable(true)
                                .build()))
                    .build())
            .fieldsMetadataForContact(
                FieldsMetadata.builder()
                    .fields(
                        Arrays.asList(
                            MetaDataFields.builder()
                                .name("Id")
                                .type("string")
                                .aggregatable(true)
                                .build()))
                    .build())
            .fieldsMetadataForSiteMapping(
                FieldsMetadata.builder()
                    .fields(Arrays.asList(MetaDataFields.builder().name("test").build()))
                    .build())
            .fieldsMetadataForSite(
                FieldsMetadata.builder()
                    .fields(Arrays.asList(MetaDataFields.builder().name("test").build()))
                    .build())
            .fieldsMetadataForEvent(
                FieldsMetadata.builder()
                    .fields(Arrays.asList(MetaDataFields.builder().name("test").build()))
                    .build())
            .build();
    ContactSalesforce contactSalesforce = new ContactSalesforce();
    contactSalesforce.setId(UUID.randomUUID().toString());
    contactSalesforce.setSalutation("Mr.");
    contactSalesforce.setOtherAddress(PhysicalAddressSalesforce.builder().state("pk").build());
    service.setLiftMetaData(metadata);
    assertNotNull(
        service.validate(
            contactSalesforce, LiftEntityName.ACCOUNT, locale, resourceBundleMessageSource));
    assertNotNull(
        service.validate(
            contactSalesforce, LiftEntityName.CONTACT, locale, resourceBundleMessageSource));
    assertNotNull(
        service.validate(
            new ContactSalesforce(), LiftEntityName.SITE, locale, resourceBundleMessageSource));
    assertNotNull(
        service.validate(
            contactSalesforce, LiftEntityName.SITE_MAPPING, locale, resourceBundleMessageSource));
    assertNotNull(
        service.validate(
            contactSalesforce, LiftEntityName.EVENT, locale, resourceBundleMessageSource));
    Account accountUpdateModel = new Account();
    accountUpdateModel.setId(UUID.randomUUID().toString());
    accountUpdateModel.setBillingAddress(PhysicalAddressSalesforce.builder().state("pak").build());
    accountUpdateModel.setCustomerAccountType("test");
    assertNotNull(
        service.validate(
            contactSalesforce, LiftEntityName.ACCOUNT, locale, resourceBundleMessageSource));

    TestAccount testAccount = new TestAccount();
    testAccount.setAttributes("test");
    testAccount.setValue("Mr.");
    assertNotNull(
        service.validate(testAccount, LiftEntityName.ACCOUNT, locale, resourceBundleMessageSource));
  }

  @Test
  void testgetRecordsWithQueryParam() throws IOException {
    // because of the mockwebserver response chaining this should be first
    AuthToken authToken = getToken();

    mockBackEnd.enqueue(new MockResponse().setResponseCode(204));
    AccountUpdateModel accountUpdateModel = new AccountUpdateModel();
    assertDoesNotThrow(
        () -> {
          service.getRecordsWithQueryParam(
              authToken,
              "/anything",
              SitesLiftDeletedWrapperList.class,
              Instant.now(),
              Instant.now());
        });
  }

  @Data
  public class TestAccount implements Serializable {

    @Serial private static final long serialVersionUID = 1L;

    @JsonProperty("id")
    @JsonAlias("test")
    @PickListValueLookUp(key = "Salutation", value = "label")
    private String attributes;

    @JsonProperty("value")
    @JsonAlias("Salutation")
    @PickListValueLookUp(key = "Salutation", value = "label")
    private String value;
  }
}
