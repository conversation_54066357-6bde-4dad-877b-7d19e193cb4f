/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.app.cargill.document.EventDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@ExtendWith(MockitoExtension.class)
@SuppressWarnings("unchecked")
class LiftEventServiceTest {

  @Mock private LiftApiService liftApi;
  @InjectMocks private LiftEventService eventService;
  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @Test
  void whenCreateSucceedsCorrectObjectIsReturned() throws Exception {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setDeActivityExternalIdC("123");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setSubject("sub");
    eventDocument.setStartDateTime("date");
    eventDocument.setActivityDateTime("datetime");
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    eventDocument.setTypeC("typec");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    EventDocument result = eventService.createEvent(eventDocument, null, null);
    assertEquals(createRecordResponse.getId(), result.getEventId());
  }

  @Test
  void whenCreateObjectIsReturnedError() throws JsonProcessingException, CustomDEExceptions {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setDeActivityExternalIdC("123");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setSubject("sub");
    eventDocument.setStartDateTime("date");
    eventDocument.setActivityDateTime("datetime");
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    eventDocument.setTypeC("typec");
    doThrow(new WebClientResponseException(422, "Unprocessable Entity", null, null, null))
        .when(liftApi)
        .createRecord(any(), any(), any(), any());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        CustomDEExceptions.class,
        () -> eventService.createEvent(eventDocument, null, bundleMessageSource));
  }

  @Test
  void whenCreateFailedMissingMandatoryFields() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setDeActivityExternalIdC("123");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setSubject("sub");

    eventDocument.setTypeC("typec");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertThrows(
        IllegalArgumentException.class, () -> eventService.createEvent(eventDocument, null, null));
  }

  @Test
  void whenCreateFailedForMissingMandatoryFieldStartDate() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setDeActivityExternalIdC("123");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setSubject("sub");
    eventDocument.setTypeC("typec");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertThrows(
        IllegalArgumentException.class, () -> eventService.createEvent(eventDocument, null, null));
  }

  @Test
  void whenCreateFailedForMissingMandatoryFieldDeSiteVisitIdC() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setStartDateTime("07/14/2023");

    eventDocument.setSubject("sub");
    eventDocument.setTypeC("typec");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertThrows(
        IllegalArgumentException.class, () -> eventService.createEvent(eventDocument, null, null));
  }

  void whenCreateFailedForMissingMandatoryFieldSubject() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setStartDateTime("07/14/2023");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setTypeC("typec");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertThrows(
        IllegalArgumentException.class, () -> eventService.createEvent(eventDocument, null, null));
  }

  void whenCreateFailedForMissingMandatoryFieldTypeC() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setStartDateTime("07/14/2023");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setDeActivityExternalIdC("externalid");
    eventDocument.setSubject("sub");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertThrows(
        IllegalArgumentException.class, () -> eventService.createEvent(eventDocument, null, null));
  }

  void whenCreateFailedForMissingMandatoryFieldDeActivityExternalIdC() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("123");
    eventDocument.setIsReminderSet(true);
    eventDocument.setStartDateTime("07/14/2023");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setSubject("sub");
    eventDocument.setTypeC("typec");

    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertThrows(
        IllegalArgumentException.class, () -> eventService.createEvent(eventDocument, null, null));
  }

  @Test
  void whenUpdateSucceedsCorrectObjectIsReturned() {
    EventDocument eventDocument = new EventDocument();
    eventDocument.setId(UUID.randomUUID());
    eventDocument.setEventId("321");
    eventDocument.setIsReminderSet(true);
    eventDocument.setDeActivityExternalIdC("321");
    eventDocument.setDeSiteVisitIdC("external");
    eventDocument.setSubject("update");
    eventDocument.setStartDateTime("09/01/2023");
    eventDocument.setActivityDateTime("09/01/2023");
    eventDocument.setTypeC("typec");
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-golden-record-id");
    assertDoesNotThrow(() -> eventService.updateEvent(eventDocument));
  }
}
