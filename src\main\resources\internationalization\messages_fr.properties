(Préparation=des trayons inadéquate\: &lt;10 secondes; mise en place des manchons à traire\:  &lt; 60 ou &gt; 120 secondes après le nettoyage)
AAEfficiency=Efficacité des AA
AMSUtilization=Utilisation du robot de traite
AMSUtilizationChart=Tableau d'utilisation du robot de traite
ARA=ARA
ARS=Argentine ($ ARS)
AUD=Australie ($ AUD)
Account=Compe
Account-Not-Synced-To-Lift=Le compte n'a pas été synchronisé avec LIFT ; veuillez contacter l'administrateur pour une résolution
Acre=Acre
Action=Action
AddBag=Ajouter un boudin
AddBunker=Ajouter un silo couloir
AddPile=Ajouter un silo taupinière
AddTMRScore=Ajouter un score
AdjustingKPtoAssureSuccess=Contrôle de l'éclatement du grain
Afghanistan=Afghanistan
Agrigento=Agrigento
Aguascalientes=Aguascalientes
Alabama=Alabama
Alagoas=Alagoas
Aland_Islands=Iles Aland
Alaska=Vers le bas
Albania=Albanie
Alberta=Alberta
Alessandria=Alexandrie
Algeria=Algérie
AmapÃ¡=Ampasse
Amazonas=Amazonas
Amount=Quantité
Ancona=AncÃ´ne
Andaman_and_Nicobar_Islands=Ãles Andaman et Nicobar
Andhra_Pradesh=Andhra Pradesh
Andorra=Andorre
Angola=Angola
Anguilla=Anguille
Anhui=Anhui
AnimalInformation=Informations sur l'animal
AnimalListViewModel.Title=Classe de l'animal / Sous-classe de l'animal
Animals=Animaux
AnimalsInHerd=Animaux dans l'élevage
AnimalsInPen=Animaux dans le lot
AnimalsObserved=Animaux observés
Annually=Annuellement
Answers=Réponses
Antarctica=Antarctique
Antigua_and_Barbuda=Antigua-et-Barbuda
Aosta=Aosta
AppName=Dairy Enteligen
Arezzo=Arezzo
Argentina=Argentine
Arizona=Arizona
Arkansas=Arkansa
Armenia=ArmÃ©nie
Aruba=Aruba
Arunachal_Pradesh=Arunachal Pradesh
Ascoli_Piceno=Ascoli Piceno
Assam=Assam
Asti=Jusqu'Ã 
AtSixLengthPerDayImperial=à 15 cm par jour
AtSixLengthPerDayMetric=à 15 cm. par jour
AtThreeLengthPerDayImperial=à 8 cm par jour
AtThreeLengthPerDayMetric=à 7 cm par jour
Australia=Australie
Australian_Capital_Territory=Territoire de la capitale australienne
Austria=L'Autriche
Auto_Sync=Auto Sync
Avellino=Avellino
Average=Moyen
AverageMilkLoss=Perte moyenne de lait ({0})
AverageScoreTitle=Moyenne du Score des particules de la ration mélangée
AvgBCS=État corporel moyen
AvgLocomotionScore=Score de locomotion moyen (calculé)
Azerbaijan=AzerbaÃ¯djan
BAM=BAM
BCS=État corporel
BCSCategory1=Note d'état corporelle \= 1
BCSCategory1pt5=Note d'état corporelle \= 1.5
BCSCategory2=Note d'état corporelle \= 2
BCSCategory2pt5=Note d'état corporelle \= 2.5
BCSCategory3=Note d'état corporelle \= 3
BCSCategory3pt5=Note d'état corporelle \= 3.5
BCSCategory4=Note d'état corporelle \= 4
BCSCategory4pt5=Note d'état corporelle \= 4.5
BCSCategory5=Note d'état corporelle \= 5
BCSEditMilkAndDimViewModel.BCSDIMTitle=Jours de lactation
BCSEditMilkAndDimViewModel.BCSMilkTitle=Production laitière
BCSEditMilkAndDimViewModel.Title=Modifier le jour de lactation et la production laitière
BCSHerdAnalysisInputsViewModel.BCS=Note d'état corporel
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Analyser l'état corporel
BCSHerdAnalysisInputsViewModel.BCSDIM=Jours de lactation
BCSHerdAnalysisInputsViewModel.BCSEdit=Modifier
BCSHerdAnalysisInputsViewModel.BCSMilk=Production laitière
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Analyse de troupeau
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Objectifs
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Données
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Résultats
BCSHerdAnalysisMasterViewModel.BCSTitle=Note d'état corporel
BCSHerdAnalysisMasterViewModel.Title=Note d'état corporel
BCSHerdAnalysisResultsViewModel.BCSAvg=État corporel moyen
BCSHerdAnalysisResultsViewModel.GraphTitle=Analyse de la note d'état corporel
BCSHerdAnalysisResultsViewModel.MaxBCS=État corporel maximum
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Lait/vache/jour
BCSHerdAnalysisResultsViewModel.MinBCS=État corporel minimum
BCSHerdAnalysisResultsViewModel.SubHeading=Analyse du troupeau
BCSHerdAnalysisResultsViewModel.Title=Note d'état corporel
BCSPenSelectionViewModel.PenSelectionList=GROUPES
BCSPenSelectionViewModel.Pens=Groupes
BCSPenSelectionViewModel.SelectPointScale=Selectionnez une échelle de temps
BCSPenSelectionViewModel.Title=Note d'état corporel
BCSSelectPointScaleViewModel.FooterText=Une seule échelle de temps peut être utilisée par visite. Changer l'échelle de temps entraînera la perte de valeurs.
BCSSelectPointScaleViewModel.SelectPointScale=Selectionnez une échelle de temps
BCSSelectPointScaleViewModel.Title=Note d'état corporel
BGL=BGL
BRL=Brésil (R$ BRL)
BRR=BRR
Bad=Bad
Bag=Silo
BaggedConventionalSilage=Boudins d'ensilages
Bags=Silo
Bahamas=Bahamas
Bahia=Bahia
Bahrain=BahreÃ¯n
Baja_California=Baja Californie
Baja_California_Sur=Baja California Sur
Baleage=Enrubannage
BaleageFQAs=Enrubannage FAQ
Baleage_AreBalesWrappedWith=Est-ce que les bottes sont filmées avec\:
Baleage_BagsPlacedOnStableWellManagedSurface=Les bottes sont placées sur une surface plane et propre toute la saison (asphalte ou béton)?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Les bottes sont contrôlés régulièrement et les trous sont réparés? (chaque semaine)
Baleage_TrashVegRodentControlledAroundBags=Les déchets, la végétation et les rongeurs sont maitrisés autour des bottes?
Baleage_WaterShedsOffPlasticNotIntoBaleage=L'eau ne pénètre pas dans les bottes d'enrubannage ou le silo ?
Bangladesh=Bangladesh
Barbados=Barbade
Bari=Ils Ã©taient
Barletta-Andria-Trani=Barletta-andria-Trani
Bayern=Bayern
BeddedPack=Aire paillée
Beijing=PÃ©kin
Belarus=BiÃ©lorussie
Belgium=Belgique
Belize=Belize
Belluno=Belluno
Benchmarks_Serum_ToolTip=<value />
Benevento=Bienfaisance
Benin=BÃ©nin
Bergamo=Bergame
Bermuda=Bermudes
BetweenFifteenTwenty=Entre 15 et 21
Bhutan=Bhoutan
BiWeekly=Toutes les 2 semaines
Biella=Birella
Bihar=Bihar
BodyConditionHerdGoals=Note d'état corporel - évaluation du troupeau - objectifs
BodyConditionHerdInputs=Note d'état corporel - évaluation du troupeau - données
BodyConditionHerdResults=Note d'état corporel - évaluation du troupeau - résultats
BodyConditionInputs=Note d'état corporel - données
BodyConditionResults=Note d'état corporel - résultats
BodyConditionScoreCategory=Note d'état corporel Categorie {0}
BodyConditionScoreEditInputsViewModel.Count=Nombre
BodyConditionScoreEditInputsViewModel.NumberOfCows=Nombre de vaches
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Veuillez compter le nombre de vaches
BodyConditionScoreEditInputsViewModel.Title=Nombre de vaches
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=Vaches en préparation vêlage (-20 à -1 jours)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=Début de lactation (16 à 60 jours de lactation)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=Vaches Taries (- 60 à - 21 jours avant vêlage)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=Fraiches vêlées (0 à 15 jours)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=Fin de lactation (plus de 201 j de lactation)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=Note d'état corporel minimum
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=Milieu de lactation (121 à 200 j de lactation)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=Note d'état corporel minimum
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=Pic de lactation (61 à 120 jours de lactation)
BodyConditionScoreHerdEditGoalsViewModel.Title=Modifier les objectifs
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Vaches en préparation vêlage (-20 à -1 jours)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Début de lactation (16 à 60 jours de lactation)
BodyConditionScoreHerdGoalsViewModel.Edit=Modifier
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Vaches Taries (- 60 à - 21 jours avant vêlage)
BodyConditionScoreHerdGoalsViewModel.Fresh=Fraiches vêlées (0 à 15 jours)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Maximum objectif pour l'état corporel
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Minimum objectif pour l'état corporel
BodyConditionScoreHerdGoalsViewModel.LateLactation=Fin de lactation (plus de 201 j de lactation)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Milieu de lactation (121 à 200 j de lactation)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Pic de lactation (61 à 120 jours de lactation)
BodyConditionScoreHerdGoalsViewModel.TableTitle=Objectif de note d'état corporel(NEC)
BodyConditionScoreInputsViewModel.AnimalsObserved=Nombre d'animaux évalués
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Note d'état corporel moyen (calculée)
BodyConditionScoreInputsViewModel.BCSCategory=Catégorie NEC
BodyConditionScoreInputsViewModel.BCSPercentOfPen=% du lot
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=Note d'état corporel
BodyConditionScoreInputsViewModel.Edit=Modifier
BodyConditionScoreInputsViewModel.StdDevCalculated=Variation (calculée)
BodyConditionScoreMasterViewModel.Goals=Objectifs
BodyConditionScoreMasterViewModel.Inputs=Données
BodyConditionScoreMasterViewModel.Results=Résultats
BodyConditionScoreMasterViewModel.SubHeading=Analyse du troupeau
BodyConditionScoreMasterViewModel.Title=État corporel
BodyConditionScoreResultsViewModel.BCSAverageTitle=Score moyen
BodyConditionScoreResultsViewModel.PercentPen=Pourcentage du lot (%)
BodyConditionScoreResultsViewModel.SelectedDates=Sélectionnez les dates
BodyConditionScoreResultsViewModel.Title=Note d'état corporel - résultats
BodyConditionScoresMasterViewModel.BodyConditionScore=Note d'état corporel
BodyConditionScoresMasterViewModel.Inputs=Données
BodyConditionScoresMasterViewModel.Results=Résultats
BodyConditionScoresMasterViewModel.Title=Note d'état corporel
BodyConditionScoresMasterViewModel.VisitNotebook=Cahier de notes
Bolivia,_Plurinational_State_of=Bolivie, Ã©tat plurinal de
Bologna=Bologne
Bolzano=Bolzano
Bonaire=Bonaire
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius et Saba
Bosnia_and_Herzegovina=Bosnie HerzÃ©govine
Botswana=Botwana
BottomUnloadingSilo=Silo tour
BottomUnloadingSilos=Silo tour
Bouvet_Island=Ãle Bouvet
Brazil=Brésil
Brescia=Brescia
Brindisi=Toasts
British_Columbia=Colombie britannique
British_Indian_Ocean_Territory=Territoire britannique de l'ocÃ©an Indien
Brunei_Darussalam=Brunei Darussalam
Bulgaria=Bulgarie
Bull=Taureau
Bunker=Silo couloir
BunkerCapacity=Silo couloir-Capacité
BunkerFeedOutRate=Silo couloir-Reste
Bunkers=Silo couloir
BunkersAndPiles=Silo couloir et silo taupinière
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Bonus \: 2 couches de plastique avec une couche imperméable?
BunkersAndPiles_CleanlinessOfFeedArea=Propreté de la zone d'alimentation? Échelle de notation 1 - 10, 10 étant le meilleur
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=A quelle fréquence la bache est reculée lors de la prise d'ensilage?
BunkersAndPiles_FaceRemoveRate=Vitesse d'avancement quotidien du front d'attaque
BunkersAndPiles_LooseOrFacedFeedIsFed=Sous combien de temps l’ensilage détassé est distribué?
BunkersAndPiles_PackingInitialSpreadLayers=Tassage de couches de moins de 15 cm à la fois ?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Pile slope and bunker crown should not be less than a 3\:1 run to rise ratio (18 degree Slope)
BunkersAndPiles_PorosityScoresConsistently=Le tassage du silo est homogène
BunkersAndPiles_SealedImmedAfterPack6milPlastic=Couvert immédiatement après la fin du tassage?
BunkersAndPiles_SideWallsSealedPlastic=Les murs sont-ils couverts de plastiques ? 
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Front d’attaque lisse, pas de signe de passages permettant à l’oxygène de s'introduire dans le silo
BunkersAndPiles_TiresSplitsTouching=Pneus/boudins côte à côte?
Burkina_Faso=Burkina Faso
Burundi=Burundi
CAD=Canada (CA$ CAD)
CFNChina=Chine
CFNIndia=Inde
CHF=Suisse (CHF CHF)
CLF=CLF
CLP=Chili ($ CLP)
CNY=Chine (CNY CNY)
COP=COP
CPNBrazil=Bresil
CPNFrance=France
CPNPoland=Pologne
CPNUS=Les États-unis d'Amérique
CRC=CRC
CZK=République Tchèque (CZK CZK)
Cagliari=Cagliari
Calf=Veau
CalfHeiferColostrum=Audit veaux &amp; génisses - Colostrum
CalfHeiferGrowerPuberty=Audit veaux &amp; génisses - Croissance, Puberté, Gestation, Prépa-vêlage
CalfHeiferKeyBenchmarks=Audit veaux &amp; génisses - Repères clefs
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=La couleur de cette section est définie suivant\:  &amp;lt;75%\: Rouge, &amp;gt;\=75 and &amp;lt;90\: Orange, &amp;gt;\=90\: Vert
CalfHeiferPostweaned=Audit veaux &amp; génisses - Post-sevrage
CalfHeiferPreweaned=Audit veaux &amp; génisses - Pré-sevrage
CalfHeiferQuestionViewModel.Close=Prépa-vêlage
CalfHeiferQuestionViewModel.Colostrum=Colostrum
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Prépa-vêlage
CalfHeiferQuestionViewModel.KeyBenchmarks=Repères clefs
CalfHeiferQuestionViewModel.Postweaned=Post-sevrage
CalfHeiferQuestionViewModel.Preweaned=Pré-sevrage
CalfHeiferQuestionViewModel.Resources=Ressources
CalfHeiferQuestionViewModel.VisitNotebook=Prise de note
CalfHeiferResources=Audit veaux &amp; génisses - Ressources
CalfHeiferResults=Audit veaux &amp; génisses - Resultats
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Veaux &amp; génisses score
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Prépa-vêlage
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Inclu dans la fiche d'évaluation globale 
CalfHeiferScoreCardScoreViewModel.PhaseOne=Colostrum
CalfHeiferScoreCardScoreViewModel.PhaseThree=Post-sevrage
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Pre-sevrage
CalfHeiferScorecardImprovementViewModel.Colostrum=Colostrum
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Prépa-vêlage
CalfHeiferScorecardImprovementViewModel.Postweaned=post-sevrage
CalfHeiferScorecardImprovementViewModel.Preweaned=pré-sevrage
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Cliquez sur l'une des catégories ci-dessous pour voir les résultats par phase
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=La couleur de cette section est définie suivant\: équivaut à 100%\: Vert, &amp;lt;100%\: Rouge
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Enregistrer
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Repères clefs
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Phase 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Phase 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Phase 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Phase 7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Phase 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Phase 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Maintenir un enregistrement  des données de croissance et santé actualisé
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Gestation à 15 - 23 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Puberté à 9 - 15 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Colostrum à 1 - 3 jours
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Prépa vêlage / Production à 23 - 26 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Croissance à 3-9 mois
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Pre/Post Sevrage à 0 - 3 mois
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Prise de note
CalfHeiferScorecardLanding=Audit veaux &amp; génisses
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Repères clefs
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Améliorations
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Réponses
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Evaluation
CalfHeiferScorecardResultsViewModel.VisitNotebook=Prise de note
CalfHeiferScorecardViewModel.Colostrum=Colostrum
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Prépa-vêlage
CalfHeiferScorecardViewModel.KeyBenchmarks=Repères clefs
CalfHeiferScorecardViewModel.Postweaned=Post-sevrage
CalfHeiferScorecardViewModel.Preweaned=Pré-sevrage
CalfHeiferScorecardViewModel.Resources=Ressources
CalfHeiferScorecardViewModel.Title=Audit
CalfHeiferScorecardViewModel.VisitNotebook=Prise de note
CalfHeiferTools=Outils veaux &amp; génisses
CalfHeiferToolsViewModel.CalfHeiferScorecard=Audit
CalfHeiferToolsViewModel.CalfHeiferTools=Outils veaux &amp; génisses
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Outils  
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Merci de sélectionner un outil de la liste ci-dessous pour commercer la visite
CalfHeiferToolsViewModel.CalfHeiferToolsList=Outils
CalfHeiferToolsViewModel.Title=Outils veaux &amp; génisses
CalfHeiferToolsViewModel.VisitNotebook=Prise de note
California=Californie
Caltanissetta=Caltanissetta
Cambodia=Cambodge
Cameroon=Cameroun
Campeche=Campche
Campobasso=Campobasso
Canada=Canada
Capacity=Capacité
Cape_Verde=Cape verte
Carbonia-Iglesias=Carbonia-iglesias
Cargill=Cargill
CargillForageLabKPTest=Tamis Cargill évaluation éclatement grain maïs
Carlow=Carlow
Caserta=Caserta
Catania=Catane
Catanzaro=Catanzaro
Category1=Note d'évaluation des bouses - 1
Category2=Note d'évaluation des bouses - 2
Category3=Note d'évaluation des bouses - 3
Category4=Note d'évaluation des bouses - 4
Category5=Note d'évaluation des bouses - 5
Cavan=Cavan
Cayman_Islands=Ãles CaÃ¯mans
CearÃ¡=CarrÃ©
Central_African_Republic=RÃ©publique centrafricaine
Chad=Tchad
Chandigarh=Chandigarh
ChewsPerCud=Nombre de mastication par bol alimentaire
ChewsPerCudMasterViewModel.AddNew=Ajouter nouveau
ChewsPerCudMasterViewModel.CudChewingInputs=Données
ChewsPerCudMasterViewModel.CudChewingResults=Résultats
ChewsPerCudMasterViewModel.NumOfChews=\# de coups de machoires
ChewsPerCudMasterViewModel.Title={0} / \# Mastication
Chhattisgarh=Chhattisgarh
Chiapas=Chiapas
Chieti=Chieti
Chihuahua=Chihuahua
Chile=Chili
China=Chine
Chinese_Taipei=Taipei chinois
Chongqing=Chongqing
ChooseAppPDF=Choisissez une application pour voir le PDF
ChoosingtheCorrectAdditive=Choix du bon conservateur
Christmas_Island=L'Ã®le de noÃ«l
Clare=Clare
ClassSubClass=Type d'animal / Sous Classe
Clean=Clean
ClinicalMastitisLosses=Pertes dues à des mammites cliniques
CloseUp=Vaches en préparation vêlage
CloseUpDry=Prével.
CloseUpHeifer=Génisse en préparation vêlage
Coahuila=Coahuila
Cocos_(Keeling)_Islands=Ãles Cocos (Keeling)
Colima=Colima
Colombia=Colombie
Colorado=Colorado
Colostrum=Colostrum
Colostrum_AmountOfColostrumOrFed=Quantité de colostrum ou d'aliment colostral
Colostrum_BrixPercentOfColostrumFed=% Brix du colostrum consommé
Colostrum_CleanAndDryCalvingArea=La zone de vêlage est propre et sèche?
Colostrum_CleanAndDryCalvingArea_ToolTip=Utiliser le test du genou pour savoir si la zone est propre et sèche
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Nettoyer et désinfecter le matériel d'alimentation des veaux entre chaque repas
Colostrum_CleanCalfCartToTransportCalf=Chariot à veau propre pour transporter le veau
Colostrum_HoursTillCalfIsRemovedFromMother=Heure(s) avant que le veau soit séparé de la mère 
Colostrum_HoursTillCalfReceivesColostrum=Heure(s) avant que le veau reçoive le colostrum 
Colostrum_NumberOfCowsInCalvingArea=Nombre de vaches dans la zone de vêlage
Colostrum_PasteurizeColostrumBeforeFeeding=Le colostrum pasteurisé est consommé
Colostrum_PercentageOfNavelsDippedInSevenPercent=% de veaux ayant eu une désinfection du nombril 1 heure après vêlage?
Colostrum_RefrigeratedColostrumStoredLess=Le colostrum réfrigéré est-il stocké moins de 24h?
ComfortToolsViewModel.ComfortHeading=Sélectionnez un outil dans la liste ci-dessous pour débuter votre visite.
ComfortToolsViewModel.ComfortToolsList=Outils
ComfortToolsViewModel.ComfortToolsTitle=Outils de confort
ComfortToolsViewModel.HealthHeading=Sélectionnez un outil dans la liste ci-dessous pour débuter votre visite.
ComfortToolsViewModel.HeatstressEvaluationTitle=Evaluation du stress thermique
ComfortToolsViewModel.PenTimeTitle=Evaluation du temps de repos
ComfortToolsViewModel.Title=Outils de confort
ComfortToolsViewModel.VisitNotebook=Cahier de notes
Comments=Commentaires
CommonSpinnerViewModel.BodyConditionPDF=Guide sur la NEC
CommonSpinnerViewModel.InadequateStimulation=Stimulation inadéquate
CommonSpinnerViewModel.LocomotionPDF=Guide de notation de la locomotion
CommonSpinnerViewModel.ManureScorePDF=Guide de notation fumier
CommonSpinnerViewModel.NoStimulation=Pas de préparation à la traite (mise en place des manchons uniquement)
CommonSpinnerViewModel.OptimalStimulation=Stimulation de la mamelle adéquate (10-20 secondes de nettoyage; mise en place des manchons de traite après 60-90 secondes)
Como=Comme
Comoros=Comores
Competitor=Concurrente
CompletedTimeKey=Temps ecoulé
Component=Composantes
Compostbarn=Compostbarn
ConcentrateDistribution=Taux de distribution
ConfirmExport=En appuyant sur OK, l'application va charger toutes les pages sélectionnées et va prendre une photo avant de revenir à cet écran.
ConfirmScalePointSwitch=La modification de l’échelle de points réinitialisera toutes les données entrées
ConfirmScorerSwitch=La modification de la méthode d'évaluation va réinitialiser toutes les données entrées.
Congo=Congo
Congo,_the_Democratic_Republic_of_the=Congo, la RÃ©publique dÃ©mocratique du
Connecticut=Connecticut
Consumer=Consommateurs
ConsumerDetailsViewModel.DeleteProspect=Effacer un consommateur
ConsumerDetailsViewModel.DeleteProspectPrompt=Êtes-vous certain de vouloir effacer ce consommateur? Les informations sur le consommateur, le site et les visites seront perdues
ConsumerDetailsViewModel.MainHeading=SITES
ConsumerDetailsViewModel.NewSite=Ajouter un nouveau site
ConsumerDetailsViewModel.ProspectTitle=Détails du consommateur
ConsumersViewModel.NewConsumer=Ajouter un nouveau consommateur
Continue=Continuer
Cook_Islands=les Ãles Cook
Cork=LiÃ¨ge
Corn=ensilage de maïs
CornSilage=Ensilage de maïs
CornSilageKernel=Ensilage de maïs humide
CornSilageResources=Quantité d'ensilage de maïs restante
CornSilageStopGo=Aide mémoire d'un bon ensilage de maïs
Cosenza=Cosenza
Costa_Rica=Costa Rica
Costs=Coûts
Cote_d'Ivoire=Cote d'Ivoire
Count=Compter
Cow=Vache 
CowEfficiency=EfficacitÃ© des vaches
CowPerRobot=Vaches par robot
CowsOutsideTargetRangeToolTip=le but est d'avoir &lt; 20% des vaches qui n'atteignent pas l'objectif
CowsPerDayNeeded=Nombre de vaches nécessaires pour assurer l'avancement de 15-20 cm du silo
CowsSectionToolTip=Un groupe de 8 ou plus vaches devrait être testé pour pouvoir conclure. Dans les petits troupeaux, tester toutes les fraiches vélées.
CowsToBeFed=Nombre de vaches alimentées
CreateDuplicateDiet=Une ration existe déjà pour ce type d'animal. En créer une autre?
CreateDuplicateNameDiet=Une ration sous le même nom existe déjà. Veuillez entrer un nouveau nom
Created=Créé
Cremona=Cremona
Croatia=Croatie
CropCharacteristicsDecisionGuide=Optimum entre stade de récolte, type de conservateur et type de stockage
Crotone=Crotone
Cuba=Cuba
CudChewingAverageNumber=Nombre moyen
CudChewingDataEntryViewModel.CudChewing=Rumination
CudChewingDataEntryViewModel.HerdCudChewingDescription=Pour cette visite, comptez le nombre d'animaux qui ruminent dans ce lot en utilisant le compteur ci-dessous. Vous devez compter au moins 10 vaches.
CudChewingDataEntryViewModel.No=Non
CudChewingDataEntryViewModel.Yes=Oui
CudChewingHerdEditScoreViewModel.AverageChewsItem=Nombre moyen de mastication par bol alimentaire
CudChewingHerdEditScoreViewModel.Close=Fermer
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Jour de lactation
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Modifier le score pour la rumination
CudChewingHerdEditScoreViewModel.EditScoreTitle=Modifier le score de comptage de coup de machoire
CudChewingHerdEditScoreViewModel.PercentChewingItem=Pourcentage de vache qui ruminent
CudChewingMasterViewModel.CudChewing=Rumination
CudChewingMasterViewModel.CudChewingInputs=Données
CudChewingMasterViewModel.CudChewingResults=Résultats
CudChewingPen=Lot
CudChewingPercentChewing=% d'animaux qui ruminent
CudChewingPercentGoal={0}% des objectifs
CudChewingPercentOfPen=Rumination (% du lot)
CudChewingViewModel.CudChewing=Lots
CudChewingViewModel.CudChewingList=Lots (lactation et taries)
CudChewingViewModel.CudChewingTitle=Nom du lot
CudChewingViewModel.Title=Lots
CudChewsCalculatorViewModel.CalculatorHeading=Sélectionner une vache pour compter le nombre de mastications par bol alimentaire. 'Ajouter une nouvelle vache' ci-dessus.
CudChewsCalculatorViewModel.CudChewCategorySection=Vaches
CudChewsCalculatorViewModel.NumOfChews=\# Mastication
CudChewsDatesForComparisonViewModel.CudChewsPercent=Rumination, en %
Cundinamarca=Cundinamarca
Cuneo=Coin
CuraÃ§ao=CuraÃ§ao
Current=Actuel
CurrentDownResponse=Baisse en lait engendrée ({0}/cow/day)
CurrentMilkPrice=Prix du lait actuel ({0}/{1})
CurrentSCC=Comptage cellulaire (cellules / {0})
CurrentVisitSummary=Résumé de la visite
Customer=Client
CustomerDetailViewModel.CustomerTitle=Profil du client
CustomerDetailViewModel.MainHeading=SITES
CustomerDetailViewModel.NewSite=Ajouter un nouveau site
CustomerDetailViewModel.NewVisit=Nouvelle Visite
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=End User
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Sélectionnez un segment
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Title=Détails
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=Nom du Client - Nom du Site
Cyprus=Chypre
CzechRepublic=République Tchèque
Czech_Republic=RÃ©publique tchÃ¨que
DDW=Données de la ferme
DDWOfflineMessage=Comme il n'y a pas de réseau, aimeriez-vous voir le rapport hors ligne?
DDWUpdatedTime=Dernière mise à jour des données du troupeau \: {0}
DKK=DKK
DZD=Algérie (DA DZD)
Dadra_and_Nagar_Haveli=Dadra et Nagar Haveli
Daily=Journalier
DairyEnteligenFarmReportsources=Source d’information - Rapport Dairy Enteligen
Daman_and_Diu=Daman et Diu
DashboardViewModel.Alert=Alerte\!
DashboardViewModel.AlertMessage=Les données n'ont pas été synchronisées sur plus de {0} jours.
DashboardViewModel.GoodAfternoon=Bonne après-midi,
DashboardViewModel.GoodMorning=Bonjour,
DashboardViewModel.MessageBody=Pour rappel\: Utiliser les notes libres à chaque visite du site pour enregistrer des observations particulières.
DashboardViewModel.MessageHeader=Message
DashboardViewModel.RecentSiteVisit=Visites récentes du site
DashboardViewModel.UserPreferences=Préférences de l'utilisateur
Date=Date
DateGone=Date de fin
DatesForComparison=Dates de comparaison
Days=Jours
DaysInMilkItem=Jours de lactation
DeLaval=DeLaval
DeathLoss=Perte pour mortalite
DecidingSilageStorage=Choix du type de stockage
Delaware=Delaware
Delete=Supprimer
DeleteMatrixValue=Êtes-vous sûr de vouloir supprimer cette valeur ?
Delhi=Delhi
Denmark=Danemark
DensityLossesinPressedBagSilos=Densité et pertes en boudins d'ensilages
Diet=Ration
DietDCAD=BACA ration mEq/100g
DietDetailViewModel.Created=Créé
DietDetailViewModel.DDW=Données de la ferme
DietDetailViewModel.Max=Lynx
DietDetailViewModel.SystemGenerated=Système généré
DietDetailViewModel.Title=Détail de la ration
DietDetailViewModel.UserCreated=Par utilisateur
DietListViewModel.InfoNewDiet=Les noms des rations seront mis à jour automatiquement si Lynx est connecté au site de la ferme dans Dairy Enteligen. Sinon, ajoutez manuellement les rations ou laissez cette liste vide et sélectionnez la classe / sous-classe d'animaux appropriée pour chaque enclos. 
DietListViewModel.MainHeading=Ration
DietListViewModel.New=Nouveau
DietListViewModel.NewDiet=Ajouter une ration
DietListViewModel.Title=Ration
Dirty=Dirty
DisplacedAbomasum=Caillette
District_of_Columbia=District de Colombie
Distrito_Federal=District fÃ©dÃ©ral
Djibouti=Djibouti
DoNotTest=&lt;20% ou ne pas tester
Dominica=Dominique
Dominican_Republic=RÃ©publique dominicaine
Donegal=Donegal
DontKnow=Ne sais pas
DownResponse=Baisse en lait engendrée ({0} / vache / jour)
Dry=Vache tarie
DryCow=Vache tarie
DryLot=Lot des vaches taries
Dryhay=Foin sec
Dublin=Dublin
DuplicatePenName=Un groupe existe déjà avec ce nom. Choisissez un autre nom.
Durango=Durango
Dystocia=Dystocie
EGP=EGP
EarlyLactation=Début lact.
Ecuador=Equateur
Edit=Modifier
EditDatesForComparison=Modifier les dates de comparaison
EditDatesForComparisonViewModel.Chews=Mastication
EditDatesForComparisonViewModel.EditDatesClose=Fermer
EditDatesForComparisonViewModel.EditDatesLabel=Sélectionnez les dates pour la comparaison de ce groupe dans la liste ci-dessous.
EditDatesForComparisonViewModel.EditDatesTitle=Modifier les dates de comparaison
EditDatesForComparisonViewModel.EditDatesVisits=Visites
EditDatesForComparisonViewModel.LocomotionScoreAverage=Score moyen de locomotion\:
EditDatesForComparisonViewModel.ManureScoreAverage=Score de bouse moyen
EditDatesForComparisonViewModel.MetabolicIncidence=Veuillez sélectionner jusqu'à 5 dates de visite dans la liste ci-dessous à fin de comparaison
EditDatesForComparisonViewModel.PenTimeBudget=Veuillez sélectionner jusqu'à 7 dates de visite dans la liste ci-dessous à fin de comparaison
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Veuillez sélectionner une date ci-dessous pour comparer avec la visite en cours.
EditDatesForComparisonViewModel.TimeRemainingForResting=Temps restant pour le repos\:
EditDatesForComparisonViewModel.Title=Modifier les dates de comparaison
EditDatesForComparisonViewModel.Visits=Visites
EditGoalsCudChewingViewModel.Close=Fermer
EditGoalsCudChewingViewModel.CloseUpDry=Vaches en préparation vêlage
EditGoalsCudChewingViewModel.CudChews=Nombre moyen de mastication par cycle de rumination
EditGoalsCudChewingViewModel.EarlyLactation=Début de lactation
EditGoalsCudChewingViewModel.EditGoalsTitle=Modifier les objectifs pour la rumination
EditGoalsCudChewingViewModel.FarOffDry=Vaches taries
EditGoalsCudChewingViewModel.Fresh=Fraîches vêlées
EditGoalsCudChewingViewModel.LateLactation=Fin de lactation
EditGoalsCudChewingViewModel.MidLactation=Milieu de lactation
EditGoalsCudChewingViewModel.PeakMilk=Pic de lactation
EditGoalsCudChewingViewModel.PercentChewing=Pourcentage en rumination
EditNoteViewModel.Action=Action
EditNoteViewModel.Cancel=Annuler
EditNoteViewModel.Category=Catégorie
EditNoteViewModel.Close=Fermer
EditNoteViewModel.CreatedByMetadata=Crée le {0} @ {1} par {2}
EditNoteViewModel.Delete=Effacer
EditNoteViewModel.DeleteImageButtonText=Supprimer l'image
EditNoteViewModel.DeleteImagePrompt=Voulez-vous supprimer cette image?
EditNoteViewModel.DeletePrompt=Voulez-vous supprimer cette note?
EditNoteViewModel.DeleteVideoButtonText=Supprimer la vidéo
EditNoteViewModel.DeleteVideoPrompt=Voulez-vous supprimer cette vidéo?
EditNoteViewModel.Event=Événement
EditNoteViewModel.LastUpdatedByMetadata=Dernier Modifié sur {0} @ {1} par {2}
EditNoteViewModel.NoteCamcorderNotImplemented=Fonctionnalité de la caméra n'est pas encore configurée.
EditNoteViewModel.NoteGalleryNotImplemented=La fonctionnalité de la galerie n'est pas encore configurée.
EditNoteViewModel.NoteLabel=Annotations
EditNoteViewModel.NoteOnlyOneImage=Une seule image est autorisée par note. Supprimer l'image actuelle en premier.
EditNoteViewModel.NoteOnlyOneVideo=Une seule vidéo est autorisée par note. Supprimer la vidéo actuelle en premier.
EditNoteViewModel.Observation=Observation
EditNoteViewModel.Save=Sauvegarder
EditNoteViewModel.Task=Tâche
EditNoteViewModel.Title=Note
EditNoteViewModel.TitleLabel=Titre
Egypt=Egypte
El_Salvador=Le sauveur
EmailReportViewModel.AnimalImpact=Impact Animal
EmailReportViewModel.CalfHeiferItem=Veaux &amp; génisses
EmailReportViewModel.CalfHeiferScorecard=Audit
EmailReportViewModel.Capacity=Capacité
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Liste des catégories
EmailReportViewModel.Charts=Graphiques
EmailReportViewModel.CoefficientVariation=Coefficient de variation (CV) (%)
EmailReportViewModel.ComfortHeatStressBanner=Outil pour évaluer le stress termique
EmailReportViewModel.ComfortItem=Outil de confort
EmailReportViewModel.ComfortPenTimeBanner=Outil pour evaluer le temps de repos
EmailReportViewModel.ComfortToolsTitle=Outils de confort
EmailReportViewModel.CowsOutsideTargetRange=Vaches en dehors des normes
EmailReportViewModel.CudChewingTitle=Mastication
EmailReportViewModel.DietDCADStr=BACA ration
EmailReportViewModel.EmailBody={0} - {1} Rapport
EmailReportViewModel.EmailSelectedTools=Envoyer par email
EmailReportViewModel.EmailSubject={0}Rapport
EmailReportViewModel.ExportSelected=Envoyer les outils sélectionnés par email
EmailReportViewModel.FeedOut=Reprise
EmailReportViewModel.ForageAuditScorecard=Tableau de bord pour le diagnostic des fourrages
EmailReportViewModel.ForageImprovements=Fiche d'évaluation
EmailReportViewModel.ForageLanding=Page d'accueil pour le diagnostic des fourrages
EmailReportViewModel.ForageScorecard=Tableau de bord pour le diagnostic des fourrages
EmailReportViewModel.GeneratingReport=Création du rapport…
EmailReportViewModel.GotoMarketBranding=Marque orientée marché
EmailReportViewModel.HealthItem=Outil de santé
EmailReportViewModel.HeatstressEvaluationTitle=Évaluation de stress thermique
EmailReportViewModel.Herd=Troupeau
EmailReportViewModel.HerdAnalysis=Analyse du troupeau
EmailReportViewModel.HerdGoals=Analyse de troupeau - Objectifs
EmailReportViewModel.HerdInputs=Analyse de troupeau - Données
EmailReportViewModel.HerdResults=Analyse de troupeau - Résultats
EmailReportViewModel.HerdRevenue=Analyse de troupeau - Revenu
EmailReportViewModel.Improvements=Améliorations
EmailReportViewModel.Inputs=Données
EmailReportViewModel.InputsOutputsChart=Données / Résultats / Graphiques
EmailReportViewModel.LocomotionScoreTitle=Score de locomotion
EmailReportViewModel.ManureScoreTitle=Score de fumier
EmailReportViewModel.MarketBranding=Marque orientée marché
EmailReportViewModel.MetabolicIncidenceTitle=Maladies métaboliques
EmailReportViewModel.MilkProcessCalcInputsTab=Revenu pour la production laitière\: Données
EmailReportViewModel.MilkProcessCalcResourcesTab=Revenu pour la production laitière\: Ressources
EmailReportViewModel.MilkProcessCalcResultsTab=Revenu pour la production laitière\: Résultats
EmailReportViewModel.MilkProcessRevenue=Calculateur de revenu de la production laitière
EmailReportViewModel.MilkProcessRevenueCalculator=Calculateur de Revenu pour la production laitière
EmailReportViewModel.MilkingTime=Durée de traite
EmailReportViewModel.Notes=Annotations
EmailReportViewModel.NumOfChews=nombre de mastications
EmailReportViewModel.NutritionForage=Diagnostic des fourrages
EmailReportViewModel.NutritionItem=Nutrition
EmailReportViewModel.NutritionPile=Inventaires des fourrages
EmailReportViewModel.Outputs=Résultat
EmailReportViewModel.PenCompare=Comparaison d'analyse par groupe
EmailReportViewModel.PenDensity=Densité du groupe
EmailReportViewModel.PenInputs=Analyse de groupe - Données
EmailReportViewModel.PenResults=Analyse de groupe - Résultats
EmailReportViewModel.PenTimeTitle=Temps utilisé par groupe
EmailReportViewModel.PileAndBunkerTitle=Inventaires des fourrages
EmailReportViewModel.ProductivityItem=Outil de productivité
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Ressources
EmailReportViewModel.Results=Résultats
EmailReportViewModel.RumenHealthBodyConditionTitle=Note d'état corporel
EmailReportViewModel.RumenHealthLocomotionTitle=Score de Locomotion
EmailReportViewModel.RumenHealthManureTitle=Score de Santé du rumen par évaluation des bouses
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
EmailReportViewModel.RumenHealthTMRTitle=Santé du rumen -Taille des particules dans la ration mélangée
EmailReportViewModel.RumenHealthTitle=Santé du rumen - Mastication
EmailReportViewModel.RumenHealthUrinePHTitle=pH urinaire
EmailReportViewModel.ScoreScreen=Scores
EmailReportViewModel.TMRParticleScoreTitle=Taille des particules
EmailReportViewModel.TimeBudget=Heures nécessaires
EmailReportViewModel.Title=Rapport par mail
EmailReportViewModel.UrinePhSTDDEV=Ecart type
EmailReportViewModel.UserPreferences=Configuration de l'utilisateur
EmailReportViewModel.UserSettings=Configuration de l'utilisateur
EmailReportViewModel.WalkthroughReportTitle=Visite pas-à-pas
EnergyImperial=Mcal/lb
EnergyMetric=Mcal/kg
Enna=Enna
Equatorial_Guinea=GuinÃ©e Ãquatoriale
Eritrea=ÃrythrÃ©e
ErrorDescription=Une erreur est survenue lors de la lecture ou de l'écriture de vos informations
ErrorTitle=Erreur
EspÃ­rito_Santo=Saint
Estonia=Estonie
Ethiopia=Ethiopie
Eula=Contrat de licence utilisateur final
Euro=Pays membres de l'UE (€ EUR)
Event=Événement
EveryOtherDay=Tous les autres jours
Excessive=&gt;0.5pt NEC 
ExtraDaysOpenCostInfoMessage=Il varie généralement entre 3 et 5 euros par jour supplémentaire
FAQDairyEnteligenFarmReportandDDW=FAQ (questions fréquentes) Rapport Dairy Enteligen et Herdnet
Falkland_Islands_(Malvinas)=Ãles Falkland (Malvinas)
FarOff=Taries
FarOffDry=Taries
Faroe_Islands=Ãles FÃ©roÃ©
Federal_District=District fÃ©dÃ©ral
FeedFirst=Feed first
FeedOut=Reprise
FeedOutRateInfo=Avancement du silo
FeedOutRatesFilmsStorageSysExamined=Diagnostic du front d'attaque, des baches et du mode de stockage
FeedOutSurfaceAreaImperial=Surface du front d'attaque (ft²)
FeedOutSurfaceAreaMetric=Surface du front d'attaque (m²)
FeedingRate=Consommation (MB/vache/jour)
FeedoutLossesForageStorageSys=Perte de fourrage au stockage
FermentationAnalysisSilageQT=Analyse de la conservation et test qualité fourrage
Fermo=ArrÃªtÃ©
Ferrara=Ferrare
FieldKPTest=Evaluation de l'éclatement du grain au champs
Fiji=Fidji
FillAllFields=SVP remplir tous les champs.
FillAllMandatoryFields=Veuillez remplir tous les champs obligatoires.
FinalObservations=Observations finales
Finish=Terminer
Finland=Finlande
Florence=Florence
Florida=Floride
Foggia=Foggia
ForageAuditScorecard=Fiches repères  pour la vérification des fourrages
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Point d'amélioration sur les fourrages
ForageAuditScorecardResponsesViewModel.ResponsesTab=Réponses à l'audit fourrage
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Améliorations
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Réponses
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Score
ForageAuditScorecardResultsViewModel.ImprovementsTab=Améliorations
ForageAuditScorecardResultsViewModel.ResponsesTab=Réponses
ForageAuditScorecardResultsViewModel.ScoreTab=Score
ForageAuditScorecardResultsViewModel.Title=Silo-tour
ForageAuditScorecardResultsViewModel.VisitNotebook=Voir le Cahier de notes
ForageAuditScorecardScoreViewModel.GoodIndicator=Bon
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Améliorations
ForageAuditScorecardScoreViewModel.OverallForageScore=inclure dans le score fourrager global
ForageAuditScorecardScoreViewModel.Title=Score fourrager global
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Types d'ensilage
ForageAuditViewModel.ForageAuditScorecard=Fiches repères  pour la vérification des fourrages
ForageAuditViewModel.ForageDetail=La qualité du fourrage est la base de tout programme nutritionnel et la clef de la rentabilité globale de l’élevage. La grille d’audit des fourrages peut être utilisée pour évaluer la gestion des fourrages et proposer des points d’améliorations. La grille d’audit est organisée par points clefs. Vous pouvez couvrir tous les points pendant une visite ou ne sélectionner que ceux qui sont importants ce jour-là. Des supports  d'explication supplémentaires sont disponibles pour aider à améliorer les points où des leviers d'amélioration existent.
ForageAuditViewModel.ForageHeading=Information de base sur le fourrage
ForageAuditViewModel.Resources=Fiches repères
ForageAuditViewModel.Title=Vérification des fourrages
ForageAuditViewModel.VisitNotebook=Voir le Carnet de notes
ForageAudit_Sample_ToolTip=Exemple de texte. Effacer quand le nouveau est disponible.
ForageManagement_ForagesHarvestedAtProperMaturity=Les fourrages sont-ils récoltés au bon stade de maturité?
ForageManagement_ForagesHarvestedAtProperMoisture=Les fourrages sont-ils récoltés au bon taux de matière sèche?
ForageScorecardResultsViewModel.Title=Botte d'enrubannage
ForageScorecardViewModel.Baleage=Enrubannage
ForageScorecardViewModel.BunkersAndPiles=Silos couloir et taupinières
ForageScorecardViewModel.ForageAuditCategories=Catégories
ForageScorecardViewModel.ForageAuditScore=Score
ForageScorecardViewModel.ForageAuditScorecard=Grille d'audit des fourrages
ForageScorecardViewModel.ForageCategoryTooltip=La qualité des fourrages constitue la base de tous programmes alimentaires des ruminants et est un élément clé de la rentabilité globale de l'exploitation. Cet outil permet d'évaluer les pratiques actuelles de gestion des fourrages et  cibler les points à améliorer.
ForageScorecardViewModel.Harvest=Récolte
ForageScorecardViewModel.MaintainingForageQuality=Maintenir la Qualité des Fourrages
ForageScorecardViewModel.No=Non
ForageScorecardViewModel.SilageBags=Boudins d'ensilage
ForageScorecardViewModel.SurveyCategories=Grille d'évaluation des fourrages
ForageScorecardViewModel.SurveyOfForages=Enquête sur les fourrages
ForageScorecardViewModel.Title=Grille d'évaluation des fourrages
ForageScorecardViewModel.TowerSilos=Silo-tour
ForageScorecardViewModel.ViewOverallForageScore=Voir le résultat de l'audit fourrage
ForageScorecardViewModel.VisitNotebook=Voir le carnet de notes
ForageScorecardViewModel.Yes=Oui
ForlÃÂ¬-Cesena=ForlÃ£Â¬-cesena
FourScreenNew=4 tamis - nouveau
FourScreenNewType=(4 mm)
FourScreenOld=4 tamis - ancien
FourScreenOldType=(1.18 mm)
FourToSevenDays=4 à 7 jours
France=France
FreeFlow=Free flow
FreeFormReportViewModel.CalfHeiferItem=Veaux &amp; génisses
FreeFormReportViewModel.CalfHeiferScorecard=Audit
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Graphiques
FreeFormReportViewModel.ComfortHeatStressBanner=Evaluation stress thermique par groupe
FreeFormReportViewModel.ComfortItem=Outil de confort
FreeFormReportViewModel.ExportSelected=Exporter les outils sélectionnés
FreeFormReportViewModel.GeneralNotes=Annotations générales de la visite
FreeFormReportViewModel.HealthItem=Outil de santé
FreeFormReportViewModel.Inputs=Données
FreeFormReportViewModel.KeyBenchmarks=Repères clefs
FreeFormReportViewModel.MarketingBranding=Marque orientée marché
FreeFormReportViewModel.MilkProcessCalcInputsTab=Procédure de traite - Données
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Procédure de traite - Ressources
FreeFormReportViewModel.MilkProcessCalcResultsTab=Procédure de traite - Résultats
FreeFormReportViewModel.MilkProcessRevenueCalculator=Procédure de traite
FreeFormReportViewModel.MilkSoldEvaluation=Evaluation du lait vendu
FreeFormReportViewModel.Notes=Annotations
FreeFormReportViewModel.NutritionForage=Vérification des fourrages
FreeFormReportViewModel.NutritionItem=Outil de nutrition
FreeFormReportViewModel.NutritionPile=Inventaires des fourrages
FreeFormReportViewModel.Outputs=Résultats
FreeFormReportViewModel.OverallImprovements=Amélioration globale
FreeFormReportViewModel.OverallResponses=Réponse globale
FreeFormReportViewModel.OverallScore=Score global
FreeFormReportViewModel.PenTimeTitle=Temp de repos par groupe
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Taux de reprise des fourrages
FreeFormReportViewModel.ProductivityItem=Outil de productivité
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Résultats
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Score  pour l'état corporel
FreeFormReportViewModel.RumenHealthLocomotionTitle=Score de Locomotion
FreeFormReportViewModel.RumenHealthManureTitle=Santé du rumen - Score de bouses
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Santé du rumen - Grosseur de particules de la ration mélangée pour le troupeau
FreeFormReportViewModel.RumenHealthTMRTitle=Santé du rumen - Grosseur de particules de la ration mélangée
FreeFormReportViewModel.RumenHealthTitle=Santé du rumen - mastication
FreeFormReportViewModel.RumenHealthUrinePHTitle=pH urinaire
FreeFormReportViewModel.Title=Rapport version imprimable
FreeFormReportViewModel.VisitTitle=Nom de la visite
FreeFormReportViewModel.WalkThroughNotes=Annotations générales de la visite
FreeHandNoteClearPaletteDialogMessage=Voulez-vous tout supprimer ?
FreeHandNoteEditorPageTitle=Note Main Libre
FreeHandNoteSaveUserDialogMessage=Voulez-vous enregistrer cette note?
FreeHandNotesViewModel.Save=Enregistrer
Freestall=Freestall
French_Guiana=Guyane FranÃ§aise
French_Polynesia=PolynÃ©sie franÃ§aise
French_Southern_Territories=Territoires du Sud franÃ§ais
Fresh=Fraîches
FreshCow=Vache fraiche
FreshHeifer=Génisse fraîche vêlée
Frosinone=Frosinone
Fujian=Fujian
GBP=Royaume-Uni (GBP GBP)
GEA=GEA
GTQ=Guatemala (Q GTQ)
Gabon=Gabon
Galway=Galway
Gambia=Gambie
Gansu=Gansu
Gardez=l'application ouverte pendant que la synchronisation est en cours. La synchronisation peut prendre plus de temps avec des connexions plus lentes. "
General=Général
Genoa=GÃªnes
Georgia=GÃ©orgie
Germany=Allemagne
GettingtheMostOutofYourForage=Obtenez le meilleur de votre fourrage
Ghana=Ghana
Gibraltar=Gibraltar
Girolando=Girolando
Global=Mondial
Goa=Goa
Goal=Objectifs
GoiÃ¡s=GoiÃ£o
Good=Good
Gorizia=Gorizia
GreaterThan8Hours=Supérieur à 8 heures
GreaterThanFive=Supérieur à 5
GreaterThanSevenDays=Supérieur à 7 jours
GreaterThanSixHours=Supérieur à  6 heures
GreaterThanThirtySixInchesPerDay=plus de 90 cm par jour
GreaterThanTwelveHours=Supérieur à 12 heures
GreaterThanTwenty=&gt;20
Greece=GrÃ¨ce
Greenland=Groenland
Grenada=Grenade
Grosseto=Grosseto
GrowerPubertyPregnancyCloseup=Croissance, Puberté, Gestation, Prépa-vêlage
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Case propre et sèche
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Utiliser le test du genou pour savoir si la zone est propre et sèche
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=NEC souhaitée atteinte pour le stade de maturité
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Signe de fécès mou
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=L'auge est nettoyée tous les jours et les refus retirés
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Accès à l'abreuvoir et qualité de l'eau satisfaisants
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=Pas de preuve de contamination de l'eau
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Groupes avec des génisses de taille homogènes
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Les groupes sont composés d'animaux de même taille
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% de surdensité
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Ration adaptée aux objectifs de croissance, revue régulièrement
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=La longueur d'auge est adaptée pour chaque génisse
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg ont besoin de 30cm, 270-400kg ont besoin de 38cm, &gt;400 ont besoin de 46cm
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Nombre de m² adapté par génisse
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg ont besoin de 4m2, 270-400kg ont besoin de 5m2, &gt;400 ont besoin de 7m2
Guadeloupe=Guadeloupe
Guam=Guam
Guanajuato=Guanajuato
Guangdong=Guangdong
Guangxi=Guangxi
Guatemala=Guatemala
Guernsey=Guernesey
Guerrero=Guerrero
Guinea=GuinÃ©e
Guinea-Bissau=GuinÃ©e-Bissau
Guizhou=Guizhou
Gujarat=Gujarat
Guyana=Guyane
HKD=HKD
HNL=Honduras (HNL HNL)
HRK=HRK
HUF=Hongrie (Ft HUF)
Hainan=Hainan
Haiti=HaÃ¯ti
HalfPointScale=Echelle avec demi-point
Harvest=Récolte
Harvest_AdequateEquipmentAndLabor=Travail et équipement adéquat pour la récolte du foin
Harvest_CornSilageMoistureRangeConsistent=Le taux de matière sèche de l'ensilage de maïs est homogène sur tous les silos
Harvest_ForageHarvestingDocumented=Les conditions de récolte des fourrages, les champs et l’emplacement du stockage sont-ils enregistrés ?
Harvest_ForagesHarvestedAtProper=Fourrages récoltés à la bonne maturité et à la bonne matière sèche pour le type de culture et de stockage.
Harvest_KPScoreIsMonitored=Evaluation de l'éclatement du grain avec le tamis KP
Harvest_LengthOfCutMonitored=La longueur de coupes lors du chantier d'ensilage a-t-elle été contrôlée avec le tamis penn state
Harvest_UseSilageAdditive=Utilisation d'un conservateur d'ensilage?
Harvest_WholePlantMoistureDetermined=La matière sèche est-elle déterminée pour chaque parcelle ?
Haryana=Haryana
Hawaii=Hawaii
Haylage=Enrubannage
HealthToolsViewModel.HealthHeading=Sélectionner un outil dans la liste ci-dessous.
HealthToolsViewModel.HealthToolsList=OUTILS
HealthToolsViewModel.RumenHealthBodyConditionTitle=Note d'Etat corporel
HealthToolsViewModel.RumenHealthLocomotionTitle=Score de locomotion
HealthToolsViewModel.RumenHealthManureScreening=Tamis a fumier
HealthToolsViewModel.RumenHealthManureTitle=Santé Ruminale- score de bouse
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
HealthToolsViewModel.RumenHealthTMRTitle=Evaluation - Taille des particules dans la ration mélangée
HealthToolsViewModel.RumenHealthTitle=Santé Ruminale- rumination
HealthToolsViewModel.RumenHealthUrinePHTitle=Urine pH
HealthToolsViewModel.Title=Outils de santé
Heard_Island_and_McDonald_Islands=Entendus Ã®les et Ã®les McDonald
HeatstressCalculations=Calculs\: stress thermique
HeatstressChart=Graphique Stress thermique
HeatstressChartViewModel.DMIReduction=Baisse de la MSI
HeatstressChartViewModel.EnergyEquivMilkLoss=Perte de lait potentielle dûe à la perte énergétique
HeatstressChartViewModel.EstimateDryMatter=Estimation de la MSI
HeatstressChartViewModel.HeatstressEvalLabel=Température ajustée pour la temperature et l'humidité moyenne (non ensoleillé)
HeatstressChartViewModel.IntakeAdjustment=Ajustement de la consommation
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=Perte d'énergie
HeatstressChartViewModel.Mcal=Repas
HeatstressChartViewModel.MilkValueLossPerDay=Perte de lait/jour
HeatstressChartViewModel.MilkValueLossPerMonth=Perte Lait/Mois
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.ReductionDMI=Réduction de la MSI
HeatstressChartViewModel.TempHumidIndex=Index température humidité
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Voir le carnet de notes
HeatstressData=Données\: stress thermique
HeatstressDataEntryViewModel.AnimalInputs=Données des animaux
HeatstressDataEntryViewModel.CurrentMilkPrice=Prix du lait actuel ({0}/{1})
HeatstressDataEntryViewModel.DMI=Matière sèche ingérée en KG
HeatstressDataEntryViewModel.Exposure=Exposition
HeatstressDataEntryViewModel.HoursExposed=Durée d'ensoleillement
HeatstressDataEntryViewModel.Humidity=Humidité (%)
HeatstressDataEntryViewModel.LactatingAnimals=Vaches en lactation
HeatstressDataEntryViewModel.Milk=Lait({0})
HeatstressDataEntryViewModel.MilkFat=Matière Grasse %
HeatstressDataEntryViewModel.MilkProtein=Protéine %
HeatstressDataEntryViewModel.NEL=Densité énergétique de la ration (en UFL)
HeatstressDataEntryViewModel.Temperature=Température ({0})
HeatstressDataEntryViewModel.VisitNotebook=Voir le carnet de notes
HeatstressDataEntryViewModel.Weather=Météo
HeatstressGreen=Seuil de stress
HeatstressOrange=Stress modéré-sévère
HeatstressRed=Stress Sévère
HeatstressTableViewModel.HeatstressChartTab=Graphiques
HeatstressTableViewModel.HeatstressDataTab=Entrée de données
HeatstressTableViewModel.Title=Evaluation du stress thermique
HeatstressTableViewModel.VisitNotebook=Voir le carnet de notes
HeatstressYellow=Stress léger-modéré
Hebei=Hebei
Heifer=Génisse
Heilongjiang=Heilongjiang
Henan=Henan
HerdAnalysisGoalsViewModel.CloseUpDry=Vaches en préparation vêlage
HerdAnalysisGoalsViewModel.CudChewingGoals=Objectif de mastication
HerdAnalysisGoalsViewModel.CudChews=Coups de machoire
HerdAnalysisGoalsViewModel.DIM=Jours de lactation
HerdAnalysisGoalsViewModel.EarlyLactation=Début de lactation
HerdAnalysisGoalsViewModel.FarOffDry=Vaches taries
HerdAnalysisGoalsViewModel.Fresh=Fraîches vêlées
HerdAnalysisGoalsViewModel.LateLactation=Fin de lactation
HerdAnalysisGoalsViewModel.MidLactation=Milieu de lactation
HerdAnalysisGoalsViewModel.PeakMilk=Pic de lactation
HerdAnalysisGoalsViewModel.PercentChewing=% de vaches qui ruminent
HerdAnalysisGoalsViewModel.Title=Analyse du troupeau
HerdAnalysisGoalsViewModel.To=à
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Santé Ruminale - Rumination
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Analyse du troupeau
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Analyse du troupeau
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Objectifs
HerdAnalysisMasterViewModel.Title=Santé Ruminale - Rumination
HerdAnalysisTableTitle=Rumination, Analyse du score
HerdAnalysisViewModel.AverageChews=Nombre moyen de mastication par bol alimentaire
HerdAnalysisViewModel.DaysInMilk=Jours de lactation
HerdAnalysisViewModel.Edit=Modifier
HerdAnalysisViewModel.EditLabel=Modifier
HerdAnalysisViewModel.HerdAnalysisTableTitle=Rumination, Analyse du score
HerdAnalysisViewModel.HerdCudChewing=Pourcentage du troupeau qui rumine
HerdAnalysisViewModel.NoOfCows=Compléter l'analyse des groupes pour tous les groupes que vous avez commencé.
HerdAnalysisViewModel.NumberofChewsPerCud=Nombre de mastication par cycle de rumination
HerdAnalysisViewModel.PenNameLabel=Nom du groupe
HerdAnalysisViewModel.PercentChewing=Poucentage de vache qui ruminent
HerdAnalysisViewModel.TableTitle=Rumination, Analyse du score
HerdAverage=Moyenne pour le troupeau (%)
HerdGoal=Objectif pour le troupeau (%)
HerdInformation=Données du troupeau
HerdReporting=Rapport sur le troupeau\: rumination
Hidalgo=Hidalgo
Himachal_Pradesh=Himachal Pradesh
Hokkaido=Hokkaido
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=Saint-SiÃ¨ge (Ãtat de la ville du Vatican)
HomeViewModel.AutoSync=Synchronisation automatique
HomeViewModel.ConsumersTab=Consommateurs
HomeViewModel.CustomersTab=Clients
HomeViewModel.DashboardTab=Tableau de bord
HomeViewModel.Eula=Contrat de licence utilisateur final
HomeViewModel.Logout=Déconnexion
HomeViewModel.PrivacyStatement=Déclaration de confidentialité
HomeViewModel.ProspectsTab=Prospects
HomeViewModel.Settings=Paramètres
HomeViewModel.SyncWithDash=Synchroniser
HomeViewModel.SyncWithDate=Synchroniser- Dernière date de sync\: MM JJ AA
HomeViewModel.SyncWithTime=Synchroniser- Dernière date de sync\: {0\:hh\:mm tt}
HomeViewModel.Title=Outils de vente
Honduras=Honduras
Hong_Kong=Hong Kong
HowtoGetBetterKPResults=Comment améliorer l'éclatement du grain?
Hubei=Hubei
Hunan=Soi
Hungary=Hongrie
IDR=Indonésie (Rp IDR)
INR=Inde (INR INR)
Iceland=Islande
Idaho=Idaho
Illinois=Illinois
Imperia=ImpÃ©ria
Imperial=Impérial
Improvements=Améliorations
India=Inde
Indiana=Indiana
Indonesia=Indonésie
InoculantFQAs=Inoculant de conservation FAQ
Iowa=Iowa
Iran,_Islamic_Republic_of=Iran (RÃ©publique islamique d
Iraq=Irak
Ireland=Irlande
Isernia=Isernia
Isle_of_Man=Ã®le de Man
Israel=IsraÃ«l
Italy=Italie
JOD=JOD
JPY=JPY
Jalisco=Jalisco
Jamaica=JamaÃ¯que
Jammu_and_Kashmir=Jammu et Cachemire
Japan=Japon
Jersey=Jersey
Jharkhand=Jharkhand
Jiangsu=Jiangsu
Jiangxi=Jiangxi
Jilin=Jilin
Jordan=Jordan
KBLastPhase=Tenir des registres précis de croissance et de santé
KRW=Coree du Sud (₩ KRW)
Kansas=Kansas
Karnataka=Karnataka
Kazakhstan=Kazakhstan
Kentucky=Kentucky
Kenya=Kenya
Kerala=Kerala
Kerry=Kerry
Ketosis=Acétonémie
KeyBenchmarks=Repères clefs
KeyBenchmarks_AgeInMonthAtFirstCalving=Age au 1er vêlage (en mois)
KeyBenchmarks_CalvingAndHeiferReocrd=Registre des vêlages et des génisses à jour
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=15%  du poids mature à 90 jours
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=55%  du poids mature à l'IA
KeyBenchmarks_HeiferPeakProduce=Pic de lactation des génisses en % de la moyenne du troupeau
KeyBenchmarks_NintyDaysMorbidityf=morbidité (nombre d'animaux malades) à 90 jours
KeyBenchmarks_NintyDaysMortality=mortalité à 90 jours
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=94% du poids mature avant le vêlage
KeyBenchmarks_PercentOfHeifersPregnant=% de génisses gestantes à 15 mois
KeyBenchmarks_SerumlgG=IgG dans le serum IgG (g/L) à 48 hours
Kildare=Kildare
Kilkenny=Kilkenny
Kiribati=Kiribati
Korea=Corée
Korea,_Democratic_People's_Republic_of=RÃ©publique populaire dÃ©mocratique de CorÃ©e
Korea,_Republic_of=CorÃ©e, RÃ©publique de
Kuwait=Koweit
Kyrgyzstan=Kirghizistan
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=Pimenter
Lactating=En lactation
Lactation=Lactation
Lakshadweep=Lakshadweep
Lao_People's_Democratic_Republic=RÃ©publique dÃ©mocratique populaire lao
Laois=Laois
Last_Synced=Synchroniser
LateLactation=Fin Lact.
Latina=Latina
Latvia=Lettonie
Lebanon=Liban
Lecce=Licce
Lecco=LECCO
Leitrim=Leitrim
Lely=Lely
Length-exceed-allowed-limit={0} longueur dépasse du total des caractères autorisés {0}
LengthPerDayImperial=pouces. par jour
LengthPerDayMetric=cm par jour
Les=données de configuration d’un site seront automatiquement mises à jour au moment des mises à jours, à partir de Dairy Enteligen.
Lesotho=Lesotho
LessThan4Days=Moins que 4 jours
LessThanFifteen=&lt;15 (dureté du grain)
LessThanFiveWholeKernals=Moins de 5 grains entiers
LessThanOneHour=Moins de 1 heure
LessThanSixInches=moins de 15 cm
LessThanSixLayers=moins de 6 couches
LessThanTwentFourInchesPerDay=moins de 60 cm  par jour
Liaoning=Liaon
Liberia=LibÃ©ria
Libyan_Arab_Jamahiriya=Jamahiriya arabe libyen
Liechtenstein=Liechtenstein
Lift-Sync-Fail=La synchronisation a  chou  pour des raisons inconnues, veuillez contacter l'administrateur pour r solution dans les coordonn es
Limerick=Limerick
LinkToPens=Liaison avec le lot  (optionel)
Lithuania=Lituanie
Livorno=Viveno
LocoCategory1=1
LocoCategory2=2
LocoCategory3=3
LocoCategory4=4
LocoCategory5=5
LocomotionEditTableViewModel.Category1=Score de locomotion - 1
LocomotionEditTableViewModel.Category2=Score de locomotion - 2
LocomotionEditTableViewModel.Category3=Score de locomotion - 3
LocomotionEditTableViewModel.Category4=Score de locomotion - 4
LocomotionEditTableViewModel.Category5=Score de locomotion - 5
LocomotionEditTableViewModel.EnterNumberOfCows=Veuillez compter le nombre de vaches.
LocomotionEditTableViewModel.Title=Nombre de vaches
LocomotionHerdEditGoalViewModel.Category1=Catégorie 1.0
LocomotionHerdEditGoalViewModel.Category2=Catégorie 2.0
LocomotionHerdEditGoalViewModel.Category3=Catégorie 3.0
LocomotionHerdEditGoalViewModel.Category4=Catégorie 4.0
LocomotionHerdEditGoalViewModel.Category5=Catégorie 5.0
LocomotionHerdEditGoalViewModel.HerdGoal=Objectif du troupeau
LocomotionHerdEditGoalViewModel.Title=Modifier les objectifs
LocomotionHerdInputsViewModel.Herd=TROUPEAU
LocomotionHerdMasterViewModel.Inputs=Données
LocomotionHerdMasterViewModel.Results=Résultats
LocomotionHerdMasterViewModel.Revenue=Revenu
LocomotionHerdMasterViewModel.SubHeading=Analyse du troupeau
LocomotionHerdMasterViewModel.Title=Locomotion
LocomotionHerdResultsViewModel.Category1=Catégorie 1.0
LocomotionHerdResultsViewModel.Category2=Catégorie 2.0
LocomotionHerdResultsViewModel.Category3=Catégorie 3.0
LocomotionHerdResultsViewModel.Category4=Catégorie 4.0
LocomotionHerdResultsViewModel.Category5=Catégorie 5.0
LocomotionHerdResultsViewModel.HerdAverage=Moyenne pour le troupeau
LocomotionHerdResultsViewModel.HerdGoal=Objectif
LocomotionHerdResultsViewModel.Title=Analyse du score de locomotion
LocomotionHerdRevenueViewModel.Revenue=Revenu
LocomotionHerdRevenueViewModel.Title=Locomotion du troupeau - Revenu
LocomotionNumberinHerd=Locomotion (Nombre dans le lot)
LocomotionNumberinPen=Locomotion (Nombre dans le lot)
LocomotionPenInputsViewModel.FromPenSetup=Organisation par lot
LocomotionPenInputsViewModel.Milk=LAIT
LocomotionPenMasterViewModel.Inputs=Données
LocomotionPenMasterViewModel.Results=Résultats
LocomotionPenMasterViewModel.Title=Locomotion
LocomotionPercentofHerd=Locomotion (% du troupeau
LocomotionPercentofPen=Locomotion (% du lot)
LocomotionPreviousVisitsViewModel.AverageScore=Score moyen
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Score moyen
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Date
LocomotionPreviousVisitsViewModel.PercentPen=Pourcentage du lot (%)
LocomotionPreviousVisitsViewModel.SelectedDates=Sélectionnez les dates
LocomotionPreviousVisitsViewModel.Title=Résultats du score de locomotion
LocomotionScore=Évaluation de la locomotion
LocomotionScoreAverage=Score de locomotion moyen
LocomotionScoreHerd=Analyse du score de locomotion
LocomotionScoreReference=Score de Locomotion de référence
LocomotionSelectPenViewModel.MissingDiet=S'il vous plaît entrer une ration valide pour ce lot
LocomotionSelectPenViewModel.PenSelectionList=Lots
LocomotionSelectPenViewModel.Title=Locomotion
Lodi=Contraire
LoginViewModel.Copyright=© {0} Cargill, Incorporated. All Rights Reserved.
LoginViewModel.EmailLabel=Adresse mail
LoginViewModel.ErrorDescription=Nom d'utilisateur et mot de passe ne peuvent pas être vide
LoginViewModel.ErrorTitle=Erreur d'identifiant
LoginViewModel.InvalidMessage=Le nom d'utilisateur ou le mot de passe fourni n'était pas valide pour se connecter.
LoginViewModel.InvalidMessageTitle=Identifiant invalide
LoginViewModel.LoginPrompt=S'identifier
LoginViewModel.LoginPromptConsumer=Utilisateur non-Cargill
LoginViewModel.NetworkErrorMessage=Pas de réseau disponible
LoginViewModel.NetworkErrorMessageTitle=Erreur de réseau
LoginViewModel.PasswordLabel=Mot de Passe
LoginViewModel.Title=Se connecter
LoginViewModel.Unauthorized=Accès non autorisé
LoginViewModel.UnauthorizedTitle=Non autorisé
Longford=Longford
Lors=de la création d'un nouveau site, les seuls éléments exigés sont le nom du site, le prix actuel du lait, le système de traite et les lots (cliquez sur Configuration - Lot). La création de l’élément Lot est nécessaire pour utiliser les outils au niveau des lots  et le rapport. 
Louisiana=Louisiane
Louth=Louth
LowForage=Fourrage de qualité faible
Lucca=Lucca
Luxembourg=Luxembourg
MEQ100G=mEq/100g
MKD=MKD
MUN=Urée dans le lait (mg/dL)
MXN=Mexique (PESO MXN)
MYR=Malaisie (MYR MYR)
Macao=Macao
Macedonia,_the_former_Yugoslav_Republic_of=MacÃ©doine, ancienne RÃ©publique yougoslave
Macerata=MacÃ©rer
Madagascar=Madagascar
Madhya_Pradesh=Madhya Pradesh
Maharashtra=Maharashtra
MainViewModel.EmailLabel=Adresse mail
Maine=Maine
MaintainingForageQuality=Maintenir la Qualité des Fourrages
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Bonus \: stabilisateur/conservateur utilisés dans la ration mélangée pendant l'été
MaintainingForageQuality_TMRMixHasPleasantAroma=La ration mélangée sent bon
MaintainingForageQuality_TMRMixIsCoolToTouch=La ration mélangée est fraiche au toucher
Making_Feed_InventoryFOF=Inventaire des fourrages
Malawi=Flamme
Malaysia=Malaisie
Maldives=Maldives
Male=Mâle
Mali=Devait
Malta=Malte
ManagingForageinSiloBags=Gestion du fourrage en silo boudin
ManagingForageinTowerSilos=Gestion du fourrage en silo tour
Manipur=Manipur
Manitoba=Manitoba
Mantua=Mantua
ManureEditScores=Evaluation des bouses  - Modifier le score
ManureScoreHerdAnalysisEditInputsViewModel.Close=Fermer
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Jours de lactation
ManureScoreHerdAnalysisEditInputsViewModel.Title=Modifier la valeur jour de lactation
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Score de bouses
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Analyse de Score de bouses
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Jours de lactation
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Modifier
ManureScoreHerdAnalysisMasterViewModel.Goals=Objectifs
ManureScoreHerdAnalysisMasterViewModel.Inputs=Données
ManureScoreHerdAnalysisMasterViewModel.Results=Résultats
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Analyse du troupeau
ManureScoreHerdAnalysisMasterViewModel.Title=Score de bouses pour la santé du rumen
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Analyse de score de bouses
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Score de bouses
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Moyenne
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Score maximum
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Score minimum
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Taries en preparation (entre - 21 et -1)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Début de lactation  (16 -60)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Fermer
ManureScoreHerdEditGoalsViewModel.FarOffDry=Préparation vêlage (mois de - 21)
ManureScoreHerdEditGoalsViewModel.Fresh=Début de lactation (0 - 15)
ManureScoreHerdEditGoalsViewModel.LateLactation=Fin de lactation (plus que 201)
ManureScoreHerdEditGoalsViewModel.MaxGoal=Consistance de bouses  Objectif maximum
ManureScoreHerdEditGoalsViewModel.MidLactation=Milieu de lactation (121 to 200)
ManureScoreHerdEditGoalsViewModel.MinGoal=Consistance de bouses
ManureScoreHerdEditGoalsViewModel.PeakMilk=Pic de lactation (60 - 120)
ManureScoreHerdEditGoalsViewModel.Title=Modifier les objetifs
ManureScoreHerdGoalsViewModel.CloseUpDry=Taries en preparation (entre - 21 et -1)
ManureScoreHerdGoalsViewModel.EarlyLactation=Début de lactation  (16 -60)
ManureScoreHerdGoalsViewModel.Edit=Modifier
ManureScoreHerdGoalsViewModel.FarOffDry=Prépa vêlage  (mois que - 21)
ManureScoreHerdGoalsViewModel.Fresh=Début de lactation (0 - 15)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Bouses Objectif maximum
ManureScoreHerdGoalsViewModel.GoalMinTitle=Bouses Objetif minimum
ManureScoreHerdGoalsViewModel.LateLactation=Fin de lactation (plus que 201)
ManureScoreHerdGoalsViewModel.MidLactation=Mi-lactation (121 to 200)
ManureScoreHerdGoalsViewModel.PeakMilk=Pic de lactation (60 - 120)
ManureScoreHerdGoalsViewModel.TableTitle=Score par stade de lactation
ManureScorePenSelectionViewModel.ManureScoreTitle=Evaluation du bouses
ManureScorePenSelectionViewModel.PenSelectionList=Groupes
ManureScorePercentOfPen=Score bouses (% de la case)
ManureScoresChart=Score de bouses - Graphique
ManureScoresResult=Score de bouses - Résultats
MaranhÃ£o=MaranhÃ£ Â£ o
Martinique=Martinique
Maryland=Maryland
Massa_and_Carrara=Massa et Carrara
Massachusetts=Massachusetts
Matera=Matera
Mato_Grosso=Mato Grosso
Mato_Grosso_do_Sul=Mato grosse do sul
Mauritania=Mauritanie
Mauritius=Maurice
Max=MAX
Mayo=Mayo
Mayotte=Mayotte
Mcal=Mcal
Meath=Mine
Medio_Campidano=Medio Campidano
Medium=Medium
Meghalaya=Meghalaya
MenuViewModel.Close=Fermer
MenuViewModel.LogoutPrompt=Déconnexion
MenuViewModel.Menu=Menu
MenuViewModel.ResetDatabaseCancel=Annuler
MenuViewModel.ResetDatabasePrompt=Cette option remplacera toutes les données existantes et les préférences de l'utilisateur, avec un ensemble de données prédéterminées pour le départ. Tous les outils de la visite en cours, les nouvelles visites créées et les laiteries seront perdus. Vous serez également déconnecté de l'application. Continuer? "
MenuViewModel.ResetDatabaseReset=Réinitialiser
MenuViewModel.ResetDatabaseTitle=Réinitialiser le test
MenuViewModel.Sync_PopUp="Synchronisation de données ...
Messina=Messina
MetabolicIncidenceChartsViewModel.Current=Actuel
MetabolicIncidenceChartsViewModel.DeathLoss=Mortalité
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Problèmes métaboliques, coût / vache
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceChartsViewModel.Dystocia=Dystocie
MetabolicIncidenceChartsViewModel.GoalPercent=Objectif (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Maladies métaboliques%
MetabolicIncidenceChartsViewModel.IncidencePercent=Incidence (%)
MetabolicIncidenceChartsViewModel.Ketosis=Acétonémie
MetabolicIncidenceChartsViewModel.Metritis=Métrite
MetabolicIncidenceChartsViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceChartsViewModel.Title=Graphiques d'incidence métabolique
MetabolicIncidenceEditOutputsViewModel.Close=Fermer
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Perte pour mortalite
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceEditOutputsViewModel.Dystocia=Dystocie
MetabolicIncidenceEditOutputsViewModel.Ketosis=Acétonémie
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Objectif (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Métrite
MetabolicIncidenceEditOutputsViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceEditOutputsViewModel.Title=Modifier les objectifs
MetabolicIncidenceInputsEditViewModel.Close=Fermer
MetabolicIncidenceInputsEditViewModel.DeathLoss=Mortalité
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceInputsEditViewModel.Dystocia=Dystocie
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=IV-IAF
MetabolicIncidenceInputsEditViewModel.Ketosis=Acétonémie
MetabolicIncidenceInputsEditViewModel.Metritis=Métrite
MetabolicIncidenceInputsEditViewModel.MilkCow=Lait / Vache ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Fièvre du lait
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceInputsEditViewModel.Title=Modifier les attributions de coût
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Coûts de traitement and coûts associés (réforme,…)
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Coût par jour d'IV-IAF additionnel
MetabolicIncidenceInputsViewModel.Costs=Coûts
MetabolicIncidenceInputsViewModel.DeathLoss=Perte pour mortalite
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceInputsViewModel.Dystocia=Dystocie
MetabolicIncidenceInputsViewModel.Herd=RENSEIGNEMENTS SUR LE TROUPEAU
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Entrez le nombre de fraîches vêlées et le nombre de maladies métaboliques pendant la période d'évaluation. Cela sera converti en coût d'incidence annuel sur l'onglet résultats
MetabolicIncidenceInputsViewModel.IncidenceCases=Nombre de maladies métaboliques
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Augment. de l'IV-IAF
MetabolicIncidenceInputsViewModel.Ketosis=Acétonémie
MetabolicIncidenceInputsViewModel.Mastitis=Mammites
MetabolicIncidenceInputsViewModel.Metritis=Métrite
MetabolicIncidenceInputsViewModel.MilkFever=Fièvre de lait
MetabolicIncidenceInputsViewModel.MilkLossKg=Lait perdu par Lactation ({0}) 
MetabolicIncidenceInputsViewModel.MilkPrice=Prix du lait
MetabolicIncidenceInputsViewModel.PerformanceMessage=Données de référence utilisées pour calculer l'impact économique de chaque incidence métabolique.
MetabolicIncidenceInputsViewModel.PerformanceTreatment=COÛTS DE PERFORMANCE ET DE TRAITEMENT
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Coût d'une génisse
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceInputsViewModel.Title=Données pour incidence métabolique
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Nombre total de vaches
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Nombre de vêlage/An
MetabolicIncidenceInputsViewModel.TreatmentCost=Coût du traitement et coûts associées (réformes,…)
MetabolicIncidenceMasterViewModel.Charts=Graphiques
MetabolicIncidenceMasterViewModel.Inputs=Données
MetabolicIncidenceMasterViewModel.Outputs=Résultats
MetabolicIncidenceMasterViewModel.Title=Maladies métaboliques
MetabolicIncidenceOutputsViewModel.DeathLoss=Perte pour mortalite
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Caillette
MetabolicIncidenceOutputsViewModel.Dystocia=Dystocie
MetabolicIncidenceOutputsViewModel.Ketosis=Acétonémie
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Incidence (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Coût / Vache
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Augmentation de l'IV-IAF
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Différence (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Modifier
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Objectif (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Impact économique des maladies métaboliques au-dessus des objectifs du troupeau
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Impact économique annuel
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Impact économique annuel - Total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Valeur de perte de lait
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Pourcentage de troubles métaboliques
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Coût total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Coût de traitement et coût associés (Réforme,…)
MetabolicIncidenceOutputsViewModel.Metritis=Métrite
MetabolicIncidenceOutputsViewModel.MilkFever=Fièvre de lait
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Rétention Placentaire
MetabolicIncidenceOutputsViewModel.Title=Résultats d'incidence métabolique
MetabolicIncidenceOutputsViewModel.TotalLosses=Pertes annuelles totales
Metric=MÃ©trique
MetricTonsAF=Tonnage MB silo
MetricTonsAFSilo=Tonnage (MB) restant dans le silo
MetricTonsDM=Tonnage MS silo
MetricTonsDMSilo=Tonnage (MS) restant dans le silo
Metritis=Métrite
Mexico=Mexique
Mexico_State=Ãtat du Mexique
Michigan=MICHIGAN
MichoacÃ¡n=MichoacÃ¡n
MidLactation=Mi-lact.
MidOne=Milieu 2
MidOneValue=(8mm)
MidTwo=Milieu 3
Milan=Milan
Milk=Lait ({0})
MilkChange=Changement en production ({0})
MilkFever=Fièvre du lait
MilkLetDownResponse=Réponse en baisse de lait
MilkLossDay=Perte de lait ({0} / Jour)
MilkLossGain=Perte / gain potentiel, en lait
MilkLossKg=Perte de lait (kg)
MilkLossPounds=Perte de lait (lbs)
MilkLossYear=Perte de lait ({0} / Année)
MilkPrice=Prix du lait ({0}/{1})
MilkPricePremiums=Primes de prix du lait
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=TABLEAU DE RÉFÉRENCE
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Données
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Ressources
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Résultats
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Comparaison de procédure de traite
MilkProcessRevenueCalculatorMasterViewModel.Title=Comparaison de procédure de traite
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Cahier de notes
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Stimulation Inadequate 
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Prix du lait  ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=Aucune stimulation
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Stimulation optimale
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Scénario 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Scénario 2
MilkProcessorEditComparisonValuesViewModel.Title=Modifier les valeurs de comparaison
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorInputViewModel.ComparisonValues=Valeurs de comparaison
MilkProcessorInputViewModel.Edit=modifier
MilkProcessorInputViewModel.MilkPrice=Prix du lait  ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=La laiterie sélectionnée avant a été supprimée. Sélectionner une autre laiterie  pour continuer.
MilkProcessorInputViewModel.ScenarioOne=Scénario 1
MilkProcessorInputViewModel.ScenarioTwo=Scénario 2
MilkProcessorInputViewModel.SelectProcessor=Sélectionner la laiterie
MilkProcessorInputViewModel.Title=Données de comparaison de procédure de traite
MilkProcessorInputViewModel.WeightImperialCWT=CWT
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorResourcesViewModel.ApproxSCC=SCC approximatif (cellules / ml)
MilkProcessorResourcesViewModel.LinearScore=Score linéaire
MilkProcessorResourcesViewModel.Mastitis=Pertes de production par Mammite ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Tableau de référence
MilkProcessorResourcesViewModel.Title=Comparaison de procédure de traite- Ressources
MilkProcessorResultsViewModel.Change=Changement
MilkProcessorResultsViewModel.HundredWeight=CWT
MilkProcessorResultsViewModel.MilkPrice=Prix du lait  ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Changements annuels de la valeur
MilkProcessorResultsViewModel.ScenarioOne=Scénario 1
MilkProcessorResultsViewModel.ScenarioTwo=Scénario 2
MilkProcessorResultsViewModel.Title=Comparaison de procédure de traite- Résultats
MilkProcessorResultsViewModel.WeightImperialCWT=CWT
MilkProcessorResultsViewModel.WeightMetric=kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Composantes
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Composantes
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsMasterViewModel.MilkProcNew=Nouveau
MilkProcessorSettingsMasterViewModel.Title=Configuration de la laiterie
MilkProcessorViewModel.Amount=Quantité (1000 cellules / ml)
MilkProcessorViewModel.AmountCFU=Quantité (1000 ufc / ml)
MilkProcessorViewModel.BasePriceMilkFat=Matière Grasse (euro/kg)({0} / {1})
MilkProcessorViewModel.BasePriceMilkPrice=Lait ({0} / {1})
MilkProcessorViewModel.BasePriceMilkProtein=Proteine du lait ({0} / {1})
MilkProcessorViewModel.BasePriceOtherSolids=Autres solides ({0} / {1})
MilkProcessorViewModel.BasePrices=PRIX DE BASE
MilkProcessorViewModel.ComponentProcessor=Composantes du paiement - laiterie
MilkProcessorViewModel.ConcentrationProcessor=Laiterie paiement au litre de lait et qualité (g/L)
MilkProcessorViewModel.Delete=Effacer
MilkProcessorViewModel.DeletePrompt=La suppression de cette laiterie annulera les résultats dans l'outil Calculatrice du produit lait. Voulez-vous supprimer cette laiterie?
MilkProcessorViewModel.HundredWeight=CWT
MilkProcessorViewModel.Name=NOM
MilkProcessorViewModel.NameNotUnique=Une laiterie nommée "{0}" existe déjà. Les noms doivent être uniques.
MilkProcessorViewModel.NewComponentProcessorName=Laiterie - Matière utile \# {0}
MilkProcessorViewModel.NewConcentrationProcessorName=Laiterie - Volume et taux \# {0}
MilkProcessorViewModel.PricingMatrices=Matrice de prix
MilkProcessorViewModel.SelectCurrency=Sélectionnez la devise
MilkProcessorViewModel.WeightImperial=LBS
MilkProcessorViewModel.WeightImperialCWT=CWT
MilkProcessorViewModel.WeightMetric=KG
MilkProduction=Production de lait({0})
MilkProductionKg=Production de lait (kg)
MilkProductionPounds=Production de lait (lbs)
MilkProductionRevenue=Revenus de production de lait
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Rendements et efficacité alimentaire
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=Matière sèche ingérée et efficacité alimentaire
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=TB g/kg et TP g/kg
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Production laitière et Jour de lactation
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Comptage cellulaire et Urée du lait
MilkSoldEvaluationChartsListViewModel.VisitComparison=Sélectionnez une visite pour comparer
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Efficacité alimentaire
MilkSoldEvaluationChartsViewModel.ComponentYield=Rendement en composant
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Rendements et efficacité alimentaire
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=Matière sèche ingérée et efficacité alimentaire
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Jour de lactation
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Matière sèche ingérée
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Efficacité alimentaire
MilkSoldEvaluationChartsViewModel.MilkFat=TB (g/kg)
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=TB (g/kg) et TP (g/kg)
MilkSoldEvaluationChartsViewModel.MilkProduction=Production laitière
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Production laitière et Jour de lactation
MilkSoldEvaluationChartsViewModel.MilkProtein=TP (g/kg)
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Urée
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Comptage cellulaire
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Comptage cellulaire et Urée du lait
MilkSoldEvaluationChartsViewModel.Title=Evaluation vente de lait
MilkSoldEvaluationInputsViewModel.AddPickup=Ajouter une collecte
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Nombre d'animaux au tank  ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Jour de lactation (JEL) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Matière sèche ingérée ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=RENSEIGNEMENTS SUR LE TROUPEAU
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Vaches en lactation ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Livraison de lait ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Information laiterie
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Taux d'urée ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Ajouter nouveau
MilkSoldEvaluationMasterViewModel.Charts=Graphiques
MilkSoldEvaluationMasterViewModel.Inputs=Données
MilkSoldEvaluationMasterViewModel.Outputs=Résultats
MilkSoldEvaluationMasterViewModel.Title=Evaluation des ventes de lait
MilkSoldEvaluationOutputsViewModel.AvgBCC=Germes ( 1,000 ufc/mL)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=TB (%)
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Production laitière moyenne, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Production laitière moyenne, {0} (animaux au tank)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=TP(%)
MilkSoldEvaluationOutputsViewModel.AvgSCC=Taux cellulaire moyen ( 1,000 cellules/mL)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Efficacité alimentaire (% MSI)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Jours d'évaluation
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Efficacité alimentaire (ratio)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Rendement en protéine+matière grasse ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Matière Grasse ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Protéine ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Mise à jour du site
MilkSoldPickupViewModel.AnimalsInTank=Nombre d'animaux au tank ⃰
MilkSoldPickupViewModel.BCC=Germes (1 000 cfu/mL)
MilkSoldPickupViewModel.DaysInTank=Jours dans le tank ⃰
MilkSoldPickupViewModel.MilkFat=TB (%)
MilkSoldPickupViewModel.MilkProtein=TP (%)
MilkSoldPickupViewModel.MilkSold=Lait vendu, {0} ⃰
MilkSoldPickupViewModel.SCC=Cellules Somatiques (1 000 cellules/mL)
MilkSoldPickupViewModel.Title=Editer collecte {0}
MilkSoldSpinnerViewModel.Title=Evaluation des ventes de lait
MilkUrea=Urée ( mg/dL)
Milking=En lactation
MilkingFailure=Ãchecs de traite
MilkingFirst=Milking first
MilkingProcessRevenueInputs=Comparaison de procédure de traite - Entrées
MilkingProcessRevenueResources=Comparaison de procédure de traite - Ressources
MilkingProcessRevenueResults=Comparaison de procédure de traite- Résultats
Minas_Gerais=minas Gerais
Minnesota=Minnesota
Mississippi=Mississippi
Missouri=Missouri
Mizoram=Mizoram
Modena=Modena
Moderate=Moderate
ModeratelyClean=Modérément propre
Moldova,_Republic_of=Moldavie, RÃ©publique de
Monaco=Monaco
Monaghan=Monaghan
Mongolia=Mongolie
Montana=Montana
Montenegro=MontÃ©nÃ©gro
Monthly=Mensuel
Montserrat=Montserrat
Monza_and_Brianza=Monza et Brianza
MoreThan8LayersOfPlastic=plus de 8 couches de plastiques
Morelos=Morelos
Morocco=Maroc
Mozambique=Mozambique
Myanmar=Myanmar
NGN=NGN
NIO=Nicaragua (NIO NIO)
NOK=NOK
Nagaland=Nagaland
Namibia=Namibie
Naples=Naples
Nauru=Nauru
Nayarit=Nayarit
Nebraska=Nebraska
Nei_Mongol=Nei Mongol
Nepal=NÃ©pal
Netherlands=Pays-Bas
Nevada=Nevada
NewBunker=Donnez un nom au silo couloir
NewDietClassViewModel.Title=Type Animal / Sous Classe
NewDietPensViewModel.AssociatePens=Vous pouvez associer cette ration à plusieurs groupes
NewDietPensViewModel.Title=Lien vers le groupe
NewDietViewModel.Cancel=Annuler
NewDietViewModel.MainHeading=Nom de la ration ⃰
NewDietViewModel.Save=Sauvegarder
NewDietViewModel.Title=Nouvelle ration
NewPenDietViewModel.New=Nouveau
NewPenDietViewModel.Title=ration
NewPenViewModel.Animals=Animaux par groupe
NewPenViewModel.AnimalsInputsPen=Données d'animaux, par groupe
NewPenViewModel.AsFedIntake=Ingestion MB
NewPenViewModel.Barn=Nom de l'étable
NewPenViewModel.Cancel=Annuler
NewPenViewModel.DaysInMilk=jours en lacatation
NewPenViewModel.Diet=ration
NewPenViewModel.DietInputsPen=Données de la ration, par groupe
NewPenViewModel.DryMatterIntake=Matière sèche ingérée en KG
NewPenViewModel.FeedingSystem=Système d'alimentation
NewPenViewModel.General=Général
NewPenViewModel.HousingSystem=Type de logement
NewPenViewModel.Milk=Production de lait({0})
NewPenViewModel.MilkingFrequency=Fréquence de traite
NewPenViewModel.NumberOfStalls=Nombre de logettes
NewPenViewModel.OnlyOnePen=Vous n'avez seulement qu'un lot
NewPenViewModel.PenDetail=Caractéristiques du groupe
NewPenViewModel.PenMapping=Plan de la case
NewPenViewModel.PenName=Nom du groupe
NewPenViewModel.PenSelection=Selectionner une case
NewPenViewModel.PublishPenAlert=Merci de publier toutes les visites en lien avec la case d'animaux que vous souhaitez fusionner
NewPenViewModel.RationCostPerAnimal=Coût de la ration / vache ({0})
NewPenViewModel.Save=Sauvegarder
NewPenViewModel.Title=Nouveau groupe
NewPenViewModel.UserCreatedPen=Lot créé par l'utilisateur
NewPile=Donnez un nom au lieu de stockage
NewProspectViewModel.Address1=ADRESSE COMMERCIALE 1
NewProspectViewModel.Address2=ADRESSE COMMERCIALE 2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.BusinessName=NOM DU L'ENTREPRISE
NewProspectViewModel.City=VILLE
NewProspectViewModel.ConsumerDetails=Détails sur le consommateur
NewProspectViewModel.Country=PAYS
NewProspectViewModel.Customer=Clients
NewProspectViewModel.CustomerDetail=DÉTAILS DU CLIENT
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=EMAIL DU CONTACT
NewProspectViewModel.EndUser=Utilisateur final
NewProspectViewModel.FarmProducer=Agriculteur
NewProspectViewModel.Image=Cliquez pour éditer l'image
NewProspectViewModel.InvalidEmail=Merci de saisir une adresse mail.
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=Un client potentiel nommé "{0}" existe déjà. Les noms doivent être uniques.
NewProspectViewModel.NameNotUniqueForConsumer=Ce nom "{0}" existe déjà. Les noms doivent être uniques.
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=- 
NewProspectViewModel.NullBusinessName=Nom commercial requis
NewProspectViewModel.NullFirstName=Prénom requis
NewProspectViewModel.NullSecondName=Nom requis
NewProspectViewModel.PostalCode=CODE POSTAL
NewProspectViewModel.PrimaryContactFirstName=PRENOM DU CONTACT
NewProspectViewModel.PrimaryContactLastName=NOM DE FAMILLE DU CONTACT
NewProspectViewModel.PrimaryPhone=NUMÉRO DE TÉLÉPHONE DU CONTACT
NewProspectViewModel.Prospect=Prospect
NewProspectViewModel.ProspectDetail=DÉTAILS DU PROSPECT
NewProspectViewModel.Segment=SEGMENT
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Spence=Spence
NewProspectViewModel.State=ETAT
NewProspectViewModel.Title=Détails
NewProspectViewModel.Type=TYPE
NewProspectViewModel.Walton=Walton
NewVisitViewModel.Title=Détails de la visite
New_Brunswick=Nouveau-Brunswick
New_Caledonia=Nouvelle CalÃ©donie
New_Hampshire=New Hampshire
New_Jersey=New Jersey
New_Mexico=Nouveau Mexique
New_South_Wales=Nouvelle Galles du Sud
New_York=New York
New_Zealand=Nouvelle-ZÃ©lande
Newfoundland_and_Labrador=Terre-Neuve-et-Labrador
Next=suivant
Nicaragua=Nicaragua
Niedersachsen=SAXONE DE BAIS
Niger=Niger
Nigeria=Nigeria
Ningxia=Ningxia
Niue=Niue
No=Non
No-User-Found=Utilisateur introuvable sur LIFT ; veuillez contacter l'administrateur pour une résolution
NoResourcesAvailable=Ressource non disponible
NoResults=Aucun résultat trouvé
NoWholeKernals=Aucun grain entier
Noah=Noah
None=Aucun
NoneSelected=Aucun sélectionné
Nordrhein=Rhin du Nord
Norfolk_Island=l'ile de Norfolk
Normal=&lt;0.5pt NEC 
NorthAmerica=Amerique du Nord
North_Carolina=Caroline du Nord
North_Dakota=Dakota du nord
Northern_Territory=Territoire du Nord
Northwest_Territories=Territoires du nord-ouest
Norway=NorvÃ¨ge
Not-matching-with-allowed-values={0} ne correspondant pas aux valeurs autorisées
NotMeasured=Non mesuré
NotRemoved=Non enlevés
NotSet=-
NoteCamcorderNotImplemented=Fonctionnalité de la caméra n'est pas encore configurée
NoteCategoryViewModel.FooterText=Une seule catégorie peut être sélectionnée par annotation
NoteCategoryViewModel.SelectCategory=CHOISIR UNE CATÉGORIE
NoteCategoryViewModel.Title=Note
NotebookBCSHerdAnalysisGoals=Analyse d'état corporel de troupeau - Objectifs
NotebookBCSHerdAnalysisInputs=Analyse d'état corporel de troupeau - Données
NotebookBCSHerdAnalysisResults=Analyse d'état corporel de troupeau - Résultats
NotebookBCSSelectPointScale=État corporel - Sélectionnez une échelle de points
NotebookBodyConditionEdit=État corporel Modifier le tableau
NotebookCudCalculators=Santé du rumen - compteur proportion vaches qui ruminent
NotebookCudChewing=Santé du rumen - compteur nombre de coups de machoire - sélectionner le lot
NotebookCudChewingDataEntry=Santé du rumen - compteur nombre de coups de machoire par cycle de rumination Données
NotebookCudChewingResults=Santé du rumen-Résultats nombre de coups de machoire par cycle de rumination Résultats
NotebookLocomotionEditTable=Locomotion - Nombre de Vaches
NotebookLocomotionHerdInputs=Locomotion - Analyse données troupeau
NotebookLocomotionHerdResults=Locomotion - Résultats d'analyse du troupeau
NotebookLocomotionHerdRevenue=Locomotion - Incidences performances troupeau
NotebookLocomotionLanding=Locomotion - Sélection du lot
NotebookLocomotionPenInputs=Locomotion - Données du lot
NotebookLocomotionPenResults=Locomotion - Résultats du lot
NotebookLocomotionPenSelection=Locomotion - Sélection du lot
NotebookManurePenSelection=Score des bouses - Sélection du lot
NotebookManureScoreHerdAnalysisGoals=Score des bouses du troupeau - Objectifs
NotebookManureScoreHerdAnalysisInputs=Score des bouses du troupeau - Données
NotebookManureScoreHerdAnalysisResults=Score des bouses du troupeau - Résultats
NotebookManureScoreLanding=Evaluation des bouses
NotebookMetabolicIncidenceCharts=Graphiques maladies métaboliques
NotebookMetabolicIncidenceInputs=Données maladies métaboliques
NotebookMetabolicIncidenceOutputs=Résultats maladies métaboliques
NotebookParticleScoreHerdAnalysisEdit=Tamisage de la ration - Analyse du score des particules de la ration mélangée - EDITER la quantité de MSI
NotebookParticleScoreLanding=Evaluation - Taille des particules dans la ration mélangée
NotebookParticleScoreSelectPen=Tamisage ration - Sélectionner le lot
NotebookParticleScoreSelectScorer=Tamisage ration - Sélectionner le tamis
NotebookPenTimeComparison=Evaluation du temps de repos - Comparaison
NotebookPenTimeInputs=Evaluation du temps de repos - Données du lot
NotebookPenTimePenSelection=Evaluation du temps de repos - Sélection du lot
NotebookPenTimeResults=Evaluation du temps de repos - Résultats du lot
NotebookReadyToMilkCharts=Graphiques Ready2Milk 
NotebookReadyToMilkInputs=Données Ready2Milk 
NotebookReadyToMilkOutputs=Résultats Ready2Milk 
NotebookRumenHealthNumberOfChewsInput=Santé du rumen - Nombre de mastications
NotebookRumenHealthNumberOfChewsResults=Santé du rumen - Nombre de coups de machoires -Résultats
NotebookRumenHealthTMRParticlePercent=Répartition par étage du tamis
NotebookRumenHealthTMRParticleScore=Evaluation - taille des particules dans le RTM
NotebookSectionComfortTools=Evaluation du confort
NotebookSectionForageAudit=Page référence pour la vérification des fourrages
NotebookSectionForageAuditBaleage=Audit des fourrages - Bottes d'enrubannage
NotebookSectionForageAuditBunkersPiles=Audit des fourrages - Taupinières et silos couloirs
NotebookSectionForageAuditHarvest=Audit des fourrages - Conditions de récoltes
NotebookSectionForageAuditLanding=Vérification des fourrages
NotebookSectionForageAuditMaintainingQuality=Audit des fourrages - Maintien de la qualité
NotebookSectionForageAuditSilageBags=Audit des fourrages- Boudins d'ensilage
NotebookSectionForageAuditSurveyOfForages=Audit des fourrages
NotebookSectionForageAuditTowerSilos=audit des fourrages - Silo tour
NotebookSectionHealthTools=Outils de santé
NotebookSectionHeatstressCalculations=Calculateur \: stress thermique
NotebookSectionHeatstressChart=Graphique \: Stress thermique
NotebookSectionHeatstressData=Données \: stress thermique
NotebookSectionHerdAnalysisGoals=Santé ruminale - Analyse des objectifs du troupeau
NotebookSectionMilkSoldEvaluationCharts=Evaluation du lait vendu - Graphiques
NotebookSectionMilkSoldEvaluationEditPickup=Evaluation du lait vendu - Modifier la collecte
NotebookSectionMilkSoldEvaluationInputs=Evaluation du lait vendu - Données
NotebookSectionMilkSoldEvaluationOutputs=Evaluation du lait vendu - Résultats
NotebookSectionNutritionTools=Outils nutrition
NotebookSectionRumenHealthLanding=Santé ruminale
NotebookSectionVisit=Visite
NotebookTMRParticleHerdAnalysisPenInputs=Tamisage de la ration - Résultats troupeau - Données
NotebookTMRParticleHerdAnalysisPenResults=Analyse du score des particules de la ration mélangée -Résultats
NotebookUrinePHEditGoals=Edition des objectifs de pH urinaire
NotebookUrinePHInputs=Rentrée des pH urinaire
NotebookUrinePHOutputs=Résultats des pH urinaire
NotebookVisitSummary=Rapport de la visite
NotebookWalkthroughReport=Visite pas-à-pas
NotebookWalkthroughReportLanding=Visite pas-à-pas
NotebookWalkthroughReportPen=Rapport pas-à-pas \: Analyse par groupe
Nova_Scotia=Nouvelle-Ãcosse
Novara=Novara
Nuevo_LeÃ³n=Nouveau LeÃ­n
Null-values-not-allowed=Valeur nulle en {0} non autorisée
NumChewsGoal={0} Objectif
NumOfCows=Nombre de vaches
NumberOfChewsReportsViewModel.Average=Nombre moyen de mastications
NumberOfChewsReportsViewModel.AverageChews=Mastication moyenne
NumberOfChewsReportsViewModel.AverageNumberChews=Nombre moyen de mastications
NumberOfChewsReportsViewModel.DateDescription=Date
NumberOfChewsReportsViewModel.DatesComparison=Dates de comparaison
NumberOfChewsReportsViewModel.EditVisits=Sélectionner
NumberOfChewsReportsViewModel.StdDevCalculated=Ecart-type (calculé)
NumberOfChewsReportsViewModel.VisitDate=Date
NumberOfChewsViewModel.Count=Nombre
NumberOfChewsViewModel.CountHeader=Compter le nombre de mastication par vache
NumberOfChewsViewModel.NextCow=Vache suivante
NumberOfChewsViewModel.NumberOfChewsCow=Nombre de mastications / cycle de rumination / vache
NumberOfChewsViewModel.Title=Nombre de mastications /vache \# {0}
NumberOfChewsViewModel.ValidCudInput=Merci d'entrer une donnée valide
NumberOfCows=nombre de vaches
Nunavut=Nunavut
Nuoro=Nuoro
NutritionViewModel.NutritionForage=Vérification des fourrages
NutritionViewModel.NutritionLabel=Choisir parmis les outils suivants pour débuter votre visite
NutritionViewModel.NutritionPile=Inventaires des fourrages
NutritionViewModel.NutritionTools=Outils nutrition
NutritionViewModel.NutritionToolsCaption=Outils
NutritionViewModel.NutritionToolsInstructions=Choissisez un outil de la liste ci-dessous pour débuter votre visite
NutritionViewModel.NutritionToolsList=Outils
NutritionViewModel.Title=Outils nutrition
NutritionViewModel.VisitNotebook=Cahier de notes
OKLabel=OK
Oaxaca=Oxaca
Objetif=minimum
Observation=Observation
Odisha=Odisha
Offaly=Offnaly
Ogliastra=Oglistra
Ohio=Ohio
Oklahoma=Oklahola
Olbia-Tempio=Olbia-Tempio
Oman=Le sien
OncePerWeek=Une fois par semaine
One=Un
OneHourBeforeActionIsDue=Une heure avant que l'action ne soit due
OneToSixHours=1 à 6 heures
Ontario=Ontario
Opportunities=Opportunités
Optimal=Réponses optimales
Oregon=Oregon
Oristano=Oristano
Other=Autre
OtherSilage=Autres sources d'ensilage
Overall=Globalement
OverallCalfHeiferDetails=Pour voir le score global de l'outil Veaux et Génisses, complétez au moins un questionnaire dans la liste ci-dessus
OverallForageScoreDetails=Pour voir le score fourrager global, complétez au moins une des rubriques dans la liste ci-dessus
PDFDisclaimer=Cargill Incorporated, ses sociétés mères ou affiliées ne justifie pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. © {0} Cargill, Incorporated. Tous les droits sont réservés
PDFDisclaimer_ProvimiUS=Provimi North America, ses sociétés mères ou affiliées ne justifie pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. © {0} Provimi North America. Tous les droits sont réservés
PDFPageNumber=Page {0} de {1}
PEN=Pérou (S/. PEN)
PHP=Philippines ($ PHP)
PLN=Pologne (zł PLN)
PMRConcentrate=Ration semi-complète
PON=Roumanie (lei PON)
Padua=Padoue
Pakistan=Pakistan
Palermo=Palerme
Palestinian_Territory,_Occupied=Territoire palestinien, occupÃ©
Panama=Panama
Papua_New_Guinea=Papouasie Nouvelle GuinÃ©e
Paraguay=Paraguay
ParanÃ¡=ParansÃ©
ParaÃ­ba=ParaÃ£ba
Parlor=Salle de traite
Parma=Parme
ParticleScorePreviousVisitsViewModel.MidOne=Milieu 2
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Milieu 3
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Quantité sur le tamis (%)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Milieu 1 (8mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Milieu (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Haut (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Tamis
ParticleScorePreviousVisitsViewModel.SelectDates=Sélectionnez les dates
ParticleScorePreviousVisitsViewModel.Top=Haut
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.Tray=Tamis
ParÃ¡=Paraictoire
PasteurizedWholeMilk=Lait pasteurisé distribué
Pasto=Pasto
Pasture=Pâturage
PastureOther=Pâturage + Autre
Pavia=Pavie
Pays=Paie
PeakMilk=Pic lact.
PenDetailViewModel.Title=Caractéristiques du groupe
PenListViewModel.DietSetup=Configuration – Ration
PenListViewModel.MainHeading=LOTS
PenListViewModel.NewPen=Ajouter un nouveau lot
PenListViewModel.Title=Lots
PenName=Nom du lot
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Variation de l'état corporel (sur 100 jours)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Variation du poids corporel ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Vaches dans le lot
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Vaches traites par heure
PenTimeBudgetComparisonViewModel.Current=Actuel
PenTimeBudgetComparisonViewModel.EnergyChange=Changement d'énergie (Mcals)
PenTimeBudgetComparisonViewModel.Overcrowding=Surdensité (%)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Nombre de vaches dans la salle de traite par heure
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Perte / gain potentiel de lait ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Temps restant (heures)
PenTimeBudgetComparisonViewModel.TableTitle=Comparaison
PenTimeBudgetComparisonViewModel.TimePerMilking=Temps par traite (heures)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Temps restant pour le repos (heures)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Temps requis pour le repos (heures)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Temps total sans repos (heures)
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Temps total de la traite (Heures))
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Temps de marche vers la traite (Heures)
PenTimeBudgetPenMasterViewModel.Compare=Comparer
PenTimeBudgetPenMasterViewModel.Inputs=Données
PenTimeBudgetPenMasterViewModel.Results=Résultats
PenTimeBudgetPenMasterViewModel.Title=Temps de repos par groupe
PenTimeBudgetResultsViewModel.Hours=Heures
PenTimeBudgetResultsViewModel.MilkDifference=Différence potentielle en lait
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=lbs
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Perte / gain potentiel, en lait
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Temps disponible pour le repos
PenTimeBudgetResultsViewModel.TimeRemaining=Temps restant
PenTimeBudgetResultsViewModel.TimeRequired=Temps requis
PenTimeBudgetResultsViewModel.Title=Temps de repos par groupe - Résultats
PenTimeInputsViewModel.CowsPen=Nombre de vaches dans le lot
PenTimeInputsViewModel.Drinking=Temps pour boire et socialisation (Heures)
PenTimeInputsViewModel.Eating=Temps pour manger (heures)
PenTimeInputsViewModel.Frequency=Fréquence de traite (par jour)
PenTimeInputsViewModel.LockUp=Temps bloqué (heures)
PenTimeInputsViewModel.NonRestTime=Autres temps d'activité (heures)
PenTimeInputsViewModel.ParlorTime=Temps en salle de traite (heures)
PenTimeInputsViewModel.PenTimeTitle=Temps de repos par lot
PenTimeInputsViewModel.Resting=Exigence de repos (heures)
PenTimeInputsViewModel.StallsPen=Nombre de logettes dans le batiment (lot)
PenTimeInputsViewModel.TotalStalls=Nombre de places dans la salle de traite
PenTimeInputsViewModel.WalkingTimeFrom=Temps de retour batiment/pâture (après traite) (heures)
PenTimeInputsViewModel.WalkingTimeTo=Temps de marche vers la traite (heures)
PenTimePenSelectionViewModel.NoLactatingPen=Pour utiliser cet outil, assurez-vous d’avoir au moins un lot de VL associé à une ration VL.
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Temps de repos par lot
PenTimePenSelectionViewModel.PenTimeSection=LOTS
PenTimePenSelectionViewModel.Title=Temps de repos par groupe
Pendetails=Détails du lot
PennStateShakerBoxForageResults=Résultat du Penn State
Pennsylvania=Pennsylvanie
Pens=Lots
PerceivedHeatStressDietInfoMessage=Stress thermique modéré\: haletant, bave ou mousse mais pas de bouche ouverte, fréquence de respiration de 40 à 120 bpm.
PercentLossPerCow=% de perte / vache
PercentOnScreenTitle=Quantité sur le tamis (%)
PercentPen=%
PercentageOnScreen=Quantité sur le tamis (%)
PercentageOnScreenCurrentVisit=Poucentage sur l'écran (%)  - Visite en cours
PercentageOnScreenTrend=Poucentage sur l'écran (%) - Tendance
Pernambuco=Pernambuco
Peru=Pérou
Perugia=PÃ©rugie
Pesaro_and_Urbino=Pesaro et Urbino
Pescara=Pescara
PhaseFive=Puberté
PhaseFour=Croissance
PhaseOne=Croissance
PhaseSeven=Préparation vêlage
PhaseSix=Gestation
PhaseTwoThree=Pre/post sevrage
Philippines=Philippines
PhotoExamples=Exemple photo
Piacenza=Piacenza
PiauÃ­=PiauÃ£
Pickup=Collecte {0}
Pile=Silo
PileAndBunkerCapacitiesDensity=Guide de référence de la capacité des structures d'entreposage des fourrages
PileAndBunkerCapacity=Capacité des silos
PileAndBunkerCapacityViewModel.AddBag=Ajouter un boudin
PileAndBunkerCapacityViewModel.AddBunker=Ajouter silo couloir
PileAndBunkerCapacityViewModel.AddPile=Ajouter une taupinière
PileAndBunkerCapacityViewModel.Bag=Silo
PileAndBunkerCapacityViewModel.Bags=Silos
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=Silo avec déchargement par le bas
PileAndBunkerCapacityViewModel.Bunker=Silo couloir
PileAndBunkerCapacityViewModel.Bunkers=Silos couloirs
PileAndBunkerCapacityViewModel.NameNotUnique=Le nom d'un  silo couloir ou d'une silo doivent contenir 40 caractères maximum
PileAndBunkerCapacityViewModel.NameTooLong=Le nom d'un tas ou d'un bunker doivent être 40 caractères maximum
PileAndBunkerCapacityViewModel.Pile=Silo
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Inventaires des fourrages
PileAndBunkerCapacityViewModel.Piles=Silo
PileAndBunkerCapacityViewModel.Resources=RESSOURCES
PileAndBunkerCapacityViewModel.Title=Inventaires des fourrages
PileAndBunkerCapacityViewModel.TopUnloadingSilo=Silo avec déchargement par le haut
PileAndBunkerCapacityViewModel.VisitNotebook=Cahier de notes
PileAndBunkerName=Nom de l'inventaire de fourrages
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=Densité de l'ensilage TQS {0} (objectif\: &gt; {1})
PileAndBunkerResultsBagCapacityInputViewModel.CapacityBag=Capacité
PileAndBunkerResultsBagCapacityInputViewModel.DiameterBag=Diamètre ({0})
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=% Matière sèche
PileAndBunkerResultsBagCapacityInputViewModel.LenghtBag=Longueur ({0})
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=Tonnes MB
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=Tonnes MS
PileAndBunkerResultsBagCapacityInputViewModel.SilageDMDensityBag=densité de la matière sèche de l'ensilage
PileAndBunkerResultsBagCapacityInputViewModel.TonsAFBag=Tonnes MB
PileAndBunkerResultsBagCapacityInputViewModel.TonsDMBag=Tonnes MS
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Longueur en bas ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Largeur en bas ({0}))
PileAndBunkerResultsCapacityInputViewModel.Capacity=Capacité
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=MS %
PileAndBunkerResultsCapacityInputViewModel.Height=Hauteur ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=Tonne MB silo
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Tonne MS silo
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Longueur en bas (p.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Longueur en bas (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Largeur en bas (p.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Largeur en bas (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Hauteur (p.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Hauteur (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Longueur en haut (p.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Longueur en haut (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Largeur en haut (p.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Largeur en haut (m.)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Densité MS d'ensilage ({0})
PileAndBunkerResultsCapacityInputViewModel.Title=Capacité
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Densité en MB d'ensilage {0} (objectif\:&gt; 44)
PileAndBunkerResultsCapacityInputViewModel.TonsAF=TONNES MB
PileAndBunkerResultsCapacityInputViewModel.TonsDM=TONNES MS
PileAndBunkerResultsCapacityInputViewModel.TopLength=Longueur en haut ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Largeur en haut ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=à 6 pouces par jour
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=à 15 cm par jour
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=à 3 pouces par jour
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=à 7 cm par jour
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=Nombre de vaches nécessaires pour assurer avancement silo
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Nombre de vaches alimentées
PileAndBunkerResultsFeedOutViewModel.DateGone=Date à ce jour
PileAndBunkerResultsFeedOutViewModel.Days=Jours
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Consommation de fourrage par le troupeau par jour
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Surface du front d'attaque (ft²)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Surface d'alimentation (m²)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Consommation (MB/vache/jour)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=pouces par jour
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=cm Par jour
PileAndBunkerResultsFeedOutViewModel.StartDate=Date de début
PileAndBunkerResultsFeedOutViewModel.Title=Reprise
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tonnes par jour
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Lbs matière sèche sur 1 pied
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kg matière sèche sur 1 mètre
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Capacité
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Reprise
PileAndBunkerResultsMasterViewModel.VisitNotebook=Cahier de notes
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=Capacité
PileAndBunkerResultsSiloCapacityInputViewModel.Diameter=Diamètre ({0}))
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=% Matière sèche
PileAndBunkerResultsSiloCapacityInputViewModel.FilledHeight=Hauteur de remplissage({0})
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=Tonnes restantes dans le silo (MB)
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=Tonnes restantes dans le silo (MS)
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=Densité de MS de l'ensilage
PileAndBunkerResultsSiloCapacityInputViewModel.SilageLeft=Hauteur de l'ensilage restant dans le silo ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.TonsAFSilo=Tonnes restantes dans le silo (MB)
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=Tonnes restantes dans le silo (MS)
PileAndBunkerTitle=Inventaire des fourrages
PileBunkerCapacities=Inventaires des fourrages
PileCapacity=silo - Capacité
PileFeedOutRate=silo - Reprise
Piles=Silo
Pisa=Pise
Pistoia=Pistoia
Pitcairn=Pitcairn
PlBacteriaCell=Comptage Bactérien (1 000 cfu/mL)
PlMilkFat=Matière Grasse (euro/kg)
PlMilkProtein=Protéine du lait (euro/kg)
PlSomaticCell=Cellules Somatiques (1 000 cellules/mL)
Placentaire=
Poland=Pologne
Poor=Mauvais
Pordenone=PordÃ©none
Porosity=Densité de tassage
Portugal=le Portugal
Postweaned=Post-sevrage
Postweaned_CleanAndDryPen=Case propre et sèche
Postweaned_CleanAndDryPen_ToolTip=Utiliser le test du genou pour savoir si la zone est propre et seche
Postweaned_EvidenceOfAcidosisInManure=Signe d'acidose dans les bouses
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Y-a-t-il des bulles dans les bouses?
Postweaned_EvidenceOfScoursOrPneumonia=Preuve de diarrhée ou de pneumonie
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% des veaux souffrent de diarrhée ou de pneumonie 
Postweaned_FeedBunkIsClaanedDaily=L'auge est nettoyée tous les jours et les refus retirés
Postweaned_ForageAvailability=Fourrage disponible
Postweaned_ForageAvailability_ToolTip=Les brins (paille/ foin) sont plus longs que 5cm
Postweaned_FreeChoiceCleanWaterIsAvailable=Accès à l'abreuvoir et qualité de l'eau satisfaisants
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=Pas de preuve de contamination de l'eau
Postweaned_FreshQualityStarterAvailable=Aliment starter/croissance renouvelé fréquemment et disponible
Postweaned_FreshQualityStarterAvailable_ToolTip=L'aliment starter / croissance n'a pas de fines, pas de moisissure et n'est pas mouillé
Postweaned_SizeOfBunkSpace=Taille de la case adaptée pour chaque veau
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45cm par veau
Postweaned_SizeOfPenAdequate=Taille de la case adaptée pour chaque génisse
Postweaned_SizeOfPenAdequate_ToolTip=Case Individuelle \: 3m2,  Case en groupe\: 2.75m2
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Enclos bien ventilé et sans courant d'air sur les mollets
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=Si les vêtements sentent l'ammoniac après avoir quitté le bâtiment\: problème de ventilation
PotentialDownResponse=Perte de lait potentielle ({0}/cow/day)
PotentialSCC=Comptage potentiel de cellules (cellules/{0})
Potenza=Pouvoir
Prato=Plat
PreWeaned_CMRisProperlyMixed_ToolTip=Quantité d'aliment d'allaitement\: &gt;600g &lt;800g, Temp\: 39-41°C, MS 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Utiliser le test du genou pour savoir si la zone est propre et seche
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% des veaux souffrent de diarrhées ou de pneumonies
PreWeaned_Forageavailability_ToolTip=Si un aliment cellulosique est utilisé, pas besoin de fourrage
PreWeaned_FreeChoiceCleanWater_ToolTip=Disponibilité de l'eau dès le premier jour et pas de preuve de contamination
PreWeaned_FreeChoiceFreshCalf_ToolTip=L'aliment starter  n'a pas de fines, pas de moisissures et n'est pas mouillé
PreWeaned_SizeOfPen_ToolTip=Case individuelle\: 3m2, Case par groupe\: 2.5m2
PreWeaned_WellVenilated_ToolTip=Si les vêtements sentent l'ammoniac après avoir quitté le bâtiment\: problème de ventilation
PrematureKelvingsKeyInfoMessage=Naissance d'un veau vivant au moins 10 jours avant la date prévue
PreventingStorageLosses=Prévenir des pertes dues au stockage
Previous=Précédent
Preweaned=Pre-sevrage
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=L'aliment d'allaitement est convenablement mélangé et la consommation est adaptée
Preweaned_CleanAndDryPen=Case propre et sèche
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Nettoyer et désinfecter le matériel d'alimentation des veaux entre chaque repas
Preweaned_ConsistentFeedingTimesAndProtocols=Horaires d'alimentation et protocole constant
Preweaned_EvidenceOfScoursOrPneumonia=Preuve de diarrhée ou de pneumonie
Preweaned_ForageAvailability=Fourrage disponible
Preweaned_FreeChoiceCleanWaterIsAvailable=Accès à l'abreuvoir et qualité de l'eau satisfaisants
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Accès à l'abreuvoir et qualité de l'eau satisfaisants
Preweaned_SizeOfPenAadequatePerHeifer=Taille de la case adaptée pour chaque génisse
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Période de sevrage avec au moins 1Kg d'aliment par jour
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Enclos bien ventilé et sans courant d'air sur les mollets
PricingMatrixEditViewModel.Cancel=Annuler
PricingMatrixEditViewModel.Save=Sauvegarder
PricingMatrixEditViewModel.Title=Détail grille de paiement par critère
PricingMatrixPickListViewModel.PricingMatrix=Matrice de prix
PricingMatrixViewModel.Amount=Quantité (1000 cellules / ml)
PricingMatrixViewModel.AmountCFU=Quantité (1000 cfu / ml)
PricingMatrixViewModel.New=Nouveau
PricingMatrixViewModel.Title=Modifier la matrice
Prince_Edward_Island=Ãle-du-Prince-Ãdouard
Privacy_Statement=Déclaration de confidentialité
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Monnaies
ProcessorCurrencyPickListViewModel.Title=Monnaies
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Comparaison de procédure de traite
ProductivityToolsViewModel.MilkRevenueAnalysis=Analyse des revenus de production laitière
ProductivityToolsViewModel.MilkSoldEvaluation=Evaluation des ventes de lait
ProductivityToolsViewModel.ProductivityTitle=Outils de productivité
ProductivityToolsViewModel.ProductivityTools=OUTILS
ProductivityToolsViewModel.VisitNotebook=Cahier de notes
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=Annuler
PromptOK=Ok
PromptPermissionMsg=Sans cette autorisation, certaines fonctionnalités peuvent ne pas fonctionner. Êtes-vous certain de vouloir refuser cette autorisation ?
PromptPermissionMsgIMSure=Je suis certain
PromptPermissionMsgRetry=réessayer
PromptPermissionTitle=Permission refusée
ProspectProfileViewModel.DeleteProspect=Supprimer le prospect
ProspectProfileViewModel.DeleteProspectPrompt=Êtes-vous sûr de vouloir supprimer ce prospect? Le prospect, le site et les informations de la visite en cours seront perdus.
ProspectProfileViewModel.MainHeading=SITES
ProspectProfileViewModel.NewSite=Ajouter un nouveau site
ProspectProfileViewModel.ProspectInfo=Info Prospect
ProspectProfileViewModel.ProspectTitle=Détails de Prospect
ProspectsViewModel.NewProspect=Ajouter un prospect
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=Provimi
ProvimiUS=Provimi US
PublishVisit=Publié
Puducherry=PudichÃ©ry
Puebla=Puebla
Puerto_Rico=Porto Rico
Punjab=Punjab
Purina=Purina
Qatar=Qatar
Qinghai=Qinghai
QuarterPointScale=0,25 points
Quarterly=Trimestriel
Quebec=QuÃ©bec
Queensland=Queensland
QuerÃ©taro=Quer Ã© Taro
QuestionTableTitle=Question {0} de {1}
QuestionViewModel.Baleage=Enrubannage
QuestionViewModel.BunkersAndPiles=Silo couloir
QuestionViewModel.Close=Fermer
QuestionViewModel.Harvest=Récolte
QuestionViewModel.MaintainingForageQuality=Maintenir la qualité des fourrages
QuestionViewModel.SilageBags=Boudins d'ensilage 
QuestionViewModel.SurveyOfForages=Etude des fourrages
QuestionViewModel.TowerSilos=Silos tour
QuestionViewModel.VisitNotebook=Cahier de notes
Quintana_Roo=Quintana Roo
ROL=ROL
RUB=Russie (₽‎ RUB)
Ragusa=Ragusa
Rajasthan=Rajasthan
Rationcost=Coût de la ration ({0})
Ravenna=Corbeau
ReadyToMilkChartViewModel.Current=Actuel
ReadyToMilkChartViewModel.DeathLoss=Mammites
ReadyToMilkChartViewModel.DisorderGraphTitle=Coût annuel des troubles métaboliques / VL
ReadyToMilkChartViewModel.DisplacedAbomasum=Caillette
ReadyToMilkChartViewModel.Dystocia=Dystocie
ReadyToMilkChartViewModel.Ketosis=Acétonémie
ReadyToMilkChartViewModel.Metritis=Métrite
ReadyToMilkChartViewModel.MilkFever=Fièvre du lait
ReadyToMilkChartViewModel.RetainedPlacenta=Rétention
ReadyToMilkChartViewModel.Title=Graphiques Ready2Milk™ 
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Index Ready2Milk™ 
ReadyToMilkInputViewModel.Back=Précédent
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Variation de la NEC au tarissement vs 21 JEL
ReadyToMilkInputViewModel.CloseUp=Vaches en prépa vêlage
ReadyToMilkInputViewModel.ComfortCloseUp=Confort en prépa vêlage
ReadyToMilkInputViewModel.ComfortDiet=Confort 0-60 JEL
ReadyToMilkInputViewModel.CostExtraDaysOpen=Coût par jour d'IVV supplémentaire
ReadyToMilkInputViewModel.CudChewingDiet=Rumination des vaches en début de lactation
ReadyToMilkInputViewModel.DeadCowsOrCulled=Nombre de vaches mortes ou réformées à cause des troubles de santé
ReadyToMilkInputViewModel.DisplacedAbomasum=Caillette
ReadyToMilkInputViewModel.Dystocia=Dystocie
ReadyToMilkInputViewModel.FreshCows=Vaches en début de lactation
ReadyToMilkInputViewModel.Health=SANTE
ReadyToMilkInputViewModel.HealthDesc=Entrer le nombre de vaches en début de lactation et le nombre de troubles métaboliques observés pendant la période d'évaluation. Cela sera converti en coût annuel dans l'onglet résultats.
ReadyToMilkInputViewModel.HealthRecords=Santé du troupeau
ReadyToMilkInputViewModel.Herd=RENSEIGNEMENTS SUR LE TROUPEAU
ReadyToMilkInputViewModel.Ketosis=Acétonémie
ReadyToMilkInputViewModel.LocomotionScore=Score de Locomotion
ReadyToMilkInputViewModel.Mastitis=Mammites 0-60 JEL
ReadyToMilkInputViewModel.Metritis=Métrite
ReadyToMilkInputViewModel.MilkFever=Fièvre du lait
ReadyToMilkInputViewModel.MilkPrice=Prix du lait
ReadyToMilkInputViewModel.MilkYield=PL 15-60 JEL
ReadyToMilkInputViewModel.Next=Suivant
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Niveau de stress thermique 0-60 JEL
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Niveau de stress thermique en prépa vêlage
ReadyToMilkInputViewModel.PrematureCalvings=Nombre de vélages prématurés  (au moins 2 semaines avant la date prévue)
ReadyToMilkInputViewModel.ReplacementCowCost=Coût de revient d'une génisse (€/VL/an)
ReadyToMilkInputViewModel.RetainedPlacenta=Rétention Placentaire
ReadyToMilkInputViewModel.RumenFill=Remplissage du rumen
ReadyToMilkInputViewModel.SccFirstTest=Taux de cellules au 1er contrôle (x1000/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Ration prépa vêlage
ReadyToMilkInputViewModel.SpecificDiet=Supplémentation spécifique 0-60 JEL
ReadyToMilkInputViewModel.Title=Données Ready2Milk™ 
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Nombre de vêlages / an
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Nombre total de vaches en début de lactation
ReadyToMilkListViewModel.Title=Index Ready2Milk™
ReadyToMilkMasterViewModel.Charts=Graphiques
ReadyToMilkMasterViewModel.Inputs=Données
ReadyToMilkMasterViewModel.MastitisNotPresent=Si aucune mammite, entrez la valeur 0.
ReadyToMilkMasterViewModel.Outputs=Résultats
ReadyToMilkMasterViewModel.Title=Index Ready2Milk™
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Index Ready2Milk™
ReadyToMilkOutputViewModel.Title=Résultats Ready2Milk™ 
RecommendedTLCSettings=Objectif de longueur de coupe
RefreshTokenFailed=Session expirée, veuillez vous reconnecter
Reggio_Calabria=Reggio Calabre
Reggio_Emilia=Reggio Emilia
RemovedAndMeasured=Enlevés et mesurés
RemovedOnly=Seulement enlevés
Report=Rapport
Report.Analysis.Type=Type d'analyse
Report.Animal.Analysis=Analyse animale
Report.Animal.Tag.Name=Pièce d'identité animale
Report.AvgRumenFillScore=Moyenne des résultats du RDR (calculée)
Report.BCS.EvalDataTitle=Données d'évaluation BCS calculées
Report.BCS.LactationStages=Étapes de lactation
Report.BCS.Max=État corporel maximum
Report.BCS.MilkHeadDay=Lait / hd / jour
Report.BCSAvg=État corporel moyen
Report.Bcs=État corporel
Report.Bcs.ChartName=Score de condition corporelle - Animal {0}
Report.Bcs.HerdAnalysis.ChartName=BCS vs lait
Report.Bcs.Milk=Lait
Report.Bcs.Min=État corporel minimum
Report.Calving.Date=Vêlage
Report.Cargill.Report=Cargill rapport
Report.Chewing=d'animaux qui ruminent
Report.Chews=Mâcher
Report.CudChewing.EvalDataTitle=Données d'évaluation CUD CUD CUD
Report.CudChewingPercentage=Rumination, en %
Report.CudChewingPercentage.Vs.LactStages=Rumination, en %
Report.EvalDataTitle=Données d'évaluation calculées
Report.ForagePennState=Penn State -Fourrage
Report.General.Comments=GÃ©nÃ©ral Commentaires
Report.GoalCudChewingPercentage=Objectifs Rumination, en %
Report.Heatstress.Dmi.Adjustment=Ajustement DMI
Report.Heatstress.Energy.Equivalent.Milk.Loss=Perte de lait équivalente énergétique ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Admission estimée à la matière sèche ({0})
Report.Heatstress.Intake.Adjustment=Réglage de l'admission
Report.Heatstress.Legend=Légende
Report.Heatstress.Legends=LÃ©gendes
Report.Heatstress.Loss.Of.Energy.Consumed=Perte d'énergie consommée (MCAL)
Report.Heatstress.Mild.Moderate.Stress=Stress doux - modéré
Report.Heatstress.Mild.Moderate.Stress.Message=La respiration dépasse 75 bpm | La température rectale dépasse 39  U2103 (102.2  U2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Perte de valeur de lait (par mois) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Perte de valeur de lait (par jour) ({0})
Report.Heatstress.Moderate.Severe.Stress=Stress modéré - grave
Report.Heatstress.Moderate.Severe.Stress.Message=La respiration dépasse 85 bpm | La température rectale dépasse 40  U2103 (104  U2109)
Report.Heatstress.Reduction.In.Dmi=Réduction de DMI ({0})
Report.Heatstress.Relative.Humidity=Humidité relative (%)
Report.Heatstress.Severe.Stress=Stress sévère
Report.Heatstress.Severe.Stress.Message=La respiration dépasse 120-140 bpm | La température rectale dépasse 41  U2103 (106  U2109)
Report.Heatstress.Stress.Threshold=Seuil de stress
Report.Heatstress.Stress.Threshold.Message=La respiration dépasse 60 bpm | Pertes de repro détectables | La température rectale dépasse 38,5  U2103 (101.3  U2109)
Report.Heatstress.Temperature=Température
Report.Heatstress.Temperature.In.Celcius=Température  U2103
Report.Heatstress.Temperature.In.Farenhiet=Température  U2109
Report.Heatstress.TemperatureHumidityIndex=Indice d'humidité de la température
Report.Herd.Analysis.CudChewingPercentage=Rumination, en %
Report.Locomotion.HerdAnalysis.ChartName=Pourcentages de score de locomotion
Report.LocomotionScore.X.Axis=Score de locomotion
Report.LocomotionScore.Y.Axis=Pour cent%
Report.LocomotionScore.chartName=Score de locomotion - Animal {0}
Report.No.OfChews=\# de coups de machoires
Report.No.OfChewsPerRegurgitation=Nombre de mâtes par régurgitation
Report.NoOfChews.Vs.LactStages=\# de coups de machoires
Report.Not.Chewing=Ne pas mÃ¢cher
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Temps disponible pour le repos
Report.PenTimeBudget.TimeAvailableForResting.Label=Heures
Report.PenTimeBudgetTimeRemaining=Temps restant
Report.PenTimeBudgetTimeRequired=Temps requis
Report.Pentime.Budget.Hours=Heures
Report.PercentageOnScreen=Quantité sur le tamis (%)
Report.RumenHealthManureScreening.Bottom=Bas
Report.RumenHealthManureScreening.BottomGoalMax=But inférieur max
Report.RumenHealthManureScreening.BottomGoalMin=Bott-but Min
Report.RumenHealthManureScreening.Middle=Milieu
Report.RumenHealthManureScreening.MiddleGoalMax=But intermédiaire max
Report.RumenHealthManureScreening.MiddleGoalMin=Objectif moyen min
Report.RumenHealthManureScreening.Top=Haut
Report.RumenHealthManureScreening.TopGoalMax=Top but max
Report.RumenHealthManureScreening.TopGoalMin=Top objectif Min
Report.SheetName=MaÃ®tre de rapport
Report.Tool.Details=Outils DÃ©tails
Report.Tool.Name=Nom d'outil
Report.Visit.Date=Date de visite
Report.Visit.Report=Visite rapport
Report.Visit.name=Nom de visite
Report.goalChews=Mâcher des objectifs
Report.locomotionScore.Pen.Analysis.ChartName=Catégories vs dates de visite
ReportDate=Date du Rapport
ReportPDFNote=Note\:  
Reset_Database=Réinitialiser la base de données
Resources=Ressources
ResourcesViewModel.Title=Ressources Fouragères
ResourcesViewModel.VisitNotebook=Cahier de notes
RetainedPlacenta=Rétention Placentaire
Reunion=RÃ©union
Revenue=Revenu
RevenueEditComparisonValuesViewModel.CurrentSCC=Comptage cellulaire actuel (cellules / {0})
RevenueEditComparisonValuesViewModel.DownResponse=Réponse en lait ({0} / vache / jour)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Modifier les valeurs de comparaison
RevenueEditComparisonValuesViewModel.MilkChange=Variation de la production (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Prix du lait (euro/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Production de lait
RevenueEditComparisonValuesViewModel.NumOfCows=Nombre de vaches
RevenueEditComparisonValuesViewModel.PotentialSCC=Comptage cellulaire potentiel  (cellules/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Scénario 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Scénario 2
RevenueEditComparisonValuesViewModel.Title=Modifier les valeurs de comparaison
RevenueInputViewModel.ComparisonValues=Valeurs de comparaison
RevenueInputViewModel.Edit=Modifier
RevenueInputViewModel.ScenarioOne=Scénario 1
RevenueInputViewModel.ScenarioTwo=Scénario 2
RevenueInputViewModel.Title=Comparaison de procédures de traite  - Données
RevenueLossDay=Perte de revenus ({0} / Jour)
RevenueLossYear=Perte des revenus ({0} / Année)
Rheinland=RhÃ©nanie
Rhode_Island=Rhode Island
Rieti=Rieti
Rimini=Rimini
Rio_Grande_do_Norte=Grande riviÃ¨re nord
Rio_Grande_do_Sul=Rio Grande do sul
Rio_de_Janeiro=Rio de Janeiro
Robot=Robot
Romania=Roumanie
Rome=Rome
RondÃÂ´nia=RondÃ¢mes
Roraima=Roraima
Roscommon=Roscommon
Rovigo=Rovigo
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Analyse des groupes
RumenHealthBodyConditionLandingViewModel.Pens=Lots
RumenHealthBodyConditionLandingViewModel.Resources=Ressources
RumenHealthBodyConditionLandingViewModel.Title=Etat corporel
RumenHealthEditManureScoresViewModel.Close=Fermer
RumenHealthEditManureScoresViewModel.Count=Nombre
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Veuillez compter le nombre de vaches
RumenHealthEditManureScoresViewModel.NumOfCows=Nombre de vaches
RumenHealthEditManureScoresViewModel.NumberOfCows=Nombre de vaches
RumenHealthEditManureScoresViewModel.VisitNotebook=Cahier de notes
RumenHealthLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthLandingViewModel.PenAnalysis=Analyse des lots
RumenHealthLandingViewModel.Pens=Lots
RumenHealthLandingViewModel.Title=Santé Ruminale - Rumination
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthLocomotionLandingViewModel.PenAnalysis=Analyse du lot
RumenHealthLocomotionLandingViewModel.Pens=Lots
RumenHealthLocomotionLandingViewModel.Resources=Ressources
RumenHealthLocomotionLandingViewModel.Title=Locomotion
RumenHealthManureLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthManureLandingViewModel.PenAnalysis=Analyse des lots
RumenHealthManureLandingViewModel.Pens=Lots
RumenHealthManureLandingViewModel.Resources=Ressources
RumenHealthManureLandingViewModel.Title=Santé Ruminale - score des bouses
RumenHealthManureMasterViewModel.Inputs=Données
RumenHealthManureMasterViewModel.Results=Résultats
RumenHealthManureMasterViewModel.RumenHealthManureScore=Evaluation des bouses
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Evaluation des bouses pour la santé du rumen
RumenHealthManureMasterViewModel.VisitNotebook=Cahier de notes
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Score moyen
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Date
RumenHealthManureScoresResultsViewModel.PercentPen=%
RumenHealthManureScoresResultsViewModel.SelectedDates=Sélectionnez les dates
RumenHealthManureScoresResultsViewModel.Title=Evaluation des bouses - Résultats
RumenHealthManureScoresViewModel.AnimalsObserved=Nombre d'animaux évalués
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Score moyen des bouses (calculé)
RumenHealthManureScoresViewModel.Edit=Modifier
RumenHealthManureScoresViewModel.ManureScore=Evaluation des bouses
RumenHealthManureScoresViewModel.PercentOfPen=%
RumenHealthManureScoresViewModel.ScoreCategory=Categorie d'évaluation des bouses
RumenHealthManureScoresViewModel.StdDevCalculated=Ecart-type (calculé)
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=CALCULATEURS
RumenHealthPenCudCalculatorViewModel.CudChewing=Rumination
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Nombres de vaches qui ruminent
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Nombre de mastication
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Nombre de mastications par cycle de rumination
RumenHealthTMRLandingViewModel.HerdAnalysis=Analyse du troupeau
RumenHealthTMRLandingViewModel.PenAnalysis=Analyse du lot
RumenHealthTMRLandingViewModel.Pens=Lots
RumenHealthTMRLandingViewModel.Title=Santé ruminale - penn state
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Poids en gramme (balance)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Poids du tamis (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Objectif max  (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Objectif min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Objectifs
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Milieu 1(8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Milieu 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Milieu 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Nom de la ration mélangée testée avec le Pennstate
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=Score du test Penn State de la ration mélangée
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Poids du tamis
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Poids
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Haut (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Tamis
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Ajouter un score
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Score moyen
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Actuel
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Poids en gramme (balance)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Objectif max  (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Objectif au milieu 1 (8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Objectif au milieu 2 (1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Objectif au milieu 2 (4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Objectif min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Objectifs
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Objectif en haut (19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Objectif au fond
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Max
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Milieu 2 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Milieu 3
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Milieu 3 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Min
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Taille des particules (% sur les tamis)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Quantité sur le tamis (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Ecart type
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Information sur le score des particules de la ration mélangée
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=Evaluation - Taille des particules dans la ration mélangée
RumenHealthTMRParticleScorePenTableInputViewModel.Title=Evaluation - Taille des particules dans la ration mélangée
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Haut (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Fond
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Données
RumenHealthTMRPenScorerTableMasterViewModel.Results=Résultats
RumenHealthTMRPenScorerTableMasterViewModel.Title=Taille des particules
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=Aucun sélectionné
RumenHealthTMRSelectPenViewModel.NoScorerSelected=Un critère doit  être sélectionné avant d'évaluer le lot.
RumenHealthTMRSelectPenViewModel.SelectPen=Nombre des lots (vaches en lactation et taries seulement)
RumenHealthTMRSelectPenViewModel.SelectScorer=Sélectionner votre penn state
RumenHealthTMRSelectPenViewModel.Title=Taille des particules
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=Aucun sélectionné
RumenHealthTMRSelectScorerViewModel.FooterText=Seulement un critère d'évaluation peut être utilisé par visite.
RumenHealthTMRSelectScorerViewModel.SelectScorer=Sélectionner votre penn state
RumenHealthTMRSelectScorerViewModel.Title=Taille des particules
Russia=Russie
Russian_Federation=FÃ©dÃ©ration Russe
Rwanda=Rwanda
SAR=Arabie Saoudite (﷼ SAR)
SCCPremiumDeduction=Bonus / pénalité sur les cellules ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Surinam ($ SRD)
Saint_BarthÃ©lemy=Saint BarthÃ©lemy
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Ascension et Tristan da Cunha
Saint_Kitts_and_Nevis=Saint-Christophe-et-NiÃ©vÃ¨s
Saint_Lucia=Sainte-Lucie
Saint_Martin_(French_part)=Saint Martin (partie franÃ§aise)
Saint_Pierre_and_Miquelon=Saint Pierre and Miquelon
Saint_Vincent_and_the_Grenadines=Saint-Vincent-et-les-Grenadines
Salerno=Salerne
Samoa=Samoa
San_Luis_PotosÃ­=San Luis PotosÃ£
San_Marino=Saint Marin
Santa_Catarina=Santa Catarina
Sao_Tome_and_Principe=Sao tome et principe
Saskatchewan=Saskatchewan
Sassari=Sassari
SaudiArabia=Arabie Saoudite
Saudi_Arabia=Arabie Saoudite
Savona=Savona
Schleswig=Schleswig
ScorecardPrompt=Veuillez compléter les questions suivantes pour voir la fiche d'évaluation.
Screen=tamis -
ScreenNew=tamis - nouveau
ScreenOld=tamis - ancien
Search=Chercher
SegmentViewModel.SegmentTitle=SÉLECTIONNEZ UN SEGMENT
SegmentViewModel.Title=Détails
Select=Sélectionner
SelectCurrencyViewModel.Title=Choisir une monnaie
SelectDates=Sélectionnez les dates
SelectFeedingSystemViewModel.SelectFeedingSystem=Sélectionner le système d'alimentation
SelectFeedingSystemViewModel.Title=Configuration du groupe
SelectForageImprovement=Merci de sélectionnez seulement 12 améliorations parmi la liste
SelectHousingSystemViewModel.SelectHousingSystem=Sélectionner le système de logement
SelectHousingSystemViewModel.Title=Configuration du groupe
SelectImprovement=Merci de sélectionner seulement 10 améliorations dans la liste.
SelectMatrix=Sélectionner un système de prime
SelectMilkingSystemViewModel.NoneSelected=Aucun sélectionné
SelectMilkingSystemViewModel.SelectMilkingSystem=Selectionner un système de traite
SelectMilkingSystemViewModel.Title=Configurer le site
SelectOnlyThreeNotes=Sélectionner seulement 3 notes dans l'outil.
SelectOnlyTwoNotes=Sélectionner seulement 2 notes dans l'outil.
SelectPen=Sélectionner une ration pour le nouveau lot
SelectProcessor=Sélectionner une laiterie
SelectProcessorViewModel.COMPONENT=Composantes
SelectProcessorViewModel.CONCENTRATION=Concentration
SelectProcessorViewModel.Edit=Modifier
SelectProcessorViewModel.MilkProcessors=Laiterie
SelectVisitComparison=Sélectionnez les visites à comparer
SemiAnnually=Semestriellement
Semiconfinamento=Semiconfinamento
Send=Envoyer
Senegal=SÃ©nÃ©gal
Seoul=SÃ©oul
Serbia=Serbie
Sergipe=Sergipe
SettingsViewModel.Imperial=Impérial
SettingsViewModel.Metric=Métrique
SettingsViewModel.Milk_Processor_Set_Up=Configuration de la laiterie
SettingsViewModel.More_Settings=Plus de paramètres
SettingsViewModel.Select_Unit_Of_Measure=Sélectionner une unité de mesure
Severe=Sévère
Seychelles=les Seychelles
Shaanxi=Shaanxi
Shandong=Shandong
Shanghai=Shanghai
Shanxi=Shanxi
ShortDryPeriod=Tarissement court
ShowEulaViewModel.Accept=Accepter
ShowEulaViewModel.ConfirmationNo=Non
ShowEulaViewModel.ConfirmationText=Êtes-vous d'accord avec les termes du Contrat de Licence d'utilisateur dans l'Application Mobile?
ShowEulaViewModel.ConfirmationTitle=Confirmation
ShowEulaViewModel.ConfirmationYes=oui
ShowEulaViewModel.Decline=Décliner
ShowEulaViewModel.Eula=Contrat de licence utilisateur final
ShowEulaViewModel.EulaError=Le contrat de licence de l'utilisateur final (CLUF) ne peut pas être affiché. Connectez-vous à Internet avant d'essayer à nouveau.
ShowEulaViewModel.EulaScreenTitle=Licence de l'utilisateur final
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Déclaration de confidentialité
ShowSyncStatusViewModel.GetAccounts=Données sur le compte reçues
ShowSyncStatusViewModel.GetNotes=Données sur les notes reçues
ShowSyncStatusViewModel.GetVisits=Données sur la visite reçues
ShowSyncStatusViewModel.Title=Résumé de la synchronisation des données
Sichuan=Sichuan
Siena=Sienne
Sierra_Leone=Sierra Leone
Sikkim=Sikkim
SilageBags=Enrubannage
SilageBags_BagsPlacedOnStableWellManagedSurface=Les boudins sont placés sur une surface stable et bien gérée toute la saison?
SilageBags_BonusSecureCoverIsUsed=Une couche de sécurité est utilisée?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Front d'attaque propre et bien travaillé, sans signe de détérioration de l'ensilage?
SilageBags_FaceRemovalRate=Avancement quotidien\:
SilageBags_InspectedForPestHoleDamageRepairOnBasis=les boudins sont contrôlés régulièrement et les trous sont réparés?
SilageBags_PorosityScoresConsistently=tassage homogène
SilageBags_TrashVegRodentControlledAroundBags=les déchets, la végétation et les rongeurs sont maitrisés autour des boudins?
SilagePrevention1st=Incontournables d'un ensilage réussi
Sinaloa=Sinaloa
Singapore=Singapour
Sint_Maarten_(Dutch_part)=Sint Marthes (partie nÃ©erlandaise)
Site-Not-Synced-To-Lift=Le site n'a pas été synchronisé avec LIFT ; veuillez contacter l'administrateur pour une résolution
SiteDetailViewModel.AnimalInputsSite=Données des animaux
SiteDetailViewModel.DairyEnteligenReport=Rapport Dairy Enteligen
SiteDetailViewModel.Detailed=Détaillé
SiteDetailViewModel.DietInputsSiteLactating=Données de la ration, Site (Vaches en lactation)
SiteDetailViewModel.DownloadingVisit=Téléchargement visite...
SiteDetailViewModel.GeneralCustomerSiteSetup=Configuration générale du site
SiteDetailViewModel.GetReportMsg=Téléchargement du rapport en cours ...
SiteDetailViewModel.MainHeading=Visites
SiteDetailViewModel.NetworkErrorMessage=Il n'y a pas de réseau disponible
SiteDetailViewModel.NetworkErrorMessageTitle=Erreur réseau
SiteDetailViewModel.NewVisit=Nouvelle visite
SiteDetailViewModel.ReportDownloadTimeout=Impossible de télécharger le fichier, essayez quand vous aurez une meilleure connexion
SiteDetailViewModel.ReportNotAvailable=Le rapport n'est pas disponible pour téléchargement.
SiteDetailViewModel.ReportNotAvailableTitle=Statut
SiteDetailViewModel.Reports=Rapport Dairy Enteligen
SiteDetailViewModel.Resources=Ressources
SiteDetailViewModel.SiteSetup=Configuration du site
SiteDetailViewModel.Summary=Sommaire
SiteDetailViewModel.Title=Détails du site
SiteDetailViewModel.VisitDownloadPrompt=Visite non téléchargée, voulez-vous essayer de télécharger la visite ?
SiteDetailViewModel.VisitNotDownloaded=Visite non téléchargée
SiteDetailViewModel.VisitUnavailable=Donnée de visite indisponible.
SiteDetailsResourcesViewModel.Title=Ressources
SiteDetailsSetupViewModel.AnimalInputsSite=Données des animaux
SiteDetailsSetupViewModel.AsFedIntake=Consommation MB ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=Comptage Bactérien (1,000cfu/mL)
SiteDetailsSetupViewModel.Continue=Continuer
SiteDetailsSetupViewModel.CurrentMilkPrice=Prix du lait actuel ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Jour de lactation
SiteDetailsSetupViewModel.Delete=Supprimer
SiteDetailsSetupViewModel.DietInputsSiteLactating=Données de la ration, Site (Vaches en lactation)
SiteDetailsSetupViewModel.DietSetup=Configuration – Ration
SiteDetailsSetupViewModel.Diets=ration
SiteDetailsSetupViewModel.DryMatterIntake=Matière sèche ingérée
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=Configuration générale du site
SiteDetailsSetupViewModel.InfoSiteSetup=Ajoutez un nouveau site pour chaque emplacement de l’exploitation agricole appartenant à un client. Vous devez créer au moins 1 site pour accéder aux outils Dairy Enteligen. 
SiteDetailsSetupViewModel.LactatingAnimals=Vaches en lactation
SiteDetailsSetupViewModel.MilkFatPercent=Matière Grasse %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Autres Matière utiles du Lait %
SiteDetailsSetupViewModel.MilkProteinPercent=Protéine %
SiteDetailsSetupViewModel.MilkYield=Production de lait({0})
SiteDetailsSetupViewModel.MilkingSystem=Système de Traite
SiteDetailsSetupViewModel.NameNotUnique=Un site nommé "{0}" existe déjà. Les noms doivent être uniques.
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=Densité énergétique de la ration (en UFL)
SiteDetailsSetupViewModel.NewSite=Nouveau Site
SiteDetailsSetupViewModel.NullSiteName=Pour continuer, merci de configurer au moins un lot. Le nom du site, le prix du lait, le système de traite et le lot sont des champs obligatoires. 
SiteDetailsSetupViewModel.NumberOfStalls=Nombre de place dans la salle de traite
SiteDetailsSetupViewModel.PenSetup=Configuration du lot
SiteDetailsSetupViewModel.Pens=Groupes
SiteDetailsSetupViewModel.RationCost=Coût d'alimentation / vache (euro)
SiteDetailsSetupViewModel.SiteMandatoryFields=Pour continuer, merci de configurer au moins un lot. Le nom du site, le prix du lait, le système de traite et le lot sont des champs obligatoires. 
SiteDetailsSetupViewModel.SiteName=Nom du Site
SiteDetailsSetupViewModel.SiteSetup=Configurer le site
SiteDetailsSetupViewModel.SomaticCellCount=Cellules Somatiques (1,000 cellules/mL)
SiteDetailsSetupViewModel.Title=Détails de site
SiteDetailsSetupViewModel.WeightImperialCWT=CWT
SiteVisitSummary=Rapport sommaire du site
SiteVisitSummaryReport=Rapport sommaire du site
SixToEightLayers=6 à 8 couches
SixToTwelveHours=7 à 12 heures
SixToTwelveInches=15 à 30 cm
Sligo=Sligo
Slovakia=Slovaquie
Slovenia=SlovÃ¨ne
Solomon_Islands=Les Ã®les Salomon
Somalia=Somalie
SomanticCellCount=Comptage cellulaire
SomaticCellPerML=Cellules  (cellules/mL)
Sondrio=Sondrio
Sonora=Sonora
SouthAfrica=Afrique du Sud
SouthKorea=Coree du Sud
South_Africa=Afrique du Sud
South_Australia=Australie du Sud
South_Carolina=Caroline du Sud
South_Dakota=Dakota du Sud
South_Georgia_and_the_South_Sandwich_Islands=GÃ©orgie du Sud et Ã®les Sandwich du Sud
South_Sudan=Soudan du sud
Spain=Espagne
Sri_Lanka=Sri Lanka
StandardDeviationScoreTitle=Ecart type
StartDate=Date de début
StatusArchived=Archivé
StatusCompleted=complet
StatusInProgress=En cours
StdDevCalculated=Ecart type (calculé)
Steer=Boeuf
StorageCalculators=Outil cubage silo
StrategyToReduceDisplacedAbomasum=Caillette
StrategyToReduceDisplacedAbomasumDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence des déplacements de la caillette (DGC)&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;La stratégie de prévention repose sur la gestion des facteurs de risque car il n'existe actuellement aucune cause directe claire à cette condition.&lt;/p&gt;&lt;p&gt;La prévention comprend la nutrition et la gestion ainsi que la prise en charge des maladies concomitantes \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Contrôle des facteurs de risque nutritionnels \:&lt;ul&gt;&lt;li&gt;Éviter des vaches grasses (idéalement 3,00 NEC au tarissement et au vêlage)&lt;/li&gt;&lt;li&gt;Fournir assez de fourrage avec une quantité suffisante de NDF&lt;/li&gt;&lt;li&gt;Gérer les caractéristiques physiques de la ration&lt;/li&gt;&lt;li&gt;Faire attention aux exigences en minéraux&lt;/li&gt;&lt;li&gt;Éviter les autres troubles métaboliques tels que l'hypocalcémie ainsi que les maladies infectieuses qui pourraient diminuer la consommation de matière sèche&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Meilleures pratiques de gestion \:&lt;ul&gt;&lt;li&gt;Assurer une bonne CMS chez les vaches fraîches, en particulier pendant les heures et les jours qui suivent le vêlage&lt;/li&gt;&lt;li&gt;Bien gérer l'accès à l'auge&lt;/li&gt;&lt;li&gt;Augmenter le confort des vaches, réduire les facteurs de stress&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceDystocia=Dystocie
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de la dystocie&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Pour aider à prévenir la dystocie \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;S'assurer que les génisses sont inséminées à l'âge et au poids corporel appropriés&lt;/li&gt;&lt;li&gt;Sélection des taureaux sur la base de la facilité de vêlage&lt;/li&gt;&lt;li&gt;Améliorer la formation du personnel concernant le bon moment et les méthodes d'intervention pendant le vêlage ainsi que des méthodes appropriées pour prendre soin des veaux nouveau-nés en détresse&lt;/li&gt;&lt;li&gt;Réviser la ration en suivant les exigences OptiLac pour les vaches et les génisses à tous les stades en se concentrant sur \:&lt;ul&gt;&lt;li&gt;L'énergie pour maintenir l'évaluation de la condition corporelle et la croissance du fœtus&lt;/li&gt;&lt;li&gt;La prévention du sur-conditionnement des vaches au vêlage&lt;/li&gt;&lt;li&gt;La gestion du risque d'hypocalcémie au sein du troupeau&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=Stratégies pour réduire l'incidence
StrategyToReduceKetosis=Acétonémie
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de la cétose&lt;/strong&gt;&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;Assurer un confort adéquat aux vaches (litière, contrôle du stress thermique et ventilation, limiter les facteurs de stress et la surdensité d'élevage, etc.)&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="2"&gt;&lt;li&gt;Équilibrer les apports alimentaires en fonction des nutriments et des caractéristiques physiques selon les recommandations Provimi. Consulter l'ensemble des produits s'adressant aux vaches en période de transition pour des formulations spécifiques de produits développés pour la prévention de la cétose. Utiliser des fourrages de bonne qualité qui augmenteront l'apport alimentaire et permettront de sécurité la ration.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="3"&gt;&lt;li&gt;Surveiller le changement de NEC (évaluation de la note d'état corporelle) entre la période de tarissement et le vêlage\: Début de la période tarie à 3,00 NEC et maintenir le NEC pendant la période sèche pour éviter une mobilisation excessive des lipides autour de la période du vêlage.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="4"&gt;&lt;li&gt;Mettre en œuvre un protocole de santé spécifique développé pour les vaches ayant vêlée récemment afin de détecter et de prévenir les troubles métaboliques et les maladies infectieuses. Surveiller l'apport alimentaire, le remplissage du rumen et la rumination.&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=Mammites
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence des mammites&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Les micro-organismes qui causent le plus souvent les mammites peuvent être divisés en deux catégories principales\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Agents pathogènes contagieux qui se propagent principalement de vache à vache pendant la traite &lt;em&gt;(c'est-à-dire streptococcus aureus et staphylocoque doré)&lt;/em&gt;&lt;/li&gt;&lt;li&gt;Pathogènes environnementaux provenant de l'environnement des vaches laitières  &lt;em&gt;(i.e. E. coli&amp;nbsp;et&amp;nbsp;Strep. Uberis)&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Le traitement doit être adapté selon le type de bactéries responsable de la mammite.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Contrôle de la mammite contagieuse\:&lt;/strong&gt;\: il n'y a pas de solution simple contre les infections pour tous les pathogènes. Les étapes suivantes aideront\:&lt;ul&gt;&lt;li&gt;Mettre l'accent sur l'hygiène autour et pendant la traite y compris le trempage des trayons&lt;/li&gt;&lt;li&gt;Traire les vaches infectées en dernier&lt;/li&gt;&lt;li&gt;Effectuer l'entretien régulier de la machine à traire&lt;/li&gt;&lt;li&gt;Réviser la ration des vaches taries à l'aide des recommandations Provimi pour assurer un apport adéquat en nutriments (énergie, antioxydant) et soutenir ainsi la fonction immunitaire&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Contrôler les mammites d'environnement&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;Assurer une litière propre et sèche, un bâtiment bien ventilé&lt;/li&gt;&lt;li&gt;Optimiser la densité d'élevage&lt;/li&gt;&lt;li&gt;Hygiène générale de la mamelle&lt;/li&gt;&lt;li&gt;La routine de la traite&lt;/li&gt;&lt;li&gt;Réviser la ration des vaches taries à l'aide des recommandations Provimi pour assurer un apport suffisant en nutriments (énergie, antioxydant) afin de soutenir les fonctions immunitaires&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=Métrite
StrategyToReduceMetritisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence des métrites&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Les facteurs de risque des métrites comprennent la rétention placentaire, les lésions de l'appareil génital dues à un vêlage difficile, un protocole de vêlage inadéquat, une zone de vêlage impropre, une carence nutritionnelle comme la vitamine E ou des carences en sélénium et des vaches trop grasses.&lt;/p&gt;&lt;p&gt;Surveiller les points suivants, par ordre décroissant d'importance\:&amp;nbsp;\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Pratiques de vêlage&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Les employés transportent-ils des bactéries au niveau de l'appareil génital lorsqu'ils aident au vêlage ?&lt;/li&gt;&lt;li&gt;Les vaches sont-elles aidées trop tôt ou trop tard ?&lt;/li&gt;&lt;li&gt;Les veaux sont-ils tirés trop souvent ?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Les pratiques de traitement&lt;br /&gt;&lt;ul&gt;&lt;li&gt;Comment sont traitées les vaches atteintes de rétention placentaire (RP) ou de métrite ?&lt;/li&gt;&lt;li&gt;Existe-t-il un risque de transporter des bactéries d'environnement extérieur ou du vagin dans l'utérus à ce moment-là ?&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="3"&gt;&lt;li&gt;Stress animal&lt;br /&gt;&lt;ul style\="list-style-type\: circle;"&gt;&lt;li&gt;Un stress excessif avant le vêlage peut épuiser le système immunitaire des vaches réduisant ainsi la résistance aux infections après le vêlage&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="4"&gt;&lt;li&gt;Nutrition&lt;ul&gt;&lt;li&gt;Revoir l'équilibre de la ration chez les vaches taries en se concentrant sur le contrôle de la NEC, l'équilibre minéral et l'apport de nutriments antioxydants (vit E, A, sélénium, zinc et cuivre)&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;Autre contrôle de maladie (cétose, DGC)&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMilkFever=Fière de lait
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;Stratégie pour réduire l'incidence de l'hypocalcémie&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;Deux stratégies alternatives pour la prévention de l'hypocalcémie chez les vaches laitières dépendent à 100% de la gestion de la ration.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Utiliser les régimes à faible teneur en Ca pour vaches taries&lt;/li&gt;&lt;li&gt;Utiliser des régimes avec un BACA faible formulé pour les vaches en pré-vêlage&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;Points à considérer pour contrôler l'hypocalcémie \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Analyse minérale des fourrages (calcium, phosphore, magnésium, potassium, sodium, soufre et chlorure) dans un laboratoire homologué&lt;/li&gt;&lt;li&gt;Réviser la ration suivant les recommandations Provimi pour les vaches en pré-vêlage et réviser les pratiques de gestion de l'alimentation (porter une attention spéciale aux fourrages à volonté, à la concentration en K des fourrages, aux minéraux donnés aux vaches taries et au tri)&lt;/li&gt;&lt;li&gt;Consulter l'inventaire des produits spécifiques développés pour prévenir l'hypocalcémie chez les vaches en transition&lt;/li&gt;&lt;li&gt;Lors de l'utilisation de régimes alimentaires faible en BACA\:&lt;ul&gt;&lt;li&gt;Surveiller attentivement la consommation d'aliments car les sels anioniques peuvent être peu appétant et peuvent réduire la consommation en matière sèche&lt;/li&gt;&lt;li&gt;Surveiller le pH urinaire pour vérifier l'efficacité des changements de régime alimentaire&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=Rétention Placentaire
StrategyToReduceRetainedPlacentaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;span&gt;&lt;strong&gt;Stratégie pour réduire l'incidence des rétentions placentaires&lt;/strong&gt;&lt;/span&gt;&lt;/h4&gt;&lt;p&gt;L'incidence des rétentions placentaires est positivement corrélée à l'immunodepression, aux hormones de stress élevées, à l'hypocalcémie (y compris subclinique), au vêlage assisté, à l'avortement, aux agents infectieux endémiques et à la génétique.&lt;/p&gt;&lt;p&gt;Surveiller les points suivants, par ordre décroissant d'importance \:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;Nutrition &lt;br/&gt; &lt;p style\="padding-left\: 30px;"&gt;Réviser les rations pré-vêlage suivant les recommandations Provimi en mettant l'accent sur \:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;Se et vitamine E&lt;/li&gt;&lt;li&gt;Niveaux minéraux (K, Ca, Mg) pour diminuer le risque d'hypocalcémie subclinique et clinique&lt;/li&gt;&lt;li&gt;Utiliser un produit avec un BACA faible ou négatif pour diminuer l'incidence de l'hypocalcémie&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;Gestion&lt;/li&gt;&lt;ul&gt;&lt;li&gt;Gérer NEC (3,00 au vêlage) en fin de lactation et en période tarie&lt;/li&gt;&lt;li&gt;Réduire au minimum les facteurs de stress dans la zone de pré-vêlage et la zone de vaches fraîches pour diminuer la mobilisation des graisses et l'immunodepression autour du vêlage&lt;/li&gt;&lt;li&gt;Améliorer le confort des vaches et fournir suffisamment d'espace à l'auge (~ 30 cm par vache)&lt;/li&gt;&lt;li&gt;Suivre une procédure de vêlage définie par le vétérinaire&lt;/li&gt;&lt;li&gt;Ne pas aider les vaches à vêler inutilement&lt;/li&gt;&lt;li&gt;Maintenir un environnement propre pour diminuer les risques d'infection utérine&lt;/li&gt;&lt;/ul&gt;&lt;/ol&gt;&lt;/span&gt;
Straw=Paille
Stress=thermique sévère \: bouche ouverte + bave Fréquence de respiration de 120 à plus de 160 bpm. 
Sudan=Soudan
Sur=cette page, ajoutez ou mettez à jour les données propres au site de l’exploitation agricole. Vous pouvez également ajouter ou actualiser des données au fur et à mesure que vous utilisez les outils.
Surinam=Surinam
Suriname=Suriname
SurveyCategories=Choix de l'audit
SurveyOfForages=Sondage sur les fourrages
SurveyOfForages_AnnualCowNumAndForageNeeds=Les besoins fourragers pour l'ensemble des vaches sont-ils planifiés annuelement?
SurveyOfForages_AshLevelsInCornSilage=Quel est le taux de cendre de l’ensilage de maïs?
SurveyOfForages_AshLevelsInHaylage=Quel est le taux de cendre de l’enrubannage?
SurveyOfForages_ButyricAcidLevelsInHaylage=Quel est le taux d'acide butyrique?
SurveyOfForages_CornSilageProcessingScore=Quelle est la longueur de hâchage de l'échantillon?
SurveyOfForages_CornSilageScoreMonitored=Est-ce que l'éclatement des grains a été évalué pour l'ensilage de maïs
SurveyOfForages_InspectedForSpoilageAndMold=Tous les fourrages sont-ils inspectés pour les détériorations et moisissures, et le fourrage détérioré jeté?
SurveyOfForages_InventoryIsMonitored=A quelle fréquence l'inventaire est-il réalisé?
SurveyOfForages_LacticAcidToAceticAcidLevels=Quel est le ratio acide lactique/acide butyrique?
SurveyOfForages_LooseOrFacedFeedWithin=Reste-t-il de l’ensilage détassé au front d'attaque du silo après la distribution de la ration?
SurveyOfForages_NoLooseFeedRemaining=Pas de perte de nourriture après l’alimentation ?
SurveyOfForages_SilosSizedForCapacity=Les silos classés pour leur capacité par rapport au besoin du troupeau ? Sans avoir besoin de surcharger.
SurveyOfForages_VisibleSignsOfSoil=Est-ce que l'ensilage est exempt de tout signe visible de contamination avec de la terre?
Svalbard_and_Jan_Mayen=Svalbard et Jan Mayen
Swaziland=Swaziland
Sweden=SuÃ¨de
Switzerland=Suisse
Sync-failed-due-to-unknown-reason=La synchronisation a Ã©chouÃ© pour des raisons inconnues
SyncFailed=La synchronisation n'a pas pu être complétée pour l'instant, réessayez.
Sync_Data=synchroniser les données
Syracuse=Syracuse
Syrian_Arab_Republic=RÃ©publique arabe syrienne
SystemGenerated=Système généré
SÃ£o_Paulo=SÃ£ Â£ o Paulo
THB=Thaïlande (THB THB)
TMR=Ration complète mélangée
TMRHerdAnalysisTableTitle=Analyse du troupeau - Grosseur des particules dans la ration mélangée
TMRParticleScore=Analyse de la taille des particules
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Fermer
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Jours de lactation
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Modifier la valeur jours de lactation
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=Jours de lactation
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Modifier
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Milieu 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Milieu 2
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Analyse de la taille des particules
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Haut
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Fond
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Analyse du troupeau
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Données
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Résultats
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Evaluation de la taille des particules pour la Santé Ruminale
TMRParticleScoreHerdAnalysisResultsText=Analyse de la taille des particules de la TMR
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Milieu 1 (8 mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Milieu 2 (4 mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Haut (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Tamis
TRY=TRY
TWD=Taïwan (NT$ TWD)
Tabasco=Tabasco
Taipei_City=Ville de Taipei
Taiwan=Taïwan
Tajikistan=Tadjikistan
Tamaulipas=Tamoulipas
Tamil_Nadu=Tamil Nadu
Tanzania,_United_Republic_of=Tanzanie, RÃ©publique unie de
Taranto=Tarente
Task=Tâche
Tasmania=Tasmanie
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=Tennessee
Teramo=TÃ©ramo
Terni=Terni
Texas=Texas
TextureFeed=Aliment Mash
Thailand=Thaïlande
Thirdparty=Tierce personne
ThisVisit=(Cette visite)
ThreePotentialStorageSolutions=Trois sytèmes de stockage de fourrage
ThreeScreen=3 tamis -
ThreeTimesPerWeek=3 fois par semaines
Tianjin=Tianjin
Tiestall=logette entravée
TimeRemaining=Temps restant pour le repos
Timor-Leste=Timor Read
Tipperary=Tipperary
Tlaxcala=Tlaxcala
Tocantins=Tocantins
Togo=Aller
Tokelau=Tokelau
Tokyo=Tokyo
Tonga=ArrivÃ©
TonsAF=TONNES MB
TonsAFSilo=Tonnes restantes dans le silo (MB)
TonsDM=Tons MS
TonsDMSilo=Tonnes restantes dans le silo (MS)
TonsPerDay=Tonnes par jour
ToolNotSelected=Vous devez sélectionner au moins un outil
Top=Haut
TopUnloadingSilo=Silo avec déchargement par le haut
TopUnloadingSilos=Silo avec déchargement par le haut
TopValue=(19mm)
Total=Total
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=Nombre d'animaux du lot
TotalAnimalsHerd=Total des animaux
TotalPenPerScore=Nombre total d'animaux par score
TotalRevenue=Revenu total
TowerSilos=Silo tour
TowerSilos_FaceRemovalRateGreaterThan4Inches=Avancement supérieur à 10cm par jour?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Est-ce qu'après confection, le silo est laissé fermé pendant 1 mois?
TowerSilos_SiloFillingTimeof3DaysOrLess=Le remplissage du silo prend moins de 3 jours?
TransitionCow=Vache de transition
Trapani=Trapani
Tray=Tamis
Trends=Tendances positives
Trento=Trento
Treviso=Treviso
Trieste=Trieste
Trinidad_and_Tobago=TrinitÃ©-et-Tobago
Tripura=Tripura
Tunisia=Tunisie
Turin=Turin
Turkey=Turquie
Turkmenistan=TurkmÃ©nistan
Turks_and_Caicos_Islands=Ã®les Turques-et-CaÃ¯ques
Tuvalu=Tuvalu
TwelveInchesOrGreater=30 cm ou plus
TwentyFourHoursAndNoSync=Vingt-quatre heures et aucune synchronisation
TwentyFourHoursBeforeActionIsDue=Vingt-quatre heures avant que l'action ne soit due
TwentyFourToThirtySixInchesPerDay=60 à 90 cm par jour
TwicePerWeek=Deux fois pas semaine
UAH=Ukraine (UAH UAH)
UK=Royaume-Uni
UNITED_STATES=États-Unis
US=États-Unis d'Amérique
USD=États-Unis d'Amérique ($ USD)
Udine=Udine
Uganda=Ouganda
Ukraine=Ukraine
Une=fois la ration créé, sélectionnez la classe / sous-classe d'animal associé à la ration
UnitedKingdom=Royaume-Uni
UnitedStates=États-Unis d'Amérique
United_Arab_Emirates=Emirats Arabes Unis
United_Kingdom=Royaume-Uni
United_States=Ãtats-Unis
UrinePH=pH urinaire
UrinePHAVG=Moyenne pH urinaire
UrinePHAverageNumber=Nombre moyen
UrinePHDensity=pH urinaire 
UrinePHEditCowViewModel.AddNew=vache suivante
UrinePHEditCowViewModel.CowName=Nom vache
UrinePHEditCowViewModel.CowValue=valeur vache
UrinePHEditCowViewModel.UrinePHEnterCowValue=Entrer la valeur de la vache
UrinePHEditCowViewModel.ValidCudInput=Merci d'entrer une valeur d'entrée valide
UrinePHEditGoalViewModel.GoalMax=Objectif - Max
UrinePHEditGoalViewModel.GoalMin=Objectif - Min
UrinePHEditGoalViewModel.TargetUrinePHRange=Objectif cible pour le pH urinaire
UrinePHEditGoalViewModel.Title=Edition des objectifs
UrinePHInputsViewModel.AddNew=Nouveau
UrinePHInputsViewModel.CalculatorHeading=Merci de sélectionner une vache pour renter la valeur du pH urinaire. Cliquer sur "Nouveau" pour ajouter une vache à la liste
UrinePHInputsViewModel.CoefficientVariation=Coefficient de variation (CV) (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Vaches en dehors des valeurs types (%)
UrinePHInputsViewModel.CudChewCategorySection=Vaches
UrinePHInputsViewModel.DietDCAD=BACA ration, mEq/100g
UrinePHInputsViewModel.Resources=Ressources
UrinePHInputsViewModel.TargetUrinePHRange=Plage cible pour pH urinaire 
UrinePHInputsViewModel.UrinePHAVG=Moyenne pH urinaire (calculé)
UrinePHInputsViewModel.UrinePhSTDDEV=Deviation standard (calculé)
UrinePHMasterViewModel.Inputs=Entrées
UrinePHMasterViewModel.Results=Résultats
UrinePHMasterViewModel.Title=pH urinaire
UrinePHPenSelectionViewModel.Title=pH urinaire
UrinePHPenSelectionViewModel.UrinePHPenList=Case (lactation et tarie uniquement)
UrinePHResultsViewModel.DietDCAD=BACA ration, mEq/100g
UrinePHResultsViewModel.MaxpH=pH max
UrinePHResultsViewModel.MinpH=pH min
UrinePHResultsViewModel.UrinePHAVG=Moyenne pH urinaire
Uruguay=Uruguay
UserCreated=Créé par l'utilisateur
UserPreferencesViewModel.Branding=Choisir votre logo corporatif
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=Choisir une monnaie
UserPreferencesViewModel.Imperial=Impérial
UserPreferencesViewModel.MainHeading=Les options suivantes peuvent être modifiées plus tard dans les paramètres de l'application
UserPreferencesViewModel.Metric=Métrique
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Choisir une monnaie
UserPreferencesViewModel.SelectPointScale=Selectionnez une échelle de temps
UserPreferencesViewModel.Title=Paramètres de l'utilisateur
UserPreferencesViewModel.UnitOfMeasure=Selectionner votre unité de mesure
UserPreferencesViewModel.UserPreferencesMilkProcessor=Configurer la laiterie
UserPreferencesViewModel.UserPreferencesMoreSettings=PLUS DE PARAMETRES
User_Settings=Paramètres de l'utilisateur
Utah=Utah
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
Uzbekistan=OuzbÃ©kistan
VEF=Vénézuela (Bs VEF)
VND=Vietnam (₫ VND)
Vanuatu=Vanuatu
Varese=Varese
Venezuela=Venezuela
Venezuela,_Bolivarian_Republic_of=Venezuela, RÃ©publique bolivarienne de
Venice=Venise
Veracruz=Veracruz
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Vermont=Vermont
Verona=VÃ©rone
Vestland=Westland
Vibo_Valentia=Vibo Valentia
Vicenza=Vicenza
Victoria=Victoria
Viet_Nam=Vietnam
Vietnam=Vietnam
ViewOverallCalfHaiferScore=Voir le score global de l'outil Veaux et Génisses
ViewOverallForageScore=voir le résultat audit fourrage
Virgin_Islands,_British=Ãles vierges, britanniques
Virginia=Virginie
Visit.Report.Footer.Patent=Cargill Incorporated, ses sociétés mères ou affliliées ne jusitifie pas l'exactitude de ces estimations, en raison de nombreux facteurs. Il n'y a pas de garantie de production ou de résultats financiers. ©2023 Cargill, Incorporated. Tous les droit sont réservés
VisitAutoPublished=Visitez Auto PubliÃ©
VisitDate=Date de visite
VisitDownloadProceed=Procéder
VisitNotebook=Cahier de notes
VisitNotesViewModel.Action=Action
VisitNotesViewModel.Close=Fermer
VisitNotesViewModel.DownloadingNotes=Chargement des notes…
VisitNotesViewModel.Event=Évenement
VisitNotesViewModel.New=Nouveau
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=Observation
VisitNotesViewModel.Task=Tâche
VisitNotesViewModel.Title=Cahier de notes
VisitNotesViewModel.VisitNotebook=Cahier de notes
VisitSummaryViewModel.CalfHeiferItem=Veaux &amp; génisses
VisitSummaryViewModel.CalfHeiferScorecard=Audit
VisitSummaryViewModel.CategorySection=Catégories d'outils
VisitSummaryViewModel.ComfortHeatStressBanner=Outil pour l'evaluation du stress thermique
VisitSummaryViewModel.ComfortItem=Confort
VisitSummaryViewModel.EmailReport=Rapport imprimé
VisitSummaryViewModel.FreeFormReport=Rapport en format livre
VisitSummaryViewModel.HealthItem=Santé
VisitSummaryViewModel.HeatstressEvaluationTitle=Évaluation du stress thermique
VisitSummaryViewModel.HerdAnalysis=Analyse du troupeau
VisitSummaryViewModel.InputsOutputsChart=Données / Résultats / Graphiques
VisitSummaryViewModel.MilkProcessRevenueCalculator=Revenus de production
VisitSummaryViewModel.NoToolPrompt=Aucun outil n'a été complété.
VisitSummaryViewModel.NutritionForage=Evaluation des fourrages
VisitSummaryViewModel.NutritionItem=Nutrition
VisitSummaryViewModel.NutritionPile=Inventaires des fourrages
VisitSummaryViewModel.PenTimeTitle=Temp de repos par groupe
VisitSummaryViewModel.ProductivityItem=Productivité
VisitSummaryViewModel.RumenHealthBodyConditionTitle=État corporel
VisitSummaryViewModel.RumenHealthLocomotionTitle=Score de Locomotion
VisitSummaryViewModel.RumenHealthManureTitle=Santé du rumen - Score de bouses
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Maladies métaboliques
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
VisitSummaryViewModel.RumenHealthTMRTitle=Santé du rumen - Grosseur de particules de la ration mélangée
VisitSummaryViewModel.RumenHealthTitle=Santé du rumen - mastication
VisitSummaryViewModel.RumenHealthUrinePHTitle=pH urinaire
VisitSummaryViewModel.Title=Rapport de visite
VisitSummaryViewModel.VisitSummaryMilkCalc=Données / Résultats / Ressources
VisitSummaryViewModel.VisitTitle=NOM DE VISITE
VisitViewModel.CalfHeiferItem=Veaux &amp; génisses
VisitViewModel.CategorySection=Catégorie d'outils
VisitViewModel.ComfortItem=Confort
VisitViewModel.Delete=Supprimer la visite
VisitViewModel.DeletePrompt=Êtes-vous sûr de vouloir publier cette visite? Cela ne peut pas être annulé.
VisitViewModel.HealthItem=Santé
VisitViewModel.Instructions=Choissisez un outils catégorie ou rapport de la liste ci-dessous
VisitViewModel.NullVisitName=Le nom de la visite ne peut pas être vide. Entrez un nom de visite.
VisitViewModel.NutritionItem=Nutrition
VisitViewModel.ProductivityItem=Productivité
VisitViewModel.Publish=Publier
VisitViewModel.PublishNotes=Publier les notes
VisitViewModel.PublishNotesPrompt=Êtes-vous sûr de vouloir publier les notes de cette visite? Cela ne peut pas être annulé.
VisitViewModel.PublishPrompt=Êtes-vous sûr de vouloir publier cette visite? Cela ne peut pas être annulé.
VisitViewModel.PublishVisit=Publier la visite
VisitViewModel.SiteVisitSummary=Rapport de visite
VisitViewModel.Title=Détails de la visite
VisitViewModel.ToolCategories=Catégories d'outils
VisitViewModel.VisitNotebook=Cahier de notes
VisitViewModel.VisitTitle=Titre de la visite
VisitViewModel.WalkthroughReport=Visite pas-à-pas
Viterbo=Viterbo
VolumeImperial=gal
VolumeMetric=ml
WalkthroughPenSelectionViewModel.Pens=LOTS
WalkthroughPenSelectionViewModel.Title=Visite pas-à-pas
WalkthroughReport=Visite pas-à-pas
WalkthroughReportHerdAnalysisViewModel.Appearance=Observation d'état
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Propreté de la litière
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Litière\: profondeur et douceur
WalkthroughReportHerdAnalysisViewModel.Branding=NOM DE MARCHANDISE
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Confort ,% d' animaux couchés
WalkthroughReportHerdAnalysisViewModel.Comments=COMMENTAIRES
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Nombre de coup de machoire, mastication par bolus
WalkthroughReportHerdAnalysisViewModel.CudChewing=Rumination,% mastication
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} Rapport
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} Rapport
WalkthroughReportHerdAnalysisViewModel.ExportSelected=selection de l'outil d'email
WalkthroughReportHerdAnalysisViewModel.FinalObservations=OBSERVATIONS FINALES
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Création du rapport…
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Blessures aux jarrets,% des animaux
WalkthroughReportHerdAnalysisViewModel.MainHeading=Visite pas-à-pas
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Décharge nasale, % d'animaux
WalkthroughReportHerdAnalysisViewModel.Notes=Annotations
WalkthroughReportHerdAnalysisViewModel.Opportunities=OPPORTUNITÉS
WalkthroughReportHerdAnalysisViewModel.PensForExport=GROUPES POUR EXPORTER
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Remplissage de rumen
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=Note d'état corporel
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Score de locomotion
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Score des bouses
WalkthroughReportHerdAnalysisViewModel.SubHeading=Analyse du troupeau
WalkthroughReportHerdAnalysisViewModel.Title=Visite pas-à-pas - Analyse du troupeau
WalkthroughReportHerdAnalysisViewModel.Trends=TENDANCES POSITIVES
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Décharge utérine,% d'animaux
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Qualité d'eau
WalkthroughReportLandingViewModel.HerdAnalysis=Analyse du troupeau
WalkthroughReportLandingViewModel.PenAnalysis=Analyse de lots
WalkthroughReportLandingViewModel.Title=Visite pas-à-pas
WalkthroughReportQualityViewModel.BeddingCleanliness=Sélectionnez la propreté de la litière
WalkthroughReportQualityViewModel.Clean=Propre
WalkthroughReportQualityViewModel.Dirty=Sale
WalkthroughReportQualityViewModel.ModeratelyClean=Modérément propre
WalkthroughReportQualityViewModel.Title=Visite pas-à-pas
WalkthroughReportQualityViewModel.WaterQuality=Sélectionnez la qualité de l'eau
WalkthroughReportViewModel.Appearance=Apparence
WalkthroughReportViewModel.BeddingCleanliness=Propreté de la litière
WalkthroughReportViewModel.BeddingDepthSoft=Litière\: profondeur et douceur
WalkthroughReportViewModel.Clean=Propre
WalkthroughReportViewModel.ComfortItem=Confort de la vache,% de vaches couchées
WalkthroughReportViewModel.Comments=Commentaires
WalkthroughReportViewModel.CudChewCategorySection=Nombre de bolus et mastication par bolus
WalkthroughReportViewModel.CudChewing=Rumination,% mastication
WalkthroughReportViewModel.Current=Actuel
WalkthroughReportViewModel.Dirty=Sale
WalkthroughReportViewModel.Goals=Objectif
WalkthroughReportViewModel.HockAbrasion=Blessures aux jarrets,% des animaux
WalkthroughReportViewModel.ModeratelyClean=Modérément propre
WalkthroughReportViewModel.NasalDischarge=Décharge nasale,% d'animaux
WalkthroughReportViewModel.Opportunities=Opportunités
WalkthroughReportViewModel.Previous=Précédent
WalkthroughReportViewModel.RumenFill=Ramplissage du rumen
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=Note d'état corporel
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Score de locomotion
WalkthroughReportViewModel.RumenHealthManureTitle=Note bouses
WalkthroughReportViewModel.Title=Visite pas-à-pas
WalkthroughReportViewModel.Trends=Tendances positives
WalkthroughReportViewModel.UterineDischarge=Décharge utérine,% d'animaux
WalkthroughReportViewModel.WaterQuality=Qualité d'eau
Wallis_and_Futuna=Wallis et Futuna
Washington=Washington
Waterford=Waterford
Weekly=Hebdomadaire
WeightDMInLengthImperial=Lbs. matière seche en 1 pied
WeightDMInLengthMetric=Kgs. matière seche en 1 mètre
WeightImperial=lbs
WeightImperialCWT=CWT
WeightMetric=kg
West_Bengal=Bengale-Occidental
West_Virginia=Virginie-Occidentale
Western_Australia=Australie occidentale
Western_Sahara=Sahara occidental
Westmeath=Ã l'ouest
Wexford=Wexford
Wicklow=Moustiquaire
Wisconsin=Wisconsin
WithinEightHours=Moins de 8 heures
Wyoming=Wyoming
Xinjiang=Xinjiang
Xizang=Xizang
Yemen=YÃ©men
Yes=Oui
YucatÃ¡n=YucatÄn
Yukon_Territories=Territoires du Yukon
Yunnan=Yunnan
ZAR=Afrique du Sud (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=Zambie
Zhejiang=Zhejiang
Zimbabwe=Zimbabwe
welcome.message=Bonjour {0}
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventionelle
PMR=RPM
CompleteFeed=Moulée Complète
Supplement=Supplément
Ingredients=Ingrédients
RoundBales=Ensilage ou balle ronde
Silage=Ensilage
SmallGrainSilage=Ensilage de céréales
DryCorn=Sec Maïs
HighMoistureCorn=Humide Maïs
Barley=Orge
MixedGrain=Avoine
Wheat=Blé
DryHay=Foin sec
Oats=Avoine
Cobmeal=Maïs-épi
Soybeans=Fève soya
butterfat=Matière Grasse
protein=Protéine
lactoseAndOtherSolids=Lactose et autres solides
deductions=Déductions
class2Protein=Classe 2 Protéine
class2LactoseAndOtherSolids=Lactose et autres solides
Report.Return.Over.Feed.YAxis=Retour sur l alimentation ($/vache/jour)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole
Rof.Kg.Per.Fat=Return Over Feed Kg Per Fat




