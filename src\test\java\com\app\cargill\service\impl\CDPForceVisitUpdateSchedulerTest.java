/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.document.*;
import com.app.cargill.model.Visits;
import com.app.cargill.schedulers.CDPForceVisitUpdateScheduler;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
class CDPForceVisitUpdateSchedulerTest {

  @InjectMocks private CDPForceVisitUpdateScheduler cDPForceVisitUpdateScheduler;
  @Mock CdpV1ServiceImpl cdpV1Service;

  @Test
  void forceUpdateVisits() {
    // Mock data
    // Mock data
    Long visitId1 = 121L;
    VisitDocument visitDocument1 = new VisitDocument();

    visitDocument1.setVisitName("Visit 1");

    Long visitId2 = 122L;
    VisitDocument visitDocument2 = new VisitDocument();
    visitDocument2.setVisitName("Visit 2");

    Visits visit1 = new Visits();
    visit1.setVisitDocument(visitDocument1);
    visit1.setId(121L);

    Visits visit2 = new Visits();
    visit2.setVisitDocument(visitDocument2);
    visit2.setId(122L);

    List<Visits> retVisit = new ArrayList<>();
    retVisit.add(visit1);
    retVisit.add(visit2);

    when(cdpV1Service.getAllVisitsForOneYear()).thenReturn(retVisit);

    cDPForceVisitUpdateScheduler.forceUpdateVisits();
    assertNotNull("TEst");
  }
}
