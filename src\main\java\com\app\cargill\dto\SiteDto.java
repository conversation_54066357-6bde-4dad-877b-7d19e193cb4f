/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.MilkingSystem;
import jakarta.validation.constraints.NotNull;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SiteDto extends BaseDto {

  @NotNull private UUID accountId;
  private String externalAccountId; // golden record id
  @NotNull private String siteName;
  @NotNull private Double currentMilkPrice;
  private Integer daysInMilk;
  @NotNull private MilkingSystem milkingSystemType;
  private Double dryMatterIntake;
  private Integer lactatingAnimal;
  private Double milk;
  private Double milkFatPercent;
  private Double milkProteinPercent;
  private Double milkOtherSolidsPercent;
  private List<SiteMappingDto> siteMappings;
  private Integer milkSomaticCellCount;
  private Integer bacteriaCellCount;
  private Double netEnergyOfLactationDairy;
  private Double rationCost;
  @Builder.Default private List<BarnDto> barns = new ArrayList<>();
  private Double asFedIntake;
  @Builder.Default private String origination = "LM_SITE";
  private Instant dDWLastUpdatedDate;
  private Integer numberOfParlorStalls;
  private Instant createTimeUtc;
  private Instant lastModifiedTimeUtc;
  private Instant lastSyncTimeUtc;
  private boolean isNew;
  private Integer penCount;
  private Instant dateOfLastVisit;
  private Boolean hasReport;

  @Builder.Default private Map<String, String> keys = new HashMap<>();
}
