<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="003" author="Aweem">
		<sql>
			CREATE TABLE IF NOT EXISTS segments (id  bigserial not null,
			created_date timestamp,
			deleted BOOLEAN default false,
			local_id varchar(255),
			updated_date timestamp,
			segment_document jsonb,
			primary key (id));

			INSERT INTO public.segments(segment_document)
			VALUES  ('{"key":1, "value":"<PERSON>"}'),
							('{"key":2, "value":"<PERSON>"}'),
							('{"key":3, "value":"Denis"}'),
							('{"key":4, "value":"End-User"}'),
							('{"key":5, "value":"Kobe"}'),
							('{"key":6, "value":"Mila"}'),
							('{"key":7, "value":"Noah"}'),
							('{"key":8, "value":"Spence"}'),
							('{"key":9, "value":"Walton"}');
		</sql>
	</changeSet>

</databaseChangeLog>