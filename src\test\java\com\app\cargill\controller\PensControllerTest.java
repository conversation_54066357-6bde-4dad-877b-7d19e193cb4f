/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.*;
import com.app.cargill.service.IAnimalClassService;
import com.app.cargill.service.IDietService;
import com.app.cargill.service.IPensService;
import java.time.Instant;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class PensControllerTest {

  @Mock private IPensService pensServiceImpl;

  @Mock private IDietService dietServiceImpl;

  @Mock private IAnimalClassService animalClassServiceImpl;
  @InjectMocks private PensController controller;

  @Test
  void getAllPens() {
    ResponseEntity<ResponseEntityDto<PageImpl<PenDto>>> result =
        controller.getAllPens(1, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
  }

  @Test
  void getAllPensBySiteId() {
    ResponseEntity<ResponseEntityDto<PageImpl<PenDto>>> result =
        controller.getAllPensBySiteId("1", 1, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
  }

  @Test
  void whenSavingPensHasPartialFailReturnCorrectRespons() {
    PenGroupingDto input = PenGroupingDto.builder().pens(List.of(PenDto.builder().build())).build();
    PenGroupingDto output =
        PenGroupingDto.builder()
            .pens(
                List.of(
                    PenDto.builder().status(ResponseStatus.FAILED).build(),
                    PenDto.builder().status(ResponseStatus.SUCCESS).build()))
            .build();
    when(pensServiceImpl.save(input)).thenReturn(output);
    ResponseEntity<ResponseEntityDto<PenGroupingDto>> result = controller.save(input);
    assertEquals(HttpStatus.PARTIAL_CONTENT, result.getStatusCode());
    assertNotNull(result.getBody());
    assertEquals(ResponseStatus.PARTIAL, result.getBody().getStatus());
  }

  @Test
  void whenSavingPensHasFailedReturnCorrectRespons() {
    PenGroupingDto input = PenGroupingDto.builder().pens(List.of(PenDto.builder().build())).build();
    PenGroupingDto output =
        PenGroupingDto.builder()
            .pens(
                List.of(
                    PenDto.builder().status(ResponseStatus.FAILED).build(),
                    PenDto.builder().status(ResponseStatus.FAILED).build()))
            .build();
    when(pensServiceImpl.save(input)).thenReturn(output);
    ResponseEntity<ResponseEntityDto<PenGroupingDto>> result = controller.save(input);
    assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
    assertNotNull(result.getBody());
    assertEquals(ResponseStatus.FAILED, result.getBody().getStatus());
  }

  @Test
  void whenSavingPensHasSucceededReturnCorrectRespons() {
    PenGroupingDto input = PenGroupingDto.builder().pens(List.of(PenDto.builder().build())).build();
    PenGroupingDto output =
        PenGroupingDto.builder()
            .pens(
                List.of(
                    PenDto.builder().status(ResponseStatus.SUCCESS).build(),
                    PenDto.builder().status(ResponseStatus.SUCCESS).build()))
            .build();
    when(pensServiceImpl.save(input)).thenReturn(output);
    ResponseEntity<ResponseEntityDto<PenGroupingDto>> result = controller.save(input);
    assertEquals(HttpStatus.OK, result.getStatusCode());
    assertNotNull(result.getBody());
    assertEquals(ResponseStatus.SUCCESS, result.getBody().getStatus());
  }

  @Test
  void whenUpdatingPensHasPartialFailReturnCorrectRespons() {
    PenGroupingDto input = PenGroupingDto.builder().pens(List.of(PenDto.builder().build())).build();
    PenGroupingDto output =
        PenGroupingDto.builder()
            .pens(
                List.of(
                    PenDto.builder().status(ResponseStatus.FAILED).build(),
                    PenDto.builder().status(ResponseStatus.SUCCESS).build()))
            .build();
    when(pensServiceImpl.update(input)).thenReturn(output);
    ResponseEntity<ResponseEntityDto<PenGroupingDto>> result = controller.update(input);
    assertEquals(HttpStatus.PARTIAL_CONTENT, result.getStatusCode());
    assertNotNull(result.getBody());
    assertEquals(ResponseStatus.PARTIAL, result.getBody().getStatus());
  }

  @Test
  void whenUpdatingPensHasFailedReturnCorrectRespons() {
    PenGroupingDto input = PenGroupingDto.builder().pens(List.of(PenDto.builder().build())).build();
    PenGroupingDto output =
        PenGroupingDto.builder()
            .pens(
                List.of(
                    PenDto.builder().status(ResponseStatus.FAILED).build(),
                    PenDto.builder().status(ResponseStatus.FAILED).build()))
            .build();
    when(pensServiceImpl.update(input)).thenReturn(output);
    ResponseEntity<ResponseEntityDto<PenGroupingDto>> result = controller.update(input);
    assertEquals(HttpStatus.BAD_REQUEST, result.getStatusCode());
    assertNotNull(result.getBody());
    assertEquals(ResponseStatus.FAILED, result.getBody().getStatus());
  }

  @Test
  void whenUpdatingPensHasSucceededReturnCorrectRespons() {
    PenGroupingDto input = PenGroupingDto.builder().pens(List.of(PenDto.builder().build())).build();
    PenGroupingDto output =
        PenGroupingDto.builder()
            .pens(
                List.of(
                    PenDto.builder().status(ResponseStatus.SUCCESS).build(),
                    PenDto.builder().status(ResponseStatus.SUCCESS).build()))
            .build();
    when(pensServiceImpl.update(input)).thenReturn(output);
    ResponseEntity<ResponseEntityDto<PenGroupingDto>> result = controller.update(input);
    assertEquals(HttpStatus.OK, result.getStatusCode());
    assertNotNull(result.getBody());
    assertEquals(ResponseStatus.SUCCESS, result.getBody().getStatus());
  }

  @Test
  void getAllAnimalClasses() {
    ResponseEntity<ResponseEntityDto<AnimalClassDto>> result = controller.getAllAnimalClasses();
    assertEquals(HttpStatus.OK, result.getStatusCode());
  }

  @Test
  void getAllDiets() {
    ResponseEntity<ResponseEntityDto<PageImpl<DietDto>>> result =
        controller.getDietsPaginated(1, 10, "id", Instant.now(), "desc", DietSource.MAX);
    assertNotNull(result);
  }

  @Test
  void getDuplicatePens() {
    ResponseEntity<ResponseEntityDto<List<DuplicatePenIdDTO>>> result =
        controller.getAllPensDuplicate(1, 1);
    assertNotNull(result);
  }
}
