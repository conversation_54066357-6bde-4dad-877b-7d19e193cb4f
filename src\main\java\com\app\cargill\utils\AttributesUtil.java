/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class AttributesUtil {

  public static final String JAVA_UTIL_UUID = "java.util";
  public static final String JAVA_TIME = "java.time";

  private AttributesUtil() {}

  @SuppressWarnings("java:S3011") // intentional
  public static <T> List<AttributesDTO> getAttributes(T tool, String path) {
    if (tool != null) {
      if (inaccessibleClasses(tool.getClass().getName())) {
        return Collections.emptyList();
      }

      List<AttributesDTO> attributes = new ArrayList<>();

      Field[] fields =
          Arrays.stream(tool.getClass().getDeclaredFields())
              .filter(field -> !Modifier.isStatic(field.getModifiers()))
              .toArray(Field[]::new);

      for (Field field : fields) {

        if (isSpringAnnotation(field)
            || field.getName().contains("ANNOTATION")
            || field.getName().contains("serialVersionUID")) {
          continue;
        }

        field.setAccessible(true);

        Class<?> fieldType = field.getType();

        if (isArray(fieldType)) {
          processArray(tool, path, attributes, field);
        } else if (isAcceptableList(fieldType)) {
          processList(tool, path, attributes, field);
        } else if (isAcceptableClass(fieldType)) {
          processClass(tool, path, attributes, field);
        } else {
          setAttribute(tool, path, attributes, field);
        }
      }

      return attributes;
    }

    return Collections.emptyList();
  }

  private static boolean inaccessibleClasses(String toolClass) {
    return toolClass.contains(JAVA_UTIL_UUID) || toolClass.contains(JAVA_TIME);
  }

  @SuppressWarnings("java:S3011")
  private static <T> void processList(
      T tool, String path, List<AttributesDTO> attributes, Field field) {
    List<?> values;
    try {
      field.setAccessible(true);
      values = (List<?>) field.get(tool);
    } catch (IllegalAccessException e) {
      log.error("[AttributesUtil][processList] field " + field.getName() + " " + e.getMessage());
      values = null;
    }

    if (values != null) {
      for (int i = 0; i < values.size(); i++) {
        String name = getAttributeName(field.getName(), path, i + 1);
        Object listItem = values.get(i);
        attributes.addAll(getAttributes(listItem, name));
      }
    }
  }

  @SuppressWarnings("java:S3011")
  private static <T> void processClass(
      T tool, String path, List<AttributesDTO> attributes, Field field) {
    Object value;
    try {

      field.setAccessible(true);
      value = field.get(tool);
    } catch (IllegalAccessException e) {
      value = null;
    }

    String name = getAttributeName(field.getName(), path);
    attributes.addAll(getAttributes(value, StringUtils.capitalize(name)));
  }

  private static <T> void setAttribute(
      T tool, String path, List<AttributesDTO> attributes, Field field) {

    String name = getAttributeName(field.getName(), path);
    String value = null;
    try {
      Object fieldValue = field.get(tool);
      if (fieldValue != null) {
        value = fieldValue.toString();
      }
    } catch (IllegalAccessException e) {
      // Ignore and continue to the next field
    }

    AttributesDTO attribute = getAttributeForSinglePrimitiveType(field, name, value);
    attributes.add(attribute);
  }

  private static boolean isSpringAnnotation(Field field) {
    Package fieldPackage = field.getDeclaringClass().getPackage();
    return fieldPackage != null && fieldPackage.getName().startsWith("org.springframework");
  }

  static AttributesDTO getAttributeForSinglePrimitiveType(Field field, String name, String value) {
    AttributesDTO attribute = new AttributesDTO();

    boolean isNumericType = isNumericType(field.getType());
    if (field.isAnnotationPresent(JsonProperty.class)) {
      JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
      if (jsonProperty != null && !name.contains("/")) {
        name = jsonProperty.value();
      }
    }
    attribute.setName(name);
    attribute.setToolName(field.getDeclaringClass().getSimpleName());
    attribute.setTextValue((!isNumericType && value != null && !value.isEmpty()) ? value : "");

    attribute.setValueType(isNumericType ? "Numeric" : "Text");

    if (isNumericType && value != null && !value.isEmpty()) {
      try {
        attribute.setNumericValue(new BigDecimal(value));
      } catch (NumberFormatException e) {
        attribute.setNumericValue(BigDecimal.ZERO);
      }
    }

    attribute.setUnitOfMeasure(""); // TODO: determine the unit of measure

    return attribute;
  }

  private static boolean isNumericType(Class<?> type) {
    return type == int.class
        || type == Integer.class
        || type == double.class
        || type == Double.class
        || type == float.class
        || type == Float.class
        || type == long.class
        || type == Long.class
        || type == short.class
        || type == Short.class
        || type == byte.class
        || type == Byte.class
        || type == BigDecimal.class;
  }

  private static boolean isAcceptableClass(Class<?> type) {
    if (type.isPrimitive() || type.isArray() || type.isEnum()) {
      return false;
    }

    if (type.getName().startsWith("java.lang")) {
      return false;
    }

    return !type.getName().startsWith(JAVA_UTIL_UUID) && !type.getName().startsWith(JAVA_TIME);
  }

  private static boolean isAcceptableList(Class<?> type) {
    if (type.isArray()) {
      Class<?> componentType = type.getComponentType();
      if (componentType != null && componentType.getName().startsWith(JAVA_UTIL_UUID)) {
        return false;
      }
    }

    return List.class.isAssignableFrom(type);
  }

  private static String getAttributeName(String name, String path, Integer index) {
    if (index != null) {
      name = String.format("%s%s", StringUtils.capitalize(name), index);
    }

    if (path != null && !path.isEmpty()) {
      name = String.format("%s/%s", path, StringUtils.capitalize(name));
    }

    return name;
  }

  private static String getAttributeName(String name, String path) {
    return getAttributeName(name, path, null);
  }

  @SuppressWarnings("java:S3011")
  private static <T> void processArray(
      T tool, String path, List<AttributesDTO> attributes, Field field) {
    Object value;
    try {
      field.setAccessible(true);
      value = field.get(tool);
    } catch (IllegalAccessException e) {
      log.error("[AttributesUtil][processList] field " + field.getName() + " " + e.getMessage());
      value = null;
    }
    for (int i = 0; i < java.lang.reflect.Array.getLength(value); i++) {
      Object arrayElement = java.lang.reflect.Array.get(value, i);
      String name = getAttributeName(field.getName(), path, i + 1);
      attributes.addAll(getAttributes(arrayElement, name));
    }
  }

  private static boolean isArray(Class<?> type) {
    if (type.isArray()) {
      Class<?> componentType = type.getComponentType();
      return componentType == null || !componentType.getName().startsWith(JAVA_UTIL_UUID);
    }
    return false;
  }
}
