/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.document.ForagePennStateTool;
import com.app.cargill.document.ForagePennStateToolItem;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ForagePenStateCalculationTest {

  @InjectMocks private ForagePenStateCalculation foragePenStateCalculation;

  @Test
  void calculateFields() {
    ForagePennStateTool foragePennStateTool =
        ForagePennStateTool.builder()
            .inputs(
                List.of(
                    ForagePennStateToolItem.builder()
                        .top(12.1)
                        .mid1(10.0)
                        .mid2(3.3)
                        .tray(12.5)
                        .build()))
            .build();

    foragePennStateTool = foragePenStateCalculation.calculateFields(foragePennStateTool);
    assertTrue(
        foragePennStateTool.getInputs().stream()
            .allMatch(
                item ->
                    item.getTopOnScreenPercentage() != null
                        && item.getMid1OnScreenPercentage() != null
                        && item.getMid2OnScreenPercentage() != null
                        && item.getTrayOnScreenPercentage() != null));
  }

  @Test
  void calculateFieldsWithNull() {
    ForagePennStateTool foragePennStateTool =
        ForagePennStateTool.builder()
            .inputs(List.of(ForagePennStateToolItem.builder().build()))
            .build();

    foragePennStateTool = foragePenStateCalculation.calculateFields(foragePennStateTool);
    assertTrue(
        foragePennStateTool.getInputs().stream()
            .allMatch(
                item ->
                    item.getTopOnScreenPercentage() != null
                        && item.getMid1OnScreenPercentage() != null
                        && item.getMid2OnScreenPercentage() != null
                        && item.getTrayOnScreenPercentage() != null));
  }

  @Test
  void verifyCalculation() {

    ForagePennStateTool foragePennStateTool =
        ForagePennStateTool.builder()
            .inputs(
                List.of(
                    ForagePennStateToolItem.builder()
                        .top(90.0)
                        .mid1(181.0)
                        .mid2(65.0)
                        .tray(127.0)
                        .build()))
            .build();

    foragePennStateTool = foragePenStateCalculation.calculateFields(foragePennStateTool);

    assertEquals(463.0, foragePennStateTool.getInputs().get(0).getTotalScreenAmount());

    assertEquals(19.4, foragePennStateTool.getInputs().get(0).getTopOnScreenPercentage());

    assertEquals(39.1, foragePennStateTool.getInputs().get(0).getMid1OnScreenPercentage());

    assertEquals(14.0, foragePennStateTool.getInputs().get(0).getMid2OnScreenPercentage());

    assertEquals(27.4, foragePennStateTool.getInputs().get(0).getTrayOnScreenPercentage());

    assertEquals(19.4, foragePennStateTool.getAvgTopOnScreenPercentage());

    assertEquals(39.1, foragePennStateTool.getAvgMid1OnScreenPercentage());

    assertEquals(14.0, foragePennStateTool.getAvgMid2OnScreenPercentage());

    assertEquals(27.4, foragePennStateTool.getAvgTrayOnScreenPercentage());

    assertEquals(0.0, foragePennStateTool.getInputs().get(0).getTopStdDevValue());

    assertEquals(0.0, foragePennStateTool.getInputs().get(0).getMid1StdDevValue());

    assertEquals(0.0, foragePennStateTool.getInputs().get(0).getMid2StdDevValue());

    assertEquals(0.0, foragePennStateTool.getInputs().get(0).getTrayStdDevValue());
  }
}
