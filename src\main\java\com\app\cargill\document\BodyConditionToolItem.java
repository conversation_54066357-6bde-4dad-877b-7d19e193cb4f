/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BodyConditionToolItem implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("PenId")
  private UUID penId;

  @JsonProperty("PenName")
  private String penName;

  @JsonProperty("BodyConditionScoreVisitsSelected")
  private List<UUID> bodyConditionScoreVisitsSelected;

  @JsonProperty("BodyConditionScores")
  private List<BodyConditionToolItemScoreItem> bodyConditionScores;

  @JsonProperty("ToolStatus")
  private ToolStatuses toolStatus;

  @JsonProperty("DaysInMilk")
  private int daysInMilk;

  @JsonProperty("Milk")
  private Double milk;

  @JsonProperty("IsToolItemNew")
  private boolean isToolItemNew;

  @JsonProperty("IsFirstTimeWithScore")
  public boolean isFirstTimeWithScore;

  @JsonProperty("AverageBCS")
  public Double averageBCS;

  @JsonProperty("StandardDeviationBCS")
  public Double standardDeviationBCS;
}
