/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class FeedCosts implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("ForageFeedCostPerCowPerDay")
  private Double forageFeedCostPerCowPerDay;

  @JsonProperty("GrainsCostPerCowPerDay")
  private Double grainsCostPerCowPerDay;

  @JsonProperty("TotalOnFarmFeedCostPerCowPerDay")
  private Double totalOnFarmFeedCostPerCowPerDay;

  @JsonProperty("PurchasedBulkFeedPerCowPerDay")
  private Double purchasedBulkFeedPerCowPerDay;

  @JsonProperty("PurchasedBagsFeedPerCowPerDay")
  private Double purchasedBagsFeedPerCowPerDay;

  @JsonProperty("TotalPurchasedCostPerCowPerDay")
  private Double totalPurchasedCostPerCowPerDay;

  @JsonProperty("TotalFeedCostPerCowPerDay")
  private Double totalFeedCostPerCowPerDay;

  @JsonProperty("FeedCostPerKgOfBF")
  private Double feedCostPerKgOfBF;

  @JsonProperty("FeedCostPerHlOfMilk")
  private Double feedCostPerHlOfMilk;

  @JsonProperty("ForagePercentage")
  private Double foragePercentage;

  // Getters and setters...
}
