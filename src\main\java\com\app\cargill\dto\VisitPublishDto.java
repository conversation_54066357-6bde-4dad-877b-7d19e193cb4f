/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitPublishDto {

  @NotNull(message = "Account ID is mandatory")
  private UUID accountId;

  @NotNull(message = "Site ID is mandatory")
  private UUID siteId;

  @NotNull(message = "Visit ID is mandatory")
  private UUID visitId;
}
