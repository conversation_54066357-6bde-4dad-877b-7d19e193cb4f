/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.constants.Country;
import java.io.Serial;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AccountRoles implements Serializable {
  @Serial private static final long serialVersionUID = 1L;
  private UUID accountId;
  private String goldenRecordId;
  private Country country;
  private Set<UserRole> users = new HashSet<>();

  @AllArgsConstructor
  @NoArgsConstructor
  @Data
  public static class UserRole implements Serializable {
    @Serial private static final long serialVersionUID = 1L;
    private String email;
    private String role;
  }
}
