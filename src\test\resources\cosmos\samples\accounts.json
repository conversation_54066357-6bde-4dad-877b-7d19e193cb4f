{"GoldenRecordId": "001j00000196RhoAAE", "AccountName": "HOHLS FARM SUPPLY", "LegalName": null, "Type": null, "AccountType": 1, "Contacts": [{}], "PrimaryContactId": "********-0000-0000-0000-************", "PrimaryContactTitle": null, "PrimaryContactPhoneNumber": null, "LastUpdateUserId": null, "AccountValidated": true, "PhotoId": "********-0000-0000-0000-************", "NeedsSync": true, "Users": [], "OwnerId": "<EMAIL>", "ProspectStatus": null, "IsDuplicate": false, "AutoValidate": false, "SourceSystem": "SFDC", "ParentAccountID": null, "SubTypeID": 8, "ExternalLeadSourceID": null, "BuyingGroupID": null, "ExternalBuyingGroupID": null, "AccountStatus": 3, "DateOfLastVisit": null, "DateOfLastCall": null, "WonLostId": null, "WonLostComments": null, "CreditFlag": false, "PriceFlag": false, "ServiceFlag": false, "PerformanceFlag": false, "PortfolioFlag": false, "BusinessSolutionFlag": false, "QualityFlag": false, "OtherFlag": false, "BusinessID": 7, "DefaultCargillPlantID": null, "DefaultCustServiceID": null, "LastModificationDate": null, "Active": false, "BrandId": null, "NineBoxStepTwoID": 10, "SegmentStepOneId": 8, "CompanyEmail": null, "LastInvoiceDate": null, "LastOrderDate": null, "DeliveryInstructions": null, "ERPPayerId": null, "ERPShipToId": null, "IsServicedbyCSPro": false, "LastAdminUpdate": null, "LastInvoicesInfo": null, "LastOrdersInfo": null, "Phone": "**************", "ReqProcessingLog": null, "Liabilities": null, "LimitChangeReasonId": null, "MarketInfluencer": false, "OtherActivityProduction": null, "PersonalID": null, "PreviousStatus": null, "ReasonDescription": null, "Securities": null, "Assets": null, "VolumeEstimate": null, "MarginEstimate": null, "PhysicalAddress": {"Street": "W11942 HIGHWAY 33", "City": "PORTAGE", "StateOrProvince": "WISCONSIN", "PostalCode": "53901", "Country": "United States", "AddressID": "********-0000-0000-0000-************", "CountyCommunity": null}, "CorrespondenceAddress": null, "SocialMediaAddress": null, "WebSiteAddress": null, "lstOtherBU": [], "ExternalParentAccountID": null, "AccountCurrency": 0, "ApprovalStatus": null, "AccountNumber": null, "AvailabilityOnMarket": 1, "ChangeAccountType": false, "NewAccountType": null, "ConsumerStatus": null, "CustomerStatus": null, "CourtId": null, "ERPIdLength": "true", "ERPPayerIdLength": "0.0", "ERPShiptoIdLength": "0.0", "IsMobileFirst": false, "VeterinaryId": null, "WonLost": null, "WonLostReasonCode": null, "CurrentUserProfileNameandId": null, "ERPIdLengthvalidatoriserror": null, "ExternalId": null, "LastModifiedBy": null, "OwnerProfileNameandId": null, "CustomerCode": null, "UserRoles": [{"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}, {"UserRole": "Account Representative", "UserName": "<EMAIL>", "UserBusinessUnit": 2}], "Description": null, "AdditionalInfo": {}, "SalesTerritory": null, "SubBrandId": null, "id": "04be8c0e-4d0d-403d-9476-9e84f48d83d1", "CreateUser": "roshnir<PERSON><EMAIL>.18", "IsDeleted": true, "LastModifyUser": "anure<PERSON><EMAIL>", "CreateTimeUtc": "2017-03-09T12:10:26Z", "LastModifiedTimeUtc": {"Date": "2022-07-13T12:42:54.7906862Z", "Epoch": **********}, "LastSyncTimeUtc": "2022-07-13T12:42:54.7906862Z", "IsNew": false}