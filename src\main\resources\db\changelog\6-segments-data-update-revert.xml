<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="006" author="Aweem">
		<sql>
			TRUNCATE TABLE segments;
			INSERT INTO public.segments(segment_document)
			VALUES  ('{"key":1, "value":"<PERSON>"}'),
							('{"key":9, "value":"Baxter"}'),
							('{"key":8, "value":"<PERSON>"}'),
							('{"key":10, "value":"End-User"}'),
							('{"key":4, "value":"Kobe"}'),
							('{"key":2, "value":"Mila"}'),
							('{"key":6, "value":"<PERSON>"}'),
							('{"key":3, "value":"<PERSON>"}'),
							('{"key":5, "value":"Walton"}'),
							('{"key":7, "value":"Sonya"}');
		</sql>
	</changeSet>

</databaseChangeLog>