/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.constants.UserSettingsBrands;
import com.app.cargill.document.UserFavourites;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class UserPreferenceDto extends BaseDto {

  private String userId;
  private UnitOfMeasureKeys unitOfMeasure;
  private Instant lastSyncOperationDateTime;
  private Instant lastEulaVersionAccepted;
  private List<UserSettingsBrands> brandList;
  private String eulaContent;
  private Currencies selectedCurrency;
  private Instant lastPrivacyVersionAccepted;

  private BCSPointScale bcsPointScale;
  private String createUser;
  private UserFavourites favourites;
  private List<CountryToolResponseDto> countryTools;

  @Builder.Default private boolean showBCSAnimalAnalysisToast = true;
  private MilkUreaMeasure defaultMilkUreaMeasure;

  private MilkPickup defaultMilkPickup;

  @Builder.Default private Map<String, String> defaultValues = new HashMap<>();
}
