/* Cargill Inc.(C) 2022 */
package com.app.cargill.salesforce.errors;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class LiftErrorResponseConstants {

  public static final String LIFT_ERROR_MESSAGE =
      "Sync failed due to unknown reasons; please contact the admin for resolution";

  public static final String USER_NOT_FOUND =
      "USER not found on LIFT; please contact the admin for resolution";

  public static final String ACCOUNT_NOT_SYNCED_TO_LIFT =
      "Account was not synced to LIFT; please contact the admin for resolution";

  public static final String SITE_NOT_SYNCED_TO_LIFT =
      "Site was not synced to LIFT; please contact the admin for resolution";

  private LiftErrorResponseConstants() {}
}
