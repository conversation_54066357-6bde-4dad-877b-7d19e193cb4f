/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.app.cargill.converter.InstantDateTimeDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactCrescendo implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("AccountId")
  private String accountId;

  // The wrong spelling was inherited by the legacy system
  @JsonProperty("GoldenRecordAcountId")
  private String goldenRecordAccountId;

  @JsonProperty("SFDCContactId")
  private String sfdcContactId;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("FunctionId")
  private FunctionIdCrescendo functionId;

  @JsonProperty("PhoneNumber")
  private String phoneNumber;

  @JsonProperty("MobilePhoneNumber")
  private String mobilePhoneNumber;

  @JsonProperty("EmailAddress")
  private String emailAddress;

  @JsonProperty("Comments")
  private String comments;

  @JsonProperty("Salutation")
  private String salutation;

  @JsonProperty("GoldenRecordId")
  private String goldenRecordId;

  @JsonProperty("NameId")
  private String nameId;

  @JsonProperty("Title")
  private String title;

  @JsonProperty("MailingAddress")
  private AddressCrescendo mailingAddress;

  @JsonProperty("OtherAddress")
  private AddressCrescendo otherAddress;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("Mobile")
  private String mobile;

  @JsonProperty("EmailId")
  private String emailId;

  @JsonProperty("Description")
  private String description;

  @JsonProperty("PrimaryOwner")
  private Boolean primaryOwner;

  @JsonProperty("ContactOwner")
  private String contactOwner;

  @JsonProperty("BusinessId")
  private BusinessUnitCrescendo businessId;

  @JsonProperty("ReportsToID")
  private String reportsToID;

  @JsonProperty("ExternalReportsToID")
  private String externalReportsToID;

  @JsonProperty("SecondaryEmail")
  private String secondaryEmail;

  @JsonProperty("PreferredMethodId")
  private String preferredMethodId;

  @JsonProperty("Website")
  private String website;

  @JsonProperty("Department")
  private String department;

  @JsonProperty("UserID")
  private String userID;

  @JsonProperty("ContactCurrency")
  private CurrencyCrescendo contactCurrency;

  @JsonProperty("CreatedBy")
  private String createdBy;

  @JsonProperty("DataDotComKey")
  private String dataDotComKey;

  @JsonProperty("ExternalId")
  private String externalId;

  @JsonProperty("FlowName")
  private String flowName;

  @JsonProperty("HomePhone")
  private String homePhone;

  @JsonProperty("Languages")
  private String languages;

  @JsonProperty("LastModifiedBy")
  private String lastModifiedBy;

  @JsonProperty("LastModifiedID")
  private String lastModifiedID;

  @JsonProperty("LeadSource")
  private LeadSourceCrescendo leadSource;

  @JsonProperty("Level")
  private ContactLevelCrescendo level;

  @JsonProperty("MobileFirst")
  private Boolean mobileFirst;

  @JsonProperty("Primary")
  private Boolean primary;

  @JsonProperty("id")
  private String id;

  @JsonProperty("CreateUser")
  private String createUser;

  @JsonProperty("IsDeleted")
  private Boolean isDeleted;

  @JsonProperty("LastModifyUser")
  private String lastModifyUser;

  @JsonProperty("CreateTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant createTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastModifiedTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonDeserialize(using = InstantDateTimeDeserializer.class)
  private Instant lastSyncTimeUtc;

  @JsonProperty("IsNew")
  private Boolean isNew = false;

  @JsonProperty("MarketingId")
  private List<String> marketingId = new ArrayList<>();
}
