welcome.message=Greetings {0}
AppName=Labyrinth
LoginViewModel.Copyright=\u00A9 {0} Cargill, incorporated. All rights reserved.
LoginViewModel.ErrorDescription=Username and password must not be blank.
LoginViewModel.ErrorTitle=Login error
LoginViewModel.InvalidMessage=The username or password supplied was not valid to login.
LoginViewModel.InvalidMessageTitle=Invalid login
LoginViewModel.LoginPrompt=Cargill login
LoginViewModel.PasswordLabel=Password
LoginViewModel.EmailLabel=Email
MenuViewModel.LogoutPrompt=Logout
MenuViewModel.Menu=Menu
MainViewModel.EmailLabel=Email
OKLabel=OK
LoginViewModel.NetworkErrorMessageTitle=Network error
LoginViewModel.NetworkErrorMessage=There is currently no network available.
UserPreferencesViewModel.MainHeading=The following option can be changed later in the app settings.
UserPreferencesViewModel.UnitOfMeasure=Select unit of measure
UserPreferencesViewModel.Metric=Metric
UserPreferencesViewModel.Imperial=Imperial
UserPreferencesViewModel.Title=User settings
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.CurrencySelection=Currency selection
UserPreferencesViewModel.SelectCurrency=Select a currency
SelectCurrencyViewModel.Title=Currency
HomeViewModel.Title=Sales field tool
HomeViewModel.DashboardTab=Dashboard
HomeViewModel.CustomersTab=Customers
HomeViewModel.ProspectsTab=Prospects
HomeViewModel.SyncWithDate=Sync - last sync date: {0:MM/dd/yy}
HomeViewModel.SyncWithTime=Sync - last sync time: {0:hh:mm tt}
HomeViewModel.SyncWithDash=Sync -
HomeViewModel.Settings=Settings
HomeViewModel.PrivacyStatement=Privacy statement
HomeViewModel.AutoSync=Auto sync
HomeViewModel.Logout=Logout
HomeViewModel.Eula=EULA
ComfortToolsViewModel.HealthHeading=Please select a tool from the list below to begin your visit.
ComfortToolsViewModel.ComfortHeading=Please select a tool from the list below to begin your visit.
ComfortToolsViewModel.ComfortToolsTitle=Comfort tools
ComfortToolsViewModel.ComfortToolsList=TOOLS
ComfortToolsViewModel.HeatstressEvaluationTitle=Heat stress evaluation
ComfortToolsViewModel.PenTimeTitle=Pen time
CustomerDetailViewModel.CustomerTitle=Customer profile
ProspectProfileViewModel.ProspectTitle=Prospect details
ProspectProfileViewModel.ProspectInfo=Prospect info
NewProspectViewModel.Customer=Customer
SiteDetailsResourcesViewModel.Title=Resources
SiteDetailViewModel.NewVisit=Begin a new visit
NewVisitViewModel.Title=Visit details
CustomerDetailViewModel.MainHeading=SITES
SiteDetailViewModel.Reports=Dairy enteligen report
SiteDetailViewModel.Summary=Summary
SiteDetailViewModel.Detailed=Detailed
SiteDetailViewModel.SiteSetup=Site setup
SiteDetailViewModel.Title=Site details
SiteDetailViewModel.MainHeading=VISITS
SiteDetailViewModel.DairyEnteligenReport=Dairy enteligen report
SiteDetailViewModel.GeneralCustomerSiteSetup=General customer site setup
SiteDetailViewModel.AnimalInputsSite=Animal inputs site
SiteDetailViewModel.DietInputsSiteLactating=Diet inputs, site (lactating animals)
VisitViewModel.VisitTitle=Visit name
VisitViewModel.ComfortItem=Comfort
VisitViewModel.HealthItem=Health
VisitViewModel.NutritionItem=Nutrition
VisitViewModel.ProductivityItem=Productivity
VisitViewModel.CategorySection=Tool categories
VisitViewModel.SiteVisitSummary=Site visit summary
VisitViewModel.PublishVisit=Publish visit
VisitViewModel.PublishNotes=Publish notes
VisitViewModel.PublishNotesPrompt=Are you sure you want to publish notes of this visit? It cannot be undone.
VisitViewModel.PublishPrompt=Are you sure you want to publish this visit? It cannot be undone.
VisitViewModel.Publish=Publish
VisitViewModel.WalkthroughReport=Walkthrough report
VisitViewModel.Delete=Delete visit
VisitViewModel.DeletePrompt=Are you sure you want to delete this visit? It cannot be undone.
CustomerDetailViewModel.NewVisit=New visit
CustomerDetailViewModel.NewSite=Add a new site
HeatstressData=Heat stress data
HeatstressCalculations=Heat stress calculations
HeatstressChart=Heat stress chart
VisitNotesViewModel.Close=Close
VisitNotesViewModel.New=New
VisitNotesViewModel.Title=Visit notebook
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=Observation
VisitNotesViewModel.Action=Action
VisitNotesViewModel.Task=Task
VisitNotesViewModel.Event=Event
EditNoteViewModel.Cancel=Cancel
HeatstressTableViewModel.Title=Heat stress evaluation
HeatstressTableViewModel.HeatstressDataTab=Data input
HeatstressTableViewModel.HeatstressChartTab=Charts
HeatstressDataEntryViewModel.AnimalInputs=Animal inputs
HeatstressDataEntryViewModel.Milk=Milk ({0})
EditNoteViewModel.Save=Save
EditNoteViewModel.Title=Note
EditNoteViewModel.Close=Close
EditNoteViewModel.CreatedByMetadata=Created on {0} @ {1} by {2}
EditNoteViewModel.LastUpdatedByMetadata=Last modified on {0} @ {1} by {2}
Yes=Yes
No=No
Delete=Delete
EditNoteViewModel.DeletePrompt=Do you want to delete this note?
EditNoteViewModel.DeleteImagePrompt=Do you want to delete this image?
EditNoteViewModel.DeleteVideoPrompt=Do you want to delete this video?
HeatstressDataEntryViewModel.DMI=Dmi ({0})
HeatstressDataEntryViewModel.NEL=Nel (mcal/{0})
HeatstressDataEntryViewModel.MilkFat=Milk fat (%)
HeatstressDataEntryViewModel.LactatingAnimals=Lactating animals
HeatstressDataEntryViewModel.CurrentMilkPrice=Current milk price ({0}/{1})
HeatstressDataEntryViewModel.MilkProtein=Milk protein (%)
HeatstressDataEntryViewModel.Weather=Weather
HeatstressDataEntryViewModel.Temperature=Temperature ({0})
HeatstressDataEntryViewModel.Humidity=Humidity (%)
HeatstressDataEntryViewModel.Exposure=Exposure
HeatstressDataEntryViewModel.HoursExposed=Hours of sun
HeatstressChartViewModel.HeatstressEvalLabel=Temperatures corrected for average temperature and relative humidity (without sunshine)
HeatstressChartViewModel.TempHumidIndex=Temperature humidity index
HeatstressChartViewModel.IntakeAdjustment=Intake adjustment
HeatstressChartViewModel.DMIReduction=Dmi reduction
HeatstressChartViewModel.EstimateDryMatter=Estimated dry matter intake
HeatstressChartViewModel.ReductionDMI=Reduction in dmi
HeatstressChartViewModel.LossEnergyConsumed=Loss of energy consumed
HeatstressChartViewModel.EnergyEquivMilkLoss=Energy equivalent milk loss
HeatstressChartViewModel.MilkValueLossPerDay=Milk value loss / day
HeatstressChartViewModel.MilkValueLossPerMonth=Milk value loss / month
HeatstressChartViewModel.Mcal=Mcal
ErrorDescription=An error occured reading or writing your information.
ErrorTitle=Error
NutritionViewModel.NutritionLabel=Please select a tool from the list below to begin your visit.
NutritionViewModel.NutritionTools=Nutrition tools
NutritionViewModel.NutritionForage=Forage audit
NutritionViewModel.NutritionPile=Forage inventories
NutritionViewModel.NutritionToolsList=Tools
ForageAuditViewModel.Title=Forage audit
ForageAuditViewModel.ForageHeading=Basic forage information
ForageAuditViewModel.ForageDetail=Forage quality is the foundation of any dairy nutrition program and is a key to the overall profitability of the farm. The forage audit scorecard can be used to evaluate the current forage management practices used on the farm and recommend the key opportunity areas for improvement. The scorecard is organized into key management areas. You can cover all areas during a visit or select only those that are important at the time. Additional management resources are available to help improve those areas where improvement opportunities exist.
ForageAuditViewModel.ForageAuditScorecard=Forage audit scorecard
ForageAuditScorecard=Forage audit scorecard
ForageAuditViewModel.Resources=Resources
HeatstressGreen=Stress threshold
HeatstressYellow=Mild-moderate stress
HeatstressOrange=Moderate-severe stress
HeatstressRed=Severe stress
WeightMetric=kg
WeightImperial=lbs
WeightImperialCWT=CWT
EnergyMetric=Mcal/kg
EnergyImperial=Mcal/lb
Mcal=Mcal
TemperatureMetric=\u00B0C
TemperatureImperial=\u00B0F
VolumeImperial=gal
VolumeMetric=ml
ForageScorecardViewModel.SurveyCategories=Forage audit scorecards
ForageScorecardViewModel.SurveyOfForages=Forage management
ForageScorecardViewModel.Harvest=Forage quality in the ration
ForageScorecardViewModel.BunkersAndPiles=Bunkers and piles
ForageScorecardViewModel.TowerSilos=Tower silos
ForageScorecardViewModel.ViewOverallForageScore=View overall forage score
ViewOverallForageScore=View overall forage score
OverallForageScoreDetails=To view the overall forage score, first complete at least one of the surveys in the list above.
ForageScorecardViewModel.SilageBags=Silage bags
ForageScorecardViewModel.Baleage=Baleage
ForageScorecardViewModel.MaintainingForageQuality=Maintaining forage quality
SurveyOfForages_InventoryIsMonitored=How often is forage inventory monitored?
SurveyOfForages_AnnualCowNumAndForageNeeds=Are the number of cows and forage needs planned annually?
SurveyOfForages_SilosSizedForCapacity=Are forage storage units sized for capacity compared to the needs of the dairy herd? (no overfilling)
SurveyOfForages_LooseOrFacedFeedWithin=Loose or "faced" feed is fed within:
SurveyOfForages_NoLooseFeedRemaining=Are feed refusals removed and measured daily?
SurveyOfForages_InspectedForSpoilageAndMold=Are all forages inspected for spoilage and mold and is spoiled forage discarded?
SurveyOfForages_VisibleSignsOfSoil=Is the silage free from any sign of soil contamination?
SurveyOfForages_AshLevelsInHaylage=What are ash levels?
SurveyOfForages_AshLevelsInCornSilage=What are ash levels?
SurveyOfForages_AshLevelsInOtherSilage=What are ash levels?
SurveyOfForages_CornSilageScoreMonitored=What is the kernel hardness score in the corn silage?
SurveyOfForages_CornSilageProcessingScore=What is the length of chop of the sample? (cargill forage particle score)
SurveyOfForages_ButyricAcidLevelsInHaylage=What are the butyric acid levels?
SurveyOfForages_LacticAcidToAceticAcidLevels=What are the lactic to acetic acids ratios?
Harvest_AdequateEquipmentAndLabor=How long does it take to complete harvest? (adequate equipment and labor)
Harvest_WholePlantMoistureDetermined=Is whole plant moisture determined for each field?
Harvest_CornSilageMoistureRangeConsistent=What is the average moisture content of the silage? (moisture should be consistent in 90% of samples)
Harvest_ForagesHarvestedAtProper=Are forages harvested at proper maturity/moisture for crop type and storage facility?
Harvest_LengthOfCutMonitored=Is the length of cut monitored with penn state shaker box at harvest?
Harvest_KPScoreIsMonitored=How many whole kernels are present in a 1l (32 ounce) sample?
Harvest_UseSilageAdditive=Are silage additives, inoculants or aerobic stabilizers used?
Harvest_ForageHarvestingDocumented=Are forage harvesting conditions, field and storage locations documented?
BunkersAndPiles_CleanlinessOfFeedArea=What is the cleanliness of the feed area?
BunkersAndPiles_PackingInitialSpreadLayers=Packing: are layers 15 cm (6 inches) or less?
BunkersAndPiles_PorosityScoresConsistently=What is the porosity score, with respect to the dm?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Is the pile and bunker slope less than a 3:1 run to rise ratio?
BunkersAndPiles_TiresSplitsTouching=Are the tires/splits touching?
BunkersAndPiles_SideWallsSealedPlastic=Are side walls sealed with plastic?
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Are bunkers/piles covered with 2 layers of plastic and a layer of non-permeable plastic?
BunkersAndPiles_SealedImmedAfterPack6milPlastic=How long after packing are bunkers/piles sealed with 8mm plastic?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Is the face smooth? (no indication of disrupted layers allowing oxygen penetration)
BunkersAndPiles_FaceRemoveRate=What is the face removal rate?
BunkersAndPiles_LooseOrFacedFeedIsFed=Loose or "faced" feed is fed:
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=To what frequency is the cover plastic removed from silage?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Is silo covered for a month after filling if not used immediately?
TowerSilos_SiloFillingTimeof3DaysOrLess=Is the filling time per silo 3 days or less?
TowerSilos_FaceRemovalRateGreaterThan4Inches=Is the removal rate greater than 10 cm (4 inches) per day?
SilageBags_BagsPlacedOnStableWellManagedSurface=Are bags placed on a stable, well managed all season surface?
SilageBags_TrashVegRodentControlledAroundBags=Are trash, vegetation and rodents controlled around bags?
SilageBags_BonusSecureCoverIsUsed=Is a security cover used?
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Are bags inspected for pest hole damage and repaired on a regular basis?
SilageBags_FaceRemovalRate=What is the face removal rate?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Is the face clean and well managed with no indication of loose feed heating and shrinking?
SilageBags_PorosityScoresConsistently=What is the porosity score, with respect to the dm?
Baleage_BagsPlacedOnStableWellManagedSurface=Are bales placed on a stable, well managed all season surface?
Baleage_TrashVegRodentControlledAroundBags=Are trash, vegetation and rodents controlled around bales?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Are bales inspected for pest hole damage and repaired on weekly basis?
Baleage_WaterShedsOffPlasticNotIntoBaleage=Does the water shed off plastic and not into baleage? (challenge with large square bales)
Baleage_AreBalesWrappedWith=Are bales wrapped with:
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Is a stabilizer/mold inhibitor used in tmr during hot/humid weather?
MaintainingForageQuality_TMRMixHasPleasantAroma=Does the tmr mix smell good?
MaintainingForageQuality_TMRMixIsCoolToTouch=Is the tmr mix cool to the touch?
Quarterly=Quarterly
SemiAnnually=Semi annually
Annually=Annually
OneToSixHours=1 to 6 hours
SixToTwelveHours=6 to 12 hours
GreaterThanTwelveHours=Greater than 12 hours
LessThan4Days=Less than 4 days
FourToSevenDays=4 to 7 days
GreaterThanSevenDays=Greater than 7 days
NoWholeKernals=No whole kernals
LessThanFiveWholeKernals=Less than 5 whole kernals
GreaterThanFive=Greater than 5
DontKnow=Don't know
LessThanOneHour=Less than 1 hour
WithinEightHours=Within 8 hours
GreaterThan8Hours=Greater than 8 hours
TwelveInchesOrGreater=Greater than 15 cm (6 inches)
SixToTwelveInches=7.5 to 15 cm (3 to 6 inches)
LessThanSixInches=Less than 7.5 Cm (3 inches)
ForageScorecardViewModel.Yes=Yes
ForageScorecardViewModel.No=No
GreaterThanSixHours=Greater than 6 hours
ThreeTimesPerWeek=3 times per week
TwicePerWeek=Twice per week
OncePerWeek=Once per week
GreaterThanThirtySixInchesPerDay=Greater than 30 cm (12 inches) per day
TwentyFourToThirtySixInchesPerDay=15 to 30 cm (6 to 12 inches) per day
LessThanTwentFourInchesPerDay=Less than 15 cm (6 inches) per day
MoreThan8LayersOfPlastic=More than 8 layers of plastic
SixToEightLayers=6 to 8 layers
LessThanSixLayers=Less than 6 layers
ForageAuditScorecardScoreViewModel.OverallForageScore=Include in overall forage score
Next=Next
Finish=Finish
QuestionViewModel.Close=Close
ForageAuditScorecardResultsViewModel.ScoreTab=Score
ForageAuditScorecardResultsViewModel.ResponsesTab=Responses
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Forage audit improvements
ForageAuditScorecardResponsesViewModel.ResponsesTab=Forage audit responses
ForageAuditScorecardResultsViewModel.ImprovementsTab=Improvements
ForageAuditScorecardScoreViewModel.GoodIndicator=Good
ForageAuditScorecardScoreViewModel.Title=Forage audit overall score
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Improvements
ForageScorecardResultsViewModel.Title=Baleage
SurveyOfForages=Forage management
Harvest=Forage quality in the ration
BunkersAndPiles=Bunkers and piles
TowerSilos=Tower silos
SilageBags=Silage bags
Baleage=Baleage
MaintainingForageQuality=Maintaining forage quality
Improvements=Improvements
QuestionViewModel.SurveyOfForages=Forage management
QuestionViewModel.Harvest=Forage quality in the ration
QuestionViewModel.BunkersAndPiles=Bunkers and piles
QuestionViewModel.TowerSilos=Tower silos
QuestionViewModel.SilageBags=Silage bags
QuestionViewModel.Baleage=Baleage
QuestionViewModel.MaintainingForageQuality=Maintaining forage quality
QuestionTableTitle=Question {0} of {1}
ResourcesViewModel.Title=Forage audit resources
Making_Feed_InventoryFOF=Making a feed inventory
DairyEnteligenFarmReportsources=Dairy enteligen sources of information
FAQDairyEnteligenFarmReportandDDW=Faq dairy enteligen farm report
StorageCalculators=Storage calculators
DecidingSilageStorage=Deciding on a silage storage type
PreventingStorageLosses=Preventing storage losses
FeedoutLossesForageStorageSys=Feedout losses from forage storage systems
CargillForageLabKPTest=Cargill forage lab kp test
FermentationAnalysisSilageQT=Fermentation analysis &amp; silage quality testing
GettingtheMostOutofYourForage=Getting the most out of your forage
SilagePrevention1st=Silage prevention: 1st things 1st
CornSilageResources=Corn silage resources
CornSilageStopGo=Corn silage stop-n-go
CropCharacteristicsDecisionGuide=Crop characteristics decision guide
RecommendedTLCSettings=Recommended tlc settings
PennStateShakerBoxForageResults=Penn state shaker box forage results
FieldKPTest=Field kp test
AdjustingKPtoAssureSuccess=Adjusting kp to assure success
HowtoGetBetterKPResults=How to get better kp results
InoculantFQAs=Faq about inoculants
ChoosingtheCorrectAdditive=Choosing the right inoculant
PhotoExamples=Photo examples
Porosity=Porosity
ThreePotentialStorageSolutions=Three potential storage solutions
FeedOutRatesFilmsStorageSysExamined=Feed out rates, films &amp; storage systems examined
ManagingForageinTowerSilos=Managing forage in tower silos
BaggedConventionalSilage=Bagged conventional silage
ManagingForageinSiloBags=Managing forage in silo bags
DensityLossesinPressedBagSilos=Density &amp; losses in pressed bag silos
BaleageFQAs=Baleage faqs
CornSilageKernel=Baleage faqs
ScorecardPrompt=Please complete the following survey to view the {0} scorecard
PromptOK=Ok
PromptCancel=Cancel
StatusInProgress=In progress
StatusCompleted=Completed
Resources=Resources
SiteDetailViewModel.Resources=Resources
Answers=Answers
ForageScorecardViewModel.Title=Forage audit scorecard
SurveyCategories=Survey categories
NotebookSectionForageAuditLanding=Forage audit
NotebookSectionForageAudit=Forage audit scorecard
NotebookSectionForageAuditBaleage=Forage audit-baleage
NotebookSectionForageAuditBunkersPiles=Forage audit-bunkers and piles
NotebookSectionForageAuditHarvest=Forage audit-harvest
NotebookSectionForageAuditMaintainingQuality=Forage audit-maintaining forage quality
NotebookSectionForageAuditSilageBags=Forage audit-silage bags
NotebookSectionForageAuditSurveyOfForages=Forage audit-survey of forages
NotebookSectionForageAuditTowerSilos=Forage audit-tower silos
NotebookSectionComfortTools=Comfort tools
NotebookSectionHeatstressCalculations=Heat stress-calculations
NotebookSectionHeatstressChart=Heat stress-chart
NotebookSectionHeatstressData=Heat stress-data
NotebookSectionNutritionTools=Nutrition tools
NotebookSectionVisit=Visit
NotebookWalkthroughReportLanding=Walkthrough report
NotebookManureScoreHerdAnalysisInputs=Manure score herd-inputs
NotebookManureScoreHerdAnalysisGoals=Manure score herd-goals
NotebookManureScoreHerdAnalysisResults=Manure score herd-results
NotebookMetabolicIncidenceInputs=Metabolic incidence inputs
NotebookMetabolicIncidenceOutputs=Metabolic incidence outputs
NotebookMetabolicIncidenceCharts=Metabolic incidence charts
NotebookPenTimePenSelection=Pen time budget-pen selection
NotebookPenTimeInputs=Pen time budget-inputs
NotebookPenTimeComparison=Pen time budget-comparison
NotebookPenTimeResults=Pen time budget-results
NotebookWalkthroughReport=Walkthrough report
VisitViewModel.ToolCategories=Tool categories
PileAndBunkerCapacity=Pile and bunker capacity
PileCapacity=Pile capacity
PileFeedOutRate=Pile feed out
BunkerCapacity=Bunker capacity
BunkerFeedOutRate=Bunker feed out
NotebookRumenHealthTMRParticleScore=Tmr particle score
NotebookRumenHealthTMRParticlePercent=Tmr percent on screen
NotebookLocomotionLanding=Locomotion-Main
NotebookLocomotionPenSelection=Locomotion-pen selection
NotebookLocomotionPenInputs=Locomotion-pen inputs
NotebookLocomotionPenResults=Locomotion-pen results
NotebookLocomotionHerdInputs=Locomotion-herd analysis inputs
NotebookLocomotionHerdRevenue=Locomotion-herd analysis revenue
NotebookLocomotionHerdResults=Locomotion-herd analysis results
NotebookCudCalculators=Rumen health-cud calculators
NotebookCudChewingDataEntry=Rumen health-cud chewing entry
NotebookCudChewingResults=Rumen health-cud chewing results
NotebookCudChewing=Rumen health-cud chewing select pen
NotebookRumenHealthNumberOfChewsInput=Rumen health-number of chews inputs
NotebookRumenHealthNumberOfChewsResults=Rumen health-number of chews results
NotebookParticleScoreLanding=Tmr particle score
NotebookParticleScoreSelectPen=Tmr particle score-select pen
NotebookParticleScoreSelectScorer=Tmr particle score-select scorer
NotebookLocomotionEditTable=Locomotion-number of cows
NotebookManureScoreLanding=Manure score
NotebookManurePenSelection=Manure score-pen selection
NotebookVisitSummary=Visit summary
NotebookTMRParticleHerdAnalysisPenInputs=Tmr particle score herd analysis-inputs
NotebookTMRParticleHerdAnalysisPenResults=Tmr particle score herd analysis-results
NotebookParticleScoreHerdAnalysisEdit=Tmr particle score herd analysis-edit dim amount
NotebookWalkthroughReportPen=Walkthrough report-pen analysis
Overall=Overall
ResourcesViewModel.VisitNotebook=Visit notebook
VisitViewModel.Instructions=Please select a category or report from the list below
SiteDetailsSetupViewModel.Title=Site details
SiteDetailsSetupViewModel.Pens=Pens
SiteDetailsSetupViewModel.Diets=Diets
SiteDetailsSetupViewModel.PenSetup=Pen setup
SiteDetailsSetupViewModel.DietSetup=Diet setup
SiteDetailsSetupViewModel.SiteName=Site name
SiteDetailsSetupViewModel.Continue=Continue
Continue=Continue
SiteDetailsSetupViewModel.CurrentMilkPrice=Current milk price ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Days in milk (dim)
SiteDetailsSetupViewModel.DryMatterIntake=Dry matter intake ({0})
SiteDetailsSetupViewModel.MilkYield=Milk yield ({0})
SiteDetailsSetupViewModel.AsFedIntake=As-fed intake ({0})
SiteDetailsSetupViewModel.LactatingAnimals=Lactating animals
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=Nel dairy (mcal/{0})
SiteDetailsSetupViewModel.MilkingSystem=Milking system
SiteDetailsSetupViewModel.MilkFatPercent=Milk fat %
SiteDetailsSetupViewModel.MilkProteinPercent=Milk protein %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Milk other solids %
SiteDetailsSetupViewModel.SomaticCellCount=Somatic cell count (1,000 cells/ml)
SiteDetailsSetupViewModel.BacteriaCellCount=Bacteria cell count (1,000 cfu/ml)
SiteDetailsSetupViewModel.RationCost=Ration cost, per animal ({0})
SiteDetailsSetupViewModel.NewSite=New site
SiteDetailsSetupViewModel.NullSiteName=Site name, milk price, milking system and pen are mandatory fields. Do you wish to continue or delete the site?
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=General customer site setup
SiteDetailsSetupViewModel.AnimalInputsSite=Animal inputs site
SiteDetailsSetupViewModel.DietInputsSiteLactating=Diet inputs, site (lactating animals)
SiteDetailsSetupViewModel.NameNotUnique=A site named "{0}" already exists. Names must be unique.
SiteDetailsSetupViewModel.SiteSetup=Site setup
SiteDetailsSetupViewModel.WeightImperialCWT=CWT
SiteDetailsSetupViewModel.Delete=Delete
Parlor=Parlor
Robot=Robot
SelectMilkingSystemViewModel.Title=Site setup
SelectMilkingSystemViewModel.SelectMilkingSystem=Select a milking system
SelectMilkingSystemViewModel.NoneSelected=None selected
DietListViewModel.NewDiet=Add a new diet
PenListViewModel.DietSetup=Diet setup
DietListViewModel.MainHeading=Diets
DietListViewModel.InfoNewDiet=Diet names will be updated automatically if max is connected to the farm site in dairy enteligen. Otherwise, add diets manually or leave this list blank and select the correct animal class / subclass for each pen.
Once the diet is created, select the class / subclass of animal associated with the Diet.
DietListViewModel.New=New
NutritionViewModel.NutritionToolsInstructions=Please select a tool from the list below to begin your visit
NutritionViewModel.NutritionToolsCaption=Tools
ForageScorecardViewModel.VisitNotebook=Visit notebook
ForageScorecardViewModel.ForageAuditScorecard=Forage audit scorecard
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Forage inventories
AddPile=Add pile
TopUnloadingSilo=Top unloading silo
BottomUnloadingSilo=Bottom unloading silo
TopUnloadingSilos=Top unloading silo
BottomUnloadingSilos=Bottom unloading silo
AddBag=Add bag
AddBunker=Add bunker
PileAndBunkerCapacityViewModel.Pile=Pile
Pile=Pile
PileAndBunkerCapacityViewModel.Bunker=Bunker
PileAndBunkerCapacityViewModel.Bag=Bag
PileAndBunkerCapacityViewModel.Bags=Bags
PileAndBunkerCapacityViewModel.NameNotUnique=A pile or bunker named "{0}" already exists. Names must be unique.
PileAndBunkerCapacityViewModel.NameTooLong=A pile or bunker name must be 40 characters or less.
Piles=Piles
Bunkers=Bunkers
Bags=Bag
NewPile=Please give the new pile a name.
NewBunker=Please give the new bunker a name.
NewDietViewModel.Title=New diet
ClassSubClass=Animal class / sub class
LinkToPens=Link to pens (optional)
NewDietViewModel.MainHeading=Diet name \u20F0
NewDietViewModel.Save=Save
NewDietViewModel.Cancel=Cancel
DietListViewModel.Title=Diets
NewDietClassViewModel.Title=Animal class / sub class
AnimalListViewModel.Title=Animal class / sub class
Lactating=Lactating
Dry=Dry
Heifer=Heifer
Calf=Calf
Male=Male
Milking=Milking
LowForage=Low forage
FreshHeifer=Fresh heifer
AAEfficiency=Aa efficiency
CloseUp=Close up
FarOff=Far off
CloseUpHeifer=Close up heifer
ShortDryPeriod=Short dry period
Bull=Bull
Steer=Steer
Max=MAX
UserCreated=User created
DDW=Farm data
DDWUpdatedTime=Last updated herd data: {0}
PenListViewModel.MainHeading=PENS
PenListViewModel.Title=Pens
PenListViewModel.NewPen=Add a new pen
NewPenViewModel.Save=Save
NewPenViewModel.InfoNewPenDetails=Add or update pen specific data on this page. You can also add or update data within the tools as you use them.
When creating a new pen the only items required are the pen name, diet, housing system, and feeding system. 
Pen setup data will be updated automatically for sites with farm data downloads in dairy enteligen. 
NewPenViewModel.Cancel=Cancel
NewPenViewModel.Title=New pen
NewPenViewModel.PenDetail=Pen detail
Diet=Diet
Rationcost=Ration cost ({0})
Pendetails=Pen details
NewPenViewModel.Diet=Diet
NewPenViewModel.PenName=Pen name
NewPenViewModel.Barn=Barn name
NewPenViewModel.HousingSystem=Housing system
NewPenViewModel.NumberOfStalls=Number of stalls
NewPenViewModel.FeedingSystem=Feeding system
NewPenViewModel.MilkingFrequency=Milking frequency
NewPenViewModel.Animals=Animals per pen
NewPenViewModel.DaysInMilk=Days in milk (dim)
NewPenViewModel.Milk=Milk yield ({0})
NewPenViewModel.DryMatterIntake=Dry matter intake ({0})
NewPenViewModel.AsFedIntake=As-fed intake ({0})
NewPenViewModel.RationCostPerAnimal=Ration cost per animal ({0})
NewPenViewModel.General=General
General=General
NewPenViewModel.AnimalsInputsPen=Animals inputs, pen
NewPenViewModel.DietInputsPen=Diet inputs, pen
SelectHousingSystemViewModel.Title=Pen setup
SelectHousingSystemViewModel.SelectHousingSystem=Select housing system
Freestall=Freestall
Tiestall=Tiestall
DryLot=Dry lot
BeddedPack=Bedded pack
PastureOther=Pasture + other
SelectFeedingSystemViewModel.SelectFeedingSystem=Select feeding system
SelectFeedingSystemViewModel.Title=Pen setup
TMR=TMR
PMRConcentrate=Pmr + concentrate
Pasture=Pasture
Component=Component
SelectPen=Please select a maxdiet for the new pen.
NewPenDietViewModel.Title=Diet
NewPenDietViewModel.New=New
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Score
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Responses
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Improvements
FillAllFields=Please fill in all fields.
FillAllMandatoryFields=Please fill in all mandatory fields.
NewDietPensViewModel.Title=Link to pens
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Feed out rate information
PileAndBunkerResultsFeedOutViewModel.Days=Days
PileAndBunkerResultsFeedOutViewModel.StartDate=Start date
PileAndBunkerResultsFeedOutViewModel.DateGone=Date gone
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=Cows/day needed
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Feeding rate (as-fed / cow)
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Cows to be fed
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kgs. dm in 1 meter
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Lbs. dm in 1 foot
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Feed out surface area (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Feed out surface area (ft^2)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=Cm. per day
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=In. per day
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=At 3 in. per day
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=At 7 cm. per day
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=At 6 in. per day
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=At 15 cm. per day
PileAndBunkerResultsFeedOutViewModel.Title=Feed out
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Top width ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.FilledHeight=Filled height ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.Diameter=Diameter ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.SilageLeft=Height of silage left in silo ({0})
PileAndBunkerResultsBagCapacityInputViewModel.LenghtBag=Length ({0})
PileAndBunkerResultsBagCapacityInputViewModel.DiameterBag=Diameter ({0})
PileAndBunkerResultsBagCapacityInputViewModel.TonsDMBag=Tons dm
PileAndBunkerResultsBagCapacityInputViewModel.TonsAFBag=Tons af
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=Metric tons dm
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=Metric tons af
PileAndBunkerResultsCapacityInputViewModel.Height=Height ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Bottom width ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Bottom length ({0})
PileAndBunkerResultsCapacityInputViewModel.TopLength=Top length ({0})
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=Dry matter %
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=Dry matter %
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=Dry matter %
PileAndBunkerResultsBagCapacityInputViewModel.SilageDMDensityBag=Silage dm density ({0})
PileAndBunkerResultsBagCapacityInputViewModel.CapacityBag=Capacity
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Silage dm density ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=Silage dm density ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=Capacity
PileAndBunkerResultsCapacityInputViewModel.TonsDM=Tons dm
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=Tons dm (remaining in silo)
PileAndBunkerResultsCapacityInputViewModel.TonsAF=Tons af
PileAndBunkerResultsSiloCapacityInputViewModel.TonsAFSilo=Tons af (remaining in silo)
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Metric tons dm
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=Metric tons dm (remaining in silo)
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=Metric tons af
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=Metric tons af (remaining in silo)
NewDietPensViewModel.AssociatePens=You can associate this maxdiet to multiple pens.
DietDetailViewModel.Title=Diet detail
DietDetailViewModel.Created=Created
DietDetailViewModel.DDW=Farm data
DietDetailViewModel.UserCreated=By user
DietDetailViewModel.Max=MAX
Pens=Pens
Created=Created
PenName=Pen name
PenDetailViewModel.Title=Pen detail
CreateDuplicateDiet=A maxdiet already exists for that animal type. Create anyways?
DuplicatePenName=A pen already exists with that name. Please choose a different name.
LoginViewModel.Title=Login
ForageAuditScorecardResultsViewModel.Title=Tower silos
VisitNotesViewModel.VisitNotebook=Visit notebook
EditNoteViewModel.TitleLabel=Title
EditNoteViewModel.NoteLabel=Note
EditNoteViewModel.Delete=Delete
VisitNotebook=Visit notebook
PileAndBunkerCapacityViewModel.Title=Forage inventory
PileAndBunkerCapacityViewModel.AddBunker=Add bunker
PileAndBunkerCapacityViewModel.AddPile=Add pile
PileAndBunkerCapacityViewModel.TopUnloadingSilo=Top unloading silo
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=Bottom unloading silo
PileAndBunkerCapacityViewModel.AddBag=Add bag
PileAndBunkerCapacityViewModel.Bunkers=Bunkers
PileAndBunkerCapacityViewModel.Piles=Piles
SettingsViewModel.Milk_Processor_Set_Up=Milk processor set up
SettingsViewModel.Metric=Metric
SettingsViewModel.Imperial=Imperial
SettingsViewModel.Select_Unit_Of_Measure=Select unit of measure
SettingsViewModel.More_Settings=More settings
User_Settings=User settings
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Capacity
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Feed out
PileAndBunkerResultsCapacityInputViewModel.Title=Capacity
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Height (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Height (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Top width (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Top width (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Bottom width (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Bottom width (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Bottom length (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Bottom length (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Top length (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Top length (f.)
UserPreferencesViewModel.UserPreferencesMoreSettings=More settings
UserPreferencesViewModel.UserPreferencesMilkProcessor=Set up milk processor
PricingMatrixViewModel.Title=Edit matrix
PricingMatrixViewModel.Amount=Amount (1000 cells/ml)
PricingMatrixViewModel.AmountCFU=Amount (1000 cfu/ml)
MilkProcessorViewModel.Delete=Delete
MilkProcessorViewModel.ConcentrationProcessor=Concentration processor
MilkProcessorViewModel.ComponentProcessor=Component processor
MilkProcessorViewModel.Name=Name
MilkProcessorViewModel.BasePrices=Base prices
MilkProcessorViewModel.PricingMatrices=Pricing matrices
MilkProcessorSettingsMasterViewModel.MilkProcNew=New
MilkProcessorSettingsMasterViewModel.Title=Milk processor set up
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Component
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Concentration
Amount=Amount
PricingMatrixEditViewModel.Title=Matrix item detail
PricingMatrixEditViewModel.Save=Save
PricingMatrixEditViewModel.Cancel=Cancel
PricingMatrixViewModel.New=New
DeleteMatrixValue=Are you sure you want to delete this matrix value?
UnitedStates=United states of america
US=United states of america
UnitedKingdom=United kingdom
UK=United kingdom
Canada=Canada
Algeria=Algeria
Brazil=Brazil
Chile=Chile
China=China
CzechRepublic=Czech republic
Guatemala=Guatemala
Honduras=Honduras
Hungary=Hungary
India=India
Indonesia=Indonesia
SouthKorea=Korea (south)
Malaysia=Malaysia
Mexico=Mexico
Nicaragua=Nicaragua
Peru=Peru
Philippines=Philippines
Poland=Poland
Romania=Romania
Russia=Russia
SaudiArabia=Saudi arabia
SouthAfrica=South africa
Surinam=Surinam
Switzerland=Switzerland
Taiwan=Taiwan
Ukraine=Ukraine
Venezuela=Venezuela
Vietnam=Vietnam
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Currencies
ProcessorCurrencyPickListViewModel.Title=Currencies
PricingMatrixPickListViewModel.PricingMatrix=Pricing matrix
PlBacteriaCell=Bacteria cell count (1,000 cfu/ml)
PlSomaticCell=Somatic cell count (1,000 cells/ml)
SomaticCellPerML=Somatic cell count (cells/ml)
SomanticCellCount=Somatic cell count
PlMilkFat=Milk fat
PlMilkProtein=Milk protein
MilkProcessorViewModel.SelectCurrency=Select currency
SelectMatrix=Select a matrix
MilkProcessorViewModel.NewComponentProcessorName=Component processor #{0}
MilkProcessorViewModel.NewConcentrationProcessorName=Concentration processor #{0}
MilkProcessorViewModel.BasePriceMilkFat=Milk fat ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Milk ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Milk protein ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Other solids ({0}/{1})
MilkProcessorViewModel.Amount=Amount (1000 cells / ml)
MilkProcessorViewModel.AmountCFU=Amount (1000 cfu / ml)
MilkProcessorViewModel.HundredWeight=CWT
MilkProcessorViewModel.WeightMetric=KG
MilkProcessorViewModel.WeightImperialCWT=CWT
MilkProcessorViewModel.WeightImperial=LBS
MilkProcessorViewModel.DeletePrompt=Deleting this processor will invalidate results in the milking procedure comparison tool. Do you want to delete this processor?
Edit=Edit
MenuViewModel.Close=Close
ForageAuditViewModel.VisitNotebook=Visit notebook
NutritionViewModel.VisitNotebook=Visit notebook
ComfortToolsViewModel.VisitNotebook=Visit notebook
HeatstressTableViewModel.VisitNotebook=Visit notebook
RevenueInputViewModel.ComparisonValues=Comparison values
SelectProcessor=Select processor
PotentialSCC=Potential scc (cells/{0})
RevenueEditComparisonValuesViewModel.PotentialSCC=Potential scc (cells/{0})
RevenueEditComparisonValuesViewModel.NumOfCows=Number of cows
RumenHealthEditManureScoresViewModel.NumOfCows=Number of cows
NumOfCows=Number of cows
RevenueEditComparisonValuesViewModel.MilkProduction=Milk production
MilkProduction=Milk production ({0})
CurrentMilkPrice=Current milk price ({0}/{1})
MilkPrice=Milk price ({0}/{1})
RevenueEditComparisonValuesViewModel.MilkPrice=Milk price ($/{0}})
RevenueEditComparisonValuesViewModel.MilkChange=Milk change (%)
MilkChange=Milk change ({0})
DownResponse=Let down response ({0}/cow/day)
RevenueEditComparisonValuesViewModel.DownResponse=Let down response ({0}/cow/day)
RevenueEditComparisonValuesViewModel.CurrentSCC=Current scc (cells/{0})
RevenueInputViewModel.ScenarioOne=Scenario 1
RevenueInputViewModel.ScenarioTwo=Scenario 2
MilkProcessorInputViewModel.ProcessorDeletedPrompt=The processor previously selected has been deleted. Please select another processor to continue.
MilkProcessorInputViewModel.Title=Milking procedure comparison inputs
HeatstressDataEntryViewModel.VisitNotebook=Visit notebook
HeatstressChartViewModel.VisitNotebook=Visit notebook
HeatstressChartViewModel.TemperatureMetric=\u00B0C
HeatstressChartViewModel.TemperatureImperial=\u00B0F
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.Pounds=Lbs
CudChewingViewModel.CudChewing=Rumen health
CudChewingViewModel.CudChewingList=Pens (lactating and dry only)
CudChewingViewModel.CudChewingTitle=Pen name
HealthToolsViewModel.Title=Health tools
HealthToolsViewModel.HealthHeading=Please select a tool from the list below.
HealthToolsViewModel.HealthToolsList=TOOLS
HealthToolsViewModel.RumenHealthTitle=Rumen health cud chewing
HealthToolsViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
HealthToolsViewModel.RumenHealthManureScreening=Rumen health manure screening
HealthToolsViewModel.RumenHealthManureTitle=Rumen health manure score
HealthToolsViewModel.RumenHealthLocomotionTitle=Locomotion score
HealthToolsViewModel.RumenHealthBodyConditionTitle=Body condition score
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&#8482;
ProductivityToolsViewModel.ProductivityTools=Tools
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
HerdInformation=Herd information
ProductivityToolsViewModel.MilkRevenueAnalysis=Milk revenue analysis
ProductivityToolsViewModel.ProductivityTitle=Productivity tools
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Milking procedure comparison
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Inputs
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Results
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Resources
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Reference chart
MilkProcessorResourcesViewModel.LinearScore=Linear score
MilkProcessorResourcesViewModel.ApproxSCC=Approximate scc (cells/ml)
MilkProcessorResourcesViewModel.Mastitis=Mastitis milk loss ({0})
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Component
DashboardViewModel.UserPreferences=User preferences
MilkProcessRevenueCalculatorMasterViewModel.Title=Milking procedure comparison
MilkProcessorInputViewModel.SelectProcessor=Select processor
MilkProcessorInputViewModel.ComparisonValues=Comparison values
MilkProcessorInputViewModel.Edit=Edit
MilkProcessorInputViewModel.ScenarioOne=Scenario 1
MilkProcessorInputViewModel.ScenarioTwo=Scenario 2
MilkProcessorEditComparisonValuesViewModel.Title=Edit comparison values
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Scenario 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Scenario 2
CudChewsCalculatorViewModel.CalculatorHeading=Please select a cow to count the number of chews. Tap "add new" above to add cows to the count list.
CudChewsCalculatorViewModel.CudChewCategorySection=Cows
ChewsPerCudMasterViewModel.AddNew=Add new
ChewsPerCudMasterViewModel.CudChewingInputs=Inputs
ChewsPerCudMasterViewModel.CudChewingResults=Results
RevenueEditComparisonValuesViewModel.Title=Edit comparison values
RevenueEditComparisonValuesViewModel.ScenarioOne=Scenario 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Scenario 2
MilkProcessorResultsViewModel.ScenarioOne=Scenario 1
MilkProcessorResultsViewModel.ScenarioTwo=Scenario 2
MilkProcessorResultsViewModel.Change=Change
MilkProcessorResultsViewModel.ResultsHeader=Annual value changes
MilkProcessorResultsViewModel.HundredWeight=CWT
MilkProductionRevenue=Milk production revenue
ClinicalMastitisLosses=Clinical mastitis losses
MilkPricePremiums=Milk price premiums
MilkLetDownResponse=Milk let down response
Revenue=Revenue
TotalRevenue=Total revenue
Total=Total
NumberOfChewsViewModel.CountHeader=Please count the number of chews for this cow.
NumberOfChewsViewModel.NextCow=Next cow
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=Reference chart
SelectProcessorViewModel.COMPONENT=Component
SelectProcessorViewModel.CONCENTRATION=Concentration
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=Cud calculators
RumenHealthPenCudCalculatorViewModel.CudChewing=Cud chewing
CudChewingDataEntryViewModel.CudChewing=Cud chewing
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Number of chews
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Capture the number of cows chewing cud
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Capture the number of chews per cow
RumenHealthLandingViewModel.Title=Rumen health cud chewing
RumenHealthLandingViewModel.PenAnalysis=Pen analysis
RumenHealthLandingViewModel.Pens=Pens
RumenHealthLandingViewModel.HerdAnalysis=Herd analysis
HerdAnalysisMasterViewModel.Title=Rumen health cud chewing
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Rumen health cud chewing
MilkProcessorViewModel.NameNotUnique=A component processor named "{0}" already exists. Names must be unique.
CudChewingHerdEditScoreViewModel.EditScoreTitle=Edit cud chewing scores
CudChewingHerdEditScoreViewModel.PercentChewingItem=Percent chewing
CudChewingHerdEditScoreViewModel.AverageChewsItem=Average chews per cud
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Days in milk
Cow=Cow 
ChewsPerCudMasterViewModel.NumOfChews=# of chews
CudChewsCalculatorViewModel.NumOfChews=# of chews
NumberOfChewsViewModel.NumberOfChewsCow=Number of chews / cow #
NumberOfChewsViewModel.Count=Count
PileAndBunkerResultsCapacityInputViewModel.Capacity=Capacity
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Herd analysis
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Herd analysis
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Goals
HerdAnalysisGoalsViewModel.Title=Herd analysis
HerdAnalysisGoalsViewModel.CudChewingGoals=Cud chewing goals
HerdAnalysisGoalsViewModel.DIM=DIM
HerdAnalysisGoalsViewModel.PercentChewing=%
Chewing
HerdAnalysisGoalsViewModel.CudChews=Cud
Chews
HerdAnalysisGoalsViewModel.FarOffDry=Far-off dry
HerdAnalysisGoalsViewModel.CloseUpDry=Close-up dry
HerdAnalysisGoalsViewModel.Fresh=Fresh
HerdAnalysisGoalsViewModel.EarlyLactation=Early lactation
HerdAnalysisGoalsViewModel.PeakMilk=Peak milk
HerdAnalysisGoalsViewModel.MidLactation=Mid lactation
HerdAnalysisGoalsViewModel.LateLactation=Late lactation
HerdAnalysisGoalsViewModel.To=to
NotebookSectionRumenHealthLanding=Rumen health
NotebookSectionHerdAnalysisGoals=Rumen health - herd analysis goals
CudChewingMasterViewModel.CudChewingInputs=Inputs
CudChewingMasterViewModel.CudChewingResults=Results
CudChewingDataEntryViewModel.Yes=Yes
CudChewingDataEntryViewModel.No=No
CudChewingDataEntryViewModel.HerdCudChewingDescription=For this visit, please count the number of animals chewing cud in this pen using the counter below. You must count a minimum of 10 cows.
CudChewingMasterViewModel.CudChewing=Cud chewing
HerdAnalysisViewModel.TableTitle=Cud chewing score analysis
HerdAnalysisViewModel.NumberofChewsPerCud=Number of chews per cud
HerdAnalysisViewModel.HerdCudChewing=Herd cud chewing %
HerdAnalysisViewModel.EditLabel=Edit
HerdAnalysisViewModel.PenNameLabel=Pen name
HerdAnalysisViewModel.NoOfCows=Complete the pen analysis for all pens that you have started.
VisitViewModel.VisitNotebook=Visit notebook
PileAndBunkerCapacityViewModel.VisitNotebook=Visit notebook
PileAndBunkerResultsMasterViewModel.VisitNotebook=Visit notebook
ForageAuditScorecardResultsViewModel.VisitNotebook=Visit notebook
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Visit notebook
ProductivityToolsViewModel.VisitNotebook=Visit notebook
EditNoteViewModel.DeleteImageButtonText=Delete image
EditNoteViewModel.DeleteVideoButtonText=Delete video
EditNoteViewModel.NoteOnlyOneVideo=Only one video is allowed per note. Please delete the current video first.
EditNoteViewModel.NoteOnlyOneImage=Only one image is allowed per note. Please delete the current image first.
EditNoteViewModel.NoteGalleryNotImplemented=Gallery feature not yet implemented
EditNoteViewModel.NoteCamcorderNotImplemented=Camcorder feature not yet implemented
EditNoteViewModel.Category=Category
EditNoteViewModel.Observation=Observation
EditNoteViewModel.Action=Action
EditNoteViewModel.Task=Task
EditNoteViewModel.Event=Event
HerdAnalysisTableTitle=Cud chewing score analysis
HerdAnalysisViewModel.HerdAnalysisTableTitle=Cud chewing score analysis
HerdAnalysisViewModel.Edit=Edit
NoteCamcorderNotImplemented" xml:space="preserve">"
Camcorder Feature Not Yet Implemented
Date=Date
DatesForComparison=Dates for comparison
RumenHealthTMRLandingViewModel.Title=Rumen health particle score
RumenHealthManureLandingViewModel.Title=Rumen health manure score
RumenHealthManureLandingViewModel.Pens=Pens
RumenHealthManureLandingViewModel.PenAnalysis=Pen analysis
RumenHealthManureLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthBodyConditionLandingViewModel.Title=Body condition score
RumenHealthBodyConditionLandingViewModel.Pens=Pens
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Pen analysis
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Herd analysis
BodyConditionScoreHerdGoalsViewModel.TableTitle=Bcs by stage of lactation
BodyConditionScoreHerdGoalsViewModel.Edit=Edit
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Bcs goal min
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Bcs goal max
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Far-off dry (less than -21)
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
BodyConditionScoreHerdGoalsViewModel.Fresh=Fresh (0 to 15)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Peak milk (61 to 120)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Mid lactation (121 to 200)
BodyConditionScoreHerdGoalsViewModel.LateLactation=Late lactation (greater than 201)
BodyConditionScoreHerdEditGoalsViewModel.Title=Edit goals
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=Bcs min
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=Bcs max
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=Far-off dry (less than -21)
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=Fresh (0 to 15)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=Peak milk (61 to 120)
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=Mid lactation (121 to 200)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=Late lactation (greater than 201)
PenTimeBudgetComparisonViewModel.TableTitle=Comparison
PenTimeBudgetComparisonViewModel.Current=Current
PenTimeBudgetComparisonViewModel.CowsInPen=Cows in pen
PenTimeBudgetComparisonViewModel.Overcrowding=Overcrowding (%)
PenTimeBudgetComparisonViewModel.TimePerMilking=Time per milking (hours)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Parlor turns per hour
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Cows milked per hour
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Total time milking (hours)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Walking to find stall (hours)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Total non-resting time (hours)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Time remaining for resting (hours)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Time required for resting (hours)
PenTimeBudgetComparisonViewModel.RestingDifference=Resting difference (hours)
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Potential milk loss / gain ({0})
PenTimeBudgetComparisonViewModel.EnergyChange=Energy change (mcals)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Body weight change ({0})
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Body condition score change (per 100 days)
RumenHealthTMRLandingViewModel.Pens=Pens
RumenHealthTMRLandingViewModel.PenAnalysis=Pen analysis
RumenHealthTMRLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthLocomotionLandingViewModel.Title=Locomotion
RumenHealthLocomotionLandingViewModel.Pens=Pens
RumenHealthLocomotionLandingViewModel.PenAnalysis=Pen analysis
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Herd analysis
HerdAnalysisViewModel.PercentChewing=Percent chewing
HerdAnalysisViewModel.AverageChews=Average chews per cud
HerdAnalysisViewModel.DaysInMilk=Days in milk
RevenueEditComparisonValuesViewModel.EditComparisonValues=Edit comparison values
SelectProcessorViewModel.MilkProcessors=Milk processors
CudChewingViewModel.Title=Pens
ChewsPerCudMasterViewModel.Title={0} / # of chews
EditGoalsCudChewingViewModel.EditGoalsTitle=Edit cud chewing goals
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Edit cud chewing scores
EditGoalsCudChewingViewModel.Close=Close
EditGoalsCudChewingViewModel.PercentChewing=Percent chewing
EditGoalsCudChewingViewModel.CudChews=Average chews per cud
EditGoalsCudChewingViewModel.FarOffDry=Far-off dry
EditGoalsCudChewingViewModel.CloseUpDry=Close-up dry
EditGoalsCudChewingViewModel.Fresh=Fresh
EditGoalsCudChewingViewModel.EarlyLactation=Early lactation
EditGoalsCudChewingViewModel.PeakMilk=Peak milk
EditGoalsCudChewingViewModel.MidLactation=Mid lactation
EditGoalsCudChewingViewModel.LateLactation=Late lactation
Select=Select
ThisVisit=(this visit)
EditDatesForComparison=Edit dates for comparison
EditDatesForComparisonViewModel.EditDatesTitle=Edit dates for comparison
EditDatesForComparisonViewModel.EditDatesLabel=Please select the dates for comparison for this pen from the list below.
EditDatesForComparisonViewModel.EditDatesVisits=Visits
EditDatesForComparisonViewModel.EditDatesClose=Close
NumberOfChewsReportsViewModel.VisitDate=Date
NumberOfChewsReportsViewModel.AverageNumberChews=Average # of chews
NumberOfChewsReportsViewModel.DatesComparison=Dates for comparison
NumberOfChewsReportsViewModel.EditVisits=Select
NumberOfChewsReportsViewModel.DateDescription=Date
NumberOfChewsReportsViewModel.Average=Average # of chews
CudChewingHerdEditScoreViewModel.Close=Close
MilkingProcessRevenueInputs=Milking procedure comparison - inputs
MilkingProcessRevenueResults=Milking procedure comparison - results
MilkingProcessRevenueResources=Milking procedure comparison - resources
NumberOfCows=Number of cows
ManureEditScores=Manure scores - edit scores
NumberOfChewsViewModel.Title=Number of chews / cow # {0}
NumberOfChewsViewModel.ValidCudInput=Please enter a valid input.
EditDatesForComparisonViewModel.Chews=Chews
EditDatesForComparisonViewModel.Visits=Visits
EditDatesForComparisonViewModel.Title=Edit dates for comparison
EditDatesForComparisonViewModel.ManureScoreAverage=Average manure score
EditDatesForComparisonViewModel.LocomotionScoreAverage=Average locomotion score:
EditDatesForComparisonViewModel.TimeRemainingForResting=Time remaining for resting:
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Please select a date below to compare with the current visit.
FarOffDry=Far-off dry
BodyConditionHerdInputs=Body condition herd analysis inputs
BodyConditionHerdGoals=Body condition herd analysis goals
BodyConditionHerdResults=Body condition herd results
HerdReporting=Herd reporting: cud chewing
NumberOfChewsReportsViewModel.AverageChews=Average chews
NumberOfChewsReportsViewModel.StdDevCalculated=Std. deviation (calculated)
CloseUpDry=Close-up dry
Fresh=Fresh
EarlyLactation=Early lactation
PeakMilk=Peak milk
MidLactation=Mid lactation
LateLactation=Late lactation
CudChewingPercentGoal={0}% goal
NumChewsGoal={0} goal
CudChewsDatesForComparisonViewModel.CudChewsPercent=Cud chewing %
RumenHealthTMRSelectPenViewModel.SelectScorer=Select a scorer
RumenHealthTMRSelectPenViewModel.SelectPen=Pens (lactating and dry only)
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=None selected
RumenHealthTMRSelectPenViewModel.Title=Rumen health particle score
RumenHealthTMRSelectPenViewModel.NoScorerSelected=A scorer must first be selected in order to view a pen.
RumenHealthManureScoresResultsViewModel.PercentPen=Percent of pen (%)
RumenHealthManureScoresResultsViewModel.Title=Manure score results
RumenHealthManureScoresResultsViewModel.SelectedDates=Select dates
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Date
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Average score
RumenHealthTMRSelectScorerViewModel.SelectScorer=Select a scorer
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=None selected
RumenHealthTMRSelectScorerViewModel.FooterText=Only one scorer can be used per visit. Changing the scorer will result in lost values.
RumenHealthTMRSelectScorerViewModel.Title=Rumen health particle score
NoneSelected=None selected
FourScreenNew=4 screen new
ThreeScreen=3 screen
FourScreenOld=4 screen old
RumenHealthManureScoresViewModel.ManureScore=Manure score
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Avg. manure score (calculated)
RumenHealthManureScoresViewModel.StdDevCalculated=Std. deviation (calculated)
RumenHealthManureScoresViewModel.PercentOfPen=Percent of pen (%)
RumenHealthManureScoresViewModel.AnimalsObserved=Animals observed
RumenHealthManureScoresViewModel.Edit=Edit
RumenHealthManureScoresViewModel.ScoreCategory=Manure score category
RumenHealthManureMasterViewModel.VisitNotebook=Visit notebook
RumenHealthManureMasterViewModel.RumenHealthManureScore=Rumen health manure score
RumenHealthEditManureScoresViewModel.NumberOfCows=Number of cows
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Please count the number of cows
RumenHealthEditManureScoresViewModel.Count=Count
Category1=Manure score category 1.0
Category2=Manure score category 2.0
Category3=Manure score category 3.0
Category4=Manure score category 4.0
Category5=Manure score category 5.0
RumenHealthEditManureScoresViewModel.VisitNotebook=Visit notebook
ManureScorePenSelectionViewModel.ManureScoreTitle=Rumen health manure score
ManureScorePenSelectionViewModel.PenSelectionList=PENS
LocomotionSelectPenViewModel.Title=Locomotion
LocomotionSelectPenViewModel.PenSelectionList=PENS
BCSPenSelectionViewModel.PenSelectionList=PENS
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Inputs
RumenHealthTMRPenScorerTableMasterViewModel.Results=Results
RumenHealthTMRPenScorerTableMasterViewModel.Title=Rumen health particle score
RumenHealthTMRParticleScorePenTableInputViewModel.Title=Tmr particle score
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Top (19 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Mid 1 (8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Mid 2 (4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Mid 2 (1.18 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Tray
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Percent on screen (%)
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Scale amount (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Goal - min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Goal - max (%)
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Percent on screen (%)
ParticleScorePreviousVisitsViewModel.SelectDates=Select dates
ParticleScorePreviousVisitsViewModel.Top=Top
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.MidOne=Mid 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Mid 2
ParticleScorePreviousVisitsViewModel.Tray=Tray
PercentageOnScreen=Percent on screen (%)
SelectDates=Select dates
Top=Top
TopValue=(19mm)
MidOne=Mid 1
MidOneValue=(8mm)
MidTwo=Mid 2
Tray=Tray
FourScreenOldType=(1.18 mm)
FourScreenNewType=(4 mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
PercentOnScreenTitle=Percent on screen (%)
MenuViewModel.ResetDatabaseTitle=Reset test data
MenuViewModel.ResetDatabasePrompt=This will replace any existing data, including user preferences, with a starting set of test data. Visit tools and new visits created and processors will be lost. You will also be logged out of the app. Continue?"
MenuViewModel.ResetDatabaseReset=Reset
MenuViewModel.ResetDatabaseCancel=Cancel
Reset_Database">
Reset Database
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Screen tare
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Top (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Middle 1(8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Middle 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Middle 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Tray
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Enter screen tare amount (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Goal - min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Goal - max (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Enter scale amount (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Enter scale amount
BodyConditionScoresMasterViewModel.Title=Body condition score
BodyConditionScoresMasterViewModel.Inputs=Inputs
BodyConditionScoresMasterViewModel.Results=Results
BodyConditionScoresMasterViewModel.BodyConditionScore=Body condition score
BodyConditionScoresMasterViewModel.VisitNotebook=Visit notebook
RumenHealthManureMasterViewModel.Inputs=Inputs
RumenHealthManureMasterViewModel.Results=Results
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Rumen health manure score
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=Body condition score (bcs)
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Avg. bcs (calculated)
BodyConditionScoreInputsViewModel.StdDevCalculated=Std. deviation (calculated)
BodyConditionScoreInputsViewModel.BCSPercentOfPen=Percent of pen (%)
BodyConditionScoreInputsViewModel.AnimalsObserved=Animals observed
BodyConditionScoreInputsViewModel.BCSCategory=Bcs category
BodyConditionScoreInputsViewModel.Edit=Edit
LocomotionPenMasterViewModel.Title=Locomotion
LocomotionPenMasterViewModel.Inputs=Inputs
LocomotionPenMasterViewModel.Results=Results
LocomotionHerdMasterViewModel.Title=Locomotion
LocomotionHerdMasterViewModel.SubHeading=Herd analysis
LocomotionHerdMasterViewModel.Inputs=Inputs
LocomotionHerdMasterViewModel.Revenue=Revenue
LocomotionHerdMasterViewModel.Results=Results
BodyConditionScoreEditInputsViewModel.Title=Number of cows
BodyConditionScoreEditInputsViewModel.NumberOfCows=Number of cows
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Please count the number of cows.
BodyConditionScoreEditInputsViewModel.Count=Count
BodyConditionScoreCategory=Body condition score category {0}
BCSCategory1=Body condition score category 1.0
BCSCategory1pt5=Body condition score category 1.5
BCSCategory2=Body condition score category 2.0
BCSCategory2pt5=Body condition score category 2.5
BCSCategory3=Body condition score category 3.0
BCSCategory3pt5=Body condition score category 3.5
BCSCategory4=Body condition score category 4.0
BCSCategory4pt5=Body condition score category 4.5
BCSCategory5=Body condition score category 5.0
BodyConditionInputs=Body condition inputs
BodyConditionResults=Body condition results
NotebookBodyConditionEdit=Body condition edit table
ShowEulaViewModel.Accept=Accept
ShowEulaViewModel.Decline=Decline
ShowEulaViewModel.ConfirmationTitle=Confirmation
ShowEulaViewModel.ConfirmationText=Do you agree to the terms of the mobile application end user license agreement?
ShowEulaViewModel.ConfirmationYes=Yes
ShowEulaViewModel.ConfirmationNo=No
ShowEulaViewModel.EulaScreenTitle=End user license
SelectProcessorViewModel.Edit=Edit
LocomotionScore=Locomotion score
AvgLocomotionScore=Avg. locomotion score (calculated)
StdDevCalculated=Std. deviation (calculated)
LocoCategory1=Cat. 1.0
LocoCategory2=Cat. 2.0
LocoCategory3=Cat. 3.0
LocoCategory4=Cat. 4.0
LocoCategory5=Cat. 5.0
AnimalsObserved=Animals observed
LocomotionScoreHerd=Locomotion score analysis
TotalAnimalsHerd=Total animals
LocomotionScoreAverage=Average locomotion score
PercentPen=Percent of pen (%)
TotalPenPerScore=Total # of animals in pen per score
LocomotionPenInputsViewModel.FromPenSetup=From pen setup
TotalAnimals=Total animals in pen
DaysInMilkItem=Days in milk (dim)
MilkProductionPounds=Milk production (lbs)
MilkProductionKg=Milk production (kgs)
LocomotionPenInputsViewModel.Milk=MILK
LocomotionHerdInputsViewModel.Herd=HERD
LocomotionHerdEditGoalViewModel.Title=Edit goal amount
LocomotionHerdEditGoalViewModel.HerdGoal=Herd goal
LocomotionHerdEditGoalViewModel.Category1=Category 1.0
LocomotionHerdEditGoalViewModel.Category2=Category 2.0
LocomotionHerdEditGoalViewModel.Category3=Category 3.0
LocomotionHerdEditGoalViewModel.Category4=Category 4.0
LocomotionHerdEditGoalViewModel.Category5=Category 5.0
MilkLossPounds=Milk loss (lbs)
MilkLossKg=Milk loss (kgs)
LocomotionScoreReference=Locomotion score reference
PercentLossPerCow=% loss / cow
AvgBCS=Average bcs
LocomotionEditTableViewModel.Title=Number of cows
LocomotionEditTableViewModel.EnterNumberOfCows=Please count the number of cows.
LocomotionEditTableViewModel.Category1=Locomotion score category 1.0
LocomotionEditTableViewModel.Category2=Locomotion score category 2.0
LocomotionEditTableViewModel.Category3=Locomotion score category 3.0
LocomotionEditTableViewModel.Category4=Locomotion score category 4.0
LocomotionEditTableViewModel.Category5=Locomotion score category 5.0
RumenHealthEditManureScoresViewModel.Close=Close
Count=Count
BCSPenSelectionViewModel.Title=Body condition score
BCSPenSelectionViewModel.SelectPointScale=Select a point scale
HalfPointScale=Half point scale
QuarterPointScale=Quarter point scale
BCSPenSelectionViewModel.Pens=Pens
BodyConditionScoreResultsViewModel.BCSAverageTitle=Average score
BodyConditionScoreResultsViewModel.Title=Bcs results
BodyConditionScoreResultsViewModel.PercentPen=Percent of pen (%)
BodyConditionScoreResultsViewModel.SelectedDates=Select dates
LocomotionPreviousVisitsViewModel.PercentPen=Percent of pen (%)
LocomotionPreviousVisitsViewModel.Title=Locomotion score results
LocomotionPreviousVisitsViewModel.SelectedDates=Select dates
LocomotionPreviousVisitsViewModel.AverageScore=Average score
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Date
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Average score
ProspectsViewModel.NewProspect=Add a new prospect
VisitSummaryViewModel.Title=Site visit summary
VisitSummaryViewModel.VisitTitle=Visit name
VisitSummaryViewModel.ComfortItem=Comfort
VisitSummaryViewModel.HealthItem=Health
VisitSummaryViewModel.NutritionItem=Nutrition
VisitSummaryViewModel.ProductivityItem=Productivity
VisitSummaryViewModel.CategorySection=Tool categories
VisitSummaryViewModel.ComfortHeatStressBanner=Heat stress tool pen
VisitSummaryViewModel.HeatstressEvaluationTitle=Heat stress evaluation
VisitSummaryViewModel.RumenHealthTitle=Rumen health cud chewing
VisitSummaryViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
VisitSummaryViewModel.RumenHealthManureTitle=Rumen health manure score
VisitSummaryViewModel.RumenHealthLocomotionTitle=Locomotion score
VisitSummaryViewModel.RumenHealthBodyConditionTitle=Body condition score
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&#8482;
VisitSummaryViewModel.InputsOutputsChart=Inputs / outputs / charts
VisitSummaryViewModel.NutritionForage=Forage audit
VisitSummaryViewModel.NutritionPile=Forage inventories
VisitSummaryViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
VisitSummaryViewModel.NoToolPrompt=No tools have been completed.
VisitSummaryViewModel.VisitSummaryMilkCalc=Inputs / results / resources
VisitSummaryViewModel.HerdAnalysis=Herd analysis
VisitSummaryViewModel.EmailReport=Email report
VisitSummaryViewModel.FreeFormReport=Free form report
VisitSummaryViewModel.PenTimeTitle=Pen time budget
ProspectProfileViewModel.MainHeading=SITES
ProspectProfileViewModel.NewSite=Add a new site
ProspectProfileViewModel.DeleteProspect=Delete prospect
ProspectProfileViewModel.DeleteProspectPrompt=Are you sure you want to delete this prospect? Prospect, site and in progress visit information will be lost.
NewProspectViewModel.Title=Details
NewProspectViewModel.ProspectDetail=Prospect details
NewProspectViewModel.CustomerDetail=Customer details
NewProspectViewModel.BusinessName=Business name
NewProspectViewModel.Image=Tap to edit picture
NewProspectViewModel.PrimaryContactFirstName=Primary contact first name
NewProspectViewModel.PrimaryContactLastName=Primary contact last name
NewProspectViewModel.Address1=Business address 1
NewProspectViewModel.Address2=Business address 2
NewProspectViewModel.City=CITY
NewProspectViewModel.State=STATE
NewProspectViewModel.PostalCode=Postal code
NewProspectViewModel.Country=COUNTRY
NewProspectViewModel.PrimaryPhone=Contact phone number
NewProspectViewModel.EmailAddress=Contact email
NewProspectViewModel.Type=TYPE
NewProspectViewModel.Segment=SEGMENT
NewProspectViewModel.Prospect=Prospect
NewProspectViewModel.NameNotUnique=A prospect named "{0}" already exists. Names must be unique.
NewProspectViewModel.NullBusinessName=Business name is required.
NewProspectViewModel.NullFirstName=First name is required.
NewProspectViewModel.NullSecondName=Last name is required.
NewProspectViewModel.InvalidEmail=Please enter a valid email.
NewProspectViewModel.NotSet= - 
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EndUser=End user
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Noah=Noah
NewProspectViewModel.Spence=Spence
NewProspectViewModel.Walton=Walton
Last_Synced=Last synced
Sync_Data=Sync data
FreeFormReportViewModel.Title=Free form report
FreeFormReportViewModel.VisitTitle=Visit name
FreeFormReportViewModel.ExportSelected=Export selected tools
FreeFormReportViewModel.Inputs=Inputs
FreeFormReportViewModel.Results=Results
FreeFormReportViewModel.Outputs=Outputs
FreeFormReportViewModel.Charts=Charts
FreeFormReportViewModel.Notes=Notes 
FreeFormReportViewModel.ComfortItem=Comfort tool
FreeFormReportViewModel.HealthItem=Health tool
FreeFormReportViewModel.NutritionItem=Nutrition tool
FreeFormReportViewModel.ProductivityItem=Productivity tool
FreeFormReportViewModel.ComfortHeatStressBanner=Heat stress tool pen
FreeFormReportViewModel.PenTimeTitle=Pen time budget
FreeFormReportViewModel.RumenHealthTitle=Rumen health cud chewing
FreeFormReportViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Rumen health tmr particle herd score
FreeFormReportViewModel.RumenHealthManureTitle=Rumen health manure score
FreeFormReportViewModel.RumenHealthLocomotionTitle=Locomotion score
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Body condition score
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&#8482;
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&#8482;
FreeFormReportViewModel.NutritionForage=Forage audit
FreeFormReportViewModel.NutritionPile=Forage inventories
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Forage inventory feed out
FreeFormReportViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
FreeFormReportViewModel.OverallScore=Overall score
FreeFormReportViewModel.OverallResponses=Overall responses
FreeFormReportViewModel.OverallImprovements=Overall improvements
FreeFormReportViewModel.MilkProcessCalcInputsTab=Milking procedure comparison - inputs
FreeFormReportViewModel.MilkProcessCalcResultsTab=Milking procedure comparison - results
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Milking procedure comparison - resources
FreeFormReportViewModel.MarketingBranding=Go-to-market branding
UserPreferencesViewModel.Branding=Go-to-marketing branding
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Provimi=Provimi
RevenueInputViewModel.Title=Milking procedure comparison - inputs
RevenueInputViewModel.Edit=Edit
MilkProcessorResultsViewModel.Title=Milking procedure comparison - results
MilkProcessorResourcesViewModel.Title=Milking procedure comparison - resources
FreeFormReportViewModel.GeneralNotes=General visit notes
FreeFormReportViewModel.WalkThroughNotes=Walkthrough notes
MenuViewModel.Sync_PopUp=Syncing your data..
Please keep the app open while sync is in progress. Syncing may take longer on slower connections.
DashboardViewModel.GoodMorning=Good morning,
DashboardViewModel.GoodAfternoon=Good afternoon,
DashboardViewModel.MessageHeader=Message
DashboardViewModel.MessageBody=Just a reminder to utilize the notebook on each site visit to document specifics of that particular visit.
DashboardViewModel.RecentSiteVisit=Recent site visits
DashboardViewModel.Alert=Alert!
DashboardViewModel.AlertMessage=Data has not been synced in over {0} days.
ConfirmExport=Pressing ok will load every selected page and take a picture before returning to this screen.
ToolNotSelected=You must select at least one tool
SelectOnlyTwoNotes=Please select only 2 notes per tool.
ManureScoresChart=Manure scores-chart
ManureScoresResult=Manure scores-result
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Rumen health particle score
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Herd analysis
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Inputs
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Results
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Top
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Mid 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Mid 2
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Tray
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=DIM
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Edit
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Tmr particle score analysis
TMRParticleScoreHerdAnalysisResultsText=Tmr particle score analysis
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Edit dim amount
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Close
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Days in milk (dim)
BCSHerdAnalysisMasterViewModel.Title=Body condtion score
BCSHerdAnalysisMasterViewModel.BCSTitle=Body condition score
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Herd analysis
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Inputs
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Goals
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Results
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Body condition score analysis
BCSHerdAnalysisInputsViewModel.BCSEdit=Edit
BCSHerdAnalysisInputsViewModel.BCS=BCS
BCS=BCS
BCSHerdAnalysisInputsViewModel.BCSDIM=DIM
BCSHerdAnalysisInputsViewModel.BCSMilk=Milk
BCSEditMilkAndDimViewModel.Title=Edit dim and milk
BCSEditMilkAndDimViewModel.BCSDIMTitle=Days in milk (dim)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Milk
NotebookBCSHerdAnalysisInputs=Bcs herd analysis inputs
NotebookBCSHerdAnalysisGoals=Bcs herd analysis goals
NotebookBCSHerdAnalysisResults=Bcs herd analysis results
BodyConditionScoreMasterViewModel.Title=Body condition score
BodyConditionScoreMasterViewModel.SubHeading=Herd analysis
BodyConditionScoreMasterViewModel.Inputs=Inputs
BodyConditionScoreMasterViewModel.Goals=Goals
BodyConditionScoreMasterViewModel.Results=Results
TMRHerdAnalysisTableTitle=Tmr particle score herd analysis
BCSHerdAnalysisResultsViewModel.Title=Body condition score
BCSHerdAnalysisResultsViewModel.SubHeading=Herd analysis
BCSHerdAnalysisResultsViewModel.GraphTitle=Body condition score analysis
BCSHerdAnalysisResultsViewModel.MinBCS=Min. bcs
BCSHerdAnalysisResultsViewModel.MaxBCS=Max. bcs
BCSHerdAnalysisResultsViewModel.BCSAvg=Bcs avg.
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Milk/Head/Day
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Top (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Mid 1 (8mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Mid 2 (4mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Tray
MilkProcessorInputViewModel.MilkPrice=Milk price ({0}/{1})
MilkProcessorInputViewModel.WeightImperialCWT=CWT
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Milk price ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorResultsViewModel.MilkPrice=Milk price ({0}/{1})
MilkProcessorResultsViewModel.WeightImperialCWT=CWT
MilkProcessorResultsViewModel.WeightMetric=kg
SyncFailed=The sync could not be completed at this time, please try again.
NoResourcesAvailable=Resource not available.
ConfirmScorerSwitch=Changing the scorer will reset any entered data.
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Silage af density {0} (goal: > {1})
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=Silage af density {0} (goal: > {1})
VisitViewModel.NullVisitName=Visit name cannot be blank. Please enter a visit name.
ManureScoreHerdAnalysisMasterViewModel.Inputs=Inputs
ManureScoreHerdAnalysisMasterViewModel.Goals=Goals
ManureScoreHerdAnalysisMasterViewModel.Results=Results
ManureScoreHerdAnalysisMasterViewModel.Title=Rumen health manure score
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Herd analysis
CustomerProspectsSegmentViewModel.Title=Details
CustomerProspectsSegmentViewModel.SelectSegment=Select a segment
CustomerProspectsSegmentViewModel.NotSet= - 
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=End user
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Walton=Walton
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Manure score analysis
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Edit
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Manure score
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Days in milk (dim)
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Manure score analysis
SCCPremiumDeduction=Scc premium / deduction ({0}/{1})
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Days in milk (dim)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Edit dim amount
ManureScoreHerdAnalysisEditInputsViewModel.Close=Close
LocomotionHerdResultsViewModel.Title=Locomotion score analysis
LocomotionHerdRevenueViewModel.Revenue=Revenue
AverageMilkLoss=Average milk loss ({0})
MilkLossDay=Milk loss ({0}/day)
MilkLossYear=Milk loss ({0}/year)
RevenueLossDay=Revenue loss ({0}/day)
RevenueLossYear=Revenue loss ({0}/year)
SegmentViewModel.Title=Details
SegmentViewModel.SegmentTitle=Select a segment
ManureScoreHerdGoalsViewModel.TableTitle=Score by stage of lactation
ManureScoreHerdGoalsViewModel.Edit=Edit
ManureScoreHerdGoalsViewModel.GoalMinTitle=Manure goal min
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Manure goal max
ManureScoreHerdGoalsViewModel.FarOffDry=Far-off dry (less than -21)
ManureScoreHerdGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
ManureScoreHerdGoalsViewModel.Fresh=Fresh (0 to 15)
ManureScoreHerdGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
ManureScoreHerdGoalsViewModel.PeakMilk=Peak milk (61 to 120)
ManureScoreHerdGoalsViewModel.MidLactation=Mid lactation (121 to 200)
ManureScoreHerdGoalsViewModel.LateLactation=Late lactation (greater than 201)
ManureScoreHerdEditGoalsViewModel.Title=Edit goals
ManureScoreHerdEditGoalsViewModel.MinGoal=Manure min
ManureScoreHerdEditGoalsViewModel.MaxGoal=Manure max
ManureScoreHerdEditGoalsViewModel.FarOffDry=Far-off dry (less than -21)
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
ManureScoreHerdEditGoalsViewModel.Fresh=Fresh (0 to 15)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
ManureScoreHerdEditGoalsViewModel.PeakMilk=Peak milk (61 to 120)
ManureScoreHerdEditGoalsViewModel.MidLactation=Mid lactation (121 to 200)
ManureScoreHerdEditGoalsViewModel.LateLactation=Late lactation (greater than 201)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Close
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Min. score
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Max. score
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Average
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Manure score
LocomotionHerdResultsViewModel.Category1=Category 1.0
LocomotionHerdResultsViewModel.Category2=Category 2.0
LocomotionHerdResultsViewModel.Category3=Category 3.0
LocomotionHerdResultsViewModel.Category4=Category 4.0
LocomotionHerdResultsViewModel.Category5=Category 5.0
LocomotionHerdResultsViewModel.HerdAverage=Herd average
LocomotionHerdResultsViewModel.HerdGoal=Goal
MetabolicIncidenceMasterViewModel.Title=Metabolic incidence
MetabolicIncidenceMasterViewModel.Inputs=Inputs
MetabolicIncidenceMasterViewModel.Outputs=Outputs
Charts=Charts
Outputs=Outputs
MetabolicIncidenceMasterViewModel.Charts=Charts
Eula=EULA
Privacy_Statement=Privacy statement
Auto_Sync=Auto sync
ShowEulaViewModel.Eula=EULA
ShowEulaViewModel.EulaError=The end user license agreement cannot be displayed. Please connect to the internet before trying again.
NoResults=No results found
Search=Search
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Metabolic incidence percent
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Edit
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Incidence (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Goal (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Difference (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Economic impact of metabolic incidence levels above the defined goals for the herd.
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Annual economic impact
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Milk loss value
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Increased days open
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Treatment and other cost (culling,death)-usd
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Annual economic impact - total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Total cost
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Cost / cow
MetabolicIncidenceOutputsViewModel.Title=Metabolic incidence outputs
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Goal (%)
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceEditOutputsViewModel.Metritis=Metritis
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceEditOutputsViewModel.Ketosis=Ketosis
MetabolicIncidenceEditOutputsViewModel.MilkFever=Milk fever
MetabolicIncidenceEditOutputsViewModel.Dystocia=Dystocia
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Death loss
MetabolicIncidenceEditOutputsViewModel.Title=Edit goals
MetabolicIncidenceEditOutputsViewModel.Close=Close
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceOutputsViewModel.Metritis=Metritis
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceOutputsViewModel.Ketosis=Ketosis
MetabolicIncidenceOutputsViewModel.MilkFever=Milk fever
MetabolicIncidenceOutputsViewModel.Dystocia=Dystocia
MetabolicIncidenceOutputsViewModel.DeathLoss=Death loss
MetabolicIncidenceOutputsViewModel.TotalLosses=Total annual losses
MetabolicIncidenceInputsViewModel.Herd=Herd level information
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Total fresh cows/year
MetabolicIncidenceInputsViewModel.MilkPrice=Milk price
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Replacement cow cost
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Cost of extra days open
MetabolicIncidenceInputsViewModel.IncidenceCases=Metabolic incidence cases
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Enter the number of fresh cows and the number of metabolic incidence cases during the evaluation period. This will be converted to an annualized incidence cost on the outputs tab.
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Total fresh cows for evaluation
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceInputsViewModel.Metritis=Metritis
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceInputsViewModel.Ketosis=Ketosis
MetabolicIncidenceInputsViewModel.MilkFever=Milk fever
MetabolicIncidenceInputsViewModel.Dystocia=Dystocia
MetabolicIncidenceInputsViewModel.DeathLoss=Death loss
MetabolicIncidenceInputsViewModel.Mastitis=Mastitis
MetabolicIncidenceInputsViewModel.PerformanceTreatment=Performance and treatment costs
MetabolicIncidenceInputsViewModel.PerformanceMessage=Reference data used to calculate the economic impact of each metabolic incidence.
MetabolicIncidenceInputsViewModel.Costs=Costs
MetabolicIncidenceInputsViewModel.MilkLossKg=Milk loss per lactation ({0})
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Increased days open
MetabolicIncidenceInputsViewModel.TreatmentCost=Treatment and other cost (culling,death)-usd
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceInputsEditViewModel.Metritis=Metritis
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceInputsEditViewModel.Ketosis=Ketosis
MetabolicIncidenceInputsEditViewModel.MilkFever=Milk fever
MetabolicIncidenceInputsEditViewModel.Dystocia=Dystocia
MetabolicIncidenceInputsEditViewModel.DeathLoss=Mastitis
MetabolicIncidenceInputsEditViewModel.Title=Edit cost attributes
MetabolicIncidenceInputsEditViewModel.Close=Close
MetabolicIncidenceInputsEditViewModel.MilkCow=Milk / cow ({0})
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Days open
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Treatment and other costs (culling, death)-usd
MetabolicIncidenceInputsViewModel.Title=Metabolic incidence inputs
PenTimePenSelectionViewModel.PenTimeSection=PENS
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Pen time budget
PenTimeBudgetPenMasterViewModel.Inputs=Inputs
PenTimeBudgetPenMasterViewModel.Compare=Compare
PenTimeBudgetPenMasterViewModel.Results=Results
PenTimeBudgetPenMasterViewModel.Title=Pen time budget
EmailReportViewModel.MarketBranding=Go-to-market branding
EmailReportViewModel.UserPreferences=User settings
EmailReportViewModel.ExportSelected=Email selected tools
EmailReportViewModel.HerdAnalysis=Herd analysis
EmailReportViewModel.ComfortPenTimeBanner=Pen time budget tool
EmailReportViewModel.ComfortItem=Comfort tool
EmailReportViewModel.InputsOutputsChart=Inputs / outputs / charts
EmailReportViewModel.ForageLanding=Forage audit landing page
EmailReportViewModel.ForageScorecard=Forage audit scorecard
EmailReportViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
EmailReportViewModel.MilkProcessCalcInputsTab=Milking procedure comparison: inputs
EmailReportViewModel.MilkProcessCalcResultsTab=Milking procedure comparison: results
EmailReportViewModel.MilkProcessCalcResourcesTab=Milking procedure comparison: resources
EmailReportViewModel.Title=Email report
EmailReportViewModel.GotoMarketBranding=Go-to-market branding
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.EmailSelectedTools=Email selected tools
EmailReportViewModel.UserSettings=User settings
EmailReportViewModel.ComfortHeatStressBanner=Heat stress tool pen
EmailReportViewModel.ComfortToolsTitle=Comfort tools
EmailReportViewModel.RumenHealthTitle=Rumen health cud chewing
EmailReportViewModel.HealthItem=Health tool
EmailReportViewModel.Herd=Herd
EmailReportViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
EmailReportViewModel.RumenHealthManureTitle=Rumen health manure score
EmailReportViewModel.RumenHealthLocomotionTitle=Locomotion score
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
EmailReportViewModel.NutritionForage=Forage audit
EmailReportViewModel.NutritionItem=Nutrition
EmailReportViewModel.ForageAuditScorecard=Forage audit scorecard
EmailReportViewModel.ProductivityItem=Productivity tool
EmailReportViewModel.NutritionPile=Forage inventories
EmailReportViewModel.EmailSubject={0} report
EmailReportViewModel.EmailBody={0}-{1} report
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} report
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} report
PenTimeInputsViewModel.CowsPen=Cows in pen
PenTimeInputsViewModel.StallsPen=Stalls in pen
PenTimeInputsViewModel.WalkingTimeTo=Walking time to parlor(hours)
PenTimeInputsViewModel.ParlorTime=Time in parlor(hours)
PenTimeInputsViewModel.WalkingTimeFrom=Walking time from parlor(hours)
PenTimeInputsViewModel.Frequency=Milking frequency(per day)
PenTimeInputsViewModel.TotalStalls=Total stalls in parlor
PenTimeInputsViewModel.LockUp=Time in lock-up(hours)
PenTimeInputsViewModel.NonRestTime=Other non-rest time(hours)
PenTimeInputsViewModel.Resting=Resting requirement(hours)
PenTimeInputsViewModel.Eating=Eating time(hours)
PenTimeInputsViewModel.Drinking=Drinking / grooming time(hours)
MetabolicIncidenceChartsViewModel.Title=Metabolic incidence charts
MetabolicIncidenceChartsViewModel.GraphTitle=Metabolic incidence %
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Retained placenta
RetainedPlacenta=Retained placenta
MetabolicIncidenceChartsViewModel.Metritis=Metritis
Metritis=Metritis
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Displaced abomasum
DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceChartsViewModel.Ketosis=Ketosis
Ketosis=Ketosis
MetabolicIncidenceChartsViewModel.MilkFever=Milk fever
MilkFever=Milk fever
MetabolicIncidenceChartsViewModel.Dystocia=Dystocia
Dystocia=Dystocia
MetabolicIncidenceChartsViewModel.DeathLoss=Death loss
DeathLoss=Death loss
MetabolicIncidenceChartsViewModel.GoalPercent=Goal (%)
MetabolicIncidenceChartsViewModel.IncidencePercent=Incidence (%)
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Metabolic disorder cost / cow
MetabolicIncidenceChartsViewModel.Current=Current
EditDatesForComparisonViewModel.MetabolicIncidence=Please select up to 5 visit dates for comparison from the list below.
PenTimePenSelectionViewModel.Title=Pen time budget
PenTimeInputsViewModel.PenTimeTitle=Pen time budget
ChooseAppPDF=Please choose an app to view the pdf
PenTimeBudgetResultsViewModel.Title=Pen time budget results
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Time available for resting
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Potential milk loss/gain
PenTimeBudgetResultsViewModel.TimeRequired=Time required
PenTimeBudgetResultsViewModel.TimeRemaining=Time remaining
PenTimeBudgetResultsViewModel.Hours=Hours
EditDatesForComparisonViewModel.Hour=Hours
PenTimeBudgetResultsViewModel.MilkDifference=Potential milk difference
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=lbs
TimeRemaining=Time remaining for resting
MilkLossGain=Potential milk loss/gain
EditDatesForComparisonViewModel.PenTimeBudget=Please select up to 7 visit dates for comparison from the list below.
NoteCategoryViewModel.Title=Note
NoteCategoryViewModel.SelectCategory=Select a category
NoteCategoryViewModel.FooterText=Only one category can be selected per note.
Observation=Observation
Action=Action
WalkthroughReportLandingViewModel.Title=Walkthrough report
WalkthroughReportLandingViewModel.PenAnalysis=Pen analysis
WalkthroughReportLandingViewModel.HerdAnalysis=Herd analysis
WalkthroughPenSelectionViewModel.Title=Walkthrough report
WalkthroughPenSelectionViewModel.Pens=PENS
Task=Task
Event=Event
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Privacy statement
ShowPrivacyStatementViewModel.PrivacyStatement=<![CDATA[<p><b>Privacy Statement</b></p><p>Last Updated: January 3, 2017</p><p><b>Scope</b></p><p>Cargill, Incorporated (\u201CCargill\u201D or \u201CWe\u201D) collects information about you when you use this mobile application (\u201CApp\u201D), which is intended for consultants offering the Dairy Enteligen\u2122 service on behalf of Cargill.</p><p><b>Personal Information</b></p><p>Cargill may collect the following personal information directly from you, for instance when you register with us, and through your use of the App.</p><p>\u00B7 <b>Your Name</b></p><p>\u00B7 <b>Your Location</b> (via GPS or other similar technology)</p><p>\u00B7 <b>Your Photos </b>or<b> Videos</b> (if shared with Cargill via the App)</p><p>We may use common technologies, such as cookies and beacons, in the App to collect such personal information. </p><p><b>Use and Sharing \u2013 Business Context</b></p><p>Our <a href="http://www.cargill.com/privacy/business-notice/index.jsp">Business Information Notice</a> explains how we use personal information collected about you in a business context.</p><p><b>Location Collection Consent</b></p><p>By using the App, you expressly consent to Cargill\u2019s collection of your real-time location information, and expressly waive and release Cargill from any and all liability, claims, causes of action or damages arising from your use of the App, or in any way relating to the use of the location information.</p>]]>
SiteVisitSummaryReport=Site visit summary report
WalkthroughReport=Walkthrough report
Customer=Customer
CustomerWithSiteName=Customer name - site name
VisitDate=Date of visit
ReportDate=Report date
Report=Report
PDFPageNumber=Page {0} of {1}
PDFDisclaimer=Cargill incorporated, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. \u00a9{0} cargill, incorporated. All rights reserved
SiteVisitSummary=Site visit summary
AnimalsInPen=Animals in pen
Animals=Animals
AnimalInformation=Animal information
LocomotionNumberinPen=Locomotion (number in pen)
LocomotionPercentofPen=Locomotion(% of pen)
AnimalsInHerd=Animals in herd
LocomotionNumberinHerd=Locomotion (number in herd)
LocomotionPercentofHerd=Locomotion(% of herd)
CudChewingPercentOfPen=Cud chewing(% of pen)
ManureScorePercentOfPen=Manure score(% of pen)
CudChewingAverageNumber=Average number
CudChewingPen=Pen
CudChewingPercentChewing=% chewing
ChewsPerCud=Chews per cud
WalkthroughReportViewModel.Title=Walkthrough report
WalkthroughReportViewModel.CudChewing=Rumination, % chewing
WalkthroughReportViewModel.CudChewCategorySection=Cud counts, chews per cud
WalkthroughReportViewModel.RumenHealthManureTitle=Manure score
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Locomotion score
WalkthroughReportViewModel.HockAbrasion=Hock abrasion, % of animals
WalkthroughReportViewModel.Appearance=Appearance
WalkthroughReportViewModel.RumenFill=Rumen fill
WalkthroughReportViewModel.UterineDischarge=Uterine discharge, % of animals
WalkthroughReportViewModel.NasalDischarge=Nasal discharge, % of animals
WalkthroughReportViewModel.ComfortItem=Cow comfort, % of animals laying
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=Body condition score (bcs)
WalkthroughReportViewModel.WaterQuality=Water quality
WalkthroughReportViewModel.BeddingCleanliness=Bedding cleanliness
WalkthroughReportViewModel.BeddingDepthSoft=Bedding: depth and softness
WalkthroughReportViewModel.Trends=Positive trends
WalkthroughReportViewModel.Opportunities=Opportunities
WalkthroughReportViewModel.Comments=Comments
WalkthroughReportViewModel.Current=Current
WalkthroughReportViewModel.Previous=Previous
WalkthroughReportViewModel.Goals=Goal
WalkthroughReportViewModel.Clean=Clean
WalkthroughReportViewModel.ModeratelyClean=Moderately clean
WalkthroughReportViewModel.Dirty=Dirty
WalkthroughReportQualityViewModel.Title=Walkthrough report
WalkthroughReportQualityViewModel.Clean=Clean
WalkthroughReportQualityViewModel.ModeratelyClean=Moderately clean
WalkthroughReportQualityViewModel.Dirty=Dirty
WalkthroughReportQualityViewModel.WaterQuality=Select water quality
WalkthroughReportQualityViewModel.BeddingCleanliness=Select bedding cleanliness
Send=Send
WalkthroughReportHerdAnalysisViewModel.PensForExport=Pens for export
WalkthroughReportHerdAnalysisViewModel.FinalObservations=Final observations
WalkthroughReportHerdAnalysisViewModel.Trends=Positive trends
WalkthroughReportHerdAnalysisViewModel.Opportunities=Opportunities
WalkthroughReportHerdAnalysisViewModel.Comments=Comments
WalkthroughReportHerdAnalysisViewModel.Branding=Go-to-market branding
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.Title=Walkthrough report herd analysis
WalkthroughReportHerdAnalysisViewModel.MainHeading=Walkthrough report
WalkthroughReportHerdAnalysisViewModel.SubHeading=Herd analysis
WalkthroughReportHerdAnalysisViewModel.ExportSelected=Email selected tools
EmailReportViewModel.HeatstressEvaluationTitle=Heat stress evaluation
EmailReportViewModel.Inputs=Inputs
EmailReportViewModel.Charts=Charts
EmailReportViewModel.CudChewingTitle=Cud chewing
EmailReportViewModel.Results=Results
EmailReportViewModel.NumOfChews=Number of chews
EmailReportViewModel.PenInputs=Pen analysis - inputs
EmailReportViewModel.PenResults=Pen analysis - results
EmailReportViewModel.HerdInputs=Herd analysis - inputs
EmailReportViewModel.Notes=Notes
EmailReportViewModel.HerdResults=Herd analysis - results
EmailReportViewModel.TMRParticleScoreTitle=Rumen health particle score
EmailReportViewModel.HerdGoals=Herd analysis - goals
EmailReportViewModel.ManureScoreTitle=Manure score
EmailReportViewModel.LocomotionScoreTitle=Locomotion score
EmailReportViewModel.HerdRevenue=Herd analysis - revenue
EmailReportViewModel.RumenHealthBodyConditionTitle=Body condition score
EmailReportViewModel.MetabolicIncidenceTitle=Metabolic incidence
EmailReportViewModel.Outputs=Outputs
EmailReportViewModel.PileAndBunkerTitle=Forage inventory
EmailReportViewModel.Capacity=Capacity
EmailReportViewModel.FeedOut=Feed out
LocomotionHerdRevenueViewModel.Title=Locomotion herd revenue
EmailReportViewModel.PenTimeTitle=Pen time budget
EmailReportViewModel.PenCompare=Pen analysis - compare
EmailReportViewModel.MilkProcessRevenue=Milking procedure comparison
EmailReportViewModel.Resources=Resources
EmailReportViewModel.CategoryList=Category list
EmailReportViewModel.Improvements=Improvements
EmailReportViewModel.ScoreScreen=Scores
FinalObservations=Final observations
Trends=Positive trends
Opportunities=Opportunities
Comments=Comments
Current=Current
Previous=Previous
Goal=Goal
Clean=Clean
ModeratelyClean=Moderately clean
Dirty=Dirty
WalkthroughReportHerdAnalysisViewModel.CudChewing=Rumination, % chewing
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Cud counts, chews per cud
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Manure score
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Locomotion score
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Hock abrasion, % of animals
WalkthroughReportHerdAnalysisViewModel.Appearance=Appearance
WalkthroughReportHerdAnalysisViewModel.RumenFill=Rumen fill
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Uterine discharge, % of animals
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Nasal discharge, % of animals
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Cow comfort, % of animals laying
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=Body condition score (bcs)
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Water quality
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Bedding cleanliness
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Bedding: depth and softness
SiteDetailViewModel.ReportNotAvailable=Report not available for download.
SiteDetailViewModel.ReportNotAvailableTitle=Status
SiteDetailViewModel.ReportDownloadTimeout=Unable to download the file, please try when you have better connectivity
SiteDetailViewModel.GetReportMsg=Downloading report...
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0.0
PublishVisit=Published
CurrentSCC=Current scc (cells/{0})
ReportPDFNote=Note:  
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Particle score (% on screen)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Goal top (19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Goal mid 1 (8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Goal mid 2 (4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Goal mid 2 (1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Goal tray
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Min
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Max
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Current
ReadyToMilkMasterViewModel.Title=Ready2Milk&#8482; index
ReadyToMilkInputViewModel.Title=Ready2Milk&#8482; inputs
ReadyToMilkOutputViewModel.Title=Ready2Milk&#8482; outputs
ReadyToMilkChartViewModel.Title=Ready2Milk&#8482; charts
ReadyToMilkMasterViewModel.Inputs=Inputs
ReadyToMilkMasterViewModel.Outputs=Outputs
ReadyToMilkMasterViewModel.Charts=Charts
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Ready2Milk&#8482; index
ReadyToMilkChartViewModel.RetainedPlacenta=Retained placenta
ReadyToMilkChartViewModel.Metritis=Metritis
ReadyToMilkChartViewModel.DisplacedAbomasum=Displaced abomasum
ReadyToMilkChartViewModel.Ketosis=Ketosis
ReadyToMilkChartViewModel.MilkFever=Milk fever
ReadyToMilkChartViewModel.Dystocia=Dystocia
ReadyToMilkChartViewModel.DeathLoss=Mastitis
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Ready2Milk&#8482; index
ReadyToMilkChartViewModel.DisorderGraphTitle=Annual metabolic disorder cost/cow
ReadyToMilkChartViewModel.Current=Current
ReadyToMilkListViewModel.Title=Ready2Milk&#8482; index
NotebookReadyToMilkInputs=ReadyToMilk inputs
NotebookReadyToMilkOutputs=ReadyToMilk outputs
NotebookReadyToMilkCharts=ReadyToMilk charts
None=None
Moderate=Moderate
Good=Good
Medium=Medium
Poor=Poor
Normal=&lt;0.5bcs unit
Excessive=&gt;0.5bcs unit
ReadyToMilkInputViewModel.HealthRecords=Health records
ReadyToMilkInputViewModel.Herd=Herd level information
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Total fresh cows/year
ReadyToMilkInputViewModel.MilkPrice=Milk price
ReadyToMilkInputViewModel.ReplacementCowCost=Replacement cow cost
ReadyToMilkInputViewModel.CostExtraDaysOpen=Cost of extra days open
ReadyToMilkInputViewModel.CloseUp=Close up cows
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Specific close-up diet
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Perceived heat stress close-up
ReadyToMilkInputViewModel.ComfortCloseUp=Comfort close-up
ReadyToMilkInputViewModel.FreshCows=Fresh cows
ReadyToMilkInputViewModel.SpecificDiet=Specific 0-21 dim diet
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Perceived heat stress 0-21 dim
ReadyToMilkInputViewModel.ComfortDiet=Comfort 0-21 dim
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Bcs change dry off to 21 dim
ReadyToMilkInputViewModel.CudChewingDiet=Cud chewing fresh cows
ReadyToMilkInputViewModel.LocomotionScore=Locomotion score
ReadyToMilkInputViewModel.RumenFill=Rumen fill
ReadyToMilkInputViewModel.MilkYield=Milk yield 15-60 dim
ReadyToMilkInputViewModel.Health=HEALTH
ReadyToMilkInputViewModel.HealthDesc=Enter the number of fresh cows and the number of metabolic incidence cases during the evaluation period. This will be converted to an annualized incidence cost on the outputs tab.
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Total fresh cows for evaluation
ReadyToMilkInputViewModel.MilkFever=Milk fever
ReadyToMilkInputViewModel.Ketosis=Ketosis
ReadyToMilkInputViewModel.DisplacedAbomasum=Displaced abomasum
ReadyToMilkInputViewModel.PrematureCalvings=Premature calvings
ReadyToMilkInputViewModel.Dystocia=Dystocia
ReadyToMilkInputViewModel.RetainedPlacenta=Retained placenta
ReadyToMilkInputViewModel.Metritis=Metritis
ReadyToMilkInputViewModel.Mastitis=Mastitis 0-21 dim
ReadyToMilkInputViewModel.SccFirstTest=Scc 1st test (x1000 scc/ml)
ReadyToMilkInputViewModel.DeadCowsOrCulled=Health-related death or culling
ReadyToMilkInputViewModel.Next=Next
ReadyToMilkInputViewModel.Back=Back
StrategyToReduceIncidence=Strategies to reduce incidence
StrategyToReduceRetainedPlacenta=Retained placenta
StrategyToReduceMetritis=Metritis
StrategyToReduceDisplacedAbomasum=Displaced abomasum
StrategyToReduceKetosis=Ketosis
StrategyToReduceMilkFever=Milk fever
StrategyToReduceDystocia=Dystocia
StrategyToReduceMastitis=Mastitis
StrategyToReduceRetainedPlacentaDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><span><strong>Strategy to reduce retained placenta incidence</strong></span></h4><p>Retained placenta incidence is positively correlated with immune suppression, high stress hormones, hypocalcemia (including subclinical), assisted calving and abortions, endemic infectious agents, and genetics.</p><p>Monitor the following areas, in descending order of importance:</p><ol><li>Nutrition <br/> <p style="padding-left: 30px;">Review close-up diets following MAX<sup>TM</sup> guidelines with a special focus on:</p><ul><li>Se and vitamin E</li><li>Minerals levels (K, Ca, Mg) to decrease risk of subclinical and clinical hypocalcemia</li><li>Use a low or negative DCAD to decrease the incidence of hypocalcemia</li></ul></li></ol><ol start="2"><li>Management</li><ul><li>Manage BCS (3.00 at calving) during late lactation and the dry period</li><li>Minimize stressors in the close up and postpartum area to decrease fat mobilization and immunosuppression around parturition.</li><li>Improve cow comfort and provide enough bunk space</li><li>Follow a calving procedure set by your veterinarian</li><li>Do not help cows calving unnecessarily</li><li>Maintain a clean environment to decrease chances of uterine infection.</li></ul></ol></span>]]>
StrategyToReduceMetritisDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><strong>Strategy to reduce metritis incidence</strong></h4><p>Risk factors for metritis include retained placenta, injury to the reproductive tract due to a difficult calving, improper calving protocol , unsanitary calving area, nutritional deficiency such as vitamin E or selenium deficiencies, and over-conditioned cows.</p><p>Monitor the following areas, in descending order of importance&nbsp;:</p><ol><li>Calving practices<br /><ul><li>Are employees carrying bacteria into the uterus when assisting in calving?</li><li>Are cows being helped too early or too late?</li><li>Are calves being pulled too often?</li></ul></li></ol><ol start="2"><li>Treatment practices<br /><ul><li>How are cows with RP's or metritis treated?</li><li>Any chance of carrying bacteria from the outside environment or vagina into the uterus at that time?</li></ul></li></ol><ol start="3"><li>Animal Stress<br /><ul style="list-style-type: circle;"><li>Excess stress before calving may be depleting the cow immune system , lowering the resistance to any infection after calving.</li></ul></li></ol><ol start="4"><li>Nutrition<ul><li>Review maxDiet balance in dry cow, focusing on BCS control, mineral balance and supply of antioxidant nutrients (vit E, A, selenium, zinc and copper)</li></ul></li><li>Other disease check (ketosis, LDA)</li></ol></span>]]>
StrategyToReduceDisplacedAbomasumDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><strong>Strategy to reduce displaced abomasum incidence</strong></h4><p>The prevention strategy relies on managing the risk factors, as we currently do not have a clear direct cause.</p><p>Prevention includes appropriate nutrition and management, and management of concomitant diseases:</p><ul><li>Control of nutritional risk factors:<ul><li>Avoid over-conditioning cows (ideal 3.00 BCS at dry-off and calving).</li><li>Provide enough forage NDF.</li><li>Manage physical form of the maxDiet.</li><li>Pay attention to mineral requirements.</li><li>Avoid other metabolic disorders such as hypocalcemia, and also infectious disease that could reduce intake.</li></ul></li><li>Best management practices:<ul><li>Assure feed intake in fresh cows especially during the hours/days after calving</li><li>Manage feed bunks properly</li><li>Increase cow comfort, reduce any stressors</li></ul></li></ul></span>]]>
StrategyToReduceKetosisDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><strong>Strategy to reduce ketosis incidence</strong></h4><ol><li>Assure adequate cow comfort (bedding, heat stress control and ventilation, avoid stress and overcrowding, etc.)</li></ol><p>&nbsp;</p><ol start="2"><li>Balance diets according to MAX<sup>TM</sup> requirements for nutrients and physical maxDiet characteristics. Consult the Transition Cow products inventory for specific product formulations developed for prevention of ketosis. Assure use of good quality forages that will increase intake and rumen buffer capacity.</li></ol><p>&nbsp;</p><ol start="3"><li>Monitor BCS change between far-off and calving: Dry cows off at 3.00 BCS and maintain BCS during the dry period to avoid excessive lipid mobilization around calving.</li></ol><p>&nbsp;</p><ol start="4"><li>Implement a specific health protocol developed for fresh cows to detect and prevent metabolic disorders and infectious diseases. Monitor intake, rumen fill and rumination.</li></ol></span>]]>
StrategyToReduceMilkFeverDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><strong>Strategy to reduce milk fever incidence</strong></h4><p>Two alternative strategies for the prevention of hypocalcemia in dairy cows rely 100% on dietary management.</p><ul><li>Use dry cow diets low in Ca</li><li>Use dry cow diets formulated to a low DCAD</li></ul><p>&nbsp;</p><p>Area to consider to keep hypocalcemia under control are:</p><ul><li>Testing forages for minerals ( calcium, phosphorus, magnesium, potassium, sodium, sulfur, and chloride) in a validated laboratory</li><li>Review the current ration program following MAX<sup>TM</sup> requirements for close-up cows and feeding management practices (special attention to ad libitum forages, K concentration in forages, free-choice minerals fed to dry cows, dry cows sorting out their maxDiet)</li><li>Consult the Transition cow product inventory for specific formulations developed to prevent hypocalcemia</li><li>When using DCAD diets:<ul><li>Carefully monitor feed intake as anionic salts are unpalatable and can reduce dry matter intake</li><li>Monitor urine pH to check the efficacy of the maxDiet changes</li></ul></li></ul></span>]]>
StrategyToReduceDystociaDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><strong>Strategy to reduce dystocia incidence</strong></h4><p>Help prevent dystocia by:</p><ul><li>Ensuring heifers are inseminated at the proper age and bodyweight.</li><li>Selecting potential sires on the basis of known calving ease.</li><li>Improving personnel training regarding proper timing and methods of intervention during calving, plus appropriate methods to care for compromised newborn calves</li><li>Review the current ration program following MAX<sup>TM</sup> requirements for far-off and close-up cows and heifers focusing on:<ul><li>Energy to maintain body condition score and fetal growth</li><li>Prevention of over-conditioned cows at calving</li><li>Control of hypocalcemia risk in the herd</li></ul></li></ul></span>]]>
StrategyToReduceMastitisDetails=<![CDATA[<span style="font-family:Calibri,Calibrib;"><h4><strong>Strategy to reduce mastitis incidence</strong></h4><p>Microorganisms that most frequently cause mastitis can be divided into two main categories:</p><ul><li>Contagious pathogens, which spray from cow to cow primarily during the milking, process <em>(i.e. Strep. agalactiae&nbsp;and&nbsp;Staph. Aureus)</em></li><li>Environmental pathogens, which come from the dairy cows environment <em>(i.e. E. coli&nbsp;and&nbsp;Strep. Uberis)</em></li></ul><p>&nbsp;</p><p>Depending on which bacteria category is causing mastitis, the intervention focus has to be adapted.</p><ul><li><strong>Contagious mastitis control</strong>: there is no magic silver bullet preventing infections for all pathogens but the following steps will help:<ul><li>Focus on hygiene around and during milking , including teat dipping</li><li>Milk infected cows at last</li><li>Carry out regular milking machine maintenance</li><li>Review the dry cow nutrition plan using MAX<sup>TM</sup> guidelines to ensure a proper nutrient supply (energy, antioxidant) to support immune function</li></ul></li></ul><p>&nbsp;</p><ul><li><strong>Controlling environmental mastitis</strong><ul><li>Ensure a clean and dry bedding, well-ventilated barn</li><li>Correct stocking density</li><li>General hygiene of the udder</li><li>Parlor routine</li><li>Review the dry cow nutrition plan using MAX<sup>TM</sup> guidelines to ensure a proper nutrient supply (energy, antioxidant) to support immune function</li></ul></li></ul></span>]]>
EmailReportViewModel.PenDensity=Pen density
EmailReportViewModel.MilkingTime=Milking time
EmailReportViewModel.TimeBudget=Time budget
EmailReportViewModel.AnimalImpact=Animal impact
BCSSelectPointScaleViewModel.SelectPointScale=Select a point scale
BCSSelectPointScaleViewModel.FooterText=Only one point scale can be used per visit. Changing the point scale will result in lost values.
BCSSelectPointScaleViewModel.Title=Body condition score
ConfirmScalePointSwitch=Changing the scale point will reset any entered data.
NotebookBCSSelectPointScale=Body condition-select point scale
NotebookSectionHealthTools=Health tools
SiteDetailViewModel.VisitNotDownloaded=Visit not downloaded
SiteDetailViewModel.VisitDownloadPrompt=Visit not downloaded, do you want to try download this visit?
VisitDownloadProceed=Proceed
SiteDetailViewModel.DownloadingVisit=Downloading visit...
SiteDetailViewModel.NetworkErrorMessageTitle=Network error
SiteDetailViewModel.NetworkErrorMessage=There is currently no network available.
SiteDetailViewModel.VisitUnavailable=Visit data is unavailable.
PenTimePenSelectionViewModel.NoLactatingPen=To access this tool, please ensure you have at least one pen with a lactating maxdiet.
SiteDetailsSetupViewModel.SiteMandatoryFields=Site name, milk price, milking system and pen are mandatory fields. Fill all the mandatory fields to continue.
ProductivityToolsViewModel.MilkSoldEvaluation=Milk sold evaluation
FreeFormReportViewModel.MilkSoldEvaluation=Milk sold evaluation
MilkSoldEvaluationMasterViewModel.Title=Milk sold evaluation
MilkSoldSpinnerViewModel.Title=Milk sold evaluation
MilkSoldEvaluationChartsViewModel.Title=Milk sold evaluation
MilkSoldEvaluationMasterViewModel.Inputs=Inputs
MilkSoldEvaluationMasterViewModel.Outputs=Outputs
MilkSoldEvaluationMasterViewModel.Charts=Charts
MilkSoldEvaluationInputsViewModel.Herd=Herd level information
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Lactating animals \u20F0
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Animals in tank \u20F0
MilkSoldEvaluationInputsViewModel.MilkPickup=Milk pickup \u20F0
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Dry matter intake ({0}) \u20F0
MilkSoldEvaluationInputsViewModel.DaysInMilk=Days in milk (dim) \u20F0
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Milk urea measure \u20F0
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Milk processor information
Pickup=Pickup {0}
MilkSoldEvaluationMasterViewModel.AddNew=Add new
Daily=Daily
EveryOtherDay=Every other day
MUN=Mun (mg/dl)
MilkUrea=Milk urea (mg/dl)
MilkSoldPickupViewModel.Title=Edit pickup {0}
MilkSoldPickupViewModel.MilkSold=Milk sold, {0} \u20F0
MilkSoldPickupViewModel.AnimalsInTank=Animals in tank \u20F0
MilkSoldPickupViewModel.DaysInTank=Days in tank \u20F0
MilkSoldPickupViewModel.MilkFat=Milk fat %
MilkSoldPickupViewModel.MilkProtein=Milk protein %
MilkSoldPickupViewModel.SCC=Somatic cell count (1,000 cells/ml)
MilkSoldPickupViewModel.BCC=Bacteria cell count (1,000 cfu/ml)
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Update site setup
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Evaluation days
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Average milk production, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Average milk production, {0} (animals in tank)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Average milk fat %
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Milk fat yield ({0})
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Average milk protein %
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Milk protein yield ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Milk fat + protein yield ({0})
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Component efficiency (% of dmi)
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Feed efficiency (ratio)
MilkSoldEvaluationOutputsViewModel.AvgSCC=Average scc (1,000 cells/ml)
MilkSoldEvaluationOutputsViewModel.AvgBCC=Average bacteria count (1,000 cfu/ml)
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Milk production and days in milk
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Component yield and efficiency
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=Milk fat % and milk protein %
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Somatic cell count and milk urea
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=Dry matter intake and feed efficiency
MilkSoldEvaluationChartsListViewModel.VisitComparison=Please select visits for comparison
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Days in milk
MilkSoldEvaluationChartsViewModel.MilkProduction=Milk production
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Milk production and days in milk
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Component yield and efficiency
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=Milk fat % and milk protein %
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Somatic cell count and milk urea
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=Dry matter intake and feed efficiency
MilkSoldEvaluationChartsViewModel.MilkFat=Milk fat %
MilkSoldEvaluationChartsViewModel.MilkProtein=Milk protein %
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Somatic cell count
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Milk urea
NotebookSectionMilkSoldEvaluationInputs=Milk sold evaluation inputs
NotebookSectionMilkSoldEvaluationOutputs=Milk sold evaluation outputs
NotebookSectionMilkSoldEvaluationCharts=Milk sold evaluation charts
NotebookSectionMilkSoldEvaluationEditPickup=Milk sold evaluation edit pickup
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Feed efficiency
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Dry matter intake
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Component efficiency
MilkSoldEvaluationChartsViewModel.ComponentYield=Component yield
MilkSoldEvaluationInputsViewModel.AddPickup=Add pickup
CurrentVisitSummary=Current visit summary
TonsDM=Tons dm
TonsAF=Tons af
MetricTonsDM=Metric tons dm
MetricTonsAF=Metric tons af
TonsDMSilo=Tons dm (remaining in silo)
TonsAFSilo=Tons af (remaining in silo)
MetricTonsDMSilo=Metric tons dm (remaining in silo)
MetricTonsAFSilo=Metric tons af (remaining in silo)
FeedingRate=Feeding rate (as-fed / cow)
CowsToBeFed=Cows to be fed
WeightDMInLengthMetric=Kgs. dm in 1 meter
WeightDMInLengthImperial=Lbs. dm in 1 foot
FeedOutSurfaceAreaMetric=Feed out surface area (m^2)
FeedOutSurfaceAreaImperial=Feed out surface area (ft^2)
LengthPerDayMetric=Cm. per day
LengthPerDayImperial=In. per day
AtThreeLengthPerDayImperial=At 3 in. per day
AtSixLengthPerDayImperial=At 6 in. per day
AtThreeLengthPerDayMetric=At 7 cm. per day
AtSixLengthPerDayMetric=At 15 cm. per day
FeedOutRateInfo=Feed out rate information
Days=Days
StartDate=Start date
DateGone=Date gone
CowsPerDayNeeded=Cows/day needed
PileAndBunkerName=Forage inventory name
Capacity=Capacity
Costs=Costs
PileAndBunkerTitle=Forage inventory
PileBunkerCapacities=Forage inventories
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tons per day
<!--   Pile and bunker end-->
MilkProcessorEditComparisonValuesViewModel.NoStimulation=No stimulation
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Inadequate stimulation
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Optimal stimulation
CommonSpinnerViewModel.NoStimulation=No stimulation (unit attachment only)
CommonSpinnerViewModel.InadequateStimulation=Inadequate stimulation (inadequate teat preparation: &lt;10 seconds; unit attachment: &lt; 60 or &gt; 120 seconds)
CommonSpinnerViewModel.OptimalStimulation=Optimal stimulation (adequate teat preparation: 10-20 seconds; unit attachment within 60 to 90 seconds)
CompletedTimeKey=Time completed
WalkthroughReportHerdAnalysisViewModel.Notes=Notes
EmailReportViewModel.WalkthroughReportTitle=Walkthrough report
SelectOnlyThreeNotes=Please select only 3 notes in the tool.
SystemGenerated=System generated
DietDetailViewModel.SystemGenerated=System generated
QuestionViewModel.VisitNotebook=Visit notebook
SiteDetailsSetupViewModel.NumberOfStalls=Total stalls in parlor
FreeHandNoteEditorPageTitle=Free hand note
FreeHandNoteSaveUserDialogMessage=Do you want save this note ?
FreeHandNoteClearPaletteDialogMessage=Do you want to delete everything on the screen ?
DDWOfflineMessage=Since there is no network, would you like to view the offline report?
PerceivedHeatStressDietInfoMessage=Moderate heat stress: panting, drool or foam but no open mouth, respiration rate 40 to 120 bpm
Severe heat stress: open mouth + drooling, respiration rate 120 to more than 160 bpm
PrematureKelvingsKeyInfoMessage=The delivery of one or more alive calves at least 10 days before due delivery date.
ExtraDaysOpenCostInfoMessage=It typically varies from $3 to $5 per additional open day.
ReadyToMilkMasterViewModel.MastitisNotPresent=Mastitis is not filled.
PileAndBunkerCapacityViewModel.Resources=Resources
RumenHealthManureLandingViewModel.Resources=Resources
RumenHealthLocomotionLandingViewModel.Resources=Resources
RumenHealthBodyConditionLandingViewModel.Resources=Resources
PileAndBunkerCapacitiesDensity=Forage inventory capacities density reference guide
CommonSpinnerViewModel.ManureScorePDF=Manure score guide
CommonSpinnerViewModel.LocomotionPDF=Locomotion scoring guide
CommonSpinnerViewModel.BodyConditionPDF=Body condition scoring guide
TonsPerDay=Tons per day
CurrentDownResponse=Current let down response ({0}/cow/day)
PotentialDownResponse=Potential let down response ({0}/cow/day)
<!--TMRParticleScoreName=Tmr particle score
-->
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Tmr particle score name
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Add tmr score
AddTMRScore=Add tmr score
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Avg. tmr particle score
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Standard deviation
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Goals
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Top (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Mid 1 (8mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Mid 2 (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Tray
PercentageOnScreenCurrentVisit=Percent on screen (%) - current visit
PercentageOnScreenTrend=Percent on screen (%) - trend
StandardDeviationScoreTitle=Standard deviation
AverageScoreTitle=Avg. tmr particle score
CreateDuplicateNameDiet=A maxdiet already exists with same name, please enter unique one.
NewPenViewModel.PenMapping=Pen mapping
NewPenViewModel.PenSelection=Select pen
NewPenViewModel.OnlyOnePen=You have only one pen.
NewPenViewModel.PublishPenAlert=Please publish all visit related to pen you want to merge.
NewPenViewModel.UserCreatedPen=User created pen
ShowSyncStatusViewModel.Title=Data sync summary
ShowSyncStatusViewModel.GetAccounts=Account records received :
ShowSyncStatusViewModel.GetVisits=Visit records received :
ShowSyncStatusViewModel.GetNotes=Notes records received :
HomeViewModel.ConsumersTab=Consumers
ConsumerDetailsViewModel.ProspectTitle=Consumer details
ConsumersViewModel.NewConsumer=Add a new consumer
ConsumerDetailsViewModel.MainHeading=SITES
ConsumerDetailsViewModel.NewSite=Add a new site
ConsumerDetailsViewModel.DeleteProspect=Delete consumer
ConsumerDetailsViewModel.DeleteProspectPrompt=Are you sure you want to delete this consumer? Consumer, site and in progress visit information will be lost.
NewProspectViewModel.ConsumerDetails=Consumer details
NewProspectViewModel.NameNotUniqueForConsumer=A consumer named "{0}" already exists. Names must be unique.
LocomotionSelectPenViewModel.MissingDiet=Please enter a valid maxdiet for this pen.
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Tmr particle score information
FreeHandNotesViewModel.Save=Save
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=Tmr particle score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Goals
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=Tmr particle score
StatusArchived=Archived
HealthToolsViewModel.RumenHealthUrinePHTitle=Urine ph
UrinePHPenSelectionViewModel.Title=Urine ph
UrinePHPenSelectionViewModel.UrinePHPenList=Pens (lactating and dry only)
UrinePHMasterViewModel.Title=Urine ph
UrinePHMasterViewModel.Inputs=Inputs
UrinePHMasterViewModel.Results=Results
UrinePHInputsViewModel.CalculatorHeading=Please select a cow to enter the urine ph value. Tap "Add New" to add cows to the list.
UrinePHInputsViewModel.CudChewCategorySection=Cows
UrinePHInputsViewModel.DietDCAD=Diet dcad, meq/100g
UrinePHInputsViewModel.UrinePhSTDDEV=Std. deviation (calculated)
UrinePHInputsViewModel.UrinePHAVG=Avg. urine ph (calculated)
UrinePHInputsViewModel.Resources=Resources
UrinePHDensity=Urine ph resource
UrinePHInputsViewModel.AddNew=Add new
UrinePHInputsViewModel.CowsOutsideTargetRange=Cows outside the range (%)
UrinePHInputsViewModel.TargetUrinePHRange=Target urine ph range
UrinePHResultsViewModel.UrinePHAVG=Average urine ph
UrinePHResultsViewModel.DietDCAD=Diet dcad, meq/100g
UrinePHAVG=Average urine ph
DietDCAD=Diet dcad meq/100g
UrinePHEditGoalViewModel.GoalMin=Goal - min
UrinePHEditGoalViewModel.GoalMax=Goal - max
UrinePHEditGoalViewModel.Title=Edit goals
UrinePHEditGoalViewModel.TargetUrinePHRange=Target urine ph range
NotebookUrinePHInputs=Urine ph inputs
NotebookUrinePHOutputs=Urine ph results
NotebookUrinePHEditGoals=Urine ph edit goals
UrinePHInputsViewModel.CoefficientVariation=Coefficient of variation (c.v.) (%)
VisitSummaryViewModel.RumenHealthUrinePHTitle=Urine ph
EmailReportViewModel.RumenHealthUrinePHTitle=Urine ph
FreeFormReportViewModel.RumenHealthUrinePHTitle=Urine ph
UrinePHResultsViewModel.MinpH=Min ph
UrinePHResultsViewModel.MaxpH=Max ph
UrinePH=Urine ph
EmailReportViewModel.DietDCADStr=Diet dcad
UrinePHAverageNumber=Average number
MEQ100G=mEq/100g
CowsOutsideTargetRangeToolTip=Goal is to have &lt;20% of cows outside the target range.
CowsSectionToolTip=A group of 8 or more cows should be tested to draw conclusions. In small herds should test all pre-fresh cows.
UrinePHEditCowViewModel.CowName=Cow name
UrinePHEditCowViewModel.UrinePHEnterCowValue=Enter cow value
UrinePHEditCowViewModel.CowValue=Cow value
UrinePHEditCowViewModel.AddNew=Next cow
UrinePHEditCowViewModel.ValidCudInput=Please enter a valid input.
EmailReportViewModel.CowsOutsideTargetRange=Cows outside range (%)
EmailReportViewModel.UrinePhSTDDEV=Std. deviation
EmailReportViewModel.CoefficientVariation=c.v. (%)
VisitNotesViewModel.DownloadingNotes=Downloading notes...
EmailReportViewModel.GeneratingReport=Generating report...
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Generating report...
VisitViewModel.CalfHeiferItem=Calf and heifer
CalfHeiferToolsViewModel.CalfHeiferScorecard=Scorecard
CalfHeiferToolsViewModel.CalfHeiferToolsList=Tools
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Please select a tool from the list below to begin your visit
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Tools
CalfHeiferToolsViewModel.Title=Calf and heifer tools
CalfHeiferToolsViewModel.VisitNotebook=Visit notebook
CalfHeiferToolsViewModel.CalfHeiferTools=Calf and heifer tools
CalfHeiferScorecardViewModel.VisitNotebook=Visit notebook
CalfHeiferScorecardViewModel.Title=Scorecard
CalfHeiferScorecardViewModel.Colostrum=Colostrum
CalfHeiferScorecardViewModel.Preweaned=Pre-weaned
CalfHeiferScorecardViewModel.Postweaned=Post-weaned
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
CalfHeiferScorecardViewModel.KeyBenchmarks=Key benchmarks
CalfHeiferScorecardViewModel.Resources=Resources
CalfHeiferQuestionViewModel.VisitNotebook=Visit notebook
CalfHeiferQuestionViewModel.Close=Close
CalfHeiferQuestionViewModel.Colostrum=Colostrum
CalfHeiferQuestionViewModel.Preweaned=Pre-weaned
CalfHeiferQuestionViewModel.Postweaned=Post-weaned
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
CalfHeiferQuestionViewModel.KeyBenchmarks=Key benchmarks
CalfHeiferQuestionViewModel.Resources=Resources
One=One
Colostrum_CleanAndDryCalvingArea=Clean and dry calving area
Colostrum_RefrigeratedColostrumStoredLess=Refrigerated colostrum stored less than 24 hours
Colostrum_NumberOfCowsInCalvingArea=Number of cows in calving area
Colostrum_PasteurizeColostrumBeforeFeeding=Pasteurized colostrum is fed
Colostrum_PercentageOfNavelsDippedInSevenPercent=% of navels dipped in 7% iodine in 1 hour
Colostrum_BrixPercentOfColostrumFed=Brix % of colostrum fed
Colostrum_AmountOfColostrumOrFed=Amount of colostrum or fed
Colostrum_HoursTillCalfReceivesColostrum=Hours till calf receives colostrum
Colostrum_HoursTillCalfIsRemovedFromMother=Hours till calf is removed from mother
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Clean and sanitize calf feeding equipment between feedings
Colostrum_CleanCalfCartToTransportCalf=Clean calf cart to transport calf
Preweaned_CleanAndDryPen=Clean and dry pen
Preweaned_SizeOfPenAadequatePerHeifer=Size of pen is adequate per heifer
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Well ventilated pen with no draft on calf
Preweaned_ForageAvailability=Forage availability
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Properly clean and sanitize calf feeding equipment between feedings
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=Cmr is properly mixed and adequately fed
Preweaned_ConsistentFeedingTimesAndProtocols=Consistent feeding times and protocols
Preweaned_FreeChoiceCleanWaterIsAvailable=Free choice, clean water is available
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Free choice, fresh calf starter is available
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Weaning at intake of 1kg starter per day
Preweaned_EvidenceOfScoursOrPneumonia=Evidence of scours or pneumonia
Postweaned_CleanAndDryPen=Clean and dry pen
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Well ventilated pen with no draft on calf
Postweaned_SizeOfPenAdequate=Size of pen is adequate per heifer
Postweaned_SizeOfBunkSpace=Size of bunk space adequate per calf
Postweaned_FreshQualityStarterAvailable=Fresh, quality starter/grower is available
Postweaned_FeedBunkIsClaanedDaily=Feed bunk is cleaned daily, refusals removed
Postweaned_ForageAvailability=Forage availability
Postweaned_FreeChoiceCleanWaterIsAvailable=Free choice, clean water is available
Postweaned_EvidenceOfAcidosisInManure=Evidence of acidosis in manure
Postweaned_EvidenceOfScoursOrPneumonia=Evidence of scours or pneumonia
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Clean and dry pen
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Size of pen is adequate per heifer
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% of overcrowding
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Size of bunk space is adequate per heifer
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Groups with uniform heifer size
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Evidence of loose manure
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Rations balanced for growth targets, reviewed often
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=Feed bunk is cleaned daily, refusals removed
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Free choice, clean water is available
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=Desired bcs is achieved for stage of maturity
KeyBenchmarks_SerumlgG=Serum igg (g/l) at 48 hours
KeyBenchmarks_NintyDaysMorbidityf=90 days morbidity
KeyBenchmarks_NintyDaysMortality=90 days mortality
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=15% of mature body weight at 90 days
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=55% of mature body weight at pregnancy
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=94% of mature body weight before calving
KeyBenchmarks_PercentOfHeifersPregnant=Percent of heifers pregnant at 15 months
KeyBenchmarks_AgeInMonthAtFirstCalving=Age in months at first calving
KeyBenchmarks_HeiferPeakProduce=Heifer peak milk as a % of herd average
KeyBenchmarks_CalvingAndHeiferReocrd=Calving and heifer record keeping system used
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Score
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Key benchmarks
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Improvements
Colostrum=Colostrum
Preweaned=Pre-weaned
Postweaned=Post-weaned
GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
KeyBenchmarks=Key benchmarks
CalfHeiferScorecardResultsViewModel.VisitNotebook=Visit notebook
ViewOverallCalfHaiferScore=View overall calf and heifer score
OverallCalfHeiferDetails=To view the overall calf and heifer score, first complete at least one of the surveys in the list above.
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Include in overall scorecard score
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Calf and heifer score
VisitSummaryViewModel.CalfHeiferItem=Calf and heifer
VisitSummaryViewModel.CalfHeiferScorecard=Scorecard
EmailReportViewModel.CalfHeiferItem=Calf and heifer
EmailReportViewModel.CalfHeiferScorecard=Scorecard
FreeFormReportViewModel.CalfHeiferItem=Calf and heifer
FreeFormReportViewModel.CalfHeiferScorecard=Scorecard
FreeFormReportViewModel.KeyBenchmarks=Key benchmarks
Colostrum_CleanAndDryCalvingArea_ToolTip=Use wet knee test to determine if area is clean and dry
PreWeaned_CleanAndDryPen_ToolTip=Use wet knee test to determine if area is clean and dry
PreWeaned_SizeOfPen_ToolTip=Individual pen: 32ft2 / 3m2, group pen: 25ft2 / 2.5m2
PreWeaned_WellVenilated_ToolTip=If clothes smell of ammonia after leaving barn, it is too high
PreWeaned_Forageavailability_ToolTip=If texture feed, no forage is needed
PreWeaned_CMRisProperlyMixed_ToolTip=Amount of cmr: &gt;600g &lt;800g, temp: 39-41c, solids 12-18%
PreWeaned_FreeChoiceCleanWater_ToolTip=Available form first day, no evidence of contamination in water
PreWeaned_FreeChoiceFreshCalf_ToolTip=The starter has no fines, no mold, and is not wet
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% of calves get scours or pneumonia
Postweaned_CleanAndDryPen_ToolTip=Use wet knee test to determine if area is clean and dry
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=If clothes smell of ammonia after leaving barn, it is too high
Postweaned_SizeOfPenAdequate_ToolTip=Individual pen: 32ft2 / 3m2, group pen: 28ft2 / 2.75m2
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45cm per calf
Postweaned_FreshQualityStarterAvailable_ToolTip=The starter/grower has no fines, no mold, and is not wet
Postweaned_ForageAvailability_ToolTip=Stems are greater than 5cm
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=No evidence of contamination in water
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Are there bubbles in loose manure
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% of calves get scours or pneumonia
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Use wet knee test to determine if area is clean and dry
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg need 40ft2 / 4m2, 270-400kg need 50ft2 / 5m2, >400 need 70ft2 / 7m2
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg need 30cm, 270-400kg need 38cm, >400 need 46cm
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Animals in group should be same size
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=No evidence of contamination in water
Benchmarks_Serum_ToolTip=
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Phase 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Phase 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Phase 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Phase 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Phase 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Phase 7
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Records
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Key benchmarks
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Colostrum 1 - 3 days
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Pre/post weaning 0 - 3 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Grower 3 - 9 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Puberty 9 - 15 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Pregnancy 15 - 23 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Close up / production 23 - 26 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Maintain accurate growth and health records
CalfHeiferTools=Calf &amp; heifer tools
CalfHeiferScorecardLanding=Calf &amp; heifer scorecard
AmountOfColostrumOrFed=> 3lak
CalfHeiferColostrum=Calf &amp; heifer scorecard - colostrum
CalfHeiferPreweaned=Calf &amp; heifer scorecard - pre-weaned
CalfHeiferPostweaned=Calf &amp; heifer scorecard - post-weaned
CalfHeiferGrowerPuberty=Calf &amp; heifer scorecard - grower, puberty, pregnancy, Close-up
CalfHeiferKeyBenchmarks=Calf &amp; heifer scorecard - key benchmarks
CalfHeiferResources=Calf &amp; heifer scorecard - resources
CalfHeiferResults=Calf &amp; heifer scorecard - results
SelectImprovement=Please select only 10 improvement from list.
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Visit notebook
CalfHeiferScoreCardScoreViewModel.PhaseOne=Colostrum
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Pre-weaned
CalfHeiferScoreCardScoreViewModel.PhaseThree=Post-weaned
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
DoNotTest=&lt;20% or do not test
TextureFeed=Textured feed
PasteurizedWholeMilk=Pasteurized whole milk fed
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Responses
Optimal=Optimal answer
CalfHeiferScorecardImprovementViewModel.Colostrum=Colostrum
CalfHeiferScorecardImprovementViewModel.Preweaned=Pre-weaned
CalfHeiferScorecardImprovementViewModel.Postweaned=Post-weaned
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
PhaseOne=Colostrum
PhaseTwoThree=Pre/post weaning
PhaseFour=Grower
PhaseFive=Puberty
PhaseSix=Pregnancy
PhaseSeven=Close up / production
KBLastPhase=Maintain accurate growth and health records
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Tap on below categories to see phase-wise result
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=The coloring of sections is decided by the range: equals 100%: green, &lt;100%: red
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=The coloring of sections is decided by the range: &lt;75%: red, &gt;
LoginViewModel.LoginPromptConsumer=Other login
RefreshTokenFailed=Session expired, you are required to re-login.
LoginViewModel.UnauthorizedTitle=Unauthorized
LoginViewModel.Unauthorized=Unauthorized access.
PromptPermissionMsgRetry=Re-Try
PromptPermissionMsgIMSure=I&apos;m sure
PromptPermissionMsg=Without this permission some feature may not work. Are you sure you want to deny this permission?
PromptPermissionTitle=Permission denied
NewProspectViewModel.FarmProducer=Farm producer
UserPreferencesViewModel.ProvimiUS=Provimi us
EmailReportViewModel.ProvimiUS=Provimi us
FreeFormReportViewModel.ProvimiUS=Provimi us
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi us
PDFDisclaimer_ProvimiUS=Provimi north america, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. \u00a9{0} provimi north america. All rights reserved
StatusDeleted=Deleted
VersionUpdatePopUp=This version of application is not compatible with new version. Please update this application.
UserPreferencesViewModel.SelectPointScale=Select body condition score scale
ForageScorecardViewModel.ForageCategoryTooltip=Forage quality is the foundation of any dairy nutrition program and is a key to the overall profitability of the farm. This tools can be used to evaluate the current forage management practices used on the farm and recommend the key opportunity areas for improvement
LessThanFifteen=&lt;15 (kernal hardness)
BetweenFifteenTwenty=Between 15 and 20
GreaterThanTwenty=&gt;20
Average=Average
Weekly=Weekly
BiWeekly=Bi-Weekly
Monthly=Monthly
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Forage silage types
CornSilage=Corn silage
OtherSilage=Other silage
FeedOut=Feed out
ForageScorecardViewModel.ForageAuditCategories=Forage audit categories
ForageAudit_Sample_ToolTip=Sample text for tool tip. Remove when actual available
EmailReportViewModel.ForageImprovements=Forage audit scorecard
ForageScorecardViewModel.ForageAuditScore=Forage audit score
NotMeasured=Not measured
RemovedAndMeasured=Removed and measured
RemovedOnly=Removed only
NotRemoved=Not removed
SelectForageImprovement=Please select only 12 improvement from list.
ForageManagement_ForagesHarvestedAtProperMoisture=Are forages harvested at proper moisture for crop type and storage facility?
ForageManagement_ForagesHarvestedAtProperMaturity=Are forages harvested at proper maturity for crop type and storage facility?
OptimizationSelectionViewModel.SelectDietOptimizationType=Select maxdiet optimization type
Analyze=Analyze
Formulate=Formulate
OptimizationSelectionViewModel.Title=Diet optimization
NewPenViewModel.NetEnergyOfLactationDairy=Nel dairy (mcal/{0})
PenListViewModel.DietNotMappedInfo=Pens does not associate with any maxdiet, please map the maxdiet and proceed
DietNotMapped=Diet not mapped
PileAndBunkerResultsCapacityInputViewModel.SlopeMessagePile=Slope {0} to 1.0 (goal > 3.5 To 1.0)
PileAndBunkerResultsCapacityInputViewModel.SlopeMessage=Slope {0} to 1.0
Export_Logs=Export logs
Send_Error_Report=Send account info
MenuViewModel.UploadingBlob=Capturing data... Please wait.
LogoutConnectivityCheck=Error sending logs. Please check your internet connectivity.
BlobSuccess=Device logs uploaded successfully.
BlobFailed=Device logs upload failed.
ProductivityToolsViewModel.RoboticMilkingEvaluation=Robotic milking evaluation
RoboticMilkEvaluationMasterViewModel.Title=Robotic milking evaluation
RoboticMilkEvaluationMasterViewModel.Inputs=Inputs
RoboticMilkEvaluationMasterViewModel.Outputs=Outputs
RoboticMilkEvaluationMasterViewModel.Analysis=Analysis
RoboticMilkEvaluationMasterViewModel.Trends=Trends
RoboticMilkEvaluationInputsViewModel.Herd=Herd level information
RoboticMilkEvaluationInputsViewModel.RobotType=Type of robot \u20F0
RoboticMilkEvaluationInputsViewModel.CowFlowDesign=Cow flow design \u20F0
RoboticMilkEvaluationInputsViewModel.RobotsInHerd=Robots in herd \u20F0
RoboticMilkEvaluationInputsViewModel.LactatingCows=Lactating cows \u20F0
RoboticMilkEvaluationInputsViewModel.AverageMilkYield=Average milk yield ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.AverageBoxTime=Average box time (min/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.Milkings=Milkings (/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.RobotFreeTime=Robot free time % \u20F0
RoboticMilkEvaluationInputsViewModel.MilkingSpeed=Milking speed ({0}/min) \u20F0
RoboticMilkEvaluationInputsViewModel.MilkingRefusals=Milking refusals (/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.TotalMilkingFailures=Total milking failures (/herd) \u20F0
RoboticMilkEvaluationInputsViewModel.MaximumConcentrate=Maximum concentrate ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.AverageConcentrateFed=Average concentrate fed ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.MinimumConcentrate=Minimum concentrate ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.ConcentratePer100KGMilk=Concentrate per 100 {0} milk ({0}/100 {0} milk)
RoboticMilkEvaluationInputsViewModel.RestFeed=Rest feed % \u20F0
RoboticMilkEvaluationSpinnerViewModel.Title=Robotic milking evaluation
RoboticMilkEvaluationOutputsViewModel.AMSUtilization=Ams utilization
RoboticMilkEvaluationOutputsViewModel.CowsPerRobot=Cows per robot
RoboticMilkEvaluationOutputsViewModel.MilkingsPerRobot=Milkings per robot
RoboticMilkEvaluationOutputsViewModel.RobotFreeTime=Robot free time %
RoboticMilkEvaluationOutputsViewModel.MilkPerRobot=Milk per robot ({0})
RoboticMilkEvaluationOutputsViewModel.CowEfficiency=Cow efficiency
RoboticMilkEvaluationOutputsViewModel.Milkings=Milkings (/cow)
RoboticMilkEvaluationOutputsViewModel.MilkingRefusals=Milking refusals (/cow)
RoboticMilkEvaluationOutputsViewModel.MilkingFailures=Milking failures (/robot)
RoboticMilkEvaluationOutputsViewModel.MilkingSpeed=Milking speed ({0}/min)
RoboticMilkEvaluationOutputsViewModel.AverageBoxTime=Average box time (min/cow)
RoboticMilkEvaluationOutputsViewModel.ConcentrateDistribution=Concentrate distribution
RoboticMilkEvaluationOutputsViewModel.MaximumConcentrate=Maximum concentrate ({0}/cow)
RoboticMilkEvaluationOutputsViewModel.AverageConcentrate=Average concentrate ({0}/cow)
RoboticMilkEvaluationOutputsViewModel.MinimumConcentrate=Minimum concentrate ({0}/cow)
RoboticMilkEvaluationOutputsViewModel.ConcentratePer100KGMilk=Concentrate per 100 {0} milk ({0}/100 {0} milk)
RoboticMilkEvaluationAnalysisViewModel.RoboticMilkingEvaluation=Robotic milking evaluation
RoboticMilkEvaluationAnalysisViewModel.CowsPerRobot=Cows per robot
RoboticMilkEvaluationAnalysisViewModel.RobotFreeTime=Robot free time %
RoboticMilkEvaluationAnalysisViewModel.Milkings=Milkings (/cow)
RoboticMilkEvaluationAnalysisViewModel.MilkingRefusals=Milking refusals (/cow)
RoboticMilkEvaluationAnalysisViewModel.MilkingFailures=Milking failures (/robot)
RoboticMilkEvaluationAnalysisViewModel.AverageBoxTime=Average box time (min/cow)
RoboticMilkEvaluationAnalysisViewModel.AverageConcentrate=Average concentrate
RoboticMilkEvaluationTrendsListViewModel.AMSUtilization=Ams utilization
RoboticMilkEvaluationTrendsListViewModel.CowEfficiency=Cow efficiency
RoboticMilkEvaluationTrendsListViewModel.ConcentrateDistribution=Concentrate distribution
RoboticMilkEvaluationTrendsListViewModel.VisitComparison=Please select visits for comparison
RoboticMilkEvaluationChartsViewModel.Title=Robotic milking evaluation
RoboticMilkEvaluationChartsViewModel.AMSUtilization=Robot free time and average box time per cow
RoboticMilkEvaluationChartsViewModel.CowEfficiency=Milkings per cow and milking refusals per cow
RoboticMilkEvaluationChartsViewModel.ConcentrateDistribution=Average concentrate and concentrate per 100 kg milk
RoboticMilkEvaluationChartsViewModel.CowsPerRobot=Cows per robot
RoboticMilkEvaluationChartsViewModel.RobotFreeTime=Robot free time %
RoboticMilkEvaluationChartsViewModel.AverageBoxTime=Average box time (min/cow)
RoboticMilkEvaluationChartsViewModel.Milkings=Milkings (/cow)
RoboticMilkEvaluationChartsViewModel.MilkingRefusals=Milking refusals (/cow)
RoboticMilkEvaluationChartsViewModel.MilkingFailures=Milking failures
RoboticMilkEvaluationChartsViewModel.AverageConcentrate=Average concentrate
NotebookSectionRoboticMilkEvaluationInputs=Robotic milking evaluation inputs
NotebookSectionRoboticMilkEvaluationOutputs=Robotic milking evaluation outputs
NotebookSectionRoboticMilkEvaluationAnalysis=Robotic milking evaluation analysis
NotebookSectionRoboticMilkEvaluationTrends=Robotic milking evaluation trends
NotebookSectionRoboticMilkEvaluationCharts=Robotic milking evaluation charts
FreeFormReportViewModel.RoboticMilkingEvaluation=Robotic milking evaluation
FreeFormReportViewModel.Analysis=Analysis
FreeFormReportViewModel.Trends=Trends
Lely=Lely
DeLaval=DeLaval
GEA=GEA
Other=Other
FreeFlow=Free flow
FeedFirst=Feed first
MilkingFirst=Milking first
AverageConcentrate=Avg conc
RoboticMilkEvaluationInputsViewModel.LelyAverageMilkYield=Milk/cow/day ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyAverageBoxTime=Box time (min/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyMilkings=Milkings/cow/day (/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyRobotFreeTime=Free time % \u20F0
RoboticMilkEvaluationInputsViewModel.LelyMilkingSpeed=Milk speed ({0}/min) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyMilkingRefusals=Refusals (/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyTotalMilkingFailures=Failure (total/day) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyAverageConcentrateFed=Average concentrate fed ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} milk- ({0}/100 {0} milk) \u20F0
RoboticMilkEvaluationInputsViewModel.DLAverageMilkYield=Avg. milk yield ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.DLAverageBoxTime=Avg. milk duration per milking (min/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.DLMilkings=Avg. daily milkings per animal (/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.DLRobotFreeTime=Idle time % \u20F0
RoboticMilkEvaluationInputsViewModel.DLMilkingSpeed=Av. daily harvesting flow per animal ({0}/min) \u20F0
RoboticMilkEvaluationInputsViewModel.DLTotalMilkingFailures=Nb incomplete (/herd) \u20F0
RoboticMilkEvaluationInputsViewModel.DLAverageConcentrateFed=Average concentrate consumed ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.DLConcentratePer100KGMilk=Feed/milk ratio- ({0}/100 {0} milk) \u20F0
RoboticMilkEvaluationInputsViewModel.DLRestFeed=Concentrate not consumed % \u20F0
RoboticMilkEvaluationInputsViewModel.GAverageMilkYield=Avg. milk production ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.GAverageBoxTime=Avg. staying time (min/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.GMilkings=Avg. nr milkings (/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.GMilkingSpeed=Avg. milk speed ({0}/min) \u20F0
RoboticMilkEvaluationInputsViewModel.GTotalMilkingFailures=Number incomplete milkings (/herd) \u20F0
RoboticMilkEvaluationInputsViewModel.GAverageConcentrateFed=Concentrate fed ({0}/cow) \u20F0
RoboticMilkEvaluationInputsViewModel.GConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk) \u20F0
RoboticMilkEvaluationInputsViewModel.GRestFeed=Rest feed (%) \u20F0
RoboticMilkEvaluationOutputsViewModel.RestFeed=Rest feed
RoboticMilkEvaluationOutputsViewModel.DLRestFeed=Concentrate not cons %
RoboticMilkEvaluationOutputsViewModel.LelyAverageBoxTime=Box time (min/cow)
RoboticMilkEvaluationOutputsViewModel.DLAverageBoxTime=Avg. milk duration per milking (min/cow)
RoboticMilkEvaluationOutputsViewModel.GAverageBoxTime=Avg. staying time (min/cow)
RoboticMilkEvaluationOutputsViewModel.LelyMilkingSpeed=Milk speed ({0}/min)
RoboticMilkEvaluationOutputsViewModel.DLMilkingSpeed=Av. daily harvesting flow per animal ({0}/min)
RoboticMilkEvaluationOutputsViewModel.GMilkingSpeed=Avg. milk speed ({0}/min)
RoboticMilkEvaluationOutputsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} milk- ({0}/100 {0} milk)
RoboticMilkEvaluationOutputsViewModel.DLConcentratePer100KGMilk=Feed/milk ratio - ({0}/100 {0} milk)
RoboticMilkEvaluationOutputsViewModel.GConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk)
RoboticMilkEvaluationInputsViewModel.RobotFreeTimeMsg=Utilization time: 100 - system utilization%
RoboticMilkEvaluationInputsViewModel.MilkingRefusalsDLMsg=Static system; total refusal/ cow number
RoboticMilkEvaluationInputsViewModel.MilkingRefusalsGEMsg=Calculate refusals: average nr of visit/cow - average nr milkings/cow??
RoboticMilkEvaluationInputsViewModel.TotalMilkingFailureMsg=Total milkings x % incomplete
RoboticMilkEvaluationInputsViewModel.ConcentratePer100KGMilkMsg=System shows 1 kg/ milk so x 100
RoboticMilkEvaluationInputsViewModel.RestFeedDLMsg=Rest feed: 100 - % cons. yest.
RoboticMilkEvaluationInputsViewModel.RestFeedMsg=Calculate rest feed (kg) / total fed
LoginViewModel.TitleMsg=Logging in... Please wait...
RoboticMilkEvaluationChartsViewModel.ConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.LelyRobotFreeTime=Free time %
RoboticMilkEvaluationChartsViewModel.DLRobotFreeTime=Idle time %
RoboticMilkEvaluationChartsViewModel.LelyAverageBoxTime=Box time (min/cow)
RoboticMilkEvaluationChartsViewModel.DLAverageBoxTime=Avg. milk duration per milking (min/cow)
RoboticMilkEvaluationChartsViewModel.GAverageBoxTime=Avg. staying time (min/cow)
RoboticMilkEvaluationChartsViewModel.LelyMilkings=Milkings/cow/day (/cow)
RoboticMilkEvaluationChartsViewModel.DLMilkings=Avg. daily milkings per animal (/cow)
RoboticMilkEvaluationChartsViewModel.GMilkings=Avg. nr milkings (/cow)
RoboticMilkEvaluationChartsViewModel.LelyMilkingRefusals=Refusals (/cow)
RoboticMilkEvaluationChartsViewModel.DLTotalMilkingFailures=Nb incomplete (/herd)
RoboticMilkEvaluationChartsViewModel.GTotalMilkingFailures=Number incomplete milkings (/herd)
RoboticMilkEvaluationChartsViewModel.AverageConcentrateFed=Average concentrate ({0}/cow)
RoboticMilkEvaluationChartsViewModel.LelyAverageConcentrateFed=Average concentrate fed ({0}/cow)
RoboticMilkEvaluationChartsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} milk- ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.DLAverageConcentrateFed=Average concentrate consumed ({0}/cow)
RoboticMilkEvaluationChartsViewModel.DLConcentratePer100KGMilk=Feed/milk ratio- ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.GAverageConcentrateFed=Concentrate fed ({0}/cow)
RoboticMilkEvaluationChartsViewModel.GConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk)
Global=Global
France=France
CPNFrance=France
Netherlands=Netherlands
Spain=Spain
Italy=Italy
Korea=Korea
CPNBrazil=Brazil
NorthAmerica=North america
Portugal=Portugal
CFNChina=China
CFNIndia=India
CPNPoland=Poland
CPNUS=United states of america
Report.SheetName=Report master
Report.BCS.EvalDataTitle=Calculated bcs evaluation data
Report.EvalDataTitle=Calculated evaluation data
Report.Bcs.Min=Bcs min
Report.BCS.Max=Bcs max
Report.BCS.MilkHeadDay=Milk/hd/day
Report.BCSAvg=Bcs avg
Report.BCS.LactationStages=Lactation stages
Report.Visit.name=Visit name
Report.Visit.Date=Visit date
Report.Tool.Name=Tool name
Report.Analysis.Type=Analysis type
Report.Bcs.HerdAnalysis.ChartName=BCS vs milk
Report.Bcs=BCS
Report.Bcs.Milk=Milk
Report.CudChewing.EvalDataTitle=Calculated cud chewing evaluation data
Report.CudChewingPercentage=Cud chewing %
Report.No.OfChews=No. of chews
Report.No.OfChewsPerRegurgitation=No. of chews per regurgitation
Report.goalChews=Goal chews
Report.Chews=Chews
Report.GoalCudChewingPercentage=Goal cud chewing %
Report.CudChewingPercentage.Vs.LactStages=Cud chewing %
Report.NoOfChews.Vs.LactStages=No. of chews
Report.Herd.Analysis.CudChewingPercentage=Cud chewing %
Report.locomotionScore.Pen.Analysis.ChartName=Categories vs visit dates
Report.Bcs.ChartName=Body condition score - animal {0}
Report.Animal.Tag.Name=Animal id
Report.Calving.Date=Calving
Report.LocomotionScore.chartName=Locomotion score - animal {0}
Report.Locomotion.HerdAnalysis.ChartName=Locomotion score percentages
Report.LocomotionScore.X.Axis=Locomotion score
Report.LocomotionScore.Y.Axis=Percent %
Report.Cargill.Report=Cargill - report
Report.Visit.Report=Visit report
Report.Tool.Details=Tool details
Visit.Report.Footer.Patent=Cargill incorporated, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. ©2023 cargill, incorporated. All rights reserved.
Lactation=Lactation
FreshCow=Fresh cow
DryCow=Dry cow
TransitionCow=Transition cow
Report.AvgRumenFillScore=Avg rumen fill score
Report.ForagePennState=Forage penn state
Report.PercentageOnScreen=On screen (%)
USD=United States of America ($ USD)
Euro=Euro Member Countries (\u20AC EUR)
GBP=United Kingdom (GBP GBP)
CAD=Canada (CA$ CAD)
DZD=Algeria (DA DZD)
ARS=Argentina ($ ARS)
AUD=Australia ($ AUD)
CNY=China (CNY CNY)
CZK=Czech Republic (CZK CZK)
GTQ=Guatemala (Q GTQ)
HNL=Honduras (HNL HNL)
HUF=Hungary (Ft HUF)
BRL=Brazil (R$ BRL)
INR=India (INR INR)
IDR=Indonesia (Rp IDR)
MYR=Malaysia (MYR MYR)
MXN=Mexico (PESO MXN)
NIO=Nicaragua (NIO NIO)
PEN=Peru (S/. PEN)
PHP=Philippines ($ PHP)
PLN=Poland (z\u0142 PLN)
PON=Romania (lei PON)
RUB=Russia (\u20BD\u200E RUB)
SAR=Saudi Arabia (\uFDFC SAR)
ZAR=South Africa (ZAR ZAR)
KRW=South Korea (\u20A9 KRW)
SRD=Surinam ($ SRD)
CHF=Switzerland (CHF CHF)
TWD=Taiwan (NT$ TWD)
THB=Thailand (THB THB)
UAH=Ukraine (UAH UAH)
VEF=Venezuela (Bs VEF)
VND=Vietnam (\u20AB VND)
CLP=Chile ($ CLP)
Cargill=Cargill
Purina=Purina
Provimi=Provimi
ProvimiUS=Provimi us
Imperial=Imperial
Metric=Metric
Screen=Screen
ScreenOld=Screen old
ScreenNew=Screen new
Straw=Straw
Dryhay=Dry hay
Haylage=Haylage/Grass
Corn=Corn
Report.PenTimeBudgetTimeRemaining=Time remaining
Report.PenTimeBudgetTimeRequired=Time required
Report.PenTimeBudget.TimeAvailableForResting.Label=Hours
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Time available for resting
Report.Heatstress.Temperature.In.Celcius=Temperature \u2103
Report.Heatstress.Temperature.In.Farenhiet=Temperature \u2109
Report.Heatstress.Intake.Adjustment=Intake adjustment
Report.Heatstress.Estimated.Dry.Matter.Intake=Estimated dry matter intake ({0})
Report.Heatstress.Loss.Of.Energy.Consumed=Loss of energy consumed (mcal)
Report.Heatstress.Milk.Value.Loss.Perday=Milk value loss (per day) ({0})
Report.Heatstress.Dmi.Adjustment=Dmi adjustment
Report.Heatstress.Reduction.In.Dmi=Reduction in dmi ({0})
Report.Heatstress.Energy.Equivalent.Milk.Loss=Energy equivalent milk loss ({0})
Report.Heatstress.Milk.Value.Loss.PerMonth=Milk value loss (per month) ({0})
Report.Pentime.Budget.Hours=Hours
Report.Heatstress.TemperatureHumidityIndex=Temperature humidity index
Report.Heatstress.Legend=Legend
Report.Heatstress.Legends=Legends
Report.Heatstress.Stress.Threshold=Stress threshold
Report.Heatstress.Mild.Moderate.Stress=Mild - moderate stress
Report.Heatstress.Moderate.Severe.Stress=Moderate - severe stress
Report.Heatstress.Severe.Stress=Severe stress
Report.Heatstress.Mild.Moderate.Stress.Message=Respiration exceeds 75 bpm | rectal temperature exceeds 39\u2103 (102.2\u2109)
Report.Heatstress.Stress.Threshold.Message=Respiration exceeds 60 bpm | repro losses detectable | rectal temperature exceeds 38.5\u2103 (101.3\u2109)
Report.Heatstress.Moderate.Severe.Stress.Message=Respiration exceeds 85 bpm | rectal temperature exceeds 40\u2103 (104\u2109)
Report.Heatstress.Severe.Stress.Message=Respiration exceeds 120-140 bpm | rectal temperature exceeds 41\u2103 (106\u2109)
Report.RumenHealthManureScreening.Top=Top
Report.RumenHealthManureScreening.Middle=Middle
Report.RumenHealthManureScreening.Bottom=Bottom
Report.RumenHealthManureScreening.TopGoalMin=Top goal min
Report.RumenHealthManureScreening.TopGoalMax=Top goal max
Report.RumenHealthManureScreening.MiddleGoalMin=Middle goal min
Report.RumenHealthManureScreening.MiddleGoalMax=Middle goal max
Report.RumenHealthManureScreening.BottomGoalMin=Bottom goal min
Report.RumenHealthManureScreening.BottomGoalMax=Bottom goal max
Report.Heatstress.Temperature=Temperature
Report.Heatstress.Relative.Humidity=Relative humidity (%)
Thirdparty=Thirdparty
Consumer=Consumer
Competitor=Competitor
Report.Chewing=Chewing
Report.Not.Chewing=Not chewing
Report.Animal.Analysis=Animal analysis
Report.General.Comments=General comments
Bunker=Bunker
Bag=Bag
NotSet= - 
AMSUtilization=Ams utilization
CowEfficiency=Cow efficiency
ConcentrateDistribution=Concentrate distribution
AMSUtilizationChart=Ams utilization chart
CowPerRobot=Cows per robot
MilkingFailure=Milking failures
SelectVisitComparison=Select visits for comparison


Andorra=Andorra
United_Arab_Emirates=United Arab Emirates
Afghanistan=Afghanistan
Antigua_and_Barbuda=Antigua and Barbuda
Anguilla=Anguilla
Albania=Albania
Armenia=Armenia
Angola=Angola
Antarctica=Antarctica
Argentina=Argentina
Austria=Austria
Australia=Australia
Aruba=Aruba
Aland_Islands=Aland Islands
Azerbaijan=Azerbaijan
Bosnia_and_Herzegovina=Bosnia and Herzegovina
Barbados=Barbados
Bangladesh=Bangladesh
Belgium=Belgium
Burkina_Faso=Burkina Faso
Bulgaria=Bulgaria
Bahrain=Bahrain
Burundi=Burundi
Benin=Benin
Saint_Barthélemy=Saint Barthélemy
Bermuda=Bermuda
Brunei_Darussalam=Brunei Darussalam
Bolivia,_Plurinational_State_of=Bolivia, Plurinational State of
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius and Saba
Bahamas=Bahamas
Bhutan=Bhutan
Bouvet_Island=Bouvet Island
Botswana=Botswana
Belarus=Belarus
Belize=Belize
Cocos_(Keeling)_Islands=Cocos (Keeling) Islands
Congo,_the_Democratic_Republic_of_the=Congo, the Democratic Republic of the
Central_African_Republic=Central African Republic
Congo=Congo
Cote_d'Ivoire=Cote d'Ivoire
Cook_Islands=Cook Islands
Cameroon=Cameroon
Costa_Rica=Costa Rica
Cuba=Cuba
Cape_Verde=Cape Verde
Curaçao=Curaçao
Christmas_Island=Christmas Island
Cyprus=Cyprus
Czech_Republic=Czech Republic
Germany=Germany
Djibouti=Djibouti
Denmark=Denmark
Dominica=Dominica
Dominican_Republic=Dominican Republic
Ecuador=Ecuador
Estonia=Estonia
Egypt=Egypt
Western_Sahara=Western Sahara
Eritrea=Eritrea
Ethiopia=Ethiopia
Finland=Finland
Fiji=Fiji
Falkland_Islands_(Malvinas)=Falkland Islands (Malvinas)
Faroe_Islands=Faroe Islands
Gabon=Gabon
United_Kingdom=United Kingdom
Grenada=Grenada
Georgia=Georgia
French_Guiana=French Guiana
Guernsey=Guernsey
Ghana=Ghana
Gibraltar=Gibraltar
Greenland=Greenland
Gambia=Gambia
Guinea=Guinea
Guadeloupe=Guadeloupe
Equatorial_Guinea=Equatorial Guinea
Greece=Greece
South_Georgia_and_the_South_Sandwich_Islands=South Georgia and the South Sandwich Islands
Guam=Guam
Guinea-Bissau=Guinea-Bissau
Guyana=Guyana
Heard_Island_and_McDonald_Islands=Heard Island and McDonald Islands
Croatia=Croatia
Haiti=Haiti
Ireland=Ireland
Israel=Israel
Isle_of_Man=Isle of Man
British_Indian_Ocean_Territory=British Indian Ocean Territory
Iraq=Iraq
Iran,_Islamic_Republic_of=Iran, Islamic Republic of
Iceland=Iceland
Jersey=Jersey
Jamaica=Jamaica
Jordan=Jordan
Japan=Japan
Kenya=Kenya
Kyrgyzstan=Kyrgyzstan
Cambodia=Cambodia
Kiribati=Kiribati
Comoros=Comoros
Saint_Kitts_and_Nevis=Saint Kitts and Nevis
Korea,_Democratic_People's_Republic_of=Korea, Democratic People's Republic of
Korea,_Republic_of=Korea, Republic of
Kuwait=Kuwait
Cayman_Islands=Cayman Islands
Kazakhstan=Kazakhstan
Lao_People's_Democratic_Republic=Lao People's Democratic Republic
Lebanon=Lebanon
Saint_Lucia=Saint Lucia
Liechtenstein=Liechtenstein
Sri_Lanka=Sri Lanka
Liberia=Liberia
Lesotho=Lesotho
Lithuania=Lithuania
Luxembourg=Luxembourg
Latvia=Latvia
Libyan_Arab_Jamahiriya=Libyan Arab Jamahiriya
Morocco=Morocco
Monaco=Monaco
Moldova,_Republic_of=Moldova, Republic of
Montenegro=Montenegro
Saint_Martin_(French_part)=Saint Martin (French part)
Madagascar=Madagascar
Chad=Chad
Macedonia,_the_former_Yugoslav_Republic_of=Macedonia, the former Yugoslav Republic of
Mali=Mali
Myanmar=Myanmar
Mongolia=Mongolia
Macao=Macao
Martinique=Martinique
Mauritania=Mauritania
Montserrat=Montserrat
Malta=Malta
Mauritius=Mauritius
Maldives=Maldives
Malawi=Malawi
Mozambique=Mozambique
Namibia=Namibia
New_Caledonia=New Caledonia
Niger=Niger
Norfolk_Island=Norfolk Island
Nigeria=Nigeria
Norway=Norway
Nepal=Nepal
Nauru=Nauru
Niue=Niue
New_Zealand=New Zealand
Oman=Oman
Panama=Panama
French_Polynesia=French Polynesia
Papua_New_Guinea=Papua New Guinea
Pakistan=Pakistan
Saint_Pierre_and_Miquelon=Saint Pierre and Miquelon
Pitcairn=Pitcairn
Puerto_Rico=Puerto Rico
Palestinian_Territory,_Occupied=Palestinian Territory, Occupied
Paraguay=Paraguay
Qatar=Qatar
Reunion=Reunion
Serbia=Serbia
Russian_Federation=Russian Federation
Rwanda=Rwanda
Saudi_Arabia=Saudi Arabia
Solomon_Islands=Solomon Islands
Seychelles=Seychelles
Sudan=Sudan
Sweden=Sweden
Singapore=Singapore
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Ascension and Tristan da Cunha
Slovenia=Slovenia
Svalbard_and_Jan_Mayen=Svalbard and Jan Mayen
Slovakia=Slovakia
Sierra_Leone=Sierra Leone
San_Marino=San Marino
Senegal=Senegal
Somalia=Somalia
Suriname=Suriname
South_Sudan=South Sudan
Sao_Tome_and_Principe=Sao Tome and Principe
El_Salvador=El Salvador
Sint_Maarten_(Dutch_part)=Sint Maarten (Dutch part)
Syrian_Arab_Republic=Syrian Arab Republic
Swaziland=Swaziland
Turks_and_Caicos_Islands=Turks and Caicos Islands
French_Southern_Territories=French Southern Territories
Togo=Togo
Thailand=Thailand
Tajikistan=Tajikistan
Tokelau=Tokelau
Timor-Leste=Timor-Leste
Turkmenistan=Turkmenistan
Tunisia=Tunisia
Tonga=Tonga
Turkey=Turkey
Trinidad_and_Tobago=Trinidad and Tobago
Tuvalu=Tuvalu
Chinese_Taipei=Chinese Taipei
Tanzania,_United_Republic_of=Tanzania, United Republic of
Uganda=Uganda
United_States=United States
Uruguay=Uruguay
Uzbekistan=Uzbekistan
Holy_See_(Vatican_City_State)=Holy See (Vatican City State)
Saint_Vincent_and_the_Grenadines=Saint Vincent and the Grenadines
Venezuela,_Bolivarian_Republic_of=Venezuela, Bolivarian Republic of
Virgin_Islands,_British=Virgin Islands, British
Viet_Nam=Viet Nam
Vanuatu=Vanuatu
Wallis_and_Futuna=Wallis and Futuna
Samoa=Samoa
Yemen=Yemen
Mayotte=Mayotte
South_Africa=South Africa
Zambia=Zambia
Zimbabwe=Zimbabwe

Australian_Capital_Territory=Australian Capital Territory
New_South_Wales=New South Wales
Northern_Territory=Northern Territory
Queensland=Queensland
South_Australia=South Australia
Tasmania=Tasmania
Victoria=Victoria
Western_Australia=Western Australia
Bonaire=Bonaire
Acre=Acre
Alagoas=Alagoas
Amazonas=Amazonas
Amapá=Amapá
Bahia=Bahia
Ceará=Ceará
Distrito_Federal=Distrito Federal
Espírito_Santo=Espírito Santo
Goiás=Goiás
Maranhão=Maranhão
Minas_Gerais=Minas Gerais
Mato_Grosso_do_Sul=Mato Grosso do Sul
Mato_Grosso=Mato Grosso
Pará=Pará
Paraíba=Paraíba
Pernambuco=Pernambuco
Piauí=Piauí
Paraná=Paraná
Rio_de_Janeiro=Rio de Janeiro
Rio_Grande_do_Norte=Rio Grande do Norte
RondÃ´nia=RondÃ´nia
Roraima=Roraima
Rio_Grande_do_Sul=Rio Grande do Sul
Santa_Catarina=Santa Catarina
Sergipe=Sergipe
São_Paulo=São Paulo
Tocantins=Tocantins
Alberta=Alberta
British_Columbia=British Columbia
Manitoba=Manitoba
New_Brunswick=New Brunswick
Newfoundland_and_Labrador=Newfoundland and Labrador
Nova_Scotia=Nova Scotia
Northwest_Territories=Northwest Territories
Nunavut=Nunavut
Ontario=Ontario
Prince_Edward_Island=Prince Edward Island
Quebec=Quebec
Saskatchewan=Saskatchewan
Yukon_Territories=Yukon Territories
Beijing=Beijing
Tianjin=Tianjin
Hebei=Hebei
Shanxi=Shanxi
Nei_Mongol=Nei Mongol
Liaoning=Liaoning
Jilin=Jilin
Heilongjiang=Heilongjiang
Shanghai=Shanghai
Jiangsu=Jiangsu
Zhejiang=Zhejiang
Anhui=Anhui
Fujian=Fujian
Jiangxi=Jiangxi
Shandong=Shandong
Henan=Henan
Hubei=Hubei
Hunan=Hunan
Guangdong=Guangdong
Guangxi=Guangxi
Hainan=Hainan
Chongqing=Chongqing
Sichuan=Sichuan
Guizhou=Guizhou
Yunnan=Yunnan
Xizang=Xizang
Shaanxi=Shaanxi
Gansu=Gansu
Qinghai=Qinghai
Ningxia=Ningxia
Xinjiang=Xinjiang
Hong_Kong=Hong Kong
Cundinamarca=Cundinamarca
Bayern=Bayern
Niedersachsen=Niedersachsen
Nordrhein=Nordrhein
Rheinland=Rheinland
Schleswig=Schleswig
Pays=Pays
Clare=Clare
Cavan=Cavan
Cork=Cork
Carlow=Carlow
Dublin=Dublin
Donegal=Donegal
Galway=Galway
Kildare=Kildare
Kilkenny=Kilkenny
Kerry=Kerry
Longford=Longford
Louth=Louth
Limerick=Limerick
Leitrim=Leitrim
Laois=Laois
Meath=Meath
Monaghan=Monaghan
Mayo=Mayo
Offaly=Offaly
Roscommon=Roscommon
Sligo=Sligo
Tipperary=Tipperary
Waterford=Waterford
Westmeath=Westmeath
Wicklow=Wicklow
Wexford=Wexford
Andaman_and_Nicobar_Islands=Andaman and Nicobar Islands
Andhra_Pradesh=Andhra Pradesh
Arunachal_Pradesh=Arunachal Pradesh
Assam=Assam
Bihar=Bihar
Chandigarh=Chandigarh
Chhattisgarh=Chhattisgarh
Daman_and_Diu=Daman and Diu
Delhi=Delhi
Dadra_and_Nagar_Haveli=Dadra and Nagar Haveli
Goa=Goa
Gujarat=Gujarat
Himachal_Pradesh=Himachal Pradesh
Haryana=Haryana
Jharkhand=Jharkhand
Jammu_and_Kashmir=Jammu and Kashmir
Karnataka=Karnataka
Kerala=Kerala
Lakshadweep=Lakshadweep
Maharashtra=Maharashtra
Meghalaya=Meghalaya
Manipur=Manipur
Madhya_Pradesh=Madhya Pradesh
Mizoram=Mizoram
Nagaland=Nagaland
Odisha=Odisha
Punjab=Punjab
Puducherry=Puducherry
Rajasthan=Rajasthan
Sikkim=Sikkim
Tamil_Nadu=Tamil Nadu
Tripura=Tripura
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
West_Bengal=West Bengal
Agrigento=Agrigento
Alessandria=Alessandria
Ancona=Ancona
Aosta=Aosta
Ascoli_Piceno=Ascoli Piceno
L'Aquila=L'Aquila
Arezzo=Arezzo
Asti=Asti
Avellino=Avellino
Bari=Bari
Bergamo=Bergamo
Biella=Biella
Belluno=Belluno
Benevento=Benevento
Bologna=Bologna
Brindisi=Brindisi
Brescia=Brescia
Barletta-Andria-Trani=Barletta-Andria-Trani
Bolzano=Bolzano
Cagliari=Cagliari
Campobasso=Campobasso
Caserta=Caserta
Chieti=Chieti
Carbonia-Iglesias=Carbonia-Iglesias
Caltanissetta=Caltanissetta
Cuneo=Cuneo
Como=Como
Cremona=Cremona
Cosenza=Cosenza
Catania=Catania
Catanzaro=Catanzaro
Enna=Enna
ForlÃ¬-Cesena=ForlÃ¬-Cesena
Ferrara=Ferrara
Foggia=Foggia
Florence=Florence
Fermo=Fermo
Frosinone=Frosinone
Genoa=Genoa
Gorizia=Gorizia
Grosseto=Grosseto
Imperia=Imperia
Isernia=Isernia
Crotone=Crotone
Lecco=Lecco
Lecce=Lecce
Livorno=Livorno
Lodi=Lodi
Latina=Latina
Lucca=Lucca
Monza_and_Brianza=Monza and Brianza
Macerata=Macerata
Messina=Messina
Milan=Milan
Mantua=Mantua
Modena=Modena
Massa_and_Carrara=Massa and Carrara
Matera=Matera
Naples=Naples
Novara=Novara
Nuoro=Nuoro
Ogliastra=Ogliastra
Oristano=Oristano
Olbia-Tempio=Olbia-Tempio
Palermo=Palermo
Piacenza=Piacenza
Padua=Padua
Pescara=Pescara
Perugia=Perugia
Pisa=Pisa
Pordenone=Pordenone
Prato=Prato
Parma=Parma
Pistoia=Pistoia
Pesaro_and_Urbino=Pesaro and Urbino
Pavia=Pavia
Potenza=Potenza
Ravenna=Ravenna
Reggio_Calabria=Reggio Calabria
Reggio_Emilia=Reggio Emilia
Ragusa=Ragusa
Rieti=Rieti
Rome=Rome
Rimini=Rimini
Rovigo=Rovigo
Salerno=Salerno
Siena=Siena
Sondrio=Sondrio
La_Spezia=La Spezia
Syracuse=Syracuse
Sassari=Sassari
Savona=Savona
Taranto=Taranto
Teramo=Teramo
Trento=Trento
Turin=Turin
Trapani=Trapani
Terni=Terni
Trieste=Trieste
Treviso=Treviso
Udine=Udine
Varese=Varese
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Venice=Venice
Vicenza=Vicenza
Verona=Verona
Medio_Campidano=Medio Campidano
Viterbo=Viterbo
Vibo_Valentia=Vibo Valentia
Hokkaido=Hokkaido
Tokyo=Tokyo
Seoul=Seoul
Aguascalientes=Aguascalientes
Baja_California=Baja California
Baja_California_Sur=Baja California Sur
Chihuahua=Chihuahua
Colima=Colima
Campeche=Campeche
Coahuila=Coahuila
Chiapas=Chiapas
Federal_District=Federal District
Durango=Durango
Guerrero=Guerrero
Guanajuato=Guanajuato
Hidalgo=Hidalgo
Jalisco=Jalisco
Mexico_State=Mexico State
Michoacán=Michoacán
Morelos=Morelos
Nayarit=Nayarit
Nuevo_León=Nuevo León
Oaxaca=Oaxaca
Puebla=Puebla
Querétaro=QuerÃétaro
Quintana_Roo=Quintana Roo
Sinaloa=Sinaloa
San_Luis_Potosí=San Luis Potosí
Sonora=Sonora
Tabasco=Tabasco
Tlaxcala=Tlaxcala
Tamaulipas=Tamaulipas
Veracruz=Veracruz
Yucatán=Yucatán
Zacatecas=Zacatecas
Vestland=Vestland
Taipei_City=Taipei City
Alaska=Alaska
Alabama=Alabama
Arkansas=Arkansas
Arizona=Arizona
California=California
Colorado=Colorado
Connecticut=Connecticut
District_of_Columbia=District of Columbia
Delaware=Delaware
Florida=Florida
Hawaii=Hawaii
Iowa=Iowa
Idaho=Idaho
Illinois=Illinois
Indiana=Indiana
Kansas=Kansas
Kentucky=Kentucky
Louisiana=Louisiana
Massachusetts=Massachusetts
Maryland=Maryland
Maine=Maine
Michigan=Michigan
Minnesota=Minnesota
Missouri=Missouri
Mississippi=Mississippi
Montana=Montana
North_Carolina=North Carolina
North_Dakota=North Dakota
Nebraska=Nebraska
New_Hampshire=New Hampshire
New_Jersey=New Jersey
New_Mexico=New Mexico
Nevada=Nevada
New_York=New York
Ohio=Ohio
Oklahoma=Oklahoma
Oregon=Oregon
Pennsylvania=Pennsylvania
Rhode_Island=Rhode Island
South_Carolina=South Carolina
South_Dakota=South Dakota
Tennessee=Tennessee
Texas=Texas
Utah=Utah
Virginia=Virginia
Vermont=Vermont
Washington=Washington
Wisconsin=Wisconsin
West_Virginia=West Virginia
Wyoming=Wyoming
TwentyFourHoursBeforeActionIsDue=Twenty four hours before action is due
OneHourBeforeActionIsDue=One hour before action is due
TwentyFourHoursAndNoSync=Twenty four hours and no sync
VisitAutoPublished=Visit auto published
Sync-failed-due-to-unknown-reason=Sync failed due to unknown reasons
Null-values-not-allowed=Null Value in {0} not allowed
Length-exceed-allowed-limit={0} length Exceed of total allowed {0} characters
Not-matching-with-allowed-values={0} not matching with allowed values
Lift-Sync-Fail=Sync failed due to unknown reasons; please contact the admin for resolution
No-User-Found= USER not found on LIFT; please contact the admin for resolution
Account-Not-Synced-To-Lift= Account was not synced to LIFT; please contact the admin for resolution
Site-Not-Synced-To-Lift= Site was not synced to LIFT; please contact the admin for resolution
Account=Account
Clean=Clean
Moderate=Moderate
Dirty=Dirty
Holandesa=Holandesa
Jersey=Jersey
Girolando=Girolando
Good=Good
Medium=Medium
Bad=Bad
Freestall=Freestall
Compostbarn=Compostbarn
Semiconfinamento=Semiconfinamento
Pasto=Pasto
Noah=Noah
ProfitablityAnalysis.Date=Date
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
Brown_Swiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
DryHay=Dry hay
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole






