/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.document.*;
import java.util.Arrays;
import java.util.List;

public class RumenFillCalculation {
  RumenFillTool tool;
  RumenFillScoreToolItem docDBCurrentPen;
  RumenHealthManureScoreToolGoalItem docDBCurrentGoal;

  public RumenFillTool calculateFields(RumenFillTool tool) {
    if (tool == null) return null;

    this.tool = tool;

    if (tool.getPens() != null) {
      for (RumenFillScoreToolItem pen : tool.getPens()) {
        docDBCurrentPen = pen;
        pen.getRumenFillScores().averageValue = averageScore();
        pen.getRumenFillScores().standardDeviation = calculateStandardDeviation();
      }
    }

    if (tool.getGoals() != null) {
      for (RumenHealthManureScoreToolGoalItem goal : tool.getGoals()) docDBCurrentGoal = goal;
    }

    return tool;
  }

  private Double averageScore() {
    double totalSum = 0;
    double totalNumberOfAnimals = 0;

    RumenHealthManureScores currentPenManureScore = docDBCurrentPen.getRumenFillScores();
    if (currentPenManureScore.getItems() != null) {
      for (RumenHealthManureScoreItem item : currentPenManureScore.getItems())
        if (item.getAnimalNumbers() != null) {
          totalNumberOfAnimals += item.getAnimalNumbers();
        }
    }

    if (totalNumberOfAnimals > 0) {

      for (RumenHealthManureScoreItem item : currentPenManureScore.getItems()) {
        double pOp = (item.getAnimalNumbers() / totalNumberOfAnimals) * 100;
        item.setPercentOfPen(roundAvoid(pOp, 2));
        totalSum += (item.getScoreCategory() * item.getPercentOfPen());
      }

      return totalSum / 100;
    }
    return 0.0;
  }

  private Double calculateStandardDeviation() {
    double sumOfSquareDelta = 0;
    double n = 0;
    final int perAnimal = 1;
    double avg = docDBCurrentPen.getRumenFillScores().averageValue;

    if (docDBCurrentPen.getRumenFillScores().getItems() != null) {
      List<RumenHealthManureScoreItem> activeScores =
          Arrays.stream(docDBCurrentPen.getRumenFillScores().getItems())
              .filter(arg -> arg.getPercentOfPen() != null && arg.getPercentOfPen() > 0)
              .toList();

      for (RumenHealthManureScoreItem score : activeScores) {
        int i = 0;
        for (; i < score.getAnimalNumbers(); i++) {
          double delta = Math.abs(perAnimal * score.getScoreCategory() - avg);
          double sqDelta = Math.pow(delta, 2);
          sumOfSquareDelta += sqDelta;
        }
        n += i;
      }

      if (n != 0) {
        double variance = sumOfSquareDelta / n;
        double sd = Math.sqrt(variance);
        return roundAvoid(sd, 2);
      }
    }
    return 0.0;
  }
}
