/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.SiteMappings;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SiteMappingsRepository extends JpaRepository<SiteMappings, Long> {

  @Query(
      value = "Select a.* FROM site_mappings a where a.site_mapping_document ->> 'id' = :id",
      nativeQuery = true)
  SiteMappings findById(@Param("id") String siteId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>"
              + " 'LabyrinthAccountId' = :accountId and sm.deleted=false",
      nativeQuery = true)
  List<SiteMappings> findAllByLabyrinthAccountId(@Param("accountId") String accountId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>"
              + " 'MaxSiteId' ilike :siteId and sm.deleted=false ",
      nativeQuery = true)
  List<SiteMappings> findByDairyMaxSiteId(@Param("siteId") String siteId, Pageable pageable);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>"
              + " 'MaxSiteId' ilike :siteId and sm.deleted=false",
      nativeQuery = true)
  List<SiteMappings> findByMaxSiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>'LabyrinthSiteId' ="
              + " :siteId and sm.deleted=false LIMIT 1",
      nativeQuery = true)
  SiteMappings findBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>'LabyrinthSiteId' ="
              + " :siteId and sm.deleted = false",
      nativeQuery = true)
  SiteMappings findBySiteIdAndIsDeleted(@Param("siteId") String siteId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>'DDWHerdId' ="
              + " :ddwHerdId and sm.deleted = false LIMIT 1",
      nativeQuery = true)
  SiteMappings findByDDWHerdIdAndDeleted(@Param("ddwHerdId") String ddwHerdId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>'LabyrinthSiteId' ="
              + " :siteId LIMIT 1",
      nativeQuery = true)
  SiteMappings findBySiteIdWithDeleted(@Param("siteId") String siteId);

  @Query(
      value =
          "Select sm.* FROM site_mappings sm where sm.site_mapping_document ->>'DDWHerdId' is not"
              + " null AND sm.deleted = false",
      nativeQuery = true)
  List<SiteMappings> findAllWithDDWHerdId();
}
