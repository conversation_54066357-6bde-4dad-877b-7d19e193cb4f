/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Silages;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SilagesRepository extends JpaRepository<Silages, Long> {

  @Query(
      value =
          "Select s.* from Silages s where s.silage_Document->>'accountId' in(:accountIdsByUser) "
              + " AND s.deleted= false AND timezone('UTC',s.updated_date) > :lastSyncTime",
      nativeQuery = true)
  Page<Silages> findByAccountIdsPaginated(
      @Param("accountIdsByUser") List<String> accountIdsByUser,
      Pageable pageable,
      @Param("lastSyncTime") Instant lastSyncTime);

  Boolean existsByLocalId(String localId);

  @Query(
      value =
          "Select s.* from Silages s where s.silage_document->>'id' = :id and  s.deleted= false",
      nativeQuery = true)
  Silages findBySilageId(@Param("id") String id);
}
