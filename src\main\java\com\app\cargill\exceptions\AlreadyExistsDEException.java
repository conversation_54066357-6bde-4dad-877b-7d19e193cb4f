/* Cargill Inc.(C) 2022 */
package com.app.cargill.exceptions;

import java.io.Serial;
import lombok.Getter;

@Getter
public class AlreadyExistsDEException extends RuntimeException {
  @Serial private static final long serialVersionUID = 1L;

  public AlreadyExistsDEException() {}

  public AlreadyExistsDEException(String message) {
    super(message);
  }

  public AlreadyExistsDEException(String message, Throwable cause) {
    super(message, cause);
  }
}
