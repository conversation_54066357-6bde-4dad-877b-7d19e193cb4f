/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.exceptions.CustomDEExceptions;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Getter
@Setter
public class BlobServiceClientFactory {

  @Value("${azure.blob.storage.connection}")
  private String blobStorageConnectionString;

  public BlobServiceClient getBlobClient() throws CustomDEExceptions {
    log.debug(" - creating blob client");

    BlobServiceClient blobServiceClient;
    try {
      blobServiceClient =
          new BlobServiceClientBuilder()
              .connectionString(new String(Base64.decode(blobStorageConnectionString)))
              .buildClient();

    } catch (Exception ex) {
      log.error(ex.getMessage());
      throw new CustomDEExceptions(
          "Unable to connect to Azure Blob Storage", HttpStatus.BAD_REQUEST.value());
    }

    return blobServiceClient;
  }
}
