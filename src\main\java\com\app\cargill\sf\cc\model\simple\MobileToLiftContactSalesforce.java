/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class MobileToLiftContactSalesforce {

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("DE_Contact_External_ID__c")
  private String contactExternalId;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("FirstName")
  private String firstName;
}
