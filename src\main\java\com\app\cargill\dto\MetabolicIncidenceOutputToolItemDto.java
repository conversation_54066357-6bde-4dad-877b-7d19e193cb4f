/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MetabolicIncidenceOutputToolItemDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Double retainedPlacentaGoal;

  private Double metritisGoal;

  private Double displacedAbomasumGoal;

  private Double ketosisGoal;

  private Double milkFeverGoal;

  private Double deathLossGoal;

  private Double dystociaGoal;
}
