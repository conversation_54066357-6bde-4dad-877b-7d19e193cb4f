<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitSummaryViewModel.HealthItem", [], "Health",
                locale)}</span>
            <span class="lg-head">${localization.getMessage("WalkthroughReportViewModel.RumenFill", [], "Rumen fill",
                locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000888RF</div>
    </div>
</div>

<!-- Pen Analysis -->
<#if model.rumenFillTool.penAnalysis ?? && model.rumenFillTool.penAnalysis[0]??>
<div class="container">
    <h3 class="title-secondary mb-1 d-flex justify-space-between">
        <span>${localization.getMessage("RumenHealthLandingViewModel.PenAnalysis", [],
            "Pen Analysis", locale)}</span>
    </h3>

    <div class="row mx-neg-4">
    <#if  model.rumenFillTool.penAnalysis?? &&
                model.rumenFillTool.penAnalysis[0] ?? &&
                model.rumenFillTool.penAnalysis[0].penDetails[0] ??>
                <#list model.rumenFillTool.penAnalysis as penAnalysis>
        <div class="col-6 mb-1 px-4 table-secondary">
            <h3 class="title">${penAnalysis.penName!}</h3>
            <table class="table-center">
                <tbody>
                    <tr>
                        <#list penAnalysis.penDetails[0] as penDetailsObj>
                                        <th>${penDetailsObj.column!}</th>
                                    </#list>
                    </tr>
                    <#list penAnalysis.penDetails as penDetailsObj>
                                    <tr>
                                        <#list penDetailsObj as innerlistObj>
                                            <td>${innerlistObj.value!}</td>
                                        </#list>
                                    </tr>
                                </#list>
                </tbody>
            </table>
        </div>
        </#list>
        </#if>
    </div>			
</div>


<#assign counter=0>
<!-- Pen 1 / Pen 2 -->
<#if  model.rumenFillTool.penAnalysis ?? &&
        model.rumenFillTool.penAnalysis[0] ?? && model.rumenFillTool.penAnalysis[0].chart ??>
            <div class="break-page"></div>
<div class="container">
<#list model.rumenFillTool.penAnalysis?chunk(2) as row>
    <div class="row mx-neg-4">
    <#list row as penAnalysis>
        <div class="col-6 px-4">
            <h5 class="title-sub">${penAnalysis.penName!}</h5>
            <div class="card mb-1">
                <div class="card-body">
                    <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart2}"></canvas>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="legend-wrap mb-2">
                            <p>${localization.getMessage("Average", [], "Average", locale)}: <b>${penAnalysis.average!0.0} </b></p>
                        </div>
                        <div class="legend-wrap mb-2">
                            <p>${localization.getMessage("StandardDeviationScoreTitle", [],"Standard Deviation", locale)}: <b>${penAnalysis.standardDeviation!0.0} </b></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script>

                                (function () {
const colors = {
purple: {
default: "#1baca7",
half: "#1baca778",
quarter: "#1baca73b",
zero: "#1baca71c"
},
indigo: {
default: "#1baca7",
quarter: "#1baca778"
}
};


const yAxis = [
<#list penAnalysis.chart as category>
    ${(category.categoryAverage)!'NaN'}<#sep>, </#sep>
</#list>
];

const xAxis = [
<#list penAnalysis.chart as category>
    "${(category.visitDate)!'NaN'}"<#sep>, </#sep>
</#list>
];

const ctx = document.getElementById("${linechart2}").getContext("2d");
ctx.canvas.height = 100;

gradient = ctx.createLinearGradient(0, 25, 0, 300);
gradient.addColorStop(0, colors.purple.half);
gradient.addColorStop(0.35, colors.purple.quarter);
gradient.addColorStop(1, colors.purple.zero);

const options = {
type: "line",
data: {
labels: xAxis,
datasets: [
{
fill: true,
backgroundColor: gradient,
borderColor: '#1BACA7',
pointBackgroundColor: '#1BACA7',
pointBorderColor: '#fff',
data: yAxis,
lineTension: 0.2,
borderWidth: 1,
pointRadius: 6,
}
]
},
options: {
plugins: {
legend: {
display: false,
},
    tooltip: {
callbacks: {
title : () => null // or function () { return null; }
     },
        yAlign: 'bottom',
        backgroundColor: "#fff",
        borderColor: "rgba(0, 0, 0, 0.25)",
        borderWidth: 1,
        displayColors: false,
        bodyColor: "#1BACA7",
        bodyAlign: "center",
    },
  },

layout: {
    padding: {
        top:20,
        right: 15
    }
},

responsive: true,

scales: {
y: {
// beginAtZero: true,
title: {
display: true,
color: '#6C7782',
text: '${localization.getMessage("EditNoteViewModel.Category", [], "Category", locale)}',
            padding: {
bottom: 15,
}
        },

                    grid: {
display: false,
},
      },

    x: {
grid: {
display: false,
},
        color: '#6C7782',
        title: {
display: true,
color: '#6C7782',
text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
        padding: {
bottom: 15,
}
    }
    }
},
animation: {
duration: 0,
onComplete: function() {
    var chart = this;
    var ctx = chart.ctx;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'bottom';
    ctx.fillStyle =  '#6C7782';
    this.data.datasets.forEach(function(dataset, i) {
                    var meta = chart.getDatasetMeta(i);
                    meta.data.forEach(function(bar, index) {
                    var data = dataset.data[index];
                    data = isNaN(data) ? '': data;
                    var yIndex = bar.y - 5;
                    if(data && data < 0) {
                        yIndex = bar.y + 15;
                    }
                    ctx.fillText(data, bar.x, yIndex);
                    });
            });
        }
    }
}
};

window.myLine = new Chart(ctx, options);
}) ();
                            </script>
                            <#assign counter=counter+1>
                        </#list>
                    </div>
                    <#if counter%8==0>
                        <div class="break-page"></div>
                    </#if>
                </#list>
</div>
</#if>
</#if>

<!-- Herd Analysis -->
<#if  model.rumenFillTool.herdAnalysis??>
<div class="container">
    <h3 class="title-secondary mb-1">${localization.getMessage("RumenHealthLandingViewModel.HerdAnalysis", [],"Herd Analysis", locale)}</h3>
    <div class="row">
        <div class="col-12 table-secondary">
            <table>
                <tbody>
                    <#if model.rumenFillTool.herdAnalysis?? &&
                            model.rumenFillTool.herdAnalysis.herdAnalysisDetails[0] ??>
                            <tr>
                                <#list model.rumenFillTool.herdAnalysis.herdAnalysisDetails[0] as headings>
                                    <th>${headings.column!}</th>
                                </#list>
                            </tr>
                        </#if>

                        <#if  model.rumenFillTool.herdAnalysis?? &&
                            model.rumenFillTool.herdAnalysis.herdAnalysisDetails[0] ??>
                            <#list model.rumenFillTool.herdAnalysis.herdAnalysisDetails as innerlist>
                                <tr>
                                    <#list innerlist as obj>
                                        <td>${obj.value!}</td>
                                    </#list>
                                </tr>
                            </#list>
                        </#if>
                </tbody>
            </table>
        </div>
    </div>
</div>
<#if  model.rumenFillTool.herdAnalysis?? &&
            model.rumenFillTool.herdAnalysis.graph??>
<div class="break-page"></div>

<!-- Graph -->
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="card mt-2">
                <div class="card-body">
                    <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                            <canvas id="${linechart2}"></canvas>
                </div>
                <div class="card-footer">
                    <div class="row">
                        <div class="legend-wrap mb-2">
                            <p class="green-solid">${localization.getMessage("Report.AvgRumenFillScore", [], "Average Rumen Fill Score", locale)}</p>
                        </div>
                        <div class="legend-wrap mb-2">
                            <p class="orange-strip">${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Min", [], "Min", locale)}</p>
                        </div>
                        <div class="legend-wrap mb-2">
                            <p class="red-strip">${localization.getMessage("RumenHealthTMRParticleScorePenTableInputViewModel.Max", [], "Max", locale)}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>

                    (function () {
const colors = {
    purple: {
    default: "#1baca7",
    half: "#1baca778",
    quarter: "#1baca73b",
    zero: "#1baca71c"
    },
        indigo: {
    default: "#1baca7",
    quarter: "#1baca778"
    }
    };

    const averageRumenFillScore = [
    <#list model.rumenFillTool.herdAnalysis.graph.lactationStages as lacStage>
        ${(model.rumenFillTool.herdAnalysis.graph.averageRumenFillScore[lacStage])!'NaN'}<#sep>, </#sep>
    </#list>
    ];

    const max = [
    <#list model.rumenFillTool.herdAnalysis.graph.lactationStages as lacStage>
        ${(model.rumenFillTool.herdAnalysis.graph.max[lacStage])!'NaN'}<#sep>, </#sep>
    </#list>
    ];

    const min = [
    <#list model.rumenFillTool.herdAnalysis.graph.lactationStages as lacStage>
        ${(model.rumenFillTool.herdAnalysis.graph.min[lacStage])!'NaN'}<#sep>, </#sep>
    </#list>
    ];

    const labels = [
    <#list model.rumenFillTool.herdAnalysis.graph.lactationStages as lacStage>
        '${localization.getMessage(lacStage, [], lacStage, locale)}'<#sep>, </#sep>
    </#list>
    ];


    const ctx = document.getElementById("${linechart2!}").getContext("2d");
    ctx.canvas.height = 100;

    gradient = ctx.createLinearGradient(0, 25, 0, 300);
    gradient.addColorStop(0, colors.purple.half);
    gradient.addColorStop(0.35, colors.purple.quarter);
    gradient.addColorStop(1, colors.purple.zero);

    const options = {
        type: "line",

        data: {
            labels: labels,
            datasets: [
                {
                    fill: true,
                    backgroundColor: gradient,
                    borderColor: '#1BACA7',
                    pointBackgroundColor: '#1BACA7',
                    pointBorderColor: '#fff',
                    data: averageRumenFillScore,
                    lineTension: 0.2,
                    borderWidth: 1,
                    pointRadius: 6,
                    yAxisID: 'y',
                },

                {
                    borderColor: '#8D0909',
                    pointBackgroundColor: '#8D0909',
                    pointBorderColor: '#fff',
                    data: max,
                    lineTension: 0.2,
                    borderWidth: 1,
                    pointRadius: 6,
                    borderDash: [5, 4],
                    yAxisID: 'y',
                },
                {
                    borderColor: '#DA9E44',
                    pointBackgroundColor: '#DA9E44',
                    pointBorderColor: '#fff',
                    data: min,
                    lineTension: 0.2,
                    borderWidth: 1,
                    pointRadius: 6,
                    borderDash: [5, 4],
                    yAxisID: 'y',
                },
            ]
        },
        options: {
            plugins: {
                legend: {
                    display: false,
                },
                tooltip: {
                    callbacks: {
                        title: () => null // or function () { return null; }
                    },
                    yAlign: 'bottom',
                    backgroundColor: "#fff",
                    borderColor: "rgba(0, 0, 0, 0.25)",
                    borderWidth: 1,
                    displayColors: false,
                    bodyColor: "#1BACA7",
                    bodyAlign: "center",
                },
            },

            layout: {
                padding: {
                    top:20,
                    right: 15
                }
            },

            responsive: true,

            scales: {
                y: {
                    type: 'linear',
                    position: 'left',
                    title: {
                        display: true,
                        color: '#6C7782',
                        text: '${localization.getMessage("EditNoteViewModel.Category", [], "Category", locale)}',
                        padding: {
                            bottom: 15,
                        },
                    },
                    grid: {
                        display: false
                    }
                },

                x: {
                    title: {
                        display: true,
                        color: '#6C7782',
                        text: '${localization.getMessage("Report.BCS.LactationStages", [], "Lactation Stages", locale)}',
                        padding: {
                            top: 15,
                        }
                    },
                    grid: {
                        display: false,
                    },
                }
            },
            animation: {
                duration: 0,
                onComplete: function() {
                    var chart = this;
                    var ctx = chart.ctx;
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'bottom';
                    ctx.fillStyle = '#6C7782';
                    this.data.datasets.forEach(function(dataset, i) {
                        var meta = chart.getDatasetMeta(i);
                        meta.data.forEach(function(bar, index) {
                            var data = dataset.data[index];
                            data = isNaN(data) ? '': data;
                            var yIndex = bar.y - 5;
                            if(data && data < 0) {
                                yIndex = bar.y + 15;
                            }
                            ctx.fillText(data, bar.x, yIndex);
                        });
                    });
                }
            }
        }
    };

        window.myLine = new Chart(ctx, options);
                    })();
                </script>
</#if>
</#if>
<#if model.rumenFillTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.rumenFillTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>