/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.RumenFillHealthHerdAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("rumenFillHealthHerdAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class RumenFillHealthHerdAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    RumenFillHealthHerdAnalysisReportDto dto =
        modelMapper.map(data, RumenFillHealthHerdAnalysisReportDto.class);
    dto.setLactationStages(
        dto.getAverageRumenFillScore().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));

    try (XSSFWorkbook rumenFillHerdAnalysisWB = new XSSFWorkbook()) {
      // create rumenFillHerdAnalysisWBSheet
      XSSFSheet rumenFillHerdAnalysisWBSheet =
          rumenFillHerdAnalysisWB.createSheet(
              ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              rumenFillHerdAnalysisWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(rumenFillHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              rumenFillHerdAnalysisWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(rumenFillHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              rumenFillHerdAnalysisWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(rumenFillHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(
              rumenFillHerdAnalysisWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      prepareHeader(
          rumenFillHerdAnalysisWB,
          rumenFillHerdAnalysisWBSheet,
          rowNumber,
          cellNumber,
          dto,
          boldStyle,
          locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = rumenFillHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      rumenFillHerdAnalysisWBSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // Lact Stages
      cellNumber.set(0);

      int lactationStageStartRowNumber = rowNumber.get();

      row = rumenFillHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.createAndSetCellValue(
            row, cellNumber, centerBlack, ExcelUtils.getLangValue(lStage, null, source, locale));
      }

      //  Avg Rumen fill score
      cellNumber.set(0);

      int avgRumenFillRowNumber = rowNumber.get();
      row = rumenFillHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_AVG_RUMEN_FILL_SCORE, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row,
            dto.getAverageRumenFillScore().get(lStage),
            cellNumber,
            decimalStyle,
            greyCellStyle);
      }

      //  Min
      cellNumber.set(0);
      int minRowNumber = rowNumber.get();
      row = rumenFillHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_MIN, null, source, locale));
      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getMin().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // Max
      cellNumber.set(0);
      int maxRowNumber = rowNumber.get();
      row = rumenFillHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_MAX, null, source, locale));
      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getMax().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      int columnStart = 1;
      int columnEnd = columnStart + dto.getLactationStages().length - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

      XDDFDataSource<String> lactationStage =
          XDDFDataSourcesFactory.fromStringCellRange(
              rumenFillHerdAnalysisWBSheet,
              new CellRangeAddress(
                  lactationStageStartRowNumber,
                  lactationStageStartRowNumber,
                  columnStart,
                  columnEnd));
      XDDFNumericalDataSource<Double> averageRumenFillDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenFillHerdAnalysisWBSheet,
              new CellRangeAddress(
                  avgRumenFillRowNumber, avgRumenFillRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> minDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenFillHerdAnalysisWBSheet,
              new CellRangeAddress(minRowNumber, minRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> maxDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenFillHerdAnalysisWBSheet,
              new CellRangeAddress(maxRowNumber, maxRowNumber, columnStart, columnEnd));

      // needed objects for the charts
      XSSFChart chart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 1;
      // ======first line chart==========
      chart =
          ExcelUtils.initChart(
              rumenFillHerdAnalysisWBSheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_FILL_HERD_ANALYSIS_CHART_NAME, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart,
              ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_RUMEN_HEALTH_MS_PEN_ANALYSIS_CATEGORIES, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, minDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_MIN, null, source, locale),
          new CellReference(
              rumenFillHerdAnalysisWBSheet.getSheetName(), minRowNumber, 0, true, true));
      series.setSmooth(true);
      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, maxDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_MAX, null, source, locale),
          new CellReference(
              rumenFillHerdAnalysisWBSheet.getSheetName(), maxRowNumber, 0, true, true));
      series.setSmooth(true);
      series =
          (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, averageRumenFillDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_AVG_RUMEN_FILL_SCORE, null, source, locale),
          new CellReference(
              rumenFillHerdAnalysisWBSheet.getSheetName(), avgRumenFillRowNumber, 0, true, true));
      series.setSmooth(true);
      chart.plot(dataLeft);
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.ORANGE, true);
      ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.RED, true);
      ExcelUtils.drawLineSeries(dataLeft, 2, PresetColor.GREEN, false);

      // ExcelUtils.drawGridLinesInChart(chart, true);

      return ExcelUtils.finalizeWorkbook(
          rumenFillHerdAnalysisWB, rumenFillHerdAnalysisWBSheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public Object prepareData(Object data) {
    RumenFillHealthHerdAnalysisReportDto rumenFillMappedDto =
        modelMapper.map(data, RumenFillHealthHerdAnalysisReportDto.class);
    rumenFillMappedDto.setLactationStages(
        rumenFillMappedDto.getAverageRumenFillScore().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));
    return rumenFillMappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    RumenFillHealthHerdAnalysisReportDto rumenFillMappedDto =
        (RumenFillHealthHerdAnalysisReportDto) prepareData(dto);
    byte[] report =
        freeMarkerComponent.render(
            rumenFillMappedDto,
            ReportsToBeanMappings.RUMEN_FILL_HEALTH_HERD_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    RumenFillHealthHerdAnalysisReportDto rumenFillMappedDto =
        modelMapper.map(data, RumenFillHealthHerdAnalysisReportDto.class);
    return StringUtils.isBlank(rumenFillMappedDto.getFileName())
        ? ReportsToBeanMappings.RUMEN_FILL_HEALTH_HERD_ANALYSIS_REPORT.getFileName()
        : rumenFillMappedDto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook rumenHealthHerdAnalysisWB,
      XSSFSheet rumenHealthHerdAnalysisSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      RumenFillHealthHerdAnalysisReportDto rumenFillMappedDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        rumenHealthHerdAnalysisWB,
        rumenHealthHerdAnalysisSheet,
        rowNumber.get(),
        cellNumber.getAndIncrement());
    // headings
    XSSFRow row = rumenHealthHerdAnalysisSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, rumenFillMappedDto.getVisitName());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, rumenFillMappedDto.getVisitDate());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = rumenHealthHerdAnalysisSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, rumenFillMappedDto.getToolName());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, rumenFillMappedDto.getAnalysisType());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
  }
}
