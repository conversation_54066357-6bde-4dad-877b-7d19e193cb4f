/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.MappedSuperclass;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder(toBuilder = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
public class EditableDocumentBaseDto implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  private UUID id;

  private String createUser;

  private boolean isDeleted;

  private String lastModifyUser;

  private Instant createTimeUtc;

  private Instant lastModifiedTimeUtc;

  private Instant mobileLastUpdatedTime;

  private Instant lastSyncTimeUtc;

  private boolean isNew;
}
