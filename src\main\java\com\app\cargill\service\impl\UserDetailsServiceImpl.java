/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.UserDetailsDto;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Aslam
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

  private final UserRepository repositoryUsers;

  @Override
  public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
    log.info("Loading user: {}", username);

    User user = getUser(username);
    if (user == null) {
      throw new UsernameNotFoundException("User with name " + username + " not found");
    }
    return setUserDetailsObject(user);
  }

  private UserDetails setUserDetailsObject(User users) {
    if (users != null) {
      UserDetailsDto user = new UserDetailsDto(users.getPrincipalName(), users.getPassword());
      List<String> authorities = new ArrayList<>();
      if (users.getRoles() != null) {
        users.getRoles().forEach(role -> authorities.add(role.getCode()));
        user.setAuthorities(
            AuthorityUtils.createAuthorityList(authorities.toArray(new String[] {})));
      }

      return user;
    }
    return null;
  }

  /**
   * Returns SQL query to load user
   *
   * @param username The username
   * @return logged user if exists, else null
   */
  private User getUser(String username) {
    Optional<User> user = repositoryUsers.findByPrincipalName(username);
    return user.orElse(null);
  }
}
