/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers.cdp;

import static org.junit.Assert.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.document.AnimalClass;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.List;
import java.util.UUID;
import org.javatuples.Triplet;
import org.junit.jupiter.api.Test;

class CdpSiteDocumentMapperTest {

  @Test
  void accountIdFieldsSwitch() throws JsonProcessingException {
    SiteDocument siteDocument = new SiteDocument();
    String uuid = "fd86092a-0d31-4915-9a79-91df6b68ec1c";
    String sfId = "1234";
    siteDocument.setAccountId(UUID.fromString(uuid));
    siteDocument.setExternalAccountId(sfId);

    SiteDocumentDTO siteDocumentDTO = CdpSiteDocumentMapper.mapToDto(siteDocument);
    ObjectMapper objectMapper = new ObjectMapper();
    String result = objectMapper.writeValueAsString(siteDocumentDTO);

    assertTrue(result.contains(String.format("\"AccountId\":\"%s\"", sfId)));
    assertTrue(result.contains(String.format("\"ExternalAccountId\":\"%s\"", uuid)));
  }

  @Test
  void whenCreateAnimalListIscalledCorrectResultisReturned() {
    List<Triplet<UUID, String, String>> animalList = CdpSiteDocumentMapper.getAnimalClassList();
    assertNotNull(animalList);
  }

  @Test
  void whenGetAnimalClassById() {

    AnimalClass animalType =
        CdpSiteDocumentMapper.getAnimalClassById(
            UUID.fromString("********-0000-0000-0000-********0006"));
    assertNotNull(animalType);
  }

  @Test
  void setDietsDataForPensIsCalledResultIsReturned() {
    CdpSiteDocumentMapper.setDietsDataForPens(
        PenDocument.builder()
            .animalClassId(UUID.fromString("********-0000-0000-0000-********0006"))
            .build());
    assertNotNull(PenDocument.builder().build());
  }
}
