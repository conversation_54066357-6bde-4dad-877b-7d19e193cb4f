/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import java.io.IOException;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.web.multipart.MultipartFile;

public interface IVisitReportService {
  ByteArrayResource downloadVisitReport(
      VisitReportDto dto, ResourceBundleMessageSource source, Locale locale) throws IOException;

  String getFileName(VisitReportDto dto);

  void shareVisitReportToSharePoint(String visitId, MultipartFile visitReportFile)
      throws CustomDEExceptions, IOException;
}
