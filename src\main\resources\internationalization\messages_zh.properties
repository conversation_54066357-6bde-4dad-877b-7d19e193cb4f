AAEfficiency=氨基酸效率
AMSUtilization=二手机器人单元
AMSUtilizationChart=处理机器人使用表
ARA=ARA
ARS=阿根廷 ($ ARS)
AUD=澳大利亚 ($ AUD)
Account=账户
Account-Not-Synced-To-Lift= 帐户未同步到LIFT；请联系管理员以获得解决方案。
Acre=英亩
Action=行动
AddBag=添加袋
AddBunker=添加饲槽
AddPile=添加堆
AddTMRScore=添加TMR得分
AdjustingKPtoAssureSuccess=调整籽实评分确保符合实际情况
Afghanistan=阿富汗
Agrigento=阿格里真托
Aguascalientes=阿瓜斯卡连特斯
Alabama=阿拉巴马州
Alagoas=阿拉戈亚人
Aland_Islands=阿兰群岛
Alaska=向下
Albania=阿尔巴尼亚
Alberta=艾伯塔省
Alessandria=亚历山大
Algeria=阿尔及利亚
Amapá=阿马帕
Amazonas=亚马逊
Amount=数量
Ancona=安科纳
Andaman_and_Nicobar_Islands=安达曼和尼科巴群岛
Andhra_Pradesh=安德拉邦
Andorra=安道尔
Angola=安哥拉
Anguilla=安圭拉
Anhui=安海
AnimalInformation=动物信息
AnimalListViewModel.Title=动物类型/子分类
Animals=动物数量
AnimalsInHerd=全场动物数量
AnimalsInPen=全圈动物数量
AnimalsObserved=受观察动物数量
Annually=每年
Answers=答案
Antarctica=南极洲
Antigua_and_Barbuda=安提瓜和巴布达
Aosta=奥斯塔
AppName=奶牛云系统
Arezzo=阿雷佐
Argentina=阿根廷
Arizona=亚利桑那
Arkansas=阿肯色州
Armenia=亚美尼亚
Aruba=阿鲁巴
Arunachal_Pradesh=阿鲁纳恰尔邦
Ascoli_Piceno=阿斯科利皮切诺
Assam=阿萨姆邦
Asti=直到
AtSixLengthPerDayImperial=每天6英寸（英制）
AtSixLengthPerDayMetric=每天15厘米（公制）
AtThreeLengthPerDayImperial=每天3英尺（英制）
AtThreeLengthPerDayMetric=每天7厘米（公制）
Australia=澳大利亚
Australian_Capital_Territory=澳大利亚首都地区
Austria=奥地利
Auto_Sync=自动同步
Avellino=阿维利诺
Average=平均
AverageMilkLoss=平均奶量下降
AverageScoreTitle=平均TMR颗粒度评分
AvgBCS=平均体况评分
AvgLocomotionScore=平均蹄部行走评分（计算）
Azerbaijan=阿塞拜疆
BAM=BAM
BCS=体况评分
BCSCategory1=体况评分得分1.0
BCSCategory1pt5=体况评分得分1.5
BCSCategory2=体况评分得分2.0
BCSCategory2pt5=体况评分得分2.5
BCSCategory3=体况评分得分3.0
BCSCategory3pt5=体况评分得分3.5
BCSCategory4=体况评分得分4.0
BCSCategory4pt5=体况评分得分4.5
BCSCategory5=体况评分得分5.0
BCSEditMilkAndDimViewModel.BCSDIMTitle=泌乳天数
BCSEditMilkAndDimViewModel.BCSMilkTitle=产奶量
BCSEditMilkAndDimViewModel.Title=可编辑 泌乳天数和产奶量
BCSHerdAnalysisInputsViewModel.BCS=体况评分
BCSHerdAnalysisInputsViewModel.BCSAnalysis=体况评分分析
BCSHerdAnalysisInputsViewModel.BCSDIM=泌乳天数
BCSHerdAnalysisInputsViewModel.BCSEdit=编辑
BCSHerdAnalysisInputsViewModel.BCSMilk=产奶量
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=全群分析
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=目标
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=输入
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=结果
BCSHerdAnalysisMasterViewModel.BCSTitle=体况评分
BCSHerdAnalysisMasterViewModel.Title=体况评分
BCSHerdAnalysisResultsViewModel.BCSAvg=平均体况得分
BCSHerdAnalysisResultsViewModel.GraphTitle=体况评分分析
BCSHerdAnalysisResultsViewModel.MaxBCS=体况评分上限
BCSHerdAnalysisResultsViewModel.MilkHeadDay=产奶量/头/天
BCSHerdAnalysisResultsViewModel.MinBCS=体况评分下限
BCSHerdAnalysisResultsViewModel.SubHeading=全群分析
BCSHerdAnalysisResultsViewModel.Title=体况评分
BCSPenSelectionViewModel.PenSelectionList=圈舍
BCSPenSelectionViewModel.Pens=圈舍
BCSPenSelectionViewModel.SelectPointScale=选择评分制
BCSPenSelectionViewModel.Title=体况评分
BCSSelectPointScaleViewModel.FooterText=每次拜访只能选择一种评分制。改变评分制会导致数据的丢失。
BCSSelectPointScaleViewModel.SelectPointScale=选择评分制
BCSSelectPointScaleViewModel.Title=体况评分
BGL=BGL
BRL=巴西 (R$ BRL)
BRR=BRR
Bad=Bad
Bag=袋
BaggedConventionalSilage=袋装传统青贮
Bags=袋
Bahamas=巴哈马
Bahia=巴伊亚
Bahrain=巴林
Baja_California=巴哈加利福尼亚
Baja_California_Sur=南下加利福尼亚州
Baleage=裹包青贮
BaleageFQAs=裹包青贮 FAQs
Baleage_AreBalesWrappedWith=裹包青贮用什么打包材料？
Baleage_BagsPlacedOnStableWellManagedSurface=袋子是否放在管理很好的平整表面（柏油或水泥）
Baleage_InspectedForPestHoleDamageRepairOnBasis=检查是否有害虫造成的破口，并且有固定的维修周期？（每周一次）
Baleage_TrashVegRodentControlledAroundBags=袋子附近是否有任何垃圾，植物和啮齿类动物的控制方式？
Baleage_WaterShedsOffPlasticNotIntoBaleage=是否使用防水的塑料袋并且不漏水？（较大的正方形的裹包挑战较大）
Bangladesh=孟加拉国
Barbados=巴巴多斯
Bari=他们是
Barletta-Andria-Trani=巴列塔-安德里亚-特拉尼
Bayern=拜仁
BeddedPack=大通铺
Beijing=北京
Belarus=白俄罗斯
Belgium=比利时
Belize=伯利兹
Belluno=贝鲁诺
Benchmarks_Serum_ToolTip=<value />
Benevento=贝内文托
Benin=贝宁
Bergamo=贝加莫
Bermuda=百慕大
BetweenFifteenTwenty=15到20之间
Bhutan=不丹
BiWeekly=每两周
Biella=比拉
Bihar=比哈尔邦
BodyConditionHerdGoals=体况评分全群分析目标
BodyConditionHerdInputs=体况评分全群分析输入
BodyConditionHerdResults=体况评分全群结果
BodyConditionInputs=体况输入
BodyConditionResults=体况结果
BodyConditionScoreCategory=体况评分等级{0}
BodyConditionScoreEditInputsViewModel.Count=计数
BodyConditionScoreEditInputsViewModel.NumberOfCows=母牛头数
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=请计算母牛头数
BodyConditionScoreEditInputsViewModel.Title=母牛头数
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=围产前期 （－20 至 －1）
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=泌乳早期（16 至 60）
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=干奶前期（低于21天）
BodyConditionScoreHerdEditGoalsViewModel.Fresh=新产期（0 至15天）
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=泌乳后期（大于201天）
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=体况评分上限
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=泌乳中期（121天至200天）
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=体况评分下限
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=高峰期（61天至120天
BodyConditionScoreHerdEditGoalsViewModel.Title=编辑目标
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=围产前期 （－20 至 －1）
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=泌乳早期（16 至 60）
BodyConditionScoreHerdGoalsViewModel.Edit=可编辑
BodyConditionScoreHerdGoalsViewModel.FarOffDry=干奶前期（低于21天）
BodyConditionScoreHerdGoalsViewModel.Fresh=新产期（0 至15天）
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=体况评分目标上限
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=体况评分目标下限
BodyConditionScoreHerdGoalsViewModel.LateLactation=泌乳后期（大于201天）
BodyConditionScoreHerdGoalsViewModel.MidLactation=泌乳中期（121天至200天）
BodyConditionScoreHerdGoalsViewModel.PeakMilk=高峰期（61天至120天）
BodyConditionScoreHerdGoalsViewModel.TableTitle=不同泌乳阶段的体况评分
BodyConditionScoreInputsViewModel.AnimalsObserved=已观察动物数量
BodyConditionScoreInputsViewModel.AvgBCSCalculated=平均体况评分（计算）
BodyConditionScoreInputsViewModel.BCSCategory=体况评分等级
BodyConditionScoreInputsViewModel.BCSPercentOfPen=每圈的比例（%）
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=体况评分(BCS)
BodyConditionScoreInputsViewModel.Edit=编辑
BodyConditionScoreInputsViewModel.StdDevCalculated=标准偏差（计算）
BodyConditionScoreMasterViewModel.Goals=目标
BodyConditionScoreMasterViewModel.Inputs=输入
BodyConditionScoreMasterViewModel.Results=结果
BodyConditionScoreMasterViewModel.SubHeading=全群分析
BodyConditionScoreMasterViewModel.Title=体况评分
BodyConditionScoreResultsViewModel.BCSAverageTitle=平均得分
BodyConditionScoreResultsViewModel.PercentPen=每圈的比例（%）
BodyConditionScoreResultsViewModel.SelectedDates=选择日期
BodyConditionScoreResultsViewModel.Title=体况评分结果
BodyConditionScoresMasterViewModel.BodyConditionScore=体况评分
BodyConditionScoresMasterViewModel.Inputs=输入
BodyConditionScoresMasterViewModel.Results=结果
BodyConditionScoresMasterViewModel.Title=体况评分
BodyConditionScoresMasterViewModel.VisitNotebook=查阅记录本
Bolivia,_Plurinational_State_of=玻利维亚，多元状态
Bologna=博洛尼亚
Bolzano=博尔扎诺
Bonaire=博内尔岛
Bonaire,_Sint_Eustatius_and_Saba=博内尔岛、圣尤斯特歇斯岛和萨巴岛
Bosnia_and_Herzegovina=波斯尼亚和黑塞哥维那
Botswana=博茨瓦纳
BottomUnloadingSilo=底部取料窖仓
BottomUnloadingSilos=底部取料窖仓
Bouvet_Island=布维岛
Brazil=巴西
Brescia=布雷西亚
Brindisi=烤面包
British_Columbia=不列颠哥伦比亚省
British_Indian_Ocean_Territory=英属印度洋领地
Brunei_Darussalam=文莱达鲁萨兰国
Bulgaria=保加利亚
Bull=公牛
Bunker=青贮窖
BunkerCapacity=青贮窖容量
BunkerFeedOutRate=青贮窖饲喂速度
Bunkers=青贮窖
BunkersAndPiles=青贮窖和青贮堆
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=奖励：2层塑料覆膜，有一层是非永久使用的
BunkersAndPiles_CleanlinessOfFeedArea=青贮区域是否清洁？评分范围为1－10分，10分是最好的
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=青贮窖覆盖膜掀开频率？
BunkersAndPiles_FaceRemoveRate=窖面饲喂速度
BunkersAndPiles_LooseOrFacedFeedIsFed=饲喂散落的青贮
BunkersAndPiles_PackingInitialSpreadLayers=压实：初始的第一层青贮厚度为15厘米或更低？
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=青贮堆的斜坡和青贮窖的坡度不能低于  3\:1的起高度 （18度的斜坡）
BunkersAndPiles_PorosityScoresConsistently=Porosity scores consistently
BunkersAndPiles_SealedImmedAfterPack6milPlastic=压实后立即使用6M的覆膜封窖
BunkersAndPiles_SideWallsSealedPlastic=侧墙也使用塑料袋进行密封
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=窖面光滑，没有不好的窖面管理导致氧气能够进入
BunkersAndPiles_TiresSplitsTouching=有轮胎/内胎相连压实？
Burkina_Faso=布基纳法索
Burundi=布隆迪
CAD=加拿大 (CA$ CAD)
CFNChina=中国
CFNIndia=印度
CHF=瑞士 (CHF CHF)
CLF=CLF
CLP=智利 ($ CLP)
CNY=中国 (CNY CNY)
COP=COP
CPNBrazil=巴西
CPNFrance=法国
CPNPoland=波兰
CPNUS=美国
CRC=CRC
CZK=0 (CZK CZK)
Cagliari=卡利亚里
Calf=犊牛
CalfHeiferColostrum=犊牛&amp;青年牛评分-初乳
CalfHeiferGrowerPuberty=犊牛&amp;青年牛评分-育成，青春期，怀孕，围产
CalfHeiferKeyBenchmarks=犊牛&amp;青年牛评分-关键基准
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=颜色由范围决定：&amp;lt;75%\: 红色, &amp;gt;\=75 and &amp;lt;90\: 橙色, &amp;gt;\=90\: 绿色 
CalfHeiferPostweaned=犊牛&amp;青年牛评分- 断奶后
CalfHeiferPreweaned=犊牛&amp;青年牛评分- 断奶前
CalfHeiferQuestionViewModel.Close=关闭
CalfHeiferQuestionViewModel.Colostrum=初乳
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=育成，青春期，怀孕，围产
CalfHeiferQuestionViewModel.KeyBenchmarks=关键基准
CalfHeiferQuestionViewModel.Postweaned=断奶后
CalfHeiferQuestionViewModel.Preweaned=断奶前
CalfHeiferQuestionViewModel.Resources=资料
CalfHeiferQuestionViewModel.VisitNotebook=拜访笔记
CalfHeiferResources=犊牛&amp;青年牛评分- 资料
CalfHeiferResults=犊牛&amp;青年牛评分- 结果
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=犊牛&amp;青年牛评分 
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=育成，青春期，怀孕，围产
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=包含所有评分卡得分
CalfHeiferScoreCardScoreViewModel.PhaseOne=初乳
CalfHeiferScoreCardScoreViewModel.PhaseThree=断奶后
CalfHeiferScoreCardScoreViewModel.PhaseTwo=断奶前
CalfHeiferScorecardImprovementViewModel.Colostrum=初乳
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=育成，性成熟，怀孕，围产前期
CalfHeiferScorecardImprovementViewModel.Postweaned=断奶后
CalfHeiferScorecardImprovementViewModel.Preweaned=断奶前
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=点击以下类别观察各阶段结果
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=颜色由范围决定：等于100%：绿色，&amp;lt，；100%：红色
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=记录？
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=关键基准
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=阶段5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=阶段4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=阶段1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=阶段7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=阶段6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=阶段2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=保持准确的生长和健康记录
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=怀孕&amp;\#xA;15 - 23 月龄
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=青春期&amp;\#xA;9 - 15月龄
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=初乳&amp;\#xA;1 - 3日龄
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=围产期/ 生产&amp;\#xA;23 - 26 月龄
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=育成&amp;\#xA;3 - 9月龄
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=断奶前/后&amp;\#xA;0 - 3月龄
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=拜访笔记
CalfHeiferScorecardLanding=犊牛&amp;青年牛评分 
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=关键基准
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=提升
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=反应
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=评分
CalfHeiferScorecardResultsViewModel.VisitNotebook=拜访笔记
CalfHeiferScorecardViewModel.Colostrum=初乳
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=育成，青春期，怀孕，围产
CalfHeiferScorecardViewModel.KeyBenchmarks=关键基准
CalfHeiferScorecardViewModel.Postweaned=断奶后
CalfHeiferScorecardViewModel.Preweaned=断奶前
CalfHeiferScorecardViewModel.Resources=资料
CalfHeiferScorecardViewModel.Title=评分卡
CalfHeiferScorecardViewModel.VisitNotebook=拜访笔记
CalfHeiferTools=犊牛&amp;青年牛工具
CalfHeiferToolsViewModel.CalfHeiferScorecard=评分卡
CalfHeiferToolsViewModel.CalfHeiferTools=犊牛和青年牛工具
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=工具
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=请在列表中选出一种工具开始你的拜访
CalfHeiferToolsViewModel.CalfHeiferToolsList=工具
CalfHeiferToolsViewModel.Title=犊牛和青年牛工具
CalfHeiferToolsViewModel.VisitNotebook=拜访笔记
California=加利福尼亚
Caltanissetta=卡尔塔尼塞塔
Cambodia=柬埔寨
Cameroon=喀麦隆
Campeche=坎佩切
Campobasso=坎帕巴索
Canada=加拿大
Capacity=容量
Cape_Verde=绿色斗篷
Carbonia-Iglesias=碳含量
Cargill=嘉吉
CargillForageLabKPTest=嘉吉粗饲料试验室KP测试
Carlow=卡洛
Caserta=卡斯塔
Catania=卡塔尼亚
Catanzaro=卡坦扎罗
Category1=粪便评分1.0
Category2=粪便评分2.0
Category3=粪便评分3.0
Category4=粪便评分4.0
Category5=粪便评分5.0
Cavan=卡文
Cayman_Islands=开曼群岛
Ceará=正方形
Central_African_Republic=中非共和国
Chad=乍得
Chandigarh=昌迪加尔
ChewsPerCud=食团咀嚼次数
ChewsPerCudMasterViewModel.AddNew=添加新牛
ChewsPerCudMasterViewModel.CudChewingInputs=输入
ChewsPerCudMasterViewModel.CudChewingResults=结果
ChewsPerCudMasterViewModel.NumOfChews=\# 咀嚼
ChewsPerCudMasterViewModel.Title={0} / \# 咀嚼
Chhattisgarh=恰蒂斯加尔邦
Chiapas=恰帕斯州
Chieti=chieti
Chihuahua=奇瓦瓦
Chile=智利
China=中国
Chinese_Taipei=中华台北
Chongqing=重庆
ChooseAppPDF=请选择一个APP去审阅PDF文件
ChoosingtheCorrectAdditive=选择正确的添加剂
Christmas_Island=圣诞岛
Clare=克莱尔
ClassSubClass=动物分类/子分类
Clean=Clean
ClinicalMastitisLosses=临床性乳房炎损失
CloseUp=围产前期
CloseUpDry=围产前期经产牛
CloseUpHeifer=围产前期头胎牛
Coahuila=科阿韦拉
Cocos_(Keeling)_Islands=可可（Keeling）岛屿
Colima=科里马
Colombia=哥伦比亚
Colorado=科罗拉多州
Colostrum=初乳
Colostrum_AmountOfColostrumOrFed=初乳饲喂量
Colostrum_BrixPercentOfColostrumFed=初乳折光率（Brix %）
Colostrum_CleanAndDryCalvingArea=产犊区域干净干燥
Colostrum_CleanAndDryCalvingArea_ToolTip=落膝测试检测该区域是否干净干燥
Colostrum_CleanAndSanitizeCalfFeedingEquipment=每次饲喂前清洁消毒犊牛饲喂设备
Colostrum_CleanCalfCartToTransportCalf=清洁犊牛转运车
Colostrum_HoursTillCalfIsRemovedFromMother=母子分离所花时间
Colostrum_HoursTillCalfReceivesColostrum=采食到初乳的时间间隔
Colostrum_NumberOfCowsInCalvingArea=产犊区域牛头数
Colostrum_PasteurizeColostrumBeforeFeeding=饲喂巴杀初乳
Colostrum_PercentageOfNavelsDippedInSevenPercent=新生牛在1小时候用7%碘酊消毒的比例
Colostrum_RefrigeratedColostrumStoredLess=冷藏初乳小于24小时
ComfortToolsViewModel.ComfortHeading=请从下面的清单中选择一个工具开始你的牧场拜访
ComfortToolsViewModel.ComfortToolsList=工具
ComfortToolsViewModel.ComfortToolsTitle=舒适度工具
ComfortToolsViewModel.HealthHeading=请从下面的清单中选择一个工具开始你的牧场拜访
ComfortToolsViewModel.HeatstressEvaluationTitle=热应激评估
ComfortToolsViewModel.PenTimeTitle=圈内时间
ComfortToolsViewModel.Title=舒适度工具
ComfortToolsViewModel.VisitNotebook=查阅记录本
Comments=评论
CommonSpinnerViewModel.BodyConditionPDF=体况评分指导原则
CommonSpinnerViewModel.InadequateStimulation=不充分的刺激（不充分的乳头准备：&lt;10 秒；套杯时间&lt;60秒或大于120秒）
CommonSpinnerViewModel.LocomotionPDF=蹄部行走评分指导原则
CommonSpinnerViewModel.ManureScorePDF=粪便评分指导原则
CommonSpinnerViewModel.NoStimulation=没有刺激（只有套杯）
CommonSpinnerViewModel.OptimalStimulation=最佳的刺激（充足的乳头准备：10－20秒；套杯时间在60至90秒）
Como=作为
Comoros=科摩罗
Competitor=竞争者
CompletedTimeKey=时间完成
Component=组成
Compostbarn=Compostbarn
ConcentrateDistribution=内容提要分布
ConfirmExport=按OK键 将会下载每一个已选择页，并且在回到原屏幕前进行拍照
ConfirmScalePointSwitch=改变评分制式将会改变所有已输入的数据
ConfirmScorerSwitch=改变评分人将会改变所有已输入数据
Congo=刚果
Congo,_the_Democratic_Republic_of_the=刚果，民主共和国
Connecticut=康涅狄格州
Consumer=消费者
ConsumerDetailsViewModel.DeleteProspect=删除消费者
ConsumerDetailsViewModel.DeleteProspectPrompt=你确定是否删除这个消费者？消费者，牧场和本次拜访的信息都将丢失？
ConsumerDetailsViewModel.MainHeading=牧场
ConsumerDetailsViewModel.NewSite=添加一个新牧场
ConsumerDetailsViewModel.ProspectTitle=消费者详细信息
ConsumersViewModel.NewConsumer=添加新的消费者
Continue=继续
Cook_Islands=库克群岛
Cork=软木
Corn=玉米
CornSilage=玉米青贮
CornSilageKernel=裹包青贮FAQs
CornSilageResources=青贮来源
CornSilageStopGo=青贮质量不好，不能饲喂
Cosenza=科森扎
Costa_Rica=哥斯达黎加
Costs=成本
Cote_d'Ivoire=象牙海岸
Count=计数
Cow=母牛
CowEfficiency=牛表演
CowPerRobot=每个机器人的母牛
CowsOutsideTargetRangeToolTip=目标是超出目标范围的奶牛&lt;20%。
CowsPerDayNeeded=母牛/每天需要
CowsSectionToolTip=应该以8头牛或更多为一组进行测试得出结论。在小的牛群中应该测试所有的围产前期奶牛。
CowsToBeFed=每天需要饲喂的母牛
CreateDuplicateDiet=本动物类型已经有在使用的日粮。还需要创建吗？
CreateDuplicateNameDiet=相同的日粮名称已经存在，请输入惟一的名称。
Created=创建
Cremona=克雷莫纳
Croatia=克罗地亚
CropCharacteristicsDecisionGuide=作物特性决策指导
Crotone=克罗托内
Cuba=古巴
CudChewingAverageNumber=平均数量
CudChewingDataEntryViewModel.CudChewing=反刍比例
CudChewingDataEntryViewModel.HerdCudChewingDescription=为了本次拜访，使用下面的计数器，对本圈牛中，反刍牛的数量进行统计。你必须完成至少10头牛。
CudChewingDataEntryViewModel.No=否
CudChewingDataEntryViewModel.Yes=是
CudChewingHerdEditScoreViewModel.AverageChewsItem=平均每个食团的咀嚼次数
CudChewingHerdEditScoreViewModel.Close=关闭
CudChewingHerdEditScoreViewModel.DaysInMilkItem=泌乳天数
CudChewingHerdEditScoreViewModel.EditGoalsTitle=编辑食团反刍得分
CudChewingHerdEditScoreViewModel.EditScoreTitle=编辑食团反刍得分
CudChewingHerdEditScoreViewModel.PercentChewingItem=反刍比例
CudChewingMasterViewModel.CudChewing=反刍比例
CudChewingMasterViewModel.CudChewingInputs=输入
CudChewingMasterViewModel.CudChewingResults=结果 
CudChewingPen=圈 
CudChewingPercentChewing=%反刍
CudChewingPercentGoal={0}% 目标
CudChewingPercentOfPen=食团反刍（% 圈）
CudChewingViewModel.CudChewing=圈
CudChewingViewModel.CudChewingList=圈（只有泌乳牛和干奶牛）
CudChewingViewModel.CudChewingTitle=圈名
CudChewingViewModel.Title=圈
CudChewsCalculatorViewModel.CalculatorHeading=请选择一头牛进行食团反刍次数计数。点击上面的“添加新牛”去添加新的牛只。
CudChewsCalculatorViewModel.CudChewCategorySection=奶牛
CudChewsCalculatorViewModel.NumOfChews=\# 反刍
CudChewsDatesForComparisonViewModel.CudChewsPercent=食团反刍%/
Cundinamarca=昆迪纳马卡
Cuneo=楔
Curaçao=库拉
Current=目前
CurrentDownResponse=当前下奶速度
CurrentMilkPrice=当前奶价 ({0}/{1})
CurrentSCC=目前的SCC (细胞计数/{0})
CurrentVisitSummary=目前拜访总结
Customer=客户
CustomerDetailViewModel.CustomerTitle=客户信息
CustomerDetailViewModel.MainHeading=牧场
CustomerDetailViewModel.NewSite=添加新的牧场
CustomerDetailViewModel.NewVisit=新的拜访
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=终端用户
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=—
CustomerProspectsSegmentViewModel.SelectSegment=选择客户分类
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Title=详细信息
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=客户名称－牧场名称
Cyprus=塞浦路斯
CzechRepublic=<value />
Czech_Republic=捷克共和国
DDW=牧场数据
DDWOfflineMessage=由于没有网络，是否查阅线下报告
DDWUpdatedTime=最后更新全群数据：{0}
DKK=DKK
DZD=阿尔及利亚 (DA DZD)
Dadra_and_Nagar_Haveli=达德拉和纳加尔·哈维利
Daily=每日
DairyEnteligenFarmReportsources=奶牛云系统信息来源
Daman_and_Diu=达曼和迪乌
DashboardViewModel.Alert=警告！
DashboardViewModel.AlertMessage=数据已经有超过（）天未同步
DashboardViewModel.GoodAfternoon=下午好
DashboardViewModel.GoodMorning=上午好
DashboardViewModel.MessageBody=提醒在每个牧场拜访时，使用记录本记录特殊事件
DashboardViewModel.MessageHeader=信息
DashboardViewModel.RecentSiteVisit=最近牧场拜访
DashboardViewModel.UserPreferences=用户喜好
Date=日期
DateGone=数据丢失
DatesForComparison=对比数据
Days=天数
DaysInMilkItem=泌乳天数
DeLaval=DeLaval
DeathLoss=死亡损失
DecidingSilageStorage=取决于青贮储存的方式
Delaware=特拉华州
Delete=删除
DeleteMatrixValue=确定是否删除方阵数据？
Delhi=德里
Denmark=丹麦
DensityLossesinPressedBagSilos=压实的袋装青贮的密度和损失
Diet=日粮
DietDCAD=日粮DCAD mEq/100g
DietDetailViewModel.Created=创建
DietDetailViewModel.DDW=牧场数据
DietDetailViewModel.Max=MAX
DietDetailViewModel.SystemGenerated=系统生成
DietDetailViewModel.Title=日粮详细信息
DietDetailViewModel.UserCreated=依据使用者
DietListViewModel.InfoNewDiet=如果MAX系统，已经与奶牛云系统中牧场建立连接，日粮的名称将会被自动更新。否则，请手动添加日粮。或者保持本清单空白，并为每圈选择正确的动物分类/子分类。一旦日粮被创建，选择与该日粮相关的动物分类/子分类
DietListViewModel.MainHeading=日粮
DietListViewModel.New=新的 
DietListViewModel.NewDiet=添加新日粮
DietListViewModel.Title=日粮
Dirty=Dirty
DisplacedAbomasum=真胃变位
District_of_Columbia=哥伦比亚特区
Distrito_Federal=联邦区
Djibouti=吉布提
DoNotTest=&amp;It;20% 或没有检测
Dominica=多米尼加
Dominican_Republic=多明尼加共和国
Donegal=多尼戈尔
DontKnow=不知道
DownResponse=下奶速度
Dry=干奶
DryCow=干奶牛
DryLot=干运动场式
Dryhay=干草
Dublin=都柏林
DuplicatePenName=使用该名称的圈已经存在。请选择另外一个名称。
Durango=杜兰戈
Dystocia=难产
EGP=EGP
EarlyLactation=泌乳早期（16 至 60）
Ecuador=厄瓜多尔
Edit=编辑
EditDatesForComparison=编辑对比数据
EditDatesForComparisonViewModel.Chews=咀嚼
EditDatesForComparisonViewModel.EditDatesClose=关闭
EditDatesForComparisonViewModel.EditDatesLabel=从下面的名单中选择本圈的对比数据
EditDatesForComparisonViewModel.EditDatesTitle=编辑对比数据
EditDatesForComparisonViewModel.EditDatesVisits=拜访
EditDatesForComparisonViewModel.LocomotionScoreAverage=平均的蹄部行走评分
EditDatesForComparisonViewModel.ManureScoreAverage=平均的粪便评分
EditDatesForComparisonViewModel.MetabolicIncidence=在下面的名单中选择最多5次的拜访数据进行对比
EditDatesForComparisonViewModel.PenTimeBudget=在下面的名单中选择最多7次的拜访数据进行对比
EditDatesForComparisonViewModel.PenTimeBudgetTitle=选择下面日期拜访的数据，与本次拜访数据进行对比
EditDatesForComparisonViewModel.TimeRemainingForResting=剩余的休息时间
EditDatesForComparisonViewModel.Title=编辑对比数据
EditDatesForComparisonViewModel.Visits=拜访
EditGoalsCudChewingViewModel.Close=关闭
EditGoalsCudChewingViewModel.CloseUpDry=围产前期
EditGoalsCudChewingViewModel.CudChews=平均每个食团的咀嚼次数
EditGoalsCudChewingViewModel.EarlyLactation=泌乳早期 
EditGoalsCudChewingViewModel.EditGoalsTitle=编辑食团反刍目标
EditGoalsCudChewingViewModel.FarOffDry=干奶前期
EditGoalsCudChewingViewModel.Fresh=新产
EditGoalsCudChewingViewModel.LateLactation=泌乳后期 
EditGoalsCudChewingViewModel.MidLactation=泌乳中期 
EditGoalsCudChewingViewModel.PeakMilk=高峰期
EditGoalsCudChewingViewModel.PercentChewing=反刍比例
EditNoteViewModel.Action=下一步
EditNoteViewModel.Cancel=取消
EditNoteViewModel.Category=类别
EditNoteViewModel.Close=关闭
EditNoteViewModel.CreatedByMetadata=创建 {0} @ {1} by {2}
EditNoteViewModel.Delete=删除
EditNoteViewModel.DeleteImageButtonText=删除图片
EditNoteViewModel.DeleteImagePrompt=想要删除图片吗？
EditNoteViewModel.DeletePrompt=想要删除记录吗？
EditNoteViewModel.DeleteVideoButtonText=删除视频
EditNoteViewModel.DeleteVideoPrompt=想要删除视频吗？
EditNoteViewModel.Event=事件
EditNoteViewModel.LastUpdatedByMetadata=最后一个修改{0} @ {1} by {2}
EditNoteViewModel.NoteCamcorderNotImplemented=还未安装摄像设备
EditNoteViewModel.NoteGalleryNotImplemented=图库功能尚不能用
EditNoteViewModel.NoteLabel=备注
EditNoteViewModel.NoteOnlyOneImage=每一个备注只能加载一张照片。请先删除现在的照片。
EditNoteViewModel.NoteOnlyOneVideo=每一个视频只能加载一张照片。请先删除现在的照片。
EditNoteViewModel.Observation=观察
EditNoteViewModel.Save=保存
EditNoteViewModel.Task=任务
EditNoteViewModel.Title=备注
EditNoteViewModel.TitleLabel=题目
Egypt=埃及
El_Salvador=救星
EmailReportViewModel.AnimalImpact=动物影响
EmailReportViewModel.CalfHeiferItem=犊牛和青年牛
EmailReportViewModel.CalfHeiferScorecard=评分卡
EmailReportViewModel.Capacity=容量
EmailReportViewModel.Cargill=嘉吉
EmailReportViewModel.CategoryList=分类名单
EmailReportViewModel.Charts=图表
EmailReportViewModel.CoefficientVariation=变异系数 (C.V.) (%)
EmailReportViewModel.ComfortHeatStressBanner=热应激工具圈
EmailReportViewModel.ComfortItem=舒适度工具
EmailReportViewModel.ComfortPenTimeBanner=每圈时间安排工具
EmailReportViewModel.ComfortToolsTitle=舒适度工具
EmailReportViewModel.CowsOutsideTargetRange=范围之外的奶牛 (%)
EmailReportViewModel.CudChewingTitle=食团反刍
EmailReportViewModel.DietDCADStr=日粮DCAD
EmailReportViewModel.EmailBody={0}-{1}  报告
EmailReportViewModel.EmailSelectedTools=对选择的工具发送邮件
EmailReportViewModel.EmailSubject={0}报告
EmailReportViewModel.ExportSelected=对选择的工具发送邮件
EmailReportViewModel.FeedOut=饲喂速度
EmailReportViewModel.ForageAuditScorecard=粗饲料评分卡
EmailReportViewModel.ForageImprovements=粗饲料审计评分卡
EmailReportViewModel.ForageLanding=粗饲料评估下载界面
EmailReportViewModel.ForageScorecard=粗饲料评分卡
EmailReportViewModel.GeneratingReport=生成报告
EmailReportViewModel.GotoMarketBranding=走向市场商标
EmailReportViewModel.HealthItem=健康工具
EmailReportViewModel.HeatstressEvaluationTitle=热应激评估
EmailReportViewModel.Herd=全群
EmailReportViewModel.HerdAnalysis=全群分析
EmailReportViewModel.HerdGoals=全群分析－目标
EmailReportViewModel.HerdInputs=全群分析－输入
EmailReportViewModel.HerdResults=全群分析－结果
EmailReportViewModel.HerdRevenue=全群分析－收入
EmailReportViewModel.Improvements=提升
EmailReportViewModel.Inputs=输入
EmailReportViewModel.InputsOutputsChart=输入/输出/图表
EmailReportViewModel.LocomotionScoreTitle=蹄部行走评分 
EmailReportViewModel.ManureScoreTitle=粪便评分
EmailReportViewModel.MarketBranding=Go to Market商标
EmailReportViewModel.MetabolicIncidenceTitle=代谢病发病率 
EmailReportViewModel.MilkProcessCalcInputsTab=挤奶程序收益：输入
EmailReportViewModel.MilkProcessCalcResourcesTab=挤奶程序收益：资源
EmailReportViewModel.MilkProcessCalcResultsTab=挤奶程序收益：结果
EmailReportViewModel.MilkProcessRevenue=挤奶程序收益：计算器
EmailReportViewModel.MilkProcessRevenueCalculator=挤奶程序收益：计算器
EmailReportViewModel.MilkingTime=挤奶时间
EmailReportViewModel.Notes=备注
EmailReportViewModel.NumOfChews=咀嚼次数
EmailReportViewModel.NutritionForage=粗饲料评估 
EmailReportViewModel.NutritionItem=营养
EmailReportViewModel.NutritionPile=堆贮和窖贮的容量
EmailReportViewModel.Outputs=输出
EmailReportViewModel.PenCompare=圈分析－对比
EmailReportViewModel.PenDensity=圈密度
EmailReportViewModel.PenInputs=圈分析－输入
EmailReportViewModel.PenResults=圈分析－结果
EmailReportViewModel.PenTimeTitle=圈时间安排
EmailReportViewModel.PileAndBunkerTitle=堆贮和窖贮 
EmailReportViewModel.PileBunkerCapacities=青贮窖容量
EmailReportViewModel.ProductivityItem=生产工具
EmailReportViewModel.Provimi=普罗维美
EmailReportViewModel.ProvimiUS=Provimi US
EmailReportViewModel.Purina=普瑞纳
EmailReportViewModel.Resources=资源
EmailReportViewModel.Results=结果
EmailReportViewModel.RumenHealthBodyConditionTitle=体况评分
EmailReportViewModel.RumenHealthLocomotionTitle=蹄部行走评分
EmailReportViewModel.RumenHealthManureTitle=瘤胃健康粪便评分
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=代谢病发病率 
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk
EmailReportViewModel.RumenHealthTMRTitle=瘤胃健康TMR颗粒度评分
EmailReportViewModel.RumenHealthTitle=瘤胃健康反刍
EmailReportViewModel.RumenHealthUrinePHTitle=尿液pH值
EmailReportViewModel.ScoreScreen=评分
EmailReportViewModel.TMRParticleScoreTitle=瘤胃健康颗粒度评分
EmailReportViewModel.TimeBudget=时间安排
EmailReportViewModel.Title=报告发邮件
EmailReportViewModel.UrinePhSTDDEV=标准误差
EmailReportViewModel.UserPreferences=用户设置
EmailReportViewModel.UserSettings=用户设置
EmailReportViewModel.WalkthroughReportTitle=Walkthrough报告
EnergyImperial=兆卡/磅
EnergyMetric=兆卡/公斤
Enna=埃纳
Equatorial_Guinea=赤道几内亚
Eritrea=厄立特里亚
ErrorDescription=在阅读或书写你的信息时发生错误
ErrorTitle=错误
Espírito_Santo=圣埃斯皮里图州
Estonia=爱沙尼亚
Ethiopia=埃塞俄比亚
Eula=最终用户许可协议
Euro=欧盟国家 (€ EUR)
Event=事件
EveryOtherDay=每隔一天
Excessive=&gt;0.5个体况评分单位
ExtraDaysOpenCostInfoMessage=空怀天数每增加一天，成本增加3美金至5美金
FAQDairyEnteligenFarmReportandDDW=FAQ奶牛云系统牧场报告
Falkland_Islands_(Malvinas)=福克兰群岛（马尔维纳斯）
FarOff=干奶前期
FarOffDry=干奶前期
Faroe_Islands=法罗群岛
Federal_District=联邦区
FeedFirst=先进
FeedOut=饲料消耗
FeedOutRateInfo=饲喂速度信息
FeedOutRatesFilmsStorageSysExamined=饲喂速度，窖面和贮存系统检查
FeedOutSurfaceAreaImperial=饲喂表面积（平方英尺）
FeedOutSurfaceAreaMetric=饲喂表面积（平方米）
FeedingRate=饲喂速度（鲜基基础/头）
FeedoutLossesForageStorageSys=粗饲料贮存系统造成的饲喂损失
FermentationAnalysisSilageQT=发酵分析和青贮质量测试
Fermo=停了下来
Ferrara=费拉拉
FieldKPTest=牧场KP测试
Fiji=斐济
FillAllFields=请填写所有空白
FillAllMandatoryFields=请填写必填空白
FinalObservations=最后的观察
Finish=完成
Finland=芬兰
Florence=佛罗伦萨
Florida=佛罗里达
Foggia=雾
ForageAuditScorecard=粗饲料评估卡
ForageAuditScorecardResponsesViewModel.ImprovementsTab=粗饲料评估提高
ForageAuditScorecardResponsesViewModel.ResponsesTab=粗饲料评估反应
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=提高
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=反应
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=得分
ForageAuditScorecardResultsViewModel.ImprovementsTab=提高
ForageAuditScorecardResultsViewModel.ResponsesTab=反应
ForageAuditScorecardResultsViewModel.ScoreTab=得分
ForageAuditScorecardResultsViewModel.Title=青贮塔
ForageAuditScorecardResultsViewModel.VisitNotebook=查阅记录本
ForageAuditScorecardScoreViewModel.GoodIndicator=好
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=提高
ForageAuditScorecardScoreViewModel.OverallForageScore=包含所有的粗饲料评分
ForageAuditScorecardScoreViewModel.Title=粗饲料评估总体评分
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=粗饲料青贮类型
ForageAuditViewModel.ForageAuditScorecard=粗饲料评估得分卡
ForageAuditViewModel.ForageDetail=粗饲料质量是奶牛营养的基础，是牧场总盈利的关键。粗饲料评估得分卡，可以用于评估目前牧场粗饲料管理水平。得分卡很好的评估了牧场关键的管理点。你可以在一次拜访中，检查所有的方面，也可以检查当时比较重要的方面。我们也可以利用其他的管理资源，去帮助可能有机会提高的方面
ForageAuditViewModel.ForageHeading=基础粗饲料信息
ForageAuditViewModel.Resources=资源
ForageAuditViewModel.Title=粗饲料评估
ForageAuditViewModel.VisitNotebook=查阅记录本
ForageAudit_Sample_ToolTip=工具提示的示例文本。实际可用时删除
ForageManagement_ForagesHarvestedAtProperMaturity=粗饲料是否针对作物类型和存储设施在适当的成熟期收获？
ForageManagement_ForagesHarvestedAtProperMoisture=粗饲料是否针对作物类型和存储设施在适当的含水量时收获？
ForageScorecardResultsViewModel.Title=裹包青贮
ForageScorecardViewModel.Baleage=裹包青贮
ForageScorecardViewModel.BunkersAndPiles=青贮窖和青贮堆
ForageScorecardViewModel.ForageAuditCategories=粗饲料审计类型
ForageScorecardViewModel.ForageAuditScore=粗饲料审计评分
ForageScorecardViewModel.ForageAuditScorecard=粗饲料审计评分卡
ForageScorecardViewModel.ForageCategoryTooltip=粗饲料质量是所有牧场营养方案的基础，也是牧场整体盈利能力的关键。该工具可用于评估牧场当前使用的粗饲料管理方法，并对需要改进的关键机会领域提出建议。
ForageScorecardViewModel.Harvest=收割
ForageScorecardViewModel.MaintainingForageQuality=维持粗饲料质量
ForageScorecardViewModel.No=否
ForageScorecardViewModel.SilageBags=青贮袋
ForageScorecardViewModel.SurveyCategories=粗饲料评估得分卡
ForageScorecardViewModel.SurveyOfForages=粗饲料调查
ForageScorecardViewModel.Title=粗饲料评估得分卡
ForageScorecardViewModel.TowerSilos=青贮塔
ForageScorecardViewModel.ViewOverallForageScore=审阅所有粗饲料得分
ForageScorecardViewModel.VisitNotebook=查阅记录本
ForageScorecardViewModel.Yes=是
ForlÃ¬-Cesena=福尔切塞纳
FourScreenNew=4层筛新
FourScreenNewType=（4毫米）
FourScreenOld=4层筛旧
FourScreenOldType=（1.18毫米）
FourToSevenDays=4至7天
France=法国
FreeFlow=自由流动
FreeFormReportViewModel.CalfHeiferItem=犊牛和青年牛
FreeFormReportViewModel.CalfHeiferScorecard=评分卡
FreeFormReportViewModel.Cargill=嘉吉
FreeFormReportViewModel.Charts=图表
FreeFormReportViewModel.ComfortHeatStressBanner=热应激工具圈
FreeFormReportViewModel.ComfortItem=舒适度工具
FreeFormReportViewModel.ExportSelected=导出已选择的工具
FreeFormReportViewModel.GeneralNotes=总体拜访备注
FreeFormReportViewModel.HealthItem=健康工具
FreeFormReportViewModel.Inputs=输入
FreeFormReportViewModel.KeyBenchmarks=关键基准
FreeFormReportViewModel.MarketingBranding=Go to Market商标
FreeFormReportViewModel.MilkProcessCalcInputsTab=挤奶程序收益：输入
FreeFormReportViewModel.MilkProcessCalcResourcesTab=挤奶程序收益：资源
FreeFormReportViewModel.MilkProcessCalcResultsTab=挤奶程序收益：结果
FreeFormReportViewModel.MilkProcessRevenueCalculator=挤奶程序收益：计算器
FreeFormReportViewModel.MilkSoldEvaluation=出售奶评估
FreeFormReportViewModel.Notes=备注
FreeFormReportViewModel.NutritionForage=粗饲料评估
FreeFormReportViewModel.NutritionItem=营养工具
FreeFormReportViewModel.NutritionPile=堆贮和窖贮的容量
FreeFormReportViewModel.Outputs=输出
FreeFormReportViewModel.OverallImprovements=整体提高
FreeFormReportViewModel.OverallResponses=整体反应
FreeFormReportViewModel.OverallScore=整体得分
FreeFormReportViewModel.PenTimeTitle=圈时间安排
FreeFormReportViewModel.PileAndBunkerFeedOutTab=堆贮和窖贮喂出
FreeFormReportViewModel.ProductivityItem=生产工具
FreeFormReportViewModel.Provimi=普洛维美
FreeFormReportViewModel.ProvimiUS=Provimi US
FreeFormReportViewModel.Purina=普瑞纳
FreeFormReportViewModel.Results=结果
FreeFormReportViewModel.RumenHealthBodyConditionTitle=体况评分
FreeFormReportViewModel.RumenHealthLocomotionTitle=蹄部运动评分
FreeFormReportViewModel.RumenHealthManureTitle=瘤胃健康粪便评分
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=代谢病发病率 
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk
FreeFormReportViewModel.RumenHealthTMRHerdTitle=瘤胃健康TMR颗粒度全群评分
FreeFormReportViewModel.RumenHealthTMRTitle=瘤胃健康TMR颗粒度评分
FreeFormReportViewModel.RumenHealthTitle=瘤胃健康反刍
FreeFormReportViewModel.RumenHealthUrinePHTitle=尿液pH值
FreeFormReportViewModel.Title=自由格式报告
FreeFormReportViewModel.VisitTitle=拜访名字
FreeFormReportViewModel.WalkThroughNotes=Walk through 备注
FreeHandNoteClearPaletteDialogMessage=你是否想要删除屏幕上所有的信息？
FreeHandNoteEditorPageTitle=笔记本
FreeHandNoteSaveUserDialogMessage=你是否想要保存备注？
FreeHandNotesViewModel.Save=保存
Freestall=Freestall
French_Guiana=法属圭亚那
French_Polynesia=法属波利尼西亚
French_Southern_Territories=法国南部地区
Fresh=新产
FreshCow=新产牛
FreshHeifer=头胎新产牛
Frosinone=弗罗西诺内
Fujian=福建
GBP=英国 (GBP GBP)
GEA=GEA
GTQ=危地马拉 (Q GTQ)
Gabon=加蓬
Galway=戈尔韦
Gambia=冈比亚
Gansu=甘努
General=总体
Genoa=热那亚
Georgia=乔治亚州
Germany=德国
GettingtheMostOutofYourForage=最好地利用你的粗饲料
Ghana=加纳
Gibraltar=直布罗陀
Girolando=Girolando
Global=全球的
Goa=果阿
Goal=目标
Goals=目标
Goiás=戈亚斯州
Good=Good
Gorizia=戈里兹亚
GreaterThan8Hours=大于8小时
GreaterThanFive=大于5
GreaterThanSevenDays=大于7天
GreaterThanSixHours=大于6小时
GreaterThanThirtySixInchesPerDay=每天大于36英寸
GreaterThanTwelveHours=大于12小时
GreaterThanTwenty=&gt;20
Greece=希腊
Greenland=格陵兰
Grenada=格林纳达
Grosseto=格罗塞托
GrowerPubertyPregnancyCloseup=育成，青春期，怀孕，围产
GrowerPubertyPregnancyCloseup_CleanAndDryPen=清洁和干燥牛圈
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=落膝测试检测该区域是否干净干燥
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=成熟时达到理想的体况
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=腹泻的迹象
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=饲槽每日清洁，移除剩料
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=自由选择，有充足干净的水
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=没有迹象表明水污染
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=圈内青年牛体型均衡
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=同一圈动物体型大小相似
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=过度拥挤的比例%
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=以生长目标平衡日粮,时常回顾
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=每头青年牛采食空间充足
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg 需要 30cm, 270-400kg 需要 38cm, &gt;400 需要 46cm
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=每头青年牛畜舍空间充足
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg 需要 4m2, 270-400kg 需要  5m2, &gt;400需要 7m2
Guadeloupe=瓜德罗普
Guam=关岛
Guanajuato=瓜纳华托
Guangdong=粤
Guangxi=广西
Guatemala=危地马拉
Guernsey=根西岛
Guerrero=格雷罗
Guinea=几内亚
Guinea-Bissau=几内亚 - 比索
Guizhou=瓜苏
Gujarat=古吉拉特邦
Guyana=圭亚那
HKD=HKD
HNL=洪都拉斯 (HNL HNL)
HRK=HRK
HUF=匈牙利 (Ft HUF)
Hainan=海南
Haiti=海地
HalfPointScale=0.5分制
Harvest=收割
Harvest_AdequateEquipmentAndLabor=足够的设备和劳动力去收割牧草青贮作物
Harvest_CornSilageMoistureRangeConsistent=90%的青贮样品水平在下列哪个范围内？
Harvest_ForageHarvestingDocumented=粗饲料的收割条件，地段和储存位置都有记录吗？
Harvest_ForagesHarvestedAtProper=针对不同作物的类型和贮存方式，是否根据成熟度和水分含量，做到适时收割？
Harvest_KPScoreIsMonitored=使用900克青贮样品或浸泡漂浮方法进行玉米籽实加工评分的结果是？
Harvest_LengthOfCutMonitored=切割长度是否使用了滨州筛进行分析？
Harvest_UseSilageAdditive=使用青贮添加剂：接种剂或厌氧稳定剂
Harvest_WholePlantMoistureDetermined=每块地的整株作物的水分都有检测吗？
Haryana=哈里亚纳邦
Hawaii=夏威夷
Haylage=草贮
HealthToolsViewModel.HealthHeading=请在下拉栏中选择一项工具
HealthToolsViewModel.HealthToolsList=工具
HealthToolsViewModel.RumenHealthBodyConditionTitle=体况评分
HealthToolsViewModel.RumenHealthLocomotionTitle=蹄部运动评分
HealthToolsViewModel.RumenHealthManureScreening=瘤胃健康粪便筛
HealthToolsViewModel.RumenHealthManureTitle=瘤胃健康粪便评分
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=代谢病发病率 
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk
HealthToolsViewModel.RumenHealthTMRTitle=瘤胃健康TMR颗粒度评分
HealthToolsViewModel.RumenHealthTitle=瘤胃健康反刍
HealthToolsViewModel.RumenHealthUrinePHTitle=尿液pH值
HealthToolsViewModel.Title=健康工具
Heard_Island_and_McDonald_Islands=听到的岛和麦当劳群岛
HeatstressCalculations=热应激计算
HeatstressChart=热应激图表
HeatstressChartViewModel.DMIReduction=干物质采食量下降
HeatstressChartViewModel.EnergyEquivMilkLoss=能量当量牛奶损失
HeatstressChartViewModel.EstimateDryMatter=估测干物质采食量
HeatstressChartViewModel.HeatstressEvalLabel=温度校正为平均温度和相对湿度（没有阳光）
HeatstressChartViewModel.IntakeAdjustment=采食量校正
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=摄入能量的损失
HeatstressChartViewModel.Mcal=Mcal
HeatstressChartViewModel.MilkValueLossPerDay=牛奶价格损失/天
HeatstressChartViewModel.MilkValueLossPerMonth=牛奶价格损失/月
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=磅（0.45 kg）
HeatstressChartViewModel.ReductionDMI=干物质采食量下降
HeatstressChartViewModel.TempHumidIndex=温湿度指数（THI）
HeatstressChartViewModel.TemperatureImperial=°F（华氏）
HeatstressChartViewModel.TemperatureMetric=°C（摄氏）
HeatstressChartViewModel.VisitNotebook=访问笔记本
HeatstressData=热应激数据
HeatstressDataEntryViewModel.AnimalInputs=动物信息输入
HeatstressDataEntryViewModel.CurrentMilkPrice=当前牛奶价格 ({0}/{1})
HeatstressDataEntryViewModel.DMI=干物质采食量 ({0})
HeatstressDataEntryViewModel.Exposure=阳光直射
HeatstressDataEntryViewModel.HoursExposed=白天时间
HeatstressDataEntryViewModel.Humidity=湿度（%）
HeatstressDataEntryViewModel.LactatingAnimals=泌乳牛
HeatstressDataEntryViewModel.Milk=牛奶 ({0})
HeatstressDataEntryViewModel.MilkFat=乳脂（%）
HeatstressDataEntryViewModel.MilkProtein=乳蛋白（%）
HeatstressDataEntryViewModel.NEL=NEL (Mcal/{0})
HeatstressDataEntryViewModel.Temperature=温度 ({0})
HeatstressDataEntryViewModel.VisitNotebook=访问笔记本
HeatstressDataEntryViewModel.Weather=天气
HeatstressGreen=应激阈值
HeatstressOrange=中度－重度应激
HeatstressRed=重度应激
HeatstressTableViewModel.HeatstressChartTab=图表
HeatstressTableViewModel.HeatstressDataTab=数据输入
HeatstressTableViewModel.Title=热应激评估
HeatstressTableViewModel.VisitNotebook=访问笔记本
HeatstressYellow=轻度－中度应激
Hebei=河北
Heifer=青年牛
Heilongjiang=海伦吉安
Henan=河南
HerdAnalysisGoalsViewModel.CloseUpDry=围产前期
HerdAnalysisGoalsViewModel.CudChewingGoals=反刍咀嚼目标
HerdAnalysisGoalsViewModel.CudChews=反刍咀嚼
HerdAnalysisGoalsViewModel.DIM=泌乳天数
HerdAnalysisGoalsViewModel.EarlyLactation=泌乳早期
HerdAnalysisGoalsViewModel.FarOffDry=干奶早期
HerdAnalysisGoalsViewModel.Fresh=新产
HerdAnalysisGoalsViewModel.LateLactation=泌乳末期
HerdAnalysisGoalsViewModel.MidLactation=泌乳中期
HerdAnalysisGoalsViewModel.PeakMilk=泌乳高峰期
HerdAnalysisGoalsViewModel.PercentChewing=%咀嚼
HerdAnalysisGoalsViewModel.Title=牛群分析
HerdAnalysisGoalsViewModel.To=到
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=瘤胃健康反刍咀嚼
HerdAnalysisMasterViewModel.HerdAnalysisHeading=牛群分析
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=牛群分析
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=目标
HerdAnalysisMasterViewModel.Title=瘤胃健康反刍咀嚼
HerdAnalysisTableTitle=反刍咀嚼评分分析
HerdAnalysisViewModel.AverageChews=每口平均咀嚼次数
HerdAnalysisViewModel.DaysInMilk=泌乳天数
HerdAnalysisViewModel.Edit=编辑
HerdAnalysisViewModel.EditLabel=编辑
HerdAnalysisViewModel.HerdAnalysisTableTitle=反刍咀嚼评分分析
HerdAnalysisViewModel.HerdCudChewing=牛群反刍比例%
HerdAnalysisViewModel.NoOfCows=完成你已经开始的所有圈舍的分析。
HerdAnalysisViewModel.NumberofChewsPerCud=每口咀嚼次数
HerdAnalysisViewModel.PenNameLabel=圈舍名称
HerdAnalysisViewModel.PercentChewing=反刍咀嚼百分比
HerdAnalysisViewModel.TableTitle=反刍咀嚼评分分析
HerdAverage=牛群平均（%）
HerdGoal=牛群目标（%）
HerdInformation=牛群信息
HerdReporting=牛群报告：反刍咀嚼
Hidalgo=伊达尔戈
Himachal_Pradesh=喜马al尔邦
Hokkaido=北海道
Holandesa=Holandesa
Holy_See_(Vatican_City_State)=罗马教廷（梵蒂冈州立国家）
HomeViewModel.AutoSync=自动同步
HomeViewModel.ConsumersTab=消费者
HomeViewModel.CustomersTab=客户
HomeViewModel.DashboardTab=控制面板
HomeViewModel.Eula=最终用户许可协议
HomeViewModel.Logout=退出
HomeViewModel.PrivacyStatement=隐私声明
HomeViewModel.ProspectsTab=目标客户
HomeViewModel.Settings=设置
HomeViewModel.SyncWithDash=同步－
HomeViewModel.SyncWithDate=同步－上次同步日期： {0\:月/日/年}
HomeViewModel.SyncWithTime=同步－上次同步时间： {0\:时\:分 tt}
HomeViewModel.Title=现场访问工具
Honduras=洪都拉斯
Hong_Kong=香港
HowtoGetBetterKPResults=如何获得更好的KP结果？
Hubei=湖北
Hunan=自己
Hungary=匈牙利
IDR=印度尼西亚 (Rp IDR)
INR=印度 (INR INR)
Iceland=冰岛
Idaho=爱达荷州
Illinois=伊利诺伊州
Imperia=帝国
Imperial=英制
Improvements=改进
India=印度
Indiana=印第安纳州
Indonesia=印度尼西亚
InoculantFQAs=接种物常见问题解答
Iowa=爱荷华州
Iran,_Islamic_Republic_of=伊朗，伊斯兰共和国
Iraq=伊拉克
Ireland=爱尔兰
Isernia=伊塞尔尼亚
Isle_of_Man=人岛
Israel=以色列
Italy=意大利
JOD=JOD
JPY=JPY
Jalisco=哈利斯科
Jamaica=牙买加
Jammu_and_Kashmir=查mu和克什米尔
Japan=日本
Jersey=Jersey
Jharkhand=贾坎德邦
Jiangsu=江苏
Jiangxi=江西
Jilin=吉林
Jordan=约旦
KBLastPhase=维持准确的生长和健康记录
KRW=韩国 (₩ KRW)
Kansas=堪萨斯州
Karnataka=卡纳塔克邦
Kazakhstan=哈萨克斯坦
Kentucky=肯塔基
Kenya=肯尼亚
Kerala=喀拉拉邦
Kerry=克里
Ketosis=酮病
KeyBenchmarks=关键基准
KeyBenchmarks_AgeInMonthAtFirstCalving=第一次产犊月龄
KeyBenchmarks_CalvingAndHeiferReocrd=产犊和青年牛记录保持持续利用
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=90日龄达到成熟体重的15%
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=怀孕时达到成熟体重的55%
KeyBenchmarks_HeiferPeakProduce=青年牛泌乳高峰达到全群的比例%
KeyBenchmarks_NintyDaysMorbidityf=90日发病率
KeyBenchmarks_NintyDaysMortality=90日死亡率
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=产犊前达到成熟体重94%
KeyBenchmarks_PercentOfHeifersPregnant=青年牛15月龄怀孕比例
KeyBenchmarks_SerumlgG=产后48小时IgG (g/L) 
Kildare=基尔代尔
Kilkenny=基尔肯尼
Kiribati=基里巴斯
Korea=韩国
Korea,_Democratic_People's_Republic_of=韩国，民主人民共和国
Korea,_Republic_of=韩国，共和国
Kuwait=科威特
Kyrgyzstan=吉尔吉斯斯坦
L'Aquila=拉奎拉
LKR=LKR
La_Spezia=香料
Lactating=泌乳
Lactation=泌乳牛
Lakshadweep=拉克沙群岛
Lao_People's_Democratic_Republic=老挝人民民主共和国
Laois=老挝
Last_Synced=上次同步
LateLactation=泌乳末期
Latina=拉丁
Latvia=拉脱维亚
Lebanon=黎巴嫩
Lecce=莱切
Lecco=莱克科
Leitrim=利特里姆
Lely=Lely
Length-exceed-allowed-limit= 长度超过允许的限制
LengthPerDayImperial=英寸/天
LengthPerDayMetric=厘米/天
Lesotho=莱索托
LessThan4Days=少于4天
LessThanFifteen=&lt;15 (颗粒硬度)
LessThanFiveWholeKernals=少于5颗整理玉米
LessThanOneHour=少于1小时
LessThanSixInches=少于6英寸
LessThanSixLayers=少于6层
LessThanTwentFourInchesPerDay=每天少于24英寸
Liaoning=骗子
Liberia=利比里亚
Libyan_Arab_Jamahiriya=阿拉伯利比亚民众国
Liechtenstein=列支敦士登
Lift-Sync-Fail=Lift同步失败
Limerick=利默里克
LinkToPens=链接到圈舍（可选）
Lithuania=立陶宛
Livorno=利沃诺
LocoCategory1=得分1
LocoCategory2=得分2
LocoCategory3=得分3
LocoCategory4=得分4
LocoCategory5=得分5
LocomotionEditTableViewModel.Category1=运动评分1.0
LocomotionEditTableViewModel.Category2=运动评分2.0
LocomotionEditTableViewModel.Category3=运动评分3.0
LocomotionEditTableViewModel.Category4=运动评分4.0
LocomotionEditTableViewModel.Category5=运动评分5.0
LocomotionEditTableViewModel.EnterNumberOfCows=请计算牛的数量。
LocomotionEditTableViewModel.Title=牛头数
LocomotionHerdEditGoalViewModel.Category1=1
LocomotionHerdEditGoalViewModel.Category2=2
LocomotionHerdEditGoalViewModel.Category3=3
LocomotionHerdEditGoalViewModel.Category4=4
LocomotionHerdEditGoalViewModel.Category5=5
LocomotionHerdEditGoalViewModel.HerdGoal=牛群目标
LocomotionHerdEditGoalViewModel.Title=编辑目标数量
LocomotionHerdInputsViewModel.Herd=牛群
LocomotionHerdMasterViewModel.Inputs=输入
LocomotionHerdMasterViewModel.Results=结果
LocomotionHerdMasterViewModel.Revenue=收益
LocomotionHerdMasterViewModel.SubHeading=牛群分析
LocomotionHerdMasterViewModel.Title=运动
LocomotionHerdResultsViewModel.Category1=得分1
LocomotionHerdResultsViewModel.Category2=得分2
LocomotionHerdResultsViewModel.Category3=得分3
LocomotionHerdResultsViewModel.Category4=得分4
LocomotionHerdResultsViewModel.Category5=得分5
LocomotionHerdResultsViewModel.HerdAverage=牛群平均
LocomotionHerdResultsViewModel.HerdGoal=目标
LocomotionHerdResultsViewModel.Title=运动评分分析
LocomotionHerdRevenueViewModel.Revenue=收益
LocomotionHerdRevenueViewModel.Title=运动-牛群收益
LocomotionNumberinHerd=运动（牛群中数量）
LocomotionNumberinPen=运动（圈舍中数量）
LocomotionPenInputsViewModel.FromPenSetup=源自圈舍设置
LocomotionPenInputsViewModel.Milk=牛奶
LocomotionPenMasterViewModel.Inputs=输入
LocomotionPenMasterViewModel.Results=结果
LocomotionPenMasterViewModel.Title=运动
LocomotionPercentofHerd=运动（牛群%）
LocomotionPercentofPen=运动（圈舍%）
LocomotionPreviousVisitsViewModel.AverageScore=平均分数
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=平均分数
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=日期
LocomotionPreviousVisitsViewModel.PercentPen=圈舍百分比（%）
LocomotionPreviousVisitsViewModel.SelectedDates=选择日期
LocomotionPreviousVisitsViewModel.Title=运动评分结果
LocomotionScore=运动评分
LocomotionScoreAverage=平均运动评分
LocomotionScoreHerd=运动评分分析
LocomotionScoreReference=运动评分参考
LocomotionSelectPenViewModel.MissingDiet=请为此圈输入一个有效的名称
LocomotionSelectPenViewModel.PenSelectionList=圈舍
LocomotionSelectPenViewModel.Title=运动
Lodi=相反
LoginViewModel.Copyright=© {0} Cargill, Incorporated. All Rights Reserved.
LoginViewModel.EmailLabel=电子邮箱
LoginViewModel.ErrorDescription=用户名和密码不能为空。
LoginViewModel.ErrorTitle=登录错误
LoginViewModel.InvalidMessage=
LoginViewModel.InvalidMessageTitle=无效登录
LoginViewModel.LoginPrompt=登录
LoginViewModel.LoginPromptConsumer=其他登录
LoginViewModel.NetworkErrorMessage=目前没有可用的网络。
LoginViewModel.NetworkErrorMessageTitle=网络错误
LoginViewModel.PasswordLabel=密码
LoginViewModel.Title=登录
LoginViewModel.Unauthorized=未经授权的访问
LoginViewModel.UnauthorizedTitle=未经授权
Longford=朗福德
Louisiana=路易斯安那州
Louth=劳
LowForage=低粗饲料
Lucca=卢卡
Luxembourg=卢森堡
MEQ100G=mEq/100g
MKD=MKD
MUN=MUN (mg/dL)
MXN=墨西哥 (PESO MXN)
MYR=马来西亚 (MYR MYR)
Macao=澳门
Macedonia,_the_former_Yugoslav_Republic_of=马其顿，前南斯拉夫共和国
Macerata=押金
Madagascar=马达加斯加
Madhya_Pradesh=中央邦
Maharashtra=马哈拉施特拉邦
MainViewModel.EmailLabel=电子邮箱
Maine=缅因州
MaintainingForageQuality=维持粗饲料质量
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=额外加分：在炎热/潮湿的天气中TMR是否使用稳定剂/霉菌抑制剂？
MaintainingForageQuality_TMRMixHasPleasantAroma=TMR日粮是否有令人愉快的香气？
MaintainingForageQuality_TMRMixIsCoolToTouch=TMR日粮触摸起来是否凉爽？
Making_Feed_InventoryFOF=饲料库存记录
Malawi=火焰
Malaysia=马来西亚
Maldives=马尔代夫
Male=公
Mali=必须
Malta=马耳他
ManagingForageinSiloBags=管理青贮包中的粗饲料
ManagingForageinTowerSilos=管理青贮料塔中的粗饲料
Manipur=曼尼普尔
Manitoba=曼尼托巴省
Mantua=曼图亚
ManureEditScores=粪便评分－编辑评分
ManureScoreHerdAnalysisEditInputsViewModel.Close=关闭
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=泌乳天数
ManureScoreHerdAnalysisEditInputsViewModel.Title=编辑泌乳天数
ManureScoreHerdAnalysisInputsViewModel.ManureScore=粪便评分
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=粪便评分分析
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=泌乳天数
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=编辑
ManureScoreHerdAnalysisMasterViewModel.Goals=目标
ManureScoreHerdAnalysisMasterViewModel.Inputs=输入
ManureScoreHerdAnalysisMasterViewModel.Results=结果
ManureScoreHerdAnalysisMasterViewModel.SubHeading=牛群分析
ManureScoreHerdAnalysisMasterViewModel.Title=瘤胃健康粪便评分
ManureScoreHerdAnalysisMasterViewModel.VisitNotebook=笔记本
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=粪便评分分析
ManureScoreHerdAnalysisResultsViewModel.ManureScore=粪便评分
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=平均
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=最高评分
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=最低评分
ManureScoreHerdEditGoalsViewModel.CloseUpDry=围产前期（-20到-1天）
ManureScoreHerdEditGoalsViewModel.EarlyLactation=泌乳早期（16-60天）
ManureScoreHerdEditGoalsViewModel.EditDatesClose=关闭
ManureScoreHerdEditGoalsViewModel.FarOffDry=干奶前期（早于-21天）
ManureScoreHerdEditGoalsViewModel.Fresh=新产（0-15天）
ManureScoreHerdEditGoalsViewModel.LateLactation=泌乳末期（大于201天）
ManureScoreHerdEditGoalsViewModel.MaxGoal=粪便评分最大值
ManureScoreHerdEditGoalsViewModel.MidLactation=泌乳中期（121-200天）
ManureScoreHerdEditGoalsViewModel.MinGoal=粪便评分最小值
ManureScoreHerdEditGoalsViewModel.PeakMilk=泌乳高峰期（61-120天）
ManureScoreHerdEditGoalsViewModel.Title=编辑目标
ManureScoreHerdGoalsViewModel.CloseUpDry=围产前期（-20--1天）
ManureScoreHerdGoalsViewModel.EarlyLactation=泌乳早期（16-60天）
ManureScoreHerdGoalsViewModel.Edit=编辑
ManureScoreHerdGoalsViewModel.FarOffDry=干奶前期（早于-21天）
ManureScoreHerdGoalsViewModel.Fresh=新产（0-15天）
ManureScoreHerdGoalsViewModel.GoalMaxTitle=粪便评分目标最大值
ManureScoreHerdGoalsViewModel.GoalMinTitle=粪便评分目标最小值
ManureScoreHerdGoalsViewModel.LateLactation=泌乳末期（大于201天）
ManureScoreHerdGoalsViewModel.MidLactation=泌乳中期（121到200天）
ManureScoreHerdGoalsViewModel.PeakMilk=泌乳高峰期（61-120天）
ManureScoreHerdGoalsViewModel.TableTitle=按泌乳阶段进行评分
ManureScorePenSelectionViewModel.ManureScoreTitle=瘤胃健康粪便评分
ManureScorePenSelectionViewModel.PenSelectionList=圈舍
ManureScorePercentOfPen=粪便评分（圈舍%）
ManureScoresChart=粪便评分－图表
ManureScoresResult=粪便评分－结果
Maranhão=马拉尼昂州
Martinique=马提尼克
Maryland=马里兰州
Massa_and_Carrara=马萨和卡拉拉
Massachusetts=马萨诸塞州
Matera=马特拉
Mato_Grosso=马托格罗索州
Mato_Grosso_do_Sul=南马托格罗索州
Mauritania=毛里塔尼亚
Mauritius=毛里求斯
Max=MAX
Mayo=梅奥
Mayotte=马约特岛
Mcal=Mcal
Meath=米斯
Medio_Campidano=梅迪奥·坎皮达诺
Medium=Medium
Meghalaya=梅加拉亚邦
MenuViewModel.Close=关闭
MenuViewModel.LogoutPrompt=退出
MenuViewModel.Menu=菜单
MenuViewModel.ResetDatabaseCancel=取消
MenuViewModel.ResetDatabasePrompt=现有数据（包括用户偏好）将替换为测试数据的默认值。访问工具，新创建的访问和乳企信息将丢失。你将会退出app登录。是否继续？
MenuViewModel.ResetDatabaseReset=重置
MenuViewModel.ResetDatabaseTitle=重置测试数据
MenuViewModel.Sync_PopUp=“正在同步您的数据..
Messina=墨西拿
MetabolicIncidenceChartsViewModel.Current=目前
MetabolicIncidenceChartsViewModel.DeathLoss=死亡损失
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=代谢疾病费用/头
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=真胃变位
MetabolicIncidenceChartsViewModel.Dystocia=难产
MetabolicIncidenceChartsViewModel.GoalPercent=目标（%）
MetabolicIncidenceChartsViewModel.GraphTitle=代谢疾病发病率%
MetabolicIncidenceChartsViewModel.IncidencePercent=发病率（%）
MetabolicIncidenceChartsViewModel.Ketosis=酮病
MetabolicIncidenceChartsViewModel.Metritis=子宫炎
MetabolicIncidenceChartsViewModel.MilkFever=产褥热
MetabolicIncidenceChartsViewModel.RetainedPlacenta=胎衣不下
MetabolicIncidenceChartsViewModel.Title=代谢疾病发病率图表
MetabolicIncidenceEditOutputsViewModel.Close=关闭
MetabolicIncidenceEditOutputsViewModel.DeathLoss=死亡损失
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=真胃变位
MetabolicIncidenceEditOutputsViewModel.Dystocia=难产
MetabolicIncidenceEditOutputsViewModel.Ketosis=酮病
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=目标（%）
MetabolicIncidenceEditOutputsViewModel.Metritis=子宫炎
MetabolicIncidenceEditOutputsViewModel.MilkFever=产褥热
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=胎衣不下
MetabolicIncidenceEditOutputsViewModel.Title=编辑目标
MetabolicIncidenceInputsEditViewModel.Close=关闭
MetabolicIncidenceInputsEditViewModel.DeathLoss=乳房炎
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=真胃变位
MetabolicIncidenceInputsEditViewModel.Dystocia=难产
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=空怀天数
MetabolicIncidenceInputsEditViewModel.Ketosis=酮病
MetabolicIncidenceInputsEditViewModel.Metritis=子宫炎
MetabolicIncidenceInputsEditViewModel.MilkCow=牛奶/头 ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=产褥热
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=胎衣不下
MetabolicIncidenceInputsEditViewModel.Title=编辑成本属性
MetabolicIncidenceInputsEditViewModel.TreatmentCost=治疗和其他费用（淘汰、死亡）
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=额外空怀天数的费用
MetabolicIncidenceInputsViewModel.Costs=费用
MetabolicIncidenceInputsViewModel.DeathLoss=死亡损失
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=真胃变位
MetabolicIncidenceInputsViewModel.Dystocia=难产
MetabolicIncidenceInputsViewModel.Herd=牛群信息
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=输入评估期间新产牛的数量和代谢疾病例数。这将在“输出”选项卡中转换为年度疾病费用。
MetabolicIncidenceInputsViewModel.IncidenceCases=代谢疾病发病例数
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=增加空怀天数
MetabolicIncidenceInputsViewModel.Ketosis=酮病
MetabolicIncidenceInputsViewModel.Mastitis=乳房炎
MetabolicIncidenceInputsViewModel.Metritis=子宫炎
MetabolicIncidenceInputsViewModel.MilkFever=产褥热
MetabolicIncidenceInputsViewModel.MilkLossKg=每个泌乳期的牛奶损失 ({0})
MetabolicIncidenceInputsViewModel.MilkPrice=奶价
MetabolicIncidenceInputsViewModel.PerformanceMessage=用于计算每种代谢疾病发病率经济影响的参考数据。
MetabolicIncidenceInputsViewModel.PerformanceTreatment=性能和治疗费用
MetabolicIncidenceInputsViewModel.ReplacementCowCost=后备牛费用
MetabolicIncidenceInputsViewModel.RetainedPlacenta=胎衣不下
MetabolicIncidenceInputsViewModel.Title=代谢疾病发病率输入
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=评估的新产牛总头数
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=新产牛总头数/年
MetabolicIncidenceInputsViewModel.TreatmentCost=治疗和其他费用（淘汰、死亡）
MetabolicIncidenceMasterViewModel.Charts=图表
MetabolicIncidenceMasterViewModel.Inputs=输入
MetabolicIncidenceMasterViewModel.Outputs=输出
MetabolicIncidenceMasterViewModel.Title=代谢疾病发病率
MetabolicIncidenceOutputsViewModel.DeathLoss=死亡损失
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=真胃变位
MetabolicIncidenceOutputsViewModel.Dystocia=难产
MetabolicIncidenceOutputsViewModel.Ketosis=酮病
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=发病率（%）
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=费用/头
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=增加空怀天数
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=差异（%）
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=编辑
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=目标（%）
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=高于牛群既定目标的代谢疾病发病率对经济的影响。
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=年度经济影响
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=年度经济影响－总
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=牛奶损失价值
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=代谢疾病发病率百分比
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=总费用
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=治疗和其他费用（淘汰、死亡）
MetabolicIncidenceOutputsViewModel.Metritis=子宫炎
MetabolicIncidenceOutputsViewModel.MilkFever=产褥热
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=胎衣不下
MetabolicIncidenceOutputsViewModel.Title=代谢疾病发病率输出
MetabolicIncidenceOutputsViewModel.TotalLosses=总年度损失
Metric=公制
MetricTonsAF=湿基吨数
MetricTonsAFSilo=鲜基吨数（窖里剩余）
MetricTonsDM=干基吨数
MetricTonsDMSilo=干物质吨数（窖里剩余）
Metritis=子宫炎
Mexico=墨西哥
Mexico_State=墨西哥州
Michigan=密歇根州
Michoacán=米却肯州
MidLactation=泌乳中期
MidOne=中层1
MidOneValue=(8mm)
MidTwo=中层2
Milan=米兰
Milk=牛奶 ({0})
MilkChange=牛奶变化 ({0})
MilkFever=产褥热
MilkLetDownResponse=下奶速度
MilkLossDay=牛奶损失 ({0}/天)
MilkLossGain=潜在牛奶损失/收益
MilkLossKg=牛奶损失 (kg)
MilkLossPounds=牛奶损失 (磅)
MilkLossYear=牛奶损失 ({0}/年)
MilkPrice=奶价({0}/{1})
MilkPricePremiums=奶价溢价
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=参考图表
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=输入
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=资源
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=结果
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=挤奶程序收益
MilkProcessRevenueCalculatorMasterViewModel.Title=挤奶程序收益
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=访问笔记本
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=乳房刺激不足
MilkProcessorEditComparisonValuesViewModel.MilkPrice=奶价({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=乳房没有刺激
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=适当的乳房刺激
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=情景1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=情景2
MilkProcessorEditComparisonValuesViewModel.Title=编辑比较数值
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT（100英磅）
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorInputViewModel.ComparisonValues=比较数值
MilkProcessorInputViewModel.Edit=编辑
MilkProcessorInputViewModel.MilkPrice=奶价({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=之前选择的乳企已被删除。请选择其他乳企继续。
MilkProcessorInputViewModel.ScenarioOne=情景1
MilkProcessorInputViewModel.ScenarioTwo=情景2
MilkProcessorInputViewModel.SelectProcessor=选择乳企
MilkProcessorInputViewModel.Title=乳企收益计算器输入
MilkProcessorInputViewModel.Title1=挤奶程序对比输入
MilkProcessorInputViewModel.WeightImperialCWT=CWT（100英磅）
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorResourcesViewModel.ApproxSCC=大约体细胞数 (细胞/ml)
MilkProcessorResourcesViewModel.LinearScore=线性评分
MilkProcessorResourcesViewModel.Mastitis=乳房炎牛奶损失 ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=参考图表
MilkProcessorResourcesViewModel.Title=挤奶程序收益－资源
MilkProcessorResultsViewModel.Change=改变
MilkProcessorResultsViewModel.HundredWeight=CWT（100英磅）
MilkProcessorResultsViewModel.MilkPrice=奶价 ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=年度价值变动
MilkProcessorResultsViewModel.ScenarioOne=情景1
MilkProcessorResultsViewModel.ScenarioTwo=情景2
MilkProcessorResultsViewModel.Title=挤奶程序收益－结果
MilkProcessorResultsViewModel.WeightImperialCWT=CWT（100英磅）
MilkProcessorResultsViewModel.WeightMetric=kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=乳成分
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=浓度
MilkProcessorSettingsMasterViewModel.MilkProcComponent=乳成分
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=浓度
MilkProcessorSettingsMasterViewModel.MilkProcNew=新的
MilkProcessorSettingsMasterViewModel.Title=乳企设置
MilkProcessorViewModel.Amount=数量 (1000细胞/ml)
MilkProcessorViewModel.AmountCFU=数量 (1000 cfu/ml)
MilkProcessorViewModel.BasePriceMilkFat=乳脂 ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=牛奶 ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=乳蛋白 ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=其他固体 ({0}/{1})
MilkProcessorViewModel.BasePrices=基础价
MilkProcessorViewModel.ComponentProcessor=以乳成分产量计价的乳企
MilkProcessorViewModel.ConcentrationProcessor=以乳成分浓度计价的乳企
MilkProcessorViewModel.Delete=删除
MilkProcessorViewModel.DeletePrompt=删除此乳企将使“挤奶程序收益计算器”工具中的结果无效。要删除此乳企吗？
MilkProcessorViewModel.HundredWeight=CWT（100英磅）
MilkProcessorViewModel.Name=名称
MilkProcessorViewModel.NameNotUnique=名称为"{0}"的乳成分乳企已存在。名称必须是唯一的。
MilkProcessorViewModel.NewComponentProcessorName=以乳成分产量计价的乳企 \#{0}
MilkProcessorViewModel.NewConcentrationProcessorName=以乳成分浓度计价的乳企 \#{0}
MilkProcessorViewModel.PricingMatrices=定价矩阵
MilkProcessorViewModel.SelectCurrency=选择货币
MilkProcessorViewModel.WeightImperial=磅（0.45kg）
MilkProcessorViewModel.WeightImperialCWT=CWT（100英磅）
MilkProcessorViewModel.WeightMetric=KG
MilkProduction=产奶量 ({0})
MilkProductionKg=产奶量（kg）
MilkProductionPounds=产奶量（磅）
MilkProductionRevenue=产奶收益
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=乳成分产量和效率
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=干物质采食量和饲料效率
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=乳脂%和乳蛋白%
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=产奶量和泌乳天数
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=体细胞数和牛奶尿素
MilkSoldEvaluationChartsListViewModel.VisitComparison=请选择要比较的拜访
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=乳成分效率
MilkSoldEvaluationChartsViewModel.ComponentYield=乳成分产量
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=乳成分产量和效率
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=干物质采食量和饲料效率
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=泌乳天数
MilkSoldEvaluationChartsViewModel.DryMatterIntake=干物质采食量
MilkSoldEvaluationChartsViewModel.FeedEfficiency=饲料效率
MilkSoldEvaluationChartsViewModel.MilkFat=乳脂%
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=乳脂%和乳蛋白%
MilkSoldEvaluationChartsViewModel.MilkProduction=产奶量
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=产奶量和泌乳天数
MilkSoldEvaluationChartsViewModel.MilkProtein=乳蛋白%
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=牛奶尿素
MilkSoldEvaluationChartsViewModel.SomanticCellCount=体细胞数
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=体细胞数和牛奶尿素
MilkSoldEvaluationChartsViewModel.Title=牛奶销售评估
MilkSoldEvaluationInputsViewModel.AddPickup=添加收奶
MilkSoldEvaluationInputsViewModel.AnimalsInTank=牛奶入罐的奶牛 ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=泌乳天数（DIM） ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=干物质采食量 ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=牛群信息
MilkSoldEvaluationInputsViewModel.LactatingAnimals=泌乳牛 ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=收奶 ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=乳企信息
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=牛奶尿素测量 ⃰
MilkSoldEvaluationMasterViewModel.AddNew=添加新的
MilkSoldEvaluationMasterViewModel.Charts=图表
MilkSoldEvaluationMasterViewModel.Inputs=输入
MilkSoldEvaluationMasterViewModel.Outputs=输出
MilkSoldEvaluationMasterViewModel.Title=牛奶销售评估
MilkSoldEvaluationOutputsViewModel.AvgBCC=平均细菌数 (1,000 cfu/mL)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=平均乳脂%
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=平均产奶量， {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=平均产奶量， {0} （牛奶入罐的奶牛）
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=平均乳蛋白%
MilkSoldEvaluationOutputsViewModel.AvgSCC=平均体细胞数 (1,000细胞/mL)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=乳成分效率（干物质采食量%）
MilkSoldEvaluationOutputsViewModel.EvaluationDays=评估天数
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=饲料效率（比例）
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=乳脂+乳蛋白产量 ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=乳脂产量 ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=乳蛋白产量 ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=更新地点设置
MilkSoldPickupViewModel.AnimalsInTank=牛奶入罐的奶牛 ⃰
MilkSoldPickupViewModel.BCC=细菌数 (1,000 cfu/mL)
MilkSoldPickupViewModel.DaysInTank=入罐天数 ⃰
MilkSoldPickupViewModel.MilkFat=乳脂%
MilkSoldPickupViewModel.MilkProtein=乳蛋白%
MilkSoldPickupViewModel.MilkSold=销售的牛奶，{0} ⃰
MilkSoldPickupViewModel.SCC=体细胞数 (1,000细胞/mL)
MilkSoldPickupViewModel.Title=编辑收奶{0}
MilkSoldSpinnerViewModel.Title=牛奶销售评估
MilkUrea=牛奶尿素 (mg/dL)
Milking=挤奶
MilkingFailure=挤奶失败
MilkingFirst=首先挤奶
MilkingProcessRevenueInputs=挤奶程序收益－输入
MilkingProcessRevenueResources=挤奶程序收益－资源
MilkingProcessRevenueResults=挤奶程序收益－结果
Minas_Gerais=米纳斯吉拉斯州
Minnesota=明尼苏达州
Mississippi=密西西比州
Missouri=密苏里州
Mizoram=米佐拉姆
Modena=莫德纳
Moderate=Moderate
ModeratelyClean=中级清洁度
Moldova,_Republic_of=摩尔多瓦共和国
Monaco=摩纳哥
Monaghan=莫纳汉
Mongolia=蒙古
Montana=蒙大拿
Montenegro=黑山
Monthly=每月
Montserrat=蒙特塞拉特
Monza_and_Brianza=蒙扎和布里安扎
MoreThan8LayersOfPlastic=多于8层塑料膜
Morelos=莫雷洛斯
Morocco=摩洛哥
Mozambique=莫桑比克
Myanmar=缅甸
NGN=NGN
NIO=尼加拉瓜 (NIO NIO)
NOK=NOK
Nagaland=纳加兰
Namibia=纳米比亚
Naples=那不勒斯
Nauru=瑙鲁
Nayarit=nayarit
Nebraska=内布拉斯加州
Nei_Mongol=内蒙古
Nepal=尼泊尔
Netherlands=荷兰
Nevada=内华达州
NewBunker=请输入新料仓的名称。
NewDietClassViewModel.Title=动物类别/子类别
NewDietPensViewModel.AssociatePens=此日粮可用于多个圈舍。
NewDietPensViewModel.Title=链接到圈舍
NewDietViewModel.Cancel=取消
NewDietViewModel.MainHeading=日粮名称 ⃰
NewDietViewModel.Save=保存
NewDietViewModel.Title=新日粮
NewPenDietViewModel.New=新的
NewPenDietViewModel.Title=日粮
NewPenViewModel.Animals=每圈牛头数
NewPenViewModel.AnimalsInputsPen=动物输入，圈舍
NewPenViewModel.AsFedIntake=湿基采食量 ({0})
NewPenViewModel.Barn=圈舍名称
NewPenViewModel.Cancel=取消
NewPenViewModel.DaysInMilk=泌乳天数
NewPenViewModel.Diet=日粮
NewPenViewModel.DietInputsPen=日粮输入，圈
NewPenViewModel.DryMatterIntake=干物质采食量
NewPenViewModel.FeedingSystem=饲喂系统
NewPenViewModel.General=总体
NewPenViewModel.HousingSystem=圈舍系统
NewPenViewModel.InfoNewPenDetails=在这一页添加或更新圈的细节数据。你也可以在你使用的工具中添加或更新数据。当创建一个新的圈时，以下信息是必须填写的：圈舍名称、日粮。圈舍系统和饲喂系统。圈舍建立数据将被Dairy Entelingen中下载的牧场数据自动更新。
NewPenViewModel.Milk=奶产量
NewPenViewModel.MilkingFrequency=挤奶频率
NewPenViewModel.NumberOfStalls=卧床数量
NewPenViewModel.OnlyOnePen=只有一个牛圈
NewPenViewModel.PenDetail=圈舍细节
NewPenViewModel.PenMapping=牛圈绘图
NewPenViewModel.PenName=圈舍名称
NewPenViewModel.PenSelection=牛圈选择
NewPenViewModel.PublishPenAlert=请发布你想要合并的牛舍相关所有访问信息。
NewPenViewModel.RationCostPerAnimal=每头日粮成本
NewPenViewModel.Save=保存
NewPenViewModel.Title=新建圈舍
NewPenViewModel.UserCreatedPen=用户建立牛圈
NewPile=请给新建的青贮堆命名
NewProspectViewModel.Address1=公司地址1
NewProspectViewModel.Address2=公司地址2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.BusinessName=公司名称
NewProspectViewModel.City=城市
NewProspectViewModel.ConsumerDetails=消费者详细信息
NewProspectViewModel.Country=乡镇
NewProspectViewModel.Customer=客户
NewProspectViewModel.CustomerDetail=客户详述
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=联系邮箱
NewProspectViewModel.EndUser=终端用户
NewProspectViewModel.FarmProducer=牧场场长
NewProspectViewModel.Image=单击编辑图片
NewProspectViewModel.InvalidEmail=请输入有效邮箱
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=这个客户的名称已经存在。客户名称不能重复
NewProspectViewModel.NameNotUniqueForConsumer=消费者名字 "{0}" 已经存在。名称必须惟一
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=-
NewProspectViewModel.NullBusinessName=必须填写公司名称
NewProspectViewModel.NullFirstName=必须填写名字
NewProspectViewModel.NullSecondName=必须填写姓氏
NewProspectViewModel.PostalCode=邮编
NewProspectViewModel.PrimaryContactFirstName=第一联系人名字
NewProspectViewModel.PrimaryContactLastName=第一联系人姓氏
NewProspectViewModel.PrimaryPhone=联系电话
NewProspectViewModel.Prospect=客户名称
NewProspectViewModel.ProspectDetail=客户详述
NewProspectViewModel.Segment=类型
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Spence=Spence
NewProspectViewModel.State=省
NewProspectViewModel.Title=详述
NewProspectViewModel.Type=类型
NewProspectViewModel.Walton=Walton
NewVisitViewModel.Title=拜访详情
New_Brunswick=新不伦瑞克省
New_Caledonia=新喀里多尼亚
New_Hampshire=新罕布什尔
New_Jersey=新泽西州
New_Mexico=新墨西哥
New_South_Wales=新南威尔士州
New_York=纽约
New_Zealand=新西兰
Newfoundland_and_Labrador=纽芬兰和拉布拉多
Next=下一个
Nicaragua=尼加拉瓜
Niedersachsen=下萨克森
Niger=尼日尔
Nigeria=尼日利亚
Ningxia=宁静
Niue=尼
No=否
No-User-Found= 找不到用户
NoResourcesAvailable=来源无效
NoResults=没有找到结果
NoWholeKernals=没有完整的籽实
Noah=Noah
None=没有
NoneSelected=没有选择
Nordrhein=北莱茵河
Norfolk_Island=诺福克岛
Normal=&lt;0.5 BCS 单位
NorthAmerica=北美
North_Carolina=北卡罗来纳
North_Dakota=北达科他州
Northern_Territory=北方领土
Northwest_Territories=西北地区
Norway=挪威
Not-matching-with-allowed-values= 与允许的值不匹配
NotMeasured=未测量
NotRemoved=未移除
NotSet=-
NoteCamcorderNotImplemented=摄像头没有被打开
NoteCategoryViewModel.FooterText=每一份笔记只能选择一种类型
NoteCategoryViewModel.SelectCategory=选择类型
NoteCategoryViewModel.Title=笔记
NotebookBCSHerdAnalysisGoals=牛群BCS分析目标值
NotebookBCSHerdAnalysisInputs=牛群BCS分析输入
NotebookBCSHerdAnalysisResults=牛群BCS分析结果
NotebookBCSSelectPointScale=体况选择点范围
NotebookBodyConditionEdit=体况编辑表
NotebookCudCalculators=瘤胃健康－反刍计算器
NotebookCudChewing=瘤胃健康－反刍
NotebookCudChewingDataEntry=瘤胃健康－反刍
NotebookCudChewingResults=瘤胃健康－反刍
NotebookLocomotionEditTable=运动评分－奶牛头数
NotebookLocomotionHerdInputs=运动评分－牛群分析输入
NotebookLocomotionHerdResults=运动评分－牛群分析结果
NotebookLocomotionHerdRevenue=运动评分－牛群分析收益
NotebookLocomotionLanding=运动评分－总评
NotebookLocomotionPenInputs=运动评分－圈舍分析输入
NotebookLocomotionPenResults=运动评分－圈舍分析结果
NotebookLocomotionPenSelection=运动评分－选择圈舍
NotebookManurePenSelection=粪便评分－选择圈舍
NotebookManureScoreHerdAnalysisGoals=牛群粪便评分－目标
NotebookManureScoreHerdAnalysisInputs=牛群粪便评分－输入
NotebookManureScoreHerdAnalysisResults=牛群粪便评分－结果
NotebookManureScoreLanding=粪便评分
NotebookMetabolicIncidenceCharts=代谢疾病图表
NotebookMetabolicIncidenceInputs=代谢疾病输入
NotebookMetabolicIncidenceOutputs=代谢疾病结果
NotebookParticleScoreHerdAnalysisEdit=牛群TMR颗粒度评分分析－编辑泌乳天数
NotebookParticleScoreLanding=TMR颗粒度评分
NotebookParticleScoreSelectPen=TMR颗粒度评分－选择圈舍
NotebookParticleScoreSelectScorer=TMR颗粒度评分－选择分数
NotebookPenTimeComparison=奶牛在圈时间预算－对比
NotebookPenTimeInputs=奶牛在圈时间预算－输入
NotebookPenTimePenSelection=奶牛在圈时间预算－选择圈舍
NotebookPenTimeResults=奶牛在圈时间预算－结果
NotebookReadyToMilkCharts=ReadyToMilk 图表
NotebookReadyToMilkInputs=ReadyToMilk 输入
NotebookReadyToMilkOutputs=ReadyToMilk 结果
NotebookRumenHealthNumberOfChewsInput=瘤胃健康－反刍数量输入
NotebookRumenHealthNumberOfChewsResults=瘤胃健康－反刍数量结果
NotebookRumenHealthTMRParticlePercent=筛子上的TMR比例
NotebookRumenHealthTMRParticleScore=TMR颗粒度评分
NotebookSectionComfortTools=舒适度工具
NotebookSectionForageAudit=粗饲料审计评分卡
NotebookSectionForageAuditBaleage=粗饲料审计－大包青贮
NotebookSectionForageAuditBunkersPiles=粗饲料审计－堆贮或窖贮
NotebookSectionForageAuditHarvest=粗饲料审计－收割
NotebookSectionForageAuditLanding=粗饲料审计
NotebookSectionForageAuditMaintainingQuality=粗饲料审计－保存粗饲料质量
NotebookSectionForageAuditSilageBags=粗饲料审计－裹包青贮
NotebookSectionForageAuditSurveyOfForages=粗饲料审计－粗饲料概述
NotebookSectionForageAuditTowerSilos=粗饲料审计－立式青贮塔
NotebookSectionHealthTools=健康工具
NotebookSectionHeatstressCalculations=热应激－计算器
NotebookSectionHeatstressChart=热应激－表格
NotebookSectionHeatstressData=热应激－数据
NotebookSectionHerdAnalysisGoals=瘤胃健康－牛群分析目标
NotebookSectionMilkSoldEvaluationCharts=牛奶销售评估图表
NotebookSectionMilkSoldEvaluationEditPickup=牛奶销售评估编辑奶车
NotebookSectionMilkSoldEvaluationInputs=牛奶销售评估输入
NotebookSectionMilkSoldEvaluationOutputs=牛奶销售评估输出
NotebookSectionNutritionTools=营养工具
NotebookSectionRumenHealthLanding=瘤胃健康
NotebookSectionVisit=访问
NotebookTMRParticleHerdAnalysisPenInputs=TMR颗粒度评分牛群分析－输入
NotebookTMRParticleHerdAnalysisPenResults=TMR颗粒度评分牛群分析－结果
NotebookUrinePHEditGoals=尿液pH值编辑目标
NotebookUrinePHInputs=尿液pH值输入
NotebookUrinePHOutputs=尿液pH值结果
NotebookVisitSummary=访问总结
NotebookWalkthroughReport=Walkthrough 报告
NotebookWalkthroughReportLanding=Walkthrough 报告
NotebookWalkthroughReportPen=Walkthrough 报告－圈舍分析
Nova_Scotia=新斯科舍省
Novara=诺瓦拉
Nuevo_León=新莱恩
Null-values-not-allowed= 不允许空值
NumChewsGoal=目标
NumOfCows=奶牛数量
NumberOfChewsReportsViewModel.Average=平均咀嚼次数
NumberOfChewsReportsViewModel.AverageChews=平均反刍
NumberOfChewsReportsViewModel.AverageNumberChews=平均咀嚼次数
NumberOfChewsReportsViewModel.DateDescription=日期
NumberOfChewsReportsViewModel.DatesComparison=比较的日期
NumberOfChewsReportsViewModel.EditVisits=选择类型
NumberOfChewsReportsViewModel.StdDevCalculated=标准差（计算值）
NumberOfChewsReportsViewModel.VisitDate=日期
NumberOfChewsViewModel.Count=计数
NumberOfChewsViewModel.CountHeader=请对这头奶牛的咀嚼次数进行计数。
NumberOfChewsViewModel.NextCow=下一头牛
NumberOfChewsViewModel.NumberOfChewsCow=反刍次数/牛号
NumberOfChewsViewModel.Title=反刍次数/牛号
NumberOfChewsViewModel.ValidCudInput=请输入有效值
NumberOfCows=奶牛数量
Nunavut=Nunavut
Nuoro=努奥罗
NutritionViewModel.NutritionForage=粗饲料审计
NutritionViewModel.NutritionLabel=访问前请先从下面的菜单中选择一个工具
NutritionViewModel.NutritionPile=青贮堆或是窖的存贮能力
NutritionViewModel.NutritionTools=营养工具
NutritionViewModel.NutritionToolsCaption=工具
NutritionViewModel.NutritionToolsInstructions=访问前请先从下面的菜单中选择一个工具
NutritionViewModel.NutritionToolsList=工具
NutritionViewModel.Title=营养工具
NutritionViewModel.VisitNotebook=访问笔记
OKLabel=OK
Oaxaca=奥沙卡
Observation=观察
Odisha=奥里萨邦
Offaly=外地
Ogliastra=奥利亚斯特拉
Ohio=俄亥俄州
Oklahoma=俄克拉荷拉
Olbia-Tempio=奥尔比亚-坦皮奥
Oman=己方
OncePerWeek=每周一次
One=一
OneHourBeforeActionIsDue=动作到期一小时
OneToSixHours=1～6小时
Ontario=安大略省
Opportunities=机会
Optimal=最佳答案
Oregon=俄勒冈州
Oristano=奥里斯塔诺
Other=其他
OtherSilage=其他青贮
Overall=总体
OverallCalfHeiferDetails=为了观察犊牛和青年牛得分，首先完成至少上表中一项调研
OverallForageScoreDetails=为查看总体粗饲料得分，需要先至少完成一项以下列表中的调查。
PDFDisclaimer=由于存在多种影响因素，嘉吉母公司及其分支机构并不能保证这些评估的精确性。对于产量和经济结果不作出保证。©{0}嘉吉公司。版权所有
PDFDisclaimer_ProvimiUS=Provimi North America，其母公司和关联公司不保证这些预测的准确性。无法保证生产或财务结果。©{0} Provimi North America. 版权所有
PDFPageNumber=第 {0}页，共 {1}页
PEN=秘鲁 (S/. PEN)
PHP=菲律宾 ($ PHP)
PLN=波兰 (zł PLN)
PMRConcentrate=PMR+浓缩料
PON=罗马尼亚 (lei PON)
Padua=帕多瓦
Pakistan=巴基斯坦
Palermo=巴勒莫
Palestinian_Territory,_Occupied=巴勒斯坦领土，被占领
Panama=巴拿马
Papua_New_Guinea=巴布亚新几内亚
Paraguay=巴拉圭
Paraná=帕拉诺
Paraíba=帕拉阿巴
Parlor=奶厅
Parma=帕尔马
ParticleScorePreviousVisitsViewModel.MidOne=中层
ParticleScorePreviousVisitsViewModel.MidOneValue=（8毫米）
ParticleScorePreviousVisitsViewModel.MidTwo=下层
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=每次比例（%）
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=中层（8mm）
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=下层（4mm）
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=上层（19mm）
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=底层
ParticleScorePreviousVisitsViewModel.SelectDates=选择日期
ParticleScorePreviousVisitsViewModel.Top=上层
ParticleScorePreviousVisitsViewModel.TopValue=（19毫米）
ParticleScorePreviousVisitsViewModel.Tray=底层
Pará=寄生虫
PasteurizedWholeMilk=巴氏杀菌常乳
Pasto=Pasto
Pasture=放牧
PastureOther=放牧+其他
Pavia=帕维亚
Pays=付款
PeakMilk=高峰奶量
PenDetailViewModel.Title=圈舍详述
PenListViewModel.DietSetup=新建日粮
PenListViewModel.InfoPenList=在对牧场访问时，圈舍要求使用圈舍水平的工具和Walkthrough报告。牧场中的圈舍名称和数据将被自动更新为奶牛云系统下载的数据。如果下载的牧场数据没有设置圈舍，需要手动添加。如果奶牛云系统和MAX软件有连接的话，日粮名称也会被自动更新。单击日粮名称定期更新日粮，以确保报告中获得正确的日粮信息。如果MAX没有和奶牛云系统连接，需要手动选择日粮或正确的类型/子类型。单击圈舍名称，更新牧场中圈舍的数据。
PenListViewModel.MainHeading=圈舍详述
PenListViewModel.NewPen=添加新圈舍
PenListViewModel.Title=圈舍
PenName=圈舍名称
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=体况评分变化（每100天）
PenTimeBudgetComparisonViewModel.BodyWeightChange=体重变化({0})
PenTimeBudgetComparisonViewModel.CowsInPen=圈舍中的牛头数
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=每小时挤奶牛头数
PenTimeBudgetComparisonViewModel.Current=目前
PenTimeBudgetComparisonViewModel.EnergyChange=能量变化（兆卡）
PenTimeBudgetComparisonViewModel.Overcrowding=拥挤情况（%）
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=奶厅周转批次/小时
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=潜在的奶量损失/增加({0})
PenTimeBudgetComparisonViewModel.RestingDifference=休息差异（小时）
PenTimeBudgetComparisonViewModel.TableTitle=比较
PenTimeBudgetComparisonViewModel.TimePerMilking=剩余的用于休息的时间（小时）
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=剩余的休息时间（小时)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=需要的休息时间（小时)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=总共的非休息时间（小时）
PenTimeBudgetComparisonViewModel.TotalTimeMilking=总共的挤奶时间（小时）
PenTimeBudgetComparisonViewModel.WalkingToFindStall=找到卧床的步行时间（小时）
PenTimeBudgetPenMasterViewModel.Compare=比较
PenTimeBudgetPenMasterViewModel.Inputs=输入
PenTimeBudgetPenMasterViewModel.Results=结果
PenTimeBudgetPenMasterViewModel.Title=圈舍时间预算
PenTimeBudgetResultsViewModel.Hours=小时
PenTimeBudgetResultsViewModel.MilkDifference=潜在的奶量差异
PenTimeBudgetResultsViewModel.MilkLossKg=公斤
PenTimeBudgetResultsViewModel.MilkLossPounds=磅
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=潜在的奶量损失/增加
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=可用于休息的时间
PenTimeBudgetResultsViewModel.TimeRemaining=剩余时间
PenTimeBudgetResultsViewModel.TimeRequired=要求的时间
PenTimeBudgetResultsViewModel.Title=圈舍时间预算结果
PenTimeInputsViewModel.CowsPen=每圈的牛头数
PenTimeInputsViewModel.Drinking=喝水和刷毛的时间（小时）
PenTimeInputsViewModel.Eating=采食时间（小时）
PenTimeInputsViewModel.Frequency=挤奶频率（每天）
PenTimeInputsViewModel.LockUp=颈夹上的时间（小时）
PenTimeInputsViewModel.NonRestTime=其他的非休息时间（小时）
PenTimeInputsViewModel.ParlorTime=在奶厅的时间（小时）
PenTimeInputsViewModel.PenTimeTitle=圈舍时间预算
PenTimeInputsViewModel.Resting=要求的休息时间（小时）
PenTimeInputsViewModel.StallsPen=圈舍中的卧床数
PenTimeInputsViewModel.TotalStalls=奶厅中的挤奶位数量
PenTimeInputsViewModel.WalkingTimeFrom=从奶厅返回的时间（小时）
PenTimeInputsViewModel.WalkingTimeTo=去奶厅的时间（小时）
PenTimePenSelectionViewModel.NoLactatingPen=要想使用这个工具，请确保你至少有一个牛舍有泌乳日粮。
PenTimePenSelectionViewModel.PenTimeBudgetTitle=圈舍时间预算
PenTimePenSelectionViewModel.PenTimeSection=圈舍
PenTimePenSelectionViewModel.Title=圈舍时间预算
Pendetails=圈舍详述
PennStateShakerBoxForageResults=宾州筛
Pennsylvania=宾夕法尼亚州
Pens=圈舍
PerceivedHeatStressDietInfoMessage=中等热应激：喘气、流涎或沫，没有张开嘴，呼吸频率每分钟40到120次。严重的热应激：张开嘴+喘气，呼吸频率每分钟120到160次以上。
PercentLossPerCow=损失的%/头牛
PercentOnScreenTitle=宾州筛的比例（%）
PercentPen=每圈的百分比（%）
PercentageOnScreen=宾州筛的比例（%）
PercentageOnScreenCurrentVisit=TMR筛各层比例（%）－本次拜访
PercentageOnScreenTrend=TMR筛各层比例（%）－趋势
Pernambuco=伯南布哥州
Peru=秘鲁
Perugia=佩鲁亚
Pesaro_and_Urbino=佩萨罗和乌尔比诺
Pescara=佩斯卡拉
PhaseFive=性成熟
PhaseFour=育成
PhaseOne=初乳
PhaseSeven=围产前期/生产
PhaseSix=怀孕
PhaseTwoThree=断奶前/后
Philippines=菲律宾
PhotoExamples=照片示例
Piacenza=皮亚琴察
Piauí=皮奥阿
Pickup=奶罐车{0}
Pile=堆
PileAndBunkerCapacitiesDensity=堆和窖的存贮能力密度参数指导
PileAndBunkerCapacity=堆和窖的存贮能力
PileAndBunkerCapacityViewModel.AddBag=添加袋
PileAndBunkerCapacityViewModel.AddBunker=添加青贮窖
PileAndBunkerCapacityViewModel.AddPile=添加青贮堆
PileAndBunkerCapacityViewModel.Bag=袋
PileAndBunkerCapacityViewModel.Bags=袋
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=底部取料窖仓
PileAndBunkerCapacityViewModel.Bunker=青贮窖
PileAndBunkerCapacityViewModel.Bunkers=青贮窖
PileAndBunkerCapacityViewModel.NameNotUnique=青贮窖 "{0}"名字已经存在。名字不能重复
PileAndBunkerCapacityViewModel.NameTooLong=青贮窖堆的名字必须在40个字符以内
PileAndBunkerCapacityViewModel.Pile=堆
PileAndBunkerCapacityViewModel.PileBunkerCapacities=堆和窖的存贮能力
PileAndBunkerCapacityViewModel.Piles=堆
PileAndBunkerCapacityViewModel.Resources=来源
PileAndBunkerCapacityViewModel.Title=堆和窖的存贮能力
PileAndBunkerCapacityViewModel.TopUnloadingSilo=顶部取料窖仓
PileAndBunkerCapacityViewModel.VisitNotebook=访问笔记
PileAndBunkerName=堆和窖的名称
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=青贮鲜基密度 {0} (目标\: > {1})
PileAndBunkerResultsBagCapacityInputViewModel.CapacityBag=容积
PileAndBunkerResultsBagCapacityInputViewModel.DiameterBag=直径 ({0})
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=干物质%
PileAndBunkerResultsBagCapacityInputViewModel.LenghtBag=长度 ({0})
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=鲜基吨数
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=干物质吨数
PileAndBunkerResultsBagCapacityInputViewModel.SilageDMDensityBag=青贮干物质密度 ({0})
PileAndBunkerResultsBagCapacityInputViewModel.TonsAFBag=鲜基吨数
PileAndBunkerResultsBagCapacityInputViewModel.TonsDMBag=干物质吨数
PileAndBunkerResultsCapacityInputViewModel.BottomLength=底部长度({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=底部宽度 ({0})
PileAndBunkerResultsCapacityInputViewModel.Capacity=能力
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=干物质 %
PileAndBunkerResultsCapacityInputViewModel.Height=高度({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=吨（鲜基）
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=吨（干物质）
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=底部长度(英尺)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=底部长度(米)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=底部宽度(英尺)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=底部宽度(米)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=高度（英尺）
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=高度（米）
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=顶部长度（英尺）
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=顶部长度（米）
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=顶部宽度（英尺）
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=顶部宽度（米）
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=青贮干物质密度({0})
PileAndBunkerResultsCapacityInputViewModel.Title=能力
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=青贮鲜基密度{0} （目标：&gt;44）
PileAndBunkerResultsCapacityInputViewModel.TonsAF=吨（鲜基）
PileAndBunkerResultsCapacityInputViewModel.TonsDM=吨（干物质）
PileAndBunkerResultsCapacityInputViewModel.TopLength=顶部长度({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=顶部宽度({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=每天6英寸
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=每天15厘米
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=每天3英寸
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=每天7厘米
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=奶牛头数/每天需要的
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=需要饲喂的奶牛数
PileAndBunkerResultsFeedOutViewModel.DateGone=消耗完的时间
PileAndBunkerResultsFeedOutViewModel.Days=天数
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=饲喂速度信息
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=饲喂表面积（平方英尺）
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=饲喂表面积（平方米）
PileAndBunkerResultsFeedOutViewModel.FeedingRate=饲喂速度（鲜基/头）
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=英尺/天
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=厘米/天
PileAndBunkerResultsFeedOutViewModel.Resources=来源
PileAndBunkerResultsFeedOutViewModel.StartDate=开始时间
PileAndBunkerResultsFeedOutViewModel.Title=饲喂出
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=吨/天
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=磅/英尺（干物质下）
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=公斤/米 （干物质下）
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=能力
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=饲喂出
PileAndBunkerResultsMasterViewModel.VisitNotebook=访问笔记
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=容积
PileAndBunkerResultsSiloCapacityInputViewModel.Diameter=直径 ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=干物质 %
PileAndBunkerResultsSiloCapacityInputViewModel.FilledHeight=填充高度 ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=鲜基吨数（窖里剩余）
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=干物质吨数（窖里剩余）
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=青贮干物质密度 ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.SilageLeft=窖里青贮剩余高度 ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.TonsAFSilo=鲜基吨数（窖里剩余）
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=干物质吨数（窖里剩余）
PileAndBunkerTitle=青贮堆和窖
PileBunkerCapacities=青贮窖存储能力
PileCapacity=青贮堆存储能力
PileFeedOutRate=青贮堆饲喂完
Piles=青贮堆
Pisa=比萨
Pistoia=跳手
Pitcairn=皮特凯恩
PlBacteriaCell=细菌数 (1,000 cfu/mL)
PlMilkFat=乳脂肪
PlMilkProtein=乳蛋白
PlSomaticCell=体细胞数(1,000 cells/mL)
Poland=波兰
Poor=少
Pordenone=波代诺内
Porosity=疏松
Portugal=葡萄牙
Postweaned=断奶后
Postweaned_CleanAndDryPen=清洁和干燥牛圈
Postweaned_CleanAndDryPen_ToolTip=落膝测试检测该区域是否干净干燥
Postweaned_EvidenceOfAcidosisInManure=粪便酸中毒的迹象
Postweaned_EvidenceOfAcidosisInManure_ToolTip=稀粪中是否有气泡
Postweaned_EvidenceOfScoursOrPneumonia=腹泻或肺炎的迹象
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20%的犊牛发生腹泻或肺炎
Postweaned_FeedBunkIsClaanedDaily=饲槽每日清洁，移除剩料
Postweaned_ForageAvailability=提供粗饲料
Postweaned_ForageAvailability_ToolTip=茎秆长于5cm
Postweaned_FreeChoiceCleanWaterIsAvailable=自由选择，有充足干净的水
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=没有迹象表明水污染
Postweaned_FreshQualityStarterAvailable=提供新鲜，高质开食料/生长料
Postweaned_FreshQualityStarterAvailable_ToolTip=开食料/生长料无粉末，无霉变，不潮湿
Postweaned_SizeOfBunkSpace=每头犊牛采食空间充沛
Postweaned_SizeOfBunkSpace_ToolTip=每头犊牛&gt;45cm
Postweaned_SizeOfPenAdequate=每头青年牛圈舍空间充足
Postweaned_SizeOfPenAdequate_ToolTip=个体牛圈\: 3m2, 群体牛圈\: 2.75m2
Postweaned_WellVentilatedPenWithNoDraftOnCalf=牛圈良好的通风无遮风
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=如果在离开畜舍后衣物问起来有氨气味，表明太高
PotentialDownResponse=潜在的下奶速度({0}/cow/day)
PotentialSCC=潜在的体细胞(cells/{0})
Potenza=力量
Prato=盘子
PreWeaned_CMRisProperlyMixed_ToolTip=代乳粉用量\: &gt;600g &lt;800g, 体温\: 39-41C, 固体 12-18%
PreWeaned_CleanAndDryPen_ToolTip=落膝测试检测该区域是否干净干燥
PreWeaned_EvidenceOfSource_ToolTip=&lt;20%的犊牛发生腹泻或肺炎
PreWeaned_Forageavailability_ToolTip=如果饲喂组织化（口感化）颗粒料，不需要饲喂粗饲料
PreWeaned_FreeChoiceCleanWater_ToolTip=第一天开始提供饮水，无迹象表明水污染
PreWeaned_FreeChoiceFreshCalf_ToolTip=开食料无粉末，无霉变，不潮湿
PreWeaned_SizeOfPen_ToolTip=个体牛舍\:3m2, 群体牛舍2.5m2
PreWeaned_WellVenilated_ToolTip=如果在离开畜舍后衣物问起来有氨气味，表明太高
PrematureKelvingsKeyInfoMessage=在预产期至少10天前分娩一个或一个以上的成活犊牛。
PreventingStorageLosses=预防储存损失
Previous=先前
Preweaned=断奶前
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=代乳粉充分混合足量添加
Preweaned_CleanAndDryPen=清洁和干燥牛圈
Preweaned_CleanAndSanitizeCalfFeedingEquipment=每次饲喂之间适度清洁和消毒
Preweaned_ConsistentFeedingTimesAndProtocols=持续的饲喂时间和策略
Preweaned_EvidenceOfScoursOrPneumonia=腹泻和肺炎的迹象
Preweaned_ForageAvailability=提供粗饲料
Preweaned_FreeChoiceCleanWaterIsAvailable=自由选择，有充足干净的水
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=自由选择，有充足干净的水
Preweaned_SizeOfPenAadequatePerHeifer=每头青年牛圈舍空间充足
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=断奶时每头开食料采食量1kg
Preweaned_WellVentilatedPenWithNoDraftOnCalf=犊牛圈舍良好的通风，无贼风
PricingMatrixEditViewModel.Cancel=取消
PricingMatrixEditViewModel.Save=保存
PricingMatrixEditViewModel.Title=矩阵项目详述
PricingMatrixPickListViewModel.PricingMatrix=定价矩阵
PricingMatrixViewModel.Amount=数量(1000 cells/ml)
PricingMatrixViewModel.AmountCFU=数量(1000 cfu/ml)
PricingMatrixViewModel.New=新建
PricingMatrixViewModel.Title=编辑矩阵
Prince_Edward_Island=爱德华王子岛
Privacy_Statement=隐私声明
ProcessorCurrencyPickListViewModel.CurrenciesLabel=货币
ProcessorCurrencyPickListViewModel.Title=货币
ProductivityToolsViewModel.MilkProcessRevenueCalculator=挤奶程序收益计算器
ProductivityToolsViewModel.MilkRevenueAnalysis=牛奶收益分析
ProductivityToolsViewModel.MilkSoldEvaluation=牛奶销售评估
ProductivityToolsViewModel.ProductivityTitle=生产率工具
ProductivityToolsViewModel.ProductivityTools=工具
ProductivityToolsViewModel.VisitNotebook=访问笔记
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=取消
PromptOK=OK
PromptPermissionMsg=没有此许可，某些功能可能无法使用。您确定要拒绝此权限吗？
PromptPermissionMsgIMSure=我确定
PromptPermissionMsgRetry=重新尝试
PromptPermissionTitle=权限被拒绝
ProspectProfileViewModel.DeleteProspect=删除客户
ProspectProfileViewModel.DeleteProspectPrompt=你确定要删除这个客户吗？客户、牧场和里面的信息都会丢失。
ProspectProfileViewModel.MainHeading=牧场
ProspectProfileViewModel.NewSite=新建牧场
ProspectProfileViewModel.ProspectInfo=客户信息
ProspectProfileViewModel.ProspectTitle=客户详述
ProspectsViewModel.NewProspect=新建客户
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=普罗维美
ProvimiUS=Provimi US
PublishVisit=发布
Puducherry=本地治里
Puebla=普韦布拉
Puerto_Rico=波多黎各
Punjab=旁遮普邦
Purina=普瑞纳
Qatar=卡塔尔
Qinghai=青海
QuarterPointScale=0.25分制
Quarterly=每季度
Quebec=魁北克
Queensland=昆士兰州
Querétaro=查询©芋头
QuestionTableTitle=第{0}个问题
QuestionViewModel.Baleage=裹包青贮
QuestionViewModel.BunkersAndPiles=青贮窖和青贮堆
QuestionViewModel.Close=关闭
QuestionViewModel.Harvest=收割
QuestionViewModel.MaintainingForageQuality=维护粗饲料质量
QuestionViewModel.SilageBags=青贮包
QuestionViewModel.SurveyOfForages=粗饲料测量
QuestionViewModel.TowerSilos=塔式青贮
QuestionViewModel.VisitNotebook=访问报告
Quintana_Roo=金塔纳罗奥州
ROL=ROL
RUB=俄罗斯 (₽‎ RUB)
Ragusa=拉古萨
Rajasthan=拉贾斯坦邦
Rationcost=日粮成本({0})
Ravenna=拉文纳
ReadyToMilkChartViewModel.Current=目前
ReadyToMilkChartViewModel.DeathLoss=乳房炎
ReadyToMilkChartViewModel.DisorderGraphTitle=年度代谢疾病成本/头
ReadyToMilkChartViewModel.DisplacedAbomasum=真胃移位
ReadyToMilkChartViewModel.Dystocia=难产
ReadyToMilkChartViewModel.Ketosis=酮病
ReadyToMilkChartViewModel.Metritis=子宫炎
ReadyToMilkChartViewModel.MilkFever=产乳热
ReadyToMilkChartViewModel.RetainedPlacenta=胎衣不下
ReadyToMilkChartViewModel.Title=Ready2Milk™表格
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Ready3Milk™指数
ReadyToMilkInputViewModel.Back=返回
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=干奶期到产后21天体况变化
ReadyToMilkInputViewModel.CloseUp=围产前期奶牛
ReadyToMilkInputViewModel.ComfortCloseUp=围产前期舒适度
ReadyToMilkInputViewModel.ComfortDiet=产后0－21天舒适度
ReadyToMilkInputViewModel.CostExtraDaysOpen=额外空怀天数的成本
ReadyToMilkInputViewModel.CudChewingDiet=新产牛反刍比例
ReadyToMilkInputViewModel.DeadCowsOrCulled=和健康相关的饲喂或淘汰
ReadyToMilkInputViewModel.DisplacedAbomasum=真胃移位
ReadyToMilkInputViewModel.Dystocia=难产
ReadyToMilkInputViewModel.FreshCows=新产牛
ReadyToMilkInputViewModel.Health=健康
ReadyToMilkInputViewModel.HealthDesc=输入评估期间的新产牛数量和有代谢疾病的数据。这将被转化到输出表格的年度疾病成本中。
ReadyToMilkInputViewModel.HealthRecords=健康记录
ReadyToMilkInputViewModel.Herd=牛群水平信息
ReadyToMilkInputViewModel.Ketosis=酮病
ReadyToMilkInputViewModel.LocomotionScore=运动评分
ReadyToMilkInputViewModel.Mastitis=0－21天乳房炎
ReadyToMilkInputViewModel.Metritis=子宫炎
ReadyToMilkInputViewModel.MilkFever=产乳热
ReadyToMilkInputViewModel.MilkPrice=奶价
ReadyToMilkInputViewModel.MilkYield=15－60天奶量
ReadyToMilkInputViewModel.Next=下一步
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=观察到的0－21天牛的热应激
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=观察到的围产前期热应激
ReadyToMilkInputViewModel.PrematureCalvings=早产
ReadyToMilkInputViewModel.ReplacementCowCost=替换成本
ReadyToMilkInputViewModel.RetainedPlacenta=胎衣不下
ReadyToMilkInputViewModel.RumenFill=瘤胃充盈度
ReadyToMilkInputViewModel.SccFirstTest=第一次检测的体细胞数(x1000 SCC/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=特定的围产前期日粮
ReadyToMilkInputViewModel.SpecificDiet=特定的产后0－21天日粮
ReadyToMilkInputViewModel.Title=Ready2Milk™ 输入
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=总新产牛数/年
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=总新产牛评估
ReadyToMilkListViewModel.Title=Ready2Milk™ 指数
ReadyToMilkMasterViewModel.Charts=表格
ReadyToMilkMasterViewModel.Inputs=输入
ReadyToMilkMasterViewModel.MastitisNotPresent=乳房炎没有被揭发
ReadyToMilkMasterViewModel.Outputs=输出
ReadyToMilkMasterViewModel.Title=Ready2Milk™ 指数
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Ready2Milk™ 指数
ReadyToMilkOutputViewModel.Title=Ready2Milk™ 输出
RecommendedTLCSettings=推荐的TLC 环境
RefreshTokenFailed=验证错误，需要重新登陆
Reggio_Calabria=雷焦卡拉布里亚
Reggio_Emilia=雷焦艾米利亚
RemovedAndMeasured=移除并测量
RemovedOnly=只移除
Report=报告
Report.Analysis.Type=分析类型
Report.Animal.Analysis=动物分析
Report.Animal.Tag.Name=动物ID
Report.AvgRumenFillScore=平均瘤胃充盈度评分（计算）
Report.BCS.EvalDataTitle=计算的体况评分评估数据
Report.BCS.LactationStages=泌乳阶段
Report.BCS.Max=体况评分上限
Report.BCS.MilkHeadDay=牛奶/头/天
Report.BCSAvg=平均体况评分
Report.Bcs=体况评分
Report.Bcs.ChartName=体况评分 - 动物{0}
Report.Bcs.HerdAnalysis.ChartName=体况评分 vs 牛奶
Report.Bcs.Milk=牛奶
Report.Bcs.Min=体况评分下限
Report.Calving.Date=产犊
Report.Cargill.Report=嘉吉 报告
Report.Chewing=反刍
Report.Chews=咀嚼
Report.CudChewing.EvalDataTitle=计算出的食团咀嚼评估数据
Report.CudChewingPercentage=食团反刍%/
Report.CudChewingPercentage.Vs.LactStages=食团反刍%/
Report.EvalDataTitle=计算的评估数据
Report.ForagePennState=粗饲料宾州筛
Report.General.Comments=总体 评论
Report.GoalCudChewingPercentage=目标 食团反刍%/
Report.Heatstress.Dmi.Adjustment=DMI调整
Report.Heatstress.Energy.Equivalent.Milk.Loss=能量等效牛奶损失（{0}）
Report.Heatstress.Estimated.Dry.Matter.Intake=估计的干物质摄入量（{0}）
Report.Heatstress.Intake.Adjustment=采食量调整
Report.Heatstress.Legend=图例
Report.Heatstress.Legends=传奇
Report.Heatstress.Loss.Of.Energy.Consumed=摄入能量的损失（MCAL）
Report.Heatstress.Mild.Moderate.Stress=轻度-中度应激
Report.Heatstress.Mild.Moderate.Stress.Message=呼吸超过75 bpm |直肠温度超过39 \\ u2103（102.2 \\ u2109）
Report.Heatstress.Milk.Value.Loss.PerMonth=牛奶价值损失（每月）（{0}）
Report.Heatstress.Milk.Value.Loss.Perday=牛奶价值损失（每天）（{0}）
Report.Heatstress.Moderate.Severe.Stress=中度-重度应激
Report.Heatstress.Moderate.Severe.Stress.Message=呼吸超过85 bpm |直肠温度超过40 \\ u2103（104 \\ u2109）
Report.Heatstress.Reduction.In.Dmi=减少DMI（{0}）
Report.Heatstress.Relative.Humidity=相对湿度 （％）
Report.Heatstress.Severe.Stress=重度应激
Report.Heatstress.Severe.Stress.Message=呼吸超过120-140 bpm |直肠温度超过41 \\ u2103（106 \\ u2109）
Report.Heatstress.Stress.Threshold=应激阈值
Report.Heatstress.Stress.Threshold.Message=呼吸超过60 bpm |可检测到的重建损失|直肠温度超过38.5 \\ u2103（101.3 \\ u2109）
Report.Heatstress.Temperature=温度
Report.Heatstress.Temperature.In.Celcius=温度\\ U2103
Report.Heatstress.Temperature.In.Farenhiet=温度\\ U2109
Report.Heatstress.TemperatureHumidityIndex=温湿度指数
Report.Herd.Analysis.CudChewingPercentage=食团反刍%/
Report.Locomotion.HerdAnalysis.ChartName=运动得分百分比
Report.LocomotionScore.X.Axis=运动得分
Report.LocomotionScore.Y.Axis=百分比 ％
Report.LocomotionScore.chartName=运动得分 - 动物{0}
Report.No.OfChews=咀嚼次数
Report.No.OfChewsPerRegurgitation=每次反刍的咀嚼次数
Report.NoOfChews.Vs.LactStages=咀嚼次数
Report.Not.Chewing=不咀嚼
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=可用于休息的时间
Report.PenTimeBudget.TimeAvailableForResting.Label=小时
Report.PenTimeBudgetTimeRemaining=剩余时间
Report.PenTimeBudgetTimeRequired=要求的时间
Report.Pentime.Budget.Hours=小时
Report.PercentageOnScreen=筛上物（%）
Report.RumenHealthManureScreening.Bottom=底层
Report.RumenHealthManureScreening.BottomGoalMax=底层目标最大
Report.RumenHealthManureScreening.BottomGoalMin=底层目标最小
Report.RumenHealthManureScreening.Middle=中层（8mm）
Report.RumenHealthManureScreening.MiddleGoalMax=中层目标最大
Report.RumenHealthManureScreening.MiddleGoalMin=中层目标最小
Report.RumenHealthManureScreening.Top=顶层
Report.RumenHealthManureScreening.TopGoalMax=顶层目标最大
Report.RumenHealthManureScreening.TopGoalMin=顶层目标最小
Report.SheetName=报告主
Report.Tool.Details=工具 详细信息
Report.Tool.Name=工具名称
Report.Visit.Date=访问日期
Report.Visit.Report=访问 报告
Report.Visit.name=访问名称
Report.goalChews=咀嚼目标
Report.locomotionScore.Pen.Analysis.ChartName=类别与访问日期
ReportDate=报告日期
ReportPDFNote=备注：
Reset_Database=重置数据库
Resources=来源
ResourcesViewModel.Title=粗饲料审计来源
ResourcesViewModel.VisitNotebook=访问笔记
RetainedPlacenta=胎衣不下
Reunion=团圆
Revenue=收益
RevenueEditComparisonValuesViewModel.CurrentSCC=目前的体细胞数(cells/{0})
RevenueEditComparisonValuesViewModel.DownResponse=下奶速度 ({0}/cow/day)
RevenueEditComparisonValuesViewModel.EditComparisonValues=编辑比较值
RevenueEditComparisonValuesViewModel.MilkChange=奶量变化 (%)
RevenueEditComparisonValuesViewModel.MilkPrice=奶价 ($/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=奶产量
RevenueEditComparisonValuesViewModel.NumOfCows=牛头数
RevenueEditComparisonValuesViewModel.PotentialSCC=潜在的体细胞(cells/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=情景 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=情景 2
RevenueEditComparisonValuesViewModel.Title=编辑比较值
RevenueInputViewModel.ComparisonValues=比较值
RevenueInputViewModel.Edit=编辑
RevenueInputViewModel.ScenarioOne=情景 1
RevenueInputViewModel.ScenarioTwo=情景 2
RevenueInputViewModel.Title=挤奶程序收益－输入
RevenueLossDay=收益损失({0}/天)
RevenueLossYear=收益损失({0}/年)
Rheinland=犀牛
Rhode_Island=罗德岛
Rieti=列蒂
Rimini=里米尼
Rio_Grande_do_Norte=大北河
Rio_Grande_do_Sul=里奥格兰德
Rio_de_Janeiro=里约热内卢
Robot=机器人
Romania=罗马尼亚
Rome=罗马
RondÃ´nia=隆德尼亚
Roraima=罗拉梅
Roscommon=罗斯科蒙
Rovigo=罗维戈
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=牛群分析
RumenHealthBodyConditionLandingViewModel.PenAnalysis=圈舍分析
RumenHealthBodyConditionLandingViewModel.Pens=圈舍
RumenHealthBodyConditionLandingViewModel.Resources=来源
RumenHealthBodyConditionLandingViewModel.Title=体况评分
RumenHealthEditManureScoresViewModel.Close=关闭
RumenHealthEditManureScoresViewModel.Count=计数
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=请数牛头数
RumenHealthEditManureScoresViewModel.NumOfCows=奶牛数量
RumenHealthEditManureScoresViewModel.NumberOfCows=奶牛数量
RumenHealthEditManureScoresViewModel.VisitNotebook=访问笔记
RumenHealthLandingViewModel.HerdAnalysis=牛群分析
RumenHealthLandingViewModel.PenAnalysis=圈舍分析
RumenHealthLandingViewModel.Pens=圈舍
RumenHealthLandingViewModel.Title=瘤胃健康 反刍比例
RumenHealthLocomotionLandingViewModel.HerdAnalysis=牛群分析
RumenHealthLocomotionLandingViewModel.PenAnalysis=圈舍分析
RumenHealthLocomotionLandingViewModel.Pens=圈舍
RumenHealthLocomotionLandingViewModel.Resources=来源
RumenHealthLocomotionLandingViewModel.Title=运动
RumenHealthManureLandingViewModel.HerdAnalysis=牛群分析
RumenHealthManureLandingViewModel.PenAnalysis=圈舍分析
RumenHealthManureLandingViewModel.Pens=圈舍
RumenHealthManureLandingViewModel.Resources=来源
RumenHealthManureLandingViewModel.Title=瘤胃健康 粪便评分
RumenHealthManureMasterViewModel.Inputs=输入
RumenHealthManureMasterViewModel.Results=结果
RumenHealthManureMasterViewModel.RumenHealthManureScore=瘤胃健康 粪便评分
RumenHealthManureMasterViewModel.RumenHealthManureTitle=瘤胃健康 粪便评分
RumenHealthManureMasterViewModel.VisitNotebook=访问笔记
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=平均分
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=日期
RumenHealthManureScoresResultsViewModel.PercentPen=圈舍中比例 （%）
RumenHealthManureScoresResultsViewModel.SelectedDates=选择日期
RumenHealthManureScoresResultsViewModel.Title=粪便评分结果
RumenHealthManureScoresViewModel.AnimalsObserved=观察的动物数量
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=平均粪便评分（计算值）
RumenHealthManureScoresViewModel.Edit=编辑
RumenHealthManureScoresViewModel.ManureScore=粪便评分
RumenHealthManureScoresViewModel.PercentOfPen=圈舍中比例（%）
RumenHealthManureScoresViewModel.ScoreCategory=粪便评分
RumenHealthManureScoresViewModel.StdDevCalculated=标准差（计算值）
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=反刍计算器
RumenHealthPenCudCalculatorViewModel.CudChewing=反刍比例
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=数反刍牛的数量
RumenHealthPenCudCalculatorViewModel.NumberOfChews=反刍的数量
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=数单头牛的反刍次数
RumenHealthTMRLandingViewModel.HerdAnalysis=牛群分析
RumenHealthTMRLandingViewModel.PenAnalysis=圈舍分析
RumenHealthTMRLandingViewModel.Pens=牛舍
RumenHealthTMRLandingViewModel.Title=瘤胃健康 颗粒度评分
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=输入测量重量（g）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=输入筛重（g）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=目标-最大（%）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=目标-最小（%）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=目标
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=中层（8mm）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=下层（1.18mm）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=下层（4mm）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=TMR颗粒度评分名称
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=TMR颗粒度评分
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=筛重
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=输入测量重量
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=上层（19mm）
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=底层
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=添加TMR得分
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=平均得分
RumenHealthTMRParticleScorePenTableInputViewModel.Current=现在
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=测量重量 (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=目标-最大（%）
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=中层目标(8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=下层目标(1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=下层目标(4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=目标-最小（%）
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=目标
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=上层目标(19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=底层目标
RumenHealthTMRParticleScorePenTableInputViewModel.Goals=目标
RumenHealthTMRParticleScorePenTableInputViewModel.Max=最大
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=中层1（8mm）
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=下层（1.18mm）
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=中层2（4mm）
RumenHealthTMRParticleScorePenTableInputViewModel.Min=最小
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=颗粒度评分（%每层）
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=每层比例（%）
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=标准差
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=TMR颗粒度评分信息
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=TMR颗粒度评分
RumenHealthTMRParticleScorePenTableInputViewModel.Title=TMR颗粒评分
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=上层（19mm）
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=底层
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=输入
RumenHealthTMRPenScorerTableMasterViewModel.Results=结果
RumenHealthTMRPenScorerTableMasterViewModel.Title=瘤胃健康颗粒度评分
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=未选择
RumenHealthTMRSelectPenViewModel.NoScorerSelected=为了查看牛圈首选必须选择一个评分员
RumenHealthTMRSelectPenViewModel.SelectPen=圈舍（泌乳和干奶）
RumenHealthTMRSelectPenViewModel.SelectScorer=选择宾州筛类型
RumenHealthTMRSelectPenViewModel.Title=瘤胃健康颗粒度评分
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=未选择
RumenHealthTMRSelectScorerViewModel.FooterText=每次拜访只能使用同一个评分员，更改评分员会导致价值损伤
RumenHealthTMRSelectScorerViewModel.SelectScorer=选择宾州筛类型
RumenHealthTMRSelectScorerViewModel.Title=瘤胃健康颗粒度评分
Russia=俄罗斯
Russian_Federation=俄罗斯联邦
Rwanda=卢旺达
SAR=沙特阿拉伯 (﷼ SAR)
SCCPremiumDeduction=体细胞数奖励/罚款({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=苏里南 ($ SRD)
Saint_Barthélemy=圣巴泰勒米岛
Saint_Helena,_Ascension_and_Tristan_da_Cunha=圣赫勒拿岛、阿森松岛和特里斯坦 _da_Cunha
Saint_Kitts_and_Nevis=圣基茨和尼维斯
Saint_Lucia=圣卢西亚
Saint_Martin_(French_part)=圣马丁（法国部分）
Saint_Pierre_and_Miquelon=圣皮埃尔和米克隆
Saint_Vincent_and_the_Grenadines=圣文森特和格林纳丁斯
Salerno=萨勒诺
Samoa=萨摩亚
San_Luis_Potosí=圣路易斯·波托索
San_Marino=圣马力诺
Santa_Catarina=圣卡塔琳娜
Sao_Tome_and_Principe=圣托马和普林西比
Saskatchewan=萨斯喀彻温省
Sassari=萨萨里
SaudiArabia=沙特阿拉伯
Saudi_Arabia=沙特阿拉伯
Savona=萨沃纳
Schleswig=石勒苏益格
ScorecardPrompt=请完成以下调研来查看{0}积分卡
Screen=三层筛
ScreenNew=层筛新
ScreenOld=层筛旧
Search=搜索
SegmentViewModel.SegmentTitle=选择一个片段
SegmentViewModel.Title=详细
Select=选择
SelectCurrencyViewModel.Title=货币
SelectDates=选择日期
SelectFeedingSystemViewModel.SelectFeedingSystem=选择饲喂方式
SelectFeedingSystemViewModel.Title=圈舍设置
SelectForageImprovement=请从列表中仅选择12项进行改进。
SelectHousingSystemViewModel.SelectHousingSystem=选择饲养方式
SelectHousingSystemViewModel.Title=圈舍设置
SelectImprovement=请只选择列表中10个提升
SelectMatrix=选择一个模型
SelectMilkingSystemViewModel.NoneSelected=未选择
SelectMilkingSystemViewModel.SelectMilkingSystem=选择挤奶系统
SelectMilkingSystemViewModel.Title=牧场设置
SelectOnlyThreeNotes=请在工具中仅选择3个注意点
SelectOnlyTwoNotes=请在工具中仅选择2个注意点
SelectPen=请为新牛圈选择日粮
SelectProcessor=选择加工器
SelectProcessorViewModel.COMPONENT=成分
SelectProcessorViewModel.CONCENTRATION=浓度
SelectProcessorViewModel.Edit=编辑
SelectProcessorViewModel.MilkProcessors=乳企
SelectVisitComparison=选择访问以进行比较
SemiAnnually=半自动
Semiconfinamento=Semiconfinamento
Send=发送
Senegal=塞内加尔
Seoul=汉城
Serbia=塞尔维亚
Sergipe=塞尔希培
SettingsViewModel.Imperial=英制
SettingsViewModel.Metric=公制
SettingsViewModel.Milk_Processor_Set_Up=乳企设置
SettingsViewModel.More_Settings=更多设置
SettingsViewModel.Select_Unit_Of_Measure=选择测量单位
Severe=严重
Seychelles=塞舌尔
Shaanxi=陕西
Shandong=山东
Shanghai=上海
Shanxi=山西
ShortDryPeriod=短干奶期
ShowEulaViewModel.Accept=接受
ShowEulaViewModel.ConfirmationNo=否
ShowEulaViewModel.ConfirmationText=你同意手机应用的最终用户许可协议条款吗?
ShowEulaViewModel.ConfirmationTitle=确认
ShowEulaViewModel.ConfirmationYes=是
ShowEulaViewModel.Decline=取消
ShowEulaViewModel.Eula=最终用户许可协议
ShowEulaViewModel.EulaError=最终用户许可协议无法显示，请链接网络后再次尝试。
ShowEulaViewModel.EulaScreenTitle=最终用户许可
ShowPrivacyStatementViewModel.PrivacyStatement=&lt;p&gt;&lt;b&gt;隐私权说明&lt;/b&gt;&lt;/p&gt;&lt;p&gt;最新更新日期：2017年1月3日&lt;/p&gt;&lt;p&gt;&lt;b&gt;范围&lt;/b&gt;&lt;/p&gt;&lt;p&gt; 当你使用本手机应用时嘉吉股份有限公司 (“嘉吉” 或者 “我们”)将收集关于你的信息，目的是为了让顾问代表嘉吉提供Dairy Enteligen™ 服务。&lt;/p&gt;&lt;p&gt;&lt;b&gt;个人信息&lt;/b&gt;&lt;/p&gt;&lt;p&gt;嘉吉可能会直接向你搜集以下个人信息，例如当你注册时，当你使用应用时。&lt;/p&gt;&lt;p&gt;· &lt;b&gt;你的姓名&lt;/b&gt;&lt;/p&gt;&lt;p&gt;· &lt;b&gt;你的位置&lt;/b&gt; (通过GPS或其他相似技术)&lt;/p&gt;&lt;p&gt;· &lt;b&gt;你的照片&lt;/b&gt;或&lt;b&gt; 视频&lt;/b&gt; (如果通过应用分享给嘉吉)&lt;/p&gt;&lt;p&gt;我们可能在应用中使用普通的技术来搜集这些个人信息。 &lt;/p&gt;&lt;p&gt;&lt;b&gt;使用和分享—商务情境&lt;/b&gt;&lt;/p&gt;&lt;p&gt;我们的&lt;a href\="http\://www.cargill.com/privacy/business-notice/index.jsp"&gt;商务信息通知&lt;/a&gt; 说明我们如何利用在商务情境中搜集你的个人信息&lt;/p&gt;&lt;p&gt;&lt;b&gt;同意位置搜集&lt;/b&gt;&lt;/p&gt;&lt;p&gt;通过使用应用，你明确同意嘉吉搜集你实时的位置信息，当由于使用该应用或与分享位置有关的任何情况造成损失时，同意放弃追究嘉吉的任何和所有责任和索赔&lt;/p&gt;
ShowPrivacyStatementViewModel.PrivacyStatementTitle=个人声明
ShowSyncStatusViewModel.GetAccounts=账户记录已收到
ShowSyncStatusViewModel.GetNotes=笔记记录已收到
ShowSyncStatusViewModel.GetVisits=拜访报告已收到
ShowSyncStatusViewModel.Title=数据同步概况
Sichuan=四川
Siena=锡耶纳
Sierra_Leone=塞拉利昂
Sikkim=锡金
SilageBags=青贮袋
SilageBags_BagsPlacedOnStableWellManagedSurface=袋放置在平整的管理良好的地面上(沥青或混凝土)
SilageBags_BonusSecureCoverIsUsed=奖励：是否有安全覆盖？
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=干净整洁的料面，无发热和失水的松散青贮
SilageBags_FaceRemovalRate=饲喂速度
SilageBags_InspectedForPestHoleDamageRepairOnBasis=检查虫洞损伤并定期的修复
SilageBags_PorosityScoresConsistently=Porosity scores consistently\:
SilageBags_TrashVegRodentControlledAroundBags=青贮袋旁，是否有控制垃圾，植物和老鼠地的措施？
SilagePrevention1st=青贮保护：头等大事
Sinaloa=锡那罗亚
Singapore=新加坡
Sint_Maarten_(Dutch_part)=SINT MARTHE（荷兰部分）
Site-Not-Synced-To-Lift= 牧场未同步到LIFT；请联系管理员以获得解决方案。
SiteDetailViewModel.AnimalInputsSite=动物进入牧场
SiteDetailViewModel.DairyEnteligenReport=Dairy Enteligen 报告
SiteDetailViewModel.Detailed=详细内容
SiteDetailViewModel.DietInputsSiteLactating=日粮输入，牧场（泌乳奶牛）
SiteDetailViewModel.DownloadingVisit=下载访问…
SiteDetailViewModel.GeneralCustomerSiteSetup=客户牧场信息设置
SiteDetailViewModel.GetReportMsg=下载报告…
SiteDetailViewModel.MainHeading=访问
SiteDetailViewModel.NetworkErrorMessage=网络不可用
SiteDetailViewModel.NetworkErrorMessageTitle=网络错误
SiteDetailViewModel.NewVisit=开始新的拜访
SiteDetailViewModel.ReportDownloadTimeout=文件不能下载，请在网络更好时尝试
SiteDetailViewModel.ReportNotAvailable=报告不能下载
SiteDetailViewModel.ReportNotAvailableTitle=状态
SiteDetailViewModel.Reports=Dairy Enteligen 报告
SiteDetailViewModel.Resources=资源
SiteDetailViewModel.SiteSetup=牧场设置
SiteDetailViewModel.Summary=概要
SiteDetailViewModel.Title=牧场详细内容
SiteDetailViewModel.VisitDownloadPrompt=访问未能下载，是否重新下载该次访问？
SiteDetailViewModel.VisitDownloadPrompt1=是否需要恢复此次拜访？
SiteDetailViewModel.VisitNotDownloaded=访问未下载
SiteDetailViewModel.VisitUnavailable=访问数据不可用
SiteDetailsResourcesViewModel.Title=资源
SiteDetailsSetupViewModel.AnimalInputsSite=动物进入牧场
SiteDetailsSetupViewModel.AsFedIntake=鲜重摄入（{0}）
SiteDetailsSetupViewModel.BacteriaCellCount=细菌数量(1000 cfu/mL)
SiteDetailsSetupViewModel.Continue=继续
SiteDetailsSetupViewModel.CurrentMilkPrice=奶价（{0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=泌乳天数（DIM)
SiteDetailsSetupViewModel.Delete=删除
SiteDetailsSetupViewModel.DietInputsSiteLactating=日粮输入，牧场（泌乳奶牛）
SiteDetailsSetupViewModel.DietSetup=日粮设置
SiteDetailsSetupViewModel.Diets=日粮
SiteDetailsSetupViewModel.DryMatterIntake=干物质采食量（{0}）
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=客户牧场信息设置
SiteDetailsSetupViewModel.InfoSiteSetup=为客户不同位置的牧场增加一个新的牧场信息。当完成一个访问时至少需要创造一个牧场信息并访问Dairy Enteligen工具。当创造一个新的牧场信息时需要为牧场添加名称，奶价，挤奶系统和牛舍（点击牛舍设置）。牛舍设置需要使用Pen level工具和Walkthrough报告来完成。添加或更新牧场数据时在本页完成。同时你也可以在你使用的工具重添加或更新数据。牧场设置数据将随牧场数据下载到DE里完成自动更新。
SiteDetailsSetupViewModel.LactatingAnimals=泌乳奶牛
SiteDetailsSetupViewModel.MilkFatPercent=乳脂率%
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=其他固形物%
SiteDetailsSetupViewModel.MilkProteinPercent=乳蛋白%
SiteDetailsSetupViewModel.MilkYield=奶产量（{0}）
SiteDetailsSetupViewModel.MilkingSystem=挤奶系统
SiteDetailsSetupViewModel.NameNotUnique=牧场“{0}”的名称已经存在，命名需不同。
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=泌乳净能（Mcal/{0})
SiteDetailsSetupViewModel.NewSite=新牧场
SiteDetailsSetupViewModel.NullSiteName=牧场名称，奶价，挤奶系统和牛舍必须填写。是否继续或删除？
SiteDetailsSetupViewModel.NumberOfStalls=奶厅挤奶位总数
SiteDetailsSetupViewModel.PenSetup=牛舍设置
SiteDetailsSetupViewModel.Pens=牛舍
SiteDetailsSetupViewModel.RationCost=日粮成本，每头牛({0})
SiteDetailsSetupViewModel.SiteMandatoryFields=牧场名称，奶价，挤奶系统和牛舍必须填写。输入所有信息后继续。
SiteDetailsSetupViewModel.SiteName=牧场名称
SiteDetailsSetupViewModel.SiteSetup=牧场设置
SiteDetailsSetupViewModel.SomaticCellCount=体细胞数（1,000 cells/mL)
SiteDetailsSetupViewModel.Title=牧场详细内容
SiteDetailsSetupViewModel.WeightImperialCWT=英担
SiteVisitSummary=牧场访问概要
SiteVisitSummaryReport=牧场访问概要报告
SixToEightLayers=6到8层
SixToTwelveHours=6到12小时
SixToTwelveInches=6到12英寸
Sligo=斯莱戈
Slovakia=斯洛伐克
Slovenia=斯洛文尼亚
Solomon_Islands=所罗门群岛
Somalia=索马里
SomanticCellCount=体细胞数
SomaticCellPerML=体细胞数（cells/mL）
Sondrio=桑德里奥
Sonora=索诺拉
SouthAfrica=南非
SouthKorea=韩国
South_Africa=南非
South_Australia=南澳大利亚
South_Carolina=南卡罗来纳
South_Dakota=南达科他州
South_Georgia_and_the_South_Sandwich_Islands=南乔治亚和南三明治群岛
South_Sudan=南苏丹
Spain=西班牙
Sri_Lanka=斯里兰卡
StandardDeviationScoreTitle=标准差
StartDate=开始日期
StatusArchived=存档
StatusCompleted=完成
StatusInProgress=进行中
StdDevCalculated=标准差（计算）
Steer=阉牛
StorageCalculators=仓位计算器
StrategyToReduceDisplacedAbomasum=真胃移位
StrategyToReduceDisplacedAbomasumDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;减少真胃移位发生的策略&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;依靠管控风险的预防策略，由于目前没有发现明确的直接原因。&lt;/p&gt;&lt;p&gt;预防包括适度的营养和管理以及相关疾病管控\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;控制营养风险因素\:&lt;ul&gt;&lt;li&gt;避免过度肥胖的奶牛（在干奶和产犊时理想体况在3分）。&lt;/li&gt;&lt;li&gt;提供足够的粗饲料NDF。&lt;/li&gt;&lt;li&gt;控制日粮的物理形态。&lt;/li&gt;&lt;li&gt;关注矿物质的需求。&lt;/li&gt;&lt;li&gt;避免如低血钙等代谢类疾病和其他能引起采食量降低的感染性疾病。&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;最好的管理策略\:&lt;ul&gt;&lt;li&gt;确保新产牛产后数小时数天的采食量&lt;/li&gt;&lt;li&gt;合适的食槽管理&lt;/li&gt;&lt;li&gt;增加奶牛舒适度，减少应激源&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceDystocia=难产
StrategyToReduceDystociaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;减少难产发生的策略&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;通过以下几点预防难产\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;确保青年牛在适宜的年纪和体况授精。&lt;/li&gt;&lt;li&gt;选择更易产犊的公牛的冻精。&lt;/li&gt;&lt;li&gt;增加关于产犊时间和干预方法的个人培训，增加合适的方法来照顾新生犊牛&lt;/li&gt;&lt;li&gt;通过MAX回顾现有日粮配方&lt;sup&gt;TM&lt;/sup&gt; 干奶牛，围产牛和青年牛的需求要关注\:&lt;ul&gt;&lt;li&gt;维持体况和胎儿发育的能量&lt;/li&gt;&lt;li&gt;防止产犊时出现过肥的牛&lt;/li&gt;&lt;li&gt;控制群内低血钙的发生&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceIncidence=降低疾病发生的策略
StrategyToReduceKetosis=酮病
StrategyToReduceKetosisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;减少酮病的策略&lt;/strong&gt;&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;确保奶牛拥有较好的舒适度 (卧床，热应激，通风，避免应激和过度拥挤）etc.)&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="2"&gt;&lt;li&gt;通过MAX制作的均衡的日粮&lt;sup&gt;TM&lt;/sup&gt; 日粮营养和物理形态的需求。咨询围产日粮情况来进一步预防酮病。确保优质粗饲料的使用将会增加采食量和瘤胃缓冲能力。&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="3"&gt;&lt;li&gt;监控干奶牛到产犊时体况变化：干奶牛理想体况3分并在干奶期维持住体况以避免产犊时过度的脂肪动员。&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ol start\="4"&gt;&lt;li&gt;为新产牛实施一个特定的方案来检测和控制代谢性和传染性疾病。监控采食量，瘤胃充盈度和反刍情况。&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMastitis=乳房炎
StrategyToReduceMastitisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;减少乳房炎的策略&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;引起乳房炎的微生物主要分为两类\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;感染性病原菌，将会主要在挤奶时传播，加工 &lt;em&gt;(例如无乳链球菌&amp;nbsp;和&amp;nbsp;金黄色葡萄球菌)&lt;/em&gt;&lt;/li&gt;&lt;li&gt;环境致病菌，来源于奶牛饲养环境 &lt;em&gt;(例如大肠杆菌&amp;nbsp;和&amp;nbsp;乳房链球菌)&lt;/em&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;根据是由哪种细菌引起的乳房炎，采取相应的干预的措施。&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;传染性乳房炎控制&lt;/strong&gt;\: 没有万能药来防止所有细菌的感染，但以下步骤能帮助预防乳房炎\:&lt;ul&gt;&lt;li&gt;关注周围环境卫生和挤奶时清洁，包括药浴&lt;/li&gt;&lt;li&gt;牛奶最后会感染奶牛&lt;/li&gt;&lt;li&gt;执行常规的挤奶设备维护&lt;/li&gt;&lt;li&gt;通过MAX回顾干奶牛营养方案&lt;sup&gt;TM&lt;/sup&gt; 保证合适的营养供给(能量，抗氧化) 来满足免疫功能&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;控制环境性乳房炎&lt;/strong&gt;&lt;ul&gt;&lt;li&gt;确保卧床清洁干燥，保证畜舍通风良好&lt;/li&gt;&lt;li&gt;修正存栏密度&lt;/li&gt;&lt;li&gt;乳房的干净&lt;/li&gt;&lt;li&gt;奶厅日程&lt;/li&gt;&lt;li&gt;利用MAX回顾干奶牛营养&lt;sup&gt;TM&lt;/sup&gt; 保证合适的营养供给(能量，抗氧化) 来满足免疫功能&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceMetritis=子宫炎
StrategyToReduceMetritisDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;降低子宫炎策略&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;引起子宫炎的风险因素包括胎衣不下，难产时引起产道损伤,不正确的分娩程序，不卫生的产房，营养缺乏如VE或硒的缺乏,以及过胖的奶牛。&lt;/p&gt;&lt;p&gt;按重要性排序监管以下几个区域&amp;nbsp;\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;分娩措施&lt;br /&gt;&lt;ul&gt;&lt;li&gt;员工是否有不当操作在助产时将细菌带入子宫？&lt;/li&gt;&lt;li&gt;是否接产过早或过晚？&lt;/li&gt;&lt;li&gt;助产时是否拖拽过于频繁？&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;治疗措施&lt;br /&gt;&lt;ul&gt;&lt;li&gt;奶牛胎衣不下和子宫炎如何治疗的？&lt;/li&gt;&lt;li&gt;有无可能在那时将细菌从外界或阴道带入子宫？&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="3"&gt;&lt;li&gt;动物应激&lt;br /&gt;&lt;ul style\="list-style-type\: circle;"&gt;&lt;li&gt;产犊前过多的应激可能会破坏奶牛免疫系统，降低产的抵抗力。&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="4"&gt;&lt;li&gt;营养&lt;ul&gt;&lt;li&gt;回顾平衡干奶牛日粮，关注体况控制，矿物质平衡和抗氧化营养素的添加 (VE, A, 硒, 锌和铜)&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;li&gt;其他疾病检查 (酮病,真胃移位)&lt;/li&gt;&lt;/ol&gt;&lt;/span&gt;
StrategyToReduceMilkFever=产乳热
StrategyToReduceMilkFeverDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;strong&gt;降低产乳热的策略&lt;/strong&gt;&lt;/h4&gt;&lt;p&gt;两种100%完全依靠营养调控的二选其一的策略来防止低血钙的发生。&lt;/p&gt;&lt;ul&gt;&lt;li&gt;使用低钙日粮&lt;/li&gt;&lt;li&gt;使用低DCAD日粮&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;&amp;nbsp;&lt;/p&gt;&lt;p&gt;管控低血钙症发生的区域包括\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;在合格的实验室中检测饲草中矿物质 (钙，磷，镁，钠，硫和氯)&lt;/li&gt;&lt;li&gt;利用MAX回顾现有的配方&lt;sup&gt;TM&lt;/sup&gt; 围产期奶牛的需求以及饲喂管理措施(特别关注自由采食粗饲料的，饲草中钾的浓度，干奶牛自由采食矿物质，干奶牛挑食)&lt;/li&gt;&lt;li&gt;咨询围产期日粮来配制防止低血钙发生的特殊日粮&lt;/li&gt;&lt;li&gt;当使用DACD日粮时\:&lt;ul&gt;&lt;li&gt;小心监控日粮采食量因为阴离子盐适口性差会降低干物质采食量。&lt;/li&gt;&lt;li&gt;监管尿素pH来检查饲料更换的效率&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ul&gt;&lt;/span&gt;
StrategyToReduceRetainedPlacenta=胎衣不下
StrategyToReduceRetainedPlacentaDetails=&lt;span style\="font-family\:Calibri,Calibrib;"&gt;&lt;h4&gt;&lt;span&gt;&lt;strong&gt;降低胎衣不下的策略&lt;/strong&gt;&lt;/span&gt;&lt;/h4&gt;&lt;p&gt;胎衣不下的发病率与免疫抑制有极大正相关性, 高应激荷尔蒙, 低血钙症 (包括压临床), 助产和流产, 流行的传染病发生和遗传。&lt;/p&gt;&lt;p&gt;按重要性排序监管以下几个区域\:&lt;/p&gt;&lt;ol&gt;&lt;li&gt;营养 &lt;br/&gt; &lt;p style\="padding-left\: 30px;"&gt;利用MAX回顾日粮配方&lt;sup&gt;TM&lt;/sup&gt;特别关注指南\:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;有机硒VE&lt;/li&gt;&lt;li&gt;矿物质水平(K, Ca, Mg) 来降低临产和亚临床低血钙的风险&lt;/li&gt;&lt;li&gt;利用低或负DCAD值日粮来降低低血钙的发生&lt;/li&gt;&lt;/ul&gt;&lt;/li&gt;&lt;/ol&gt;&lt;ol start\="2"&gt;&lt;li&gt;管理&lt;/li&gt;&lt;ul&gt;&lt;li&gt;在泌乳后期和干奶期控制体况 (分娩时3分)&lt;/li&gt;&lt;li&gt;减少围产前期和围产后期应激源来降低体脂动员和免疫抑制。&lt;/li&gt;&lt;li&gt;提高奶牛舒适度并提高饲槽空间&lt;/li&gt;&lt;li&gt;执行兽医制定的分娩程序&lt;/li&gt;&lt;li&gt;避免不必要的助产&lt;/li&gt;&lt;li&gt;维持环境卫生来减少子宫感染的风险.&lt;/li&gt;&lt;/ul&gt;&lt;/ol&gt;&lt;/span&gt;
Straw=稻草
Sudan=苏丹
Surinam=苏里南
Suriname=苏里南
SurveyCategories=调查分类
SurveyOfForages=粗饲料调查
SurveyOfForages_AnnualCowNumAndForageNeeds=每年预计的奶牛头数和粗饲料需求？
SurveyOfForages_AshLevelsInCornSilage=全株青贮玉米粗灰分水平？
SurveyOfForages_AshLevelsInHaylage=牧草（半干）青贮粗灰分水平？
SurveyOfForages_ButyricAcidLevelsInHaylage=牧草（半干）青贮丁酸的水平？
SurveyOfForages_CornSilageProcessingScore=青贮玉米加工制作评分？
SurveyOfForages_CornSilageScoreMonitored=是否通过利用嘉吉粗饲料实验室籽粒破碎测试监控青贮玉米籽粒破碎评分？
SurveyOfForages_InspectedForSpoilageAndMold=是否所有粗饲料都检测腐败和霉变？腐败的粗饲料是否弃用？
SurveyOfForages_InventoryIsMonitored=是否监测库存？
SurveyOfForages_LacticAcidToAceticAcidLevels=乳酸与乙酸的比值？
SurveyOfForages_LooseOrFacedFeedWithin=松散的或窖面的青贮在多少小时内饲喂？
SurveyOfForages_NoLooseFeedRemaining=饲喂完成后，青贮窖没有松散的青贮？
SurveyOfForages_SilosSizedForCapacity=青贮窖是否根据牛群的需要设计？不需要装得太满
SurveyOfForages_VisibleSignsOfSoil=青贮是否不受到任何可见的土污染？
Svalbard_and_Jan_Mayen=斯瓦尔巴群岛和扬马延岛
Swaziland=斯威士兰
Sweden=瑞典
Switzerland=瑞士
Sync-failed-due-to-unknown-reason=Si prega di contattare l'amministratore per la risoluzione.
SyncFailed=同步未能完成，请重新尝试。
Sync_Data=同步数据
Syracuse=锡拉丘兹
Syrian_Arab_Republic=叙利亚阿拉伯共和国
SystemGenerated=生成系统
São_Paulo=宝保罗
THB=泰国 (THB THB)
TMR=TMR
TMRHerdAnalysisTableTitle=TMR颗粒度得分牛群分析
TMRParticleScore=TMR颗粒度评分分析
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=关闭
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=泌乳天数（DIM)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=编辑泌乳天数
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=泌乳天数（DIM)
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=编辑
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=（4mm）
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=（1.18m）
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=中层
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=（8mm）
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=下层
TMRParticleScoreHerdAnalysisInputsViewModel.Title=TMR颗粒度评分分析
TMRParticleScoreHerdAnalysisInputsViewModel.Top=上层
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=（19mm）
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=底层
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=牛群分析
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=输入
TMRParticleScoreHerdAnalysisMasterViewModel.Results=结果
TMRParticleScoreHerdAnalysisMasterViewModel.Title=瘤胃健康颗粒度评分
TMRParticleScoreHerdAnalysisResultsText=TMR颗粒度评分分析
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=中层（8mm）
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=下层（4mm）
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=上层（19mm）
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=底层
TRY=TRY
TWD=台湾 (NT$ TWD)
Tabasco=塔巴斯科
Taipei_City=台北城
Taiwan=台湾
Tajikistan=塔吉克斯坦
Tamaulipas=摩丽帕
Tamil_Nadu=泰米尔纳德邦
Tanzania,_United_Republic_of=坦桑尼亚，联合共和国
Taranto=塔兰托
Task=任务
Tasmania=塔斯马尼亚
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=田纳西州
Teramo=泰拉莫
Terni=特尔尼
Texas=德克萨斯州
TextureFeed=组织化（口感化）颗粒料
Thailand=泰国
Thirdparty=第三者
ThisVisit=（本次拜访）
ThreePotentialStorageSolutions=三种可能的储存解决方案
ThreeScreen=三层筛
ThreeTimesPerWeek=每周三次
Tianjin=天津
Tiestall=栓系式
TimeRemaining=休息所剩的时间
Timor-Leste=帝摩尔阅读
Tipperary=蒂珀雷里
Tlaxcala=Tlaxcala
Tocantins=托坎廷斯
Togo=多哥
Tokelau=托克劳
Tokyo=东京
Tonga=到达的
TonsAF=鲜基吨数
TonsAFSilo=鲜基吨数（窖里剩余）
TonsDM=干物质吨数
TonsDMSilo=干物质吨数（窖里剩余）
TonsPerDay=吨每天
ToolNotSelected=你必须选择至少一种工具
Top=上层
TopUnloadingSilo=顶部取料窖仓
TopUnloadingSilos=顶部取料窖仓
TopValue=（19mm）
Total=总计
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=牛舍内所有奶牛
TotalAnimalsHerd=所有奶牛
TotalPenPerScore=牛舍内每得分奶牛头数总计
TotalRevenue=总收益
TowerSilos=青贮塔
TowerSilos_FaceRemovalRateGreaterThan4Inches=取料面每天移动超过4英寸？
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=青贮是否在填满后密封，如果没有，需要在14天内用完。
TowerSilos_SiloFillingTimeof3DaysOrLess=青贮塔的填满时间是否小于3天？
TransitionCow=围产牛
Trapani=特拉帕尼
Tray=底层
Trends=积极趋势
Trento=特伦托
Treviso=特雷维索
Trieste=的里雅斯特
Trinidad_and_Tobago=特立尼达和多巴哥
Tripura=特里普拉邦
Tunisia=突尼斯
Turin=都灵
Turkey=火鸡
Turkmenistan=土库曼斯坦
Turks_and_Caicos_Islands=特克斯和凯科斯群岛
Tuvalu=图瓦卢
TwelveInchesOrGreater=12英寸或更高
TwentyFourHoursAndNoSync=二十四个小时，没有同步
TwentyFourHoursBeforeActionIsDue=动作到期前二十四个小时
TwentyFourToThirtySixInchesPerDay=24到36英寸每天
TwicePerWeek=每周两次
UAH=乌克兰 (UAH UAH)
UK=英国
UNITED_STATES=UNITED_STATES
US=美国
USD=美国 ($ USD)
Udine=乌迪内
Uganda=乌干达
Ukraine=乌克兰
UnitedKingdom=英国
UnitedStates=美国
United_Arab_Emirates=阿拉伯联合酋长国
United_Kingdom=英国
United_States=美国
UrinePH=平均值
UrinePHAVG=平均 尿液pH值
UrinePHAverageNumber=平均值
UrinePHDensity=尿液pH值资源
UrinePHEditCowViewModel.AddNew=下一头牛
UrinePHEditCowViewModel.CowName=奶牛编号
UrinePHEditCowViewModel.CowValue=奶牛价值
UrinePHEditCowViewModel.UrinePHEnterCowValue=输入奶牛价值
UrinePHEditCowViewModel.ValidCudInput=请输入有效值。
UrinePHEditGoalViewModel.GoalMax=目标－最大
UrinePHEditGoalViewModel.GoalMin=目标－最小
UrinePHEditGoalViewModel.TargetUrinePHRange=尿液pH值目标范围
UrinePHEditGoalViewModel.Title=编辑目标
UrinePHInputsViewModel.AddNew=添加新的
UrinePHInputsViewModel.CalculatorHeading=请选择一头奶牛输入尿液pH值。点击“添加新的”将奶牛添加到列表中。
UrinePHInputsViewModel.CoefficientVariation=变异系数 (C.V.) (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=范围之外的奶牛 (%)
UrinePHInputsViewModel.CudChewCategorySection=奶牛
UrinePHInputsViewModel.DietDCAD=日粮DCAD, mEq/100g
UrinePHInputsViewModel.Resources=资源
UrinePHInputsViewModel.TargetUrinePHRange=尿液pH值目标范围
UrinePHInputsViewModel.UrinePHAVG=平均尿液pH值（计算）
UrinePHInputsViewModel.UrinePhSTDDEV=标准差（计算）
UrinePHMasterViewModel.Inputs=输入
UrinePHMasterViewModel.Results=结果
UrinePHMasterViewModel.Title=尿液pH值
UrinePHPenSelectionViewModel.Title=尿液pH值
UrinePHPenSelectionViewModel.UrinePHPenList=圈舍（泌乳和干奶）
UrinePHResultsViewModel.DietDCAD=日粮DCAD, mEq/100g
UrinePHResultsViewModel.MaxpH=最大pH值
UrinePHResultsViewModel.MinpH=最小pH值
UrinePHResultsViewModel.UrinePHAVG=平均尿液pH值
Uruguay=乌拉圭
UserCreated=用户建立
UserPreferencesViewModel.Branding=走向市场 商标
UserPreferencesViewModel.Cargill=嘉吉
UserPreferencesViewModel.CurrencySelection=货币选择
UserPreferencesViewModel.Imperial=英制
UserPreferencesViewModel.MainHeading=以下选项能在app设置中进行更改。
UserPreferencesViewModel.Metric=公制
UserPreferencesViewModel.Provimi=普乐维美
UserPreferencesViewModel.ProvimiUS=Provimi US
UserPreferencesViewModel.Purina=普瑞纳
UserPreferencesViewModel.SelectCurrency=选择一种货币
UserPreferencesViewModel.SelectPointScale=选择评分制
UserPreferencesViewModel.Title=用户设置
UserPreferencesViewModel.UnitOfMeasure=选择测量单位
UserPreferencesViewModel.UserPreferencesMilkProcessor=设置乳企
UserPreferencesViewModel.UserPreferencesMoreSettings=更多设置
User_Settings=用户设置
Utah=犹他州
Uttar_Pradesh=北方邦
Uttarakhand=北阿坎德邦
Uzbekistan=乌兹别克斯坦
VEF=委内瑞拉 (Bs VEF)
VND=越南 (₫ VND)
Vanuatu=瓦努阿图
Varese=瓦雷泽
Venezuela=委内瑞拉
Venezuela,_Bolivarian_Republic_of=委内瑞拉，玻利瓦尔共和国
Venice=威尼斯
Veracruz=Veracruz
Verbano-Cusio-Ossola=韦尔巴诺-库西奥-奥索拉
Vercelli=韦尔切利
Vermont=佛蒙特
Verona=维罗纳
Vestland=韦斯特兰
Vibo_Valentia=维博瓦伦蒂亚
Vicenza=维森扎
Victoria=维多利亚
Viet_Nam=越南
Vietnam=越南
ViewOverallCalfHaiferScore=观察整体的犊牛和青年牛得分
ViewOverallForageScore=查看整体粗饲料评分
Virgin_Islands,_British=英国维尔京群岛
Virginia=弗吉尼亚
Visit.Report.Footer.Patent=由于多种因素，嘉吉公司及其母公司和附属公司不保证这些估计的准确性。无法保证生产或财务结果。©2023嘉吉公司。保留所有权利。
VisitAutoPublished=访问Auto出版
VisitDate=拜访日期
VisitDownloadProceed=进行
VisitNotebook=查看笔记本
VisitNotesViewModel.Action=执行
VisitNotesViewModel.Close=关闭
VisitNotesViewModel.DownloadingNotes=下载笔记。。。
VisitNotesViewModel.Event=事件
VisitNotesViewModel.New=新建
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=观察
VisitNotesViewModel.Task=任务
VisitNotesViewModel.Title=查看笔记本
VisitNotesViewModel.VisitNotebook=查看笔记本
VisitSummaryViewModel.CalfHeiferItem=犊牛和青年牛
VisitSummaryViewModel.CalfHeiferScorecard=评分卡
VisitSummaryViewModel.CategorySection=工具分类
VisitSummaryViewModel.ComfortHeatStressBanner=热应激工具栏
VisitSummaryViewModel.ComfortItem=舒适度
VisitSummaryViewModel.EmailReport=邮箱报告
VisitSummaryViewModel.FreeFormReport=自由格式报告
VisitSummaryViewModel.HealthItem=健康
VisitSummaryViewModel.HeatstressEvaluationTitle=热应激评估
VisitSummaryViewModel.HerdAnalysis=牛群分析
VisitSummaryViewModel.InputsOutputsChart=输入/输出/图表
VisitSummaryViewModel.MilkProcessRevenueCalculator=挤奶流程收益计算器
VisitSummaryViewModel.NoToolPrompt=工具尚未完成
VisitSummaryViewModel.NutritionForage=粗饲料审计
VisitSummaryViewModel.NutritionItem=营养
VisitSummaryViewModel.NutritionPile=料仓和料槽容量
VisitSummaryViewModel.PenTimeTitle=奶牛时间分配
VisitSummaryViewModel.ProductivityItem=生产能力
VisitSummaryViewModel.RumenHealthBodyConditionTitle=体况评分
VisitSummaryViewModel.RumenHealthLocomotionTitle=运动评分
VisitSummaryViewModel.RumenHealthManureTitle=瘤胃健康粪便评分
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=代谢病发病率
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk™
VisitSummaryViewModel.RumenHealthTMRTitle=瘤胃健康TMR颗粒度评分
VisitSummaryViewModel.RumenHealthTitle=瘤胃健康食团咀嚼
VisitSummaryViewModel.RumenHealthUrinePHTitle=尿液pH值
VisitSummaryViewModel.Title=牧场拜访概要
VisitSummaryViewModel.VisitSummaryMilkCalc=输入/结果/资源
VisitSummaryViewModel.VisitTitle=拜访名称
VisitViewModel.CalfHeiferItem=犊牛和青年牛
VisitViewModel.CategorySection=工具分类
VisitViewModel.ComfortItem=舒适度
VisitViewModel.Delete=删除拜访
VisitViewModel.DeletePrompt=确定删除吗？删除后不能撤回。
VisitViewModel.HealthItem=健康
VisitViewModel.Instructions=请在以下列表中选择一种类型或报告
VisitViewModel.NullVisitName=拜访名称不能为空白。请输入拜访名称。
VisitViewModel.NutritionItem=营养
VisitViewModel.ProductivityItem=生产能力
VisitViewModel.Publish=发布
VisitViewModel.PublishNotes=发布记录
VisitViewModel.PublishNotesPrompt=你确定要发布这次拜访的记录吗？发布后不能撤回。
VisitViewModel.PublishPrompt=你确定要发布这次拜访吗？发布后不能撤回。
VisitViewModel.PublishVisit=发布拜访
VisitViewModel.SiteVisitSummary=牧场拜访概要
VisitViewModel.Title=拜访详细信息
VisitViewModel.ToolCategories=工具类型
VisitViewModel.VisitNotebook=拜访笔记本
VisitViewModel.VisitTitle=拜访名称
VisitViewModel.WalkthroughReport=Walkthrough报告
Viterbo=Viterbo
VolumeImperial=加仑
VolumeMetric=毫升
WalkthroughPenSelectionViewModel.Pens=圈舍
WalkthroughPenSelectionViewModel.Title=Walkthrough报告
WalkthroughReport=Walkthrough报告
WalkthroughReportHerdAnalysisViewModel.Appearance=表现
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=卧床清洁度
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=垫料：深度和松软度
WalkthroughReportHerdAnalysisViewModel.Branding=走向市场 商标
WalkthroughReportHerdAnalysisViewModel.Cargill=嘉吉
WalkthroughReportHerdAnalysisViewModel.ComfortItem=奶牛舒适度，趴卧奶牛的比例
WalkthroughReportHerdAnalysisViewModel.Comments=评论
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=食团计数，食团咀嚼次数计数
WalkthroughReportHerdAnalysisViewModel.CudChewing=反刍，咀嚼奶牛的比例
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} 报告
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} 报告
WalkthroughReportHerdAnalysisViewModel.ExportSelected=邮件选择工具
WalkthroughReportHerdAnalysisViewModel.FinalObservations=最终观察结果
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=生成报告
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=飞节损伤，奶牛比例
WalkthroughReportHerdAnalysisViewModel.MainHeading=Walkthrough报告
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=流鼻涕，奶牛比例
WalkthroughReportHerdAnalysisViewModel.Notes=记录
WalkthroughReportHerdAnalysisViewModel.Opportunities=机会
WalkthroughReportHerdAnalysisViewModel.PensForExport=导出圈舍
WalkthroughReportHerdAnalysisViewModel.Provimi=普乐维美
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi US
WalkthroughReportHerdAnalysisViewModel.Purina=普瑞纳
WalkthroughReportHerdAnalysisViewModel.RumenFill=瘤胃充盈度
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=体况评分（BCS）
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=运动评分
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=粪便评分
WalkthroughReportHerdAnalysisViewModel.SubHeading=牛群分析
WalkthroughReportHerdAnalysisViewModel.Title=Walkthrough报告牛群分析
WalkthroughReportHerdAnalysisViewModel.Trends=积极趋势
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=子宫恶露，奶牛比例
WalkthroughReportHerdAnalysisViewModel.WaterQuality=饮水质量
WalkthroughReportLandingViewModel.HerdAnalysis=圈舍分析
WalkthroughReportLandingViewModel.PenAnalysis=牛圈分析
WalkthroughReportLandingViewModel.Title=Walkthrough报告
WalkthroughReportQualityViewModel.BeddingCleanliness=选择卧床清洁度
WalkthroughReportQualityViewModel.Clean=洁净
WalkthroughReportQualityViewModel.Dirty=脏
WalkthroughReportQualityViewModel.ModeratelyClean=中度洁净
WalkthroughReportQualityViewModel.Title=Walkthrough报告
WalkthroughReportQualityViewModel.WaterQuality=选择饮水质量
WalkthroughReportViewModel.Appearance=表现
WalkthroughReportViewModel.BeddingCleanliness=卧床清洁度
WalkthroughReportViewModel.BeddingDepthSoft=垫料：深度和松软度
WalkthroughReportViewModel.Clean=洁净
WalkthroughReportViewModel.ComfortItem=奶牛舒适度，趴卧奶牛的比例
WalkthroughReportViewModel.Comments=评论
WalkthroughReportViewModel.CudChewCategorySection=食团计数，每个食团咀嚼次数
WalkthroughReportViewModel.CudChewing=反刍，咀嚼奶牛的比例
WalkthroughReportViewModel.Current=现在
WalkthroughReportViewModel.Dirty=脏
WalkthroughReportViewModel.Goals=目标
WalkthroughReportViewModel.HockAbrasion=飞节损伤，奶牛比例
WalkthroughReportViewModel.ModeratelyClean=中度洁净
WalkthroughReportViewModel.NasalDischarge=流鼻涕，奶牛比例
WalkthroughReportViewModel.Opportunities=机会
WalkthroughReportViewModel.Previous=先前
WalkthroughReportViewModel.RumenFill=瘤胃充盈度
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=体况评分（BCS）
WalkthroughReportViewModel.RumenHealthLocomotionTitle=运动评分
WalkthroughReportViewModel.RumenHealthManureTitle=粪便评分
WalkthroughReportViewModel.Title=Walkthrough报告
WalkthroughReportViewModel.Trends=积极趋势
WalkthroughReportViewModel.UterineDischarge=子宫恶露，奶牛比例
WalkthroughReportViewModel.WaterQuality=饮水质量
Wallis_and_Futuna=沃利斯和五人
Washington=华盛顿
Waterford=沃特福德
Weekly=每周
WeightDMInLengthImperial=一英尺多少磅干物质
WeightDMInLengthMetric=一米多少千克干物质
WeightImperial=磅
WeightImperialCWT=英担
WeightMetric=千克
West_Bengal=西孟加拉邦
West_Virginia=西弗吉尼亚
Western_Australia=澳大利亚西部
Western_Sahara=撒哈拉沙漠西部
Westmeath=韦斯特米斯
Wexford=韦克斯福德
Wicklow=威克洛
Wisconsin=威斯康星州
WithinEightHours=在8小时内
Wyoming=怀俄明州
Xinjiang=新疆
Xizang=西藏
Yemen=也门
Yes=是
Yucatán=尤卡特
Yukon_Territories=育空地区
Yunnan=云南
ZAR=南非 (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=赞比亚
Zhejiang=浙江
Zimbabwe=津巴布韦
welcome.message=Greetings {0}
Holstein=Holstein
BrownSwiss=Brown Swiss
Ayrshire=Ayrshire
Conventional=Conventional
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole
同步时不要退出应用程序。较慢的连接可能需要更长的时间来同步。“=
提供的用户名或密码无效。=
粗饲料结果=
结果=
输入=
选择圈舍=
