/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class ReturnOverFeedKgFatReportDtoTest {

  @Test
  void testBuilder_shouldCreateValidDto() {
    // Given
    String fileName = "test_file.xlsx";
    String visitName = "Test Visit";
    String visitDate = "2025-01-15";
    String toolName = "Test Tool";
    String returnOverFeedKgPerFatLabel = "ROF Per Kg Fat Label";
    String rofPerKgButterFatLabel = "ROF Per Kg Butter Fat Label";
    String concentrateCostPerKgButterFatLabel = "Concentrate Cost Label";
    String xAxisLabel = "X Axis Label";
    String totalRevenueDollarKgPerFatLabel = "Total Revenue Label";
    String totalCostsPerKgPerFatLabel = "Total Costs Label";
    List<ReturnOverFeedPerKgFatDataPoints> dataPoints = new ArrayList<>();

    // When
    ReturnOverFeedKgFatReportDto dto = ReturnOverFeedKgFatReportDto.builder()
        .fileName(fileName)
        .visitName(visitName)
        .visitDate(visitDate)
        .toolName(toolName)
        .returnOverFeedKgPerFatLabel(returnOverFeedKgPerFatLabel)
        .rofPerKgButterFatLabel(rofPerKgButterFatLabel)
        .concentrateCostPerKgButterFatLabel(concentrateCostPerKgButterFatLabel)
        .xAxisLabel(xAxisLabel)
        .totalRevenueDollarKgPerFatLabel(totalRevenueDollarKgPerFatLabel)
        .totalCostsPerKgPerFatLabel(totalCostsPerKgPerFatLabel)
        .dataPoints(dataPoints)
        .build();

    // Then
    assertNotNull(dto);
    assertEquals(fileName, dto.getFileName());
    assertEquals(visitName, dto.getVisitName());
    assertEquals(visitDate, dto.getVisitDate());
    assertEquals(toolName, dto.getToolName());
    assertEquals(returnOverFeedKgPerFatLabel, dto.getReturnOverFeedKgPerFatLabel());
    assertEquals(rofPerKgButterFatLabel, dto.getRofPerKgButterFatLabel());
    assertEquals(concentrateCostPerKgButterFatLabel, dto.getConcentrateCostPerKgButterFatLabel());
    assertEquals(xAxisLabel, dto.getXAxisLabel());
    assertEquals(totalRevenueDollarKgPerFatLabel, dto.getTotalRevenueDollarKgPerFatLabel());
    assertEquals(totalCostsPerKgPerFatLabel, dto.getTotalCostsPerKgPerFatLabel());
    assertEquals(dataPoints, dto.getDataPoints());
  }

  @Test
  void testNoArgsConstructor_shouldCreateEmptyDto() {
    // When
    ReturnOverFeedKgFatReportDto dto = new ReturnOverFeedKgFatReportDto();

    // Then
    assertNotNull(dto);
    assertNull(dto.getFileName());
    assertNull(dto.getVisitName());
    assertNull(dto.getVisitDate());
    assertNull(dto.getToolName());
    assertNull(dto.getReturnOverFeedKgPerFatLabel());
    assertNull(dto.getRofPerKgButterFatLabel());
    assertNull(dto.getConcentrateCostPerKgButterFatLabel());
    assertNull(dto.getXAxisLabel());
    assertNull(dto.getTotalRevenueDollarKgPerFatLabel());
    assertNull(dto.getTotalCostsPerKgPerFatLabel());
    assertNotNull(dto.getDataPoints()); // Should be initialized as empty list due to @Builder.Default
    assertTrue(dto.getDataPoints().isEmpty());
  }

  @Test
  void testAllArgsConstructor_shouldCreateValidDto() {
    // Given
    String fileName = "test_file.xlsx";
    String visitName = "Test Visit";
    String visitDate = "2025-01-15";
    String toolName = "Test Tool";
    String returnOverFeedKgPerFatLabel = "ROF Per Kg Fat Label";
    String rofPerKgButterFatLabel = "ROF Per Kg Butter Fat Label";
    String concentrateCostPerKgButterFatLabel = "Concentrate Cost Label";
    String xAxisLabel = "X Axis Label";
    String totalRevenueDollarKgPerFatLabel = "Total Revenue Label";
    String totalCostsPerKgPerFatLabel = "Total Costs Label";
    List<ReturnOverFeedPerKgFatDataPoints> dataPoints = new ArrayList<>();

    // When
    ReturnOverFeedKgFatReportDto dto = new ReturnOverFeedKgFatReportDto(
        fileName, visitName, visitDate, toolName, returnOverFeedKgPerFatLabel,
        rofPerKgButterFatLabel, concentrateCostPerKgButterFatLabel, xAxisLabel,
        totalRevenueDollarKgPerFatLabel, totalCostsPerKgPerFatLabel, dataPoints);

    // Then
    assertNotNull(dto);
    assertEquals(fileName, dto.getFileName());
    assertEquals(visitName, dto.getVisitName());
    assertEquals(visitDate, dto.getVisitDate());
    assertEquals(toolName, dto.getToolName());
    assertEquals(returnOverFeedKgPerFatLabel, dto.getReturnOverFeedKgPerFatLabel());
    assertEquals(rofPerKgButterFatLabel, dto.getRofPerKgButterFatLabel());
    assertEquals(concentrateCostPerKgButterFatLabel, dto.getConcentrateCostPerKgButterFatLabel());
    assertEquals(xAxisLabel, dto.getXAxisLabel());
    assertEquals(totalRevenueDollarKgPerFatLabel, dto.getTotalRevenueDollarKgPerFatLabel());
    assertEquals(totalCostsPerKgPerFatLabel, dto.getTotalCostsPerKgPerFatLabel());
    assertEquals(dataPoints, dto.getDataPoints());
  }

  @Test
  void testSettersAndGetters_shouldWorkCorrectly() {
    // Given
    ReturnOverFeedKgFatReportDto dto = new ReturnOverFeedKgFatReportDto();
    String fileName = "updated_file.xlsx";
    String visitName = "Updated Visit";
    List<ReturnOverFeedPerKgFatDataPoints> dataPoints = List.of(
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(15.5)
            .visitDate("Week 1")
            .build()
    );

    // When
    dto.setFileName(fileName);
    dto.setVisitName(visitName);
    dto.setDataPoints(dataPoints);

    // Then
    assertEquals(fileName, dto.getFileName());
    assertEquals(visitName, dto.getVisitName());
    assertEquals(dataPoints, dto.getDataPoints());
    assertEquals(1, dto.getDataPoints().size());
  }

  @Test
  void testEquals_shouldReturnTrueForSameContent() {
    // Given
    ReturnOverFeedKgFatReportDto dto1 = ReturnOverFeedKgFatReportDto.builder()
        .fileName("test.xlsx")
        .visitName("Test Visit")
        .build();

    ReturnOverFeedKgFatReportDto dto2 = ReturnOverFeedKgFatReportDto.builder()
        .fileName("test.xlsx")
        .visitName("Test Visit")
        .build();

    // When & Then
    assertEquals(dto1, dto2);
    assertEquals(dto1.hashCode(), dto2.hashCode());
  }

  @Test
  void testEquals_shouldReturnFalseForDifferentContent() {
    // Given
    ReturnOverFeedKgFatReportDto dto1 = ReturnOverFeedKgFatReportDto.builder()
        .fileName("test1.xlsx")
        .visitName("Test Visit 1")
        .build();

    ReturnOverFeedKgFatReportDto dto2 = ReturnOverFeedKgFatReportDto.builder()
        .fileName("test2.xlsx")
        .visitName("Test Visit 2")
        .build();

    // When & Then
    assertNotEquals(dto1, dto2);
    assertNotEquals(dto1.hashCode(), dto2.hashCode());
  }

  @Test
  void testToString_shouldContainAllFields() {
    // Given
    ReturnOverFeedKgFatReportDto dto = ReturnOverFeedKgFatReportDto.builder()
        .fileName("test.xlsx")
        .visitName("Test Visit")
        .toolName("Test Tool")
        .build();

    // When
    String toString = dto.toString();

    // Then
    assertNotNull(toString);
    assertTrue(toString.contains("fileName"));
    assertTrue(toString.contains("visitName"));
    assertTrue(toString.contains("toolName"));
    assertTrue(toString.contains("test.xlsx"));
    assertTrue(toString.contains("Test Visit"));
    assertTrue(toString.contains("Test Tool"));
  }

  @Test
  void testDataPointsDefaultInitialization_shouldBeEmptyList() {
    // When
    ReturnOverFeedKgFatReportDto dto = ReturnOverFeedKgFatReportDto.builder().build();

    // Then
    assertNotNull(dto.getDataPoints());
    assertTrue(dto.getDataPoints().isEmpty());
  }

  @Test
  void testInheritanceFromBaseDto_shouldExtendBaseDto() {
    // Given
    ReturnOverFeedKgFatReportDto dto = new ReturnOverFeedKgFatReportDto();

    // When & Then
    assertTrue(dto instanceof BaseDto);
  }
}
