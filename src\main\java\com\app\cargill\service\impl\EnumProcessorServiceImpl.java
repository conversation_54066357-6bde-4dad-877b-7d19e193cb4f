/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.BCSPointScale;
import com.app.cargill.constants.BeddingQuality;
import com.app.cargill.constants.Breed;
import com.app.cargill.constants.BreedReturnOverFeed;
import com.app.cargill.constants.Business;
import com.app.cargill.constants.CowFlowDesign;
import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.FeedStorageType;
import com.app.cargill.constants.Feeding;
import com.app.cargill.constants.FeedingSystem;
import com.app.cargill.constants.HomeGrownForageTypes;
import com.app.cargill.constants.HomeGrownGrainTypes;
import com.app.cargill.constants.HousingSystem;
import com.app.cargill.constants.HttpMethods;
import com.app.cargill.constants.LabyrinthContentType;
import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.LangCodes;
import com.app.cargill.constants.MediaTypes;
import com.app.cargill.constants.MetabolicTypeKeys;
import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.constants.MobileDeviceType;
import com.app.cargill.constants.NoteCategoryType;
import com.app.cargill.constants.NotificationType;
import com.app.cargill.constants.OptimizationType;
import com.app.cargill.constants.PenSource;
import com.app.cargill.constants.ProductionSystem;
import com.app.cargill.constants.ProfitabilityAnalysisQuality;
import com.app.cargill.constants.ReturnOverFeedMilkProductionType;
import com.app.cargill.constants.RobotType;
import com.app.cargill.constants.RoboticMilkTrendsListType;
import com.app.cargill.constants.RumenHealthTmrScores;
import com.app.cargill.constants.SilageType;
import com.app.cargill.constants.SupplementTypes;
import com.app.cargill.constants.ToolScores;
import com.app.cargill.constants.UnitOfMeasureKeys;
import com.app.cargill.constants.UserSettingsBrands;
import com.app.cargill.constants.VisitReportType;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.constants.WaterQuality;
import com.app.cargill.dto.EnumSerializerDto;
import com.app.cargill.dto.MultilingualEnumSerializerDto;
import com.app.cargill.model.User;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.service.IEnumProcessorService;
import com.app.cargill.service.IUserService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("enumProcessorServiceImpl")
@RequiredArgsConstructor
public class EnumProcessorServiceImpl implements IEnumProcessorService {

  private final IUserService userServiceImpl;

  private final UserRepository userRepository;

  @Override
  public EnumSerializerDto fetchEnums() {

    return EnumSerializerDto.builder()
        .visitStatus(VisitStatus.values())
        .httpMethods(HttpMethods.values())
        .brandList(UserSettingsBrands.values())
        .unitOfMeasureKeys(UnitOfMeasureKeys.values())
        .bcsPointScale(BCSPointScale.values())
        .mobileDeviceType(MobileDeviceType.values())
        .visitReportType(VisitReportType.values())
        .locomotionScore(ToolScores.getLocomotionScore())
        .halfPointScale(ToolScores.getHalfPointScale())
        .quarterPointScale(ToolScores.getQuarterPointScale())
        .labyrinthContentTypes(LabyrinthContentType.values())
        .heatStressColours(IndexedColors.values())
        .metabolicTypeKeys(
            Arrays.stream(MetabolicTypeKeys.values()).map(Enum::name).toArray(String[]::new))
        .notesMediaTypes(MediaTypes.values())
        .optimizationTypes(OptimizationType.values())
        .penSources(PenSource.values())
        .dietSources(DietSource.values())
        .build();
  }

  @SuppressWarnings(
      "java:S3776") // Reducing complexity will only make the code harder to read without any
  // benefit
  @Override
  public MultilingualEnumSerializerDto fetchEnumsMultilingual(
      ResourceBundleMessageSource resourceBundleMessageSource) {

    MultilingualEnumSerializerDto dto = new MultilingualEnumSerializerDto();

    User user = userRepository.findByUserName(userServiceImpl.getCurrentLoggedInUser());

    List<UserSettingsBrands> brands = fetchBrandlistValues(user.getUserDocument().getCountryId());
    // housing system
    for (HousingSystem enumValue : HousingSystem.values()) {
      dto.getHousingSystem()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    // pen source
    for (PenSource enumValue : PenSource.values()) {
      dto.getPenSource()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    // Feeding System
    for (FeedingSystem enumValue : FeedingSystem.values()) {
      dto.getFeedingSystems()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    // Milking System
    for (MilkingSystem enumValue : MilkingSystem.values()) {
      dto.getMilkingSystem()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    // BCS Point scale
    for (BCSPointScale enumValue : BCSPointScale.values()) {
      dto.getBcsPointScales()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    // Lactation stage

    for (LactationStage enumValue : LactationStage.values()) {
      dto.getLactationStages()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    // countries
    for (Business enumValue : Business.values()) {
      dto.getBusiness()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    // BCS point scale
    for (BCSPointScale enumValue : BCSPointScale.values()) {
      dto.getBcsPointScale()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (CowFlowDesign enumValue : CowFlowDesign.values()) {
      dto.getCowFlowDesign()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (RobotType enumValue : RobotType.values()) {
      dto.getRobotType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    for (MilkPickup enumValue : MilkPickup.values()) {
      dto.getMilkPickup()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (MilkUreaMeasure enumValue : MilkUreaMeasure.values()) {
      dto.getMilkUreaMeasure()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    // Metabolic type keys
    for (MetabolicTypeKeys enumValue : MetabolicTypeKeys.values()) {
      dto.getMetabolicTypeKeys()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (RoboticMilkTrendsListType enumValue : RoboticMilkTrendsListType.values()) {
      dto.getRoboticMilkTrendsListType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    Arrays.stream(Currencies.values())
        .parallel()
        .sorted(
            Comparator.comparing(
                i ->
                    resourceBundleMessageSource.getMessage(
                        i.name(), new Object[] {}, i.name(), Locale.ENGLISH)))
        .forEachOrdered(
            enumValue ->
                dto.getCurrencies()
                    .add(
                        Collections.singletonMap(
                            enumValue.name(),
                            getAllLangCaptionsForSingleEnum(
                                enumValue.name(), resourceBundleMessageSource))));

    for (UnitOfMeasureKeys enumValue : UnitOfMeasureKeys.values()) {
      dto.getUnitOfMeasureKeys()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (UserSettingsBrands brand : brands) {
      dto.getBrandList()
          .add(
              Collections.singletonMap(
                  brand.name(),
                  getAllLangCaptionsForSingleEnum(brand.name(), resourceBundleMessageSource)));
    }

    // NoteCategoryType
    for (NoteCategoryType enumValue : NoteCategoryType.values()) {
      dto.getNoteCategoryType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    for (RumenHealthTmrScores enumValue : RumenHealthTmrScores.values()) {
      dto.getScorers()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    for (SilageType enumValue : SilageType.values()) {
      dto.getSilageType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (FeedStorageType enumValue : FeedStorageType.values()) {
      dto.getFeedStorageType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (NotificationType enumValue : NotificationType.values()) {
      dto.getNotificationType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (Breed enumValue : Breed.values()) {
      dto.getBreed()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (ProductionSystem enumValue : ProductionSystem.values()) {
      dto.getProductionSystem()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (WaterQuality enumValue : WaterQuality.values()) {
      dto.getWaterQuality()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (ProfitabilityAnalysisQuality enumValue : ProfitabilityAnalysisQuality.values()) {
      dto.getProfitabilityQualityAnalysis()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (BeddingQuality enumValue : BeddingQuality.values()) {
      dto.getBeddingQuality()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (BreedReturnOverFeed enumValue : BreedReturnOverFeed.values()) {
      dto.getBreedReturnOverFeed()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (Feeding enumValue : Feeding.values()) {
      dto.getFeeding()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    for (SupplementTypes enumValue : SupplementTypes.values()) {
      dto.getSupplementTypes()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    for (HomeGrownForageTypes enumValue : HomeGrownForageTypes.values()) {
      dto.getHomeGrownForageTypes()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    for (HomeGrownGrainTypes enumValue : HomeGrownGrainTypes.values()) {
      dto.getHomegrownGrainTypes()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }
    for (ReturnOverFeedMilkProductionType enumValue : ReturnOverFeedMilkProductionType.values()) {
      dto.getReturnOverFeedMilkProductionType()
          .add(
              Collections.singletonMap(
                  enumValue.name(),
                  getAllLangCaptionsForSingleEnum(enumValue.name(), resourceBundleMessageSource)));
    }

    return dto;
  }

  private List<UserSettingsBrands> fetchBrandlistValues(Business countryId) {
    List<UserSettingsBrands> brands = new ArrayList<>();
    if (countryId.equals(Business.Canada)) {
      brands.add(UserSettingsBrands.Cargill);
      brands.add(UserSettingsBrands.Purina);
    } else if (countryId.equals(Business.Italy)) {
      brands.add(UserSettingsBrands.Agridea);
      brands.add(UserSettingsBrands.Cargill);
      brands.add(UserSettingsBrands.RaggioDiSole);
    } else {
      brands.add(UserSettingsBrands.Provimi);
      brands.add(UserSettingsBrands.Cargill);
      brands.add(UserSettingsBrands.Purina);
    }
    return brands;
  }

  private Map<String, String> getAllLangCaptionsForSingleEnum(
      String enumValue, ResourceBundleMessageSource resourceBundleMessageSource) {
    Map<String, String> keyCaptionSet = new LinkedHashMap<>();
    for (LangCodes code : LangCodes.values()) {
      Locale locale = Locale.forLanguageTag(code.name());
      String value =
          resourceBundleMessageSource.getMessage(enumValue, new Object[] {}, enumValue, locale);
      keyCaptionSet.put(code.name(), value);
    }
    return keyCaptionSet;
  }
}
