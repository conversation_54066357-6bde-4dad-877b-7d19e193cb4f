/* Cargill Inc.(C) 2022 */
package com.app.cargill.schedulers;

import com.app.cargill.service.admin.UserAdminService;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class APPUsageAnalyticsScheduler {

  private final UserAdminService userAdminService;

  /*
   * runs at 1 am
   */
  @Scheduled(cron = "0 0 1 * * *")
  public void userDailyLoginAnalytics() {
    log.info("userDailyLoginAnalytics invoked");
    Instant dateTo = Instant.now();
    Instant dateFrom = dateTo.minus(24, ChronoUnit.HOURS);
    log.info("userDailyLoginAnalytics Start Date : " + dateFrom);
    log.info("userDailyLoginAnalytics End Date : " + dateTo);
    userAdminService.getUsersAllLogin(dateFrom, dateTo);
  }
}
