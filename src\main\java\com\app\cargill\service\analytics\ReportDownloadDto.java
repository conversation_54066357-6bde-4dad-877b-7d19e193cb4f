/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.analytics;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * { "createdDate": "2023-12-21 18:07:42.944", "siteId": "ebfd6221-a780-47af-8269-296df2001c6f",
 * "userId": "52af68f3-7f3f-4758-be34-eeae93008327", "accountId":
 * "240ffe85-3767-47cd-9f6e-b6aa5c958560", "userEmail": "<EMAIL>", "accountName":
 * "Ferme Paul Massicotte et fil Inc.", "userCountry": "Canada" }
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ReportDownloadDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Instant createdTime;
  private String siteId;
  private String userId;
  private String accountId;
  private String userEmail;
  private String accountName;
  private String userCountry;
}
