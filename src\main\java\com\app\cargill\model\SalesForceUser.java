/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.document.SalesForceUserDocument;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "salesforceusers")
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class SalesForceUser extends BaseEntity implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /* public SalesForceUser(SalesForceUserDocument salesForceUserDocument) {
    this.email = salesForceUserDocument.getUserName();
    this.salesForceUserDocument = salesForceUserDocument;
  }*/

  @Column(length = 100)
  @NotNull
  @Email
  private String email;

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb")
  private SalesForceUserDocument salesForceUserDocument;
}
