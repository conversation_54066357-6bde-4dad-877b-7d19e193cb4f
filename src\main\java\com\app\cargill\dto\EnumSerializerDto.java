/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.poi.ss.usermodel.IndexedColors;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EnumSerializerDto {
  HttpMethods[] httpMethods;
  VisitStatus[] visitStatus;
  double[] locomotionScore;
  String[] metabolicTypeKeys;
  UserSettingsBrands[] brandList;
  UnitOfMeasureKeys[] unitOfMeasureKeys;

  MobileDeviceType[] mobileDeviceType;
  BCSPointScale[] bcsPointScale;
  MediaTypes[] notesMediaTypes;

  @JsonProperty("HalfPointScale")
  double[] halfPointScale;

  @JsonProperty("QuarterPointScale")
  double[] quarterPointScale;

  IndexedColors[] heatStressColours;

  LabyrinthContentType[] labyrinthContentTypes;

  VisitReportType[] visitReportType;

  OptimizationType[] optimizationTypes;

  PenSource[] penSources;

  DietSource[] dietSources;
}
