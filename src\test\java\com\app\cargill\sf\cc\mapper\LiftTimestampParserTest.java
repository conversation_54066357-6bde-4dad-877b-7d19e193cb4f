/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import java.time.Instant;
import org.junit.jupiter.api.Test;

class LiftTimestampParserTest {

  @Test
  void whenTimestampIsCorrectCorrectValueIsReturned() {
    String value = "2023-05-17T14:28:44.000+0000";
    Instant result = LiftTimestampParser.parse(value);
    assertNotNull(result);
    assertEquals("2023-05-17T14:28:44Z", result.toString());
  }

  @Test
  void whenTimestampIsNullSameIsReturned() {
    Instant result = LiftTimestampParser.parse(null);
    assertNull(result);
  }

  @Test
  void whenTimestampIsWrongMinIsReturned() {
    Instant result = LiftTimestampParser.parse("wrong");
    assertEquals(Instant.MIN, result);
  }
}
