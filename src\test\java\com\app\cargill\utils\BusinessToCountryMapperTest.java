/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Country;
import org.junit.jupiter.api.Test;

class BusinessToCountryMapperTest {
  @Test
  void testValues() {
    assertEquals(Country.UNDEFINED, BusinessToCountryMapper.businessToCountry(null));
    assertEquals(Country.BRAZIL, BusinessToCountryMapper.businessToCountry(Business.Brazil));
    assertEquals(Country.BRAZIL, BusinessToCountryMapper.businessToCountry(Business.CPNBrazil));
    assertEquals(Country.FRANCE, BusinessToCountryMapper.businessToCountry(Business.France));
    assertEquals(Country.FRANCE, BusinessToCountryMapper.businessToCountry(Business.CPNFrance));
    assertEquals(
        Country.NETHERLANDS, BusinessToCountryMapper.businessToCountry(Business.Netherlands));
    assertEquals(
        Country.PHILIPPINES, BusinessToCountryMapper.businessToCountry(Business.Philippines));
    assertEquals(Country.POLAND, BusinessToCountryMapper.businessToCountry(Business.Poland));
    assertEquals(Country.POLAND, BusinessToCountryMapper.businessToCountry(Business.CPNPoland));
    assertEquals(Country.UNITED_STATES, BusinessToCountryMapper.businessToCountry(Business.US));
    assertEquals(Country.UNITED_STATES, BusinessToCountryMapper.businessToCountry(Business.CPNUS));
    assertEquals(
        Country.UNITED_STATES, BusinessToCountryMapper.businessToCountry(Business.UNITED_STATES));
    assertEquals(
        Country.UNITED_STATES, BusinessToCountryMapper.businessToCountry(Business.NorthAmerica));
    assertEquals(Country.VIETNAM, BusinessToCountryMapper.businessToCountry(Business.Vietnam));
    assertEquals(Country.SPAIN, BusinessToCountryMapper.businessToCountry(Business.Spain));
    assertEquals(Country.ITALY, BusinessToCountryMapper.businessToCountry(Business.Italy));
    assertEquals(Country.KOREA, BusinessToCountryMapper.businessToCountry(Business.Korea));
    assertEquals(Country.INDIA, BusinessToCountryMapper.businessToCountry(Business.India));
    assertEquals(Country.INDIA, BusinessToCountryMapper.businessToCountry(Business.CFNIndia));
    assertEquals(Country.MEXICO, BusinessToCountryMapper.businessToCountry(Business.Mexico));
    assertEquals(Country.RUSSIA, BusinessToCountryMapper.businessToCountry(Business.Russia));
    assertEquals(
        Country.SOUTH_AFRICA, BusinessToCountryMapper.businessToCountry(Business.SouthAfrica));
    assertEquals(Country.CHINA, BusinessToCountryMapper.businessToCountry(Business.China));
    assertEquals(Country.CHINA, BusinessToCountryMapper.businessToCountry(Business.CFNChina));
    assertEquals(Country.PORTUGAL, BusinessToCountryMapper.businessToCountry(Business.Portugal));
    assertEquals(Country.UKRAINE, BusinessToCountryMapper.businessToCountry(Business.Ukraine));
    assertEquals(Country.HUNGARY, BusinessToCountryMapper.businessToCountry(Business.Hungary));
    assertEquals(Country.UNITED_KINGDOM, BusinessToCountryMapper.businessToCountry(Business.UK));
    assertEquals(Country.PAKISTAN, BusinessToCountryMapper.businessToCountry(Business.Pakistan));
    assertEquals(Country.ARGENTINA, BusinessToCountryMapper.businessToCountry(Business.Argentina));
  }
}
