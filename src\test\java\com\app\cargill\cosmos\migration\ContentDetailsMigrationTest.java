/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.migration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.cosmos.migration.CosmosDataMigration.MigrationType;
import com.app.cargill.cosmos.model.ContentDetailsCosmos;
import com.app.cargill.cosmos.repo.ContentDetailsCosmosRepository;
import com.app.cargill.model.ContentDetails;
import com.app.cargill.repository.ContentDetailsRepository;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import reactor.core.publisher.Flux;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ContentDetailsMigrationTest {

  @Mock private ContentDetailsCosmosRepository cosmosRepository;

  @Mock private ContentDetailsRepository dbRepository;

  @InjectMocks private ContentDetailsMigration migration;

  @Test
  void whenMigrationPassesCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    ContentDetailsCosmos contentDetailsCosmos1 = new ContentDetailsCosmos();
    contentDetailsCosmos1.setId(UUID.randomUUID().toString());
    contentDetailsCosmos1.setAccountId(UUID.randomUUID().toString());

    ContentDetailsCosmos contentDetailsCosmos2 = new ContentDetailsCosmos();
    contentDetailsCosmos2.setId(UUID.randomUUID().toString());
    contentDetailsCosmos2.setAccountId(UUID.randomUUID().toString());
    Flux<ContentDetailsCosmos> cosmosFlux = Flux.just(contentDetailsCosmos1, contentDetailsCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(ContentDetails.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(2, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenMigrationPassesButSomeRecordsFailCorrectResultIsReturned()
      throws ExecutionException, InterruptedException {
    ContentDetailsCosmos contentDetailsCosmos1 = new ContentDetailsCosmos();
    contentDetailsCosmos1.setId(UUID.randomUUID().toString());
    contentDetailsCosmos1.setAccountId(UUID.randomUUID().toString());

    ContentDetailsCosmos contentDetailsCosmos2 = new ContentDetailsCosmos();
    contentDetailsCosmos2.setId(UUID.randomUUID().toString());
    contentDetailsCosmos2.setAccountId(UUID.randomUUID().toString());

    ContentDetailsCosmos contentDetailsCosmos3 = new ContentDetailsCosmos();
    contentDetailsCosmos3.setId(UUID.randomUUID().toString());
    contentDetailsCosmos3.setAccountId(UUID.randomUUID().toString());

    Flux<ContentDetailsCosmos> cosmosFlux =
        Flux.just(contentDetailsCosmos1, contentDetailsCosmos2, contentDetailsCosmos3);
    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.findByDocumentId(any()))
        .thenThrow(new IllegalStateException())
        .thenReturn(null);
    when(dbRepository.save(any()))
        .thenThrow(new RuntimeException("Custom RuntimeException Message"))
        .thenReturn(mock(ContentDetails.class));

    MigrationResult migrationResult = migration.moveAll().get();

    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(2, migrationResult.getFailed());
  }

  @Test
  void whenDataIsWrongThePipelinePassesButFailsAreMarked()
      throws ExecutionException, InterruptedException {
    ContentDetailsCosmos contentDetailsCosmos1 = new ContentDetailsCosmos();
    contentDetailsCosmos1.setId("random-string");
    contentDetailsCosmos1.setAccountId(UUID.randomUUID().toString());

    ContentDetailsCosmos contentDetailsCosmos2 = new ContentDetailsCosmos();
    contentDetailsCosmos2.setId(UUID.randomUUID().toString());
    contentDetailsCosmos2.setAccountId(UUID.randomUUID().toString());
    Flux<ContentDetailsCosmos> cosmosFlux = Flux.just(contentDetailsCosmos1, contentDetailsCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(ContentDetails.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(1, migrationResult.getFailed());
  }

  @Test
  void whenAccountIdIsMissingTheRecordIsSkipped() throws ExecutionException, InterruptedException {
    ContentDetailsCosmos contentDetailsCosmos1 = new ContentDetailsCosmos();
    contentDetailsCosmos1.setId(UUID.randomUUID().toString());
    contentDetailsCosmos1.setAccountId(null);

    ContentDetailsCosmos contentDetailsCosmos2 = new ContentDetailsCosmos();
    contentDetailsCosmos2.setId(UUID.randomUUID().toString());
    contentDetailsCosmos2.setAccountId(UUID.randomUUID().toString());
    Flux<ContentDetailsCosmos> cosmosFlux = Flux.just(contentDetailsCosmos1, contentDetailsCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(ContentDetails.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void whenAccountIdIsInvalidTheRecordIsSkipped() throws ExecutionException, InterruptedException {
    ContentDetailsCosmos contentDetailsCosmos1 = new ContentDetailsCosmos();
    contentDetailsCosmos1.setId(UUID.randomUUID().toString());
    contentDetailsCosmos1.setAccountId("invalid-id");

    ContentDetailsCosmos contentDetailsCosmos2 = new ContentDetailsCosmos();
    contentDetailsCosmos2.setId(UUID.randomUUID().toString());
    contentDetailsCosmos2.setAccountId(UUID.randomUUID().toString());
    Flux<ContentDetailsCosmos> cosmosFlux = Flux.just(contentDetailsCosmos1, contentDetailsCosmos2);

    when(cosmosRepository.findAll()).thenReturn(cosmosFlux);
    when(dbRepository.save(any())).thenReturn(mock(ContentDetails.class));

    MigrationResult migrationResult = migration.moveAll().get();
    assertEquals(1, migrationResult.getSucceeded());
    assertEquals(0, migrationResult.getFailed());
  }

  @Test
  void correctTypeIsReturned() {
    assertEquals(MigrationType.CONTENT_DETAILS, migration.migrationType());
  }
}
