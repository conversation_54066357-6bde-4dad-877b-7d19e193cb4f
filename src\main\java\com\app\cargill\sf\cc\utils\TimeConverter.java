/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import java.text.SimpleDateFormat;
import java.time.Instant;

public class TimeConverter {

  private TimeConverter() {}

  public static Instant fromSfTime(String time) {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSz");
    try {
      return formatter.parse(time).toInstant();
    } catch (Exception e) {
      throw new IllegalArgumentException(e);
    }
  }
}
