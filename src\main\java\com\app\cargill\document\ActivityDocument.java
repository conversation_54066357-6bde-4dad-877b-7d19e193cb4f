/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.Types;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(Include.NON_NULL)
public class ActivityDocument extends EditableDocumentBase implements Serializable {

  @JsonProperty("ActivityDateTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant activityDateTime;

  @JsonProperty("ExtendedAttributes")
  private Map<String, String> extendedAttributes;

  @JsonProperty("ActivityType")
  private Integer activityType;

  @JsonProperty("AccountLevelAccess")
  private String accountLevelAccess;

  @JsonProperty("Subject")
  private String subject;

  @JsonProperty("RecordTypeId")
  private String recordTypeId; // SiteVisit

  @JsonProperty("IsPrivate")
  private Boolean isPrivate;

  @JsonProperty("IsAllDayEvent")
  private Boolean isAllDayEvent;

  @JsonProperty("IsGroupEvent")
  private Boolean isGroupEvent;

  @JsonProperty("EventLocation")
  private String eventLocation;

  @JsonProperty("EventStartDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant eventStartDate;

  @JsonProperty("AllDayEvent")
  private Boolean allDayEvent;

  @JsonProperty("SFDCAccountId")
  private String sfdcAccountId;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("ComplaintRaised")
  private Boolean complaintRaised;

  @JsonProperty("TrainingScheduled")
  private Boolean trainingScheduled;

  @JsonProperty("NeedsSync")
  private Boolean needsSync;

  @JsonProperty("SFDCId")
  private String sfdcId;

  @JsonProperty("SFDCVisitId")
  private String sfdcVisitId;

  @JsonProperty("AccountId")
  private UUID accountId;

  @JsonProperty("AssignedToID")
  private String assignedToID;

  @JsonProperty("EndDateTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant endDateTime;

  @JsonProperty("EventRecordTypeID")
  private Integer eventRecordTypeID;

  @JsonProperty("SubTypeID")
  private Integer subTypeID;

  @JsonProperty("CancelledFlag")
  private Boolean cancelledFlag;

  @JsonProperty("RelatedToTypeID")
  private Integer relatedToTypeID;

  @JsonProperty("RelatedToListId")
  private String relatedToListId;

  @JsonProperty("ExternalRelatedToListId")
  private UUID externalRelatedToListId; // Look Up Account

  @JsonProperty("NameListId")
  private String nameListId; // look up contact

  @JsonProperty("ExternalNameListId")
  private UUID externalNameListId;

  @JsonProperty("NameTypeID")
  private Integer nameTypeID;

  @JsonProperty("ServiceFlag")
  private Boolean serviceFlag;

  @JsonProperty("ClaimComplaintFlag")
  private Boolean claimComplaintFlag;

  @JsonProperty("Location")
  private String location;

  @JsonProperty("ShowTimeAsID")
  private Integer showTimeAsID;

  @JsonProperty("DescriptionInfo")
  private String descriptionInfo;

  @JsonProperty("BusinessID")
  private Integer businessID;

  @JsonProperty("SegmentID")
  private Integer segmentID;

  @JsonProperty("ProductLineID")
  private Integer productLineID;

  @JsonProperty("GroupID")
  private Integer groupID;

  @JsonProperty("ClassID")
  private Integer classID;

  @JsonProperty("ProductFunctionID")
  private List<Integer> productFunctionID;

  @JsonProperty("ReminderFollowupDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant reminderFollowupDate;

  @JsonProperty("ReminderDateTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant reminderDateTime;

  @JsonProperty("ReminderFlag")
  private Boolean reminderFlag;

  @JsonProperty("ApprovalRequestNo")
  private Integer approvalRequestNo;

  @JsonProperty("ReadyforApprovalFlag")
  private Boolean readyforApprovalFlag;

  @JsonProperty("DueDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant dueDate;

  @JsonProperty("Comments")
  private String comments;

  @JsonProperty("StatusId")
  private Integer statusId;

  @JsonProperty("PriorityId")
  private Integer priorityId;

  @JsonProperty("Recurrence")
  private Integer recurrence;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("ActivityCurrency")

  // New Fields as per DD
  private Currencies activityCurrency;

  @JsonProperty("ApprovalStatus")
  private String approvalStatus;

  @JsonProperty("Approved")
  private String approved;

  @JsonProperty("CallDuration")
  private String callDuration;

  @JsonProperty("CallNotesUpdated")
  private String callNotesUpdated;

  @JsonProperty("CallObjectIdentifier")
  private String callObjectIdentifier;

  @JsonProperty("CallResult")
  private String callResult;

  @JsonProperty("CallType")
  private String callType;

  @JsonProperty("Cancelled")
  private Boolean cancelled;

  @JsonProperty("ClaimComplaint")
  private Boolean claimComplaint;

  @JsonProperty("CompletedDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant completedDate;

  @JsonProperty("CreatedBy")
  private String createdBy;

  @JsonProperty("DBActivityType")
  private String dbActivityType;

  @JsonProperty("DueDateFlag")
  private Boolean dueDateFlag;

  @JsonProperty("Email")
  private String email;

  @JsonProperty("ExternalAccountId")
  private String externalAccountId;

  @JsonProperty("ExternalId")
  private String externalId;

  @JsonProperty("MobileFirst")
  private Boolean mobileFirst;

  @JsonProperty("OwnerSManagerRoleName")
  private String ownerSManagerRoleName;

  @JsonProperty("OwnerRole")
  private String ownerRole;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("ReadyforApproval")
  private String readyforApproval;

  @JsonProperty("RemindertoScheduleFollowUp")
  private String remindertoScheduleFollowUp;

  @JsonProperty("RepeatThisTask")
  private String repeatThisTask;

  @JsonProperty("ReportLink")
  private String reportLink;

  @JsonProperty("Sales")
  private Boolean sales;

  @JsonProperty("Service")
  private Boolean service;

  @JsonProperty("SourceSystem")
  private String sourceSystem;

  @JsonProperty("TaskSubtype")
  private String taskSubtype;

  @JsonProperty("TimeTaken")
  private String timeTaken;

  @JsonProperty("Type")
  private Types type;

  @JsonProperty("VisitReport")
  private String visitReport;

  @JsonProperty("Weekno")
  private String weekNo;

  @JsonProperty("YearMonth")
  private String yearMonth;

  @JsonProperty("CreateRecurringSeriesOfTasks")
  private Boolean createRecurringSeriesOfTasks;

  @JsonProperty("Frequency")
  private Integer frequency;

  @JsonProperty("EveryWeekDay")
  private Boolean everyWeekDay;

  @JsonProperty("EveryNthDay")
  private Integer everyNthDay;

  @JsonProperty("RecurrenceStart")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant recurrenceStart;

  @JsonProperty("RecurrenceEnd")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant recurrenceEnd;

  @JsonProperty("Time")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant time; //

  @JsonProperty("TimeTakenHours")
  private String timeTakenHours;

  @JsonProperty("EventSfdcId")
  private String eventSfdcId;

  @JsonProperty("DataSource")
  private DataSource dataSource;
}
