/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.AnalyticsDataPoint;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface AnalyticsRepository<T extends Serializable>
    extends JpaRepository<AnalyticsDataPoint<T>, Long> {

  @Query(
      value =
          "SELECT a.* FROM analytics a where a.datapoint_name= :dataPointName AND"
              + " timezone('UTC',a.created_date) >= :from AND timezone('UTC',a.created_date) <="
              + " :to",
      nativeQuery = true)
  List<AnalyticsDataPoint<HashMap<String, String>>> findAllByType(
      @Param("dataPointName") String dataPointName,
      @Param("from") Instant from,
      @Param("to") Instant to);
}
