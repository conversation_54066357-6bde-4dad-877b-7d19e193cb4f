/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.LinkedHashMap;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RumenFillHealthHerdAnalysisReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String analysisType;

  @Builder.Default
  private LinkedHashMap<String, Double> averageRumenFillScore = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> min = new LinkedHashMap<>();

  @Builder.Default private LinkedHashMap<String, Double> max = new LinkedHashMap<>();
  @JsonIgnore private String[] lactationStages;
}
