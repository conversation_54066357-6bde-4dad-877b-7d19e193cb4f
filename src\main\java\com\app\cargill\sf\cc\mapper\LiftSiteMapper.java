/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.sf.cc.model.ExternalDataSource;
import com.app.cargill.sf.cc.model.simple.Site;
import com.app.cargill.sf.cc.utils.TimeConverter;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LiftSiteMapper {
  private LiftSiteMapper() {}

  public static SiteDocument transform(Site input) {
    try {
      return innerTransform(input);
    } catch (Exception e) {
      log.error("Cannot transform Site", e);
      throw new IllegalArgumentException("Site transformation error");
    }
  }

  private static SiteDocument innerTransform(Site input) {

    SiteDocument siteDocument = new SiteDocument();
    Boolean hasReport = checkHerdId(input);
    siteDocument.setId(
        input.getExternalId() != null ? UUID.fromString(input.getExternalId()) : null);
    siteDocument.setExternalId(input.getId());
    siteDocument.setCreateUser(input.getCreatedBy().getName());
    siteDocument.setDeleted(input.isDeleted());
    siteDocument.setLastModifyUser(input.getModifiedBy().getName());
    siteDocument.setCreateTimeUtc(TimeConverter.fromSfTime(input.getCreatedDate()));
    siteDocument.setLastModifiedTimeUtc(TimeConverter.fromSfTime(input.getLastModifiedDate()));
    if (input.getAccountRecord().getExternalId() != null) {
      siteDocument.setAccountId(UUID.fromString(input.getAccountRecord().getExternalId()));
    }
    siteDocument.setExternalAccountId(input.getAccountRecord().getId());
    siteDocument.setSiteName(input.getSiteName());
    siteDocument.setCurrentMilkPrice(input.getCurrentMilkPrice());
    if (input.getHousingType() != null) {
      siteDocument.setMilkingSystemType(MilkingSystem.valueOf(input.getHousingType()));
    }
    if ("Lactating Cows".equals(input.getSubSpecies())) {
      siteDocument.setLactatingAnimal(input.getNumberOfAnimals());
    }
    if (input.getExternalDataSources() != null) {
      siteDocument.setDataSourceMappings(
          input.getExternalDataSources().getRecords().stream()
              .map(LiftSiteMapper::mapSiteMapping)
              .toList());
    }
    siteDocument.setHerdSummaryReport(input.getHerdSummaryReport());
    siteDocument.setHerdStatusReport(input.getHerdStatusReport());
    siteDocument.setDataSource(DataSource.LIFT);
    siteDocument.setHasReport(hasReport);
    return siteDocument;
  }

  private static Boolean checkHerdId(Site input) {
    if (input.getExternalDataSources() != null) {
      for (ExternalDataSource dataSource : input.getExternalDataSources().getRecords()) {
        if ("DDW".equals(dataSource.getSystem())) {
          return true;
        }
      }
    }
    return false;
  }

  public static DataSourceMapping mapSiteMapping(ExternalDataSource dataSource) {
    return new DataSourceMapping(
        dataSource.getSystem(), dataSource.getUniqueExternalKey(), dataSource.getId());
  }
}
