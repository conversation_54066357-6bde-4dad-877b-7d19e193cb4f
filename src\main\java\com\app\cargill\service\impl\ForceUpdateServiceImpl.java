/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.dto.ForceUpdateDto;
import com.app.cargill.model.BuildInformation;
import com.app.cargill.repository.BuildInformationRepository;
import com.app.cargill.service.IForceUpdateService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("ForceUpdateServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125"})
public class ForceUpdateServiceImpl implements IForceUpdateService {

  private final BuildInformationRepository buildInformationRepository;

  @Override
  public ForceUpdateDto getBuildInfo() {
    BuildInformation buildInformation = buildInformationRepository.getBuildInfo();

    return mapToDto(buildInformation);
  }

  private ForceUpdateDto mapToDto(BuildInformation buildInformation) {
    if (Objects.isNull(buildInformation)) return null;

    if (Objects.isNull(buildInformation.getBuildInfoDocument())) return null;

    return ForceUpdateDto.builder()
        .androidVersion(buildInformation.getBuildInfoDocument().getAndroidVersion())
        .iosVersion(buildInformation.getBuildInfoDocument().getIosVersion())
        .isForceUpdateAndroid(buildInformation.getBuildInfoDocument().getIsForceUpdateAndroid())
        .isForceUpdateIos(buildInformation.getBuildInfoDocument().getIsForceUpdateIos())
        .build();
  }
}
