/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class ManureScreenerToolItemDto extends EditableToolPenEntityBaseDto {

  /** */
  private static final long serialVersionUID = 7928335324366970552L;

  private List<UUID> visitsSelected;

  private Boolean isToolItemNew;

  private UUID mstScoreId;

  private Double topScaleAmountInGrams;

  private Double topGoalMinimumPercent;

  private Double topGoalMaximumPercent;

  private Double midScaleAmountInGrams;

  private Double midGoalMinimumPercent;

  private Double midGoalMaximumPercent;

  private Double bottomScaleAmountInGrams;

  private Double bottomGoalMinimumPercent;

  private Double bottomGoalMaximumPercent;

  private String mstScoreName;

  private ToolStatuses toolStatus;

  private Boolean isFirstTimeWithScore;

  private String observation;
}
