/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.VisitStatus;
import com.app.cargill.document.*;
import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.impl.CdpServiceImpl;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class ManureScoreCalculationTest {

  @InjectMocks private ManureScoreCalculation manureScoreCalculation;
  @Mock private VisitsRepository visitsRepository;

  @Mock private SitesRepository sitesRepository;

  @InjectMocks private CdpServiceImpl cdpService;

  @Test
  void calculateFields() {
    RumenHealthManureScoreTool rumenHealthManureScoreTool =
        RumenHealthManureScoreTool.builder()
            .pens(
                List.of(
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    new RumenHealthManureScoreItem[] {
                                      RumenHealthManureScoreItem.builder()
                                          .animalNumbers(10)
                                          .scoreCategory(1.0)
                                          .build()
                                    })
                                .build())
                        .build()))
            .goals(
                List.of(
                    RumenHealthManureScoreToolGoalItem.builder()
                        .goalMax(10.2)
                        .goalMin(5.1)
                        .build()))
            .build();

    rumenHealthManureScoreTool = manureScoreCalculation.calculateFields(rumenHealthManureScoreTool);

    assertTrue(
        rumenHealthManureScoreTool.getPens().stream()
            .allMatch(
                pen ->
                    pen.getManureScores().averageValue != null
                        && pen.getManureScores().standardDeviation != null));

    assertNotNull(rumenHealthManureScoreTool.getGoals().get(0));
  }

  @Test
  void calculateFieldsWithNull() {
    RumenHealthManureScoreTool rumenHealthManureScoreTool =
        RumenHealthManureScoreTool.builder()
            .pens(
                List.of(
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    new RumenHealthManureScoreItem[] {
                                      RumenHealthManureScoreItem.builder().build()
                                    })
                                .build())
                        .build()))
            .goals(List.of(RumenHealthManureScoreToolGoalItem.builder().build()))
            .build();

    rumenHealthManureScoreTool = manureScoreCalculation.calculateFields(rumenHealthManureScoreTool);

    assertTrue(
        rumenHealthManureScoreTool.getPens().stream()
            .allMatch(
                pen ->
                    pen.getManureScores().averageValue != null
                        && pen.getManureScores().standardDeviation != null));

    assertNotNull(rumenHealthManureScoreTool.getGoals().get(0));
  }

  @Test
  void verifyCalculations() {
    RumenHealthManureScoreTool manureScoreTool =
        RumenHealthManureScoreTool.builder()
            .pens(
                List.of(
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(2.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(6)
                                                .scoreCategory(3.0)
                                                .percentOfPen(75.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(2)
                                                .scoreCategory(4.0)
                                                .percentOfPen(25.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(0.0)
                                                .build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .averageValue(3.25)
                                .build())
                        .build(),
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(1)
                                                .scoreCategory(2.0)
                                                .percentOfPen(12.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(7)
                                                .scoreCategory(3.0)
                                                .percentOfPen(87.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(4.0)
                                                .percentOfPen(4.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(5.0)
                                                .build())
                                        .toArray((new RumenHealthManureScoreItem[0])))
                                .averageValue(2.875)
                                .build())
                        .build(),
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(2.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(2)
                                                .scoreCategory(3.0)
                                                .percentOfPen(66.67)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(1)
                                                .scoreCategory(4.0)
                                                .percentOfPen(33.33)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(5.0)
                                                .build())
                                        .toArray((new RumenHealthManureScoreItem[0])))
                                .averageValue(3.3333)
                                .build())
                        .build()))
            .build();

    manureScoreTool = manureScoreCalculation.calculateFields(manureScoreTool);

    assertEquals(3.25, manureScoreTool.getPens().get(0).getManureScores().getAverageValue());
    assertEquals(0.43, manureScoreTool.getPens().get(0).getManureScores().getStandardDeviation());

    assertEquals(2.875, manureScoreTool.getPens().get(1).getManureScores().getAverageValue());
    assertEquals(0.33, manureScoreTool.getPens().get(1).getManureScores().getStandardDeviation());

    assertEquals(3.3333, manureScoreTool.getPens().get(2).getManureScores().getAverageValue());
    assertEquals(0.47, manureScoreTool.getPens().get(2).getManureScores().getStandardDeviation());
  }

  @Test
  void verifyNameAndToolName() {
    RumenHealthManureScoreTool manureScoreTool =
        RumenHealthManureScoreTool.builder()
            .visitId(UUID.fromString("d6a1d4b4-9251-4bcd-a4ba-6bf416421b29"))
            .pens(
                List.of(
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(2.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(6)
                                                .scoreCategory(3.0)
                                                .percentOfPen(75.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(2)
                                                .scoreCategory(4.0)
                                                .percentOfPen(25.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(0.0)
                                                .build())
                                        .toArray(new RumenHealthManureScoreItem[0]))
                                .averageValue(3.25)
                                .build())
                        .build(),
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(1)
                                                .scoreCategory(2.0)
                                                .percentOfPen(12.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(7)
                                                .scoreCategory(3.0)
                                                .percentOfPen(87.5)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(4.0)
                                                .percentOfPen(4.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(5.0)
                                                .build())
                                        .toArray((new RumenHealthManureScoreItem[0])))
                                .averageValue(2.875)
                                .build())
                        .build(),
                    RumenHealthManureScoreToolItem.builder()
                        .manureScores(
                            RumenHealthManureScores.builder()
                                .items(
                                    List.of(
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(1.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(2.0)
                                                .percentOfPen(0.0)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(2)
                                                .scoreCategory(3.0)
                                                .percentOfPen(66.67)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(1)
                                                .scoreCategory(4.0)
                                                .percentOfPen(33.33)
                                                .build(),
                                            RumenHealthManureScoreItem.builder()
                                                .animalNumbers(0)
                                                .scoreCategory(5.0)
                                                .percentOfPen(5.0)
                                                .build())
                                        .toArray((new RumenHealthManureScoreItem[0])))
                                .averageValue(3.3333)
                                .build())
                        .build()))
            .build();

    VisitDocument visitDocument =
        VisitDocument.builder()
            .id(UUID.fromString("d6a1d4b4-9251-4bcd-a4ba-6bf416421b29"))
            .siteId(UUID.fromString("9a35cdcc-048a-4963-8790-27771b324b2e"))
            .customerId(UUID.fromString("ec628ef0-4b2c-4477-8d93-bef603e91e8b"))
            .visitDate(Instant.parse("2023-08-15T07:50:05.43278Z"))
            .firstName("Bhajan Dass")
            .selected(false)
            .visitName("August 15, 2023")
            .status(VisitStatus.Published)
            .formattedCreationDate("August 8, 2023")
            .needsSync(true)
            .rumenHealthManureScore(manureScoreTool)
            .build();

    List<Visits> visits =
        List.of(
            Visits.builder()
                .id(1L)
                .localId(UUID.randomUUID().toString())
                .visitDocument(visitDocument)
                .build());

    Page<Visits> visitPages = new PageImpl<>(visits);

    when(visitsRepository.findAllVisitsByFromAndToDate(any(), any(), any())).thenReturn(visitPages);

    when(sitesRepository.findMilkBySiteIds(any())).thenReturn(new ArrayList<>());

    Page<VisitDocumentDTO> response = cdpService.getAllVisitsByFromAndToDate(any(), any(), any());

    assertNotNull(response);

    List<AttributesDTO> filteredList =
        response.getContent().get(0).getAttributes().stream()
            .filter(
                attributesDTO ->
                    attributesDTO != null
                        && attributesDTO
                            .getName()
                            .equalsIgnoreCase("Pens1/ManureScores/Items5/PercentOfPen"))
            .toList();

    assertEquals("Pens1/ManureScores/Items5/PercentOfPen", filteredList.get(0).getName());

    assertEquals(BigDecimal.valueOf(0.0), filteredList.get(0).getNumericValue());
  }
}
