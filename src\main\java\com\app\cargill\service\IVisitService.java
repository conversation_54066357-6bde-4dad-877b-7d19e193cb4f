/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.*;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;

public interface IVisitService {

  List<VisitDto> fetchAllVisits();

  Page<VisitDto> getAllVisitsByCurrentLoggedInUser(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting);

  VisitDto save(
      VisitDto visitDto, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource)
      throws NotFoundDEException, AlreadyExistsDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions;

  VisitDto update(
      VisitDto visitDto, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource)
      throws NotFoundDEException, JsonProcessingException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions;

  Page<VisitDto> getAllVisitsBySearchPaginated(
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      VisitSearchDto visitSearchDto);

  VisitPublishResponseDto autoPublish(
      List<VisitPublishDto> visitPublishDto,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions;

  Page<ISelectDto> getVisitIdAndNameList(
      int page, int size, String sortBy, Instant lastSyncTime, String sorting);
}
