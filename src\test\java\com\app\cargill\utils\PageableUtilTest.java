/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Pageable;

class PageableUtilTest {

  @Test
  void whenDescendingCorrectOrderIsReturned() {
    Pageable result = PageableUtil.getPageable(0, 1, "id", "desc");
    assertEquals("id: DESC", result.getSort().toString());
  }

  @Test
  void whenAscendingCorrectOrderIsReturned() {
    Pageable result = PageableUtil.getPageable(0, 1, "id", "asc");
    assertEquals("id: ASC", result.getSort().toString());
  }

  @Test
  void whenUnknownSortOrderIsPassedAscIsReturned() {
    Pageable result = PageableUtil.getPageable(0, 1, "id", "unknown");
    assertEquals("id: ASC", result.getSort().toString());
  }
}
