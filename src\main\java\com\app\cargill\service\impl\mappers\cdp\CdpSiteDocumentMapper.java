/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers.cdp;

import com.app.cargill.constants.DietSource;
import com.app.cargill.constants.FormulateStatus;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.OptimizationType;
import com.app.cargill.document.AnimalClass;
import com.app.cargill.document.DietDocument;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.dto.cdp.site.BarnDTO;
import com.app.cargill.dto.cdp.site.SiteDocumentDTO;
import com.app.cargill.model.CdpSiteDataWrapper;
import com.app.cargill.utils.DeepCopy;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.javatuples.Triplet;

@Slf4j
public class CdpSiteDocumentMapper {

  private static final int DEFAULT_ANALYZE_OPTIMIZATION_ID = 10;
  private static final int DEFAULT_FORMULATE_OPTIMIZATION_ID = 20;
  private static final String DEFAULT_ANALYZE_OPTIMIZATION_TYPE = "Analyze";
  private static final String DEFAULT_FORMULATE_OPTIMIZATION_TYPE = "Formulate";
  private static final FormulateStatus DEFAULT_OPTIMIZATION_STATUS = FormulateStatus.NA;

  private static final UUID LACTATING_PASTURE_ID =
      UUID.fromString("********-0000-0000-0000-********0004");
  private static final UUID LACTATING_MILKING_ID =
      UUID.fromString("********-0000-0000-0000-********0002");
  private static final UUID DRY_CLOSEUP_ID =
      UUID.fromString("********-0000-0000-0000-********0007");
  private static final UUID LACTATING_LOW_FORGE_ID =
      UUID.fromString("********-0000-0000-0000-********0003");
  private static final UUID LACTATING_AA_EFFICIENCY_ID =
      UUID.fromString("********-0000-0000-0000-********0006");
  private static final UUID LACTATING_FRESH_HEIFER_ID =
      UUID.fromString("********-0000-0000-0000-********0005");
  private static final UUID DRY_FAR_OFF_ID =
      UUID.fromString("********-0000-0000-0000-********0008");
  private static final UUID MALE_BULL_ID = UUID.fromString("********-0000-0000-0000-********0013");
  private static final UUID DRY_SHORT_DRY_PERIOD_ID =
      UUID.fromString("********-0000-0000-0000-********0010");
  private static final UUID HEIFER_ID = UUID.fromString("********-0000-0000-0000-********0011");
  private static final UUID DRY_CLOSEUP_HEIFER_ID =
      UUID.fromString("********-0000-0000-0000-********0009");
  private static final UUID CALF_ID = UUID.fromString("********-0000-0000-0000-********0012");
  private static final UUID MALE_STEER_ID = UUID.fromString("********-0000-0000-0000-************");
  private static final UUID LACTATING_FRESH_ID =
      UUID.fromString("********-0000-0000-0000-************");

  private CdpSiteDocumentMapper() {}

  public static SiteDocumentDTO mapToDto(SiteDocument siteDocument) {

    if (siteDocument == null) {
      String errorMessage = "SiteDocument should not be null";
      log.error(errorMessage);
      throw new IllegalArgumentException(errorMessage);
    }

    return SiteDocumentDTO.builder()
        .id(siteDocument.getId())
        // accountId and getExternalAccountId are switched for no specific reason but for backwards
        // compatibility
        .cdpAccountId(siteDocument.getExternalAccountId())
        .cdpExternalAccountId(
            siteDocument.getAccountId() != null ? siteDocument.getAccountId().toString() : null)
        .accountId(siteDocument.getAccountId())
        .externalAccountId(siteDocument.getExternalAccountId())
        .externalId(siteDocument.getExternalId())
        .siteName(siteDocument.getSiteName())
        .currentMilkPrice(siteDocument.getCurrentMilkPrice())
        .daysInMilk(siteDocument.getDaysInMilk())
        .milkingSystemType(siteDocument.getMilkingSystemType())
        .dryMatterIntake(siteDocument.getDryMatterIntake())
        .lactatingAnimal(siteDocument.getLactatingAnimal())
        .milk(siteDocument.getMilk())
        .milkFatPercent(siteDocument.getMilkFatPercent())
        .milkProteinPercent(siteDocument.getMilkProteinPercent())
        .milkOtherSolidsPercent(siteDocument.getMilkOtherSolidsPercent())
        .milkSomaticCellCount(siteDocument.getMilkSomaticCellCount())
        .bacteriaCellCount(siteDocument.getBacteriaCellCount())
        .netEnergyOfLactationDairy(siteDocument.getNetEnergyOfLactationDairy())
        .rationCost(siteDocument.getRationCost())
        .visits(siteDocument.getVisits())
        .asFedIntake(siteDocument.getAsFedIntake())
        .dataSourceMappings(siteDocument.getDataSourceMappings())
        .origination(siteDocument.getOrigination())
        .dateOfLastVisit(siteDocument.getDateOfLastVisit())
        .dDWLastUpdatedDate(siteDocument.getDDWLastUpdatedDate())
        .numberOfParlorStalls(siteDocument.getNumberOfParlorStalls())
        .needsSync(siteDocument.getNeedsSync())
        .dataSource(siteDocument.getDataSource())
        .keys(siteDocument.getKeys())
        .build();
  }

  public static SiteDocumentDTO mapV1ToDto(CdpSiteDataWrapper siteWrapper) {
    SiteDocumentDTO siteDocumentDTO = mapToDto(siteWrapper.getSiteDocument());
    addBarns(siteDocumentDTO, siteWrapper);
    //    addDiets(siteDocumentDTO, siteWrapper);
    return siteDocumentDTO;
  }

  private static void addBarns(SiteDocumentDTO siteDocumentDTO, CdpSiteDataWrapper siteWrapper) {

    log.trace("Pens created count: {}", siteWrapper.getPensData().size());
    List<BarnDTO> barnDtoList =
        siteWrapper.getSiteDocument().getBarns().stream()
            .map(
                barn -> {
                  log.trace("BarnId: {}", barn.getId());
                  // Using a Set to avoid duplicates
                  Set<PenDocument> uniquePens =
                      siteWrapper.getPensData().stream()
                          .filter(
                              penDocument -> {
                                log.trace(
                                    "Pen id: {} Barn id: {}",
                                    penDocument.getBarnId(),
                                    barn.getId());
                                setDietsDataForPens(penDocument);
                                penDocument.setOptimizationId(
                                    fetchOptimizationId(penDocument.getOptimizationType()));
                                return penDocument.getBarnId() != null
                                    && penDocument.getBarnId().compareTo(barn.getId()) == 0;
                              })
                          .collect(Collectors.toSet());

                  return BarnDTO.builder()
                      .id(barn.getId())
                      .barnName(barn.getBarnName())
                      .createUser(barn.getCreateUser())
                      .pens(new ArrayList<>(uniquePens)) // Convert Set back to List
                      .build();
                })
            .toList();

    siteDocumentDTO.setBarnsDTO(barnDtoList);
  }

  private static Integer fetchOptimizationId(OptimizationType optimizationType) {
    if (optimizationType == null) return null;

    if (optimizationType.equals(OptimizationType.ANALYZE)) {
      return 10;
    } else if (optimizationType.equals(OptimizationType.FORMULATE)) {
      return 20;
    }

    return null;
  }

  public static void setDietsDataForPens(PenDocument penDocument) {
    penDocument.setAnimalType(
        penDocument.getAnimalClassId() != null
            ? getAnimalClassById(penDocument.getAnimalClassId())
            : null);
  }

  @SuppressWarnings(
      "unused") // added as this code might be required in the future, but right now the function
  // isn't in use.
  private static void addDiets(SiteDocumentDTO siteDocumentDTO, CdpSiteDataWrapper siteWrapper) {
    // There is a setting in the old code that sets pen optimization id to default. But it is not
    // used anywhere and useless. So it is not used here.
    List<UUID> availablePenDietIdList =
        siteDocumentDTO.getBarnsDTO().stream()
            .flatMap(barnDTO -> barnDTO.getPens().stream())
            .map(PenDocument::getDietId)
            .toList();

    // Add diets to sites and only the ones that do not have system generated values and not in pens
    List<DietDocument> dietsList =
        siteWrapper.getDietsData().stream()
            .filter(
                dietDocument ->
                    dietDocument != null
                        && (Boolean.TRUE.equals(dietDocument.getIsSystemGenerated())
                            || availablePenDietIdList.contains(dietDocument.getId())))
            .map(
                dietDocument -> {
                  addPensToSelectedGuids(siteDocumentDTO, dietDocument);
                  return dietDocument;
                })
            .toList();
    siteDocumentDTO.setDiets(dietsList);

    createFormulations(siteDocumentDTO);
  }

  private static void addPensToSelectedGuids(
      SiteDocumentDTO siteDocumentDTO, DietDocument dietDocument) {
    // Add pend ids to selectGuids in diet where pen.DietId = diet.id
    List<UUID> selectedGuids =
        siteDocumentDTO.getBarnsDTO().stream()
            .flatMap(barnDTO -> barnDTO.getPens().stream())
            .filter(
                penDocument ->
                    penDocument != null
                        && penDocument.getDietId() != null
                        && penDocument.getDietId().compareTo(dietDocument.getId()) == 0)
            .map(PenDocument::getId)
            .toList();

    dietDocument.setSelectedPenGuids(selectedGuids);
  }

  private static void createFormulations(SiteDocumentDTO siteDocumentDTO) {
    // Duplicate the diet list with a deep copy of what it has - AnalyzeDiet and also update
    // existing diet
    List<DietDocument> analyzeDietList = new ArrayList<>();

    for (DietDocument dietItem : siteDocumentDTO.getDiets()) {
      // No need for duplicate diet for source max and no formulation and optimization need
      if (dietItem.getSource() == DietSource.MAX
          && dietItem.getFormulateOptimization() == null
          && dietItem.getAnalyzeOptimization() == null) {
        continue;
      }

      /* Duplicate the diet list with a deep copy of what it has - analyzeDiet.
      There will be two diets - formulate and analyze with same data.
      Making temp analyzeDietItem variable to create duplicate analyze diet.
      dietItem will always contain formulate diet.
      After all operation analyze diet will be saved together with itemDiet
      into the diets in SiteDocument */
      DietDocument analyzeDietItem = (DietDocument) DeepCopy.copy(dietItem);

      // making this diet formulate
      dietItem.setAnalyzeOptimization(null);

      // making this diet analyze
      analyzeDietItem.setFormulateOptimization(null);

      // UPDATE Existing dietItem
      updateExistingDietItem(dietItem);

      // Update analyzeDietItem and add to analyzeDietList
      updateAnalyzeItem(analyzeDietItem);

      log.debug("Adding to list item diet: {}", analyzeDietItem);
      analyzeDietList.add(analyzeDietItem);
    }

    log.trace("Updating diets in siteDocumentDTO");
    List<DietDocument> updatedDietDocument = new ArrayList<>(siteDocumentDTO.getDiets());

    log.trace("Adding new analyze diets to existing diets");
    updatedDietDocument.addAll(analyzeDietList);
    siteDocumentDTO.setDiets(updatedDietDocument);
  }

  private static void updateAnalyzeItem(DietDocument analyzeDietItem) {
    if (analyzeDietItem.getSource() == DietSource.MAX
        && analyzeDietItem.getAnalyzeOptimization() != null) {
      analyzeDietItem.setOptimizationId(DEFAULT_ANALYZE_OPTIMIZATION_ID);
      analyzeDietItem.setOptimizationType(DEFAULT_ANALYZE_OPTIMIZATION_TYPE);
      analyzeDietItem.setOptimizationStatus(
          analyzeDietItem.getAnalyzeOptimization().getStatus().name());
    } else if (analyzeDietItem.getSource() == DietSource.USER_CREATED
        && analyzeDietItem.getAnalyzeOptimization() == null) {
      analyzeDietItem.setOptimizationId(DEFAULT_ANALYZE_OPTIMIZATION_ID);
      analyzeDietItem.setOptimizationType(DEFAULT_ANALYZE_OPTIMIZATION_TYPE);
      analyzeDietItem.setOptimizationStatus(DEFAULT_OPTIMIZATION_STATUS.name());
    }
  }

  private static void updateExistingDietItem(DietDocument dietItem) {
    if (dietItem.getSource() == DietSource.MAX && dietItem.getFormulateOptimization() != null) {
      dietItem.setOptimizationId(DEFAULT_FORMULATE_OPTIMIZATION_ID);
      dietItem.setOptimizationType(DEFAULT_FORMULATE_OPTIMIZATION_TYPE);
      dietItem.setOptimizationStatus(dietItem.getFormulateOptimization().getStatus().name());
    } else if (dietItem.getSource() == DietSource.USER_CREATED
        && dietItem.getFormulateOptimization() == null) {
      dietItem.setOptimizationId(DEFAULT_FORMULATE_OPTIMIZATION_ID);
      dietItem.setOptimizationType(DEFAULT_FORMULATE_OPTIMIZATION_TYPE);
      dietItem.setOptimizationStatus(DEFAULT_OPTIMIZATION_STATUS.name());
    }
  }

  public static List<Triplet<UUID, String, String>> getAnimalClassList() {
    var animals = new ArrayList<Triplet<UUID, String, String>>();

    animals.add(new Triplet<>(LACTATING_MILKING_ID, LangKeys.LACTATING, LangKeys.MILKING));
    animals.add(new Triplet<>(HEIFER_ID, LangKeys.HEIFER, LangKeys.HEIFER));
    animals.add(new Triplet<>(LACTATING_LOW_FORGE_ID, LangKeys.LACTATING, LangKeys.LOWFORGE));
    animals.add(
        new Triplet<>(LACTATING_FRESH_HEIFER_ID, LangKeys.LACTATING, LangKeys.FRESH_HEIFER));
    animals.add(new Triplet<>(LACTATING_PASTURE_ID, LangKeys.LACTATING, LangKeys.PASTURE));
    animals.add(new Triplet<>(LACTATING_FRESH_ID, LangKeys.LACTATING, LangKeys.FRESH));

    animals.add(new Triplet<>(DRY_FAR_OFF_ID, LangKeys.DRY, LangKeys.FAR_OFF));
    animals.add(new Triplet<>(DRY_CLOSEUP_ID, LangKeys.DRY, LangKeys.CLOSE_UP));
    animals.add(
        new Triplet<>(LACTATING_AA_EFFICIENCY_ID, LangKeys.LACTATING, LangKeys.AAEFFICIENCY));
    animals.add(new Triplet<>(DRY_CLOSEUP_HEIFER_ID, LangKeys.DRY, LangKeys.CLOSE_UP_HEIFER));
    animals.add(new Triplet<>(DRY_SHORT_DRY_PERIOD_ID, LangKeys.DRY, LangKeys.SHORT_DRY_PERIOD));

    animals.add(new Triplet<>(MALE_STEER_ID, LangKeys.MALE, LangKeys.STEER));

    animals.add(new Triplet<>(CALF_ID, LangKeys.CALF, LangKeys.CALF));

    animals.add(new Triplet<>(MALE_BULL_ID, LangKeys.MALE, LangKeys.BULL));

    return animals;
  }

  public static AnimalClass getAnimalClassById(UUID animalClassId) {

    List<Triplet<UUID, String, String>> animalList = getAnimalClassList();

    return animalList.stream()
        .filter(animal -> animal.getValue0().equals(animalClassId))
        .findFirst()
        .map(animal -> new AnimalClass(animal.getValue0(), animal.getValue1(), animal.getValue2()))
        .orElse(null);
  }
}
