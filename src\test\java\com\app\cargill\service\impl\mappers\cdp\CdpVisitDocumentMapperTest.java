/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers.cdp;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.document.*;
import com.app.cargill.dto.cdp.visit.AttributesDTO;
import com.app.cargill.dto.cdp.visit.VisitDocumentDTO;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CdpVisitDocumentMapperTest {

  @Mock private VisitDocument visitDocument;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testMapToDto() {
    // Arrange
    UUID visitId = UUID.randomUUID();
    UUID customerId = UUID.randomUUID();
    UUID siteId = UUID.randomUUID();

    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setId(visitId);
    visitDocument.setCustomerId(customerId);
    visitDocument.setSiteId(siteId);
    visitDocument.setVisitDate(Instant.now());
    visitDocument.setStatus(VisitStatus.Published);
    visitDocument.setVisitName("Visit 1");
    visitDocument.setFormattedCreationDate("2023-06-22");
    visitDocument.setSelectedCurrency(Currencies.USD);
    visitDocument.setCreateUser("User1");
    visitDocument.setDeleted(false);
    visitDocument.setLastModifiedTimeUtc(Instant.now());
    visitDocument.setLastSyncTimeUtc(Instant.now());
    visitDocument.setNew(true);
    visitDocument.setLastModifyUser("User2");
    visitDocument.setCreateTimeUtc(Instant.now());
    visitDocument.setMobileLastUpdatedTime(Instant.now());

    visitDocument.setHeatStress(HeatStressTool.builder().build());
    visitDocument.setMetabolicIncidence(MetabolicIncidenceTool.builder().build());
    visitDocument.setBodyCondition(BodyConditionTool.builder().build());
    visitDocument.setForageAuditScorecard(Scorecard.builder().build());
    visitDocument.setForagePennState(ForagePennStateTool.builder().build());
    visitDocument.setLocomotionScore(LocomotionTool.builder().build());
    visitDocument.setManureScreenerTool(ManureScreenerTool.builder().build());
    visitDocument.setPenTimeBudgetTool(PenTimeBudgetTool.builder().build());
    visitDocument.setMilkSoldEvaluation(MilkSoldEvaluationTool.builder().build());
    visitDocument.setRumenHealth(RumenHealthTool.builder().build());
    visitDocument.setRevenue(RevenueInputs.builder().build());
    visitDocument.setRoboticMilkEvaluation(RoboticMilkEvaluationTool.builder().build());
    visitDocument.setTmrParticleScore(RumenHealthTMRParticleScoreTool.builder().build());
    visitDocument.setRumenHealthManureScore(RumenHealthManureScoreTool.builder().build());
    visitDocument.setReadyToMilk(ReadyToMilkTool.builder().build());
    visitDocument.setUrinePHTool(UrinePHTool.builder().build());
    visitDocument.setCalfHeiferScorecard(CalfHeiferScorecard.builder().build());
    visitDocument.setRumenFillManureScore(RumenFillTool.builder().build());

    // Act
    VisitDocumentDTO visitDocumentDTO = CdpVisitDocumentMapper.mapToDto(visitDocument);

    // Assert
    assertEquals(visitId, visitDocumentDTO.getId());
    assertEquals(customerId, visitDocumentDTO.getAccountId());
    assertEquals(siteId, visitDocumentDTO.getSiteId());
    assertNotNull(visitDocumentDTO.getVisitDate());
    assertEquals(VisitStatus.Published, visitDocumentDTO.getStatus());
    assertEquals("Visit 1", visitDocumentDTO.getVisitName());
    assertEquals("2023-06-22", visitDocumentDTO.getFormattedCreationDate());
    assertEquals(Currencies.USD, visitDocumentDTO.getSelectedCurrency());
    assertEquals("User1", visitDocumentDTO.getCreateUser());
    Assertions.assertFalse(visitDocumentDTO.isDeleted());
    assertNotNull(visitDocumentDTO.getLastModifiedTimeUtc());
    assertNotNull(visitDocumentDTO.getLastSyncTimeUtc());
    Assertions.assertTrue(visitDocumentDTO.isNew());
    assertEquals("User2", visitDocumentDTO.getLastModifyUser());
    assertNotNull(visitDocumentDTO.getCreateTimeUtc());
    assertNotNull(visitDocumentDTO.getMobileLastUpdatedTime());

    //    List<AttributesDTO> expectedAttributes = Collections.emptyList();
    //    assertEquals(expectedAttributes, visitDocumentDTO.getAttributes());
    assertFalse(visitDocumentDTO.getAttributes().isEmpty());
  }

  @Test
  @DisplayName("Test setAttributes method")
  void testSetAttributes() {
    VisitDocument visitDocument = new VisitDocument();
    visitDocument.setHeatStress(HeatStressTool.builder().dmiReductionPercent(10.0).build());
    visitDocument.setRumenHealth(RumenHealthTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setMetabolicIncidence(
        MetabolicIncidenceTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setBodyCondition(BodyConditionTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setForageAuditScorecard(Scorecard.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setLocomotionScore(
        LocomotionTool.builder()
            .herd(LocomotionHerdToolItem.builder().daysInMilk(2.0).build())
            .visitId(UUID.randomUUID())
            .build());
    visitDocument.setManureScreenerTool(
        ManureScreenerTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setMetabolicIncidence(
        MetabolicIncidenceTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setForagePennState(
        ForagePennStateTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setMilkSoldEvaluation(
        MilkSoldEvaluationTool.builder()
            .visitMilkEvaluationData(MilkSoldEvaluationToolItem.builder().daysInMilk(20).build())
            .build());
    visitDocument.setPenTimeBudgetTool(
        PenTimeBudgetTool.builder().visitId(UUID.randomUUID()).build());
    PileAndBunker pileAndBunker = PileAndBunker.builder().createTimeUtc(Instant.now()).build();
    List<PileAndBunker> pileAndBunkers = new ArrayList<>();
    pileAndBunkers.add(pileAndBunker);
    visitDocument.setPileAndBunker(
        PileAndBunkerTool.builder()
            .pileBunkers(pileAndBunkers)
            .visitId(UUID.randomUUID())
            .createTimeUtc(Instant.now())
            .build());
    visitDocument.setRoboticMilkEvaluation(
        RoboticMilkEvaluationTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setTmrParticleScore(
        RumenHealthTMRParticleScoreTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setRumenFillManureScore(
        RumenFillTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setAnimalAnalysis(
        AnimalAnalysisTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setCalfHeiferScorecard(
        CalfHeiferScorecard.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setCudChewing(CudChewingTool.builder().visitId(UUID.randomUUID()).build());
    visitDocument.setUrinePHTool(UrinePHTool.builder().visitId(UUID.randomUUID()).build());

    List<AttributesDTO> expectedAttributes = new ArrayList<>();
    expectedAttributes.add(
        AttributesDTO.builder()
            .name("dmiReductionPercent\n")
            .numericValue(BigDecimal.valueOf(10.0))
            .build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(
        AttributesDTO.builder().name("daysInMilk").numericValue(BigDecimal.valueOf(2.0)).build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(
        AttributesDTO.builder().name("daysInMilk").numericValue(BigDecimal.valueOf(20.0)).build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());
    expectedAttributes.add(AttributesDTO.builder().name("VisitId").build());

    List<AttributesDTO> actualAttributes = CdpVisitDocumentMapper.setAttributes(visitDocument);

    // assert expectedAttributes size is bigger than actualAttributes size as there are also
    // standard attributes
    assertTrue(actualAttributes.size() > expectedAttributes.size());
  }
}
