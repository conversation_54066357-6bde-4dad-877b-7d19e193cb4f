/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Activities;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ActivitiesRepository extends JpaRepository<Activities, Long> {

  @Query(
      value =
          "select  count(activity_document->>'VisitId') > 0 as end from activities where"
              + " activity_document->>'VisitId'= :visitId AND deleted=false",
      nativeQuery = true)
  boolean existByVisitId(@Param("visitId") String visitId);

  @Query(
      value =
          "SELECT * from activities where activity_document->>'VisitId' = :visitId AND"
              + " deleted=false",
      nativeQuery = true)
  Activities findByVisitId(@Param("visitId") String visitId);

  @Query(
      value = "SELECT a.* FROM activities a where a.activity_document ->> 'id' = :id",
      nativeQuery = true)
  Activities findByDocumentId(@Param("id") String id);

  @Query(
      value =
          "SELECT a.* FROM activities a where a.activity_document ->> 'id' = :id and a.deleted="
              + " false",
      nativeQuery = true)
  Activities findByDocumentIdAndDeleted(@Param("id") String id);

  @Query(
      value =
          "select * from activities where timezone('UTC',updated_date) > :from and"
              + "  deleted = false and activity_document ->> 'DataSource' = 'CRESCENDO'",
      nativeQuery = true)
  List<Activities> getActivitiesByUpdatedDateAfterForCrescendo(Instant from);
}
