/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
/// <summary>
/// A question that can be part of a scorecard
/// </summary>
public class CalfHeiferScorecardQuestionDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  /// <summary>
  /// Indicates where this question is presented in the order of all questions.
  /// </summary>
  /// <value>The index.</value>
  private Integer index;
  /// <summary>
  /// The question of the text to be displayed to the user.
  /// </summary>
  /// <value>The question text.</value>
  /// <remarks>This will be a key to the String resources files.</remarks>
  private String questionText;
  /// <summary>
  /// Indicates if the scorecard this question is part of can be considered
  /// complete
  /// if this question is not answered.
  /// </summary>
  /// <value>The is question required.</value>
  private Boolean isQuestionRequired;

  /// <summary>
  /// All the answers available for this question.
  /// </summary>
  private List<CalfHeiferScorecardAnswerDto> availableAnswers;
  /// <summary>
  /// Which answer is the currently selected one.
  /// </summary>
  /// <value>The selected answer.</value>
  private CalfHeiferScorecardAnswerDto selectedAnswer;
}
