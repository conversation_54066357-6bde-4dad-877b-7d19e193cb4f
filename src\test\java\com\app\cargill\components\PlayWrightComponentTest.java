/* Cargill Inc.(C) 2022 */
package com.app.cargill.components;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.TemplateExportType;
import java.io.IOException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;

@ExtendWith(MockitoExtension.class)
class PlayWrightComponentTest {
  @Mock Environment environment;

  @InjectMocks private PlayWrightComponent playWrightComponent;

  @BeforeEach
  void init() {
    when(environment.getActiveProfiles()).thenReturn(new String[] {"local"});
  }

  @Test
  void renderTemplateReturnValidResult() throws IOException {
    String generatedFilePath = System.getProperty("user.dir");
    byte[] bytes =
        playWrightComponent.initAndPrepareExport(
            generatedFilePath + "/src/test/resources/visitReport.html",
            TemplateExportType.EXPORT_PDF);
    assertNotNull(bytes);
    bytes =
        playWrightComponent.initAndPrepareExport(
            generatedFilePath + "/src/test/resources/visitReport.html",
            TemplateExportType.EXPORT_IMAGE);
    assertNotNull(bytes);
  }
}
