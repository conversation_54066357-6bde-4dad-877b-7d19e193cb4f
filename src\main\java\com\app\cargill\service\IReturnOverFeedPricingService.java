/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.constants.ReturnOverFeedType;
import com.app.cargill.dto.ReturnOverFeedPricePerTonDto;
import java.util.List;

public interface IReturnOverFeedPricingService {

  List<ReturnOverFeedPricePerTonDto> getPricePerTon(ReturnOverFeedType returnOverFeedType);

  ReturnOverFeedPricePerTonDto update(ReturnOverFeedPricePerTonDto returnOverFeedPricePerTonList);
}
