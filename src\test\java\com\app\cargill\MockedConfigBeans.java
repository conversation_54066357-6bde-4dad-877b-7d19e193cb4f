/* Cargill Inc.(C) 2022 */
package com.app.cargill;

import static org.mockito.Mockito.mock;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;

@Configuration
public class MockedConfigBeans {

  @Bean
  @Primary
  public ClientRegistrationRepository getClientRegistrationRepository() {
    return mock(ClientRegistrationRepository.class);
  }
}
