/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.LocomotionScoreHerdAnalysisReportDto;
import com.app.cargill.dto.Pair;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("locomotionScoreHerdAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"})
public class LocomotionScoreHerdAnalysisReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  void prepareHeader(
      XSSFWorkbook wb,
      XSSFSheet sheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      LocomotionScoreHerdAnalysisReportDto dto,
      XSSFCellStyle bold,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(getClass(), wb, sheet, rowNum.get(), cellNum.getAndIncrement());
    // headings
    XSSFRow row = sheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        bold,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, dto.getVisitName());
    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        bold,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, dto.getVisitDate());
    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = sheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        bold,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, dto.getToolName());
    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        bold,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, dto.getAnalysisType());
    sheet.addMergedRegion(new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    LocomotionScoreHerdAnalysisReportDto dto =
        modelMapper.map(data, LocomotionScoreHerdAnalysisReportDto.class);

    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      // create sheet
      XSSFSheet sheet =
          wb.createSheet(ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;

      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));

      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 6));

      ExcelUtils.addAvgAndStdDeviation(
          sheet,
          rowNumber,
          centerBlack,
          decimalStyle,
          source,
          locale,
          new Pair<>(dto.getAverage(), dto.getStandardDeviation()));
      // setting rest of rows
      cellNumber.set(0);
      int locomotionScoresNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_LOCOMOTION_SCORE, null, source, locale));
      for (Entry<String, Double> entry : dto.getHerdAvg().entrySet()) {

        if (!Objects.isNull(entry.getKey())) {
          ExcelUtils.createAndSetCellValue(row, cellNumber, decimalStyle, entry.getKey());
        } else {
          ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, null);
        }
      }
      cellNumber.set(0);
      int locomotionAvgRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_LOCOMOTION_AVG, null, source, locale));

      for (Entry<String, Double> entry : dto.getHerdAvg().entrySet()) {

        if (!Objects.isNull(entry.getValue())) {
          ExcelUtils.createAndSetCellValue(row, cellNumber, decimalStyle, entry.getValue());
        } else {
          ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, null);
        }
      }

      cellNumber.set(0);

      int locomotionScoreGoalsRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_GOALS, null, source, locale));

      for (Entry<String, Double> entry : dto.getGoals().entrySet()) {

        if (!Objects.isNull(entry.getValue())) {
          ExcelUtils.createAndSetCellValue(row, cellNumber, decimalStyle, entry.getValue());
        } else {
          ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, null);
        }
      }

      // create data sources
      // y0 axis
      int columnStart = 1;
      int columnEnd = columnStart + dto.getHerdAvg().size() - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);
      XDDFNumericalDataSource<Double> locomotionScoreAvgDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  locomotionAvgRowNumber, locomotionAvgRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> goalsDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  locomotionScoreGoalsRowNumber,
                  locomotionScoreGoalsRowNumber,
                  columnStart,
                  columnEnd));
      XDDFDataSource<Double> locomotionScoreDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  locomotionScoresNumber, locomotionScoresNumber, columnStart, columnEnd));
      // needed objects for the charts
      // XSSFClientAnchor anchor;
      XSSFChart chart;
      // XDDFChartLegend legend;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 2;
      // ==============first line chart===================================
      chart =
          ExcelUtils.initChart(
              sheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_CHART_NAME, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_LOCOMOTION_SCORE_X_AXIS, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_Y_AXIS, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series =
          (XDDFLineChartData.Series)
              dataLeft.addSeries(locomotionScoreDataSource, locomotionScoreAvgDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_LOCOMOTION_AVG, null, source, locale),
          new CellReference(sheet.getSheetName(), locomotionAvgRowNumber, 0, true, true));
      series.setSmooth(true);
      series =
          (XDDFLineChartData.Series) dataLeft.addSeries(locomotionScoreDataSource, goalsDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(
              LangKeys.REPORT_LOCOMOTION_SCORE_HERD_ANALYSIS_GOALS, null, source, locale),
          new CellReference(sheet.getSheetName(), locomotionScoreGoalsRowNumber, 0, true, true));
      series.setSmooth(true);
      chart.plot(dataLeft);
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.GREEN, false);
      ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.PURPLE, true);
      // ExcelUtils.drawGridLinesInChart(chart, true);
      return ExcelUtils.finalizeWorkbook(wb, sheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    byte[] report =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    LocomotionScoreHerdAnalysisReportDto dto =
        modelMapper.map(data, LocomotionScoreHerdAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }
}
