/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.converter.EpochDateTimeSerializer;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(
    getterVisibility = JsonAutoDetect.Visibility.NONE,
    isGetterVisibility = JsonAutoDetect.Visibility.NONE)
public class AnimalAnalysisToolCDP implements Serializable {
  /** */
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("id")
  public UUID id;

  @JsonProperty("MobileLastUpdatedTime")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant mobileLastUpdatedTime;

  @JsonProperty("CreateUser")
  public String createUser;

  @JsonProperty("CreateTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant createTimeUtc;

  @JsonProperty("LastSyncTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant lastSyncTimeUtc;

  @JsonProperty("LastModifiedTimeUtc")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  public Instant lastModifiedTimeUtc = Instant.MIN;

  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("LastModifyUser")
  public String lastModifyUser;

  @JsonProperty("Animals")
  private List<AnimalAnalysisToolItemCDP> animals;
}
