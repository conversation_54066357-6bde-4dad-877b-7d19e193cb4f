/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.MilkingSystem;
import com.app.cargill.document.*;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.Site;
import com.app.cargill.sf.cc.model.simple.SiteUpdateModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@ExtendWith(MockitoExtension.class)
class LiftSitesServiceTest {

  @Mock private LiftApiService liftApi;

  @Mock private LiftApiReactiveService liftApiReactiveService;

  @Mock private ResourceBundleMessageSource bundleMessageSource;
  @InjectMocks private LiftSitesService sitesService;

  @Mock private SitesRepository sitesRepository;

  @Mock private AccountsRepository accountsRepository;

  @Mock private SiteMappingsRepository siteMappingsRepository;

  private static MockWebServer mockWebServer;

  @Mock private SalesforceClientFactory clientFactory;

  @BeforeAll
  static void setUp() throws IOException {
    mockWebServer = new MockWebServer();
    WebClient mockedWebClient =
        WebClient.builder().baseUrl(mockWebServer.url("/").toString()).build();
    // mockWebServer.start();

  }

  @Test
  void fetchingMultiplePagesOfSimpleSitesReturnsSitesAndSiteMappings() throws IOException {
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse<Site> response1 =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/query-simple-sites-list.json"),
            new TypeReference<>() {});
    response1.setNextRecordsUrl("next-url");
    SalesforceRecordsResponse<Site> response2 =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/query-simple-sites-list.json"),
            new TypeReference<>() {});

    doReturn(response1).when(liftApi).getRecordsQuery(any(), any(), any(), any());
    doReturn(response2).when(liftApi).getRecordsPage(any(), any(), any());
    SitesAndMappingsWrapper result = sitesService.getAllSites(null);
    assertNotNull(result);
    assertEquals(4, result.getSiteDocuments().size());
    assertEquals(2, result.getSiteMappingDocuments().size());
  }

  @Test
  void whenUpdateSucceedsTrueIsReturned() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("random-id");
    siteDocument.setSiteName("name");
    siteDocument.setLactatingAnimal(1);
    siteDocument.setMilkingSystemType(MilkingSystem.Robot);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    boolean result = sitesService.updateSite(siteDocument);
    assertTrue(result);
  }

  @Test
  void whenNoAnimalsAndUpdateSucceedsTrueIsReturned() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("random-id");
    siteDocument.setSiteName("name");
    siteDocument.setMilkingSystemType(MilkingSystem.Parlor);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    boolean result = sitesService.updateSite(siteDocument);
    assertTrue(result);
  }

  @Test
  void whenUpdateFailedFalseIsReturned() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("random-id");
    siteDocument.setSiteName("name");
    siteDocument.setMilkingSystemType(MilkingSystem.Parlor);
    doThrow(new WebClientResponseException(422, "Unprocessable Entity", null, null, null))
        .when(liftApi)
        .updateRecord(any(), any(), any(), any());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    boolean result = sitesService.updateSite(siteDocument);
    assertFalse(result);
  }

  @Test
  void whenCreateSucceedsCorrectObjectIsReturned() throws Exception {
    SiteDocument siteDocument =
        SiteDocument.builder().milkingSystemType(MilkingSystem.Robot).build();
    siteDocument.setId(UUID.randomUUID());
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    String result = sitesService.createSite(siteDocument, null, null);
    assertEquals("new-id", result);
  }

  @Test
  void whenSiteIdIsMissingCreateSiteThrowsAnException() {
    SiteDocument siteDocument =
        SiteDocument.builder().milkingSystemType(MilkingSystem.Robot).build();
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        IllegalArgumentException.class, () -> sitesService.createSite(siteDocument, null, null));
  }

  @Test
  void whenSiteCreationFailTheExceptionIsThrown()
      throws JsonProcessingException, CustomDEExceptions {
    SiteDocument siteDocument =
        SiteDocument.builder().milkingSystemType(MilkingSystem.Robot).build();
    siteDocument.setId(UUID.randomUUID());
    when(liftApi.createRecord(any(), any(), any(), any()))
        .thenThrow(mock(UnknownHttpStatusCodeException.class));
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        CustomDEExceptions.class,
        () -> sitesService.createSite(siteDocument, null, bundleMessageSource));
  }

  @Test
  void whenUpdateExternalDataSucceedsTrueIsReturned() {
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("random-id");
    siteDocument.setSiteName("name");
    siteDocument.setLactatingAnimal(1);
    siteDocument.setMilkingSystemType(MilkingSystem.Robot);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    boolean result = sitesService.updateSiteExternalData(siteDocument);
    assertTrue(result);
  }

  @Test
  void whenSiteIdIsMissingUpdateExternalDataReturnFalse() {
    SiteDocument siteDocument = SiteDocument.builder().id(UUID.randomUUID()).build();
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertFalse(sitesService.updateSiteExternalData(siteDocument));
  }

  @Test
  void whenUpdateSiteReportSucceedsTrueIsReturned() {
    SiteUpdateModel siteUpdateModel = new SiteUpdateModel();
    siteUpdateModel.setHerdStatusReport(
        "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%20Documents/DE_Reports/18667_detailed.pdf");
    siteUpdateModel.setHerdSummaryReport(
        "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%20Documents/DE_Reports/18667_summary.pdf");
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    boolean result =
        sitesService.updateSiteReport(
            "a0X4x000004lGmNEAU",
            siteUpdateModel.getHerdSummaryReport(),
            siteUpdateModel.getHerdStatusReport());
    assertTrue(result);
  }

  @Test
  void getSiteExecutesSuccessfully() throws IOException {
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    ObjectMapper objectMapper = new ObjectMapper();
    SalesforceRecordsResponse<Site> response =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/lift_site_response.json"),
            new TypeReference<>() {});

    doReturn(response).when(liftApi).getRecordsQuery(any(), any(), any(), any());

    SitesAndMappingsWrapper sitesAndMappingsWrapper = sitesService.getSite("random-id");

    assertNotNull(sitesAndMappingsWrapper);
    assertFalse(sitesAndMappingsWrapper.getSiteDocuments().isEmpty());
    assertFalse(sitesAndMappingsWrapper.getSiteMappingDocuments().isEmpty());
  }

  @Test
  void getDeletedSites() throws IOException {
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    ObjectMapper objectMapper = new ObjectMapper();

    SitesLiftDeletedWrapperList sitesLiftDeletedWrapperList = new SitesLiftDeletedWrapperList();
    List<DeletedRecords> deletedRecordsList = new ArrayList<>();
    DeletedRecords deletedRecords = new DeletedRecords();
    deletedRecords.setDeletedDate(Instant.now());
    deletedRecords.setId("a0XU8000000bqUHMAY");
    deletedRecordsList.add(deletedRecords);
    sitesLiftDeletedWrapperList.setDeletedRecords(deletedRecordsList);
    UUID accountId = UUID.randomUUID();
    Sites sites = new Sites();
    SiteDocument siteDocument = new SiteDocument();
    siteDocument.setId(UUID.randomUUID());
    siteDocument.setExternalId("a0XU8000000bqUHMAY");
    siteDocument.setAccountId(accountId);
    siteDocument.setDeleted(false);
    sites.setSiteDocument(siteDocument);

    Accounts accounts = new Accounts();
    AccountDocument accountDocument = new AccountDocument();
    accountDocument.setId(accountId);
    accountDocument.setBusinessID(1);
    accountDocument.setAccountName("Test");
    accounts.setAccountDocument(accountDocument);

    SiteMappings siteMappings = new SiteMappings();
    SiteMappingDocument siteMappingDocument = new SiteMappingDocument();
    siteMappingDocument.setId(UUID.randomUUID());
    siteMappings.setSiteMappingDocument(siteMappingDocument);
    siteMappings.setId(1L);

    // doReturn(response).when(liftApi).getRecordsWithQueryParam(any(),any(),any(),any(),any()).thenReturn(Mono.just(sitesLiftDeletedWrapperList));
    when(liftApi.getRecordsWithQueryParam(any(), any(), any(), any(), any()))
        .thenReturn(sitesLiftDeletedWrapperList);
    when(sitesRepository.findByExternalId(any())).thenReturn(sites);
    when(siteMappingsRepository.findBySiteId(any())).thenReturn(siteMappings);

    when(accountsRepository.findByAccountId(any())).thenReturn(accounts);
    sitesService.autoDeleteLiftdeletedSites();
    assertTrue(true);
  }
}
