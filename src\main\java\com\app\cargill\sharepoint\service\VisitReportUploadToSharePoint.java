/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint.service;

import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.constants.VisitReportType;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.document.EventDocument;
import com.app.cargill.document.ReportType;
import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Activities;
import com.app.cargill.model.Sites;
import com.app.cargill.model.Visits;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.ActivitiesRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.VisitsRepository;
import com.app.cargill.service.IDDWReportService;
import com.app.cargill.sf.cc.service.LiftEventService;
import com.app.cargill.sharepoint.SharepointServiceCalls;
import com.app.cargill.sharepoint.model.VisitReport;
import com.app.cargill.utils.JsonUtils;
import java.text.MessageFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service("visitReportUploadToSharePoint")
@Slf4j
@RequiredArgsConstructor
public class VisitReportUploadToSharePoint {

  /*sharepoint folder name*/
  private String sharepointFolderName = "/Site Visit Report";
  /*site report type*/
  private static final VisitReportType REPORT_TYPE = VisitReportType.SiteVisit;
  /*file extension*/
  private static final String FILE_EXTENSION = ".pdf";
  /** The Constant LOGGER. */
  private final SharepointServiceCalls sharepointServiceCalls;

  private final SharepointProperties sharepointProperties;
  private final IDDWReportService dDWReportServiceImpl;
  private final VisitsRepository visitsRepository;
  private final AccountsRepository accountsRepository;
  private final ActivitiesRepository activitiesRepository;
  private final LiftEventService liftEventService;
  private final SitesRepository sitesRepository;

  @Async
  public CompletableFuture<VisitReportDto> uploadVisitReportToSharePoint(
      VisitReportDto dto, byte[] bytes) {
    log.info("----start exporting visit report to sharepoint----");
    if (dto.getVisitId() != null) {
      Visits byVisitId = visitsRepository.findByVisitId(dto.getVisitId().toString());
      if (byVisitId != null
          && byVisitId.getVisitDocument() != null
          && byVisitId.getVisitDocument().getCustomerId() != null) {
        Accounts byAccountId =
            accountsRepository.findByAccountId(
                byVisitId.getVisitDocument().getCustomerId().toString());
        if (byAccountId != null && byAccountId.getAccountDocument() != null) {
          prepareUpload(byAccountId, byVisitId, bytes);
        }
      }
    }
    log.info("----end exporting visit report to sharepoint----");
    return CompletableFuture.completedFuture(dto);
  }

  public void uploadOfflineVisitReportToSharePoint(String visitId, byte[] bytes)
      throws CustomDEExceptions {
    log.info("----start exporting visit report to sharepoint----");
    if (visitId != null) {
      Visits byVisitId = visitsRepository.findByVisitId(visitId);
      if (byVisitId != null
          && byVisitId.getVisitDocument() != null
          && byVisitId.getVisitDocument().getCustomerId() != null) {
        Accounts byAccountId =
            accountsRepository.findByAccountId(
                byVisitId.getVisitDocument().getCustomerId().toString());
        if (byAccountId != null && byAccountId.getAccountDocument() != null) {
          prepareUpload(byAccountId, byVisitId, bytes);
        }
      }
    } else {
      throw new CustomDEExceptions("VisitId is NULL for Visit Report");
    }
    log.info("----end exporting visit report to sharepoint----");
  }

  void prepareUpload(Accounts byAccountId, Visits byVisitId, byte[] bytes) {

    List<VisitReport> visitReports =
        Arrays.asList(
            VisitReport.builder()
                .goldenRecordId(byAccountId.getAccountDocument().getGoldenRecordId())
                .visitId(byVisitId.getVisitDocument().getId().toString())
                .accountId(byAccountId.getAccountDocument().getId().toString())
                .reportFile(bytes)
                .reportDate(byVisitId.getVisitDocument().getVisitDate().toString())
                .reportType(VisitReportType.SiteVisit)
                .build());
    uploadVisitReportToSharePoint(visitReports);
    visitReports.stream()
        .forEach(
            report -> {
              if (report.getUrl() != null) {
                log.info("Exported Visit report to sharePoint.");
                Visits visit = visitsRepository.findByVisitId(report.getVisitId());
                if (visit != null && visit.getVisitDocument() != null) {
                  ReportType build =
                      ReportType.builder()
                          .accountId(visit.getVisitDocument().getCustomerId())
                          .url(report.getUrl())
                          .creationDateTime(Instant.parse(report.getReportDate()))
                          .type(VisitReportType.SiteVisit)
                          .build();
                  List<ReportType> reports =
                      visit.getVisitDocument().getReportType() != null
                          ? visit.getVisitDocument().getReportType()
                          : new ArrayList<>();
                  reports.add(build);
                  visit.getVisitDocument().setReportType(reports);
                  visitsRepository.save(visit);
                  Activities activity = activitiesRepository.findByVisitId(report.getVisitId());
                  if (activity != null && activity.getActivityDocument().getReportLink() == null) {
                    activity.getActivityDocument().setReportLink(report.getUrl());
                    EventDocument event = createEvent(activity.getActivityDocument(), visit);
                    if (activity.getActivityDocument().getEventSfdcId() != null) {
                      liftEventService.updateEvent(event);
                    }
                    activitiesRepository.save(activity);
                  }
                }
              }
            });
  }

  public EventDocument createEvent(ActivityDocument activityDocument, Visits visit) {
    Sites site =
        sitesRepository.findBySiteId(
            visit.getVisitDocument().getSiteId() != null
                ? visit.getVisitDocument().getSiteId().toString()
                : null);
    return EventDocument.builder()
        .activityDateTime(
            activityDocument.getActivityDateTime() != null
                ? activityDocument.getActivityDateTime().toString()
                : null)
        .businessC(null)
        .deActivityExternalIdC(
            activityDocument.getId() != null
                ? activityDocument.getId().toString()
                : UUID.randomUUID().toString())
        .deSiteVisitIdC(
            visit.getVisitDocument().getId() != null
                ? visit.getVisitDocument().getId().toString()
                : UUID.randomUUID().toString())
        .endDateTime(
            activityDocument.getEndDateTime() != null
                ? activityDocument.getEndDateTime().toString()
                : null)
        .isAllDayEvent(false)
        .isReminderSet(true)
        .whatId(
            activityDocument.getSfdcAccountId() != null
                ? activityDocument.getSfdcAccountId()
                : null)
        .deSiteId(site != null ? site.getSiteDocument().getExternalId() : null)
        .eventId(activityDocument.getEventSfdcId())
        .startDateTime(
            activityDocument.getActivityDateTime() != null
                ? activityDocument.getActivityDateTime().toString()
                : null)
        .subject(activityDocument.getSubject())
        .reportLinkC(activityDocument.getReportLink())
        .typeC("Dairy Site Visit")
        .ownerId(activityDocument.getOwnerId())
        .build();
  }

  public void uploadVisitReportToSharePoint(List<VisitReport> visitReportList) {
    log.info("Entering getVisitReportFileList()-----");
    visitReportList.forEach(
        visitReport -> {
          try {
            Oauth2Dto oauth2Dto = sharepointAuthentication();
            // get file from azure
            String folderName = visitReport.getGoldenRecordId();

            if (!checkFolderExistsInSharePoint(oauth2Dto, visitReport.getGoldenRecordId())) {
              sharepointServiceCalls.createFolderInSharePoint(oauth2Dto, folderName);
            }

            if (!visitReport.getReportType().equals(REPORT_TYPE)) {
              sharepointFolderName = "Other";
            }
            if (!checkFolderExistsInSharePoint(oauth2Dto, folderName + sharepointFolderName)) {
              sharepointServiceCalls.createFolderInSharePoint(
                  oauth2Dto, folderName + sharepointFolderName);
            }
            // post file to sharepoint
            String fileName =
                "VisitReport_"
                    + visitReport.getReportDate().replaceAll("[\\:\\.]", "")
                    + "_"
                    + visitReport.getVisitId()
                    + FILE_EXTENSION;
            ResponseEntity<String> response =
                sharepointServiceCalls.postFileToSharePoint(
                    oauth2Dto,
                    visitReport.getReportFile(),
                    visitReport.getGoldenRecordId() + sharepointFolderName,
                    fileName);
            if (response.getStatusCode() == HttpStatus.OK) {
              String sharePointFileUrl =
                  MessageFormat.format(
                      sharepointProperties.getVisitReportFileUrl(),
                      folderName + sharepointFolderName,
                      fileName);
              visitReport.setUrl(sharePointFileUrl);
            }

          } catch (Exception e) {
            log.error(
                "BaseException occur while processing getVisitReportFileList - {}", e.getMessage());
          }
        });
    log.info("Leaving getVisitReportFileList()-----");
  }

  private boolean checkFolderExistsInSharePoint(Oauth2Dto oauth2Dto, String folderName) {
    String response = sharepointServiceCalls.checkFolderExistsInSharePoint(oauth2Dto, folderName);
    JSONObject jsonObject = JsonUtils.convertStringToJsonObject(response);
    return jsonObject.getJSONObject("d").getBoolean("Exists");
  }

  // sharepoint authentication
  private Oauth2Dto sharepointAuthentication() {
    return dDWReportServiceImpl.fetchAccessToken();
  }
}
