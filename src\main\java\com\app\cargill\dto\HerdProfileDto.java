/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.BreedReturnOverFeed;
import com.app.cargill.constants.Feeding;
import com.app.cargill.constants.SupplementTypes;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HerdProfileDto implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private BreedReturnOverFeed breed;
  private String otherBreedType;
  private Feeding feedingType;
  private Integer numberOfTmrGroups;
  private SupplementTypes typeOfSupplement;
  private Boolean coolAid;
  private Boolean fortissaFit;
  private Double mun;
  private Integer milkingPerDay;
}
