/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.Pair;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.sf.cc.config.IgnoreValidation;
import com.app.cargill.sf.cc.config.PickListValueLookUp;
import com.app.cargill.sf.cc.model.FieldsMetadata;
import com.app.cargill.sf.cc.model.MetaDataFields;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ClassUtils;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.util.CollectionUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ReflectionUtils {

  public static final String STRING = "string";
  public static final String PICKLIST = "picklist";

  public static List<String> getFieldNamesFromJsonProperty(Field field) {
    List<String> fieldAlias = new ArrayList<>();
    if (field.getAnnotations() != null) {

      if (field.getAnnotation(JsonProperty.class) != null) {
        JsonProperty jsonProperty = field.getAnnotation(JsonProperty.class);
        fieldAlias.add(jsonProperty.value());
      }
      if (field.getAnnotation(JsonAlias.class) != null) {
        JsonAlias jsonProperty = field.getAnnotation(JsonAlias.class);
        fieldAlias.addAll(Arrays.asList(jsonProperty.value()));
      }
    }
    return fieldAlias;
  }

  static boolean isValidationIgnored(Field field) {
    return field.getAnnotation(IgnoreValidation.class) != null;
  }

  public static void validate(
      Object obj,
      PayloadValidationDto response,
      FieldsMetadata fieldMetaData,
      Locale locale,
      ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException {
    for (Field field :
        Arrays.stream(obj.getClass().getDeclaredFields())
            .filter(
                f ->
                    !java.lang.reflect.Modifier.isStatic(f.getModifiers())
                        && !isValidationIgnored(f))
            .toList()) {
      boolean wasAccessible = field.canAccess(obj);
      org.springframework.util.ReflectionUtils.makeAccessible(field);
      List<String> jsonAnnotationValues = ReflectionUtils.getFieldNamesFromJsonProperty(field);
      Object value = field.get(obj);
      List<MetaDataFields> metaDataFields =
          fieldMetaData.getFields().parallelStream()
              .filter(f -> jsonAnnotationValues.contains(f.getName()))
              .toList();
      if (metaDataFields.stream().findFirst().isPresent()) {
        MetaDataFields fieldMetadata =
            metaDataFields.stream().findFirst().orElse(MetaDataFields.builder().build());
        // check for additional picklist
        updateAdditionalPickList(field, fieldMetadata, fieldMetaData);
        if (isPrimitiveOrWrapper(field) || fieldMetadata.isAggregatable()) {
          applyRules(obj, field, fieldMetadata, value, response, locale, source);
        } else if (value != null) {
          validate(value, response, fieldMetaData, locale, source);
        }
      }
      field.setAccessible(wasAccessible);
    }
  }

  static void updateAdditionalPickList(
      Field field, MetaDataFields fieldMetadata, FieldsMetadata fieldMetaData) {
    Pair<List<String>, String> keyValue = checkIfPickListValueLookUpFound(field);
    if (keyValue != null && fieldMetadata != null && !CollectionUtils.isEmpty(keyValue.left())) {
      List<MetaDataFields> additionalMetaDataFields =
          fieldMetaData.getFields().parallelStream()
              .filter(f -> keyValue.left().contains(f.getName()))
              .toList();
      if (!CollectionUtils.isEmpty(additionalMetaDataFields)) {
        fieldMetadata.setAdditionalPicklistValues(
            additionalMetaDataFields.stream()
                .findFirst()
                .orElse(MetaDataFields.builder().picklistValues(new ArrayList<>()).build())
                .getPicklistValues()
                .stream()
                .map(
                    col -> {
                      if (keyValue.right().contentEquals("label")) {
                        col.setValue(col.getLabel());
                      }
                      return col;
                    })
                .toList());
      }
    }
  }

  private static boolean isPrimitiveOrWrapper(Field field) throws ClassNotFoundException {
    return ClassUtils.isPrimitiveOrWrapper(ClassUtils.getClass(field.getType().getName()))
        || field.getType().isAssignableFrom(String.class);
  }

  static Pair<List<String>, String> checkIfPickListValueLookUpFound(Field field) {
    if (field.getAnnotation(PickListValueLookUp.class) != null) {
      PickListValueLookUp jsonProperty = field.getAnnotation(PickListValueLookUp.class);
      return new Pair<>(Arrays.asList(jsonProperty.key()), jsonProperty.value());
    }
    return null;
  }

  public static void applyRules(
      Object obj,
      Field field,
      MetaDataFields fieldMetadata,
      Object value,
      PayloadValidationDto response,
      Locale locale,
      ResourceBundleMessageSource source) {
    // if null allowed
    if (!fieldMetadata.isNillable() && value == null) {
      response
          .getErrorDetails()
          .add(
              LiftResponseEntityDto.builder()
                  .message(
                      source.getMessage(
                          LangKeys.NULL_VALUES_NOT_ALLOWED,
                          new Object[] {fieldMetadata.getLabel()},
                          "Null Value in " + fieldMetadata.getLabel() + " not allowed",
                          locale))
                  .status(ResponseStatus.FAILED)
                  .build());
    }
    // check default value case
    else if (value == null) {
      org.springframework.util.ReflectionUtils.setField(
          field, obj, fieldMetadata.getDefaultValue());
    } else {
      // check length
      if (fieldMetadata.getType().equalsIgnoreCase(STRING)
          && String.valueOf(value).length() > fieldMetadata.getLength()) {
        response
            .getErrorDetails()
            .add(
                LiftResponseEntityDto.builder()
                    .message(
                        source.getMessage(
                            LangKeys.LENGTH_EXCEED_ALLOWED_LIMIT,
                            new Object[] {fieldMetadata.getLabel(), fieldMetadata.getLength()},
                            fieldMetadata.getLabel()
                                + " length Exceed of total allowed "
                                + fieldMetadata.getLength()
                                + " characters",
                            locale))
                    .status(ResponseStatus.FAILED)
                    .build());
      }
      validatePickLists(fieldMetadata, value, response, locale, source);
    }
  }

  private static void validatePickLists(
      MetaDataFields fieldMetadata,
      Object value,
      PayloadValidationDto response,
      Locale locale,
      ResourceBundleMessageSource source) {
    // check picklistValues
    if (fieldMetadata.getPicklistValues() != null
        && !fieldMetadata.getPicklistValues().isEmpty()
        && (fieldMetadata.getType().equalsIgnoreCase(STRING)
            || fieldMetadata.getType().equalsIgnoreCase(PICKLIST))
        && fieldMetadata.getPicklistValues().parallelStream()
            .noneMatch(v -> v.getValue().contentEquals(String.valueOf(value)) && v.isActive())) {
      response
          .getErrorDetails()
          .add(
              LiftResponseEntityDto.builder()
                  .message(
                      source.getMessage(
                          LangKeys.NOT_MATCHING_WITH_ALLOWED_VALUES,
                          new Object[] {fieldMetadata.getLabel()},
                          fieldMetadata.getLabel() + " not matching from allowed values ",
                          locale))
                  .status(ResponseStatus.FAILED)
                  .build());
    } else if (fieldMetadata.getAdditionalPicklistValues() != null
        && !fieldMetadata.getAdditionalPicklistValues().isEmpty()
        && fieldMetadata.getType().equalsIgnoreCase(STRING)
        && fieldMetadata.getAdditionalPicklistValues().parallelStream()
            .noneMatch(v -> v.getValue().contentEquals(String.valueOf(value)) && v.isActive())) {
      response
          .getErrorDetails()
          .add(
              LiftResponseEntityDto.builder()
                  .message(
                      source.getMessage(
                          LangKeys.NOT_MATCHING_WITH_ALLOWED_VALUES,
                          new Object[] {fieldMetadata.getLabel()},
                          fieldMetadata.getLabel() + " not matching from allowed values",
                          locale))
                  .status(ResponseStatus.FAILED)
                  .build());
    }
  }
}
