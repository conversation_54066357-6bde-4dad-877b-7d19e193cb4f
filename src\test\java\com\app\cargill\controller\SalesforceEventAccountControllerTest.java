/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.app.cargill.document.EventDocument;
import com.app.cargill.sf.cc.service.LiftEventService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SalesforceEventAccountControllerTest {

  @Mock private LiftEventService liftEventService;

  @InjectMocks private SalesforceLiftEventController salesforceLiftEventController;

  @Test
  void createEventReturnsCorrectObject() throws Exception {
    EventDocument eventDocumentInput = new EventDocument();
    eventDocumentInput.setTypeC("typeC");
    eventDocumentInput.setStartDateTime("10/07/2023");
    eventDocumentInput.setDeActivityExternalIdC("random-desctivitynumber");
    eventDocumentInput.setActivityDateTime("sctivityDate");
    when(liftEventService.createEvent(any(), any(), any())).thenReturn(new EventDocument());
    EventDocument eventDocument = salesforceLiftEventController.createEvent(eventDocumentInput);
    assertNotNull(eventDocument);
  }

  @Test
  void updateEventReturnsCorrectObject() throws Exception {
    EventDocument eventDocumentInput = new EventDocument();
    eventDocumentInput.setTypeC("typeC");
    eventDocumentInput.setStartDateTime("10/07/2023");
    eventDocumentInput.setDeActivityExternalIdC("random-desctivitynumber");
    eventDocumentInput.setActivityDateTime("sctivityDate");
    eventDocumentInput.setEventId("00U7j000004sTDuEAM");
    when(liftEventService.createEvent(any(), any(), any())).thenReturn(new EventDocument());
    EventDocument eventDocument = salesforceLiftEventController.createEvent(eventDocumentInput);
    assertNotNull(eventDocument);
  }
}
