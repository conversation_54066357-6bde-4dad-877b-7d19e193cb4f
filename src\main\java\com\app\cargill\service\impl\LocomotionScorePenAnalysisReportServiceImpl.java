/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.LocomotionScorePenAnalysisCategoryDto;
import com.app.cargill.dto.LocomotionScorePenAnalysisReportDto;
import com.app.cargill.dto.Pair;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("locomotionScorePenAnalysisExcelReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class LocomotionScorePenAnalysisReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public String getFileName(Object data) {
    LocomotionScorePenAnalysisReportDto dto =
        modelMapper.map(data, LocomotionScorePenAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    LocomotionScorePenAnalysisReportDto dto =
        modelMapper.map(data, LocomotionScorePenAnalysisReportDto.class);
    try (XSSFWorkbook wb = new XSSFWorkbook()) {
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(wb, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));

      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              wb,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              wb,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(wb, false, true, IndexedColors.BLACK));

      // create sheet
      XSSFSheet sheet =
          wb.createSheet(ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;

      prepareHeader(wb, sheet, rowNumber, cellNumber, dto, boldStyle, locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      sheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 9));

      ExcelUtils.addAvgAndStdDeviation(
          sheet,
          rowNumber,
          centerBlack,
          decimalStyle,
          source,
          locale,
          new Pair<>(dto.getAverage(), dto.getStandardDeviation()));
      // visit Dates
      cellNumber.set(0);

      int visitDateStartRowNumber = rowNumber.get();

      row = sheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      for (LocomotionScorePenAnalysisCategoryDto entry : dto.getCategories()) {
        ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, entry.getVisitDate());
      }

      // Categories
      cellNumber.set(0);

      int percentageChewingStartRowNumber = rowNumber.get();
      row = sheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_CUD_CHEWING_CATEGORY, null, source, locale));

      for (LocomotionScorePenAnalysisCategoryDto entry : dto.getCategories()) {
        ExcelUtils.highlightEmptyCell(
            row,
            entry.getLocomotionCategoryScoreAverage(),
            cellNumber,
            decimalStyle,
            greyCellStyle);
      }
      // create data sources
      // y0 axis
      int columnStart = 1;
      int columnEnd = columnStart + dto.getCategories().size() - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);
      XDDFDataSource<String> visitDateDataSource =
          XDDFDataSourcesFactory.fromStringCellRange(
              sheet,
              new CellRangeAddress(
                  visitDateStartRowNumber, visitDateStartRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> categoryDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  percentageChewingStartRowNumber,
                  percentageChewingStartRowNumber,
                  columnStart,
                  columnEnd));

      // needed objects for the charts
      XSSFChart xssdLineChart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 3;
      // ==============================first line chart====================================
      xssdLineChart =
          ExcelUtils.initChart(
              sheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_LOCOMOTION_SCORE_PEN_ANALYSIS_CHART_NAME, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              xssdLineChart,
              ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              xssdLineChart,
              ExcelUtils.getLangValue(LangKeys.REPORT_CUD_CHEWING_CATEGORY, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      dataLeft =
          (XDDFLineChartData) xssdLineChart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series =
          (XDDFLineChartData.Series) dataLeft.addSeries(visitDateDataSource, categoryDataSource);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_CUD_CHEWING_CATEGORY, null, source, locale),
          new CellReference(sheet.getSheetName(), percentageChewingStartRowNumber, 0, true, true));
      xssdLineChart.plot(dataLeft);
      ExcelUtils.setLineMarker(series, MarkerStyle.CIRCLE);
      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.GREEN, false);

      return ExcelUtils.finalizeWorkbook(wb, sheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    byte[] report =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  void prepareHeader(
      XSSFWorkbook xSSFWorkbook,
      XSSFSheet xSSFSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      LocomotionScorePenAnalysisReportDto locomotionScorePenAnalysisReportDto,
      XSSFCellStyle cellStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(), xSSFWorkbook, xSSFSheet, rowNumber.get(), cellNumber.getAndIncrement());
    // headings
    XSSFRow row = xSSFSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, locomotionScorePenAnalysisReportDto.getVisitName());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, locomotionScorePenAnalysisReportDto.getVisitDate());
    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = xSSFSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, locomotionScorePenAnalysisReportDto.getToolName());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, locomotionScorePenAnalysisReportDto.getAnalysisType());

    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);

    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        cellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_PEN_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNumber, null, locomotionScorePenAnalysisReportDto.getPenName());
    xSSFSheet.addMergedRegion(new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 8, 9));
  }
}
