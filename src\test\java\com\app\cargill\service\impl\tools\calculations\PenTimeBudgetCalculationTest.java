/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.PenTimeBudgetTool;
import com.app.cargill.document.PenTimeBudgetToolItem;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PenTimeBudgetCalculationTest {

  @InjectMocks private PenTimeBudgetCalculation penTimeBudgetCalculation;

  @Test
  void calculatePenTimeBudgetFields() {

    PenTimeBudgetTool tool =
        PenTimeBudgetTool.builder()
            .pens(
                List.of(
                    PenTimeBudgetToolItem.builder()
                        .animals(10.0)
                        .stallsInPen(7)
                        .walkingTimeToParlor(0.25)
                        .walkingTimeFromParlor(0.25)
                        .timeInParlor(0.5)
                        .milkingFrequency(2.0)
                        .eatingTime(0.33)
                        .drinkingGroomingTime(0.1)
                        .otherNonRestTime(5.0)
                        .timeInLockUp(8.0)
                        .restingRequirement(3.0)
                        .stallsInParlor(2.0)
                        .build()))
            .build();

    tool = penTimeBudgetCalculation.calculateFields(tool);

    Assertions.assertTrue(tool.getPens().get(0).getRestingDifference() > 0);
  }

  @Test
  void verifyCalculations() {

    PenTimeBudgetTool tool =
        PenTimeBudgetTool.builder()
            .pens(
                List.of(
                    PenTimeBudgetToolItem.builder()
                        .animals(8.0)
                        .stallsInPen(10)
                        .timeInParlor(0.5)
                        .milkingFrequency(2.0)
                        .eatingTime(5.0)
                        .drinkingGroomingTime(0.5)
                        .otherNonRestTime(0.1)
                        .timeInLockUp(0.1)
                        .restingRequirement(15.0)
                        .stallsInParlor(2.0)
                        .toolStatus(ToolStatuses.Completed)
                        .build()))
            .build();

    tool = penTimeBudgetCalculation.calculateFields(tool);

    Assertions.assertEquals(80.0, tool.getPens().get(0).getOvercrowding());
    Assertions.assertEquals(0.5, tool.getPens().get(0).getTimePerMilking());
    Assertions.assertEquals(1.0, tool.getPens().get(0).getTotalTimeMilking());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getWalkingTimeToStall());
    Assertions.assertEquals(15.5, tool.getPens().get(0).getRestingRemaining());
    Assertions.assertEquals(0.5, tool.getPens().get(0).getRestingDifference());
    Assertions.assertEquals(0.79, tool.getPens().get(0).getPotentialMilkGainLoss());
    Assertions.assertEquals(8.0, tool.getPens().get(0).getParlorTurnsPerHour());
    Assertions.assertEquals(16.0, tool.getPens().get(0).getCowsMilkedPerHour());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getWalkingToFindStall());
    Assertions.assertEquals(8.5, tool.getPens().get(0).getTotalNonRestingTime());
    Assertions.assertEquals(15.5, tool.getPens().get(0).getTimeRemainingForResting());
    Assertions.assertEquals(15.0, tool.getPens().get(0).getTimeRequiredForResting());
    //        Assertions.assertEquals(0.58, tool.getPens().get(0).getEnergyChange());
    Assertions.assertEquals(0.12, tool.getPens().get(0).getBodyWeightChange());
    Assertions.assertEquals(0.22, tool.getPens().get(0).getBodyConditionScoreChange());
  }

  @Test
  void verifyCalculationsWithNull() {

    PenTimeBudgetTool tool =
        PenTimeBudgetTool.builder().pens(List.of(PenTimeBudgetToolItem.builder().build())).build();

    tool = penTimeBudgetCalculation.calculateFields(tool);

    Assertions.assertEquals(0.0, tool.getPens().get(0).getOvercrowding());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getTimePerMilking());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getTotalTimeMilking());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getWalkingTimeToStall());
    Assertions.assertEquals(22.2, tool.getPens().get(0).getRestingRemaining());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getRestingDifference());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getPotentialMilkGainLoss());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getParlorTurnsPerHour());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getCowsMilkedPerHour());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getWalkingToFindStall());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getTotalNonRestingTime());
    Assertions.assertEquals(22.2, tool.getPens().get(0).getTimeRemainingForResting());
    Assertions.assertEquals(null, tool.getPens().get(0).getTimeRequiredForResting());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getBodyWeightChange());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getBodyConditionScoreChange());
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyCalculations_AnotherDataSet() {

    PenTimeBudgetTool tool =
        PenTimeBudgetTool.builder()
            .pens(
                List.of(
                    PenTimeBudgetToolItem.builder()
                        .stallsInPen(52)
                        .stallsInParlor(24.0)
                        .restingRequirement(12.0)
                        .eatingTime(5.0)
                        .drinkingGroomingTime(0.5)
                        .toolStatus(ToolStatuses.Completed)
                        .animals(44.0)
                        .milkingFrequency(3.0)
                        .build(),
                    PenTimeBudgetToolItem.builder()
                        .stallsInPen(70)
                        .stallsInParlor(24.0)
                        .restingRequirement(12.0)
                        .eatingTime(5.0)
                        .drinkingGroomingTime(0.5)
                        .toolStatus(ToolStatuses.Completed)
                        .animals(37.0)
                        .milkingFrequency(2.0)
                        .build()))
            .build();

    tool = penTimeBudgetCalculation.calculateFields(tool);
    //
    Assertions.assertEquals(85.0, tool.getPens().get(0).getOvercrowding());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getTimePerMilking());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getTotalTimeMilking());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getWalkingTimeToStall());
    Assertions.assertEquals(16.7, tool.getPens().get(0).getRestingRemaining());
    Assertions.assertEquals(4.7, tool.getPens().get(0).getRestingDifference());
    Assertions.assertEquals(7.46, tool.getPens().get(0).getPotentialMilkGainLoss());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getParlorTurnsPerHour());
    Assertions.assertEquals(0.0, tool.getPens().get(0).getCowsMilkedPerHour());
    Assertions.assertEquals(1.8, tool.getPens().get(0).getWalkingToFindStall());
    Assertions.assertEquals(7.3, tool.getPens().get(0).getTotalNonRestingTime());
    Assertions.assertEquals(16.7, tool.getPens().get(0).getTimeRemainingForResting());
    Assertions.assertEquals(12.0, tool.getPens().get(0).getTimeRequiredForResting());
    // 7.46 * 0.7276 = 5.42789
    Assertions.assertEquals(5.43, tool.getPens().get(0).getEnergyChange());
    Assertions.assertEquals(1.10, tool.getPens().get(0).getBodyWeightChange());
    Assertions.assertEquals(2.02, tool.getPens().get(0).getBodyConditionScoreChange());

    // For Pen 2

    Assertions.assertEquals(53.0, tool.getPens().get(1).getOvercrowding());
    Assertions.assertEquals(0.0, tool.getPens().get(1).getTimePerMilking());
    Assertions.assertEquals(0.0, tool.getPens().get(1).getTotalTimeMilking());
    Assertions.assertEquals(1.8, tool.getPens().get(1).getWalkingTimeToStall());
    Assertions.assertEquals(16.7, tool.getPens().get(1).getRestingRemaining());
    Assertions.assertEquals(4.7, tool.getPens().get(1).getRestingDifference());
    Assertions.assertEquals(7.46, tool.getPens().get(1).getPotentialMilkGainLoss());
    Assertions.assertEquals(0.0, tool.getPens().get(1).getParlorTurnsPerHour());
    Assertions.assertEquals(0.0, tool.getPens().get(1).getCowsMilkedPerHour());
    Assertions.assertEquals(1.8, tool.getPens().get(1).getWalkingToFindStall());
    Assertions.assertEquals(7.3, tool.getPens().get(1).getTotalNonRestingTime());
    Assertions.assertEquals(16.7, tool.getPens().get(1).getTimeRemainingForResting());
    Assertions.assertEquals(12.0, tool.getPens().get(1).getTimeRequiredForResting());
    // 7.46 * 0.7276 = 5.42789
    Assertions.assertEquals(5.43, tool.getPens().get(1).getEnergyChange());
    Assertions.assertEquals(1.10, tool.getPens().get(1).getBodyWeightChange());
    Assertions.assertEquals(2.02, tool.getPens().get(1).getBodyConditionScoreChange());
  }
}
