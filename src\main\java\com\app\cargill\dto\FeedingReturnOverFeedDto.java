/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeedingReturnOverFeedDto implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private Integer lactatingCows;
  private Double daysInMilk;
  private List<HomeGrownForageDto> homeGrownForages;
  private List<HomeGrownGrainDto> homeGrownGrains;
  private List<PurchaseBulkFeedDto> purchaseBulkFeed;
  private List<PurchaseBagFeedDto> purchaseBagsFeed;
}
