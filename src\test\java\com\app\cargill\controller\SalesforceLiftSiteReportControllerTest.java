/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.sf.cc.model.simple.SiteUpdateModel;
import com.app.cargill.sf.cc.service.LiftSitesService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SalesforceLiftSiteReportControllerTest {

  @Mock private LiftSitesService liftSitesService;

  @InjectMocks private SalesforceLiftSiteReportController salesforceLiftSiteReportController;

  @Test
  void createSiteReportCorrectObject() {
    boolean retVal = true;
    SiteUpdateModel siteUpdateModel = new SiteUpdateModel();
    siteUpdateModel.setHerdStatusReport(
        "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%20Documents/DE_Reports/18667_detailed.pdf");
    siteUpdateModel.setHerdSummaryReport(
        "https://cargillonline.sharepoint.com/sites/dairyenteligen/Shared%20Documents/DE_Reports/18667_summary.pdf");
    siteUpdateModel.setExternalId("a0X4x000004lGmNEAU");
    when(liftSitesService.updateSiteReport(any(), any(), any())).thenReturn(retVal);

    boolean result = salesforceLiftSiteReportController.createSiteReport(siteUpdateModel);
    assertTrue(result);
  }
}
