/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum CANBusinessIDCrescendo {
  CFN("CFN"),
  CPN("CPN"),
  BOTH("Both");
  private final String value;

  @JsonCreator
  CANBusinessIDCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
