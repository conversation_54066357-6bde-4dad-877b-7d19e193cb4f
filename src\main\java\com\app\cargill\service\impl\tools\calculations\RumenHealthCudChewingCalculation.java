/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.CudChewingCount;
import com.app.cargill.document.HerdAnalysisGoal;
import com.app.cargill.document.RumenHealthTool;
import com.app.cargill.document.RumenHealthToolItem;

public class RumenHealthCudChewingCalculation {
  RumenHealthToolItem docDbCurrentPen;
  HerdAnalysisGoal docDbCurrentGoal;

  public RumenHealthTool calculateFields(RumenHealthTool tool) {
    if (tool == null) return null;
    if (tool.getPens() != null) {
      for (RumenHealthToolItem pen : tool.getPens()) {
        docDbCurrentPen = pen;
        pen.setPercentChewing(percentChewing());
        pen.setAverageChewsPerCud(averageChewsPerCud());
        pen.setStandardDeviationOfChewsPerCud(standardDeviationOfChewsPerCud());
        pen.setStage(stage());
      }
    }
    if (tool.getGoals() != null) {
      for (HerdAnalysisGoal goal : tool.getGoals()) {
        docDbCurrentGoal = goal;
        goal.setToolStatus(toolStatus());
      }
    }
    return tool;
  }

  private Double percentChewing() {
    if (docDbCurrentPen != null
        && docDbCurrentPen.getCudChewingCowsCount() != null
        && docDbCurrentPen.getCudChewingCowsCount().getTotalCount() != null
        && docDbCurrentPen.getCudChewingCowsCount().getTotalCount() < 10) return 0.0;
    else if (docDbCurrentPen != null
        && docDbCurrentPen.getCudChewingCowsCount() != null
        && docDbCurrentPen.getCudChewingCowsCount().getTotalCount() != null) {
      return (docDbCurrentPen.getCudChewingCowsCount().getTotalCount() == 0)
          ? 0.0
          : roundAvoid(
              (docDbCurrentPen.getCudChewingCowsCount().getCountYes()
                      / docDbCurrentPen.getCudChewingCowsCount().getTotalCount())
                  * 100,
              1);
    }
    return 0.0;
  }

  private Double averageChewsPerCud() {
    if (docDbCurrentPen != null && docDbCurrentPen.getCudChewsCount().isEmpty()) return 0.0;
    else if (docDbCurrentPen != null) {
      // replaced roundAvoid with floor after comparing with prod data
      return Math.floor(
          (double)
                  docDbCurrentPen.getCudChewsCount().stream()
                      .filter(c -> c.getChewsCount() != null)
                      .mapToInt(CudChewingCount::getChewsCount)
                      .sum()
              / docDbCurrentPen.getCudChewsCount().size());
    }
    return 0.0;
  }

  private Double standardDeviationOfChewsPerCud() {
    double avg = averageChewsPerCud();
    double n = docDbCurrentPen.getCudChewsCount().size();

    double sumOfSquareDelta =
        docDbCurrentPen.getCudChewsCount().stream()
            .filter(c -> c.getChewsCount() != null)
            .mapToDouble(chewCount -> Math.pow(Math.abs(chewCount.getChewsCount() - avg), 2))
            .sum();

    if (n != 0) return Math.sqrt(sumOfSquareDelta / n);
    return null;
  }

  private ToolStatuses toolStatus() {
    return averageChewsPerCud() > 0 ? ToolStatuses.Completed : ToolStatuses.NotStarted;
  }

  private LactationStage stage() {
    if (docDbCurrentPen.getDaysInMilk() == null) return LactationStage.LateLactation;
    else if (docDbCurrentPen.getDaysInMilk() < -20) return LactationStage.FarOffDry;
    else if (docDbCurrentPen.getDaysInMilk() < 0) return LactationStage.CloseUpDry;
    else if (docDbCurrentPen.getDaysInMilk() < 15) return LactationStage.Fresh;
    else if (docDbCurrentPen.getDaysInMilk() < 60) return LactationStage.EarlyLactation;
    else if (docDbCurrentPen.getDaysInMilk() < 120) return LactationStage.PeakMilk;
    else if (docDbCurrentPen.getDaysInMilk() <= 200) return LactationStage.MidLactation;
    else return LactationStage.LateLactation;
  }
}
