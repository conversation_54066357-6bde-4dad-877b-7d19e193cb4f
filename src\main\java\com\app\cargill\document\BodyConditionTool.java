/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.BCSPointScale;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class BodyConditionTool extends EditableDocumentBase {
  @JsonProperty("VisitId")
  private UUID visitId;

  @JsonProperty("Pens")
  private List<BodyConditionToolItem> pens;

  @JsonProperty("Goals")
  private List<BodyConditionToolGoalItem> goals;

  @JsonProperty("SelectedPointScale")
  private BCSPointScale selectedPointScale;
}
