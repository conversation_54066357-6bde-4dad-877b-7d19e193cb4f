/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.NotesDto;
import com.app.cargill.dto.NotesSearchDto;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;

public interface INotesService {

  Page<NotesDto> getAllNotesPaginated(
      int page,
      int size,
      String sortBy,
      String sorting,
      Instant lastSyncTime,
      String currentLoggedInUserJsonObj,
      String currentLoggedInUser);

  NotesDto save(NotesDto notesDto, String currentLoggedInUser);

  NotesDto update(NotesDto notesDto, String currentLoggedInUser);

  List<NotesDto> updateOrInsert(List<NotesDto> notesDto, String currentLoggedInUser);

  Page<NotesDto> getAllNotesOnlinePaginated(
      int page, int size, String sortBy, String sorting, NotesSearchDto notesSearchDto);
}
