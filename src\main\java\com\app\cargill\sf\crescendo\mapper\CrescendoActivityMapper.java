/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.mapper;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.document.ActivityDocument;
import com.app.cargill.model.Activities;
import com.app.cargill.sf.crescendo.model.*;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CrescendoActivityMapper {

  private CrescendoActivityMapper() {}

  public static ActivityCrescendo modelToCrescendo(Activities activities) {
    return documentToCrescendo(activities.getActivityDocument());
  }

  public static ActivityCrescendo documentToCrescendo(ActivityDocument existingDocument) {
    DateTimeFormatter dateTimeFormatter =
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX").withZone(ZoneId.of("UTC"));
    ActivityCrescendo activityCrescendo = new ActivityCrescendo();
    // Changed due to different setup for Crescendo
    activityCrescendo.setActivityDateTime(existingDocument.getActivityDateTime());
    activityCrescendo.setExtendedAttributes(existingDocument.getExtendedAttributes());
    activityCrescendo.setActivityType(
        ActivityTypeCrescendo.getFromInt(existingDocument.getActivityType()));
    activityCrescendo.setAccountLevelAccess(existingDocument.getAccountLevelAccess());
    activityCrescendo.setSubject(existingDocument.getSubject());
    activityCrescendo.setRecordTypeId(existingDocument.getRecordTypeId());
    activityCrescendo.setIsPrivate(existingDocument.getIsPrivate());
    activityCrescendo.setIsGroupEvent(existingDocument.getIsPrivate());
    activityCrescendo.setEventStartDate(existingDocument.getEventStartDate());
    activityCrescendo.setAllDayEvent(existingDocument.getAllDayEvent());
    activityCrescendo.setSfdcAccountId(existingDocument.getSfdcAccountId());
    if (existingDocument.getVisitId() != null)
      activityCrescendo.setVisitId(existingDocument.getVisitId().toString());
    activityCrescendo.setName(existingDocument.getName());
    activityCrescendo.setComplaintRaised(existingDocument.getComplaintRaised());
    activityCrescendo.setTrainingScheduled(existingDocument.getTrainingScheduled());
    activityCrescendo.setSfdcId(existingDocument.getSfdcId());
    activityCrescendo.setSfdcVisitId(existingDocument.getSfdcVisitId());
    activityCrescendo.setAccountId(existingDocument.getAccountId().toString());
    activityCrescendo.setAssignedToID(existingDocument.getAssignedToID());
    activityCrescendo.setEndDateTime(existingDocument.getEndDateTime());
    activityCrescendo.setEventRecordTypeID(existingDocument.getEventRecordTypeID());
    activityCrescendo.setSubTypeID(existingDocument.getSubTypeID());
    activityCrescendo.setRelatedToTypeID(existingDocument.getRelatedToTypeID());
    activityCrescendo.setRelatedToListId(existingDocument.getRelatedToListId());
    activityCrescendo.setExternalRelatedToListId(existingDocument.getExternalRelatedToListId());
    activityCrescendo.setNameListId(existingDocument.getNameListId());
    activityCrescendo.setExternalNameListId(existingDocument.getExternalNameListId());
    activityCrescendo.setNameTypeID(existingDocument.getNameTypeID());
    activityCrescendo.setServiceFlag(existingDocument.getServiceFlag());
    activityCrescendo.setLocation(existingDocument.getLocation());
    activityCrescendo.setShowTimeAsID(existingDocument.getShowTimeAsID());
    activityCrescendo.setDescriptionInfo(existingDocument.getDescriptionInfo());
    activityCrescendo.setBusinessId(
        BusinessUnitCrescendo.getFromInt(existingDocument.getBusinessID()));
    if (existingDocument.getSegmentID() != null) {
      activityCrescendo.setSegmentId(
          SegmentStepOneIdCrescendo.fromValue(existingDocument.getSegmentID().toString()));
    }
    activityCrescendo.setProductLineID(existingDocument.getProductLineID());
    activityCrescendo.setGroupID(existingDocument.getGroupID());
    activityCrescendo.setClassID(existingDocument.getClassID());
    activityCrescendo.setProductFunctionID(existingDocument.getProductFunctionID());
    activityCrescendo.setReminderFollowupDate(existingDocument.getReminderFollowupDate());
    activityCrescendo.setReminderDateTime(existingDocument.getReminderDateTime());
    activityCrescendo.setReminderFlag(existingDocument.getReminderFlag());
    activityCrescendo.setApprovalRequestNo(existingDocument.getApprovalRequestNo());
    activityCrescendo.setReadyforApprovalFlag(existingDocument.getReadyforApprovalFlag());
    activityCrescendo.setDueDate(existingDocument.getDueDate());
    activityCrescendo.setComments(existingDocument.getComments());
    activityCrescendo.setStatusId(existingDocument.getStatusId());
    activityCrescendo.setPriorityId(existingDocument.getPriorityId());
    activityCrescendo.setRecurrence(existingDocument.getRecurrence());
    activityCrescendo.setOwnerId(existingDocument.getOwnerId());
    activityCrescendo.setActivityCurrency(
        CurrencyCrescendo.getFromCurrencies(existingDocument.getActivityCurrency()));
    activityCrescendo.setApprovalStatus(existingDocument.getApprovalStatus());
    activityCrescendo.setApproved(existingDocument.getApproved());
    activityCrescendo.setCallDuration(existingDocument.getCallDuration());
    activityCrescendo.setCallNotesUpdated(existingDocument.getCallNotesUpdated());
    activityCrescendo.setCallObjectIdentifier(existingDocument.getCallObjectIdentifier());
    activityCrescendo.setCallResult(existingDocument.getCallResult());
    activityCrescendo.setCallType(existingDocument.getCallType());
    activityCrescendo.setCancelled(existingDocument.getCancelled());
    activityCrescendo.setClaimComplaint(existingDocument.getClaimComplaint());
    activityCrescendo.setCompletedDate(existingDocument.getCompletedDate());
    activityCrescendo.setCreatedBy(existingDocument.getCreatedBy());
    activityCrescendo.setDbActivityType(existingDocument.getDbActivityType());
    activityCrescendo.setEmail(existingDocument.getEmail());
    activityCrescendo.setExternalAccountId(existingDocument.getExternalAccountId());
    activityCrescendo.setExternalId(existingDocument.getExternalId());
    activityCrescendo.setMobileFirst(existingDocument.getMobileFirst());
    activityCrescendo.setOwnerSManagerRoleName(existingDocument.getOwnerSManagerRoleName());
    activityCrescendo.setOwnerRole(existingDocument.getOwnerRole());
    activityCrescendo.setPhone(existingDocument.getPhone());
    activityCrescendo.setReadyforApproval(existingDocument.getReadyforApproval());
    activityCrescendo.setReportLink(existingDocument.getReportLink());
    activityCrescendo.setSales(existingDocument.getSales());
    activityCrescendo.setService(existingDocument.getService());
    activityCrescendo.setSourceSystem(ApplicationMapping.LM_ACCOUNT_SYSTEM_NAME);
    activityCrescendo.setType(EventTypeCrescendo.getFromTypes(existingDocument.getType()));
    activityCrescendo.setVisitReport(existingDocument.getVisitReport());
    activityCrescendo.setFrequency(existingDocument.getFrequency());
    activityCrescendo.setTime(existingDocument.getTime());
    activityCrescendo.setTimeTakenHours(existingDocument.getTimeTakenHours());
    activityCrescendo.setWeekNo(existingDocument.getWeekNo());
    activityCrescendo.setYearMonth(existingDocument.getYearMonth());
    activityCrescendo.setCreateTimeUtc((existingDocument.getCreateTimeUtc()));
    activityCrescendo.setCreateUser(existingDocument.getCreateUser());
    activityCrescendo.setIsDeleted(existingDocument.isDeleted());
    activityCrescendo.setId(existingDocument.getId().toString());
    activityCrescendo.setLastModifiedTimeUtc(existingDocument.getLastModifiedTimeUtc());
    activityCrescendo.setLastModifyUser(existingDocument.getLastModifyUser());
    activityCrescendo.setLastSyncTimeUtc(existingDocument.getLastSyncTimeUtc());
    activityCrescendo.setIsNew(Boolean.TRUE);
    activityCrescendo.setVisitReportFilePathDateTime(
        dateTimeFormatter.format(existingDocument.getActivityDateTime()));

    return activityCrescendo;
  }
}
