<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="036" author="Taha">
		<sql>
		
		
			INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '8212d7a3-eab3-4b9f-8f93-ca0b00adbf50', 'Price', 300, 'Name', 'Corn', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '7ca606fc-31db-4e5d-9740-a4026c2aedf2', 'Price', 300, 'Name', 'Dry_Corn', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'a6f325aa-7162-48bc-8d9a-fdd2a964c3df', 'Price', 300, 'Name', 'High_Moisture_Corn', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'f8642c68-75c2-44a8-a8fe-36ecb6347421', 'Price', 330, 'Name', 'Barley', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '9b14e02d-d222-4701-9f2e-c85db52b0254', 'Price', 340, 'Name', 'Mixed_Grain', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'd5bda25e-b442-49b3-96d2-9d9c86b1f584', 'Price', 350, 'Name', 'Wheat', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'aa63609b-1483-40a5-86a8-ada6d92a83bb', 'Price', 255, 'Name', 'Cobmeal', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'f2a072fe-8863-461d-a644-7a96e577b124', 'Price', 620, 'Name', 'Soybeans', 'ReturnOverFeedType', 'HOME_GROWN_GRAINS'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'bbe73aef-5738-44c5-90e3-900f8d1e2551', 'Price', 285, 'Name', 'DryHay', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'd2f7dcdd-ebd4-4259-8301-70d7ee8de4bf', 'Price', 285, 'Name', 'Round_Bales', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '119d2fdf-61d1-4d7e-9c10-6f83bc3c0b68', 'Price', 285, 'Name', 'Silage', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '1678ddca-6744-4fc8-8d75-fe47acaba0c0', 'Price', 195, 'Name', 'CornSilage', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '9e631063-b2f4-4dd2-b08d-d16ebe642ccf', 'Price', 230, 'Name', 'Small_Grain_Silage', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '714656c5-cb9e-4c0a-8350-d166e9efc759', 'Price', 85, 'Name', 'Pasture', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'c5f38afd-5406-44a8-942f-89af8c99acd2', 'Price', 200, 'Name', 'Straw', 'ReturnOverFeedType', 'HOME_GROWN_FORAGES'));


		</sql>
	</changeSet>

</databaseChangeLog>