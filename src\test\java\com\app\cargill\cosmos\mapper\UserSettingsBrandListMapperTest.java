/* Cargill Inc.(C) 2022 */
package com.app.cargill.cosmos.mapper;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.constants.UserSettingsBrands;
import com.app.cargill.cosmos.mapper.userprererences.UserSettingsBrandListMapper;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;

class UserSettingsBrandListMapperTest {

  @Test
  void map() {
    List<Integer> brandList = new ArrayList<>();
    brandList.add(0);
    brandList.add(1);
    List<UserSettingsBrands> result = UserSettingsBrandListMapper.map(brandList);
    assertNotNull(result);
  }

  @Test
  void WhenBrandListIsEmpty() {
    List<Integer> brandList = new ArrayList<>();
    List<UserSettingsBrands> result = UserSettingsBrandListMapper.map(brandList);
    assertNotNull(result);
  }
}
