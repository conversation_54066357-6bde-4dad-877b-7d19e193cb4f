[{"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cmFJlAAM"}, "Id": "0014x00000cmFJlAAM", "Name": "<PERSON><PERSON><PERSON>", "RecordTypeId": "0124x000000QgwpAAC", "Customer_Account_Type__c": "Prospect", "Segment__c": "<PERSON>", "X9_Box__c": "3 - Large/Selected", "Cargill_Business__c": "United States", "Visible_on_DE__c": false, "OwnerId": "0054x0000053XXzAAM", "Account_Status__c": "Active", "Phone": "**********", "Review_Status__c": "Completed", "CurrencyIsoCode": "USD", "New_Account_Type__c": "Standard", "DE_Account_External_ID__c": "079031bf-7e4e-409c-a436-905a390901dd", "CreatedById": "0054x0000053XXzAAM", "LastModifiedById": "0054x0000054b19AAA", "CreatedDate": "2012-07-04T15:51:42.000+0000", "LastModifiedDate": "2022-08-22T12:42:07.000+0000", "BillingAddress": {"city": "St-Barnabe Nord", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "G0X 2X0", "state": "Quebec", "stateCode": "QC", "street": "1120, Haut St-Joseph"}, "ShippingAddress": null, "SystemModstamp": "2022-08-22T12:42:07.000+0000", "Contacts": {"totalSize": 2, "done": true, "records": [{"attributes": {"type": "Contact", "url": "/services/data/v54.0/sobjects/Contact/0034x00000hEmHUAA0"}, "Id": "0034x00000hEmHUAA0", "DE_Contact_External_ID__c": "03cf7a56-bdea-49bf-8242-f361ce1ba688", "FirstName": "<PERSON>", "LastName": "<PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON>", "MailingAddress": {"city": "St-Barnabe Nord", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "G0X 2X0", "state": null, "stateCode": null, "street": "1120, Haut St-Joseph"}, "OtherAddress": {"city": null, "country": "United States", "countryCode": "US", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": null, "state": null, "stateCode": null, "street": null}, "CreatedDate": "2017-01-17T19:21:19.000+0000", "LastModifiedDate": "2022-06-28T11:34:48.000+0000", "Account": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cmFJlAAM"}, "Id": "0014x00000cmFJlAAM", "DE_Account_External_ID__c": "079031bf-7e4e-409c-a436-905a390901dd", "Cargill_Business__c": "United States"}}, {"attributes": {"type": "Contact", "url": "/services/data/v54.0/sobjects/Contact/0034x00000hEmHTAA0"}, "Id": "0034x00000hEmHTAA0", "DE_Contact_External_ID__c": "1b89fbb0-23f4-431c-a76d-a32aae4ae4b3", "FirstName": "<PERSON>", "LastName": "<PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON>", "Phone": "**********", "Email": "<EMAIL>", "MailingAddress": {"city": "St-Barnabe Nord", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "G0X 2X0", "state": null, "stateCode": null, "street": "1120, Haut St-Joseph"}, "OtherAddress": {"city": null, "country": "United States", "countryCode": "US", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": null, "state": null, "stateCode": null, "street": null}, "CreatedDate": "2012-07-04T15:51:42.000+0000", "LastModifiedDate": "2022-08-16T09:32:30.000+0000", "Account": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cmFJlAAM"}, "Id": "0014x00000cmFJlAAM", "DE_Account_External_ID__c": "079031bf-7e4e-409c-a436-905a390901dd", "Cargill_Business__c": "United States"}}]}, "Owner": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000053XXzAAM"}, "Id": "0054x0000053XXzAAM", "Username": "<EMAIL>", "Name": "Data Management User", "Email": "<EMAIL>", "ProfileId": "00e4x000001s1WuAAI", "Profile": {"attributes": {"type": "Profile", "url": "/services/data/v54.0/sobjects/Profile/00e4x000001s1WuAAI"}, "Id": "00e4x000001s1WuAAI", "Name": "System Administrator"}}, "CreatedBy": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000053XXzAAM"}, "Id": "0054x0000053XXzAAM", "Name": "Data Management User"}, "LastModifiedBy": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054b19AAA"}, "Id": "0054x0000054b19AAA", "Name": "<PERSON>"}}, {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "Name": "FERME DUCFOR C/O PIER-LUC FORTIN", "RecordTypeId": "0124x000000QgwpAAC", "Customer_Account_Type__c": "Customer", "Segment__c": "End User", "X9_Box__c": "0 - Not Defined", "Source_System__c": "LM", "Business_Type__c": "Farm Producer", "Cargill_Business__c": "Canada", "Visible_on_DE__c": true, "OwnerId": "0054x0000054b15AAA", "Account_Status__c": "Active", "Review_Status__c": "Complete", "CurrencyIsoCode": "USD", "Customer_Number__c": "**********", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e", "CreatedById": "0054x0000053WP1AAM", "LastModifiedById": "0054x0000053j9fAAA", "CreatedDate": "2021-08-25T08:37:17.000+0000", "LastModifiedDate": "2022-11-07T18:01:13.000+0000", "BillingAddress": {"city": "SAINT-H<PERSON><PERSON>É DE SHENLEY", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "G0M 1V0", "state": "Quebec", "stateCode": "QC", "street": "271 LE PETIT SHENLEY"}, "ShippingAddress": {"city": "Caistor Centre", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "L0R 1E0", "state": "Ontario", "stateCode": "ON", "street": "8893 Sixteen Road"}, "SystemModstamp": "2022-11-08T05:29:16.000+0000", "DE_User_Access__r": {"totalSize": 5, "done": true, "records": [{"attributes": {"type": "DE_User_Access__c", "url": "/services/data/v54.0/sobjects/DE_User_Access__c/a0d4x000000bc13AAA"}, "DE_Account__c": "0014x00000cjneKAAQ", "Id": "a0d4x000000bc13AAA", "DE_Role_Name__c": "Account Representative", "Name": "DEUA-30599", "DE_User__c": "0054x0000054ayRAAQ", "LastModifiedDate": "2022-06-13T12:23:39.000+0000", "DE_Account__r": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e"}, "DE_User__r": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054ayRAAQ"}, "Id": "0054x0000054ayRAAQ", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Country": "Canada", "Username": "<EMAIL>"}, "Owner": {"attributes": {"type": "Name", "url": "/services/data/v54.0/sobjects/User/0054x0000054D3uAAE"}, "Id": "0054x0000054D3uAAE", "Email": "<EMAIL>"}}, {"attributes": {"type": "DE_User_Access__c", "url": "/services/data/v54.0/sobjects/DE_User_Access__c/a0d4x000000bc14AAA"}, "DE_Account__c": "0014x00000cjneKAAQ", "Id": "a0d4x000000bc14AAA", "DE_Role_Name__c": "Account Representative", "Name": "DEUA-30600", "DE_User__c": "0054x0000054b0nAAA", "LastModifiedDate": "2022-06-13T12:23:42.000+0000", "OwnerId": "0054x0000054D3uAAE", "DE_Account__r": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e"}, "DE_User__r": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054b0nAAA"}, "Id": "0054x0000054b0nAAA", "Name": "<PERSON>", "Email": "<EMAIL>", "Country": "Canada", "Username": "<EMAIL>"}, "Owner": {"attributes": {"type": "Name", "url": "/services/data/v54.0/sobjects/User/0054x0000054D3uAAE"}, "Id": "0054x0000054D3uAAE", "Email": "<EMAIL>"}}, {"attributes": {"type": "DE_User_Access__c", "url": "/services/data/v54.0/sobjects/DE_User_Access__c/a0d4x000000bc15AAA"}, "DE_Account__c": "0014x00000cjneKAAQ", "Id": "a0d4x000000bc15AAA", "DE_Role_Name__c": "Account Representative", "Name": "DEUA-30601", "DE_User__c": "0054x0000054b0lAAA", "LastModifiedDate": "2022-06-13T12:23:42.000+0000", "OwnerId": "0054x0000054D3uAAE", "DE_Account__r": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e"}, "DE_User__r": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054b0lAAA"}, "Id": "0054x0000054b0lAAA", "Name": "<PERSON>", "Email": "<EMAIL>", "Country": "Canada", "Username": "<EMAIL>"}, "Owner": {"attributes": {"type": "Name", "url": "/services/data/v54.0/sobjects/User/0054x0000054D3uAAE"}, "Id": "0054x0000054D3uAAE", "Email": "<EMAIL>"}}, {"attributes": {"type": "DE_User_Access__c", "url": "/services/data/v54.0/sobjects/DE_User_Access__c/a0d4x000000bc16AAA"}, "DE_Account__c": "0014x00000cjneKAAQ", "Id": "a0d4x000000bc16AAA", "DE_Role_Name__c": "Account Representative", "Name": "DEUA-30602", "DE_User__c": "0054x0000054avHAAQ", "LastModifiedDate": "2022-06-13T12:23:42.000+0000", "OwnerId": "0054x0000054D3uAAE", "DE_Account__r": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e"}, "DE_User__r": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054avHAAQ"}, "Id": "0054x0000054avHAAQ", "Name": "<PERSON><PERSON><PERSON>", "Email": "pierre<PERSON><PERSON><EMAIL>", "Country": "Canada", "Username": "pierre<PERSON><PERSON><EMAIL>"}, "Owner": {"attributes": {"type": "Name", "url": "/services/data/v54.0/sobjects/User/0054x0000054D3uAAE"}, "Id": "0054x0000054D3uAAE", "Email": "<EMAIL>"}}, {"attributes": {"type": "DE_User_Access__c", "url": "/services/data/v54.0/sobjects/DE_User_Access__c/a0d4x000006hUDtAAM"}, "DE_Account__c": "0014x00000cjneKAAQ", "Id": "a0d4x000006hUDtAAM", "DE_Role_Name__c": "Account Representative", "Name": "DEUA-70535", "DE_User__c": "0054x0000054avTAAQ", "LastModifiedDate": "2022-08-04T06:30:27.000+0000", "OwnerId": "0054x000005nG83AAE", "DE_Account__r": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e"}, "DE_User__r": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054avTAAQ"}, "Id": "0054x0000054avTAAQ", "Name": "<PERSON><PERSON>", "Email": "<EMAIL>", "Country": "Canada", "Username": "<EMAIL>"}, "Owner": {"attributes": {"type": "Name", "url": "/services/data/v54.0/sobjects/User/0054x000005nG83AAE"}, "Id": "0054x000005nG83AAE", "Email": "<EMAIL>"}}]}, "Contacts": {"totalSize": 2, "done": true, "records": [{"attributes": {"type": "Contact", "url": "/services/data/v54.0/sobjects/Contact/0034x00000hEe3CAAS"}, "Id": "0034x00000hEe3CAAS", "DE_Contact_External_ID__c": "a0c48a35-f23d-4d6e-b3ca-ff63e7af5599", "FirstName": "<PERSON><PERSON><PERSON>", "LastName": "Fortin", "Name": "<PERSON><PERSON><PERSON>", "Phone": "**********", "Email": "<EMAIL>", "Primary_Language__c": "French", "Preferred_Contact_Method__c": "Text", "MailingAddress": {"city": "St-Honoré", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "G0M 1V0", "state": null, "stateCode": null, "street": "271 petit <PERSON>"}, "OtherAddress": {"city": null, "country": "United States", "countryCode": "US", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": null, "state": null, "stateCode": null, "street": null}, "CreatedDate": "2020-04-17T20:54:48.000+0000", "LastModifiedDate": "2022-11-07T18:01:13.000+0000", "Account": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e", "Cargill_Business__c": "Canada"}}, {"attributes": {"type": "Contact", "url": "/services/data/v54.0/sobjects/Contact/0034x00000hEe3DAAS"}, "Id": "0034x00000hEe3DAAS", "DE_Contact_External_ID__c": "694d6d55-dc6e-476a-9057-abab1c012f33", "FirstName": "<PERSON>", "LastName": "<PERSON><PERSON>", "Name": "<PERSON>", "MailingAddress": {"city": "ST-HONORÉ", "country": "Canada", "countryCode": "CA", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": "G0M1V0", "state": null, "stateCode": null, "street": "271 PETIT SHENLEY"}, "OtherAddress": {"city": null, "country": "United States", "countryCode": "US", "geocodeAccuracy": null, "latitude": null, "longitude": null, "postalCode": null, "state": null, "stateCode": null, "street": null}, "CreatedDate": "2007-08-20T01:31:51.000+0000", "LastModifiedDate": "2022-10-25T20:20:05.000+0000", "Account": {"attributes": {"type": "Account", "url": "/services/data/v54.0/sobjects/Account/0014x00000cjneKAAQ"}, "Id": "0014x00000cjneKAAQ", "DE_Account_External_ID__c": "9c9bc358-0257-425e-b563-3711cc8f2b9e", "Cargill_Business__c": "Canada"}}]}, "Owner": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000054b15AAA"}, "Id": "0054x0000054b15AAA", "Username": "<EMAIL>", "Name": "<PERSON>", "Email": "<EMAIL>", "ProfileId": "00e4x000000ZNTlAAO", "Profile": {"attributes": {"type": "Profile", "url": "/services/data/v54.0/sobjects/Profile/00e4x000000ZNTlAAO"}, "Id": "00e4x000000ZNTlAAO", "Name": "ANH Standard Profile"}}, "CreatedBy": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000053WP1AAM"}, "Id": "0054x0000053WP1AAM", "Name": "sfdc integration"}, "LastModifiedBy": {"attributes": {"type": "User", "url": "/services/data/v54.0/sobjects/User/0054x0000053j9fAAA"}, "Id": "0054x0000053j9fAAA", "Name": "<PERSON>"}}]