AAEfficiency=AA Efficiency
AMSUtilization=Ams utilization
AMSUtilizationChart=Ams utilization chart
ARA=ARA
ARS=Argentina ($ ARS)
AUD=Australia ($ AUD)
Account=Account
Account-Not-Synced-To-Lift=Account was not synced to LIFT; please contact the admin for resolution
Acre=Acre
Action=Action
AddBag=Add bag
AddBunker=Add bunker
AddPile=Add pile
AddTMRScore=Add tmr score
AdjustingKPtoAssureSuccess=Adjusting kp to assure success
Afghanistan=Afghanistan
Agrigento=Agrigento
Aguascalientes=Aguascalientes
Alabama=Alabama
Alagoas=Alagoas
Aland_Islands=Aland Islands
Alaska=Alaska
Albania=Albania
Alberta=Alberta
Alessandria=Alessandria
Algeria=Algeria
Amapá=Amapá
Amazonas=Amazonas
Amount=Amount
AmountOfColostrumOrFed=> 3lak
Analyze=Analyze
Ancona=Ancona
Andaman_and_Nicobar_Islands=Andaman and Nicobar Islands
Andhra_Pradesh=Andhra Pradesh
Andorra=Andorra
Angola=Angola
Anguilla=Anguilla
Anhui=Anhui
AnimalInformation=Animal information
AnimalListViewModel.Title=Animal class / sub class
Animals=Animals
AnimalsInHerd=Animals in herd
AnimalsInPen=Animals in pen
AnimalsObserved=Animals observed
Annually=Annually
Answers=Answers
Antarctica=Antarctica
Antigua_and_Barbuda=Antigua and Barbuda
Aosta=Aosta
AppName=Labyrinth
Arezzo=Arezzo
Argentina=Argentina
Arizona=Arizona
Arkansas=Arkansas
Armenia=Armenia
Aruba=Aruba
Arunachal_Pradesh=Arunachal Pradesh
Ascoli_Piceno=Ascoli Piceno
Assam=Assam
Asti=Asti
AtSixLengthPerDayImperial=At 6 in. per day
AtSixLengthPerDayMetric=At 15 cm. per day
AtThreeLengthPerDayImperial=At 3 in. per day
AtThreeLengthPerDayMetric=At 7 cm. per day
Australia=Australia
Australian_Capital_Territory=Australian Capital Territory
Austria=Austria
Auto_Sync=Auto sync
Avellino=Avellino
Average=Average
AverageConcentrate=Avg conc
AverageMilkLoss=Average milk loss ({0})
AverageScoreTitle=Avg. tmr particle score
AvgBCS=Average bcs
AvgLocomotionScore=Avg. locomotion score (calculated)
Azerbaijan=Azerbaijan
BAM=BAM
BCS=BCS
BCSCategory1=Body condition score category 1.0
BCSCategory1pt5=Body condition score category 1.5
BCSCategory2=Body condition score category 2.0
BCSCategory2pt5=Body condition score category 2.5
BCSCategory3=Body condition score category 3.0
BCSCategory3pt5=Body condition score category 3.5
BCSCategory4=Body condition score category 4.0
BCSCategory4pt5=Body condition score category 4.5
BCSCategory5=Body condition score category 5.0
BCSEditMilkAndDimViewModel.BCSDIMTitle=Days in milk (dim)
BCSEditMilkAndDimViewModel.BCSMilkTitle=Milk
BCSEditMilkAndDimViewModel.Title=Edit dim and milk
BCSHerdAnalysisInputsViewModel.BCS=BCS
BCSHerdAnalysisInputsViewModel.BCSAnalysis=Body condition score analysis
BCSHerdAnalysisInputsViewModel.BCSDIM=DIM
BCSHerdAnalysisInputsViewModel.BCSEdit=Edit
BCSHerdAnalysisInputsViewModel.BCSMilk=Milk
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysis=Herd analysis
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisGoalsTab=Goals
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisInputsTab=Inputs
BCSHerdAnalysisMasterViewModel.BCSHerdAnalysisResultsTab=Results
BCSHerdAnalysisMasterViewModel.BCSTitle=Body condition score
BCSHerdAnalysisMasterViewModel.Title=Body condtion score
BCSHerdAnalysisResultsViewModel.BCSAvg=Bcs avg.
BCSHerdAnalysisResultsViewModel.GraphTitle=Body condition score analysis
BCSHerdAnalysisResultsViewModel.MaxBCS=Max. bcs
BCSHerdAnalysisResultsViewModel.MilkHeadDay=Milk/Head/Day
BCSHerdAnalysisResultsViewModel.MinBCS=Min. bcs
BCSHerdAnalysisResultsViewModel.SubHeading=Herd analysis
BCSHerdAnalysisResultsViewModel.Title=Body condition score
BCSPenSelectionViewModel.PenSelectionList=PENS
BCSPenSelectionViewModel.Pens=Pens
BCSPenSelectionViewModel.SelectPointScale=Select a point scale
BCSPenSelectionViewModel.Title=Body Condition Score
BCSSelectPointScaleViewModel.FooterText=Only one point scale can be used per visit. Changing the point scale will result in lost values.
BCSSelectPointScaleViewModel.SelectPointScale=Select a point scale
BCSSelectPointScaleViewModel.Title=Body condition score
BGL=BGL
BRL=Brazil (R$ BRL)
BRR=BRR
Bad=Bad
Bag=Bag
BaggedConventionalSilage=Bagged conventional silage
Bags=Bag
Bahamas=Bahamas
Bahia=Bahia
Bahrain=Bahrain
Baja_California=Baja California
Baja_California_Sur=Baja California Sur
Baleage=Baleage
BaleageFQAs=Baleage faqs
Baleage_AreBalesWrappedWith=Are bales wrapped with\:
Baleage_BagsPlacedOnStableWellManagedSurface=Are bales placed on a stable, well managed all season surface?
Baleage_InspectedForPestHoleDamageRepairOnBasis=Are bales inspected for pest hole damage and repaired on weekly basis?
Baleage_TrashVegRodentControlledAroundBags=Are trash, vegetation and rodents controlled around bales?
Baleage_WaterShedsOffPlasticNotIntoBaleage=Does the water shed off plastic and not into baleage? (challenge with large square bales)
Bangladesh=Bangladesh
Barbados=Barbados
Bari=Bari
Barletta-Andria-Trani=Barletta-Andria-Trani
Bayern=Bayern
BeddedPack=Bedded pack
Beijing=Beijing
Belarus=Belarus
Belgium=Belgium
Belize=Belize
Belluno=Belluno
Benchmarks_Serum_ToolTip=
Benevento=Benevento
Benin=Benin
Bergamo=Bergamo
Bermuda=Bermuda
BetweenFifteenTwenty=Between 15 and 20
Bhutan=Bhutan
BiWeekly=Bi-Weekly
Biella=Biella
Bihar=Bihar
BlobFailed=Device logs upload failed.
BlobSuccess=Device logs uploaded successfully.
BodyConditionHerdGoals=Body condition herd analysis goals
BodyConditionHerdInputs=Body condition herd analysis inputs
BodyConditionHerdResults=Body condition herd results
BodyConditionInputs=Body condition inputs
BodyConditionResults=Body condition results
BodyConditionScoreCategory=Body condition score category {0}
BodyConditionScoreEditInputsViewModel.Count=Count
BodyConditionScoreEditInputsViewModel.NumberOfCows=Number of cows
BodyConditionScoreEditInputsViewModel.PleaseCountNumberOfCows=Please count the number of cows.
BodyConditionScoreEditInputsViewModel.Title=Number of cows
BodyConditionScoreHerdEditGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
BodyConditionScoreHerdEditGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
BodyConditionScoreHerdEditGoalsViewModel.FarOffDry=Far-off dry (less than -21)
BodyConditionScoreHerdEditGoalsViewModel.Fresh=Fresh (0 to 15)
BodyConditionScoreHerdEditGoalsViewModel.LateLactation=Late lactation (greater than 201)
BodyConditionScoreHerdEditGoalsViewModel.MaxGoal=Bcs max
BodyConditionScoreHerdEditGoalsViewModel.MidLactation=Mid lactation (121 to 200)
BodyConditionScoreHerdEditGoalsViewModel.MinGoal=Bcs min
BodyConditionScoreHerdEditGoalsViewModel.PeakMilk=Peak milk (61 to 120)
BodyConditionScoreHerdEditGoalsViewModel.Title=Edit goals
BodyConditionScoreHerdGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
BodyConditionScoreHerdGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
BodyConditionScoreHerdGoalsViewModel.Edit=Edit
BodyConditionScoreHerdGoalsViewModel.FarOffDry=Far-off dry (less than -21)
BodyConditionScoreHerdGoalsViewModel.Fresh=Fresh (0 to 15)
BodyConditionScoreHerdGoalsViewModel.GoalMaxTitle=Bcs goal max
BodyConditionScoreHerdGoalsViewModel.GoalMinTitle=Bcs goal min
BodyConditionScoreHerdGoalsViewModel.LateLactation=Late lactation (greater than 201)
BodyConditionScoreHerdGoalsViewModel.MidLactation=Mid lactation (121 to 200)
BodyConditionScoreHerdGoalsViewModel.PeakMilk=Peak milk (61 to 120)
BodyConditionScoreHerdGoalsViewModel.TableTitle=Bcs by stage of lactation
BodyConditionScoreInputsViewModel.AnimalsObserved=Animals observed
BodyConditionScoreInputsViewModel.AvgBCSCalculated=Avg. bcs (calculated)
BodyConditionScoreInputsViewModel.BCSCategory=Bcs category
BodyConditionScoreInputsViewModel.BCSPercentOfPen=Percent of pen (%)
BodyConditionScoreInputsViewModel.BodyConditionScoreBCS=Body condition score (bcs)
BodyConditionScoreInputsViewModel.Edit=Edit
BodyConditionScoreInputsViewModel.StdDevCalculated=Std. deviation (calculated)
BodyConditionScoreMasterViewModel.Goals=Goals
BodyConditionScoreMasterViewModel.Inputs=Inputs
BodyConditionScoreMasterViewModel.Results=Results
BodyConditionScoreMasterViewModel.SubHeading=Herd analysis
BodyConditionScoreMasterViewModel.Title=Body condition score
BodyConditionScoreResultsViewModel.BCSAverageTitle=Average score
BodyConditionScoreResultsViewModel.PercentPen=Percent of pen (%)
BodyConditionScoreResultsViewModel.SelectedDates=Select dates
BodyConditionScoreResultsViewModel.Title=Bcs results
BodyConditionScoresMasterViewModel.BodyConditionScore=Body condition score
BodyConditionScoresMasterViewModel.Inputs=Inputs
BodyConditionScoresMasterViewModel.Results=Results
BodyConditionScoresMasterViewModel.Title=Body condition score
BodyConditionScoresMasterViewModel.VisitNotebook=Visit notebook
Bolivia,_Plurinational_State_of=Bolivia, Plurinational State of
Bologna=Bologna
Bolzano=Bolzano
Bonaire=Bonaire
Bonaire,_Sint_Eustatius_and_Saba=Bonaire, Sint Eustatius and Saba
Bosnia_and_Herzegovina=Bosnia and Herzegovina
Botswana=Botswana
BottomUnloadingSilo=Bottom unloading silo
BottomUnloadingSilos=Bottom unloading silo
Bouvet_Island=Bouvet Island
Brazil=Brazil
Brescia=Brescia
Brindisi=Brindisi
British_Columbia=British Columbia
British_Indian_Ocean_Territory=British Indian Ocean Territory
Brunei_Darussalam=Brunei Darussalam
Bulgaria=Bulgaria
Bull=Bull
Bunker=Bunker
BunkerCapacity=Bunker capacity
BunkerFeedOutRate=Bunker feed out
Bunkers=Bunkers
BunkersAndPiles=Bunkers and piles
BunkersAndPiles_Bonus2LayersPlasticNonPermeable=Are bunkers/piles covered with 2 layers of plastic and a layer of non-permeable plastic?
BunkersAndPiles_CleanlinessOfFeedArea=What is the cleanliness of the feed area?
BunkersAndPiles_CoverPlasticOnlyRemovedSilage=To what frequency is the cover plastic removed from silage?
BunkersAndPiles_FaceRemoveRate=What is the face removal rate?
BunkersAndPiles_LooseOrFacedFeedIsFed=Loose or "faced" feed is fed\:
BunkersAndPiles_PackingInitialSpreadLayers=Packing\: are layers 15 cm (6 inches) or less?
BunkersAndPiles_PileSlopeBunkerCrownShouldntBe=Is the pile and bunker slope less than a 3\:1 run to rise ratio?
BunkersAndPiles_PorosityScoresConsistently=What is the porosity score, with respect to the dm?
BunkersAndPiles_SealedImmedAfterPack6milPlastic=How long after packing are bunkers/piles sealed with 8mm plastic?
BunkersAndPiles_SideWallsSealedPlastic=Are side walls sealed with plastic?
BunkersAndPiles_SmoothFaceNoIndDisruptedLayers=Is the face smooth? (no indication of disrupted layers allowing oxygen penetration)
BunkersAndPiles_TiresSplitsTouching=Are the tires/splits touching?
Burkina_Faso=Burkina Faso
Burundi=Burundi
CAD=Canada (CA$ CAD)
CFNChina=China
CFNIndia=India
CHF=Switzerland (CHF CHF)
CLF=CLF
CLP=Chile ($ CLP)
CNY=China (CNY CNY)
COP=COP
CPNBrazil=Brazil
CPNFrance=France
CPNPoland=Poland
CPNUS=United states of america
CRC=CRC
CZK=Czech Republic (CZK CZK)
Cagliari=Cagliari
Calf=Calf
CalfHeiferColostrum=Calf &amp; heifer scorecard - colostrum
CalfHeiferGrowerPuberty=Calf &amp; heifer scorecard - grower, puberty, pregnancy, Close-up
CalfHeiferKeyBenchmarks=Calf &amp; heifer scorecard - key benchmarks
CalfHeiferKeybenchmarkScoreImprovementViewModel.KBInnerScreenInfo=The coloring of sections is decided by the range\: &lt;75%\: red, &gt;
CalfHeiferPostweaned=Calf &amp; heifer scorecard - post-weaned
CalfHeiferPreweaned=Calf &amp; heifer scorecard - pre-weaned
CalfHeiferQuestionViewModel.Close=Close
CalfHeiferQuestionViewModel.Colostrum=Colostrum
CalfHeiferQuestionViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
CalfHeiferQuestionViewModel.KeyBenchmarks=Key benchmarks
CalfHeiferQuestionViewModel.Postweaned=Post-weaned
CalfHeiferQuestionViewModel.Preweaned=Pre-weaned
CalfHeiferQuestionViewModel.Resources=Resources
CalfHeiferQuestionViewModel.VisitNotebook=Visit notebook
CalfHeiferResources=Calf &amp; heifer scorecard - resources
CalfHeiferResults=Calf &amp; heifer scorecard - results
CalfHeiferScoreCardScoreViewModel.CalfHeiferScore=Calf and heifer score
CalfHeiferScoreCardScoreViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
CalfHeiferScoreCardScoreViewModel.OverallScorecardScore=Include in overall scorecard score
CalfHeiferScoreCardScoreViewModel.PhaseOne=Colostrum
CalfHeiferScoreCardScoreViewModel.PhaseThree=Post-weaned
CalfHeiferScoreCardScoreViewModel.PhaseTwo=Pre-weaned
CalfHeiferScorecardImprovementViewModel.Colostrum=Colostrum
CalfHeiferScorecardImprovementViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
CalfHeiferScorecardImprovementViewModel.Postweaned=Post-weaned
CalfHeiferScorecardImprovementViewModel.Preweaned=Pre-weaned
CalfHeiferScorecardKeyBenchmarksViewModel.InstructionText=Tap on below categories to see phase-wise result
CalfHeiferScorecardKeyBenchmarksViewModel.KBLandingInfo=The coloring of sections is decided by the range\: equals 100%\: green, &lt;100%\: red
CalfHeiferScorecardKeyBenchmarksViewModel.KBLastPhase=Records
CalfHeiferScorecardKeyBenchmarksViewModel.KeyBenchmarks=Key benchmarks
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFive=Phase 5
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseFour=Phase 4
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseOne=Phase 1
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSeven=Phase 7
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseSix=Phase 6
CalfHeiferScorecardKeyBenchmarksViewModel.PhaseTwoThree=Phase 2-3
CalfHeiferScorecardKeyBenchmarksViewModel.Question_KBLastPhase=Maintain accurate growth and health records
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFive=Pregnancy 15 - 23 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseFour=Puberty 9 - 15 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseOne=Colostrum 1 - 3 days
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseSix=Close up / production 23 - 26 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseThree=Grower 3 - 9 months
CalfHeiferScorecardKeyBenchmarksViewModel.Question_PhaseTwo=Pre/post weaning 0 - 3 months
CalfHeiferScorecardKeyBenchmarksViewModel.VisitNotebook=Visit notebook
CalfHeiferScorecardLanding=Calf &amp; heifer scorecard
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardBenchmarks=Key benchmarks
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardImprovements=Improvements
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardResponses=Responses
CalfHeiferScorecardResultsViewModel.CalfHeiferScorecardScore=Score
CalfHeiferScorecardResultsViewModel.VisitNotebook=Visit notebook
CalfHeiferScorecardViewModel.Colostrum=Colostrum
CalfHeiferScorecardViewModel.GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
CalfHeiferScorecardViewModel.KeyBenchmarks=Key benchmarks
CalfHeiferScorecardViewModel.Postweaned=Post-weaned
CalfHeiferScorecardViewModel.Preweaned=Pre-weaned
CalfHeiferScorecardViewModel.Resources=Resources
CalfHeiferScorecardViewModel.Title=Scorecard
CalfHeiferScorecardViewModel.VisitNotebook=Visit notebook
CalfHeiferTools=Calf &amp; heifer tools
CalfHeiferToolsViewModel.CalfHeiferScorecard=Scorecard
CalfHeiferToolsViewModel.CalfHeiferTools=Calf and heifer tools
CalfHeiferToolsViewModel.CalfHeiferToolsCaption=Tools
CalfHeiferToolsViewModel.CalfHeiferToolsInstructions=Please select a tool from the list below to begin your visit
CalfHeiferToolsViewModel.CalfHeiferToolsList=Tools
CalfHeiferToolsViewModel.Title=Calf and heifer tools
CalfHeiferToolsViewModel.VisitNotebook=Visit notebook
California=California
Caltanissetta=Caltanissetta
Cambodia=Cambodia
Camcorder=Feature Not Yet Implemented
Cameroon=Cameroon
Campeche=Campeche
Campobasso=Campobasso
Canada=Canada
Capacity=Capacity
Cape_Verde=Cape Verde
Carbonia-Iglesias=Carbonia-Iglesias
Cargill=Cargill
CargillForageLabKPTest=Cargill forage lab kp test
Carlow=Carlow
Caserta=Caserta
Catania=Catania
Catanzaro=Catanzaro
Category1=Manure score category 1.0
Category2=Manure score category 2.0
Category3=Manure score category 3.0
Category4=Manure score category 4.0
Category5=Manure score category 5.0
Cavan=Cavan
Cayman_Islands=Cayman Islands
Ceará=Ceará
Central_African_Republic=Central African Republic
Chad=Chad
Chandigarh=Chandigarh
Charts=Charts
Chewing=
Chews=
ChewsPerCud=Chews per cud
ChewsPerCudMasterViewModel.AddNew=Add new
ChewsPerCudMasterViewModel.CudChewingInputs=Inputs
ChewsPerCudMasterViewModel.CudChewingResults=Results
ChewsPerCudMasterViewModel.NumOfChews=\# of chews
ChewsPerCudMasterViewModel.Title={0} / \# of chews
Chhattisgarh=Chhattisgarh
Chiapas=Chiapas
Chieti=Chieti
Chihuahua=Chihuahua
Chile=Chile
China=China
Chinese_Taipei=Chinese Taipei
Chongqing=Chongqing
ChooseAppPDF=Please choose an app to view the pdf
ChoosingtheCorrectAdditive=Choosing the right inoculant
Christmas_Island=Christmas Island
Clare=Clare
ClassSubClass=Animal class / sub class
Clean=Clean
ClinicalMastitisLosses=Clinical mastitis losses
CloseUp=Close Up
CloseUpDry=Close-up dry
CloseUpHeifer=Close Up Heifer
Coahuila=Coahuila
Cocos_(Keeling)_Islands=Cocos (Keeling) Islands
Colima=Colima
Colorado=Colorado
Colostrum=Colostrum
Colostrum_AmountOfColostrumOrFed=Amount of colostrum or fed
Colostrum_BrixPercentOfColostrumFed=Brix % of colostrum fed
Colostrum_CleanAndDryCalvingArea=Clean and dry calving area
Colostrum_CleanAndDryCalvingArea_ToolTip=Use wet knee test to determine if area is clean and dry
Colostrum_CleanAndSanitizeCalfFeedingEquipment=Clean and sanitize calf feeding equipment between feedings
Colostrum_CleanCalfCartToTransportCalf=Clean calf cart to transport calf
Colostrum_HoursTillCalfIsRemovedFromMother=Hours till calf is removed from mother
Colostrum_HoursTillCalfReceivesColostrum=Hours till calf receives colostrum
Colostrum_NumberOfCowsInCalvingArea=Number of cows in calving area
Colostrum_PasteurizeColostrumBeforeFeeding=Pasteurized colostrum is fed
Colostrum_PercentageOfNavelsDippedInSevenPercent=% of navels dipped in 7% iodine in 1 hour
Colostrum_RefrigeratedColostrumStoredLess=Refrigerated colostrum stored less than 24 hours
ComfortToolsViewModel.ComfortHeading=Please select a tool from the list below to begin your visit.
ComfortToolsViewModel.ComfortToolsList=TOOLS
ComfortToolsViewModel.ComfortToolsTitle=Comfort tools
ComfortToolsViewModel.HealthHeading=Please select a tool from the list below to begin your visit.
ComfortToolsViewModel.HeatstressEvaluationTitle=Heat stress evaluation
ComfortToolsViewModel.PenTimeTitle=Pen time
ComfortToolsViewModel.VisitNotebook=Visit notebook
Comments=Comments
CommonSpinnerViewModel.BodyConditionPDF=Body condition scoring guide
CommonSpinnerViewModel.InadequateStimulation=Inadequate stimulation (inadequate teat preparation\: &lt;10 seconds; unit attachment\: &lt; 60 or &gt; 120 seconds)
CommonSpinnerViewModel.LocomotionPDF=Locomotion scoring guide
CommonSpinnerViewModel.ManureScorePDF=Manure score guide
CommonSpinnerViewModel.NoStimulation=No stimulation (unit attachment only)
CommonSpinnerViewModel.OptimalStimulation=Optimal stimulation (adequate teat preparation\: 10-20 seconds; unit attachment within 60 to 90 seconds)
Como=Como
Comoros=Comoros
Competitor=Competitor
CompletedTimeKey=Time completed
Component=Component
Compostbarn=Dry Lot
ConcentrateDistribution=Concentrate distribution
ConfirmExport=Pressing ok will load every selected page and take a picture before returning to this screen.
ConfirmScalePointSwitch=Changing the scale point will reset any entered data.
ConfirmScorerSwitch=Changing the scorer will reset any entered data.
Congo=Congo
Congo,_the_Democratic_Republic_of_the=Congo, the Democratic Republic of the
Connecticut=Connecticut
Consumer=Consumer
ConsumerDetailsViewModel.DeleteProspect=Delete consumer
ConsumerDetailsViewModel.DeleteProspectPrompt=Are you sure you want to delete this consumer? Consumer, site and in progress visit information will be lost.
ConsumerDetailsViewModel.MainHeading=SITES
ConsumerDetailsViewModel.NewSite=Add a new site
ConsumerDetailsViewModel.ProspectTitle=Consumer details
ConsumersViewModel.NewConsumer=Add a new consumer
Continue=Continue
Cook_Islands=Cook Islands
Cork=Cork
Corn=Corn
CornSilage=Corn silage
CornSilageKernel=Baleage faqs
CornSilageResources=Corn silage resources
CornSilageStopGo=Corn silage stop-n-go
Cosenza=Cosenza
Costa_Rica=Costa Rica
Costs=Costs
Cote_d'Ivoire=Cote d'Ivoire
Count=Count
Cow=Cow 
CowEfficiency=Cow efficiency
CowPerRobot=Cows per robot
CowsOutsideTargetRangeToolTip=Goal is to have &lt;20% of cows outside the target range.
CowsPerDayNeeded=Cows/day needed
CowsSectionToolTip=A group of 8 or more cows should be tested to draw conclusions. In small herds should test all pre-fresh cows.
CowsToBeFed=Cows to be fed
CreateDuplicateDiet=A maxdiet already exists for that animal type. Create anyways?
CreateDuplicateNameDiet=A maxdiet already exists with same name, please enter unique one.
Created=Created
Cremona=Cremona
Croatia=Croatia
CropCharacteristicsDecisionGuide=Crop characteristics decision guide
Crotone=Crotone
Cuba=Cuba
CudChewingAverageNumber=Average number
CudChewingDataEntryViewModel.CudChewing=Cud chewing
CudChewingDataEntryViewModel.HerdCudChewingDescription=For this visit, please count the number of animals chewing cud in this pen using the counter below. You must count a minimum of 10 cows.
CudChewingDataEntryViewModel.No=No
CudChewingDataEntryViewModel.Yes=Yes
CudChewingHerdEditScoreViewModel.AverageChewsItem=Average chews per cud
CudChewingHerdEditScoreViewModel.Close=Close
CudChewingHerdEditScoreViewModel.DaysInMilkItem=Days in milk
CudChewingHerdEditScoreViewModel.EditGoalsTitle=Edit cud chewing scores
CudChewingHerdEditScoreViewModel.EditScoreTitle=Edit cud chewing scores
CudChewingHerdEditScoreViewModel.PercentChewingItem=Percent chewing
CudChewingMasterViewModel.CudChewing=Cud chewing
CudChewingMasterViewModel.CudChewingInputs=Inputs
CudChewingMasterViewModel.CudChewingResults=Results
CudChewingPen=Pen
CudChewingPercentChewing=% chewing
CudChewingPercentGoal={0}% goal
CudChewingPercentOfPen=Cud chewing(% of pen)
CudChewingViewModel.CudChewing=Rumen health
CudChewingViewModel.CudChewingList=Pens (lactating and dry only)
CudChewingViewModel.CudChewingTitle=Pen name
CudChewingViewModel.Title=Pens
CudChewsCalculatorViewModel.CalculatorHeading=Please select a cow to count the number of chews. Tap "add new" above to add cows to the count list.
CudChewsCalculatorViewModel.CudChewCategorySection=Cows
CudChewsCalculatorViewModel.NumOfChews=\# of chews
CudChewsDatesForComparisonViewModel.CudChewsPercent=Cud Chewing % /
Cundinamarca=Cundinamarca
Cuneo=Cuneo
Curaçao=Curaçao
Current=Current
CurrentDownResponse=Current let down response ({0}/cow/day)
CurrentMilkPrice=Current milk price ({0}/{1})
CurrentSCC=Current scc (cells/{0})
CurrentVisitSummary=Current visit summary
Customer=Customer
CustomerDetailViewModel.CustomerTitle=Customer profile
CustomerDetailViewModel.MainHeading=SITES
CustomerDetailViewModel.NewSite=Add a new site
CustomerDetailViewModel.NewVisit=New visit
CustomerProspectsSegmentViewModel.Aiden=Aiden
CustomerProspectsSegmentViewModel.Baxter=Baxter
CustomerProspectsSegmentViewModel.Dennis=Dennis
CustomerProspectsSegmentViewModel.EndUser=End user
CustomerProspectsSegmentViewModel.Kobe=Kobe
CustomerProspectsSegmentViewModel.Mila=Mila
CustomerProspectsSegmentViewModel.Noah=Noah
CustomerProspectsSegmentViewModel.NotSet=- 
CustomerProspectsSegmentViewModel.SelectSegment=Select a segment
CustomerProspectsSegmentViewModel.Sonya=Sonya
CustomerProspectsSegmentViewModel.Spence=Spence
CustomerProspectsSegmentViewModel.Title=Details
CustomerProspectsSegmentViewModel.Walton=Walton
CustomerWithSiteName=Customer name - site name
Cyprus=Cyprus
CzechRepublic=Czech republic
Czech_Republic=Czech Republic
DDW=Farm data
DDWOfflineMessage=Since there is no network, would you like to view the offline report?
DDWUpdatedTime=Last updated herd data\: {0}
DKK=DKK
DZD=Algeria (DA DZD)
Dadra_and_Nagar_Haveli=Dadra and Nagar Haveli
Daily=Daily
DairyEnteligenFarmReportsources=Dairy enteligen sources of information
Daman_and_Diu=Daman and Diu
DashboardViewModel.Alert=Alert\!
DashboardViewModel.AlertMessage=Data has not been synced in over {0} days.
DashboardViewModel.GoodAfternoon=Good afternoon,
DashboardViewModel.GoodMorning=Good morning,
DashboardViewModel.MessageBody=Just a reminder to utilize the notebook on each site visit to document specifics of that particular visit.
DashboardViewModel.MessageHeader=Message
DashboardViewModel.RecentSiteVisit=Recent site visits
DashboardViewModel.UserPreferences=User preferences
Date=Date
DateGone=Date gone
DatesForComparison=Dates for comparison
Days=Days
DaysInMilkItem=Days in Milk (DIM)
DeLaval=DeLaval
DeathLoss=Death loss
DecidingSilageStorage=Deciding on a silage storage type
Delaware=Delaware
Delete=Delete
DeleteMatrixValue=Are you sure you want to delete this matrix value?
Delhi=Delhi
Denmark=Denmark
DensityLossesinPressedBagSilos=Density &amp; losses in pressed bag silos
Diet=Diet
DietDCAD=Diet dcad meq/100g
DietDetailViewModel.Created=Created
DietDetailViewModel.DDW=Farm data
DietDetailViewModel.Max=MAX
DietDetailViewModel.SystemGenerated=System generated
DietDetailViewModel.Title=Diet detail
DietDetailViewModel.UserCreated=By user
DietListViewModel.InfoNewDiet=Diet names will be updated automatically if max is connected to the farm site in dairy enteligen. Otherwise, add diets manually or leave this list blank and select the correct animal class / subclass for each pen.
DietListViewModel.MainHeading=Diets
DietListViewModel.New=New
DietListViewModel.NewDiet=Add a new diet
DietListViewModel.Title=Diets
DietNotMapped=Diet not mapped
Dirty=Dirty
DisplacedAbomasum=Displaced abomasum
District_of_Columbia=District of Columbia
Distrito_Federal=Distrito Federal
Djibouti=Djibouti
DoNotTest=&lt;20% or do not test
Dominica=Dominica
Dominican_Republic=Dominican Republic
Donegal=Donegal
DontKnow=Don't know
DownResponse=Let down response ({0}/cow/day)
Dry=Dry
DryCow=Dry cow
DryLot=Dry lot
Dryhay=Dry hay
Dublin=Dublin
DuplicatePenName=A pen already exists with that name. Please choose a different name.
Durango=Durango
Dystocia=Dystocia
EGP=EGP
EarlyLactation=Early lactation
Ecuador=Ecuador
Edit=Edit
EditDatesForComparison=Edit dates for comparison
EditDatesForComparisonViewModel.Chews=Chews
EditDatesForComparisonViewModel.EditDatesClose=Close
EditDatesForComparisonViewModel.EditDatesLabel=Please select the dates for comparison for this pen from the list below.
EditDatesForComparisonViewModel.EditDatesTitle=Edit dates for comparison
EditDatesForComparisonViewModel.EditDatesVisits=Visits
EditDatesForComparisonViewModel.Hour=Hours
EditDatesForComparisonViewModel.LocomotionScoreAverage=Average locomotion score\:
EditDatesForComparisonViewModel.ManureScoreAverage=Average Manure Score\:
EditDatesForComparisonViewModel.MetabolicIncidence=Please select up to 5 visit dates for comparison from the list below.
EditDatesForComparisonViewModel.PenTimeBudget=Please select up to 7 visit dates for comparison from the list below.
EditDatesForComparisonViewModel.PenTimeBudgetTitle=Please select a date below to compare with the current visit.
EditDatesForComparisonViewModel.TimeRemainingForResting=Time remaining for resting\:
EditDatesForComparisonViewModel.Title=Edit dates for comparison
EditDatesForComparisonViewModel.Visits=Visits
EditGoalsCudChewingViewModel.Close=Close
EditGoalsCudChewingViewModel.CloseUpDry=Close-up dry
EditGoalsCudChewingViewModel.CudChews=Average chews per cud
EditGoalsCudChewingViewModel.EarlyLactation=Early lactation
EditGoalsCudChewingViewModel.EditGoalsTitle=Edit cud chewing goals
EditGoalsCudChewingViewModel.FarOffDry=Far-off dry
EditGoalsCudChewingViewModel.Fresh=Fresh
EditGoalsCudChewingViewModel.LateLactation=Late lactation
EditGoalsCudChewingViewModel.MidLactation=Mid lactation
EditGoalsCudChewingViewModel.PeakMilk=Peak milk
EditGoalsCudChewingViewModel.PercentChewing=Percent chewing
EditNoteViewModel.Action=Action
EditNoteViewModel.Cancel=Cancel
EditNoteViewModel.Category=Category
EditNoteViewModel.Close=Close
EditNoteViewModel.CreatedByMetadata=Created on {0} @ {1} by {2}
EditNoteViewModel.Delete=Delete
EditNoteViewModel.DeleteImageButtonText=Delete image
EditNoteViewModel.DeleteImagePrompt=Do you want to delete this image?
EditNoteViewModel.DeletePrompt=Do you want to delete this note?
EditNoteViewModel.DeleteVideoButtonText=Delete video
EditNoteViewModel.DeleteVideoPrompt=Do you want to delete this video?
EditNoteViewModel.Event=Event
EditNoteViewModel.LastUpdatedByMetadata=Last modified on {0} @ {1} by {2}
EditNoteViewModel.NoteCamcorderNotImplemented=Camcorder feature not yet implemented
EditNoteViewModel.NoteGalleryNotImplemented=Gallery feature not yet implemented
EditNoteViewModel.NoteLabel=Note
EditNoteViewModel.NoteOnlyOneImage=Only one image is allowed per note. Please delete the current image first.
EditNoteViewModel.NoteOnlyOneVideo=Only one video is allowed per note. Please delete the current video first.
EditNoteViewModel.Observation=Observation
EditNoteViewModel.Save=Save
EditNoteViewModel.Task=Task
EditNoteViewModel.Title=Note
EditNoteViewModel.TitleLabel=Title
Egypt=Egypt
El_Salvador=El Salvador
EmailReportViewModel.AnimalImpact=Animal impact
EmailReportViewModel.CalfHeiferItem=Calf and heifer
EmailReportViewModel.CalfHeiferScorecard=Scorecard
EmailReportViewModel.Capacity=Capacity
EmailReportViewModel.Cargill=Cargill
EmailReportViewModel.CategoryList=Category list
EmailReportViewModel.Charts=Charts
EmailReportViewModel.CoefficientVariation=c.v. (%)
EmailReportViewModel.ComfortHeatStressBanner=Heat stress tool pen
EmailReportViewModel.ComfortItem=Comfort tool
EmailReportViewModel.ComfortPenTimeBanner=Pen time budget tool
EmailReportViewModel.ComfortToolsTitle=Comfort tools
EmailReportViewModel.CowsOutsideTargetRange=Cows outside range (%)
EmailReportViewModel.CudChewingTitle=Cud chewing
EmailReportViewModel.DietDCADStr=Diet dcad
EmailReportViewModel.EmailBody={0}-{1} report
EmailReportViewModel.EmailSelectedTools=Email selected tools
EmailReportViewModel.EmailSubject={0} report
EmailReportViewModel.ExportSelected=Email selected tools
EmailReportViewModel.FeedOut=Feed out
EmailReportViewModel.ForageAuditScorecard=Forage audit scorecard
EmailReportViewModel.ForageImprovements=Forage audit scorecard
EmailReportViewModel.ForageLanding=Forage audit landing page
EmailReportViewModel.ForageScorecard=Forage audit scorecard
EmailReportViewModel.GeneratingReport=Generating report...
EmailReportViewModel.GotoMarketBranding=Go-to-market branding
EmailReportViewModel.HealthItem=Health tool
EmailReportViewModel.HeatstressEvaluationTitle=Heat stress evaluation
EmailReportViewModel.Herd=Herd
EmailReportViewModel.HerdAnalysis=Herd analysis
EmailReportViewModel.HerdGoals=Herd analysis - goals
EmailReportViewModel.HerdInputs=Herd analysis - inputs
EmailReportViewModel.HerdResults=Herd analysis - results
EmailReportViewModel.HerdRevenue=Herd analysis - revenue
EmailReportViewModel.Improvements=Improvements
EmailReportViewModel.Inputs=Inputs
EmailReportViewModel.InputsOutputsChart=Inputs / outputs / charts
EmailReportViewModel.LocomotionScoreTitle=Locomotion score
EmailReportViewModel.ManureScoreTitle=Manure score
EmailReportViewModel.MarketBranding=Go-to-market branding
EmailReportViewModel.MetabolicIncidenceTitle=Metabolic incidence
EmailReportViewModel.MilkProcessCalcInputsTab=Milking procedure comparison\: inputs
EmailReportViewModel.MilkProcessCalcResourcesTab=Milking procedure comparison\: resources
EmailReportViewModel.MilkProcessCalcResultsTab=Milking procedure comparison\: results
EmailReportViewModel.MilkProcessRevenue=Milking procedure comparison
EmailReportViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
EmailReportViewModel.MilkingTime=Milking time
EmailReportViewModel.Notes=Notes
EmailReportViewModel.NumOfChews=Number of chews
EmailReportViewModel.NutritionForage=Forage audit
EmailReportViewModel.NutritionItem=Nutrition
EmailReportViewModel.NutritionPile=Forage inventories
EmailReportViewModel.Outputs=Outputs
EmailReportViewModel.PenCompare=Pen analysis - compare
EmailReportViewModel.PenDensity=Pen density
EmailReportViewModel.PenInputs=Pen analysis - inputs
EmailReportViewModel.PenResults=Pen analysis - results
EmailReportViewModel.PenTimeTitle=Pen time budget
EmailReportViewModel.PileAndBunkerTitle=Forage inventory
EmailReportViewModel.ProductivityItem=Productivity tool
EmailReportViewModel.Provimi=Provimi
EmailReportViewModel.ProvimiUS=Provimi us
EmailReportViewModel.Purina=Purina
EmailReportViewModel.Resources=Resources
EmailReportViewModel.Results=Results
EmailReportViewModel.RumenHealthBodyConditionTitle=Body condition score
EmailReportViewModel.RumenHealthLocomotionTitle=Locomotion score
EmailReportViewModel.RumenHealthManureTitle=Rumen health manure score
EmailReportViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
EmailReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&\#8482;
EmailReportViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
EmailReportViewModel.RumenHealthTitle=Rumen health cud chewing
EmailReportViewModel.RumenHealthUrinePHTitle=Urine ph
EmailReportViewModel.ScoreScreen=Scores
EmailReportViewModel.TMRParticleScoreTitle=Rumen health particle score
EmailReportViewModel.TimeBudget=Time budget
EmailReportViewModel.Title=Email report
EmailReportViewModel.UrinePhSTDDEV=Std. deviation
EmailReportViewModel.UserPreferences=User settings
EmailReportViewModel.UserSettings=User settings
EmailReportViewModel.WalkthroughReportTitle=Walkthrough report
EnergyImperial=Mcal/lb
EnergyMetric=Mcal/kg
Enna=Enna
Equatorial_Guinea=Equatorial Guinea
Eritrea=Eritrea
ErrorDescription=An error occured reading or writing your information.
ErrorTitle=Error
Espírito_Santo=Espírito Santo
Estonia=Estonia
Ethiopia=Ethiopia
Eula=EULA
Euro=Euro Member Countries (€ EUR)
Event=Event
EveryOtherDay=Every other day
Excessive=&gt;0.5bcs unit
Export_Logs=Export logs
ExtraDaysOpenCostInfoMessage=It typically varies from $3 to $5 per additional open day.
FAQDairyEnteligenFarmReportandDDW=Faq dairy enteligen farm report
Falkland_Islands_(Malvinas)=Falkland Islands (Malvinas)
FarOff=Far Off
FarOffDry=Far-off dry
Faroe_Islands=Faroe Islands
Federal_District=Federal District
FeedFirst=Feed first
FeedOut=Feed out
FeedOutRateInfo=Feed out rate information
FeedOutRatesFilmsStorageSysExamined=Feed out rates, films &amp; storage systems examined
FeedOutSurfaceAreaImperial=Feed out surface area (ft^2)
FeedOutSurfaceAreaMetric=Feed out surface area (m^2)
FeedingRate=Feeding rate (as-fed / cow)
FeedoutLossesForageStorageSys=Feedout losses from forage storage systems
FermentationAnalysisSilageQT=Fermentation analysis &amp; silage quality testing
Fermo=Fermo
Ferrara=Ferrara
FieldKPTest=Field kp test
Fiji=Fiji
FillAllFields=Please fill in all fields.
FillAllMandatoryFields=Please fill in all mandatory fields.
FinalObservations=Final observations
Finish=Finish
Finland=Finland
Florence=Florence
Florida=Florida
Foggia=Foggia
ForageAuditScorecard=Forage audit scorecard
ForageAuditScorecardResponsesViewModel.ImprovementsTab=Forage audit improvements
ForageAuditScorecardResponsesViewModel.ResponsesTab=Forage audit responses
ForageAuditScorecardResultsViewModel.ForageAuditScorecardImprovements=Improvements
ForageAuditScorecardResultsViewModel.ForageAuditScorecardResponses=Responses
ForageAuditScorecardResultsViewModel.ForageAuditScorecardScore=Score
ForageAuditScorecardResultsViewModel.ImprovementsTab=Improvements
ForageAuditScorecardResultsViewModel.ResponsesTab=Responses
ForageAuditScorecardResultsViewModel.ScoreTab=Score
ForageAuditScorecardResultsViewModel.Title=Tower silos
ForageAuditScorecardResultsViewModel.VisitNotebook=Visit notebook
ForageAuditScorecardScoreViewModel.GoodIndicator=Good
ForageAuditScorecardScoreViewModel.ImprovementsIndicator=Improvements
ForageAuditScorecardScoreViewModel.OverallForageScore=Include in overall forage score
ForageAuditScorecardScoreViewModel.Title=Forage audit overall score
ForageAuditSilageTypeViewModel.ForageSilageTypeResource=Forage silage types
ForageAuditViewModel.ForageAuditScorecard=Forage audit scorecard
ForageAuditViewModel.ForageDetail=Forage quality is the foundation of any dairy nutrition program and is a key to the overall profitability of the farm. The forage audit scorecard can be used to evaluate the current forage management practices used on the farm and recommend the key opportunity areas for improvement. The scorecard is organized into key management areas. You can cover all areas during a visit or select only those that are important at the time. Additional management resources are available to help improve those areas where improvement opportunities exist.
ForageAuditViewModel.ForageHeading=Basic forage information
ForageAuditViewModel.Resources=Resources
ForageAuditViewModel.Title=Forage audit
ForageAuditViewModel.VisitNotebook=Visit notebook
ForageAudit_Sample_ToolTip=Sample text for tool tip. Remove when actual available
ForageManagement_ForagesHarvestedAtProperMaturity=Are forages harvested at proper maturity for crop type and storage facility?
ForageManagement_ForagesHarvestedAtProperMoisture=Are forages harvested at proper moisture for crop type and storage facility?
ForageScorecardResultsViewModel.Title=Baleage
ForageScorecardViewModel.Baleage=Baleage
ForageScorecardViewModel.BunkersAndPiles=Bunkers and piles
ForageScorecardViewModel.ForageAuditCategories=Forage audit categories
ForageScorecardViewModel.ForageAuditScore=Forage audit score
ForageScorecardViewModel.ForageAuditScorecard=Forage audit scorecard
ForageScorecardViewModel.ForageCategoryTooltip=Forage quality is the foundation of any dairy nutrition program and is a key to the overall profitability of the farm. This tools can be used to evaluate the current forage management practices used on the farm and recommend the key opportunity areas for improvement
ForageScorecardViewModel.Harvest=Forage quality in the ration
ForageScorecardViewModel.MaintainingForageQuality=Maintaining forage quality
ForageScorecardViewModel.No=No
ForageScorecardViewModel.SilageBags=Silage bags
ForageScorecardViewModel.SurveyCategories=Forage audit scorecards
ForageScorecardViewModel.SurveyOfForages=Forage management
ForageScorecardViewModel.Title=Forage audit scorecard
ForageScorecardViewModel.TowerSilos=Tower silos
ForageScorecardViewModel.ViewOverallForageScore=View overall forage score
ForageScorecardViewModel.VisitNotebook=Visit notebook
ForageScorecardViewModel.Yes=Yes
ForlÃ¬-Cesena=ForlÃ¬-Cesena
Formulate=Formulate
FourScreenNew=4 screen new
FourScreenNewType=(4 mm)
FourScreenOld=4 screen old
FourScreenOldType=(1.18 mm)
FourToSevenDays=4 to 7 days
France=France
FreeFlow=Free flow
FreeFormReportViewModel.Analysis=Analysis
FreeFormReportViewModel.CalfHeiferItem=Calf and heifer
FreeFormReportViewModel.CalfHeiferScorecard=Scorecard
FreeFormReportViewModel.Cargill=Cargill
FreeFormReportViewModel.Charts=Charts
FreeFormReportViewModel.ComfortHeatStressBanner=Heat stress tool pen
FreeFormReportViewModel.ComfortItem=Comfort tool
FreeFormReportViewModel.ExportSelected=Export selected tools
FreeFormReportViewModel.GeneralNotes=General visit notes
FreeFormReportViewModel.HealthItem=Health tool
FreeFormReportViewModel.Inputs=Inputs
FreeFormReportViewModel.KeyBenchmarks=Key benchmarks
FreeFormReportViewModel.MarketingBranding=Go-to-market branding
FreeFormReportViewModel.MilkProcessCalcInputsTab=Milking procedure comparison - inputs
FreeFormReportViewModel.MilkProcessCalcResourcesTab=Milking procedure comparison - resources
FreeFormReportViewModel.MilkProcessCalcResultsTab=Milking procedure comparison - results
FreeFormReportViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
FreeFormReportViewModel.MilkSoldEvaluation=Milk sold evaluation
FreeFormReportViewModel.Notes=Notes 
FreeFormReportViewModel.NutritionForage=Forage audit
FreeFormReportViewModel.NutritionItem=Nutrition tool
FreeFormReportViewModel.NutritionPile=Forage inventories
FreeFormReportViewModel.Outputs=Outputs
FreeFormReportViewModel.OverallImprovements=Overall improvements
FreeFormReportViewModel.OverallResponses=Overall responses
FreeFormReportViewModel.OverallScore=Overall score
FreeFormReportViewModel.PenTimeTitle=Pen time budget
FreeFormReportViewModel.PileAndBunkerFeedOutTab=Forage inventory feed out
FreeFormReportViewModel.ProductivityItem=Productivity tool
FreeFormReportViewModel.Provimi=Provimi
FreeFormReportViewModel.ProvimiUS=Provimi us
FreeFormReportViewModel.Purina=Purina
FreeFormReportViewModel.Results=Results
FreeFormReportViewModel.RoboticMilkingEvaluation=Robotic milking evaluation
FreeFormReportViewModel.RumenHealthBodyConditionTitle=Body condition score
FreeFormReportViewModel.RumenHealthLocomotionTitle=Locomotion score
FreeFormReportViewModel.RumenHealthManureTitle=Rumen health manure score
FreeFormReportViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
FreeFormReportViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&\#8482;
FreeFormReportViewModel.RumenHealthTMRHerdTitle=Rumen health tmr particle herd score
FreeFormReportViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
FreeFormReportViewModel.RumenHealthTitle=Rumen health cud chewing
FreeFormReportViewModel.RumenHealthUrinePHTitle=Urine ph
FreeFormReportViewModel.Title=Free form report
FreeFormReportViewModel.Trends=Trends
FreeFormReportViewModel.VisitTitle=Visit name
FreeFormReportViewModel.WalkThroughNotes=Walkthrough notes
FreeHandNoteClearPaletteDialogMessage=Do you want to delete everything on the screen ?
FreeHandNoteEditorPageTitle=Free hand note
FreeHandNoteSaveUserDialogMessage=Do you want save this note ?
FreeHandNotesViewModel.Save=Save
Freestall=Freestall
French_Guiana=French Guiana
French_Polynesia=French Polynesia
French_Southern_Territories=French Southern Territories
Fresh=Fresh
FreshCow=Fresh cow
FreshHeifer=Fresh Heifer
Frosinone=Frosinone
Fujian=Fujian
GBP=United Kingdom (GBP GBP)
GEA=GEA
GTQ=Guatemala (Q GTQ)
Gabon=Gabon
Galway=Galway
Gambia=Gambia
Gansu=Gansu
General=General
Genoa=Genoa
Georgia=Georgia
Germany=Germany
GettingtheMostOutofYourForage=Getting the most out of your forage
Ghana=Ghana
Gibraltar=Gibraltar
Girolando=Girolando
Global=Global
Goa=Goa
Goal=Goal
Goiás=Goiás
Good=Good
Gorizia=Gorizia
GreaterThan8Hours=Greater than 8 hours
GreaterThanFive=Greater than 5
GreaterThanSevenDays=Greater than 7 days
GreaterThanSixHours=Greater than 6 hours
GreaterThanThirtySixInchesPerDay=Greater than 30 cm (12 inches) per day
GreaterThanTwelveHours=Greater than 12 hours
GreaterThanTwenty=&gt;20
Greece=Greece
Greenland=Greenland
Grenada=Grenada
Grosseto=Grosseto
GrowerPubertyPregnancyCloseup=Grower, puberty, pregnancy, close-up
GrowerPubertyPregnancyCloseup_CleanAndDryPen=Clean and dry pen
GrowerPubertyPregnancyCloseup_CleanAndDryPen_ToolTip=Use wet knee test to determine if area is clean and dry
GrowerPubertyPregnancyCloseup_DesiredBCSIsAchieved=Desired bcs is achieved for stage of maturity
GrowerPubertyPregnancyCloseup_EvidenceOfLooseManure=Evidence of loose manure
GrowerPubertyPregnancyCloseup_FeedBunkIsCleanedDaily=Feed bunk is cleaned daily, refusals removed
GrowerPubertyPregnancyCloseup_FreeChoiceCleanWaterAvailable=Free choice, clean water is available
GrowerPubertyPregnancyCloseup_FreeChoice_ToolTip=No evidence of contamination in water
GrowerPubertyPregnancyCloseup_GroupWithUniformHeiferSize=Groups with uniform heifer size
GrowerPubertyPregnancyCloseup_GroupsWithUniform_ToolTip=Animals in group should be same size
GrowerPubertyPregnancyCloseup_PercentageOfOverCrowding=% of overcrowding
GrowerPubertyPregnancyCloseup_RationsBalanceFroGrowth=Rations balanced for growth targets, reviewed often
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace=Size of bunk space is adequate per heifer
GrowerPubertyPregnancyCloseup_SizeOfBunkSpace_ToolTip=135-270kg need 30cm, 270-400kg need 38cm, >400 need 46cm
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete=Size of pen is adequate per heifer
GrowerPubertyPregnancyCloseup_SizeOfPenIsAdequete_ToolTip=135-270kg need 40ft2 / 4m2, 270-400kg need 50ft2 / 5m2, >400 need 70ft2 / 7m2
Guadeloupe=Guadeloupe
Guam=Guam
Guanajuato=Guanajuato
Guangdong=Guangdong
Guangxi=Guangxi
Guatemala=Guatemala
Guernsey=Guernsey
Guerrero=Guerrero
Guinea=Guinea
Guinea-Bissau=Guinea-Bissau
Guizhou=Guizhou
Gujarat=Gujarat
Guyana=Guyana
HKD=HKD
HNL=Honduras (HNL HNL)
HRK=HRK
HUF=Hungary (Ft HUF)
Hainan=Hainan
Haiti=Haiti
HalfPointScale=Half point scale
Harvest=Forage quality in the ration
Harvest_AdequateEquipmentAndLabor=How long does it take to complete harvest? (adequate equipment and labor)
Harvest_CornSilageMoistureRangeConsistent=What is the average moisture content of the silage? (moisture should be consistent in 90% of samples)
Harvest_ForageHarvestingDocumented=Are forage harvesting conditions, field and storage locations documented?
Harvest_ForagesHarvestedAtProper=Are forages harvested at proper maturity/moisture for crop type and storage facility?
Harvest_KPScoreIsMonitored=How many whole kernels are present in a 1l (32 ounce) sample?
Harvest_LengthOfCutMonitored=Is the length of cut monitored with penn state shaker box at harvest?
Harvest_UseSilageAdditive=Are silage additives, inoculants or aerobic stabilizers used?
Harvest_WholePlantMoistureDetermined=Is whole plant moisture determined for each field?
Haryana=Haryana
Hawaii=Hawaii
Haylage=Haylage/Grass
HealthToolsViewModel.HealthHeading=Please select a tool from the list below.
HealthToolsViewModel.HealthToolsList=TOOLS
HealthToolsViewModel.RumenHealthBodyConditionTitle=Body condition score
HealthToolsViewModel.RumenHealthLocomotionTitle=Locomotion score
HealthToolsViewModel.RumenHealthManureScreening=Rumen Health Manure Screening
HealthToolsViewModel.RumenHealthManureTitle=Rumen health manure score
HealthToolsViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
HealthToolsViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&\#8482;
HealthToolsViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
HealthToolsViewModel.RumenHealthTitle=Rumen health cud chewing
HealthToolsViewModel.RumenHealthUrinePHTitle=Urine ph
HealthToolsViewModel.Title=Health tools
Heard_Island_and_McDonald_Islands=Heard Island and McDonald Islands
HeatstressCalculations=Heat stress calculations
HeatstressChart=Heat stress chart
HeatstressChartViewModel.DMIReduction=Dmi reduction
HeatstressChartViewModel.EnergyEquivMilkLoss=Energy equivalent milk loss
HeatstressChartViewModel.EstimateDryMatter=Estimated dry matter intake
HeatstressChartViewModel.HeatstressEvalLabel=Temperatures corrected for average temperature and relative humidity (without sunshine)
HeatstressChartViewModel.IntakeAdjustment=Intake adjustment
HeatstressChartViewModel.Kilograms=Kg
HeatstressChartViewModel.LossEnergyConsumed=Loss of energy consumed
HeatstressChartViewModel.Mcal=Mcal
HeatstressChartViewModel.MilkValueLossPerDay=Milk value loss / day
HeatstressChartViewModel.MilkValueLossPerMonth=Milk value loss / month
HeatstressChartViewModel.Percentage=%
HeatstressChartViewModel.Pounds=Lbs
HeatstressChartViewModel.ReductionDMI=Reduction in dmi
HeatstressChartViewModel.TempHumidIndex=Temperature humidity index
HeatstressChartViewModel.TemperatureImperial=°F
HeatstressChartViewModel.TemperatureMetric=°C
HeatstressChartViewModel.VisitNotebook=Visit notebook
HeatstressData=Heat stress data
HeatstressDataEntryViewModel.AnimalInputs=Animal inputs
HeatstressDataEntryViewModel.CurrentMilkPrice=Current milk price ({0}/{1})
HeatstressDataEntryViewModel.DMI=Dmi ({0})
HeatstressDataEntryViewModel.Exposure=Exposure
HeatstressDataEntryViewModel.HoursExposed=Hours of sun
HeatstressDataEntryViewModel.Humidity=Humidity (%)
HeatstressDataEntryViewModel.LactatingAnimals=Lactating animals
HeatstressDataEntryViewModel.Milk=Milk ({0})
HeatstressDataEntryViewModel.MilkFat=Milk fat (%)
HeatstressDataEntryViewModel.MilkProtein=Milk protein (%)
HeatstressDataEntryViewModel.NEL=Nel (mcal/{0})
HeatstressDataEntryViewModel.Temperature=Temperature ({0})
HeatstressDataEntryViewModel.VisitNotebook=Visit notebook
HeatstressDataEntryViewModel.Weather=Weather
HeatstressGreen=Stress threshold
HeatstressOrange=Moderate-severe stress
HeatstressRed=Severe stress
HeatstressTableViewModel.HeatstressChartTab=Charts
HeatstressTableViewModel.HeatstressDataTab=Data input
HeatstressTableViewModel.Title=Heat stress evaluation
HeatstressTableViewModel.VisitNotebook=Visit notebook
HeatstressYellow=Mild-moderate stress
Hebei=Hebei
Heifer=Heifer
Heilongjiang=Heilongjiang
Henan=Henan
HerdAnalysisGoalsViewModel.CloseUpDry=Close-up dry
HerdAnalysisGoalsViewModel.CudChewingGoals=Cud chewing goals
HerdAnalysisGoalsViewModel.CudChews=Cud
HerdAnalysisGoalsViewModel.DIM=DIM
HerdAnalysisGoalsViewModel.EarlyLactation=Early lactation
HerdAnalysisGoalsViewModel.FarOffDry=Far-off dry
HerdAnalysisGoalsViewModel.Fresh=Fresh
HerdAnalysisGoalsViewModel.LateLactation=Late lactation
HerdAnalysisGoalsViewModel.MidLactation=Mid lactation
HerdAnalysisGoalsViewModel.PeakMilk=Peak milk
HerdAnalysisGoalsViewModel.PercentChewing=%
HerdAnalysisGoalsViewModel.Title=Herd analysis
HerdAnalysisGoalsViewModel.To=to
HerdAnalysisMasterViewModel.HerdAnalysisCudChewing=Rumen health cud chewing
HerdAnalysisMasterViewModel.HerdAnalysisHeading=Herd analysis
HerdAnalysisMasterViewModel.HerdAnalysisSegmentAnalysis=Herd analysis
HerdAnalysisMasterViewModel.HerdAnalysisSegmentGoals=Goals
HerdAnalysisMasterViewModel.Title=Rumen health cud chewing
HerdAnalysisTableTitle=Cud chewing score analysis
HerdAnalysisViewModel.AverageChews=Average chews per cud
HerdAnalysisViewModel.DaysInMilk=Days in milk
HerdAnalysisViewModel.Edit=Edit
HerdAnalysisViewModel.EditLabel=Edit
HerdAnalysisViewModel.HerdAnalysisTableTitle=Cud chewing score analysis
HerdAnalysisViewModel.HerdCudChewing=Herd cud chewing %
HerdAnalysisViewModel.NoOfCows=Complete the pen analysis for all pens that you have started.
HerdAnalysisViewModel.NumberofChewsPerCud=Number of chews per cud
HerdAnalysisViewModel.PenNameLabel=Pen name
HerdAnalysisViewModel.PercentChewing=Percent chewing
HerdAnalysisViewModel.TableTitle=Cud chewing score analysis
HerdInformation=Herd information
HerdReporting=Herd reporting\: cud chewing
Hidalgo=Hidalgo
Himachal_Pradesh=Himachal Pradesh
Hokkaido=Hokkaido
Holandesa=Holstein
Holy_See_(Vatican_City_State)=Holy See (Vatican City State)
HomeViewModel.AutoSync=Auto sync
HomeViewModel.ConsumersTab=Consumers
HomeViewModel.CustomersTab=Customers
HomeViewModel.DashboardTab=Dashboard
HomeViewModel.Eula=EULA
HomeViewModel.Logout=Logout
HomeViewModel.PrivacyStatement=Privacy statement
HomeViewModel.ProspectsTab=Prospects
HomeViewModel.Settings=Settings
HomeViewModel.SyncWithDash=Sync -
HomeViewModel.SyncWithDate=Sync - last sync date\: {0\:MM/dd/yy}
HomeViewModel.SyncWithTime=Sync - last sync time\: {0\:hh\:mm tt}
HomeViewModel.Title=Sales field tool
Honduras=Honduras
Hong_Kong=Hong Kong
HowtoGetBetterKPResults=How to get better kp results
Hubei=Hubei
Hunan=Hunan
Hungary=Hungary
IDR=Indonesia (Rp IDR)
INR=India (INR INR)
Iceland=Iceland
Idaho=Idaho
Illinois=Illinois
Imperia=Imperia
Imperial=Imperial
Improvements=Improvements
India=India
Indiana=Indiana
Indonesia=Indonesia
InoculantFQAs=Faq about inoculants
Iowa=Iowa
Iran,_Islamic_Republic_of=Iran, Islamic Republic of
Iraq=Iraq
Ireland=Ireland
Isernia=Isernia
Isle_of_Man=Isle of Man
Israel=Israel
Italy=Italy
JOD=JOD
JPY=JPY
Jalisco=Jalisco
Jamaica=Jamaica
Jammu_and_Kashmir=Jammu and Kashmir
Japan=Japan
Jersey=Jersey
Jharkhand=Jharkhand
Jiangsu=Jiangsu
Jiangxi=Jiangxi
Jilin=Jilin
Jordan=Jordan
KBLastPhase=Maintain accurate growth and health records
KRW=South Korea (₩ KRW)
Kansas=Kansas
Karnataka=Karnataka
Kazakhstan=Kazakhstan
Kentucky=Kentucky
Kenya=Kenya
Kerala=Kerala
Kerry=Kerry
Ketosis=Ketosis
KeyBenchmarks=Key benchmarks
KeyBenchmarks_AgeInMonthAtFirstCalving=Age in months at first calving
KeyBenchmarks_CalvingAndHeiferReocrd=Calving and heifer record keeping system used
KeyBenchmarks_FifteenPercentOfMatureBodyWeight=15% of mature body weight at 90 days
KeyBenchmarks_FiftyFivePercentOfMatureBodyWeight=55% of mature body weight at pregnancy
KeyBenchmarks_HeiferPeakProduce=Heifer peak milk as a % of herd average
KeyBenchmarks_NintyDaysMorbidityf=90 days morbidity
KeyBenchmarks_NintyDaysMortality=90 days mortality
KeyBenchmarks_NintyFourPercentOfMatureBodyWeight=94% of mature body weight before calving
KeyBenchmarks_PercentOfHeifersPregnant=Percent of heifers pregnant at 15 months
KeyBenchmarks_SerumlgG=Serum igg (g/l) at 48 hours
Kildare=Kildare
Kilkenny=Kilkenny
Kiribati=Kiribati
Korea=Korea
Korea,_Democratic_People's_Republic_of=Korea, Democratic People's Republic of
Korea,_Republic_of=Korea, Republic of
Kuwait=Kuwait
Kyrgyzstan=Kyrgyzstan
L'Aquila=L'Aquila
LKR=LKR
La_Spezia=La Spezia
Lactating=Lactating
Lactation=Lactation
Lakshadweep=Lakshadweep
Lao_People's_Democratic_Republic=Lao People's Democratic Republic
Laois=Laois
Last_Synced=Last synced
LateLactation=Late lactation
Latina=Latina
Latvia=Latvia
Lebanon=Lebanon
Lecce=Lecce
Lecco=Lecco
Leitrim=Leitrim
Lely=Lely
Length-exceed-allowed-limit=Length exceeded allowed limit
LengthPerDayImperial=In. per day
LengthPerDayMetric=Cm. per day
Lesotho=Lesotho
LessThan4Days=Less than 4 days
LessThanFifteen=&lt;15 (kernal hardness)
LessThanFiveWholeKernals=Less than 5 whole kernals
LessThanOneHour=Less than 1 hour
LessThanSixInches=Less than 7.5 Cm (3 inches)
LessThanSixLayers=Less than 6 layers
LessThanTwentFourInchesPerDay=Less than 15 cm (6 inches) per day
Liaoning=Liaoning
Liberia=Liberia
Libyan_Arab_Jamahiriya=Libyan Arab Jamahiriya
Liechtenstein=Liechtenstein
Lift-Sync-Fail=Lift Sync failed
Limerick=Limerick
LinkToPens=Link to pens (optional)
Lithuania=Lithuania
Livorno=Livorno
LocoCategory1=Cat. 1.0
LocoCategory2=Cat. 2.0
LocoCategory3=Cat. 3.0
LocoCategory4=Cat. 4.0
LocoCategory5=Cat. 5.0
LocomotionEditTableViewModel.Category1=Locomotion score category 1.0
LocomotionEditTableViewModel.Category2=Locomotion score category 2.0
LocomotionEditTableViewModel.Category3=Locomotion score category 3.0
LocomotionEditTableViewModel.Category4=Locomotion score category 4.0
LocomotionEditTableViewModel.Category5=Locomotion score category 5.0
LocomotionEditTableViewModel.EnterNumberOfCows=Please count the number of cows.
LocomotionEditTableViewModel.Title=Number of cows
LocomotionHerdEditGoalViewModel.Category1=Category 1.0
LocomotionHerdEditGoalViewModel.Category2=Category 2.0
LocomotionHerdEditGoalViewModel.Category3=Category 3.0
LocomotionHerdEditGoalViewModel.Category4=Category 4.0
LocomotionHerdEditGoalViewModel.Category5=Category 5.0
LocomotionHerdEditGoalViewModel.HerdGoal=Herd goal
LocomotionHerdEditGoalViewModel.Title=Edit goal amount
LocomotionHerdInputsViewModel.Herd=HERD
LocomotionHerdMasterViewModel.Inputs=Inputs
LocomotionHerdMasterViewModel.Results=Results
LocomotionHerdMasterViewModel.Revenue=Revenue
LocomotionHerdMasterViewModel.SubHeading=Herd analysis
LocomotionHerdMasterViewModel.Title=Locomotion
LocomotionHerdResultsViewModel.Category1=Category 1.0
LocomotionHerdResultsViewModel.Category2=Category 2.0
LocomotionHerdResultsViewModel.Category3=Category 3.0
LocomotionHerdResultsViewModel.Category4=Category 4.0
LocomotionHerdResultsViewModel.Category5=Category 5.0
LocomotionHerdResultsViewModel.HerdAverage=Herd Average
LocomotionHerdResultsViewModel.HerdGoal=Goal
LocomotionHerdResultsViewModel.Title=Locomotion score analysis
LocomotionHerdRevenueViewModel.Revenue=Revenue
LocomotionHerdRevenueViewModel.Title=Locomotion herd revenue
LocomotionNumberinHerd=Locomotion (number in herd)
LocomotionNumberinPen=Locomotion (number in pen)
LocomotionPenInputsViewModel.FromPenSetup=From pen setup
LocomotionPenInputsViewModel.Milk=MILK
LocomotionPenMasterViewModel.Inputs=Inputs
LocomotionPenMasterViewModel.Results=Results
LocomotionPenMasterViewModel.Title=Locomotion
LocomotionPercentofHerd=Locomotion(% of herd)
LocomotionPercentofPen=Locomotion(% of pen)
LocomotionPreviousVisitsViewModel.AverageScore=Average score
LocomotionPreviousVisitsViewModel.LocomotionScoreAverageTitle=Average score
LocomotionPreviousVisitsViewModel.LocomotionScoreDatesTitle=Date
LocomotionPreviousVisitsViewModel.PercentPen=Percent of pen (%)
LocomotionPreviousVisitsViewModel.SelectedDates=Select dates
LocomotionPreviousVisitsViewModel.Title=Locomotion score results
LocomotionScore=Locomotion Score
LocomotionScoreAverage=Average locomotion score
LocomotionScoreHerd=Locomotion score analysis
LocomotionScoreReference=Locomotion score reference
LocomotionSelectPenViewModel.MissingDiet=Please enter a valid maxdiet for this pen.
LocomotionSelectPenViewModel.PenSelectionList=PENS
LocomotionSelectPenViewModel.Title=Locomotion
Lodi=Lodi
LoginViewModel.Copyright=© {0} Cargill, incorporated. All rights reserved.
LoginViewModel.EmailLabel=Email
LoginViewModel.ErrorDescription=Username and password must not be blank.
LoginViewModel.ErrorTitle=Login error
LoginViewModel.InvalidMessage=The username or password supplied was not valid to login.
LoginViewModel.InvalidMessageTitle=Invalid login
LoginViewModel.LoginPrompt=Cargill login
LoginViewModel.LoginPromptConsumer=Other login
LoginViewModel.NetworkErrorMessage=There is currently no network available.
LoginViewModel.NetworkErrorMessageTitle=Network error
LoginViewModel.PasswordLabel=Password
LoginViewModel.Title=Login
LoginViewModel.TitleMsg=Logging in... Please wait...
LoginViewModel.Unauthorized=Unauthorized access.
LoginViewModel.UnauthorizedTitle=Unauthorized
LogoutConnectivityCheck=Error sending logs. Please check your internet connectivity.
Longford=Longford
Louisiana=Louisiana
Louth=Louth
LowForage=Low Forage
Lucca=Lucca
Luxembourg=Luxembourg
MEQ100G=mEq/100g
MKD=MKD
MUN=Mun (mg/dl)
MXN=Mexico (PESO MXN)
MYR=Malaysia (MYR MYR)
Macao=Macao
Macedonia,_the_former_Yugoslav_Republic_of=Macedonia, the former Yugoslav Republic of
Macerata=Macerata
Madagascar=Madagascar
Madhya_Pradesh=Madhya Pradesh
Maharashtra=Maharashtra
MainViewModel.EmailLabel=Email
Maine=Maine
MaintainingForageQuality=Maintaining forage quality
MaintainingForageQuality_BonusMoldInhibitorUsedTMR=Is a stabilizer/mold inhibitor used in tmr during hot/humid weather?
MaintainingForageQuality_TMRMixHasPleasantAroma=Does the tmr mix smell good?
MaintainingForageQuality_TMRMixIsCoolToTouch=Is the tmr mix cool to the touch?
Making_Feed_InventoryFOF=Making a feed inventory
Malawi=Malawi
Malaysia=Malaysia
Maldives=Maldives
Male=Male
Mali=Mali
Malta=Malta
ManagingForageinSiloBags=Managing forage in silo bags
ManagingForageinTowerSilos=Managing forage in tower silos
Manipur=Manipur
Manitoba=Manitoba
Mantua=Mantua
ManureEditScores=Manure scores - edit scores
ManureScoreHerdAnalysisEditInputsViewModel.Close=Close
ManureScoreHerdAnalysisEditInputsViewModel.ManureScoreDIMTitle=Days in milk (dim)
ManureScoreHerdAnalysisEditInputsViewModel.Title=Edit dim amount
ManureScoreHerdAnalysisInputsViewModel.ManureScore=Manure score
ManureScoreHerdAnalysisInputsViewModel.ManureScoreAnalysis=Manure score analysis
ManureScoreHerdAnalysisInputsViewModel.ManureScoreDIM=Days in milk (dim)
ManureScoreHerdAnalysisInputsViewModel.ManureScoreEdit=Edit
ManureScoreHerdAnalysisMasterViewModel.Goals=Goals
ManureScoreHerdAnalysisMasterViewModel.Inputs=Inputs
ManureScoreHerdAnalysisMasterViewModel.Results=Results
ManureScoreHerdAnalysisMasterViewModel.SubHeading=Herd analysis
ManureScoreHerdAnalysisMasterViewModel.Title=Rumen health manure score
ManureScoreHerdAnalysisResultsViewModel.GraphTitle=Manure score analysis
ManureScoreHerdAnalysisResultsViewModel.ManureScore=Manure score
ManureScoreHerdAnalysisResultsViewModel.ManureScoreAvg=Average
ManureScoreHerdAnalysisResultsViewModel.MaxManureScore=Max. score
ManureScoreHerdAnalysisResultsViewModel.MinManureScore=Min. score
ManureScoreHerdEditGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
ManureScoreHerdEditGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
ManureScoreHerdEditGoalsViewModel.EditDatesClose=Close
ManureScoreHerdEditGoalsViewModel.FarOffDry=Far-off dry (less than -21)
ManureScoreHerdEditGoalsViewModel.Fresh=Fresh (0 to 15)
ManureScoreHerdEditGoalsViewModel.LateLactation=Late lactation (greater than 201)
ManureScoreHerdEditGoalsViewModel.MaxGoal=Manure max
ManureScoreHerdEditGoalsViewModel.MidLactation=Mid lactation (121 to 200)
ManureScoreHerdEditGoalsViewModel.MinGoal=Manure min
ManureScoreHerdEditGoalsViewModel.PeakMilk=Peak milk (61 to 120)
ManureScoreHerdEditGoalsViewModel.Title=Edit goals
ManureScoreHerdGoalsViewModel.CloseUpDry=Close-up dry (-20 to -1)
ManureScoreHerdGoalsViewModel.EarlyLactation=Early lactation (16 to 60)
ManureScoreHerdGoalsViewModel.Edit=Edit
ManureScoreHerdGoalsViewModel.FarOffDry=Far-off dry (less than -21)
ManureScoreHerdGoalsViewModel.Fresh=Fresh (0 to 15)
ManureScoreHerdGoalsViewModel.GoalMaxTitle=Manure goal max
ManureScoreHerdGoalsViewModel.GoalMinTitle=Manure goal min
ManureScoreHerdGoalsViewModel.LateLactation=Late lactation (greater than 201)
ManureScoreHerdGoalsViewModel.MidLactation=Mid lactation (121 to 200)
ManureScoreHerdGoalsViewModel.PeakMilk=Peak milk (61 to 120)
ManureScoreHerdGoalsViewModel.TableTitle=Score by stage of lactation
ManureScorePenSelectionViewModel.ManureScoreTitle=Rumen health manure score
ManureScorePenSelectionViewModel.PenSelectionList=PENS
ManureScorePercentOfPen=Manure score(% of pen)
ManureScoresChart=Manure scores-chart
ManureScoresResult=Manure scores-result
Maranhão=Maranhão
Martinique=Martinique
Maryland=Maryland
Massa_and_Carrara=Massa and Carrara
Massachusetts=Massachusetts
Matera=Matera
Mato_Grosso=Mato Grosso
Mato_Grosso_do_Sul=Mato Grosso do Sul
Mauritania=Mauritania
Mauritius=Mauritius
Max=MAX
Mayo=Mayo
Mayotte=Mayotte
Mcal=Mcal
Meath=Meath
Medio_Campidano=Medio Campidano
Medium=Medium
Meghalaya=Meghalaya
MenuViewModel.Close=Close
MenuViewModel.LogoutPrompt=Logout
MenuViewModel.Menu=Menu
MenuViewModel.ResetDatabaseCancel=Cancel
MenuViewModel.ResetDatabasePrompt=This will replace any existing data, including user preferences, with a starting set of test data. Visit tools and new visits created and processors will be lost. You will also be logged out of the app. Continue?"
MenuViewModel.ResetDatabaseReset=Reset
MenuViewModel.ResetDatabaseTitle=Reset test data
MenuViewModel.Sync_PopUp=Syncing your data..
MenuViewModel.UploadingBlob=Capturing data... Please wait.
Messina=Messina
MetabolicIncidenceChartsViewModel.Current=Current
MetabolicIncidenceChartsViewModel.DeathLoss=Death loss
MetabolicIncidenceChartsViewModel.DisorderGraphTitle=Metabolic Disorder Cost / Cow
MetabolicIncidenceChartsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceChartsViewModel.Dystocia=Dystocia
MetabolicIncidenceChartsViewModel.GoalPercent=Goal (%)
MetabolicIncidenceChartsViewModel.GraphTitle=Metabolic Incidence %
MetabolicIncidenceChartsViewModel.IncidencePercent=Incidence (%)
MetabolicIncidenceChartsViewModel.Ketosis=Ketosis
MetabolicIncidenceChartsViewModel.Metritis=Metritis
MetabolicIncidenceChartsViewModel.MilkFever=Milk fever
MetabolicIncidenceChartsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceChartsViewModel.Title=Metabolic incidence charts
MetabolicIncidenceEditOutputsViewModel.Close=Close
MetabolicIncidenceEditOutputsViewModel.DeathLoss=Death loss
MetabolicIncidenceEditOutputsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceEditOutputsViewModel.Dystocia=Dystocia
MetabolicIncidenceEditOutputsViewModel.Ketosis=Ketosis
MetabolicIncidenceEditOutputsViewModel.MetabolicIncidenceGoalTitle=Goal (%)
MetabolicIncidenceEditOutputsViewModel.Metritis=Metritis
MetabolicIncidenceEditOutputsViewModel.MilkFever=Milk fever
MetabolicIncidenceEditOutputsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceEditOutputsViewModel.Title=Edit goals
MetabolicIncidenceInputsEditViewModel.Close=Close
MetabolicIncidenceInputsEditViewModel.DeathLoss=Mastitis
MetabolicIncidenceInputsEditViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceInputsEditViewModel.Dystocia=Dystocia
MetabolicIncidenceInputsEditViewModel.IncreasedDaysOpen=Days open
MetabolicIncidenceInputsEditViewModel.Ketosis=Ketosis
MetabolicIncidenceInputsEditViewModel.Metritis=Metritis
MetabolicIncidenceInputsEditViewModel.MilkCow=Milk / cow ({0})
MetabolicIncidenceInputsEditViewModel.MilkFever=Milk fever
MetabolicIncidenceInputsEditViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceInputsEditViewModel.Title=Edit cost attributes
MetabolicIncidenceInputsEditViewModel.TreatmentCost=Treatment and other costs (culling, death)-usd
MetabolicIncidenceInputsViewModel.CostExtraDaysOpen=Cost of extra days open
MetabolicIncidenceInputsViewModel.Costs=Costs
MetabolicIncidenceInputsViewModel.DeathLoss=Death loss
MetabolicIncidenceInputsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceInputsViewModel.Dystocia=Dystocia
MetabolicIncidenceInputsViewModel.Herd=Herd level information
MetabolicIncidenceInputsViewModel.IncidenceCaseMessage=Enter the number of fresh cows and the number of metabolic incidence cases during the evaluation period. This will be converted to an annualized incidence cost on the outputs tab.
MetabolicIncidenceInputsViewModel.IncidenceCases=Metabolic Incidence Cases
MetabolicIncidenceInputsViewModel.IncreasedDaysOpen=Increased days open
MetabolicIncidenceInputsViewModel.Ketosis=Ketosis
MetabolicIncidenceInputsViewModel.Mastitis=Mastitis
MetabolicIncidenceInputsViewModel.Metritis=Metritis
MetabolicIncidenceInputsViewModel.MilkFever=Milk fever
MetabolicIncidenceInputsViewModel.MilkLossKg=Milk loss per lactation ({0})
MetabolicIncidenceInputsViewModel.MilkPrice=Milk price
MetabolicIncidenceInputsViewModel.PerformanceMessage=Reference data used to calculate the economic impact of each metabolic incidence.
MetabolicIncidenceInputsViewModel.PerformanceTreatment=Performance and treatment costs
MetabolicIncidenceInputsViewModel.ReplacementCowCost=Replacement cow cost
MetabolicIncidenceInputsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceInputsViewModel.Title=Metabolic incidence inputs
MetabolicIncidenceInputsViewModel.TotalFreshCowsEvaluation=Total fresh cows for evaluation
MetabolicIncidenceInputsViewModel.TotalFreshCowsPerYear=Total fresh cows/year
MetabolicIncidenceInputsViewModel.TreatmentCost=Treatment and other cost (culling,death)-usd
MetabolicIncidenceMasterViewModel.Charts=Charts
MetabolicIncidenceMasterViewModel.Inputs=Inputs
MetabolicIncidenceMasterViewModel.Outputs=Outputs
MetabolicIncidenceMasterViewModel.Title=Metabolic incidence
MetabolicIncidenceOutputsViewModel.DeathLoss=Death loss
MetabolicIncidenceOutputsViewModel.DisplacedAbomasum=Displaced abomasum
MetabolicIncidenceOutputsViewModel.Dystocia=Dystocia
MetabolicIncidenceOutputsViewModel.Ketosis=Ketosis
MetabolicIncidenceOutputsViewModel.MetabolicIncidence=Incidence (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceCostCow=Cost / cow
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDaysOpen=Increased days open
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceDifference=Difference (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceEdit=Edit
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceGoal=Goal (%)
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpact=Economic impact of metabolic incidence levels above the defined goals for the herd.
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTitle=Annual economic impact
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceImpactTotalTitle=Annual economic impact - total
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceMilkLoss=Milk loss value
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTitle=Metabolic incidence percent
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTotalCost=Total cost
MetabolicIncidenceOutputsViewModel.MetabolicIncidenceTreatment=Treatment and other cost (culling,death)-usd
MetabolicIncidenceOutputsViewModel.Metritis=Metritis
MetabolicIncidenceOutputsViewModel.MilkFever=Milk fever
MetabolicIncidenceOutputsViewModel.RetainedPlacenta=Retained placenta
MetabolicIncidenceOutputsViewModel.Title=Metabolic incidence outputs
MetabolicIncidenceOutputsViewModel.TotalLosses=Total annual losses
Metric=Metric
MetricTonsAF=Metric tons af
MetricTonsAFSilo=Metric tons af (remaining in silo)
MetricTonsDM=Metric tons dm
MetricTonsDMSilo=Metric tons dm (remaining in silo)
Metritis=Metritis
Mexico=Mexico
Mexico_State=Mexico State
Michigan=Michigan
Michoacán=Michoacán
MidLactation=Mid lactation
MidOne=Mid 1
MidOneValue=(8mm)
MidTwo=Mid 2
Milan=Milan
MilkChange=Milk change ({0})
MilkFever=Milk fever
MilkLetDownResponse=Milk let down response
MilkLossDay=Milk loss ({0}/day)
MilkLossGain=Potential milk loss/gain
MilkLossKg=Milk loss (kgs)
MilkLossPounds=Milk loss (lbs)
MilkLossYear=Milk loss ({0}/year)
MilkPrice=Milk price ({0}/{1})
MilkPricePremiums=Milk price premiums
MilkProcessRevCalcResourcesViewModel.ResourcesReferenceChart=Reference chart
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcInputsTab=Inputs
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResourcesTab=Resources
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessCalcResultsTab=Results
MilkProcessRevenueCalculatorMasterViewModel.MilkProcessRevenue=Milking procedure comparison
MilkProcessRevenueCalculatorMasterViewModel.Title=Milking procedure comparison
MilkProcessRevenueCalculatorMasterViewModel.VisitNotebook=Visit notebook
MilkProcessorEditComparisonValuesViewModel.InadequateStimulation=Inadequate stimulation
MilkProcessorEditComparisonValuesViewModel.MilkPrice=Milk price ({0}/{1})
MilkProcessorEditComparisonValuesViewModel.NoStimulation=No stimulation
MilkProcessorEditComparisonValuesViewModel.OptimalStimulation=Optimal stimulation
MilkProcessorEditComparisonValuesViewModel.ScenarioOne=Scenario 1
MilkProcessorEditComparisonValuesViewModel.ScenarioTwo=Scenario 2
MilkProcessorEditComparisonValuesViewModel.Title=Edit comparison values
MilkProcessorEditComparisonValuesViewModel.WeightImperialCWT=CWT
MilkProcessorEditComparisonValuesViewModel.WeightMetric=kg
MilkProcessorInputViewModel.ComparisonValues=Comparison values
MilkProcessorInputViewModel.Edit=Edit
MilkProcessorInputViewModel.MilkPrice=Milk price ({0}/{1})
MilkProcessorInputViewModel.ProcessorDeletedPrompt=The processor previously selected has been deleted. Please select another processor to continue.
MilkProcessorInputViewModel.ScenarioOne=Scenario 1
MilkProcessorInputViewModel.ScenarioTwo=Scenario 2
MilkProcessorInputViewModel.SelectProcessor=Select processor
MilkProcessorInputViewModel.Title=Milking procedure comparison inputs
MilkProcessorInputViewModel.WeightImperialCWT=CWT
MilkProcessorInputViewModel.WeightMetric=kg
MilkProcessorResourcesViewModel.ApproxSCC=Approximate scc (cells/ml)
MilkProcessorResourcesViewModel.LinearScore=Linear score
MilkProcessorResourcesViewModel.Mastitis=Mastitis milk loss ({0})
MilkProcessorResourcesViewModel.ResourcesReferenceChart=Reference chart
MilkProcessorResourcesViewModel.Title=Milking procedure comparison - resources
MilkProcessorResultsViewModel.Change=Change
MilkProcessorResultsViewModel.HundredWeight=CWT
MilkProcessorResultsViewModel.MilkPrice=Milk price ({0}/{1})
MilkProcessorResultsViewModel.ResultsHeader=Annual value changes
MilkProcessorResultsViewModel.ScenarioOne=Scenario 1
MilkProcessorResultsViewModel.ScenarioTwo=Scenario 2
MilkProcessorResultsViewModel.Title=Milking procedure comparison - results
MilkProcessorResultsViewModel.WeightImperialCWT=CWT
MilkProcessorResultsViewModel.WeightMetric=kg
MilkProcessorSettingsComponentViewModel.MilkProcComponent=Component
MilkProcessorSettingsConcentrationViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsMasterViewModel.MilkProcComponent=Component
MilkProcessorSettingsMasterViewModel.MilkProcConcentration=Concentration
MilkProcessorSettingsMasterViewModel.MilkProcNew=New
MilkProcessorSettingsMasterViewModel.Title=Milk processor set up
MilkProcessorViewModel.Amount=Amount (1000 cells / ml)
MilkProcessorViewModel.AmountCFU=Amount (1000 cfu / ml)
MilkProcessorViewModel.BasePriceMilkFat=Milk fat ({0}/{1})
MilkProcessorViewModel.BasePriceMilkPrice=Milk ({0}/{1})
MilkProcessorViewModel.BasePriceMilkProtein=Milk protein ({0}/{1})
MilkProcessorViewModel.BasePriceOtherSolids=Other solids ({0}/{1})
MilkProcessorViewModel.BasePrices=Base prices
MilkProcessorViewModel.ComponentProcessor=Component processor
MilkProcessorViewModel.ConcentrationProcessor=Concentration processor
MilkProcessorViewModel.Delete=Delete
MilkProcessorViewModel.DeletePrompt=Deleting this processor will invalidate results in the milking procedure comparison tool. Do you want to delete this processor?
MilkProcessorViewModel.HundredWeight=CWT
MilkProcessorViewModel.Name=Name
MilkProcessorViewModel.NameNotUnique=A component processor named "{0}" already exists. Names must be unique.
MilkProcessorViewModel.NewComponentProcessorName=Component processor \#{0}
MilkProcessorViewModel.NewConcentrationProcessorName=Concentration processor \#{0}
MilkProcessorViewModel.PricingMatrices=Pricing matrices
MilkProcessorViewModel.SelectCurrency=Select currency
MilkProcessorViewModel.WeightImperial=LBS
MilkProcessorViewModel.WeightImperialCWT=CWT
MilkProcessorViewModel.WeightMetric=KG
MilkProduction=Milk production ({0})
MilkProductionKg=Milk production (kgs)
MilkProductionPounds=Milk production (lbs)
MilkProductionRevenue=Milk production revenue
MilkSoldEvaluationChartsListViewModel.ComponentYieldEfficiency=Component Yield and Efficiency
MilkSoldEvaluationChartsListViewModel.DMIAndFeedEfficiency=Dry Matter Intake and Feed Efficiency
MilkSoldEvaluationChartsListViewModel.MilkFatPercentMilkProteinPercent=Milk Fat % and Milk Protein %
MilkSoldEvaluationChartsListViewModel.MilkProductionDIM=Milk Production and Days in Milk
MilkSoldEvaluationChartsListViewModel.SomanticCellMilkUrea=Somatic Cell Count and Milk Urea
MilkSoldEvaluationChartsListViewModel.VisitComparison=Please select visits for comparison
MilkSoldEvaluationChartsViewModel.ComponentEfficiency=Component Efficiency
MilkSoldEvaluationChartsViewModel.ComponentYield=Component Yield
MilkSoldEvaluationChartsViewModel.ComponentYieldEfficiency=Component yield and efficiency
MilkSoldEvaluationChartsViewModel.DMIAndFeedEfficiency=Dry matter intake and feed efficiency
MilkSoldEvaluationChartsViewModel.DaysInMilkItem=Days in milk
MilkSoldEvaluationChartsViewModel.DryMatterIntake=Dry Matter Intake
MilkSoldEvaluationChartsViewModel.FeedEfficiency=Feed Efficiency
MilkSoldEvaluationChartsViewModel.MilkFat=Milk fat %
MilkSoldEvaluationChartsViewModel.MilkFatPercentMilkProteinPercent=Milk fat % and milk protein %
MilkSoldEvaluationChartsViewModel.MilkProduction=Milk Production
MilkSoldEvaluationChartsViewModel.MilkProductionDIM=Milk production and days in milk
MilkSoldEvaluationChartsViewModel.MilkProtein=Milk protein %
MilkSoldEvaluationChartsViewModel.MilkUreaMeasure=Milk Urea
MilkSoldEvaluationChartsViewModel.SomanticCellCount=Somatic Cell Count
MilkSoldEvaluationChartsViewModel.SomanticCellMilkUrea=Somatic cell count and milk urea
MilkSoldEvaluationChartsViewModel.Title=Milk sold evaluation
MilkSoldEvaluationInputsViewModel.AddPickup=Add pickup
MilkSoldEvaluationInputsViewModel.AnimalsInTank=Animals in tank ⃰
MilkSoldEvaluationInputsViewModel.DaysInMilk=Days in milk (dim) ⃰
MilkSoldEvaluationInputsViewModel.DryMatterIntake=Dry matter intake ({0}) ⃰
MilkSoldEvaluationInputsViewModel.Herd=Herd level information
MilkSoldEvaluationInputsViewModel.LactatingAnimals=Lactating animals ⃰
MilkSoldEvaluationInputsViewModel.MilkPickup=Milk pickup ⃰
MilkSoldEvaluationInputsViewModel.MilkProcessorInformation=Milk processor information
MilkSoldEvaluationInputsViewModel.MilkUreaMeasure=Milk urea measure ⃰
MilkSoldEvaluationMasterViewModel.AddNew=Add new
MilkSoldEvaluationMasterViewModel.Charts=Charts
MilkSoldEvaluationMasterViewModel.Inputs=Inputs
MilkSoldEvaluationMasterViewModel.Outputs=Outputs
MilkSoldEvaluationMasterViewModel.Title=Milk sold evaluation
MilkSoldEvaluationOutputsViewModel.AvgBCC=Average bacteria count (1,000 cfu/ml)
MilkSoldEvaluationOutputsViewModel.AvgMilkFat=Average milk fat %
MilkSoldEvaluationOutputsViewModel.AvgMilkProduction=Average milk production, {0}
MilkSoldEvaluationOutputsViewModel.AvgMilkProductionAnimalsInTank=Average milk production, {0} (animals in tank)
MilkSoldEvaluationOutputsViewModel.AvgMilkProtein=Average milk protein %
MilkSoldEvaluationOutputsViewModel.AvgSCC=Average scc (1,000 cells/ml)
MilkSoldEvaluationOutputsViewModel.ComponentEfficiency=Component efficiency (% of dmi)
MilkSoldEvaluationOutputsViewModel.EvaluationDays=Evaluation days
MilkSoldEvaluationOutputsViewModel.FeedEfficiency=Feed efficiency (ratio)
MilkSoldEvaluationOutputsViewModel.MilkFatProteinYield=Milk fat + protein yield ({0})
MilkSoldEvaluationOutputsViewModel.MilkFatYield=Milk fat yield ({0})
MilkSoldEvaluationOutputsViewModel.MilkProteinYield=Milk protein yield ({0})
MilkSoldEvaluationOutputsViewModel.UpdateSiteSetup=Update site setup
MilkSoldPickupViewModel.AnimalsInTank=Animals in tank ⃰
MilkSoldPickupViewModel.BCC=Bacteria cell count (1,000 cfu/ml)
MilkSoldPickupViewModel.DaysInTank=Days in tank ⃰
MilkSoldPickupViewModel.MilkFat=Milk fat %
MilkSoldPickupViewModel.MilkProtein=Milk Protein %
MilkSoldPickupViewModel.MilkSold=Milk sold, {0} ⃰
MilkSoldPickupViewModel.SCC=Somatic cell count (1,000 cells/ml)
MilkSoldPickupViewModel.Title=Edit pickup {0}
MilkSoldSpinnerViewModel.Title=Milk sold evaluation
MilkUrea=Milk urea (mg/dl)
Milking=Milking
MilkingFailure=Milking failures
MilkingFirst=Milking first
MilkingProcessRevenueInputs=Milking procedure comparison - inputs
MilkingProcessRevenueResources=Milking procedure comparison - resources
MilkingProcessRevenueResults=Milking procedure comparison - results
Minas_Gerais=Minas Gerais
Minnesota=Minnesota
Mississippi=Mississippi
Missouri=Missouri
Mizoram=Mizoram
Modena=Modena
Moderate=Moderate
ModeratelyClean=Moderately clean
Moldova,_Republic_of=Moldova, Republic of
Monaco=Monaco
Monaghan=Monaghan
Mongolia=Mongolia
Montana=Montana
Montenegro=Montenegro
Monthly=Monthly
Montserrat=Montserrat
Monza_and_Brianza=Monza and Brianza
MoreThan8LayersOfPlastic=More than 8 layers of plastic
Morelos=Morelos
Morocco=Morocco
Mozambique=Mozambique
Myanmar=Myanmar
NGN=NGN
NIO=Nicaragua (NIO NIO)
NOK=NOK
Nagaland=Nagaland
Namibia=Namibia
Naples=Naples
Nauru=Nauru
Nayarit=Nayarit
Nebraska=Nebraska
Nei_Mongol=Nei Mongol
Nepal=Nepal
Netherlands=Netherlands
Nevada=Nevada
NewBunker=Please give the new bunker a name.
NewDietClassViewModel.Title=Animal class / sub class
NewDietPensViewModel.AssociatePens=You can associate this maxdiet to multiple pens.
NewDietPensViewModel.Title=Link to pens
NewDietViewModel.Cancel=Cancel
NewDietViewModel.MainHeading=Diet name ⃰
NewDietViewModel.Save=Save
NewDietViewModel.Title=New diet
NewPenDietViewModel.New=New
NewPenDietViewModel.Title=Diet
NewPenViewModel.Animals=Animals per pen
NewPenViewModel.AnimalsInputsPen=Animals inputs, pen
NewPenViewModel.AsFedIntake=As-fed intake ({0})
NewPenViewModel.Barn=Barn name
NewPenViewModel.Cancel=Cancel
NewPenViewModel.DaysInMilk=Days in milk (dim)
NewPenViewModel.Diet=Diet
NewPenViewModel.DietInputsPen=Diet inputs, pen
NewPenViewModel.DryMatterIntake=Dry matter intake ({0})
NewPenViewModel.FeedingSystem=Feeding system
NewPenViewModel.General=General
NewPenViewModel.HousingSystem=Housing system
NewPenViewModel.InfoNewPenDetails=Add or update pen specific data on this page. You can also add or update data within the tools as you use them.
NewPenViewModel.Milk=Milk yield ({0})
NewPenViewModel.MilkingFrequency=Milking frequency
NewPenViewModel.NetEnergyOfLactationDairy=Nel dairy (mcal/{0})
NewPenViewModel.NumberOfStalls=Number of stalls
NewPenViewModel.OnlyOnePen=You have only one pen.
NewPenViewModel.PenDetail=Pen detail
NewPenViewModel.PenMapping=Pen mapping
NewPenViewModel.PenName=Pen name
NewPenViewModel.PenSelection=Select pen
NewPenViewModel.PublishPenAlert=Please publish all visit related to pen you want to merge.
NewPenViewModel.RationCostPerAnimal=Ration cost per animal ({0})
NewPenViewModel.Save=Save
NewPenViewModel.Title=New pen
NewPenViewModel.UserCreatedPen=User created pen
NewPile=Please give the new pile a name.
NewProspectViewModel.Address1=Business address 1
NewProspectViewModel.Address2=Business address 2
NewProspectViewModel.Aiden=Aiden
NewProspectViewModel.Baxter=Baxter
NewProspectViewModel.BusinessName=Business name
NewProspectViewModel.City=CITY
NewProspectViewModel.ConsumerDetails=Consumer details
NewProspectViewModel.Country=COUNTRY
NewProspectViewModel.Customer=Customer
NewProspectViewModel.CustomerDetail=Customer details
NewProspectViewModel.Dennis=Dennis
NewProspectViewModel.EmailAddress=Contact email
NewProspectViewModel.EndUser=End user
NewProspectViewModel.FarmProducer=Farm producer
NewProspectViewModel.Image=Tap to edit picture
NewProspectViewModel.InvalidEmail=Please enter a valid email.
NewProspectViewModel.Kobe=Kobe
NewProspectViewModel.Mila=Mila
NewProspectViewModel.NameNotUnique=A prospect named "{0}" already exists. Names must be unique.
NewProspectViewModel.NameNotUniqueForConsumer=A consumer named "{0}" already exists. Names must be unique.
NewProspectViewModel.Noah=Noah
NewProspectViewModel.NotSet=- 
NewProspectViewModel.NullBusinessName=Business name is required.
NewProspectViewModel.NullFirstName=First name is required.
NewProspectViewModel.NullSecondName=Last name is required.
NewProspectViewModel.PostalCode=Postal code
NewProspectViewModel.PrimaryContactFirstName=Primary contact first name
NewProspectViewModel.PrimaryContactLastName=Primary contact last name
NewProspectViewModel.PrimaryPhone=Contact phone number
NewProspectViewModel.Prospect=Prospect
NewProspectViewModel.ProspectDetail=Prospect details
NewProspectViewModel.Segment=SEGMENT
NewProspectViewModel.Sonya=Sonya
NewProspectViewModel.Spence=Spence
NewProspectViewModel.State=STATE
NewProspectViewModel.Title=Details
NewProspectViewModel.Type=TYPE
NewProspectViewModel.Walton=Walton
NewVisitViewModel.Title=Visit Details
New_Brunswick=New Brunswick
New_Caledonia=New Caledonia
New_Hampshire=New Hampshire
New_Jersey=New Jersey
New_Mexico=New Mexico
New_South_Wales=New South Wales
New_York=New York
New_Zealand=New Zealand
Newfoundland_and_Labrador=Newfoundland and Labrador
Next=Next
Nicaragua=Nicaragua
Niedersachsen=Niedersachsen
Niger=Niger
Nigeria=Nigeria
Ningxia=Ningxia
Niue=Niue
No=No
No-User-Found=User not found on LIFT; please contact the admin for resolution
NoResourcesAvailable=Resource not available.
NoResults=No results found
NoWholeKernals=No whole kernals
Noah=Noah
None=None
NoneSelected=None selected
Nordrhein=Nordrhein
Norfolk_Island=Norfolk Island
Normal=&lt;0.5bcs unit
NorthAmerica=North america
North_Carolina=North Carolina
North_Dakota=North Dakota
Northern_Territory=Northern Territory
Northwest_Territories=Northwest Territories
Norway=Norway
Not-matching-with-allowed-values=Not matching with allowed values
NotMeasured=Not measured
NotRemoved=Not removed
NotSet=-
NoteCamcorderNotImplemented"=xml\:space\="preserve">"
NoteCategoryViewModel.FooterText=Only one category can be selected per note.
NoteCategoryViewModel.SelectCategory=Select a category
NoteCategoryViewModel.Title=Note
NotebookBCSHerdAnalysisGoals=Bcs herd analysis goals
NotebookBCSHerdAnalysisInputs=Bcs herd analysis inputs
NotebookBCSHerdAnalysisResults=Bcs herd analysis results
NotebookBCSSelectPointScale=Body condition-select point scale
NotebookBodyConditionEdit=Body condition edit table
NotebookCudCalculators=Rumen health-cud calculators
NotebookCudChewing=Rumen health-cud chewing select pen
NotebookCudChewingDataEntry=Rumen health-cud chewing entry
NotebookCudChewingResults=Rumen health-cud chewing results
NotebookLocomotionEditTable=Locomotion-number of cows
NotebookLocomotionHerdInputs=Locomotion-herd analysis inputs
NotebookLocomotionHerdResults=Locomotion-herd analysis results
NotebookLocomotionHerdRevenue=Locomotion-herd analysis revenue
NotebookLocomotionLanding=Locomotion-Main
NotebookLocomotionPenInputs=Locomotion-pen inputs
NotebookLocomotionPenResults=Locomotion-pen results
NotebookLocomotionPenSelection=Locomotion-pen selection
NotebookManurePenSelection=Manure score-pen selection
NotebookManureScoreHerdAnalysisGoals=Manure score herd-goals
NotebookManureScoreHerdAnalysisInputs=Manure score herd-inputs
NotebookManureScoreHerdAnalysisResults=Manure score herd-results
NotebookManureScoreLanding=Manure Score
NotebookMetabolicIncidenceCharts=Metabolic incidence charts
NotebookMetabolicIncidenceInputs=Metabolic incidence inputs
NotebookMetabolicIncidenceOutputs=Metabolic incidence outputs
NotebookParticleScoreHerdAnalysisEdit=Tmr particle score herd analysis-edit dim amount
NotebookParticleScoreLanding=Tmr particle score
NotebookParticleScoreSelectPen=Tmr particle score-select pen
NotebookParticleScoreSelectScorer=Tmr particle score-select scorer
NotebookPenTimeComparison=Pen time budget-comparison
NotebookPenTimeInputs=Pen time budget-inputs
NotebookPenTimePenSelection=Pen time budget-pen selection
NotebookPenTimeResults=Pen time budget-results
NotebookReadyToMilkCharts=ReadyToMilk charts
NotebookReadyToMilkInputs=ReadyToMilk inputs
NotebookReadyToMilkOutputs=ReadyToMilk outputs
NotebookRumenHealthNumberOfChewsInput=Rumen health-number of chews inputs
NotebookRumenHealthNumberOfChewsResults=Rumen health-number of chews results
NotebookRumenHealthTMRParticlePercent=Tmr percent on screen
NotebookRumenHealthTMRParticleScore=Tmr particle score
NotebookSectionComfortTools=Comfort tools
NotebookSectionForageAudit=Forage audit scorecard
NotebookSectionForageAuditBaleage=Forage audit-baleage
NotebookSectionForageAuditBunkersPiles=Forage audit-bunkers and piles
NotebookSectionForageAuditHarvest=Forage audit-harvest
NotebookSectionForageAuditLanding=Forage audit
NotebookSectionForageAuditMaintainingQuality=Forage audit-maintaining forage quality
NotebookSectionForageAuditSilageBags=Forage audit-silage bags
NotebookSectionForageAuditSurveyOfForages=Forage audit-survey of forages
NotebookSectionForageAuditTowerSilos=Forage audit-tower silos
NotebookSectionHealthTools=Health tools
NotebookSectionHeatstressCalculations=Heat stress-calculations
NotebookSectionHeatstressChart=Heat stress-chart
NotebookSectionHeatstressData=Heat stress-data
NotebookSectionHerdAnalysisGoals=Rumen health - herd analysis goals
NotebookSectionMilkSoldEvaluationCharts=Milk sold evaluation charts
NotebookSectionMilkSoldEvaluationEditPickup=Milk sold evaluation edit pickup
NotebookSectionMilkSoldEvaluationInputs=Milk sold evaluation inputs
NotebookSectionMilkSoldEvaluationOutputs=Milk sold evaluation outputs
NotebookSectionNutritionTools=Nutrition tools
NotebookSectionRoboticMilkEvaluationAnalysis=Robotic milking evaluation analysis
NotebookSectionRoboticMilkEvaluationCharts=Robotic milking evaluation charts
NotebookSectionRoboticMilkEvaluationInputs=Robotic milking evaluation inputs
NotebookSectionRoboticMilkEvaluationOutputs=Robotic milking evaluation outputs
NotebookSectionRoboticMilkEvaluationTrends=Robotic milking evaluation trends
NotebookSectionRumenHealthLanding=Rumen health
NotebookSectionVisit=Visit
NotebookTMRParticleHerdAnalysisPenInputs=Tmr particle score herd analysis-inputs
NotebookTMRParticleHerdAnalysisPenResults=Tmr particle score herd analysis-results
NotebookUrinePHEditGoals=Urine ph edit goals
NotebookUrinePHInputs=Urine ph inputs
NotebookUrinePHOutputs=Urine ph results
NotebookVisitSummary=Visit summary
NotebookWalkthroughReport=Walkthrough report
NotebookWalkthroughReportLanding=Walkthrough report
NotebookWalkthroughReportPen=Walkthrough report-pen analysis
Nova_Scotia=Nova Scotia
Novara=Novara
Nuevo_León=Nuevo León
Null-values-not-allowed=Null values not allowed
NumChewsGoal={0} goal
NumOfCows=Number of cows
NumberOfChewsReportsViewModel.Average=Average \# of chews
NumberOfChewsReportsViewModel.AverageChews=Average chews
NumberOfChewsReportsViewModel.AverageNumberChews=Average \# of chews
NumberOfChewsReportsViewModel.DateDescription=Date
NumberOfChewsReportsViewModel.DatesComparison=Dates for comparison
NumberOfChewsReportsViewModel.EditVisits=Select
NumberOfChewsReportsViewModel.StdDevCalculated=Std. deviation (calculated)
NumberOfChewsReportsViewModel.VisitDate=Date
NumberOfChewsViewModel.Count=Count
NumberOfChewsViewModel.CountHeader=Please count the number of chews for this cow.
NumberOfChewsViewModel.NextCow=Next cow
NumberOfChewsViewModel.NumberOfChewsCow=Number of chews / cow \#
NumberOfChewsViewModel.Title=Number of chews / cow \# {0}
NumberOfChewsViewModel.ValidCudInput=Please enter a valid input.
NumberOfCows=Number of cows
Nunavut=Nunavut
Nuoro=Nuoro
NutritionViewModel.NutritionForage=Forage audit
NutritionViewModel.NutritionLabel=Please select a tool from the list below to begin your visit.
NutritionViewModel.NutritionPile=Forage inventories
NutritionViewModel.NutritionTools=Nutrition tools
NutritionViewModel.NutritionToolsCaption=Tools
NutritionViewModel.NutritionToolsInstructions=Please select a tool from the list below to begin your visit
NutritionViewModel.NutritionToolsList=Tools
NutritionViewModel.VisitNotebook=Visit notebook
OKLabel=OK
Oaxaca=Oaxaca
Observation=Observation
Odisha=Odisha
Offaly=Offaly
Ogliastra=Ogliastra
Ohio=Ohio
Oklahoma=Oklahoma
Olbia-Tempio=Olbia-Tempio
Oman=Oman
Once=the diet is created, select the class / subclass of animal associated with the Diet.
OncePerWeek=Once per week
One=One
OneHourBeforeActionIsDue=One hour before action is due
OneToSixHours=1 to 6 hours
Ontario=Ontario
Opportunities=Opportunities
Optimal=Optimal answer
OptimizationSelectionViewModel.SelectDietOptimizationType=Select maxdiet optimization type
OptimizationSelectionViewModel.Title=Diet optimization
Oregon=Oregon
Oristano=Oristano
Other=Other
OtherSilage=Other silage
Outputs=Outputs
Overall=Overall
OverallCalfHeiferDetails=To view the overall calf and heifer score, first complete at least one of the surveys in the list above.
OverallForageScoreDetails=To view the overall forage score, first complete at least one of the surveys in the list above.
PDFDisclaimer=Cargill incorporated, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. ©{0} cargill, incorporated. All rights reserved
PDFDisclaimer_ProvimiUS=Provimi north america, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. ©{0} provimi north america. All rights reserved
PDFPageNumber=Page {0} of {1}
PEN=Peru (S/. PEN)
PHP=Philippines ($ PHP)
PLN=Poland (zł PLN)
PMRConcentrate=Pmr + concentrate
PON=Romania (lei PON)
Padua=Padua
Pakistan=Pakistan
Palermo=Palermo
Palestinian_Territory,_Occupied=Palestinian Territory, Occupied
Panama=Panama
Papua_New_Guinea=Papua New Guinea
Paraguay=Paraguay
Paraná=Paraná
Paraíba=Paraíba
Parlor=Parlor
Parma=Parma
ParticleScorePreviousVisitsViewModel.MidOne=Mid 1
ParticleScorePreviousVisitsViewModel.MidOneValue=(8mm)
ParticleScorePreviousVisitsViewModel.MidTwo=Mid 2
ParticleScorePreviousVisitsViewModel.PercentageOnScreen=Percent on screen (%)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid1=Mid 1 (8mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRMid2=Mid 2 (4mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTop=Top (19mm)
ParticleScorePreviousVisitsViewModel.RumenHealthTMRTray=Tray
ParticleScorePreviousVisitsViewModel.SelectDates=Select dates
ParticleScorePreviousVisitsViewModel.Top=Top
ParticleScorePreviousVisitsViewModel.TopValue=(19mm)
ParticleScorePreviousVisitsViewModel.Tray=Tray
Pará=Pará
PasteurizedWholeMilk=Pasteurized whole milk fed
Pasto=Pasture
Pasture=Pasture
PastureOther=Pasture + other
Pavia=Pavia
Pays=Pays
PeakMilk=Peak milk
Pen=setup data will be updated automatically for sites with farm data downloads in dairy enteligen. 
PenDetailViewModel.Title=Pen detail
PenListViewModel.DietNotMappedInfo=Pens does not associate with any maxdiet, please map the maxdiet and proceed
PenListViewModel.DietSetup=Diet setup
PenListViewModel.MainHeading=PENS
PenListViewModel.NewPen=Add a new pen
PenListViewModel.Title=Pens
PenName=Pen Name
PenTimeBudgetComparisonViewModel.BodyConditionScoreChange=Body condition score change (per 100 days)
PenTimeBudgetComparisonViewModel.BodyWeightChange=Body weight change ({0})
PenTimeBudgetComparisonViewModel.CowsInPen=Cows in pen
PenTimeBudgetComparisonViewModel.CowsMilkedPerHour=Cows milked per hour
PenTimeBudgetComparisonViewModel.Current=Current
PenTimeBudgetComparisonViewModel.EnergyChange=Energy change (mcals)
PenTimeBudgetComparisonViewModel.Overcrowding=Overcrowding (%)
PenTimeBudgetComparisonViewModel.ParlorTurnsPerHour=Parlor turns per hour
PenTimeBudgetComparisonViewModel.PotentialMilkLossGain=Potential milk loss / gain ({0})
PenTimeBudgetComparisonViewModel.RestingDifference=Resting difference (hours)
PenTimeBudgetComparisonViewModel.TableTitle=Comparison
PenTimeBudgetComparisonViewModel.TimePerMilking=Time per milking (hours)
PenTimeBudgetComparisonViewModel.TimeRemainingForResting=Time remaining for resting (hours)
PenTimeBudgetComparisonViewModel.TimeRequiresForResting=Time required for resting (hours)
PenTimeBudgetComparisonViewModel.TotalNonRestingTime=Total non-resting time (hours)
PenTimeBudgetComparisonViewModel.TotalTimeMilking=Total time milking (hours)
PenTimeBudgetComparisonViewModel.WalkingToFindStall=Walking to find stall (hours)
PenTimeBudgetPenMasterViewModel.Compare=Compare
PenTimeBudgetPenMasterViewModel.Inputs=Inputs
PenTimeBudgetPenMasterViewModel.Results=Results
PenTimeBudgetPenMasterViewModel.Title=Pen time budget
PenTimeBudgetResultsViewModel.Hours=Hours
PenTimeBudgetResultsViewModel.MilkDifference=Potential Milk Difference
PenTimeBudgetResultsViewModel.MilkLossKg=kg
PenTimeBudgetResultsViewModel.MilkLossPounds=lbs
PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle=Potential milk loss/gain
PenTimeBudgetResultsViewModel.PenTimeBudgetTitle=Time available for resting
PenTimeBudgetResultsViewModel.TimeRemaining=Time remaining
PenTimeBudgetResultsViewModel.TimeRequired=Time required
PenTimeBudgetResultsViewModel.Title=Pen time budget results
PenTimeInputsViewModel.CowsPen=Cows in pen
PenTimeInputsViewModel.Drinking=Drinking / grooming time(hours)
PenTimeInputsViewModel.Eating=Eating time(hours)
PenTimeInputsViewModel.Frequency=Milking frequency(per day)
PenTimeInputsViewModel.LockUp=Time in lock-up(hours)
PenTimeInputsViewModel.NonRestTime=Other non-rest time(hours)
PenTimeInputsViewModel.ParlorTime=Time in parlor(hours)
PenTimeInputsViewModel.PenTimeTitle=Pen time budget
PenTimeInputsViewModel.Resting=Resting requirement(hours)
PenTimeInputsViewModel.StallsPen=Stalls in pen
PenTimeInputsViewModel.TotalStalls=Total stalls in parlor
PenTimeInputsViewModel.WalkingTimeFrom=Walking time from parlor(hours)
PenTimeInputsViewModel.WalkingTimeTo=Walking time to parlor(hours)
PenTimePenSelectionViewModel.NoLactatingPen=To access this tool, please ensure you have at least one pen with a lactating maxdiet.
PenTimePenSelectionViewModel.PenTimeBudgetTitle=Pen time budget
PenTimePenSelectionViewModel.PenTimeSection=PENS
PenTimePenSelectionViewModel.Title=Pen time budget
Pendetails=Pen details
PennStateShakerBoxForageResults=Penn state shaker box forage results
Pennsylvania=Pennsylvania
Pens=Pens
PerceivedHeatStressDietInfoMessage=Moderate heat stress\: panting, drool or foam but no open mouth, respiration rate 40 to 120 bpm
PercentLossPerCow=% loss / cow
PercentOnScreenTitle=Percent on screen (%)
PercentPen=Percent of pen (%)
PercentageOnScreen=Percent on screen (%)
PercentageOnScreenCurrentVisit=Percent on screen (%) - current visit
PercentageOnScreenTrend=Percent on screen (%) - trend
Pernambuco=Pernambuco
Peru=Peru
Perugia=Perugia
Pesaro_and_Urbino=Pesaro and Urbino
Pescara=Pescara
PhaseFive=Puberty
PhaseFour=Grower
PhaseOne=Colostrum
PhaseSeven=Close up / production
PhaseSix=Pregnancy
PhaseTwoThree=Pre/post weaning
Philippines=Philippines
PhotoExamples=Photo examples
Piacenza=Piacenza
Piauí=Piauí
Pickup=Pickup {0}
Pile=Pile
PileAndBunkerCapacitiesDensity=Forage inventory capacities density reference guide
PileAndBunkerCapacity=Pile and bunker capacity
PileAndBunkerCapacityViewModel.AddBag=Add bag
PileAndBunkerCapacityViewModel.AddBunker=Add bunker
PileAndBunkerCapacityViewModel.AddPile=Add pile
PileAndBunkerCapacityViewModel.Bag=Bag
PileAndBunkerCapacityViewModel.Bags=Bags
PileAndBunkerCapacityViewModel.BottomUnloadingSilo=Bottom unloading silo
PileAndBunkerCapacityViewModel.Bunker=Bunker
PileAndBunkerCapacityViewModel.Bunkers=Bunkers
PileAndBunkerCapacityViewModel.NameNotUnique=A pile or bunker named "{0}" already exists. Names must be unique.
PileAndBunkerCapacityViewModel.NameTooLong=A pile or bunker name must be 40 characters or less.
PileAndBunkerCapacityViewModel.Pile=Pile
PileAndBunkerCapacityViewModel.PileBunkerCapacities=Forage inventories
PileAndBunkerCapacityViewModel.Piles=Piles
PileAndBunkerCapacityViewModel.Resources=Resources
PileAndBunkerCapacityViewModel.Title=Forage inventory
PileAndBunkerCapacityViewModel.TopUnloadingSilo=Top unloading silo
PileAndBunkerCapacityViewModel.VisitNotebook=Visit notebook
PileAndBunkerName=Forage inventory name
PileAndBunkerResultsBagCapacityInputViewModel.BagLabel=Silage af density {0} (goal\: > {1})
PileAndBunkerResultsBagCapacityInputViewModel.CapacityBag=Capacity
PileAndBunkerResultsBagCapacityInputViewModel.DiameterBag=Diameter ({0})
PileAndBunkerResultsBagCapacityInputViewModel.DryMatterPercentageBag=Dry matter %
PileAndBunkerResultsBagCapacityInputViewModel.LenghtBag=Length ({0})
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsAFBag=Metric tons af
PileAndBunkerResultsBagCapacityInputViewModel.MetricTonsDMBag=Metric tons dm
PileAndBunkerResultsBagCapacityInputViewModel.SilageDMDensityBag=Silage dm density ({0})
PileAndBunkerResultsBagCapacityInputViewModel.TonsAFBag=Tons af
PileAndBunkerResultsBagCapacityInputViewModel.TonsDMBag=Tons dm
PileAndBunkerResultsCapacityInputViewModel.BottomLength=Bottom length ({0})
PileAndBunkerResultsCapacityInputViewModel.BottomWidth=Bottom width ({0})
PileAndBunkerResultsCapacityInputViewModel.Capacity=Capacity
PileAndBunkerResultsCapacityInputViewModel.DryMatterPercentage=Dry matter %
PileAndBunkerResultsCapacityInputViewModel.Height=Height ({0})
PileAndBunkerResultsCapacityInputViewModel.MetricTonsAF=Metric tons af
PileAndBunkerResultsCapacityInputViewModel.MetricTonsDM=Metric tons dm
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInFeet=Bottom length (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomLengthInMeters=Bottom length (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInFeet=Bottom width (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelBottomWidthInMeters=Bottom width (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInFeet=Height (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelHeightInMeters=Height (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInFeet=Top length (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopLengthInMeters=Top length (m.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInFeet=Top width (f.)
PileAndBunkerResultsCapacityInputViewModel.PileAndBunkerLabelTopWidthInMeters=Top width (m.)
PileAndBunkerResultsCapacityInputViewModel.SilageDMDensity=Silage dm density ({0})
PileAndBunkerResultsCapacityInputViewModel.SlopeMessage=Slope {0} to 1.0
PileAndBunkerResultsCapacityInputViewModel.SlopeMessagePile=Slope {0} to 1.0 (goal > 3.5 To 1.0)
PileAndBunkerResultsCapacityInputViewModel.Title=Capacity
PileAndBunkerResultsCapacityInputViewModel.TitleLabel=Silage af density {0} (goal\: > {1})
PileAndBunkerResultsCapacityInputViewModel.TonsAF=Tons af
PileAndBunkerResultsCapacityInputViewModel.TonsDM=Tons dm
PileAndBunkerResultsCapacityInputViewModel.TopLength=Top length ({0})
PileAndBunkerResultsCapacityInputViewModel.TopWidth=Top width ({0})
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayImperial=At 6 in. per day
PileAndBunkerResultsFeedOutViewModel.AtSixLengthPerDayMetric=At 15 cm. per day
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayImperial=At 3 in. per day
PileAndBunkerResultsFeedOutViewModel.AtThreeLengthPerDayMetric=At 7 cm. per day
PileAndBunkerResultsFeedOutViewModel.CowsPerDayNeeded=Cows/day needed
PileAndBunkerResultsFeedOutViewModel.CowsToBeFed=Cows to be fed
PileAndBunkerResultsFeedOutViewModel.DateGone=Date gone
PileAndBunkerResultsFeedOutViewModel.Days=Days
PileAndBunkerResultsFeedOutViewModel.FeedOutRateInfo=Feed out rate information
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaImperial=Feed out surface area (ft^2)
PileAndBunkerResultsFeedOutViewModel.FeedOutSurfaceAreaMetric=Feed out surface area (m^2)
PileAndBunkerResultsFeedOutViewModel.FeedingRate=Feeding rate (as-fed / cow)
PileAndBunkerResultsFeedOutViewModel.LengthPerDayImperial=In. per day
PileAndBunkerResultsFeedOutViewModel.LengthPerDayMetric=Cm. per day
PileAndBunkerResultsFeedOutViewModel.StartDate=Start date
PileAndBunkerResultsFeedOutViewModel.Title=Feed out
PileAndBunkerResultsFeedOutViewModel.TonsPerDay=Tons per day
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthImperial=Lbs. dm in 1 foot
PileAndBunkerResultsFeedOutViewModel.WeightDMInLengthMetric=Kgs. dm in 1 meter
PileAndBunkerResultsFeedOutViewModel.ZeroDecimalHint=0.0
PileAndBunkerResultsMasterViewModel.PileAndBunkerCapacityTab=Capacity
PileAndBunkerResultsMasterViewModel.PileAndBunkerFeedOutTab=Feed out
PileAndBunkerResultsMasterViewModel.VisitNotebook=Visit notebook
PileAndBunkerResultsSiloCapacityInputViewModel.CapacitySilo=Capacity
PileAndBunkerResultsSiloCapacityInputViewModel.Diameter=Diameter ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.DryMatterPercentageSilo=Dry matter %
PileAndBunkerResultsSiloCapacityInputViewModel.FilledHeight=Filled height ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsAFSilo=Metric tons af (remaining in silo)
PileAndBunkerResultsSiloCapacityInputViewModel.MetricTonsDMSilo=Metric tons dm (remaining in silo)
PileAndBunkerResultsSiloCapacityInputViewModel.SilageDMDensitySilo=Silage dm density ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.SilageLeft=Height of silage left in silo ({0})
PileAndBunkerResultsSiloCapacityInputViewModel.TonsAFSilo=Tons af (remaining in silo)
PileAndBunkerResultsSiloCapacityInputViewModel.TonsDMSilo=Tons dm (remaining in silo)
PileAndBunkerTitle=Forage inventory
PileBunkerCapacities=Forage inventories
PileCapacity=Pile capacity
PileFeedOutRate=Pile feed out
Piles=Piles
Pisa=Pisa
Pistoia=Pistoia
Pitcairn=Pitcairn
PlBacteriaCell=Bacteria cell count (1,000 cfu/ml)
PlMilkFat=Milk fat
PlMilkProtein=Milk protein
PlSomaticCell=Somatic cell count (1,000 cells/ml)
Please=keep the app open while sync is in progress. Syncing may take longer on slower connections.
Poland=Poland
Poor=Poor
Pordenone=Pordenone
Porosity=Porosity
Portugal=Portugal
Postweaned=Post-weaned
Postweaned_CleanAndDryPen=Clean and dry pen
Postweaned_CleanAndDryPen_ToolTip=Use wet knee test to determine if area is clean and dry
Postweaned_EvidenceOfAcidosisInManure=Evidence of acidosis in manure
Postweaned_EvidenceOfAcidosisInManure_ToolTip=Are there bubbles in loose manure
Postweaned_EvidenceOfScoursOrPneumonia=Evidence of scours or pneumonia
Postweaned_EvidenceOfScoursOrPneumonia_ToolTip=&lt;20% of calves get scours or pneumonia
Postweaned_FeedBunkIsClaanedDaily=Feed bunk is cleaned daily, refusals removed
Postweaned_ForageAvailability=Forage availability
Postweaned_ForageAvailability_ToolTip=Stems are greater than 5cm
Postweaned_FreeChoiceCleanWaterIsAvailable=Free choice, clean water is available
Postweaned_FreeChoiceCleanWaterIsAvailable_ToolTip=No evidence of contamination in water
Postweaned_FreshQualityStarterAvailable=Fresh, quality starter/grower is available
Postweaned_FreshQualityStarterAvailable_ToolTip=The starter/grower has no fines, no mold, and is not wet
Postweaned_SizeOfBunkSpace=Size of bunk space adequate per calf
Postweaned_SizeOfBunkSpace_ToolTip=&gt;45cm per calf
Postweaned_SizeOfPenAdequate=Size of pen is adequate per heifer
Postweaned_SizeOfPenAdequate_ToolTip=Individual pen\: 32ft2 / 3m2, group pen\: 28ft2 / 2.75m2
Postweaned_WellVentilatedPenWithNoDraftOnCalf=Well ventilated pen with no draft on calf
Postweaned_WellVentilatedPenWithNoDraftOnCalf_ToolTip=If clothes smell of ammonia after leaving barn, it is too high
PotentialDownResponse=Potential let down response ({0}/cow/day)
PotentialSCC=Potential scc (cells/{0})
Potenza=Potenza
Prato=Prato
PreWeaned_CMRisProperlyMixed_ToolTip=Amount of cmr\: &gt;600g &lt;800g, temp\: 39-41c, solids 12-18%
PreWeaned_CleanAndDryPen_ToolTip=Use wet knee test to determine if area is clean and dry
PreWeaned_EvidenceOfSource_ToolTip=&lt;20% of calves get scours or pneumonia
PreWeaned_Forageavailability_ToolTip=If texture feed, no forage is needed
PreWeaned_FreeChoiceCleanWater_ToolTip=Available form first day, no evidence of contamination in water
PreWeaned_FreeChoiceFreshCalf_ToolTip=The starter has no fines, no mold, and is not wet
PreWeaned_SizeOfPen_ToolTip=Individual pen\: 32ft2 / 3m2, group pen\: 25ft2 / 2.5m2
PreWeaned_WellVenilated_ToolTip=If clothes smell of ammonia after leaving barn, it is too high
PrematureKelvingsKeyInfoMessage=The delivery of one or more alive calves at least 10 days before due delivery date.
PreventingStorageLosses=Preventing storage losses
Previous=Previous
Preweaned=Pre-weaned
Preweaned_CMRIsProperlyMixedAndAdequatelyFed=Cmr is properly mixed and adequately fed
Preweaned_CleanAndDryPen=Clean and dry pen
Preweaned_CleanAndSanitizeCalfFeedingEquipment=Properly clean and sanitize calf feeding equipment between feedings
Preweaned_ConsistentFeedingTimesAndProtocols=Consistent feeding times and protocols
Preweaned_EvidenceOfScoursOrPneumonia=Evidence of scours or pneumonia
Preweaned_ForageAvailability=Forage availability
Preweaned_FreeChoiceCleanWaterIsAvailable=Free choice, clean water is available
Preweaned_FreeChoiceFreshCalfStarterIsAvailable=Free choice, fresh calf starter is available
Preweaned_SizeOfPenAadequatePerHeifer=Size of pen is adequate per heifer
Preweaned_WeaningAtIntakeOfOnekgStarterPerDay=Weaning at intake of 1kg starter per day
Preweaned_WellVentilatedPenWithNoDraftOnCalf=Well ventilated pen with no draft on calf
PricingMatrixEditViewModel.Cancel=Cancel
PricingMatrixEditViewModel.Save=Save
PricingMatrixEditViewModel.Title=Matrix item detail
PricingMatrixPickListViewModel.PricingMatrix=Pricing matrix
PricingMatrixViewModel.Amount=Amount (1000 cells/ml)
PricingMatrixViewModel.AmountCFU=Amount (1000 cfu/ml)
PricingMatrixViewModel.New=New
PricingMatrixViewModel.Title=Edit matrix
Prince_Edward_Island=Prince Edward Island
Privacy_Statement=Privacy statement
ProcessorCurrencyPickListViewModel.CurrenciesLabel=Currencies
ProcessorCurrencyPickListViewModel.Title=Currencies
ProductivityToolsViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
ProductivityToolsViewModel.MilkRevenueAnalysis=Milk revenue analysis
ProductivityToolsViewModel.MilkSoldEvaluation=Milk sold evaluation
ProductivityToolsViewModel.ProductivityTitle=Productivity tools
ProductivityToolsViewModel.ProductivityTools=Tools
ProductivityToolsViewModel.RoboticMilkingEvaluation=Robotic milking evaluation
ProductivityToolsViewModel.VisitNotebook=Visit notebook
Profitability.Analysis.Milk.Price.Chart.Title=Milk Price vs Feeding Cost
ProfitabilityAnalysis.Feeding.Cost.Per.Litre.Of.Milk=Feeding Cost Per Liter Of Milk
ProfitabilityAnalysis.Iofc=IOFC
ProfitabilityAnalysis.Milk.Price=Milk Price($)
ProfitabilityAnalysis.Production.In.150.Dim=Production In 150 DIM(Cow)
ProfitabilityAnalysis.Production.In.150.Dim.Chart.Title=Production In 150 DIM vs IOFC
ProfitabilityAnalysis.Revenue.Per.Cow.Per.Day=Revenue Per Cow Per Day
ProfitabilityAnalysis.Total.Diet.Cost=Total Diet Cost($/Cow/Day)
ProfitabilityAnalysis.TotalProduction=Total Production (cow/day)
ProfitabilityAnalysis.TotalProduction.Concentrated=Total Production / Concentrate Total Consumed
ProfitablityAnalysis.Date=Date
ProftabilityAnalysis.TotalProduction.Chart.Title=Total Production vs Concentrate Consumed
PromptCancel=Cancel
PromptOK=Ok
PromptPermissionMsg=Without this permission some feature may not work. Are you sure you want to deny this permission?
PromptPermissionMsgIMSure=I&apos;m sure
PromptPermissionMsgRetry=Re-Try
PromptPermissionTitle=Permission denied
ProspectProfileViewModel.DeleteProspect=Delete prospect
ProspectProfileViewModel.DeleteProspectPrompt=Are you sure you want to delete this prospect? Prospect, site and in progress visit information will be lost.
ProspectProfileViewModel.MainHeading=SITES
ProspectProfileViewModel.NewSite=Add a new site
ProspectProfileViewModel.ProspectInfo=Prospect info
ProspectProfileViewModel.ProspectTitle=Prospect details
ProspectsViewModel.NewProspect=Add a new prospect
ProtabilityAnalysis.Revenue.Cow.Per.Day.Chart.Title=Revenue Per Cow Per Day vs Total Diet Cost
Provimi=Provimi
ProvimiUS=Provimi us
PublishVisit=Published
Puducherry=Puducherry
Puebla=Puebla
Puerto_Rico=Puerto Rico
Punjab=Punjab
Purina=Purina
Qatar=Qatar
Qinghai=Qinghai
QuarterPointScale=Quarter point scale
Quarterly=Quarterly
Quebec=Quebec
Queensland=Queensland
Querétaro=QuerÃétaro
QuestionTableTitle=Question {0} of {1}
QuestionViewModel.Baleage=Baleage
QuestionViewModel.BunkersAndPiles=Bunkers and piles
QuestionViewModel.Close=Close
QuestionViewModel.Harvest=Forage quality in the ration
QuestionViewModel.MaintainingForageQuality=Maintaining forage quality
QuestionViewModel.SilageBags=Silage bags
QuestionViewModel.SurveyOfForages=Forage management
QuestionViewModel.TowerSilos=Tower silos
QuestionViewModel.VisitNotebook=Visit notebook
Quintana_Roo=Quintana Roo
ROL=ROL
RUB=Russia (₽‎ RUB)
Ragusa=Ragusa
Rajasthan=Rajasthan
Rationcost=Ration cost ({0})
Ravenna=Ravenna
ReadyToMilkChartViewModel.Current=Current
ReadyToMilkChartViewModel.DeathLoss=Mastitis
ReadyToMilkChartViewModel.DisorderGraphTitle=Annual metabolic disorder cost/cow
ReadyToMilkChartViewModel.DisplacedAbomasum=Displaced abomasum
ReadyToMilkChartViewModel.Dystocia=Dystocia
ReadyToMilkChartViewModel.Ketosis=Ketosis
ReadyToMilkChartViewModel.Metritis=Metritis
ReadyToMilkChartViewModel.MilkFever=Milk fever
ReadyToMilkChartViewModel.RetainedPlacenta=Retained placenta
ReadyToMilkChartViewModel.Title=Ready2Milk&\#8482; charts
ReadyToMilkIndexViewModel.LabelReadyToMilkIndex=Ready2Milk&\#8482; index
ReadyToMilkInputViewModel.Back=Back
ReadyToMilkInputViewModel.BcsVariationDryOffDiet=Bcs change dry off to 21 dim
ReadyToMilkInputViewModel.CloseUp=Close up cows
ReadyToMilkInputViewModel.ComfortCloseUp=Comfort close-up
ReadyToMilkInputViewModel.ComfortDiet=Comfort 0-21 dim
ReadyToMilkInputViewModel.CostExtraDaysOpen=Cost of extra days open
ReadyToMilkInputViewModel.CudChewingDiet=Cud chewing fresh cows
ReadyToMilkInputViewModel.DeadCowsOrCulled=Health-related death or culling
ReadyToMilkInputViewModel.DisplacedAbomasum=Displaced abomasum
ReadyToMilkInputViewModel.Dystocia=Dystocia
ReadyToMilkInputViewModel.FreshCows=Fresh cows
ReadyToMilkInputViewModel.Health=HEALTH
ReadyToMilkInputViewModel.HealthDesc=Enter the number of fresh cows and the number of metabolic incidence cases during the evaluation period. This will be converted to an annualized incidence cost on the outputs tab.
ReadyToMilkInputViewModel.HealthRecords=Health records
ReadyToMilkInputViewModel.Herd=Herd level information
ReadyToMilkInputViewModel.Ketosis=Ketosis
ReadyToMilkInputViewModel.LocomotionScore=Locomotion score
ReadyToMilkInputViewModel.Mastitis=Mastitis 0-21 dim
ReadyToMilkInputViewModel.Metritis=Metritis
ReadyToMilkInputViewModel.MilkFever=Milk fever
ReadyToMilkInputViewModel.MilkPrice=Milk price
ReadyToMilkInputViewModel.MilkYield=Milk yield 15-60 dim
ReadyToMilkInputViewModel.Next=Next
ReadyToMilkInputViewModel.PerceivedHeatStressDiet=Perceived heat stress 0-21 dim
ReadyToMilkInputViewModel.PercievedHeatStressCloseUp=Perceived heat stress close-up
ReadyToMilkInputViewModel.PrematureCalvings=Premature calvings
ReadyToMilkInputViewModel.ReplacementCowCost=Replacement cow cost
ReadyToMilkInputViewModel.RetainedPlacenta=Retained placenta
ReadyToMilkInputViewModel.RumenFill=Rumen fill
ReadyToMilkInputViewModel.SccFirstTest=Scc 1st test (x1000 scc/ml)
ReadyToMilkInputViewModel.SpecificCloseUpDiet=Specific close-up diet
ReadyToMilkInputViewModel.SpecificDiet=Specific 0-21 dim diet
ReadyToMilkInputViewModel.Title=Ready2Milk&\#8482; inputs
ReadyToMilkInputViewModel.TotalFreshCowsPerYear=Total fresh cows/year
ReadyToMilkInputViewModel.TotalFreshCowsforEvalution=Total fresh cows for evaluation
ReadyToMilkListViewModel.Title=Ready2Milk&\#8482; index
ReadyToMilkMasterViewModel.Charts=Charts
ReadyToMilkMasterViewModel.Inputs=Inputs
ReadyToMilkMasterViewModel.MastitisNotPresent=Mastitis is not filled.
ReadyToMilkMasterViewModel.Outputs=Outputs
ReadyToMilkMasterViewModel.Title=Ready2Milk&\#8482; index
ReadyToMilkOutputViewModel.LabelReadyToMilkIndex=Ready2Milk&\#8482; index
ReadyToMilkOutputViewModel.Title=Ready2Milk&\#8482; outputs
RecommendedTLCSettings=Recommended tlc settings
RefreshTokenFailed=Session expired, you are required to re-login.
Reggio_Calabria=Reggio Calabria
Reggio_Emilia=Reggio Emilia
RemovedAndMeasured=Removed and measured
RemovedOnly=Removed only
Report=Report
Report.Analysis.Type=Analysis type
Report.Animal.Analysis=Animal analysis
Report.Animal.Tag.Name=Animal id
Report.AvgRumenFillScore=Avg. Rumen Fill Score (Calculated)
Report.BCS.EvalDataTitle=Calculated bcs evaluation data
Report.BCS.LactationStages=Lactation stages
Report.BCS.Max=Bcs max
Report.BCS.MilkHeadDay=Milk/hd/day
Report.BCSAvg=Bcs average
Report.Bcs=BCS
Report.Bcs.ChartName=Body condition score - animal {0}
Report.Bcs.HerdAnalysis.ChartName=BCS vs milk
Report.Bcs.Milk=Milk
Report.Bcs.Min=Bcs min
Report.Calving.Date=Calving
Report.Cargill.Report=Cargill - report
Report.Chewing=Chewing
Report.Chews=Chews
Report.CudChewing.EvalDataTitle=Calculated cud chewing evaluation data
Report.CudChewingPercentage=Cud chewing %
Report.CudChewingPercentage.Vs.LactStages=Cud chewing %
Report.EvalDataTitle=Calculated evaluation data
Report.ForagePennState=Forage Penn State
Report.General.Comments=General comments
Report.GoalCudChewingPercentage=Goal cud chewing %
Report.Heatstress.Dmi.Adjustment=Dmi adjustment
Report.Heatstress.Energy.Equivalent.Milk.Loss=Energy equivalent milk loss ({0})
Report.Heatstress.Estimated.Dry.Matter.Intake=Estimated dry matter intake ({0})
Report.Heatstress.Intake.Adjustment=Intake adjustment
Report.Heatstress.Legend=Legend
Report.Heatstress.Legends=Legends
Report.Heatstress.Loss.Of.Energy.Consumed=Loss of energy consumed (mcal)
Report.Heatstress.Mild.Moderate.Stress=Mild - moderate stress
Report.Heatstress.Mild.Moderate.Stress.Message=Respiration exceeds 75 bpm | rectal temperature exceeds 39\\u2103 (102.2\\u2109)
Report.Heatstress.Milk.Value.Loss.PerMonth=Milk value loss (per month) ({0})
Report.Heatstress.Milk.Value.Loss.Perday=Milk value loss (per day) ({0})
Report.Heatstress.Moderate.Severe.Stress=Moderate - severe stress
Report.Heatstress.Moderate.Severe.Stress.Message=Respiration exceeds 85 bpm | rectal temperature exceeds 40\\u2103 (104\\u2109)
Report.Heatstress.Reduction.In.Dmi=Reduction in dmi ({0})
Report.Heatstress.Relative.Humidity=Relative humidity (%)
Report.Heatstress.Severe.Stress=Severe stress
Report.Heatstress.Severe.Stress.Message=Respiration exceeds 120-140 bpm | rectal temperature exceeds 41\\u2103 (106\\u2109)
Report.Heatstress.Stress.Threshold=Stress threshold
Report.Heatstress.Stress.Threshold.Message=Respiration exceeds 60 bpm | repro losses detectable | rectal temperature exceeds 38.5\\u2103 (101.3\\u2109)
Report.Heatstress.Temperature=Temperature
Report.Heatstress.Temperature.In.Celcius=Temperature \\u2103
Report.Heatstress.Temperature.In.Farenhiet=Temperature \\u2109
Report.Heatstress.TemperatureHumidityIndex=Temperature humidity index
Report.Herd.Analysis.CudChewingPercentage=Cud chewing %
Report.Locomotion.HerdAnalysis.ChartName=Locomotion score percentages
Report.LocomotionScore.X.Axis=Locomotion score
Report.LocomotionScore.Y.Axis=Percent %
Report.LocomotionScore.chartName=Locomotion score - animal {0}
Report.No.OfChews=No. of chews
Report.No.OfChewsPerRegurgitation=No. of chews per regurgitation
Report.NoOfChews.Vs.LactStages=No. of chews
Report.Not.Chewing=Not chewing
Report.PenTimeBudget.TimeAvailableForResting.CategoryLabel=Time available for resting
Report.PenTimeBudget.TimeAvailableForResting.Label=Hours
Report.PenTimeBudgetTimeRemaining=Time remaining
Report.PenTimeBudgetTimeRequired=Time required
Report.Pentime.Budget.Hours=Hours
Report.PercentageOnScreen=On screen %
Report.RumenHealthManureScreening.Bottom=Bottom
Report.RumenHealthManureScreening.BottomGoalMax=Bottom goal max
Report.RumenHealthManureScreening.BottomGoalMin=Bottom goal min
Report.RumenHealthManureScreening.Middle=Middle
Report.RumenHealthManureScreening.MiddleGoalMax=Middle goal max
Report.RumenHealthManureScreening.MiddleGoalMin=Middle goal min
Report.RumenHealthManureScreening.Top=Top
Report.RumenHealthManureScreening.TopGoalMax=Top goal max
Report.RumenHealthManureScreening.TopGoalMin=Top goal min
Report.SheetName=Report master
Report.Tool.Details=Tool details
Report.Tool.Name=Tool name
Report.Visit.Date=Visit date
Report.Visit.Report=Visit Report
Report.Visit.name=Visit name
Report.goalChews=Goal chews
Report.locomotionScore.Pen.Analysis.ChartName=Categories vs visit dates
ReportDate=Report date
ReportPDFNote=Note\:  
Reset=Database
Reset_Database">=
Resources=Resources
ResourcesViewModel.Title=Forage audit resources
ResourcesViewModel.VisitNotebook=Visit notebook
RetainedPlacenta=Retained placenta
Reunion=Reunion
Revenue=Revenue
RevenueEditComparisonValuesViewModel.CurrentSCC=Current scc (cells/{0})
RevenueEditComparisonValuesViewModel.DownResponse=Let down response ({0}/cow/day)
RevenueEditComparisonValuesViewModel.EditComparisonValues=Edit comparison values
RevenueEditComparisonValuesViewModel.MilkChange=Milk change (%)
RevenueEditComparisonValuesViewModel.MilkPrice=Milk price ($/{0}})
RevenueEditComparisonValuesViewModel.MilkProduction=Milk production
RevenueEditComparisonValuesViewModel.NumOfCows=Number of cows
RevenueEditComparisonValuesViewModel.PotentialSCC=Potential scc (cells/{0})
RevenueEditComparisonValuesViewModel.ScenarioOne=Scenario 1
RevenueEditComparisonValuesViewModel.ScenarioTwo=Scenario 2
RevenueEditComparisonValuesViewModel.Title=Edit comparison values
RevenueInputViewModel.ComparisonValues=Comparison values
RevenueInputViewModel.Edit=Edit
RevenueInputViewModel.ScenarioOne=Scenario 1
RevenueInputViewModel.ScenarioTwo=Scenario 2
RevenueInputViewModel.Title=Milking procedure comparison - inputs
RevenueLossDay=Revenue loss ({0}/day)
RevenueLossYear=Revenue loss ({0}/year)
Rheinland=Rheinland
Rhode_Island=Rhode Island
Rieti=Rieti
Rimini=Rimini
Rio_Grande_do_Norte=Rio Grande do Norte
Rio_Grande_do_Sul=Rio Grande do Sul
Rio_de_Janeiro=Rio de Janeiro
Robot=Robot
RoboticMilkEvaluationAnalysisViewModel.AverageBoxTime=Average box time (min/cow)
RoboticMilkEvaluationAnalysisViewModel.AverageConcentrate=Average concentrate
RoboticMilkEvaluationAnalysisViewModel.CowsPerRobot=Cows per robot
RoboticMilkEvaluationAnalysisViewModel.MilkingFailures=Milking failures (/robot)
RoboticMilkEvaluationAnalysisViewModel.MilkingRefusals=Milking refusals (/cow)
RoboticMilkEvaluationAnalysisViewModel.Milkings=Milkings (/cow)
RoboticMilkEvaluationAnalysisViewModel.RobotFreeTime=Robot free time %
RoboticMilkEvaluationAnalysisViewModel.RoboticMilkingEvaluation=Robotic milking evaluation
RoboticMilkEvaluationChartsViewModel.AMSUtilization=Robot free time and average box time per cow
RoboticMilkEvaluationChartsViewModel.AverageBoxTime=Average box time (min/cow)
RoboticMilkEvaluationChartsViewModel.AverageConcentrate=Average concentrate
RoboticMilkEvaluationChartsViewModel.AverageConcentrateFed=Average concentrate ({0}/cow)
RoboticMilkEvaluationChartsViewModel.ConcentrateDistribution=Average concentrate and concentrate per 100 kg milk
RoboticMilkEvaluationChartsViewModel.ConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.CowEfficiency=Milkings per cow and milking refusals per cow
RoboticMilkEvaluationChartsViewModel.CowsPerRobot=Cows per robot
RoboticMilkEvaluationChartsViewModel.DLAverageBoxTime=Avg. milk duration per milking (min/cow)
RoboticMilkEvaluationChartsViewModel.DLAverageConcentrateFed=Average concentrate consumed ({0}/cow)
RoboticMilkEvaluationChartsViewModel.DLConcentratePer100KGMilk=Feed/milk ratio- ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.DLMilkings=Avg. daily milkings per animal (/cow)
RoboticMilkEvaluationChartsViewModel.DLRobotFreeTime=Idle time %
RoboticMilkEvaluationChartsViewModel.DLTotalMilkingFailures=Nb incomplete (/herd)
RoboticMilkEvaluationChartsViewModel.GAverageBoxTime=Avg. staying time (min/cow)
RoboticMilkEvaluationChartsViewModel.GAverageConcentrateFed=Concentrate fed ({0}/cow)
RoboticMilkEvaluationChartsViewModel.GConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.GMilkings=Avg. nr milkings (/cow)
RoboticMilkEvaluationChartsViewModel.GTotalMilkingFailures=Number incomplete milkings (/herd)
RoboticMilkEvaluationChartsViewModel.LelyAverageBoxTime=Box time (min/cow)
RoboticMilkEvaluationChartsViewModel.LelyAverageConcentrateFed=Average concentrate fed ({0}/cow)
RoboticMilkEvaluationChartsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} milk- ({0}/100 {0} milk)
RoboticMilkEvaluationChartsViewModel.LelyMilkingRefusals=Refusals (/cow)
RoboticMilkEvaluationChartsViewModel.LelyMilkings=Milkings/cow/day (/cow)
RoboticMilkEvaluationChartsViewModel.LelyRobotFreeTime=Free time %
RoboticMilkEvaluationChartsViewModel.MilkingFailures=Milking failures
RoboticMilkEvaluationChartsViewModel.MilkingRefusals=Milking refusals (/cow)
RoboticMilkEvaluationChartsViewModel.Milkings=Milkings (/cow)
RoboticMilkEvaluationChartsViewModel.RobotFreeTime=Robot free time %
RoboticMilkEvaluationChartsViewModel.Title=Robotic milking evaluation
RoboticMilkEvaluationInputsViewModel.AverageBoxTime=Average box time (min/cow) ⃰
RoboticMilkEvaluationInputsViewModel.AverageConcentrateFed=Average concentrate fed ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.AverageMilkYield=Average milk yield ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.ConcentratePer100KGMilk=Concentrate per 100 {0} milk ({0}/100 {0} milk)
RoboticMilkEvaluationInputsViewModel.ConcentratePer100KGMilkMsg=System shows 1 kg/ milk so x 100
RoboticMilkEvaluationInputsViewModel.CowFlowDesign=Cow flow design ⃰
RoboticMilkEvaluationInputsViewModel.DLAverageBoxTime=Avg. milk duration per milking (min/cow) ⃰
RoboticMilkEvaluationInputsViewModel.DLAverageConcentrateFed=Average concentrate consumed ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.DLAverageMilkYield=Avg. milk yield ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.DLConcentratePer100KGMilk=Feed/milk ratio- ({0}/100 {0} milk) ⃰
RoboticMilkEvaluationInputsViewModel.DLMilkingSpeed=Av. daily harvesting flow per animal ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.DLMilkings=Avg. daily milkings per animal (/cow) ⃰
RoboticMilkEvaluationInputsViewModel.DLRestFeed=Concentrate not consumed % ⃰
RoboticMilkEvaluationInputsViewModel.DLRobotFreeTime=Idle time % ⃰
RoboticMilkEvaluationInputsViewModel.DLTotalMilkingFailures=Nb incomplete (/herd) ⃰
RoboticMilkEvaluationInputsViewModel.GAverageBoxTime=Avg. staying time (min/cow) ⃰
RoboticMilkEvaluationInputsViewModel.GAverageConcentrateFed=Concentrate fed ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.GAverageMilkYield=Avg. milk production ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.GConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk) ⃰
RoboticMilkEvaluationInputsViewModel.GMilkingSpeed=Avg. milk speed ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.GMilkings=Avg. nr milkings (/cow) ⃰
RoboticMilkEvaluationInputsViewModel.GRestFeed=Rest feed (%) ⃰
RoboticMilkEvaluationInputsViewModel.GTotalMilkingFailures=Number incomplete milkings (/herd) ⃰
RoboticMilkEvaluationInputsViewModel.Herd=Herd level information
RoboticMilkEvaluationInputsViewModel.LactatingCows=Lactating cows ⃰
RoboticMilkEvaluationInputsViewModel.LelyAverageBoxTime=Box time (min/cow) ⃰
RoboticMilkEvaluationInputsViewModel.LelyAverageConcentrateFed=Average concentrate fed ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.LelyAverageMilkYield=Milk/cow/day ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} milk- ({0}/100 {0} milk) ⃰
RoboticMilkEvaluationInputsViewModel.LelyMilkingRefusals=Refusals (/cow) ⃰
RoboticMilkEvaluationInputsViewModel.LelyMilkingSpeed=Milk speed ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.LelyMilkings=Milkings/cow/day (/cow) ⃰
RoboticMilkEvaluationInputsViewModel.LelyRobotFreeTime=Free time % ⃰
RoboticMilkEvaluationInputsViewModel.LelyTotalMilkingFailures=Failure (total/day) ⃰
RoboticMilkEvaluationInputsViewModel.MaximumConcentrate=Maximum concentrate ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.MilkingRefusals=Milking refusals (/cow) ⃰
RoboticMilkEvaluationInputsViewModel.MilkingRefusalsDLMsg=Static system; total refusal/ cow number
RoboticMilkEvaluationInputsViewModel.MilkingRefusalsGEMsg=Calculate refusals\: average nr of visit/cow - average nr milkings/cow??
RoboticMilkEvaluationInputsViewModel.MilkingSpeed=Milking speed ({0}/min) ⃰
RoboticMilkEvaluationInputsViewModel.Milkings=Milkings (/cow) ⃰
RoboticMilkEvaluationInputsViewModel.MinimumConcentrate=Minimum concentrate ({0}/cow) ⃰
RoboticMilkEvaluationInputsViewModel.RestFeed=Rest feed % ⃰
RoboticMilkEvaluationInputsViewModel.RestFeedDLMsg=Rest feed\: 100 - % cons. yest.
RoboticMilkEvaluationInputsViewModel.RestFeedMsg=Calculate rest feed (kg) / total fed
RoboticMilkEvaluationInputsViewModel.RobotFreeTime=Robot free time % ⃰
RoboticMilkEvaluationInputsViewModel.RobotFreeTimeMsg=Utilization time\: 100 - system utilization%
RoboticMilkEvaluationInputsViewModel.RobotType=Type of robot ⃰
RoboticMilkEvaluationInputsViewModel.RobotsInHerd=Robots in herd ⃰
RoboticMilkEvaluationInputsViewModel.TotalMilkingFailureMsg=Total milkings x % incomplete
RoboticMilkEvaluationInputsViewModel.TotalMilkingFailures=Total milking failures (/herd) ⃰
RoboticMilkEvaluationMasterViewModel.Analysis=Analysis
RoboticMilkEvaluationMasterViewModel.Inputs=Inputs
RoboticMilkEvaluationMasterViewModel.Outputs=Outputs
RoboticMilkEvaluationMasterViewModel.Title=Robotic milking evaluation
RoboticMilkEvaluationMasterViewModel.Trends=Trends
RoboticMilkEvaluationOutputsViewModel.AMSUtilization=Ams utilization
RoboticMilkEvaluationOutputsViewModel.AverageBoxTime=Average box time (min/cow)
RoboticMilkEvaluationOutputsViewModel.AverageConcentrate=Average concentrate ({0}/cow)
RoboticMilkEvaluationOutputsViewModel.ConcentrateDistribution=Concentrate distribution
RoboticMilkEvaluationOutputsViewModel.ConcentratePer100KGMilk=Concentrate per 100 {0} milk ({0}/100 {0} milk)
RoboticMilkEvaluationOutputsViewModel.CowEfficiency=Cow efficiency
RoboticMilkEvaluationOutputsViewModel.CowsPerRobot=Cows per robot
RoboticMilkEvaluationOutputsViewModel.DLAverageBoxTime=Avg. milk duration per milking (min/cow)
RoboticMilkEvaluationOutputsViewModel.DLConcentratePer100KGMilk=Feed/milk ratio - ({0}/100 {0} milk)
RoboticMilkEvaluationOutputsViewModel.DLMilkingSpeed=Av. daily harvesting flow per animal ({0}/min)
RoboticMilkEvaluationOutputsViewModel.DLRestFeed=Concentrate not cons %
RoboticMilkEvaluationOutputsViewModel.GAverageBoxTime=Avg. staying time (min/cow)
RoboticMilkEvaluationOutputsViewModel.GConcentratePer100KGMilk=Concen./100 {0} of milk ({0}/100 {0} milk)
RoboticMilkEvaluationOutputsViewModel.GMilkingSpeed=Avg. milk speed ({0}/min)
RoboticMilkEvaluationOutputsViewModel.LelyAverageBoxTime=Box time (min/cow)
RoboticMilkEvaluationOutputsViewModel.LelyConcentratePer100KGMilk=Conc./100 {0} milk- ({0}/100 {0} milk)
RoboticMilkEvaluationOutputsViewModel.LelyMilkingSpeed=Milk speed ({0}/min)
RoboticMilkEvaluationOutputsViewModel.MaximumConcentrate=Maximum concentrate ({0}/cow)
RoboticMilkEvaluationOutputsViewModel.MilkPerRobot=Milk per robot ({0})
RoboticMilkEvaluationOutputsViewModel.MilkingFailures=Milking failures (/robot)
RoboticMilkEvaluationOutputsViewModel.MilkingRefusals=Milking refusals (/cow)
RoboticMilkEvaluationOutputsViewModel.MilkingSpeed=Milking speed ({0}/min)
RoboticMilkEvaluationOutputsViewModel.Milkings=Milkings (/cow)
RoboticMilkEvaluationOutputsViewModel.MilkingsPerRobot=Milkings per robot
RoboticMilkEvaluationOutputsViewModel.MinimumConcentrate=Minimum concentrate ({0}/cow)
RoboticMilkEvaluationOutputsViewModel.RestFeed=Rest feed
RoboticMilkEvaluationOutputsViewModel.RobotFreeTime=Robot free time %
RoboticMilkEvaluationSpinnerViewModel.Title=Robotic milking evaluation
RoboticMilkEvaluationTrendsListViewModel.AMSUtilization=Ams utilization
RoboticMilkEvaluationTrendsListViewModel.ConcentrateDistribution=Concentrate distribution
RoboticMilkEvaluationTrendsListViewModel.CowEfficiency=Cow efficiency
RoboticMilkEvaluationTrendsListViewModel.VisitComparison=Please select visits for comparison
Romania=Romania
Rome=Rome
RondÃ´nia=RondÃ´nia
Roraima=Roraima
Roscommon=Roscommon
Rovigo=Rovigo
RumenHealthBodyConditionLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthBodyConditionLandingViewModel.PenAnalysis=Pen analysis
RumenHealthBodyConditionLandingViewModel.Pens=Pens
RumenHealthBodyConditionLandingViewModel.Resources=Resources
RumenHealthBodyConditionLandingViewModel.Title=Body condition score
RumenHealthEditManureScoresViewModel.Close=Close
RumenHealthEditManureScoresViewModel.Count=Count
RumenHealthEditManureScoresViewModel.EnterNumberOfCows=Please count the number of cows
RumenHealthEditManureScoresViewModel.NumOfCows=Number of cows
RumenHealthEditManureScoresViewModel.NumberOfCows=Number of cows
RumenHealthEditManureScoresViewModel.VisitNotebook=Visit notebook
RumenHealthLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthLandingViewModel.PenAnalysis=Pen analysis
RumenHealthLandingViewModel.Pens=Pens
RumenHealthLandingViewModel.Title=Rumen health cud chewing
RumenHealthLocomotionLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthLocomotionLandingViewModel.PenAnalysis=Pen analysis
RumenHealthLocomotionLandingViewModel.Pens=Pens
RumenHealthLocomotionLandingViewModel.Resources=Resources
RumenHealthLocomotionLandingViewModel.Title=Locomotion
RumenHealthManureLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthManureLandingViewModel.PenAnalysis=Pen analysis
RumenHealthManureLandingViewModel.Pens=Pens
RumenHealthManureLandingViewModel.Resources=Resources
RumenHealthManureLandingViewModel.Title=Rumen health manure score
RumenHealthManureMasterViewModel.Inputs=Inputs
RumenHealthManureMasterViewModel.Results=Results
RumenHealthManureMasterViewModel.RumenHealthManureScore=Rumen health manure score
RumenHealthManureMasterViewModel.RumenHealthManureTitle=Rumen health manure score
RumenHealthManureMasterViewModel.VisitNotebook=Visit notebook
RumenHealthManureScoresResultsViewModel.ManureScoreAverageTitle=Average score
RumenHealthManureScoresResultsViewModel.ManureScoreDatesTitle=Date
RumenHealthManureScoresResultsViewModel.PercentPen=Percent of pen (%)
RumenHealthManureScoresResultsViewModel.SelectedDates=Select dates
RumenHealthManureScoresResultsViewModel.Title=Manure score results
RumenHealthManureScoresViewModel.AnimalsObserved=Animals observed
RumenHealthManureScoresViewModel.AvgManureScoreCalculated=Avg. manure score (calculated)
RumenHealthManureScoresViewModel.Edit=Edit
RumenHealthManureScoresViewModel.ManureScore=Manure score
RumenHealthManureScoresViewModel.PercentOfPen=Percent of pen (%)
RumenHealthManureScoresViewModel.ScoreCategory=Manure score category
RumenHealthManureScoresViewModel.StdDevCalculated=Std. deviation (calculated)
RumenHealthPenCudCalculatorViewModel.CudCalculatorsSection=Cud calculators
RumenHealthPenCudCalculatorViewModel.CudChewing=Cud chewing
RumenHealthPenCudCalculatorViewModel.CudChewingSubTitle=Capture the number of cows chewing cud
RumenHealthPenCudCalculatorViewModel.NumberOfChews=Number of chews
RumenHealthPenCudCalculatorViewModel.NumberOfChewsSubTitle=Capture the number of chews per cow
RumenHealthTMRLandingViewModel.HerdAnalysis=Herd analysis
RumenHealthTMRLandingViewModel.PenAnalysis=Pen analysis
RumenHealthTMRLandingViewModel.Pens=Pens
RumenHealthTMRLandingViewModel.Title=Rumen health particle score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScaleAmountTitle=Enter scale amount (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.EnterScreenTareAmount=Enter screen tare amount (g)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMaxTitle=Goal - max (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.GoalMinTitle=Goal - min (%)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Goals=Goals
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid1Title=Middle 1(8 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenOldTitle=Middle 2 (1.18 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Mid24ScreenTitle=Middle 2 (4 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRParticleScoreName=Tmr particle score name
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TMRScoreName=Tmr particle score
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TareAmountTitle=Screen tare
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.Title=Enter scale amount
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TopTitle=Top (19 mm)
RumenHealthTMRParticleScorePenEditScaleAmountViewModel.TrayTitle=Tray
RumenHealthTMRParticleScorePenTableInputViewModel.AddTMRScore=Add tmr score
RumenHealthTMRParticleScorePenTableInputViewModel.AverageScoreTitle=Avg. tmr particle score
RumenHealthTMRParticleScorePenTableInputViewModel.Current=Current
RumenHealthTMRParticleScorePenTableInputViewModel.EnterScaleAmountTitle=Scale amount (g)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMaxTitle=Goal - max (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid1Title=Goal mid 1 (8mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2OldTitle=Goal mid 2 (1.18mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMid2Title=Goal mid 2 (4mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalMinTitle=Goal - min (%)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTilte=Goals
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTop19Title=Goal top (19mm)
RumenHealthTMRParticleScorePenTableInputViewModel.GoalTrayTitle=Goal tray
RumenHealthTMRParticleScorePenTableInputViewModel.Max=Max
RumenHealthTMRParticleScorePenTableInputViewModel.Mid1Title=Mid 1\n(8 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenOldTitle=Mid 2
RumenHealthTMRParticleScorePenTableInputViewModel.Mid24ScreenTitle=Mid 2\n(4 mm)
RumenHealthTMRParticleScorePenTableInputViewModel.Min=Min
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnPdfTitle=Particle score (% on screen)
RumenHealthTMRParticleScorePenTableInputViewModel.PercentOnScreenTitle=Percent on screen (%)
RumenHealthTMRParticleScorePenTableInputViewModel.StandardDeviationScoreTitle=Standard deviation
RumenHealthTMRParticleScorePenTableInputViewModel.TMRParticleScoreInformation=Tmr particle score information
RumenHealthTMRParticleScorePenTableInputViewModel.TMRScoreName=Tmr particle score
RumenHealthTMRParticleScorePenTableInputViewModel.Title=Tmr particle score
RumenHealthTMRParticleScorePenTableInputViewModel.TopTitle=Top
RumenHealthTMRParticleScorePenTableInputViewModel.TrayTitle=Tray
RumenHealthTMRPenScorerTableMasterViewModel.Inputs=Inputs
RumenHealthTMRPenScorerTableMasterViewModel.Results=Results
RumenHealthTMRPenScorerTableMasterViewModel.Title=Rumen health particle score
RumenHealthTMRSelectPenViewModel.DefaultScorerTitle=None selected
RumenHealthTMRSelectPenViewModel.NoScorerSelected=A scorer must first be selected in order to view a pen.
RumenHealthTMRSelectPenViewModel.SelectPen=Pens (lactating and dry only)
RumenHealthTMRSelectPenViewModel.SelectScorer=Select a scorer
RumenHealthTMRSelectPenViewModel.Title=Rumen health particle score
RumenHealthTMRSelectScorerViewModel.DefaultScorerTitle=None selected
RumenHealthTMRSelectScorerViewModel.FooterText=Only one scorer can be used per visit. Changing the scorer will result in lost values.
RumenHealthTMRSelectScorerViewModel.SelectScorer=Select a scorer
RumenHealthTMRSelectScorerViewModel.Title=Rumen health particle score
Russia=Russia
Russian_Federation=Russian Federation
Rwanda=Rwanda
SAR=Saudi Arabia (﷼ SAR)
SCCPremiumDeduction=Scc premium / deduction ({0}/{1})
SEK=SEK
SGD=SGD
SKK=SKK
SRD=Surinam ($ SRD)
Saint_Barthélemy=Saint Barthélemy
Saint_Helena,_Ascension_and_Tristan_da_Cunha=Saint Helena, Ascension and Tristan da Cunha
Saint_Kitts_and_Nevis=Saint Kitts and Nevis
Saint_Lucia=Saint Lucia
Saint_Martin_(French_part)=Saint Martin (French part)
Saint_Pierre_and_Miquelon=Saint Pierre and Miquelon
Saint_Vincent_and_the_Grenadines=Saint Vincent and the Grenadines
Salerno=Salerno
Samoa=Samoa
San_Luis_Potosí=San Luis Potosí
San_Marino=San Marino
Santa_Catarina=Santa Catarina
Sao_Tome_and_Principe=Sao Tome and Principe
Saskatchewan=Saskatchewan
Sassari=Sassari
SaudiArabia=Saudi arabia
Saudi_Arabia=Saudi Arabia
Savona=Savona
Schleswig=Schleswig
ScorecardPrompt=Please complete the following survey to view the {0} scorecard
Screen=Screen
ScreenNew=Screen new
ScreenOld=Screen old
Search=Search
SegmentViewModel.SegmentTitle=Select a segment
SegmentViewModel.Title=Details
Select=Select
SelectCurrencyViewModel.Title=Currency
SelectDates=Select dates
SelectFeedingSystemViewModel.SelectFeedingSystem=Select feeding system
SelectFeedingSystemViewModel.Title=Pen setup
SelectForageImprovement=Please select only 12 improvement from list.
SelectHousingSystemViewModel.SelectHousingSystem=Select housing system
SelectHousingSystemViewModel.Title=Pen setup
SelectImprovement=Please select only 10 improvement from list.
SelectMatrix=Select a matrix
SelectMilkingSystemViewModel.NoneSelected=None selected
SelectMilkingSystemViewModel.SelectMilkingSystem=Select a milking system
SelectMilkingSystemViewModel.Title=Site setup
SelectOnlyThreeNotes=Please select only 3 notes in the tool.
SelectOnlyTwoNotes=Please select only 2 notes per tool.
SelectPen=Please select a maxdiet for the new pen.
SelectProcessor=Select processor
SelectProcessorViewModel.COMPONENT=Component
SelectProcessorViewModel.CONCENTRATION=Concentration
SelectProcessorViewModel.Edit=Edit
SelectProcessorViewModel.MilkProcessors=Milk processors
SelectVisitComparison=Select visits for comparison
SemiAnnually=Semi annually
Semiconfinamento=Tiestall
Send=Send
Send_Error_Report=Send account info
Senegal=Senegal
Seoul=Seoul
Serbia=Serbia
Sergipe=Sergipe
SettingsViewModel.Imperial=Imperial
SettingsViewModel.Metric=Metric
SettingsViewModel.Milk_Processor_Set_Up=Milk processor set up
SettingsViewModel.More_Settings=More settings
SettingsViewModel.Select_Unit_Of_Measure=Select unit of measure
Severe=heat stress\: open mouth + drooling, respiration rate 120 to more than 160 bpm
Seychelles=Seychelles
Shaanxi=Shaanxi
Shandong=Shandong
Shanghai=Shanghai
Shanxi=Shanxi
ShortDryPeriod=Short Dry Period
ShowEulaViewModel.Accept=Accept
ShowEulaViewModel.ConfirmationNo=No
ShowEulaViewModel.ConfirmationText=Do you agree to the terms of the mobile application end user license agreement?
ShowEulaViewModel.ConfirmationTitle=Confirmation
ShowEulaViewModel.ConfirmationYes=Yes
ShowEulaViewModel.Decline=Decline
ShowEulaViewModel.Eula=EULA
ShowEulaViewModel.EulaError=The end user license agreement cannot be displayed. Please connect to the internet before trying again.
ShowEulaViewModel.EulaScreenTitle=End user license
ShowPrivacyStatementViewModel.PrivacyStatement=<\![CDATA[<p><b>Privacy Statement</b></p><p>Last Updated\: January 3, 2017</p><p><b>Scope</b></p><p>Cargill, Incorporated (“Cargill” or “We”) collects information about you when you use this mobile application (“App”), which is intended for consultants offering the Dairy Enteligen™ service on behalf of Cargill.</p><p><b>Personal Information</b></p><p>Cargill may collect the following personal information directly from you, for instance when you register with us, and through your use of the App.</p><p>· <b>Your Name</b></p><p>· <b>Your Location</b> (via GPS or other similar technology)</p><p>· <b>Your Photos </b>or<b> Videos</b> (if shared with Cargill via the App)</p><p>We may use common technologies, such as cookies and beacons, in the App to collect such personal information. </p><p><b>Use and Sharing – Business Context</b></p><p>Our <a href\="http\://www.cargill.com/privacy/business-notice/index.jsp">Business Information Notice</a> explains how we use personal information collected about you in a business context.</p><p><b>Location Collection Consent</b></p><p>By using the App, you expressly consent to Cargill’s collection of your real-time location information, and expressly waive and release Cargill from any and all liability, claims, causes of action or damages arising from your use of the App, or in any way relating to the use of the location information.</p>]]>
ShowPrivacyStatementViewModel.PrivacyStatementTitle=Privacy statement
ShowSyncStatusViewModel.GetAccounts=Account records received \:
ShowSyncStatusViewModel.GetNotes=Notes records received \:
ShowSyncStatusViewModel.GetVisits=Visit records received \:
ShowSyncStatusViewModel.Title=Data sync summary
Sichuan=Sichuan
Siena=Siena
Sierra_Leone=Sierra Leone
Sikkim=Sikkim
SilageBags=Silage bags
SilageBags_BagsPlacedOnStableWellManagedSurface=Are bags placed on a stable, well managed all season surface?
SilageBags_BonusSecureCoverIsUsed=Is a security cover used?
SilageBags_CleanWellManagedFeedFaceNoLooseFeed=Is the face clean and well managed with no indication of loose feed heating and shrinking?
SilageBags_FaceRemovalRate=What is the face removal rate?
SilageBags_InspectedForPestHoleDamageRepairOnBasis=Are bags inspected for pest hole damage and repaired on a regular basis?
SilageBags_PorosityScoresConsistently=What is the porosity score, with respect to the dm?
SilageBags_TrashVegRodentControlledAroundBags=Are trash, vegetation and rodents controlled around bags?
SilagePrevention1st=Silage prevention\: 1st things 1st
Sinaloa=Sinaloa
Singapore=Singapore
Sint_Maarten_(Dutch_part)=Sint Maarten (Dutch part)
Site-Not-Synced-To-Lift=Site was not synced to LIFT; please contact the admin for resolution
SiteDetailViewModel.AnimalInputsSite=Animal inputs site
SiteDetailViewModel.DairyEnteligenReport=Dairy enteligen report
SiteDetailViewModel.Detailed=Detailed
SiteDetailViewModel.DietInputsSiteLactating=Diet inputs, site (lactating animals)
SiteDetailViewModel.DownloadingVisit=Downloading visit...
SiteDetailViewModel.GeneralCustomerSiteSetup=General customer site setup
SiteDetailViewModel.GetReportMsg=Downloading report...
SiteDetailViewModel.MainHeading=VISITS
SiteDetailViewModel.NetworkErrorMessage=There is currently no network available.
SiteDetailViewModel.NetworkErrorMessageTitle=Network error
SiteDetailViewModel.NewVisit=Begin a new visit
SiteDetailViewModel.ReportDownloadTimeout=Unable to download the file, please try when you have better connectivity
SiteDetailViewModel.ReportNotAvailable=Report not available for download.
SiteDetailViewModel.ReportNotAvailableTitle=Status
SiteDetailViewModel.Reports=Dairy enteligen report
SiteDetailViewModel.Resources=Resources
SiteDetailViewModel.SiteSetup=Site setup
SiteDetailViewModel.Summary=Summary
SiteDetailViewModel.Title=Site details
SiteDetailViewModel.VisitDownloadPrompt=Visit not downloaded, do you want to try download this visit?
SiteDetailViewModel.VisitNotDownloaded=Visit not downloaded
SiteDetailViewModel.VisitUnavailable=Visit data is unavailable.
SiteDetailsResourcesViewModel.Title=Resources
SiteDetailsSetupViewModel.AnimalInputsSite=Animal inputs site
SiteDetailsSetupViewModel.AsFedIntake=As-fed intake ({0})
SiteDetailsSetupViewModel.BacteriaCellCount=Bacteria cell count (1,000 cfu/ml)
SiteDetailsSetupViewModel.Continue=Continue
SiteDetailsSetupViewModel.CurrentMilkPrice=Current milk price ({0}/{1})
SiteDetailsSetupViewModel.DaysInMilk=Days in milk (dim)
SiteDetailsSetupViewModel.Delete=Delete
SiteDetailsSetupViewModel.DietInputsSiteLactating=Diet inputs, site (lactating animals)
SiteDetailsSetupViewModel.DietSetup=Diet setup
SiteDetailsSetupViewModel.Diets=Diets
SiteDetailsSetupViewModel.DryMatterIntake=Dry matter intake ({0})
SiteDetailsSetupViewModel.GeneralCustomerSiteSetup=General customer site setup
SiteDetailsSetupViewModel.LactatingAnimals=Lactating animals
SiteDetailsSetupViewModel.MilkFatPercent=Milk Fat %
SiteDetailsSetupViewModel.MilkOtherSolidsPercent=Milk other solids %
SiteDetailsSetupViewModel.MilkProteinPercent=Milk protein %
SiteDetailsSetupViewModel.MilkYield=Milk yield ({0})
SiteDetailsSetupViewModel.MilkingSystem=Milking system
SiteDetailsSetupViewModel.NameNotUnique=A site named "{0}" already exists. Names must be unique.
SiteDetailsSetupViewModel.NetEnergyOfLactationDairy=Nel dairy (mcal/{0})
SiteDetailsSetupViewModel.NewSite=New site
SiteDetailsSetupViewModel.NullSiteName=Site name, milk price, milking system and pen are mandatory fields. Do you wish to continue or delete the site?
SiteDetailsSetupViewModel.NumberOfStalls=Total stalls in parlor
SiteDetailsSetupViewModel.PenSetup=Pen setup
SiteDetailsSetupViewModel.Pens=Pens
SiteDetailsSetupViewModel.RationCost=Ration cost, per animal ({0})
SiteDetailsSetupViewModel.SiteMandatoryFields=Site name, milk price, milking system and pen are mandatory fields. Fill all the mandatory fields to continue.
SiteDetailsSetupViewModel.SiteName=Site name
SiteDetailsSetupViewModel.SiteSetup=Site setup
SiteDetailsSetupViewModel.SomaticCellCount=Somatic cell count (1,000 cells/ml)
SiteDetailsSetupViewModel.Title=Site details
SiteDetailsSetupViewModel.WeightImperialCWT=CWT
SiteVisitSummary=Site visit summary
SiteVisitSummaryReport=Site visit summary report
SixToEightLayers=6 to 8 layers
SixToTwelveHours=6 to 12 hours
SixToTwelveInches=7.5 to 15 cm (3 to 6 inches)
Sligo=Sligo
Slovakia=Slovakia
Slovenia=Slovenia
Solomon_Islands=Solomon Islands
Somalia=Somalia
SomanticCellCount=Somatic cell count
SomaticCellPerML=Somatic cell count (cells/ml)
Sondrio=Sondrio
Sonora=Sonora
SouthAfrica=South africa
SouthKorea=Korea (south)
South_Africa=South Africa
South_Australia=South Australia
South_Carolina=South Carolina
South_Dakota=South Dakota
South_Georgia_and_the_South_Sandwich_Islands=South Georgia and the South Sandwich Islands
South_Sudan=South Sudan
Spain=Spain
Sri_Lanka=Sri Lanka
StandardDeviationScoreTitle=Standard Deviation
StartDate=Start date
StatusArchived=Archived
StatusCompleted=Completed
StatusDeleted=Deleted
StatusInProgress=In progress
StdDevCalculated=Std. deviation (calculated)
Steer=Steer
StorageCalculators=Storage calculators
StrategyToReduceDisplacedAbomasum=Displaced abomasum
StrategyToReduceDisplacedAbomasumDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><strong>Strategy to reduce displaced abomasum incidence</strong></h4><p>The prevention strategy relies on managing the risk factors, as we currently do not have a clear direct cause.</p><p>Prevention includes appropriate nutrition and management, and management of concomitant diseases\:</p><ul><li>Control of nutritional risk factors\:<ul><li>Avoid over-conditioning cows (ideal 3.00 BCS at dry-off and calving).</li><li>Provide enough forage NDF.</li><li>Manage physical form of the maxDiet.</li><li>Pay attention to mineral requirements.</li><li>Avoid other metabolic disorders such as hypocalcemia, and also infectious disease that could reduce intake.</li></ul></li><li>Best management practices\:<ul><li>Assure feed intake in fresh cows especially during the hours/days after calving</li><li>Manage feed bunks properly</li><li>Increase cow comfort, reduce any stressors</li></ul></li></ul></span>]]>
StrategyToReduceDystocia=Dystocia
StrategyToReduceDystociaDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><strong>Strategy to reduce dystocia incidence</strong></h4><p>Help prevent dystocia by\:</p><ul><li>Ensuring heifers are inseminated at the proper age and bodyweight.</li><li>Selecting potential sires on the basis of known calving ease.</li><li>Improving personnel training regarding proper timing and methods of intervention during calving, plus appropriate methods to care for compromised newborn calves</li><li>Review the current ration program following MAX<sup>TM</sup> requirements for far-off and close-up cows and heifers focusing on\:<ul><li>Energy to maintain body condition score and fetal growth</li><li>Prevention of over-conditioned cows at calving</li><li>Control of hypocalcemia risk in the herd</li></ul></li></ul></span>]]>
StrategyToReduceIncidence=Strategies to reduce incidence
StrategyToReduceKetosis=Ketosis
StrategyToReduceKetosisDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><strong>Strategy to reduce ketosis incidence</strong></h4><ol><li>Assure adequate cow comfort (bedding, heat stress control and ventilation, avoid stress and overcrowding, etc.)</li></ol><p>&nbsp;</p><ol start\="2"><li>Balance diets according to MAX<sup>TM</sup> requirements for nutrients and physical maxDiet characteristics. Consult the Transition Cow products inventory for specific product formulations developed for prevention of ketosis. Assure use of good quality forages that will increase intake and rumen buffer capacity.</li></ol><p>&nbsp;</p><ol start\="3"><li>Monitor BCS change between far-off and calving\: Dry cows off at 3.00 BCS and maintain BCS during the dry period to avoid excessive lipid mobilization around calving.</li></ol><p>&nbsp;</p><ol start\="4"><li>Implement a specific health protocol developed for fresh cows to detect and prevent metabolic disorders and infectious diseases. Monitor intake, rumen fill and rumination.</li></ol></span>]]>
StrategyToReduceMastitis=Mastitis
StrategyToReduceMastitisDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><strong>Strategy to reduce mastitis incidence</strong></h4><p>Microorganisms that most frequently cause mastitis can be divided into two main categories\:</p><ul><li>Contagious pathogens, which spray from cow to cow primarily during the milking, process <em>(i.e. Strep. agalactiae&nbsp;and&nbsp;Staph. Aureus)</em></li><li>Environmental pathogens, which come from the dairy cows environment <em>(i.e. E. coli&nbsp;and&nbsp;Strep. Uberis)</em></li></ul><p>&nbsp;</p><p>Depending on which bacteria category is causing mastitis, the intervention focus has to be adapted.</p><ul><li><strong>Contagious mastitis control</strong>\: there is no magic silver bullet preventing infections for all pathogens but the following steps will help\:<ul><li>Focus on hygiene around and during milking , including teat dipping</li><li>Milk infected cows at last</li><li>Carry out regular milking machine maintenance</li><li>Review the dry cow nutrition plan using MAX<sup>TM</sup> guidelines to ensure a proper nutrient supply (energy, antioxidant) to support immune function</li></ul></li></ul><p>&nbsp;</p><ul><li><strong>Controlling environmental mastitis</strong><ul><li>Ensure a clean and dry bedding, well-ventilated barn</li><li>Correct stocking density</li><li>General hygiene of the udder</li><li>Parlor routine</li><li>Review the dry cow nutrition plan using MAX<sup>TM</sup> guidelines to ensure a proper nutrient supply (energy, antioxidant) to support immune function</li></ul></li></ul></span>]]>
StrategyToReduceMetritis=Metritis
StrategyToReduceMetritisDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><strong>Strategy to reduce metritis incidence</strong></h4><p>Risk factors for metritis include retained placenta, injury to the reproductive tract due to a difficult calving, improper calving protocol , unsanitary calving area, nutritional deficiency such as vitamin E or selenium deficiencies, and over-conditioned cows.</p><p>Monitor the following areas, in descending order of importance&nbsp;\:</p><ol><li>Calving practices<br /><ul><li>Are employees carrying bacteria into the uterus when assisting in calving?</li><li>Are cows being helped too early or too late?</li><li>Are calves being pulled too often?</li></ul></li></ol><ol start\="2"><li>Treatment practices<br /><ul><li>How are cows with RP's or metritis treated?</li><li>Any chance of carrying bacteria from the outside environment or vagina into the uterus at that time?</li></ul></li></ol><ol start\="3"><li>Animal Stress<br /><ul style\="list-style-type\: circle;"><li>Excess stress before calving may be depleting the cow immune system , lowering the resistance to any infection after calving.</li></ul></li></ol><ol start\="4"><li>Nutrition<ul><li>Review maxDiet balance in dry cow, focusing on BCS control, mineral balance and supply of antioxidant nutrients (vit E, A, selenium, zinc and copper)</li></ul></li><li>Other disease check (ketosis, LDA)</li></ol></span>]]>
StrategyToReduceMilkFever=Milk fever
StrategyToReduceMilkFeverDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><strong>Strategy to reduce milk fever incidence</strong></h4><p>Two alternative strategies for the prevention of hypocalcemia in dairy cows rely 100% on dietary management.</p><ul><li>Use dry cow diets low in Ca</li><li>Use dry cow diets formulated to a low DCAD</li></ul><p>&nbsp;</p><p>Area to consider to keep hypocalcemia under control are\:</p><ul><li>Testing forages for minerals ( calcium, phosphorus, magnesium, potassium, sodium, sulfur, and chloride) in a validated laboratory</li><li>Review the current ration program following MAX<sup>TM</sup> requirements for close-up cows and feeding management practices (special attention to ad libitum forages, K concentration in forages, free-choice minerals fed to dry cows, dry cows sorting out their maxDiet)</li><li>Consult the Transition cow product inventory for specific formulations developed to prevent hypocalcemia</li><li>When using DCAD diets\:<ul><li>Carefully monitor feed intake as anionic salts are unpalatable and can reduce dry matter intake</li><li>Monitor urine pH to check the efficacy of the maxDiet changes</li></ul></li></ul></span>]]>
StrategyToReduceRetainedPlacenta=Retained placenta
StrategyToReduceRetainedPlacentaDetails=<\![CDATA[<span style\="font-family\:Calibri,Calibrib;"><h4><span><strong>Strategy to reduce retained placenta incidence</strong></span></h4><p>Retained placenta incidence is positively correlated with immune suppression, high stress hormones, hypocalcemia (including subclinical), assisted calving and abortions, endemic infectious agents, and genetics.</p><p>Monitor the following areas, in descending order of importance\:</p><ol><li>Nutrition <br/> <p style\="padding-left\: 30px;">Review close-up diets following MAX<sup>TM</sup> guidelines with a special focus on\:</p><ul><li>Se and vitamin E</li><li>Minerals levels (K, Ca, Mg) to decrease risk of subclinical and clinical hypocalcemia</li><li>Use a low or negative DCAD to decrease the incidence of hypocalcemia</li></ul></li></ol><ol start\="2"><li>Management</li><ul><li>Manage BCS (3.00 at calving) during late lactation and the dry period</li><li>Minimize stressors in the close up and postpartum area to decrease fat mobilization and immunosuppression around parturition.</li><li>Improve cow comfort and provide enough bunk space</li><li>Follow a calving procedure set by your veterinarian</li><li>Do not help cows calving unnecessarily</li><li>Maintain a clean environment to decrease chances of uterine infection.</li></ul></ol></span>]]>
Straw=Straw
Sudan=Sudan
Surinam=Surinam
Suriname=Suriname
SurveyCategories=Survey categories
SurveyOfForages=Forage management
SurveyOfForages_AnnualCowNumAndForageNeeds=Are the number of cows and forage needs planned annually?
SurveyOfForages_AshLevelsInCornSilage=What are ash levels?
SurveyOfForages_AshLevelsInHaylage=What are ash levels?
SurveyOfForages_AshLevelsInOtherSilage=What are ash levels?
SurveyOfForages_ButyricAcidLevelsInHaylage=What are the butyric acid levels?
SurveyOfForages_CornSilageProcessingScore=What is the length of chop of the sample? (cargill forage particle score)
SurveyOfForages_CornSilageScoreMonitored=What is the kernel hardness score in the corn silage?
SurveyOfForages_InspectedForSpoilageAndMold=Are all forages inspected for spoilage and mold and is spoiled forage discarded?
SurveyOfForages_InventoryIsMonitored=How often is forage inventory monitored?
SurveyOfForages_LacticAcidToAceticAcidLevels=What are the lactic to acetic acids ratios?
SurveyOfForages_LooseOrFacedFeedWithin=Loose or "faced" feed is fed within\:
SurveyOfForages_NoLooseFeedRemaining=Are feed refusals removed and measured daily?
SurveyOfForages_SilosSizedForCapacity=Are forage storage units sized for capacity compared to the needs of the dairy herd? (no overfilling)
SurveyOfForages_VisibleSignsOfSoil=Is the silage free from any sign of soil contamination?
Svalbard_and_Jan_Mayen=Svalbard and Jan Mayen
Swaziland=Swaziland
Sweden=Sweden
Switzerland=Switzerland
Sync-failed-due-to-unknown-reason=Sync failed due to unknown reasons
SyncFailed=The sync could not be completed at this time, please try again.
Sync_Data=Sync data
Syracuse=Syracuse
Syrian_Arab_Republic=Syrian Arab Republic
SystemGenerated=System generated
São_Paulo=São Paulo
THB=Thailand (THB THB)
TMR=TMR
TMRHerdAnalysisTableTitle=Tmr particle score herd analysis
TMRParticleScore=TMR particle score analysis
TMRParticleScoreHerdAnalysisEditTableViewModel.Close=Close
TMRParticleScoreHerdAnalysisEditTableViewModel.HerdAnalysisTableTitle=Days in milk (dim)
TMRParticleScoreHerdAnalysisEditTableViewModel.Title=Edit dim amount
TMRParticleScoreHerdAnalysisInputsViewModel.DIM=DIM
TMRParticleScoreHerdAnalysisInputsViewModel.Edit=Edit
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenNewType=(4mm)
TMRParticleScoreHerdAnalysisInputsViewModel.FourScreenOldType=(1.18mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidOne=Mid 1
TMRParticleScoreHerdAnalysisInputsViewModel.MidOneValue=(8mm)
TMRParticleScoreHerdAnalysisInputsViewModel.MidTwo=Mid 2
TMRParticleScoreHerdAnalysisInputsViewModel.Title=Tmr particle score analysis
TMRParticleScoreHerdAnalysisInputsViewModel.Top=Top
TMRParticleScoreHerdAnalysisInputsViewModel.TopValue=(19mm)
TMRParticleScoreHerdAnalysisInputsViewModel.Tray=Tray
TMRParticleScoreHerdAnalysisMasterViewModel.HerdAnalysis=Herd analysis
TMRParticleScoreHerdAnalysisMasterViewModel.Inputs=Inputs
TMRParticleScoreHerdAnalysisMasterViewModel.Results=Results
TMRParticleScoreHerdAnalysisMasterViewModel.Title=Rumen health particle score
TMRParticleScoreHerdAnalysisResultsText=Tmr particle score analysis
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid1=Mid 1 (8mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRMid2=Mid 2 (4mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTop=Top (19mm)
TMRParticleScoreHerdAnalysisResultsViewModel.RumenHealthTMRTray=Tray
TRY=TRY
TWD=Taiwan (NT$ TWD)
Tabasco=Tabasco
Taipei_City=Taipei City
Taiwan=Taiwan
Tajikistan=Tajikistan
Tamaulipas=Tamaulipas
Tamil_Nadu=Tamil Nadu
Tanzania,_United_Republic_of=Tanzania, United Republic of
Taranto=Taranto
Task=Task
Tasmania=Tasmania
TemperatureImperial=°F
TemperatureMetric=°C
Tennessee=Tennessee
Teramo=Teramo
Terni=Terni
Texas=Texas
TextureFeed=Textured feed
Thailand=Thailand
Thirdparty=Thirdparty
ThisVisit=(this visit)
ThreePotentialStorageSolutions=Three potential storage solutions
ThreeScreen=3 screen
ThreeTimesPerWeek=3 times per week
Tianjin=Tianjin
Tiestall=Tiestall
TimeRemaining=Time remaining for resting
Timor-Leste=Timor-Leste
Tipperary=Tipperary
Tlaxcala=Tlaxcala
Tocantins=Tocantins
Togo=Togo
Tokelau=Tokelau
Tokyo=Tokyo
Tonga=Tonga
TonsAF=Tons af
TonsAFSilo=Tons af (remaining in silo)
TonsDM=Tons dm
TonsDMSilo=Tons dm (remaining in silo)
TonsPerDay=Tons per day
ToolNotSelected=You must select at least one tool
Top=Top
TopUnloadingSilo=Top unloading silo
TopUnloadingSilos=Top unloading silo
TopValue=(19mm)
Total=Total
Total\ Production\ (cow/day)=Total Production (cow/day)
TotalAnimals=Total animals in pen
TotalAnimalsHerd=Total animals
TotalPenPerScore=Total \# of animals in pen per score
TotalRevenue=Total revenue
TowerSilos=Tower silos
TowerSilos_FaceRemovalRateGreaterThan4Inches=Is the removal rate greater than 10 cm (4 inches) per day?
TowerSilos_IsSiloCoveredAfterFillingIfNotUsed=Is silo covered for a month after filling if not used immediately?
TowerSilos_SiloFillingTimeof3DaysOrLess=Is the filling time per silo 3 days or less?
TransitionCow=Transition cow
Trapani=Trapani
Tray=Tray
Trends=Positive trends
Trento=Trento
Treviso=Treviso
Trieste=Trieste
Trinidad_and_Tobago=Trinidad and Tobago
Tripura=Tripura
Tunisia=Tunisia
Turin=Turin
Turkey=Turkey
Turkmenistan=Turkmenistan
Turks_and_Caicos_Islands=Turks and Caicos Islands
Tuvalu=Tuvalu
TwelveInchesOrGreater=Greater than 15 cm (6 inches)
TwentyFourHoursAndNoSync=Twenty four hours and no sync
TwentyFourHoursBeforeActionIsDue=Twenty four hours before action is due
TwentyFourToThirtySixInchesPerDay=15 to 30 cm (6 to 12 inches) per day
TwicePerWeek=Twice per week
UAH=Ukraine (UAH UAH)
UK=United kingdom
UNITED_STATES=UNITED_STATES
US=United states of america
USD=United States of America ($ USD)
Udine=Udine
Uganda=Uganda
Ukraine=Ukraine
UnitedKingdom=United kingdom
UnitedStates=United states of america
United_Arab_Emirates=United Arab Emirates
United_Kingdom=United Kingdom
United_States=United States
UrinePH=Urine ph
UrinePHAVG=Average urine ph
UrinePHAverageNumber=Average number
UrinePHDensity=Urine ph resource
UrinePHEditCowViewModel.AddNew=Next cow
UrinePHEditCowViewModel.CowName=Cow name
UrinePHEditCowViewModel.CowValue=Cow value
UrinePHEditCowViewModel.UrinePHEnterCowValue=Enter cow value
UrinePHEditCowViewModel.ValidCudInput=Please enter a valid input.
UrinePHEditGoalViewModel.GoalMax=Goal - max
UrinePHEditGoalViewModel.GoalMin=Goal - min
UrinePHEditGoalViewModel.TargetUrinePHRange=Target urine ph range
UrinePHEditGoalViewModel.Title=Edit goals
UrinePHInputsViewModel.AddNew=Add new
UrinePHInputsViewModel.CalculatorHeading=Please select a cow to enter the urine ph value. Tap "Add New" to add cows to the list.
UrinePHInputsViewModel.CoefficientVariation=Coefficient of variation (c.v.) (%)
UrinePHInputsViewModel.CowsOutsideTargetRange=Cows outside the range (%)
UrinePHInputsViewModel.CudChewCategorySection=Cows
UrinePHInputsViewModel.DietDCAD=Diet dcad, meq/100g
UrinePHInputsViewModel.Resources=Resources
UrinePHInputsViewModel.TargetUrinePHRange=Target urine ph range
UrinePHInputsViewModel.UrinePHAVG=Avg. urine ph (calculated)
UrinePHInputsViewModel.UrinePhSTDDEV=Std. deviation (calculated)
UrinePHMasterViewModel.Inputs=Inputs
UrinePHMasterViewModel.Results=Results
UrinePHMasterViewModel.Title=Urine ph
UrinePHPenSelectionViewModel.Title=Urine ph
UrinePHPenSelectionViewModel.UrinePHPenList=Pens (lactating and dry only)
UrinePHResultsViewModel.DietDCAD=Diet dcad, meq/100g
UrinePHResultsViewModel.MaxpH=Max ph
UrinePHResultsViewModel.MinpH=Min ph
UrinePHResultsViewModel.UrinePHAVG=Average urine ph
Uruguay=Uruguay
UserCreated=User created
UserPreferencesViewModel.Branding=Go-to-marketing branding
UserPreferencesViewModel.Cargill=Cargill
UserPreferencesViewModel.CurrencySelection=Currency selection
UserPreferencesViewModel.Imperial=Imperial
UserPreferencesViewModel.MainHeading=The following option can be changed later in the app settings.
UserPreferencesViewModel.Metric=Metric
UserPreferencesViewModel.Provimi=Provimi
UserPreferencesViewModel.ProvimiUS=Provimi us
UserPreferencesViewModel.Purina=Purina
UserPreferencesViewModel.SelectCurrency=Select a currency
UserPreferencesViewModel.SelectPointScale=Select body condition score scale
UserPreferencesViewModel.Title=User settings
UserPreferencesViewModel.UnitOfMeasure=Select unit of measure
UserPreferencesViewModel.UserPreferencesMilkProcessor=Set up milk processor
UserPreferencesViewModel.UserPreferencesMoreSettings=More settings
User_Settings=User settings
Utah=Utah
Uttar_Pradesh=Uttar Pradesh
Uttarakhand=Uttarakhand
Uzbekistan=Uzbekistan
VEF=Venezuela (Bs VEF)
VND=Vietnam (₫ VND)
Vanuatu=Vanuatu
Varese=Varese
Venezuela=Venezuela
Venezuela,_Bolivarian_Republic_of=Venezuela, Bolivarian Republic of
Venice=Venice
Veracruz=Veracruz
Verbano-Cusio-Ossola=Verbano-Cusio-Ossola
Vercelli=Vercelli
Vermont=Vermont
Verona=Verona
VersionUpdatePopUp=This version of application is not compatible with new version. Please update this application.
Vestland=Vestland
Vibo_Valentia=Vibo Valentia
Vicenza=Vicenza
Victoria=Victoria
Viet_Nam=Viet Nam
Vietnam=Vietnam
ViewOverallCalfHaiferScore=View overall calf and heifer score
ViewOverallForageScore=View overall forage score
Virgin_Islands,_British=Virgin Islands, British
Virginia=Virginia
Visit.Report.Footer.Patent=Cargill Incorporated, its parents and affiliates does not warrant the accuracy of these estimates, due to many factors. There is no guarantee of production or financial results. ©2023 Cargill, Incorporated. All Rights Reserved.
VisitAutoPublished=Visit auto published
VisitDate=Date of visit
VisitDownloadProceed=Proceed
VisitNotebook=Visit notebook
VisitNotesViewModel.Action=Action
VisitNotesViewModel.Close=Close
VisitNotesViewModel.DownloadingNotes=Downloading notes...
VisitNotesViewModel.Event=Event
VisitNotesViewModel.New=New
VisitNotesViewModel.NoteMetadata={0} @ {1} by {2}
VisitNotesViewModel.Observation=Observation
VisitNotesViewModel.Task=Task
VisitNotesViewModel.Title=Visit notebook
VisitNotesViewModel.VisitNotebook=Visit notebook
VisitSummaryViewModel.CalfHeiferItem=Calf and heifer
VisitSummaryViewModel.CalfHeiferScorecard=Scorecard
VisitSummaryViewModel.CategorySection=Tool categories
VisitSummaryViewModel.ComfortHeatStressBanner=Heat stress tool pen
VisitSummaryViewModel.ComfortItem=Comfort
VisitSummaryViewModel.EmailReport=Email report
VisitSummaryViewModel.FreeFormReport=Free form report
VisitSummaryViewModel.HealthItem=Health
VisitSummaryViewModel.HeatstressEvaluationTitle=Heat stress evaluation
VisitSummaryViewModel.HerdAnalysis=Herd analysis
VisitSummaryViewModel.InputsOutputsChart=Inputs / outputs / charts
VisitSummaryViewModel.MilkProcessRevenueCalculator=Milking procedure comparison
VisitSummaryViewModel.NoToolPrompt=No tools have been completed.
VisitSummaryViewModel.NutritionForage=Forage audit
VisitSummaryViewModel.NutritionItem=Nutrition
VisitSummaryViewModel.NutritionPile=Forage inventories
VisitSummaryViewModel.PenTimeTitle=Pen time budget
VisitSummaryViewModel.ProductivityItem=Productivity
VisitSummaryViewModel.RumenHealthBodyConditionTitle=Body condition score
VisitSummaryViewModel.RumenHealthLocomotionTitle=Locomotion score
VisitSummaryViewModel.RumenHealthManureTitle=Rumen health manure score
VisitSummaryViewModel.RumenHealthMetabolicIncidenceTitle=Metabolic incidence
VisitSummaryViewModel.RumenHealthReadyToMilkTitle=Ready2Milk&\#8482;
VisitSummaryViewModel.RumenHealthTMRTitle=Rumen health tmr particle score
VisitSummaryViewModel.RumenHealthTitle=Rumen health cud chewing
VisitSummaryViewModel.RumenHealthUrinePHTitle=Urine ph
VisitSummaryViewModel.Title=Site visit summary
VisitSummaryViewModel.VisitSummaryMilkCalc=Inputs / results / resources
VisitSummaryViewModel.VisitTitle=Visit name
VisitViewModel.CalfHeiferItem=Calf and heifer
VisitViewModel.CategorySection=Tool categories
VisitViewModel.ComfortItem=Comfort
VisitViewModel.Delete=Delete visit
VisitViewModel.DeletePrompt=Are you sure you want to delete this visit? It cannot be undone.
VisitViewModel.HealthItem=Health
VisitViewModel.Instructions=Please select a category or report from the list below
VisitViewModel.NullVisitName=Visit name cannot be blank. Please enter a visit name.
VisitViewModel.NutritionItem=Nutrition
VisitViewModel.ProductivityItem=Productivity
VisitViewModel.Publish=Publish
VisitViewModel.PublishNotes=Publish notes
VisitViewModel.PublishNotesPrompt=Are you sure you want to publish notes of this visit? It cannot be undone.
VisitViewModel.PublishPrompt=Are you sure you want to publish this visit? It cannot be undone.
VisitViewModel.PublishVisit=Publish visit
VisitViewModel.SiteVisitSummary=Site visit summary
VisitViewModel.ToolCategories=Tool categories
VisitViewModel.VisitNotebook=Visit notebook
VisitViewModel.VisitTitle=Visit name
VisitViewModel.WalkthroughReport=Walkthrough report
Viterbo=Viterbo
VolumeImperial=gal
VolumeMetric=ml
WalkthroughPenSelectionViewModel.Pens=PENS
WalkthroughPenSelectionViewModel.Title=Walkthrough report
WalkthroughReport=Walkthrough report
WalkthroughReportHerdAnalysisViewModel.Appearance=Appearance
WalkthroughReportHerdAnalysisViewModel.BeddingCleanliness=Bedding cleanliness
WalkthroughReportHerdAnalysisViewModel.BeddingDepthSoft=Bedding\: depth and softness
WalkthroughReportHerdAnalysisViewModel.Branding=Go-to-market branding
WalkthroughReportHerdAnalysisViewModel.Cargill=Cargill
WalkthroughReportHerdAnalysisViewModel.ComfortItem=Cow comfort, % of animals laying
WalkthroughReportHerdAnalysisViewModel.Comments=Comments
WalkthroughReportHerdAnalysisViewModel.CudChewCategorySection=Cud counts, chews per cud
WalkthroughReportHerdAnalysisViewModel.CudChewing=Rumination, % chewing
WalkthroughReportHerdAnalysisViewModel.EmailBody={0}-{1} report
WalkthroughReportHerdAnalysisViewModel.EmailSubject={0} report
WalkthroughReportHerdAnalysisViewModel.ExportSelected=Email selected tools
WalkthroughReportHerdAnalysisViewModel.FinalObservations=Final observations
WalkthroughReportHerdAnalysisViewModel.GeneratingReport=Generating report...
WalkthroughReportHerdAnalysisViewModel.HockAbrasion=Hock abrasion, % of animals
WalkthroughReportHerdAnalysisViewModel.MainHeading=Walkthrough report
WalkthroughReportHerdAnalysisViewModel.NasalDischarge=Nasal discharge, % of animals
WalkthroughReportHerdAnalysisViewModel.Notes=Notes
WalkthroughReportHerdAnalysisViewModel.Opportunities=Opportunities
WalkthroughReportHerdAnalysisViewModel.PensForExport=Pens for export
WalkthroughReportHerdAnalysisViewModel.Provimi=Provimi
WalkthroughReportHerdAnalysisViewModel.ProvimiUS=Provimi us
WalkthroughReportHerdAnalysisViewModel.Purina=Purina
WalkthroughReportHerdAnalysisViewModel.RumenFill=Rumen fill
WalkthroughReportHerdAnalysisViewModel.RumenHealthBodyConditionTitle=Body condition score (bcs)
WalkthroughReportHerdAnalysisViewModel.RumenHealthLocomotionTitle=Locomotion score
WalkthroughReportHerdAnalysisViewModel.RumenHealthManureTitle=Manure score
WalkthroughReportHerdAnalysisViewModel.SubHeading=Herd analysis
WalkthroughReportHerdAnalysisViewModel.Title=Walkthrough report herd analysis
WalkthroughReportHerdAnalysisViewModel.Trends=Positive trends
WalkthroughReportHerdAnalysisViewModel.UterineDischarge=Uterine discharge, % of animals
WalkthroughReportHerdAnalysisViewModel.WaterQuality=Water quality
WalkthroughReportLandingViewModel.HerdAnalysis=Herd analysis
WalkthroughReportLandingViewModel.PenAnalysis=Pen analysis
WalkthroughReportLandingViewModel.Title=Walkthrough report
WalkthroughReportQualityViewModel.BeddingCleanliness=Select bedding cleanliness
WalkthroughReportQualityViewModel.Clean=Clean
WalkthroughReportQualityViewModel.Dirty=Dirty
WalkthroughReportQualityViewModel.ModeratelyClean=Moderately clean
WalkthroughReportQualityViewModel.Title=Walkthrough report
WalkthroughReportQualityViewModel.WaterQuality=Select water quality
WalkthroughReportViewModel.Appearance=Appearance
WalkthroughReportViewModel.BeddingCleanliness=Bedding cleanliness
WalkthroughReportViewModel.BeddingDepthSoft=Bedding\: depth and softness
WalkthroughReportViewModel.Clean=Clean
WalkthroughReportViewModel.ComfortItem=Cow comfort, % of animals laying
WalkthroughReportViewModel.Comments=Comments
WalkthroughReportViewModel.CudChewCategorySection=Cud counts, chews per cud
WalkthroughReportViewModel.CudChewing=Rumination, % chewing
WalkthroughReportViewModel.Current=Current
WalkthroughReportViewModel.Dirty=Dirty
WalkthroughReportViewModel.Goals=Goal
WalkthroughReportViewModel.HockAbrasion=Hock abrasion, % of animals
WalkthroughReportViewModel.ModeratelyClean=Moderately clean
WalkthroughReportViewModel.NasalDischarge=Nasal discharge, % of animals
WalkthroughReportViewModel.Opportunities=Opportunities
WalkthroughReportViewModel.Previous=Previous
WalkthroughReportViewModel.RumenFill=Rumen Fill
WalkthroughReportViewModel.RumenHealthBodyConditionTitle=Body condition score (bcs)
WalkthroughReportViewModel.RumenHealthLocomotionTitle=Locomotion score
WalkthroughReportViewModel.RumenHealthManureTitle=Manure score
WalkthroughReportViewModel.Title=Walkthrough report
WalkthroughReportViewModel.Trends=Positive trends
WalkthroughReportViewModel.UterineDischarge=Uterine discharge, % of animals
WalkthroughReportViewModel.WaterQuality=Water quality
Wallis_and_Futuna=Wallis and Futuna
Washington=Washington
Waterford=Waterford
Weekly=Weekly
WeightDMInLengthImperial=Lbs. dm in 1 foot
WeightDMInLengthMetric=Kgs. dm in 1 meter
WeightImperial=lbs
WeightImperialCWT=CWT
WeightMetric=kg
West_Bengal=West Bengal
West_Virginia=West Virginia
Western_Australia=Western Australia
Western_Sahara=Western Sahara
Westmeath=Westmeath
Wexford=Wexford
When=creating a new pen the only items required are the pen name, diet, housing system, and feeding system. 
Wicklow=Wicklow
Wisconsin=Wisconsin
WithinEightHours=Within 8 hours
Wyoming=Wyoming
Xinjiang=Xinjiang
Xizang=Xizang
Yemen=Yemen
Yes=Yes
Yucatán=Yucatán
Yukon_Territories=Yukon Territories
Yunnan=Yunnan
ZAR=South Africa (ZAR ZAR)
Zacatecas=Zacatecas
Zambia=Zambia
Zhejiang=Zhejiang
Zimbabwe=Zimbabwe
welcome.message=Greetings {0}
Agridea=Agridea
RagioDiSole=Ragio Di Sole
Holstein=Holstein
BrownSwiss=Brown swiss
Ayrshire=Ayrshire
Conventional=Conv.
PMR=PMR
CompleteFeed=Complete feed (C)
Supplement=Supplement (S)
Ingredients=Ingredients (I)
RoundBales=Round bales
Silage=Silage
SmallGrainSilage=Small grain silage
DryCorn=Dry corn
HighMoistureCorn=High moisture corn
Barley=Barley
MixedGrain=Mixed grain
Wheat=Wheat
Oats=Oats
Cobmeal=Cobmeal
Soybeans=Soybeans
butterfat=Butterfat
protein=Protein
DryHay=Dry hay
lactoseAndOtherSolids=Lactose And Other Solids
deductions=Deductions
class2Protein=Class 2 Protein
class2LactoseAndOtherSolids=Class 2 Lactose And Other Solids
Report.Return.Over.Feed.YAxis=Return Over Feed ($/cow/day)
PurinaCanada=Purina Canada
RaggioDiSole=Raggio Di Sole
Rof.Kg.Per.Fat=Return Over Feed Kg Per Fat




