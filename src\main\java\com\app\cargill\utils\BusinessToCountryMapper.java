/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Country;

public class BusinessToCountryMapper {

  private BusinessToCountryMapper() {}

  public static Country businessToCountry(Business business) {
    if (business == null) {
      return Country.UNDEFINED;
    }
    return switch (business) {
      case Brazil, CPNBrazil -> Country.BRAZIL;
      case Canada -> Country.CANADA;
      case France, CPNFrance, Neolait_FRANCE, Provimi_FRANCE -> Country.FRANCE;
      case Netherlands -> Country.NETHERLANDS;
      case Philippines -> Country.PHILIPPINES;
      case Poland, CPNPoland -> Country.POLAND;
      case US, CPNUS, UNITED_STATES, NorthAmerica -> Country.UNITED_STATES;
      case Vietnam -> Country.VIETNAM;
      case Spain -> Country.SPAIN;
      case Italy -> Country.ITALY;
      case Korea -> Country.KOREA;
      case India, CFNIndia -> Country.INDIA;
      case Mexico -> Country.MEXICO;
      case Russia -> Country.RUSSIA;
      case SouthAfrica -> Country.SOUTH_AFRICA;
      case China, CFNChina -> Country.CHINA;
      case Portugal -> Country.PORTUGAL;
      case Ukraine -> Country.UKRAINE;
      case Hungary -> Country.HUNGARY;
      case UK -> Country.UNITED_KINGDOM;
      case Pakistan -> Country.PAKISTAN;
      case Argentina -> Country.ARGENTINA;
      default -> Country.UNDEFINED;
    };
  }
}
