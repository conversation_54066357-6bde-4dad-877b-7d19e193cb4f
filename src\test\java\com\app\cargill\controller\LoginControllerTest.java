/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.service.ILoginService;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class LoginControllerTest {

  private ILoginService loginServiceImpl;
  private LoginController controller;

  @BeforeEach
  void setUp() {
    loginServiceImpl = mock(ILoginService.class);
    controller = new LoginController(loginServiceImpl);
  }

  /*@Test
  void whenFetchTokenWithOktaIsSuccessfulReturnUser()
      throws MalformedURLException, JwtVerificationException {
    when(loginServiceImpl.fetchAccessTokenByOktaCode(any(), any())).thenReturn(new Oauth2Dto());
    ResponseEntity<ResponseEntityDto<Object>> result = controller.fetchTokenByOktaCode("c", "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }*/

  /*@Test
  void whenUserNotLoggedInOktaCorrectExceptionIsThrown()
      throws MalformedURLException, JwtVerificationException {
    when(loginServiceImpl.fetchAccessTokenByOktaCode(any(), any()))
        .thenThrow(JwtVerificationException.class);
    Assertions.assertThrows(
        UnauthorizedUserDEException.class, () -> controller.fetchTokenByOktaCode("c", "s"));
  }

  @Test
  void whenMalformedTokenInOktaExceptionIsThrown()
      throws MalformedURLException, JwtVerificationException {
    when(loginServiceImpl.fetchAccessTokenByOktaCode(any(), any()))
        .thenThrow(MalformedURLException.class);
    Assertions.assertThrows(
        RuntimeException.class, () -> controller.fetchTokenByOktaCode("c", "s"));
  }

  @Test
  void whenFetchTokenByAzureIsSuccessfulReturnUser() throws Exception {
    when(loginServiceImpl.fetchAccessTokenByAzureAD(any(), any())).thenReturn(new Oauth2Dto());
    ResponseEntity<ResponseEntityDto<Object>> result =
        controller.fetchAccessTokenByAzureAD("c", "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void whenUserNotLoggedInAzureCorrectExceptionIsThrown() throws Exception {
    when(loginServiceImpl.fetchAccessTokenByAzureAD(any(), any()))
        .thenThrow(JwtVerificationException.class);
    Assertions.assertThrows(
        UnauthorizedUserDEException.class, () -> controller.fetchAccessTokenByAzureAD("c", "s"));
  }

  @Test
  void whenExceptionInAzureExceptionIsThrown() throws Exception {
    when(loginServiceImpl.fetchAccessTokenByAzureAD(any(), any())).thenThrow(Exception.class);
    Assertions.assertThrows(
        RuntimeException.class, () -> controller.fetchAccessTokenByAzureAD("c", "s"));
  }

  @Test
  void whenFetchTokenIsSuccessfulReturnUser() throws Exception {
    when(loginServiceImpl.fetchAccessToken(any(), any())).thenReturn(new Oauth2Dto());
    ResponseEntity<ResponseEntityDto<Object>> result = controller.fetchAccessToken("c", "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void whenFetchTokenFailsExceptionIsThrown() throws Exception {
    when(loginServiceImpl.fetchAccessToken(any(), any())).thenThrow(Exception.class);
    Assertions.assertThrows(
        UnauthorizedUserDEException.class, () -> controller.fetchAccessToken("c", "s"));
  }*/

  @Test
  void onLogoutCorrectResponseIsReturned() {
    when(loginServiceImpl.logout()).thenReturn(true);
    ResponseEntity<ResponseEntityDto<Boolean>> result = controller.logout();
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertTrue(result.getBody().getData());
  }
}
