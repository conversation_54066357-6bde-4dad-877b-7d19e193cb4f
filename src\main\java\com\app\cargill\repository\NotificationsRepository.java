/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Notifications;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface NotificationsRepository extends JpaRepository<Notifications, Long> {

  @Query(
      value =
          "Select n.* from Notifications n WHERE"
              + " LOWER(n.notification_document->>'UserId')=LOWER(:currentLoggedInUser) AND"
              + " n.deleted= false AND timezone('UTC',n.updated_date) > :syncTime",
      nativeQuery = true)
  Page<Notifications> findByUserId(
      @Param("currentLoggedInUser") String currentLoggedUser,
      Pageable pageable,
      @Param("syncTime") Instant lastSyncTime);

  List<Notifications> findByLocalId(String localId);

  @Query(
      value =
          "Select n.* from Notifications n where n.notification_document->>'id' = :id and n.deleted"
              + " = false",
      nativeQuery = true)
  Notifications findByNotificationId(@Param("id") String id);

  @Query(
      value =
          "Select notification_document->>'id' from notifications where"
              + " notification_document->>'Type' = :type AND"
              + " notification_document->'Keys'->>'visitId' in (:visitIds) AND"
              + " LOWER(notification_document->>'UserId')=LOWER(:userId) AND deleted = false",
      nativeQuery = true)
  List<String> findNotificationIdsByTypeUserIdAndVisitIds(
      @Param("type") String type,
      @Param("visitIds") List<String> visitIds,
      @Param("userId") String userId);

  @Query(
      value =
          "Select notification_document->>'id' from notifications where"
              + " notification_document->>'Type' = :type AND"
              + " notification_document->'Keys'->>'noteId' in (:noteIds) AND"
              + " LOWER(notification_document->>'UserId')=LOWER(:userId) AND deleted = false",
      nativeQuery = true)
  List<String> findNotificationIdsByTypeUserIdAndNoteIds(
      @Param("type") String type,
      @Param("noteIds") List<String> noteIds,
      @Param("userId") String userId);

  @Query(
      value =
          "Select notification_document->>'id' from notifications where"
              + " notification_document->>'Type' = :type AND"
              + " LOWER(notification_document->>'UserId')=LOWER(:userId) AND deleted = false",
      nativeQuery = true)
  List<String> findNotificationIdsByTypeAndUserId(
      @Param("type") String type, @Param("userId") String userId);
}
