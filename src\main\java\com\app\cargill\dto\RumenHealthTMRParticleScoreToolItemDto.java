/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ToolStatuses;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class RumenHealthTMRParticleScoreToolItemDto extends EditableToolPenEntityBaseDto {

  /** */
  private static final long serialVersionUID = 1L;

  private List<UUID> visitsSelected;

  private Boolean isToolItemNew;

  private Double topScaleAmountInGrams;

  private Double topGoalMinimumPercent;

  private Double topGoalMaximumPercent;

  private Double topScreenTareAmountInGrams;

  private Double mid1ScaleAmountInGrams;

  private Double mid1GoalMinimumPercent;

  private Double mid1GoalMaximumPercent;

  private Double mid1ScreenTareAmountInGrams;

  private Double mid2ScaleAmountInGrams;

  private Double mid2GoalMinimumPercent;

  private Double mid2GoalMaximumPercent;

  private Double mid2ScreenTareAmountInGrams;

  private Double trayScaleAmountInGrams;

  private Double trayGoalMinimumPercent;

  private Double trayGoalMaximumPercent;

  private Double trayScreenTareAmountInGrams;

  private ToolStatuses toolStatus;

  private Integer daysInMilk;

  private Boolean isFirstTimeWithScore;

  private String tmrScoreName;

  private String tmrScoreId;
}
