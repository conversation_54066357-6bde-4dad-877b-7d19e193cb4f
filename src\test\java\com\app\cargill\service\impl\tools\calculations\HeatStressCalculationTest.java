/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.app.cargill.document.HeatStressTool;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class HeatStressCalculationTest {

  @InjectMocks private HeatStressCalculation heatStressCalculation;

  @Test
  void calculateFields() {
    HeatStressTool heatStressTool =
        HeatStressTool.builder()
            .milkValueLossPerDay(10.2)
            .avgCurrentMilkPrice(30.2)
            .energyEquivalentMilkLossWeightInkg(5.1)
            .temperatureInCelsius(34.2)
            .humidityPercent(12.2)
            .hoursExposedToSun(3)
            .intakeAdjustmentPercent(31.5)
            .dmiReductionPercent(21.1)
            .estimatedDryMatterIntakeWeightInkg(2.1)
            .reductionInDMIWeightInkg(12.3)
            .lossOfEnergyConsumedInMcal(12.1)
            .energyEquivalentMilkLossWeightInkg(5.1)
            .avgLactatingAnimals(2.1)
            .build();

    heatStressTool = heatStressCalculation.calculateFields(heatStressTool);
    assertNotNull(heatStressTool.getMilkValueLossPerDay());
    assertNotNull(heatStressTool.getTemperatureInCelsius());
    assertNotNull(heatStressTool.getIntakeAdjustmentPercent());
    assertNotNull(heatStressTool.getDmiReductionPercent());
    assertNotNull(heatStressTool.getEstimatedDryMatterIntakeWeightInkg());
    assertNotNull(heatStressTool.getReductionInDMIWeightInkg());
    assertNotNull(heatStressTool.getLossOfEnergyConsumedInMcal());
    assertNotNull(heatStressTool.getEnergyEquivalentMilkLossWeightInkg());
  }

  @Test
  void calculateFieldsWithNullValues() {
    HeatStressTool heatStressTool = HeatStressTool.builder().build();

    heatStressTool = heatStressCalculation.calculateFields(heatStressTool);
    Assertions.assertNull(heatStressTool.getMilkValueLossPerDay());
    Assertions.assertNull(heatStressTool.getTemperatureInCelsius());
    Assertions.assertNull(heatStressTool.getIntakeAdjustmentPercent());
    Assertions.assertNull(heatStressTool.getDmiReductionPercent());
    Assertions.assertNull(heatStressTool.getEstimatedDryMatterIntakeWeightInkg());
    Assertions.assertNull(heatStressTool.getReductionInDMIWeightInkg());
    Assertions.assertNull(heatStressTool.getLossOfEnergyConsumedInMcal());
    Assertions.assertNull(heatStressTool.getEnergyEquivalentMilkLossWeightInkg());
  }

  @Test
  void validateCalculations() {
    HeatStressTool heatStressTool =
        HeatStressTool.builder()
            .avgMilkWeightInkg(19.0)
            .avgDMIWeightInkg(17.0)
            .avgNELWeightInkg(1.51)
            .avgMilkFatPercent(3.6)
            .avgMilkProteinPercent(3.1)
            .temperatureInCelsius(41.0)
            .humidityPercent(65.0)
            .hoursExposedToSun(5)
            .temperatureHumidityInCelsius(38.05711111111111)
            .intakeAdjustmentPercent(83.26688666666666)
            .dmiReductionPercent(16.733113333333332)
            .estimatedDryMatterIntakeWeightInkg(14.155370733333333)
            .reductionInDMIWeightInkg(2.844629266666667)
            .lossOfEnergyConsumedInMcal(4.295390192666667)
            .energyEquivalentMilkLossWeightInkg(6.171448962898043)
            .avgLactatingAnimals(29.0)
            .avgCurrentMilkPrice(41.0)
            .build();

    heatStressTool = heatStressCalculation.calculateFields(heatStressTool);

    assertEquals(6.2, heatStressTool.energyEquivalentMilkLossWeightInkg);
    assertEquals(7337.9, heatStressTool.milkValueLossPerDay);
    assertEquals(38.1, heatStressTool.temperatureHumidityInCelsius);
    assertEquals(83.3, heatStressTool.intakeAdjustmentPercent);
    assertEquals(16.7, heatStressTool.dmiReductionPercent);
    assertEquals(14.2, heatStressTool.estimatedDryMatterIntakeWeightInkg);
    assertEquals(2.8, heatStressTool.reductionInDMIWeightInkg);
    assertEquals(4.30, heatStressTool.lossOfEnergyConsumedInMcal);
  }
}
