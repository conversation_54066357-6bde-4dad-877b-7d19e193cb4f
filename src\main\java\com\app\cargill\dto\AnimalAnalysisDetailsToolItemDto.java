/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimalAnalysisDetailsToolItemDto implements Serializable {

  private UUID penId;

  private UUID earTagId;

  private Double bcsCategory;

  private Double locomotionScore;

  private Integer daysInMilk;

  private List<UUID> visitsSelected;

  private Boolean isToolItemNew;
}
