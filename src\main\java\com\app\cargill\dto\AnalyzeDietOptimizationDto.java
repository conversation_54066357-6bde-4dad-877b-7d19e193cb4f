/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.AnalyzeStatus;
import com.app.cargill.document.AnalyzeDietOptimization;
import com.app.cargill.document.NelDaiKg;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnalyzeDietOptimizationDto {

  private AnalyzeStatus status;
  private NelDaiKg nELDAIKG;
  private Double asFedAmount;
  private Double dryMatterAmount;
  private Double totalCost;
  private Integer optimizationId;

  public static AnalyzeDietOptimizationDto mapAnalyzeDietOptimizationToDto(
      AnalyzeDietOptimization analyzeDietOptimization) {

    if (analyzeDietOptimization != null) {
      return AnalyzeDietOptimizationDto.builder()
          .optimizationId(analyzeDietOptimization.getOptimizationId())
          .totalCost(analyzeDietOptimization.getTotalCost())
          .dryMatterAmount(analyzeDietOptimization.getDryMatterAmount())
          .asFedAmount(analyzeDietOptimization.getAsFedAmount())
          .nELDAIKG(analyzeDietOptimization.getNELDAIKG())
          .status(analyzeDietOptimization.getStatus())
          .build();
    }
    return null;
  }
}
