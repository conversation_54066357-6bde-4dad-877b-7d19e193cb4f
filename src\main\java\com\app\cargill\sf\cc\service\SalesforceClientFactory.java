/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

@Component
@Slf4j
@RequiredArgsConstructor
public class SalesforceClientFactory {

  @Value("${app.debug.web-client:false}")
  private boolean loggingEnabled;

  private final WebClient.Builder builder;

  @PostConstruct
  public void init() {
    if (loggingEnabled) {
      builder.filter(WebClientLogFilter.logRequest());
      log.debug("WebClient Logging enabled");
    }
  }

  public WebClient createClient() {
    return createClient(null);
  }

  public WebClient createClient(String baseUrl) {
    if (baseUrl != null && !baseUrl.isEmpty()) {
      builder.baseUrl(baseUrl);
    } else {
      builder.baseUrl(null);
    }
    return builder.build();
  }
}
