/* Cargill Inc.(C) 2022 */
package com.app.cargill;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import io.swagger.v3.oas.models.Operation;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.method.HandlerMethod;

@ExtendWith(MockitoExtension.class)
class OpenApiConfigurationTest {
  @Mock HandlerMethod method;
  @Mock Operation operation;
  @InjectMocks OpenApiConfiguration openApiConfiguration;

  @Test
  void contextLoads() {
    assertDoesNotThrow(
        () -> {
          openApiConfiguration.customize(operation, method);
        });
  }
}
