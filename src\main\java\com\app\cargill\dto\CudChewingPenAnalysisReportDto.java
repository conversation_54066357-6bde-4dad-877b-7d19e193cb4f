/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CudChewingPenAnalysisReportDto extends BaseDto {

  private String fileName;
  private String visitName;
  private String visitDate;
  private String toolName;
  private String analysisType;
  private String penName;
  private boolean isNoOfChews;
  private String standardDeviationLabel;
  private Double standardDeviation;

  @Builder.Default
  private List<CudChewingPenAnalysisChewingDto> chewingPercentages = new ArrayList<>();
}
