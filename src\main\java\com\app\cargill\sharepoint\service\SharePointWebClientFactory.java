/* Cargill Inc.(C) 2022 */
package com.app.cargill.sharepoint.service;

import com.app.cargill.sf.cc.service.WebClientLogFilter;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

@Component
@Slf4j
@RequiredArgsConstructor
public class SharePointWebClientFactory {

  @Value("${app.debug.web-client:false}")
  private boolean sharePointLoggingEnabled;

  private final WebClient.Builder webClientBuilder;

  @PostConstruct
  public void init() {
    if (sharePointLoggingEnabled) {
      webClientBuilder.filter(WebClientLogFilter.logRequest());
      log.debug("SharePoint WebClient Logging enabled");
    }
  }

  public WebClient createWebClient() {
    return createWebClient(null);
  }

  public WebClient createWebClient(String baseUrl) {
    if (baseUrl != null && !baseUrl.isEmpty()) {
      webClientBuilder.baseUrl(baseUrl);
    }
    return webClientBuilder.build();
  }
}
