/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto.cdp.visit;

import com.app.cargill.constants.Currencies;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.converter.EpochDateTimeSerializer;
import com.app.cargill.document.EditableDocumentBase;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class VisitDocumentDTO extends EditableDocumentBase implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("AccountId")
  private UUID accountId;

  @JsonProperty("SiteId")
  private UUID siteId;

  @JsonProperty("VisitDate")
  @JsonSerialize(using = EpochDateTimeSerializer.class)
  private Instant visitDate;

  @JsonProperty("Status")
  private VisitStatus status;

  @JsonProperty("VisitName")
  private String visitName;

  @JsonProperty("FormattedCreationDate")
  private String formattedCreationDate;

  @JsonProperty("Attributes")
  private List<AttributesDTO> attributes;

  @JsonProperty("SelectedCurrency")
  private Currencies selectedCurrency;
}
