/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.constants.FeedStorageType;
import com.app.cargill.document.PileAndBunker;
import com.app.cargill.document.PileAndBunkerTool;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ForageInventoriesCalculationTest {

  @InjectMocks private ForageInventoriesCalculation forageInventoriesCalculation;

  @Test
  void calculateForageInventoriesFields() {

    PileAndBunkerTool tool =
        PileAndBunkerTool.builder()
            .pileBunkers(
                List.of(
                    getTool(FeedStorageType.Pile),
                    getTool(FeedStorageType.TopUnloadingSilo),
                    getTool(FeedStorageType.BottomUnloadingSilo)))
            .build();

    tool = forageInventoriesCalculation.calculateFields(tool, Instant.now());
    PileAndBunker pile = tool.getPileBunkers().get(0);
    PileAndBunker top = tool.getPileBunkers().get(1);
    PileAndBunker bottom = tool.getPileBunkers().get(2);

    Assertions.assertEquals(0.0, pile.tonnesAFSilo);
    Assertions.assertNotNull(top.tonnesAFSilo);
    Assertions.assertNotNull(bottom.tonnesAFSilo);
  }

  @Test
  void calculateForageInventoriesFieldsWithNull() {
    PileAndBunkerTool tool =
        PileAndBunkerTool.builder()
            .pileBunkers(
                List.of(PileAndBunker.builder().isPileOrBunker(FeedStorageType.Pile).build()))
            .build();

    tool = forageInventoriesCalculation.calculateFields(tool, Instant.now());

    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesPerDay());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesOfDryMatter());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesAsFed());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesDMSilo());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getCentimetersPerDay());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesAFSilo());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesDMBag());
    Assertions.assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesAFBag());
  }

  private PileAndBunker getTool(FeedStorageType type) {
    return PileAndBunker.builder()
        .tonnesAsFed(2.0)
        .cowsToBeFed(10)
        .feedOutInclusionRate(80.0)
        .tonnesOfDryMatter(5.0)
        .isPileOrBunker(type)
        .heightInMeters(3.0)
        .bottomWidthInMeters(1.5)
        .topWidthInMeters(1.5)
        .dryMatterPercentageSilo(10.0)
        .diameterBagInMeters(0.5)
        .lengthInMeters(1.75)
        .silageDMDensityBagKgPerMeter(0.2)
        .dryMatterPercentageSilo(20.0)
        .filledHeightInMeters(2.5)
        .diameterInMeters(1.75)
        .silageLeftInMeters(0.5)
        .dryMatterPercentageBag(0.6)
        .tonnesDMBag(0.5)
        .build();
  }

  @SuppressWarnings("java:S5961")
  @Test
  void verifyCalculations() {
    PileAndBunkerTool tool =
        PileAndBunkerTool.builder()
            .pileBunkers(
                List.of(
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.TopUnloadingSilo)
                        .createTimeUtc(Instant.parse("2023-04-19T21:24:13.684177Z"))
                        .cowsToBeFed(37)
                        .feedOutInclusionRate(15.8)
                        .tonnesAsFed(40.4420940800784)
                        .footPrintArea(20.0)
                        .tonnesAsFedPerMeterSquaredFootPrintArea(2.02210470400392)
                        .feedOutSurfaceAreaMetersSquared(23.7582940375)
                        .cowsPerDayNeededAtLowerFeedRate(86)
                        .cowsPerDayNeededAtHigherFeedRate(184)
                        .kilogramsDryMatterInOneMeter(9299.0)
                        .filledHeightInMeters(20.0)
                        .diameterInMeters(5.5)
                        .silageLeftInMeters(1.0)
                        .dryMatterPercentageSilo(48.0)
                        .build(),
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.TopUnloadingSilo)
                        .createTimeUtc(Instant.parse("2023-04-19T21:29:53.677645Z"))
                        .cowsToBeFed(60)
                        .feedOutInclusionRate(12.0)
                        .tonnesAsFed(133.07279550981)
                        .footPrintArea(30.0)
                        .tonnesAsFedPerMeterSquaredFootPrintArea(4.43575985032698)
                        .feedOutSurfaceAreaMetersSquared(29.2246651615)
                        .cowsPerDayNeededAtLowerFeedRate(186)
                        .cowsPerDayNeededAtHigherFeedRate(399)
                        .kilogramsDryMatterInOneMeter(11468.0)
                        .filledHeightInMeters(20.0)
                        .diameterInMeters(6.1)
                        .silageLeftInMeters(1.5)
                        .dryMatterPercentageSilo(35.9)
                        .build(),
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bag)
                        .createTimeUtc(Instant.parse("2023-04-19T21:36:21.350174Z"))
                        .cowsToBeFed(78)
                        .feedOutInclusionRate(6.0)
                        .tonnesAsFed(65.9509202453988)
                        .footPrintArea(27.36)
                        .tonnesAsFedPerMeterSquaredFootPrintArea(2.41048685107452)
                        .feedOutSurfaceAreaMetersSquared(2.544690006)
                        .cowsPerDayNeededAtLowerFeedRate(16)
                        .cowsPerDayNeededAtHigherFeedRate(35)
                        .kilogramsDryMatterInOneMeter(458.0)
                        .lengthInMeters(15.2)
                        .diameterBagInMeters(1.8)
                        .dryMatterPercentageBag(32.6)
                        .silageDMDensityBagKgPerMeter(180.0)
                        .silageAsFedDensityBag(552.0)
                        .build(),
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bag)
                        .createTimeUtc(Instant.parse("2023-04-19T21:36:21.350174Z"))
                        .cowsToBeFed(78)
                        .feedOutInclusionRate(6.0)
                        .tonnesAsFed(34.6625766871166)
                        .footPrintArea(12.6)
                        .tonnesAsFedPerMeterSquaredFootPrintArea(2.75099814977116)
                        .feedOutSurfaceAreaMetersSquared(2.544690006)
                        .cowsPerDayNeededAtLowerFeedRate(19)
                        .cowsPerDayNeededAtHigherFeedRate(40)
                        .kilogramsDryMatterInOneMeter(521.7)
                        .lengthInMeters(7.0)
                        .diameterBagInMeters(1.8)
                        .dryMatterPercentageBag(32.6)
                        .silageDMDensityBagKgPerMeter(205.0)
                        .silageAsFedDensityBag(629.0)
                        .build(),
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bag)
                        .createTimeUtc(Instant.parse("2023-04-19T21:44:42.407731Z"))
                        .cowsToBeFed(36)
                        .feedOutInclusionRate(8.1)
                        .tonnesAsFed(29.9290780141844)
                        .footPrintArea(23.4)
                        .tonnesAsFedPerMeterSquaredFootPrintArea(1.2790204279566)
                        .feedOutSurfaceAreaMetersSquared(2.544690006)
                        .cowsPerDayNeededAtLowerFeedRate(14)
                        .cowsPerDayNeededAtHigherFeedRate(30)
                        .kilogramsDryMatterInOneMeter(1142.6)
                        .lengthInMeters(13.0)
                        .diameterBagInMeters(1.8)
                        .dryMatterPercentageBag(70.5)
                        .silageDMDensityBagKgPerMeter(449.0)
                        .silageAsFedDensityBag(637.0)
                        .build(),
                    PileAndBunker.builder()
                        .isPileOrBunker(FeedStorageType.Bag)
                        .createTimeUtc(Instant.parse("2023-07-04T20:03:43.157341Z"))
                        .cowsToBeFed(36)
                        .feedOutInclusionRate(20.0)
                        .tonnesAsFed(128.011204481793)
                        .footPrintArea(54.9)
                        .tonnesAsFedPerMeterSquaredFootPrintArea(2.3317159286301)
                        .feedOutSurfaceAreaMetersSquared(2.544690006)
                        .cowsPerDayNeededAtLowerFeedRate(5)
                        .cowsPerDayNeededAtHigherFeedRate(11)
                        .kilogramsDryMatterInOneMeter(534.4)
                        .lengthInMeters(30.5)
                        .diameterBagInMeters(1.8)
                        .dryMatterPercentageBag(35.7)
                        .silageDMDensityBagKgPerMeter(210.0)
                        .silageAsFedDensityBag(588.0)
                        .build()))
            .build();

    // For Pen 1

    Instant day = Instant.parse("2023-07-04T19:37:23.065206Z");
    tool = forageInventoriesCalculation.calculateFields(tool, day);

    assertEquals(
        "9/11/2023 7:37:23 PM", convertInstantToString(tool.getPileBunkers().get(0).getDateGone()));

    assertEquals(0.6, tool.getPileBunkers().get(0).getTonnesPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesOfDryMatter());
    assertEquals(40.0, tool.getPileBunkers().get(0).getTonnesAsFed());
    assertEquals(0.0, tool.getPileBunkers().get(0).getCentimetersPerDay());
    assertEquals(19.4, tool.getPileBunkers().get(0).getTonnesAFSilo());
    assertEquals(9.3, tool.getPileBunkers().get(0).getTonnesDMSilo());
    assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesDMBag());
    assertEquals(0.0, tool.getPileBunkers().get(0).getTonnesAFBag());

    // For Pen 2
    assertEquals(
        "1/5/2024 7:37:23 PM", convertInstantToString(tool.getPileBunkers().get(1).getDateGone()));

    assertEquals(0.7, tool.getPileBunkers().get(1).getTonnesPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(1).getTonnesOfDryMatter());
    assertEquals(133.0, tool.getPileBunkers().get(1).getTonnesAsFed());
    assertEquals(0.0, tool.getPileBunkers().get(1).getCentimetersPerDay());
    assertEquals(47.8, tool.getPileBunkers().get(1).getTonnesAFSilo());
    assertEquals(17.2, tool.getPileBunkers().get(1).getTonnesDMSilo());
    assertEquals(0.0, tool.getPileBunkers().get(1).getTonnesDMBag());
    assertEquals(0.0, tool.getPileBunkers().get(1).getTonnesAFBag());

    // For Pen 3
    assertEquals(
        "11/22/2023 7:37:23 PM",
        convertInstantToString(tool.getPileBunkers().get(2).getDateGone()));

    assertEquals(0.5, tool.getPileBunkers().get(2).getTonnesPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(2).getTonnesOfDryMatter());
    assertEquals(66.0, tool.getPileBunkers().get(2).getTonnesAsFed());
    assertEquals(0.0, tool.getPileBunkers().get(2).getCentimetersPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(2).getTonnesAFSilo());
    assertEquals(0.0, tool.getPileBunkers().get(2).getTonnesDMSilo());
    assertEquals(7.0, tool.getPileBunkers().get(2).getTonnesDMBag());
    assertEquals(21.5, tool.getPileBunkers().get(2).getTonnesAFBag());

    // For Pen 4

    assertEquals(
        "9/16/2023 7:37:23 PM", convertInstantToString(tool.getPileBunkers().get(3).getDateGone()));

    assertEquals(0.5, tool.getPileBunkers().get(3).getTonnesPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(3).getTonnesOfDryMatter());
    assertEquals(35.0, tool.getPileBunkers().get(3).getTonnesAsFed());
    assertEquals(0.0, tool.getPileBunkers().get(3).getCentimetersPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(3).getTonnesAFSilo());
    assertEquals(0.0, tool.getPileBunkers().get(3).getTonnesDMSilo());
    assertEquals(3.7, tool.getPileBunkers().get(3).getTonnesDMBag());
    assertEquals(11.3, tool.getPileBunkers().get(3).getTonnesAFBag());

    // For Pen 5

    assertEquals(
        "10/15/2023 7:37:23 PM",
        convertInstantToString(tool.getPileBunkers().get(4).getDateGone()));

    assertEquals(0.3, tool.getPileBunkers().get(4).getTonnesPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(4).getTonnesOfDryMatter());
    assertEquals(30.0, tool.getPileBunkers().get(4).getTonnesAsFed());
    assertEquals(0.0, tool.getPileBunkers().get(4).getCentimetersPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(4).getTonnesAFSilo());
    assertEquals(0.0, tool.getPileBunkers().get(4).getTonnesDMSilo());
    assertEquals(14.9, tool.getPileBunkers().get(4).getTonnesDMBag());
    assertEquals(21.1, tool.getPileBunkers().get(4).getTonnesAFBag());

    // For Pen 6

    assertEquals(
        "12/29/2023 7:37:23 PM",
        convertInstantToString(tool.getPileBunkers().get(5).getDateGone()));

    assertEquals(0.7, tool.getPileBunkers().get(5).getTonnesPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(5).getTonnesOfDryMatter());
    assertEquals(128.0, tool.getPileBunkers().get(5).getTonnesAsFed());
    assertEquals(0.0, tool.getPileBunkers().get(5).getCentimetersPerDay());
    assertEquals(0.0, tool.getPileBunkers().get(5).getTonnesAFSilo());
    assertEquals(0.0, tool.getPileBunkers().get(5).getTonnesDMSilo());
    assertEquals(16.3, tool.getPileBunkers().get(5).getTonnesDMBag());
    assertEquals(45.7, tool.getPileBunkers().get(5).getTonnesAFBag());
  }

  private String convertInstantToString(Instant dateTime) {

    // Convert the Instant to a ZonedDateTime in a specific time zone
    ZoneId zoneId = ZoneId.of("UTC"); // or your desired time zone
    ZonedDateTime zonedDateTime = dateTime.atZone(zoneId);

    // Create a custom DateTimeFormatter for the desired output format
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d/yyyy h:mm:ss a");

    // Format the ZonedDateTime using the custom formatter
    return zonedDateTime.format(formatter).toUpperCase();
  }
}
