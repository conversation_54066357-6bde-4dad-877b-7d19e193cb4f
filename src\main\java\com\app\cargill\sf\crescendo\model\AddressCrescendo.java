/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AddressCrescendo implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("Street")
  private String street;

  @JsonProperty("City")
  private String city;

  @JsonProperty("StateOrProvince")
  private String stateOrProvince;

  @JsonProperty("PostalCode")
  private String postalCode;

  @JsonProperty("Country")
  private String country;

  @JsonProperty("AddressID")
  private String addressID = "00000000-0000-0000-0000-000000000000";

  @JsonProperty("CountyCommunity")
  private String countyCommunity;
}
