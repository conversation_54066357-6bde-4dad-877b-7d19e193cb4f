/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.Countries;
import java.time.Instant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CountriesRepository extends JpaRepository<Countries, Long> {

  @Query(
      value =
          "Select c.* FROM countries c where c.updated_date > :lastSyncTime AND c.deleted=false",
      nativeQuery = true)
  Page<Countries> findAllByUpdatedDate(
      @Param("lastSyncTime") Instant lastSyncTime, Pageable pageable);
}
