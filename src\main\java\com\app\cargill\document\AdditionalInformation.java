/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdditionalInformation implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ID")
  public UUID id;

  @JsonProperty("PaymentTerms")
  public String paymentTerms;

  @JsonProperty("VAT")
  public String vat;

  @JsonProperty("DUNS")
  public String duns;

  @JsonProperty("CANBusinessID")
  public Integer canBusinessID;

  @JsonProperty("StatisticalID")
  public String statisticalId;

  @JsonProperty("CustomerServiceNotes")
  public String customerServiceNotes;

  @JsonProperty("VATFarmer")
  public Boolean vatFarmer;
}
