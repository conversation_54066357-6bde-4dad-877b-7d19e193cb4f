/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.app.cargill.constants.SalesforceAccountType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class AccountTypeMapperTest {
  @Test
  void when0IsPassedProspectIsReturned() {
    assertEquals(SalesforceAccountType.PROSPECT, AccountTypeMapper.map(0));
  }

  @Test
  void when1IsPassedCustomerIsReturned() {
    assertEquals(SalesforceAccountType.CUSTOMER, AccountTypeMapper.map(1));
  }

  @Test
  void when2IsPassedThirdPartyIsReturned() {
    assertEquals(SalesforceAccountType.THIRD_PARTY, AccountTypeMapper.map(2));
  }

  @Test
  void when3IsPassedConsumerIsReturned() {
    assertEquals(SalesforceAccountType.CONSUMER, AccountTypeMapper.map(3));
  }

  @Test
  void when4IsPassedCompetitorIsReturned() {
    assertEquals(SalesforceAccountType.COMPETITOR, AccountTypeMapper.map(4));
  }

  @Test
  void whenNullIsPassedExceptionIsThrown() {
    Assertions.assertThrows(IllegalArgumentException.class, () -> AccountTypeMapper.map(null));
  }

  @Test
  void whenUknownIsPassedExceptionIsThrown() {
    Assertions.assertThrows(IllegalArgumentException.class, () -> AccountTypeMapper.map(5));
  }
}
