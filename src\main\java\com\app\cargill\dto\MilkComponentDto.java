/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MilkComponentDto implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private Double pricePerKg;
  private Double percentagePerHl;
  private Double kgPerCow;
}
