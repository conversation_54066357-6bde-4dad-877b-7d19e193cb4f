/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.RumenHealthManureScreeningOnScreenPercentageDto;
import com.app.cargill.dto.RumenHealthManureScreeningPenAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("rumenHealthManureScreeningPenAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class RumenHealthManureScreeningPenAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;
  private List<byte[]> penAnalysisManureScreeningColors =
      Arrays.asList(
          new byte[] {(byte) 122, (byte) 216, (byte) 220},
          new byte[] {(byte) 131, (byte) 190, (byte) 244},
          new byte[] {(byte) 171, (byte) 161, (byte) 227},
          new byte[] {(byte) 171, (byte) 161, (byte) 227},
          new byte[] {(byte) 131, (byte) 190, (byte) 244},
          new byte[] {(byte) 122, (byte) 216, (byte) 220});

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object penAnalysisManureScreeningData, ResourceBundleMessageSource source, Locale locale)
      throws IOException {
    this.source = source;
    RumenHealthManureScreeningPenAnalysisReportDto dto =
        modelMapper.map(
            penAnalysisManureScreeningData, RumenHealthManureScreeningPenAnalysisReportDto.class);
    try (XSSFWorkbook rumenHealthManureScreeningPenAnalysisWB = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              rumenHealthManureScreeningPenAnalysisWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(
                  rumenHealthManureScreeningPenAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              rumenHealthManureScreeningPenAnalysisWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  rumenHealthManureScreeningPenAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              rumenHealthManureScreeningPenAnalysisWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  rumenHealthManureScreeningPenAnalysisWB, false, true, IndexedColors.BLACK));

      // rumenHealthTmrParticleScore
      XSSFSheet sheet =
          addRumenHealthManureScreeningPenAnalysisSheet(
              rumenHealthManureScreeningPenAnalysisWB,
              source,
              locale,
              dto,
              boldStyle,
              greyCellStyle,
              centerBlack);
      int totalSheetColumns = sheet.getLastRowNum();
      return ExcelUtils.finalizeWorkbook(
          rumenHealthManureScreeningPenAnalysisWB, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  private XSSFSheet addRumenHealthManureScreeningPenAnalysisSheet(
      XSSFWorkbook rumenHealthManureScreeningPenAnalysisWB,
      ResourceBundleMessageSource source,
      Locale locale,
      RumenHealthManureScreeningPenAnalysisReportDto rumenHealthManureScreeningPenAnalysisDto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {
    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(
            rumenHealthManureScreeningPenAnalysisWB,
            false,
            IndexedColors.BLACK,
            HorizontalAlignment.CENTER);

    XSSFSheet rumenHealthManureScreeningWBSheet =
        rumenHealthManureScreeningPenAnalysisWB.createSheet(
            ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, "Report", null, source, locale)
                .replace("/", "⧸"));
    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    prepareHeader(
        rumenHealthManureScreeningPenAnalysisWB,
        rumenHealthManureScreeningWBSheet,
        rowNumber,
        cellNumber,
        rumenHealthManureScreeningPenAnalysisDto,
        boldStyle,
        locale);

    // create the data
    // calculated table heading
    cellNumber.set(0);
    XSSFRow row = rumenHealthManureScreeningWBSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        greyCellStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, "Top", null, source, locale));
    rumenHealthManureScreeningWBSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

    // setting dates  first
    int penNameStartRowNumber = rowNumber.get();
    cellNumber.set(1);
    row = rumenHealthManureScreeningWBSheet.createRow(rowNumber.getAndIncrement());

    for (RumenHealthManureScreeningOnScreenPercentageDto onScreenPercentage :
        rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {
      // set pen name in first cell
      ExcelUtils.highlightEmptyCell(
          row, onScreenPercentage.getVisitDate(), cellNumber, centerBlack, greyCellStyle);
    }

    // rumen health tmr particle score herd analysis

    int onScreenPercentageStartRowNumber = rowNumber.get();
    // first rest cell and create row
    int goalSize = 9;

    for (int goalIndex = 0; goalIndex < goalSize; goalIndex++) {
      cellNumber.set(0);
      row = rumenHealthManureScreeningWBSheet.createRow(rowNumber.getAndIncrement());
      if (goalIndex == 0) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_TOP, "Top", null, source, locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getTop(), cellNumber, decimalStyle, greyCellStyle);
        }

      } else if (goalIndex == 1) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_MIDDLE,
                "Middle",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getMiddle(), cellNumber, decimalStyle, greyCellStyle);
        }

      } else if (goalIndex == 2) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_BOTTOM,
                "Bottom",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getBottom(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 3) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_TOP_GOAL_MIN,
                "Top Goal Min",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getTopGoalMin(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 4) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_TOP_GOAL_MAX,
                "Top Goal Max",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getTopGoalMax(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 5) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_MIDDLE_GOAL_MIN,
                "Middle Goal Min",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getMiddleGoalMin(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 6) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_MIDDLE_GOAL_MAX,
                "Middle Goal Max",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getMiddleGoalMax(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 7) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_BOTTOM_GOAL_MIN,
                "Bottom Goal Min",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getBottomGoalMin(), cellNumber, decimalStyle, greyCellStyle);
        }
      } else if (goalIndex == 8) {
        ExcelUtils.createAndSetCellValue(
            row,
            cellNumber,
            centerBlack,
            ExcelUtils.getLangValue(
                LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING_BOTTOM_GOAL_MAX,
                "Bottom Goal Max",
                null,
                source,
                locale));
        for (RumenHealthManureScreeningOnScreenPercentageDto value :
            rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage()) {

          ExcelUtils.highlightEmptyCell(
              row, value.getBottomGoalMax(), cellNumber, decimalStyle, greyCellStyle);
        }
      }
    }

    // create data sources
    // y0 axis
    int columnStart = 1;
    int columnEnd =
        columnStart + rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    XSSFChart chart;
    XDDFCategoryAxis bottomAxis;
    XDDFValueAxis leftAxis;
    XDDFBarChartData dataLeft;
    Series series;
    int chartCol0 = columnEnd + 1;
    // ===============first Bar chart======================
    chart =
        ExcelUtils.initChart(
            rumenHealthManureScreeningWBSheet,
            ExcelUtils.getLangValue(LangKeys.REPORT_ON_SCREEN_PERCENTAGE, null, source, locale),
            chartCol0,
            3,
            chartCol0
                + (rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage().size() > 10
                    ? rumenHealthManureScreeningPenAnalysisDto.getOnScreenPercentage().size()
                    : 10),
            23);

    ExcelUtils.initLegends(chart);

    bottomAxis = ExcelUtils.createBottomAxis(chart, "");

    leftAxis = ExcelUtils.createLeftAxis(chart, "");
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);
    // create chart data
    dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);
    // create series
    XDDFDataSource<String> penNameDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            rumenHealthManureScreeningWBSheet,
            new CellRangeAddress(
                penNameStartRowNumber, penNameStartRowNumber, columnStart, columnEnd));
    for (int ds = 0; ds < goalSize - 6; ds++) {
      XDDFNumericalDataSource<Double> onScreenPercentDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenHealthManureScreeningWBSheet,
              new CellRangeAddress(
                  onScreenPercentageStartRowNumber,
                  onScreenPercentageStartRowNumber,
                  columnStart,
                  columnEnd));
      series = (Series) dataLeft.addSeries(penNameDataSource, onScreenPercentDataSource);
      series.setTitle(
          rumenHealthManureScreeningWBSheet
              .getRow(onScreenPercentageStartRowNumber)
              .getCell(0)
              .getStringCellValue(),
          new CellReference(
              rumenHealthManureScreeningWBSheet.getSheetName(),
              onScreenPercentageStartRowNumber++,
              0,
              true,
              true));
    }
    chart.plot(dataLeft);
    ExcelUtils.setBarColorInBarChart(chart, penAnalysisManureScreeningColors, 3);

    return rumenHealthManureScreeningWBSheet;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    RumenHealthManureScreeningPenAnalysisReportDto mappedDto =
        modelMapper.map(data, RumenHealthManureScreeningPenAnalysisReportDto.class);

    Map<String, byte[]> imageTemplates = new HashMap<>();

    // create sheet 1
    byte[] rumenHealthManureScreening =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.RUMEN_HEALTH_MANURE_SCREENING_PEN_ANALYSIS_REPORT
                .getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            LangKeys.REPORT_RUMEN_HEALTH_MANURE_SCREENING,
            "Rumen Health Manure Screening",
            null,
            source,
            locale),
        rumenHealthManureScreening);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    RumenHealthManureScreeningPenAnalysisReportDto dto =
        modelMapper.map(data, RumenHealthManureScreeningPenAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.RUMEN_HEALTH_MANURE_SCREENING_PEN_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook rumenHealthManureScreeningPenAnalysisWB,
      XSSFSheet rumenHealthManureScreeningPenAnalysisSheet,
      AtomicInteger rowNum,
      AtomicInteger cellNum,
      RumenHealthManureScreeningPenAnalysisReportDto rumenHealthManureScreeningPenAnalysisReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        rumenHealthManureScreeningPenAnalysisWB,
        rumenHealthManureScreeningPenAnalysisSheet,
        rowNum.get(),
        cellNum.getAndIncrement());
    // headings
    XSSFRow row = rumenHealthManureScreeningPenAnalysisSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthManureScreeningPenAnalysisReportDto.getVisitName());
    rumenHealthManureScreeningPenAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthManureScreeningPenAnalysisReportDto.getVisitDate());
    rumenHealthManureScreeningPenAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));

    // second row
    cellNum.set(1);
    row = rumenHealthManureScreeningPenAnalysisSheet.createRow(rowNum.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthManureScreeningPenAnalysisReportDto.getToolName());
    rumenHealthManureScreeningPenAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthManureScreeningPenAnalysisReportDto.getAnalysisType());
    rumenHealthManureScreeningPenAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 5, 6));
    ExcelUtils.createAndSetCellValue(row, cellNum, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNum,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_PEN_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(
        row, cellNum, null, rumenHealthManureScreeningPenAnalysisReportDto.getPenName());
    rumenHealthManureScreeningPenAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNum.get() - 1, rowNum.get() - 1, 8, 9));
  }
}
