/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.app.cargill.confproperties.SharepointProperties;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.ExceptionInstanceValidator;
import com.app.cargill.model.SiteMappings;
import com.app.cargill.repository.SiteMappingsRepository;
import java.io.IOException;
import java.net.UnknownHostException;
import java.util.UUID;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

@ExtendWith(MockitoExtension.class)
class DDWReportServiceImplTest {

  @Mock private SiteMappingsRepository siteMappingsRepository;
  @InjectMocks private DDWReportServiceImpl dDWReportServiceImpl;
  @Mock SharepointProperties sharepointProperties;
  private static MockWebServer mockBackEnd;

  void init() throws IOException {
    mockBackEnd = new MockWebServer();
    mockBackEnd.start();
    when(sharepointProperties.getAuthority())
        .thenReturn(
            "http://localhost:"
                + mockBackEnd.getPort()
                + "/57368c21-b8cf-42cf-bd0b-43ecd4bc62ae/tokens/OAuth/2");
    when(sharepointProperties.getResource()).thenReturn("test");
    when(sharepointProperties.getClientId()).thenReturn("test");
    when(sharepointProperties.getClientSecret()).thenReturn("test");
    when(sharepointProperties.getWebUrl())
        .thenReturn("https://localhost:" + mockBackEnd.getPort() + "/sites/dairyenteligen");
    when(sharepointProperties.getFileUrl())
        .thenReturn("/sites/dairyenteligen/Shared Documents/DE_Reports");
    when(sharepointProperties.getTenantId()).thenReturn("test");
    when(sharepointProperties.getGrantType()).thenReturn("client_credentials");
  }

  @Test
  void whenDDWDetailedReturnsUnauthorizedResponse() throws IOException {
    init();
    final ExchangeStrategies strategies =
        ExchangeStrategies.builder()
            .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024))
            .build();

    dDWReportServiceImpl =
        new DDWReportServiceImpl(
            siteMappingsRepository,
            WebClient.builder().exchangeStrategies(strategies),
            sharepointProperties);
    when(siteMappingsRepository.findBySiteId(any()))
        .thenReturn(
            SiteMappings.builder()
                .siteMappingDocument(SiteMappingDocument.builder().ddwHerdId("123456").build())
                .build());

    mockBackEnd.enqueue(new MockResponse().setResponseCode(401));
    WebClientResponseException.Unauthorized unAuthorized =
        assertThrows(
            WebClientResponseException.Unauthorized.class,
            () -> dDWReportServiceImpl.getLatestDetailedReport(UUID.randomUUID()));
    assertEquals(401, unAuthorized.getStatusCode().value());
  }

  @Test
  void whenDDWSummaryReportReturnsUnauthorizedResponse() throws IOException {
    init();
    final ExchangeStrategies strategies =
        ExchangeStrategies.builder()
            .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024))
            .build();

    dDWReportServiceImpl =
        new DDWReportServiceImpl(
            siteMappingsRepository,
            WebClient.builder().exchangeStrategies(strategies),
            sharepointProperties);
    when(siteMappingsRepository.findBySiteId(any()))
        .thenReturn(
            SiteMappings.builder()
                .siteMappingDocument(SiteMappingDocument.builder().ddwHerdId("123456").build())
                .build());

    mockBackEnd.enqueue(new MockResponse().setResponseCode(401));
    WebClientResponseException.Unauthorized unAuthorized =
        assertThrows(
            WebClientResponseException.Unauthorized.class,
            () -> dDWReportServiceImpl.getLatestSummaryReport(UUID.randomUUID()));
    assertEquals(401, unAuthorized.getStatusCode().value());
  }

  @Test
  void whenDDWDetailedReturnsUnauthorizedResponseWithTrueSuffix() throws IOException {
    init();
    when(sharepointProperties.isDetailedFileNameWithSuffix()).thenReturn(true);
    final ExchangeStrategies strategies =
        ExchangeStrategies.builder()
            .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024))
            .build();

    dDWReportServiceImpl =
        new DDWReportServiceImpl(
            siteMappingsRepository,
            WebClient.builder().exchangeStrategies(strategies),
            sharepointProperties);
    when(siteMappingsRepository.findBySiteId(any()))
        .thenReturn(
            SiteMappings.builder()
                .siteMappingDocument(SiteMappingDocument.builder().ddwHerdId("123456").build())
                .build());

    mockBackEnd.enqueue(new MockResponse().setResponseCode(401));
    WebClientResponseException.Unauthorized unAuthorized =
        assertThrows(
            WebClientResponseException.Unauthorized.class,
            () -> dDWReportServiceImpl.getLatestDetailedReport(UUID.randomUUID()));
    assertEquals(401, unAuthorized.getStatusCode().value());
  }

  @Test
  void whenDDWSummaryReportReturnsUnauthorizedResponseWithTrueSuffix() throws IOException {
    init();
    when(sharepointProperties.isSummaryFileNameWithSuffix()).thenReturn(true);
    final ExchangeStrategies strategies =
        ExchangeStrategies.builder()
            .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024))
            .build();

    dDWReportServiceImpl =
        new DDWReportServiceImpl(
            siteMappingsRepository,
            WebClient.builder().exchangeStrategies(strategies),
            sharepointProperties);
    when(siteMappingsRepository.findBySiteId(any()))
        .thenReturn(
            SiteMappings.builder()
                .siteMappingDocument(SiteMappingDocument.builder().ddwHerdId("123456").build())
                .build());

    mockBackEnd.enqueue(new MockResponse().setResponseCode(401));
    WebClientResponseException.Unauthorized unAuthorized =
        assertThrows(
            WebClientResponseException.Unauthorized.class,
            () -> dDWReportServiceImpl.getLatestSummaryReport(UUID.randomUUID()));
    assertEquals(401, unAuthorized.getStatusCode().value());
  }

  @Test
  void whenDDWDownloadReportMethodThrowsCustomDEException() throws CustomDEExceptions {
    final ExchangeStrategies strategies =
        ExchangeStrategies.builder()
            .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(1024))
            .build();

    dDWReportServiceImpl =
        new DDWReportServiceImpl(
            siteMappingsRepository,
            WebClient.builder().exchangeStrategies(strategies),
            sharepointProperties);

    Exception exception =
        assertThrows(
            Exception.class,
            () -> dDWReportServiceImpl.downloadReport(sharepointProperties.getWebUrl(), null));
    assertTrue(exception instanceof CustomDEExceptions);
  }

  @Test
  void is5xxServerErrorReturnsValidResult() {
    WebClientResponseException exp =
        new WebClientResponseException(500, "server error", null, null, null);
    boolean xxServerError = ExceptionInstanceValidator.is5xxServerError(exp);
    assertTrue(xxServerError);
  }

  @Test
  void isIOExceptionReturnsValidResult() {
    IOException exp = new IOException("IoException");
    boolean xxServerError = ExceptionInstanceValidator.isIOException(exp);
    assertTrue(xxServerError);
  }

  @Test
  void isUnknownHostExceptionErrorReturnsValidResult() {
    UnknownHostException exp = new UnknownHostException("UnknownHostException");
    boolean xxServerError = ExceptionInstanceValidator.isUnknownHostExceptionError(exp);
    assertTrue(xxServerError);
    WebClientResponseException webClientResponseException =
        new WebClientResponseException(500, "server error", null, null, null);
    xxServerError =
        ExceptionInstanceValidator.isUnknownHostExceptionError(webClientResponseException);
    assertFalse(xxServerError);
  }
}
