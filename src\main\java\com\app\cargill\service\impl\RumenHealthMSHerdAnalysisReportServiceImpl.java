/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.RumenHealthMSHerdAnalysisReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xssf.usermodel.*;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("rumenHealthMSHerdAnalysisReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class RumenHealthMSHerdAnalysisReportServiceImpl implements IExcelReportService {
  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    RumenHealthMSHerdAnalysisReportDto dto =
        modelMapper.map(data, RumenHealthMSHerdAnalysisReportDto.class);
    dto.setLactationStages(
        dto.getAverageManureScore().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));

    try (XSSFWorkbook rumenHealthHerdAnalysisWB = new XSSFWorkbook()) {
      // create rumenHealthHerdAnalysisWBSheet
      XSSFSheet rumenHealthHerdAnalysisWBSheet =
          rumenHealthHerdAnalysisWB.createSheet(
              ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              rumenHealthHerdAnalysisWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(rumenHealthHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              rumenHealthHerdAnalysisWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(rumenHealthHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              rumenHealthHerdAnalysisWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(rumenHealthHerdAnalysisWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(
              rumenHealthHerdAnalysisWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      prepareHeader(
          rumenHealthHerdAnalysisWB,
          rumenHealthHerdAnalysisWBSheet,
          rowNumber,
          cellNumber,
          dto,
          boldStyle,
          locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = rumenHealthHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      rumenHealthHerdAnalysisWBSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // Lact Stages
      cellNumber.set(0);

      int lactationStageStartRowNumber = rowNumber.get();

      row = rumenHealthHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.createAndSetCellValue(
            row, cellNumber, centerBlack, ExcelUtils.getLangValue(lStage, null, source, locale));
      }

      //  Avg manure score
      cellNumber.set(0);

      int avgManureScoreRowNumber = rowNumber.get();
      row = rumenHealthHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_AVG_MANURE_SCORE, null, source, locale));

      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getAverageManureScore().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      //  Min
      cellNumber.set(0);
      int minRowNumber = rowNumber.get();
      row = rumenHealthHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_MIN, null, source, locale));
      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getMin().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // Max
      cellNumber.set(0);
      int maxRowNumber = rowNumber.get();
      row = rumenHealthHerdAnalysisWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(LangKeys.REPORT_MAX, null, source, locale));
      for (String lStage : dto.getLactationStages()) {
        ExcelUtils.highlightEmptyCell(
            row, dto.getMax().get(lStage), cellNumber, decimalStyle, greyCellStyle);
      }

      // create data sources
      int columnStart = 1;
      int columnEnd = columnStart + dto.getLactationStages().length - 1;
      columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

      XDDFDataSource<String> lactationStage =
          XDDFDataSourcesFactory.fromStringCellRange(
              rumenHealthHerdAnalysisWBSheet,
              new CellRangeAddress(
                  lactationStageStartRowNumber,
                  lactationStageStartRowNumber,
                  columnStart,
                  columnEnd));
      // y axis
      XDDFNumericalDataSource<Double> averageManureScore =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenHealthHerdAnalysisWBSheet,
              new CellRangeAddress(
                  avgManureScoreRowNumber, avgManureScoreRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> min =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenHealthHerdAnalysisWBSheet,
              new CellRangeAddress(minRowNumber, minRowNumber, columnStart, columnEnd));
      XDDFNumericalDataSource<Double> max =
          XDDFDataSourcesFactory.fromNumericCellRange(
              rumenHealthHerdAnalysisWBSheet,
              new CellRangeAddress(maxRowNumber, maxRowNumber, columnStart, columnEnd));

      // needed objects for the charts
      XSSFChart chart;
      XDDFCategoryAxis bottomAxis;
      XDDFValueAxis leftAxis;
      XDDFLineChartData dataLeft;
      XDDFLineChartData.Series series;
      int chartCol0 = columnEnd + 1;
      // ======first line chart==========
      chart =
          ExcelUtils.initChart(
              rumenHealthHerdAnalysisWBSheet,
              ExcelUtils.getLangValue(
                  LangKeys.REPORT_RUMEN_HEALTH_HERD_ANALYSIS_CHART_NAME, null, source, locale),
              chartCol0,
              3,
              chartCol0 + 10,
              23);

      ExcelUtils.initLegends(chart);

      bottomAxis =
          ExcelUtils.createBottomAxis(
              chart,
              ExcelUtils.getLangValue(LangKeys.REPORT_LACTATION_STAGES, null, source, locale));

      leftAxis =
          ExcelUtils.createLeftAxis(
              chart, ExcelUtils.getLangValue(LangKeys.REPORT_MANURE_SCORE, null, source, locale));
      bottomAxis.crossAxis(leftAxis);
      bottomAxis.setCrosses(AxisCrosses.MIN);
      // create chart data
      dataLeft = (XDDFLineChartData) chart.createData(ChartTypes.LINE, bottomAxis, leftAxis);

      // create series
      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, averageManureScore);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_AVG_MANURE_SCORE, null, source, locale),
          new CellReference(
              rumenHealthHerdAnalysisWBSheet.getSheetName(),
              avgManureScoreRowNumber,
              0,
              true,
              true));
      series.setSmooth(true);

      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, min);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_MIN, null, source, locale),
          new CellReference(
              rumenHealthHerdAnalysisWBSheet.getSheetName(), minRowNumber, 0, true, true));
      series.setSmooth(true);

      series = (XDDFLineChartData.Series) dataLeft.addSeries(lactationStage, max);
      series.setTitle(
          ExcelUtils.getLangValue(LangKeys.REPORT_MAX, null, source, locale),
          new CellReference(
              rumenHealthHerdAnalysisWBSheet.getSheetName(), maxRowNumber, 0, true, true));
      series.setSmooth(true);

      chart.plot(dataLeft);

      ExcelUtils.drawLineSeries(dataLeft, 0, PresetColor.GREEN, false);
      ExcelUtils.drawLineSeries(dataLeft, 1, PresetColor.ORANGE, true);
      ExcelUtils.drawLineSeries(dataLeft, 2, PresetColor.RED, true);

      // ExcelUtils.drawGridLinesInChart(chart, true);

      return ExcelUtils.finalizeWorkbook(
          rumenHealthHerdAnalysisWB, rumenHealthHerdAnalysisWBSheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public Object prepareData(Object dto) {
    RumenHealthMSHerdAnalysisReportDto mappedDto =
        modelMapper.map(dto, RumenHealthMSHerdAnalysisReportDto.class);
    mappedDto.setLactationStages(
        mappedDto.getAverageManureScore().entrySet().stream()
            .map(Map.Entry::getKey)
            .toArray(String[]::new));
    return mappedDto;
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    dto = prepareData(dto);
    byte[] report =
        freeMarkerComponent.render(
            dto,
            ReportsToBeanMappings.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }

  @Override
  public String getFileName(Object data) {
    RumenHealthMSHerdAnalysisReportDto dto =
        modelMapper.map(data, RumenHealthMSHerdAnalysisReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT.getFileName()
        : dto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook rumenHealthHerdAnalysisWB,
      XSSFSheet rumenHealthHerdAnalysisSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      RumenHealthMSHerdAnalysisReportDto dto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(),
        rumenHealthHerdAnalysisWB,
        rumenHealthHerdAnalysisSheet,
        rowNumber.get(),
        cellNumber.getAndIncrement());
    // headings
    XSSFRow row = rumenHealthHerdAnalysisSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getVisitName());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getVisitDate());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = rumenHealthHerdAnalysisSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getToolName());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_ANALYSIS_TYPE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, dto.getAnalysisType());
    rumenHealthHerdAnalysisSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));
  }
}
