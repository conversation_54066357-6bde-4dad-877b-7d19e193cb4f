/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.assertj.core.api.Assertions.assertThat;

import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.exceptions.UnauthorizedUserDEException;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

  @InjectMocks private GlobalExceptionHandler globalExceptionHandler;

  private MockHttpServletRequest request;
  private MockHttpServletResponse response;

  @BeforeEach
  public void setUp() {
    request = new MockHttpServletRequest();
    response = new MockHttpServletResponse();
  }

  @Test
  void whenNotFoundCorrectStatusIsSet() throws IOException {
    globalExceptionHandler.handleMyCustomException(
        request, response, new NotFoundDEException("something"));
    assertThat(response.getStatus()).isEqualTo(HttpServletResponse.SC_BAD_REQUEST);
    assertThat(response.getErrorMessage()).contains("Error occurred:");
  }

  @Test
  void whenAlreadyExistsCorrectStatusIsSet() throws IOException {
    globalExceptionHandler.handleMyCustomException(
        request, response, new AlreadyExistsDEException());
    assertThat(response.getStatus()).isEqualTo(HttpServletResponse.SC_BAD_REQUEST);
    assertThat(response.getErrorMessage()).contains("Error occurred:");
  }

  @Test
  void whenUnauthorizedCorrectStatusIsSet() throws IOException {
    globalExceptionHandler.handleMyCustomException(
        request, response, new UnauthorizedUserDEException("something"));
    assertThat(response.getStatus()).isEqualTo(HttpServletResponse.SC_UNAUTHORIZED);
    assertThat(response.getErrorMessage()).contains("something");
  }

  @Test
  void whenRuntimeExceptionCorrectStatusIsSet() throws IOException {
    globalExceptionHandler.handleMyCustomException(
        request, response, new UnauthorizedUserDEException("something"));
    assertThat(response.getStatus()).isEqualTo(HttpServletResponse.SC_UNAUTHORIZED);
    assertThat(response.getErrorMessage()).contains("something");
  }

  @Test
  void whenUnknownExceptionCorrectStatusIsSet() throws IOException {
    globalExceptionHandler.handleMyCustomException(request, response, new Exception("something"));
    assertThat(response.getStatus()).isEqualTo(HttpServletResponse.SC_BAD_REQUEST);
    assertThat(response.getErrorMessage()).contains("Error occurred:");
  }

  @Test
  void whenCustomDEExceptionCorrectStatusIsSet() throws IOException {
    globalExceptionHandler.handleMyCustomException(
        request, response, new CustomDEExceptions("something", HttpStatus.NOT_FOUND.value()));
    assertThat(response.getStatus()).isEqualTo(HttpServletResponse.SC_BAD_REQUEST);
    assertThat(response.getErrorMessage()).contains("Error occurred:");
  }
}
