/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.model.LongRunningTask;
import com.app.cargill.repository.LongRunningTasksRepository;
import java.io.Serializable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Profiles;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@SuppressWarnings("java:S3740") // Raw type used on purpose
@Slf4j
public class LongRunningTasksService {
  private final LongRunningTasksRepository repository;

  @SuppressWarnings("unchecked")
  public <T extends Serializable> LongRunningTask<T> saveTask(LongRunningTask<T> task) {
    return (LongRunningTask<T>) repository.save(task);
  }

  @EventListener(ApplicationReadyEvent.class)
  public void clearRunningTasks(ApplicationReadyEvent event) {
    if (event.getApplicationContext().getEnvironment().acceptsProfiles(Profiles.of("test"))) return;
    try {
      repository.stopRunningTasks();
    } catch (Exception e) {
      log.error("Error while clearing tasks", e);
    }
  }
}
