/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.ManureScreenerScoreGoalToolItem;
import com.app.cargill.document.ManureScreenerTool;
import com.app.cargill.document.ManureScreenerToolItem;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ManureScreenerCalculationTest {

  @InjectMocks private ManureScreenerCalculation manureScreenerCalculation;

  @Test
  void calculateFields() {
    ManureScreenerTool manureScreenerTool =
        ManureScreenerTool.builder()
            .mstScores(
                List.of(
                    ManureScreenerToolItem.builder()
                        .topScaleAmountInGrams(5.0)
                        .midScaleAmountInGrams(2.1)
                        .bottomScaleAmountInGrams(3.5)
                        .totalScaleAmount(20.2)
                        .build()))
            .mstGoal(ManureScreenerScoreGoalToolItem.builder().build())
            .build();

    manureScreenerTool = manureScreenerCalculation.calculateFields(manureScreenerTool);
    assertTrue(
        manureScreenerTool.getMstScores().stream()
            .allMatch(
                pen ->
                    pen.totalScaleAmount != null
                        && pen.topScalePercentage != null
                        && pen.midScalePercentage != null
                        && pen.bottomScalePercentage != null));

    assertNotNull(manureScreenerTool.getMstGoal());
  }

  @Test
  void calculateFieldsWithNull() {
    ManureScreenerTool manureScreenerTool =
        ManureScreenerTool.builder()
            .mstScores(List.of(ManureScreenerToolItem.builder().build()))
            .mstGoal(ManureScreenerScoreGoalToolItem.builder().build())
            .build();

    manureScreenerTool = manureScreenerCalculation.calculateFields(manureScreenerTool);
    assertTrue(
        manureScreenerTool.getMstScores().stream()
            .allMatch(
                pen ->
                    pen.totalScaleAmount != null
                        && pen.topScalePercentage != null
                        && pen.midScalePercentage != null
                        && pen.bottomScalePercentage != null));

    assertNotNull(manureScreenerTool.getMstGoal());
  }

  @Test
  void verifyCalculations() {
    ManureScreenerTool manureScreenerTool =
        ManureScreenerTool.builder()
            .mstScores(
                List.of(
                    ManureScreenerToolItem.builder()
                        .topScaleAmountInGrams(31.0)
                        .midScaleAmountInGrams(111.0)
                        .bottomScaleAmountInGrams(160.0)
                        .isToolItemNew(false)
                        .toolStatus(ToolStatuses.Completed)
                        .isFirstTimeWithScore(true)
                        .penName("Lactation")
                        .build()))
            .mstGoal(
                ManureScreenerScoreGoalToolItem.builder()
                    .isToolItemNew(true)
                    .topGoalMinimumPercent(0.0)
                    .topGoalMaximumPercent(10.0)
                    .midGoalMinimumPercent(0.0)
                    .midGoalMaximumPercent(20.0)
                    .bottomGoalMinimumPercent(50.0)
                    .bottomGoalMaximumPercent(100.0)
                    .toolStatus(ToolStatuses.NotStarted)
                    .build())
            .build();

    manureScreenerTool = manureScreenerCalculation.calculateFields(manureScreenerTool);

    assertEquals(302.0, manureScreenerTool.getMstScores().get(0).getTotalScaleAmount());

    assertEquals(10.3, manureScreenerTool.getMstScores().get(0).getTopScalePercentage());

    assertEquals(36.8, manureScreenerTool.getMstScores().get(0).getMidScalePercentage());

    assertEquals(53.0, manureScreenerTool.getMstScores().get(0).getBottomScalePercentage());
  }
}
