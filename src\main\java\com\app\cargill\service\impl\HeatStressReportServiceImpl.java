/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.HeatStressReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.util.Collections;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("heatStressReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"})
public class HeatStressReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public String getFileName(Object data) {
    HeatStressReportDto heatStressReportDto = modelMapper.map(data, HeatStressReportDto.class);
    return StringUtils.isBlank(heatStressReportDto.getFileName())
        ? ReportsToBeanMappings.HEAT_STRESS_REPORT.getFileName()
        : heatStressReportDto.getFileName();
  }

  void prepareHeader(
      XSSFWorkbook heatStressWB,
      XSSFSheet heatStressSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      HeatStressReportDto heatStressReportDto,
      XSSFCellStyle boldStyle,
      Locale locale) {
    // add logo in chart
    ExcelUtils.addCargillLogo(
        getClass(), heatStressWB, heatStressSheet, rowNumber.get(), cellNumber.getAndIncrement());
    // headings
    XSSFRow row = heatStressSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, heatStressReportDto.getVisitName());
    heatStressSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, heatStressReportDto.getVisitDate());
    heatStressSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 5, 6));

    // second row
    cellNumber.set(1);
    row = heatStressSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, heatStressReportDto.getToolName());
    heatStressSheet.addMergedRegion(
        new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 2, 3));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, null);
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object data, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    HeatStressReportDto heatStressReportDto = modelMapper.map(data, HeatStressReportDto.class);

    try (XSSFWorkbook heatStressWB = new XSSFWorkbook()) {
      XSSFSheet heatStressWBSheet =
          heatStressWB.createSheet(
              ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, null, source, locale));
      AtomicInteger rowNumber = new AtomicInteger(0);
      AtomicInteger cellNumber = new AtomicInteger(0);

      XSSFRow row;
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              heatStressWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(heatStressWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              heatStressWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(heatStressWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              heatStressWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(heatStressWB, false, true, IndexedColors.BLACK));
      XSSFCellStyle thresholdStyle =
          ExcelUtils.applyCellStyle(
              heatStressWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(
                  heatStressWB, false, true, heatStressReportDto.getData().getStressColour()));
      XSSFCellStyle decimalStyle =
          ExcelUtils.decimalCellStyle(
              heatStressWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

      prepareHeader(
          heatStressWB,
          heatStressWBSheet,
          rowNumber,
          cellNumber,
          heatStressReportDto,
          boldStyle,
          locale);

      // create the data
      // calculated table heading
      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          greyCellStyle,
          ExcelUtils.getLangValue(LangKeys.REPORT_EVAL_DATA_TITLE, null, source, locale));
      heatStressWBSheet.addMergedRegion(
          new CellRangeAddress(rowNumber.get() - 1, rowNumber.get() - 1, 0, 7));

      // Lact Stages
      cellNumber.set(0);

      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_TEMPERATURE_IN_CENTIGRADE, null, source, locale));

      ExcelUtils.createAndSetCellValue(
          row, cellNumber, thresholdStyle, heatStressReportDto.getData().getTemperatureInCelsius());
      //	      for (String visitDate : MappedDto.getVisitDates()) {
      //	        ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, visitDate);
      //	      }

      cellNumber.set(0);

      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_TEMPERATURE_IN_FARENHIET, null, source, locale));
      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          thresholdStyle,
          heatStressReportDto.getData().getTemperatureInFahrenheit());

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_REDUCTION_IN_DMI,
              new String[] {heatStressReportDto.getData().getWeightLabel()},
              source,
              locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getReductionInDmi(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_DMI_ADJUSTMENT, null, source, locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getDmiAdjustmentPercentage(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_IN_TAKE_ADJUSTMENT, null, source, locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getInTakeAdjustment(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_ESTIMATED_DRY_MATTER_INTAKE,
              new String[] {heatStressReportDto.getData().getWeightLabel()},
              source,
              locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getEstimatedDryMatterIntake(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_LOSS_OF_ENERGY_CONSUMED, null, source, locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getLossOfEnergyConsumed(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_MILK_VALUE_LOSS_PER_DAY,
              new String[] {heatStressReportDto.getData().getCurrencyLabel()},
              source,
              locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getMilkValueLossPerDay(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_ENERGY_EQUIVALENT_MILK_LOSS,
              new String[] {heatStressReportDto.getData().getWeightLabel()},
              source,
              locale));
      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getEnergyEquivalentMilkLoss(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      cellNumber.set(0);
      row = heatStressWBSheet.createRow(rowNumber.getAndIncrement());

      ExcelUtils.createAndSetCellValue(
          row,
          cellNumber,
          centerBlack,
          ExcelUtils.getLangValue(
              LangKeys.REPORT_HEAT_STRESS_MILK_VALUE_LOSS_PER_MONTH,
              new String[] {heatStressReportDto.getData().getCurrencyLabel()},
              source,
              locale));

      ExcelUtils.highlightEmptyCell(
          row,
          heatStressReportDto.getData().getMilkValueLossPerMonth(),
          cellNumber,
          decimalStyle,
          greyCellStyle);

      return ExcelUtils.finalizeWorkbook(
          heatStressWB, heatStressWBSheet.getRow(0).getLastCellNum());

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    HeatStressReportDto heatStressReportDto = modelMapper.map(dto, HeatStressReportDto.class);

    byte[] report =
        freeMarkerComponent.render(
            heatStressReportDto,
            ReportsToBeanMappings.HEAT_STRESS_REPORT.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);
    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(
            Collections.singletonMap(getFileName(dto).split(Pattern.quote("."))[0], report),
            ExportFileExtensions.PNG.getExtension()));
  }
}
