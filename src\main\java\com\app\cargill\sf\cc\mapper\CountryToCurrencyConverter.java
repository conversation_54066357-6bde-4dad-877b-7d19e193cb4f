/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

public class CountryToCurrencyConverter {

  private CountryToCurrencyConverter() {}

  public static String countryToCurrency(String country) {
    String result;
    switch (country) {
      case "Argentina" -> result = "ARS";
      case "Brazil" -> result = "BRL";
      case "Canada" -> result = "CAD";
      case "Paraguay" -> result = "BRL";
      default -> result = "USD";
    }
    return result;
  }
}
