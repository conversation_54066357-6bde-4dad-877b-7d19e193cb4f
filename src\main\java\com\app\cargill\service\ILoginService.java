/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.Oauth2Dto;
import org.springframework.util.MultiValueMap;

@SuppressWarnings("java:S125") // remove when all commented code is removed
public interface ILoginService {
  // Object fetchAccessTokenByOktaCode(String code, String state)
  //   throws JwtVerificationException, MalformedURLException;

  boolean logout();

  // Object fetchAccessTokenByAzureAD(String code, String state) throws Exception;

  // Object fetchAccessToken(String authServer, String token) throws Exception;

  Oauth2Dto fetchAccessTokenUsingOkta(MultiValueMap<String, String> dto);
}
