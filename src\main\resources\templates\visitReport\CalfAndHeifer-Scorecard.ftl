<div class="container">
		<div class="legend-head">
			<div class="count">${toolNumber!}</div>
			<div class="main-title">
				<span class="sm-head">${localization.getMessage("VisitViewModel.CalfHeiferItem", [],"Calf and heifer",locale)}</span>
				<span class="lg-head"></span>
			</div>
			 <div style="font-size: 1;color: white;">0000222CH</div>
		</div>
	</div>

    <!-- Survey Categories -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-secondary mb-1">
					<span>${localization.getMessage("SurveyCategories", [],"Survey Categories",locale)}</span>
				</h3>
            </div>
        </div>
    </div>

    <!-- Colostrum -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-primary">
					<span>Colostrum</span>
				</h3>
            </div>
        </div>

        <div class="row mx-neg-4">
            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>1- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>2- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>3- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>4- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>5- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>6- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>7- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>8- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>
            </div>

            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>9- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>10- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>11- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>12- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>13- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>14- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>15- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>16- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>
            </div>
        </div>
    </div>

    <div class="break-page"></div>

    <!-- Pre-weaned -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-primary">
					<span>Pre-weaned</span>
				</h3>
            </div>
        </div>

        <div class="row mx-neg-4">
            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>1- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>2- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>3- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>4- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>5- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>6- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>7- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>8- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>
            </div>

            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>9- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>10- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>11- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>12- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
				</h3>

                <p>13- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>14- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>15- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>

                <p>16- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
					<span>No</span>
                    <span class="title-plain">Yes</span>
				</h3>
            </div>
        </div>
    </div>

    <div class="break-page"></div>

    <!-- Post-weaned -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-primary">
                    <span>Post-weaned</span>
                </h3>
            </div>
        </div>

        <div class="row mx-neg-4">
            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>1- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>2- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>3- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>4- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>5- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>6- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>7- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>8- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>
            </div>

            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>9- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>10- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>11- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>12- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>13- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>14- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>15- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>16- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>
            </div>
        </div>
    </div>

    <div class="break-page"></div>

    <!-- Grower, Puberty, Pregnancy, Close-up -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-primary">
                    <span>Grower, Puberty, Pregnancy, Close-up</span>
                </h3>
            </div>
        </div>

        <div class="row mx-neg-4">
            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>1- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>2- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>3- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>4- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>5- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>6- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>7- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>8- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>
            </div>

            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>9- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>10- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>11- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>12- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>13- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>14- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>15- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>16- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>
            </div>
        </div>
    </div>

    <div class="break-page"></div>

    <!-- Key Benchmarks -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-primary">
                    <span>Key Benchmarks</span>
                </h3>
            </div>
        </div>

        <div class="row mx-neg-4">
            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>1- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>2- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>3- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>4- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>5- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>6- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>7- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>8- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>
            </div>

            <div class="col-6 px-4">
                <h6 class="d-flex justify-space-between">
                    <span>Answers</span>
                    <span class="title-plain">Optimal</span>
                </h6>

                <p>9- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>10- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>11- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>12- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>Quarterly</span>
                    <span class="title-plain">Semi Annually</span>
                </h3>

                <p>13- Is whole plant moisture determined for each field?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>14- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>15- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>

                <p>16- Are the number of cows and forage needs planned annually?</p>
                <h3 class="title-secondary d-flex justify-space-between my-1">
                    <span>No</span>
                    <span class="title-plain">Yes</span>
                </h3>
            </div>
        </div>
    </div>
    
    <div class="break-page"></div>

    <!-- Overall Forage Score -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="title-secondary mb-1">
					<span>Overall Forage Score</span>
				</h3>
            </div>
        </div>
    </div>

    
    <div class="container">
        <div class="row mx-neg-4">

            <div class="col-4-equal px-4">
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
                        <canvas id="donutchart"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">Colostrum</h6>
                </div>

            </div>

            <div class="col-4-equal px-4">
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
                        <canvas id="donutchart2"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">Pre-weaned</h6>
                </div>
            </div>

            <div class="col-4-equal px-4">
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
                        <canvas id="donutchart3"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">Post-weaned</h6>
                </div>
            </div>

            <div class="col-4-equal px-4">
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
                        <canvas id="donutchart4"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">Grower, Puberty, Pregnancy, Close-up</h6>
                </div>
            </div>

            <div class="col-4-equal px-4">
                <div class="card pa-10 d-flex align-center mb-1">
                    <div class="donut-wrapper-sm">
                        <canvas id="donutchart5"></canvas>
                    </div>
                    <h6 class="my-0 ml-1">Key Benchmarks</h6>
                </div>
            </div>


        </div>
    </div>





    


    <!-- Donut Chart  -->
	<script>

        var centerText = {
        id: 'centerText',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.font = '10px';
                ctx.fillStyle = '#D95D5D';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        };

		const ctx1 = document.getElementById("donutchart").getContext("2d");

		var options = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['#D95D5D', '#E3E7EB'],
                        borderWidth: 0,
                        percent: 68,
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 0,
						right: 0,
						top: 0,
						bottom: 0
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '75%',
			},

            plugins: [centerText]              
		};
	</script>

    <!-- Donut Chart 2-->
	<script>

        var centerText = {
        id: 'centerText',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.font = '10px';
                ctx.fillStyle = '#4EBA7D';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        };

		const ctx2 = document.getElementById("donutchart2").getContext("2d");

		var options2 = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['#4EBA7D', '#E3E7EB'],
                        borderWidth: 0,
                        percent: 75,
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 0,
						right: 0,
						top: 0,
						bottom: 0
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '75%',
			},

            plugins: [centerText]              
		};
	</script>

    <!-- Donut Chart 3 -->
	<script>

        var centerText = {
        id: 'centerText',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.font = '10px';
                ctx.fillStyle = '#D9A25E';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        };

		const ctx3 = document.getElementById("donutchart3").getContext("2d");

		var options3 = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['#D9A25E', '#E3E7EB'],
                        borderWidth: 0,
                        percent: 91,
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 0,
						right: 0,
						top: 0,
						bottom: 0
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '75%',
			},

            plugins: [centerText]              
		};
	</script>

    <!-- Donut Chart 4-->
	<script>

        var centerText = {
        id: 'centerText',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.font = '10px';
                ctx.fillStyle = '#D95D5D';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        };

		const ctx4 = document.getElementById("donutchart4").getContext("2d");

		var options4 = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['#D95D5D', '#E3E7EB'],
                        borderWidth: 0,
                        percent: 37,
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 0,
						right: 0,
						top: 0,
						bottom: 0
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '75%',
			},

            plugins: [centerText]              
		};
	</script>

    <!-- Donut Chart 5-->
	<script>

        var centerText = {
        id: 'centerText',
            beforeInit: function(chart, args, pluginOptions) {
                const dataset = chart.data.datasets[0];
                chart.data.labels = [dataset.label];
                dataset.data = [dataset.percent, 100 - dataset.percent];
            },
            beforeDatasetsDraw: function(chart, args, pluginOptions) {
                const ctx = chart.ctx;
                const width = chart.width;
                const height = chart.height;
            
                ctx.restore();
                ctx.textBaseline = 'middle';
                ctx.font = '10px';
                ctx.fillStyle = '#D9A25E';
                const text = chart.data.datasets[0].percent + "%";
                const textX = Math.round((width - ctx.measureText(text).width) / 2);
                const textY = height / 2;

                ctx.fillText(text, textX, textY);
                ctx.save();
            }
        };

		const ctx5 = document.getElementById("donutchart5").getContext("2d");

		var options5 = {
			type: 'doughnut',

			data: {
				datasets: [
					{
						backgroundColor: ['#D9A25E', '#E3E7EB'],
                        borderWidth: 0,
                        percent: 82,
					}
				],
                
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
                    tooltip: {
                        enabled: false,
					},
				},

				layout: {
					padding: {
						left: 0,
						right: 0,
						top: 0,
						bottom: 0
					}
				},

				responsive: true,
                aspectRatio: 1,
                cutout: '75%',
			},

            plugins: [centerText]              
		};
	</script>



<script>
    window.onload = function () {
        window.myLine = new Chart(ctx1, options);
        window.myLine = new Chart(ctx2, options2);
        window.myLine = new Chart(ctx3, options3);
        window.myLine = new Chart(ctx4, options4);
        window.myLine = new Chart(ctx5, options5);
    };
</script>