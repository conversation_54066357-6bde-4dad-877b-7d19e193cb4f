/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString
public class LongRunningTask<T extends Serializable> extends BaseEntity implements Serializable {

  public LongRunningTask(String localId, TaskName name, TaskStatus status) {
    super();
    setLocalId(localId);
    this.status = status;
    this.name = name;
  }

  public LongRunningTask(String localId, TaskName name, TaskStatus status, T metaData) {
    super();
    setLocalId(localId);
    this.status = status;
    this.name = name;
    this.metaData = metaData;
  }

  @Serial private static final long serialVersionUID = 1L;

  @Column(name = "status")
  @Enumerated(EnumType.STRING)
  private TaskStatus status;

  @Column(name = "task_name")
  @Enumerated(EnumType.STRING)
  private TaskName name;

  @Type(JsonBinaryType.class)
  @Column(name = "meta_data", columnDefinition = "jsonb")
  private T metaData;

  public enum TaskName {
    ACCOUNTS_TO_LIFT_SYNC,
    SITES_TO_LIFT_SYNC,
    COSMOS_DATA_MIGRATION,
    POST_COSMOS_DATA_MIGRATION,
    LIFT_ACCOUNTS_SYNC,
    LIFT_ACCOUNTS_FULL_SYNC,
    LIFT_SITES_SYNC,
    LIFT_SITES_FULL_SYNC,
    LIFT_FULL_SYNC,
    MISSING_ID_FIX_IN_MIGRATION,
    CONTENT_MIGRATION,
    LIFT_ACCOUNTS_MERGE_SYNC,
    UNDEFINED
  }
}
