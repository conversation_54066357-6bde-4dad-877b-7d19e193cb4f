<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="033" author="Taha">
        <sql>
update Country_tools
set deleted = true
where country_tool_document->>'ToolId' in ('PenTimeBudgetTool','MetabolicIncidence','ForagePennState','TMRParticleScore') and country_tool_document->>'CountryId' in ('India','CFNIndia');
        </sql>
    </changeSet>

</databaseChangeLog>