/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.MilkSoldEvaluationToolItem;
import com.app.cargill.document.MilkSoldEvaluationToolOutputToolItem;
import com.app.cargill.document.MilkSoldMilkProcessorToolItem;
import java.util.Objects;

public class MilkSoldEvaluationCalculation {
  public MilkSoldEvaluationTool calculateFields(MilkSoldEvaluationTool tool, Double siteMilk) {
    if (tool == null
        || tool.getVisitMilkEvaluationData() == null
        || tool.getVisitMilkEvaluationData().getOutputs() == null) {
      return null;
    }

    MilkSoldEvaluationToolItem item = tool.getVisitMilkEvaluationData();
    item.getOutputs().toolStatus = toolStatus(item);

    item.getOutputs().averageMilkProduction = averageMilkProduction(item, siteMilk);
    item.getOutputs().averageMilkProductionAnimalsTank =
        averageMilkProductionAnimalsTank(item, siteMilk);
    item.getOutputs().milkFatYield = milkFatYield(item.getOutputs());
    item.getOutputs().milkProteinYield = milkProteinYield(item.getOutputs());
    item.getOutputs().milkFatProteinYield = milkFatProteinYield(item.getOutputs());
    item.getOutputs().componentEfficiency = componentEfficiency(item);
    item.getOutputs().feedEfficiency = feedEfficiency(item);
    item.getOutputs().averageMilkSolidNonFat = averageMilkSolidNonFat(item);
    item.getOutputs().milkSolidNonFat = milkSolidNonFat(item.getOutputs());

    tool.setVisitMilkEvaluationData(item);

    return tool;
  }

  private ToolStatuses toolStatus(MilkSoldEvaluationToolItem item) {
    if (totalMilkSold(item) != null) return ToolStatuses.Completed;
    return ToolStatuses.InProgress;
  }

  private Double averageMilkProduction(MilkSoldEvaluationToolItem item, Double siteMilk) {
    double averageMilkProduction = 0;
    double finalValue = 0;

    if (item.getPickups() != null && !(item.getPickups().isEmpty())) {

      if (Objects.requireNonNullElse(item.getLactatingAnimals(), 0) > 0) {
        for (MilkSoldMilkProcessorToolItem pickupItem : item.getPickups())
          averageMilkProduction +=
              Objects.requireNonNullElse(pickupItem.getMilkSold(), 0.0)
                  / item.getLactatingAnimals();
      }

      if (item.getOutputs() != null
          && Objects.requireNonNullElse(item.getOutputs().evaluationDays, 0.0) > 0.0) {
        averageMilkProduction = averageMilkProduction / item.getOutputs().evaluationDays;
        finalValue = roundAvoid(averageMilkProduction, 2);
      }

      if (finalValue == 0) {
        finalValue = Objects.requireNonNullElse(siteMilk, 0.0);
        finalValue = roundAvoid(finalValue, 2);
      }
    }
    return finalValue;
  }

  private Double averageMilkProductionAnimalsTank(
      MilkSoldEvaluationToolItem item, Double siteMilk) {
    double avgMilkProductionAnimalsTank = 0;
    double finalValue = 0;

    if (item.getPickups() != null && !(item.getPickups().isEmpty())) {
      for (MilkSoldMilkProcessorToolItem pickupItem : item.getPickups()) {
        if (pickupItem.getMilkSold() != null
            && Objects.requireNonNullElse(pickupItem.getAnimalsInTank(), 0) > 0)
          avgMilkProductionAnimalsTank +=
              (pickupItem.getMilkSold() / pickupItem.getAnimalsInTank());
      }

      if (item.getOutputs() != null
          && item.getOutputs().evaluationDays != null
          && item.getOutputs().evaluationDays > 0) {
        avgMilkProductionAnimalsTank =
            avgMilkProductionAnimalsTank / item.getOutputs().evaluationDays;
        finalValue = roundAvoid(avgMilkProductionAnimalsTank, 2);
      }

      if (finalValue == 0) {
        finalValue = Objects.requireNonNullElse(siteMilk, 0.0);
        finalValue = roundAvoid(finalValue, 2);
      }
    }

    return finalValue;
  }

  private Double milkProteinYield(MilkSoldEvaluationToolOutputToolItem item) {
    double milkProteinYield =
        (item.averageMilkProductionAnimalsTank
            * Objects.requireNonNullElse(item.averageMilkProteinPer, 0.0)
            / 100);
    return roundAvoid(milkProteinYield, 2);
  }

  private Double milkFatProteinYield(MilkSoldEvaluationToolOutputToolItem item) {
    return roundAvoid((item.milkFatYield + item.milkProteinYield), 2);
  }

  private Double milkFatYield(MilkSoldEvaluationToolOutputToolItem item) {
    double milkFatYield =
        (item.averageMilkProductionAnimalsTank
            * Objects.requireNonNullElse(item.averageMilkFatPer, 0.0)
            / 100);
    return roundAvoid(milkFatYield, 2);
  }

  private Double componentEfficiency(MilkSoldEvaluationToolItem item) {
    if (item.getDryMatterIntake() != null && item.getDryMatterIntake() > 0) {
      double componentEfficiency =
          (item.getOutputs().milkFatProteinYield / item.getDryMatterIntake() * 100);
      return roundAvoid(componentEfficiency, 2);
    }
    return null;
  }

  private Double feedEfficiency(MilkSoldEvaluationToolItem item) {
    if (item.getDryMatterIntake() != null && item.getDryMatterIntake() > 0) {
      double feedEfficiency =
          (item.getOutputs().averageMilkProductionAnimalsTank / (item.getDryMatterIntake()));
      return roundAvoid(feedEfficiency, 2);
    }
    return null;
  }

  private Integer totalMilkSold(MilkSoldEvaluationToolItem item) {
    if (item.getPickups() == null) {
      return null;
    }

    MilkSoldMilkProcessorToolItem isValidPickup =
        item.getPickups().stream()
            .filter(
                x ->
                    Objects.requireNonNullElse(x.getAnimalsInTank(), 0) > 0
                        && Objects.requireNonNullElse(x.getDaysInTank(), 0) > 0
                        && Objects.requireNonNullElse(x.getMilkSold(), 0.0) > 0.0)
            .findFirst()
            .orElse(null);

    if (Objects.requireNonNullElse(item.getAnimalsinTank(), 0) > 0
        && Objects.requireNonNullElse(item.getDaysInMilk(), 0) != 0
        && Objects.requireNonNullElse(item.getDryMatterIntake(), 0.0) > 0.0
        && Objects.requireNonNullElse(item.getLactatingAnimals(), 0) > 0
        && isValidPickup != null) return 1;

    return null;
  }

  private Double milkSolidNonFat(MilkSoldEvaluationToolOutputToolItem item) {
    double nonFatSolid =
        (item.averageMilkProductionAnimalsTank
            * Objects.requireNonNullElse(item.averageMilkSolidNonFat, 0.0)
            / 100);
    return roundAvoid(nonFatSolid, 2);
  }

  private Double averageMilkSolidNonFat(MilkSoldEvaluationToolItem item) {
    double averageMilkSoldNonFatPer = 0;
    if (item.getPickups() != null
        && item.getPickups().stream().anyMatch(x -> x.getNonFatSolid() != null)) {
      for (MilkSoldMilkProcessorToolItem pickupItem : item.getPickups())
        averageMilkSoldNonFatPer += Objects.requireNonNullElse(pickupItem.getNonFatSolid(), 0.0);

      averageMilkSoldNonFatPer =
          averageMilkSoldNonFatPer
              / item.getPickups().stream().filter(x -> x.getNonFatSolid() != null).count();
    }
    return roundAvoid(averageMilkSoldNonFatPer, 2);
  }
}
