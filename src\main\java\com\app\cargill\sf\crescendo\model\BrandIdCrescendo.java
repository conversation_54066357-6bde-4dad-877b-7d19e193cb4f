/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum BrandIdCrescendo {
  CARGILL("Cargill"),
  PROVIMI("Provimi"),
  NUTRENA("Nutrena"),
  ACCO("Acco"),
  MULTI("Multi"),
  EWOS("EWOS"),
  PURINA("Purina"),
  NUTRON("Nutron");

  private final String value;

  @JsonCreator
  BrandIdCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
