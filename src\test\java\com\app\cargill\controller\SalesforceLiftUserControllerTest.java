/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.mockito.Mockito.when;

import com.app.cargill.model.SalesForceUser;
import com.app.cargill.service.admin.UserAdminService;
import com.app.cargill.sf.cc.service.LiftUserService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class SalesforceLiftUserControllerTest {

  @Mock private LiftUserService liftUserService;
  @Mock private UserAdminService userAdminService;
  @InjectMocks private SalesforceLiftUserController controller;

  @Test
  void getUsersDetailFromLiftTest() {
    SalesForceUser user = new SalesForceUser();
    user.setEmail("type");

    when(userAdminService.updateLiftUsersData()).thenReturn(Flux.just(user));

    Flux<SalesForceUser> result = controller.getUsersDetailFromLift();
    StepVerifier.create(result).expectNextCount(1).verifyComplete();
  }
}
