/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static com.app.cargill.utils.PageableUtil.getPageable;

import com.app.cargill.dto.CityDto;
import com.app.cargill.dto.CountryDto;
import com.app.cargill.dto.StateDto;
import com.app.cargill.model.Cities;
import com.app.cargill.model.Countries;
import com.app.cargill.model.States;
import com.app.cargill.repository.CitiesRepository;
import com.app.cargill.repository.CountriesRepository;
import com.app.cargill.repository.StatesRepository;
import com.app.cargill.service.IGeoLocationService;
import java.time.Instant;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class GeoLocationServiceImpl implements IGeoLocationService {

  private final CountriesRepository countriesRepository;
  private final StatesRepository statesRepository;
  private final CitiesRepository citiesRepository;

  @Override
  public List<CountryDto> fetchAllCountries(
      Locale locale, ResourceBundleMessageSource resourceBundleMessageSource) {

    List<Countries> countriesList = countriesRepository.findAll();
    return countriesList.stream()
        .map(e -> mapToCountriesDto(e, locale, resourceBundleMessageSource))
        .toList();
  }

  @Override
  public Page<CountryDto> fetchAllCountriesPaginated(
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);
    Page<Countries> countries = countriesRepository.findAllByUpdatedDate(lastSyncTime, pageable);
    if (Objects.isNull(countries) || countries.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        countries.stream()
            .map(e -> mapToCountriesDto(e, locale, resourceBundleMessageSource))
            .toList(),
        pageable,
        countries.getTotalElements());
  }

  private CountryDto mapToCountriesDto(
      Countries country, Locale locale, ResourceBundleMessageSource source) {
    String name = null;
    if (source != null && country.getCountryDocument().getCountryName() != null) {
      name =
          getAllLangCaptionsForSingleEnum(
              country.getCountryDocument().getCountryName(),
              country.getCountryDocument().getCountryName(),
              locale,
              source);
    }
    return CountryDto.builder()
        .id(country.getCountryDocument().getId())
        .countryName(name)
        .countryCode(country.getCountryDocument().getCountryCode())
        .countryBusinessIdMapping(country.getCountryDocument().getCountryBusinessIdMapping())
        .deleted(false)
        .updatedDate(country.getUpdatedDate().toInstant())
        .createdDate(country.getCreatedDate().toInstant())
        .countryKey(country.getCountryDocument().getCountryName())
        .localId(country.getLocalId())
        .build();
  }

  private String getAllLangCaptionsForSingleEnum(
      String key,
      String defaultValue,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource) {
    key = key.replaceAll("\\s+", "_");

    return resourceBundleMessageSource.getMessage(key, new Object[] {}, defaultValue, locale);
  }

  @Override
  public List<StateDto> fetchStateByCountryCode(
      String countryCode, Locale locale, ResourceBundleMessageSource resourceBundleMessageSource) {

    List<States> statesList;
    if (countryCode == null) {
      statesList = statesRepository.findAll();
    } else {
      statesList = statesRepository.findByCountryCode(countryCode);
    }

    if (statesList.isEmpty()) {
      return new ArrayList<>();
    }
    return statesList.stream()
        .map(s -> mapToStatesDto(s, locale, resourceBundleMessageSource))
        .toList();
  }

  @Override
  public Page<StateDto> fetchStateByCountryCodePaginated(
      String countryCode,
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);
    Page<States> states;
    if (StringUtils.isBlank(countryCode)) {
      states = statesRepository.findAllByUpdatedDate(lastSyncTime, pageable);
    } else {
      states = statesRepository.findByCountryCode(countryCode, lastSyncTime, pageable);
    }
    if (Objects.isNull(states) || states.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        states.stream().map(s -> mapToStatesDto(s, locale, resourceBundleMessageSource)).toList(),
        pageable,
        states.getTotalElements());
  }

  @Override
  public Page<StateDto> fetchStatesByCountryCodesPaginated(
      List<String> countryCodes,
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting,
      Locale locale,
      ResourceBundleMessageSource resourceBundleMessageSource) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);
    Page<States> states;
    if (CollectionUtils.isEmpty(countryCodes)) {
      states = statesRepository.findAllByUpdatedDate(lastSyncTime, pageable);
    } else {
      states = statesRepository.findByCountryCodeIn(countryCodes, lastSyncTime, pageable);
    }
    if (Objects.isNull(states) || states.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        states.stream().map(s -> mapToStatesDto(s, locale, resourceBundleMessageSource)).toList(),
        pageable,
        states.getTotalElements());
  }

  private StateDto mapToStatesDto(States state, Locale locale, ResourceBundleMessageSource source) {
    String name = null;
    if (source != null && state.getStateDocument().getStateName() != null) {
      name =
          getAllLangCaptionsForSingleEnum(
              state.getStateDocument().getStateName(),
              state.getStateDocument().getStateName(),
              locale,
              source);
    }
    return StateDto.builder()
        .countryCode(state.getStateDocument().getCountryCode())
        .stateName(name)
        .stateCode(state.getStateDocument().getStateCode())
        .id(state.getStateDocument().getId())
        .stateKey(state.getStateDocument().getStateName())
        .deleted(false)
        .updatedDate(state.getUpdatedDate().toInstant())
        .createdDate(state.getCreatedDate().toInstant())
        .localId(state.getLocalId())
        .build();
  }

  @Override
  public List<CityDto> fetchCitiesByStateCodeAndCountryCode(String stateCode, String countryCode) {

    List<Cities> citiesList;
    if (StringUtils.isBlank(stateCode)) {
      citiesList = citiesRepository.findAll();
    } else {
      citiesList = citiesRepository.findByStateCodeAndCountryCode(stateCode, countryCode);
    }

    if (citiesList == null || citiesList.isEmpty()) {
      return new ArrayList<>();
    }
    return citiesList.stream().map(this::mapToCitiesDto).toList();
  }

  @Override
  public Page<CityDto> fetchCitiesByStateCodeAndCountryCodePaginated(
      String stateCode,
      String countryCode,
      int page,
      int size,
      String sortBy,
      Instant lastSyncTime,
      String sorting) {
    Pageable pageable = getPageable(page, size, sortBy, sorting);
    Page<Cities> cities;
    if (StringUtils.isBlank(stateCode)) {
      cities = citiesRepository.findAllByUpdatedDate(lastSyncTime, pageable);
    } else {
      cities =
          citiesRepository.findByStateCodeAndCountryCode(
              stateCode, countryCode, lastSyncTime, pageable);
    }

    if (Objects.isNull(cities) || cities.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        cities.stream().map(this::mapToCitiesDto).toList(), pageable, cities.getTotalElements());
  }

  private CityDto mapToCitiesDto(Cities city) {
    return CityDto.builder()
        .id(city.getCityDocument().getId())
        .countryCode(city.getCityDocument().getCountryCode())
        .countryName(city.getCityDocument().getCountryName())
        .name(city.getCityDocument().getName())
        .stateCode(city.getCityDocument().getStateCode())
        .stateName(city.getCityDocument().getStateName())
        .deleted(false)
        .createdDate(city.getCreatedDate().toInstant())
        .updatedDate(city.getUpdatedDate().toInstant())
        .localId(city.getLocalId())
        .build();
  }
}
