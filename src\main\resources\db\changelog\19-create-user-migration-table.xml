<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="019" author="anenkov">
    <sql>
      CREATE TABLE IF NOT EXISTS user_migration (
        id SERIAL not null,
        email varchar(255),
        status varchar(20),
        local_id varchar(255),
        created_date timestamp NOT NULL DEFAULT CURRENT_DATE,
        updated_date timestamp NOT NULL DEFAULT CURRENT_DATE,
        deleted BOOLEAN default false,
        primary key (id));

      CREATE UNIQUE INDEX IF NOT EXISTS user_migration_email_uidx ON user_migration(email);
    </sql>
  </changeSet>

</databaseChangeLog>
