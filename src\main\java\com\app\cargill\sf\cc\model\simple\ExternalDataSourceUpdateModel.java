/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.model.simple;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@ToString
public class ExternalDataSourceUpdateModel implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("System__c")
  private String systemName;

  @JsonProperty("Species__c")
  private String salesforceSiteId;

  @JsonProperty("Unique_External_Key__c")
  private String uniqueExternalKey;

  @JsonProperty("Account__c")
  private String salesforceAccountId;

  @JsonProperty("ID__c")
  private String idCustom;
}
