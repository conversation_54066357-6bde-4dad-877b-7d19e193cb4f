/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.dto.Oauth2Dto;
import com.app.cargill.service.ILoginService;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Service("loginServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings("java:S125") // remove when all commented code is removed
public class LoginServiceImpl implements ILoginService {

  // private final SpringDocProperties springDocProperties;

  private final OktaProperties oktaProperties;

  // @Autowired AzureADProperties azureADProperties;

  // @Autowired IUserService userServiceImpl;

  /*@Override
  public Object fetchAccessToken(String authServer, String token) throws Exception {
    final String baseUrl = getURLBase();
    switch (authServer) {
      case "azure" -> {

        // validate the token first
        URL url = new URL(azureADProperties.getIssuer());
        String[] split = url.getPath().split(Pattern.quote("/"));
        WebClient client = WebClient.create("https://login.microsoftonline.com/" + split[1]);
        Mono<DiscoveryKeyDto> stringMono =
            client
                .get()
                .uri(
                    builder ->
                        builder
                            .path("/discovery/keys")
                            .queryParam("appid", azureADProperties.getClientId())
                            .build())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
                // .header("Authorization", "Basic " + encodedClientCred)
                // .body(Mono.just(body), String.class)
                .retrieve()
                .bodyToMono(DiscoveryKeyDto.class);
        DiscoveryKeyDto block = stringMono.block();
        Map<String, Object> headers = parseJWTHeaders(token);
        Long exp = (Long) parseJWT(token).get("exp");
        boolean found =
            block.getKeys().stream()
                    .filter(
                        m ->
                            m.get("x5t").toString().equals(headers.get("x5t").toString())
                                && m.get("kid").toString().equals(headers.get("kid").toString()))
                    .count()
                > 0;
        if (!found
            || LocalDateTime.ofEpochSecond(exp, 0, ZoneOffset.UTC)
                .isBefore(LocalDateTime.now(ZoneOffset.UTC))) {
          throw new Exception("Token not verified or expired");
        }

        log.info("received token from /token endpoint : {} for Azure AD", token);
        UserDto userDto =
            buildUserDto(
                parseJWT(token), UUID.randomUUID().toString(), AuthenticationPlatform.AZURE_AD);
        userServiceImpl.saveOrUpdate(userDto);
        return fetchAuthServerAccessToken(
            userDto.getPrincipalName(), userDto.getPassword(), baseUrl);
      }
      case "okta" -> {
        AccessTokenVerifier jwtVerifier =
            JwtVerifiers.accessTokenVerifierBuilder()
                .setIssuer(oktaProperties.getIssuer())
                .setAudience("api://default") // defaults to 'api://default'
                .setConnectionTimeout(Duration.ofSeconds(10)) // defaults to 1s
                .setRetryMaxAttempts(2) // defaults to 2
                .setRetryMaxElapsed(Duration.ofSeconds(10)) // defaults to 10s
                .build();

        Jwt jwt = jwtVerifier.decode(token);

        // fetch user info
        // https://dev-16033766.okta.com/oauth2/default/v1/userinfo
        WebClient client = WebClient.create(oktaProperties.getIssuer());
        Mono<Map> stringMono =
            client
                .get()
                .uri(builder -> builder.path("/v1/userinfo").build())
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
                .header("Authorization", "Bearer " + token)
                // .body(Mono.just(body), String.class)
                .retrieve()
                .bodyToMono(Map.class);
        Map claims = stringMono.block();
        UserDto userDto =
            buildUserDto(claims, UUID.randomUUID().toString(), AuthenticationPlatform.OKTA);
        userServiceImpl.saveOrUpdate(userDto);
        return fetchAuthServerAccessToken(
            userDto.getPrincipalName(), userDto.getPassword(), baseUrl);
      }
      default -> {
        throw new IllegalStateException("Unexpected value: " + authServer);
      }
    }
  }*/

  @Override
  public Oauth2Dto fetchAccessTokenUsingOkta(MultiValueMap<String, String> dto) {
    String body =
        "redirect_uri=com.embeddedauth://callback&scope=offline_access openid profile&client_id="
            + oktaProperties.getClientId()
            + "&grant_type=password&username="
            + dto.getOrDefault("username", Collections.singletonList(""))
                .toString()
                .substring(
                    1,
                    dto.getOrDefault("username", Collections.singletonList("")).toString().length()
                        - 1)
            + "&password="
            + dto.getOrDefault("password", Collections.singletonList(""))
                .toString()
                .substring(
                    1,
                    dto.getOrDefault("password", Collections.singletonList("")).toString().length()
                        - 1);
    log.trace("created body for okta");
    WebClient client = WebClient.create(oktaProperties.getIssuer());
    Mono<Oauth2Dto> stringMono =
        client
            .post()
            .uri("/v1/token")
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
            .header("Accept", "application/json")
            .body(Mono.just(body), String.class)
            .retrieve()
            .bodyToMono(Oauth2Dto.class);
    log.trace("returning response from okta");
    return stringMono.block();
  }

  @Override
  public boolean logout() {
    return true;
  }

  /* @Override
  public Object fetchAccessTokenByOktaCode(String code, String state)
      throws JwtVerificationException, MalformedURLException {
    final String baseUrl = getURLBase();
    String body =
        "grant_type=authorization_code&code="
            + code
            + "&redirect_uri="
            + baseUrl
            + "/auth/fetchAccessTokenByOktaCode";
    log.info("created body : {} for okta", body);
    String encodedClientCred =
        Base64.getEncoder()
            .encodeToString(
                (oktaProperties.getClientId() + ":" + ""*/
  /*oktaProperties.getClientSecret()*/
  /*)
                  .getBytes(StandardCharsets.UTF_8));
  WebClient client = WebClient.create(oktaProperties.getIssuer());
  Mono<Oauth2Dto> stringMono =
      client
          .post()
          .uri("/v1/token")
          .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
          .header("Authorization", "Basic " + encodedClientCred)
          .body(Mono.just(body), String.class)
          .retrieve()
          .bodyToMono(Oauth2Dto.class);
  */
  /*AccessTokenVerifier jwtVerifier = JwtVerifiers.accessTokenVerifierBuilder()
  .setIssuer(oktaProperties.getIssuer())
  .setAudience("api://default")                   // defaults to 'api://default'
  .setConnectionTimeout(Duration.ofSeconds(10))    // defaults to 1s
  .setRetryMaxAttempts(2)                     // defaults to 2
  .setRetryMaxElapsed(Duration.ofSeconds(10)) // defaults to 10s
  .build();*/
  /*

  // Jwt jwt = jwtVerifier.decode(oauth2.getAccessToken());
  IdTokenVerifier build =
      JwtVerifiers.idTokenVerifierBuilder()
          .setIssuer(oktaProperties.getIssuer())
          .setClientId(oktaProperties.getClientId())
          .setIssuer(oktaProperties.getIssuer())
          .setConnectionTimeout(Duration.ofSeconds(10)) // defaults to 1s
          .setRetryMaxAttempts(2) // defaults to 2
          .setRetryMaxElapsed(Duration.ofSeconds(10)) // defaults to 10s
          .build();

  log.info("Starting call : {} for okta", "/v1/token");
  Oauth2Dto oauth2 = stringMono.block();
  log.info("Response received for call : {} for okta : ", "/v1/token", oauth2);
  Jwt jwt = build.decode(oauth2.getIdToken(), ""*/
  /*oktaProperties.getNonce()*/
  /*);
    Map<String, Object> claims = jwt.getClaims();
    UserDto userDto = buildUserDto(claims, code, AuthenticationPlatform.OKTA);
    userServiceImpl.saveOrUpdate(userDto);
    return fetchAuthServerAccessToken(userDto.getPrincipalName(), userDto.getPassword(), baseUrl);
  }*/

  /*UserDto buildUserDto(
      Map<String, Object> claims, String code, AuthenticationPlatform authPlatForm) {
    UserDto userDto =
        UserDto.builder()
            .roles(Arrays.asList(RoleDto.builder().code("ROLE_USER").build()))
            .fullName((String) claims.get("name"))
            .accountType(UserAccountType.EMAIL)
            .authenticationPlatform(authPlatForm)
            .email((String) claims.getOrDefault("upn", claims.get("email")))
            .principalName(
                (String) claims.getOrDefault("unique_name", claims.get("preferred_username")))
            .password(code)
            .build();
    return userDto;
  }

  Oauth2Dto fetchAuthServerAccessToken(String userName, String password, String baseUrl) {

    String body = "grant_type=password&username=" + userName + "&password=" + password;
    String encodedClientCred =
        Base64.getEncoder()
            .encodeToString(
                (springDocProperties.getClientId() + ":" + springDocProperties.getClientSecret())
                    .getBytes(StandardCharsets.UTF_8));
    WebClient client = WebClient.create(baseUrl);
    Mono<Oauth2Dto> stringMono =
        client
            .post()
            .uri("/security/oauth/token")
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
            .header("Authorization", "Basic " + encodedClientCred)
            .body(Mono.just(body), String.class)
            .retrieve()
            .bodyToMono(Oauth2Dto.class);
    return stringMono.block();
  }

  public String getURLBase() throws MalformedURLException, MalformedURLException {

    URL requestURL = new URL(springDocProperties.getAccessTokenUrl());
    String port = requestURL.getPort() == -1 ? "" : ":" + requestURL.getPort();
    return requestURL.getProtocol() + "://" + requestURL.getHost() + port;
  }

  @Override
  public Object fetchAccessTokenByAzureAD(String code, String state) throws Exception {
    final String baseUrl = getURLBase();
    String body =
        "grant_type=authorization_code&code="
            + code
            + "&redirect_uri="
            + baseUrl
            + "/auth/fetchAccessTokenByAzureAD";
    log.info("created body : {} for Azure AD", body);
    String encodedClientCred =
        Base64.getEncoder()
            .encodeToString(
                (azureADProperties.getClientId() + ":" + azureADProperties.getClientSecret())
                    .getBytes(StandardCharsets.UTF_8));
    WebClient client = WebClient.create(azureADProperties.getIssuer());
    Mono<Oauth2Dto> stringMono =
        client
            .post()
            .uri("/token")
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED.toString())
            .header("Authorization", "Basic " + encodedClientCred)
            .body(Mono.just(body), String.class)
            .retrieve()
            .bodyToMono(Oauth2Dto.class);
    Oauth2Dto oauth2 = stringMono.block();
    log.info("received token from /token endpoint : {} for Azure AD", oauth2);
    UserDto userDto =
        buildUserDto(
            parseJWT(oauth2.getAccessToken()),
            UUID.randomUUID().toString(),
            AuthenticationPlatform.AZURE_AD);
    userServiceImpl.saveOrUpdate(userDto);
    return fetchAuthServerAccessToken(userDto.getPrincipalName(), userDto.getPassword(), baseUrl);
  }

  private Map<String, Object> parseJWT(String accessToken) throws Exception {
    try {
      var decodedJWT = SignedJWT.parse(accessToken);
      var header = decodedJWT.getHeader().toString();
      var payload = decodedJWT.getPayload().toJSONObject();
      return payload;
    } catch (Exception e) {
      throw new Exception("Invalid token!");
    }
  }

  private Map<String, Object> parseJWTHeaders(String accessToken) throws Exception {
    try {
      var decodedJWT = SignedJWT.parse(accessToken);
      var header = decodedJWT.getHeader().toJSONObject();

      return header;
    } catch (Exception e) {
      throw new Exception("Invalid token!");
    }
  }*/
}
