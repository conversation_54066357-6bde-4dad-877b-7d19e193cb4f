/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApplicationMappingCrescendo implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @JsonProperty("ExternalSystemId")
  private String externalSystemId;

  @JsonProperty("ExternalSystemName")
  private String externalSystemName;

  @JsonProperty("SFDCAccountId")
  private String sfdcAccountId;

  @JsonProperty("LabyrinthAccountId")
  private String labyrinthAccountId;

  @JsonProperty("XRefExternalSystemId")
  private String xRefExternalSystemId;

  @JsonProperty("XRefExternalSystemName")
  private String xRefExternalSystemName;
}
