/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.dto.SiteDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.ISiteService;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class SiteControllerTest {

  @Mock private ISiteService siteService;

  @InjectMocks private SiteController controller;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void getAllSitesByAccountId() {
    when(siteService.getAllSitesByAccountId(any(), anyInt(), anyInt(), any(), any(), any()))
        .thenReturn(generatePage());
    ResponseEntity<ResponseEntityDto<Page<SiteDto>>> result =
        controller.getAllSitesByAccountId(
            UUID.randomUUID().toString(), 0, 10, "sb", Instant.now(), "s");
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
    assertEquals(2, result.getBody().getData().getTotalElements());
  }

  @Test
  void save() throws NotFoundDEException, AlreadyExistsDEException, Exception {
    when(siteService.save(any(), any(), any())).thenReturn(SiteDto.builder().build());
    ResponseEntity<ResponseEntityDto<Object>> result =
        controller.save(SiteDto.builder().build(), locale.getLanguage());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  @Test
  void update()
      throws JsonProcessingException, NotFoundDEException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    when(siteService.update(any(), any(), any())).thenReturn(SiteDto.builder().build());
    ResponseEntity<ResponseEntityDto<Object>> result =
        controller.update(SiteDto.builder().build(), locale.getLanguage());
    assertNotNull(result);
    assertNotNull(Objects.requireNonNull(result.getBody()));
    assertNotNull(result.getBody().getData());
  }

  private List<SiteDto> generateSites() {
    return List.of(SiteDto.builder().build(), SiteDto.builder().build());
  }

  private Page<SiteDto> generatePage() {
    return new PageImpl<>(generateSites());
  }

  @Test
  void testSaveSiteFailure()
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {

    when(siteService.save(any(), any(), any()))
        .thenThrow(new CustomDEExceptions("Test exception message"));

    ResponseEntity<ResponseEntityDto<Object>> response =
        controller.save(generateSites().get(0), locale.getLanguage());

    verify(siteService, times(1)).save(any(), any(), any());

    assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
  }

  @Test
  void testUpdateSitesFailure()
      throws AlreadyExistsDEException, NotFoundDEException, JsonProcessingException,
          IllegalAccessException, ClassNotFoundException, CustomDEExceptions {

    when(siteService.update(any(), any(), any()))
        .thenThrow(new CustomDEExceptions("Test exception message"));

    ResponseEntity<ResponseEntityDto<Object>> response =
        controller.update(generateSites().get(0), locale.getLanguage());

    verify(siteService, times(1)).update(any(), any(), any());

    assertEquals(HttpStatus.NOT_ACCEPTABLE, response.getStatusCode());
  }
}
