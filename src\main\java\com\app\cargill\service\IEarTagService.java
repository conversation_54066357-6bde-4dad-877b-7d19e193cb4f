/* Cargill Inc.(C) 2022 */
package com.app.cargill.service;

import com.app.cargill.dto.EarTagDto;
import jakarta.validation.Valid;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;

public interface IEarTagService {

  Page<EarTagDto> getAllEarTagsPaginated(
      int page, int size, String sortBy, String sorting, Instant lastSyncTime);

  List<EarTagDto> save(@Valid EarTagDto earTagDto);

  List<EarTagDto> update(EarTagDto earTagDto);
}
