/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static com.app.cargill.utils.MathUtils.roundAvoid;

import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.*;
import java.util.List;
import java.util.Objects;

public class LocomotionScoreCalculation {
  private static final double MILK_SCORE_THREE = 5.1;
  private static final double MILK_SCORE_FOUR = 16.8;
  private static final double MILK_SCORE_FIVE = 36;
  private static final double MILK_SCORE_THREE_PER_HUNDRED = 0.051;
  private static final double MILK_SCORE_FOUR_PER_HUNDRED = 0.168;
  private static final double MILK_SCORE_FIVE_PER_HUNDRED = 0.36;
  private static final double HUNDRED = 100;
  private static final double DAYS_IN_YEAR = 365;

  public LocomotionTool calculateFields(LocomotionTool tool) {
    if (tool == null || tool.getHerd() == null) return null;

    if (tool.getPens() != null) {
      for (LocomotionToolItem pen : tool.getPens()) {
        if (pen.getCategories() != null) {
          for (LocomotionToolItemCategoryItem item : pen.getCategories()) {
            item.percentOfPen = percentOfPen(item, pen);
            item.totalCountOfAnimalsInPenPerScore = totalCountOfAnimalsInPenPerScore(pen, item);
          }
          pen.averageLocomotionScore = averageLocomotionScore(pen);
          pen.standardDeviation = standardDeviation(pen);
          pen.milkLoss = milkLoss(pen);
          pen.toolStatus = toolStatus(pen);
        }
      }

      tool.getHerd().averageLocomotionScore = averageLocomotionHerdScore(tool);
      tool.getHerd().toolStatus = toolHerdStatus(tool.getHerd());
    }
    if (tool.getHerd().getCategories() != null) {
      for (LocomotionHerdToolItemCategoryItem herdItem : tool.getHerd().getCategories()) {
        herdItem.animalsObserved = animalsObserved(tool.getHerd(), herdItem);
        herdItem.herdAverage = herdAverage(tool.getHerd(), herdItem);
        herdItem.totalAnimals = totalAnimals(tool.getHerd(), herdItem);
      }

      tool.getHerd().milkLoss = milkHerdLoss(tool.getHerd());
      tool.getHerd().milkLossInKgPerDay = milkLossInKgPerDay(tool.getHerd());
      tool.getHerd().milkLossInKgPerYear = milkLossInKgPerYear(tool.getHerd());
      tool.getHerd().revenueLossInDollarPerDay = revenueLossInDollarPerDay(tool.getHerd());
      tool.getHerd().revenueLossInDollarPerYear = revenueLossInDollarPerYear(tool.getHerd());
      tool.getHerd().standardDeviationScore = standardDeviationScore(tool.getHerd());
    }
    return tool;
  }

  private Double percentOfPen(
      LocomotionToolItemCategoryItem penCategoryItem, LocomotionToolItem penItem) {
    double percentOfPen = 0.0;
    if (penCategoryItem.getAnimalsObserved() != null && penCategoryItem.animalsObserved != 0) {
      int animalsObservedTotal =
          penItem.getCategories().stream()
              .mapToInt(x -> Objects.requireNonNullElse(x.animalsObserved, 0))
              .sum();
      if (animalsObservedTotal > 0) {
        percentOfPen =
            roundAvoid((double) penCategoryItem.animalsObserved / animalsObservedTotal * 100, 1);
      }
    }
    return percentOfPen;
  }

  private Integer totalCountOfAnimalsInPenPerScore(
      LocomotionToolItem pen, LocomotionToolItemCategoryItem penItem) {
    if (penItem.percentOfPen == 0 || pen.getTotalAnimalsInPen() == null) return 0;
    return (int) roundAvoid(pen.getTotalAnimalsInPen() * (penItem.percentOfPen / 100), 0);
  }

  private Double averageLocomotionScore(LocomotionToolItem pen) {
    double categoryScore = 0.0;
    double sumOfPercents = 0.0;

    for (int iterator = 1; iterator <= 5; iterator++) {
      int finalIterator = iterator;
      LocomotionToolItemCategoryItem penItem =
          pen.getCategories().stream()
              .filter(x -> x.getCategory() != null && x.getCategory() == finalIterator)
              .findFirst()
              .orElse(null);

      if (penItem != null && penItem.percentOfPen != null) {
        categoryScore += penItem.percentOfPen * penItem.getCategory();
        sumOfPercents += penItem.percentOfPen;
      }
    }

    if (sumOfPercents == 0) return 0.0;
    else return roundAvoid(categoryScore / sumOfPercents, 1);
  }

  private Double standardDeviation(LocomotionToolItem pen) {
    double sumOfSquareDelta = 0;
    double n = 0;
    final int perAnimal = 1;

    List<LocomotionToolItemCategoryItem> activeScores =
        pen.getCategories().stream()
            .filter(arg -> arg.percentOfPen != null && arg.percentOfPen > 0)
            .toList();

    for (LocomotionToolItemCategoryItem score : activeScores) {
      int i = 0;
      for (; i < score.animalsObserved; i++) {
        double delta = Math.abs(perAnimal * score.getCategory() - pen.averageLocomotionScore);
        double sqDelta = Math.pow(delta, 2);
        sumOfSquareDelta += sqDelta;
      }
      n += i;
    }
    if (n == 0) return 0.0;
    double variance = sumOfSquareDelta / n;
    double sd = Math.sqrt(variance);
    return roundAvoid(sd, 2);
  }

  private Double milkLoss(LocomotionToolItem pen) {

    Integer totalCountThree = null;
    Integer totalCountFour = null;
    Integer totalCountFive = null;

    for (LocomotionToolItemCategoryItem categoryItem : pen.getCategories()) {
      if (totalCountThree == null
          && categoryItem.getCategory() != null
          && categoryItem.getCategory() == 3) {
        totalCountThree =
            Objects.requireNonNullElse(categoryItem.getTotalCountOfAnimalsInPenPerScore(), 0);
      } else if (totalCountFour == null
          && categoryItem.getCategory() != null
          && categoryItem.getCategory() == 4) {
        totalCountFour =
            Objects.requireNonNullElse(categoryItem.getTotalCountOfAnimalsInPenPerScore(), 0);
      } else if (totalCountFive == null
          && categoryItem.getCategory() != null
          && categoryItem.getCategory() == 5) {
        totalCountFive =
            Objects.requireNonNullElse(categoryItem.getTotalCountOfAnimalsInPenPerScore(), 0);
      }
    }

    if (pen.getTotalAnimalsInPen() == null
        || pen.getTotalAnimalsInPen() == 0
        || totalCountThree == null
        || totalCountFour == null
        || totalCountFive == null) return 0.0;
    else {
      double milkLoss =
          (totalCountThree * pen.getMilkProductionInKg() * (MILK_SCORE_THREE / 100)
                  + totalCountFour * pen.getMilkProductionInKg() * (MILK_SCORE_FOUR / 100)
                  + totalCountFive * pen.getMilkProductionInKg() * (MILK_SCORE_FIVE / 100))
              / pen.getTotalAnimalsInPen();
      return roundAvoid(milkLoss, 2);
    }
  }

  private ToolStatuses toolStatus(LocomotionToolItem pen) {
    return Objects.requireNonNullElse(pen.averageLocomotionScore, 0.0) > 0
        ? ToolStatuses.Completed
        : ToolStatuses.NotStarted;
  }

  private ToolStatuses toolHerdStatus(LocomotionHerdToolItem herd) {
    return Objects.requireNonNullElse(herd.averageLocomotionScore, 0.0) > 0
        ? ToolStatuses.Completed
        : ToolStatuses.NotStarted;
  }

  private Integer animalsObserved(
      LocomotionHerdToolItem herd, LocomotionHerdToolItemCategoryItem herdItem) {
    int total = 0;
    if (herd.getPensForVisit() == null) return total;
    for (LocomotionToolItem item : herd.getPensForVisit()) {
      total +=
          item.getCategories().stream()
              .filter(
                  x ->
                      x.getCategory() != null
                          && x.getCategory().equals(herdItem.category)
                          && x.getAnimalsObserved() != null)
              .mapToInt(LocomotionToolItemCategoryItem::getAnimalsObserved)
              .sum();
    }
    return total;
  }

  private Double herdAverage(
      LocomotionHerdToolItem herd, LocomotionHerdToolItemCategoryItem herdItem) {
    if (herdItem.animalsObserved == null
        || herdItem.animalsObserved == 0
        || herd.getTotalAnimalsInHerd() == null
        || herd.getTotalAnimalsInHerd() == 0) return 0.0;
    else {
      int animalsInAllPensObservedTotal =
          herd.getCategories().stream()
              .filter(hc -> hc.animalsObserved != null)
              .mapToInt(LocomotionHerdToolItemCategoryItem::getAnimalsObserved)
              .sum();

      return roundAvoid((double) herdItem.animalsObserved / animalsInAllPensObservedTotal * 100, 1);
    }
  }

  private Integer totalAnimals(
      LocomotionHerdToolItem herd, LocomotionHerdToolItemCategoryItem herdItem) {
    if (herdItem.herdAverage == 0) return 0;
    else
      return (int)
          roundAvoid(
              Objects.requireNonNullElse(herd.getTotalAnimalsInHerd(), 0)
                  * (herdItem.herdAverage)
                  / 100,
              0);
  }

  private Double averageLocomotionHerdScore(LocomotionTool tool) {
    double avgLocomotionScoreSum = 0.0;
    double totalAnimalsInAllPens = 0.0;

    for (LocomotionToolItem item : tool.getPens()) {
      totalAnimalsInAllPens +=
          item.getTotalAnimalsInPen() == null ? 0 : item.getTotalAnimalsInPen();
      avgLocomotionScoreSum +=
          item.averageLocomotionScore
              * (item.getTotalAnimalsInPen() == null ? 0 : item.getTotalAnimalsInPen());
    }
    if (totalAnimalsInAllPens == 0) return 0.0;
    else return avgLocomotionScoreSum / totalAnimalsInAllPens;
  }

  private Double standardDeviationScore(LocomotionHerdToolItem herd) {
    double avg = Objects.requireNonNullElse(herd.averageLocomotionScore, 0.0);
    double sumOfSquareDelta = 0;
    double n = 0;
    final int perAnimal = 1;

    List<LocomotionHerdToolItemCategoryItem> activeScores =
        herd.getCategories().stream()
            .filter(arg -> arg.animalsObserved != null && arg.animalsObserved > 0)
            .toList();

    for (LocomotionHerdToolItemCategoryItem score : activeScores) {
      int i = 0;
      for (; i < score.animalsObserved; i++) {
        double delta = Math.abs(perAnimal * score.category - avg);
        double sqDelta = Math.pow(delta, 2);
        sumOfSquareDelta += sqDelta;
      }
      n += i;
    }
    if (n == 0) return null;
    double variance = sumOfSquareDelta / n;
    double sd = Math.sqrt(variance);
    return roundAvoid(sd, 2);
  }

  private Double milkProductionInKgLocal(LocomotionHerdToolItem herd) {
    return herd.getMilkProductionInKg() != null && herd.getMilkProductionInKg() > 0
        ? herd.getMilkProductionInKg()
        : null;
  }

  private Double milkHerdLoss(LocomotionHerdToolItem herd) {

    Double herdAverageThree = null;
    Double herdAverageFour = null;
    Double herdAverageFive = null;

    for (LocomotionHerdToolItemCategoryItem categoryItem : herd.getCategories()) {
      if (herdAverageThree == null
          && categoryItem.getCategory() != null
          && categoryItem.getCategory() == 3) {
        herdAverageThree = Objects.requireNonNullElse(categoryItem.getHerdAverage(), 0.0);
      } else if (herdAverageFour == null
          && categoryItem.getCategory() != null
          && categoryItem.getCategory() == 4) {
        herdAverageFour = Objects.requireNonNullElse(categoryItem.getHerdAverage(), 0.0);
      } else if (herdAverageFive == null
          && categoryItem.getCategory() != null
          && categoryItem.getCategory() == 5) {
        herdAverageFive = Objects.requireNonNullElse(categoryItem.getHerdAverage(), 0.0);
      }
    }

    if ((herd.getTotalAnimalsInHerd() == null || herd.getTotalAnimalsInHerd() == 0)
        || (herdAverageThree == null)
        || (herdAverageFour == null)
        || (herdAverageFive == null)) return 0.0;
    else {
      Double milkProductionInKgLocal = milkProductionInKgLocal(herd);
      milkProductionInKgLocal = milkProductionInKgLocal == null ? 0 : milkProductionInKgLocal;
      double milkLossSum =
          ((herdAverageThree * milkProductionInKgLocal * MILK_SCORE_THREE_PER_HUNDRED)
                  + (herdAverageFour * milkProductionInKgLocal * MILK_SCORE_FOUR_PER_HUNDRED)
                  + (herdAverageFive * milkProductionInKgLocal * MILK_SCORE_FIVE_PER_HUNDRED))
              / HUNDRED;

      return roundAvoid(milkLossSum, 4);
    }
  }

  private Double milkLossInKgPerDay(LocomotionHerdToolItem herd) {
    return herd.getMilkLoss() > 0
        ? roundAvoid(
            (herd.getMilkLoss() * Objects.requireNonNullElse(herd.getTotalAnimalsInHerd(), 0)), 2)
        : 0.0;
  }

  private Double milkLossInKgPerYear(LocomotionHerdToolItem herd) {
    if (herd.getMilkLoss() > 0) {
      double milkLossInKgPerDay =
          herd.getMilkLoss() * Objects.requireNonNullElse(herd.getTotalAnimalsInHerd(), 0);
      return roundAvoid(milkLossInKgPerDay * DAYS_IN_YEAR, 2);
    }
    return 0.0;
  }

  private Double revenueLossInDollarPerDay(LocomotionHerdToolItem herd) {
    return herd.milkPriceAtSiteLevel != null
        ? roundAvoid(herd.getMilkLossInKgPerDay() * herd.milkPriceAtSiteLevel, 1)
        : null;
  }

  private Double revenueLossInDollarPerYear(LocomotionHerdToolItem herd) {
    return herd.milkPriceAtSiteLevel != null
        ? roundAvoid(herd.getMilkLossInKgPerYear() * herd.milkPriceAtSiteLevel, 1)
        : null;
  }
}
