<div class="container  mid-body">
    <div class="pt-0">
        <div class="legend-head">
            <div class="count">${toolNumber!}</div>
            <div class="main-title">
                <span class="sm-head">${localization.getMessage("VisitViewModel.ProductivityItem", [], "Productivity",
                    locale)}</span>
                <span class="lg-head">${localization.getMessage("MilkSoldSpinnerViewModel.Title", [], "Milk Sold
                    Evaluation", locale)}</span>
            </div>
            <div style="font-size: 1;color: white;">0000151515MSE</div>
        </div>
        <#if
            model.milkSoldEvaluationTool.herdLevelInformationLeftTable??||model.milkSoldEvaluationTool.herdLevelInformationRightTable??>
            <h3 class="title-secondary mb-1 d-flex justify-space-between">
                <span>${localization.getMessage("MetabolicIncidenceInputsViewModel.Herd", [], "Herd Level Information",
                    locale)}</span>
            </h3>
            <div class="row mx-neg-4">
                <#if model.milkSoldEvaluationTool.herdLevelInformationLeftTable??>
                    <div class="col-6 mb-1 px-4 table-secondary">
                        <table class="w-50">
                            <tbody>
                                <#list model.milkSoldEvaluationTool.herdLevelInformationLeftTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.milkSoldEvaluationTool.herdLevelInformationLeftTable[prop]!}</td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </#if>
                <#if model.milkSoldEvaluationTool.herdLevelInformationRightTable??>
                    <div class="col-6 mb-1 px-4 table-secondary">
                        <table class="w-50">
                            <tbody>
                                <#list model.milkSoldEvaluationTool.herdLevelInformationRightTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.milkSoldEvaluationTool.herdLevelInformationRightTable[prop]!}</td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </#if>
            </div>
        </#if>
        <#if model.milkSoldEvaluationTool.outputLeftTable?? || model.milkSoldEvaluationTool.outputRightTable??>
            <h3 class="title-secondary mb-1 d-flex justify-space-between">
                <span>${localization.getMessage("Outputs", [], "Outputs", locale)}</span>
            </h3>
            <div class="row mx-neg-4">
                <#if model.milkSoldEvaluationTool.outputLeftTable??>
                    <div class="col-6 mb-1 px-4 table-secondary">
                        <table class="w-50 mb-1">
                            <tbody>
                                <#list model.milkSoldEvaluationTool.outputLeftTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.milkSoldEvaluationTool.outputLeftTable[prop]!}</td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </#if>
                <#if model.milkSoldEvaluationTool.outputRightTable??>
                    <div class="col-6 mb-1 px-4 table-secondary">
                        <table class="w-50 mb-1">
                            <tbody>
                                <#list model.milkSoldEvaluationTool.outputRightTable?keys as prop>
                                    <tr>
                                        <td>${prop!}</td>
                                        <td>${model.milkSoldEvaluationTool.outputRightTable[prop]!}</td>
                                    </tr>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </#if>
            </div>
        </#if>
    </div>
</div>
<#if model.milkSoldEvaluationTool.milkProcessorInformation?? || (model.milkSoldEvaluationTool.graphs?? &&
    model.milkSoldEvaluationTool.graphs[0]??)>
    <div class="break-page"></div>

    <div class="container mid-body">
        <div class="pt-0">
            <#if model.milkSoldEvaluationTool.milkProcessorInformation ?? && model.milkSoldEvaluationTool.milkProcessorInformation[0]??>
                <div class="row mx-neg-4">
                    <div class="col-12 px-4">
                        <h6>${localization.getMessage("MilkSoldEvaluationInputsViewModel.MilkProcessorInformation", [],
                            "Milk Processor Information", locale)}</h6>
                    </div>
                    <div class="col-12 px-4 table-secondary">
                        <table>
                            <thead>
                                <tr>
                                    <#list model.milkSoldEvaluationTool.milkProcessorInformation[0]?keys as prop>
                                        <#list model.milkSoldEvaluationTool.milkProcessorInformation[0][prop] as key>
                                            <th>${key.column!}</th>
                                        </#list>
                                    </#list>
                                </tr>
                            </thead>
                            <tbody>
                                <#list model.milkSoldEvaluationTool.milkProcessorInformation as item>
                                    <#list item?keys as prop>
                                        <tr>
                                            <td colspan="10">
                                                <h3 class="title mb-0">${prop!}</h3>
                                            </td>
                                        </tr>
                                        <tr>
                                            <#list item[prop] as key>
                                                <td>${key.value!}</td>
                                            </#list>
                                        </tr>
                                    </#list>
                                </#list>
                            </tbody>
                        </table>
                    </div>
                </div>
            </#if>
            <#assign counter=0>
                <#if model.milkSoldEvaluationTool.graphs?? && model.milkSoldEvaluationTool.graphs[0]??>
                    <#list model.milkSoldEvaluationTool.graphs?chunk(2) as row>
                        <div class="row mx-neg-4 mt-2">
                            <#list row as graph>

                                <div class="col-6 px-4">
                                    <div class="card mb-1">
                                        <div class="card-header">
                                            <h4 class="text-center">${graph.chartLabels['CHART_TITLE']}</h4>
                                        </div>
                                        <div class="card-body">
                                            <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                                <canvas id="${linechart2}"></canvas>
                                        </div>
                                        <div class="card-footer">
                                            <div class="row">
                                                <div class="legend-wrap mb-2">
                                                    <p class="green-solid">${graph.chartLabels['Y_LEFT']}</p>
                                                </div>
                                                <div class="legend-wrap mb-2">
                                                    <p class="purple-solid">${graph.chartLabels['Y_RIGHT']}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <script>
                                    (function () {
                                        const colors = {
                                            purple: {
                                                default: "#1baca7",
                                                half: "#1baca778",
                                                quarter: "#1baca73b",
                                                zero: "#1baca71c"
                                            },
                                            indigo: {
                                                default: "#1baca7",
                                                quarter: "#1baca778"
                                            }
                                        };

                                        const yLeft = [
                                            <#list graph.dataPoints as dataPoint >
                                            ${ dataPoint.yaxisLeft!0.0} <#sep>, </#sep>
</#list >
];

                                        const yRight = [
                                            <#list graph.dataPoints as dataPoint >
                                            ${ dataPoint.yaxisRight!0.0} <#sep>, </#sep>
</#list >
];

                                        const labels = [
                                            <#list graph.dataPoints as dataPoint >
                                            '${dataPoint.visitDate!}' <#sep>, </#sep>
</#list >
];


                                        const ctx = document.getElementById("${linechart2}").getContext("2d");
                                        ctx.canvas.height = 100;

                                        gradient = ctx.createLinearGradient(0, 25, 0, 300);
                                        gradient.addColorStop(0, colors.purple.half);
                                        gradient.addColorStop(0.35, colors.purple.quarter);
                                        gradient.addColorStop(1, colors.purple.zero);

                                        const options = {
                                            type: "line",

                                            data: {
                                                labels: labels,
                                                datasets: [
                                                    {
                                                        borderColor: '#A160E6',
                                                        pointBackgroundColor: '#A160E6',
                                                        pointBorderColor: '#fff',
                                                        data: yRight,
                                                        lineTension: 0.2,
                                                        borderWidth: 1,
                                                        pointRadius: 6,
                                                        borderDash: [5, 4],
                                                        yAxisID: 'y1',
                                                    },
                                                    {
                                                        fill: true,
                                                        backgroundColor: gradient,
                                                        borderColor: '#1BACA7',
                                                        pointBackgroundColor: '#1BACA7',
                                                        pointBorderColor: '#fff',
                                                        data: yLeft,
                                                        lineTension: 0.2,
                                                        borderWidth: 1,
                                                        pointRadius: 6,
                                                        yAxisID: 'y',
                                                    }

                                                ]
                                            },
                                            options: {
                                                plugins: {
                                                    legend: {
                                                        display: false,
                                                    },
                                                    tooltip: {
                                                        callbacks: {
                                                            title: () => null // or function () { return null; }
                                                        },
                                                        yAlign: 'bottom',
                                                        backgroundColor: "#fff",
                                                        borderColor: "rgba(0, 0, 0, 0.25)",
                                                        borderWidth: 1,
                                                        displayColors: false,
                                                        bodyColor: "#1BACA7",
                                                        bodyAlign: "center",
                                                    },
                                                },

                                                layout: {
                                                    padding: {
                                                        top: 20,
                                                        right: 15
                                                    }
                                                },

                                                responsive: true,

                                                scales: {

                                                    y1: {
                                                        beginAtZero: true,
                                                        type: 'linear',
                                                        position: 'right',
                                                        title: {
                                                            display: true,
                                                            color: '#6C7782',
                                                            text: '${graph.chartLabels.Y_RIGHT}',
                                                            padding: {
                                                                bottom: 15,
                                                            },
                                                        },
                                                        grid: { display: false }
                                                    },
                                                    y: {
                                                        type: 'linear',
                                                        position: 'left',
                                                        title: {
                                                            display: true,
                                                            color: '#6C7782',
                                                            text: '${graph.chartLabels.Y_LEFT}',
                                                            padding: {
                                                                bottom: 15,
                                                            },
                                                        },
                                                        grid: { display: false }
                                                    },

                                                    x: {
                                                        title: {
                                                            display: true,
                                                            color: '#6C7782',
                                                            text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
                                                            padding: {
                                                                top: 15,
                                                            }
                                                        },
                                                        grid: {
                                                            display: false,
                                                        },
                                                    }
                                                },
                                                animation: {
                                                    duration: 0,
                                                    onComplete: function () {
                                                        var chart = this;
                                                        var ctx = chart.ctx;
                                                        ctx.textAlign = 'center';
                                                        ctx.textBaseline = 'bottom';
                                                        ctx.fillStyle = '#6C7782';
                                                        this.data.datasets.forEach(function (dataset, i) {
                                                            var meta = chart.getDatasetMeta(i);
                                                            meta.data.forEach(function (bar, index) {
                                                                var data = dataset.data[index];
                                                                data = isNaN(data) ? '' : data;
                                                                var yIndex = bar.y - 5;
                                                                if (data && data < 0) {
                                                                    yIndex = bar.y + 15;
                                                                }
                                                                ctx.fillText(data, bar.x, yIndex);
                                                            });
                                                        });
                                                    }
                                                }
                                            }
                                        };

                                        window.myLine = new Chart(ctx, options);
                                    })();

                                </script>
                                <#assign counter=counter+1>
                            </#list>

                        </div>
                        <#if counter==6 && model.milkSoldEvaluationTool.milkProcessorInformation?? &&
                            (model.milkSoldEvaluationTool.milkProcessorInformation?size <=5)>
                            <div class="break-page"></div>
                            <#elseif counter==0 && model.milkSoldEvaluationTool.milkProcessorInformation?? &&
                                (model.milkSoldEvaluationTool.milkProcessorInformation?size>= 25) >
                                <div class="break-page"></div>
                                <#elseif counter==2 && model.milkSoldEvaluationTool.milkProcessorInformation?? &&
                                    (model.milkSoldEvaluationTool.milkProcessorInformation?size>= 12) >
                                    <div class="break-page"></div>
                                    <#elseif counter gt 7 && counter%7==0>
                                        <div class="break-page"></div>
                        </#if>
                    </#list>
                </#if>
        </div>
    </div>
</#if>
<#if model.milkSoldEvaluationTool?? && model.milkSoldEvaluationTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.milkSoldEvaluationTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>