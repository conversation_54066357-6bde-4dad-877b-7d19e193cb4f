/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RoboticMilkEvaluationToolOutputToolItemDto implements Serializable {

  @Serial private static final long serialVersionUID = 1L;

  private Double cowsPerRobot;

  private Double milkingsPerRobot;

  private Double robotFreeTime;

  private Double milkPerRobot;

  private Double milkings;

  private Double milkingRefusals;

  private Double milkingFailures;

  private Double milkingSpeed;

  private Double averageBoxTime;

  private Double maximumConcentrate;

  private Double averageConcentrate;

  private Double minimumConcentrate;

  private Double concentratePer100KGMilk;

  private Double restFeed;
}
