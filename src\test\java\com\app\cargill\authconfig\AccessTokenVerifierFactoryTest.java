/* Cargill Inc.(C) 2022 */
package com.app.cargill.authconfig;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.confproperties.AzureADProperties;
import com.app.cargill.confproperties.OktaProperties;
import com.app.cargill.dto.DiscoveryKeyDto;
import java.net.MalformedURLException;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

// @TODO Fix tests to run in isolation or change them to integration tests
class AccessTokenVerifierFactoryTest {

  private AzureADProperties azureADProperties;
  private OktaProperties oktaProperties;
  private AccessTokenVerifierFactory factory;

  @BeforeEach
  void init() {
    azureADProperties = new AzureADProperties();
    azureADProperties.setIssuer(
        "https://login.microsoftonline.com/a348b8b8-a465-4163-859b-9644d697c2b6/");
    azureADProperties.setClientId("azure-client");
    oktaProperties = new OktaProperties();
    oktaProperties.setIssuer("https://c.com");
    oktaProperties.setClientId("okta-client");
  }

  @Test
  void getVerifier() {
    factory = new AccessTokenVerifierFactory(azureADProperties, oktaProperties);
    factory.getVerifier();
    assertNotNull(factory.getOktaAccessTokenVerifier());
  }

  @Test
  void getAzureDiscoveryKeys() throws MalformedURLException {

    factory = new AccessTokenVerifierFactory(azureADProperties, oktaProperties);
    factory.setAzureDiscoveredKeys(DiscoveryKeyDto.builder().keys(new ArrayList<>()).build());
    assertNotNull(factory.getAzureDiscoveredKeys());
  }
}
