/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.ResponseStatus;
import java.time.Instant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class ResponseEntityDto<T> {

  private ResponseStatus status;
  private String message;
  private T data;
  private Instant currentTimeStamp;

  @Builder
  public ResponseEntityDto(ResponseStatus status, String message, T data) {
    this.status = status;
    this.message = message;
    this.data = data;
    currentTimeStamp = Instant.now();
  }
}
