<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="038" author="Taha">
	 <sql splitStatements="false">
    
   DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ReturnOverFeed' 
      AND country_tool_document->>'CountryId' = 'Global'
  ) THEN 
    INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.023', false, newLocalId, '2023-01-15 11:39:46.023', 
jsonb_build_object(
    'id', newDocumentId, 
    'IsNew', false, 
    'ToolId', 'ReturnOverFeed', 
    'CountryId', 'Global', 
    'IsDeleted', false, 
    'CreateUser', null, 
    'ToolGroupId', 'Nutrition', 
    'CreateTimeUtc', '2021-11-11T03:30:29.906973500Z', 
    'LastModifyUser', null, 
    'LastSyncTimeUtc', null, 
    'LastModifiedTimeUtc', '2021-11-11T03:30:29Z')
);
  END IF; 
END $$;

</sql>

 <sql splitStatements="false">
    
   DO $$ 
DECLARE
  newLocalId uuid := gen_random_uuid();
  newDocumentId uuid := gen_random_uuid();
BEGIN 
  IF NOT EXISTS (
    SELECT 1 FROM country_tools
    WHERE country_tool_document->>'ToolId' = 'ReturnOverFeed' 
      AND country_tool_document->>'CountryId' = 'Canada'
  ) THEN 
    INSERT INTO public.country_tools
(created_date, deleted, local_id, updated_date, country_tool_document)
VALUES('2023-01-15 11:39:46.023', false, newLocalId, '2023-01-15 11:39:46.023', 
jsonb_build_object(
    'id', newDocumentId, 
    'IsNew', false, 
    'ToolId', 'ReturnOverFeed', 
    'CountryId', 'Canada', 
    'IsDeleted', false, 
    'CreateUser', null, 
    'ToolGroupId', 'Nutrition', 
    'CreateTimeUtc', '2021-11-11T03:30:29.906973500Z', 
    'LastModifyUser', null, 
    'LastSyncTimeUtc', null, 
    'LastModifiedTimeUtc', '2021-11-11T03:30:29Z')
);
  END IF; 
END $$;

</sql>


	</changeSet>

</databaseChangeLog>