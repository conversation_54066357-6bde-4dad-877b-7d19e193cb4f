/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.document.Contact;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.simple.MobileToLiftContactSalesforce;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.client.UnknownHttpStatusCodeException;

@ExtendWith(MockitoExtension.class)
class LiftContactServiceTest {

  @Mock private LiftApiService liftApi;
  @Mock ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private LiftContactService contactService;

  @Test
  void whenCreateSucceedsCorrectObjectIsReturned() throws Exception {
    Contact contactDocument = new Contact();
    contactDocument.setContactId(UUID.randomUUID());
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    String result = contactService.createContact(contactDocument, null, bundleMessageSource);
    assertEquals("new-id", result);
  }

  @Test
  void whenSavingToLiftCorrectResultIsReturned() throws Exception {
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("new-id");
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    List<Contact> sfContacts =
        contactService.saveToLift(
            List.of(
                Contact.builder()
                    .accountId(UUID.randomUUID())
                    .contactId(UUID.randomUUID())
                    .emailAddress("<EMAIL>")
                    .phoneNumber("**********")
                    .build()),
            null,
            bundleMessageSource);
    assertNotNull(sfContacts);
  }

  @Test
  void whenContactIdIsMissingCreateContactThrowsAnException() {
    Contact contact = new Contact();
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        IllegalArgumentException.class,
        () -> contactService.createContact(contact, null, bundleMessageSource));
  }

  @Test
  void whenValidateDocumentIsCalledCorrectResultIsReturned() {
    Contact contact =
        Contact.builder()
            .goldenRecordAcountId(UUID.randomUUID().toString())
            .contactId(UUID.randomUUID())
            .firstName("Tes")
            .lastName("Test")
            .phoneNumber("*********")
            .emailAddress("<EMAIL>")
            .build();

    MobileToLiftContactSalesforce contactValidation = contactService.validateDocument(contact);
    assertNotNull(contactValidation);
  }

  @Test
  void whenContactCreationFailTheExceptionIsThrown()
      throws JsonProcessingException, CustomDEExceptions {
    Contact contact = new Contact();
    contact.setContactId(UUID.randomUUID());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    when(liftApi.createRecord(any(), any(), any(), any()))
        .thenThrow(mock(UnknownHttpStatusCodeException.class));
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        CustomDEExceptions.class,
        () -> contactService.createContact(contact, null, bundleMessageSource));
  }

  @Test
  void whenUpdateSucceedsCorrectObjectIsReturned()
      throws CustomDEExceptions, JsonProcessingException {
    Contact contactDocument = new Contact();
    contactDocument.setSFDCContactId("id");
    contactDocument.setContactId(UUID.randomUUID());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    boolean result = contactService.updateContact(contactDocument, bundleMessageSource, null);
    assertTrue(result);
  }

  @Test
  void whenSfIdIsMissingUpdateContactThrowsException() throws CustomDEExceptions {
    Contact contactDocument = new Contact();
    contactDocument.setContactId(UUID.randomUUID());
    when(liftApi.getTokenAndApiPath()).thenReturn(AccessTokenAndApiPathDto.builder().build());
    assertThrows(
        CustomDEExceptions.class,
        () -> contactService.updateContact(contactDocument, bundleMessageSource, null));
  }

  @Test
  void whenContactIsNullValidateReturnsNull() {
    MobileToLiftContactSalesforce result = contactService.validateDocument(null);
    assertNull(result);
  }
}
