/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.simple.ExternalDataSourceUpdateModel;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class LiftSiteMappingsService {

  private final LiftApiService liftApi;

  public String createSiteMapping(
      SiteDocument siteDocument, Locale locale, ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    return createSiteMapping(
        tokenAndApiPath.getAuthToken(), tokenAndApiPath.getApiPath(), siteDocument, locale, source);
  }

  /**
   * Create a Site Mapping on lift
   *
   * <pre>
   * {
   *     "System__c": "LM_SITE",
   *     "Species__c": "a0X7e000007YFtSEAW",
   *     "Unique_External_Key__c": "26c2efd7-0d50-48ea-b10a-856ef85d664d",
   *     "Account__c": "0017e00001hJcZNAA0",
   *     "ID__c": "26c2efd7-0d50-48ea-b10a-856ef85d664d"
   *
   * }
   * </pre>
   *
   * @param authToken the token object
   * @param apiPath url to salesforce instance
   * @param siteDocument the document model
   * @param source
   * @param locale
   * @return Created contact ID
   * @throws Exception
   */
  public String createSiteMapping(
      AuthToken authToken,
      String apiPath,
      SiteDocument siteDocument,
      Locale locale,
      ResourceBundleMessageSource source)
      throws JsonProcessingException, CustomDEExceptions {

    if (siteDocument.getExternalId() == null) {
      throw new IllegalArgumentException(
          "External Site ID is a required field for site id : " + siteDocument.getId());
    }

    if (siteDocument.getExternalAccountId() == null) {
      throw new IllegalArgumentException(
          "External Account ID is a required field for site id: " + siteDocument.getId());
    }

    if (siteDocument.getId() == null) {
      throw new IllegalArgumentException("Site ID is a required field");
    }
    try {
      String siteMappingsUrl = String.format("%s/sobjects/External_Data_Source__c", apiPath);
      ExternalDataSourceUpdateModel siteMappingsModel = documentToModel(siteDocument);
      CreateRecordResponse recordResponse =
          liftApi.createRecord(
              authToken, siteMappingsModel, new ParameterizedTypeReference<>() {}, siteMappingsUrl);
      log.debug("ExternalDataSource created: {}", recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception e) {
      log.error(String.format("Error on ExternalDataSource creation: %s", siteDocument.getId()), e);
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  source.getMessage(
                      LangKeys.LIFT_SYNC_FAILED,
                      new Object[] {},
                      LiftErrorResponseConstants.LIFT_ERROR_MESSAGE,
                      locale))
              .entity("SiteMapping")
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()));
    }
  }

  public void updateMapping(String recordId, ExternalDataSourceUpdateModel dataSourceUpdateModel) {
    log.debug("UPDATE_LIFT_MAPPING {}", dataSourceUpdateModel);
    AccessTokenAndApiPathDto tokenAndApiPath = liftApi.getTokenAndApiPath();
    String siteMappingsUrl =
        String.format(
            "%s/sobjects/External_Data_Source__c/%s", tokenAndApiPath.getApiPath(), recordId);
    liftApi.updateRecord(
        tokenAndApiPath.getAuthToken(),
        dataSourceUpdateModel,
        new ParameterizedTypeReference<>() {},
        siteMappingsUrl);
  }

  public ExternalDataSourceUpdateModel documentToModel(SiteDocument siteDocument) {
    ExternalDataSourceUpdateModel updateModel = new ExternalDataSourceUpdateModel();
    updateModel.setUniqueExternalKey(siteDocument.getId().toString());
    updateModel.setSalesforceSiteId(siteDocument.getExternalId());
    updateModel.setIdCustom(siteDocument.getId().toString());
    // This can be passed from outside if we want to create other mappings
    updateModel.setSystemName("LM_SITE");
    updateModel.setSalesforceAccountId(siteDocument.getExternalAccountId());
    return updateModel;
  }
}
