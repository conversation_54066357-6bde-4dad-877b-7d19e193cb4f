<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="017" author="Taha">
    <sql>
        CREATE TABLE IF NOT EXISTS configurations (id  bigserial not null,
			created_date timestamp,
			deleted BOOLEAN default false,
			local_id varchar(255),
			updated_date timestamp,
			configuration_document jsonb,
			primary key (id));
			
      insert into configurations (configuration_document) values ('{
                                                                "minPageSize": 100,
                                                                "maxPageSize": 500,
                                                                "percentage": 10
                                                                }');
    </sql>
  </changeSet>
</databaseChangeLog>