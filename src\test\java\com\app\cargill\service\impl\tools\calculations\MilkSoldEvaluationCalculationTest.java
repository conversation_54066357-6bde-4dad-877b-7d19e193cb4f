/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.tools.calculations;

import static org.junit.jupiter.api.Assertions.*;

import com.app.cargill.constants.MilkPickup;
import com.app.cargill.constants.MilkUreaMeasure;
import com.app.cargill.constants.ToolStatuses;
import com.app.cargill.document.MilkSoldEvaluationTool;
import com.app.cargill.document.MilkSoldEvaluationToolItem;
import com.app.cargill.document.MilkSoldEvaluationToolOutputToolItem;
import com.app.cargill.document.MilkSoldMilkProcessorToolItem;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MilkSoldEvaluationCalculationTest {

  @InjectMocks private MilkSoldEvaluationCalculation milkSoldEvaluationCalculation;

  @Test
  void calculateFields() {
    MilkSoldEvaluationTool milkSoldEvaluationTool =
        MilkSoldEvaluationTool.builder()
            .visitMilkEvaluationData(
                MilkSoldEvaluationToolItem.builder()
                    .pickups(
                        List.of(
                            MilkSoldMilkProcessorToolItem.builder()
                                .animalsInTank(21)
                                .daysInTank(12)
                                .milkSold(222.1)
                                .build(),
                            MilkSoldMilkProcessorToolItem.builder()
                                .animalsInTank(25)
                                .daysInTank(2)
                                .milkSold(212.1)
                                .build(),
                            MilkSoldMilkProcessorToolItem.builder()
                                .animalsInTank(21)
                                .daysInTank(12)
                                .milkSold(222.1)
                                .build()))
                    .outputs(
                        MilkSoldEvaluationToolOutputToolItem.builder()
                            .evaluationDays(5.1)
                            .averageMilkProductionAnimalsTank(2.1)
                            .averageMilkFatPer(21.1)
                            .milkFatYield(22.1)
                            .milkProteinYield(12.1)
                            .averageMilkSolidNonFat(12.1)
                            .averageMilkProteinPer(21.1)
                            .build())
                    .animalsinTank(22)
                    .daysInMilk(12)
                    .dryMatterIntake(222.1)
                    .lactatingAnimals(2)
                    .build())
            .build();

    milkSoldEvaluationTool =
        milkSoldEvaluationCalculation.calculateFields(milkSoldEvaluationTool, 30.0);
    assertNotNull(milkSoldEvaluationTool);
    assertNotNull(milkSoldEvaluationTool.getVisitMilkEvaluationData());
    MilkSoldEvaluationToolOutputToolItem milkSoldEvaluationToolOutputToolItem =
        milkSoldEvaluationTool.getVisitMilkEvaluationData().getOutputs();
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getAverageMilkProduction());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getAverageMilkProductionAnimalsTank());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getMilkFatYield());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getMilkProteinYield());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getMilkFatProteinYield());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getComponentEfficiency());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getFeedEfficiency());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getMilkSolidNonFat());
    assertNotNull(milkSoldEvaluationToolOutputToolItem.getAverageMilkSolidNonFat());
  }

  @Test
  void calculateFieldsWithNullValues() {
    MilkSoldEvaluationTool milkSoldEvaluationTool =
        MilkSoldEvaluationTool.builder()
            .visitMilkEvaluationData(
                MilkSoldEvaluationToolItem.builder()
                    .pickups(List.of(MilkSoldMilkProcessorToolItem.builder().build()))
                    .outputs(MilkSoldEvaluationToolOutputToolItem.builder().build())
                    .build())
            .build();

    milkSoldEvaluationTool =
        milkSoldEvaluationCalculation.calculateFields(milkSoldEvaluationTool, null);

    MilkSoldEvaluationToolOutputToolItem milkSoldEvaluationToolOutputToolItem =
        milkSoldEvaluationTool.getVisitMilkEvaluationData().getOutputs();
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getAverageMilkProduction());
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getAverageMilkProductionAnimalsTank());
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getMilkFatYield());
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getMilkProteinYield());
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getMilkFatProteinYield());
    assertNull(milkSoldEvaluationToolOutputToolItem.getComponentEfficiency());
    assertNull(milkSoldEvaluationToolOutputToolItem.getFeedEfficiency());
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getMilkSolidNonFat());
    assertEquals(0.0, milkSoldEvaluationToolOutputToolItem.getAverageMilkSolidNonFat());
  }

  @Test
  void verifyCalculations() {
    MilkSoldEvaluationTool milkSoldEvaluationTool =
        MilkSoldEvaluationTool.builder()
            .visitMilkEvaluationData(
                MilkSoldEvaluationToolItem.builder()
                    .pickups(
                        List.of(
                            MilkSoldMilkProcessorToolItem.builder()
                                .pickUpIndex(1)
                                .milkSold(420.0)
                                .animalsInTank(21)
                                .daysInTank(1)
                                .milkFatPer(3.7)
                                .milkProteinPer(3.4)
                                .mun(0.0)
                                .somaticCellCount(0)
                                .bacteriaCellCount(0.0)
                                .nonFatSolid(8.4)
                                .mastitis(4.0)
                                .build()))
                    .outputs(
                        MilkSoldEvaluationToolOutputToolItem.builder()
                            .evaluationDays(1.0)
                            .averageMilkFatPer(3.7)
                            .milkFatYield(0.0)
                            .averageMilkProteinPer(3.4)
                            .milkProteinYield(0.0)
                            .mun(0.0)
                            .averageSCC(0.0)
                            .averageBacteriaCount(0.0)
                            .isOutputUpdated(true)
                            .milkSolidNonFat(0.0)
                            .averageMilkSolidNonFat(8.4)
                            .build())
                    .animalsinTank(21)
                    .daysInMilk(260)
                    .dryMatterIntake(16.5)
                    .milkPickup(MilkPickup.Daily)
                    .milkUreaMeasure(MilkUreaMeasure.MUN)
                    .lactatingAnimals(21)
                    .build())
            .build();

    milkSoldEvaluationTool =
        milkSoldEvaluationCalculation.calculateFields(milkSoldEvaluationTool, 20.0);

    assertNotNull(milkSoldEvaluationTool);
    assertNotNull(milkSoldEvaluationTool.getVisitMilkEvaluationData());
    MilkSoldEvaluationToolOutputToolItem milkSoldEvaluationToolOutputToolItem =
        milkSoldEvaluationTool.getVisitMilkEvaluationData().getOutputs();

    assertEquals(ToolStatuses.Completed, milkSoldEvaluationToolOutputToolItem.getToolStatus());

    assertEquals(20.0, milkSoldEvaluationToolOutputToolItem.getAverageMilkProduction());

    assertEquals(20.0, milkSoldEvaluationToolOutputToolItem.getAverageMilkProductionAnimalsTank());

    assertEquals(0.74, milkSoldEvaluationToolOutputToolItem.getMilkFatYield());

    assertEquals(0.68, milkSoldEvaluationToolOutputToolItem.getMilkProteinYield());

    assertEquals(1.42, milkSoldEvaluationToolOutputToolItem.getMilkFatProteinYield());

    assertEquals(8.61, milkSoldEvaluationToolOutputToolItem.getComponentEfficiency());

    assertEquals(1.21, milkSoldEvaluationToolOutputToolItem.getFeedEfficiency());

    assertEquals(8.4, milkSoldEvaluationToolOutputToolItem.getAverageMilkSolidNonFat());

    assertEquals(1.68, milkSoldEvaluationToolOutputToolItem.getMilkSolidNonFat());
  }
}
