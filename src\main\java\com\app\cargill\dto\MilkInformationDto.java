/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkInformationDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private String numberOfMilkings;
  private Double totalProductionHerd;
  private Double totalProduction;
  private Double milkPrice;
  private Double DIM;
  private Double productionIn150DIM;
  private Double milkFatPercentage;
  private Double milkProteinPercentage;
  private Double somanticCellCount;
  private Double bacteriaCellCount;
  private Double MUN;
}
