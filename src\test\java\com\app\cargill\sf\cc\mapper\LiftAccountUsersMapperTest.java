/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.UserAccessContainer;
import com.app.cargill.sf.cc.model.UserRecord;
import com.app.cargill.sf.cc.model.UserReferenceRecord;
import com.app.cargill.sf.cc.model.simple.Account;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;

class LiftAccountUsersMapperTest {

  @Test
  void whenUsersAreOnlyTechSpecialistsNoUsersAreReturned() {

    UserAccessContainer userAccessContainer = new UserAccessContainer();
    userAccessContainer.setRoleName("Technical Specialist");
    UserRecord userRecord = new UserRecord();
    userRecord.setEmail("<EMAIL>");
    userAccessContainer.setUserRecord(userRecord);

    SalesforceRecordsResponse<UserAccessContainer> recordsResponse =
        new SalesforceRecordsResponse<>();
    recordsResponse.setRecords(List.of(userAccessContainer));
    Account account = new Account();
    UserReferenceRecord ownerRecord = new UserReferenceRecord();
    ownerRecord.setEmail("<EMAIL>");
    account.setOwner(ownerRecord);
    account.setUserAccessContainer(recordsResponse);

    Set<String> users = LiftAccountUsersMapper.transformToUsers(account);
    assertEquals(2, users.size());
    assertTrue(users.contains("<EMAIL>"));
  }

  @Test
  void whenUsersContainTechSpecialistsOnlyOthersAreReturned() {
    UserRecord userRecord1 = new UserRecord();
    userRecord1.setEmail("<EMAIL>");
    UserAccessContainer userAccessContainer1 = new UserAccessContainer();
    userAccessContainer1.setRoleName("Technical Specialist");
    userAccessContainer1.setUserRecord(userRecord1);
    UserRecord userRecord2 = new UserRecord();
    userRecord2.setEmail("<EMAIL>");
    UserAccessContainer userAccessContainer2 = new UserAccessContainer();
    userAccessContainer2.setRoleName("Some guy");
    userAccessContainer2.setUserRecord(userRecord1);
    SalesforceRecordsResponse<UserAccessContainer> recordsResponse =
        new SalesforceRecordsResponse<>();
    recordsResponse.setRecords(List.of(userAccessContainer1, userAccessContainer2));

    Account account = new Account();
    account.setUserAccessContainer(recordsResponse);

    UserReferenceRecord ownerRecord = new UserReferenceRecord();
    ownerRecord.setEmail("<EMAIL>");
    account.setOwner(ownerRecord);

    Set<String> users = LiftAccountUsersMapper.transformToUsers(account);
    assertEquals(2, users.size());
    assertTrue(users.contains("<EMAIL>"));
  }
}
