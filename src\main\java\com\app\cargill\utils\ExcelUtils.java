/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.constants.LangKeys;
import com.app.cargill.dto.Pair;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellAddress;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.apache.poi.xddf.usermodel.PresetLineDash;
import org.apache.poi.xddf.usermodel.XDDFColor;
import org.apache.poi.xddf.usermodel.XDDFLineProperties;
import org.apache.poi.xddf.usermodel.XDDFNoFillProperties;
import org.apache.poi.xddf.usermodel.XDDFPresetLineDash;
import org.apache.poi.xddf.usermodel.XDDFShapeProperties;
import org.apache.poi.xddf.usermodel.XDDFSolidFillProperties;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xddf.usermodel.chart.XDDFLineChartData.Series;
import org.apache.poi.xssf.usermodel.*;
import org.bouncycastle.util.Strings;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTDispBlanksAs;
import org.openxmlformats.schemas.drawingml.x2006.chart.CTPlotArea;
import org.openxmlformats.schemas.drawingml.x2006.chart.STDispBlanksAs;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;

@Slf4j
@SuppressWarnings({"java:S125", "java:S3776", "java:S1172"})
public class ExcelUtils {
  private ExcelUtils() {}

  public static XSSFCell createAndSetCellValue(
      XSSFRow row, AtomicInteger cellNumber, XSSFCellStyle cellStyle, Object value) {
    XSSFCell cell = row.createCell(cellNumber.getAndIncrement());
    if (!Objects.isNull(cellStyle)) {
      cell.setCellStyle(cellStyle);
    }

    if (!Objects.isNull(value)) {
      if (value instanceof String castedValue) {
        cell.setCellValue(castedValue);
      } else if (value instanceof Double castedValue) {
        cell.setCellValue(castedValue.doubleValue());
      } else if (value instanceof Integer castedValue) {
        cell.setCellValue(castedValue.intValue());
      }
    }
    return cell;
  }

  public static void highlightEmptyCell(
      XSSFRow row,
      Object value,
      AtomicInteger cellNumber,
      XSSFCellStyle decimalStyle,
      XSSFCellStyle greyCellStyle) {
    if (!Objects.isNull(value)) {
      ExcelUtils.createAndSetCellValue(row, cellNumber, decimalStyle, value);
    } else {
      ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, null);
    }
  }

  public static XSSFCellStyle applyCellStyle(
      XSSFWorkbook wb,
      IndexedColors bgColor,
      FillPatternType fillPattern,
      HorizontalAlignment horizontalAlignment,
      Font font) {
    XSSFCellStyle style = wb.createCellStyle();

    if (bgColor != null) {
      style.setFillBackgroundColor(bgColor.getIndex());
      style.setFillForegroundColor(bgColor.getIndex());
    }
    style.setFillPattern(fillPattern == null ? FillPatternType.NO_FILL : fillPattern);
    style.setAlignment(horizontalAlignment);

    style.setFont(font);
    return style;
  }

  public static XSSFFont getFont(
      XSSFWorkbook wb, boolean isItalic, boolean isBold, IndexedColors color) {
    XSSFFont font = wb.createFont();
    font.setFontHeightInPoints((short) 10);
    font.setFontName("Arial");
    font.setColor(color == null ? IndexedColors.WHITE.getIndex() : color.getIndex());
    font.setBold(isBold);
    font.setItalic(isItalic);
    return font;
  }

  public static XSSFCellStyle decimalCellStyle(
      XSSFWorkbook workbook,
      boolean isBold,
      IndexedColors fontColor,
      HorizontalAlignment horizontalAlignment) {
    DataFormat format = workbook.createDataFormat();
    XSSFCellStyle decimalCellStyle = workbook.createCellStyle();
    decimalCellStyle.setDataFormat(format.getFormat("0.00"));
    decimalCellStyle.setAlignment(horizontalAlignment);
    if (isBold) {
      decimalCellStyle.setFont(getFont(workbook, false, isBold, fontColor));
    }
    return decimalCellStyle;
  }

  public static void addCargillLogo(
      Class<?> clazz, XSSFWorkbook wb, XSSFSheet sheet, int col, int row) {
    int pictureIndex = 0;
    sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));
    try (InputStream inputStream = clazz.getClassLoader().getResourceAsStream("cargill.png")) {
      assert inputStream != null;
      byte[] imageBytes = IOUtils.toByteArray(inputStream);

      pictureIndex = wb.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
    } catch (Exception e) {
      log.error("Unable to find Cargill logo", e);
      log.error(e.getLocalizedMessage());
    }

    XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 250, col, row, col + 1, row + 2);

    XSSFDrawing drawingPatriarch = sheet.createDrawingPatriarch();
    drawingPatriarch.createPicture(anchor, pictureIndex).resize(1, 0.95);
  }

  public static void drawLineSeries(
      XDDFChartData data, int index, PresetColor color, boolean isDotted) {
    XDDFSolidFillProperties fill = new XDDFSolidFillProperties(XDDFColor.from(color));
    XDDFLineProperties line = new XDDFLineProperties();
    line.setFillProperties(fill);

    // set doted line
    if (isDotted) {
      XDDFPresetLineDash xddfPresetLineDash = new XDDFPresetLineDash(PresetLineDash.DASH);
      line.setPresetDash(xddfPresetLineDash);
    }
    XDDFChartData.Series series = data.getSeries(index);
    XDDFShapeProperties properties = series.getShapeProperties();
    if (properties == null) {
      properties = new XDDFShapeProperties();
    }
    properties.setLineProperties(line);
    series.setShapeProperties(properties);
  }

  public static ByteArrayResource finalizeWorkbook(XSSFWorkbook wb, int totalSheetColumns) {
    for (int sheetIndex = 0; sheetIndex < wb.getNumberOfSheets(); sheetIndex++) {
      // auto size columns of sheet
      for (int i = 0; i <= totalSheetColumns + 1; i++) {
        wb.getSheetAt(sheetIndex).autoSizeColumn(i);
      }
      wb.getSheetAt(sheetIndex).setActiveCell(new CellAddress(0, 0));
    }
    // evaluate all formulas in the workbook
    XSSFFormulaEvaluator evaluator = new XSSFFormulaEvaluator(wb);
    evaluator.evaluateAll();
    try (ByteArrayOutputStream stream = new ByteArrayOutputStream()) {
      wb.write(stream);
      wb.close();
      return new ByteArrayResource(stream.toByteArray());
    } catch (IOException e) {
      log.error("Unable to write Workbook", e);
      log.error(e.getLocalizedMessage());
    }
    return new ByteArrayResource(Strings.toByteArray(""));
  }

  // chart related  methods
  public static XDDFValueAxis createRightAxis(
      XSSFChart chart, String title, XDDFCategoryAxis bottomAxis, Double minimumValue) {
    XDDFValueAxis rightAxis;
    rightAxis = chart.createValueAxis(AxisPosition.RIGHT);
    rightAxis.setTitle(title);
    rightAxis.setCrosses(AxisCrosses.MAX);
    rightAxis.setTickLabelPosition(AxisTickLabelPosition.HIGH);
    rightAxis.setMajorTickMark(AxisTickMark.CROSS);
    rightAxis.setMinorTickMark(AxisTickMark.NONE);
    // set correct cross axis
    bottomAxis.crossAxis(rightAxis);
    rightAxis.crossAxis(bottomAxis);
    // set minimum value to overcome overlapping
    rightAxis.setMinimum(minimumValue);
    return rightAxis;
  }

  public static XSSFChart plotYRightAxis(
      XSSFChart chart, XDDFLineChartData dataRight, Series series, boolean isSmoothLine) {
    chart.plot(dataRight);
    series.setSmooth(isSmoothLine);
    // correct the id and order, must not be 0 again because there is one line series already
    int serArrSize = chart.getCTChart().getPlotArea().getLineChartArray(0).sizeOfSerArray() + 1;
    chart
        .getCTChart()
        .getPlotArea()
        .getLineChartArray(1)
        .getSerArray(0)
        .getIdx()
        .setVal(serArrSize);
    chart
        .getCTChart()
        .getPlotArea()
        .getLineChartArray(1)
        .getSerArray(0)
        .getOrder()
        .setVal(serArrSize);
    return chart;
  }

  public static XDDFValueAxis createLeftAxis(XSSFChart chart, String title) {

    XDDFValueAxis leftAxis = chart.createValueAxis(AxisPosition.LEFT);
    leftAxis.setTitle(title);
    leftAxis.setTickLabelPosition(AxisTickLabelPosition.LOW);
    leftAxis.setMajorTickMark(AxisTickMark.CROSS);
    leftAxis.setMinorTickMark(AxisTickMark.NONE);
    leftAxis.setCrosses(AxisCrosses.MIN);
    // leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);

    return leftAxis;
  }

  public static XDDFCategoryAxis createBottomAxis(XSSFChart chart, String title) {

    // create the axes
    XDDFCategoryAxis bottomAxis = chart.createCategoryAxis(AxisPosition.BOTTOM);
    bottomAxis.setTitle(title);
    bottomAxis.setTickLabelPosition(AxisTickLabelPosition.LOW);
    bottomAxis.setMajorTickMark(AxisTickMark.CROSS);
    bottomAxis.setMinorTickMark(AxisTickMark.NONE);
    bottomAxis.setCrosses(AxisCrosses.MIN);

    return bottomAxis;
  }

  public static XDDFValueAxis createValueBottomAxis(XSSFChart chart, String title) {

    // create the axes
    XDDFValueAxis bottomAxis = chart.createValueAxis(AxisPosition.BOTTOM);
    bottomAxis.setTitle(title);
    bottomAxis.setTickLabelPosition(AxisTickLabelPosition.LOW);
    bottomAxis.setMajorTickMark(AxisTickMark.CROSS);
    bottomAxis.setMinorTickMark(AxisTickMark.NONE);
    bottomAxis.setCrosses(AxisCrosses.MIN);
    return bottomAxis;
  }

  public static void initLegends(XSSFChart chart) {
    XDDFChartLegend legend = chart.getOrAddLegend();
    legend.setPosition(LegendPosition.TOP);
  }

  static void setFontInChart(XSSFChart chart, Double fontSize) {
    chart.getFormattedTitle().getParagraph(0).addDefaultRunProperties().setFontSize(fontSize);
  }

  public static XSSFChart initChart(
      XSSFSheet sheet, String chartTitle, int col1, int row1, int col2, int row2) {
    // create sheets drawing
    XSSFDrawing drawing = sheet.createDrawingPatriarch();
    // create anchor
    XSSFClientAnchor anchor =
        drawing.createAnchor(0, 0, 0, 0, col1, row1, col2, row2); // anchor col8,row0 to col14,row12
    // create chart
    XSSFChart chart = drawing.createChart(anchor);
    // chart.setTitleText(chartTitle);
    // chart.setTitleOverlay(false);
    // setFontInChart(chart, 17d);

    // this will set blank values as gaps in the chart, so you
    // can accurately plot data series of different lengths
    CTDispBlanksAs disp = CTDispBlanksAs.Factory.newInstance();
    disp.setVal(STDispBlanksAs.GAP);
    chart.getCTChart().setDispBlanksAs(disp);
    return chart;
  }

  public static void drawGridLinesInChart(XSSFChart chart, boolean isMajorGridLines) {
    CTPlotArea plotArea = chart.getCTChart().getPlotArea();
    if (isMajorGridLines) {
      plotArea.getCatAxArray()[0].addNewMajorGridlines();
      plotArea.getValAxArray()[0].addNewMajorGridlines();
    } else {
      plotArea.getCatAxArray()[0].addNewMinorGridlines();
      plotArea.getValAxArray()[0].addNewMinorGridlines();
    }
  }

  public static String getLangValue(
      String langKey, Object[] args, ResourceBundleMessageSource source, Locale locale) {
    if (args == null) {
      args = new Object[] {};
    }
    return source.getMessage(langKey, args, "n/a", locale);
  }

  public static String getLangValue(
      String langKey,
      String defaultValue,
      Object[] args,
      ResourceBundleMessageSource source,
      Locale locale) {
    if (args == null) {
      args = new Object[] {};
    }
    return source.getMessage(langKey, args, defaultValue, locale);
  }

  public static void setLineMarker(Series series, MarkerStyle style) {
    // to smooth lines rather then edges
    series.setSmooth(true);

    // set marker style
    series.setMarkerStyle(style);
    // set marker size
    series.setMarkerSize((short) 5);
  }

  public static int fixColumnEndIndex(int columnStart, int columnEnd) {
    if (columnEnd < columnStart) {
      columnEnd = columnStart;
    }
    return columnEnd;
  }

  public static void setLineNoFill(XDDFScatterChartData.Series series) {
    XDDFNoFillProperties noFillProperties = new XDDFNoFillProperties();
    XDDFLineProperties lineProperties = new XDDFLineProperties();
    lineProperties.setFillProperties(noFillProperties);
    XDDFShapeProperties shapeProperties = series.getShapeProperties();
    if (shapeProperties == null) shapeProperties = new XDDFShapeProperties();
    shapeProperties.setLineProperties(lineProperties);
    series.setShapeProperties(shapeProperties);
  }

  public static void setLineMarker(XDDFScatterChartData.Series series, MarkerStyle circle) {
    // to smooth lines rather then edges
    series.setSmooth(true);

    // set marker style
    series.setMarkerStyle(circle);
    // set marker size
    series.setMarkerSize((short) 5);
  }

  public static void addAvgAndStdDeviation(
      XSSFSheet sheet,
      AtomicInteger rowNumber,
      XSSFCellStyle centerBlack,
      XSSFCellStyle decimalStyle,
      ResourceBundleMessageSource source,
      Locale locale,
      Pair<Double, Double> values) {
    // setting Avg and Standard deviation
    XSSFRow row = sheet.createRow(rowNumber.getAndIncrement());
    AtomicInteger cellNumber = new AtomicInteger(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_AVERAGE, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, decimalStyle, values.left());
    row = sheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        centerBlack,
        ExcelUtils.getLangValue(LangKeys.REPORT_STANDARD_DEVIATION, null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, decimalStyle, values.right());
  }

  public static void setBarColorInBarChart(XSSFChart chart, List<byte[]> colors, int size) {

    for (int index = 0; index < size; index++) {
      chart
          .getCTChart()
          .getPlotArea()
          .getBarChartArray(0)
          .getSerArray(index)
          .addNewSpPr()
          .addNewSolidFill()
          .addNewSrgbClr()
          .setVal(colors.get(index));
    }
  }

  /**
   * Creates a standardized Return Over Feed bar chart with multiple metrics.
   * This method eliminates code duplication between different Return Over Feed report implementations.
   *
   * @param sheet The Excel sheet to add the chart to
   * @param chartTitle The title for the chart
   * @param dataPointsSize The number of data points (periods) in the chart
   * @param xAxisLabel The label for the X-axis
   * @param yAxisLabel The label for the Y-axis
   * @param periodHeaderRowNumber The row number containing period headers
   * @param dataStartRowNumber The row number where metric data starts
   * @param metricCount The number of metrics to plot
   * @return The created XSSFChart object
   */
  public static XSSFChart createReturnOverFeedBarChart(
      XSSFSheet sheet,
      String chartTitle,
      int dataPointsSize,
      String xAxisLabel,
      String yAxisLabel,
      int periodHeaderRowNumber,
      int dataStartRowNumber,
      int metricCount) {

    // Calculate chart positioning
    int columnStart = 1;
    int columnEnd = columnStart + dataPointsSize - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    int chartCol0 = columnEnd + 1;

    // Create chart
    XSSFChart chart =
        ExcelUtils.initChart(
            sheet,
            chartTitle,
            chartCol0,
            3,
            chartCol0 + (dataPointsSize > 10 ? dataPointsSize : 10),
            23);

    ExcelUtils.initLegends(chart);

    // Create axes
    XDDFCategoryAxis bottomAxis = ExcelUtils.createBottomAxis(chart, xAxisLabel != null ? xAxisLabel : "");
    XDDFValueAxis leftAxis = ExcelUtils.createLeftAxis(chart, yAxisLabel != null ? yAxisLabel : "");
    leftAxis.setCrosses(AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(AxisCrossBetween.BETWEEN);

    // Create chart data
    XDDFBarChartData dataLeft = (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);

    // Create data source for categories (periods)
    XDDFDataSource<String> periodDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            sheet, new CellRangeAddress(periodHeaderRowNumber, periodHeaderRowNumber, columnStart, columnEnd));

    // Create series for each metric
    for (int metricIndex = 0; metricIndex < metricCount; metricIndex++) {
      XDDFNumericalDataSource<Double> metricDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              sheet,
              new CellRangeAddress(
                  dataStartRowNumber + metricIndex,
                  dataStartRowNumber + metricIndex,
                  columnStart,
                  columnEnd));

      XDDFBarChartData.Series series = (XDDFBarChartData.Series) dataLeft.addSeries(periodDataSource, metricDataSource);

      series.setTitle(
          sheet.getRow(dataStartRowNumber + metricIndex).getCell(0).getStringCellValue(),
          new org.apache.poi.ss.util.CellReference(
              sheet.getSheetName(), dataStartRowNumber + metricIndex, 0, true, true));
    }

    chart.plot(dataLeft);

    // Set standard colors for the bars
    List<byte[]> colors =
        java.util.Arrays.asList(
            new byte[] {(byte) 54, (byte) 162, (byte) 235}, // Blue
            new byte[] {(byte) 255, (byte) 99, (byte) 132}, // Red
            new byte[] {(byte) 75, (byte) 192, (byte) 192}, // Green
            new byte[] {(byte) 153, (byte) 102, (byte) 255} // Purple
        );
    ExcelUtils.setBarColorInBarChart(chart, colors, metricCount);

    return chart;
  }
}
