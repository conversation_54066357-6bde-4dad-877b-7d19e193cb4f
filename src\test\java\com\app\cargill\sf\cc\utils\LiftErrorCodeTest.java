/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

class LiftErrorCodeTest {

  @Test
  void whenDeletedMessageCorrectValueIsReturned() {
    LiftErrorCode liftErrorCode = LiftErrorCode.fromString("ENTITY_IS_DELETED");
    assertEquals(LiftErrorCode.ENTITY_IS_DELETED, liftErrorCode);
  }

  @Test
  void whenMessageIsUnknownCorrectValueIsReturned() {
    LiftErrorCode liftErrorCode = LiftErrorCode.fromString("SOME_RANDOM_MESSAGE");
    assertEquals(LiftErrorCode.UNKNOWN, liftErrorCode);
  }
}
