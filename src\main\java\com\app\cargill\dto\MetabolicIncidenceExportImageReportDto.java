/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MetabolicIncidenceExportImageReportDto extends BaseDto {

  @Builder.Default private List<Double> visitDate1 = new ArrayList<>();
  @Builder.Default private List<Double> visitDate2 = new ArrayList<>();
  @Builder.Default private List<Double> visitDate3 = new ArrayList<>();
  @Builder.Default private List<Double> visitDate4 = new ArrayList<>();
  @Builder.Default private List<Double> visitDate5 = new ArrayList<>();
  @Builder.Default private List<Double> visitDate6 = new ArrayList<>();
  @Builder.Default private List<Double> visitDate7 = new ArrayList<>();
  @Builder.Default private List<String> formattedDates = new ArrayList<>();
}
