<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitViewModel.HealthItem", [],
                "Health",locale)}</span>
            <span class="lg-head">${localization.getMessage("HealthToolsViewModel.RumenHealthManureScreening", [],
                "Rumen health manure screening",locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000101010MS</div>
    </div>
</div>
<!-- Pen Analysis -->
<#if model.rumenHealthManureScreeningTool.penAnalysis?? &&
    ((model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails?? &&
    model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails[0]??) ||
    (model.rumenHealthManureScreeningTool.penAnalysis.graph?? &&
    model.rumenHealthManureScreeningTool.penAnalysis.graph[0]??))>
    <div class="container">
        <div class="row mx-neg-4">
            <div class="col-12 px-4">
                <h3 class="title-secondary mb-1">
                    <span>${localization.getMessage("WalkthroughReportLandingViewModel.PenAnalysis", [], "Pen Analysis",
                        locale)}</span>
                </h3>

            </div>
        </div>
    </div>
    <#assign counter=0>
        <#assign penRows=0>
		<#assign isFirstIteration=true>
            <#if model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails?? &&
                model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails[0]??>

                <div class="container mb-1">


                    <div class="row mx-neg-4">

                        <div class="col-12 px-4 table-secondary">
                            <table>
                                <thead>
                                    <tr>
                                        <#list
                                            model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails[0]?keys
                                            as prop>
                                            <#list
                                                model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails[0][prop]
                                                as key>
                                                <th>${key.column!}</th>
                                            </#list>
                                        </#list>
                                    </tr>
                                </thead>
                                <tbody>
                                    <#list model.rumenHealthManureScreeningTool.penAnalysis.penAnalysisDetails as item>
                                        <#list item?keys as prop>
                                            <#assign penRows=penRows+1>
                                                <tr>
                                                    <td colspan="10">
                                                        <h3 class="title mb-0">${prop!}</h3>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <#list item[prop] as key>
                                                        <td>${key.value!}</td>
                                                    </#list>
                                                </tr>
                                        </#list>
                                    </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <#if penRows gte 16>
                    <div class="break-page"></div>
                </#if>
            </#if>
            <#if model.rumenHealthManureScreeningTool.penAnalysis.graph?? &&
                model.rumenHealthManureScreeningTool.penAnalysis.graph[0]??>

                <!-- TMR Particle Score 1 & 2 -->
                <div class="container">
                    <#list model.rumenHealthManureScreeningTool.penAnalysis.graph?chunk(2) as row>
                        <div class="row mx-neg-4">
                            <#list row as column>
                                <div class="col-6 px-4">
                                    <h5 class="title-sub">${column.penName!}</h5>
                                    <div class="card mb-1">
                                        <div class="card-body">
                                            <#assign linechart2=statics["java.util.UUID"].randomUUID()>
                                                <canvas id="${linechart2}"></canvas>
                                        </div>
                                        <div class="card-footer">
                                            <div class="row">
                                                <div class="legend-wrap mb-1-1 px-4 legend-sm">
                                                    <p class="middle-blue-solid">
                                                        ${localization.getMessage("Report.RumenHealthManureScreening.Top",
                                                        [], "Top", locale)}</p>
                                                </div>
                                                <div class="legend-wrap mb-1-1 px-4 legend-sm">
                                                    <p class="jordy-blue-solid">
                                                        ${localization.getMessage("Report.RumenHealthManureScreening.Middle",
                                                        [], "Middle", locale)}</p>
                                                </div>
                                                <div class="legend-wrap mb-1-1 px-4 legend-sm">
                                                    <p class="blue-purple-solid">
                                                        ${localization.getMessage("Report.RumenHealthManureScreening.Bottom",
                                                        [], "Bottom", locale)}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <script>
                                    (function () {

                                    const bar1 = [
                                    <#list column.onScreenPercentage as val>
                                    ${(val.top)!'NaN'}<#sep>, </#sep>
                                        </#list>
                                    ];

                                    const bar2 = [
                                        <#list column.onScreenPercentage as val>
                                             ${(val.middle)!'NaN'}<#sep>, </#sep>
                                        </#list>
                                    ];

                                    const bar3 = [
                                        <#list column.onScreenPercentage as val>
                                             ${(val.bottom)!'NaN'}<#sep>, </#sep>
                                        </#list>
                                    ];


                                    const xAxis = [
                                    <#list column.onScreenPercentage as val>
                                        '${val.visitDate!}'<#sep>, </#sep>
                                    </#list>
                                    ];

                                    const ctx = document.getElementById("${linechart2}").getContext("2d");
                                    ctx.canvas.height = 130;
                                    const borderWidth = 1;
                                    const categoryPercentage = 0.80;
                                    const barPercentage = 1;
                                    const options = {
                                        type: "bar",
                                        data: {
                                        labels: xAxis,
                                        datasets: [{
                                        data: bar1,
                                        grouped: true,
                                        backgroundColor: "#7AD8DC",
                                        categoryPercentage: categoryPercentage,
                                        barPercentage: barPercentage,
                                        borderColor: "rgba(0,0,0,0)",
                                        borderWidth: borderWidth,
                                        },

                                                    {
                                        data: bar2,
                                        backgroundColor: "#83BEF4",
                                        categoryPercentage: categoryPercentage,
                                        barPercentage: barPercentage,
                                        borderColor: "rgba(0,0,0,0)",
                                        borderWidth: borderWidth,
                                        },

                                                    {
                                        data: bar3,
                                        backgroundColor: "#ABA1E3",
                                        categoryPercentage: categoryPercentage,
                                        barPercentage: barPercentage,
                                        borderColor: "rgba(0,0,0,0)",
                                        borderWidth: borderWidth,
                                        }
                                                ]
                                        },
                                        options: {
                                            plugins: {
                                                legend: {
                                                    display: false,
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        title: () => null // or function () { return null; }
                                                    },
                                                    yAlign: 'bottom',
                                                    backgroundColor: "#fff",
                                                    borderColor: "rgba(0, 0, 0, 0.25)",
                                                    borderWidth: 1,
                                                    displayColors: false,
                                                    bodyColor: "#307698",
                                                    bodyAlign: "center",
                                                },
                                            },
                                            scales: {
                                                responsive: true,
                                                interaction: {
                                                    intersect: false,
                                                },
                                                x: {
                                                    stacked: false
                                                },
                                                y: {
                                                    stacked: false,
                                                }
                                            },
                                            layout: {
                                                padding: {
                                                    top: 5,
                                                    right: 0,
                                                    bottom: 0
                                                }
                                            },

                                            responsive: true,

                                            scales: {
                                                y: {
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("", [], "", locale)}',
                                                        padding: {
                                                            bottom: 0,
                                                        }
                                                    },
                                                    grid: {
                                                        display: false,
                                                    },
                                                    suggestedMax: (scale) => {

                                                        var curr = scale.chart.data.datasets;
                                                        var arr = [];
                                                        for (let i = 0; i < curr.length; i++) {
                                                            arr.push(Math.max.apply(null, curr[i].data.filter(x => !isNaN(x))));
                                                        }
                                                        return Math.round(Math.max(...arr) + (Math.max(...arr) * 0.05));
                                                    },
                                                    suggestedMin: (scale) => {
                                                        var curr = scale.chart.data.datasets;
                                                        var arr = [];
                                                        for (let i = 0; i < curr.length; i++) {
                                                            arr.push(Math.min.apply(null, curr[i].data.filter(x => !isNaN(x))));
                                                        }
                                                        return Math.floor(Math.min(...arr) + (Math.min(...arr) * 0.15));
                                                    },

                                                },

                                                x: {
                                                    title: {
                                                        display: true,
                                                        color: '#6C7782',
                                                        text: '${localization.getMessage("", [], "", locale)}',
                                                        padding: {
                                                            top: 0,
                                                        }
                                                    },
                                                    grid: {
                                                        display: false,
                                                    },
                                                    ticks: {
                                                        autoSkip: false,
                                                        maxRotation: 40,
                                                        minRotation: 0

                                                    }
                                                }
                                            },
                                            animation: {
                                                duration: 0,
                                                onComplete: function() {
                                                var chart = this;
                                                var ctx = chart.ctx;
                                                ctx.textAlign = 'center';
                                                ctx.textBaseline = 'bottom';
                                                ctx.fillStyle = '#6C7782';
                                                this.data.datasets.forEach(function(dataset, i) {
                                                var meta = chart.getDatasetMeta(i);
                                                meta.data.forEach(function(bar, index) {
                                                var data = dataset.data[index];
                                                data = isNaN(data) ? '' : data;
                                                var yIndex = bar.y - 12;
                                                if (data && data < 0) {
                                                yIndex = bar.y + 15;
                                                }

                                                                        var fontSize = Math.round(chart.chartArea.width / dataset.data.length / 3);
                                                                        if (fontSize > 12) {
                                                fontSize = 12;
                                                } else if (fontSize < 5) {
                                                fontSize = 5;
                                                }
                                                                        ctx.font = fontSize + 'px "Helvetica Neue", Helvetica, Arial, sans-serif'
                                                                        ctx.save();
                                                                        // Translate 0,0 to the point you want the text
                                                                        ctx.translate(bar.x + 5, yIndex);
                                                                        // Rotate context by -90 degrees
                                                                        ctx.rotate(-0.4 * Math.PI);
                                                                        ctx.textAlign = "center";
                                                                        ctx.fillText(data, 0, 0);
                                                                        ctx.restore();
                                                                        chart.resize();
                                                                    });
                                                                });
                                                            }
                                                        }
                                                    }
                                                };
                                    window.myLine = new Chart(ctx, options);
                            }) ();

                                </script>
                                <#assign counter=counter+1>
                            </#list>
                        </div>
                        <#if counter%8==0 && counter lt
                            model.rumenHealthManureScreeningTool.penAnalysis.graph?size-1>
                            <div class="break-page"></div>
						<#elseif penRows lte 9 && counter==4 && counter lt
							model.rumenHealthManureScreeningTool.penAnalysis.graph?size-1 && isFirstIteration>
							<div class="break-page"></div>
							<#assign counter=0>
							<#assign isFirstIteration=false>
						<#elseif penRows gt 9 && penRows lt 16 && counter==2 && counter lt
							model.rumenHealthManureScreeningTool.penAnalysis.graph?size-1 && isFirstIteration>
							<div class="break-page"></div>
							<#assign counter=0>
							<#assign isFirstIteration=false>
                        </#if>
						
                    </#list>
                </div>
            </#if>
</#if>

<#if model.rumenHealthManureScreeningTool?? && model.rumenHealthManureScreeningTool.notes??>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.rumenHealthManureScreeningTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1" style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
    </div>
</#if>