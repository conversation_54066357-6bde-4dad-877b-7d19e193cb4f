/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.LangCodes;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.ReturnOverFeedKgFatReportDto;
import com.app.cargill.dto.ReturnOverFeedPerKgFatDataPoints;
import com.app.cargill.dto.ReturnOverFeedReportDto;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Locale;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;

@ExtendWith(MockitoExtension.class)
class ReturnOverFeedPerKgButterFatReportServiceImplTest {

  @Mock private ModelMapper modelMapper;
  @Mock private FreeMarkerComponent freeMarkerComponent;

  @InjectMocks
  private ReturnOverFeedPerKgButterFatReportServiceImpl returnOverFeedPerKgButterFatReportService;

  private ResourceBundleMessageSource messageSource;
  private Locale locale;
  private ReturnOverFeedKgFatReportDto testDto;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    messageSource = new ResourceBundleMessageSource();
    messageSource.setBasename("messages");
    locale = Locale.US;
    testDto = createTestDto();
  }

  @Test
  void testGetFileName_withValidDto_shouldReturnCorrectFileName() {
    // Given
    Object inputData = new Object();
    ReturnOverFeedKgFatReportDto mappedDto = ReturnOverFeedKgFatReportDto.builder()
        .fileName("custom_filename.xlsx")
        .build();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(mappedDto);

    // When
    String fileName = returnOverFeedPerKgButterFatReportService.getFileName(inputData);

    // Then
    assertEquals("custom_filename.xlsx", fileName);
  }

  @Test
  void testGetFileName_withBlankFileName_shouldReturnDefaultFileName() {
    // Given
    Object inputData = new Object();
    ReturnOverFeedKgFatReportDto mappedDto = ReturnOverFeedKgFatReportDto.builder()
        .fileName("")
        .build();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(mappedDto);

    // When
    String fileName = returnOverFeedPerKgButterFatReportService.getFileName(inputData);

    // Then
    assertEquals(ReportsToBeanMappings.RETURN_OVER_FEED_PER_KG_BUTTERFAT.getFileName(), fileName);
  }

  @Test
  void testGetFileName_withNullFileName_shouldReturnDefaultFileName() {
    // Given
    Object inputData = new Object();
    ReturnOverFeedKgFatReportDto mappedDto = ReturnOverFeedKgFatReportDto.builder()
        .fileName(null)
        .build();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(mappedDto);

    // When
    String fileName = returnOverFeedPerKgButterFatReportService.getFileName(inputData);

    // Then
    assertEquals(ReportsToBeanMappings.RETURN_OVER_FEED_PER_KG_BUTTERFAT.getFileName(), fileName);
  }

  @Test
  void testPrepareExportToExcel_withValidData_shouldReturnValidExcelFile() throws IOException {
    // Given
    Object inputData = new Object();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(testDto);

    // When
    ByteArrayResource resource = returnOverFeedPerKgButterFatReportService
        .prepareExportToExcel(inputData, messageSource, locale);

    // Then
    assertNotNull(resource);
    assertTrue(resource.contentLength() > 0);

    // Verify it's a valid Excel file
    try (InputStream is = resource.getInputStream()) {
      assertDoesNotThrow(() -> WorkbookFactory.create(is));
    }
  }

  @Test
  void testPrepareExportToExcel_withEmptyDataPoints_shouldReturnValidExcelFile() throws IOException {
    // Given
    Object inputData = new Object();
    ReturnOverFeedKgFatReportDto emptyDto = createTestDto();
    emptyDto.setDataPoints(List.of());
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(emptyDto);

    // When
    ByteArrayResource resource = returnOverFeedPerKgButterFatReportService
        .prepareExportToExcel(inputData, messageSource, locale);

    // Then
    assertNotNull(resource);
    assertTrue(resource.contentLength() > 0);
  }

  @Test
  void testPrepareExportToExcel_withNullValues_shouldHandleGracefully() throws IOException {
    // Given
    Object inputData = new Object();
    ReturnOverFeedKgFatReportDto dtoWithNulls = createTestDtoWithNullValues();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(dtoWithNulls);

    // When
    ByteArrayResource resource = returnOverFeedPerKgButterFatReportService
        .prepareExportToExcel(inputData, messageSource, locale);

    // Then
    assertNotNull(resource);
    assertTrue(resource.contentLength() > 0);
  }

  @Test
  void testPrepareExportToImage_withValidData_shouldReturnValidImageResource()
      throws IOException, URISyntaxException {
    // Given
    Object inputData = new Object();
    byte[] mockImageBytes = "test image content".getBytes();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(testDto);
    when(freeMarkerComponent.render(
        eq(testDto),
        eq(ReportsToBeanMappings.RETURN_OVER_FEED_PER_KG_BUTTERFAT.getImageTemplateName0()),
        eq(messageSource),
        eq(locale),
        eq(TemplateExportType.EXPORT_IMAGE)
    )).thenReturn(mockImageBytes);

    // When
    ByteArrayResource resource = returnOverFeedPerKgButterFatReportService
        .prepareExportToImage(inputData, messageSource, locale);

    // Then
    assertNotNull(resource);
    assertTrue(resource.contentLength() > 0);
  }

  @Test
  void testPrepareExportToImage_withFreeMarkerException_shouldThrowIOException()
      throws IOException, URISyntaxException {
    // Given
    Object inputData = new Object();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class)).thenReturn(testDto);
    when(freeMarkerComponent.render(any(), anyString(), any(), any(), any()))
        .thenThrow(new RuntimeException("FreeMarker error"));

    // When & Then
    assertThrows(RuntimeException.class, () ->
        returnOverFeedPerKgButterFatReportService.prepareExportToImage(inputData, messageSource, locale));
  }

  @Test
  void testPrepareExportToExcel_withMappingException_shouldThrowException() throws IOException {
    // Given
    Object inputData = new Object();
    when(modelMapper.map(inputData, ReturnOverFeedKgFatReportDto.class))
        .thenThrow(new RuntimeException("Mapping error"));

    // When & Then
    assertThrows(RuntimeException.class, () ->
        returnOverFeedPerKgButterFatReportService.prepareExportToExcel(inputData, messageSource, locale));
  }

  private ReturnOverFeedKgFatReportDto createTestDto() {
    return ReturnOverFeedKgFatReportDto.builder()
        .fileName("test_return_over_feed_per_kg_butterfat.xlsx")
        .visitName("Test Visit")
        .visitDate("2025-01-15")
        .toolName("Return Over Feed Per Kg Butterfat Tool")
        .returnOverFeedKgPerFatLabel("Return Over Feed Per Kg Butterfat (%)")
        .rofPerKgButterFatLabel("ROF Per Kg Butterfat")
        .concentrateCostPerKgButterFatLabel("Concentrate Cost Per Kg Butterfat")
        .xAxisLabel("Visit Date")
        .totalRevenueDollarKgPerFatLabel("Total Revenue Per Kg Butterfat ($)")
        .totalCostsPerKgPerFatLabel("Total Costs Per Kg Butterfat ($)")
        .dataPoints(List.of(
            ReturnOverFeedPerKgFatDataPoints.builder()
                .rofPerKgButterFat(15.75)
                .concentrateCostPerKgButterFat(8.20)
                .totalRevenueDollarKgPerFat(24.95)
                .totalCostsPerKgPerFat(9.20)
                .visitDate("Week 1")
                .build(),
            ReturnOverFeedPerKgFatDataPoints.builder()
                .rofPerKgButterFat(14.50)
                .concentrateCostPerKgButterFat(7.80)
                .totalRevenueDollarKgPerFat(23.30)
                .totalCostsPerKgPerFat(8.80)
                .visitDate("Week 2")
                .build()
        ))
        .build();
  }

  private ReturnOverFeedKgFatReportDto createTestDtoWithNullValues() {
    return ReturnOverFeedKgFatReportDto.builder()
        .fileName("test_with_nulls.xlsx")
        .visitName(null)
        .visitDate(null)
        .toolName(null)
        .dataPoints(List.of(
            ReturnOverFeedPerKgFatDataPoints.builder()
                .rofPerKgButterFat(null)
                .concentrateCostPerKgButterFat(null)
                .totalRevenueDollarKgPerFat(null)
                .totalCostsPerKgPerFat(null)
                .visitDate(null)
                .build()
        ))
        .build();
  }
}
