/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class MathUtilsTest {
  @ParameterizedTest
  @CsvSource({"3.5,4", "4.5,4", "4.75,5"})
  void testCustomRoundWithValidOddInput(String input, int expected) {

    // Act
    int result = MathUtils.customRound(input);

    // Assert
    Assertions.assertEquals(expected, result); // Use user assertions to check the result
  }

  @Test
  void testCustomRoundWithInvalidInput() {
    // Arrange
    String input = "invalidInput"; // Change this to an invalid input you want to test
    // Act and Assert
    try {
      MathUtils.customRound(input);
      Assertions.fail("Expected IllegalArgumentException was not thrown.");
    } catch (IllegalArgumentException e) {
      Assertions.assertEquals(
          "Input string is not a valid double: " + input,
          e.getMessage()); // Use user assertions to check the exception message
    }
  }
}
