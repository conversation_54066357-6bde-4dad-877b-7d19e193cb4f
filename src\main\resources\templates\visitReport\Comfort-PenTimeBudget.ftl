	<div class="container">
    <div class="legend-head">
        <div class="count">${toolNumber!}</div>
        <div class="main-title">
            <span class="sm-head">${localization.getMessage("VisitViewModel.ComfortItem", [],"Comfort",locale)}</span>
            <span class="lg-head">${localization.getMessage("VisitSummaryViewModel.PenTimeTitle", [],"Pen time budget",locale)}</span>
        </div>
        <div style="font-size: 1;color: white;">0000444PB</div>
    </div>
</div>
	
<#assign counter=0>
<#if  model.penTimeBudgetTool.penAnalysis?? && model.penTimeBudgetTool.penAnalysis[0]??>
	<!-- Pen 1 -->
	<div class="container">
		<#list model.penTimeBudgetTool.penAnalysis as penAnalysis>
		<#assign counter=counter+1>
		<h3 class="title-secondary mb-1 d-flex justify-space-between">
			<span>${penAnalysis.penName!}</span>
		</h3>
		<#if penAnalysis.penDetails?? && penAnalysis.penDetails[0]??>
		<div class="row mx-neg-4">
		<#list penAnalysis.penDetails?chunk(penAnalysis.penDetails?size/2) as column>
			<div class="col-6 mb-1 px-4 table-secondary">
				<table class="w-50">
					<tbody>
					<#list column as td>
						<tr>
							<td>${td.column!}</td>
							<td>${td.value!}</td>
						</tr>
					</#list>
					</tbody>
				</table>
			</div>
			</#list>
		</div>	
		</#if>		
		<#if penAnalysis.timeAvailableForRestingGraph?? || penAnalysis.potentialMilkLossGainGraph??>
		<div class="row mx-neg-4">
		<#if penAnalysis.timeAvailableForRestingGraph?? && penAnalysis.timeAvailableForRestingGraph.timeRequired ?? && penAnalysis.timeAvailableForRestingGraph.timeRemaining ?? >
			<div class="col-6 px-4">
				<h6>${localization.getMessage("PenTimeBudgetResultsViewModel.PenTimeBudgetTitle", [],"Time available for resting",locale)}</h6>
				<div class="card mb-1">
					<div class="card-body">
					<#assign linechart2=statics["java.util.UUID"].randomUUID()>
						<canvas id="${linechart2!}"></canvas>
					</div>
					<div class="card-footer">
						<div class="row">
							<div class="legend-wrap mb-2">
								<p class="blue-light-solid">${localization.getMessage("PenTimeBudgetResultsViewModel.TimeRequired", [],"Time required",locale)}</p>
							</div>
							<div class="legend-wrap mb-2">
								<p class="pink-light-solid">${localization.getMessage("PenTimeBudgetResultsViewModel.TimeRemaining", [],"Time remaining",locale)}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<script>

                                (function () {
	const colors = {
		purple: {
		default: "#6DB2F2",
				half: "#e1effccf",
				quarter: "#e1effcb3",
				zero: "#e1effc00"
		},
			indigo: {
		default: "#EE6E81",
				half: "#fbe2e5c7",
				quarter: "#fbe2e56b",
				zero: "#fbe2e500"
		}
		};
		const timeRequired = [
		<#list penAnalysis.timeAvailableForRestingGraph.timeRequired as timerequired>
			${(timerequired.y)!'NaN'}<#sep>, </#sep>
		</#list>
		];
		const timeRemaining = [
		<#list penAnalysis.timeAvailableForRestingGraph.timeRemaining as timeremaining>
			${(timeremaining.y)!'NaN'}<#sep>, </#sep>
			</#list>
		];
		const labels = [
		<#list penAnalysis.timeAvailableForRestingGraph.visitDates as visitDate>
			'${localization.getMessage(visitDate, [], visitDate, locale)}'<#sep>, </#sep>
		</#list>
		];
		const ctx = document.getElementById("${linechart2!}").getContext("2d");
		ctx.canvas.height = 100;
		gradient = ctx.createLinearGradient(0, 25, 0, 300);
		gradient.addColorStop(0, colors.purple.half);
		gradient.addColorStop(0.35, colors.purple.quarter);
		gradient.addColorStop(1, colors.purple.zero);
		const options = {
			type: "line",
			data: {
				labels: labels,
				datasets: [
					{
						fill: true,
						backgroundColor: gradient,
						borderColor: '#6DB2F2',
						pointBackgroundColor: '#6DB2F2',
						pointBorderColor: '#fff',
						data: timeRequired,
						lineTension: 0.2,
						borderWidth: 1,
						pointRadius: 6,
						yAxisID: 'y',
					},
					{
						fill: true,
						
						borderColor: '#EE6E81',
						pointBackgroundColor: '#EE6E81',
						pointBorderColor: '#fff',
						data: timeRemaining,
						lineTension: 0.2,
						borderWidth: 1,
						pointRadius: 6,
						yAxisID: 'y',
					},
				]
			},
			options: {
				plugins: {
					legend: {
						display: false,
					},
					tooltip: {
						callbacks: {
							title: () => null // or function () { return null; }
						},
						yAlign: 'bottom',
						backgroundColor: "#fff",
						borderColor: "rgba(0, 0, 0, 0.25)",
						borderWidth: 1,
						displayColors: false,
						bodyColor: "#1BACA7",
						bodyAlign: "center",
					},
				},
				layout: {
					padding: {
						top:20,
						right: 15
					}
				},
				responsive: true,
				scales: {
					y: {
						type: 'linear',
						position: 'left',
						title: {
							display: true,
							color: '#6C7782',
							text: '${localization.getMessage("Report.Pentime.Budget.Hours", [], "Hours", locale)}',
							padding: {
								bottom: 15,
							},
						},
						grid: {
							display: false
						}
					},
					x: {
						title: {
							display: true,
							color: '#6C7782',
							text: '${localization.getMessage("Report.Visit.Date", [], "Visit Dates", locale)}',
							padding: {
								top: 15,
							}
						},
						grid: {
							display: false,
						},
					}
				},
				animation: {
					duration: 0,
					onComplete: function() {
						var chart = this;
						var ctx = chart.ctx;
						ctx.textAlign = 'center';
						ctx.textBaseline = 'bottom';
						ctx.fillStyle = '#6C7782';
						this.data.datasets.forEach(function(dataset, i) {
							var meta = chart.getDatasetMeta(i);
							meta.data.forEach(function(bar, index) {
								var data = dataset.data[index];
								data = isNaN(data) ? '': data;
								var yIndex = bar.y - 5;
								if(data && data < 0) {
									yIndex = bar.y + 15;
								}
								ctx.fillText(data, bar.x, yIndex);
							});
						});
					}
				}
			}
		};
window.myLine = new Chart(ctx, options);
}) ();
                            </script>
			</#if>
			<#if penAnalysis.potentialMilkLossGainGraph??>
			<div class="col-6 px-4">
				<h6>${localization.getMessage("PenTimeBudgetResultsViewModel.PenTimeBudgetMilkLossTitle", [],"Potential milk loss/gain",locale)}</h6>
				<div class="card mb-1">
					<div class="card-body">
						<#assign linechart2=statics["java.util.UUID"].randomUUID()>
						<canvas id="${linechart2!}"></canvas>
					</div>
					<div class="card-footer">
						<div class="row">
							<div class="legend-wrap mb-2">
								<p class="green-solid">${localization.getMessage("PenTimeBudgetResultsViewModel.MilkDifference", [],"Potential milk difference",locale)}</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<script>

                                (function () {
								
const colors = {
purple: {
default: "#1baca7",
half: "#1baca778",
quarter: "#1baca73b",
zero: "#1baca71c"
},
	indigo: {
default: "#1baca7",
quarter: "#1baca778"
}
};


const yAxis = [
<#if penAnalysis.potentialMilkLossGainGraph.dataPoints??>
    <#list penAnalysis.potentialMilkLossGainGraph.dataPoints as datapoint>
        ${(datapoint.y)!'NaN'}<#sep>, </#sep>
    </#list>
</#if>
];

const xAxis = [
<#if penAnalysis.potentialMilkLossGainGraph.dataPoints??>
    <#list penAnalysis.potentialMilkLossGainGraph.dataPoints as datapoint>
        "${(datapoint.x)!'NaN'}"<#sep>, </#sep>
    </#list>
	</#if>
];

const ctx = document.getElementById("${linechart2!}").getContext("2d");
ctx.canvas.height = 100;
var rgb = hexToRgb('#1BACA7');
gradient = ctx.createLinearGradient(rgb.r, rgb.g, rgb.b, 300);

gradient.addColorStop(0, 'rgba('+rgb.r+','+rgb.g+','+rgb.b+',0.35)');
gradient.addColorStop(0.35,'rgba('+rgb.r+','+rgb.g+','+rgb.b+',0.20)');
gradient.addColorStop(1, 'rgba('+rgb.r+','+rgb.g+','+rgb.b+',0.05)');

const options = {
type: "line",
data: {
labels: xAxis,
datasets: [
{
fill: true,
backgroundColor: gradient,
borderColor: '#1BACA7',
pointBackgroundColor: '#1BACA7',
pointBorderColor: '#fff',
data: yAxis,
lineTension: 0.2,
borderWidth: 1,
pointRadius: 6,
}
    ]
  },
  options: {
plugins: {
legend: {
display: false,
},
		tooltip: {
callbacks: {
title : () => null // or function () { return null; }
         },
			yAlign: 'bottom',
			backgroundColor: "#fff",
			borderColor: "rgba(0, 0, 0, 0.25)",
			borderWidth: 1,
			displayColors: false,
			bodyColor: "#1BACA7",
			bodyAlign: "center",
        },
  	},

    layout: {
		padding: {
			top:20,
			right: 15
		}
	},

    responsive: true,

    scales: {
y: {
// beginAtZero: true,
title: {
display: true,
color: '#6C7782',
text: '${penAnalysis.potentialMilkLossGainGraph.label!}',
				padding: {
bottom: 15,
}
			},

						grid: {
display: false,
},
      	},

		x: {
grid: {
display: false,
},
			color: '#6C7782',
			title: {
display: true,
color: '#6C7782',
text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
			padding: {
bottom: 15,
}
		}
		}
    },
	animation: {
	duration: 0,
	onComplete: function() {
		var chart = this;
		var ctx = chart.ctx;
		ctx.textAlign = 'center';
		ctx.textBaseline = 'bottom';
		ctx.fillStyle =  '#6C7782';
		this.data.datasets.forEach(function(dataset, i) {
						var meta = chart.getDatasetMeta(i);
						meta.data.forEach(function(bar, index) {
						var data = dataset.data[index];
						data = isNaN(data) ? '': data;
						var yIndex = bar.y - 5;
						if(data && data < 0) {
							yIndex = bar.y + 15;
						}
						ctx.fillText(data, bar.x, yIndex);
						});
				});
			}
		}
  }
};

window.myLine = new Chart(ctx, options);
}) ();
                            </script>
			</#if>
		</div>		
		</#if>	
		<#if counter%2==0 && counter lt model.penTimeBudgetTool.penAnalysis?size>
		<div class="break-page"></div>
		</#if>	
		</#list>
	</div>


</#if>	
<#if model.penTimeBudgetTool?? && model.penTimeBudgetTool.notes??>
    <#if model.penTimeBudgetTool.penAnalysis?? && model.penTimeBudgetTool.penAnalysis[0]??>
    </#if>
    <div class="container mid-body">
        <div class="pt-0">
            <h3 class="title-secondary mb-1" class="title-secondary mb-1" style="margin-top: 10px;">${localization.getMessage("FreeFormReportViewModel.Notes", [], "Notes",
                locale)}</h3>
            <#list model.penTimeBudgetTool.notes as innerlist>
                <#if innerlist.id??>
                    <#list model.notes?filter(x->x.id==innerlist.id) as noteFound >
                        <h4 class="followup mb-1">
                            <span  style="white-space: pre-wrap;" >${noteFound.title!}</span>
                            <span class="date">${noteFound.cratedDateTimeFormatted!}</span>
                        </h4>
                        <p class="mb-1"  style="white-space: pre-wrap;" >${noteFound.note!}</p>
                        <#if noteFound.mediaItems?? && noteFound.mediaItems[0]??>
                            <div class="notes-images mb-1">
                                <#list noteFound.mediaItems as media>
                                    <figure>
                                        <img src="${media.base64EncodedImage!}">
                                    </figure>
                                </#list>
                            </div>
                        </#if>
                    </#list>
                </#if>
            </#list>
        </div>
</#if>