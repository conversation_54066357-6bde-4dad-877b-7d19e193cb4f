/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class SObject {
  private String name;
  private String associateEntityType;
  private String associateParentEntity;
  private String keyPrefix;
  private String label;
  private String labelPlural;
  private Map<String, String> urls;
}
