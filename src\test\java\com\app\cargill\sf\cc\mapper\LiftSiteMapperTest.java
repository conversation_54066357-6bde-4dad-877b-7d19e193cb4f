/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

import com.app.cargill.sf.cc.model.simple.Site;
import org.junit.jupiter.api.Test;

class LiftSiteMapperTest {
  @Test
  void whenThereIsAnErrorCorrectExceptionIsThrown() {
    assertThrows(IllegalArgumentException.class, () -> LiftSiteMapper.transform(mock(Site.class)));
  }
}
