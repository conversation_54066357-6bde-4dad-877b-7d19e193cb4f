<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="015" author="anenkov">
    <sql>
      CREATE TABLE IF NOT EXISTS activities (
        id bigserial not null,
        created_date timestamp,
        deleted BOOLEAN default false,
        local_id varchar(255),
        updated_date timestamp,
        activity_document jsonb,
        primary key (id));

      CREATE UNIQUE INDEX IF NOT EXISTS activities_document_id_uidx ON activities((activity_document->>'id'));

    </sql>
  </changeSet>

</databaseChangeLog>
