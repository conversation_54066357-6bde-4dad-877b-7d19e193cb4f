/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.document.PenDocument;
import com.app.cargill.model.Pens;
import com.app.cargill.model.tasks.DuplicatePens;
import java.time.Instant;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface PensRepository extends JpaRepository<Pens, Long> {

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'CustomerAccountId' IN (:accountIds) AND"
              + " timezone('UTC',p.updated_date) > :lastSyncTime AND p.deleted=false",
      nativeQuery = true)
  Page<Pens> findByCustomerAccountIdAndUpdatedDate(
      @Param("accountIds") List<String> accountIds,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'CustomerAccountId' IN (:accountIds) AND"
              + " p.deleted=false",
      nativeQuery = true)
  List<Pens> findByCustomerAccountIds(@Param("accountIds") List<String> accountIds);

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'SiteId' = :siteId AND"
              + " timezone('UTC',p.updated_date) > :lastSyncTime AND p.deleted=false ",
      nativeQuery = true)
  Page<Pens> findBySiteIdAndUpdatedDate(
      @Param("siteId") String siteId,
      @Param("lastSyncTime") Instant lastSyncTime,
      Pageable pageable);

  List<Pens> findByLocalId(String localId);

  @Query(
      value = "Select a.* FROM Pens a where a.pen_document ->> 'Id' = :id and a.deleted=false",
      nativeQuery = true)
  Pens findByPenId(@Param("id") String toString);

  @Query(
      value =
          "SELECT COUNT(p.*) FROM Pens p WHERE p.pen_document ->> 'SiteId' = :siteId AND"
              + " p.deleted=false",
      nativeQuery = true)
  Integer countByPenDocumentSiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'SiteId' IN (:siteIds) AND "
              + " p.deleted=false ",
      nativeQuery = true)
  List<Pens> findBySiteIds(@Param("siteIds") List<String> siteIds);

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'SiteId' = :siteId AND "
              + " p.deleted=false ",
      nativeQuery = true)
  List<Pens> findBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "SELECT p.pen_document FROM Pens p WHERE p.pen_document ->> 'SiteId' = :siteId AND "
              + " p.deleted=false ",
      nativeQuery = true)
  List<PenDocument> findPenDocumentBySiteId(@Param("siteId") String siteId);

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'SiteId' = :siteId AND  p.pen_document"
              + " ->> 'Name' = :penName and p.pen_document ->> 'Source' = :penSource  and  "
              + " p.pen_document ->> 'GroupId' = :groupId and "
              + " p.deleted=false LIMIT 1 ",
      nativeQuery = true)
  Pens findBySiteIdAndPenNameAndSourceAndGroupId(
      @Param("siteId") String siteId,
      @Param("penName") String penName,
      @Param("penSource") String penSource,
      @Param("groupId") String groupId);

  @Query(
      value =
          "select  count(*) penCount,  pen_document->>'SiteId' siteId, pen_document->>'Name' as"
              + " penName,  pen_document->>'Source' source,  pen_document->>'GroupID' groupId, "
              + " array_to_string(array_agg(pen_document->>'Id'), ',') penIds   from pens  GROUP"
              + " BY ( pen_document->>'SiteId', pen_document->>'Name', pen_document->>'Source',"
              + " pen_document->>'Name',  pen_document->>'GroupID') having count(*) > 1 "
              + " order by count(*) asc offset :offset limit 100",
      nativeQuery = true)
  List<DuplicatePens> findDuplicatePens(@Param("offset") int offset);

  @Query(
      value =
          "SELECT p.* FROM Pens p WHERE p.pen_document ->> 'DietId' = :dietId AND "
              + " p.deleted=false ",
      nativeQuery = true)
  List<Pens> findByDietId(@Param("dietId") String dietId);
}
