/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.app.cargill.constants.ApplicationMapping;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.constants.VisitStatus;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Barn;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.DataSourceMapping;
import com.app.cargill.document.PenDocument;
import com.app.cargill.document.SiteDocument;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.document.SiteVisit;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.dto.SiteDto;
import com.app.cargill.dto.SiteMappingDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.app.cargill.model.Pens;
import com.app.cargill.model.Sites;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.PensRepository;
import com.app.cargill.repository.SiteMappingsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.mappers.SiteMapper;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftSiteMappingsService;
import com.app.cargill.sf.cc.service.LiftSitesService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.utils.LiftUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@ExtendWith(MockitoExtension.class)
class SiteServiceImplTest {

  @Mock private SitesRepository sitesRepository;
  @Mock private AccountsRepository accountsRepository;
  @Mock private IUserService userServiceImpl;
  @Mock private LiftUserService liftUserService;
  @Mock private LiftApiService liftApiService;
  @Mock private LiftUtils liftUtils;
  @Mock private SiteMappingServiceImpl siteMappingServiceImpl;
  @Mock private LiftSiteMappingsService liftSiteMappingsService;
  @Mock private SiteMappingsRepository siteMappingsRepository;
  @Mock private PensRepository pensRepository;
  @Mock private LiftSitesService liftSitesServiceImpl;
  @InjectMocks private SiteServiceImpl siteService;
  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private Locale locale;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
    locale = Locale.ENGLISH;
  }

  @Test
  void getAllSitesByCurrentLoggedInUserReturnsExpected() {

    Page<Sites> sites =
        new PageImpl<>(
            List.of(
                Sites.builder()
                    .siteDocument(new SiteDocument())
                    .updatedDate(Date.from(Instant.now()))
                    .createdDate(Date.from(Instant.now()))
                    .build()));
    when(sitesRepository.findByUpdatedDateAndAccountsUsers(any(), any(), any())).thenReturn(sites);
    Page<SiteDto> result =
        siteService.getAllSitesByCurrentLoggedInUser(0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenNoSitesForCurrentUserAreFoundEmptyPageIsReturned() {
    when(sitesRepository.findByUpdatedDateAndAccountsUsers(any(), any(), any())).thenReturn(null);
    Page<SiteDto> result =
        siteService.getAllSitesByCurrentLoggedInUser(0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
  }

  @Test
  void getAllSitesByAccountIdReturnsExpected() {

    Page<Sites> sites =
        new PageImpl<>(
            List.of(
                Sites.builder()
                    .siteDocument(new SiteDocument())
                    .updatedDate(Date.from(Instant.now()))
                    .createdDate(Date.from(Instant.now()))
                    .build()));
    when(sitesRepository.findByAccountIdAndUpdatedDate(any(), any(), any())).thenReturn(sites);
    Page<SiteDto> result =
        siteService.getAllSitesByAccountId("1", 0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(1L, result.getTotalElements());
  }

  @Test
  void whenSaveToLiftIsCalledCorrectResultIsSaved() throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    siteService.saveToSf(
        loadSite(),
        Accounts.builder()
            .accountDocument(AccountDocument.builder().goldenRecordId("test").build())
            .build(),
        locale,
        resourceBundleMessageSource);
    assertNotNull(loadSite());
  }

  @Test
  void whenSaveToLiftIsCalledSitesValidationExceptionIsThrown() throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftApiService.validate(any(), eq(LiftEntityName.SITE), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(liftApiService.validate(any(), eq(LiftEntityName.SITE_MAPPING), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());
    Assertions.assertThrows(
        CustomDEExceptions.class,
        () ->
            siteService.saveToSf(
                loadSite(),
                Accounts.builder()
                    .accountDocument(AccountDocument.builder().goldenRecordId("test").build())
                    .build(),
                locale,
                resourceBundleMessageSource));
  }

  @Test
  void whenSaveToLiftIsCalledSitesMappingValidationExceptionIsThrown() throws Exception {

    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(
            PayloadValidationDto.builder()
                .errorDetails(
                    List.of(
                        LiftResponseEntityDto.builder()
                            .message("test")
                            .status(ResponseStatus.FAILED)
                            .build()))
                .build());
    Assertions.assertThrows(
        CustomDEExceptions.class,
        () ->
            siteService.saveToSf(
                loadSite(),
                Accounts.builder()
                    .accountDocument(AccountDocument.builder().goldenRecordId("test").build())
                    .build(),
                locale,
                resourceBundleMessageSource));
  }

  @Test
  void whenSaveToLiftIsCalledButGoldenRecordIdIsnull() throws Exception {

    //    when(accountsRepository.findByAccountId(any()))
    //        .thenReturn(
    //            Accounts.builder()
    //                .accountDocument(AccountDocument.builder().goldenRecordId(null).build())
    //                .build());
    User user = new User();
    user.setId("test");
    user.setEmail("test");
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    siteService.saveToSf(
        loadSite(),
        Accounts.builder()
            .accountDocument(AccountDocument.builder().goldenRecordId("test").build())
            .build(),
        locale,
        resourceBundleMessageSource);
    assertNotNull(loadSite());
  }

  @Test
  void whenNoSitesByAccountIdAreFoundEmptyPageIsReturned() {
    when(sitesRepository.findByAccountIdAndUpdatedDate(any(), any(), any())).thenReturn(null);
    Page<SiteDto> result =
        siteService.getAllSitesByAccountId("1", 0, 10, "id", Instant.now(), "desc");
    assertNotNull(result);
    assertEquals(0, result.getTotalElements());
  }

  @Test
  void whenAllWorksCorrectSaveResultIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    SiteDto siteDto =
        SiteDto.builder()
            .accountId(UUID.randomUUID())
            .keys(new HashMap<>())
            .siteMappings(
                List.of(
                    SiteMappingDto.builder()
                        .systemId(UUID.randomUUID().toString())
                        .systemName("LM_SITE")
                        .build()))
            .localId(localId)
            .build();
    User user = new User();
    user.setEmail("test");
    user.setId(UUID.randomUUID().toString());
    when(liftUserService.findOwner(any())).thenReturn(user);
    when(accountsRepository.findByAccountId(any()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder()
                        .dataSource(DataSource.LIFT)
                        .goldenRecordId("sf-1")
                        .id(UUID.randomUUID())
                        .build())
                .build());
    when(liftApiService.validate(any(), any(), any(), any()))
        .thenReturn(PayloadValidationDto.builder().build());
    when(sitesRepository.saveAndFlush(any())).thenReturn(loadSite());
    SiteDto result = siteService.save(siteDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void whenGetAllSiteIdsFilteredIsCalledCorrectResultIsReturned() {
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(sitesRepository.findSiteIdsByAccountIds(any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    List<String> result = siteService.getFilteredSiteIds();
    assertNotNull(result);
  }

  @Test
  void whenGetAllSiteIdsFilteredIsCalledAndResultIsNullNoExceptionIsThrown() {
    when(accountsRepository.findAccountIdsByUserWithAllFlags(any(), any()))
        .thenReturn(List.of(UUID.randomUUID().toString()));
    when(sitesRepository.findSiteIdsByAccountIds(any())).thenReturn(null);
    List<String> result = siteService.getFilteredSiteIds();
    assertNotNull(result);
  }

  @Test
  void whenBarnIsNullAndSaveResultIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    List<Pens> pens =
        Arrays.asList(
            Pens.builder()
                .penDocument(PenDocument.builder().siteId(UUID.randomUUID()).build())
                .build());
    SiteDto siteDto =
        SiteDto.builder()
            .accountId(UUID.randomUUID())
            .siteMappings(
                List.of(
                    SiteMappingDto.builder()
                        .systemId(UUID.randomUUID().toString())
                        .systemName("LM_SITE")
                        .build()))
            .barns(null)
            .localId(localId)
            .build();
    when(accountsRepository.findByAccountId(any()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder().goldenRecordId("sf-1").id(UUID.randomUUID()).build())
                .build());
    when(sitesRepository.saveAndFlush(any())).thenReturn(loadSite());
    when(pensRepository.findBySiteIds(any())).thenReturn(pens);
    SiteDto result = siteService.save(siteDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void whenValidateSalesforceDataIsCalledAndNoUserFoundExceptionIsThrown()
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    when(liftUserService.findOwner(any())).thenReturn(null);
    Accounts account =
        Accounts.builder()
            .accountDocument(
                AccountDocument.builder().goldenRecordId("test").id(UUID.randomUUID()).build())
            .build();
    Sites site = new Sites(SiteDocument.builder().id(UUID.randomUUID()).build());

    siteService.validateSalesforceData(site, account, locale, resourceBundleMessageSource);

    assertNotNull(account);
  }

  @Test
  void whenSiteExistsCorrectModelIsReturned() throws Exception {
    String localId = UUID.randomUUID().toString();
    SiteDto siteDto = SiteDto.builder().accountId(UUID.randomUUID()).localId(localId).build();

    when(sitesRepository.findByLocalId(siteDto.getLocalId())).thenReturn(List.of(loadSite()));

    SiteDto result = siteService.save(siteDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void whenAccountDoesNotExistSaveThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    SiteDto siteDto = SiteDto.builder().accountId(UUID.randomUUID()).localId(localId).build();

    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> {
          siteService.save(siteDto, locale, resourceBundleMessageSource);
        });
  }

  @Test
  void whenLocalIdIsNullSaveThrowsAnException() {
    SiteDto siteDto = SiteDto.builder().accountId(UUID.randomUUID()).build();

    Assertions.assertThrows(
        IllegalArgumentException.class,
        () -> {
          siteService.save(siteDto, locale, resourceBundleMessageSource);
        });
  }

  @Test
  void whenAccountDoesNotExistUpdateThrowsAnException() {
    String localId = UUID.randomUUID().toString();
    SiteDto siteDto = SiteDto.builder().accountId(UUID.randomUUID()).localId(localId).build();

    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> {
          siteService.update(siteDto, locale, resourceBundleMessageSource);
        });
  }

  @Test
  void whenSiteDoesNotExistUpdateThrowsAnException() {
    SiteDto siteDto =
        SiteDto.builder()
            .accountId(UUID.randomUUID())
            .localId(UUID.randomUUID().toString())
            .build();

    when(accountsRepository.findByAccountId(siteDto.getAccountId().toString()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder().goldenRecordId("sf-1").id(UUID.randomUUID()).build())
                .build());

    Assertions.assertThrows(
        NotFoundDEException.class,
        () -> {
          siteService.update(siteDto, locale, resourceBundleMessageSource);
        });
  }

  @Test
  void whenAllWorksCorrectUpdateResultIsReturned()
      throws JsonProcessingException, NotFoundDEException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    String localId = UUID.randomUUID().toString();
    SiteDto siteDto =
        SiteDto.builder()
            .id(UUID.randomUUID())
            .origination(ApplicationMapping.LM_SITE_SYSTEM_NAME)
            .dateOfLastVisit(Instant.now())
            .siteMappings(
                List.of(
                    SiteMappingDto.builder()
                        .systemId(UUID.randomUUID().toString())
                        .systemName("LM_SITE")
                        .build()))
            .accountId(UUID.randomUUID())
            .localId(localId)
            .build();
    when(accountsRepository.findByAccountId(siteDto.getAccountId().toString()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder().goldenRecordId("sf-1").id(UUID.randomUUID()).build())
                .build());
    Sites site = loadSite();

    when(sitesRepository.findBySiteId(siteDto.getId().toString())).thenReturn(site);
    when(sitesRepository.saveAndFlush(any())).thenReturn(site);

    SiteDto result = siteService.update(siteDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void whenAllWorksCorrectAndSiteMappingIsNotLmSite()
      throws JsonProcessingException, NotFoundDEException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    String localId = UUID.randomUUID().toString();
    List<SiteMappingDto> siteMappings = new ArrayList<>();
    siteMappings.add(
        SiteMappingDto.builder()
            .systemId(UUID.randomUUID().toString())
            .systemName(ApplicationMapping.DDW_SYSTEM_NAME)
            .build());
    SiteDto siteDto =
        SiteDto.builder()
            .id(UUID.randomUUID())
            .origination(ApplicationMapping.LM_SITE_SYSTEM_NAME)
            .siteMappings(siteMappings)
            .accountId(UUID.randomUUID())
            .localId(localId)
            .build();
    when(accountsRepository.findByAccountId(siteDto.getAccountId().toString()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder().goldenRecordId("sf-1").id(UUID.randomUUID()).build())
                .build());
    Sites site = loadSite();
    site.getSiteDocument()
        .setDataSourceMappings(SiteMapper.dtoToModelForSiteMappings(siteMappings));
    when(sitesRepository.findBySiteId(siteDto.getId().toString())).thenReturn(site);
    when(sitesRepository.saveAndFlush(any())).thenReturn(site);

    SiteDto result = siteService.update(siteDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void whenAllWorksCorrectAndSiteMappingIsNull()
      throws JsonProcessingException, NotFoundDEException, IllegalAccessException,
          ClassNotFoundException, CustomDEExceptions {
    String localId = UUID.randomUUID().toString();
    SiteDto siteDto =
        SiteDto.builder()
            .id(UUID.randomUUID())
            .origination(ApplicationMapping.LM_SITE_SYSTEM_NAME)
            .siteMappings(null)
            .accountId(UUID.randomUUID())
            .localId(localId)
            .build();
    when(accountsRepository.findByAccountId(siteDto.getAccountId().toString()))
        .thenReturn(
            Accounts.builder()
                .accountDocument(
                    AccountDocument.builder().goldenRecordId("sf-1").id(UUID.randomUUID()).build())
                .build());
    Sites site = loadSite();

    when(sitesRepository.findBySiteId(siteDto.getId().toString())).thenReturn(site);
    when(sitesRepository.saveAndFlush(any())).thenReturn(site);

    SiteDto result = siteService.update(siteDto, locale, resourceBundleMessageSource);

    assertNotNull(result);
    assertNotNull(result.getCreatedDate());
    assertNotNull(result.getUpdatedDate());
  }

  @Test
  void updateSiteMappingsByDairyMax() {

    Sites site =
        Sites.builder()
            .siteDocument(
                SiteDocument.builder()
                    .id(UUID.randomUUID())
                    .dataSourceMappings(
                        List.of(
                            DataSourceMapping.builder()
                                .systemName(ApplicationMapping.DDW_SYSTEM_NAME)
                                .systemId(UUID.randomUUID().toString())
                                .build()))
                    .build())
            .build();

    siteService.updateSiteMappingsMax(
        site,
        SiteMappingDocument.builder()
            .ddwHerdId(UUID.randomUUID().toString())
            .maxSiteId(UUID.randomUUID())
            //     .milkProcessorId(UUID.randomUUID().toString())
            //     .dcgoId(UUID.randomUUID().toString())
            .build());

    assertTrue(
        site.getSiteDocument().getDataSourceMappings().stream()
            .anyMatch(m -> m.getSystemName().equalsIgnoreCase(ApplicationMapping.DDW_SYSTEM_NAME)));
  }

  private Sites loadSite() {
    return Sites.builder()
        .siteDocument(
            SiteDocument.builder()
                .keys(new HashMap<>())
                .accountId(UUID.randomUUID())
                .visits(
                    List.of(
                        SiteVisit.builder()
                            .visitDate(Instant.now())
                            .status(VisitStatus.Published)
                            .build()))
                .externalAccountId("test")
                .externalId("test")
                .barns(List.of(Barn.builder().build()))
                .dataSourceMappings(List.of())
                .origination(ApplicationMapping.LM_SITE_SYSTEM_NAME)
                .dateOfLastVisit(Instant.now())
                .build())
        .createdDate(Date.from(Instant.now()))
        .updatedDate(Date.from(Instant.now()))
        .build();
  }
}
