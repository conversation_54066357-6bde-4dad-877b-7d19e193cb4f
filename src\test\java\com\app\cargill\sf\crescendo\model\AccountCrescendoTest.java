/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.crescendo.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.io.File;
import java.io.IOException;
import java.util.List;
import org.junit.jupiter.api.Test;

class AccountCrescendoTest {

  @Test
  void modelTest() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> items =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo-accounts-sample.json"),
            new TypeReference<>() {});
    assertEquals(1, items.size());
  }

  @Test
  void newAccountModelTest() throws IOException {
    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    List<AccountCrescendo> items =
        objectMapper.readValue(
            new File("src/test/resources/salesforce/crescendo/new_app_account.json"),
            new TypeReference<>() {});
    assertEquals(1, items.size());
  }
}
