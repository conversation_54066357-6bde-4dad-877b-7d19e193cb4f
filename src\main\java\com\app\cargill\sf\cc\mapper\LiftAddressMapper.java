/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.mapper;

import com.app.cargill.document.Address;
import com.app.cargill.sf.cc.model.PhysicalAddressSalesforce;

public class LiftAddressMapper {

  private LiftAddressMapper() {}

  public static Address transform(PhysicalAddressSalesforce input) {
    Address address = new Address();
    if (input != null) {
      address.setStreet(input.getStreet());
      address.setCity(input.getCity());
      address.setStateOrProvince(input.getState());
      address.setPostalCode(input.getPostalCode());
      address.setCountry(input.getCountry());
      address.setCountyCommunity(input.getStateCode());
    }
    return address;
  }
}
