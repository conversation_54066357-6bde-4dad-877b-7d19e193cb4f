/* Cargill Inc.(C) 2022 */
package com.app.cargill.sf.cc.service;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LiftUserAccessServiceTest {

  @Mock private LiftApiService liftApi;

  @InjectMocks private LiftUserAccessService userAccessService;

  @Test
  void whenDataIsCorrectUserAccessIsCreated() throws CustomDEExceptions, JsonProcessingException {
    CreateRecordResponse createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setSuccess(true);
    when(liftApi.createRecord(any(), any(), any(), any())).thenReturn(createRecordResponse);
    assertTrue(userAccessService.createUserAccess("123", "456", "Account Representative"));
  }

  @Test
  void whenUserIsInvalidExceptionIsThrown() {
    assertThrows(
        IllegalArgumentException.class,
        () -> userAccessService.createUserAccess(null, "456", "Account Representative"));
  }

  @Test
  void whenAccountIsInvalidExceptionIsThrown() {
    assertThrows(
        IllegalArgumentException.class,
        () -> userAccessService.createUserAccess("123", null, "Account Representative"));
  }

  @Test
  void whenRoleIsMissingExceptionIsThrown() {
    assertThrows(
        IllegalArgumentException.class,
        () -> userAccessService.createUserAccess("123", "456", null));
  }

  @Test
  void whenRoleIsInvalidExceptionIsThrown() {
    assertThrows(
        IllegalArgumentException.class,
        () -> userAccessService.createUserAccess("123", "456", "Invalid Role"));
  }

  @Test
  void whenApiFailsExceptionIsThrown() throws CustomDEExceptions, JsonProcessingException {
    when(liftApi.createRecord(any(), any(), any(), any()))
        .thenThrow(new IllegalArgumentException("Test Fail Case"));
    assertThrows(
        LiftException.class,
        () -> userAccessService.createUserAccess("123", "456", "Account Representative"));
  }
}
