<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <changeSet id="027" author="anenkov">
    <sql>
      CREATE INDEX IF NOT EXISTS visit_document_customer_id_idx ON visits((visit_document->>'CustomerId'));
      CREATE INDEX IF NOT EXISTS visit_document_visit_date_idx ON visits((visit_document->>'VisitDate'));
    </sql>
  </changeSet>
</databaseChangeLog>