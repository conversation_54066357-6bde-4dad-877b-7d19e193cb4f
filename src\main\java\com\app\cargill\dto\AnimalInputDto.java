/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.app.cargill.constants.Breed;
import com.app.cargill.constants.ProductionSystem;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimalInputDto implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  private Double animalsInHerd;
  private Double totalNumberOfCows;
  private Double totalNumberOfLactatingAnimals;
  private Breed breed;
  private ProductionSystem productionSystem;
}
